2025-07-31 21:21:01:005 ==>> MES查站成功:
查站序号:P510001005313269验证通过
2025-07-31 21:21:01:011 ==>> 扫码结果:P510001005313269
2025-07-31 21:21:01:012 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:21:01:014 ==>> 测试参数版本:2024.10.11
2025-07-31 21:21:01:016 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:21:01:017 ==>> 检测【打开透传】
2025-07-31 21:21:01:019 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:21:01:077 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:21:01:445 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:21:01:453 ==>> 检测【检测接地电压】
2025-07-31 21:21:01:455 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:21:01:580 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:21:01:753 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:21:01:756 ==>> 检测【打开小电池】
2025-07-31 21:21:01:759 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:21:01:869 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:21:02:053 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:21:02:055 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:21:02:057 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:21:02:169 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 21:21:02:342 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:21:02:344 ==>> 检测【等待设备启动】
2025-07-31 21:21:02:346 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:02:770 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:21:02:952 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 21:21:03:375 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:04:405 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:05:453 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:06:482 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:07:531 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:08:083 ==>>  

2025-07-31 21:21:08:571 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:09:613 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:10:645 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:11:684 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:21:12:720 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:21:12:722 ==>> #################### 【测试结束】 ####################
2025-07-31 21:21:12:802 ==>> 关闭5V供电
2025-07-31 21:21:12:805 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:21:12:872 ==>> 5A A5 04 5A A5 


2025-07-31 21:21:12:977 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:21:13:807 ==>> 关闭5V供电成功
2025-07-31 21:21:13:811 ==>> 关闭33V供电
2025-07-31 21:21:13:814 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:21:13:867 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:21:14:816 ==>> 关闭33V供电成功
2025-07-31 21:21:14:819 ==>> 关闭3.7V供电
2025-07-31 21:21:14:821 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:21:14:876 ==>> 6A A6 02 A6 6A 


2025-07-31 21:21:14:966 ==>> Battery OFF
OVER 150


2025-07-31 21:21:15:071 ==>>  

