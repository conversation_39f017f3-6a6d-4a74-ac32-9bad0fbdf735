2025-07-31 21:34:56:891 ==>> 扫码结果:P510001005313496
2025-07-31 21:34:56:900 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:34:56:901 ==>> 测试参数版本:2024.10.11
2025-07-31 21:34:56:903 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:34:56:905 ==>> 检测【打开透传】
2025-07-31 21:34:56:906 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:34:56:982 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:34:57:382 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:34:57:391 ==>> 检测【检测接地电压】
2025-07-31 21:34:57:392 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:34:57:484 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:34:57:686 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:34:57:688 ==>> 检测【打开小电池】
2025-07-31 21:34:57:690 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:34:57:773 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:34:57:971 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:34:57:973 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:34:57:976 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:34:58:075 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:34:58:263 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:34:58:266 ==>> 检测【等待设备启动】
2025-07-31 21:34:58:268 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:34:58:610 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:34:58:805 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:34:59:291 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:34:59:321 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:34:59:501 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:35:00:133 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:35:00:208 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:35:00:313 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:35:00:604 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:35:01:071 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:35:01:121 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:35:01:124 ==>> 检测【产品通信】
2025-07-31 21:35:01:126 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:35:01:266 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:35:01:602 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:35:01:604 ==>> 检测【初始化完成检测】
2025-07-31 21:35:01:609 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:35:01:778 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:35:01:900 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:35:01:902 ==>> 检测【关闭大灯控制1】
2025-07-31 21:35:01:904 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:35:01:957 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:35:02:630 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 21:35:02:935 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:35:03:013 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:35:03:118 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+ARM

2025-07-31 21:35:03:148 ==>> LAMP=0<<<<<


2025-07-31 21:35:03:255 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:35:03:257 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:35:03:260 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:35:03:529 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 21:35:04:280 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:35:04:466 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 21:35:04:679 ==>> [W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 21:35:05:323 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:35:05:464 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 21:35:06:362 ==>> 未匹配到【打开仪表指令模式1】数据,请核对检查!
2025-07-31 21:35:06:368 ==>> #################### 【测试结束】 ####################
2025-07-31 21:35:06:753 ==>> 关闭5V供电
2025-07-31 21:35:06:756 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:35:06:885 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:35:07:763 ==>> 关闭5V供电成功
2025-07-31 21:35:07:766 ==>> 关闭33V供电
2025-07-31 21:35:07:769 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:35:07:884 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:35:08:770 ==>> 关闭33V供电成功
2025-07-31 21:35:08:773 ==>> 关闭3.7V供电
2025-07-31 21:35:08:775 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:35:08:875 ==>> 6A A6 02 A6 6A 


2025-07-31 21:35:08:980 ==>> Battery OFF
OVER 150


2025-07-31 21:35:09:311 ==>>  

2025-07-31 21:35:09:784 ==>> 关闭3.7V供电成功
2025-07-31 21:35:09:789 ==>> 
单项耗时 => 【285】 毫秒 => 【打开小电池】
单项耗时 => 【292】 毫秒 => 【检测小电池分压(AD_VBAT)】
单项耗时 => 【295】 毫秒 => 【检测接地电压】
单项耗时 => 【297】 毫秒 => 【初始化完成检测】
单项耗时 => 【480】 毫秒 => 【产品通信】
单项耗时 => 【487】 毫秒 => 【打开透传】
单项耗时 => 【1354】 毫秒 => 【关闭大灯控制1】
单项耗时 => 【2857】 毫秒 => 【等待设备启动】

