2025-07-31 23:29:44:483 ==>> MES查站成功:
查站序号:P510001005313868验证通过
2025-07-31 23:29:44:496 ==>> 扫码结果:P510001005313868
2025-07-31 23:29:44:498 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:29:44:499 ==>> 测试参数版本:2024.10.11
2025-07-31 23:29:44:501 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:29:44:502 ==>> 检测【打开透传】
2025-07-31 23:29:44:504 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:29:44:635 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:29:44:844 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:29:44:866 ==>> 检测【检测接地电压】
2025-07-31 23:29:44:869 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:29:44:923 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:29:45:137 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:29:45:139 ==>> 检测【打开小电池】
2025-07-31 23:29:45:141 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:29:45:224 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:29:45:409 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:29:45:412 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:29:45:415 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:29:45:532 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 23:29:45:684 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:29:45:687 ==>> 检测【等待设备启动】
2025-07-31 23:29:45:689 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:29:45:941 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:29:46:136 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:29:46:720 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:29:46:780 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:29:46:825 ==>>                                                    

2025-07-31 23:29:47:227 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:29:47:702 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:29:47:766 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:29:47:768 ==>> 检测【产品通信】
2025-07-31 23:29:47:770 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:29:47:899 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:29:48:038 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:29:48:040 ==>> 检测【初始化完成检测】
2025-07-31 23:29:48:043 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:29:48:249 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:29:48:313 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:29:48:315 ==>> 检测【关闭大灯控制1】
2025-07-31 23:29:48:316 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:29:48:354 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:29:48:490 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:29:48:585 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:29:48:587 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:29:48:589 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:29:48:929 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:29:49:124 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:29:49:126 ==>> 检测【关闭仪表供电】
2025-07-31 23:29:49:129 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:29:49:318 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:29:49:398 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:29:49:400 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:29:49:402 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:29:49:593 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:29:49:677 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:29:49:679 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:29:49:682 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:29:49:803 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:29:49:956 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:29:49:958 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:29:49:960 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:29:50:089 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:29:50:227 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:29:50:230 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:29:50:232 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:29:50:331 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:29:50:391 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 17


2025-07-31 23:29:50:451 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 23:29:50:499 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:29:50:502 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:29:50:511 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:29:50:634 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:29:50:772 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:29:50:774 ==>> 该项需要延时执行
2025-07-31 23:29:50:799 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:29:51:306 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E2221

2025-07-31 23:29:51:336 ==>> 7 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 23:29:51:788 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:29:51:999 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:29:52:553 ==>>       :17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:17:54][COMM]Main Task receive event:66
[W][05:17:54][COMM]Bat auth off fail, error:-1
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][

2025-07-31 23:29:52:658 ==>> COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][PROT]min_index:2, type:

2025-07-31 23:29:52:764 ==>> 0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat wor

2025-07-31 23:29:52:823 ==>> k state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:29:52:898 ==>>                                                                                                  [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:29:53:808 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:29:54:468 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:29:54:784 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:29:54:787 ==>> 检测【33V输入电压ADC】
2025-07-31 23:29:54:790 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:29:54:829 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:29:55:039 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:17:57][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:0 mv
[D][05:17:57][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:29:55:318 ==>> 【33V输入电压ADC】通过,【32375mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:29:55:321 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:29:55:322 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:29:55:440 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1983mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:29:55:593 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:55:596 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:29:55:613 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:55:615 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:29:55:617 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 23:29:55:630 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:29:55:633 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:29:55:648 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1983mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:29:55:652 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:29:55:671 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:29:55:673 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:29:55:742 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1672mV
Get AD_V4 0mV
Get AD_V5 2755mV
Get AD_V6 1980mV
Get AD_V7 1088mV
OVER 150


2025-07-31 23:29:55:847 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:29:55:944 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:55:946 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:29:55:963 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:55:965 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:29:55:968 ==>> 原始值:【2755】, 乘以分压基数【2】还原值:【5510】
2025-07-31 23:29:55:985 ==>> 【TP68_VCC5V5(ADV5)】通过,【5510mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:29:55:987 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:29:56:002 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1980mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:29:56:005 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:29:56:026 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:29:56:028 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:29:56:132 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2755mV
Get AD_V6 1982mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:29:56:192 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10022


2025-07-31 23:29:56:306 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:56:308 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:29:56:324 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:29:56:326 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:29:56:327 ==>> 原始值:【2755】, 乘以分压基数【2】还原值:【5510】
2025-07-31 23:29:56:342 ==>> 【TP68_VCC5V5(ADV5)】通过,【5510mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:29:56:344 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:29:56:361 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1982mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:29:56:363 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:29:56:385 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:29:56:387 ==>> 检测【打开WIFI(1)】
2025-07-31 23:29:56:388 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:29:56:469 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:29:56:574 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:29:56:667 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:29:56:670 ==>> 检测【清空消息队列(1)】
2025-07-31 23:29:56:673 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:29:57:092 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:5

2025-07-31 23:29:57:137 ==>> 9][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:29:57:201 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:29:57:204 ==>> 检测【打开GPS(1)】
2025-07-31 23:29:57:205 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:29:57:531 ==>>                                                                                                                                                                                                                                                             d: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087620300

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:29:57:731 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:29:57:734 ==>> 检测【打开GSM联网】
2025-07-31 23:29:57:746 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:29:57:914 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 23:29:58:007 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:29:58:010 ==>> 检测【打开仪表供电1】
2025-07-31 23:29:58:013 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:29:58:221 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:29:58:285 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:29:58:287 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:29:58:290 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:29:58:538 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:29:58:814 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:29:58:818 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:29:58:820 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:29:59:018 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33247]


2025-07-31 23:29:59:100 ==>> 【读取主控ADC采集的仪表电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:29:59:105 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:29:59:108 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:29:59:318 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:29:59:391 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:29:59:395 ==>> 检测【AD_V20电压】
2025-07-31 23:29:59:397 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:29:59:498 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:29:59:777 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:29:59:852 ==>> [D][05:18:02][COMM]13727 imu init OK


2025-07-31 23:29:59:957 ==>> 本次取值间隔时间:452ms
2025-07-31 23:29:59:987 ==>> [D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:30:00:321 ==>> 本次取值间隔时间:359ms
2025-07-31 23:30:00:610 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 23:30:00:715 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:30:00:790 ==>> 本次取值间隔时间:456ms
2025-07-31 23:30:01:047 ==>> 本次取值间隔时间:249ms
2025-07-31 23:30:01:052 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:01:107 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:30:01:152 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:01:212 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:30:01:272 ==>> 本次取值间隔时间:110ms
2025-07-31 23:30:01:291 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:01:392 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:01:653 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][COMM]S->M yaw:INVALID


2025-07-31 23:30:01:683 ==>> 本次取值间隔时间:282ms
2025-07-31 23:30:01:818 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:30:01:985 ==>> 本次取值间隔时间:294ms
2025-07-31 23:30:02:184 ==>> 本次取值间隔时间:191ms
2025-07-31 23:30:02:473 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:30:02:623 ==>> 本次取值间隔时间:433ms
2025-07-31 23:30:02:627 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:02:668 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,59,,,43,40,,,42,25,,,41,39,,,41,1*71

$GBGSV,3,2,10,60,,,41,23,,,39,34,,,37,41,,,37,1*7C

$GBGSV,3,3,10,10,,,36,11,,,36,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1663.468,1663.468,53.166,2097152,2097152,2097152*48

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 23:30:02:728 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:02:743 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:30:02:803 ==>> 本次取值间隔时间:73ms
2025-07-31 23:30:02:848 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:30:02:968 ==>> 本次取值间隔时间:155ms
2025-07-31 23:30:02:986 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:03:093 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:03:293 ==>> 本次取值间隔时间:190ms
2025-07-31 23:30:03:324 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:30:03:628 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,42,59,,,41,25,,,41,39,,,41,1*7C

$GBGSV,5,2,19,43,,,41,7,,,41,60,,,40,23,,,39,1*40

$GBGSV,5,3,19,34,,,38,10,,,38,11,,,38,16,,,38,1*79

$GBGSV,5,4,19,41,,,37,6,,,36,5,,,35,12,,,32,1*7C

$GBGSV,5,5,19,9,,,32,2,,,40,4,,,39,1*4E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1585.173,1585.173,50.712,2097152,2097152,2097152*4E



2025-07-31 23:30:03:703 ==>> 本次取值间隔时间:408ms
2025-07-31 23:30:03:733 ==>> [D][05:18:06][COMM]M->S yaw:INVALID


2025-07-31 23:30:04:208 ==>> 本次取值间隔时间:496ms
2025-07-31 23:30:04:434 ==>> 本次取值间隔时间:225ms
2025-07-31 23:30:04:438 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:04:543 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:04:665 ==>> [D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,25,,,41,39,,,41,43,,,41,1*7D

$GBGSV,6,2,23,59,,,40,7,,,40,60,,,40,34,,,40,1*49

$GBGSV,6,3,23,3,,,40,23,,,39,11,,,39,16,,,39,1*49

$GBGSV,6,4,23,1,,,39,10,,,38,41,,,37,6,,,37,1*77

$GBGSV,6,5,23,2,,,36,32,,,36,5,,,34,4,,,33,1*41

$GBGSV,6,6,23,12,,,33,9,,,33,24,,,36,1*4E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1579.176,1579.176,50.510,2097152,2097152,2097152*4E

[W][05:18:07][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:30:04:785 ==>> 本次取值间隔时间:231ms
2025-07-31 23:30:04:803 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:30:04:908 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:30:05:003 ==>> 本次取值间隔时间:89ms
2025-07-31 23:30:05:033 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1662mV
OVER 150


2025-07-31 23:30:05:199 ==>> 本次取值间隔时间:182ms
2025-07-31 23:30:05:217 ==>> 【AD_V20电压】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:30:05:221 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:30:05:225 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:30:05:321 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:30:05:504 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:30:05:507 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:30:05:511 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:30:05:644 ==>> $GBGGA,153009.501,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,25,,,41,39,,,41,43,,,41,1*7C

$GBGSV,6,2,22,59,,,41,7,,,40,60,,,40,34,,,40,1*49

$GBGSV,6,3,22,3,,,40,11,,,40,23,,,39,16,,,39,1*46

$GBGSV,6,4,22,1,,,38,10,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,32,,,36,2,,,35,9,,,35,12,,,34,1*7E

$GBGSV,6,6,22,5,,,33,4,,,33,1*77

$GBRMC,153009.501,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153009.501,0.000,1584.827,1584.827,50.688,2097152,2097152,2097152*58



2025-07-31 23:30:05:749 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:30:05:782 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:30:05:786 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:30:05:788 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:30:05:931 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:30:06:054 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:30:06:057 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:30:06:061 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:30:06:250 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:30:06:329 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:30:06:334 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:30:06:339 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:30:06:523 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:30:06:602 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:30:06:605 ==>> 检测【AD_V21电压】
2025-07-31 23:30:06:607 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:30:06:628 ==>>                                                                                                       ,41,39,,,41,43,,,41,1*7C

$GBGSV,6,2,22,59,,,41,34,,,41,7,,,40,60,,,40,1*48

$GBGSV,6,3,22,3,,,40,11,,,40,23,,,39,16,,,39,1*46

$GBGSV,6,4,22,1,,,38,10,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,32,,,36,9,,,36,2,,,35,12,,,34,1*7D

$GBGSV,6,6,22,5,,,33,4,,,33,1*77

$GBRMC,153010.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153010.501,0.000,1588.595,1588.595,50.808,2097152,2097152,20971

2025-07-31 23:30:06:658 ==>> 52*56

[D][05:18:09][COMM]read battery soc:255
1A A1 20 00 00 
Get AD_V21 1050mV
OVER 150


2025-07-31 23:30:06:763 ==>> 本次取值间隔时间:151ms
2025-07-31 23:30:06:782 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:30:06:933 ==>> 1A A1 20 00 00 
Get AD_V21 1658mV
OVER 150


2025-07-31 23:30:07:177 ==>> 本次取值间隔时间:383ms
2025-07-31 23:30:07:196 ==>> 【AD_V21电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:30:07:198 ==>> 检测【关闭仪表供电2】
2025-07-31 23:30:07:201 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:30:07:433 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:10][COMM]set POWER 0
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:30:07:473 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:30:07:476 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:30:07:479 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:30:07:688 ==>> $GBGGA,153011.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,25,,,41,39,,,41,43,,,41,1*7C

$GBGSV,6,2,22,59,,,41,34,,,41,11,,,41,7,,,40,1*4F

$GBGSV,6,3,22,60,,,40,3,,,40,23,,,39,16,,,39,1*40

$GBGSV,6,4,22,1,,,38,10,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,32,,,36,9,,,36,2,,,35,12,,,34,1*7D

$GBGSV,6,6,22,5,,,33,4,,,33,1*77

$GBRMC,153011.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153011.501,0.000,1590.481,1590.481,50.869,2097152,2097152,2097152*50

[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:30:07:811 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:30:07:815 ==>> 检测【打开AccKey2供电】
2025-07-31 23:30:07:817 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:30:08:010 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:30:08:119 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:30:08:123 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:30:08:125 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:08:446 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3121  volt:5486 mv
[D][05:18:11][COMM]adc read out 24v adc:1306  volt:33032 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:0 mv
[D][05:18:11][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2385  volt:3843 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:30:08:656 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33032mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:30:08:661 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:30:08:674 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:30:08:679 ==>> $GBGGA,153012.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,25,,,41,39,,,41,43,,,41,1*7D

$GBGSV,6,2,23,34,,,41,60,,,41,59,,,40,11,,,40,1*7E

$GBGSV,6,3,23,7,,,40,3,,,40,23,,,39,16,,,39,1*70

$GBGSV,6,4,23,1,,,38,10,,,38,41,,,38,6,,,37,1*79

$GBGSV,6,5,23,32,,,36,9,,,36,2,,,35,12,,,34,1*7C

$GBGSV,6,6,23,5,,,33,4,,,33,33,,,32,1*77

$GBRMC,153012.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153012.501,0.000,1577.219,1577.219,50.456,2097152,2097152,2097152*53

[D][05:18:11][COMM]read battery soc:255


2025-07-31 23:30:08:776 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:30:09:000 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:30:09:004 ==>> 该项需要延时执行
2025-07-31 23:30:09:680 ==>> $GBGGA,153013.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,25,,,41,39,,,41,43,,,41,1*7A

$GBGSV,6,2,24,34,,,41,60,,,40,59,,,40,11,,,40,1*78

$GBGSV,6,3,24,7,,,40,3,,,40,23,,,39,16,,,39,1*77

$GBGSV,6,4,24,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,6,6,24,12,,,34,5,,,33,4,,,33,33,,,32,1*74

$GBRMC,153013.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153013.501,0.000,1570.233,1570.233,50.232,2097152,2097152,2097152*56



2025-07-31 23:30:10:663 ==>> $GBGGA,153014.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,25,,,41,39,,,41,43,,,41,1*7A

$GBGSV,6,2,24,34,,,41,11,,,41,60,,,40,59,,,40,1*79

$GBGSV,6,3,24,7,,,40,3,,,40,23,,,39,16,,,39,1*77

$GBGSV,6,4,24,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,6,6,24,12,,,34,5,,,33,4,,,33,33,,,32,1*74

$GBRMC,153014.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153014.501,0.000,1571.962,1571.962,50.289,2097152,2097152,2097152*51

[D][05:18:13][COMM]read battery soc:255


2025-07-31 23:30:11:677 ==>> $GBGGA,153015.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,60,,,40,1*78

$GBGSV,7,3,25,7,,,40,3,,,40,23,,,39,16,,,39,1*77

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,35,5,,,33,4,,,33,33,,,32,1*75

$GBGSV,7,7,25,28,,,29,1*70

$GBRMC,153015.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153015.501,0.000,1563.836,1563.836,50.055,2097152,2097152,2097152*53



2025-07-31 23:30:12:007 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:30:12:012 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:30:12:039 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:12:339 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:3 mv
[D][05:18:15][COMM]adc read in 24v adc:1273  volt:32197 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3351  volt:2699 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:30:12:541 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:30:12:546 ==>> 检测【打开AccKey1供电】
2025-07-31 23:30:12:551 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:30:12:673 ==>> $GBGGA,153016.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,42,43,,,41,1*7B

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,41,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,40,16,,,40,1*42

$GBGSV,7,4,25,1,,,39,10,,,38,41,,,38,6,,,37,1*7F

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,35,5,,,33,4,,,33,33,,,32,1*75

$GBGSV,7,7,25,28,,,29,1*70

$GBRMC,153016.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153016.501,0.000,1572.133,1572.133,50.325,2097152,2097152,2097152*54

[D][05:18:15][COMM]read battery soc:255


2025-07-31 23:30:12:778 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:30:12:814 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:30:12:818 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:30:12:820 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:30:12:928 ==>> 1A A1 00 40 00 
Get AD_V14 2653mV
OVER 150


2025-07-31 23:30:13:065 ==>> 原始值:【2653】, 乘以分压基数【2】还原值:【5306】
2025-07-31 23:30:13:095 ==>> 【读取AccKey1电压(ADV14)前】通过,【5306mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:30:13:099 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:30:13:104 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:13:437 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:0 mv
[D][05:18:16][COMM]adc read in 24v adc:1277  volt:32299 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3351  volt:2699 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:30:13:620 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5496mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:30:13:625 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:30:13:628 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:30:13:676 ==>> $GBGGA,153017.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,38,1*71

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,35,5,,,33,4,,,33,33,,,32,1*75

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153017.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153017.501,0.000,1567.147,1567.147,50.156,2097152,2097152,2097152*53



2025-07-31 23:30:13:812 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:30:13:890 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:30:13:895 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:30:13:898 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:30:14:022 ==>> 1A A1 00 40 00 
Get AD_V14 2654mV
OVER 150


2025-07-31 23:30:14:142 ==>> 原始值:【2654】, 乘以分压基数【2】还原值:【5308】
2025-07-31 23:30:14:164 ==>> 【读取AccKey1电压(ADV14)后】通过,【5308mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:30:14:168 ==>> 检测【打开WIFI(2)】
2025-07-31 23:30:14:173 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:30:14:357 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:30:14:434 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:30:14:438 ==>> 检测【转刹把供电】
2025-07-31 23:30:14:449 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:30:14:687 ==>> $GBGGA,153018.501,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,25,,,42,39,,,41,43,,,41,1*79

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,35,5,,,33,4,,,33,33,,,32,1*75

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153018.501,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153018.501,0.000,1563.828,1563.828,50.047,2097152,2097152,2097152*5D

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:17][COMM]read battery soc:255


2025-07-31 23:30:14:967 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:30:14:973 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:30:14:977 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:30:15:072 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:30:15:133 ==>> +WIFISCAN:4,0,F88C21BCF57D,-37
+WIFISCAN:4,1,CC057790A5C1,-81
+WIFISCAN:4,2,CC057790A5C0,-82
+WIFISCAN:4,3,F86FB0660A82,-83

[D][05:18:17][CAT1]wifi scan report total[4]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2391mV
OVER 150


2025-07-31 23:30:15:223 ==>> 原始值:【2391】, 乘以分压基数【2】还原值:【4782】
2025-07-31 23:30:15:259 ==>> 【读取AD_V15电压(前)】通过,【4782mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:30:15:264 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:30:15:288 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:30:15:363 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:30:15:392 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:30:15:422 ==>> 1A A1 01 00 00 
Get AD_V16 2407mV
OVER 150


2025-07-31 23:30:15:527 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 23:30:15:564 ==>> 【读取AD_V16电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:30:15:570 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:30:15:578 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:15:681 ==>> $GBGGA,153019.501,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,25,,,41,39,,,41,43,,,41,1*7A

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,35,5,,,34,4,,,33,33,,,32,1*72

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153019.501,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153019.501,0.000,1563.823,1563.823,50.042,2097152,2097152,2097152*59



2025-07-31 23:30:15:906 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:18][COMM]adc read battery ts volt:0 mv
[D][05:18:18][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3067  volt:5391 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                      

2025-07-31 23:30:16:098 ==>> 【转刹把供电电压(主控ADC)】通过,【5391mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:30:16:105 ==>> 检测【转刹把供电电压】
2025-07-31 23:30:16:136 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:16:163 ==>> [D][05:18:19][COMM]S->M yaw:INVALID


2025-07-31 23:30:16:438 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:2 mv
[D][05:18:19][COMM]adc read in 24v adc:1278  volt:32324 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3077  volt:5408 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:30:16:663 ==>> 【转刹把供电电压】通过,【5408mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:30:16:669 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:30:16:685 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:16:688 ==>> $GBGGA,153020.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,33,33,,,32,1*73

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153020.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153020.501,0.000,1565.488,1565.488,50.102,2097152,2097152,2097152*56

[D][05:18:19][COMM]read battery soc:255


2025-07-31 23:30:16:788 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:30:17:014 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:30:17:018 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:30:17:024 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:17:126 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:30:17:233 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:17:238 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:20][COMM]M->S yaw:INVALID
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:30:17:339 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:30:17:430 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:30:17:471 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:30:17:475 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:30:17:483 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:17:581 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:30:17:689 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:17:694 ==>> $GBGGA,153021.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,41,43,,,41,1*79

$GBGSV,7,2,25,34,,,41,11,,,41,59,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,33,33,,,32,1*73

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153021.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153021.501,0.000,1563.827,1563.827,50.047,2097152,2097152,2097152*57

[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:30:17:795 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:30:17:904 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:17:996 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:30:18:011 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:30:18:102 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:30:18:117 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:30:18:132 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:30:18:222 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:30:18:328 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:30:18:359 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:30:18:364 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:30:18:370 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:30:18:433 ==>> 3A A3 03 01 A3 


2025-07-31 23:30:18:524 ==>> ON_OUT3
OVER 150


2025-07-31 23:30:18:629 ==>> $GBGGA,153022.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,43,,,41,1*7A

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,40,3,,,40,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,

2025-07-31 23:30:18:670 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:30:18:674 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:30:18:677 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:30:18:682 ==>> ,36,2,,,36,24,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,31,1*77

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153022.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153022.501,0.000,1558.854,1558.854,49.889,2097152,2097152,2097152*56

[D][05:18:21][COMM]read battery soc:255


2025-07-31 23:30:18:734 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:30:19:001 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:30:19:007 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:30:19:028 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:30:19:133 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:30:19:271 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:30:19:277 ==>> 检测【左刹电压测试1】
2025-07-31 23:30:19:282 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:19:544 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1713  volt:2258 mv
[D][05:18:22][COMM]adc read right brake adc:1712  volt:2257 mv
[D][05:18:22][COMM]adc read throttle adc:1712  volt:2257 mv
[D][05:18:22][COMM]adc read battery ts volt:0 mv
[D][05:18:22][COMM]adc read in 24v adc:1278  volt:32324 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:30:19:649 ==>>                                                                                                                                                                                                                                                                                                   BGSV,7,5,25,32,,,36,9,,,36,24,,,36,2,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153023.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153023.501,0.000,1563.829,156

2025-07-31 23:30:19:679 ==>> 3.829,50.048,2097152,2097152,2097152*5A



2025-07-31 23:30:19:809 ==>> 【左刹电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 23:30:19:813 ==>> 检测【右刹电压测试1】
2025-07-31 23:30:19:836 ==>> 【右刹电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 23:30:19:841 ==>> 检测【转把电压测试1】
2025-07-31 23:30:19:867 ==>> 【转把电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 23:30:19:871 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:30:19:877 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:30:19:924 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:30:20:140 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:30:20:146 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:30:20:167 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:30:20:225 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:30:20:412 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:30:20:416 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:30:20:420 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:30:20:532 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:30:20:637 ==>> $GBGGA,153024.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,41,43,,,41,1*79

$GBGSV,7,2,25,34,,,41,59,,,41,60,,,41,11,,,41,1*79

$GBGSV,7,3,25,3,,,40,7,,,40,23,,,39,16,,,39,1*77

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,24,,,35,2,,,35,1*7F

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$

2025-07-31 23:30:20:682 ==>> GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153024.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153024.501,0.000,1563.832,1563.832,50.051,2097152,2097152,2097152*55

[D][05:18:23][COMM]read battery soc:255


2025-07-31 23:30:20:687 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:30:20:691 ==>> 检测【左刹电压测试2】
2025-07-31 23:30:20:700 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:30:21:042 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:23][COMM]adc read battery ts volt:0 mv
[D][05:18:23][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:30:21:225 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:30:21:232 ==>> 检测【右刹电压测试2】
2025-07-31 23:30:21:254 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:30:21:258 ==>> 检测【转把电压测试2】
2025-07-31 23:30:21:273 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:30:21:279 ==>> 检测【晶振检测】
2025-07-31 23:30:21:284 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:30:21:417 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]


2025-07-31 23:30:21:555 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:30:21:562 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:30:21:569 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:30:21:674 ==>> $GBGGA,153025.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,59,,,41,3,,,41,60,,,40,1*4B

$GBGSV,7,3,25,11,,,40,7,,,40,23,,,39,16,,,39,1*44

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,24,,,35,2,,,35,1*7F

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153025.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153025.501,0.000,1563.833,1563.833,50.052,2097152,2097152,2097152*57

1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1672mV
Get AD_V4 1655mV
Get AD_V5 2756mV
Get AD_V6 1983mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:30:21:828 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:30:21:833 ==>> 检测【检测BootVer】
2025-07-31 23:30:21:836 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:30:22:212 ==>> [D][05:18:24][COMM]S->M yaw:INVALID
[W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = ***************
[D][05:18:24][FCTY]HardwareID  = 867222087620300
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = D6D223929953
[D][05:18:24][FCTY]Bat         = 3944 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 32, adc = 1276
[D][05:18:24][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNS

2025-07-31 23:30:22:257 ==>> S_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3781 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:30:22:377 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:30:22:382 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:30:22:385 ==>> 检测【检测固件版本】
2025-07-31 23:30:22:400 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:30:22:406 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:30:22:424 ==>> 检测【检测蓝牙版本】
2025-07-31 23:30:22:428 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:30:22:432 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:30:22:454 ==>> 检测【检测MoBikeId】
2025-07-31 23:30:22:457 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:30:22:461 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:30:22:464 ==>> 检测【检测蓝牙地址】
2025-07-31 23:30:22:470 ==>> 取到目标值:D6D223929953
2025-07-31 23:30:22:486 ==>> 【检测蓝牙地址】通过,【D6D223929953】符合目标值【】要求!
2025-07-31 23:30:22:490 ==>> 提取到蓝牙地址:D6D223929953
2025-07-31 23:30:22:496 ==>> 检测【BOARD_ID】
2025-07-31 23:30:22:514 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:30:22:517 ==>> 检测【检测充电电压】
2025-07-31 23:30:22:542 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:30:22:546 ==>> 检测【检测VBUS电压1】
2025-07-31 23:30:22:570 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:30:22:573 ==>> 检测【检测充电电流】
2025-07-31 23:30:22:589 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:30:22:593 ==>> 检测【检测IMEI】
2025-07-31 23:30:22:598 ==>> 取到目标值:867222087620300
2025-07-31 23:30:22:607 ==>> 【检测IMEI】通过,【867222087620300】符合目标值【】要求!
2025-07-31 23:30:22:612 ==>> 提取到IMEI:867222087620300
2025-07-31 23:30:22:616 ==>> 检测【检测IMSI】
2025-07-31 23:30:22:624 ==>> 取到目标值:***************
2025-07-31 23:30:22:634 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 23:30:22:640 ==>> 提取到IMSI:***************
2025-07-31 23:30:22:654 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:30:22:670 ==>> 取到目标值:***************
2025-07-31 23:30:22:674 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 23:30:22:678 ==>> 检测【打开CAN通信】
2025-07-31 23:30:22:684 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:30:22:702 ==>> $GBGGA,153026.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,24,,,36,2,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,33,33,,,32,1*73

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153026.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153026.501,0.000,1567.145,1567.145,50.154,2097152,2097152,2097152*53

[D][05:18:25][COMM]read battery soc:255


2025-07-31 23:30:22:728 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:30:22:928 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:30:22:932 ==>> 检测【检测CAN通信】
2025-07-31 23:30:22:956 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:30:23:045 ==>> [D][05:18:25][COMM]M->S yaw:INVALID
can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:30:23:120 ==>> [D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36976
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:30:23:165 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:30:23:198 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:30:23:205 ==>> 检测【关闭CAN通信】
2025-07-31 23:30:23:227 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:30:23:255 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:30:23:330 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:30:23:483 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:30:23:487 ==>> 检测【打印IMU STATE】
2025-07-31 23:30:23:492 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:30:23:675 ==>> $GBGGA,153027.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,43,,,41,1*78

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,36,24,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,33,33,,,32,1*73

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153027.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153027.501,0.000,1567.145,1567.145,50.154,2097152,2097152,2097152*52



2025-07-31 23:30:23:780 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:30:24:009 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:30:24:017 ==>> 检测【六轴自检】
2025-07-31 23:30:24:038 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:30:24:216 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:30:24:697 ==>> $GBGGA,153028.501,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,42,43,,,41,1*7A

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,40,3,,,40,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,9,,,36,2,,,36,24,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$GBGSV,7,7,25,28,,,30,1*78

$GBRMC,153028.501,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153028.501,0.000,1563.829,1563.829,50.048,2097152,2097152,2097152*51

[D][05:18:27][COMM]read battery soc:255


2025-07-31 23:30:25:680 ==>> $GBGGA,153029.501,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,41,43,,,41,1*79

$GBGSV,7,2,25,34,,,41,59,,,41,3,,,41,11,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,37,9,,,36,2,,,35,24,,,35,1*7E

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$GBGSV,7,7,25,28,,,31,1*79

$GBRMC,153029.501,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153029.501,0.000,1565.483,1565.483,50.097,2097152,2097152,2097152*52



2025-07-31 23:30:25:905 ==>> [D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:30:26:086 ==>> [D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39932 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-8,-2,4038]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 23:30:26:347 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:30:26:351 ==>> 检测【打印IMU STATE2】
2025-07-31 23:30:26:358 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:30:26:522 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:30:26:616 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:30:26:621 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:30:26:629 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:30:26:646 ==>>            30.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,

2025-07-31 23:30:26:733 ==>> ,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,41,43,,,41,1*79

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,37,9,,,36,2,,,35,24,,,35,1*7E

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,33,,,32,1*74

$GBGSV,7,7,25,28,,,31,1*79

$GBRMC,153030.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153030.501,0.000,1565.483,1565.483,50.097,2097152,2097152,2097152*5A

[D][05:18:29][COMM]read battery soc:255
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:30:26:886 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:30:26:891 ==>> 检测【检测VBUS电压2】
2025-07-31 23:30:26:913 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:30:26:917 ==>> [D][05:18:29][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:30:27:308 ==>> [D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = ***************
[D][05:18:29][FCTY]HardwareID  = 867222087620300
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D6D223929953
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 7, adc = 295
[D][05:18:29][FCTY]Acckey1 vol = 5494 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK

2025-07-31 23:30:27:368 ==>>  = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3781 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:30:27:417 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:30:27:857 ==>> $GBGGA,153031.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,42,43,,,41,1*7B

$GBGSV,7,2,25,34,,,41,59,,,41,11,,,41,3,,,41,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,40,16,,,40,1*42

$GBGSV,7,4,25,1,,,38,10,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,37,9,,,36,2,,,36,24,,,35,1*7D

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,34,33,,,32,1*74

$GBGSV,7,7,25,28,,,31,1*79

$GBRMC,153031.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153031.501,0.000,787.909,787.909,720.561,2097152,2097152,2097152*67

[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = ***************
[D][05:18:30][FCTY]HardwareID  = 867222087620300
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D6D223929953
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 7

2025-07-31 23:30:27:947 ==>> 100 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 159
[D][05:18:30][FCTY]Acckey1 vol = 5494 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3781 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:30:28:113 ==>> [D][05:18:30][COMM]msg 0601 loss. last_tick:36969. cur_tick:41971. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41972


2025-07-31 23:30:28:207 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:30:28:587 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = ***************
[D][05:18:31][FCTY]HardwareID  = 867222087620300
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D6D223929953
[D][05:18:31][FCTY]Bat         = 3604 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 7100 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 3, adc = 128
[D][05:18:31][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05

2025-07-31 23:30:28:633 ==>> :18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3781 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:30:28:737 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         N  ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 23:30:28:743 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:30:29:306 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       p. ret=0
[D][05:18:31][GNSS]all continue location stop
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][PROT]index:0
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]=======

2025-07-31 23:30:29:412 ==>> ====================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900005]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][PROT]index:0 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900005]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock sen

2025-07-31 23:30:29:518 ==>> d ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][CAT1]gsm read msg sub id: 24
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:31][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]tx ret[12] >>> AT+GPSDR=0

[W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = ***************
[D][05:18:31][FCTY]HardwareID  = 867222087620300
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D6D223929953
[D][05:18:31][FCTY]Bat         = 3584 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D

2025-07-31 23:30:29:623 ==>> ][05:18:31][FCTY]VBUS        = 5000 mv
[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 24, ret: 6
[D][05:18:31][CAT1]sub id: 24, ret: 6

[D][05:18:31][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 105
[D][05:18:31][FCTY]Acckey1 vol = 5493 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3781 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B53E12361AAF890671B07B67144E52519896CC4B6E66F00A1EBCC7B4E91AA4996E439212C7D7CAAD2F0ADA51488A7730B03CA3B0552FEF4DC02CA85FBC3BACD919ECDBD7FDA04E87B2BA47F3E3BAA216C7446A
[D][05:18:31][CA

2025-07-31 23:30:29:698 ==>> T1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][GNSS]recv submsg id[1]
[D][05:18:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:31][GNSS]location stop evt done evt
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 23:30:29:774 ==>> [D][05:18:32][COMM]S->M yaw:INVALID


2025-07-31 23:30:29:792 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:30:29:822 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:30:29:830 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:30:29:926 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:30:30:016 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31


2025-07-31 23:30:30:076 ==>>                                   c:255


2025-07-31 23:30:30:103 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:30:30:108 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:30:30:125 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:30:30:227 ==>> 5A A5 04 5A A5 


2025-07-31 23:30:30:331 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:30:30:469 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:30:30:475 ==>> 检测【打开WIFI(3)】
2025-07-31 23:30:30:487 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:30:30:651 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:30:30:789 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:30:30:798 ==>> 检测【扩展芯片hw】
2025-07-31 23:30:30:803 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:30:30:819 ==>> [D][05:18:33][COMM]M->S yaw:INVALID


2025-07-31 23:30:31:028 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:30:31:061 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:30:31:066 ==>> 检测【扩展芯片boot】
2025-07-31 23:30:31:086 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:30:31:094 ==>> 检测【扩展芯片sw】
2025-07-31 23:30:31:111 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:30:31:116 ==>> 检测【检测音频FLASH】
2025-07-31 23:30:31:150 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:30:31:317 ==>> [D][05:18:34][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:30:31:608 ==>> +WIFISCAN:4,0,F42A7D1297A3,-71
+WIFISCAN:4,1,CC057790A5C1,-80
+WIFISCAN:4,2,CC057790A5C0,-80
+WIFISCAN:4,3,CC057790A821,-81

[D][05:18:34][CAT1]wifi scan report total[4]
[D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:30:32:317 ==>>                                                                                                                                               MM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34

2025-07-31 23:30:32:423 ==>> ][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/

2025-07-31 23:30:32:529 ==>> 48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove suc

2025-07-31 23:30:32:634 ==>> cess[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_sta

2025-07-31 23:30:32:739 ==>> rt].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:

2025-07-31 23:30:32:800 ==>> 18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][GNSS]recv submsg id[3]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255


2025-07-31 23:30:34:390 ==>> [D][05:18:36][COMM]read battery soc:255
[D][05:18:37][PROT]CLEAN,SEND:0
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88

2025-07-31 23:30:34:465 ==>> B3237796C6332840360A415228799D8489D50686B56BB35F7C81B41056E1DA873D4A0A0F6916E50C2E8DA410CFB7C7D5C76EA71EA6715031D431E1AF3DB14C359C217AEB38C30DB84CB474409C260A4A680BF5
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]
[D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:30:34:694 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:30:35:405 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 23:30:35:819 ==>> [D][05:18:38][COMM]49567 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:30:36:094 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 23:30:36:172 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:30:36:180 ==>> 检测【打开喇叭声音】
2025-07-31 23:30:36:188 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:30:36:833 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[D][05:18:39][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:

2025-07-31 23:30:36:938 ==>> [691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05

2025-07-31 23:30:36:982 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:30:36:988 ==>> 检测【打开大灯控制】
2025-07-31 23:30:36:993 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:30:37:043 ==>> :18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]50578 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 23:30:37:133 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:30:37:257 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:30:37:266 ==>> 检测【关闭仪表供电3】
2025-07-31 23:30:37:289 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:30:37:407 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 23:30:37:531 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:30:37:537 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:30:37:545 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:30:37:713 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:30:37:811 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:30:37:817 ==>> 检测【读大灯电压】
2025-07-31 23:30:37:839 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:30:38:018 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[32737]


2025-07-31 23:30:38:082 ==>> 【读大灯电压】通过,【32737mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:30:38:087 ==>> 检测【关闭大灯控制2】
2025-07-31 23:30:38:097 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:30:38:112 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:30:38:291 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:30:38:355 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:30:38:361 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:30:38:370 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:30:38:520 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:30:38:656 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:30:38:662 ==>> 检测【打开WIFI(4)】
2025-07-31 23:30:38:672 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:30:38:842 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:30:38:981 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:30:38:987 ==>> 检测【EC800M模组版本】
2025-07-31 23:30:38:992 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:30:39:212 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:30:39:625 ==>> [D][05:18:42][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132
[D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][PROT]index:1 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:2
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1

2025-07-31 23:30:39:730 ==>> ], len[198], data[0x20052df8] format[0]
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B372522EABFAF58D483B38B210714AAC4210AFCE2FA11A427D9385E7057E685CC5FA666B6E1DA7A37202AA3D4E722A1A5FFE1BC4D858A35639DD20AEA1998182CE2059532EEE55F5703785DE7C575B824A0DB7
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]
+WIFISCAN:4,0,CC057790A7C1,-71
+WIFISCAN:4,1,CC057790A5C1,-76
+WIFISCAN:4,2,F86FB0660A82,-81
+WIFISCAN:4,3,CC057790A821,-81

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 23:30:39:811 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:30:39:818 ==>> 检测【配置蓝牙地址】
2025-07-31 23:30:39:825 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:30:39:990 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:30:40:020 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D6D223929953>】
2025-07-31 23:30:40:095 ==>> [D][05:18:42][GNSS]recv submsg id[3]
[D][05:18:42][COMM]read battery soc:255


2025-07-31 23:30:40:231 ==>> recv ble 1
recv ble 2
ble set mac ok :d6,d2,23,92,99,53
enable filters ret : 0

2025-07-31 23:30:40:291 ==>> [D][05:18:43][COMM]54158 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:30:40:297 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:30:40:310 ==>> 检测【BLETEST】
2025-07-31 23:30:40:315 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:30:40:427 ==>> 4A A4 01 A4 4A 


2025-07-31 23:30:40:532 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D6D223929953*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D6D22392995399999OVER 150


2025-07-31 23:30:41:309 ==>> [D][05:18:44][COMM]55171 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:30:41:328 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:30:41:333 ==>> 该项需要延时执行
2025-07-31 23:30:41:975 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:30:42:111 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:30:42:307 ==>> [D][05:18:45][COMM]56182 imu init OK


2025-07-31 23:30:44:103 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:30:44:835 ==>> [D][05:18:47][PROT]CLEAN,SEND:1
[D][05:18:47][PROT]index:1 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:5
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:1
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900006]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B320900F6D0D2115A72EEFC1F8CEE59CCBBF63BAF1340A64A09FF440E2508CE34B4A2386906172A092B5B63279E24AD8

2025-07-31 23:30:44:895 ==>> 09271B026F3C55A1F4390B50FB632F9F1F45CA4BD097007E8F2EAE605B1A15753D2108
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]


2025-07-31 23:30:46:127 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:30:48:129 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:30:50:047 ==>> [D][05:18:52][PROT]CLEAN,SEND:1
[D][05:18:52][PROT]CLEAN:1
[D][05:18:52][PROT]index:0 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:4
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900005]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][CAT1]Send Data To Server[198][201] ... ->:
0063B982113

2025-07-31 23:30:50:122 ==>> 311331133113311331B88B559DC02FF1D05664DA54B3857BD07F16DD144FEDA8D0CB3957D36DC3091379BC5CCF45CF8D1BA045AD7B5EE334CC72E91C5123F2D98C670E3CEF658B2B1810C2D65A180373767B9129E2C5867D59B5E21F0A4
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 23:30:50:152 ==>>                                          

2025-07-31 23:30:51:342 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:30:51:354 ==>> 检测【检测WiFi结果】
2025-07-31 23:30:51:364 ==>> WiFi信号:【F88C21BCF57D】,信号值:-37
2025-07-31 23:30:51:389 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 23:30:51:399 ==>> WiFi信号:【CC057790A5C0】,信号值:-82
2025-07-31 23:30:51:420 ==>> WiFi信号:【F86FB0660A82】,信号值:-83
2025-07-31 23:30:51:430 ==>> WiFi信号:【F42A7D1297A3】,信号值:-71
2025-07-31 23:30:51:440 ==>> WiFi信号:【CC057790A821】,信号值:-81
2025-07-31 23:30:51:451 ==>> WiFi信号:【CC057790A7C1】,信号值:-71
2025-07-31 23:30:51:460 ==>> WiFi数量【7】, 最大信号值:-37
2025-07-31 23:30:51:470 ==>> 检测【检测GPS结果】
2025-07-31 23:30:51:481 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:30:51:521 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 23:30:52:122 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 23:30:52:348 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:30:52:358 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:52:382 ==>> 定位已等待【1】秒.
2025-07-31 23:30:52:733 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:30:53:360 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:53:369 ==>> 定位已等待【2】秒.
2025-07-31 23:30:53:435 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:30:53:892 ==>> [D][05:18:56][COMM]S->M yaw:INVALID


2025-07-31 23:30:54:315 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:57][COMM]read battery soc:255
[D][05:18:57][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,43,39,,,42,60,,,40,23,,,39,1*7B

$GBGSV,2,2,08,41,,,37,34,,,30,25,,,43,10,,,38,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.211,1596.211,51.126,2097152,2097152,2097152*4E

[D][05:18:57][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:57][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:57][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 23, ret: 6
[D][05:18:57][CAT1]sub id: 23, ret: 6



2025-07-31 23:30:54:375 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:54:386 ==>> 定位已等待【3】秒.
2025-07-31 23:30:54:922 ==>> [D][05:18:57][COMM]M->S yaw:INVALID


2025-07-31 23:30:55:327 ==>> [D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:4
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900005]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[198]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B8

2025-07-31 23:30:55:387 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:55:397 ==>> 定位已等待【4】秒.
2025-07-31 23:30:55:432 ==>> 8B521893085C31687BD0A274CBA93ABC958C36132DFB1ECBA8BCCA3AEEF1EA584C40A89C5DB1E034DBCA76F5236B416DD63071F0557FB6585EE361303765530DC6524DD0FBADA6052CA474EAC7F70ACC2B35715
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][GNSS]recv submsg id[1]
[D][05:18:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,59,,,44,25,,,42,40,,,42,39,,,41,1*77

$GBGSV,4,2,15,3,,,41,60,,,40,23,,,39,16,,,39,1*46

$GBGSV,4,3,15,41,,,38,7,,,38,11,,,37,34,,,37,1*40

$GBGSV,4,4,15,10,,,34,4,,,33,43,,,42,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1613.912,1613.912,51.628,2097152,2097152,2097152*47



2025-07-31 23:30:56:268 ==>> [D][05:18:59][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,59,,,41,25,,,41,39,,,41,1*7D

$GBGSV,5,2,18,43,,,40,3,,,40,60,,,40,23,,,39,1*45

$GBGSV,5,3,18,16,,,39,7,,,39,11,,,39,41,,,38,1*4D

$GBGSV,5,4,18,34,,,38,1,,,37,10,,,36,5,,,35,1*70

$GBGSV,5,5,18,4,,,33,28,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1604.647,1604.647,51.300,2097152,2097152,2097152*48



2025-07-31 23:30:56:388 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:56:398 ==>> 定位已等待【5】秒.
2025-07-31 23:30:57:310 ==>> $GBGGA,153101.135,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,59,,,41,25,,,41,39,,,41,1*74

$GBGSV,6,2,21,43,,,41,3,,,40,60,,,40,11,,,40,1*42

$GBGSV,6,3,21,23,,,39,16,,,39,7,,,39,34,,,39,1*46

$GBGSV,6,4,21,41,,,38,1,,,38,10,,,37,2,,,35,1*72

$GBGSV,6,5,21,5,,,34,4,,,33,24,,,33,32,,,31,1*75

$GBGSV,6,6,21,6,,,48,1*4F

$GBRMC,153101.135,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153101.135,0.000,787.279,787.279,719.984,2097152,2097152,2097152*6B



2025-07-31 23:30:57:400 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:57:411 ==>> 定位已等待【6】秒.
2025-07-31 23:30:57:690 ==>> $GBGGA,153101.535,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,39,,,41,25,,,41,43,,,41,1*7C

$GBGSV,6,2,22,60,,,40,3,,,40,59,,,40,34,,,40,1*4C

$GBGSV,6,3,22,11,,,40,7,,,39,16,,,39,23,,,39,1*4C

$GBGSV,6,4,22,1,,,38,41,,,38,10,,,37,6,,,37,1*77

$GBGSV,6,5,22,2,,,35,9,,,34,5,,,33,24,,,33,1*4C

$GBGSV,6,6,22,4,,,33,32,,,31,1*41

$GBRMC,153101.535,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153101.535,0.000,781.544,781.544,714.740,2097152,2097152,2097152*64



2025-07-31 23:30:58:148 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 23:30:58:404 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:58:414 ==>> 定位已等待【7】秒.
2025-07-31 23:30:58:692 ==>> $GBGGA,153102.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,25,,,41,43,,,41,1*7A

$GBGSV,6,2,24,60,,,40,7,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,34,,,40,11,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,2,,,35,24,,,34,9,,,34,5,,,33,1*4D

$GBGSV,6,6,24,12,,,33,32,,,33,4,,,32,33,,,36,1*42

$GBRMC,153102.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153102.515,0.000,780.847,780.847,714.103,2097152,2097152,2097152*64



2025-07-31 23:30:59:410 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:30:59:421 ==>> 定位已等待【8】秒.
2025-07-31 23:30:59:687 ==>> $GBGGA,153103.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,25,,,41,43,,,41,1*7D

$GBGSV,6,2,23,7,,,40,60,,,40,3,,,40,59,,,40,1*7D

$GBGSV,6,3,23,34,,,40,11,,,40,16,,,39,23,,,39,1*73

$GBGSV,6,4,23,10,,,38,1,,,38,41,,,38,6,,,37,1*79

$GBGSV,6,5,23,2,,,35,9,,,35,24,,,34,32,,,34,1*78

$GBGSV,6,6,23,5,,,33,12,,,33,4,,,33,1*75

$GBRMC,153103.515,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153103.515,0.000,783.536,783.536,716.561,2097152,2097152,2097152*67



2025-07-31 23:31:00:170 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 23:31:00:412 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:00:424 ==>> 定位已等待【9】秒.
2025-07-31 23:31:00:517 ==>> [D][05:19:03][PROT]CLEAN,SEND:0
[D][05:19:03][PROT]CLEAN:0
[D][05:19:03][PROT]index:2 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:6
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:3
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][CAT1]Send Data To Server[134][137] ... ->:
0043B6831133113311

2025-07-31 23:31:00:592 ==>> 33113311331B88BE688897ADB43E6730D2143109D6DF9676F1B96C791F7F2E6F2713ABB49E86E71C22DA856F7656B93C22F4F6B04727015B2C77
[D][05:19:03][CAT1]<<< 
SEND OK

[D][05:19:03][CAT1]exec over: func id: 15, ret: 11
[D][05:19:03][CAT1]sub id: 15, ret: 11

[D][05:19:03][SAL ]Cellular task submsg id[68]
[D][05:19:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:03][M2M ]g_m2m_is_idle become true
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:03][PROT]M2M Send ok [1629955143]


2025-07-31 23:31:00:697 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:31:01:424 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:01:435 ==>> 定位已等待【10】秒.
2025-07-31 23:31:01:690 ==>> $GBGGA,153105.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,25,,,41,1*76

$GBGSV,6,2,23,43,,,41,7,,,40,3,,,40,60,,,40,1*77

$GBGSV,6,3,23,34,,,40,11,,,40,16,,,39,23,,,39,1*73

$GBGSV,6,4,23,10,,,38,1,,,38,41,,,38,6,,,37,1*79

$GBGSV,6,5,23,9,,,36,2,,,35,32,,,35,24,,,34,1*7A

$GBGSV,6,6,23,12,,,34,5,,,33,4,,,33,1*72

$GBRMC,153105.515,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,153105.515,0.000,787.128,787.128,719.845,2097152,2097152,2097152*65



2025-07-31 23:31:02:166 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 23:31:02:425 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:02:435 ==>> 定位已等待【11】秒.
2025-07-31 23:31:02:745 ==>> $GBGGA,153102.522,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,35,5,21,255,33,33,7,321,32,43,,,41,1*49

$GBGSV,6,6,24,34,,,40,11,,,40,32,,,35,12,,,34,1*74

$GBRMC,153102.522,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,153102.522,0.679,0.335,0.275,0.456,4.041,6.812,15*58



2025-07-31 23:31:03:429 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:03:439 ==>> 定位已等待【12】秒.
2025-07-31 23:31:03:736 ==>> $GBGGA,153103.502,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,40,25,51,27,41,1,48,125,38,2,45,236,35,1*41

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,32,41,30,238,38,1*4D

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,32,34,,,40,1*49

$GBGSV,6,6,24,11,,,40,43,,,40,32,,,35,12,,,34,1*74

$GBGSV,2,1,05,40,78,196,41,39,67,54,40,25,51,27,41,23,33,318,38,5*79

$GBGSV,2,2,05,41,30,238,37,5*4C

$GBRMC,153103.502,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,153103.502,2.568,0.744,0.582,0.989,3.399,4.869,10*52



2025-07-31 23:31:04:168 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 23:31:04:439 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:04:449 ==>> 定位已等待【13】秒.
2025-07-31 23:31:04:732 ==>> $GBGGA,153104.502,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,40,25,51,27,41,1,48,125,38,2,45,236,35,1*41

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,40,1*4A

$GBGSV,6,6,24,11,,,40,43,,,40,32,,,35,12,,,33,1*73

$GBGSV,2,1,05,40,78,196,41,39,67,54,41,25,51,27,41,23,33,318,38,5*78

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153104.502,V,,,,,,,310725,1.0,E,N,V*48

$GBVTG,0.00,T,,M,0.001,N,0.003,K,N*22

$GBGST,153104.502,2.155,0.375,0.306,0.507,2.717,3.825,8.305*74



2025-07-31 23:31:05:450 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:05:461 ==>> 定位已等待【14】秒.
2025-07-31 23:31:05:756 ==>> [D][05:19:08][PROT]CLEAN,SEND:2
[D][05:19:08][PROT]index:2 1629955148
[D][05:19:08][PROT]is_send:0
[D][05:19:08][PROT]sequence_num:6
[D][05:19:08][PROT]retry_timeout:0
[D][05:19:08][PROT]retry_times:2
[D][05:19:08][PROT]send_path:0x2
[D][05:19:08][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:08][PROT]===========================================================
[W][05:19:08][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955148]
[D][05:19:08][PROT]===========================================================
[D][05:19:08][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:08][PROT]sending traceid [9999999999900007]
[D][05:19:08][PROT]Send_TO_M2M [1629955148]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:08][SAL ]sock send credit cnt[6]
[D][05:19:08][SAL ]sock send ind credit cnt[6]
[D][05:19:08][M2M ]m2m send data len[134]
[D][05:19:08][SAL ]Cellular task submsg id[10]
[D][05:19:08][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:08][CAT1]gsm read msg sub id: 15
[D][05:19:08][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:08][CAT1]<<< 
ERROR

$GBGGA,153105.502,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,

2025-07-31 23:31:05:831 ==>> ,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153105.502,V,,,,,,,310725,1.0,E,N,V*49

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,153105.502,1.947,0.330,0.273,0.448,2.341,3.242,7.099*70



2025-07-31 23:31:06:176 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 23:31:06:451 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:06:461 ==>> 定位已等待【15】秒.
2025-07-31 23:31:06:725 ==>> $GBGGA,153106.502,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,40,25,51,27,41,1,48,125,37,2,45,236,35,1*4E

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153106.502,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.001,N,0.003,K,N*22

$GBGST,153106.502,1.885,0.325,0.269,0.442,2.140,2.905,6.314*74



2025-07-31 23:31:07:455 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:07:466 ==>> 定位已等待【16】秒.
2025-07-31 23:31:07:741 ==>> $GBGGA,153107.502,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,107,37,16,65,20,39,1*40

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,40,25,51,27,41,1,48,125,38,2,45,236,35,1*41

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,41,25,51,27,41,23,33,318,38,5*78

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153107.502,V,,,,,,,310725,1.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,153107.502,2.042,0.288,0.241,0.394,2.110,2.757,5.807*73

                                     

2025-07-31 23:31:08:181 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 23:31:08:469 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:08:482 ==>> 定位已等待【17】秒.
2025-07-31 23:31:08:732 ==>> $GBGGA,153108.502,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,39,23,33,318,39,4,32,111,33,41,30,238,38,1*42

$GBGSV,6,5,24,24,23,281,35,5,21,255,33,33,7,321,32,11,,,41,1*4E

$GBGSV,6,6,24,43,,,41,34,,,40,32,,,35,12,,,34,1*72

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153108.502,V,,,,,,,310725,1.0,E,N,V*44

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,153108.502,1.753,0.316,0.261,0.432,1.855,2.453,5.303*74



2025-07-31 23:31:08:792 ==>>                                      

2025-07-31 23:31:09:475 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:31:09:485 ==>> 定位已等待【18】秒.
2025-07-31 23:31:10:329 ==>> $GBGGA,153109.502,2301.2606363,N,11421.9391300,E,1,06,2.75,97.367,M,-1.770,M,,*52

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.56,2.75,4.83,4*0B

$GBGSV,7,1,25,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,7,2,25,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,7,3,25,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,7,4,25,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,7,5,25,24,23,281,34,5,21,255,33,33,7,321,31,22,7,161,38,1*43

$GBGSV,7,6,25,11,,,41,43,,,41,34,,,40,32,,,35,1*73

$GBGSV,7,7,25,12,,,33,1*72

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153109.502,A,2301.2606363,N,11421.9391300,E,0.001,0.00,310725,,,A,U*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:19:12][GNSS]HD8040 GPS
[D][05:19:12][GNSS]GPS diff_sec 124020717, report 0x42 frame
$GBGST,153109.502,1.912,0.297,0.249,0.405,1.884,2.406,5.021*77

[D][05:19:12][COMM]Main Task receive event:131
[D][05:19:12][COMM]index:0,power_mode:0xFF
[D][05:19:12][COMM]index:1,sound_mode:0xFF
[D][05:19:12][COMM]index:2,gsensor_mode:0xFF
[D][05:19:12][COMM]index:3,report_freq_mode:0xFF
[D][05:19:12][COMM

2025-07-31 23:31:10:434 ==>> ]index:4,report_period:0xFF
[D][05:19:12][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:12][COMM]index:6,normal_reset_period:0xFF
[D][05:19:12][COMM]index:7,spock_over_speed:0xFF
[D][05:19:12][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:12][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:12][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:12][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:12][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:12][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:12][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:12][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:12][COMM]index:16,imu_config_params:0xFF
[D][05:19:12][COMM]index:17,long_connect_params:0xFF
[D][05:19:12][COMM]index:18,detain_mark:0xFF
[D][05:19:12][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:12][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:12][COMM]index:21,mc_mode:0xFF
[D][05:19:12][COMM]index:22,S_mode:0xFF
[D][05:19:12][COMM]index:23,overweight:0xFF
[D][05:19:12][COMM]index:24,standstill_mode:0xFF
[D][05:19:12][COMM]index:25,night_mode:0xFF
[D][05:19:12][COMM]index:26,experiment1:0xFF
[D][05:19:12][COMM]index:27,experiment2:0xFF
[D][05:19:12][C

2025-07-31 23:31:10:479 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 23:31:10:486 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【39】,信号值:【42】
北斗星号:【6】,信号值:【37】
北斗星号:【16】,信号值:【39】
北斗星号:【7】,信号值:【40】
北斗星号:【3】,信号值:【40】
北斗星号:【9】,信号值:【36】
北斗星号:【10】,信号值:【38】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【35】
北斗星号:【60】,信号值:【40】
北斗星号:【23】,信号值:【38】
北斗星号:【41】,信号值:【38】
北斗星号:【22】,信号值:【38】
北斗星号:【11】,信号值:【41】
北斗星号:【43】,信号值:【41】
北斗星号:【34】,信号值:【40】
北斗星号:【32】,信号值:【35】

2025-07-31 23:31:10:494 ==>> 检测【CSQ强度】
2025-07-31 23:31:10:507 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:31:10:539 ==>> OMM]index:28,experiment3:0xFF
[D][05:19:12][COMM]index:29,experiment4:0xFF
[D][05:19:12][COMM]index:30,night_mode_start:0xFF
[D][05:19:12][COMM]index:31,night_mode_end:0xFF
[D][05:19:12][COMM]index:33,park_report_minutes:0xFF
[D][05:19:12][COMM]index:34,park_report_mode:0xFF
[D][05:19:12][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:12][COMM]index:38,charge_battery_para: FF
[D][05:19:12][COMM]index:39,multirider_mode:0xFF
[D][05:19:12][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:12][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:12][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:12][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:12][COMM]index:44,riding_duration_config:0xFF
[D][05:19:12][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:12][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:12][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:12][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:12][COMM]index:49,mc_load_startup:0xFF
[D][05:19:12][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:12][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:12][COMM]index:52,traffic_mode:0xFF
[D][05:19:12][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05

2025-07-31 23:31:10:645 ==>> :19:12][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:12][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:12][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:12][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:12][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:12][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:12][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:12][COMM]index:63,experiment5:0xFF
[D][05:19:12][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:12][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:12][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:12][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:12][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:12][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:12][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:12][COMM]index:72,experiment6:0xFF
[D][05:19:12][COMM]index:73,experiment7:0xFF
[D][05:19:12][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:12][COMM]index:75,zero_value_from_server:-1
[D][05:19:12][COMM]index:76,multirider_threshold:255
[D][05:19:12][COMM]index:77,experiment8:255
[D][05:19:12][COMM]index:78,temp_park_audio_play_

2025-07-31 23:31:10:749 ==>> duration:255
[D][05:19:12][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:12][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:12][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:12][COMM]index:83,loc_report_interval:255
[D][05:19:12][COMM]index:84,multirider_threshold_p2:255
[D][05:19:12][COMM]index:85,multirider_strategy:255
[D][05:19:12][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:12][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:12][COMM]index:90,weight_param:0xFF
[D][05:19:12][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:12][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:12][COMM]index:95,current_limit:0xFF
[D][05:19:12][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:12][COMM]index:100,location_mode:0xFF

[W][05:19:12][PROT]remove success[1629955152],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:12][PROT]add success [1629955152],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:12][COMM]Main Task receive event:131 finished processing
[D][05:19:12][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:12][M2M ]m2m_task: gpc:[0]

2025-07-31 23:31:10:854 ==>> ,gpo:[1]
$GBGGA,153110.002,2301.2606085,N,11421.9391160,E,1,06,2.75,97.514,M,-1.770,M,,*52

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.56,2.75,4.83,4*0B

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,37,2,45,236,35,1*4F

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,43,,,41,1*4B

$GBGSV,6,6,24,34,,,40,11,,,40,32,,,35,12,,,33,1*73

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153110.002,A,2301.2606085,N,11421.9391160,E,0.001,0.00,310725,,,A,U*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,153110.002,1.954,0.392,0.320,0.528,1.859,2.329,4.769*7C

[D][05:19:13][COMM]read battery soc:255


2025-07-31 23:31:10:944 ==>> [D][05:19:13][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:13][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:31:11:263 ==>> $GBGGA,153111.000,2301.2606034,N,11421.9391295,E,1,06,2.74,97.432,M,-1.770,M,,*56

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.56,2.74,4.83,4*0A

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,40,25,51,27,41,1,48,125,38,2,45,236,35,1*41

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,33,1*72

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153111.000,A,2301.2606034,N,11421.9391295,E,0.002,0.00,310725,,,A,U*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,153111.000,2.039,0.255,0.217,0.350,1.868,2.291,4.577*75



2025-07-31 23:31:12:256 ==>> $GBGGA,153112.000,2301.2605775,N,11421.9392009,E,1,06,2.74,97.219,M,-1.770,M,,*5F

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.55,2.74,4.83,4*09

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,33,41,30,238,38,1*4C

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153112.000,A,2301.2605775,N,11421.9392009,E,0.000,0.00,310725,,,A,U*39

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,153112.000,1.964,0.276,0.233,0.378,1.788,2.183,4.367*7F

[D][05:19:15][COMM]read battery soc:255


2025-07-31 23:31:12:550 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:31:12:991 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:31:13:264 ==>> $GBGGA,153113.000,2301.2605374,N,11421.9392530,E,1,06,2.74,97.023,M,-1.770,M,,*5F

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.55,2.74,4.83,4*09

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,40,9,53,333,36,10,52,244,38,1*4F

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,34,41,30,238,38,1*4B

$GBGSV,6,5,24,24,23,281,34,5,21,255,33,33,7,321,31,34,,,41,1*4B

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153113.000,A,2301.2605374,N,11421.9392530,E,0.000,0.00,310725,,,A,U*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,153113.000,2.089,0.269,0.227,0.369,1.840,2.198,4.261*7A



2025-07-31 23:31:14:280 ==>> $GBGGA,153114.000,2301.2605177,N,11421.9393098,E,1,06,2.74,96.833,M,-1.770,M,,*57

$GBGSA,A,3,40,39,16,25,23,41,,,,,,,5.55,2.74,4.82,4*08

$GBGSV,6,1,24,40,78,196,42,39,67,54,41,6,65,108,37,16,65,20,39,1*4F

$GBGSV,6,2,24,7,64,252,40,3,60,190,41,9,53,333,36,10,52,244,38,1*4E

$GBGSV,6,3,24,59,52,129,41,25,51,27,41,1,48,125,38,2,45,236,35,1*40

$GBGSV,6,4,24,60,41,239,40,23,33,318,39,4,32,111,34,41,30,238,38,1*4B

$GBGSV,6,5,24,24,23,281,35,5,21,255,33,33,7,321,32,34,,,41,1*49

$GBGSV,6,6,24,43,,,41,11,,,40,32,,,35,12,,,34,1*75

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153114.000,A,2301.2605177,N,11421.9393098,E,0.000,0.00,310725,,,A,U*32

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,153114.000,2.072,0.216,0.186,0.302,1.807,2.141,4.121*74

[D][05:19:17][COMM]read battery soc:255


2025-07-31 23:31:14:310 ==>>                                                                    

2025-07-31 23:31:14:616 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:31:15:024 ==>> [W][05:19:17][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:31:15:279 ==>> $GBGGA,153115.000,2301.2604676,N,11421.9393042,E,1,08,2.46,96.816,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,16,10,25,23,41,,,,,4.65,2.46,3.94,4*0D

$GBGSV,6,1,24,40,78,196,42,7,71,223,40,39,67,54,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,39,3,60,190,40,10,58,225,38,11,54,108,40,1*7F

$GBGSV,6,3,24,9,53,333,36,59,52,129,41,43,51,158,41,25,51,27,41,1*71

$GBGSV,6,4,24,1,48,125,38,34,45,70,41,2,45,236,35,60,41,239,40,1*4B

$GBGSV,6,5,24,23,33,318,39,4,32,111,34,41,30,238,38,24,23,281,34,1*4E

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,35,12,,,34,1*72

$GBGSV,2,1,05,40,78,196,41,39,67,54,42,25,51,27,41,23,33,318,38,5*7B

$GBGSV,2,2,05,41,30,238,38,5*43

$GBRMC,153115.000,A,2301.2604676,N,11421.9393042,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,153115.000,1.679,0.222,0.197,0.303,1.529,1.861,3.817*7F



2025-07-31 23:31:15:763 ==>> [D][05:19:18][CAT1]exec over: func id: 15, ret: -93
[D][05:19:18][CAT1]sub id: 15, ret: -93

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:18][SAL ]socket send fail. id[4]
[D][05:19:18][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:18][M2M ]m2m select fd[4]
[D][05:19:18][M2M ]socket[4] Link is disconnected
[D][05:19:18][M2M ]tcpclient close[4]
[D][05:19:18][SAL ]socket[4] has closed
[D][05:19:18][PROT]protocol read data ok
[E][05:19:18][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:18][PROT]M2M Send Fail [1629955158]
[D][05:19:18][PROT]CLEAN,SEND:2
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:18][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 21
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 21

2025-07-31 23:31:15:823 ==>> 
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 21
[D][05:19:18][CAT1]gsm read msg sub id: 10
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:31:15:991 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 23:31:15:998 ==>> 检测【关闭GSM联网】
2025-07-31 23:31:16:005 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:31:16:139 ==>> [D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 10, ret: 6
[D][05:19:18][CAT1]sub id: 10, ret: 6

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:18][M2M ]m2m gsm shut done, ret[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][CAT1]gsm read msg sub id: 8
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:31:16:244 ==>> [D][05:19:19][COMM]read battery soc:255
[W][05:19:19][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:19][COMM]GSM test
[D][05:19:19][COMM]GSM test disable


2025-07-31 23:31:16:484 ==>> [D][05:19:19][CAT1]pdpdeact urc len[22]
$GBGGA,153116.000,2301.2600556,N,11421.9399945,E,1,11,1.13,94.497,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,42,7,71,223,40,39,67,54,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,38,3,60,190,40,10,58,225,38,11,54,108,40,1*7E

$GBGSV,6,3,24,9,53,333,36,59,52,129,41,43,51,158,41,25,51,27,41,1*71

$GBGSV,6,4,24,1,48,125,38,34,45,70,40,2,45,236,35,60,41,239,40,1*4A

$GBGSV,6,5,24,23,33,318,39,4,32,111,34,41,30,238,38,24,23,281,34,1*4E

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,35,12,,,34,1*72

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,39,25,51,27,41,5*7C

$GBGSV,2,2,07,34,45,70,39,23,33,318,38,41,30,238,38,5*7A

$GBRMC,153116.000,A,2301.2600556,N,11421.9399945,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,153116.000,3.015,0.206,0.205,0.317,2.291,2.495,4.058*7E



2025-07-31 23:31:16:518 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:31:16:525 ==>> 检测【4G联网测试】
2025-07-31 23:31:16:549 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:31:16:589 ==>> [D][05:19:19][COMM]S->M yaw:INVALID


2025-07-31 23:31:16:694 ==>> [W][05:19:19][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:31:17:495 ==>> [D][05:19:19][COMM]Main Task receive event:14
[D][05:19:19][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955159, allstateRepSeconds = 0
[D][05:19:19][COMM]index:0,power_mode:0xFF
[D][05:19:19][COMM]index:1,sound_mode:0xFF
[D][05:19:19][COMM]index:2,gsensor_mode:0xFF
[D][05:19:19][COMM]index:3,report_freq_mode:0xFF
[D][05:19:19][COMM]index:4,report_period:0xFF
[D][05:19:19][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:19][COMM]index:6,normal_reset_period:0xFF
[D][05:19:19][COMM]index:7,spock_over_speed:0xFF
[D][05:19:19][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:19][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:19][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:19][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:19][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:19][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:19][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:19][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:19][COMM]index:16,imu_config_params:0xFF
[D][05:19:19][COMM]index:17,long_connect_params:0xFF
[D][05:19:19][COMM]index:18,detain_mark:0xFF
[D][05:19:19][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:19][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:19][COMM]index:21,mc_mode:0xFF
[D][05

2025-07-31 23:31:17:600 ==>> :19:19][COMM]index:22,S_mode:0xFF
[D][05:19:19][COMM]index:23,overweight:0xFF
[D][05:19:19][COMM]index:24,standstill_mode:0xFF
[D][05:19:19][COMM]index:25,night_mode:0xFF
[D][05:19:19][COMM]index:26,experiment1:0xFF
[D][05:19:19][COMM]index:27,experiment2:0xFF
[D][05:19:19][COMM]index:28,experiment3:0xFF
[D][05:19:19][COMM]index:29,experiment4:0xFF
[D][05:19:19][COMM]index:30,night_mode_start:0xFF
[D][05:19:19][COMM]index:31,night_mode_end:0xFF
[D][05:19:19][COMM]index:33,park_report_minutes:0xFF
[D][05:19:19][COMM]index:34,park_report_mode:0xFF
[D][05:19:19][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:19][COMM]index:38,charge_battery_para: FF
[D][05:19:19][COMM]index:39,multirider_mode:0xFF
[D][05:19:19][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:19][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:19][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:19][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:19][COMM]index:44,riding_duration_config:0xFF
[D][05:19:19][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:19][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:19][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:19][COMM]index:48,shlm

2025-07-31 23:31:17:705 ==>> t_sensor_en:0xFF
[D][05:19:19][COMM]index:49,mc_load_startup:0xFF
[D][05:19:19][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:19][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:19][COMM]index:52,traffic_mode:0xFF
[D][05:19:19][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:19][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:19][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:19][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:19][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:19][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:19][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:19][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:19][COMM]index:63,experiment5:0xFF
[D][05:19:19][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:19][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:19][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:19][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:19][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:19][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:19][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:19][COMM]index:72,experiment6:0xFF
[D][05:19:19][COMM]index:73,

2025-07-31 23:31:17:810 ==>> experiment7:0xFF
[D][05:19:19][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:19][COMM]index:75,zero_value_from_server:-1
[D][05:19:19][COMM]index:76,multirider_threshold:255
[D][05:19:19][COMM]index:77,experiment8:255
[D][05:19:19][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:19][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:19][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:19][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:19][COMM]index:83,loc_report_interval:255
[D][05:19:19][COMM]index:84,multirider_threshold_p2:255
[D][05:19:19][COMM]index:85,multirider_strategy:255
[D][05:19:19][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:19][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:19][COMM]index:90,weight_param:0xFF
[D][05:19:19][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:19][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:19][COMM]index:95,current_limit:0xFF
[D][05:19:19][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:19][COMM]index:100,location_mode:0xFF

[W][05:19:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[

2025-07-31 23:31:17:915 ==>> 0],used[0]
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,153117.000,2301.2597238,N,11421.9405085,E,1,11,1.13,92.765,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,42,7,71,223,40,39,67,54,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,38,3,60,190,40,10,58,225,38,11,54,108,40,1*7E

$GBGSV,6,3,24,9,53,333,36,59,52,129,40,43,51,158,40,25,51,27,41,1*71

$GBGSV,6,4,24,1,48,125,37,34,45,70,40,2,45,236,35,60,41,239,40,1*45

$GBGSV,6,5,24,23,33,318,39,4,32,111,33,41,30,238,38,24,23,281,34,1*49

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,34,12,,,33,1*74

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,40,25,51,27,41,5*72

$GBGSV,2,2,07,34,45,70,40,23,33,318,38,41,30,238,38,5*74

$GBRMC,153117.000,A,2301.2597238,N,11421.9405085,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,153117.000,2.601,0.182,0.181,0.277,2.020,2.187,3.640*7C

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:20][C

2025-07-31 23:31:18:020 ==>> AT1]<<< 
+CGATT: 1

OK

[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][CAT1]<<< 
+CSQ: 19,99

OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 8, ret: 6
[D][05:19:20][CAT1]gsm read msg sub id: 13
[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][CAT1]<<< 
+CSQ: 19,99

OK

[D][05:19:20][CAT1]exec over: func id: 13, ret: 21
[D][05:19:20][M2M ]get csq[19]
                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 23:31:18:125 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 23:31:18:230 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:31:18:335 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 23:31:18:365 ==>>                                                                                                        

2025-07-31 23:31:19:289 ==>> $GBGGA,153119.000,2301.2592517,N,11421.9412915,E,1,11,1.13,90.368,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,41,7,71,223,39,39,67,54,40,6,65,108,37,1*49

$GBGSV,6,2,24,16,65,20,38,3,60,190,40,10,58,225,37,11,54,108,40,1*71

$GBGSV,6,3,24,9,53,333,35,59,52,129,40,43,51,158,40,25,51,27,40,1*73

$GBGSV,6,4,24,1,48,125,38,34,45,70,40,2,45,236,35,60,41,239,39,1*44

$GBGSV,6,5,24,23,33,318,38,4,32,111,33,41,30,238,37,24,23,281,34,1*47

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,34,12,,,33,1*74

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,40,25,51,27,41,5*72

$GBGSV,2,2,07,34,45,70,40,23,33,318,38,41,30,238,38,5*74

$GBRMC,153119.000,A,2301.2592517,N,11421.9412915,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,153119.000,2.048,0.188,0.187,0.289,1.619,1.733,3.021*70



2025-07-31 23:31:19:654 ==>> [D][05:19:22][COMM]S->M yaw:INVALID


2025-07-31 23:31:20:306 ==>> $GBGGA,153120.000,2301.2591083,N,11421.9415220,E,1,11,1.13,89.655,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,41,7,71,223,39,39,67,54,40,6,65,108,37,1*49

$GBGSV,6,2,24,16,65,20,38,3,60,190,40,10,58,225,38,11,54,108,40,1*7E

$GBGSV,6,3,24,9,53,333,35,59,52,129,40,43,51,158,40,25,51,27,40,1*73

$GBGSV,6,4,24,1,48,125,38,34,45,70,40,2,45,236,35,60,41,239,39,1*44

$GBGSV,6,5,24,23,33,318,39,4,32,111,33,41,30,238,37,24,23,281,34,1*46

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,34,12,,,33,1*74

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,40,25,51,27,41,5*72

$GBGSV,2,2,07,34,45,70,40,23,33,318,38,41,30,238,38,5*74

$GBRMC,153120.000,A,2301.2591083,N,11421.9415220,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,153120.000,2.210,0.210,0.209,0.323,1.707,1.800,2.991*71

[D][05:19:23][COMM]read battery soc:255


2025-07-31 23:31:20:685 ==>> [D][05:19:23][COMM]M->S yaw:INVALID


2025-07-31 23:31:21:277 ==>> $GBGGA,153121.000,2301.2589867,N,11421.9417358,E,1,11,1.13,88.936,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,42,7,71,223,40,39,67,54,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,38,3,60,190,40,10,58,225,38,11,54,108,40,1*7E

$GBGSV,6,3,24,9,53,333,36,59,52,129,41,43,51,158,40,25,51,27,41,1*70

$GBGSV,6,4,24,1,48,125,38,34,45,70,40,2,45,236,35,60,41,239,39,1*44

$GBGSV,6,5,24,23,34,318,39,4,32,111,33,41,30,238,38,24,23,281,34,1*4E

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,12,,,34,32,,,34,1*73

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,40,25,51,27,41,5*72

$GBGSV,2,2,07,34,45,70,41,23,34,318,38,41,30,238,38,5*72

$GBRMC,153121.000,A,2301.2589867,N,11421.9417358,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,153121.000,2.119,0.195,0.194,0.298,1.635,1.713,2.850*73



2025-07-31 23:31:22:299 ==>> $GBGGA,153122.000,2301.2588657,N,11421.9419398,E,1,11,1.13,88.250,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,196,42,7,71,223,40,39,67,54,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,39,3,60,190,41,10,58,225,38,11,54,108,40,1*7E

$GBGSV,6,3,24,9,53,333,36,59,52,129,40,43,51,158,41,25,51,27,41,1*70

$GBGSV,6,4,24,1,48,125,38,34,45,70,41,2,45,236,35,60,41,239,39,1*45

$GBGSV,6,5,24,23,34,318,39,4,32,111,34,41,30,238,38,24,23,281,35,1*48

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,35,12,,,34,1*72

$GBGSV,2,1,07,40,78,196,41,39,67,54,42,43,51,158,40,25,51,27,41,5*72

$GBGSV,2,2,07,34,45,70,40,23,34,318,38,41,30,238,38,5*73

$GBRMC,153122.000,A,2301.2588657,N,11421.9419398,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,153122.000,1.900,0.216,0.214,0.332,1.477,1.545,2.653*79

[D][05:19:25][COMM]read battery soc:255


2025-07-31 23:31:22:554 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:31:22:705 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:31:23:588 ==>>                     ain Task receive event:14
[D][05:19:25][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955165, allstateRepSeconds = 0
[D][05:19:25][COMM]index:0,power_mode:0xFF
[D][05:19:25][COMM]index:1,sound_mode:0xFF
[D][05:19:25][COMM]index:2,gsensor_mode:0xFF
[D][05:19:25][COMM]index:3,report_freq_mode:0xFF
[D][05:19:25][COMM]index:4,report_period:0xFF
[D][05:19:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:25][COMM]index:6,normal_reset_period:0xFF
[D][05:19:25][COMM]index:7,spock_over_speed:0xFF
[D][05:19:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:25][COMM]index:16,imu_config_params:0xFF
[D][05:19:25][COMM]index:17,long_connect_params:0xFF
[D][05:19:25][COMM]index:18,detain_mark:0xFF
[D][05:19:25][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:25][COMM]

2025-07-31 23:31:23:693 ==>> index:20,lock_pos_report_interval:0xFF
[D][05:19:25][COMM]index:21,mc_mode:0xFF
[D][05:19:25][COMM]index:22,S_mode:0xFF
[D][05:19:25][COMM]index:23,overweight:0xFF
[D][05:19:25][COMM]index:24,standstill_mode:0xFF
[D][05:19:25][COMM]index:25,night_mode:0xFF
[D][05:19:25][COMM]index:26,experiment1:0xFF
[D][05:19:25][COMM]index:27,experiment2:0xFF
[D][05:19:25][COMM]index:28,experiment3:0xFF
[D][05:19:25][COMM]index:29,experiment4:0xFF
[D][05:19:25][COMM]index:30,night_mode_start:0xFF
[D][05:19:25][COMM]index:31,night_mode_end:0xFF
[D][05:19:25][COMM]index:33,park_report_minutes:0xFF
[D][05:19:25][COMM]index:34,park_report_mode:0xFF
[D][05:19:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:25][COMM]index:38,charge_battery_para: FF
[D][05:19:25][COMM]index:39,multirider_mode:0xFF
[D][05:19:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:25][COMM]index:44,riding_duration_config:0xFF
[D][05:19:25][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:25][COMM]index:46,camera_park_type_cfg:0x

2025-07-31 23:31:23:798 ==>> FF
[D][05:19:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:25][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:25][COMM]index:49,mc_load_startup:0xFF
[D][05:19:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:25][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:25][COMM]index:52,traffic_mode:0xFF
[D][05:19:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:25][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:25][COMM]index:63,experiment5:0xFF
[D][05:19:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:25][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:25][COMM]index:71,camera_park_self_che

2025-07-31 23:31:23:903 ==>> ck_cfg:0xFF
[D][05:19:25][COMM]index:72,experiment6:0xFF
[D][05:19:25][COMM]index:73,experiment7:0xFF
[D][05:19:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:25][COMM]index:75,zero_value_from_server:-1
[D][05:19:25][COMM]index:76,multirider_threshold:255
[D][05:19:25][COMM]index:77,experiment8:255
[D][05:19:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:25][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:25][COMM]index:83,loc_report_interval:255
[D][05:19:25][COMM]index:84,multirider_threshold_p2:255
[D][05:19:25][COMM]index:85,multirider_strategy:255
[D][05:19:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:25][COMM]index:90,weight_param:0xFF
[D][05:19:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:25][COMM]index:95,current_limit:0xFF
[D][05:19:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:25][COMM]index:100,location_mode:0xFF

[W][05:

2025-07-31 23:31:24:008 ==>> 19:25][PROT]remove success[1629955165],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:25][PROT]index:0 1629955165
[D][05:19:25][PROT]is_send:0
[D][05:19:25][PROT]sequence_num:10
[D][05:19:25][PROT]retry_timeout:0
[D][05:19:25][PROT]retry_times:1
[D][05:19:25][PROT]send_path:0x2
[D][05:19:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:25][PROT]===========================================================
[W][05:19:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955165]
[D][05:19:25][PROT]===========================================================
[D][05:19:25][PROT]sending traceid [999999999990000B]
[D][05:19:25][PROT]Send_TO_M2M [1629955165]
[D][05:19:25][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:25][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:25][SAL ]sock send credit cnt[6]
[D][05:19:25][SAL ]sock send ind credit cnt[6]
[D][05:19:25][M2M ]m2m send data len[294]
[D][05:19:25][SAL ]Cellular task submsg id[10]
[D][05:19:25][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd8] format[0]
[W][05:19:25][PROT]add success [1629955165],send_path[2],type[4205],priorit

2025-07-31 23:31:24:113 ==>> y[0],index[0],used[1]
[D][05:19:25][CAT1]gsm read msg sub id: 15
[D][05:19:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:25][CAT1]Send Data To Server[294][294] ... ->:
0093B989113311331133113311331B88BDD948F7D050182149EF86F8BF66D8E29BA66F0417B87284C44AC1B453D998CE9B7A253727F78F8A9F04E6646188AE1751C65CE63A60DFF92E51792E0C51F2559016103A6C0C67988877F2B898CA40DC337B5C450F99530DF924B93173C21D3A08F0DEED570EF639F6367B768034B6F0ACB099AB5EC8A712B48A592E8894CC79B3A991
[D][05:19:25][CAT1]<<< 
SEND OK

[D][05:19:25][CAT1]exec over: func id: 15, ret: 11
[D][05:19:25][CAT1]sub id: 15, ret: 11

[D][05:19:25][SAL ]Cellular task submsg id[68]
[D][05:19:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:25][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:25][CAT1]gsm read msg sub id: 13
[D][05:19:25][M2M ]g_m2m_is_idle become true
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:25][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:25][PROT]M2M Send ok [1629955165]
[D][05:19:25][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:25][CAT1]exec over: func id: 13, ret: 21
[D][05:19:25][M2M ]get csq[20]
$GBGGA,153123.000

2025-07-31 23:31:24:218 ==>> ,2301.2587922,N,11421.9420473,E,1,11,1.13,87.907,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,16,10,11,43,25,34,23,41,,2.57,1.13,2.31,4*07

$GBGSV,6,1,24,40,78,197,42,7,71,223,40,39,67,55,41,6,65,108,37,1*45

$GBGSV,6,2,24,16,65,20,39,3,60,190,40,10,58,225,38,11,54,108,40,1*7F

$GBGSV,6,3,24,9,53,333,36,59,52,129,41,43,51,158,41,25,51,28,41,1*7E

$GBGSV,6,4,24,1,48,125,38,34,45,70,41,2,45,236,35,60,41,239,40,1*4B

$GBGSV,6,5,24,23,34,318,39,4,32,111,33,41,30,238,38,24,23,281,35,1*4F

$GBGSV,6,6,24,5,21,255,33,33,7,321,31,32,,,35,12,,,34,1*72

$GBGSV,2,1,07,40,78,197,41,39,67,55,42,43,51,158,41,25,51,28,41,5*7C

$GBGSV,2,2,07,34,45,70,40,23,34,318,38,41,30,238,38,5*73

$GBRMC,153123.000,A,2301.2587922,N,11421.9420473,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,153123.000,1.792,0.270,0.268,0.414,1.390,1.452,2.529*72

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:26][PROT]remove success[1629955166],s

2025-07-31 23:31:24:324 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:31:24:580 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:31:24:594 ==>> 检测【关闭GPS】
2025-07-31 23:31:24:601 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:31:24:987 ==>> [W][05:19:27][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:27][GNSS]stop locating
[D][05:19:27][GNSS]stop event:8
[D][05:19:27][GNSS]GPS stop. ret=0
[D][05:19:27][GNSS]all continue location stop
[W][05:19:27][GNSS]stop locating
[D][05:19:27][GNSS]all sing location stop
[D][05:19:27][CAT1]gsm read msg sub id: 24
[D][05:19:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]exec over: func id: 24, ret: 6
[D][05:19:27][CAT1]sub id: 24, ret: 6



2025-07-31 23:31:25:109 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:31:25:122 ==>> 检测【清空消息队列2】
2025-07-31 23:31:25:145 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:31:25:335 ==>> [D][05:19:28][GNSS]recv submsg id[1]
[D][05:19:28][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:28][GNSS]location stop evt done evt
[D][05:19:28][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:28][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:28][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:31:25:384 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:31:25:392 ==>> 检测【轮动检测】
2025-07-31 23:31:25:405 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:31:25:531 ==>> 3A A3 01 00 A3 


2025-07-31 23:31:25:622 ==>> OFF_OUT1
OVER 150


2025-07-31 23:31:25:697 ==>> [D][05:19:28][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:31:25:896 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:31:26:022 ==>> 3A A3 01 01 A3 


2025-07-31 23:31:26:127 ==>> ON_OUT1
OVER 150


2025-07-31 23:31:26:176 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:31:26:189 ==>> 检测【关闭小电池】
2025-07-31 23:31:26:207 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:31:26:231 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:31:26:448 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:31:26:474 ==>> 检测【进入休眠模式】
2025-07-31 23:31:26:505 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:31:26:521 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:

2025-07-31 23:31:26:595 ==>> 100013. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100019. period:10000. j,i:0 53
[D][05:1

2025-07-31 23:31:26:700 ==>> 9:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100019. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100019. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100020. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100020. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100020. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100021. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100021. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100021
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100022
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100022
[D][05:19:29][COMM]read battery soc:255


2025-07-31 23:31:26:776 ==>> [W][05:19:29][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:31:26:882 ==>> [D][05:19:29][COMM]Main Task receive event:28
[D][05:19:29][COMM]main task tmp_sleep_event = 8
[D][05:19:29][COMM]prepare to sleep
[D][05:19:29][CAT1]gsm read msg sub id: 12
[D][05:19:29][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:31:27:675 ==>> [D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 6
[D][05:19:30][M2M ]tcpclient close[4]
[D][05:19:30][SAL ]Cellular task submsg id[12]
[D][05:19:30][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:30][CAT1]gsm read msg sub id: 9
[D][05:19:30][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 9, ret: 6
[D][05:19:30][CAT1]sub id: 9, ret: 6

[D][05:19:30][SAL ]Cellular task submsg id[68]
[D][05:19:30][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:30][SAL ]socket close ind. id[4]
[D][05:19:30][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][CAT1]pdpdeact urc len[22]


2025-07-31 23:31:27:945 ==>> [E][05:19:30][COMM]1x1 rx timeout
[D][05:19:30][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:31:28:248 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 23:31:28:474 ==>> [E][05:19:31][COMM]1x1 rx timeout
[E][05:19:31][COMM]1x1 tp timeout
[E][05:19:31][COMM]1x1 error -3.
[W][05:19:31][COMM]CAN STOP!
[D][05:19:31][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:31][COMM]------------ready to Power off Acckey 1------------
[D][05:19:31][COMM]------------ready to Power off Acckey 2------------
[D][05:19:31][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:31][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1280
[D][05:19:31][COMM]bat sleep fail, reason:-1
[D][05:19:31][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:31][COMM]accel parse set 0
[D][05:19:31][COMM]imu rest ok. 102231
[D][05:19:31][COMM]imu sleep 0
[W][05:19:31][COMM]now sleep


2025-07-31 23:31:28:525 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:31:28:532 ==>> 检测【检测33V休眠电流】
2025-07-31 23:31:28:540 ==>> 开始33V电流采样
2025-07-31 23:31:28:553 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:28:627 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:31:29:634 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:31:29:665 ==>> Current33V:????:15.46

2025-07-31 23:31:30:140 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:30:148 ==>> 【检测33V休眠电流】通过,【15.46uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:31:30:161 ==>> 该项需要延时执行
2025-07-31 23:31:32:157 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:31:32:166 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:31:32:193 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:31:32:235 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1673mV
Get AD_V4 0mV
Get AD_V5 2746mV
Get AD_V6 2021mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:31:33:195 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:31:33:203 ==>> 检测【打开小电池2】
2025-07-31 23:31:33:211 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:31:33:335 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:31:33:479 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:31:33:487 ==>> 该项需要延时执行
2025-07-31 23:31:33:982 ==>> 此处延时了:【500】毫秒
2025-07-31 23:31:34:001 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:31:34:019 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:31:34:132 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:31:34:259 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:31:34:268 ==>> 该项需要延时执行
2025-07-31 23:31:34:767 ==>> 此处延时了:【500】毫秒
2025-07-31 23:31:34:780 ==>> 检测【进入休眠模式2】
2025-07-31 23:31:34:804 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:31:34:827 ==>> [D][05:19:37][COMM]------------ready to Power on Acckey 1------------
[D][05:19:37][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:37][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:37][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:37][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:37][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:37][COMM]----- get Acckey 1 and value:1------------
[W][05:19:37][COMM]CAN START!
[D][05:19:37][CAT1]gsm read msg sub id: 12
[D][05:19:37][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:37][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 108524
[D][05:19:37][COMM][Audio]exec status ready.
[D][05:19:37][CAT1]

2025-07-31 23:31:34:887 ==>> <<< 
OK

[D][05:19:37][CAT1]exec over: func id: 12, ret: 6
[D][05:19:37][COMM]imu wakeup ok. 108538
[D][05:19:37][COMM]imu wakeup 1
[W][05:19:37][COMM]wake up system, wakeupEvt=0x80
[D][05:19:37][COMM]frm_can_weigth_power_set 1
[D][05:19:37][COMM]Clear Sleep Block Evt
[D][05:19:37][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:37][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:31:34:977 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:31:35:067 ==>> [E][05:19:37][COMM]1x1 rx timeout
[D][05:19:37][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:31:35:172 ==>> [D][05:19:38][COMM]msg 02A0 loss. last_tick:108509. cur_tick:109018. period:50
[D][05:19:38][COMM]msg 02A4 loss

2025-07-31 23:31:35:232 ==>> . last_tick:108509. cur_tick:109018. period:50
[D][05:19:38][COMM]msg 02A5 loss. last_tick:108509. cur_tick:109018. period:50
[D][05:19:38][COMM]msg 02A6 loss. last_tick:108509. cur_tick:109019. period:50
[D][05:19:38][COMM]msg 02A7 loss. last_tick:108509. cur_tick:109019. period:50
[D][05:19:38][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 109020
[D][05:19:38][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 109020


2025-07-31 23:31:35:563 ==>> [E][05:19:38][COMM]1x1 rx timeout
[E][05:19:38][COMM]1x1 tp timeout
[E][05:19:38][COMM]1x1 error -3.
[D][05:19:38][COMM]Main Task receive event:28 finished processing
[D][05:19:38][COMM]Main Task receive event:28
[D][05:19:38][COMM]prepare to sleep
[D][05:19:38][CAT1]gsm read msg sub id: 12
[D][05:19:38][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]exec over: func id: 12, ret: 6
[D][05:19:38][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:38][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:31:35:866 ==>> [D][05:19:38][COMM]msg 0220 loss. last_tick:108509. cur_tick:109514. period:100
[D][05:19:38][COMM]msg 0221 loss. last_tick:108509. cur_tick:109515. period:100
[D][05:19:38][COMM]msg 0224 loss. last_tick:108509. cur_tick:109515. period:100
[D][05:19:38][COMM]msg 0260 loss. last_tick:108509. cur_tick:109515. period:100
[D][05:19:38][COMM]msg 0280 loss. last_tick:108509. cur_tick:109516. period:100
[D][05:19:38][COMM]msg 02C0 loss. last_tick:108509. cur_tick:109516. period:100
[D][05:19:38][COMM]msg 02C1 loss. last_tick:108509. cur_tick:109517. period:100
[D][05:19:38][COMM]msg 02C2 loss. last_tick:108509. cur_tick:109517. period:100
[D][05:19:38][COMM]msg 02E0 loss. last_tick:108509. cur_tick:109517. period:100
[D][05:19:38][COMM]msg 02E1 loss. last_tick:108509. cur_tick:109518. period:100
[D][05:19:38][COMM]msg 02E2 loss. last_tick:108509. cur_tick:109518. period:100
[D][05:19:38][COMM]msg 0300 loss. last_tick:108509. cur_tick:109518. period:100
[D][05:19:38][COMM]msg 0301 loss. last_tick:108509. cur_tick:109519. period:100
[D][05:19:38][COMM]bat msg 0240 loss. last_tick:108509. cur_tick:109519. period:100. j,i:1 54
[D][05:19:38][COMM]bat msg 0241 loss. last

2025-07-31 23:31:35:942 ==>> _tick:108509. cur_tick:109520. period:100. j,i:2 55
[D][05:19:38][COMM]bat msg 0242 loss. last_tick:108509. cur_tick:109520. period:100. j,i:3 56
[D][05:19:38][COMM]bat msg 0244 loss. last_tick:108509. cur_tick:109520. period:100. j,i:5 58
[D][05:19:38][COMM]bat msg 024E loss. last_tick:108509. cur_tick:109521. period:100. j,i:15 68
[D][05:19:38][COMM]bat msg 024F loss. last_tick:108509. cur_tick:109521. period:100. j,i:16 69
[D][05:19:38][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 109522
[D][05:19:38][COMM]CAN message bat fault change: 0x00000000->0x0001802E 109522
[D][05:19:38][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 109523


2025-07-31 23:31:35:972 ==>>                                                                               

2025-07-31 23:31:36:154 ==>> [D][05:19:39][COMM]msg 0222 loss. last_tick:108509. cur_tick:110016. period:150
[D][05:19:39][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 110017


2025-07-31 23:31:36:244 ==>>                                                                                                                                                :39][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:31:36:454 ==>> [E][05:19:39][COMM]1x1 rx timeout
[E][05:19:39][COMM]1x1 tp timeout
[E][05:19:39][COMM]1x1 error -3.
[W][05:19:39][COMM]CAN STOP!
[D][05:19:39][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:39][COMM]------------ready to Power off Acckey 1------------
[D][05:19:39][COMM]------------ready to Power off Acckey 2------------
[D][05:19:39][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:39][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 84
[D][05:19:39][COMM]bat sleep fail, reason:-1
[D][05:19:39][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:39][COMM]accel parse set 0
[D][05:19:39][COMM]imu rest ok. 110210
[D][05:19:39][COMM]imu sleep 0
[D][05:19:39][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:19:39][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:19:39][COMM]now sleep


2025-07-31 23:31:36:593 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:31:36:602 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:31:36:615 ==>> 开始小电池电流采样
2025-07-31 23:31:36:642 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:36:697 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:31:37:698 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:31:37:773 ==>> CurrentBattery:ƽ��:66.72

2025-07-31 23:31:38:208 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:38:217 ==>> 【检测小电池休眠电流】通过,【66.72uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:31:38:239 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:31:38:269 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:31:38:328 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:31:38:498 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:31:38:509 ==>> 该项需要延时执行
2025-07-31 23:31:38:569 ==>> [D][05:19:41][COMM]------------ready to Power on Acckey 1------------
[D][05:19:41][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:41][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:41][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 1
[D][05:19:41][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:41][COMM]----- get Acckey 1 and value:1------------
[W][05:19:41][COMM]CAN START!
[D][05:19:41][GNSS]handler GSMGet Base timeout
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:41][COMM]CAN message bat fault change: 0x0001802E->0x00000000 112291
[D][05:19:41][COMM][Audio]exec status ready.
[D][05:19:41][CAT1]<<< 
OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 6
[D][05:19:41][COMM]imu wakeup ok. 112305
[D][05:19:41][COMM]imu wakeup 1
[W][05:19:41][COMM]wake up system, wakeupEvt=0x80
[D][05:19:41][COMM]frm_can_weigth_power_set 1
[D][05:19:41][COMM]Clear Sleep Block Evt
[D][05:19:41][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:41][COMM]1x1 frm_can_tp_send ok
[D][05:19:41][COMM]read battery soc:0


2025-07-31 23:31:38:614 ==>>                                      

2025-07-31 23:31:38:826 ==>> [E][05:19:41][COMM]1x1 rx timeout
[D][05:19:41][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:31:38:931 ==>> [D][05:19:41][COMM]msg 02A0 loss. last_tick:112272. cur_tick:112786. period:50
[D

2025-07-31 23:31:38:991 ==>> ][05:19:41][COMM]msg 02A4 loss. last_tick:112272. cur_tick:112786. period:50
[D][05:19:41][COMM]msg 02A5 loss. last_tick:112272. cur_tick:112786. period:50
[D][05:19:41][COMM]msg 02A6 loss. last_tick:112272. cur_tick:112787. period:50
[D][05:19:41][COMM]msg 02A7 loss. last_tick:112272. cur_tick:112787. period:50
[D][05:19:41][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 112787
[D][05:19:41][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 112788


2025-07-31 23:31:39:006 ==>> 此处延时了:【500】毫秒
2025-07-31 23:31:39:019 ==>> 检测【检测唤醒】
2025-07-31 23:31:39:052 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:31:39:771 ==>> [W][05:19:42][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:42][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:42][FCTY]==========Modules-nRF5340 ==========
[D][05:19:42][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:42][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:42][FCTY]DeviceID    = ***************
[D][05:19:42][FCTY]HardwareID  = 867222087620300
[D][05:19:42][FCTY]MoBikeID    = 9999999999
[D][05:19:42][FCTY]LockID      = FFFFFFFFFF
[D][05:19:42][FCTY]BLEFWVersion= 105
[D][05:19:42][FCTY]BLEMacAddr   = D6D223929953
[D][05:19:42][FCTY]Bat         = 3864 mv
[D][05:19:42][FCTY]Current     = 0 ma
[D][05:19:42][FCTY]VBUS        = 2600 mv
[D][05:19:42][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:42][FCTY]Ext battery vol = 32, adc = 1276
[D][05:19:42][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 0 mv
[D][05:19:42][FCTY]Bike Type flag is invalied
[D][05:19:42][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:42][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:42][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:42][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:42][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:42][FCTY]CAT1_GNSS_VERSION 

2025-07-31 23:31:39:805 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:31:39:817 ==>> 检测【关机】
2025-07-31 23:31:39:848 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:31:39:877 ==>> = V3465b5b1
[D][05:19:42][FCTY]Bat1         = 3781 mv
[D][05:19:42][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:42][FCTY]==========Modules-nRF5340 ==========
[E][05:19:42][COMM]1x1 rx timeout
[E][05:19:42][COMM]1x1 tp timeout
[E][05:19:42][COMM]1x1 error -3.
[D][05:19:42][COMM]Main Task receive event:28 finished processing
[D][05:19:42][COMM]Main Task receive event:65
[D][05:19:42][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:42][COMM]Main Task receive event:65 finished processing
[D][05:19:42][COMM]Main Task receive event:60
[D][05:19:42][COMM]smart_helmet_vol=255,255
[D][05:19:42][COMM]report elecbike
[W][05:19:42][PROT]remove success[1629955182],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:42][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:42][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:42][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:42][PROT]index:0
[D][05:19:42][PROT]is_send:1
[D][05:19:42][PROT]sequence_num:12
[D][05:19:42][PROT]retry_timeout:0
[D][05:19:42][PROT]retry_times:3
[D][05:19:42][PROT]send_path:0x3
[D][05:19:42][PROT]msg_type:0x5d03
[D][05:19:42][PROT]=========

2025-07-31 23:31:39:981 ==>> ==================================================
[W][05:19:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955182]
[D][05:19:42][PROT]===========================================================
[D][05:19:42][PROT]Sending traceid[999999999990000D]
[D][05:19:42][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:42][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:42][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:42][PROT]add success [1629955182],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:42][COMM]Main Task receive event:60 finished processing
[D][05:19:42][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:42][SAL ]open socket ind id[4], rst[0]
[D][05:19:42][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:42][SAL ]Cellular task submsg id[8]
[D][05:19:42][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:42][CAT1]gsm read msg sub id:

2025-07-31 23:31:40:086 ==>>  8
[D][05:19:42][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:42][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:42][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:42][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:42][CAT1]<<< 
+CME ERROR: 100

[D][05:19:42][COMM]msg 0220 loss. last_tick:112272. cur_tick:113284. period:100
[D][05:19:42][COMM]msg 0221 loss. last_tick:112272. cur_tick:113284. period:100
[D][05:19:42][COMM]msg 0224 loss. last_tick:112272. cur_tick:113284. period:100
[D][05:19:42][COMM]msg 0260 loss. last_tick:112272. cur_tick:113285. period:100
[D][05:19:42][COMM]msg 0280 loss. last_tick:112272. cur_tick:113285. period:100
[D][05:19:42][COMM]msg 02C0 loss. last_tick:112272. cur_tick:113285. period:100
[D][05:19:42][COMM]msg 02C1 loss. last_tick:112272. cur_tick:113286. period:100
[D][05:19:42][COMM]msg 02C2 loss. last_tick:112272. cur_tick:113286. period:100
[D][05:19:42][COMM]msg 02E0 loss. last_tick:112272. cur_tick:113286. period:100
[D][05:19:42][COMM]msg 02E1 loss. last_tick:112272. cur_tick:113287. period:100
[D][05:19:42][COMM]msg 02E2 loss. last_tick:112272. cur_tick:113287. period:100
[D][05:19:42][COMM]msg 0300 loss. last_tick:112272. cur_tick:11328

2025-07-31 23:31:40:191 ==>> 7. period:100
[D][05:19:42][COMM]msg 0301 loss. last_tick:112272. cur_tick:113288. period:100
[D][05:19:42][COMM]bat msg 0240 loss. last_tick:112272. cur_tick:113288. period:100. j,i:1 54
[D][05:19:42][COMM]bat msg 0241 loss. last_tick:112272. cur_tick:113289. period:100. j,i:2 55
[D][05:19:42][COMM]bat msg 0242 loss. last_tick:112272. cur_tick:113289. period:100. j,i:3 56
[D][05:19:42][COMM]bat msg 0244 loss. last_tick:112272. cur_tick:113289. period:100. j,i:5 58
[D][05:19:42][COMM]bat msg 024E loss. last_tick:112272. cur_tick:113290. period:100. j,i:15 68
[D][05:19:42][COMM]bat msg 024F loss. last_tick:112272. cur_tick:113290. period:100. j,i:16 69
[D][05:19:42][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 113291
[D][05:19:42][COMM]CAN message bat fault change: 0x00000000->0x0001802E 113291
[D][05:19:42][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 113291


2025-07-31 23:31:40:796 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:31:40:826 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:31:40:901 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:31:41:006 ==>>                                                                                                                                                                                                                                                                                                                                                1, type:0x5D03, priority:4
[D][05:19:42][PROT]index:1
[D][05:19:42][PROT]is_send:1
[D][05:19:42][PROT]sequence_num:13
[D][05:19:42][PROT]retry_timeout:0
[D][05:19:42][PROT]retry_times:3
[D][05:19:42][PROT]send_path:0x3
[D][05:19:42][PROT]msg_type:0x5d03
[D][05:19:42][PROT]===========================================================
[D][05:19:42][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:42][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955182]
[D][05:19:42][PROT]===========================================================
[D][05:19:42][PROT]Sending traceid[999999999990000E]
[D][05:19:42][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:42][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:42][PROT]ble is not inited or not c

2025-07-31 23:31:41:111 ==>> onnected or cccd not enabled
[W][05:19:42][PROT]add success [1629955182],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:42][COMM]Main Task receive event:60 finished processing
[D][05:19:42][COMM]Main Task receive event:61
[D][05:19:42][COMM][D301]:type:3, trace id:280
[D][05:19:42][COMM]id[], hw[000
[D][05:19:42][COMM]get mcMaincircuitVolt error
[D][05:19:42][COMM]get mcSubcircuitVolt error
[D][05:19:42][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:42][COMM]BAT CAN get state1 Fail 204
[D][05:19:42][COMM]BAT CAN get soc Fail, 204
[D][05:19:42][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:42][COMM]BAT CAN get state2 fail 204
[D][05:19:42][COMM]get bat work mode err
[W][05:19:42][PROT]remove success[1629955182],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:42][PROT]add success [1629955182],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:42][COMM]Main Task receive event:61 finished processing
[D][05:19:42][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:42][M2M ]m2m_task: g

2025-07-31 23:31:41:216 ==>> pc:[0],gpo:[1]
[D][05:19:42][COMM]--->crc16:0xb8a
[D][05:19:42][COMM]read file success
[W][05:19:42][COMM][Audio].l:[936].close hexlog save
[D][05:19:42][COMM]accel parse set 1
[D][05:19:42][COMM][Audio]mon:9,05:19:42
[D][05:19:42][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:42][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:43][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:43][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:43][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:43][COMM]f:[ec800m_audio_sen

2025-07-31 23:31:41:321 ==>> d_hexdata_start].l:[756].recv >
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:43][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:43][COMM]arm_hub_enable: hub power: 0
[D][05:19:43][HSDK]hexlog index save 0 4096 139 @ 0 : 0
[D][05:19:43][HSDK]write save hexlog index [0]
[D][05:19:43][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:43][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:43][COMM]read battery soc:255
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend,

2025-07-31 23:31:41:381 ==>>  index:5, len:2048
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 23:31:41:441 ==>>                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 23:31:41:516 ==>>                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:31:41:849 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:31:42:059 ==>> [W][05:19:44][COMM]Power Off
[W][05:19:44][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:44][COMM]arm_hub_enable: hub power: 0
[D][05:19:44][HSDK]hexlog index save 0 4096 139 @ 0 : 0
[D][05:19:44][HSDK]write save hexlog index [0]
[D][05:19:44][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:44][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:31:42:150 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:31:42:164 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:31:42:186 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:31:42:225 ==>> 5A A5 02 5A A5 


2025-07-31 23:31:42:330 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:31:42:405 ==>> [D][05:19:45][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:45][FCTY]

2025-07-31 23:31:42:447 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:31:42:464 ==>> 检测【检测小电池关机电流】
2025-07-31 23:31:42:486 ==>> 开始小电池电流采样
2025-07-31 23:31:42:506 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:42:555 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:31:43:563 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:31:43:640 ==>> CurrentBattery:ƽ��:67.48

2025-07-31 23:31:44:073 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:31:44:081 ==>> 【检测小电池关机电流】通过,【67.48uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:31:44:428 ==>> MES过站成功
2025-07-31 23:31:44:437 ==>> #################### 【测试结束】 ####################
2025-07-31 23:31:44:484 ==>> 关闭5V供电
2025-07-31 23:31:44:499 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:31:44:630 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:31:45:486 ==>> 关闭5V供电成功
2025-07-31 23:31:45:502 ==>> 关闭33V供电
2025-07-31 23:31:45:524 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:31:45:636 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:31:46:494 ==>> 关闭33V供电成功
2025-07-31 23:31:46:509 ==>> 关闭3.7V供电
2025-07-31 23:31:46:534 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:31:46:629 ==>> 6A A6 02 A6 6A 


2025-07-31 23:31:46:734 ==>> Battery OFF
OVER 150


2025-07-31 23:31:47:504 ==>> 关闭3.7V供电成功
