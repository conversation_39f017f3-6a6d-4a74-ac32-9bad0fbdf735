2025-07-31 18:31:43:446 ==>> MES查站成功:
查站序号:P510001005312F16验证通过
2025-07-31 18:31:43:451 ==>> 扫码结果:P510001005312F16
2025-07-31 18:31:43:452 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:31:43:453 ==>> 测试参数版本:2024.10.11
2025-07-31 18:31:43:456 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:31:43:457 ==>> 检测【打开透传】
2025-07-31 18:31:43:459 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:31:43:590 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:31:43:844 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:31:43:877 ==>> 检测【检测接地电压】
2025-07-31 18:31:43:879 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:31:43:988 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:31:44:157 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:31:44:159 ==>> 检测【打开小电池】
2025-07-31 18:31:44:161 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:31:44:290 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:31:44:428 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:31:44:430 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:31:44:432 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:31:44:487 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:31:44:703 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:31:44:705 ==>> 检测【等待设备启动】
2025-07-31 18:31:44:709 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:31:45:035 ==>> 鹺 ?囫鵢W 燏 罫X0L頛} ??嶐狺O?衼#?濑X ?% 0/苦 羹 ???狺`?唈
9?鼍z?? !,j
慁痨??权?满 ???镥鸨翩 ?縓疝痨? ?

2025-07-31 18:31:45:231 ==>> 縵 鷠 囫??怔??&yy}L烲 纇?齈褳 >X?笻H?u?€辋z ??棘撖筷?

2025-07-31 18:31:45:730 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:31:45:895 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:31:45:940 ==>>                                                    

2025-07-31 18:31:46:340 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:31:46:772 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:31:46:817 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:31:47:083 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:31:47:086 ==>> 检测【产品通信】
2025-07-31 18:31:47:088 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:31:47:249 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 18:31:47:402 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:31:47:405 ==>> 检测【初始化完成检测】
2025-07-31 18:31:47:408 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:31:47:445 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:31:47:625 ==>> [D][05:17:51][HSDK]need to erase for write: is[0x0] ie[0x1E00]
[D][05:17:51][HSDK][0] flush to flash addr:[0xE42E00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:31:47:703 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:31:47:705 ==>> 检测【关闭大灯控制1】
2025-07-31 18:31:47:706 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:31:47:868 ==>> [D][05:17:51][COMM]2636 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:48:025 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:31:48:027 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:31:48:031 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:31:48:034 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:31:48:139 ==>> [W][05:17:5

2025-07-31 18:31:48:184 ==>> 1][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:31:48:333 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:31:48:336 ==>> 检测【关闭仪表供电】
2025-07-31 18:31:48:339 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:31:48:475 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:31:48:640 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:31:48:642 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:31:48:645 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:31:48:875 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3647 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:48:946 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:31:48:949 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:31:48:951 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:31:49:151 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:31:49:238 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:31:49:240 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:31:49:243 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:31:49:467 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE42F00] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:31:49:516 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:31:49:519 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:31:49:521 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:31:49:572 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:31:49:677 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 18:31:49:785 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:31:49:788 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:31:49:791 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:31:49:889 ==>> [D][05:17:53][COMM]4659 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 03 5A A5 


2025-07-31 18:31:49:979 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 18:31:50:057 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:31:50:059 ==>> 该项需要延时执行
2025-07-31 18:31:50:407 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5011. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5012
[D][05:17:54][COMM]CAN message bat fa

2025-07-31 18:31:50:436 ==>> ult change: 0x0001802E->0x01B987FE 5012


2025-07-31 18:31:50:679 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]


2025-07-31 18:31:50:908 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:51:243 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:31:51:762 ==>>                     rm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio

2025-07-31 18:31:51:867 ==>>  file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcirc

2025-07-31 18:31:51:972 ==>> uitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_servi

2025-07-31 18:31:52:047 ==>> ce_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 18:31:52:926 ==>> [D][05:17:56][COMM]7695 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:53:708 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 18:31:53:933 ==>> [D][05:17:57][COMM]8706 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:54:069 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:31:54:072 ==>> 检测【33V输入电压ADC】
2025-07-31 18:31:54:075 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:31:54:391 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:17:58][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:58][COMM]adc read throttle adc:9  volt:11 mv
[D][05:17:58][COMM]adc read battery ts volt:16 mv
[D][05:17:58][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:17:58][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:31:54:614 ==>> 【33V输入电压ADC】通过,【32703mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:31:54:619 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:31:54:637 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:31:54:696 ==>> 1A A1 00 00 FC 
Get AD_V2 1685mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1992mV
Get AD_V7 1085mV
OVER 150


2025-07-31 18:31:54:897 ==>> 【TP7_VCC3V3(ADV2)】通过,【1685mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:54:900 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:31:54:918 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:54:920 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:31:54:923 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:31:54:941 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:31:54:944 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:31:54:955 ==>> [D][05:17:58][COMM]9717 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:54:971 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:31:54:974 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:31:55:011 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:31:55:013 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:31:55:061 ==>> 1A A1 00 00 FC 
Get AD_V2 1684m

2025-07-31 18:31:55:091 ==>> V
Get AD_V3 1658mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1992mV
Get AD_V7 1085mV
OVER 150


2025-07-31 18:31:55:303 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10019


2025-07-31 18:31:55:334 ==>> 【TP7_VCC3V3(ADV2)】通过,【1684mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:55:337 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:31:55:364 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:55:367 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:31:55:372 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:31:55:392 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:31:55:394 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:31:55:432 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:31:55:435 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:31:55:473 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:31:55:475 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:31:55:588 ==>> 1A A1 00 00 FC 
Get AD_V2 1683mV
Get AD_V3 1659mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1994mV
Get AD_V7 1084mV
OVER 150


2025-07-31 18:31:55:679 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:31:55:766 ==>> 【TP7_VCC3V3(ADV2)】通过,【1683mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:55:770 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:31:55:813 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:31:55:815 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:31:55:819 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:31:55:859 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:31:55:861 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:31:55:894 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:31:55:897 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:31:55:932 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:31:55:935 ==>> 检测【打开WIFI(1)】
2025-07-31 18:31:55:954 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:31:55:957 ==>> [D][05:17:59][COMM]10729 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:31:56:057 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE43000] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:31:56:212 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:31:56:215 ==>> 检测【清空消息队列(1)】
2025-07-31 18:31:56:218 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:31:56:378 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:31:56:496 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:31:56:499 ==>> 检测【打开GPS(1)】
2025-07-31 18:31:56:504 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:31:56:944 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:

2025-07-31 18:31:56:989 ==>> 00][CAT1]gsm read msg sub id: 31
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 18:31:57:033 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:31:57:036 ==>> 检测【打开GSM联网】
2025-07-31 18:31:57:039 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:31:57:484 ==>>                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087504207

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
460130071536353

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:01][CAT1]<<<

2025-07-31 18:31:57:513 ==>>  
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:31:57:568 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:31:57:571 ==>> 检测【打开仪表供电1】
2025-07-31 18:31:57:573 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:31:57:835 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:31:58:093 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:31:58:098 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:31:58:102 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:31:58:557 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm re

2025-07-31 18:31:58:633 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:31:58:636 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:31:58:638 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:31:58:647 ==>> ad msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.231.128.190"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:31:58:752 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL

2025-07-31 18:31:58:797 ==>>  ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33201]


2025-07-31 18:31:58:906 ==>> 【读取主控ADC采集的仪表电压】通过,【33201mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:31:58:909 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:31:58:911 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:31:59:191 ==>> [D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][COMM]13740 imu init OK
[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:31:59:371 ==>> [D][05:18:03][COMM]S->M yaw:INVALID


2025-07-31 18:31:59:434 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:31:59:437 ==>> 检测【AD_V20电压】
2025-07-31 18:31:59:439 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:31:59:536 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:31:59:731 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE43100] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255


2025-07-31 18:31:59:896 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,38,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:31:59:926 ==>> 本次取值间隔时间:385ms
2025-07-31 18:32:00:204 ==>> 本次取值间隔时间:271ms
2025-07-31 18:32:00:392 ==>> 本次取值间隔时间:185ms
2025-07-31 18:32:00:548 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:32:00:683 ==>> 本次取值间隔时间:282ms
2025-07-31 18:32:00:687 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:32:00:743 ==>> [D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,24,,,43,26,,,41,16,,,39,21,,,39,1*71

$GBGSV,3,2,10,33,,,39,39,,,39,38,,,37,13,,,39,1*7B

$GBGSV,3,3,10,8,,,38,9,,,36,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1640.502,1640.502,52.418,2097152,2097152,2097152*45

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 18:32:00:788 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:32:00:818 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:32:00:878 ==>> 1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 18:32:00:953 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:32:01:029 ==>> 本次取值间隔时间:228ms
2025-07-31 18:32:01:109 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:32:01:112 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:32:01:114 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:32:01:182 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:32:01:411 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:32:01:414 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:32:01:417 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:32:01:766 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,43,60,,,42,26,,,41,13,,,41,1*7C

$GBGSV,5,2,18,59,,,41,3,,,41,21,,,40,8,,,40,1*7C

$GBGSV,5,3,18,1,,,40,2,,,40,16,,,39,39,,,39,1*77

$GBGSV,5,4,18,38,,,39,33,,,38,9,,,37,42,,,37,1*4B

$GBGSV,5,5,18,6,,,34,5,,,36,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1638.777,1638.777,52.379,2097152,2097152,2097152*45

[D][05:18:05][COMM]read battery soc:255


2025-07-31 18:32:02:445 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:32:02:739 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,60,,,41,26,,,41,13,,,41,1*74

$GBGSV,5,2,20,3,,,41,59,,,40,8,,,40,1,,,40,1*44

$GBGSV,5,3,20,38,,,40,42,,,40,21,,,39,16,,,39,1*7B

$GBGSV,5,4,20,39,,,39,33,,,38,2,,,37,9,,,37,1*75

$GBGSV,5,5,20,6,,,36,5,,,35,4,,,35,40,,,31,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1602.352,1602.352,51.247,2097152,2097152,2097152*4A

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:32:03:466 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:32:03:793 ==>> $GBGGA,103207.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,60,,,41,26,,,41,13,,,41,1*76

$GBGSV,6,2,21,3,,,41,42,,,41,59,,,40,8,,,40,1*70

$GBGSV,6,3,21,38,,,40,21,,,40,39,,,40,1,,,39,1*4D

$GBGSV,6,4,21,16,,,39,33,,,38,2,,,37,9,,,37,1*7A

$GBGSV,6,5,21,6,,,37,14,,,36,5,,,35,4,,,34,1*44

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,103207.505,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103207.505,0.000,1601.073,1601.073,51.210,2097152,2097152,2097152*51

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:07][COMM]read battery soc:255


2025-07-31 18:32:04:491 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:32:04:496 ==>> #################### 【测试结束】 ####################
2025-07-31 18:32:04:532 ==>> 关闭5V供电
2025-07-31 18:32:04:536 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:32:04:583 ==>> 5A A5 04 5A A5 


2025-07-31 18:32:04:689 ==>> CLOSE_POWER_OUT2
OVER 150
$GBGGA,103208.505,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,60,,,41,26,,,41,13,,,41,1*75

$GBGSV,6,2,22,3,,,41,42,,,41,59,,,40,8,,,40,1*73

$GBGSV,6,3,22,38,,,40,21,,,40,39,,,40,1,,,39,1*4E

$GBGSV,6,4,22,16,,,39,33,,,38,9,,,37,6,,,37,1*7D

$GBGSV,6,5,22,14,,,37,2,,,36,5,,,35,4,,,

2025-07-31 18:32:04:733 ==>> 34,1*43

$GBGSV,6,6,22,7,,,32,40,,,30,1*47

$GBRMC,103208.505,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103208.505,0.000,1586.735,1586.735,50.771,2097152,2097152,2097152*5D



2025-07-31 18:32:05:544 ==>> 关闭5V供电成功
2025-07-31 18:32:05:548 ==>> 关闭33V供电
2025-07-31 18:32:05:552 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:32:05:683 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:32:05:788 ==>> $GBGGA,103209.505,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,42,,,42,60

2025-07-31 18:32:05:893 ==>> ,,,41,1*77

$GBGSV,6,2,22,26,,,41,3,,,41,59,,,41,38,,,41,1*42

$GBGSV,6,3,22,8,,,40,21,,,40,39,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,33,,,38,9,,,38,14,,,38,1*4E

$GBGSV,6,5,22,6,,,37,2,,,36,5,,,35,4,,,34,1*70

$GBGSV,6,6,22,7,,,32,40,,,31,1*46

$GBRMC,103209.505,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103209.505,0.000,1599.926,1599.926,51.192,2097152,2097152,2097152*56

[D][05:18:09][COMM]read battery soc:255
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 2,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 3,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 4,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 5,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 6,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 7,volt = 19
[D][05:18:09][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:32:06:092 ==>> [D][05:18:09][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:32:06:557 ==>> 关闭33V供电成功
2025-07-31 18:32:06:561 ==>> 关闭3.7V供电
2025-07-31 18:32:06:565 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:32:06:725 ==>> $GBGGA,103210.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,42,,,42,60,,,41,1*77

$GBGSV,6,2,22,26,,,41,3,,,41,38,,,41,59,,,40,1*43

$GBGSV,6,3,22,8,,,40,21,,,40,39,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,33,,,38,14,,,38,6,,,38,1*41

$GBGSV,6,5,22,9,,,37,2,,,36,5,,,35,4,,,34,1*7F

$GBGSV,6,6,22,7,,,32,40,,,31,1*46

$GBRMC,103210.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103210.505,0.000,1598.040,1598.040,51.131,2097152,2097152,2097152*57

6A A6 02 A6 6A 


2025-07-31 18:32:06:785 ==>> Battery OFF
OVER 150


2025-07-31 18:32:07:373 ==>>  

