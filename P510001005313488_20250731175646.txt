2025-07-31 17:56:46:201 ==>> MES查站成功:
查站序号:P510001005313488验证通过
2025-07-31 17:56:46:204 ==>> 扫码结果:P510001005313488
2025-07-31 17:56:46:206 ==>> 当前测试项目:SE51_PCBA
2025-07-31 17:56:46:208 ==>> 测试参数版本:2024.10.11
2025-07-31 17:56:46:209 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 17:56:46:211 ==>> 检测【打开透传】
2025-07-31 17:56:46:213 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 17:56:46:339 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 17:56:46:561 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 17:56:46:564 ==>> 检测【检测接地电压】
2025-07-31 17:56:46:566 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 17:56:46:648 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 17:56:46:847 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 17:56:46:849 ==>> 检测【打开小电池】
2025-07-31 17:56:46:851 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 17:56:46:949 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 17:56:47:121 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 17:56:47:123 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 17:56:47:127 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 17:56:47:253 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 17:56:47:391 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 17:56:47:394 ==>> 检测【等待设备启动】
2025-07-31 17:56:47:396 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:47:700 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 17:56:47:897 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 17:56:48:434 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:48:526 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 17:56:48:586 ==>>                                  PS Will Not Open


2025-07-31 17:56:48:981 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 17:56:49:460 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 17:56:49:493 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 17:56:49:495 ==>> 检测【产品通信】
2025-07-31 17:56:49:496 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 17:56:49:610 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 17:56:49:764 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 17:56:49:766 ==>> 检测【初始化完成检测】
2025-07-31 17:56:49:769 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 17:56:49:977 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 17:56:50:040 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 17:56:50:043 ==>> 检测【关闭大灯控制1】
2025-07-31 17:56:50:044 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 17:56:50:083 ==>> [D][05:17:51][COMM][LedDisplay]LED r

2025-07-31 17:56:50:112 ==>> un over,op:0xc63,cnt:15


2025-07-31 17:56:50:202 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 17:56:50:310 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 17:56:50:339 ==>> 检测【打开仪表指令模式1】
2025-07-31 17:56:50:341 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 17:56:50:569 ==>> [D][05:17:51][COMM]2626 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:50:674 ==>>                                                                                                                                                                                                                                                                                            x[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 17:56:50:842 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 17:56:50:844 ==>> 检测【关闭仪表供电】
2025-07-31 17:56:50:846 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 17:56:51:037 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 17:56:51:115 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 17:56:51:117 ==>> 检测【关闭AccKey2供电1】
2025-07-31 17:56:51:119 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 17:56:51:311 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 17:56:51:398 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 17:56:51:401 ==>> 检测【关闭AccKey1供电1】
2025-07-31 17:56:51:404 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 17:56:51:525 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:51:600 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 17:56:51:679 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 17:56:51:682 ==>> 检测【关闭转刹把供电1】
2025-07-31 17:56:51:685 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:56:51:827 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 17:56:51:950 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 17:56:51:953 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 17:56:51:954 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 17:56:52:043 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 17:56:52:133 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
[D][05:17:53][COMM]read battery soc:255


2025-07-31 17:56:52:226 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 17:56:52:228 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 17:56:52:230 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 17:56:52:346 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 17:56:52:499 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 17:56:52:502 ==>> 该项需要延时执行
2025-07-31 17:56:52:543 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:53:085 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]

2025-07-31 17:56:53:116 ==>> CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 17:56:53:565 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:53:670 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17

2025-07-31 17:56:53:715 ==>> :54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 17:56:54:200 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54

2025-07-31 17:56:54:305 ==>> ][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][

2025-07-31 17:56:54:410 ==>> 05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_ge

2025-07-31 17:56:54:485 ==>> t_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 17:56:54:575 ==>> [D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:54:680 ==>> [D][05:17:55][CAT1]

2025-07-31 17:56:54:710 ==>> power_urc_cb ret[5]


2025-07-31 17:56:55:595 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:56:120 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 17:56:56:503 ==>> 此处延时了:【4000】毫秒
2025-07-31 17:56:56:506 ==>> 检测【33V输入电压ADC】
2025-07-31 17:56:56:508 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:56:56:595 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:56:850 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:17:57][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2386  volt:3844 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 17:56:57:034 ==>> 【33V输入电压ADC】通过,【32754mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 17:56:57:037 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 17:56:57:039 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:56:57:154 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 17:56:57:358 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:57:361 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:56:57:406 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:57:427 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:56:57:428 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 17:56:57:433 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:56:57:435 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:56:57:452 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:56:57:454 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:56:57:476 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:56:57:478 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:56:57:550 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1668mV
Get AD_V4 2mV
Get AD_V5 2762mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 17:56:57:610 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:56:57:753 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:57:755 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:56:57:773 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:57:775 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:56:57:777 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 17:56:57:809 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:56:57:811 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:56:57:826 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:56:57:828 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:56:57:848 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:56:57:850 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:56:57:976 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021
1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1669mV
Get AD_V4 0mV
Get AD_V5 2761mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 17:56:58:127 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 17:56:58:137 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:58:142 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:56:58:156 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:56:58:161 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:56:58:165 ==>> 原始值:【2761】, 乘以分压基数【2】还原值:【5522】
2025-07-31 17:56:58:174 ==>> 【TP68_VCC5V5(ADV5)】通过,【5522mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:56:58:177 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:56:58:193 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:56:58:198 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:56:58:221 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:56:58:223 ==>> 检测【打开WIFI(1)】
2025-07-31 17:56:58:225 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 17:56:58:411 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 17:56:58:504 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 17:56:58:507 ==>> 检测【清空消息队列(1)】
2025-07-31 17:56:58:509 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 17:56:58:853 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][HSDK][0

2025-07-31 17:56:58:897 ==>> ] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 17:56:59:035 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 17:56:59:039 ==>> 检测【打开GPS(1)】
2025-07-31 17:56:59:041 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 17:56:59:302 ==>>                                                                                                                                                                                                                                                                              ][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087782647

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539041

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start eve

2025-07-31 17:56:59:332 ==>> nt:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 17:56:59:563 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 17:56:59:568 ==>> 检测【打开GSM联网】
2025-07-31 17:56:59:573 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 17:56:59:605 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 17:56:59:710 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 17:56:59:833 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 17:56:59:837 ==>> 检测【打开仪表供电1】
2025-07-31 17:56:59:840 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 17:57:00:043 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 17:57:00:108 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 17:57:00:114 ==>> 检测【打开仪表指令模式2】
2025-07-31 17:57:00:119 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 17:57:00:238 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 17:57:00:343 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 17:57:00:376 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 17:57:00:390 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 17:57:00:394 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 17:57:00:539 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33108]


2025-07-31 17:57:00:648 ==>> 【读取主控ADC采集的仪表电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 17:57:00:651 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 17:57:00:653 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:00:839 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:57:00:916 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 17:57:00:920 ==>> 检测【AD_V20电压】
2025-07-31 17:57:00:923 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:01:020 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:01:140 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:01:155 ==>> 本次取值间隔时间:128ms
2025-07-31 17:57:01:174 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:01:275 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:01:352 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:01:502 ==>> 本次取值间隔时间:226ms
2025-07-31 17:57:01:522 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:01:563 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 17:57:01:608 ==>> [D][05:18:02][COMM]13730 imu init OK


2025-07-31 17:57:01:623 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:01:714 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:1

2025-07-31 17:57:01:744 ==>> 36, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:01:774 ==>> 本次取值间隔时间:138ms
2025-07-31 17:57:01:792 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:01:893 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:01:953 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:02:058 ==>> 本次取值间隔时间:163ms
2025-07-31 17:57:02:074 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:02:182 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:02:245 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:02:562 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub i

2025-07-31 17:57:02:607 ==>> 本次取值间隔时间:413ms
2025-07-31 17:57:02:626 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:02:667 ==>> d: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F38383F3531333007

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.130.14.143"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 17:57:02:727 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:02:893 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               [D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 17:57:02:923 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:03:029 ==>> 本次取值间隔时间:288ms
2025-07-31 17:57:03:047 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:03:154 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:03:245 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:03:504 ==>> 本次取值间隔时间:345ms
2025-07-31 17:57:03:523 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:03:550 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 17:57:03:624 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:03:745 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:57:04:004 ==>> 本次取值间隔时间:373ms
2025-07-31 17:57:04:023 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:57:04:124 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:57:04:249 ==>> 1A A1 10 00 00 
Get AD_V20 1660mV
OVER 150


2025-07-31 17:57:04:354 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[D][05:18:05][CAT1]<<< 
OK

[W][05:18:05][COMM]>>>>>Inpu

2025-07-31 17:57:04:414 ==>> 本次取值间隔时间:278ms
2025-07-31 17:57:04:433 ==>> 【AD_V20电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:57:04:437 ==>> 检测【拉低OUTPUT2】
2025-07-31 17:57:04:440 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 17:57:04:459 ==>> t command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,21,,,43,24,,,43,38,,,42,26,,,40,1*7F

$GBGSV,3,2,12,39,,,39,42,,,36,9,,,44,60,,,42,1*4E

[D][05:18:05][CAT1]<<< 
OK

$GBGSV,3,3,12,14,,,39,59,,,39,13,,,38,16,,,37,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1679.034,1679.034,53.683,2097152,2097152,2097152*44

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 17:57:04:549 ==>> 3A A3 02 00 A3 


2025-07-31 17:57:04:609 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 17:57:04:639 ==>> OFF_OUT2
OVER 150


2025-07-31 17:57:04:707 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 17:57:04:717 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 17:57:04:720 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 17:57:04:944 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:57:04:977 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 17:57:04:980 ==>> 检测【拉高OUTPUT2】
2025-07-31 17:57:04:982 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 17:57:05:049 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 17:57:05:251 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 17:57:05:255 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 17:57:05:266 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 17:57:05:371 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,43,21,,,42,38,,,42,13,,,42,1*79

$GBGSV,4,2,16,60,,,41,26,,,41,8,,,41,39,,,40,1*46

$GBGSV,4,3,16,16,,,40,59,,,39,42,,,39,1,,,39,1*44

$GBGSV,4,4,16,9,,,38,14,,,37,3,,,43,2,,,36,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1670.109,1670.109,53.358,2097152,2097152,2097152*47



2025-07-31 17:57:05:476 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:57:05:525 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 17:57:05:529 ==>> 检测【预留IO LED功能输出】
2025-07-31 17:57:05:531 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 17:57:05:746 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:57:05:808 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 17:57:05:811 ==>> 检测【AD_V21电压】
2025-07-31 17:57:05:813 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 17:57:05:944 ==>> 1A A1 20 00 00 
Get AD_V21 1058mV
OVER 150


2025-07-31 17:57:06:202 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 17:57:06:247 ==>> 本次取值间隔时间:430ms
2025-07-31 17:57:06:265 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 17:57:06:382 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,38,,,43,21,,,42,13,,,42,1*76

$GBGSV,5,2,19,26,,,42,3,,,41,60,,,41,8,,,41,1*73

$GBGSV,5,3,19,42,,,41,39,,,40,16,,,40,59,,,40,1*7E

$GBGSV,5,4,19,1,,,39,9,,,38,14,,,37,2,,,36,1*40

$GBGSV,5,5,19,4,,,34,5,,,33,33,,,39,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1642.202,1642.202,52.525,2097152,2097152,2097152*4A

1A A1 20 00 00 
Get AD_V21 1655mV
OVER 150


2025-07-31 17:57:06:397 ==>> 本次取值间隔时间:126ms
2025-07-31 17:57:06:415 ==>> 【AD_V21电压】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:57:06:418 ==>> 检测【关闭仪表供电2】
2025-07-31 17:57:06:421 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 17:57:06:641 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 17:57:06:690 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 17:57:06:695 ==>> 检测【关闭仪表指令模式】
2025-07-31 17:57:06:700 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 17:57:06:836 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 17:57:06:971 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 17:57:06:974 ==>> 检测【打开AccKey2供电】
2025-07-31 17:57:06:977 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 17:57:07:111 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 17:57:07:307 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 17:57:07:310 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 17:57:07:313 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:07:400 ==>> $GBGGA,095711.205,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,43,21,,,42,13,,,42,1*7E

$GBGSV,6,2,21,26,,,42,42,,,42,3,,,41,60,,,41,1*46

$GBGSV,6,3,21,8,,,41,59,,,41,39,,,40,16,,,40,1*49

$GBGSV,6,4,21,1,,,39,9,,,38,14,,,37,2,,,36,1*48

$GBGSV,6,5,21,4,,,34,5,,,34,33,,,33,40,,,31,1*71

$GBGSV,6,6,21,7,,,36,1*47

$GBRMC,095711.205,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095711.205,0.000,1616.902,1616.902,51.751,2097152,2097152,2097152*5A



2025-07-31 17:57:07:745 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:08][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:18:08][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:08][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:08][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:08][COMM]adc read battery ts volt:15 mv
[D][05:18:08][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,095711.505,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,43,26,,,43,21,,,42,1*79

$GBGSV,6,2,21,13,,,42,42,,,42,3,,,41,60,,,41,1*40

$GBGSV,6,3,21,8,,,41,59,,,41,39,,,41,16,,,40,1*48

$GBGSV,6,4,21,1,,,39,9,,,38,6,,,38,14,,,37,1*42

$GBG

2025-07-31 17:57:07:790 ==>> SV,6,5,21,2,,,36,4,,,34,5,,,34,33,,,34,1*47

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,095711.505,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095711.505,0.000,1620.846,1620.846,51.872,2097152,2097152,2097152*53



2025-07-31 17:57:07:860 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 17:57:07:875 ==>> 检测【关闭AccKey2供电2】
2025-07-31 17:57:07:880 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 17:57:08:033 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 17:57:08:183 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 17:57:08:186 ==>> 该项需要延时执行
2025-07-31 17:57:08:199 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 17:57:08:708 ==>> $GBGGA,095712.505,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,44,38,,,43,26,,,43,21,,,42,1*7E

$GBGSV,6,2,21,13,,,42,42,,,42,3,,,41,60,,,41,1*40

$GBGSV,6,3,21,8,,,41,59,,,41,39,,,41,16,,,41,1*49

$GBGSV,6,4,21,1,,,39,9,,,38,6,,,38,14,,,37,1*42

$GBGSV,6,5,21,2,,,37,4,,,34,5,,,34,33,,,34,1*46

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,095712.505,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095712.505,0.000,1626.770,1626.770,52.063,2097152,2097152,2097152*5B



2025-07-31 17:57:09:714 ==>> $GBGGA,095713.505,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,43,26,,,43,21,,,42,1*79

$GBGSV,6,2,21,13,,,42,42,,,42,3,,,41,60,,,41,1*40

$GBGSV,6,3,21,8,,,41,59,,,41,39,,,40,16,,,40,1*49

$GBGSV,6,4,21,1,,,39,9,,,39,6,,,38,14,,,38,1*4C

$GBGSV,6,5,21,2,,,37,4,,,34,5,,,34,33,,,34,1*46

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,095713.505,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095713.505,0.000,1624.790,1624.790,51.994,2097152,2097152,2097152*58



2025-07-31 17:57:10:207 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 17:57:10:701 ==>> $GBGGA,095714.505,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,43,26,,,42,21,,,42,1*78

$GBGSV,6,2,21,13,,,42,42,,,42,3,,,41,60,,,41,1*40

$GBGSV,6,3,21,8,,,41,59,,,40,39,,,40,16,,,40,1*48

$GBGSV,6,4,21,1,,,39,9,,,38,6,,,38,14,,,37,1*42

$GBGSV,6,5,21,2,,,37,4,,,35,5,,,34,33,,,34,1*47

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,095714.505,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095714.505,0.000,1618.862,1618.862,51.800,2097152,2097152,2097152*53



2025-07-31 17:57:11:196 ==>> 此处延时了:【3000】毫秒
2025-07-31 17:57:11:202 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 17:57:11:228 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:11:459 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:12][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:12][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:12][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:12][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:12][COMM]adc read battery ts volt:16 mv
[D][05:18:12][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:12][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2386  volt:3844 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 17:57:11:701 ==>> $GBGGA,095715.505,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,33,,,35,5,,,34,1*45

$GBGSV,6,6,22,45,,,34,40,,,30,1*77

$GBRMC,095715.505,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095715.505,0.000,1611.242,1611.242,51.564,2097152,2097152,2097152*5D



2025-07-31 17:57:11:735 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【25mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 17:57:11:739 ==>> 检测【打开AccKey1供电】
2025-07-31 17:57:11:741 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 17:57:11:928 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 17:57:12:007 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 17:57:12:012 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 17:57:12:017 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 17:57:12:140 ==>> 1A A1 00 40 00 
Get AD_V14 2650mV
OVER 150


2025-07-31 17:57:12:230 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 17:57:12:260 ==>> 原始值:【2650】, 乘以分压基数【2】还原值:【5300】
2025-07-31 17:57:12:306 ==>> 【读取AccKey1电压(ADV14)前】通过,【5300mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 17:57:12:310 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 17:57:12:313 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:12:753 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:18:13][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:13][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:13][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:13][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:13][COMM]adc read battery ts volt:10 mv
[D][05:18:13][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:13][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,095716.505,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,42,21,,,42,1*7B

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,34,33,,,34,5,,,34,1*45

$GBGSV,6,6,22,45,,,34,40,,,30,1*77

$GBRMC,095716.505,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*2

2025-07-31 17:57:12:782 ==>> 0

$GBGST,095716.505,0.000,1609.366,1609.366,51.512,2097152,2097152,2097152*5F



2025-07-31 17:57:12:847 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5494mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:57:12:850 ==>> 检测【关闭AccKey1供电2】
2025-07-31 17:57:12:855 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 17:57:13:011 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 17:57:13:120 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 17:57:13:124 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 17:57:13:128 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 17:57:13:240 ==>> 1A A1 00 40 00 
Get AD_V14 2650mV
OVER 150


2025-07-31 17:57:13:377 ==>> 原始值:【2650】, 乘以分压基数【2】还原值:【5300】
2025-07-31 17:57:13:396 ==>> 【读取AccKey1电压(ADV14)后】通过,【5300mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 17:57:13:401 ==>> 检测【打开WIFI(2)】
2025-07-31 17:57:13:407 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 17:57:13:564 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 17:57:13:669 ==>>                                ,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,39,6,,,38,14,,,37,1*40

$GBGSV,6,5,22,2,,,37,4,,,35,33,,,34,5,,,34,1*44

$GB

2025-07-31 17:57:13:684 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 17:57:13:688 ==>> 检测【转刹把供电】
2025-07-31 17:57:13:692 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 17:57:13:716 ==>> GSV,6,6,22,45,,,34,40,,,31,1*76

$GBRMC,095717.505,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095717.505,0.000,1616.897,1616.897,51.746,2097152,2097152,2097152*5D



2025-07-31 17:57:13:819 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 17:57:13:958 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 17:57:13:962 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 17:57:13:964 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 17:57:14:062 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 17:57:14:139 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 17:57:14:214 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 17:57:14:229 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 17:57:14:234 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 17:57:14:237 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 17:57:14:242 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 17:57:14:349 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 17:57:14:457 ==>> +WIFISCAN:4,0,F42A7D1297A3,-70
+WIFISCAN:4,1,44A1917CA62B,-77
+WIFISCAN:4,2,44A1917CAD80,-79
+WIFISCAN:4,3,44A1917CAD81,-79

[D][05:18:15][CAT1]wifi scan report total[4]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2416mV
OVER 150


2025-07-31 17:57:14:502 ==>> 原始值:【2416】, 乘以分压基数【2】还原值:【4832】
2025-07-31 17:57:14:539 ==>> 【读取AD_V16电压(前)】通过,【4832mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 17:57:14:543 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 17:57:14:545 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:14:871 ==>> $GBGGA,095718.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,42,60,,,41,1*40

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,39,6,,,38,14,,,37,1*40

$GBGSV,6,5,22,2,,,37,4,,,34,33,,,34,5,,,34,1*45

$GBGSV,6,6,22,45,,,34,40,,,31,1*76

$GBRMC,095718.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095718.505,0.000,1616.902,1616.902,51.750,2097152,2097152,2097152*55

[D][05:18:15][GNSS]recv submsg id[3]
[D][05:18:15][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:15][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat 

2025-07-31 17:57:14:916 ==>> adc:2388  volt:3847 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 17:57:15:071 ==>> 【转刹把供电电压(主控ADC)】通过,【5407mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 17:57:15:077 ==>> 检测【转刹把供电电压】
2025-07-31 17:57:15:082 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:15:361 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:16][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:16][COMM]adc read battery ts volt:10 mv
[D][05:18:16][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 17:57:15:609 ==>> 【转刹把供电电压】通过,【5414mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 17:57:15:613 ==>> 检测【关闭转刹把供电2】
2025-07-31 17:57:15:616 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:57:15:712 ==>> $GBGGA,095719.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,41,1*4A

$GBGSV,6,4,22,9,,,39,1,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,36,4,,,35,5,,,35,33,,,34,1*44

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095719.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095719.505,0.000,1615.016,1615.016,51.689,2097152,2097152,2097152*51



2025-07-31 17:57:15:817 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 17:57:15:880 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 17:57:15:886 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 17:57:15:889 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:57:15:985 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 17:57:16:015 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 17:57:16:045 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 17:57:16:110 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 17:57:16:114 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 17:57:16:120 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:57:16:213 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 17:57:16:228 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 17:57:16:318 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:57:16:426 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 17:57:16:535 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:57:16:550 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:17][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 17:57:16:640 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 17:57:16:764 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 17:57:16:768 ==>> 检测【拉高OUTPUT3】
2025-07-31 17:57:16:771 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 17:57:16:776 ==>> $GBGGA,095720.505,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,42,21,,,42,1*7B

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,5,,,35,33,,,34,1*45

$GBGSV,6,6,22,45,,,34,40,,,31,1*76

$GBRMC,095720.505,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095720.505,0.000,1615.008,1615.008,51.681,2097152,2097152,2097152*53

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 17:57:16:851 ==>> 3A A3 03 01 A3 


2025-07-31 17:57:16:941 ==>> ON_OUT3
OVER 150


2025-07-31 17:57:17:041 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 17:57:17:044 ==>> 检测【拉高OUTPUT4】
2025-07-31 17:57:17:049 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 17:57:17:139 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 17:57:17:324 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 17:57:17:328 ==>> 检测【拉高OUTPUT5】
2025-07-31 17:57:17:334 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 17:57:17:447 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 17:57:17:601 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 17:57:17:604 ==>> 检测【左刹电压测试1】
2025-07-31 17:57:17:608 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:17:706 ==>> $GBGGA,095721.505,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,36,4,,,35,5,,,34,33,,,34,1*45

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095721.505,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095721.505,0.000,1611.250,1611.250,51.571,2097152,2097152,2097152*5E



2025-07-31 17:57:17:962 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1721  volt:2268 mv
[D][05:18:18][COMM]adc read right brake adc:1720  volt:2267 mv
[D][05:18:18][COMM]adc read throttle adc:1717  volt:2263 mv
[D][05:18:18][COMM]adc read battery ts volt:7 mv
[D][05:18:18][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:18][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2386  volt:3844 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 17:57:18:134 ==>> 【左刹电压测试1】通过,【2268】符合目标值【2250】至【2500】要求!
2025-07-31 17:57:18:140 ==>> 检测【右刹电压测试1】
2025-07-31 17:57:18:159 ==>> 【右刹电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 17:57:18:162 ==>> 检测【转把电压测试1】
2025-07-31 17:57:18:178 ==>> 【转把电压测试1】通过,【2263】符合目标值【2250】至【2500】要求!
2025-07-31 17:57:18:181 ==>> 检测【拉低OUTPUT3】
2025-07-31 17:57:18:184 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 17:57:18:251 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150
[D][05:18:19][COMM]read battery soc:255


2025-07-31 17:57:18:451 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 17:57:18:455 ==>> 检测【拉低OUTPUT4】
2025-07-31 17:57:18:458 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 17:57:18:542 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 17:57:18:647 ==>> $GBGGA,095722.505,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGS

2025-07-31 17:57:18:707 ==>> V,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,41,1*4A

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,5,,,35,33,,,34,1*45

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095722.505,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095722.505,0.000,1616.899,1616.899,51.748,2097152,2097152,2097152*55



2025-07-31 17:57:18:726 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 17:57:18:730 ==>> 检测【拉低OUTPUT5】
2025-07-31 17:57:18:733 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 17:57:18:842 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 17:57:18:996 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 17:57:19:000 ==>> 检测【左刹电压测试2】
2025-07-31 17:57:19:003 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:57:19:256 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:20][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:20][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:20][COMM]adc read battery ts volt:10 mv
[D][05:18:20][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:20][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 17:57:19:546 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 17:57:19:550 ==>> 检测【右刹电压测试2】
2025-07-31 17:57:19:565 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 17:57:19:569 ==>> 检测【转把电压测试2】
2025-07-31 17:57:19:584 ==>> 【转把电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 17:57:19:588 ==>> 检测【晶振检测】
2025-07-31 17:57:19:591 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 17:57:19:763 ==>> $GBGGA,095723.505,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,5,,,35,33,,,35,1*44

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095723.505,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095723.505,0.000,1616.895,1616.895,51.744,2097152,2097152,2097152*58

[W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 17:57:19:857 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 17:57:19:863 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 17:57:19:870 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:57:19:962 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1668mV
Get AD_V4 1647mV
Get AD_V5 2762mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 17:57:20:132 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:57:20:136 ==>> 检测【检测BootVer】
2025-07-31 17:57:20:139 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:57:20:252 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 17:57:20:525 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539041
[D][05:18:21][FCTY]HardwareID  = 867222087782647
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = F42F80383B97
[D][05:18:21][FCTY]Bat         = 3944 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:21][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KE

2025-07-31 17:57:20:570 ==>> RNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3789 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 17:57:20:670 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 17:57:20:675 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 17:57:20:681 ==>> 检测【检测固件版本】
2025-07-31 17:57:20:706 ==>>                                                                                                                      ,,42,1*7B

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,33,,,35,5,,,34,1*45

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095724.505,V,,,,,,,,0.0,

2025-07-31 17:57:20:709 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 17:57:20:713 ==>> E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095724.505,0.000,1613.127,1613.127,51.624,2097152,2097152,2097152*58



2025-07-31 17:57:20:736 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 17:57:20:740 ==>> 检测【检测蓝牙版本】
2025-07-31 17:57:20:746 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 17:57:20:766 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 17:57:20:770 ==>> 检测【检测MoBikeId】
2025-07-31 17:57:20:776 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 17:57:20:782 ==>> 提取到MoBikeId:9999999999
2025-07-31 17:57:20:786 ==>> 检测【检测蓝牙地址】
2025-07-31 17:57:20:796 ==>> 取到目标值:F42F80383B97
2025-07-31 17:57:20:800 ==>> 【检测蓝牙地址】通过,【F42F80383B97】符合目标值【】要求!
2025-07-31 17:57:20:803 ==>> 提取到蓝牙地址:F42F80383B97
2025-07-31 17:57:20:807 ==>> 检测【BOARD_ID】
2025-07-31 17:57:20:813 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 17:57:20:828 ==>> 检测【检测充电电压】
2025-07-31 17:57:20:832 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 17:57:20:837 ==>> 检测【检测VBUS电压1】
2025-07-31 17:57:20:847 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 17:57:20:851 ==>> 检测【检测充电电流】
2025-07-31 17:57:20:866 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 17:57:20:873 ==>> 检测【检测IMEI】
2025-07-31 17:57:20:905 ==>> 取到目标值:867222087782647
2025-07-31 17:57:20:910 ==>> 【检测IMEI】通过,【867222087782647】符合目标值【】要求!
2025-07-31 17:57:20:920 ==>> 提取到IMEI:867222087782647
2025-07-31 17:57:20:926 ==>> 检测【检测IMSI】
2025-07-31 17:57:20:935 ==>> 取到目标值:460130071539041
2025-07-31 17:57:20:939 ==>> 【检测IMSI】通过,【460130071539041】符合目标值【】要求!
2025-07-31 17:57:20:952 ==>> 提取到IMSI:460130071539041
2025-07-31 17:57:20:956 ==>> 检测【校验网络运营商(移动)】
2025-07-31 17:57:20:959 ==>> 取到目标值:460130071539041
2025-07-31 17:57:20:963 ==>> 【校验网络运营商(移动)】通过,【460130071539041】符合目标值【】要求!
2025-07-31 17:57:20:968 ==>> 检测【打开CAN通信】
2025-07-31 17:57:20:984 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 17:57:21:045 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 17:57:21:246 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 17:57:21:255 ==>> 检测【检测CAN通信】
2025-07-31 17:57:21:281 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 17:57:21:337 ==>> [D][05:18:22][COMM]IMU: [2,-8,-939] ret=23 AWAKE!
can send success


2025-07-31 17:57:21:369 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 17:57:21:427 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33522
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 17:57:21:487 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 17:57:21:521 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 17:57:21:527 ==>> 检测【关闭CAN通信】
2025-07-31 17:57:21:551 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 17:57:21:574 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 17:57:21:607 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 17:57:21:712 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
$GBGGA,095725.505,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,38,9,,,38,6,,,38,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,4,,,35,33,,,35,5,,,35,1*45

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095725.505,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095725.505,0.000,1611.243,1611.243,51.564,2097152,2097152,2097152*5E



2025-07-31 17:57:21:790 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 17:57:21:827 ==>> 检测【打印IMU STATE】
2025-07-31 17:57:21:833 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 17:57:21:941 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-67 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 17:57:22:068 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 17:57:22:098 ==>> 检测【六轴自检】
2025-07-31 17:57:22:103 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 17:57:22:278 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


[D][05:18:23][COMM]read battery soc:255


2025-07-31 17:57:22:707 ==>> $GBGGA,095726.505,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,42,60,,,41,1*40

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,40,16,,,40,1*4A

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,37,4,,,35,33,,,35,5,,,35,1*44

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095726.505,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095726.505,0.000,1616.896,1616.896,51.745,2097152,2097152,2097152*5C



2025-07-31 17:57:23:713 ==>> $GBGGA,095727.505,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,42,60,,,41,1*40

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,39,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,22,2,,,36,4,,,35,33,,,35,5,,,35,1*45

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095727.505,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095727.505,0.000,1616.899,1616.899,51.747,2097152,2097152,2097152*5F



2025-07-31 17:57:23:942 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 17:57:24:107 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36180 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-2,-13,4053]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 17:57:24:171 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 17:57:24:178 ==>> 检测【打印IMU STATE2】
2025-07-31 17:57:24:185 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 17:57:24:350 ==>> [D][05:18:25][COMM]read battery soc:255
[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-67 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 17:57:24:444 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 17:57:24:452 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 17:57:24:473 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 17:57:24:547 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 17:57:24:721 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 17:57:24:728 ==>> 检测【检测VBUS电压2】
2025-07-31 17:57:24:761 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:57:24:789 ==>> $GBGGA,095728.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,42,,,42,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,8,,,41,59,,,41,39,,,41,16,,,40,1*4B

$GBGSV,6,4,22,1,,,38,9,,,38,6,,,38,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,4,,,35,33,,,35,5,,,34,1*44

$GBGSV,6,6,22,45,,,33,40,,,31,1*71

$GBRMC,095728.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095728.505,0.000,1611.247,1611.247,51.568,2097152,2097152,2097152*5F

[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 17:57:25:091 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539041
[D][05:18:25][FCTY]HardwareID  = 867222087782647
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = F42F80383B97
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 8, adc = 319
[D][05:18:26][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 37

2025-07-31 17:57:25:136 ==>> 89 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 17:57:25:260 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:57:25:612 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539041
[D][05:18:26][FCTY]HardwareID  = 867222087782647
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = F42F80383B97
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 7400 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 178
[D][05:18:26][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3789 mv
[D]

2025-07-31 17:57:25:702 ==>> [05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 17:57:25:793 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:57:26:113 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539041
[D][05:18:27][FCTY]HardwareID  = 867222087782647
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = F42F80383B97
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 7400 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 153
[D][05:18:27][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3789 mv
[D][05:18:27][FCTY

2025-07-31 17:57:26:143 ==>> ]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 17:57:26:343 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:57:26:434 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33518. cur_tick:38539. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38539


2025-07-31 17:57:27:052 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539041
[D][05:18:27][FCTY]HardwareID  = 867222087782647
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = F42F80383B97
[D][05:18:27][FCTY]Bat         = 3784 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 127
[D][05:18:27][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 101 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4


2025-07-31 17:57:27:131 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 17:57:27:138 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 17:57:27:159 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 17:57:27:165 ==>> [D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3789 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:1

2025-07-31 17:57:27:262 ==>> 8:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:27][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===============================================

2025-07-31 17:57:27:367 ==>> ============
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B50141E6B64A9804BC5D74786E0D777BBC17F28F

2025-07-31 17:57:27:457 ==>> 72B40382D165D5D6808D7B89438249858BE588E9259E25AE47DACD6E293FBC15C9ADB7A9C7678A52073420FDB3DE0B84DC54C45C941238D8B93338D91FC132
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 17:57:27:562 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:18:28][COMM]read battery soc:255


2025-07-31 17:57:27:665 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 17:57:27:671 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 17:57:27:676 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 17:57:27:745 ==>> [D][05:18:28][GNSS]recv submsg id[1]
[D][05:18:28][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:28][GNSS]location stop evt done evt
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 17:57:27:946 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 17:57:27:952 ==>> 检测【打开WIFI(3)】
2025-07-31 17:57:27:959 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 17:57:28:160 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 17:57:28:224 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 17:57:28:229 ==>> 检测【扩展芯片hw】
2025-07-31 17:57:28:253 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 17:57:28:433 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 17:57:28:497 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 17:57:28:503 ==>> 检测【扩展芯片boot】
2025-07-31 17:57:28:519 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 17:57:28:525 ==>> 检测【扩展芯片sw】
2025-07-31 17:57:28:548 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 17:57:28:581 ==>> 检测【检测音频FLASH】
2025-07-31 17:57:28:586 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 17:57:28:723 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 17:57:29:109 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 17:57:29:833 ==>>                                                                                                                                                                                              [COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:

2025-07-31 17:57:29:938 ==>> 30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:30][PROT]min_

2025-07-31 17:57:30:043 ==>> index:1, type:0x5D03, priority:4
[D][05:18:30][PROT]index:1
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_

2025-07-31 17:57:30:148 ==>> in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A3230

2025-07-31 17:57:30:253 ==>> 34380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[e

2025-07-31 17:57:30:343 ==>> c800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255
[D][05:18:30][GNSS]recv submsg id[3]


2025-07-31 17:57:31:585 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 17:57:32:186 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3EEA72E375CEFA05BB3A

2025-07-31 17:57:32:291 ==>> 2A433DBA34DC9895CC0396F1C5B2F220375A0D2C9B6840E8D0F602E944D2B32C823667042828617EADBF0868FC2985899CD6C587B3B338518A886648C182DD9B96C59CEEF58749331
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]
                                                            

2025-07-31 17:57:32:847 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 17:57:33:323 ==>> [D][05:18:34][COMM]45312 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 17:57:33:612 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 17:57:33:631 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 17:57:33:637 ==>> 检测【打开喇叭声音】
2025-07-31 17:57:33:642 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 17:57:34:370 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[704].audio 

2025-07-31 17:57:34:419 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 17:57:34:427 ==>> 检测【打开大灯控制】
2025-07-31 17:57:34:433 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 17:57:34:475 ==>> cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][0

2025-07-31 17:57:34:580 ==>> 5:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]46323 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 17:57:34:655 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 17:57:34:690 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 17:57:34:695 ==>> 检测【关闭仪表供电3】
2025-07-31 17:57:34:700 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 17:57:34:835 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0


2025-07-31 17:57:34:959 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 17:57:34:965 ==>> 检测【关闭AccKey2供电3】
2025-07-31 17:57:34:991 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 17:57:35:111 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 17:57:35:242 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 17:57:35:247 ==>> 检测【读大灯电压】
2025-07-31 17:57:35:253 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 17:57:35:432 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[32969]


2025-07-31 17:57:35:525 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 17:57:35:531 ==>> 检测【关闭大灯控制2】
2025-07-31 17:57:35:540 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 17:57:35:613 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 17:57:35:718 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 17:57:35:798 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 17:57:35:808 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 17:57:35:829 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 17:57:36:025 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[92]


2025-07-31 17:57:36:074 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 17:57:36:080 ==>> 检测【打开WIFI(4)】
2025-07-31 17:57:36:089 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 17:57:36:268 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 17:57:36:408 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 17:57:36:413 ==>> 检测【EC800M模组版本】
2025-07-31 17:57:36:422 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 17:57:36:639 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 17:57:36:805 ==>> [D][05:18:37][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 132
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:57:36:945 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 17:57:36:951 ==>> 检测【配置蓝牙地址】
2025-07-31 17:57:36:956 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 17:57:36:977 ==>> +WIFISCAN:4,0,CC057790A5C1,-77
+WIFISCAN:4,1,44A1917CAD81,-81
+WIFISCAN:4,2,44A1917CAD80,-82
+WIFISCAN:4,3,F86FB0660A82,-88

[D][05:18:38][CAT1]wifi scan report total[4]


2025-07-31 17:57:37:154 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:F42F80383B97>】
2025-07-31 17:57:37:413 ==>> [W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:38][PROT]CLEAN,SEND:1
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]Send Data To Server[198][198] ... ->:
0063B98C113311331133113311331B88B3FDB398958DB24D39B6F64F72494154B1A344AFB08C

2025-07-31 17:57:37:503 ==>> 9D13041A20618B485700C05ED80DB4C001A348DCD805EB114D9879A4ABB035DD0B3A385AFB921909EA053A825272C87E19FFAF596782B2DD6BBAE89B3D
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]
recv ble 1
recv ble 2
ble set mac ok :f4,2f,80,38,3b,97
enable filters ret : 0

2025-07-31 17:57:37:608 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 17:57:37:687 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 17:57:37:693 ==>> 检测【BLETEST】
2025-07-31 17:57:37:697 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 17:57:37:743 ==>> 4A A4 01 A4 4A 


2025-07-31 17:57:37:818 ==>> [D][05:18:38][GNSS]recv submsg id[3]
[D][05:18:38][COMM]49907 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:57:37:863 ==>> recv ble 1
recv ble 2
<BSJ*MAC:F42F80383B97*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9F42F80383B9799999OVER 150


2025-07-31 17:57:38:737 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 17:57:38:746 ==>> 该项需要延时执行
2025-07-31 17:57:38:809 ==>> [D][05:18:39][COMM]50918 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:57:39:474 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 17:57:39:609 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 17:57:39:806 ==>> [D][05:18:40][COMM]51929 imu init OK


2025-07-31 17:57:41:627 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 17:57:42:615 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3C194B7B736683910D132174B2D748E3262EF3FA4ABA4A83554773B055356A73F84341AE45CC336F6771B53906E65E0E0C7B23A2BCC8E34E17939F

2025-07-31 17:57:42:675 ==>> FCFCE4D5A4F66EE6A741FB5648D4FDBECDA6B342A82939C
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 17:57:43:621 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 17:57:45:628 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 17:57:47:854 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][COMM]read battery soc:255
[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5921036A00E4AF0B23230BE97E8EDAE

2025-07-31 17:57:47:929 ==>> A3804347CC5B77805E96BE849B65FCBF47A8ACD17F04B9DC8E0688E4B85694FCDBB579B0FFD0FAB6FFCA2D7C8C2595E1156C8EB7A6930798BD1260AB6244CAB0ABCB26
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 17:57:48:738 ==>> 此处延时了:【10000】毫秒
2025-07-31 17:57:48:744 ==>> 检测【检测WiFi结果】
2025-07-31 17:57:48:759 ==>> WiFi信号:【F42A7D1297A3】,信号值:-70
2025-07-31 17:57:48:776 ==>> WiFi信号:【44A1917CA62B】,信号值:-77
2025-07-31 17:57:48:801 ==>> WiFi信号:【44A1917CAD80】,信号值:-79
2025-07-31 17:57:48:807 ==>> WiFi信号:【44A1917CAD81】,信号值:-79
2025-07-31 17:57:48:815 ==>> WiFi信号:【CC057790A5C1】,信号值:-77
2025-07-31 17:57:48:837 ==>> WiFi信号:【F86FB0660A82】,信号值:-88
2025-07-31 17:57:48:845 ==>> WiFi数量【6】, 最大信号值:-70
2025-07-31 17:57:48:854 ==>> 检测【检测GPS结果】
2025-07-31 17:57:48:869 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 17:57:48:939 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all continue location stop
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all sing location stop


2025-07-31 17:57:49:651 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 17:57:49:756 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 17:57:49:764 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:49:774 ==>> 定位已等待【1】秒.
2025-07-31 17:57:50:145 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:51][COMM]Open GPS Module...
[D][05:18:51][COMM]LOC_MODEL_CONT
[D][05:18:51][GNSS]start event:8
[D][05:18:51][GNSS]GPS start. ret=0
[W][05:18:51][GNSS]start cont locating
[D][05:18:51][CAT1]gsm read msg sub id: 23
[D][05:18:51][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:51][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 17:57:50:768 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:50:777 ==>> 定位已等待【2】秒.
2025-07-31 17:57:50:861 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 17:57:51:529 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 17:57:51:740 ==>>   BGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,60,,,46,21,,,43,38,,,42,39,,,41,1*76

$GBGSV,3,2,11,59,,,41,26,,,40,42,,,38,24,,,21,1*76

$GBGSV,3,3,11,16,,,18,13,,,40,9,,,38,1*4C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1520.467,1520.467,48.960,2097152,2097152,2097152*4C

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:52][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:52][COMM]read battery soc:255
[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 23, ret: 6
[D][05:18:52][CAT1]sub id: 23, ret: 6



2025-07-31 17:57:51:770 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:51:781 ==>> 定位已等待【3】秒.
2025-07-31 17:57:51:845 ==>> [D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 17:57:52:680 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,60,,,42,21,,,42,38,,,42,26,,,42,1*71

$GBGSV,5,2,18,39,,,41,13,,,41,59,,,40,42,,,40,1*7A

$GBGSV,5,3,18,16,,,40,24,,,39,1,,,39,9,,,38,1*7F

$GBGSV,5,4,18,8,,,36,4,,,36,2,,,35,5,,,33,1*73

$GBGSV,5,5,18,43,,,38,3,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1622.043,1622.043,51.875,2097152,2097152,2097152*41



2025-07-31 17:57:52:786 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:52:794 ==>> 定位已等待【4】秒.
2025-07-31 17:57:53:063 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311

2025-07-31 17:57:53:138 ==>> 331133113311331B88B5C0B788EFF2225A0C99FE6EBE8FB4F4484EAA8C5D5B28751C4DFC9E6FF429259AA46EBBD3AD7BEB48650DF97DD5D3131FEA89B9B991D23F04AE3730013E932720CBFB22262E254AAD12D3FC661122B15694A3
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]


2025-07-31 17:57:53:680 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,21,,,42,38,,,42,26,,,42,3,,,42,1*44

$GBGSV,5,2,18,60,,,41,39,,,41,13,,,41,59,,,41,1*7A

$GBGSV,5,3,18,42,,,41,24,,,41,16,,,40,8,,,39,1*48

$GBGSV,5,4,18,1,,,38,9,,,38,2,,,36,4,,,35,1*73

$GBGSV,5,5,18,5,,,33,33,,,37,1*4E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1641.243,1641.243,52.484,2097152,2097152,2097152*40

[D][05:18:54][COMM]read battery soc:255


2025-07-31 17:57:53:800 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:53:810 ==>> 定位已等待【5】秒.
2025-07-31 17:57:54:702 ==>> $GBGGA,095758.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,21,,,42,38,,,42,26,,,42,24,,,42,1*7A

$GBGSV,5,2,20,3,,,41,60,,,41,39,,,41,13,,,41,1*4E

$GBGSV,5,3,20,42,,,41,59,,,40,16,,,40,8,,,40,1*46

$GBGSV,5,4,20,1,,,38,9,,,38,2,,,36,4,,,35,1*78

$GBGSV,5,5,20,33,,,34,5,,,33,14,,,33,6,,,47,1*76

$GBRMC,095758.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095758.501,0.000,1614.699,1614.699,51.660,2097152,2097152,2097152*57



2025-07-31 17:57:54:808 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:54:817 ==>> 定位已等待【6】秒.
2025-07-31 17:57:55:710 ==>> $GBGGA,095759.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,21,,,42,38,,,42,26,,,42,1*79

$GBGSV,6,2,21,3,,,41,60,,,41,39,,,41,13,,,41,1*4C

$GBGSV,6,3,21,42,,,41,59,,,40,16,,,40,8,,,40,1*44

$GBGSV,6,4,21,1,,,38,9,,,38,6,,,37,2,,,36,1*7A

$GBGSV,6,5,21,33,,,35,14,,,35,4,,,34,5,,,34,1*72

$GBGSV,6,6,21,7,,,31,1*40

$GBRMC,095759.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095759.501,0.000,1603.070,1603.070,51.297,2097152,2097152,2097152*5A

[D][05:18:56][COMM]read battery soc:255


2025-07-31 17:57:55:815 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:55:824 ==>> 定位已等待【7】秒.
2025-07-31 17:57:56:715 ==>> $GBGGA,095800.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,21,,,42,38,,,42,26,,,42,1*7B

$GBGSV,6,2,23,3,,,41,60,,,41,39,,,41,13,,,41,1*4E

$GBGSV,6,3,23,42,,,41,8,,,41,59,,,40,16,,,40,1*47

$GBGSV,6,4,23,1,,,38,9,,,38,6,,,37,2,,,36,1*78

$GBGSV,6,5,23,14,,,36,33,,,35,4,,,34,5,,,34,1*73

$GBGSV,6,6,23,45,,,32,7,,,31,10,,,36,1*46

$GBRMC,095800.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095800.501,0.000,1594.287,1594.287,51.026,2097152,2097152,2097152*51



2025-07-31 17:57:56:821 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:56:830 ==>> 定位已等待【8】秒.
2025-07-31 17:57:57:740 ==>> $GBGGA,095801.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,43,26,,,43,21,,,42,1*7A

$GBGSV,6,2,22,13,,,42,3,,,41,60,,,41,39,,,41,1*4C

$GBGSV,6,3,22,42,,,41,8,,,41,59,,,41,16,,,40,1*47

$GBGSV,6,4,22,9,,,39,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,22,14,,,37,33,,,35,4,,,35,5,,,34,1*72

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,095801.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095801.501,0.000,803.197,803.197,734.542,2097152,2097152,2097152*63

[D][05:18:58][COMM]read battery soc:255


2025-07-31 17:57:57:830 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:57:840 ==>> 定位已等待【9】秒.
2025-07-31 17:57:58:292 ==>> [D][05:18:59][PROT]CLEAN,SEND:0
[D][05:18:59][PROT]CLEAN:0
[D][05:18:59][PROT]index:2 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:6
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:3
[D][05:18:59][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:18:59][PROT]sending traceid [9999999999900007]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[134]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:59][CAT1]gsm read msg sub id: 15
[D][05:18:59][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:59][CAT1]Send Data To Server[134][137]

2025-07-31 17:57:58:367 ==>>  ... ->:
0043B683113311331133113311331B88BEEB4F71C25121C87EC1C1030C184A8A76F2E0078BED9D40611F2C54CC831922CED51BF00207C955507831B8C50D7810475480
[D][05:18:59][CAT1]<<< 
SEND OK

[D][05:18:59][CAT1]exec over: func id: 15, ret: 11
[D][05:18:59][CAT1]sub id: 15, ret: 11

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:59][M2M ]g_m2m_is_idle become true
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:59][PROT]M2M Send ok [1629955139]


2025-07-31 17:57:58:734 ==>> $GBGGA,095802.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,38,,,42,21,,,42,1*7C

$GBGSV,6,2,22,26,,,42,8,,,41,3,,,41,60,,,41,1*78

$GBGSV,6,3,22,39,,,41,59,,,41,42,,,41,16,,,40,1*75

$GBGSV,6,4,22,9,,,38,6,,,38,1,,,38,2,,,37,1*77

$GBGSV,6,5,22,14,,,37,33,,,36,4,,,35,5,,,34,1*71

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,095802.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095802.501,0.000,801.309,801.309,732.815,2097152,2097152,2097152*69



2025-07-31 17:57:58:840 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:58:846 ==>> 定位已等待【10】秒.
2025-07-31 17:57:59:740 ==>> $GBGGA,095803.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,42,21,,,42,26,,,42,24,,,42,1*7A

$GBGSV,6,2,23,13,,,41,8,,,41,60,,,41,3,,,41,1*7C

$GBGSV,6,3,23,59,,,41,42,,,41,39,,,40,16,,,39,1*7B

$GBGSV,6,4,23,9,,,38,6,,,38,1,,,37,14,,,37,1*4E

$GBGSV,6,5,23,2,,,36,4,,,35,33,,,35,5,,,34,1*45

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,29,1*48

$GBRMC,095803.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095803.501,0.000,787.179,787.179,719.896,2097152,2097152,2097152*6A

[D][05:19:00][COMM]read battery soc:255


2025-07-31 17:57:59:845 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:57:59:855 ==>> 定位已等待【11】秒.
2025-07-31 17:58:00:715 ==>> $GBGGA,095804.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,42,26,,,42,24,,,42,13,,,41,1*78

$GBGSV,6,2,23,60,,,41,21,,,41,42,,,41,8,,,40,1*49

$GBGSV,6,3,23,3,,,40,39,,,40,59,,,40,16,,,40,1*40

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,4,,,35,33,,,35,5,,,34,1*73

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,30,1*40

$GBRMC,095804.501,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095804.501,0.000,787.168,787.168,719.884,2097152,2097152,2097152*6E



2025-07-31 17:58:00:851 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:00:860 ==>> 定位已等待【12】秒.
2025-07-31 17:58:01:736 ==>> $GBGGA,095805.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,42,26,,,42,21,,,42,24,,,42,1*7A

$GBGSV,6,2,23,60,,,41,13,,,41,8,,,41,3,,,41,1*7C

$GBGSV,6,3,23,42,,,41,59,,,40,39,,,40,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,4,,,35,33,,,35,5,,,34,1*73

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,30,1*40

$GBRMC,095805.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095805.501,0.000,789.871,789.871,722.356,2097152,2097152,2097152*63

[D][05:19:02][COMM]read battery soc:255


2025-07-31 17:58:01:857 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:01:868 ==>> 定位已等待【13】秒.
2025-07-31 17:58:02:728 ==>> $GBGGA,095806.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,21,,,42,1*7B

$GBGSV,6,2,23,60,,,41,13,,,41,8,,,41,3,,,41,1*7C

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,5,,,34,1*70

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,30,1*40

$GBRMC,095806.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095806.501,0.000,793.472,793.472,725.650,2097152,2097152,2097152*64



2025-07-31 17:58:02:863 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:02:872 ==>> 定位已等待【14】秒.
2025-07-31 17:58:03:503 ==>> [D][05:19:04][PROT]CLEAN,SEND:2
[D][05:19:04][PROT]index:2 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:6
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:2
[D][05:19:04][PROT]send_path:0x2
[D][05:19:04][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:04][PROT]===========================================================
[W][05:19:04][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955144]
[D][05:19:04][PROT]===========================================================
[D][05:19:04][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:04][PROT]sending traceid [9999999999900007]
[D][05:19:04][PROT]Send_TO_M2M [1629955144]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:04][SAL ]sock send credit cnt[6]
[D][05:19:04][SAL ]sock send ind credit cnt[6]
[D][05:19:04][M2M ]m2m send data len[134]
[D][05:19:04][SAL ]Cellular task submsg id[10]
[D][05:19:04][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]gsm read msg sub id: 15
[D][05:19:04][CAT1]tx 

2025-07-31 17:58:03:578 ==>> ret[17] >>> AT+QISEND=0,134

[D][05:19:04][CAT1]Send Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BE9EDD34293ACCEADCCF270FC5041E4B0F0A114638E07601A2EF728380F3056D8398D753E069C3CEDFA844A9EB072E3B21F698
[D][05:19:04][CAT1]<<< 
SEND OK

[D][05:19:04][CAT1]exec over: func id: 15, ret: 11
[D][05:19:04][CAT1]sub id: 15, ret: 11

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:04][M2M ]g_m2m_is_idle become true
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:04][PROT]M2M Send ok [1629955144]


2025-07-31 17:58:03:683 ==>>                                                                                                                             7A

$GBGSV,6,2,23,21,,,42,60,,,41,8,,,41,3,,,41,1*7E

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,

2025-07-31 17:58:03:728 ==>> 38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,45,,,34,1*44

$GBGSV,6,6,23,5,,,34,7,,,31,10,,,30,1*72

$GBRMC,095807.501,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095807.501,0.000,796.165,796.165,728.112,2097152,2097152,2097152*69

[D][05:19:04][COMM]read battery soc:255


2025-07-31 17:58:03:864 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:03:874 ==>> 定位已等待【15】秒.
2025-07-31 17:58:04:717 ==>> $GBGGA,095808.501,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,60,,,42,38,,,42,26,,,42,1*7E

$GBGSV,6,2,23,21,,,42,13,,,41,8,,,41,3,,,41,1*7A

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,5,,,34,1*70

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,30,1*40

$GBRMC,095808.501,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095808.501,0.000,794.373,794.373,726.474,2097152,2097152,2097152*6D



2025-07-31 17:58:04:870 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:04:880 ==>> 定位已等待【16】秒.
2025-07-31 17:58:05:741 ==>> $GBGGA,095809.501,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,26,,,43,24,,,43,13,,,42,38,,,42,1*7B

$GBGSV,6,2,23,21,,,42,42,,,42,60,,,41,8,,,41,1*48

$GBGSV,6,3,23,3,,,41,39,,,41,59,,,40,16,,,40,1*40

$GBGSV,6,4,23,1,,,39,9,,,38,6,,,38,2,,,37,1*77

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,45,,,34,1*44

$GBGSV,6,6,23,5,,,34,7,,,31,10,,,30,1*72

$GBRMC,095809.501,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095809.501,0.000,797.967,797.967,729.760,2097152,2097152,2097152*65

[D][05:19:06][COMM]read battery soc:255


2025-07-31 17:58:05:877 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:05:886 ==>> 定位已等待【17】秒.
2025-07-31 17:58:06:704 ==>> $GBGGA,095810.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,43,26,,,43,24,,,43,13,,,42,1*7A

$GBGSV,6,2,23,21,,,42,60,,,41,8,,,41,3,,,41,1*7E

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,45,,,34,1*44

$GBGSV,6,6,23,5,,,34,7,,,31,10,,,30,1*72

$GBRMC,095810.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095810.501,0.000,797.968,797.968,729.761,2097152,2097152,2097152*6C



2025-07-31 17:58:06:888 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:06:898 ==>> 定位已等待【18】秒.
2025-07-31 17:58:07:740 ==>> $GBGGA,095811.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,43,26,,,43,24,,,43,13,,,42,1*7A

$GBGSV,6,2,23,21,,,42,42,,,42,60,,,41,8,,,41,1*48

$GBGSV,6,3,23,3,,,41,59,,,41,39,,,41,16,,,40,1*41

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,14,,,38,1*4E

$GBGSV,6,5,23,2,,,37,33,,,36,4,,,35,45,,,34,1*73

$GBGSV,6,6,23,5,,,34,7,,,31,10,,,30,1*72

$GBRMC,095811.501,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095811.501,0.000,799.768,799.768,731.408,2097152,2097152,2097152*68

[D][05:19:08][COMM]read battery soc:255


2025-07-31 17:58:07:893 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:07:903 ==>> 定位已等待【19】秒.
2025-07-31 17:58:08:808 ==>> [D][05:19:09][PROT]CLEAN,SEND:2
[D][05:19:09][PROT]index:2 1629955149
[D][05:19:09][PROT]is_send:0
[D][05:19:09][PROT]sequence_num:6
[D][05:19:09][PROT]retry_timeout:0
[D][05:19:09][PROT]retry_times:1
[D][05:19:09][PROT]send_path:0x2
[D][05:19:09][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:09][PROT]===========================================================
[D][05:19:09][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:09][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955149]
[D][05:19:09][PROT]===========================================================
[D][05:19:09][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:09][PROT]sending traceid [9999999999900007]
[D][05:19:09][PROT]Send_TO_M2M [1629955149]
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:09][SAL ]sock send credit cnt[6]
[D][05:19:09][SAL ]sock send ind credit cnt[6]
[D][05:19:09][M2M ]m2m send data len[134]
[D][05:19:09][SAL ]Cellular task submsg id[10]
[D][05:19:09][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:09][CAT1]gsm read msg sub id:

2025-07-31 17:58:08:898 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:08:912 ==>> 定位已等待【20】秒.
2025-07-31 17:58:08:935 ==>>  15
[D][05:19:09][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:09][CAT1]Send Data To Server[134][137] ... ->:
0043B686113311331133113311331B88BE0AB7641061295A69CD4412AC621D73CBEC4C0559EF49E1390FB426283621D8683974B0A05FD94B1EDDB6AD134F6F460CC887
[D][05:19:09][CAT1]<<< 
SEND OK

[D][05:19:09][CAT1]exec over: func id: 15, ret: 11
[D][05:19:09][CAT1]sub id: 15, ret: 11

[D][05:19:09][SAL ]Cellular task submsg id[68]
[D][05:19:09][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:09][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:09][M2M ]g_m2m_is_idle become true
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:09][PROT]M2M Send ok [1629955149]
$GBGGA,095812.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,43,26,,,43,24,,,43,13,,,42,1*7A

$GBGSV,6,2,23,21,,,42,42,,,42,60,,,41,8,,,41,1*48

$GBGSV,6,3,23,3,,,41,59,,,41,39,,,41,16,,,40,1*41

$GBGSV,6,4,23,9,,,39,1,,,39,6,,,38,14,,,38,1*4E

$GBGSV,6,5,23,2,,,37,33,,,36,4,,,35,45,,,34,1*73

$GBGSV,6,6,23,5,,,34,7,,,31,10,,,30,1*72

$GBRMC,095812.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095812.501,0.000,801.567,801.567,

2025-07-31 17:58:08:947 ==>> 733.052,2097152,2097152,2097152*62



2025-07-31 17:58:09:730 ==>> $GBGGA,095813.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,26,,,43,24,,,43,13,,,42,38,,,42,1*7B

$GBGSV,6,2,23,21,,,42,60,,,41,8,,,41,3,,,41,1*7E

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,39,1,,,38,6,,,38,14,,,37,1*40

$GBGSV,6,5,23,2,,,36,33,,,36,4,,,35,5,,,34,1*46

$GBGSV,6,6,23,45,,,33,7,,,31,10,,,30,1*41

$GBRMC,095813.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095813.501,0.000,796.171,796.171,728.118,2097152,2097152,2097152*66

[D][05:19:10][COMM]read battery soc:255


2025-07-31 17:58:09:913 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:09:924 ==>> 定位已等待【21】秒.
2025-07-31 17:58:10:706 ==>> $GBGGA,095814.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,38,,,42,26,,,42,1*7A

$GBGSV,6,2,23,21,,,42,42,,,42,60,,,41,8,,,41,1*48

$GBGSV,6,3,23,3,,,41,59,,,41,39,,,41,16,,,40,1*41

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,14,,,38,1*4E

$GBGSV,6,5,23,2,,,36,33,,,36,4,,,35,5,,,34,1*46

$GBGSV,6,6,23,45,,,33,7,,,31,10,,,30,1*41

$GBRMC,095814.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095814.501,0.000,796.170,796.170,728.117,2097152,2097152,2097152*6E



2025-07-31 17:58:10:921 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:10:931 ==>> 定位已等待【22】秒.
2025-07-31 17:58:11:757 ==>> $GBGGA,095815.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,38,,,42,26,,,42,1*7A

$GBGSV,6,2,23,21,,,42,60,,,41,8,,,41,3,,,41,1*7E

$GBGSV,6,3,23,59,,,41,39,,,41,42,,,41,16,,,40,1*74

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,45,,,34,1*44

$GBGSV,6,6,23,5,,,34,7,,,30,10,,,30,1*73

$GBRMC,095815.501,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095815.501,0.000,795.270,795.270,727.294,2097152,2097152,2097152*68

[D][05:19:12][COMM]read battery soc:255


2025-07-31 17:58:11:924 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:11:934 ==>> 定位已等待【23】秒.
2025-07-31 17:58:12:715 ==>> $GBGGA,095816.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,21,,,42,1*7B

$GBGSV,6,2,23,60,,,41,13,,,41,3,,,41,59,,,41,1*48

$GBGSV,6,3,23,39,,,41,42,,,41,8,,,40,16,,,40,1*41

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,2,,,37,1*76

$GBGSV,6,5,23,14,,,37,33,,,36,4,,,35,5,,,34,1*70

$GBGSV,6,6,23,45,,,33,7,,,30,10,,,30,1*40

$GBRMC,095816.501,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095816.501,0.000,792.572,792.572,724.826,2097152,2097152,2097152*6B



2025-07-31 17:58:12:927 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:12:937 ==>> 定位已等待【24】秒.
2025-07-31 17:58:13:796 ==>> $GBGGA,095817.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,26,,,43,24,,,43,13,,,42,38,,,42,1*7B

$GBGSV,6,2,23,21,,,42,42,,,42,60,,,41,8,,,41,1*48

$GBGSV,6,3,23,3,,,41,39,,,41,59,,,40,16,,,40,1*40

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,23,2,,,36,33,,,36,4,,,35,45,,,34,1*72

$GBGSV,6,6,23,5,,,34,7,,,30,10,,,30,1*73

$GBRMC,095817.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,095817.501,0.000,795.274,795.274,727.298,2097152,2097152,2097152*66

[D][05:19:14][COMM]read battery soc:255
[D][05:19:14][PROT]CLEAN,SEND:2
[D][05:19:14][PROT]CLEAN:2


2025-07-31 17:58:13:932 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:13:943 ==>> 定位已等待【25】秒.
2025-07-31 17:58:14:935 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 17:58:14:945 ==>> 定位已等待【26】秒.
2025-07-31 17:58:15:567 ==>> $GBGGA,095814.508,2301.2566987,N,11421.9411946,E,1,15,0.92,68.756,M,-1.770,M,,*5B

$GBGSA,A,3,13,08,26,16,06,38,39,42,09,24,21,14,1.35,0.92,0.98,4*04

$GBGSA,A,3,33,07,45,,,,,,,,,,1.35,0.92,0.98,4*03

$GBGSV,6,1,23,13,83,274,42,8,82,173,41,26,68,32,43,16,66,306,40,1*76

$GBGSV,6,2,23,6,65,300,38,38,65,163,42,39,64,337,41,3,62,191,41,1*75

$GBGSV,6,3,23,42,62,359,41,9,58,272,38,24,57,224,43,59,52,130,41,1*49

$GBGSV,6,4,23,21,48,103,42,1,48,126,38,2,46,239,36,60,41,238,41,1*7F

$GBGSV,6,5,23,14,38,331,37,4,32,112,34,5,22,258,34,33,14,321,36,1*72

$GBGSV,6,6,23,7,14,177,30,45,11,39,34,10,8,192,30,1*7B

$GBRMC,095814.508,A,2301.2566987,N,11421.9411946,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:15][GNSS]HD8040 GPS
[D][05:19:15][GNSS]GPS diff_sec 124000739, report 0x42 frame
$GBGST,095814.508,0.946,0.319,0.298,0.379,2.980,3.079,7.548*77

[D][05:19:15][COMM]Main Task receive event:131
[D][05:19:15][COMM]index:0,power_mode:0xFF
[D][05:19:15][COMM]index:1,sound_mode:0xFF
[D][05:19:15][COMM]index:2,gsensor_mode:0xFF
[D][05:19:15][COMM]index:3,report_freq_mode:0xFF
[D][05:19:15][COMM]index:4,report_period:0xFF
[D][05:19:15][COMM]index:5,norm

2025-07-31 17:58:15:673 ==>> al_reset_mode:0xFF
[D][05:19:15][COMM]index:6,normal_reset_period:0xFF
[D][05:19:15][COMM]index:7,spock_over_speed:0xFF
[D][05:19:15][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:15][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:15][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:15][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:15][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:15][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:15][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:15][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:15][COMM]index:16,imu_config_params:0xFF
[D][05:19:15][COMM]index:17,long_connect_params:0xFF
[D][05:19:15][COMM]index:18,detain_mark:0xFF
[D][05:19:15][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:15][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:15][COMM]index:21,mc_mode:0xFF
[D][05:19:15][COMM]index:22,S_mode:0xFF
[D][05:19:15][COMM]index:23,overweight:0xFF
[D][05:19:15][COMM]index:24,standstill_mode:0xFF
[D][05:19:15][COMM]index:25,night_mode:0xFF
[D][05:19:15][COMM]index:26,experiment1:0xFF
[D][05:19:15][COMM]index:27,experiment2:0xFF
[D][05:19:15][COMM]index:28,experiment3:0xFF
[D][05:19:15][COMM]index

2025-07-31 17:58:15:778 ==>> :29,experiment4:0xFF
[D][05:19:15][COMM]index:30,night_mode_start:0xFF
[D][05:19:15][COMM]index:31,night_mode_end:0xFF
[D][05:19:15][COMM]index:33,park_report_minutes:0xFF
[D][05:19:15][COMM]index:34,park_report_mode:0xFF
[D][05:19:15][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:15][COMM]index:38,charge_battery_para: FF
[D][05:19:15][COMM]index:39,multirider_mode:0xFF
[D][05:19:15][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:15][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:15][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:15][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:15][COMM]index:44,riding_duration_config:0xFF
[D][05:19:15][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:15][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:15][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:15][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:15][COMM]index:49,mc_load_startup:0xFF
[D][05:19:15][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:15][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:15][COMM]index:52,traffic_mode:0xFF
[D][05:19:15][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:15][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:

2025-07-31 17:58:15:884 ==>> 15][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:15][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:15][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:15][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:15][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:15][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:15][COMM]index:63,experiment5:0xFF
[D][05:19:15][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:15][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:15][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:15][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:15][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:15][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:15][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:15][COMM]index:72,experiment6:0xFF
[D][05:19:15][COMM]index:73,experiment7:0xFF
[D][05:19:15][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:15][COMM]index:75,zero_value_from_server:-1
[D][05:19:15][COMM]index:76,multirider_threshold:255
[D][05:19:15][COMM]index:77,experiment8:255
[D][05:19:15][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:15][COMM]index:79,temp_park_tail_light_twinkl

2025-07-31 17:58:15:943 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 17:58:15:950 ==>> 
北斗星号:【13】,信号值:【42】
北斗星号:【8】,信号值:【41】
北斗星号:【26】,信号值:【43】
北斗星号:【16】,信号值:【40】
北斗星号:【6】,信号值:【38】
北斗星号:【38】,信号值:【42】
北斗星号:【39】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【42】,信号值:【41】
北斗星号:【9】,信号值:【38】
北斗星号:【24】,信号值:【43】
北斗星号:【59】,信号值:【41】
北斗星号:【21】,信号值:【42】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【36】
北斗星号:【60】,信号值:【41】
北斗星号:【14】,信号值:【37】
北斗星号:【33】,信号值:【36】

2025-07-31 17:58:15:974 ==>> 检测【CSQ强度】
2025-07-31 17:58:15:983 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 17:58:16:004 ==>> e_duration:255
[D][05:19:15][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:15][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:15][COMM]index:83,loc_report_interval:255
[D][05:19:15][COMM]index:84,multirider_threshold_p2:255
[D][05:19:15][COMM]index:85,multirider_strategy:255
[D][05:19:15][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:15][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:15][COMM]index:90,weight_param:0xFF
[D][05:19:15][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:15][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:15][COMM]index:95,current_limit:0xFF
[D][05:19:15][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:15][COMM]index:100,location_mode:0xFF

[W][05:19:15][PROT]remove success[1629955155],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:15][PROT]add success [1629955155],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:15][COMM]Main Task receive event:131 finished processing
[D][05:19:15][PROT]index:0 1629955155
[D][05:19:15][PROT]is_send:0
[D][05:19:15][PROT]sequence_num:7
[D][05:19:15][PROT]retry_timeout:0
[D][05:19:15][PROT]retry_times:

2025-07-31 17:58:16:093 ==>> 1
[D][05:19:15][PROT]send_path:0x2
[D][05:19:15][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:15][PROT]===========================================================
[W][05:19:15][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955155]
[D][05:19:15][PROT]===========================================================
[D][05:19:15][PROT]sending traceid [9999999999900008]
[D][05:19:15][PROT]Send_TO_M2M [1629955155]
[D][05:19:15][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:15][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:15][SAL ]sock send credit cnt[6]
[D][05:19:15][SAL ]sock send ind credit cnt[6]
[D][05:19:15][M2M ]m2m send data len[294]
[D][05:19:15][SAL ]Cellular task submsg id[10]
[D][05:19:15][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:15][CAT1]gsm read msg sub id: 15
[D][05:19:15][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:15][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88B26AC6EB90BABB90B114FA265E69B2EBA2DE288E441C10FC476F784ADB8BE8E71FD143A9D57B8E802772306492920

2025-07-31 17:58:16:198 ==>> 3C00EAC08266FD9BE2281003F901F4B6BE469F6713463D803B851E11649E28244ECF8077E5CF85445D4BCF5A8A4D3382B8E068066FB06E478CF514BC1EDBD97576969156712EFCC26F2C6AD31407EA7A47FFAED9B
[D][05:19:15][CAT1]<<< 
SEND OK

[D][05:19:15][CAT1]exec over: func id: 15, ret: 11
[D][05:19:15][CAT1]sub id: 15, ret: 11

[D][05:19:15][SAL ]Cellular task submsg id[68]
[D][05:19:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:15][M2M ]g_m2m_is_idle become true
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]M2M Send ok [1629955156]
$GBGGA,095815.008,2301.2572497,N,11421.9411468,E,1,15,0.93,69.620,M,-1.770,M,,*57

$GBGSA,A,3,13,08,26,16,06,38,39,42,09,24,21,14,1.35,0.93,0.98,4*05

$GBGSA,A,3,33,07,45,,,,,,,,,,1.35,0.93,0.98,4*02

$GBGSV,6,1,23,13,83,274,41,8,82,173,41,26,68,32,42,16,66,306,40,1*74

$GBGSV,6,2,23,6,65,300,38,38,65,163,4

2025-07-31 17:58:16:303 ==>>                                                                                                                                                                                                                                                                

2025-07-31 17:58:16:408 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ,A,2301.2573950,N,11421.9412286,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,095816.000,1.636,0.386,0.357,0.450,1.709,1.835,4.791*72

[W][05:19:17][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21


2025-07-31 17:58:16:571 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 17:58:16:578 ==>> 检测【关闭GSM联网】
2025-07-31 17:58:16:585 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 17:58:16:731 ==>> [W][05:19:17][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:17][COMM]GSM test
[D][05:19:17][COMM]GSM test disable


2025-07-31 17:58:16:857 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 17:58:16:866 ==>> 检测【4G联网测试】
2025-07-31 17:58:16:872 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 17:58:17:884 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:18][COMM]Main Task receive event:14
[D][05:19:18][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955158, allstateRepSeconds = 0
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:1

2025-07-31 17:58:17:988 ==>> 8,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][

2025-07-31 17:58:18:093 ==>> COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:

2025-07-31 17:58:18:198 ==>> 19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display

2025-07-31 17:58:18:303 ==>>  threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][PROT]index:0 1629955158
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][PROT]is_send:0
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][PROT]sequence_num:8
[D][05:19:18][PROT]retry_timeout:0
[D][05:19:18][PROT]retry_times:1
[D][05:19:18][PROT]send_path:0x2
[D][05:19:18][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:18][PROT]===========================================================
[W][05:19:18][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]sending traceid [9999999999900009]
[D][05:19:18][PROT]Send_TO_M2M [1629955158]
[D][05:19:18][CAT1]gsm read msg sub id: 13
[D][05:19:18][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:18][SAL ]sock send credit cnt[6]
[D][05:19:18][SAL ]sock send 

2025-07-31 17:58:18:408 ==>> ind credit cnt[6]
[D][05:19:18][M2M ]m2m send data len[294]
[D][05:19:18][SAL ]Cellular task submsg id[10]
[D][05:19:18][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:18][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:18][CAT1]exec over: func id: 13, ret: 21
[D][05:19:18][M2M ]get csq[21]
[D][05:19:18][CAT1]gsm read msg sub id: 15
[D][05:19:18][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:18][CAT1]Send Data To Server[294][297] ... ->:
0093B987113311331133113311331B88B13598581A52195849A8C0286BA99EA4763CF459A7C51AB37E2CD8CBFB95FCB88EC29A260EEC0B6534EBABA5AA9E5795A3AC2E1B20F6ED64B2E2F518C5A176B886249A1E6FCBBF69AD86F81D6E8B9DA1B4EA02E5A660386BEFE0CECEB95527CEAC5500CF94D5C932C558566BCD50966E28CA5A155E062E2237384A0CD4E98ECDEF54ED
[D][05:19:18][CAT1]<<< 
SEND OK

[D][05:19:18][CAT1]exec over: func id: 15, ret: 11
[D][05:19:18][CAT1]sub id: 15, ret: 11

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:18][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:18][M2M ]g_m2m_is_idle become true
[D][05:19:18][M2M ]m2m 

2025-07-31 17:58:18:513 ==>> switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:18][PROT]M2M Send ok [1629955158]
$GBGGA,095817.000,2301.2574556,N,11421.9412653,E,1,15,0.93,70.289,M,-1.770,M,,*51

$GBGSA,A,3,13,08,26,16,06,38,39,42,09,24,21,14,1.35,0.93,0.98,4*05

$GBGSA,A,3,33,07,45,,,,,,,,,,1.35,0.93,0.98,4*02

$GBGSV,6,1,23,13,83,274,41,8,82,173,40,26,68,32,42,16,66,306,39,1*7B

$GBGSV,6,2,23,6,65,300,38,38,65,163,42,39,64,337,40,3,62,191,40,1*75

$GBGSV,6,3,23,42,62,359,41,9,58,272,38,24,57,224,42,59,52,130,40,1*49

$GBGSV,6,4,23,21,48,103,41,1,48,126,38,2,46,239,36,60,41,238,40,1*7D

$GBGSV,6,5,23,14,38,331,37,4,32,112,35,5,22,258,34,33,14,321,36,1*73

$GBGSV,6,6,23,7,14,177,30,45,11,39,33,10,8,192,30,1*7C

$GBGSV,2,1,08,26,68,32,41,38,65,163,40,39,64,337,40,42,62,359,42,5*4F

$GBGSV,2,2,08,24,57,224,42,21,48,103,40,33,14,321,30,45,11,39,33,5*48

$GBRMC,095817.000,A,2301.2574556,N,11421.9412653,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,095817.000

2025-07-31 17:58:18:618 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 17:58:18:897 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 17:58:18:906 ==>> 检测【关闭GPS】
2025-07-31 17:58:18:914 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 17:58:19:203 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]stop event:8
[D][05:19:20][GNSS]GPS stop. ret=0
[D][05:19:20][GNSS]all continue location stop
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]all sing location stop
[D][05:19:20][CAT1]gsm read msg sub id: 24
[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 24, ret: 6
[D][05:19:20][CAT1]sub id: 24, ret: 6



2025-07-31 17:58:19:435 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 17:58:19:443 ==>> 检测【清空消息队列2】
2025-07-31 17:58:19:450 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 17:58:19:634 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:20][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 17:58:19:712 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 17:58:19:721 ==>> 检测【轮动检测】
2025-07-31 17:58:19:730 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 17:58:19:755 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 17:58:19:844 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 17:58:19:949 ==>> [D][05:19:21][COMM]Wheel signal detected, lock state = 2, singal = 1
[D][05:19:21][GNSS]recv submsg id[1]
[D][05:19:21][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:21][GNSS]location stop evt done evt


2025-07-31 17:58:20:224 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 17:58:20:349 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 17:58:20:503 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 17:58:20:511 ==>> 检测【关闭小电池】
2025-07-31 17:58:20:536 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 17:58:20:641 ==>> 6A A6 02 A6 6A 


2025-07-31 17:58:20:746 ==>> Battery OFF
OVER 150


2025-07-31 17:58:20:779 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 17:58:20:792 ==>> 检测【进入休眠模式】
2025-07-31 17:58:20:799 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 17:58:20:912 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 17:58:21:002 ==>> [D][05:19:22][COMM]Main Task receive event:28
[D][05:19:22][COMM]main task tmp_sleep_event = 8
[D][05:19:22][COMM]prepare to sleep
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 17:58:21:291 ==>> [D][05:19:22][COMM]S->M yaw:INVALID


2025-07-31 17:58:21:876 ==>> [D][05:19:22][COMM]IMU: [0,3,-979] ret=23 AWAKE!
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 12, ret: 6
[D][05:19:22][M2M ]tcpclient close[4]
[D][05:19:22][SAL ]Cellular task submsg id[12]
[D][05:19:22][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:22][CAT1]gsm read msg sub id: 9
[D][05:19:22][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:22][COMM]read battery soc:255
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 9, ret: 6
[D][05:19:22][CAT1]sub id: 9, ret: 6

[D][05:19:22][SAL ]Cellular task submsg id[68]
[D][05:19:22][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:22][SAL ]socket close ind. id[4]
[D][05:19:22][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:22][COMM]1x1 frm_can_tp_send ok
[D][05:19:22][CAT1]pdpdeact urc len[22]


2025-07-31 17:58:22:153 ==>> [E][05:19:23][COMM]1x1 rx timeout
[D][05:19:23][COMM]1x1 frm_can_tp_send ok


2025-07-31 17:58:22:668 ==>> [D][05:19:23][COMM]M->S yaw:INVALID
[E][05:19:23][COMM]1x1 rx timeout
[E][05:19:23][COMM]1x1 tp timeout
[E][05:19:23][COMM]1x1 error -3.
[W][05:19:23][COMM]CAN STOP!
[D][05:19:23][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:23][COMM]------------ready to Power off Acckey 1------------
[D][05:19:23][COMM]------------ready to Power off Acckey 2------------
[D][05:19:23][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:23][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1300
[D][05:19:23][COMM]bat sleep fail, reason:-1
[D][05:19:23][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:23][COMM]accel parse set 0
[D][05:19:23][COMM]imu rest ok. 94682
[D][05:19:23][COMM]imu sleep 0
[W][05:19:23][COMM]now sleep


2025-07-31 17:58:22:870 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 17:58:22:886 ==>> 检测【检测33V休眠电流】
2025-07-31 17:58:22:900 ==>> 开始33V电流采样
2025-07-31 17:58:22:929 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:22:972 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 17:58:23:980 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 17:58:24:025 ==>> Current33V:????:16.37

2025-07-31 17:58:24:486 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:24:494 ==>> 【检测33V休眠电流】通过,【16.37uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 17:58:24:501 ==>> 该项需要延时执行
2025-07-31 17:58:26:503 ==>> 此处延时了:【2000】毫秒
2025-07-31 17:58:26:516 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 17:58:26:538 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:58:26:655 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2750mV
Get AD_V6 2014mV
Get AD_V7 1093mV
OVER 150


2025-07-31 17:58:27:528 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 17:58:27:541 ==>> 检测【打开小电池2】
2025-07-31 17:58:27:571 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 17:58:27:645 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 17:58:27:804 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 17:58:27:812 ==>> 该项需要延时执行
2025-07-31 17:58:28:317 ==>> 此处延时了:【500】毫秒
2025-07-31 17:58:28:328 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 17:58:28:353 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 17:58:28:455 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 17:58:28:594 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 17:58:28:601 ==>> 该项需要延时执行
2025-07-31 17:58:29:094 ==>> 此处延时了:【500】毫秒
2025-07-31 17:58:29:111 ==>> 检测【进入休眠模式2】
2025-07-31 17:58:29:132 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 17:58:29:154 ==>> [D][05:19:30][COMM]------------ready to Power on Acckey 1------------
[D][05:19:30][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:30][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:30][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:30][COMM]----- get Acckey 1 and value:1------------
[W][05:19:30][COMM]CAN START!
[D][05:19:30][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:30][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 101068
[D][05:19:30][COMM][Audio]exec status ready.
[D][05:19:30][CAT1]<<< 
OK

[D][05

2025-07-31 17:58:29:199 ==>> :19:30][CAT1]exec over: func id: 12, ret: 6
[D][05:19:30][COMM]imu wakeup ok. 101082
[D][05:19:30][COMM]imu wakeup 1
[W][05:19:30][COMM]wake up system, wakeupEvt=0x80
[D][05:19:30][COMM]frm_can_weigth_power_set 1
[D][05:19:30][COMM]Clear Sleep Block Evt
[D][05:19:30][COMM]Main Task receive event:28 finished processing


2025-07-31 17:58:29:550 ==>> [D][05:19:30][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:30][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:30][COMM]1x1 rx timeout
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][COMM]Main Task receive event:28
[D][05:19:30][COMM]prepare to sleep
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 6
[W][05:19:30][COMM]CAN STOP!
[D][05:19:30][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:30][COMM]------------ready to Power off Acckey 1------------
[D][05:19:30][COMM]------------ready to Power off Acckey 2------------
[D][05:19:30][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:30][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 130
[D][05:19:30][COMM]bat sleep fail, reason:-1
[D][05:19:30][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:30][COMM]accel parse set 0
[D][05:19:30][COMM]imu rest ok. 101529
[D][05:19:30][COMM]imu sleep 0
[W][05:19:30][COMM]now sleep


2025-07-31 17:58:29:648 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 17:58:29:657 ==>> 检测【检测小电池休眠电流】
2025-07-31 17:58:29:678 ==>> 开始小电池电流采样
2025-07-31 17:58:29:687 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:29:761 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 17:58:30:772 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 17:58:30:848 ==>> CurrentBattery:ƽ��:68.34

2025-07-31 17:58:31:275 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:31:283 ==>> 【检测小电池休眠电流】通过,【68.34uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 17:58:31:295 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 17:58:31:329 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 17:58:31:353 ==>> 5A A5 01 5A A5 


2025-07-31 17:58:31:442 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 17:58:31:559 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 17:58:31:568 ==>> 该项需要延时执行
2025-07-31 17:58:31:668 ==>> [D][05:19:32][COMM]------------ready to Power on Acckey 1------------
[D][05:19:32][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:32][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:32][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:32][COMM]----- get Acckey 1 and value:1------------
[W][05:19:32][COMM]CAN START!
[E][05:19:32][COMM]1x1 rx timeout
[E][05:19:32][COMM]1x1 tp timeout
[E][05:19:32][COMM]1x1 error -3.
[D][05:19:32][COMM]read battery soc:0
[D][05:19:32][CAT1]gsm read msg sub id: 12
[D][05:19:32][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:32][COMM][Audio]exec status ready.
[D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]exec over: func id: 12, ret: 6
[D][05:19:32][COMM]imu wakeup ok. 103658
[D][05:19:32][COMM]imu wakeup 1
[W][05:19:32][COMM]wake up system, wakeupEvt=0x80
[D][05:19:32][COMM]frm_can_weigth_power_set 1
[D][05:19:32][COMM]Clear Sleep Block Evt
[D][05:19:32][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:32][COMM]1x1 frm_can_tp_send ok


2025-07-31 17:58:31:958 ==>> [E][05:19:33][COMM]1x1 rx timeout
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 17:58:32:063 ==>> 此处延时了:【500】毫秒
2025-07-31 17:58:32:078 ==>> 检测【检测唤醒】
2025-07-31 17:58:32:098 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 17:58:32:118 ==>> [D][05:19:33][COMM]msg 02A0 loss. last_tick:103627. cur_tick:104138. period:50
[D][05:19:33][COMM]msg 02A4 loss. last_tick:103627. cur_tick:104138. period:50
[D][05:19:33][COMM]msg 02A5 loss. last_tick:103627. cur_tick:104139. period:50
[D][05:1

2025-07-31 17:58:32:131 ==>> 9:33][COMM]msg 02A6 loss. last_tick:103627. cur_tick:104139. period:50
[D][05:19:33][COMM]msg 02A7 loss. last_tick:103627. cur_tick:104139. period:50
[D][05:19:33][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 104140
[D][05:19:33][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 104140


2025-07-31 17:58:32:453 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[D][05:19:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:33][FCTY]DeviceID    = 460130071539041
[D][05:19:33][FCTY]HardwareID  = 867222087782647
[D][05:19:33][FCTY]MoBikeID    = 9999999999
[D][05:19:33][FCTY]LockID      = FFFFFFFFFF
[D][05:19:33][FCTY]BLEFWVersion= 105
[D][05:19:33][FCTY]BLEMacAddr   = F42F80383B97
[D][05:19:33][FCTY]Bat         = 3904 mv
[D][05:19:33][FCTY]Current     = 0 ma
[D][05:19:33][FCTY]VBUS        = 2600 mv
[D][05:19:33][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:33][FCTY]Ext battery vol = 32, adc = 1293
[D][05:19:33][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 25 mv
[D][05:19:33][FCTY]Bike Type flag is invalied
[D][05:19:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:33][FCTY]Bat1         = 378

2025-07-31 17:58:32:498 ==>> 9 mv
[D][05:19:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[E][05:19:33][COMM]1x1 rx timeout
[E][05:19:33][COMM]1x1 tp timeout
[E][05:19:33][COMM]1x1 error -3.
[D][05:19:33][COMM]Main Task receive event:28 finished processing


2025-07-31 17:58:32:593 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 17:58:32:621 ==>> 检测【关机】
2025-07-31 17:58:32:632 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 17:58:32:785 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             eriod:100
[D][05:19:33][COMM]msg 02C2 loss. last_tick:103627. cur_tick:104637. period:100
[D][05:19:33][COMM]msg 02E0 loss. last_tick:103627. cur_tick:104637. period:100
[D][05:19:33][COMM]msg 02E1 loss. last_tick:103627. cur_tick:104637. period:100
[D][05:19:33][COMM]msg 02E2 loss. last_tick:103627. cur_tick:104638. period:100
[D][05:19:33][COMM]msg 0300 loss. last_tick:103627. cur_tick:104638. period:100
[D][05:19:33][COMM]msg 0301 loss. last_tick:103627. cur_tick:104639. period:100
[D][05:19:33][COMM]bat msg 0240 loss. last_tick:103627. cur_tick:104639. period:100. j,i:1 54
[D][05:19:33][COMM]bat msg 0241 loss. last

2025-07-31 17:58:32:890 ==>> _tick:103627. cur_tick:104639. period:100. j,i:2 55
[D][05:19:33][COMM]bat msg 0242 loss. last_tick:103627. cur_tick:104640. period:100. j,i:3 56
[D][05:19:33][COMM]bat msg 0244 loss. last_tick:103627. cur_tick:104640. period:100. j,i:5 58
[D][05:19:33][COMM]bat msg 024E loss. last_tick:103627. cur_tick:104640. period:100. j,i:15 68
[D][05:19:33][COMM]bat msg 024F loss. last_tick:103627. cur_tick:104641. period:100. j,i:16 69
[D][05:19:33][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 104641
[D][05:19:33][COMM]CAN message bat fault change: 0x00000000->0x0001802E 104642
[D][05:19:33][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 104642
[W][05:19:33][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
                                                                                                                                                                                                                                                                                                                           

2025-07-31 17:58:33:040 ==>> [D][05:19:34][COMM]msg 0222 loss. last_tick:103627. cur_tick:105133. period:150
[D][05:19:34][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 105134


2025-07-31 17:58:33:268 ==>> [D][05:19:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:0------------
[D][05:19:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 17:58:33:614 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 17:58:34:112 ==>> [D][05:19:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]more than the number of battery plugs
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:34][COMM]Main Task receive event:65
[D][05:19:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:34][COMM]Main Task receive event:65 finished processing
[D][05:19:34][COMM]Main Task receive event:66
[D][05:19:34][COMM]Try to Auto Lock Bat
[D][05:19:34][COMM]Main Task receive event:66 finished processing
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:34][COMM]Bat auth off fail, error:-1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:

2025-07-31 17:58:34:217 ==>> 1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]Receive Bat Lock cmd 0
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:34][COMM]read file, len:10800, num:3
[D][05:19:34][COMM]Main Task receive event:60
[D][05:19:34][COMM]smart_helmet_vol=255,255
[D][05:19:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get soh error
[E][05:19:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:34][COMM]report elecbike
[W][05:19:34][PROT]remove success[1629955174],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:34][COMM]Main Task receive event:60 finished processing
[D][05:19:34][COMM]Main Task receive event:61
[D][05:19:34][COMM][D301]:type:3, trace id:280
[D][05:19:34][COMM]id

2025-07-31 17:58:34:322 ==>> [], hw[000
[D][05:19:34][COMM]get mcMaincircuitVolt error
[D][05:19:34][COMM]get mcSubcircuitVolt error
[D][05:19:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get bat work mode err
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][PROT]index:0
[D][05:19:34][PROT]is_send:1
[D][05:19:34][PROT]sequence_num:10
[D][05:19:34][PROT]retry_timeout:0
[D][05:19:34][PROT]retry_times:3
[D][05:19:34][PROT]send_path:0x3
[D][05:19:34][PROT]msg_type:0x5d03
[D][05:19:34][PROT]===========================================================
[W][05:19:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955174]
[D][05:19:34][PROT]===========================================================
[D][05:19:34][PROT]Sending traceid

2025-07-31 17:58:34:428 ==>> [999999999990000B]
[D][05:19:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:34][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:34][PROT]remove success[1629955174],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:34][COMM]Main Task receive event:61 finished processing
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:34][SAL ]open socket ind id[4], rst[0]
[D][05:19:34][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:34][SAL ]Cellular task submsg id[8]
[D][05:19:34][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:34][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:34][CAT1]gsm read msg sub id: 8
[D][05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:34][M2M ]m2m 

2025-07-31 17:58:34:532 ==>> switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:34][COMM]--->crc16:0xb8a
[D][05:19:34][COMM]read file success
[D][05:19:34][COMM]accel parse set 1
[D][05:19:34][COMM][Audio]mon:9,05:19:34
[D][05:19:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:34][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:34][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[W][05:19:34][COMM]Power Off
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:34][CAT1]TEST for CME ERR: +

2025-07-31 17:58:34:637 ==>> CME ERROR: 100
, 17, 2
[D][05:19:34][CAT1]<<< 
+CME ERROR: 100

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:34][COMM]read battery soc:255
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19

2025-07-31 17:58:34:683 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 17:58:34:713 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 17:58:34:743 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 17:58:34:768 ==>> :34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:34][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:34][COMM]arm_hub_enable: hub power: 0
[D][05:19:34][HSDK]hexlog index save 0 3584 188 @ 0 : 0
[D][05:19:34][HSDK]write save hexlog index [0]
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150
                              

2025-07-31 17:58:34:848 ==>> [D][05:19:35][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:3

2025-07-31 17:58:34:909 ==>> 5][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 17:58:34:957 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 17:58:34:972 ==>> 检测【检测小电池关机电流】
2025-07-31 17:58:34:992 ==>> 开始小电池电流采样
2025-07-31 17:58:35:003 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:35:058 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 17:58:36:060 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 17:58:36:105 ==>> CurrentBattery:ƽ��:66.80

2025-07-31 17:58:36:562 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 17:58:36:582 ==>> 【检测小电池关机电流】通过,【66.8uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 17:58:36:917 ==>> MES过站成功
2025-07-31 17:58:36:930 ==>> #################### 【测试结束】 ####################
2025-07-31 17:58:36:963 ==>> 关闭5V供电
2025-07-31 17:58:36:970 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 17:58:37:038 ==>> 5A A5 04 5A A5 


2025-07-31 17:58:37:143 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 17:58:37:953 ==>> 关闭5V供电成功
2025-07-31 17:58:37:966 ==>> 关闭33V供电
2025-07-31 17:58:37:988 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 17:58:38:043 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 17:58:38:957 ==>> 关闭33V供电成功
2025-07-31 17:58:38:971 ==>> 关闭3.7V供电
2025-07-31 17:58:38:984 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 17:58:39:048 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 17:58:39:855 ==>>  

2025-07-31 17:58:39:960 ==>> 关闭3.7V供电成功
