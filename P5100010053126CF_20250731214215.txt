2025-07-31 21:42:15:521 ==>> MES查站成功:
查站序号:P5100010053126CF验证通过
2025-07-31 21:42:15:528 ==>> 扫码结果:P5100010053126CF
2025-07-31 21:42:15:530 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:42:15:532 ==>> 测试参数版本:2024.10.11
2025-07-31 21:42:15:534 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:42:15:536 ==>> 检测【打开透传】
2025-07-31 21:42:15:537 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:42:15:590 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:42:15:919 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:42:15:993 ==>> 检测【检测接地电压】
2025-07-31 21:42:15:996 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:42:16:091 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:42:16:290 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:42:16:292 ==>> 检测【打开小电池】
2025-07-31 21:42:16:295 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:42:16:376 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:42:16:574 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:42:16:577 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:42:16:580 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:42:16:677 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:42:16:858 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:42:16:860 ==>> 检测【等待设备启动】
2025-07-31 21:42:16:863 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:42:17:110 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:42:17:307 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:42:17:896 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:42:17:957 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:42:18:002 ==>>                                                    

2025-07-31 21:42:18:403 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:42:18:879 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:42:18:968 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:42:18:971 ==>> 检测【产品通信】
2025-07-31 21:42:18:973 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:42:19:141 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:42:19:260 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:42:19:262 ==>> 检测【初始化完成检测】
2025-07-31 21:42:19:265 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:42:19:550 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:42:19:803 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:42:19:805 ==>> 检测【关闭大灯控制1】
2025-07-31 21:42:19:808 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:42:19:946 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:42:20:051 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:1

2025-07-31 21:42:20:081 ==>> 7:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:42:20:097 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:42:20:100 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:42:20:101 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:42:20:276 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:42:20:383 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:42:20:385 ==>> 检测【关闭仪表供电】
2025-07-31 21:42:20:387 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:42:20:576 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:42:20:675 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:42:20:677 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:42:20:679 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:42:20:862 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:42:20:967 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:42:20:974 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:42:20:976 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:42:20:977 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:42:21:147 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:42:21:267 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:42:21:270 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:42:21:272 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:21:447 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:42:21:555 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:42:21:568 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:42:21:571 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:42:21:687 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:42:21:762 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29


2025-07-31 21:42:21:837 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 21:42:21:862 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:42:21:864 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:42:21:866 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:42:21:942 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu 

2025-07-31 21:42:21:988 ==>> work error:[-1]. goto init
5A A5 03 5A A5 


2025-07-31 21:42:22:077 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 21:42:22:178 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:42:22:180 ==>> 该项需要延时执行
2025-07-31 21:42:22:504 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5010. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5011. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5011. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN messa

2025-07-31 21:42:22:534 ==>> ge bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 21:42:22:981 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:42:23:364 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:42:23:838 ==>>                                                                             [COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05

2025-07-31 21:42:23:943 ==>> :17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num

2025-07-31 21:42:24:048 ==>> :2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05

2025-07-31 21:42:24:108 ==>> :17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 21:42:24:153 ==>>                                                                                                                                                                                   

2025-07-31 21:42:25:006 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:42:25:836 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:42:26:000 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:42:26:182 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:42:26:186 ==>> 检测【33V输入电压ADC】
2025-07-31 21:42:26:190 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:26:485 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:17:58][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read throttle adc:7  volt:9 mv
[D][05:17:58][COMM]adc read battery ts volt:10 mv
[D][05:17:58][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:42:26:729 ==>> 【33V输入电压ADC】通过,【33007mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:42:26:745 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:42:26:749 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:42:26:789 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:42:27:013 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:015 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:42:27:033 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:42:27:040 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:042 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:42:27:044 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:42:27:066 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:42:27:069 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:42:27:091 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:42:27:095 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:42:27:123 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:42:27:126 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:42:27:198 ==>> 1A A1 00 00 FC 
Get AD_V2 1648mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1989mV
Get AD_V7 1094mV
OVER 150


2025-07-31 21:42:27:378 ==>> [D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E22217 10001
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F80C71E22217->0x0008F80C71E2223F 10023
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10024


2025-07-31 21:42:27:415 ==>> 【TP7_VCC3V3(ADV2)】通过,【1648mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:419 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:42:27:452 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:455 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:42:27:457 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:42:27:483 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:42:27:515 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:42:27:522 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:42:27:524 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:42:27:558 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:42:27:562 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:42:27:693 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 21:42:27:856 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:859 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:42:27:873 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][COMM]read battery soc:255


2025-07-31 21:42:27:894 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:27:898 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:42:27:901 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:42:27:938 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:42:27:943 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:42:27:976 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:42:27:979 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:42:28:016 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:42:28:018 ==>> 检测【打开WIFI(1)】
2025-07-31 21:42:28:020 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:42:28:268 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 21:42:28:313 ==>> 
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:42:28:553 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:42:28:556 ==>> 检测【清空消息队列(1)】
2025-07-31 21:42:28:559 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:42:28:705 ==>>                                                                                                                                                                  T+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087845709

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541225

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:42:28:810 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:42:29:021 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:42:29:097 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:42:29:100 ==>> 检测【打开GPS(1)】
2025-07-31 21:42:29:113 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:42:29:280 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:42:29:385 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1



2025-07-31 21:42:29:407 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:42:29:410 ==>> 检测【打开GSM联网】
2025-07-31 21:42:29:412 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:42:29:610 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:42:29:700 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:42:29:703 ==>> 检测【打开仪表供电1】
2025-07-31 21:42:29:707 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:42:29:880 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 21:42:29:985 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:42:29:987 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:42:29:989 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:42:30:184 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:42:30:272 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:42:30:275 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:42:30:278 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:42:30:473 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33247]


2025-07-31 21:42:30:565 ==>> 【读取主控ADC采集的仪表电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:42:30:570 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:42:30:573 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:30:773 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:42:30:853 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:42:30:855 ==>> 检测【AD_V20电压】
2025-07-31 21:42:30:857 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:30:956 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:31:091 ==>> 本次取值间隔时间:124ms
2025-07-31 21:42:31:096 ==>> [D][05:18:02][COMM]13728 imu init OK
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
00 00 00 00 00 
head err!


2025-07-31 21:42:31:151 ==>>                                                                               

2025-07-31 21:42:31:271 ==>> 本次取值间隔时间:170ms
2025-07-31 21:42:31:394 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:42:31:469 ==>> 本次取值间隔时间:186ms
2025-07-31 21:42:31:861 ==>> 本次取值间隔时间:384ms
2025-07-31 21:42:31:866 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:31:966 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:31:970 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:42:32:071 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:42:32:206 ==>> 本次取值间隔时间:225ms
2025-07-31 21:42:32:235 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:32:341 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:32:401 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.83.169.124"

OK

[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_

2025-07-31 21:42:32:506 ==>> DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][COMM]S->M yaw:INVALID
[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:42:32:611 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, val

2025-07-31 21:42:32:641 ==>> ue:136, enable_twinkle:0x0, power:1


2025-07-31 21:42:32:791 ==>> 本次取值间隔时间:442ms
2025-07-31 21:42:33:017 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:42:33:121 ==>> [D][05:18:04][COMM]M->S yaw:INVALID


2025-07-31 21:42:33:288 ==>> 本次取值间隔时间:484ms
2025-07-31 21:42:33:430 ==>> 本次取值间隔时间:133ms
2025-07-31 21:42:33:690 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:42:33:870 ==>> 本次取值间隔时间:431ms
2025-07-31 21:42:33:874 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:33:885 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,25,,,42,33,,,41,40,,,40,24,,,39,1*75

$GBGSV,3,2,09,39,,,39,41,,,39,34,,,38,60,,,42,1*7D

$GBGSV,3,3,09,59,,,40,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

[D][05:18:05][CAT1]<<< 
OK

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1646.400,1646.400,52.583,2097152,2097152,2097152*46

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 21:42:33:915 ==>>                                          

2025-07-31 21:42:33:975 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:34:020 ==>> [D][05:18:05][GNSS]recv submsg id[1]


2025-07-31 21:42:34:050 ==>> 
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:42:34:080 ==>> 本次取值间隔时间:90ms
2025-07-31 21:42:34:084 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:42:34:369 ==>> 本次取值间隔时间:284ms
2025-07-31 21:42:34:408 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:34:523 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:34:584 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:42:34:841 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,1,,,45,33,,,42,25,,,41,40,,,40,1*4F

$GBGSV,5,2,18,24,,,40,41,,,40,3,,,40,39,,,39,1*4C

$GBGSV,5,3,18,34,,,38,16,,,38,14,,,37,12,,,36,1*7E

$GBGSV,5,4,18,6,,,36,44,,,32,5,,,15,43,,,44,1*7A

$GBGSV,5,5,18,60,,,41,59,,,41,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1545.200,1545.200,49.613,2097152,2097152,2097152*46



2025-07-31 21:42:35:007 ==>> 本次取值间隔时间:479ms
2025-07-31 21:42:35:043 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:35:157 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:35:282 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
00 00 00 00 00 
head err!


2025-07-31 21:42:35:553 ==>> 本次取值间隔时间:386ms
2025-07-31 21:42:35:839 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,59,,,42,25,,,41,60,,,40,1*7C

$GBGSV,5,2,20,40,,,40,41,,,40,3,,,40,39,,,40,1*4B

$GBGSV,5,3,20,1,,,39,24,,,39,34,,,38,16,,,38,1*45

$GBGSV,5,4,20,14,,,38,12,,,36,6,,,36,44,,,35,1*48

$GBGSV,5,5,20,2,,,35,5,,,34,38,,,32,4,,,32,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1569.200,1569.200,50.202,2097152,2097152,2097152*4A



2025-07-31 21:42:35:900 ==>> 本次取值间隔时间:332ms
2025-07-31 21:42:35:930 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 21:42:36:364 ==>> 本次取值间隔时间:459ms
2025-07-31 21:42:36:824 ==>> 本次取值间隔时间:450ms
2025-07-31 21:42:36:830 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:36:869 ==>> $GBGGA,134240.680,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,59,,,42,25,,,41,60,,,40,1*7A

$GBGSV,6,2,24,40,,,40,41,,,40,3,,,40,39,,,40,1*4C

$GBGSV,6,3,24,1,,,39,24,,,39,34,,,38,16,,,38,1*42

$GBGSV,6,4,24,14,,,38,9,,,38,12,,,36,6,,,36,1*7B

$GBGSV,6,5,24,44,,,36,2,,,36,10,,,35,23,,,34,1*40

$GBGSV,6,6,24,5,,,34,38,,,33,4,,,33,7,,,38,1*41

$GBRMC,134240.680,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134240.680,0.000,1566.396,1566.396,50.103,2097152,2097152,2097152*58



2025-07-31 21:42:36:929 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:36:974 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:42:37:019 ==>> 本次取值间隔时间:76ms
2025-07-31 21:42:37:100 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:42:37:205 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:42:37:282 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 21:42:37:637 ==>> 本次取值间隔时间:420ms
2025-07-31 21:42:37:692 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:37:695 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:42:37:700 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:42:37:802 ==>> $GBGGA,134241.580,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,59,,,41,25,,,41,60,,,41,1*78

$GBGSV,7,2,25,40,,,40,41,,,40,3,,,40,39,,,40,1*4C

$GBGSV,7,3,25,24,,,40,1,,,39,34,,,38,16,,,38,1*4C

$GBGSV,7,4,25,14,,,38,7,,,38,9,,,37,12,,,36,1*7B

$GBGSV,7,5,25,6,,,36,44,,,36,2,,,36,10,,,35,1*75

$GBGSV,7,6,25,23,,,35,5,,,33,38,,,33,4,,,33,1*7D

$GBGSV,7,7,25,13,,,33,1*73

$GBRMC,134241.580,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134241.580,0.000,1558.830,1558.830,49.865,2097152,2097152,2097152*5B

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:42:37:907 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:42:38:012 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:42:38:014 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:42:38:018 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:42:38:198 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:42:38:328 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:42:38:332 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:42:38:334 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:42:38:381 ==>> 3A A3 02 01 A3 


2025-07-31 21:42:38:486 ==>> ON_OUT2
OVER 150


2025-07-31 21:42:38:620 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:42:38:623 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:42:38:629 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:42:38:833 ==>> $GBGGA,134242.560,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,59,,,41,25,,,41,60,,,40,1*7A

$GBGSV,7,2,26,40,,,40,41,,,40,3,,,40,39,,,40,1*4F

$GBGSV,7,3,26,24,,,39,1,,,39,34,,,38,16,,,38,1*41

$GBGSV,7,4,26,14,,,38,7,,,38,44,,,37,9,,,36,1*7B

$GBGSV,7,5,26,12,,,36,6,,,36,2,,,36,10,,,36,1*76

$GBGSV,7,6,26,23,,,35,5,,,33,38,,,33,4,,,33,1*7E

$GBGSV,7,7,26,13,,,33,11,,,33,1*70

$GBRMC,134242.560,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134242.560,0.000,1549.901,1549.901,49.580,2097152,2097152,2097152*50

[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:42:38:902 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:42:38:912 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:42:38:917 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:42:39:073 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:10][COMM]oneline display set 1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:42:39:189 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:42:39:195 ==>> 检测【AD_V21电压】
2025-07-31 21:42:39:199 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:42:39:285 ==>> 1A A1 20 00 00 
Get AD_V21 1646mV
OVER 150


2025-07-31 21:42:39:451 ==>> 本次取值间隔时间:259ms
2025-07-31 21:42:39:480 ==>> 【AD_V21电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:39:483 ==>> 检测【关闭仪表供电2】
2025-07-31 21:42:39:485 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:42:39:783 ==>> $GBGGA,134243.540,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,59,,,41,25,,,41,60,,,41,1*7B

$GBGSV,7,2,27,40,,,40,41,,,40,3,,,40,39,,,40,1*4E

$GBGSV,7,3,27,24,,,39,1,,,39,34,,,38,16,,,38,1*40

$GBGSV,7,4,27,7,,,38,14,,,37,44,,,37,9,,,36,1*75

$GBGSV,7,5,27,12,,,36,2,,,36,10,,,36,6,,,35,1*74

$GBGSV,7,6,27,23,,,35,38,,,34,4,,,34,13,,,34,1*4F

$GBGSV,7,7,27,5,,,33,11,,,33,42,,,33,1*40

$GBRMC,134243.540,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134243.540,0.000,1544.699,1544.699,49.410,2097152,2097152,2097152*5B

[W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:11][COMM]set POWER 0
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:42:39:949 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:42:40:028 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:42:40:031 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:42:40:036 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:42:40:174 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:11][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:42:40:308 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:42:40:312 ==>> 检测【打开AccKey2供电】
2025-07-31 21:42:40:316 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:42:40:446 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:42:40:596 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:42:40:600 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:42:40:604 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:40:734 ==>> $GBGGA,134244.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,40,41,,,40,39,,,40,40,,,39,1*7A

$GBGSV,7,3,28,24,,,39,1,,,39,34,,,38,16,,,38,1*4F

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,10,,,36,6,,,36,1*78

$GBGSV,7,6,28,23,,,35,38,,,34,4,,,34,13,,,34,1*40

$GBGSV,7,7,28,11,,,34,5,,,33,42,,,33,19,,,45,1*41

$GBRMC,134244.520,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134244.520,0.000,1547.765,1547.765,49.503,2097152,2097152,2097152*59



2025-07-31 21:42:40:960 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:12][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:12][COMM]adc read battery ts volt:9 mv
[D][05:18:12][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:42:41:212 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33361mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:42:41:216 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:42:41:249 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:42:41:339 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:42:41:570 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:42:41:574 ==>> 该项需要延时执行
2025-07-31 21:42:41:734 ==>> $GBGGA,134245.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,60,,,41,1*74

$GBGSV,7,2,28,3,,,40,41,,,40,39,,,39,40,,,39,1*41

$GBGSV,7,3,28,24,,,39,1,,,38,34,,,38,16,,,38,1*4E

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,6,,,36,10,,,35,1*7B

$GBGSV,7,6,28,23,,,35,38,,,34,4,,,34,13,,,34,1*40

$GBGSV,7,7,28,11,,,34,5,,,33,42,,,33,8,,,32,1*71

$GBRMC,134245.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134245.520,0.000,1535.432,1535.432,49.114,2097152,2097152,2097152*5A



2025-07-31 21:42:41:944 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:42:42:737 ==>> $GBGGA,134246.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,40,41,,,40,39,,,40,40,,,40,1*74

$GBGSV,7,3,28,24,,,39,1,,,39,34,,,38,16,,,38,1*4F

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,35,1*7B

$GBGSV,7,6,28,10,,,35,38,,,34,4,,,34,13,,,34,1*40

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134246.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134246.520,0.000,1539.879,1539.879,49.261,2097152,2097152,2097152*58



2025-07-31 21:42:43:726 ==>> $GBGGA,134247.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,40,41,,,40,39,,,40,40,,,40,1*74

$GBGSV,7,3,28,24,,,39,1,,,38,34,,,38,16,,,38,1*4E

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,36,9,,,36,1*74

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,35,38,,,34,4,,,34,13,,,34,1*40

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,34,8,,,31,1*72

$GBRMC,134247.520,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134247.520,0.000,1539.875,1539.875,49.257,2097152,2097152,2097152*5C



2025-07-31 21:42:43:936 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:42:44:572 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:42:44:577 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:42:44:582 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:44:905 ==>> $GBGGA,134248.520,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,41,41,,,40,40,,,40,39,,,39,1*7B

$GBGSV,7,3,28,24,,,39,1,,,39,34,,,38,16,,,38,1*4F

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,36,9,,,36,1*74

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,38,,,34,4,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,33,42,,,33,8,,,31,1*72

$GBRMC,134248.520,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134248.520,0.000,1539.880,1539.880,49.262,2097152,2097152,2097152*55

[D][05:18:16][COMM]S->M yaw:INVALID
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:16][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:16][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:9 mv
[D][05:18:16][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub

2025-07-31 21:42:44:950 ==>>  adc read vbat adc:2408  volt:3880 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:42:45:118 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【75mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:42:45:122 ==>> 检测【打开AccKey1供电】
2025-07-31 21:42:45:126 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:42:45:280 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:42:45:404 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:42:45:411 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:42:45:434 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:42:45:479 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 21:42:45:661 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 21:42:45:686 ==>> 【读取AccKey1电压(ADV14)前】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:42:45:691 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:42:45:696 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:45:736 ==>> $GBGGA,134249.520,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,40,41,,,40,40,,,40,39,,,40,1*74

$GBGSV,7,3,28,24,,,39,1,,,38,34,,,38,16,,,38,1*4E

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,38,,,34,4,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134249.520,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134249.520,0.000,1541.357,1541.357,49.306,2097152,2097152,2097152*57



2025-07-31 21:42:45:766 ==>>                                      

2025-07-31 21:42:46:036 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:17][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:17][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:17][COMM]adc read battery ts volt:6 mv
[D][05:18:17][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:17][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:17][COMM]read battery soc:255


2025-07-31 21:42:46:231 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5531mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:42:46:238 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:42:46:243 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:42:46:366 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:18][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:42:46:523 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:42:46:527 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:42:46:533 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:42:46:577 ==>> 1A A1 00 40 00 
Get AD_V14 2667mV
OVER 150


2025-07-31 21:42:46:682 ==>> $GBGGA,134250.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,40,41,,,40,40,,,40,39,,,40,1*74

$GBGSV,7,3,28,24,,,39,1,,,39,34,,,38,16,,,38,1*4F

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,38,,,34,4,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134250.520,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*2

2025-07-31 21:42:46:712 ==>> 0

$GBGST,134250.520,0.000,1542.838,1542.838,49.354,2097152,2097152,2097152*58



2025-07-31 21:42:46:787 ==>> 原始值:【2667】, 乘以分压基数【2】还原值:【5334】
2025-07-31 21:42:46:821 ==>> 【读取AccKey1电压(ADV14)后】通过,【5334mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:42:46:824 ==>> 检测【打开WIFI(2)】
2025-07-31 21:42:46:828 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:42:46:998 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:18][CAT1]gsm read msg sub id: 12
[D][05:18:18][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:18][CAT1]<<< 
OK

[D][05:18:18][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:42:47:105 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:42:47:111 ==>> 检测【转刹把供电】
2025-07-31 21:42:47:116 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:42:47:273 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:42:47:393 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:42:47:398 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:42:47:410 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:42:47:499 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:42:47:590 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 21:42:47:650 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 21:42:47:695 ==>> $GBGGA,134251.520,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,40,1*40

$GBGSV,7,2,28,60,,,40,41,,,40,40,,,40,39,,,39,1*7A

$GBGSV,7,3,28,24,,,39,1,,,38,34,,,38,16,,,38,1*4E

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,38,,,34,4,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

2025-07-31 21:42:47:725 ==>> 

$GBRMC,134251.520,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134251.520,0.000,1538.392,1538.392,49.207,2097152,2097152,2097152*5E



2025-07-31 21:42:47:732 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:42:47:744 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:42:47:747 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:42:47:845 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:42:47:905 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F88C21BCF57D,-35
+WIFISCAN:4,1,CC057790A621,-50
+WIFISCAN:4,2,F62A7D2297A3,-68
+WIFISCAN:4,3,CC057790A641,-74

[D][05:18:19][CAT1]wifi scan report total[4]
1A A1 01 00 00 
Get AD_V16 2437mV
OVER 150


2025-07-31 21:42:47:965 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:42:48:010 ==>> 原始值:【2437】, 乘以分压基数【2】还原值:【4874】
2025-07-31 21:42:48:048 ==>> 【读取AD_V16电压(前)】通过,【4874mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:42:48:052 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:42:48:059 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:48:100 ==>> [D][05:18:19][GNSS]recv submsg id[3]


2025-07-31 21:42:48:386 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:12 mv
[D][05:18:19][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3104  volt:5456 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:42:48:605 ==>> 【转刹把供电电压(主控ADC)】通过,【5456mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:42:48:610 ==>> 检测【转刹把供电电压】
2025-07-31 21:42:48:633 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:48:738 ==>> $GBGGA,134252.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,3,,,41,1*41

$GBGSV,7,2,28,60,,,41,41,,,40,40,,,40,39,,,39,1*7B

$GBGSV,7,3,28,24,,,39,1,,,39,34,,,38,16,,,38,1*4F

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,35,38,,,34,13,,,34,1*42

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134252.520,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134252.520,0.000,1544.318,1544.318,49.400,2097152,2097152,2097152*5C



2025-07-31 21:42:48:843 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                   99  volt:32855 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3094  volt:5438 mv
[D][05:18:20][COMM

2025-07-31 21:42:48:888 ==>> ]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:42:49:163 ==>> 【转刹把供电电压】通过,【5438mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:42:49:168 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:42:49:184 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:49:367 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:42:49:495 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:42:49:499 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:42:49:504 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:49:609 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:42:49:717 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:49:731 ==>> $GBGGA,134253.520,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,60,,,41,1*74

$GBGSV,7,2,28,3,,,40,41,,,40,40,,,40,39,,,40,1*41

$GBGSV,7,3,28,24,,,40,1,,,39,34,,,38,16,,,38,1*41

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,35,38,,,34,13,,,34,1*42

$GBGSV,7,7,28,11,,,34,5,,,33,42,,,33,8,,,31,1*72

$GBRMC,134253.520,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134253.520,0.000,1544.321,1544.321,49.403,2097152,2097152,2097152*5E

[W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:42:49:821 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:42:49:881 ==>> [W][05:18:21][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:42:49:957 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:42:49:960 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:42:49:964 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:42:49:972 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:50:061 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:42:50:166 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:42:50:181 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:42:50:271 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:42:50:347 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:42:50:377 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:42:50:420 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:42:50:424 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:42:50:438 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:42:50:482 ==>> 3A A3 03 01 A3 


2025-07-31 21:42:50:587 ==>> ON_OUT3
OVER 150


2025-07-31 21:42:50:692 ==>> $GBGGA,134254.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,60,,,41,1*74

$GBGSV,7,2,28,3,,,41,41,,,40,40,,,40,39,,,40,1*40

$GBGSV,7,3,28,24,,,40,1,,,39,34,,,38,16,,,38,1*41

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,35,38,,,34,13,,,34,1*42

$GBGSV,7,7,28,11,,,34

2025-07-31 21:42:50:709 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:42:50:715 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:42:50:718 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:42:50:737 ==>> ,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134254.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134254.520,0.000,1547.282,1547.282,49.497,2097152,2097152,2097152*54



2025-07-31 21:42:50:782 ==>> 3A A3 04 01 A3 


2025-07-31 21:42:50:887 ==>> ON_OUT4
OVER 150


2025-07-31 21:42:50:993 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:42:50:997 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:42:51:003 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:42:51:084 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:42:51:284 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:42:51:291 ==>> 检测【左刹电压测试1】
2025-07-31 21:42:51:302 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:51:593 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:1727  volt:2276 mv
[D][05:18:23][COMM]adc read right brake adc:1730  volt:2280 mv
[D][05:18:23][COMM]adc read throttle adc:1725  volt:2274 mv
[D][05:18:23][COMM]adc read battery ts volt:9 mv
[D][05:18:23][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]S->M yaw:INVALID
[D][05:18:23][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:42:51:698 ==>>                                                                                                                                                                                                                                                                                                                 ,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,34,38,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134255.520,V,,,,,,,,0.0,E,N,

2025-07-31 21:42:51:728 ==>> V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134255.520,0.000,1544.320,1544.320,49.402,2097152,2097152,2097152*59



2025-07-31 21:42:51:835 ==>> 【左刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 21:42:51:840 ==>> 检测【右刹电压测试1】
2025-07-31 21:42:51:872 ==>> 【右刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 21:42:51:876 ==>> 检测【转把电压测试1】
2025-07-31 21:42:51:904 ==>> 【转把电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 21:42:51:908 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:42:51:913 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:42:51:984 ==>> [D][05:18:23][COMM]read battery soc:255
3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 21:42:52:200 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:42:52:205 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:42:52:210 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:42:52:277 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:42:52:534 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:42:52:538 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:42:52:544 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:42:52:715 ==>> $GBGGA,134256.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,25,,,41,60,,,41,1*75

$GBGSV,7,2,28,3,,,41,41,,,40,40,,,40,39,,,40,1*40

$GBGSV,7,3,28,24,,,40,1,,,39,34,,,38,16,,,38,1*41

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,34,38,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,34,42,,,33,8,,,31,1*75

$GBRMC,134256.520,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134256.520,0.000,1547.287,1547.287,49.502,2097152,2097152,2097152*5B

3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:42:52:823 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:42:52:827 ==>> 检测【左刹电压测试2】
2025-07-31 21:42:52:832 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:42:53:090 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:24][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:24][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:24][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:24][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:24][COMM]adc read battery ts volt:6 mv
[D][05:18:24][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:24][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:24][COMM]M->S yaw:INVALID


2025-07-31 21:42:53:368 ==>> 【左刹电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 21:42:53:375 ==>> 检测【右刹电压测试2】
2025-07-31 21:42:53:401 ==>> 【右刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 21:42:53:406 ==>> 检测【转把电压测试2】
2025-07-31 21:42:53:435 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:42:53:440 ==>> 检测【晶振检测】
2025-07-31 21:42:53:448 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:42:53:745 ==>> $GBGGA,134257.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,42,25,,,41,60,,,41,1*77

$GBGSV,7,2,28,3,,,41,41,,,40,40,,,40,39,,,40,1*40

$GBGSV,7,3,28,24,,,40,1,,,39,34,,,38,16,,,38,1*41

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,34,38,,,34,13,,,34,1*43

$GBGSV,7,7,28,11,,,34,5,,,33,42,,,33,8,,,31,1*72

$GBRMC,134257.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134257.520,0.000,1545.808,1545.808,49.456,2097152,2097152,2097152*5A

[W][05:18:25][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:25][COMM][lf state:1][hf state:1]


2025-07-31 21:42:53:989 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:42:53:996 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:42:54:020 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:42:54:025 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:42:54:094 ==>> 1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1662mV
Get AD_V4 1647mV
Get AD_V5 2773mV
Get AD_V6 1992mV
Get AD_V7 1093mV
OVER 150


2025-07-31 21:42:54:293 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:42:54:300 ==>> 检测【检测BootVer】
2025-07-31 21:42:54:305 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:42:54:643 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541225
[D][05:18:26][FCTY]HardwareID  = 867222087845709
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E2D75DBE6C57
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 32, adc = 1301
[D][05:18:26][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_V

2025-07-31 21:42:54:748 ==>> ERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3825 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 21:42:54:843 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:42:54:847 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:42:54:854 ==>> 检测【检测固件版本】
2025-07-31 21:42:54:874 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:42:54:878 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:42:54:882 ==>> 检测【检测蓝牙版本】
2025-07-31 21:42:54:906 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:42:54:911 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:42:54:918 ==>> 检测【检测MoBikeId】
2025-07-31 21:42:54:939 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:42:54:946 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:42:54:950 ==>> 检测【检测蓝牙地址】
2025-07-31 21:42:54:956 ==>> 取到目标值:E2D75DBE6C57
2025-07-31 21:42:54:973 ==>> 【检测蓝牙地址】通过,【E2D75DBE6C57】符合目标值【】要求!
2025-07-31 21:42:54:991 ==>> 提取到蓝牙地址:E2D75DBE6C57
2025-07-31 21:42:54:997 ==>> 检测【BOARD_ID】
2025-07-31 21:42:55:006 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:42:55:010 ==>> 检测【检测充电电压】
2025-07-31 21:42:55:041 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:42:55:046 ==>> 检测【检测VBUS电压1】
2025-07-31 21:42:55:076 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:42:55:081 ==>> 检测【检测充电电流】
2025-07-31 21:42:55:109 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:42:55:114 ==>> 检测【检测IMEI】
2025-07-31 21:42:55:120 ==>> 取到目标值:867222087845709
2025-07-31 21:42:55:145 ==>> 【检测IMEI】通过,【867222087845709】符合目标值【】要求!
2025-07-31 21:42:55:152 ==>> 提取到IMEI:867222087845709
2025-07-31 21:42:55:157 ==>> 检测【检测IMSI】
2025-07-31 21:42:55:164 ==>> 取到目标值:460130071541225
2025-07-31 21:42:55:228 ==>> 【检测IMSI】通过,【460130071541225】符合目标值【】要求!
2025-07-31 21:42:55:232 ==>> 提取到IMSI:460130071541225
2025-07-31 21:42:55:254 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:42:55:258 ==>> 取到目标值:460130071541225
2025-07-31 21:42:55:292 ==>> 【校验网络运营商(移动)】通过,【460130071541225】符合目标值【】要求!
2025-07-31 21:42:55:301 ==>> 检测【打开CAN通信】
2025-07-31 21:42:55:320 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:42:55:392 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:42:55:602 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:42:55:610 ==>> 检测【检测CAN通信】
2025-07-31 21:42:55:634 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:42:55:767 ==>> $GBGGA,134259.520,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,60,,,41,1*74

$GBGSV,7,2,28,3,,,41,41,,,40,24,,,40,40,,,39,1*42

$GBGSV,7,3,28,39,,,39,1,,,39,34,,,38,16,,,38,1*43

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,36,1*78

$GBGSV,7,6,28,10,,,36,4,,,34,38,,,34,11,,,34,1*41

$GBGSV,7,7,28,5,,,34,13,,,33,42,,,33,8,,,31,1*70

$GBRMC,134259.520,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134259.520,0.000,1541.361,1541.361,49.309,2097152,2097152,2097152*59

can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:42:55:812 ==>> [D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38494
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:42:55:888 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:42:55:921 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:42:55:928 ==>> 检测【关闭CAN通信】
2025-07-31 21:42:55:935 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:42:55:952 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:42:55:993 ==>> [D][05:18:27][COMM]read battery soc:255
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:42:56:208 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:42:56:212 ==>> 检测【打印IMU STATE】
2025-07-31 21:42:56:220 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:42:56:382 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:42:56:507 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:42:56:513 ==>> 检测【六轴自检】
2025-07-31 21:42:56:517 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:42:56:772 ==>> $GBGGA,134300.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,59,,,41,25,,,41,60,,,41,1*7A

$GBGSV,8,2,29,3,,,40,41,,,40,24,,,40,40,,,40,1*43

$GBGSV,8,3,29,39,,,40,1,,,38,34,,,38,16,,,38,1*42

$GBGSV,8,4,29,7,,,38,14,,,38,44,,,37,9,,,36,1*7B

$GBGSV,8,5,29,12,,,36,2,,,36,23,,,36,6,,,36,1*76

$GBGSV,8,6,29,10,,,36,38,,,34,11,,,34,5,,,34,1*4E

$GBGSV,8,7,29,4,,,33,13,,,33,42,,,33,8,,,31,1*78

$GBGSV,8,8,29,46,,,37,1*7B

$GBRMC,134300.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134300.520,0.000,1539.882,1539.882,49.264,2097152,2097152,2097152*5E

[D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:42:57:759 ==>> $GBGGA,134301.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,25,,,41,60,,,41,1*74

$GBGSV,7,2,28,3,,,40,41,,,40,24,,,39,40,,,39,1*4D

$GBGSV,7,3,28,39,,,39,1,,,38,34,,,38,16,,,38,1*42

$GBGSV,7,4,28,7,,,38,14,,,38,44,,,37,9,,,36,1*75

$GBGSV,7,5,28,12,,,36,2,,,36,23,,,36,6,,,35,1*7B

$GBGSV,7,6,28,10,,,35,38,,,34,11,,,34,5,,,34,1*43

$GBGSV,7,7,28,4,,,33,13,,,33,42,,,33,8,,,31,1*76

$GBRMC,134301.520,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134301.520,0.000,764.806,764.806,699.433,2097152,2097152,2097152*60



2025-07-31 21:42:58:001 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 21:42:58:380 ==>> [D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:42:58:545 ==>> [D][05:18:30][COMM]Main Task receive event:142
[D][05:18:30][COMM]###### 41197 imu self test OK ######
[D][05:18:30][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-18,1,4044]
[D][05:18:30][COMM]Main Task receive event:142 finished processing


2025-07-31 21:42:58:610 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:42:58:617 ==>> 检测【打印IMU STATE2】
2025-07-31 21:42:58:624 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:42:58:725 ==>> $GBGGA,134302.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,60,,,41,59,,,41,1*40

$GBGSV,7,2,28,25,,,41,40,,,40,24,,,40,39,,,40,1*77

$GBGSV,7,3,28,41,,,40,7,,,38,14,,,38,1,,,38,1*71

$GBGSV,7,4,28,34,,,38,16,,,38,44,,,37,10,,,36,1*7F

$GBGSV,7,5,28,2,,,36,6,,,36,9,,,36,12,,,36,1*40

$GBGSV,7,6,28,23,,,36,38,,,34,5,,,34,4,,,34,1*74

$GBGSV,7,7,28,42,,,34,11,,,34,13,,,33,8,,,32,1*41

$GBRMC,134302.520,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134302.520,0.000,771.452,771.452,705.510,2097152,2097152,2097152*67



2025-07-31 21:42:58:830 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:30][COMM]YAW data: 32763[32763]
[D][05:18:30][COMM]pitch:-66 roll:0
[D][05:18:30][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:42:58:910 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:42:58:919 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:42:58:927 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:42:58:981 ==>> 5A A5 02 5A A5 


2025-07-31 21:42:59:086 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:42:59:191 ==>> [D][05:18:30][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 2,vo

2025-07-31 21:42:59:221 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:42:59:228 ==>> 检测【检测VBUS电压2】
2025-07-31 21:42:59:237 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:42:59:251 ==>> lt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:42:59:566 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071541225
[D][05:18:31][FCTY]HardwareID  = 867222087845709
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = E2D75DBE6C57
[D][05:18:31][FCTY]Bat         = 3944 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 11800 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 8, adc = 340
[D][05:18:31][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18

2025-07-31 21:42:59:611 ==>> :31][FCTY]Bat1         = 3825 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:42:59:716 ==>>                                                                                                                                                                                                                                                                                       36,1*7F

$GBGSV,7,5,28,2,,,36,6,,,36,9,,,36,12,,,36,1*40

$GBGSV,7,6,28,23,,,36,38,,,34,5,,,34,4,,,34,1*74

$GBGSV,7,7,28,11,,,34,13,,,33,42,,,33,8,,,31,1*45

$GBRM

2025-07-31 21:42:59:746 ==>> C,134303.520,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134303.520,0.000,770.722,770.722,704.843,2097152,2097152,2097152*6C



2025-07-31 21:43:00:755 ==>> $GBGGA,134304.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,25,,,42,33,,,42,59,,,41,3,,,40,1*43

$GBGSV,7,2,28,40,,,40,60,,,40,39,,,40,41,,,40,1*74

$GBGSV,7,3,28,1,,,39,24,,,39,7,,,38,34,,,38,1*7F

$GBGSV,7,4,28,16,,,38,14,,,37,44,,,37,2,,,36,1*41

$GBGSV,7,5,28,6,,,36,9,,,36,12,,,36,10,,,35,1*70

$GBGSV,7,6,28,23,,,35,13,,,34,5,,,34,4,,,34,1*7E

$GBGSV,7,7,28,42,,,34,11,,,34,38,,,33,8,,,31,1*4B

$GBRMC,134304.520,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134304.520,0.000,767.762,767.762,702.135,2097152,2097152,2097152*65



2025-07-31 21:43:00:830 ==>> [D][05:18:32][COMM]msg 0601 loss. last_tick:38492. cur_tick:43510. period:500
[D][05:18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43511


2025-07-31 21:43:01:044 ==>> [D][05:18:32][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 21:43:01:413 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:43:01:531 ==>> [D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[W][05:18:32][GNSS]stop locating
[D][05:18:32][GNSS]stop event:8
[D][05:18:32][GNSS]GPS stop. ret=0
[D][05:18:32][GNSS]all continue location stop
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]index:0
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [162995511

2025-07-31 21:43:01:636 ==>> 2]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900005]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][PROT]index:0 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900005]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][CAT1]gsm read msg sub id: 2

2025-07-31 21:43:01:741 ==>> 4
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 24, ret: 6
[D][05:18:32][CAT1]sub id: 24, ret: 6

[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5D47B5ABBFEE18302C6127ACB0F172AA96DF17499D96C600F0987888C6E0C8D40A613D9318202074141BD533ED8560DE6844BAAC99905EFAA0AFA628EBDB6B1F4C68FF0E44A6DDC42F56A9D1A749C8385DA05
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m

2025-07-31 21:43:01:771 ==>> 2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 21:43:02:042 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130071541225
[D][05:18:33][FCTY]HardwareID  = 867222087845709
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = E2D75DBE6C57
[D][05:18:33][FCTY]Bat         = 3864 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 5000 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 2, adc = 112
[D][05:18:33][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 50 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY

2025-07-31 21:43:02:087 ==>> ]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3825 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:43:02:192 ==>> [D][05:18:33][GNSS]recv submsg id[1]
[D][05:18:33][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:33][GNSS]location stop evt done evt


2025-07-31 21:43:02:209 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:43:02:537 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:34][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========
[D][05:18:34][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:34][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:34][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:34][FCTY]DeviceID    = 460130071541225
[D][05:18:34][FCTY]HardwareID  = 867222087845709
[D][05:18:34][FCTY]MoBikeID    = 9999999999
[D][05:18:34][FCTY]LockID      = FFFFFFFFFF
[D][05:18:34][FCTY]BLEFWVersion= 105
[D][05:18:34][FCTY]BLEMacAddr   = E2D75DBE6C57
[D][05:18:34][FCTY]Bat         = 3824 mv
[D][05:18:34][FCTY]Current     = 0 ma
[D][05:18:34][FCTY]VBUS        = 5000 mv
[D][05:18:34][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:34][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:34][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 0 mv
[D][05:18:34][FCTY]Bike Type flag is invalied
[D][05:18:34][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:34][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:1

2025-07-31 21:43:02:582 ==>> 8:34][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:34][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:34][FCTY]Bat1         = 3825 mv
[D][05:18:34][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:43:02:786 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:43:02:810 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:43:02:815 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:43:02:886 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:43:02:946 ==>> [D][05:18:34][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 21:43:03:021 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 21:43:03:124 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:43:03:130 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:43:03:145 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:43:03:187 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:43:03:432 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:43:03:437 ==>> 检测【打开WIFI(3)】
2025-07-31 21:43:03:467 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:43:03:572 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:43:03:729 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:43:03:741 ==>> 检测【扩展芯片hw】
2025-07-31 21:43:03:746 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:43:04:543 ==>> [D][05:18:36][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:0------------
[D][05:18:36][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:43:04:770 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:43:05:106 ==>>                                                                                                                                                                               1------------
[D][05:18:36][COMM]more than the number of battery plugs
[D][05:18:36][COMM]VBUS is 1
[D][05:18:36][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:36][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:36][COMM]Bat auth off fail, error:-1
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D]

2025-07-31 21:43:05:211 ==>> [05:18:36][COMM]read file, len:10800, num:3
[D][05:18:36][COMM]Main Task receive event:65
[D][05:18:36][COMM]main task tmp_sleep_event = 80
[D][05:18:36][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:36][COMM]Main Task receive event:65 finished processing
[D][05:18:36][COMM]Main Task receive event:66
[D][05:18:36][COMM]Try to Auto Lock Bat
[D][05:18:36][COMM]Main Task receive event:66 finished processing
[D][05:18:36][COMM]Main Task receive event:60
[D][05:18:36][COMM]smart_helmet_vol=255,255
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get soc error
[E][05:18:36][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:36][COMM]report elecbike
[D][05:18:36][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]--->crc16:0xb8a
[D][05:18:36][COMM]read file success
[D][05:18:36][COMM]Receive Bat Lock cmd 0
[D][05:18:36][COMM]VBUS is 1
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_

2025-07-31 21:43:05:316 ==>> ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:36][PROT]remove success[1629955116],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]index:1
[D][05:18:36][PROT]is_send:1
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x3
[D][05:18:36][PROT]msg_type:0x5d03
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]Sending traceid[9999999999900006]
[D][05:18:36][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:36][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:36

2025-07-31 21:43:05:421 ==>> ][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:36][PROT]add success [1629955116],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:36][COMM]Main Task receive event:60 finished processing
[D][05:18:36][COMM]Main Task receive event:61
[D][05:18:36][COMM][D301]:type:3, trace id:280
[D][05:18:36][COMM]id[], hw[000
[D][05:18:36][COMM]get mcMaincircuitVolt error
[D][05:18:36][COMM]get mcSubcircuitVolt error
[D][05:18:36][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get bat work state err
[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:36][COMM]Main Task receive event:61 finished processing
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][COMM]read battery soc:255


2025-07-31 21:43:05:741 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:37][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:37][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:37][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:37][COMM]48338 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][COMM]f:[drv_audio_ack_receive].wait ack timeout!![48369]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:43:05:830 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:43:05:838 ==>> 检测【扩展芯片boot】
2025-07-31 21:43:05:862 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:43:05:868 ==>> 检测【扩展芯片sw】
2025-07-31 21:43:05:893 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:43:05:898 ==>> 检测【检测音频FLASH】
2025-07-31 21:43:05:907 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:43:06:058 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:43:06:801 ==>> [D][05:18:38][PROT]CLEAN,SEND:0
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:3
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]SEND RAW data timeout
[D][05:18:38][CAT1]exec over: func id: 12, ret: -52
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,1

2025-07-31 21:43:06:861 ==>> 98

[D][05:18:38][COMM]49350 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[drv_audio_ack_receive].wait ack timeout!![49398]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:43:07:042 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 21:43:07:769 ==>> [D][05:18:39][COMM]50362 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:39][COMM]f:[drv_audio_ack_receive].wait ack timeout!![50422]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:43:08:691 ==>> [D][05:18:40][COMM]51375 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:09:040 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 21:43:09:714 ==>> [D][05:18:41][COMM]52387 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:09:942 ==>> [D][05:18:41][COMM]crc 108B
[D][05:18:41][COMM]flash test ok


2025-07-31 21:43:10:713 ==>> [D][05:18:42][COMM]53398 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:10:957 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:43:10:963 ==>> 检测【打开喇叭声音】
2025-07-31 21:43:10:989 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:43:11:048 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 21:43:11:153 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[frm_au

2025-07-31 21:43:11:183 ==>> dio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:43:11:242 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:43:11:248 ==>> 检测【打开大灯控制】
2025-07-31 21:43:11:256 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:43:11:444 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:43:11:533 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:43:11:539 ==>> 检测【关闭仪表供电3】
2025-07-31 21:43:11:550 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:43:11:781 ==>> [D][05:18:43][COMM]54410 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:43][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:43][COMM]set POWER 0


2025-07-31 21:43:11:825 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:43:11:832 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:43:11:845 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:43:11:947 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:43:12:117 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:43:12:123 ==>> 检测【读大灯电压】
2025-07-31 21:43:12:149 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:43:12:268 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:43][COMM]arm_hub read adc[5],val[33062]


2025-07-31 21:43:12:402 ==>> 【读大灯电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:43:12:408 ==>> 检测【关闭大灯控制2】
2025-07-31 21:43:12:424 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:43:12:544 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:43:12:690 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:43:12:699 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:43:12:705 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:43:12:740 ==>> [D][05:18:44][COMM]55422 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:12:845 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[92

2025-07-31 21:43:12:875 ==>> ]


2025-07-31 21:43:12:977 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:43:12:982 ==>> 检测【打开WIFI(4)】
2025-07-31 21:43:12:989 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:43:13:057 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 21:43:13:163 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:43:13:309 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:43:13:330 ==>> 检测【EC800M模组版本】
2025-07-31 21:43:13:335 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:13:751 ==>> [D][05:18:45][COMM]56434 imu init OK
[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:14:356 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:14:760 ==>> [D][05:18:46][COMM]57444 imu init OK
[D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:43:15:066 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 21:43:15:201 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:15:383 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:15:758 ==>> [D][05:18:47][COMM]imu error,enter wait


2025-07-31 21:43:16:428 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:16:736 ==>> [D][05:18:48][CAT1]exec over: func id: 15, ret: -93
[D][05:18:48][CAT1]sub id: 15, ret: -93

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:48][SAL ]socket send fail. id[4]
[D][05:18:48][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:48][CAT1]gsm read msg sub id: 12
[D][05:18:48][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:48][M2M ]m2m select fd[4]
[D][05:18:48][M2M ]socket[4] Link is disconnected
[D][05:18:48][M2M ]tcpclient close[4]
[D][05:18:48][SAL ]socket[4] has closed
[D][05:18:48][PROT]protocol read data ok
[E][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:48][PROT]M2M Send Fail [1629955128]
[D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 21:43:17:166 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59662]
[D][05:18:48][COMM]accel parse set 0
[D][05:18:48][COMM][Audio].l:[1032].open hexlog save
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:48][COMM]read file, len:15228, num:4
[D][05:18:48][COMM]--->crc16:0x419c
[D][05:18:48][COMM]read file success
[W][05:18:48][COMM][Audio].l:[936].close hexlog save
[D][05:18:48][COMM]accel parse set 1
[D][05:18:48][COMM][Audio]mon:9,05:18:48
[D][05:18:48][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:48][COMM]read battery soc:255


2025-07-31 21:43:17:240 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:17:468 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:18:106 ==>> [D][05:18:49][COMM]f:[drv_audio_ack_receive].wait ack timeout!![60762]
[D][05:18:49][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:49][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:43:18:501 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:19:130 ==>> [D][05:18:50][COMM]read battery soc:255
[D][05:18:50][COMM]f:[drv_audio_ack_receive].wait ack timeout!![61790]
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:43:19:311 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:19:540 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:19:663 ==>> [D][05:18:51][CAT1]SEND RAW data timeout
[D][05:18:51][CAT1]exec over: func id: 12, ret: -52
[W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:43:20:153 ==>> [D][05:18:51][COMM]f:[drv_audio_ack_receive].wait ack timeout!![62817]
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:51][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:43:20:443 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:20:579 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:21:085 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 21:43:21:616 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:21:661 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:22:656 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:22:685 ==>> [D][05:18:54][CAT1]SEND RAW data timeout
[D][05:18:54][CAT1]exec over: func id: 12, ret: -52
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 10
[D][05:18:54][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:43:22:946 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:23:081 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 21:43:23:698 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:24:689 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:24:749 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:25:107 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 21:43:25:447 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:25:795 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:26:739 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:26:844 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:27:105 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 21:43:27:886 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:27:948 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:28:783 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:28:918 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:29:105 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 21:43:29:954 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:30:452 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:30:665 ==>> [D][05:19:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:43:30:845 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:30:996 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:31:104 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 21:43:32:045 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:32:506 ==>> [D][05:19:04][COMM]f:[drv_audio_ack_receive].wait ack timeout!![75158]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM][Audio].l:[1032].open hexlog save
[D][05:19:04][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:43:32:889 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:32:949 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:33:085 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:33:116 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 21:43:34:110 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:34:952 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:35:120 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 21:43:35:150 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:35:451 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:36:177 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:36:987 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:37:123 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 21:43:37:214 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:37:938 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:38:261 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:38:688 ==>> [D][05:19:10][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:43:39:039 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:39:144 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 21:43:39:294 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:40:343 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:40:449 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:41:092 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:41:137 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 21:43:41:378 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:42:431 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:42:942 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:43:152 ==>> [W][05:19:14][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:14][COMM]read battery soc:255


2025-07-31 21:43:43:455 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:43:44:493 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:43:44:503 ==>> #################### 【测试结束】 ####################
2025-07-31 21:43:44:650 ==>> 关闭5V供电
2025-07-31 21:43:44:665 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:43:44:781 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:43:45:191 ==>> [D][05:19:16][COMM]read battery soc:255
[D][05:19:16][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:16][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:43:45:448 ==>> [D][05:19:17][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:43:45:659 ==>> 关闭5V供电成功
2025-07-31 21:43:45:669 ==>> 关闭33V供电
2025-07-31 21:43:45:681 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:43:45:779 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:43:45:976 ==>> [D][05:19:17][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 8,volt = 12
[D][05:19:17][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 21:43:46:659 ==>> 关闭33V供电成功
2025-07-31 21:43:46:668 ==>> 关闭3.7V供电
2025-07-31 21:43:46:690 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:43:46:780 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:43:46:885 ==>> [D][05:19:18][CAT1]exec over: func id: 10, ret: -43
[D][05:19:18][CAT1]sub id: 10, ret: -43

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
[D][05:19:18][M2M ]m2m gsm shut done, ret[1]
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
[D][05:19:18][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
[D][05:19:18][M2M ]g_m2m_is_idle become true
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:18][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
[D][05:19:18][PROT]index:1 1629955158
[D][05:19:18][PROT]is_send:0
[D][05:19:18][PROT]sequence_num:5
[D][05:19:18][PROT]re

2025-07-31 21:43:46:991 ==>> try_timeout:0
[D][05:19:18][PROT]retry_times:2
[D][05:19:18][PROT]send_path:0x2
[D][05:19:18][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:18][PROT]===========================================================
[W][05:19:18][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]sending traceid [9999999999900006]
[D][05:19:18][PROT]Send_TO_M2M [1629955158]
[D][05:19:18][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20053040], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][COMM]Main Task receive event:93
[D][05:19:18][COMM]main task tmp_sleep_event = 80
[W][05:19:18][PROT]rE

