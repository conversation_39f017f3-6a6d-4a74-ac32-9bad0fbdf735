2025-07-31 20:56:02:213 ==>> MES查站成功:
查站序号:P5100010053134C9验证通过
2025-07-31 20:56:02:229 ==>> 扫码结果:P5100010053134C9
2025-07-31 20:56:02:237 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:56:02:240 ==>> 测试参数版本:2024.10.11
2025-07-31 20:56:02:242 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:56:02:244 ==>> 检测【打开透传】
2025-07-31 20:56:02:247 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:56:02:361 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:56:02:513 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:56:02:519 ==>> 检测【检测接地电压】
2025-07-31 20:56:02:521 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:56:02:667 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:56:02:799 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:56:02:801 ==>> 检测【打开小电池】
2025-07-31 20:56:02:803 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:56:02:866 ==>> 6A A6 01 A6 6A 


2025-07-31 20:56:02:957 ==>> Battery ON
OVER 150


2025-07-31 20:56:03:086 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:56:03:089 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:56:03:091 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:56:03:168 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 20:56:03:377 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:56:03:382 ==>> 检测【等待设备启动】
2025-07-31 20:56:03:384 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:56:03:713 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:56:03:895 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:56:04:405 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:56:04:525 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:56:04:600 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:56:05:007 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:56:05:434 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:56:05:479 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:56:05:716 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:56:05:718 ==>> 检测【产品通信】
2025-07-31 20:56:05:720 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:56:06:176 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:56:06:373 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:56:06:744 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:56:07:021 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<


2025-07-31 20:56:07:066 ==>>                                         

2025-07-31 20:56:07:463 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:56:07:786 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:56:08:032 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:08:083 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:56:08:085 ==>> 检测【初始化完成检测】
2025-07-31 20:56:08:086 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:56:08:275 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 20:56:08:360 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:56:08:363 ==>> 检测【关闭大灯控制1】
2025-07-31 20:56:08:364 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:56:08:521 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:56:08:581 ==>>               COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:56:08:639 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:56:08:642 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:56:08:657 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:56:08:856 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:56:08:935 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:56:08:937 ==>> 检测【关闭仪表供电】
2025-07-31 20:56:08:942 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:56:09:006 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:09:201 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:56:09:490 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:56:09:494 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:56:09:496 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:56:09:617 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:56:09:791 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:56:09:793 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:56:09:796 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:56:09:924 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:56:10:014 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:10:082 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:56:10:085 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:56:10:088 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:10:254 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:56:10:385 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:56:10:387 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:56:10:389 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:56:10:466 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:56:10:526 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 25


2025-07-31 20:56:10:586 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:56:10:675 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:56:10:678 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:56:10:682 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:56:10:767 ==>> 5A A5 03 5A A5 


2025-07-31 20:56:10:857 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:56:10:982 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:56:10:985 ==>> 该项需要延时执行
2025-07-31 20:56:11:022 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:11:559 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5014. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5016. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5017. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E2221

2025-07-31 20:56:11:589 ==>> 7->0x0008F00C71E22217 5018
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5018


2025-07-31 20:56:12:021 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:12:126 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:56:12:628 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]

2025-07-31 20:56:12:733 ==>> ----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[

2025-07-31 20:56:12:838 ==>> D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],p

2025-07-31 20:56:12:898 ==>> riority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
                                         

2025-07-31 20:56:13:048 ==>> [D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:13:369 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:56:14:066 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:14:607 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:56:14:992 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:56:14:995 ==>> 检测【33V输入电压ADC】
2025-07-31 20:56:14:998 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:15:069 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:15:265 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3116  volt:5477 mv
[D][05:17:57][COMM]adc read out 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read left brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:6 mv
[D][05:17:57][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:56:15:534 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:56:15:538 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:56:15:541 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:56:15:666 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:56:15:811 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:15:813 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:56:15:836 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:15:838 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:56:15:841 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 20:56:15:860 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:56:15:863 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:56:15:884 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:56:15:887 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:56:15:911 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:56:15:914 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:56:15:973 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1994mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:56:16:078 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:16:197 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:16:201 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:56:16:222 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:16:224 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:56:16:227 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:56:16:247 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:56:16:249 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:56:16:272 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:56:16:274 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:56:16:301 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:56:16:304 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:56:16:370 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1994mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:56:16:430 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 20:56:16:597 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:16:599 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:56:16:622 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:16:624 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:56:16:627 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:56:16:630 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:56:16:657 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:56:16:659 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:56:16:682 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:56:16:684 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:56:16:735 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:56:16:737 ==>> 检测【打开WIFI(1)】
2025-07-31 20:56:16:749 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:56:17:322 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu def

2025-07-31 20:56:17:428 ==>> ault ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:56:17:502 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:56:17:554 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:56:17:558 ==>> 检测【清空消息队列(1)】
2025-07-31 20:56:17:561 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:56:17:730 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:56:17:834 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:56:17:837 ==>> 检测【打开GPS(1)】
2025-07-31 20:56:17:839 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:56:18:101 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:56:18:371 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:56:18:374 ==>> 检测【打开GSM联网】
2025-07-31 20:56:18:378 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:56:18:550 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:56:18:625 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:56:18:651 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:56:18:654 ==>> 检测【打开仪表供电1】
2025-07-31 20:56:18:657 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:56:18:854 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:56:18:939 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:56:18:943 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:56:18:948 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:56:19:160 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:56:19:220 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 20:56:19:274 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:56:19:296 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:56:19:298 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:56:19:803 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33293]
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087756021

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539194

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:56:20:073 ==>> 【读取主控ADC采集的仪表电压】通过,【33293mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:56:20:076 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:56:20:078 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:56:20:264 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:56:20:350 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:56:20:354 ==>> 检测【AD_V20电压】
2025-07-31 20:56:20:358 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:56:20:463 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:56:20:586 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:56:20:616 ==>>                                          

2025-07-31 20:56:20:799 ==>> 本次取值间隔时间:324ms
2025-07-31 20:56:20:824 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:56:20:939 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:56:21:095 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]14730 imu init OK


2025-07-31 20:56:21:125 ==>> 本次取值间隔时间:177ms
2025-07-31 20:56:21:162 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:56:21:263 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:56:21:369 ==>> 本次取值间隔时间:105ms
2025-07-31 20:56:21:373 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:56:21:629 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:56:21:659 ==>> 本次取值间隔时间:275ms
2025-07-31 20:56:21:708 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:56:21:811 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:56:21:947 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display 

2025-07-31 20:56:21:977 ==>> state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1653mV
OVER 150


2025-07-31 20:56:22:052 ==>> 本次取值间隔时间:235ms
2025-07-31 20:56:22:076 ==>> 【AD_V20电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:22:079 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:56:22:081 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:56:22:083 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:56:22:157 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:56:22:262 ==>>                                                                                                                                                                                                                                                                                                         

2025-07-31 20:56:22:322 ==>>                                                                                                                               [D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:56:22:393 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:56:22:396 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:56:22:398 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:56:22:564 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:56:22:669 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:56:22:709 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:56:22:713 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:56:22:716 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:56:22:759 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:56:23:034 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:56:23:091 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:56:23:094 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:56:23:098 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:56:23:261 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:56:23:408 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:56:23:411 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:56:23:415 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:56:23:552 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:56:23:657 ==>> [D

2025-07-31 20:56:23:687 ==>> ][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:56:23:728 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:56:23:731 ==>> 检测【AD_V21电压】
2025-07-31 20:56:23:735 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:56:23:807 ==>> 本次取值间隔时间:73ms
2025-07-31 20:56:23:868 ==>> 1A A1 20 00 00 
Get AD_V21 1647mV
OVER 150


2025-07-31 20:56:23:973 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,33,,,42,24,,,38,25,,,38,41,,,38,1*74

$GBGSV,2,2,08,39,,,37,14,,,41,60,,,40,59,,,38,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1600.230,1600.230,51.132,2097152,2097152,2097152*4B

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:

2025-07-31 20:56:24:003 ==>> 本次取值间隔时间:195ms
2025-07-31 20:56:24:033 ==>> 06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

[D][05:18:06][CAT1]opened : 0, 0
[D][05:18:06][SAL ]Cellular task submsg id[68]
[D][05:18:06][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:06][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:06][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:06][M2M ]g_m2m_is_idle become true
[D][05:18:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:56:24:049 ==>> 【AD_V21电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:24:052 ==>> 检测【关闭仪表供电2】
2025-07-31 20:56:24:057 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:56:24:108 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:56:24:288 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:56:24:326 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:56:24:329 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:56:24:333 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:56:24:548 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:56:24:600 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:56:24:603 ==>> 检测【打开AccKey2供电】
2025-07-31 20:56:24:608 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:56:24:836 ==>> [D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,42,59,,,41,60,,,40,24,,,40,1*7E

$GBGSV,4,2,13,25,,,40,14,,,39,41,,,39,39,,,38,1*70

$GBGSV,4,3,13,7,,,37,13,,,36,44,,,32,37,,,36,1*47

$GBGSV,4,4,13,3,,,36,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1598.014,1598.014,51.105,2097152,2097152,2097152*4F



2025-07-31 20:56:24:874 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:56:24:879 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:56:24:883 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:25:171 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3116  volt:5477 mv
[D][05:18:07][COMM]adc read out 24v adc:1295  volt:32754 mv
[D][05:18:07][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:07][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:07][COMM]adc read battery ts volt:7 mv
[D][05:18:07][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:56:25:408 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32754mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:56:25:412 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:56:25:414 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:56:25:525 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:56:25:692 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:56:25:695 ==>> 该项需要延时执行
2025-07-31 20:56:25:846 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,3,,,41,59,,,40,60,,,40,1*40

$GBGSV,5,2,19,24,,,40,25,,,40,41,,,39,39,,,39,1*77

$GBGSV,5,3,19,14,,,38,40,,,38,7,,,36,13,,,36,1*4C

$GBGSV,5,4,19,1,,,35,5,,,35,2,,,35,4,,,34,1*7C

$GBGSV,5,5,19,38,,,34,44,,,33,22,,,42,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1554.676,1554.676,49.725,2097152,2097152,2097152*42



2025-07-31 20:56:26:709 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:56:26:889 ==>> $GBGGA,125630.676,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,3,,,40,59,,,40,60,,,40,1*4A

$GBGSV,6,2,22,24,,,40,25,,,40,39,,,39,41,,,38,1*7D

$GBGSV,6,3,22,14,,,38,40,,,38,7,,,36,13,,,36,1*47

$GBGSV,6,4,22,1,,,36,2,,,35,38,,,35,16,,,35,1*78

$GBGSV,6,5,22,44,,,34,23,,,34,5,,,33,4,,,33,1*75

$GBGSV,6,6,22,34,,,31,9,,,37,1*4E

$GBRMC,125630.676,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125630.676,0.000,1526.063,1526.063,48.823,2097152,2097152,2097152*50



2025-07-31 20:56:27:803 ==>> $GBGGA,125631.576,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,3,,,40,59,,,40,1*4C

$GBGSV,6,2,24,60,,,40,24,,,40,39,,,39,41,,,39,1*7B

$GBGSV,6,3,24,14,,,38,40,,,38,7,,,36,13,,,36,1*41

$GBGSV,6,4,24,1,,,36,16,,,36,2,,,35,38,,,35,1*7D

$GBGSV,6,5,24,6,,,35,44,,,34,23,,,34,5,,,33,1*77

$GBGSV,6,6,24,4,,,33,10,,,33,34,,,32,12,,,29,1*4B

$GBRMC,125631.576,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125631.576,0.000,753.495,753.495,689.091,2097152,2097152,2097152*68



2025-07-31 20:56:28:703 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:56:28:708 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:56:28:735 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:28:793 ==>> $GBGGA,125632.556,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,25,,,41,60,,,40,1*44

$GBGSV,7,2,26,24,,,40,59,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,14,,,38,40,,,38,16,,,37,42,,,37,1*76

$GBGSV,7,4,26,7,,,36,13,,,36,1,,,36,2,,,35,1*44

$GBGSV,7,5,26,38,,,35,6,,,35,44,,,34,23,,,34,1*4C

$GBGSV,7,6,26,10,,,33,5,,,33,4,,,33,34,,,33,1*74

$GBGSV,7,7,26,8,,,30,12,,,30,1*49

$GBRMC,125632.556,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125632.556,0.000,752.038,752.038,687.759,2097152,2097152,2097152*64

[D][05:18:11][COMM]read battery soc:255


2025-07-31 20:56:29:019 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:2 mv
[D][05:18:11][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:56:29:241 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:56:29:245 ==>> 检测【打开AccKey1供电】
2025-07-31 20:56:29:248 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:56:29:432 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:56:29:518 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:56:29:524 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:56:29:528 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:56:29:663 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 20:56:29:753 ==>> $GBGGA,125633.536,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,59,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,2,26,24,,,40,25,,,40,39,,,39,14,,,38,1*78

$GBGSV,7,3,26,40,,,38,41,,,38,16,,,37,42,,,37,1*76

$GBGSV,7,4,26,7,,,36,13,,,36,1,,,36,6,,,36,1*43

$GBGSV,7,5,26,2,,,35,38,,,35,44,,,35,34,,,34,1*4F

$GBGSV,7,6,26,23,,,34,10,,,33,5,,,33,4,,,33,1*75

$GBGSV,7,7,26,8,,,31,12,,,30,1*48

$GBRMC,125633.536,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125633.536,0.000,753.619,753.619,689.203,2097152,2097152,2097152*67



2025-07-31 20:56:29:783 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 20:56:29:812 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:56:29:816 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:56:29:820 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:30:072 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3121  volt:5486 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:12][COMM]adc read battery ts volt:7 mv
[D][05:18:12][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:56:30:354 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5486mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:56:30:360 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:56:30:366 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:56:30:549 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:56:30:638 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:56:30:642 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:56:30:645 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:56:30:776 ==>> $GBGGA,125634.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,60,,,40,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,24,,,40,39,,,39,14,,,38,1*73

$GBGSV,7,3,26,40,,,38,41,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,26,42,,,37,7,,,36,13,,,36,6,,,36,1*75

$GBGSV,7,5,26,2,,,35,38,,,35,44,,,35,10,,,34,1*49

$GBGSV,7,6,26,34,,,34,23,,,34,5,,,33,4,,,33,1*74

$GBGSV,7,7,26,8,,,32,12,,,31,1*4A

$GBRMC,125634.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125634.516,0.000,756.793,756.793,692.105,2097152,2097152,2097152*6D

[D][05:18:13][COMM]read battery soc:255
1A A1 00 40 00 
Get AD_V14 2657mV
OVER 150


2025-07-31 20:56:30:897 ==>> 原始值:【2657】, 乘以分压基数【2】还原值:【5314】
2025-07-31 20:56:30:925 ==>> 【读取AccKey1电压(ADV14)后】通过,【5314mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:56:30:929 ==>> 检测【打开WIFI(2)】
2025-07-31 20:56:30:932 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:56:31:175 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:56:31:218 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:56:31:221 ==>> 检测【转刹把供电】
2025-07-31 20:56:31:226 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:56:31:449 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:56:31:517 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:56:31:523 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:56:31:528 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:56:31:618 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:56:31:724 ==>> $GBGGA,125635.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,25,,,41,60,,,40,1*44

$GBGSV,7,2,26,59,,,40,24,,,40,40,,,39,39,,,39,1*73

$GBGSV,7,3,26,41,,,39,14,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,26,42,,,37,2,,,36,7,,,36,13,,,36,1*71

$GBGSV,7,5,26,6,,,36,38,,,35,44,,,35,10,,,34,1*4E

$GBGSV,7,6,26,34,,,34,23,,,34,5,,,33,4,,,33,1*74

$GBGSV,7,7,26,8,,,32,12,,,31,1*4A

$GBRMC,125635.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125635.516,0.000,759.980,759.980,695.019,2097152,2097152,2097152*67



2025-07-31 20:56:31:906 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,CC057790A621,-59
+WIFISCAN:4,1,CC057790A620,-61
+WIFISCAN:4,2,CC057790A5C0,-81
+WIFISCAN:4,3,F86FB0660A82,-87

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 20:56:32:135 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 20:56:32:535 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:56:32:644 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:56:32:799 ==>> $GBGGA,125636.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,60,,,40,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,24,,,40,40,,,39,39,,,39,1*73

$GBGSV,7,3,26,14,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,26,42,,,37,7,,,36,13,,,36,6,,,36,1*75

$GBGSV,7,5,26,2,,,35,38,,,35,44,,,35,10,,,34,1*49

$GBGSV,7,6,26,34,,,34,23,,,34,8,,,33,5,,,33,1*78

$GBGSV,7,7,26,4,,,33,12,,,31,1*47

$GBRMC,125636.516,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125636.516,0.000,758.382,758.382,693.558,2097152,2097152,2097152*62

[W][05:18:15][COMM]>>>>>Input command = ?<<<<
[D][05:18:15][COMM]read battery soc:255
1A A1 00 80 00 
Get AD_V15 2403mV
OVER 150


2025-07-31 20:56:32:950 ==>> 原始值:【2403】, 乘以分压基数【2】还原值:【4806】
2025-07-31 20:56:32:971 ==>> 【读取AD_V15电压(前)】通过,【4806mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:56:32:976 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:56:32:982 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:56:33:074 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:33:120 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:56:33:165 ==>> 1A A1 01 00 00 
Get AD_V16 2432mV
OVER 150


2025-07-31 20:56:33:225 ==>> 原始值:【2432】, 乘以分压基数【2】还原值:【4864】
2025-07-31 20:56:33:253 ==>> 【读取AD_V16电压(前)】通过,【4864mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:56:33:256 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:56:33:259 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:33:577 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:16][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:16][COMM]adc read battery ts volt:7 mv
[D][05:18:16][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3064  volt:5385 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:56:33:682 ==>> $GBGGA,125637.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,

2025-07-31 20:56:33:757 ==>> ,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,59,,,41,60,,,40,1*4F

$GBGSV,7,2,26,24,,,40,25,,,40,39,,,39,14,,,38,1*78

$GBGSV,7,3,26,40,,,38,41,,,38,1,,,37,16,,,37,1*41

$GBGSV,7,4,26,42,,,37,7,,,36,13,,,36,6,,,36,1*75

$GBGSV,7,5,26,2,,,35,38,,,35,44,,,35,10,,,34,1*49

$GBGSV,7,6,26,34,,,34,23,,,34,8,,,33,5,,,33,1*78

$GBGSV,7,7,26,4,,,33,12,,,31,1*47

$GBRMC,125637.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125637.516,0.000,758.384,758.384,693.560,2097152,2097152,2097152*68



2025-07-31 20:56:33:797 ==>> 【转刹把供电电压(主控ADC)】通过,【5385mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:56:33:800 ==>> 检测【转刹把供电电压】
2025-07-31 20:56:33:805 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:34:075 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:16][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:16][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:16][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3066  volt:5389 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:56:34:357 ==>> 【转刹把供电电压】通过,【5389mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:56:34:361 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:56:34:363 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:34:524 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:56:34:651 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:56:34:656 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:56:34:661 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:34:753 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:56:34:798 ==>> $GBGGA,125638.516,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,60,,,41,3,,,41,25,,,41,1*45

$GBGSV,7,2,26,59,,,40,24,,,40,39,,,39,14,,,38,1*73

$GBGSV,7,3,26,40,,,38,1,,,38,41,,,38,16,,,37,1*4E

$GBGSV,7,4,26,42,,,37,7,,,36,13,,,36,6,,,36,1*75

$GBGSV,7,5,26,2,,,35,38,,,35,44,,,35,34,,,35,1*4E

$GBGSV,7,6,26,10,,,34,23,,,34,8,,,33,5,,,33,1*7E

$GBGSV,7,7,26,4,,,33,12,,,31,1*47

$GBRMC,125638.516,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125638.516,0.000,760.772,760.772,695.744,2097152,2097152,2097152*65

[D][05:18:17][COMM]read battery soc:255


2025-07-31 20:56:34:858 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:34:903 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:56:34:963 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:56:35:008 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:56:35:068 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:35:073 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:56:35:173 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:56:35:265 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:56:35:300 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:56:35:304 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:56:35:307 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:35:415 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:35:446 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:56:35:521 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:35:629 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:35:734 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:35:753 ==>> $GBGGA,125639.516,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,25,,,41,60,,,40,1*44

$GBGSV,7,2,27,59,,,40,24,,,40,14,,,39,39,,,39,1*73

$GBGSV,7,3,27,40,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,27,42,,,37,2,,,36,7,,,36,13,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,35,44,,,35,10,,,34,1*4F

$GBGSV,7,6,27,34,,,34,23,,,34,26,,,33,8,,,33,1*48

$GBGSV,7,7,27,5,,,33,4,,,33,12,,,31,1*73

$GBRMC,125639.516,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125639.516,0.000,757.889,757.889,693.107,2097152,2097152,2097152*63



2025-07-31 20:56:35:839 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:35:948 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:35:963 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<<
[W][05:18:18][COMM]>>>>>Input command = ?<<<<<
00 00 00 00 00 
head err!


2025-07-31 20:56:36:053 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:36:160 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:56:36:165 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 20:56:36:265 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:56:36:326 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:56:36:356 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 20:56:36:397 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:56:36:403 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:56:36:408 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:56:36:461 ==>> 3A A3 03 01 A3 


2025-07-31 20:56:36:566 ==>> ON_OUT3
OVER 150


2025-07-31 20:56:36:683 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:56:36:687 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:56:36:693 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:56:36:807 ==>> $GBGGA,125640.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,41,3,,,41,25,,,41,1*44

$GBGSV,7,2,27,59,,,40,24,,,40,40,,,39,39,,,39,1*72

$GBGSV,7,3,27,41,,,39,14,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,27,42,,,37,2,,,36,7,,,36,13,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,35,44,,,35,34,,,35,1*48

$GBGSV,7,6,27,10,,,34,4,,,34,23,,,34,26,,,33,1*45

$GBGSV,7,7,27,8,,,33,5,,,33,12,,,31,1*7F

$GBRMC,125640.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125640.516,0.000,760.184,760.184,695.206,2097152,2097152,2097152*69

[D][05:18:19][COMM]read battery soc:255
3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:56:36:985 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:56:36:989 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:56:36:994 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:56:37:068 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:56:37:266 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:56:37:272 ==>> 检测【左刹电压测试1】
2025-07-31 20:56:37:278 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:37:576 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3114  volt:5473 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1714  volt:2259 mv
[D][05:18:20][COMM]adc read right brake adc:1722  volt:2270 mv
[D][05:18:20][COMM]adc read throttle adc:1712  volt:2257 mv
[D][05:18:20][COMM]adc read battery ts volt:6 mv
[D][05:18:20][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:56:37:681 ==>> $GBGGA,125641.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,

2025-07-31 20:56:37:756 ==>> A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,60,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,14,,,38,1*79

$GBGSV,7,3,27,40,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,27,42,,,37,2,,,36,7,,,36,13,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,35,44,,,35,34,,,35,1*48

$GBGSV,7,6,27,10,,,34,4,,,34,23,,,34,8,,,33,1*79

$GBGSV,7,7,27,5,,,33,26,,,32,12,,,31,1*42

$GBRMC,125641.516,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125641.516,0.000,756.348,756.348,691.698,2097152,2097152,2097152*6F



2025-07-31 20:56:37:867 ==>> 【左刹电压测试1】通过,【2259】符合目标值【2250】至【2500】要求!
2025-07-31 20:56:37:871 ==>> 检测【右刹电压测试1】
2025-07-31 20:56:37:923 ==>> 【右刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 20:56:37:927 ==>> 检测【转把电压测试1】
2025-07-31 20:56:37:980 ==>> 【转把电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 20:56:37:984 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:56:37:988 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:56:38:062 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:56:38:298 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:56:38:302 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:56:38:308 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:56:38:367 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:56:38:600 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:56:38:605 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:56:38:611 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:56:38:656 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:56:38:761 ==>> $GBGGA,125642.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,60,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,14,,,38,1*79

$GBGSV,7,3,27,40,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,27,42,,,37,2,,,36,7,,,36,13,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,35,44,,,35,10,,,34,1*4F

$GBGSV,7,6,27,4,,,34,34,,,34,23,,,34,8,,,33,1*7F

$GBGSV,7,7,27,5,,,33,26,,,32,12,,,31,1*42

$GBRMC,125642.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125642.516,0.000,756.352,756.352,691.702,2097152,2097152,2097152*6E

                                         

2025-07-31 20:56:38:876 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:56:38:886 ==>> 检测【左刹电压测试2】
2025-07-31 20:56:38:895 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:56:39:173 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3115  volt:5475 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:21][COMM]adc read battery ts volt:2 mv
[D][05:18:21][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:56:39:408 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:56:39:415 ==>> 检测【右刹电压测试2】
2025-07-31 20:56:39:429 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:56:39:435 ==>> 检测【转把电压测试2】
2025-07-31 20:56:39:450 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:56:39:455 ==>> 检测【晶振检测】
2025-07-31 20:56:39:461 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:56:39:754 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]
$GBGGA,125643.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,59,,,41,60,,,40,3,,,40,1*40

$GBGSV,7,2,28,24,,,40,25,,,40,39,,,39,41,,,39,1*77

$GBGSV,7,3,28,14,,,38,40,,,38,1,,,37,16,,,37,1*4F

$GBGSV,7,4,28,42,,,37,7,,,36,13,,,36,6,,,36,1*7B

$GBGSV,7,5,28,2,,,35,38,,,35,44,,,35,10,,,34,1*47

$GBGSV,7,6,28,34,,,34,23,,,34,8,,,33,5,,,33,1*76

$GBGSV,7,7,28,4,,,33,26,,,32,12,,,31,11,,,37,1*48

$GBRMC,125643.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125643.516,0.000,754.821,754.821,690.302,2097152,2097152,2097152*6A



2025-07-31 20:56:39:989 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:56:39:996 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:56:40:001 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:56:40:070 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 1650mV
Get AD_V5 2765mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:56:40:274 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:56:40:278 ==>> 检测【检测BootVer】
2025-07-31 20:56:40:281 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:56:40:617 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539194
[D][05:18:23][FCTY]HardwareID  = 867222087756021
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = C91CDF6B9FA0
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1288
[D][05:18:23][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 0 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:2

2025-07-31 20:56:40:662 ==>> 3][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3685 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:56:40:767 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:56:40:818 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:56:40:822 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:56:40:830 ==>> 检测【检测固件版本】
2025-07-31 20:56:40:853 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:56:40:858 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:56:40:862 ==>> 检测【检测蓝牙版本】
2025-07-31 20:56:40:905 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:56:40:909 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:56:40:915 ==>> 检测【检测MoBikeId】
2025-07-31 20:56:40:929 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:56:40:933 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:56:40:936 ==>> 检测【检测蓝牙地址】
2025-07-31 20:56:40:940 ==>> 取到目标值:C91CDF6B9FA0
2025-07-31 20:56:40:956 ==>> 【检测蓝牙地址】通过,【C91CDF6B9FA0】符合目标值【】要求!
2025-07-31 20:56:40:959 ==>> 提取到蓝牙地址:C91CDF6B9FA0
2025-07-31 20:56:40:966 ==>> 检测【BOARD_ID】
2025-07-31 20:56:40:983 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:56:40:990 ==>> 检测【检测充电电压】
2025-07-31 20:56:41:012 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:56:41:016 ==>> 检测【检测VBUS电压1】
2025-07-31 20:56:41:041 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:56:41:045 ==>> 检测【检测充电电流】
2025-07-31 20:56:41:069 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:56:41:076 ==>> 检测【检测IMEI】
2025-07-31 20:56:41:081 ==>> 取到目标值:867222087756021
2025-07-31 20:56:41:097 ==>> 【检测IMEI】通过,【867222087756021】符合目标值【】要求!
2025-07-31 20:56:41:101 ==>> 提取到IMEI:867222087756021
2025-07-31 20:56:41:108 ==>> 检测【检测IMSI】
2025-07-31 20:56:41:114 ==>> 取到目标值:460130071539194
2025-07-31 20:56:41:136 ==>> 【检测IMSI】通过,【460130071539194】符合目标值【】要求!
2025-07-31 20:56:41:151 ==>> 提取到IMSI:460130071539194
2025-07-31 20:56:41:157 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:56:41:163 ==>> 取到目标值:460130071539194
2025-07-31 20:56:41:169 ==>> 【校验网络运营商(移动)】通过,【460130071539194】符合目标值【】要求!
2025-07-31 20:56:41:173 ==>> 检测【打开CAN通信】
2025-07-31 20:56:41:180 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:56:41:258 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:56:41:456 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:56:41:461 ==>> 检测【检测CAN通信】
2025-07-31 20:56:41:467 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:56:41:579 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:56:41:669 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:56:41:745 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:56:41:752 ==>> 检测【关闭CAN通信】
2025-07-31 20:56:41:763 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:56:41:774 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35275
$GBGGA,125645.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,14,,,38,1*79

$GBGSV,7,3,27,40,,,38,41,,,38,16,,,37,42,,,37,1*77

$GBGSV,7,4,27,7,,,36,13,,,36,1,,,36,6,,,36,1*42

$GBGSV,7,5,27,2,,,35,38,,,35,10,,,34,44,,,34,1*49

$GBGSV,7,6,27,34,,,34,23,,,34,8,,,33,5,,,33,1*79

$GBGSV,7,7,27,4,,,33,26,,,32,12,,,31,1*43

$GBRMC,125645.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125645.516,0.000,751.753,751.753,687.496,2097152,2097152,2097152*60

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:56:41:864 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:56:42:033 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:56:42:039 ==>> 检测【打印IMU STATE】
2025-07-31 20:56:42:043 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:56:42:262 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:56:42:322 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:56:42:326 ==>> 检测【六轴自检】
2025-07-31 20:56:42:331 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:56:42:579 ==>> [D][05:18:25][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:56:42:762 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:56:43:034 ==>> $GBGGA,125646.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,25,,,41,60,,,40,1*44

$GBGSV,7,2,27,59,,,40,24,,,40,39,,,39,41,,,39,1*73

$GBGSV,7,3,27,14,,,38,40,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,27,42,,,37,2,,,36,7,,,36,13,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,35,44,,,35,10,,,34,1*4F

$GBGSV,7,6,27,34,,,34,23,,,34,8,,,33,5,,,33,1*79

$GBGSV,7,7,27,4,,,33,26,,,32,12,,,31,1*43

$GBRMC,125646.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125646.516,0.000,757.125,757.125,692.409,2097152,2097152,2097152*61



2025-07-31 20:56:43:732 ==>> $GBGGA,125647.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,24,,,41,25,,,41,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,14,,,39,40,,,39,1*7D

$GBGSV,7,3,27,39,,,39,41,,,39,1,,,37,16,,,37,1*4E

$GBGSV,7,4,27,42,,,37,7,,,36,13,,,36,6,,,36,1*74

$GBGSV,7,5,27,2,,,35,38,,,35,44,,,35,34,,,35,1*4F

$GBGSV,7,6,27,10,,,34,23,,,34,8,,,33,5,,,33,1*7F

$GBGSV,7,7,27,4,,,33,26,,,32,12,,,31,1*43

$GBRMC,125647.516,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125647.516,0.000,759.428,759.428,694.515,2097152,2097152,2097152*6A



2025-07-31 20:56:44:267 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:56:44:372 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38017 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-7,1,4044]
[D][05:18:27][CO

2025-07-31 20:56:44:402 ==>> MM]Main Task receive event:142 finished processing


2025-07-31 20:56:44:457 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:56:44:461 ==>> 检测【打印IMU STATE2】
2025-07-31 20:56:44:465 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:56:44:658 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:56:44:776 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:56:44:782 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:56:44:787 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:56:44:856 ==>> 5A A5 02 5A A5 


2025-07-31 20:56:44:960 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:56:45:083 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:56:45:090 ==>> 检测【检测VBUS电压2】
2025-07-31 20:56:45:097 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:56:45:962 ==>> $GBGGA,125644.522,2301.2569604,N,11421.9417054,E,1,15,0.73,77.854,M,-1.770,M,,*59

$GBGSA,A,3,33,14,39,06,16,24,25,07,40,13,41,38,1.43,0.73,1.23,4*0A

$GBGSA,A,3,44,34,23,,,,,,,,,,1.43,0.73,1.23,4*0C

$GBGSV,7,1,27,33,68,254,43,3,61,190,41,14,58,189,38,59,52,129,40,1*40

$GBGSV,7,2,27,39,52,12,39,6,52,348,36,16,52,352,37,1,48,126,37,1*45

$GBGSV,7,3,27,2,46,238,35,24,45,22,40,25,44,294,41,7,43,177,36,1*42

$GBGSV,7,4,27,60,41,238,40,40,40,161,39,13,36,218,36,10,34,199,34,1*7B

$GBGSV,7,5,27,26,32,243,32,4,32,112,33,41,29,316,39,38,23,192,35,1*41

$GBGSV,7,6,27,5,22,257,33,44,18,92,35,34,18,147,35,8,18,203,33,1*49

$GBGSV,7,7,27,23,7,258,34,42,3,321,37,12,,,31,1*7D

$GBRMC,125644.522,A,2301.2569604,N,11421.9417054,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:18:27][GNSS]HD8040 GPS
[W][05:18:27][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:27][GNSS]GPS diff_sec 124011497, report 0x42 frame
$GBGST,125644.522,2.778,0.359,0.341,0.502,2.834,3.649,8.136*7D

[D][05:18:27][COMM]read battery soc:255
[D][05:18:27][COMM]Main Task receive event:131
[D][05:18:27][COMM]index:0,power_mode:0xFF
[D][05:18:27][COMM]index

2025-07-31 20:56:46:067 ==>> :1,sound_mode:0xFF
[D][05:18:27][COMM]index:2,gsensor_mode:0xFF
[D][05:18:27][COMM]index:3,report_freq_mode:0xFF
[D][05:18:27][COMM]index:4,report_period:0xFF
[D][05:18:27][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:27][COMM]index:6,normal_reset_period:0xFF
[D][05:18:27][COMM]index:7,spock_over_speed:0xFF
[D][05:18:27][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:27][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:27][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:27][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:27][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:27][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:27][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:27][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:27][COMM]index:16,imu_config_params:0xFF
[D][05:18:27][COMM]index:17,long_connect_params:0xFF
[D][05:18:27][COMM]index:18,detain_mark:0xFF
[D][05:18:27][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:27][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:27][COMM]index:21,mc_mode:0xFF
[D][05:18:27][COMM]index:22,S_mode:0xFF
[D][05:18:27][COMM]index:23,overweight:0xFF
[D][05:18:27][COMM]index:24,standstill_mode:0xFF
[D][05:18:27]

2025-07-31 20:56:46:171 ==>> [COMM]index:25,night_mode:0xFF
[D][05:18:27][COMM]index:26,experiment1:0xFF
[D][05:18:27][COMM]index:27,experiment2:0xFF
[D][05:18:27][COMM]index:28,experiment3:0xFF
[D][05:18:27][COMM]index:29,experiment4:0xFF
[D][05:18:27][COMM]index:30,night_mode_start:0xFF
[D][05:18:27][COMM]index:31,night_mode_end:0xFF
[D][05:18:27][COMM]index:33,park_report_minutes:0xFF
[D][05:18:27][COMM]index:34,park_report_mode:0xFF
[D][05:18:27][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:27][COMM]index:38,charge_battery_para: FF
[D][05:18:27][COMM]index:39,multirider_mode:0xFF
[D][05:18:27][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:27][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:27][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:27][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:27][COMM]index:44,riding_duration_config:0xFF
[D][05:18:27][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:27][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:27][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:27][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:27][COMM]index:49,mc_load_startup:0xFF
[D][05:18:27][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:27][COMM]i

2025-07-31 20:56:46:276 ==>> ndex:51,traffic_audio_play:0xFF
[D][05:18:27][COMM]index:52,traffic_mode:0xFF
[D][05:18:27][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:27][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:27][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:27][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:27][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:27][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:27][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:27][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:27][COMM]index:63,experiment5:0xFF
[D][05:18:27][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:27][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:27][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:27][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:27][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:27][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:27][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:27][COMM]index:72,experiment6:0xFF
[D][05:18:27][COMM]index:73,experiment7:0xFF
[D][05:18:27][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:27][COMM]index:75,zero_value_from_server:-1


2025-07-31 20:56:46:381 ==>> 
[D][05:18:27][COMM]index:76,multirider_threshold:255
[D][05:18:27][COMM]index:77,experiment8:255
[D][05:18:27][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:27][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:27][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:27][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:27][COMM]index:83,loc_report_interval:255
[D][05:18:27][COMM]index:84,multirider_threshold_p2:255
[D][05:18:27][COMM]index:85,multirider_strategy:255
[D][05:18:27][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:27][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:27][COMM]index:90,weight_param:0xFF
[D][05:18:27][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:27][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:27][COMM]index:95,current_limit:0xFF
[D][05:18:27][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:27][COMM]index:100,location_mode:0xFF

[W][05:18:27][PROT]remove success[1629955107],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:27][COMM

2025-07-31 20:56:46:485 ==>> ]Main Task receive event:131 finished processing
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:1
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[294]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:27][COMM]Main Task receive event:20
[D][05:18:27][GNSS]stop event:1
[D][05:18:27][COMM]frm_peripheral_device_po

2025-07-31 20:56:46:591 ==>> weron type 0.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:0------------
[D][05:18:27][COMM]------------ready to Power on Acckey 2------------
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:27][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B5115E08ED3D602DFFF0EC5064E79206B7B6CE1AB820DD8632BBE846BDE3B811493BEEE33DAFABA087744E6E8150B1D8E6D793D49D8876CE49269C007773E6CBFEA87881EA73590F0E94D77001B57FDFB7301E694333AF6CF5D3E1A439EC958394B1D193A5742C57BA2D9A608C73371EA2F0AD7A3B19B910657E59E689EDA34D02652B
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket

2025-07-31 20:56:46:698 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:56:46:757 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 20:56:47:226 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[W][05:18:29][GNSS]sing locating running
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[4],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[4],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][PROT]min_index:4, type:0x5D03, priority:3
[D][05:18:29][PROT]index:4
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:8
[D][05:18:29][PROT]re

2025-07-31 20:56:47:331 ==>> try_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900007]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:29][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6



2025-07-31 20:56:47:376 ==>>                                                                                                                                            

2025-07-31 20:56:47:647 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:56:48:022 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539194
[D][05:18:30][FCTY]HardwareID  = 867222087756021
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = C91CDF6B9FA0
[D][05:18:30][FCTY]Bat         = 3784 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:30][FCTY]Acckey1 vol = 5472 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1

2025-07-31 20:56:48:067 ==>> _GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3685 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:56:48:185 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:56:48:192 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:56:48:201 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:56:48:265 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:56:48:370 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:18:31][COMM]read battery soc:255


2025-07-31 20:56:48:475 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:56:48:480 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:56:48:484 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:56:48:566 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:56:48:767 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:56:48:775 ==>> 检测【打开WIFI(3)】
2025-07-31 20:56:48:782 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:56:48:979 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:56:49:061 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:56:49:067 ==>> 检测【扩展芯片hw】
2025-07-31 20:56:49:072 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:56:49:251 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:56:49:708 ==>> +WIFISCAN:4,0,CC057790A620,-59
+WIFISCAN:4,1,44A1917CAD81,-81
+WIFISCAN:4,2,CC057790A5C0,-82
+WIFISCAN:4,3,CC057790A5C1,-83

[D][05:18:32][CAT1]wifi scan report total[4]


2025-07-31 20:56:49:900 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:56:49:907 ==>> 检测【扩展芯片boot】
2025-07-31 20:56:49:935 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:56:49:943 ==>> 检测【扩展芯片sw】
2025-07-31 20:56:49:970 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:56:49:979 ==>> 检测【检测音频FLASH】
2025-07-31 20:56:49:987 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:56:50:718 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acck

2025-07-31 20:56:50:823 ==>> ey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main 

2025-07-31 20:56:50:928 ==>> task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[5],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[5],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05

2025-07-31 20:56:51:033 ==>> :18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][PROT]index:5
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:9
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900008]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error

2025-07-31 20:56:51:138 ==>> 
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[6],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[6],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:5 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:9
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:5

2025-07-31 20:56:51:243 ==>> , type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900008]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][PROT]CLEAN:0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]Send Data To Server[198][198] ... ->:
0063B98D113311331133113311331B88B24F9E30DC790E749E9A61515744B76FA94A36D3ABC38BD890CDB9EB1C36C019FD23897A701EC28B4AA0941B101AF

2025-07-31 20:56:51:348 ==>> 3A6EA3C21A526C85A585E5C0AB52DD10A7AAB29712DA16F16EF986DD2B90FC32E16E86179
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:32][PROT]M2M Send ok [1629955112]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:

2025-07-31 20:56:52:410 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:56:52:912 ==>> [D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:54:039 ==>> [D][05:18:36][COMM]47547 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:36][COMM]accel parse set 0
[D][05:18:36][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:56:54:129 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 20:56:54:419 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:56:54:930 ==>> [D][05:18:37][COMM]48558 imu init OK


2025-07-31 20:56:55:032 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:56:55:038 ==>> 检测【打开喇叭声音】
2025-07-31 20:56:55:046 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:56:55:785 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:37][PROT]CLEAN,SEND:5
[D][05:18:37][PROT]index:5 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:9
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900008]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SA

2025-07-31 20:56:55:842 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:56:55:855 ==>> 检测【打开大灯控制】
2025-07-31 20:56:55:895 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:56:55:906 ==>> L ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B2B5AC9E1B2F0472B0C55714567BE272A058F9C70F8652C94A1560A1D922F2AC7AF113475D4B8A182E09BAAE8CDF2B0362A76290DD8F46E25959B47974CB2FB19144F8B2E8E9BA601A8CB7FF6EB0A5144E5B75
[D][05:18:37][COMM]--->crc16:0x419c
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.dat

2025-07-31 20:56:55:995 ==>> a_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][PROT]M2M Send ok [1629955117]
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:38

2025-07-31 20:56:56:100 ==>> ][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:38][COMM]f:[ec800m_audio_play_proce

2025-07-31 20:56:56:145 ==>> ss].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:56:56:221 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:56:56:384 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:56:56:393 ==>> 检测【关闭仪表供电3】
2025-07-31 20:56:56:403 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:56:56:418 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:56:56:523 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 20:56:56:672 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:56:56:678 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:56:56:684 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:56:56:844 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:56:56:972 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:56:56:978 ==>> 检测【读大灯电压】
2025-07-31 20:56:56:983 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:56:57:149 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[33154]


2025-07-31 20:56:57:257 ==>> 【读大灯电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:56:57:263 ==>> 检测【关闭大灯控制2】
2025-07-31 20:56:57:272 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:56:57:420 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:56:57:538 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:56:57:549 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:56:57:573 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:56:57:750 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:56:57:831 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:56:57:837 ==>> 检测【打开WIFI(4)】
2025-07-31 20:56:57:846 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:56:58:073 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:56:58:161 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:56:58:167 ==>> 检测【EC800M模组版本】
2025-07-31 20:56:58:176 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:56:58:361 ==>> [D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:56:58:421 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:56:58:602 ==>> [D][05:18:41][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:56:58:706 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:56:58:713 ==>> 检测【配置蓝牙地址】
2025-07-31 20:56:58:718 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:56:58:831 ==>> [W][05:18:41][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:56:58:921 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C91CDF6B9FA0>】
2025-07-31 20:56:59:169 ==>> recv ble 1
recv ble 2
ble set mac ok :c9,1c,df,6b,9f,a0
enable filters ret : 0

2025-07-31 20:56:59:216 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:56:59:222 ==>> 检测【BLETEST】
2025-07-31 20:56:59:230 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:56:59:304 ==>> [D][05:18:41][COMM]52941 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:56:59:364 ==>> 4A A4 01 A4 4A 


2025-07-31 20:56:59:409 ==>> +WIFISCAN:4,0,F88C21BCF57D,-37
+WIFISCAN:4,1,F42A7D1297A3,-70
+WIFISCAN:4,2,74C330CCAB10,-73
+WIFISCAN:4,3,CC057790A5C1,-81

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 20:56:59:469 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C91CDF6B9FA0*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C91CDF6B9FA099999

2025-07-31 20:56:59:559 ==>> OVER 150


2025-07-31 20:57:00:257 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:57:00:263 ==>> 该项需要延时执行
2025-07-31 20:57:00:327 ==>> [D][05:18:42][GNSS]recv submsg id[3]
[D][05:18:42][COMM]53952 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:57:00:718 ==>> [D][05:18:43][COMM]read battery soc:255
[D][05:18:43][PROT]CLEAN,SEND:5
[D][05:18:43][PROT]index:5 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:9
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900008]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1

2025-07-31 20:57:00:793 ==>> ]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B2F66598EBD5019F01706C428AF67D6A8003C2D5D5EDC688FE2F82A1F613D2E87B53B614B09819DBCA9FAA4C3D61BDD1302BBD934956C7E7AE048B99B43554BB0E44D6AADD315BAF7B05D867CFCD475302C725
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 20:57:00:898 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       012].open hexlog save


2025-07-31 20:57:01:325 ==>> [D][05:18:43][COMM]54964 imu init OK


2025-07-31 20:57:02:436 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:57:04:440 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:57:05:969 ==>> [D][05:18:48][PROT]CLEAN,SEND:5
[D][05:18:48][PROT]CLEAN:5
[D][05:18:48][PROT]index:1 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:5
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:10
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900006]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B33674F9AEA3B2252BC7854C371E7D3D31724C63CD630F0C49BA9D4410E39213FC03

2025-07-31 20:57:06:044 ==>> C28A48987FF62D9869E91B3B68F703BBAE111BBD24B7E90D6025D8418054BA3F88D69496087F3A69EA29F42E54871B5703
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 20:57:06:444 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:57:08:456 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:57:10:270 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:57:10:276 ==>> 检测【检测WiFi结果】
2025-07-31 20:57:10:282 ==>> WiFi信号:【CC057790A621】,信号值:-59
2025-07-31 20:57:10:301 ==>> WiFi信号:【CC057790A620】,信号值:-61
2025-07-31 20:57:10:311 ==>> WiFi信号:【CC057790A5C0】,信号值:-81
2025-07-31 20:57:10:335 ==>> WiFi信号:【F86FB0660A82】,信号值:-87
2025-07-31 20:57:10:362 ==>> WiFi信号:【44A1917CAD81】,信号值:-81
2025-07-31 20:57:10:370 ==>> WiFi信号:【CC057790A5C1】,信号值:-83
2025-07-31 20:57:10:380 ==>> WiFi信号:【F88C21BCF57D】,信号值:-37
2025-07-31 20:57:10:408 ==>> WiFi信号:【F42A7D1297A3】,信号值:-70
2025-07-31 20:57:10:417 ==>> WiFi信号:【74C330CCAB10】,信号值:-73
2025-07-31 20:57:10:431 ==>> WiFi数量【9】, 最大信号值:-37
2025-07-31 20:57:10:453 ==>> 检测【检测GPS结果】
2025-07-31 20:57:10:464 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 20:57:10:470 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【41】
北斗星号:【14】,信号值:【38】
北斗星号:【59】,信号值:【40】
北斗星号:【39】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【37】
北斗星号:【1】,信号值:【37】
北斗星号:【2】,信号值:【35】
北斗星号:【24】,信号值:【40】
北斗星号:【25】,信号值:【41】
北斗星号:【7】,信号值:【36】
北斗星号:【60】,信号值:【40】
北斗星号:【40】,信号值:【39】
北斗星号:【13】,信号值:【36】
北斗星号:【41】,信号值:【39】
北斗星号:【38】,信号值:【35】
北斗星号:【44】,信号值:【35】
北斗星号:【34】,信号值:【35】
北斗星号:【42】,信号值:【37】

2025-07-31 20:57:10:479 ==>> 检测【CSQ强度】
2025-07-31 20:57:10:489 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:57:10:513 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:53][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:53][CAT1]exec over: func id: 12, ret: 21
[D][05:18:53][COMM]read battery soc:255


2025-07-31 20:57:10:556 ==>> 【CSQ强度】通过,【22】符合目标值【18】至【31】要求!
2025-07-31 20:57:10:565 ==>> 检测【关闭GSM联网】
2025-07-31 20:57:10:588 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:57:10:753 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:53][COMM]GSM test
[D][05:18:53][COMM]GSM test disable


2025-07-31 20:57:10:840 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:57:10:850 ==>> 检测【4G联网测试】
2025-07-31 20:57:10:862 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:57:11:893 ==>> [D][05:18:53][PROT]CLEAN,SEND:1
[D][05:18:53][PROT]index:1 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:5
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:9
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900006]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B346C6B657FAF8694E4307E6009BBADF442913AC2F6F9056A31CFD74995DF7BCF0EEA2D368E07D8C2E60F891B13346841137408CE7BD0C4D1107D

2025-07-31 20:57:11:997 ==>> 25027E1FFCF564C82C05A780A92B6AF77383BDF0866AA03AB
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:53][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:53][PROT]M2M Send ok [1629955133]
[D][05:18:53][COMM]Main Task receive event:14
[D][05:18:53][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955133, allstateRepSeconds = 0
[D][05:18:53][COMM]index:0,power_mode:0xFF
[D][05:18:53][COMM]index:1,sound_mode:0xFF
[D][05:18:53][COMM]index:2,gsensor_mode:0xFF
[D][05:18:53][COMM]index:3,report_freq_mode:0xFF
[D][05:18:53][COMM]index:4,report_period:0xFF
[D][05:18:53][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:53][COMM]index:6,normal_reset_period:0xFF
[D][05:18:53][COMM]index:7,spock_over_speed:0xFF
[D][05:18:53][COMM]index:8,spock_limit_speed:

2025-07-31 20:57:12:102 ==>> 0xFF
[D][05:18:53][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:53][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:53][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:53][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:53][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:53][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:53][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:53][COMM]index:16,imu_config_params:0xFF
[D][05:18:53][COMM]index:17,long_connect_params:0xFF
[D][05:18:53][COMM]index:18,detain_mark:0xFF
[D][05:18:53][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:53][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:53][COMM]index:21,mc_mode:0xFF
[D][05:18:53][COMM]index:22,S_mode:0xFF
[D][05:18:53][COMM]index:23,overweight:0xFF
[D][05:18:53][COMM]index:24,standstill_mode:0xFF
[D][05:18:53][COMM]index:25,night_mode:0xFF
[D][05:18:53][COMM]index:26,experiment1:0xFF
[D][05:18:53][COMM]index:27,experiment2:0xFF
[D][05:18:53][COMM]index:28,experiment3:0xFF
[D][05:18:53][COMM]index:29,experiment4:0xFF
[D][05:18:53][COMM]index:30,night_mode_start:0xFF
[D][05:18:53][COMM]index:31,night_mode_end:0xFF
[D][05:18:53][COMM]index:33,park_report_minutes

2025-07-31 20:57:12:207 ==>> :0xFF
[D][05:18:53][COMM]index:34,park_report_mode:0xFF
[D][05:18:53][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:53][COMM]index:38,charge_battery_para: FF
[D][05:18:53][COMM]index:39,multirider_mode:0xFF
[D][05:18:53][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:53][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:53][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:53][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:53][COMM]index:44,riding_duration_config:0xFF
[D][05:18:53][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:53][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:53][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:53][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:53][COMM]index:49,mc_load_startup:0xFF
[D][05:18:53][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:53][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:53][COMM]index:52,traffic_mode:0xFF
[D][05:18:53][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:53][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:53][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:53][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:53][COMM]index:58,traffic_light_threshold:0xFF
[D]

2025-07-31 20:57:12:312 ==>> [05:18:53][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:53][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:53][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:53][COMM]index:63,experiment5:0xFF
[D][05:18:53][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:53][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:53][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:53][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:53][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:53][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:53][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:53][COMM]index:72,experiment6:0xFF
[D][05:18:53][COMM]index:73,experiment7:0xFF
[D][05:18:53][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:53][COMM]index:75,zero_value_from_server:-1
[D][05:18:53][COMM]index:76,multirider_threshold:255
[D][05:18:53][COMM]index:77,experiment8:255
[D][05:18:53][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:53][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:53][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:53][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:53][COM

2025-07-31 20:57:12:417 ==>> M]index:83,loc_report_interval:255
[D][05:18:53][COMM]index:84,multirider_threshold_p2:255
[D][05:18:53][COMM]index:85,multirider_strategy:255
[D][05:18:53][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:53][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:53][COMM]index:90,weight_param:0xFF
[D][05:18:53][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:53][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:53][COMM]index:95,current_limit:0xFF
[D][05:18:53][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:53][COMM]index:100,location_mode:0xFF

[W][05:18:53][PROT]remove success[1629955133],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:53][PROT]add success [1629955133],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:11
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [

2025-07-31 20:57:12:507 ==>> 1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [999999999990000A]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[294]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 13
[D][05:18:53][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]<<< 
+CSQ: 22,99

OK

2025-07-31 20:57:12:537 ==>>                                          

2025-07-31 20:57:12:883 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:57:12:895 ==>> 检测【关闭GPS】
2025-07-31 20:57:12:919 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:57:13:067 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all continue location stop
[W][05:18:55][GNSS]sing locating running
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all sing location stop


2025-07-31 20:57:13:197 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:57:13:204 ==>> 检测【清空消息队列2】
2025-07-31 20:57:13:210 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:57:13:354 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:55][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:57:13:509 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:57:13:520 ==>> 检测【轮动检测】
2025-07-31 20:57:13:543 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:57:13:567 ==>> 3A A3 01 00 A3 


2025-07-31 20:57:13:657 ==>> OFF_OUT1
OVER 150


2025-07-31 20:57:13:732 ==>> [D][05:18:56][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:57:14:023 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:57:14:160 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:57:14:326 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:57:14:336 ==>> 检测【关闭小电池】
2025-07-31 20:57:14:346 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:57:14:478 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150
[D][05:18:57][COMM]read battery soc:255


2025-07-31 20:57:14:627 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:57:14:635 ==>> 检测【进入休眠模式】
2025-07-31 20:57:14:649 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:57:14:855 ==>> [D][05:18:57][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:57][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:57:14:945 ==>>                                                                                                            57][COMM]prepare to sleep
[D][05:18:57][CAT1]gsm read msg sub id: 12
[D][05:18:57][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:57:15:972 ==>> [D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 6
[D][05:18:58][M2M ]tcpclient close[4]
[D][05:18:58][SAL ]Cellular task submsg id[12]
[D][05:18:58][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:18:58][CAT1]gsm read msg sub id: 9
[D][05:18:58][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 9, ret: 6
[D][05:18:58][CAT1]sub id: 9, ret: 6

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:58][SAL ]socket close ind. id[4]
[D][05:18:58][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:58][COMM]1x1 frm_can_tp_send ok
[D][05:18:58][CAT1]pdpdeact urc len[22]


2025-07-31 20:57:16:241 ==>> [E][05:18:58][COMM]1x1 rx timeout
[D][05:18:58][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:57:16:482 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:57:16:756 ==>> [E][05:18:59][COMM]1x1 rx timeout
[E][05:18:59][COMM]1x1 tp timeout
[E][05:18:59][COMM]1x1 error -3.
[W][05:18:59][COMM]CAN STOP!
[D][05:18:59][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:18:59][COMM]------------ready to Power off Acckey 1------------
[D][05:18:59][COMM]------------ready to Power off Acckey 2------------
[D][05:18:59][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:18:59][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1296
[D][05:18:59][COMM]bat sleep fail, reason:-1
[D][05:18:59][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:18:59][COMM]accel parse set 0
[D][05:18:59][COMM]imu rest ok. 70290
[D][05:18:59][COMM]imu sleep 0
[W][05:18:59][COMM]now sleep


2025-07-31 20:57:16:973 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:57:16:983 ==>> 检测【检测33V休眠电流】
2025-07-31 20:57:17:003 ==>> 开始33V电流采样
2025-07-31 20:57:17:012 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:17:074 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:57:18:087 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:57:18:148 ==>> Current33V:????:16.62

2025-07-31 20:57:18:596 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:18:603 ==>> 【检测33V休眠电流】通过,【16.62uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:57:18:609 ==>> 该项需要延时执行
2025-07-31 20:57:20:615 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:57:20:625 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:57:20:652 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:57:20:766 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1663mV
Get AD_V4 0mV
Get AD_V5 2753mV
Get AD_V6 2032mV
Get AD_V7 1096mV
OVER 150


2025-07-31 20:57:21:665 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:57:21:675 ==>> 检测【打开小电池2】
2025-07-31 20:57:21:684 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:57:21:768 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:57:21:956 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:57:21:965 ==>> 该项需要延时执行
2025-07-31 20:57:22:461 ==>> 此处延时了:【500】毫秒
2025-07-31 20:57:22:471 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:57:22:483 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:57:22:567 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:57:22:745 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:57:22:752 ==>> 该项需要延时执行
2025-07-31 20:57:23:245 ==>> 此处延时了:【500】毫秒
2025-07-31 20:57:23:255 ==>> 检测【进入休眠模式2】
2025-07-31 20:57:23:270 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:57:23:294 ==>> [D][05:19:05][COMM]------------ready to Power on Acckey 1------------
[D][05:19:05][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:05][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:05][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:05][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:05][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:05][COMM]----- get Acckey 1 and value:1------------
[W][05:19:05][COMM]CAN START!
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:05][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 76707
[D][05:19:05][COMM][Audio]exec status ready.
[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]exec over: func id: 12, re

2025-07-31 20:57:23:313 ==>> t: 6
[D][05:19:05][COMM]imu wakeup ok. 76722
[D][05:19:05][COMM]imu wakeup 1
[W][05:19:05][COMM]wake up system, wakeupEvt=0x80
[D][05:19:05][COMM]frm_can_weigth_power_set 1
[D][05:19:05][COMM]Clear Sleep Block Evt
[D][05:19:05][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:05][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:57:23:365 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:57:23:650 ==>> [E][05:19:06][COMM]1x1 rx timeout
[D][05:19:06][COMM]1x1 frm_can_tp_send ok
[D][05:19:06][COMM]msg 02A0 loss. last_tick:76692. cur_tick:77200. period:50
[D][05:19:06][COMM]msg 02A4 loss. last_tick:76692. cur_tick:77201. period:50
[D][05:19:06][COMM]msg 02A5 loss. last_tick:76692. cur_tick:77201. period:50
[D][05:19:06][COMM]msg 02A6 loss. last_tick:76692. cur_tick:77202. period:50
[D][05:19:06][COMM]msg 02A7 loss. last_tick:76692. cur_tick:77202. period:50
[D][05:19:06][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 77202
[D][05:19:06][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 77203


2025-07-31 20:57:23:980 ==>> [E][05:19:06][COMM]1x1 rx timeout
[E][05:19:06][COMM]1x1 tp timeout
[E][05:19:06][COMM]1x1 error -3.
[D][05:19:06][COMM]Main Task receive event:28 finished processing
[D][05:19:06][COMM]Main Task receive event:28
[D][05:19:06][COMM]prepare to sleep
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 6
[D][05:19:06][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:06][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:57:24:284 ==>> [D][05:19:06][COMM]msg 0220 loss. last_tick:76692. cur_tick:77697. period:100
[D][05:19:06][COMM]msg 0221 loss. last_tick:76692. cur_tick:77697. period:100
[D][05:19:06][COMM]msg 0224 loss. last_tick:76692. cur_tick:77697. period:100
[D][05:19:06][COMM]msg 0260 loss. last_tick:76692. cur_tick:77698. period:100
[D][05:19:06][COMM]msg 0280 loss. last_tick:76692. cur_tick:77698. period:100
[D][05:19:06][COMM]msg 02C0 loss. last_tick:76692. cur_tick:77699. period:100
[D][05:19:06][COMM]msg 02C1 loss. last_tick:76692. cur_tick:77699. period:100
[D][05:19:06][COMM]msg 02C2 loss. last_tick:76692. cur_tick:77699. period:100
[D][05:19:06][COMM]msg 02E0 loss. last_tick:76692. cur_tick:77700. period:100
[D][05:19:06][COMM]msg 02E1 loss. last_tick:76692. cur_tick:77700. period:100
[D][05:19:06][COMM]msg 02E2 loss. last_tick:76692. cur_tick:77701. period:100
[D][05:19:06][COMM]msg 0300 loss. last_tick:76692. cur_tick:77701. period:100
[D][05:19:06][COMM]msg 0301 loss. last_tick:76692. cur_tick:77701. period:100
[D][05:19:06][COMM]bat msg 0240 loss. last_tick:76692. cur_tick:77702. period:100. j,i:1 54
[D][05:19:06][COMM]bat msg 0241 loss. last_tick:76692. cur_tick:77702. period:100. j,i:2 55
[D][05:19:06][COMM]bat m

2025-07-31 20:57:24:389 ==>> sg 0242 loss. last_tick:76692. cur_tick:77702. period:100. j,i:3 56
[D][05:19:06][COMM]bat msg 0244 loss. last_tick:76692. cur_tick:77703. period:100. j,i:5 58
[D][05:19:06][COMM]bat msg 024E loss. last_tick:76692. cur_tick:77703. period:100. j,i:15 68
[D][05:19:06][COMM]bat msg 024F loss. last_tick:76692. cur_tick:77703. period:100. j,i:16 69
[D][05:19:06][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 77704
[D][05:19:06][COMM]CAN message bat fault change: 0x00000000->0x0001802E 77704
[D][05:19:06][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 77705
                                                                              

2025-07-31 20:57:24:571 ==>> [D][05:19:07][COMM]msg 0222 loss. last_tick:76692. cur_tick:78199. period:150
[D][05:19:07][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 78200


2025-07-31 20:57:24:661 ==>>                                                        ,vbuswake : 1
[D][05:19:07][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:07][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:57:24:766 ==>> [E][05:19:07][COMM]1x1 rx timeout
[E][05:19:07][COMM]1x1 tp timeout
[E][05:19:07][COMM]1x1 error -3.
[

2025-07-31 20:57:24:841 ==>> W][05:19:07][COMM]CAN STOP!
[D][05:19:07][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:07][COMM]------------ready to Power off Acckey 1------------
[D][05:19:07][COMM]------------ready to Power off Acckey 2------------
[D][05:19:07][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:07][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 105
[D][05:19:07][COMM]bat sleep fail, reason:-1
[D][05:19:07][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:07][COMM]accel parse set 0
[D][05:19:07][COMM]imu rest ok. 78378
[D][05:19:07][COMM]imu sleep 0
[W][05:19:07][COMM]now sleep


2025-07-31 20:57:25:068 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:57:25:075 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:57:25:089 ==>> 开始小电池电流采样
2025-07-31 20:57:25:095 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:25:174 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:57:26:183 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:57:26:258 ==>> CurrentBattery:ƽ��:69.82

2025-07-31 20:57:26:697 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:26:704 ==>> 【检测小电池休眠电流】通过,【69.82uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:57:26:710 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:57:26:717 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:57:26:757 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:57:26:994 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:57:27:002 ==>> 该项需要延时执行
2025-07-31 20:57:27:008 ==>> [D][05:19:09][COMM]------------ready to Power on Acckey 1------------
[D][05:19:09][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:09][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:09][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:09][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:09][COMM]----- get Acckey 1 and value:1------------
[W][05:19:09][COMM]CAN START!
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:09][COMM]CAN message bat fault change: 0x0001802E->0x00000000 80486
[D][05:19:09][COMM][Audio]exec status ready.
[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][COMM]imu wakeup ok. 80500
[D][05:19:09][COMM]imu wakeup 1
[D][05:19:09][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:09][COMM]wake up system, wakeupEvt=0x80
[D][05:19:09][COMM]frm_can_weigth_power_set 1
[D][05:19:09][COMM]Clear Sleep Block Evt
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok
[D][05:19:09][COMM]read battery soc:0


2025-07-31 20:57:27:268 ==>> [E][05:19:09][COMM]1x1 rx timeout
[D][05:19:09][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:57:27:373 ==>> [D][05:19:09][COMM]msg 02A0 loss. last_tick:80467. cur_tick:80980. period:50
[D][05:19:09][COMM]msg 02A4 loss. last_tick:80467. cur_tick:80980. period:50
[D][05:19:09][COMM]msg 02A5 loss. last

2025-07-31 20:57:27:433 ==>> _tick:80467. cur_tick:80981. period:50
[D][05:19:09][COMM]msg 02A6 loss. last_tick:80467. cur_tick:80981. period:50
[D][05:19:09][COMM]msg 02A7 loss. last_tick:80467. cur_tick:80981. period:50
[D][05:19:09][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 80982
[D][05:19:09][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 80982


2025-07-31 20:57:27:478 ==>>                                                                                                                                                                                      

2025-07-31 20:57:27:508 ==>> 此处延时了:【500】毫秒
2025-07-31 20:57:27:519 ==>> 检测【检测唤醒】
2025-07-31 20:57:27:542 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:57:28:200 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:10][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:10][FCTY]==========Modules-nRF5340 ==========
[D][05:19:10][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:10][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:10][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:10][FCTY]DeviceID    = 460130071539194
[D][05:19:10][FCTY]HardwareID  = 867222087756021
[D][05:19:10][FCTY]MoBikeID    = 9999999999
[D][05:19:10][FCTY]LockID      = FFFFFFFFFF
[D][05:19:10][FCTY]BLEFWVersion= 105
[D][05:19:10][FCTY]BLEMacAddr   = C91CDF6B9FA0
[D][05:19:10][FCTY]Bat         = 3844 mv
[D][05:19:10][FCTY]Current     = 0 ma
[D][05:19:10][FCTY]VBUS        = 2600 mv
[D][05:19:10][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:10][FCTY]Ext battery vol = 32, adc = 1292
[D][05:19:10][FCTY]Acckey1 vol = 5480 mv, Acckey2 vol = 0 mv
[D][05:19:10][FCTY]Bike Type flag is invalied
[D][05:19:10][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:10][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:10][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:10][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:10][FCTY]CAT1_GNSS_PLAT

2025-07-31 20:57:28:305 ==>> FORM = C4
[D][05:19:10][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:10][FCTY]Bat1         = 3685 mv
[D][05:19:10][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:10][FCTY]==========Modules-nRF5340 ==========
[E][05:19:10][COMM]1x1 rx timeout
[E][05:19:10][COMM]1x1 tp timeout
[E][05:19:10][COMM]1x1 error -3.
[D][05:19:10][COMM]Main Task receive event:28 finished processing
[D][05:19:10][COMM]Main Task receive event:65
[D][05:19:10][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:10][COMM]Main Task receive event:65 finished processing
[D][05:19:10][COMM]Main Task receive event:60
[D][05:19:10][COMM]smart_helmet_vol=255,255
[D][05:19:10][COMM]report elecbike
[W][05:19:10][PROT]remove success[1629955150],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:10][COMM]Main Task receive event:60 finished processing
[D][05:19:10][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:10][PROT]index:0
[D][05:19:10][PROT]is_send:1
[D][05:19:10][PROT]sequence_num:13
[D][05:19:10][PROT]retry_timeout:0
[D][05:19:10][PROT]retry_times:3
[D

2025-07-31 20:57:28:314 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:57:28:336 ==>> 检测【关机】
2025-07-31 20:57:28:344 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:57:28:410 ==>> ][05:19:10][PROT]send_path:0x3
[D][05:19:10][PROT]msg_type:0x5d03
[D][05:19:10][PROT]===========================================================
[W][05:19:10][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955150]
[D][05:19:10][PROT]===========================================================
[D][05:19:10][PROT]Sending traceid[999999999990000C]
[D][05:19:10][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:10][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:10][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:10][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:10][SAL ]open socket ind id[4], rst[0]
[D][05:19:10][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:10][SAL ]Cellular task submsg id[8]
[D][05:19:10][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:10][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:10][M2M ]m2m switch to: M2M_GSM_SOCKET_

2025-07-31 20:57:28:515 ==>> OPEN_ACK
[D][05:19:10][CAT1]gsm read msg sub id: 8
[D][05:19:10][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:10][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:10][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:10][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:10][CAT1]<<< 
+CME ERROR: 100

[D][05:19:10][COMM]msg 0220 loss. last_tick:80467. cur_tick:81482. period:100
[D][05:19:10][COMM]msg 0221 loss. last_tick:80467. cur_tick:81482. period:100
[D][05:19:10][COMM]msg 0224 loss. last_tick:80467. cur_tick:81482. period:100
[D][05:19:10][COMM]msg 0260 loss. last_tick:80467. cur_tick:81483. period:100
[D][05:19:10][COMM]msg 0280 loss. last_tick:80467. cur_tick:81483. period:100
[D][05:19:10][COMM]msg 02C0 loss. last_tick:80467. cur_tick:81483. period:100
[D][05:19:10][COMM]msg 02C1 loss. last_tick:80467. cur_tick:81484. period:100
[D][05:19:10][COMM]msg 02C2 loss. last_tick:80467. cur_tick:81484. period:100
[D][05:19:10][COMM]msg 02E0 loss. last_tick:80467. cur_tick:81485. period:100
[D][05:19:10][COMM]msg 02E1 loss. last_tick:80467. cur_tick:81485. period:100
[D][05:19:10][COMM]msg 02E2 loss. last_tick:80467. cur_tick:81485. period:100
[D][05:19:10][COMM]msg 0300 loss. last_ti

2025-07-31 20:57:28:620 ==>> ck:80467. cur_tick:81486. period:100
[D][05:19:10][COMM]msg 0301 loss. last_tick:80467. cur_tick:81486. period:100
[D][05:19:10][COMM]bat msg 0240 loss. last_tick:80467. cur_tick:81486. period:100. j,i:1 54
[D][05:19:10][COMM]bat msg 0241 loss. last_tick:80467. cur_tick:81487. period:100. j,i:2 55
[D][05:19:10][COMM]bat msg 0242 loss. last_tick:80467. cur_tick:81487. period:100. j,i:3 56
[D][05:19:10][COMM]bat msg 0244 loss. last_tick:80467. cur_tick:81487. period:100. j,i:5 58
[D][05:19:10][COMM]bat msg 024E loss. last_tick:80467. cur_tick:81488. period:100. j,i:15 68
[D][05:19:10][COMM]bat msg 024F loss. last_tick:80467. cur_tick:81488. period:100. j,i:16 69
[D][05:19:10][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 81489
[D][05:19:10][COMM]CAN message bat fault change: 0x00000000->0x0001802E 81489
[D][05:19:10][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 81490


2025-07-31 20:57:29:204 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:57:29:309 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:57:29:354 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:57:29:414 ==>>                                                                                                                                                                                                                                                                   ive event:65 finished processing
[D][05:19:11][COMM]Main Task receive event:66
[D][05:19:11][COMM]Try to Auto Lock Bat
[D][05:19:11][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:11][COMM]Main Task receive event:66 finished processing
[D][05:19:11][COMM]Main Task receive event:60
[D][05:19:11][COMM]smart_helmet_vol=255,255
[D][05:19:11][COMM]BAT CAN get state1 Fail 204
[D][05:19:11][COMM]BAT CAN get soc Fail, 204
[D][05:19:11][COMM]Receive Bat Lock cmd 0
[D][05:19:11][COMM]VBUS is 1
[D][05:19:11][COMM]BAT CAN get state2 fail 204
[D][05:19:11][COMM]get soh error
[E][05:19:11][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:11][COMM]report elecbike
[D][05:19:11][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,108

2025-07-31 20:57:29:519 ==>> 00,0

[W][05:19:11][PROT]remove success[1629955151],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:11][PROT]add success [1629955151],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:11][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:11][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:11][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:11][PROT]index:1
[D][05:19:11][PROT]is_send:1
[D][05:19:11][PROT]sequence_num:14
[D][05:19:11][PROT]retry_timeout:0
[D][05:19:11][PROT]retry_times:3
[D][05:19:11][PROT]send_path:0x3
[D][05:19:11][PROT]msg_type:0x5d03
[D][05:19:11][PROT]===========================================================
[W][05:19:11][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955151]
[D][05:19:11][PROT]===========================================================
[D][05:19:11][PROT]Sending traceid[999999999990000D]
[D][05:19:11][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:11][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:11][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:11][COMM]Main Task receive event:60 finished processing
[

2025-07-31 20:57:29:624 ==>> D][05:19:11][COMM]Main Task receive event:61
[D][05:19:11][COMM][D301]:type:3, trace id:280
[D][05:19:11][COMM]id[], hw[000
[D][05:19:11][COMM]get mcMaincircuitVolt error
[D][05:19:11][COMM]get mcSubcircuitVolt error
[D][05:19:11][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:11][COMM]BAT CAN get state1 Fail 204
[D][05:19:11][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:11][COMM]BAT CAN get soc Fail, 204
[D][05:19:11][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:11][COMM]BAT CAN get state2 fail 204
[D][05:19:11][COMM]get bat work mode err
[W][05:19:11][PROT]remove success[1629955151],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:11][PROT]add success [1629955151],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:11][COMM]Main Task receive event:61 finished processing
[D][05:19:11][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hex

2025-07-31 20:57:29:729 ==>> send, index:1, len:2048
[D][05:19:11][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:11][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:11][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:11][COMM]arm_hub_enable: hub power: 0
[D][05:19:11][HSDK]hexlog index save 0 3584 196 @ 0 : 0
[D][05:19:11][HSDK]write save hexlog index [0]
[D][05:19:11][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:11][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:11][COMM]read battery soc:255
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:11][COMM]f:[ec8

2025-07-31 20:57:29:834 ==>> 00m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:11][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:11][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:11][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:11][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:57:29:939 ==>>                                                                                                                                                                                                                                                                                                          [D][05:19:12][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:57:30:357 ==>> [W][05:19:13][COMM]Power Off


2025-07-31 20:57:30:414 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:57:30:427 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:57:30:453 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:57:30:466 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:57:30:537 ==>> [D][05:19:13][FCTY]get_ext_48v_vol

2025-07-31 20:57:30:721 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:57:30:728 ==>> 检测【检测小电池关机电流】
2025-07-31 20:57:30:740 ==>> 开始小电池电流采样
2025-07-31 20:57:30:769 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:30:828 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:57:31:834 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:57:31:912 ==>> CurrentBattery:ƽ��:69.53

2025-07-31 20:57:32:348 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:57:32:358 ==>> 【检测小电池关机电流】通过,【69.53uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:57:32:703 ==>> MES过站成功
2025-07-31 20:57:32:715 ==>> #################### 【测试结束】 ####################
2025-07-31 20:57:32:757 ==>> 关闭5V供电
2025-07-31 20:57:32:770 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:57:32:862 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:57:33:759 ==>> 关闭5V供电成功
2025-07-31 20:57:33:771 ==>> 关闭33V供电
2025-07-31 20:57:33:783 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:57:33:867 ==>> 5A A5 02 5A A5 


2025-07-31 20:57:33:957 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:57:34:766 ==>> 关闭33V供电成功
2025-07-31 20:57:34:777 ==>> 关闭3.7V供电
2025-07-31 20:57:34:802 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:57:34:859 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:57:35:651 ==>>  

2025-07-31 20:57:35:772 ==>> 关闭3.7V供电成功
