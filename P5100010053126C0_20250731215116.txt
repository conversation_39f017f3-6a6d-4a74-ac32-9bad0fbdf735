2025-07-31 21:51:16:920 ==>> MES查站成功:
查站序号:P5100010053126C0验证通过
2025-07-31 21:51:16:928 ==>> 扫码结果:P5100010053126C0
2025-07-31 21:51:16:929 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:51:16:931 ==>> 测试参数版本:2024.10.11
2025-07-31 21:51:16:932 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:51:16:934 ==>> 检测【打开透传】
2025-07-31 21:51:16:936 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:51:16:980 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:51:18:273 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:51:18:289 ==>> 检测【检测接地电压】
2025-07-31 21:51:18:291 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:51:18:383 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:51:18:582 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:51:18:585 ==>> 检测【打开小电池】
2025-07-31 21:51:18:587 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:51:18:689 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:51:18:872 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:51:18:874 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:51:18:876 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:51:18:983 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:51:19:165 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:51:19:167 ==>> 检测【等待设备启动】
2025-07-31 21:51:19:170 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:51:19:461 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:51:19:659 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:51:20:198 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:51:20:274 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:51:20:349 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:51:20:749 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:51:21:227 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:51:21:403 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:51:21:406 ==>> 检测【产品通信】
2025-07-31 21:51:21:407 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:51:21:567 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:51:21:854 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:51:21:901 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:51:21:903 ==>> 检测【初始化完成检测】
2025-07-31 21:51:21:905 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:51:22:112 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:51:22:277 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:22:295 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:51:22:298 ==>> 检测【关闭大灯控制1】
2025-07-31 21:51:22:299 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:51:22:502 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:51:22:624 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:51:22:626 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:51:22:639 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:51:22:788 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:51:22:965 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:51:22:967 ==>> 检测【关闭仪表供电】
2025-07-31 21:51:22:968 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:51:23:180 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:51:23:285 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:23:311 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:51:23:315 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:51:23:318 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:51:23:450 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:51:23:649 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:51:23:652 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:51:23:656 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:51:23:855 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:51:24:006 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:51:24:010 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:51:24:037 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:51:24:142 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:51:24:294 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:24:338 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:51:24:342 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:51:24:344 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:51:24:488 ==>> 5A A5 01 5A A5 


2025-07-31 21:51:24:579 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:51:24:650 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:51:24:653 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:51:24:654 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:51:24:851 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE

2025-07-31 21:51:24:896 ==>>  5017
[D][05:17:54][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
[D][05:17:54][COMM]read battery soc:255
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:51:24:982 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:51:24:984 ==>> 该项需要延时执行
2025-07-31 21:51:25:311 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:26:246 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:51:26:747 ==>>                                                                         evice_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- ge

2025-07-31 21:51:26:852 ==>> t Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55

2025-07-31 21:51:26:957 ==>> ][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect


2025-07-31 21:51:27:033 ==>> 
[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][CAT1]power_urc_cb ret[5]
[D][05:17:56][COMM]read battery soc:255


2025-07-31 21:51:27:338 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:28:326 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:51:28:671 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 21:51:28:987 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:51:28:990 ==>> 检测【33V输入电压ADC】
2025-07-31 21:51:28:993 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:51:29:302 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:17:58][COMM]adc read out 24v adc:1304  volt:32982 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:5 mv
[D][05:17:58][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:51:29:347 ==>>                                                                                                  

2025-07-31 21:51:29:616 ==>> 【33V输入电压ADC】通过,【32602mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:51:29:640 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:51:29:643 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:51:29:714 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007
1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1664mV
Get AD_V4 0mV
Get AD_V5 2758mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:51:29:939 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:29:942 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:51:29:997 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:30:000 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:51:30:002 ==>> 原始值:【2758】, 乘以分压基数【2】还原值:【5516】
2025-07-31 21:51:30:094 ==>> 【TP68_VCC5V5(ADV5)】通过,【5516mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:51:30:098 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:51:30:151 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:51:30:154 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:51:30:157 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:51:30:215 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:51:30:217 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:51:30:301 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1664mV
Get AD_V4 2mV
Get AD_V5 2757mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:51:30:500 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:30:503 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:51:30:538 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:30:541 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:51:30:543 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 21:51:30:570 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:51:30:573 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:51:30:587 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10714 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0

2025-07-31 21:51:30:602 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:51:30:604 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:51:30:631 ==>> ,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:51:30:646 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:51:30:649 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:51:30:797 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1990mV
Get AD_V7 1096mV
OVER 150


2025-07-31 21:51:30:937 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:30:940 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:51:30:968 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:30:971 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:51:30:975 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 21:51:31:004 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:51:31:008 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:51:31:022 ==>>                                                                                                                                                                                                                                   [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087843548

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541239

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:51:31:039 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:51:31:041 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:51:31:079 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:51:31:098 ==>> 检测【打开WIFI(1)】
2025-07-31 21:51:31:101 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:51:31:235 ==>> [

2025-07-31 21:51:31:250 ==>> W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:51:31:341 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:51:31:382 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:51:31:384 ==>> 检测【清空消息队列(1)】
2025-07-31 21:51:31:389 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:51:31:582 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:51:31:678 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:51:31:680 ==>> 检测【打开GPS(1)】
2025-07-31 21:51:31:703 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:51:31:732 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:51:31:882 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:51:31:976 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:51:31:978 ==>> 检测【打开GSM联网】
2025-07-31 21:51:32:003 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:51:32:159 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:51:32:266 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:51:32:269 ==>> 检测【打开仪表供电1】
2025-07-31 21:51:32:271 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:51:32:481 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:51:32:574 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:51:32:578 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:51:32:581 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:51:32:691 ==>> [D][05:18:02][COMM]read battery soc:255


2025-07-31 21:51:32:796 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:51:32:867 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:51:32:872 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:51:32:875 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:51:33:067 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33317]


2025-07-31 21:51:33:177 ==>> 【读取主控ADC采集的仪表电压】通过,【33317mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:51:33:180 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:51:33:182 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:51:33:390 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13726 imu init OK


2025-07-31 21:51:33:472 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:51:33:475 ==>> 检测【AD_V20电压】
2025-07-31 21:51:33:477 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:51:33:525 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:51:33:585 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:51:33:690 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150


2025-07-31 21:51:33:765 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:51:33:795 ==>> 本次取值间隔时间:205ms
2025-07-31 21:51:33:830 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:51:33:833 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:51:33:836 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:51:33:870 ==>> 3A A3 02 00 A3 


2025-07-31 21:51:33:975 ==>> OFF_OUT2
OVER 150


2025-07-31 21:51:34:139 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:51:34:141 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:51:34:145 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:51:34:403 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 25,99

OK

[D]

2025-07-31 21:51:34:433 ==>> [05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][COMM]Main Task receive event:4


2025-07-31 21:51:34:703 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       6
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05

2025-07-31 21:51:34:808 ==>> :18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:51:35:166 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:51:35:417 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:51:36:067 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:51:36:217 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:51:36:262 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,44,33,,,42,40,,,40,41,,,40,1*76

$GBGSV,3,2,09,39,,,39,34,,,37,25,,,42,60,,,41,1*7F

$GBGSV,3,3,09,24,,,37,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1672.110,1672.110,53.448,2097152,2097152,2097152*41

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 21:51:36:367 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:51:36:472 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:51:36:747 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 21:51:37:196 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,60,,,44,59,,,42,33,,,42,40,,,40,1*7D

$GBGSV,4,2,15,41,,,40,39,,,40,34,,,38,7,,,37,1*44

$GBGSV,4,3,15,25,,,36,24,,,36,10,,,36,44,,,34,1*77

$GBGSV,4,4,15,5,,,31,3,,,37,1,,,36,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1581.822,1581.822,50.629,2097152,2097152,2097152*47



2025-07-31 21:51:37:256 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 21:51:37:260 ==>> #################### 【测试结束】 ####################
2025-07-31 21:51:37:295 ==>> 关闭5V供电
2025-07-31 21:51:37:299 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:51:37:380 ==>> 5A A5 04 5A A5 


2025-07-31 21:51:37:482 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:51:38:224 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,60,,,41,59,,,41,40,,,40,1*78

$GBGSV,5,2,17,41,,,40,39,,,40,3,,,40,25,,,39,1*42

$GBGSV,5,3,17,34,,,38,24,,,38,1,,,38,7,,,37,1*7E

$GBGSV,5,4,17,10,,,36,2,,,36,44,,,35,4,,,34,1*77

$GBGSV,5,5,17,5,,,32,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1577.842,1577.842,50.463,2097152,2097152,2097152*4B



2025-07-31 21:51:38:299 ==>> 关闭5V供电成功
2025-07-31 21:51:38:303 ==>> 关闭33V供电
2025-07-31 21:51:38:306 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:51:38:390 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:51:38:570 ==>> [D][05:18:07][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:07][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:51:38:785 ==>> [D][05:18:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:51:39:217 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,60,,,41,59,,,41,40,,,40,1*7C

$GBGSV,5,2,20,41,,,40,39,,,40,3,,,40,25,,,40,1*48

$GBGSV,5,3,20,24,,,39,34,,,38,1,,,38,7,,,38,1*74

$GBGSV,5,4,20,10,,,36,44,,,36,2,,,35,23,,,35,1*47

$GBGSV,5,5,20,4,,,34,5,,,32,16,,,19,9,,,36,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1536.246,1536.246,49.249,2097152,2097152,2097152*4D



2025-07-31 21:51:39:307 ==>> 关闭33V供电成功
2025-07-31 21:51:39:311 ==>> 关闭3.7V供电
2025-07-31 21:51:39:316 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:51:39:382 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:51:39:971 ==>>  

