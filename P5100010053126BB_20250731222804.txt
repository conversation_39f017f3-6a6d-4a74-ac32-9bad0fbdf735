2025-07-31 22:28:04:179 ==>> MES查站成功:
查站序号:P5100010053126BB验证通过
2025-07-31 22:28:04:182 ==>> 扫码结果:P5100010053126BB
2025-07-31 22:28:04:184 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:28:04:185 ==>> 测试参数版本:2024.10.11
2025-07-31 22:28:04:187 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:28:04:188 ==>> 检测【打开透传】
2025-07-31 22:28:04:190 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:28:04:298 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:28:04:523 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:28:04:526 ==>> 检测【检测接地电压】
2025-07-31 22:28:04:528 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:28:04:605 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:28:04:803 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:28:04:806 ==>> 检测【打开小电池】
2025-07-31 22:28:04:809 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:28:04:905 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:28:05:121 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:28:05:124 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:28:05:127 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:28:05:195 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:28:05:410 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:28:05:413 ==>> 检测【等待设备启动】
2025-07-31 22:28:05:415 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:28:05:610 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:28:05:807 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:28:06:425 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:28:06:455 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:28:06:500 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:28:06:898 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:28:07:370 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:28:07:532 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:28:07:534 ==>> 检测【产品通信】
2025-07-31 22:28:07:537 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:28:07:659 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:28:07:839 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:28:07:841 ==>> 检测【初始化完成检测】
2025-07-31 22:28:07:843 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:28:08:042 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:28:08:140 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:28:08:143 ==>> 检测【关闭大灯控制1】
2025-07-31 22:28:08:145 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:28:08:256 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:28:08:423 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:08:429 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:28:08:431 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:28:08:433 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:28:08:634 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:28:08:714 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:28:08:716 ==>> 检测【关闭仪表供电】
2025-07-31 22:28:08:718 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:28:08:894 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:28:08:990 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:28:08:993 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:28:08:994 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:28:09:169 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:28:09:286 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:28:09:288 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:28:09:290 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:28:09:475 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:28:09:568 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:28:09:571 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:28:09:573 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:28:09:767 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:28:09:856 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:28:09:860 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:28:09:862 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:28:10:009 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:28:10:113 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:28:10:132 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:28:10:134 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:28:10:135 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:28:10:204 ==>> 5A A5 03 5A A5 


2025-07-31 22:28:10:295 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 22:28:10:404 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:28:10:407 ==>> 该项需要延时执行
2025-07-31 22:28:10:446 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:10:598 ==>> [D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 22:28:10:982 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:1

2025-07-31 22:28:11:012 ==>> 7:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 22:28:11:468 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:11:682 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:28:12:207 ==>>                                                                                                             ue:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio

2025-07-31 22:28:12:313 ==>>  file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][

2025-07-31 22:28:12:418 ==>> COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D

2025-07-31 22:28:12:509 ==>> ][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 22:28:13:493 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:14:138 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:28:14:305 ==>> [D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 22:28:14:410 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:28:14:413 ==>> 检测【33V输入电压ADC】
2025-07-31 22:28:14:416 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:14:503 ==>> [D][05:17:57][COMM]8696 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:14:713 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:17:57][COMM]adc read out 24v adc:1318  volt:33336 mv
[D][05:17:57][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read right brake adc:15  volt:19 mv
[D][05:17:57][COMM]adc read throttle adc:16  volt:21 mv
[D][05:17:57][COMM]adc read battery ts volt:18 mv
[D][05:17:57][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2457  volt:3959 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:28:14:951 ==>> 【33V输入电压ADC】通过,【32855mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:28:14:954 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:28:14:956 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:28:15:018 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1668mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 2021mV
Get AD_V7 1088mV
OVER 150


2025-07-31 22:28:15:231 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:15:234 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:28:15:250 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:15:252 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:28:15:255 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 22:28:15:269 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:28:15:271 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:28:15:288 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:28:15:291 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:28:15:311 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:28:15:313 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:28:15:408 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1668mV
Get AD_V4 0mV
Get AD_V5 2756mV
Get AD_V6 1988mV
Get AD_V7 1088mV
OVER 150


2025-07-31 22:28:15:513 ==>> [D][05:17:58][COMM]9707 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:28:15:601 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:15:605 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:28:15:632 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:15:635 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:28:15:655 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 22:28:15:658 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:28:15:660 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:28:15:677 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:28:15:680 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:28:15:704 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:28:15:707 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:28:15:813 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1671mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 2023mV
Get AD_V7 1087mV
OVER 150


2025-07-31 22:28:15:873 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018


2025-07-31 22:28:15:986 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:15:989 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:28:16:009 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:16:012 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:28:16:013 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 22:28:16:030 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:28:16:033 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:28:16:052 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2023mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:28:16:057 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:28:16:079 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:28:16:082 ==>> 检测【打开WIFI(1)】
2025-07-31 22:28:16:102 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:28:16:147 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:28:16:252 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:28:16:361 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:28:16:363 ==>> 检测【清空消息队列(1)】
2025-07-31 22:28:16:367 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:28:16:756 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10720 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set add

2025-07-31 22:28:16:816 ==>> ress
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:28:16:910 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:28:16:913 ==>> 检测【打开GPS(1)】
2025-07-31 22:28:16:915 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:28:17:200 ==>>                                                                                                                                                                                                                                                                            [D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087842565

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541244

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret

2025-07-31 22:28:17:230 ==>> [31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:28:17:452 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:28:17:455 ==>> 检测【打开GSM联网】
2025-07-31 22:28:17:458 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:28:17:597 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:28:17:735 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:28:17:738 ==>> 检测【打开仪表供电1】
2025-07-31 22:28:17:741 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:28:17:901 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:28:18:009 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:28:18:012 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:28:18:015 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:28:18:208 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:28:18:286 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:28:18:292 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:28:18:294 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:28:18:481 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[32992]


2025-07-31 22:28:18:559 ==>> 【读取主控ADC采集的仪表电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:28:18:562 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:28:18:564 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:18:833 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:19:059 ==>>                                                                                                [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:28:19:092 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:28:19:095 ==>> 检测【AD_V20电压】
2025-07-31 22:28:19:097 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:19:164 ==>>                                                                                                                                                                                                                                                                                                                                   

2025-07-31 22:28:19:194 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:19:239 ==>>                                                                                                                                                                                                                                                                                                                                         cessing
[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.178.129.92","2409:8D5A:D8A:1CF1:1857:5C54:3118:22AD"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:28:19:284 ==>> 本次取值间隔时间:84ms
2025-07-31 22:28:19:344 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:19:389 ==>> 本次取值间隔时间:92ms
2025-07-31 22:28:19:742 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:02][COMM]13731 imu init OK
[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:28:19:878 ==>> 本次取值间隔时间:477ms
2025-07-31 22:28:20:128 ==>> 本次取值间隔时间:237ms
2025-07-31 22:28:20:131 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:20:204 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 22:28:20:234 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:20:295 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:28:20:400 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,

2025-07-31 22:28:20:430 ==>> ,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:28:20:737 ==>> 本次取值间隔时间:488ms
2025-07-31 22:28:20:755 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:20:860 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:20:920 ==>> 本次取值间隔时间:55ms
2025-07-31 22:28:21:131 ==>> 本次取值间隔时间:210ms
2025-07-31 22:28:21:135 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:28:21:237 ==>>                                                                                                                                                                                          ,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K

2025-07-31 22:28:21:266 ==>> 本次取值间隔时间:123ms
2025-07-31 22:28:21:296 ==>> ,N*20

$GBGST,,0.000,1520.098,1520.098,48.591,2097152,2097152,2097152*4E

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 22:28:21:508 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:28:21:629 ==>> 本次取值间隔时间:361ms
2025-07-31 22:28:21:633 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:21:737 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:21:768 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:28:21:798 ==>> 1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 22:28:22:136 ==>> 本次取值间隔时间:389ms
2025-07-31 22:28:22:153 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:22:227 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,60,,,40,3,,,40,40,,,39,34,,,39,1*42

$GBGSV,3,2,11,59,,,38,39,,,38,41,,,37,24,,,36,1*73

$GBGSV,3,3,11,33,,,35,25,,,35,16,,,34,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1548.989,1548.989,49.511,2097152,2097152,2097152*47

[D][05:18:05][COMM]read battery soc:255


2025-07-31 22:28:22:257 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:22:332 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:22:407 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:28:22:762 ==>> 本次取值间隔时间:495ms
2025-07-31 22:28:22:780 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:22:885 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:22:977 ==>> 本次取值间隔时间:82ms
2025-07-31 22:28:23:006 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 22:28:23:097 ==>> 本次取值间隔时间:111ms
2025-07-31 22:28:23:115 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:23:217 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:23:322 ==>> 本次取值间隔时间:91ms
2025-07-31 22:28:23:337 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,60,,,39,3,,,39,40,,,39,34,,,39,1*41

$GBGSV,4,2,15,59,,,39,39,,,39,41,,,39,1,,,38,1*47

$GBGSV,4,3,15,25,,,37,24,,,36,33,,,36,16,,,35,1*71

$GBGSV,4,4,15,2,,,33,26,,,37,23,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1556.231,1556.231,49.737,2097152,2097152,2097152*41

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 22:28:23:799 ==>> 本次取值间隔时间:476ms
2025-07-31 22:28:23:827 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:28:23:940 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:28:24:001 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1658mV
OVER 150


2025-07-31 22:28:24:289 ==>> $GBGGA,142828.115,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,60,,,39,3,,,39,40,,,39,34,,,39,1*42

$GBGSV,5,2,17,39,,,39,41,,,39,59,,,38,25,,,38,1*73

$GBGSV,5,3,17,1,,,37,33,,,37,24,,,36,16,,,36,1*46

$GBGSV,5,4,17,23,,,34,2,,,33,32,,,33,44,,,30,1*47

$GBGSV,5,5,17,6,,,37,1*42

$GBRMC,142828.115,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142828.115,0.000,1518.402,1518.402,48.568,2097152,2097152,2097152*56

[D][05:18:07][COMM]read battery soc:255


2025-07-31 22:28:24:364 ==>> 本次取值间隔时间:412ms
2025-07-31 22:28:24:382 ==>> 【AD_V20电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:24:384 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:28:24:387 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:28:24:502 ==>> 3A A3 02 00 A3 


2025-07-31 22:28:24:607 ==>> OFF_OUT2
OVER 150
$GBGGA,142828.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,41,,,40,60,,,39,3,,,39,40,,,39,1*4A

$GBGSV

2025-07-31 22:28:24:661 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:28:24:666 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:28:24:672 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:28:24:675 ==>> ,5,2,20,34,,,39,39,,,39,59,,,39,25,,,38,1*74

$GBGSV,5,3,20,33,,,37,1,,,36,24,,,36,16,,,36,1*43

$GBGSV,5,4,20,23,,,35,10,,,33,2,,,33,32,,,33,1*40

$GBGSV,5,5,20,12,,,32,44,,,31,9,,,26,6,,,38,1*74

$GBRMC,142828.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142828.515,0.000,1483.809,1483.809,47.507,2097152,2097152,2097152*54



2025-07-31 22:28:24:897 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:24:932 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:28:24:935 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:28:24:937 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:28:25:002 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:28:25:202 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:28:25:208 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:28:25:215 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:28:25:423 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:25:471 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:28:25:474 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:28:25:477 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:28:25:745 ==>> $GBGGA,142829.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,3,,,39,40,,,39,1*4E

$GBGSV,7,2,26,34,,,39,39,,,39,59,,,38,25,,,38,1*71

$GBGSV,7,3,26,33,,,37,1,,,36,24,,,36,16,,,36,1*47

$GBGSV,7,4,26,11,,,36,6,,,35,7,,,35,43,,,35,1*74

$GBGSV,7,5,26,23,,,34,10,,,34,32,,,33,12,,,33,1*72

$GBGSV,7,6,26,44,,,33,2,,,32,4,,,32,5,,,31,1*42

$GBGSV,7,7,26,9,,,30,38,,,28,1*49

$GBRMC,142829.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142829.515,0.000,1462.219,1462.219,46.796,2097152,2097152,2097152*5E

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:28:26:008 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:28:26:011 ==>> 检测【AD_V21电压】
2025-07-31 22:28:26:013 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:28:26:110 ==>> 1A A1 20 00 00 
Get AD_V21 1656mV
OVER 150


2025-07-31 22:28:26:215 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:28:26:475 ==>> 本次取值间隔时间:457ms
2025-07-31 22:28:26:505 ==>> 【AD_V21电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:26:508 ==>> 检测【关闭仪表供电2】
2025-07-31 22:28:26:512 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:28:26:751 ==>> $GBGGA,142830.515,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,3,,,39,40,,,39,1*4E

$GBGSV,7,2,26,34,,,39,39,,,39,59,,,38,25,,,38,1*71

$GBGSV,7,3,26,33,,,37,24,,,37,1,,,36,16,,,36,1*46

$GBGSV,7,4,26,11,,,36,7,,,36,43,,,35,6,,,34,1*76

$GBGSV,7,5,26,23,,,34,10,,,34,32,,,33,12,,,33,1*72

$GBGSV,7,6,26,44,,,33,2,,,32,4,,,32,5,,,31,1*42

$GBGSV,7,7,26,9,,,31,38,,,29,1*49

$GBRMC,142830.515,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142830.515,0.000,1466.996,1466.996,46.942,2097152,2097152,2097152*51

[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:28:26:790 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:28:26:795 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:28:26:800 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:28:26:992 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:28:27:069 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:28:27:074 ==>> 检测【打开AccKey2供电】
2025-07-31 22:28:27:078 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:28:27:276 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:28:27:339 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:28:27:343 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:28:27:345 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:27:610 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:10][COMM]adc read out 24v adc:1313  volt:33209 mv
[D][05:18:10][COMM]adc read left brake adc:18  volt:23 mv
[D][05:18:10][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:10][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:10][COMM]adc read battery ts volt:21 mv
[D][05:18:10][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:10][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2462  volt:3967 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:28:27:715 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                    ,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142831.515,0.000,731.341,731.341,668.832,2097152,2097152,2097152*6C



2025-07-31 22:28:27:870 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33209mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:28:27:875 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:28:27:879 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:28:28:069 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:28:28:158 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:28:28:162 ==>> 该项需要延时执行
2025-07-31 22:28:28:219 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:28:28:710 ==>> $GBGGA,142832.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,39,,,39,34,,,39,1*7B

$GBGSV,7,2,26,41,,,39,3,,,38,59,,,38,25,,,38,1*4B

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,43,,,35,10,,,34,1*71

$GBGSV,7,5,26,44,,,34,6,,,34,12,,,34,23,,,34,1*44

$GBGSV,7,6,26,9,,,33,2,,,32,32,,,32,4,,,31,1*4F

$GBGSV,7,7,26,5,,,30,38,,,29,1*44

$GBRMC,142832.515,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142832.515,0.000,731.332,731.332,668.823,2097152,2097152,2097152*6F



2025-07-31 22:28:29:716 ==>> $GBGGA,142833.515,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,39,,,39,1*77

$GBGSV,7,2,26,34,,,39,3,,,38,59,,,38,25,,,38,1*49

$GBGSV,7,3,26,7,,,37,24,,,37,33,,,37,1,,,36,1*77

$GBGSV,7,4,26,16,,,36,11,,,36,10,,,35,43,,,35,1*70

$GBGSV,7,5,26,23,,,35,44,,,34,12,,,34,6,,,34,1*45

$GBGSV,7,6,26,9,,,33,2,,,32,32,,,32,4,,,31,1*4F

$GBGSV,7,7,26,5,,,30,38,,,29,1*44

$GBRMC,142833.515,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142833.515,0.000,734.517,734.517,671.736,2097152,2097152,2097152*6D



2025-07-31 22:28:30:220 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:28:30:710 ==>> $GBGGA,142834.515,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,3,,,39,39,,,39,1*4F

$GBGSV,7,2,26,34,,,39,41,,,39,59,,,38,25,,,38,1*7E

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,10,,,35,43,,,35,1*70

$GBGSV,7,5,26,44,,,34,6,,,34,12,,,34,23,,,34,1*44

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,4,,,31,1*4E

$GBGSV,7,7,26,5,,,29,38,,,29,1*4C

$GBRMC,142834.515,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142834.515,0.000,732.927,732.927,670.282,2097152,2097152,2097152*61



2025-07-31 22:28:31:160 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:28:31:191 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:28:31:195 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:31:520 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:14][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:14][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:14][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:14][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:14][COMM]adc read battery ts volt:21 mv
[D][05:18:14][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:14][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2438  volt:3928 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:28:31:713 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:28:31:717 ==>> 检测【打开AccKey1供电】
2025-07-31 22:28:31:720 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:28:31:722 ==>> $GBGGA,142835.515,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,39,,,39,59,,,38,34,,,38,25,,,38,1*70

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,10,,,35,6,,,35,1*41

$GBGSV,7,5,26,43,,,35,44,,,34,12,,,34,23,,,34,1*74

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,4,,,31,1*4E

$GBGSV,7,7,26,5,,,30,38,,,29,1*44

$GBRMC,142835.515,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142835.515,0.000,734.515,734.515,671.733,2097152,2097152,2097152*6E



2025-07-31 22:28:31:896 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:28:32:013 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:28:32:017 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:28:32:020 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:28:32:107 ==>> 1A A1 00 40 00 
Get AD_V14 2654mV
OVER 150


2025-07-31 22:28:32:242 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 22:28:32:272 ==>> 原始值:【2654】, 乘以分压基数【2】还原值:【5308】
2025-07-31 22:28:32:306 ==>> 【读取AccKey1电压(ADV14)前】通过,【5308mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:28:32:312 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:28:32:335 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:32:622 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:15][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:15][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:15][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2394  volt:3857 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:28:32:727 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:28:32:836 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:28:32:840 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:28:32:842 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:28:32:983 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:28:33:114 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:28:33:118 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:28:33:123 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:28:33:196 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 22:28:33:377 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 22:28:33:450 ==>> 【读取AccKey1电压(ADV14)后】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:28:33:453 ==>> 检测【打开WIFI(2)】
2025-07-31 22:28:33:470 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:28:33:727 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6
$GBGGA,142837.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,59,,,39,39,,,39,34,,,39,25,,,38,1*70

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,10,,,35,6,,,35,1*41

$GBGSV,7,5,26,43,,,35,44,,,34,12,,,34,23,,,34,1*74

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,29,1*44

$GBRMC,142837.515,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142837.515,0.000,736.902,736.902,673.916,2097152,2097152,2097152*67



2025-07-31 22:28:33:989 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:28:33:994 ==>> 检测【转刹把供电】
2025-07-31 22:28:33:999 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:34:169 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:28:34:244 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:28:34:262 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:28:34:266 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:28:34:271 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:34:334 ==>> +WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,CC057790A621,-61
+WIFISCAN:4,2,CC057790A5C0,-82
+WIFISCAN:4,3,CC057790A5C1,-83

[D][05:18:17][CAT1]wifi scan report total[4]


2025-07-31 22:28:34:364 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:28:34:716 ==>> [D][05:18:17][GNSS]recv submsg id[3]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,142838.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,39,,,39,34,,,39,59,,,38,25,,,38,1*71

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,43,,,35,10,,,34,1*71

$GBGSV,7,5,26,44,,,34,6,,,34,12,,,34,23,,,34,1*44

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,29,1*44

$GBRMC,142838.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142838.515,0.000,734.515,734.515,671.733,2097152,2097152,2097152*63



2025-07-31 22:28:35:292 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:35:398 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:28:35:458 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 22:28:35:503 ==>> 1A A1 00 80 00 
Get AD_V15 2391mV
OVER 150


2025-07-31 22:28:35:563 ==>> 原始值:【2391】, 乘以分压基数【2】还原值:【4782】
2025-07-31 22:28:35:582 ==>> 【读取AD_V15电压(前)】通过,【4782mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:28:35:587 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:28:35:609 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:35:683 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:28:35:713 ==>> $GBGGA,142839.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,39,,,39,34,,,39,59,,,38,25,,,38,1*71

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,16,,,36,1*40

$GBGSV,7,4,26,11,,,36,1,,,35,6,,,35,43,,,35,1*72

$GBGSV,7,5,26,10,,,34,44,,,34,12,,,34,23,,,34,1*73

$GBGSV,7,6,26,2,,,33,9,,,33,5,,,32,32,,,32,1*4C

$GBGSV,7,7,26,4,,,31,38,,,29,1*44

$GBRMC,142839.515,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142839.515,0.000,735.307,735.307,672.457,2097152,2097152,2097152*60



2025-07-31 22:28:35:880 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:28:36:244 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:28:36:597 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:36:704 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:28:36:734 ==>> $GBGGA,142840.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,41,,,40,40,,,39,3,,,39,34,,,39,1*4E

$GBGSV,7,2,27,60,,,38,59,,,38,39,,,38,25,,,38,1*71

$GBGSV,7,3,27,7,,,37,33,,,37,24,,,36,1,,,36,1*77

$GBGSV,7,4,27,16,,,36,11,,,36,43,,,35,10,,,34,1*70

$GBGSV,7,5,27,44,,,34,6,,,34,12,,,34,23,,,34,1*45

$GBGSV,7,6,27,2,,,33,9,,,33,32,,,32,5,,,31,1*4E

$GBGSV,7,7,27,4,,,31,38,,,29,42,,,36,1*46

$GBRMC,142840.515,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142840.515,0.000,732.919,732.919,670.273,2097152,2097152,2097152*6C



2025-07-31 22:28:36:839 ==>> [W][05:18:20][COMM]>>>>>Input command

2025-07-31 22:28:36:869 ==>>  = ?<<<<<


2025-07-31 22:28:37:649 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:28:37:709 ==>> $GBGGA,142841.515,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,39,,,39,34,,,39,59,,,38,25,,,38,1*71

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,16,,,36,1*40

$GBGSV,7,4,26,11,,,36,1,,,35,43,,,35,10,,,34,1*44

$GBGSV,7,5,26,44,,,34,6,,,34,12,,,34,23,,,34,1*44

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,30,1*4C

$GBRMC,142841.515,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142841.515,0.000,734.511,734.511,671.729,2097152,2097152,2097152*66



2025-07-31 22:28:37:754 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:28:37:784 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:28:37:889 ==>> 1A A1 01 00 00 
Get AD_V16 2419mV
OVER 150


2025-07-31 22:28:37:919 ==>> 原始值:【2419】, 乘以分压基数【2】还原值:【4838】
2025-07-31 22:28:37:943 ==>> 【读取AD_V16电压(前)】通过,【4838mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:28:37:947 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:28:37:952 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:38:209 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:21][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:21][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:21][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:21][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:21][COMM]adc read battery ts volt:14 mv
[D][05:18:21][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:21][COMM]adc read throttle brake in adc:3075  volt:5405 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:28:38:239 ==>>                                          

2025-07-31 22:28:38:472 ==>> 【转刹把供电电压(主控ADC)】通过,【5405mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:28:38:478 ==>> 检测【转刹把供电电压】
2025-07-31 22:28:38:484 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:38:824 ==>> $GBGGA,142842.515,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,3,,,39,34,,,39,1*42

$GBGSV,7,2,26,41,,,39,59,,,38,39,,,38,25,,,38,1*72

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,10,,,35,11,,,35,43,,,35,1*73

$GBGSV,7,5,26,23,,,35,44,,,34,6,,,34,12,,,34,1*45

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,30,1*4C

$GBRMC,142842.515,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142842.515,0.000,734.504,734.504,671.723,2097152,2097152,2097152*6F

[W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:21][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:21][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:21][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:21][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:21][COMM]adc read battery ts volt:14 mv
[D][05:18:21][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:21][COMM]adc read throttle brake in adc:3079  volt:5412 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2397  volt:

2025-07-31 22:28:38:869 ==>> 3862 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:28:39:010 ==>> 【转刹把供电电压】通过,【5412mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:28:39:015 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:28:39:038 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:28:39:191 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:28:39:283 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:28:39:288 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:28:39:293 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:28:39:389 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:28:39:466 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:28:39:496 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:28:39:502 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:28:39:601 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:28:39:724 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:28:39:728 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:28:39:732 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:28:39:738 ==>> $GBGGA,142843.515,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,3,,,39,34,,,39,1*42

$GBGSV,7,2,26,41,,,39,59,,,38,39,,,38,25,,,38,1*72

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,6,,,35,11,,,35,43,,,35,1*44

$GBGSV,7,5,26,10,,,34,44,,,34,12,,,34,23,,,34,1*73

$GBGSV,7,6,26,2,,,33,9,,,33,5,,,31,4,,,31,1*79

$GBGSV,7,7,26,32,,,31,38,,,30,1*79

$GBRMC,142843.515,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142843.515,0.000,732.917,732.917,670.271,2097152,2097152,2097152*6D

[W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:28:39:826 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:28:39:901 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:28:39:950 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:28:39:956 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:28:39:965 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:28:40:006 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:28:40:222 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:28:40:226 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:28:40:231 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:28:40:266 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:28:40:296 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:28:40:497 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:28:40:501 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:28:40:510 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:28:40:601 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:28:40:706 ==>> $GBGGA,142844.515,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,3,,,39,39,,,39,1*4F

$GBGSV,7,2,26,34,,,39,41,,,39,59,,,38,25,,,38,1*7E

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,1,,,36,1*76

$GBGSV,7,4,26,16,,,36,11,,,36,10,,,35,43,,,35,1*70

$GBGSV,7,5,26,44,,,34,6,,,34,12,,,34,23,,,34,1*44

$GBGSV,7,6,26,2,,,33,9,,,33,5,,,31,4,,,31,1*79

$GBGSV,7,7,26,32,,,31,38,,,30,1*79

$GBRMC,142844.515,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142844.515,0.000,734.510,734.510,671.728,2097152,2097152,2097152*62



2025-07-31 22:28:40:770 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:28:40:777 ==>> 检测【左刹电压测试1】
2025-07-31 22:28:40:782 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:41:119 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:24][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:24][COMM]adc read left brake adc:1733  volt:2284 mv
[D][05:18:24][COMM]adc read right brake adc:1732  volt:2283 mv
[D][05:18:24][COMM]adc read throttle adc:1729  volt:2279 mv
[D][05:18:24][COMM]adc read battery ts volt:13 mv
[D][05:18:24][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:24][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2465  volt:3971 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:28:41:305 ==>> 【左刹电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 22:28:41:329 ==>> 检测【右刹电压测试1】
2025-07-31 22:28:41:333 ==>> 【右刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 22:28:41:336 ==>> 检测【转把电压测试1】
2025-07-31 22:28:41:351 ==>> 【转把电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 22:28:41:359 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:28:41:366 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:28:41:408 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:28:41:625 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:28:41:631 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:28:41:638 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:28:41:712 ==>> $GBGGA,142845.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,60,,,39,40,,,39,3,,,39,34,,,39,1*42

$GBGSV,7,2,26,41,,,39,59,,,38,39,,,38,25,,,38,1*72

$GBGSV,7,3,26,7,,,37,33,,,37,24,,,36,16,,,36,1*40

$GBGSV,7,4,26,11,,,36,1,,,35,6,,,35,43,,,35,1*72

$GBGSV,7,5,26,10,,,34,44,,,34,12,,,34,23,,,34,1*73

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,30,1*4C

$GBRMC,142845.515,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142845.515,0.000,733.709,733.709,670.996,2097152,2097152,2097152*69

3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:28:41:898 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:28:41:902 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:28:41:908 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:28:42:002 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:28:42:171 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:28:42:178 ==>> 检测【左刹电压测试2】
2025-07-31 22:28:42:201 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:28:42:258 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 22:28:42:513 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:25][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:25][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:25][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:25][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:25][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:25][COMM]adc read battery ts volt:13 mv
[D][05:18:25][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:25][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:25][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:25][COMM]arm_hub adc read vbat adc:2465  volt:3971 mv
[D][05:18:25][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:25][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:25][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:28:42:699 ==>> 【左刹电压测试2】通过,【14】符合目标值【0】至【50】要求!
2025-07-31 22:28:42:707 ==>> 检测【右刹电压测试2】
2025-07-31 22:28:42:722 ==>> $GBGGA,142846.515,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,34,,,39,59,,,38,39,,,38,25,,,38,1*70

$GBGSV,7,3,26,7,,,37,24,,,37,33,,,37,1,,,36,1*77

$GBGSV,7,4,26,16,,,36,10,,,35,6,,,35,11,,,35,1*42

$GBGSV,7,5,26,43,,,35,23,,,35,44,,,34,12,,,34,1*75

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,30,1*4C

$GBRMC,142846.515,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142846.515,0.000,736.894,736.894,673.908,2097152,2097152,2097152*6E



2025-07-31 22:28:42:753 ==>> 【右刹电压测试2】通过,【17】符合目标值【0】至【50】要求!
2025-07-31 22:28:42:760 ==>> 检测【转把电压测试2】
2025-07-31 22:28:42:764 ==>> 【转把电压测试2】通过,【21】符合目标值【0】至【50】要求!
2025-07-31 22:28:42:769 ==>> 检测【晶振检测】
2025-07-31 22:28:42:786 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:28:42:875 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:26][COMM][lf state:1][hf state:1]


2025-07-31 22:28:43:029 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:28:43:035 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:28:43:042 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:28:43:116 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1668mV
Get AD_V4 1647mV
Get AD_V5 2762mV
Get AD_V6 2022mV
Get AD_V7 1087mV
OVER 150


2025-07-31 22:28:43:323 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:28:43:329 ==>> 检测【检测BootVer】
2025-07-31 22:28:43:336 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:28:43:746 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541244
[D][05:18:26][FCTY]HardwareID  = 867222087842565
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E9E423913CD5
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 100 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 33, adc = 1305
[D][05:18:26][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Ba

2025-07-31 22:28:43:836 ==>> t1         = 3775 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
$GBGGA,142847.515,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,41,,,40,60,,,39,40,,,39,3,,,39,1*4E

$GBGSV,7,2,26,59,,,39,39,,,39,34,,,39,25,,,38,1*70

$GBGSV,7,3,26,7,,,37,24,,,37,16,,,37,33,,,37,1*40

$GBGSV,7,4,26,1,,,36,11,,,36,10,,,35,6,,,35,1*77

$GBGSV,7,5,26,43,,,35,23,,,35,44,,,34,12,,,34,1*75

$GBGSV,7,6,26,2,,,33,9,,,33,32,,,32,5,,,31,1*4F

$GBGSV,7,7,26,4,,,31,38,,,30,1*4C

$GBRMC,142847.515,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142847.515,0.000,740.081,740.081,676.823,2097152,2097152,2097152*62



2025-07-31 22:28:43:871 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:28:43:886 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:28:43:894 ==>> 检测【检测固件版本】
2025-07-31 22:28:43:898 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:28:43:906 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:28:43:918 ==>> 检测【检测蓝牙版本】
2025-07-31 22:28:43:945 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:28:43:951 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:28:43:955 ==>> 检测【检测MoBikeId】
2025-07-31 22:28:43:972 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:28:43:976 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:28:44:000 ==>> 检测【检测蓝牙地址】
2025-07-31 22:28:44:005 ==>> 取到目标值:E9E423913CD5
2025-07-31 22:28:44:009 ==>> 【检测蓝牙地址】通过,【E9E423913CD5】符合目标值【】要求!
2025-07-31 22:28:44:018 ==>> 提取到蓝牙地址:E9E423913CD5
2025-07-31 22:28:44:022 ==>> 检测【BOARD_ID】
2025-07-31 22:28:44:047 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:28:44:052 ==>> 检测【检测充电电压】
2025-07-31 22:28:44:063 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:28:44:078 ==>> 检测【检测VBUS电压1】
2025-07-31 22:28:44:090 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:28:44:096 ==>> 检测【检测充电电流】
2025-07-31 22:28:44:124 ==>> 【检测充电电流】通过,【100ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:28:44:128 ==>> 检测【检测IMEI】
2025-07-31 22:28:44:134 ==>> 取到目标值:867222087842565
2025-07-31 22:28:44:156 ==>> 【检测IMEI】通过,【867222087842565】符合目标值【】要求!
2025-07-31 22:28:44:160 ==>> 提取到IMEI:867222087842565
2025-07-31 22:28:44:169 ==>> 检测【检测IMSI】
2025-07-31 22:28:44:176 ==>> 取到目标值:460130071541244
2025-07-31 22:28:44:185 ==>> 【检测IMSI】通过,【460130071541244】符合目标值【】要求!
2025-07-31 22:28:44:189 ==>> 提取到IMSI:460130071541244
2025-07-31 22:28:44:198 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:28:44:202 ==>> 取到目标值:460130071541244
2025-07-31 22:28:44:209 ==>> 【校验网络运营商(移动)】通过,【460130071541244】符合目标值【】要求!
2025-07-31 22:28:44:215 ==>> 检测【打开CAN通信】
2025-07-31 22:28:44:221 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:28:44:304 ==>> [D][05:18:27][COMM]read battery soc:255
[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:28:44:488 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:28:44:503 ==>> 检测【检测CAN通信】
2025-07-31 22:28:44:507 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:28:44:626 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:28:44:671 ==>> [D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38875
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:28:44:746 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:28:44:764 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:28:44:772 ==>> 检测【关闭CAN通信】
2025-07-31 22:28:44:793 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:28:44:806 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:28:44:866 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:28:44:911 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:28:45:035 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:28:45:039 ==>> 检测【打印IMU STATE】
2025-07-31 22:28:45:046 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:28:45:602 ==>>                                                     9,0.61,76.101,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,33,11,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,59,52,129,39,10,50,204,35,1*46

$GBGSV,7,3,26,25,49,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*40

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,33,35,202,37,1*7B

$GBGSV,7,5,26,11,35,141,36,4,32,111,31,12,31,76,34,24,26,59,36,1*4D

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,32,44,17,54,34,1*78

$GBGSV,7,7,26,32,9,318,31,38,7,189,30,1*7D

$GBRMC,142844.520,A,2301.2574502,N,11421.9422522,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

[D][05:18:27][GNSS]HD8040 GPS
[D][05:18:27][GNSS]GPS diff_sec 124017017, report 0x42 frame
$GBGST,142844.520,1.867,0.169,0.163,0.244,2.506,2.768,7.601*77

[D][05:18:27][COMM]Main Task receive event:131
[D][05:18:27][COMM]index:0,power_mode:0xFF
[D][05:18:27][COMM]index:1,sound_mode:0xFF
[D][05:18:27][COMM]index:2,gsensor_mode:0xFF
[D][05:18:27][COMM]index:3,report_freq_mode:0xFF
[D][05:18:

2025-07-31 22:28:45:707 ==>> 27][COMM]index:4,report_period:0xFF
[D][05:18:27][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:27][COMM]index:6,normal_reset_period:0xFF
[D][05:18:27][COMM]index:7,spock_over_speed:0xFF
[D][05:18:27][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:27][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:27][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:27][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:27][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:27][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:27][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:27][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:27][COMM]index:16,imu_config_params:0xFF
[D][05:18:27][COMM]index:17,long_connect_params:0xFF
[D][05:18:27][COMM]index:18,detain_mark:0xFF
[D][05:18:27][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:27][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:27][COMM]index:21,mc_mode:0xFF
[D][05:18:27][COMM]index:22,S_mode:0xFF
[D][05:18:27][COMM]index:23,overweight:0xFF
[D][05:18:27][COMM]index:24,standstill_mode:0xFF
[D][05:18:27][COMM]index:25,night_mode:0xFF
[D][05:18:27][COMM]index:26,experiment1:0xFF
[D][05:18:27][COMM]index:27,experiment2:0xFF
[D][0

2025-07-31 22:28:45:812 ==>> 5:18:27][COMM]index:28,experiment3:0xFF
[D][05:18:27][COMM]index:29,experiment4:0xFF
[D][05:18:27][COMM]index:30,night_mode_start:0xFF
[D][05:18:27][COMM]index:31,night_mode_end:0xFF
[D][05:18:27][COMM]index:33,park_report_minutes:0xFF
[D][05:18:28][COMM]index:34,park_report_mode:0xFF
[D][05:18:28][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:28][COMM]index:38,charge_battery_para: FF
[D][05:18:28][COMM]index:39,multirider_mode:0xFF
[D][05:18:28][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:28][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:28][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:28][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:28][COMM]index:44,riding_duration_config:0xFF
[D][05:18:28][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:28][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:28][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:28][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:28][COMM]index:49,mc_load_startup:0xFF
[D][05:18:28][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:28][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:28][COMM]index:52,traffic_mode:0xFF
[D][05:18:28][COMM]index:53,traffic_info_collect_freq:0xFF


2025-07-31 22:28:45:917 ==>> [D][05:18:28][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:28][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:28][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:28][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:28][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:28][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:28][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:28][COMM]index:63,experiment5:0xFF
[D][05:18:28][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:28][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:28][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:28][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:28][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:28][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:28][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:28][COMM]index:72,experiment6:0xFF
[D][05:18:28][COMM]index:73,experiment7:0xFF
[D][05:18:28][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:28][COMM]index:75,zero_value_from_server:-1
[D][05:18:28][COMM]index:76,multirider_threshold:255
[D][05:18:28][COMM]index:77,experiment8:255
[D][05:18:28][COMM]index:78,temp_park_audio_p

2025-07-31 22:28:46:022 ==>> lay_duration:255
[D][05:18:28][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:28][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:28][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:28][COMM]index:83,loc_report_interval:255
[D][05:18:28][COMM]index:84,multirider_threshold_p2:255
[D][05:18:28][COMM]index:85,multirider_strategy:255
[D][05:18:28][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:28][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:28][COMM]index:90,weight_param:0xFF
[D][05:18:28][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:28][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:28][COMM]index:95,current_limit:0xFF
[D][05:18:28][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:28][COMM]index:100,location_mode:0xFF

[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:28][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:28][PROT]add success [1629955108],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:131 finished processing
[D][05:18:28][M2M 

2025-07-31 22:28:46:067 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:28:46:127 ==>> ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:1
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[294]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:28][CAT1]gsm read msg sub id: 15
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][CAT1]Send Data To S

2025-07-31 22:28:46:232 ==>> erver[294][297] ... ->:
0093B98A113311331133113311331B88B52FED58B6DA723FF896B82EC89F860B000507B9C1149B54EBDF814666ECEBDDD5C86013E9B00C72B5CE7EA0C8D1268286B40687423C23C92ADD601906B8DAE5A092D030FA3929E718D5D077F3F6320BB3B71E8E9B53C0316D6030ED962ADB1AF49B25056108F4CB35A0D88289FAAAAFDC79D6AA759889B0352934A21C1D4CEA71D99E
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]
$GBGGA,142845.020,2301.2576751,N,11421.9428252,E,1,19,0.61,76.692,M,-1.770,M,,*5D

[W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[3

2025-07-31 22:28:46:337 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 22:28:46:341 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:28:46:357 ==>> 检测【六轴自检】
2025-07-31 22:28:46:366 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:28:46:427 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    soc:255
[W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:28:46:533 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:28:47:358 ==>> $GBGGA,142847.000,2301.2580571,N,11421.9431254,E,1,19,0.61,78.193,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,33,11,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,59,52,129,39,10,50,204,35,1*46

$GBGSV,7,3,26,25,49,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*40

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,33,35,202,37,1*7B

$GBGSV,7,5,26,11,35,141,36,4,32,111,31,12,31,76,34,24,26,59,37,1*4C

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,31,38,7,189,30,1*7D

$GBGSV,3,1,11,40,63,169,41,39,59,33,41,25,49,349,40,34,43,110,39,5*43

$GBGSV,3,2,11,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*48

$GBGSV,3,3,11,23,22,293,38,44,17,54,34,32,9,318,29,5*49

$GBRMC,142847.000,A,2301.2580571,N,11421.9431254,E,0.000,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,142847.000,2.695,0.214,0.206,0.296,2.089,2.121,3.886*79



2025-07-31 22:28:48:379 ==>> $GBGGA,142848.000,2301.2581218,N,11421.9431267,E,1,19,0.61,78.452,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,33,11,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,59,52,129,39,10,50,204,35,1*46

$GBGSV,7,3,26,25,49,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*40

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,33,35,202,37,1*7B

$GBGSV,7,5,26,11,35,141,36,4,32,111,31,12,31,76,34,24,26,59,37,1*4C

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,32,44,17,54,35,1*79

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,49,349,40,34,43,110,40,5*4E

$GBGSV,3,2,12,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*4B

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,24,5*40

$GBRMC,142848.000,A,2301.2581218,N,11421.9431267,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,142848.000,3.073,0.233,0.226,0.314,2.256,2.275,3.744*76

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6
[D][05:

2025-07-31 22:28:48:409 ==>> 18:31][COMM]read battery soc:255


2025-07-31 22:28:48:454 ==>>                                                                                                                                                                                                                                                 

2025-07-31 22:28:49:365 ==>> $GBGGA,142849.000,2301.2581411,N,11421.9431316,E,1,19,0.61,78.646,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,11,33,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,37,59,52,129,39,10,50,204,35,1*47

$GBGSV,7,3,26,25,49,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*40

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,11,35,141,36,1*7E

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,34,1*7B

$GBGSV,7,7,26,32,9,318,31,38,7,189,31,1*7C

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,49,349,40,34,43,110,40,5*4E

$GBGSV,3,2,12,41,42,270,43,33,35,202,40,24,26,59,38,43,26,171,35,5*44

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,25,5*41

$GBRMC,142849.000,A,2301.2581411,N,11421.9431316,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,142849.000,3.347,0.269,0.260,0.362,2.376,2.389,3.670*78



2025-07-31 22:28:49:697 ==>> [D][05:18:32][COMM]msg 0601 loss. last_tick:38874. cur_tick:43888. period:500
[D][05:18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43889


2025-07-31 22:28:50:383 ==>> $GBGGA,142850.000,2301.2581604,N,11421.9431430,E,1,19,0.61,78.870,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,11,33,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,59,52,129,39,10,50,204,35,1*46

$GBGSV,7,3,26,25,50,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*48

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,11,35,141,36,1*7E

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,31,38,7,189,31,1*7C

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,40,24,26,59,38,43,26,171,35,5*44

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,26,5*42

$GBRMC,142850.000,A,2301.2581604,N,11421.9431430,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142850.000,3.313,0.199,0.194,0.270,2.341,2.350,3.524*75

[D][05:18:33][COMM]read battery soc:255


2025-07-31 22:28:51:367 ==>> $GBGGA,142851.000,2301.2581548,N,11421.9431619,E,1,19,0.61,79.039,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,11,33,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,37,59,52,129,39,10,50,204,35,1*47

$GBGSV,7,3,26,25,50,349,38,9,49,341,34,1,48,125,36,2,45,237,33,1*4F

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,11,35,141,36,1*7E

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,39,24,26,59,38,43,26,171,35,5*4A

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,26,5*42

$GBRMC,142851.000,A,2301.2581548,N,11421.9431619,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,142851.000,3.313,0.245,0.236,0.332,2.327,2.335,3.424*78



2025-07-31 22:28:52:397 ==>> $GBGGA,142852.000,2301.2581367,N,11421.9431773,E,1,19,0.61,79.111,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,11,33,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,38,3,60,190,39,39,59,33,39,1*4C

$GBGSV,7,2,26,6,57,4,35,16,57,8,37,59,52,129,39,10,50,204,35,1*47

$GBGSV,7,3,26,25,50,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*48

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,11,35,141,36,1*7E

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,42,33,35,202,40,24,26,59,38,43,26,171,35,5*45

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142852.000,A,2301.2581367,N,11421.9431773,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142852.000,3.279,0.220,0.213,0.300,2.299,2.305,3.328*7F

[D][05:18:35][COMM]read battery soc:255


2025-07-31 22:28:53:366 ==>> $GBGGA,142853.000,2301.2581331,N,11421.9431887,E,1,19,0.61,79.157,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,06,16,10,25,09,34,41,11,33,1.16,0.61,0.98,4*0D

$GBGSA,A,3,12,24,43,23,44,32,38,,,,,,1.16,0.61,0.98,4*01

$GBGSV,7,1,26,40,63,169,39,7,61,195,37,3,60,190,39,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,59,52,129,39,10,50,204,35,1*46

$GBGSV,7,3,26,25,50,349,38,9,49,341,33,1,48,125,36,2,45,237,33,1*48

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,41,238,39,11,35,141,36,1*7E

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*4B

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142853.000,A,2301.2581331,N,11421.9431887,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,142853.000,3.362,0.202,0.197,0.278,2.336,2.342,3.306*7F



2025-07-31 22:28:54:410 ==>> $GBGGA,142854.000,2301.2581352,N,11421.9431879,E,1,23,0.57,79.175,M,-1.770,M,,*56

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,38,39,59,33,39,1*4D

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,39,1,45,125,36,2,45,237,33,1*4B

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,36,1*7C

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,40,24,26,59,38,43,26,171,35,5*44

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142854.000,A,2301.2581352,N,11421.9431879,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142854.000,3.263,0.197,0.194,0.271,2.277,2.281,3.207*77

[D][05:18:37][COMM]read battery soc:255


2025-07-31 22:28:55:361 ==>> $GBGGA,142855.000,2301.2581467,N,11421.9431990,E,1,23,0.57,79.241,M,-1.770,M,,*54

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,40,3,61,190,39,7,61,195,38,39,59,33,39,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,37,10,50,204,35,25,50,349,38,1*4B

$GBGSV,7,3,26,9,49,341,33,59,49,130,39,1,45,125,36,2,45,237,33,1*4B

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,35,1*7F

$GBGSV,7,5,26,33,35,202,37,4,32,111,32,12,31,76,34,24,26,59,37,1*4A

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,32,38,7,189,31,1*7F

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,40,24,26,59,38,43,26,171,35,5*44

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142855.000,A,2301.2581467,N,11421.9431990,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142855.000,3.417,0.193,0.190,0.266,2.355,2.358,3.238*7D



2025-07-31 22:28:56:399 ==>> $GBGGA,142856.000,2301.2581560,N,11421.9432160,E,1,23,0.57,79.302,M,-1.770,M,,*53

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,38,39,59,33,39,1*4D

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,38,1,45,125,36,2,45,237,33,1*4A

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,36,1*7C

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,31,38,7,189,31,1*7C

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,39,24,26,59,38,43,26,171,35,5*4A

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142856.000,A,2301.2581560,N,11421.9432160,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142856.000,3.531,0.208,0.204,0.287,2.411,2.414,3.257*7A

[D][05:18:39][COMM]read battery soc:255


2025-07-31 22:28:56:753 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:28:56:909 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:28:57:462 ==>> $GBGGA,142857.000,2301.2581661,N,11421.9432263,E,1,23,0.57,79.350,M,-1.770,M,,*57

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,37,39,59,33,39,1*42

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,38,1,45,125,36,2,45,237,33,1*4A

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,36,1*7C

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,35,1*7A

$GBGSV,7,7,26,32,9,318,31,38,7,189,31,1*7C

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*4B

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142857.000,A,2301.2581661,N,11421.9432263,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,142857.000,3.628,0.217,0.212,0.299,2.458,2.460,3.273*7E



2025-07-31 22:28:58:404 ==>> $GBGGA,142858.000,2301.2581766,N,11421.9432351,E,1,23,0.57,79.359,M,-1.770,M,,*57

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,37,39,59,33,39,1*42

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,39,1,45,125,36,2,45,237,33,1*4B

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,36,1*7C

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,31,44,17,54,34,1*7B

$GBGSV,7,7,26,32,9,318,31,38,7,189,31,1*7C

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*4B

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142858.000,A,2301.2581766,N,11421.9432351,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,142858.000,3.604,0.229,0.224,0.315,2.442,2.445,3.237*7E

[D][05:18:41][COMM]read battery soc:255


2025-07-31 22:28:58:449 ==>>                                                                                   

2025-07-31 22:28:58:599 ==>> [D][05:18:41][COMM]Main Task receive event:142
[D][05:18:41][COMM]###### 52773 imu self test OK ######
[D][05:18:41][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,-8,4049]
[D][05:18:41][COMM]Main Task receive event:142 finished processing


2025-07-31 22:28:58:862 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:28:58:870 ==>> 检测【打印IMU STATE2】
2025-07-31 22:28:58:892 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:28:59:104 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:42][COMM]YAW data: 32763[32763]
[D][05:18:42][COMM]pitch:-66 roll:0
[D][05:18:42][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:28:59:137 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:28:59:143 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:28:59:148 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:28:59:209 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:28:59:417 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:28:59:424 ==>> 检测【检测VBUS电压2】
2025-07-31 22:28:59:438 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:28:59:444 ==>> $GBGGA,142859.000,2301.2581859,N,11421.9432390,E,1,23,0.57,79.415,M,-1.770,M,,*57

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,37,39,59,33,38,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,39,1,45,125,36,2,45,237,33,1*4B

$GBGSV,7,4,26,34,43,110,39,41,42,270,39,60,42,239,39,11,35,141,36,1*72

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,32,44,17,54,35,1*79

$GBGSV,7,7,26,32,9,318,31,38,7,189,30,1*7D

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,41,34,43,110,40,5*47

$GBGSV,3,2,12,41,42,270,43,33,35,202,39,24,26,59,38,43,26,171,35,5*4A

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142859.000,A,2301.2581859,N,11421.9432390,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142859.000,3.531,0.184,0.181,0.253,2.401,2.403,3.179*7D

[D][05:18:42][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:42][FCTY]get

2025-07-31 22:28:59:479 ==>> _ext_48v_vol retry i = 1,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:42][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:28:59:768 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:42][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:42][FCTY]==========Modules-nRF5340 ==========
[D][05:18:42][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:42][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:42][FCTY]DeviceID    = 460130071541244
[D][05:18:42][FCTY]HardwareID  = 867222087842565
[D][05:18:42][FCTY]MoBikeID    = 9999999999
[D][05:18:42][FCTY]LockID      = FFFFFFFFFF
[D][05:18:42][FCTY]BLEFWVersion= 105
[D][05:18:42][FCTY]BLEMacAddr   = E9E423913CD5
[D][05:18:42][FCTY]Bat         = 3944 mv
[D][05:18:42][FCTY]Current     = 100 ma
[D][05:18:42][FCTY]VBUS        = 11700 mv
[D][05:18:42][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:42][FCTY]Ext battery vol = 8, adc = 320
[D][05:18:42][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 177 mv
[D][05:18:42][FCTY]Bike Type flag is invalied
[D][05:18:42][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:42][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:42][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:42][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:42][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:42]

2025-07-31 22:28:59:813 ==>> [FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:42][FCTY]Bat1         = 3775 mv
[D][05:18:42][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:42][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:28:59:963 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:29:00:458 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:43][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:43][FCTY]==========Modules-nRF5340 ==========
[D][05:18:43][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:43][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:43][FCTY]DeviceID    = 460130071541244
[D][05:18:43][FCTY]HardwareID  = 867222087842565
[D][05:18:43][FCTY]MoBikeID    = 9999999999
[D][05:18:43][FCTY]LockID      = FFFFFFFFFF
[D][05:18:43][FCTY]BLEFWVersion= 105
[D][05:18:43][FCTY]BLEMacAddr   = E9E423913CD5
[D][05:18:43][FCTY]Bat         = 3944 mv
[D][05:18:43][FCTY]Current     = 100 ma
[D][05:18:43][FCTY]VBUS        = 11700 mv
$GBGGA,142900.000,2301.2581944,N,11421.9432470,E,1,23,0.57,79.482,M,-1.770,M,,*50

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,37,39,59,33,38,1*43

$GBGSV,7,2,26,6,57,4,35,16,57,8,37,10,50,204,35,25,50,349,38,1*4B

$GBGSV,7,3,26,9,49,341,33,59,49,130,39,1,45,125,36,2,45,237,3

2025-07-31 22:29:00:563 ==>> 3,1*4B

$GBGSV,7,4,26,34,43,110,39,41,42,270,39,60,42,239,39,11,35,141,36,1*72

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,35,5,21,256,32,44,17,54,34,1*78

$GBGSV,7,7,26,32,9,318,31,38,7,189,30,1*7D

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,40,34,43,110,40,5*46

$GBGSV,3,2,12,41,42,270,43,33,35,202,39,24,26,59,38,43,26,171,35,5*4A

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142900.000,A,2301.2581944,N,11421.9432470,E,0.003,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,142900.000,3.424,0.225,0.220,0.309,2.343,2.345,3.107*76

[D][05:18:43][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:43][FCTY]Ext battery vol = 4, adc = 176
[D][05:18:43][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 151 mv
[D][05:18:43][FCTY]Bike Type flag is invalied
[D][05:18:43][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:43][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:43][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:43][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:43][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:43][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:43][FCTY]Bat1 

2025-07-31 22:29:00:592 ==>>         = 3775 mv
[D][05:18:43][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:43][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:29:00:768 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:29:01:163 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:44][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:44][FCTY]==========Modules-nRF5340 ==========
[D][05:18:44][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:44][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:44][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:44][FCTY]DeviceID    = 460130071541244
[D][05:18:44][FCTY]HardwareID  = 867222087842565
[D][05:18:44][FCTY]MoBikeID    = 9999999999
[D][05:18:44][FCTY]LockID      = FFFFFFFFFF
[D][05:18:44][FCTY]BLEFWVersion= 105
[D][05:18:44][FCTY]BLEMacAddr   = E9E423913CD5
[D][05:18:44][FCTY]Bat         = 3944 mv
[D][05:18:44][FCTY]Current     = 100 ma
[D][05:18:44][FCTY]VBUS        = 5000 mv
[D][05:18:44][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:44][FCTY]Ext battery vol = 3, adc = 138
[D][05:18:44][FCTY]Acckey1 vol = 5514 mv, Acckey2 vol = 50 mv
[D][05:18:44][FCTY]Bike Type flag is invalied
[D][05:18:44][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:44][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:44][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:44][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:44][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:44][FCTY]CAT1_GNSS_VERSION = 

2025-07-31 22:29:01:208 ==>> V3465b5b1
[D][05:18:44][FCTY]Bat1         = 3775 mv
[D][05:18:44][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:44][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:29:01:351 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:29:01:357 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:29:01:361 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:29:01:406 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:29:01:662 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:29:01:683 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:29:01:692 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:29:01:700 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:29:01:784 ==>>                                                    ========================================================
[D][05:18:44][CAT1]gsm read msg sub id: 24
[D][05:18:44][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]Sending traceid[9999999999900006]
[D][05:18:44][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:44][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:44][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:44][PROT]index:0 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:5
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:3
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900006]
[D][05:18:

2025-07-31 22:29:01:889 ==>> 44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:44][PROT]add success [1629955124],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:44][COMM]Main Task receive event:60 finished processing
$GBGGA,142901.000,2301.2581841,N,11421.9432557,E,1,23,0.57,79.514,M,-1.770,M,,*5F

$GBGSA,A,3,40,03,07,39,06,16,10,25,09,59,01,34,1.10,0.57,0.94,4*09

$GBGSA,A,3,41,60,11,33,12,24,43,23,44,32,38,,1.10,0.57,0.94,4*0D

$GBGSV,7,1,26,40,63,169,39,3,61,190,39,7,61,195,37,39,59,33,39,1*42

$GBGSV,7,2,26,6,57,4,35,16,57,8,36,10,50,204,35,25,50,349,38,1*4A

$GBGSV,7,3,26,9,49,341,33,59,49,130,38,1,45,125,36,2,45,237,33,1*4A

$GBGSV,7,4,26,34,43,110,39,41,42,270,40,60,42,239,39,11,35,141,36,1*7C

$GBGSV,7,5,26,33,35,202,37,4,32,111,31,12,31,76,34,24,26,59,37,1*49

$GBGSV,7,6,26,43,26,171,35,23,22,293,

2025-07-31 22:29:01:995 ==>> 34,5,21,256,31,44,17,54,34,1*7A

$GBGSV,7,7,26,32,9,318,31,38,7,189,30,1*7D

$GBGSV,3,1,12,40,63,169,41,39,59,33,41,25,50,349,41,34,43,110,40,5*47

$GBGSV,3,2,12,41,42,270,42,33,35,202,39,24,26,59,38,43,26,171,35,5*4B

$GBGSV,3,3,12,23,22,293,38,44,17,54,34,32,9,318,29,38,7,189,27,5*43

$GBRMC,142901.000,A,2301.2581841,N,11421.9432557,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,142901.000,3.445,0.202,0.198,0.277,2.352,2.353,3.099*7C

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]exec over: func id: 24, ret: 6
[D][05:18:44][CAT1]sub id: 24, ret: 6

[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B315B3BEE7D117D637219C3C18DE89AB22C9AA66B4E3FB9FD2ADA573C24A662F37FED56E0A069106E908A9C092AD01AF221A7C1065BEC98FFC4947A1A5AFDA1E0C5D84360731B91CDD6897C419C3A2D403D361
[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][0

2025-07-31 22:29:02:085 ==>> 5:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]
[D][05:18:44][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:18:44][COMM]read battery soc:255
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
                                                                                                                                           

2025-07-31 22:29:02:193 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:29:02:201 ==>> 检测【打开WIFI(3)】
2025-07-31 22:29:02:222 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:29:02:421 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:45][CAT1]gsm read msg sub id: 12
[D][05:18:45][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:45][CAT1]<<< 
OK

[D][05:18:45][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:29:02:484 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:29:02:489 ==>> 检测【扩展芯片hw】
2025-07-31 22:29:02:498 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:29:02:697 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:45][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:29:02:761 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:29:02:768 ==>> 检测【扩展芯片boot】
2025-07-31 22:29:02:786 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:29:02:795 ==>> 检测【扩展芯片sw】
2025-07-31 22:29:02:804 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:29:02:813 ==>> 检测【检测音频FLASH】
2025-07-31 22:29:02:838 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:29:03:065 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:46][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:46][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:46][COMM]----- get Acckey 1 and value:1------------
[D][05:18:46][COMM]----- get Acckey 2 and value:0------------
[D][05:18:46][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:29:03:806 ==>>                                                                                                                                      18:46][COMM]----- get Acckey 2 and value:1------------
[D][05:18:46][COMM]more than the number of battery plugs
[D][05:18:46][COMM]VBUS is 1
[D][05:18:46][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:46][COMM]file:B50 exist
[D][05:18:46][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:46][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:46][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:46][COMM]Bat auth off fail, error:-1
[D][05:18:46][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:46][COMM]----- get Acckey 1 and value:1------------
[D][05:18:46][COMM]----- get Acckey 2 and value:1------------
[D][05:18:46][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:46][COMM]----- get Acckey 1 and value:1------------
[D][05:18:46][COMM]----- get Acckey 2 and value:1------------
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:46][COMM]file:B50 exist
[D][05:18:46][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'


2025-07-31 22:29:03:911 ==>> [D][05:18:46][COMM]read file, len:10800, num:3
[D][05:18:46][COMM]--->crc16:0xb8a
[D][05:18:46][COMM]read file success
[W][05:18:46][COMM][Audio].l:[936].close hexlog save
[D][05:18:46][COMM]accel parse set 1
[D][05:18:46][COMM][Audio]mon:9,05:18:46
[D][05:18:46][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]Main Task receive event:65
[D][05:18:46][COMM]main task tmp_sleep_event = 80
[D][05:18:46][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:46][COMM]Main Task receive event:65 finished processing
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:46][COMM]f:[ec800m_audio_

2025-07-31 22:29:04:016 ==>> play_process].l:[956].start ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:46][COMM]Main Task receive event:66
[D][05:18:46][COMM]Try to Auto Lock Bat
[D][05:18:46][COMM]Main Task receive event:66 finished processing
[D][05:18:46][COMM]Main Task receive event:60
[D][05:18:46][COMM]smart_helmet_vol=255,255
[D][05:18:46][COMM]BAT CAN get state1 Fail 204
[D][05:18:46][COMM]BAT CAN get soc Fail, 204
[D][05:18:46][COMM]get soc error
[E][05:18:46][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:46][COMM]report elecbike
[W][05:18:46][PROT]remove success[1629955126],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:46][PROT]add success [1629955126],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:46][COMM]Main Task receive event:60 finished processing
[D][05:18:46][COMM]Main Task receive event:61
[D][05:18:46][COMM][D301]:type:3, trace id:280
[D][05:18:46][COMM]Receive Bat Lock cmd 0
[D][05:18:46][COMM]VBUS is 1
[D][05:18:46][COMM]id[], hw[000
[D][05:18:46][COMM]get mcMaincircuitVolt error
[D][05:18:46][COMM]get mcSubcircuitVolt error
[D][05:18:46][COMM]33v/48

2025-07-31 22:29:04:121 ==>> v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:46][COMM]BAT CAN get state1 Fail 204
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:46][PROT]index:1
[D][05:18:46][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:46][PROT]is_send:1
[D][05:18:46][PROT]sequence_num:6
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:3
[D][05:18:46][PROT]send_path:0x3
[D][05:18:46][PROT]msg_type:0x5d03
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]Sending traceid[9999999999900007]
[D][05:18:46][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:46][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:46][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:46][COMM]BAT CAN get soc Fail, 204
[D][05:18:46][COMM]get bat work state err
[D][05:18:

2025-07-31 22:29:04:226 ==>> 46][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:18:46][PROT]remove success[1629955126],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:46][PROT]add success [1629955126],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:46][COMM]Main Task receive event:61 finished processing
[D][05:18:46][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:46][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len

2025-07-31 22:29:04:316 ==>> :2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:46][COMM]read battery soc:255
+WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,F42A7D1297A3,-70
+WIFISCAN:4,2,74C330CCAB10,-72
+WIFISCAN:4,3,CC057790A5C1,-83

[D][05:18:46][CAT1]wifi scan report total[4]
[D][05:18:46][GNSS]recv submsg id[3]


2025-07-31 22:29:05:557 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 22:29:06:172 ==>> [D][05:18:49][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:29:06:447 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:29:06:765 ==>> [D][05:18:49][PROT]CLEAN,SEND:0
[D][05:18:49][PROT]index:1 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:6
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:3
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900007]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BEEC9F795BF726BA924D8A8C471C0D3B6DE447ABDC574608BB7AC3

2025-07-31 22:29:06:840 ==>> 72EEDF52CC613BA6480CBA390BB6F22CE21F8845BBB25A91EF1217D2B7BE1F867C5EC8220D593C2203A5EC9717200B485A843C1046B0226A
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 22:29:07:084 ==>> [D][05:18:50][COMM]crc 108B
[D][05:18:50][COMM]flash test ok


2025-07-31 22:29:07:191 ==>> [D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:50][COMM]61379 imu init OK
[D][05:18

2025-07-31 22:29:07:250 ==>> :50][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:50][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:50][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:50][COMM]accel parse set 0
[D][05:18:50][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:29:07:588 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:29:07:903 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:29:07:928 ==>> 检测【打开喇叭声音】
2025-07-31 22:29:07:937 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:29:08:623 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:51][COMM]file:A20 exist
[D][05:18:51][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:51][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:51][COMM]file:A20 exist
[D][05:18:51][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:51][COMM]read file, len:15228, num:4
[D][05:18:51][COMM]--->crc16:0x419c
[D][05:18:51][COMM]read file success
[W][05:18:51][COMM][Audio].l:[936].close hexlog save
[D][05:18:51][COMM]accel parse set 1
[D][05:18:51][COMM][Audio]mon:9,05:18:51
[D][05:18:51][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:51][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:51][COMM]62390 imu init OK
[D][05:18:51][COMM]f:[ec800m_audio_start].

2025-07-31 22:29:08:717 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:29:08:729 ==>> 检测【打开大灯控制】
2025-07-31 22:29:08:747 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:29:08:756 ==>> l:[691].recv ok
[D][05:18:51][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:51][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:51][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18

2025-07-31 22:29:08:834 ==>> :51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:51][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:51][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:51][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:29:08:908 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:29:09:031 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:29:09:037 ==>> 检测【关闭仪表供电3】
2025-07-31 22:29:09:063 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:29:09:185 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:52][COMM]set POWER 0


2025-07-31 22:29:09:321 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:29:09:331 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:29:09:355 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:29:09:462 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:29:09:567 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 22:29:09:604 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:29:09:611 ==>> 检测【读大灯电压】
2025-07-31 22:29:09:631 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:29:09:794 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:52][COMM]arm_hub read adc[5],val[32876]


2025-07-31 22:29:09:887 ==>> 【读大灯电压】通过,【32876mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:29:09:897 ==>> 检测【关闭大灯控制2】
2025-07-31 22:29:09:919 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:29:10:068 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:29:10:172 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:29:10:181 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:29:10:206 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:29:10:389 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:53][COMM]arm_hub read adc[5],val[115]


2025-07-31 22:29:10:452 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:29:10:458 ==>> 检测【打开WIFI(4)】
2025-07-31 22:29:10:481 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:29:10:620 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:29:10:773 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:29:10:780 ==>> 检测【EC800M模组版本】
2025-07-31 22:29:10:803 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:29:10:989 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:29:11:140 ==>> [D][05:18:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:54][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:29:11:333 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:29:11:340 ==>> 检测【配置蓝牙地址】
2025-07-31 22:29:11:358 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:29:11:524 ==>> [W][05:18:54][COMM]>>>>>Input command = nRFReset<<<<<
+WIFISCAN:4,0,F42A7D1297A3,-72
+WIFISCAN:4,1,CC057790A5C1,-79
+WIFISCAN:4,2,CC057790A5C0,-81
+WIFISCAN:4,3,CC057790A4A1,-82

[D][05:18:54][CAT1]wifi scan report total[4]


2025-07-31 22:29:11:543 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E9E423913CD5>】
2025-07-31 22:29:11:599 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 22:29:11:704 ==>> recv ble 1
recv ble 2
ble set mac ok :e9,e4,23,91,3c,d5
enable filters ret : 0

2025-07-31 22:29:11:841 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:29:11:849 ==>> 检测【BLETEST】
2025-07-31 22:29:11:875 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:29:11:991 ==>> [D][05:18:54][PROT]CLEAN,SEND:1
[D][05:18:54][PROT]index:1 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:6
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:2
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900007]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:54][GNSS]recv submsg id[3]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][CAT1]Send Data To Server[198][198] ... ->:
0063B981113311331133113311331B88BEF3373031CE53C1FB2B49D73C0B2A22CE69FE10C659F4E2A16C8F8CBDD80E4FFC35C33E588C9

2025-07-31 22:29:12:066 ==>> 4CC7E94F14D3904B2BFABCE9147D4D5AD34016C82267328BD5BE15CB467CE34CB3C01DD94199236335DA7BC16
[D][05:18:55][CAT1]<<< 
SEND OK

[D][05:18:55][CAT1]exec over: func id: 15, ret: 11
[D][05:18:55][CAT1]sub id: 15, ret: 11

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:55][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:55][M2M ]g_m2m_is_idle become true
[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:55][PROT]M2M Send ok [1629955135]
4A A4 01 A4 4A 


2025-07-31 22:29:12:126 ==>>                                                                                                   recv ble 1
recv ble 2
<BSJ*MAC:E9E423913CD5*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E9E423913CD599999

2025-07-31 22:29:12:201 ==>> OVER 150


2025-07-31 22:29:12:898 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:29:12:905 ==>> 该项需要延时执行
2025-07-31 22:29:13:125 ==>> [D][05:18:56][COMM]67317 imu init OK
[D][05:18:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:29:13:741 ==>> [D][05:18:56][COMM]read battery soc:255
[D][05:18:56][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:56][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:56][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:56][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:56][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:56][COMM]accel parse set 0
[D][05:18:56][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:29:14:123 ==>> [D][05:18:57][COMM]68330 imu init OK


2025-07-31 22:29:14:787 ==>> [D][05:18:57][COMM]S->M yaw:INVALID


2025-07-31 22:29:15:609 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 22:29:15:821 ==>> [D][05:18:59][COMM]M->S yaw:INVALID


2025-07-31 22:29:17:231 ==>> [D][05:19:00][PROT]CLEAN,SEND:1
[D][05:19:00][PROT]index:1 1629955140
[D][05:19:00][PROT]is_send:0
[D][05:19:00][PROT]sequence_num:6
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:1
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:00][PROT]===========================================================
[W][05:19:00][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][PROT]sending traceid [9999999999900007]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[198]
[D][05:19:00][SAL ]Cellular task submsg id[10]
[D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:00][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88BEFB9877ADF7BF5371274DFDBCFC4A21704D346952A7302B15750A260B66D3D23A39B2C8834DA87F40A4484611B4833D00A75A94E1EE938

2025-07-31 22:29:17:291 ==>> F7E483B9242E21151231CA66E0D5DD65E0B00D07B8A330EDFF7DBD1
[D][05:19:00][CAT1]<<< 
SEND OK

[D][05:19:00][CAT1]exec over: func id: 15, ret: 11
[D][05:19:00][CAT1]sub id: 15, ret: 11

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:00][M2M ]g_m2m_is_idle become true
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:00][PROT]M2M Send ok [1629955140]


2025-07-31 22:29:17:628 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:29:19:618 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:29:21:620 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:29:22:459 ==>> [D][05:19:05][PROT]CLEAN,SEND:1
[D][05:19:05][PROT]CLEAN:1
[D][05:19:05][PROT]index:0 1629955145
[D][05:19:05][PROT]is_send:0
[D][05:19:05][PROT]sequence_num:5
[D][05:19:05][PROT]retry_timeout:0
[D][05:19:05][PROT]retry_times:2
[D][05:19:05][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:05][PROT]===========================================================
[D][05:19:05][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:05][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][PROT]sending traceid [9999999999900006]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[198]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][05:19:05][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05]

2025-07-31 22:29:22:549 ==>> [CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:05][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B357D933428F1FBE00B3862C92796366C2DB2C0D158C09ABC6072B2BE1C794D469660CDDAD11DBDB18DDB249D44130D5B12BBCB351771A2995A53291CC581148727B9773DEB64297618327840E6D3A30F2F1DA
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:05][PROT]M2M Send ok [1629955145]


2025-07-31 22:29:22:903 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:29:22:910 ==>> 检测【检测WiFi结果】
2025-07-31 22:29:22:917 ==>> WiFi信号:【CC057790A620】,信号值:-61
2025-07-31 22:29:22:922 ==>> WiFi信号:【CC057790A621】,信号值:-61
2025-07-31 22:29:22:935 ==>> WiFi信号:【CC057790A5C0】,信号值:-82
2025-07-31 22:29:22:951 ==>> WiFi信号:【CC057790A5C1】,信号值:-83
2025-07-31 22:29:22:960 ==>> WiFi信号:【F42A7D1297A3】,信号值:-70
2025-07-31 22:29:22:985 ==>> WiFi信号:【74C330CCAB10】,信号值:-72
2025-07-31 22:29:22:998 ==>> WiFi信号:【CC057790A4A1】,信号值:-82
2025-07-31 22:29:23:008 ==>> WiFi数量【7】, 最大信号值:-61
2025-07-31 22:29:23:033 ==>> 检测【检测GPS结果】
2025-07-31 22:29:23:040 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 22:29:23:050 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【37】
北斗星号:【3】,信号值:【39】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【35】
北斗星号:【16】,信号值:【36】
北斗星号:【59】,信号值:【39】
北斗星号:【10】,信号值:【35】
北斗星号:【25】,信号值:【40】
北斗星号:【1】,信号值:【36】
北斗星号:【34】,信号值:【39】
北斗星号:【41】,信号值:【42】
北斗星号:【60】,信号值:【39】
北斗星号:【33】,信号值:【39】
北斗星号:【11】,信号值:【36】
北斗星号:【24】,信号值:【38】
北斗星号:【43】,信号值:【35】
北斗星号:【23】,信号值:【38】

2025-07-31 22:29:23:074 ==>> 检测【CSQ强度】
2025-07-31 22:29:23:086 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:29:23:119 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:06][COMM]IMU: [0,5,-977] ret=22 AWAKE!
[D][05:19:06][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:29:23:300 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 22:29:23:307 ==>> 检测【关闭GSM联网】
2025-07-31 22:29:23:333 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:29:23:486 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:06][COMM]GSM test
[D][05:19:06][COMM]GSM test disable


2025-07-31 22:29:23:621 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:29:23:632 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:29:23:638 ==>> 检测【4G联网测试】
2025-07-31 22:29:23:651 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:29:24:581 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:07][COMM]Main Task receive event:14
[D][05:19:07][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955147, allstateRepSeconds = 0
[D][05:19:07][COMM]index:0,power_mode:0xFF
[D][05:19:07][COMM]index:1,sound_mode:0xFF
[D][05:19:07][COMM]index:2,gsensor_mode:0xFF
[D][05:19:07][COMM]index:3,report_freq_mode:0xFF
[D][05:19:07][COMM]index:4,report_period:0xFF
[D][05:19:07][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:07][COMM]index:6,normal_reset_period:0xFF
[D][05:19:07][COMM]index:7,spock_over_speed:0xFF
[D][05:19:07][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:07][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:07][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:07][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:07][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:07][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:07][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:07][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:07][COMM]index:16,imu_config_params:0xFF
[D][05:19:07][COMM]index:17,long_connect_params:0xFF
[D][05:19:07][COMM]index:18,detain_mark:0xFF
[D][05:19:07][COMM]

2025-07-31 22:29:24:686 ==>> index:19,lock_pos_report_count:0xFF
[D][05:19:07][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:07][COMM]index:21,mc_mode:0xFF
[D][05:19:07][COMM]index:22,S_mode:0xFF
[D][05:19:07][COMM]index:23,overweight:0xFF
[D][05:19:07][COMM]index:24,standstill_mode:0xFF
[D][05:19:07][COMM]index:25,night_mode:0xFF
[D][05:19:07][COMM]index:26,experiment1:0xFF
[D][05:19:07][COMM]index:27,experiment2:0xFF
[D][05:19:07][COMM]index:28,experiment3:0xFF
[D][05:19:07][COMM]index:29,experiment4:0xFF
[D][05:19:07][COMM]index:30,night_mode_start:0xFF
[D][05:19:07][COMM]index:31,night_mode_end:0xFF
[D][05:19:07][COMM]index:33,park_report_minutes:0xFF
[D][05:19:07][COMM]index:34,park_report_mode:0xFF
[D][05:19:07][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:07][COMM]index:38,charge_battery_para: FF
[D][05:19:07][COMM]index:39,multirider_mode:0xFF
[D][05:19:07][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:07][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:07][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:07][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:07][COMM]index:44,riding_duration_config:0xFF
[D][05:19:07][COMM]index:45,camera_park_angle_cfg:0x

2025-07-31 22:29:24:791 ==>> FF
[D][05:19:07][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:07][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:07][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:07][COMM]index:49,mc_load_startup:0xFF
[D][05:19:07][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:07][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:07][COMM]index:52,traffic_mode:0xFF
[D][05:19:07][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:07][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:07][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:07][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:07][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:07][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:07][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:07][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:07][COMM]index:63,experiment5:0xFF
[D][05:19:07][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:07][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:07][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:07][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:07][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:07][COMM]index:70,camera_park_light_c

2025-07-31 22:29:24:896 ==>> fg:0xFF
[D][05:19:07][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:07][COMM]index:72,experiment6:0xFF
[D][05:19:07][COMM]index:73,experiment7:0xFF
[D][05:19:07][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:07][COMM]index:75,zero_value_from_server:-1
[D][05:19:07][COMM]index:76,multirider_threshold:255
[D][05:19:07][COMM]index:77,experiment8:255
[D][05:19:07][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:07][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:07][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:07][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:07][COMM]index:83,loc_report_interval:255
[D][05:19:07][COMM]index:84,multirider_threshold_p2:255
[D][05:19:07][COMM]index:85,multirider_strategy:255
[D][05:19:07][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:07][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:07][COMM]index:90,weight_param:0xFF
[D][05:19:07][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:07][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:07][COMM]index:95,current_limit:0xFF
[D][05:19:07][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 

2025-07-31 22:29:25:001 ==>> 
[D][05:19:07][COMM]index:100,location_mode:0xFF

[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:07][PROT]index:0 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:8
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:1
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][PROT]sending traceid [9999999999900009]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[294]
[D][05:19:07][CAT1]gsm read msg sub id: 13
[D][05:19:07][SAL ]Ce

2025-07-31 22:29:25:106 ==>> llular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:07][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:07][CAT1]exec over: func id: 13, ret: 21
[D][05:19:07][M2M ]get csq[25]
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:07][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B1519D5F2371295E339A1FA0F475CFEB4F59F2223D68FEA1C5261E32544BE74C51D3E2B558031FF0BB62013DA5D540496EE242755A7F1CB5DA65FB0FC9F348F550F16EF773999585343E2A6410620E6F8B974AE304551FBA46CC905E006B67E3C3233221B508B511EE61D93B95D4B55A8C9C4AE406A40D9AD5EEAB889703F835B5FEA4
[D][05:19:07][CAT1]<<< 
SEND OK

[D][05:19:07][CAT1]exec over: func id: 15, ret: 11
[D][05:19:07][CAT1]sub id: 15, ret: 11

[D][05:19:07][SAL ]Cellular task submsg id[68]
[D][05:19:07][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:07][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:07][M2M ]g_m2m_is_idle become true
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:

2025-07-31 22:29:25:196 ==>> 19:07][PROT]M2M Send ok [1629955147]
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:07][COMM]------>period, report file manifest
[D][05:19:07][COMM]Main Task receive event:14 finished processing
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:07][CAT1]gsm read msg sub id: 21
[D][05:19:07][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]cell info report total[0]
[D][05:19:07][CAT1]exec over: func id: 21, ret: 6


2025-07-31 22:29:25:657 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 22:29:25:668 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:29:25:691 ==>> 检测【关闭GPS】
2025-07-31 22:29:25:702 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:29:25:932 ==>> [D][05:19:09][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:09][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:09][GNSS]stop locating
[D][05:19:09][GNSS]all continue location stop
[W][05:19:09][GNSS]stop locating
[D][05:19:09][GNSS]all sing location stop


2025-07-31 22:29:26:215 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:29:26:222 ==>> 检测【清空消息队列2】
2025-07-31 22:29:26:243 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:29:26:393 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:09][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:29:26:488 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:29:26:499 ==>> 检测【轮动检测】
2025-07-31 22:29:26:518 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:29:26:606 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:29:26:681 ==>> [D][05:19:09][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 22:29:27:003 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:29:27:096 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:29:27:281 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:29:27:293 ==>> 检测【关闭小电池】
2025-07-31 22:29:27:309 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:29:27:402 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:29:27:557 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:29:27:565 ==>> 检测【进入休眠模式】
2025-07-31 22:29:27:576 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:29:27:664 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 22:29:27:769 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:29:27:874 ==>> [D][05:19:11][COMM]Main Task receive event:28
[D][05:19:11][COMM]main task tmp_sleep_event = 8
[D][05:19:11][COMM]prepare to sleep
[D][05:19:11][CAT1]gsm read msg sub id: 12
[D][05:19:11][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:29:28:677 ==>> [D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 12, ret: 6
[D][05:19:11][M2M ]tcpclient close[4]
[D][05:19:11][SAL ]Cellular task submsg id[12]
[D][05:19:11][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:11][CAT1]gsm read msg sub id: 9
[D][05:19:11][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 9, ret: 6
[D][05:19:11][CAT1]sub id: 9, ret: 6

[D][05:19:11][SAL ]Cellular task submsg id[68]
[D][05:19:11][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:11][SAL ]socket close ind. id[4]
[D][05:19:11][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:11][COMM]1x1 frm_can_tp_send ok
[D][05:19:11][CAT1]pdpdeact urc len[22]


2025-07-31 22:29:28:968 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:29:29:476 ==>> [E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[E][05:19:12][COMM]1x1 error -3.
[W][05:19:12][COMM]CAN STOP!
[D][05:19:12][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:12][COMM]------------ready to Power off Acckey 1------------
[D][05:19:12][COMM]------------ready to Power off Acckey 2------------
[D][05:19:12][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:12][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1304
[D][05:19:12][COMM]bat sleep fail, reason:-1
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:12][COMM]accel parse set 0
[D][05:19:12][COMM]imu rest ok. 83596
[D][05:19:12][COMM]imu sleep 0
[W][05:19:12][COMM]now sleep


2025-07-31 22:29:29:663 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:29:29:671 ==>> 检测【检测33V休眠电流】
2025-07-31 22:29:29:681 ==>> 开始33V电流采样
2025-07-31 22:29:29:711 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:29:767 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:29:30:782 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:29:30:860 ==>> Current33V:????:13.72

2025-07-31 22:29:31:291 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:31:298 ==>> 【检测33V休眠电流】通过,【13.72uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:29:31:305 ==>> 该项需要延时执行
2025-07-31 22:29:33:313 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:29:33:320 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:29:33:331 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:29:33:407 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2744mV
Get AD_V6 2035mV
Get AD_V7 1087mV
OVER 150


2025-07-31 22:29:34:355 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:29:34:363 ==>> 检测【打开小电池2】
2025-07-31 22:29:34:386 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:29:34:495 ==>> 6A A6 01 A6 6A 


2025-07-31 22:29:34:600 ==>> Battery ON
OVER 150


2025-07-31 22:29:34:640 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:29:34:661 ==>> 该项需要延时执行
2025-07-31 22:29:35:156 ==>> 此处延时了:【500】毫秒
2025-07-31 22:29:35:166 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:29:35:191 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:29:35:297 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:29:35:445 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:29:35:465 ==>> 该项需要延时执行
2025-07-31 22:29:35:741 ==>> [D][05:19:18][COMM]------------ready to Power on Acckey 1------------
[D][05:19:18][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:18][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:18][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:18][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:18][COMM]----- get Acckey 1 and value:1------------
[W][05:19:18][COMM]CAN START!
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:18][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 89778
[D][05:19:18][COMM][Audio]exec status ready.
[D][05:19:18][CAT1]<<< 


2025-07-31 22:29:35:801 ==>> OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 6
[D][05:19:18][COMM]imu wakeup ok. 89792
[D][05:19:18][COMM]imu wakeup 1
[W][05:19:18][COMM]wake up system, wakeupEvt=0x80
[D][05:19:18][COMM]frm_can_weigth_power_set 1
[D][05:19:18][COMM]Clear Sleep Block Evt
[D][05:19:18][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:18][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:29:35:876 ==>>                      ULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 22:29:35:951 ==>> 此处延时了:【500】毫秒
2025-07-31 22:29:35:963 ==>> 检测【进入休眠模式2】
2025-07-31 22:29:35:985 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:29:36:165 ==>> [E][05:19:19][COMM]1x1 rx timeout
[D][05:19:19][COMM]1x1 frm_can_tp_send ok
[D][05:19:19][COMM]msg 02A0 loss. last_tick:89763. cur_tick:90272. period:50
[D][05:19:19][COMM]msg 02A4 loss. last_tick:89763. cur_tick:90272. period:50
[D][05:19:19][COMM]msg 02A5 loss. last_tick:89763. cur_tick:90273. period:50
[D][05:19:19][COMM]msg 02A6 loss. last_tick:89763. cur_tick:90273. period:50
[D][05:19:19][COMM]msg 02A7 loss. last_tick:89763. cur_tick:90273. period:50
[D][05:19:19][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 90274
[D][05:19:19][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 90274
[W][05:19:19][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:29:36:487 ==>> [E][05:19:19][COMM]1x1 rx timeout
[E][05:19:19][COMM]1x1 tp timeout
[E][05:19:19][COMM]1x1 error -3.
[D][05:19:19][COMM]Main Task receive event:28 finished processing
[D][05:19:19][COMM]Main Task receive event:28
[D][05:19:19][COMM]prepare to sleep
[D][05:19:19][CAT1]gsm read msg sub id: 12
[D][05:19:19][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 12, ret: 6
[D][05:19:19][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:19][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:29:36:791 ==>> [D][05:19:19][COMM]msg 0220 loss. last_tick:89763. cur_tick:90768. period:100
[D][05:19:19][COMM]msg 0221 loss. last_tick:89763. cur_tick:90768. period:100
[D][05:19:19][COMM]msg 0224 loss. last_tick:89763. cur_tick:90769. period:100
[D][05:19:19][COMM]msg 0260 loss. last_tick:89763. cur_tick:90769. period:100
[D][05:19:19][COMM]msg 0280 loss. last_tick:89763. cur_tick:90769. period:100
[D][05:19:19][COMM]msg 02C0 loss. last_tick:89763. cur_tick:90770. period:100
[D][05:19:19][COMM]msg 02C1 loss. last_tick:89763. cur_tick:90770. period:100
[D][05:19:19][COMM]msg 02C2 loss. last_tick:89763. cur_tick:90771. period:100
[D][05:19:19][COMM]msg 02E0 loss. last_tick:89763. cur_tick:90771. period:100
[D][05:19:19][COMM]msg 02E1 loss. last_tick:89763. cur_tick:90771. period:100
[D][05:19:19][COMM]msg 02E2 loss. last_tick:89763. cur_tick:90772. period:100
[D][05:19:19][COMM]msg 0300 loss. last_tick:89763. cur_tick:90772. period:100
[D][05:19:19][COMM]msg 0301 loss. last_tick:89763. cur_tick:90772. period:100
[D][05:19:19][COMM]bat msg 0240 loss. last_tick:89763. cur_tick:90773. period:100. j,i:1 54
[D][05:19:19][COMM]bat msg 0241 loss. last_tick:89763. cur_tick:90773. period:100. j,i:2 55
[D][05:19:19][COMM]bat msg 0242 loss. last_tick:89763. cur_tick:90774. period:100. j,i:3 56
[D][05:19:19

2025-07-31 22:29:36:881 ==>> ][COMM]bat msg 0244 loss. last_tick:89763. cur_tick:90774. period:100. j,i:5 58
[D][05:19:19][COMM]bat msg 024E loss. last_tick:89763. cur_tick:90774. period:100. j,i:15 68
[D][05:19:19][COMM]bat msg 024F loss. last_tick:89763. cur_tick:90775. period:100. j,i:16 69
[D][05:19:19][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 90776
[D][05:19:19][COMM]CAN message bat fault change: 0x00000000->0x0001802E 90776
[D][05:19:19][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 90776
                                                                              

2025-07-31 22:29:37:079 ==>> [D][05:19:20][COMM]msg 0222 loss. last_tick:89763. cur_tick:91271. period:150
[D][05:19:20][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 91272


2025-07-31 22:29:37:154 ==>>                                                  ERON  ,vbuswake : 1
[D][05:19:20][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:20][COMM]------------ready to Power off Acckey 2------------


2025-07-31 22:29:37:364 ==>> [E][05:19:20][COMM]1x1 rx timeout
[D][05:19:20][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[E][05:19:20][COMM]1x1 tp timeout
[E][05:19:20][COMM]1x1 error -3.
[W][05:19:20][COMM]CAN STOP!
[D][05:19:20][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:20][COMM]------------ready to Power off Acckey 1------------
[D][05:19:20][COMM]------------ready to Power off Acckey 2------------
[D][05:19:20][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:20][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 106
[D][05:19:20][COMM]bat sleep fail, reason:-1
[D][05:19:20][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:20][COMM]accel parse set 0
[D][05:19:20][COMM]imu rest ok. 91463
[D][05:19:20][COMM]imu sleep 0
[W][05:19:20][COMM]now sleep


2025-07-31 22:29:37:536 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:29:37:563 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:29:37:582 ==>> 开始小电池电流采样
2025-07-31 22:29:37:596 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:37:637 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:29:38:652 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:29:38:682 ==>> CurrentBattery:ƽ��:66.45

2025-07-31 22:29:39:157 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:39:166 ==>> 【检测小电池休眠电流】通过,【66.45uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:29:39:175 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:29:39:203 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:29:39:307 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:29:39:447 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:29:39:456 ==>> 该项需要延时执行
2025-07-31 22:29:39:547 ==>> [D][05:19:22][COMM]------------ready to Power on Acckey 1------------
[D][05:19:22][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:22][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:22][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:22][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:22][COMM]----- get Acckey 1 and value:1------------
[W][05:19:22][COMM]CAN START!
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:22][COMM]CAN message bat fault change: 0x0001802E->0x00000000 93598
[D][05:19:22][COMM][Audio]exec status ready.
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 12, ret: 6
[D][05:19:22][COMM]imu wakeup ok. 93613
[D][05:19:22][COMM]imu wakeup 1
[W][05:19:22][COMM]wake up system, wakeupEvt=0x80
[D][05:19:22][COMM]frm_can_weigth_power_set 1
[D][05:19:22][COMM]Clear Sleep Block Evt
[D][05:19:22][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:22][COMM]1x1 frm_can_tp_send ok
[D][05:19:22][COMM]read battery soc:0
                                                   

2025-07-31 22:29:39:804 ==>> [E][05:19:23][COMM]1x1 rx timeout
[D][05:19:23][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:29:39:909 ==>> [D][05:19:23][COMM]msg 02A0 loss. last_tick:93580. cur_tick:94092. period:50
[D][05:19:23][COMM]msg 02A4 loss. last_tick:93580. cur_tic

2025-07-31 22:29:39:954 ==>> 此处延时了:【500】毫秒
2025-07-31 22:29:39:966 ==>> 检测【检测唤醒】
2025-07-31 22:29:39:990 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:29:40:003 ==>> k:94093. period:50
[D][05:19:23][COMM]msg 02A5 loss. last_tick:93580. cur_tick:94093. period:50
[D][05:19:23][COMM]msg 02A6 loss. last_tick:93580. cur_tick:94093. period:50
[D][05:19:23][COMM]msg 02A7 loss. last_tick:93580. cur_tick:94094. period:50
[D][05:19:23][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 94094
[D][05:19:23][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 94095


2025-07-31 22:29:40:024 ==>>                                      

2025-07-31 22:29:40:669 ==>> [W][05:19:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:23][FCTY]==========Modules-nRF5340 ==========
[D][05:19:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:23][FCTY]DeviceID    = 460130071541244
[D][05:19:23][FCTY]HardwareID  = 867222087842565
[D][05:19:23][FCTY]MoBikeID    = 9999999999
[D][05:19:23][FCTY]LockID      = FFFFFFFFFF
[D][05:19:23][FCTY]BLEFWVersion= 105
[D][05:19:23][FCTY]BLEMacAddr   = E9E423913CD5
[D][05:19:23][FCTY]Bat         = 3324 mv
[D][05:19:23][FCTY]Current     = 0 ma
[D][05:19:23][FCTY]VBUS        = 2600 mv
[D][05:19:23][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:23][FCTY]Ext battery vol = 32, adc = 1304
[D][05:19:23][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 75 mv
[D][05:19:23][FCTY]Bike Type flag is invalied
[D][05:19:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:23][FCTY]Bat1         = 3775 mv

2025-07-31 22:29:40:758 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:29:40:767 ==>> 检测【关机】
2025-07-31 22:29:40:781 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:29:40:800 ==>> 
[D][05:19:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:23][FCTY]==========Modules-nRF5340 ==========
[E][05:19:23][COMM]1x1 rx timeout
[E][05:19:23][COMM]1x1 tp timeout
[E][05:19:23][COMM]1x1 error -3.
[D][05:19:23][COMM]Main Task receive event:28 finished processing
[D][05:19:23][COMM]Main Task receive event:65
[D][05:19:23][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:23][COMM]Main Task receive event:65 finished processing
[D][05:19:23][COMM]Main Task receive event:60
[D][05:19:23][COMM]smart_helmet_vol=255,255
[D][05:19:23][COMM]report elecbike
[W][05:19:23][PROT]remove success[1629955163],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:23][COMM]Main Task receive event:60 finished processing
[D][05:19:23][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:23][PROT]index:0
[D][05:19:23][PROT]is_send:1
[D][05:19:23][PROT]sequence_num:10
[D][05:19:23][PROT]retry_timeout:0
[D][05:19:23][PROT]retry_times:3
[D][05:19:23][PROT]send_path:0x3
[D][05:19:23][PROT]msg_type:0x5d03
[D][05:19:23][PROT]============

2025-07-31 22:29:40:879 ==>> ===============================================
[W][05:19:23][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955163]
[D][05:19:23][PROT]===========================================================
[D][05:19:23][PROT]Sending traceid[999999999990000B]
[D][05:19:23][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:23][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:23][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:23][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:23][SAL ]open socket ind id[4], rst[0]
[D][05:19:23][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:23][SAL ]Cellular task submsg id[8]
[D][05:19:23][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:23][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:23][CAT1]gsm read msg sub id: 8
[D][05:19:23][CAT1]tx ret[11] >>> AT+CGATT?

[D

2025-07-31 22:29:40:985 ==>> ][05:19:23][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:23][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:23][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:23][CAT1]<<< 
+CME ERROR: 100

[D][05:19:23][COMM]msg 0220 loss. last_tick:93580. cur_tick:94588. period:100
[D][05:19:23][COMM]msg 0221 loss. last_tick:93580. cur_tick:94589. period:100
[D][05:19:23][COMM]msg 0224 loss. last_tick:93580. cur_tick:94589. period:100
[D][05:19:23][COMM]msg 0260 loss. last_tick:93580. cur_tick:94589. period:100
[D][05:19:23][COMM]msg 0280 loss. last_tick:93580. cur_tick:94590. period:100
[D][05:19:23][COMM]msg 02C0 loss. last_tick:93580. cur_tick:94590. period:100
[D][05:19:23][COMM]msg 02C1 loss. last_tick:93580. cur_tick:94590. period:100
[D][05:19:23][COMM]msg 02C2 loss. last_tick:93580. cur_tick:94591. period:100
[D][05:19:23][COMM]msg 02E0 loss. last_tick:93580. cur_tick:94591. period:100
[D][05:19:23][COMM]msg 02E1 loss. last_tick:93580. cur_tick:94592. period:100
[D][05:19:23][COMM]msg 02E2 loss. last_tick:93580. cur_tick:94592. period:100
[D][05:19:23][COMM]msg 0300 loss. last_tick:93580. cur_tick:94592. period:100
[D][05:19:23][COMM]msg 0301 loss. last_tick:93580. cur_tick:94

2025-07-31 22:29:41:074 ==>> 593. period:100
[D][05:19:23][COMM]bat msg 0240 loss. last_tick:93580. cur_tick:94593. period:100. j,i:1 54
[D][05:19:23][COMM]bat msg 0241 loss. last_tick:93580. cur_tick:94593. period:100. j,i:2 55
[D][05:19:23][COMM]bat msg 0242 loss. last_tick:93580. cur_tick:94594. period:100. j,i:3 56
[D][05:19:23][COMM]bat msg 0244 loss. last_tick:93580. cur_tick:94594. period:100. j,i:5 58
[D][05:19:23][COMM]bat msg 024E loss. last_tick:93580. cur_tick:94594. period:100. j,i:15 68
[D][05:19:23][COMM]bat msg 024F loss. last_tick:93580. cur_tick:94595. period:100. j,i:16 69
[D][05:19:23][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 94595
[D][05:19:23][COMM]CAN message bat fault change: 0x00000000->0x0001802E 94596
[D][05:19:23][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 94596


2025-07-31 22:29:41:787 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:29:41:800 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 22:29:41:892 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         4][COMM]--->crc16:0xb8a
[D][05:19:24][COMM]read file success
[W][05:19:24][COMM][Audio].l:[936].close hexlog save
[D][05:19:24][COMM]accel parse set 1
[D][05:19:24][COMM][Audio]mon:9,05:19:24
[D][05:19:24][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:24][COMM]f:[ec800m_audio_sta

2025-07-31 22:29:41:997 ==>> rt].l:[691].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:24][COMM]Main Task receive event:65
[D][05:19:24][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:24][COMM]Main Task receive event:65 finished processing
[D][05:19:24][COMM]Main Task receive event:66
[D][05:19:24][COMM]Try to Auto Lock Bat
[D][05:19:24][COMM]Main Task receive event:66 finished processing
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:24][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:24][COMM]Main Task receive event:60
[D][05:19:24][COMM]smart_helmet_vol=255,255
[D][05:19:24][COMM]BAT CAN get state1 Fail 204
[D][05:19:24][COMM]BAT CAN get soc Fail, 204
[D][05:19:24][COMM]BAT CAN get state2 fail 204
[D][05:19:24][COMM]get soh error
[E][05:19:24][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:24][COMM]report elecbike
[W][05:19:24][PROT]remove success[1629955164],send_path[3],type[00

2025-07-31 22:29:42:102 ==>> 00],priority[0],index[1],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:24][COMM]Main Task receive event:60 finished processing
[D][05:19:24][COMM]Main Task receive event:61
[D][05:19:24][COMM][D301]:type:3, trace id:280
[D][05:19:24][COMM]id[], hw[000
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:24][COMM]get mcMaincircuitVolt error
[D][05:19:24][COMM]get mcSubcircuitVolt error
[D][05:19:24][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:24][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][PROT]index:1
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:24][PROT]is_send:1
[D][05:19:24][PROT]sequence_num:11
[D][05:19:24][PROT]retry_timeout:0
[D][05:19:24][PROT]retry_times:3
[D][05:19:24][PROT]send_path:0x3
[D][05:19:24][PROT]msg_type:0x5d03
[D][05:1

2025-07-31 22:29:42:207 ==>> 9:24][PROT]===========================================================
[W][05:19:24][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955164]
[D][05:19:24][PROT]===========================================================
[D][05:19:24][PROT]Sending traceid[999999999990000C]
[D][05:19:24][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:24][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:24][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:24][COMM]Receive Bat Lock cmd 0
[D][05:19:24][COMM]VBUS is 1
[D][05:19:24][COMM]BAT CAN get state1 Fail 204
[D][05:19:24][COMM]BAT CAN get soc Fail, 204
[D][05:19:24][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:24][COMM]BAT CAN get state2 fail 204
[D][05:19:24][COMM]get bat work mode err
[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:24][M2M ]m2m_task: control_queue t

2025-07-31 22:29:42:313 ==>> ype:[M2M_GSM_POWER_ON]
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:24][COMM]Main Task receive event:61 finished processing
[W][05:19:24][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:24][COMM]arm_hub_enable: hub power: 0
[D][05:19:24][HSDK]hexlog index save 0 3584 255 @ 0 : 0
[D][05:19:24][HSDK]write save hexlog index [0]
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:24][COMM]read battery soc:255
[D][05:19:24][COMM]f:[ec800m_a

2025-07-31 22:29:42:387 ==>> udio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:24][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:24][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:24][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 22:29:42:492 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:25][COMM]arm_hub_enable: hub power: 0
[D][05:19:25][HSDK]hexlog index save 0 3584 255 @ 0 : 0
[D][05:19:25][HSDK]write save hexlog index [0]
[D][05:19:25][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:25][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:29:42:809 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:29:42:854 ==>> [D][05:19:26][COMM]exit wheel stolen mode.
[D][05:19:26][COMM]Main Task receive event:68
[D][05:19:26][COMM]handlerWheelStolen evt type = 2.
[E][05:19:26][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:26][GNSS]stop locating
[D][05:19:26][GNSS]all continue location stop
[D][05:19:26][COMM]Main Task receive event:68 finished processing


2025-07-31 22:29:43:034 ==>> [W][05:19:26][COMM]Power Off
[W][05:19:26][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:26][COMM]arm_hub_enable: hub power: 0
[D][05:19:26][HSDK]hexlog index save 0 3584 255 @ 0 : 0
[D][05:19:26][HSDK]write save hexlog index [0]
[D][05:19:26][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:26][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:29:43:082 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:29:43:110 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:29:43:129 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:29:43:200 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:29:43:305 ==>> [D][05:19:26][FCTY]get_ext_48v_vol retry i = 0,volt = 17
[D][05:19:26][FCTY]get_ext_48v_vol retry i = 1,volt = 17
[D][05:19:26][FCTY]get_ext_48v_vol retry i = 2,v

2025-07-31 22:29:43:365 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:29:43:373 ==>> 检测【检测小电池关机电流】
2025-07-31 22:29:43:388 ==>> 开始小电池电流采样
2025-07-31 22:29:43:410 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:43:470 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:29:44:475 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:29:44:550 ==>> CurrentBattery:ƽ��:63.32

2025-07-31 22:29:44:982 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:29:44:999 ==>> 【检测小电池关机电流】通过,【63.32uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:29:45:362 ==>> MES过站成功
2025-07-31 22:29:45:376 ==>> #################### 【测试结束】 ####################
2025-07-31 22:29:45:484 ==>> 关闭5V供电
2025-07-31 22:29:45:496 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:29:45:606 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:29:46:498 ==>> 关闭5V供电成功
2025-07-31 22:29:46:512 ==>> 关闭33V供电
2025-07-31 22:29:46:527 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:29:46:606 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:29:47:504 ==>> 关闭33V供电成功
2025-07-31 22:29:47:536 ==>> 关闭3.7V供电
2025-07-31 22:29:47:567 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:29:47:598 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:29:48:396 ==>>  

2025-07-31 22:29:48:516 ==>> 关闭3.7V供电成功
