2025-07-31 20:14:56:018 ==>> MES查站成功:
查站序号:P5100010053132BD验证通过
2025-07-31 20:14:56:023 ==>> 扫码结果:P5100010053132BD
2025-07-31 20:14:56:025 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:14:56:026 ==>> 测试参数版本:2024.10.11
2025-07-31 20:14:56:028 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:14:56:030 ==>> 检测【打开透传】
2025-07-31 20:14:56:032 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:14:56:139 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:14:56:365 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:14:56:368 ==>> 检测【检测接地电压】
2025-07-31 20:14:56:371 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:14:56:445 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:14:56:646 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:14:56:648 ==>> 检测【打开小电池】
2025-07-31 20:14:56:651 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:14:56:748 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:14:56:917 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:14:56:920 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:14:56:924 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:14:57:048 ==>> 1A A1 00 00 01 
Get AD_V0 1291mV
OVER 150


2025-07-31 20:14:57:186 ==>> 【检测小电池分压(AD_VBAT)】通过,【1291mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:14:57:188 ==>> 检测【等待设备启动】
2025-07-31 20:14:57:191 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:14:58:226 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:14:59:273 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:00:315 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:01:344 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:02:377 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:03:420 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:04:461 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:05:497 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:06:532 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:07:572 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:15:07:575 ==>> #################### 【测试结束】 ####################
2025-07-31 20:15:07:593 ==>> 关闭5V供电
2025-07-31 20:15:07:596 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:15:07:648 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:15:08:602 ==>> 关闭5V供电成功
2025-07-31 20:15:08:605 ==>> 关闭33V供电
2025-07-31 20:15:08:607 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:15:08:740 ==>> 5A A5 02 5A A5 


2025-07-31 20:15:08:845 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:15:09:617 ==>> 关闭33V供电成功
2025-07-31 20:15:09:620 ==>> 关闭3.7V供电
2025-07-31 20:15:09:622 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:15:09:740 ==>> 6A A6 02 A6 6A 


2025-07-31 20:15:09:845 ==>> Battery OFF
OVER 150


2025-07-31 20:15:10:073 ==>>  

