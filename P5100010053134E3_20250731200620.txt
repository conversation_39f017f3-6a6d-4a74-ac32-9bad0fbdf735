2025-07-31 20:06:20:371 ==>> MES查站成功:
查站序号:P5100010053134E3验证通过
2025-07-31 20:06:20:376 ==>> 扫码结果:P5100010053134E3
2025-07-31 20:06:20:377 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:06:20:379 ==>> 测试参数版本:2024.10.11
2025-07-31 20:06:20:380 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:06:20:382 ==>> 检测【打开透传】
2025-07-31 20:06:20:384 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:06:20:447 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:06:20:719 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:06:20:722 ==>> 检测【检测接地电压】
2025-07-31 20:06:20:724 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:06:20:842 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:06:20:996 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:06:20:999 ==>> 检测【打开小电池】
2025-07-31 20:06:21:001 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:06:21:146 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:06:21:269 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:06:21:272 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:06:21:275 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:06:21:341 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:06:21:539 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:06:21:542 ==>> 检测【等待设备启动】
2025-07-31 20:06:21:545 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:06:22:013 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:06:22:208 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:06:22:568 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:06:22:846 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:06:22:906 ==>>                                                    

2025-07-31 20:06:23:305 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:06:23:605 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:06:23:774 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:06:23:883 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:06:23:886 ==>> 检测【产品通信】
2025-07-31 20:06:23:889 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:06:24:030 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:06:24:153 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:06:24:155 ==>> 检测【初始化完成检测】
2025-07-31 20:06:24:157 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:06:24:366 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:06:24:425 ==>>                      edDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:06:24:428 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:06:24:431 ==>> 检测【关闭大灯控制1】
2025-07-31 20:06:24:432 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:06:24:606 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:06:24:699 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:06:24:702 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:06:24:704 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:06:24:849 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:06:24:955 ==>> [D][05:17:51][

2025-07-31 20:06:25:029 ==>> COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:06:25:239 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:06:25:242 ==>> 检测【关闭仪表供电】
2025-07-31 20:06:25:244 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:06:25:437 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:06:25:516 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:06:25:529 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:06:25:531 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:06:25:694 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:06:25:792 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:06:25:795 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:06:25:797 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:06:25:863 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:06:25:999 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:06:26:062 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:06:26:066 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:06:26:068 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:06:26:227 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:06:26:336 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:06:26:339 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:06:26:341 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:06:26:437 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:06:26:542 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:06:26:605 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:06:26:607 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:06:26:609 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:06:26:740 ==>> 5A A5 03 5A A5 


2025-07-31 20:06:26:845 ==>> OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:06:26:878 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:06:26:881 ==>> 该项需要延时执行
2025-07-31 20:06:27:398 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217

2025-07-31 20:06:27:428 ==>>  5012
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 20:06:27:888 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:06:28:129 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:06:28:626 ==>>                                                                                                                                                             cckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[

2025-07-31 20:06:28:731 ==>> D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17

2025-07-31 20:06:28:836 ==>> :55][COMM]VBUS is 1
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:5

2025-07-31 20:06:28:926 ==>> 5][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 20:06:29:904 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:06:30:545 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:06:30:891 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:06:30:894 ==>> 检测【33V输入电压ADC】
2025-07-31 20:06:30:897 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:30:908 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:06:31:147 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3149  volt:5535 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:57][COMM]adc read throttle adc:14  volt:18 mv
[D][05:17:57][COMM]adc read battery ts volt:17 mv
[D][05:17:57][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:17:57][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2451  volt:3949 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:06:31:436 ==>> 【33V输入电压ADC】通过,【32552mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:06:31:438 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:06:31:441 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:06:31:545 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1653mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 2023mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:06:31:714 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:31:734 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:06:31:736 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:31:738 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:06:31:739 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:06:31:753 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:06:31:757 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:06:31:772 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2023mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:06:31:775 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:06:31:796 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:06:31:798 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:06:31:944 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1653mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 20:06:32:072 ==>> 【TP7_VCC3V3(ADV2)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:32:074 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:06:32:090 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:32:092 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:06:32:095 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:06:32:116 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:06:32:118 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:06:32:136 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:06:32:139 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:06:32:167 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:06:32:170 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:06:32:295 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10017
1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1653mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:06:32:454 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:32:456 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:06:32:476 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:32:478 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:06:32:481 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:06:32:496 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:06:32:498 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:06:32:515 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:06:32:518 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:06:32:537 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:06:32:539 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:06:32:542 ==>> 检测【打开WIFI(1)】
2025-07-31 20:06:32:545 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:06:32:702 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:06:32:810 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:06:32:814 ==>> 检测【清空消息队列(1)】
2025-07-31 20:06:32:816 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:06:33:155 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx 

2025-07-31 20:06:33:215 ==>> ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:06:33:340 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:06:33:342 ==>> 检测【打开GPS(1)】
2025-07-31 20:06:33:344 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:06:33:675 ==>>                                                                                                                                                                                                                                              ec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087723807

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539171

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locat

2025-07-31 20:06:33:720 ==>> ing
[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:06:33:874 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:06:33:877 ==>> 检测【打开GSM联网】
2025-07-31 20:06:33:879 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:06:33:918 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:06:34:083 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:06:34:147 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:06:34:150 ==>> 检测【打开仪表供电1】
2025-07-31 20:06:34:154 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:06:34:338 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:06:34:417 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:06:34:421 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:06:34:444 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:06:34:848 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][C

2025-07-31 20:06:34:938 ==>> AT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.79.11.174"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:06:34:944 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:06:34:948 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:06:34:952 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:06:35:225 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                       [D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][COMM]arm_hub read adc[3],val[33572]
[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:06:35:469 ==>> 【读取主控ADC采集的仪表电压】通过,【33572mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:06:35:473 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:06:35:475 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:06:35:634 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:06:35:761 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:06:35:764 ==>> 检测【AD_V20电压】
2025-07-31 20:06:35:767 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:06:35:876 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:06:35:966 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13729 imu init OK
1A A1 10 00 00 
Get AD_V20 1642mV
OVER 150


2025-07-31 20:06:36:101 ==>> 本次取值间隔时间:216ms
2025-07-31 20:06:36:120 ==>> 【AD_V20电压】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:36:123 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:06:36:127 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:06:36:236 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:06:36:395 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:06:36:398 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:06:36:401 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:06:36:785 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,24,,,41,33,,,41,60,,,41,25,,,39,1*7D

$GBGSV,3,2,10,42,,,38,40,,,37,38,,,36,39,,,33,1*7F

$GBGSV,3,3,10,13,,,40,59,,,38,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1585.768,1585.768,50.716,2097152,2097152,2097152*4A

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:06:36:925 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:06:36:935 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:06:36:955 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:06:36:960 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:06:37:042 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:06:37:196 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:06:37:203 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:06:37:208 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:06:37:442 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:06:37:475 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:06:37:479 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:06:37:483 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:06:37:728 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,41,24,,,40,60,,,40,59,,,40,1*7C

$GBGSV,4,2,13,25,,,39,42,,,39,3,,,39,1,,,38,1*70

$GBGSV,4,3,13,38,,,37,14,,,37,13,,,36,40,,,36,1*7B

$GBGSV,4,4,13,39,,,35,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1584.926,1584.926,50.647,2097152,2097152,2097152*4F



2025-07-31 20:06:38:007 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:06:38:010 ==>> 检测【AD_V21电压】
2025-07-31 20:06:38:012 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:06:38:143 ==>> 1A A1 20 00 00 
Get AD_V21 1638mV
OVER 150


2025-07-31 20:06:38:233 ==>> 本次取值间隔时间:222ms
2025-07-31 20:06:38:252 ==>> 【AD_V21电压】通过,【1638mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:38:255 ==>> 检测【关闭仪表供电2】
2025-07-31 20:06:38:257 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:06:38:428 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:06:38:532 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:06:38:535 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:06:38:540 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:06:38:563 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:06:38:758 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,41,24,,,40,60,,,40,59,,,40,1*79

$GBGSV,4,2,16,25,,,40,42,,,40,3,,,40,14,,,38,1*4F

$GBGSV,4,3,16,2,,,38,1,,,37,38,,,37,13,,,37,1*73

$GBGSV,4,4,16,40,,,36,39,,,36,4,,,32,5,,,32,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1565.040,1565.040,50.055,2097152,2097152,2097152*4A

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:06:38:807 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:06:38:810 ==>> 检测【打开AccKey2供电】
2025-07-31 20:06:38:814 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:06:39:020 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:06:39:084 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:06:39:087 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:06:39:090 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:39:352 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:05][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:05][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:18:05][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:05][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:05][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:05][COMM]adc read battery ts volt:16 mv
[D][05:18:06][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:06][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2490  volt:4012 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:06:39:613 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33260mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:06:39:616 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:06:39:618 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:06:39:716 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,41,24,,,41,60,,,40,59,,,40,1*77

$GBGSV,5,2,18,25,,,40,42,,,40,3,,,40,14,,,39,1*41

$GBGSV,5,3,18,16,,,39,2,,,37,1,,,37,38,,,37,1*78

$GBGSV,5,4,18,13,,,37,39,,,37,40,,,36,5,,,33,1*42

$GBGSV,5,5,18,4,,,32,9,,,36,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1575.397,1575.397,50.379,2097152,2097152,2097152*47



2025-07-31 20:06:39:806 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:06:39:891 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:06:39:894 ==>> 该项需要延时执行
2025-07-31 20:06:40:576 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:06:40:681 ==>> $GBGGA,120644.538,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,

2025-07-31 20:06:40:741 ==>> ,,41,24,,,41,42,,,41,60,,,40,1*75

$GBGSV,6,2,21,59,,,40,25,,,40,14,,,40,3,,,39,1*42

$GBGSV,6,3,21,39,,,38,16,,,37,1,,,37,38,,,37,1*48

$GBGSV,6,4,21,13,,,37,2,,,36,40,,,36,6,,,35,1*77

$GBGSV,6,5,21,7,,,34,9,,,33,5,,,33,4,,,33,1*7E

$GBGSV,6,6,21,10,,,31,1*76

$GBRMC,120644.538,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120644.538,0.000,1537.911,1537.911,49.205,2097152,2097152,2097152*50



2025-07-31 20:06:41:728 ==>> $GBGGA,120645.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,41,24,,,41,42,,,40,60,,,40,1*74

$GBGSV,6,2,21,59,,,40,25,,,40,14,,,40,3,,,39,1*42

$GBGSV,6,3,21,39,,,38,16,,,37,1,,,37,38,,,37,1*48

$GBGSV,6,4,21,13,,,37,2,,,36,40,,,36,6,,,35,1*77

$GBGSV,6,5,21,7,,,34,9,,,34,5,,,33,4,,,33,1*79

$GBGSV,6,6,21,10,,,31,1*76

$GBRMC,120645.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120645.518,0.000,1537.906,1537.906,49.199,2097152,2097152,2097152*55



2025-07-31 20:06:42:586 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:06:42:691 ==>> $GBGGA,120646.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,24,,,41,42,,,40,60,,,40,1*77

$GBGSV,6,2,22,59,,,40,25,,,40,14,,,40,3,,,39,1*41

$GBGSV,6,3,22,39,

2025-07-31 20:06:42:736 ==>> ,,38,16,,,37,1,,,37,38,,,37,1*4B

$GBGSV,6,4,22,13,,,37,2,,,36,40,,,36,26,,,36,1*45

$GBGSV,6,5,22,6,,,34,7,,,34,9,,,34,5,,,33,1*7F

$GBGSV,6,6,22,4,,,33,10,,,31,1*41

$GBRMC,120646.518,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120646.518,0.000,1533.957,1533.957,49.072,2097152,2097152,2097152*52



2025-07-31 20:06:42:903 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:06:42:907 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:06:42:911 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:43:252 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:09][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:09][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:09][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:09][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:09][COMM]adc read battery ts volt:19 mv
[D][05:18:09][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:09][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2443  volt:3936 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 20:06:43:438 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【177mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:06:43:442 ==>> 检测【打开AccKey1供电】
2025-07-31 20:06:43:446 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:06:43:727 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 
$GBGGA,120647.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,24,,,40,42,,,40,60,,,40,1*77

$GBGSV,6,2,23,59,,,40,25,,,40,14,,,40,3,,,39,1*40

$GBGSV,6,3,23,39,,,38,16,,,37,1,,,37,38,,,37,1*4A

$GBGSV,6,4,23,13,,,37,2,,,36,40,,,36,26,,,35,1*47

$GBGSV,6,5,23,6,,,34,9,,,34,5,,,34,7,,,33,1*7E

$GBGSV,6,6,23,4,,,33,10,,,31,21,,,31,1*41

$GBRMC,120647.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120647.518,0.000,1519.545,1519.545,48.620,2097152,2097152,2097152*53



2025-07-31 20:06:43:976 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:06:43:981 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:06:43:987 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:06:44:034 ==>> 1A A1 00 40 00 
Get AD_V14 2608mV
OVER 150


2025-07-31 20:06:44:230 ==>> 原始值:【2608】, 乘以分压基数【2】还原值:【5216】
2025-07-31 20:06:44:283 ==>> 【读取AccKey1电压(ADV14)前】通过,【5216mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:06:44:288 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:06:44:293 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:44:552 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:11][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:11][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:11][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:11][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:11][COMM]adc read battery ts volt:21 mv
[D][05:18:11][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:11][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2490  volt:4012 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:06:44:582 ==>>                                          

2025-07-31 20:06:44:687 ==>> $GBGGA,120648.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,24,,,40,42,,,40,60,,,40,1*70

$GBGSV,6,2,24,59,,,4

2025-07-31 20:06:44:747 ==>> 0,25,,,40,14,,,40,3,,,39,1*47

$GBGSV,6,3,24,39,,,38,16,,,37,1,,,37,38,,,37,1*4D

$GBGSV,6,4,24,13,,,37,2,,,36,40,,,36,26,,,35,1*40

$GBGSV,6,5,24,6,,,34,9,,,34,5,,,33,7,,,33,1*7E

$GBGSV,6,6,24,41,,,32,4,,,32,10,,,31,21,,,31,1*43

$GBRMC,120648.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120648.518,0.000,1508.063,1508.063,48.262,2097152,2097152,2097152*5E



2025-07-31 20:06:44:811 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:06:44:814 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:06:44:819 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:06:45:002 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:06:45:080 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:06:45:084 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:06:45:086 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:06:45:137 ==>> 1A A1 00 40 00 
Get AD_V14 2610mV
OVER 150


2025-07-31 20:06:45:332 ==>> 原始值:【2610】, 乘以分压基数【2】还原值:【5220】
2025-07-31 20:06:45:350 ==>> 【读取AccKey1电压(ADV14)后】通过,【5220mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:06:45:353 ==>> 检测【打开WIFI(2)】
2025-07-31 20:06:45:356 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:06:45:558 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:06:45:638 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:06:45:643 ==>> 检测【转刹把供电】
2025-07-31 20:06:45:649 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:06:45:738 ==>> $GBGGA,120649.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,24,,,40,42,,,40,60,,,40,1*70

$GBGSV,6,2,24,59,,,40,25,,,40,14,,,40,3,,,39,1*47

$GBGSV,6,3,24,39,,,38,16,,,37,1,,,37,38,,,37,1*4D

$GBGSV,6,4,24,13,,,37,2,,,36,40,,,36,26,,,35,1*40

$GBGSV,6,5,24,9,,,35,6,,,34,5,,,33,7,,,33,1*7F

$GBGSV,6,6,24,4,,,33,41,,,32,10,,,31,21,,,31,1*42

$GBRMC,120649.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120649.518,0.000,1511.514,1511.514,48.368,2097152,2097152,2097152*54



2025-07-31 20:06:45:828 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:06:45:922 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:06:45:929 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:06:45:934 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:06:46:023 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:06:46:144 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2400mV
OVER 150


2025-07-31 20:06:46:174 ==>> 原始值:【2400】, 乘以分压基数【2】还原值:【4800】
2025-07-31 20:06:46:203 ==>> 【读取AD_V15电压(前)】通过,【4800mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:06:46:207 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:06:46:210 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:06:46:309 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:06:46:429 ==>> +WIFISCAN:4,0,F42A7D1297A3,-62
+WIFISCAN:4,1,CC057790A740,-66
+WIFISCAN:4,2,CC057790A741,-66
+WIFISCAN:4,3,44A1917CAD81,-70

[D][05:18:13][CAT1]wifi scan report total[4]


2025-07-31 20:06:46:504 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:06:46:745 ==>> [D][05:18:13][COMM]read battery soc:255
$GBGGA,120650.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,40,42,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,40,3,,,40,1*49

$GBGSV,7,3,25,39,,,38,16,,,37,1,,,37,38,,,37,1*4D

$GBGSV,7,4,25,13,,,37,2,,,36,40,,,36,26,,,35,1*40

$GBGSV,7,5,25,9,,,35,6,,,34,5,,,34,7,,,34,1*7F

$GBGSV,7,6,25,4,,,33,41,,,32,10,,,31,21,,,31,1*42

$GBGSV,7,7,25,8,,,29,1*42

$GBRMC,120650.518,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120650.518,0.000,1504.133,1504.133,48.145,2097152,2097152,2097152*51



2025-07-31 20:06:46:955 ==>> [D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 20:06:47:278 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:06:47:386 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:06:47:401 ==>> [W][05:18:14][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:06:47:446 ==>> 1A A1 01 00 00 
Get AD_V16 2435mV
OVER 150


2025-07-31 20:06:47:551 ==>> 原始值:【2435】, 乘以分压基数【2】还原值:【4870】
2025-07-31 20:06:47:586 ==>> 【读取AD_V16电压(前)】通过,【4870mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:06:47:589 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:06:47:594 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:47:859 ==>> $GBGGA,120651.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,40,42,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,40,3,,,40,1*49

$GBGSV,7,3,25,39,,,38,16,,,37,1,,,37,38,,,37,1*4D

$GBGSV,7,4,25,13,,,37,2,,,36,40,,,36,26,,,35,1*40

$GBGSV,7,5,25,9,,,35,6,,,34,5,,,34,7,,,33,1*78

$GBGSV,7,6,25,4,,,33,41,,,33,8,,,33,10,,,31,1*7A

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120651.518,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120651.518,0.000,1510.752,1510.752,48.342,2097152,2097152,2097152*55

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:14][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:14][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:14][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:14][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:14][COMM]adc read battery ts volt:19 mv
[D][05:18:14][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3085  volt:5422 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read 

2025-07-31 20:06:47:904 ==>> vbat adc:2438  volt:3928 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:06:48:118 ==>> 【转刹把供电电压(主控ADC)】通过,【5422mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:06:48:122 ==>> 检测【转刹把供电电压】
2025-07-31 20:06:48:126 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:48:463 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:15][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:15][COMM]adc read left brake adc:17  volt:22 mv
[D][05:18:15][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:15][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:15][COMM]adc read battery ts volt:19 mv
[D][05:18:15][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3087  volt:5426 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2428  volt:3912 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:06:48:647 ==>> 【转刹把供电电压】通过,【5426mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:06:48:650 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:06:48:655 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:06:48:748 ==>> [D][05:18:15][COMM]read battery soc:255
$GBGGA,120652.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,41,24,,,40,60,,,40,1*71

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,40,3,,,39,1*47

$GBGSV,7,3,25,39,,,38,16,,,37,1,,,37,38,,,37,1*4D

$GBGSV,7,4,25,13,,,37,2,,,36,40,,,36,26,,,35,1*40

$GBGSV,7,5,25,9,,,35,6,,,34,5,,,34,7,,,34,1*7F

$GBGSV,7,6,25,8,,,34,4,,,33,41,,,33,10,,,31,1*7D

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120652.518,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120652.518,0.000,1514.066,1514.066,48.445,2097152,2097152,2097152*56



2025-07-31 20:06:48:823 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:06:48:917 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:06:48:921 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:06:48:926 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:06:49:023 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:06:49:130 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:06:49:145 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:06:49:235 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:06:49:340 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:06:49:360 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:06:49:365 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:06:49:370 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:06:49:461 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:06:49:507 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:06:49:537 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 20:06:49:586 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:06:49:589 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:06:49:594 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:06:49:642 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:06:49:747 ==>> $GBGGA,120653.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,40,3,,,40,1*49

$GBGSV,7,3,25,39,,,38,13,,,38,16,,,37,1,,,37,1*4B

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,6,,,34,5,,,34,7,,,34,1*7F

$GBGSV,7,6,25,8,,,34,4,,,33,41,,,33,10,,,31,1*7D

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120653.518,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120653.518,0.000,1515.724,1515.724,48.498,2097152,2097152,2097152*57



2025-07-31 20:06:49:863 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:06:49:867 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:06:49:869 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:06:49:944 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:06:50:143 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:06:50:150 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:06:50:156 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:06:50:244 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:06:50:413 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:06:50:416 ==>> 检测【左刹电压测试1】
2025-07-31 20:06:50:420 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:50:799 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:17][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:17][COMM]adc read left brake adc:1731  volt:2282 mv
[D][05:18:17][COMM]adc read right brake adc:1724  volt:2272 mv
[D][05:18:17][COMM]adc read throttle adc:1725  volt:2274 mv
[D][05:18:17][COMM]adc read battery ts volt:16 mv
[D][05:18:17][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:17][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:17][COMM]read battery soc:255
[D][05:18:17][COMM]arm_hub adc read bat_id adc:13  volt:10 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2485  volt:4004 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
$GBGGA,120654.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,40,3,,,39,1*47

$GBGSV,7,3,25,39,,,38,13,,,38,16,,,37,1,,,37,1*4B

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,35,6,,,34,5,,,33,1*76

$GBGSV,7,6,25,7,,,33,4,,,33,41,,,33,10,,,32,1*76

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120654.5

2025-07-31 20:06:50:844 ==>> 18,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120654.518,0.000,1512.409,1512.409,48.393,2097152,2097152,2097152*5C

[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:06:50:937 ==>> 【左刹电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 20:06:50:941 ==>> 检测【右刹电压测试1】
2025-07-31 20:06:50:960 ==>> 【右刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 20:06:50:963 ==>> 检测【转把电压测试1】
2025-07-31 20:06:50:978 ==>> 【转把电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 20:06:50:983 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:06:50:989 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:06:51:042 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:06:51:255 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:06:51:265 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:06:51:271 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:06:51:342 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:06:51:539 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:06:51:546 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:06:51:572 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:06:51:642 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:06:51:747 ==>> $GBGGA,120655.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,14,,,40,25,,,39,3,,,39,1*49

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,35,6,,,34,5,,,33,1*76

$GBGSV,7,6,25,7,,,33,4,,,33,41,,,33,10,,,31,1*75

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120655.518,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120655.518,0.000,1507.434,1507.434,48.235,2097152,2097152,2097152*50



2025-07-31 20:06:51:811 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:06:51:814 ==>> 检测【左刹电压测试2】
2025-07-31 20:06:51:817 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:06:52:156 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:18][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:18][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:18][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:18][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:18][COMM]adc read battery ts volt:20 mv
[D][05:18:18][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:18][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2434  volt:3921 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:06:52:340 ==>> 【左刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:06:52:345 ==>> 检测【右刹电压测试2】
2025-07-31 20:06:52:358 ==>> 【右刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:06:52:364 ==>> 检测【转把电压测试2】
2025-07-31 20:06:52:380 ==>> 【转把电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:06:52:384 ==>> 检测【晶振检测】
2025-07-31 20:06:52:387 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:06:52:531 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 20:06:52:654 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:06:52:658 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:06:52:664 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:06:52:771 ==>> [D][05:18:19][COMM]read battery soc:255
$GBGGA,120656.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,14,,,39,25,,,39,3,,,39,1*47

$GBGSV,7,3,25,39,,,37,13,,,37,16,,,37,1,,,37,1*4B

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,34,6,,,34,5,,,33,1*77

$GBGSV,7,6,25,7,,,33,4,,,33,41,,,33,10,,,31,1*75

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120656.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120656.518,0.000,1502.458,1502.458,48.075,2097152,2097152,2097152*55

1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1652mV
Get AD_V4 1645mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:06:52:925 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:06:52:929 ==>> 检测【检测BootVer】
2025-07-31 20:06:52:934 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:06:53:296 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:19][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
[D][05:18:19][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:19][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:19][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:19][FCTY]DeviceID    = 460130071539171
[D][05:18:19][FCTY]HardwareID  = 867222087723807
[D][05:18:19][FCTY]MoBikeID    = 9999999999
[D][05:18:19][FCTY]LockID      = FFFFFFFFFF
[D][05:18:19][FCTY]BLEFWVersion= 105
[D][05:18:19][FCTY]BLEMacAddr   = CD5E73B20727
[D][05:18:19][FCTY]Bat         = 4044 mv
[D][05:18:19][FCTY]Current     = 50 ma
[D][05:18:19][FCTY]VBUS        = 11700 mv
[D][05:18:19][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:19][FCTY]Ext battery vol = 32, adc = 1296
[D][05:18:19][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 202 mv
[D][05:18:19][FCTY]Bike Type flag is invalied
[D][05:18:19][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:19][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:19][FCTY

2025-07-31 20:06:53:340 ==>> ]CAT1_GNSS_PLATFORM = C4
[D][05:18:19][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:19][FCTY]Bat1         = 3725 mv
[D][05:18:19][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:06:53:468 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:06:53:472 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:06:53:478 ==>> 检测【检测固件版本】
2025-07-31 20:06:53:508 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:06:53:512 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:06:53:515 ==>> 检测【检测蓝牙版本】
2025-07-31 20:06:53:526 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:06:53:530 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:06:53:536 ==>> 检测【检测MoBikeId】
2025-07-31 20:06:53:549 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:06:53:552 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:06:53:572 ==>> 检测【检测蓝牙地址】
2025-07-31 20:06:53:575 ==>> 取到目标值:CD5E73B20727
2025-07-31 20:06:53:578 ==>> 【检测蓝牙地址】通过,【CD5E73B20727】符合目标值【】要求!
2025-07-31 20:06:53:582 ==>> 提取到蓝牙地址:CD5E73B20727
2025-07-31 20:06:53:588 ==>> 检测【BOARD_ID】
2025-07-31 20:06:53:617 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:06:53:620 ==>> 检测【检测充电电压】
2025-07-31 20:06:53:636 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:06:53:642 ==>> 检测【检测VBUS电压1】
2025-07-31 20:06:53:655 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:06:53:661 ==>> 检测【检测充电电流】
2025-07-31 20:06:53:677 ==>> 【检测充电电流】通过,【50ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:06:53:682 ==>> 检测【检测IMEI】
2025-07-31 20:06:53:708 ==>> 取到目标值:867222087723807
2025-07-31 20:06:53:712 ==>> 【检测IMEI】通过,【867222087723807】符合目标值【】要求!
2025-07-31 20:06:53:716 ==>> 提取到IMEI:867222087723807
2025-07-31 20:06:53:719 ==>> 检测【检测IMSI】
2025-07-31 20:06:53:741 ==>> 取到目标值:460130071539171
2025-07-31 20:06:53:744 ==>> 【检测IMSI】通过,【460130071539171】符合目标值【】要求!
2025-07-31 20:06:53:747 ==>> 提取到IMSI:460130071539171
2025-07-31 20:06:53:751 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:06:53:755 ==>> 取到目标值:460130071539171
2025-07-31 20:06:53:763 ==>> $GBGGA,120657.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,25,,,40,14,,,39,3,,,39,1*49

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,34,8,,,34,6,,,34,5,,,33,1*76

$GBGSV,7,6,25,7,,,33,41,,,33,4,,,32,10,,,31,1*74

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120657.518,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120657.518,0.000,1502.465,1502.465,48.081,2097152,2097152,2097152*5F



2025-07-31 20:06:53:768 ==>> 【校验网络运营商(移动)】通过,【460130071539171】符合目标值【】要求!
2025-07-31 20:06:53:773 ==>> 检测【打开CAN通信】
2025-07-31 20:06:53:779 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:06:53:841 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:06:54:042 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:06:54:046 ==>> 检测【检测CAN通信】
2025-07-31 20:06:54:052 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:06:54:132 ==>> can send success


2025-07-31 20:06:54:162 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:06:54:222 ==>> [D][05:18:20][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32001
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:06:54:282 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:06:54:316 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:06:54:323 ==>> 检测【关闭CAN通信】
2025-07-31 20:06:54:344 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:06:54:347 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:06:54:402 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:06:54:447 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:06:54:586 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:06:54:590 ==>> 检测【打印IMU STATE】
2025-07-31 20:06:54:593 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:06:54:826 ==>> [D][05:18:21][COMM]read battery soc:255
$GBGGA,120658.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,25,,,40,14,,,40,59,,,39,3,,,39,1*49

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,34,6,,,34,5,,,33,1*77

$GBGSV,7,6,25,7,,,33,41,,,33,4,,,33,10,,,31,1*75

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120658.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120658.518,0.000,1505.777,1505.777,48.183,2097152,2097152,2097152*53

[D][05:18:21][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:21][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:0
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:06:54:860 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:06:54:864 ==>> 检测【六轴自检】
2025-07-31 20:06:54:870 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:06:55:024 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:21][CAT1]gsm read msg sub id: 12
[D][05:18:21][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:06:55:746 ==>> $GBGGA,120659.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,25,,,40,14,,,40,59,,,40,3,,,39,1*47

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,35,6,,,34,5,,,34,1*71

$GBGSV,7,6,25,7,,,33,41,,,33,4,,,33,10,,,32,1*76

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120659.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120659.518,0.000,1512.406,1512.406,48.391,2097152,2097152,2097152*53



2025-07-31 20:06:56:780 ==>> [D][05:18:23][COMM]read battery soc:255
$GBGGA,120700.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,41,42,,,40,60,,,40,1*71

$GBGSV,7,2,25,25,,,40,14,,,40,59,,,40,3,,,39,1*47

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,35,6,,,34,5,,,34,1*71

$GBGSV,7,6,25,7,,,33,41,,,33,4,,,32,10,,,31,1*74

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120700.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120700.518,0.000,1510.756,1510.756,48.346,2097152,2097152,2097152*54

[D][05:18:23][CAT1]<<< 
OK

[D][05:18:23][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:06:56:870 ==>> [D][05:18:23][COMM]Main Task receive event:142
[D][05:18:23][COMM]###### 34643 imu self test OK ######
[D][05:18:23][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,-7,4050]
[D][05:18:23][COMM]Main Task receive event:142 finished processing


2025-07-31 20:06:56:949 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:06:56:953 ==>> 检测【打印IMU STATE2】
2025-07-31 20:06:56:959 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:06:57:130 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:06:57:224 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:06:57:229 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:06:57:235 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:06:57:344 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:06:57:498 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:06:57:507 ==>> 检测【检测VBUS电压2】
2025-07-31 20:06:57:529 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:06:57:532 ==>> [D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:06:57:923 ==>> $GBGGA,120701.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,40,42,,,40,25,,,40,1*71

$GBGSV,7,2,25,14,,,40,59,,,40,60,,,39,3,,,39,1*48

$GBGSV,7,3,25,39,,,38,13,,,37,16,,,37,1,,,37,1*44

$GBGSV,7,4,25,38,,,37,2,,,36,40,,,36,26,,,35,1*49

$GBGSV,7,5,25,9,,,35,8,,,35,6,,,34,5,,,34,1*71

$GBGSV,7,6,25,7,,,33,41,,,33,4,,,33,10,,,31,1*75

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120701.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120701.518,0.000,753.139,753.139,688.764,2097152,2097152,2097152*6B

[W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071539171
[D][05:18:24][FCTY]HardwareID  = 867222087723807
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = CD5E73B20727
[D][05:18:24][FCTY]Bat         = 3924 mv
[D][05:18:

2025-07-31 20:06:58:013 ==>> 24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11700 mv
[D][05:18:24][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 7, adc = 312
[D][05:18:24][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 50 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3725 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:06:58:294 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:06:58:799 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539171
[D][05:18:25][FCTY]HardwareID  = 867222087723807
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = CD5E73B20727
[D][05:18:25][FCTY]Bat         = 3904 mv
[D][05:18:25][FCTY]Current     = 50 ma
[D][05:18:25][FCTY]VBUS        = 4900 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 174
[D][05:18:25][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 75 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]

2025-07-31 20:06:58:841 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:06:58:846 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:06:58:855 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:06:58:905 ==>> CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3725 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,120702.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,42,,,41,33,,,41,3,,,40,60,,,40,1*47

$GBGSV,7,2,26,14,,,40,59,,,40,24,,,40,25,,,40,1*7F

$GBGSV,7,3,26,39,,,38,13,,,37,38,,,37,1,,,37,1*4B

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,5,,,34,6,,,34,1*4E

$GBGSV,7,6,26,7,,,33,41,,,33,10,,,32,4,,,32,1*74

$GBGSV,7,7,26,21,,,30,34,,,,1*75

$GBRMC,120702.518,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120702.518,0.000,755.628,755.628,691.041,2097152,2097152,2097152*60

5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:06:58:979 ==>> [D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32


2025-07-31 20:06:59:039 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:06:59:130 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:06:59:135 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:06:59:142 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:06:59:205 ==>> [D][05:18:25][COMM]msg 0601 loss. last_tick:31998. cur_tick:36999. period:500
[D][05:18:25][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 37000


2025-07-31 20:06:59:235 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:06:59:416 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:06:59:421 ==>> 检测【打开WIFI(3)】
2025-07-31 20:06:59:427 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:06:59:772 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6
$GBGGA,120703.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,60,,,40,14,,,40,59,,,40,1*7A

$GBGSV,7,2,26,24,,,40,42,,,40,25,,,40,3,,,39,1*4D

$GBGSV,7,3,26,39,,,38,13,,,37,38,,,37,1,,,37,1*4B

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,7,,,34,5,,,34,1*4F

$GBGSV,7,6,26,6,,,34,4,,,33,41,,,33,10,,,32,1*73

$GBGSV,7,7,26,21,,,30,34,,,,1*75

$GBRMC,120703.518,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120703.518,0.000,755.619,755.619,691.031,2097152,2097152,2097152*66



2025-07-31 20:06:59:967 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:06:59:972 ==>> 检测【扩展芯片hw】
2025-07-31 20:06:59:978 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:07:00:139 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:26][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:07:00:253 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:07:00:258 ==>> 检测【扩展芯片boot】
2025-07-31 20:07:00:271 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:07:00:279 ==>> 检测【扩展芯片sw】
2025-07-31 20:07:00:295 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:07:00:299 ==>> 检测【检测音频FLASH】
2025-07-31 20:07:00:303 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:07:00:568 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:27][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:0------------
[D][05:18:27][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:07:01:497 ==>>                                 l_device_poweron type 16.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:1------------
[D][05:18:27][COMM]more than the number of battery plugs
[D][05:18:27][COMM]VBUS is 1
[D][05:18:27][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:27][COMM]file:B50 exist
[D][05:18:27][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:27][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:27][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:27][COMM]Bat auth off fail, error:-1
[D][05:18:27][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:1------------
[D][05:18:27][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:27][COMM]----- get Acckey 1 and value:1------------
[D][05:18:27][COMM]----- get Acckey 2 and value:1------------
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:27][COMM]file:B50 exist
[D][05:18:27][COMM][Audio].l:[255]. success, file_name:B50, size:1080

2025-07-31 20:07:01:602 ==>> 0
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:27][COMM]read file, len:10800, num:3
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
$GBGGA,120704.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,60,,,40,14,,,40,1*45

$GBGSV,7,2,26,59,,,40,24,,,40,42,,,40,25,,,40,1*7C

$GBGSV,7,3,26,39,,,38,13,,,37,38,,,37,1,,,37,1*4B

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,5,,,34,9,,,34,6,,,34,1*4F

$GBGSV,7,6,26,7,,,33,41,,,33,4,,,32,10,,,31,1*77

$GBGSV,7,7,26,21,,,30,34,,,,1*75

$GBRMC,120704.518,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120704.518,0.000,753.147,753.147,688.772,2097152,2097152,2097152*69

[D][05:18:27][COMM]Main Task receive event:66
[D][05:18:27][COMM]Try to Auto Lock Bat
[D][05:18:27][COMM]Main Task receive event:66 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D

2025-07-31 20:07:01:707 ==>> ][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[D][05:18:27][COMM]get soc error
[E][05:18:27][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][PROT]index:0
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][COMM]Receive Bat Lock cmd 0
[D][05:18:27][COMM]VBUS is 1
[D][05:18:27][COMM]Main Task receive event:61
[D][05:18:27][COMM][D301]:type:3

2025-07-31 20:07:01:812 ==>> , trace id:280
[D][05:18:27][COMM]id[], hw[000
[D][05:18:27][COMM]get mcMaincircuitVolt error
[D][05:18:27][COMM]get mcSubcircuitVolt error
[D][05:18:27][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[D][05:18:27][COMM]get bat work state err
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:27][PROT]remove success[1629955107],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_pat

2025-07-31 20:07:01:916 ==>> h:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][CAT1]gsm read msg sub id: 15
[W][05:18:27][PROT]add success [1629955107],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][COMM]Main Task receive event:61 finished processing
[D][05:18:27][COMM]--->crc16:0xb8a
[D][05:18:27][COMM]read file success
[W][05:18:27][COMM][Audio].l:[936].close hexlog save
[D][05:18:27][COMM]accel parse set 1
[D][05:18:27][COMM][Audio]mon:9,05:18:27
[D][05:18:27

2025-07-31 20:07:02:021 ==>> ][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:27][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B52B0B57493AA4C41E8C16C79C11A6E00F38A6D6004BFDF553701D240EDB2AAD836AFA18D0F100923DE48B80451DEDCC7CDB11E7D8B76F2213ED332712BA5B85886BB91816DCCDB63A97D2B3EAAF1A9F992194
[D][05:18:27][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:27][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:27][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:27][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET

2025-07-31 20:07:02:126 ==>> _SEND_ACK OK
[D][05:18:27][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:27][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:27][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:27][PROT]M2M Send ok [1629955107]
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:27][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:27][COMM]f:[ec800m_audio_play_

2025-07-31 20:07:02:201 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:07:02:766 ==>> $GBGGA,120706.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,42,,,41,33,,,41,3,,,40,60,,,40,1*47

$GBGSV,7,2,26,14,,,40,59,,,40,24,,,40,25,,,40,1*7F

$GBGSV,7,3,26,39,,,38,13,,,37,38,,,37,1,,,37,1*4B

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,6,,,34,41,,,34,1*7E

$GBGSV,7,6,26,7,,,33,5,,,33,4,,,33,10,,,32,1*45

$GBGSV,7,7,26,21,,,30,34,,,,1*75

$GBRMC,120706.518,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120706.518,0.000,756.453,756.453,691.795,2097152,2097152,2097152*6A



2025-07-31 20:07:03:059 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 20:07:03:767 ==>> [D][05:18:30][COMM]imu_task imu work error:[-1]. goto init
$GBGGA,120707.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,42,,,41,33,,,41,3,,,40,60,,,40,1*47

$GBGSV,7,2,26,14,,,40,59,,,40,24,,,40,25,,,40,1*7F

$GBGSV,7,3,26,13,,,38,39,,,38,38,,,37,1,,,37,1*44

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,6,,,34,41,,,34,1*7E

$GBGSV,7,6,26,7,,,33,5,,,33,4,,,33,10,,,32,1*45

$GBGSV,7,7,26,21,,,30,34,,,,1*75

$GBRMC,120707.518,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120707.518,0.000,757.281,757.281,692.552,2097152,2097152,2097152*61



2025-07-31 20:07:04:835 ==>> [D][05:18:31][COMM]42460 imu init OK
$GBGGA,120708.518,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,41,42,,,41,33,,,41,60,,,40,1*73

$GBGSV,7,2,26,14,,,40,59,,,40,25,,,40,3,,,39,1*44

$GBGSV,7,3,26,13,,,38,39,,,38,38,,,37,1,,,37,1*44

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,5,,,34,6,,,34,1*4E

$GBGSV,7,6,26,41,,,34,7,,,33,4,,,33,10,,,32,1*72

$GBGSV,7,7,26,21,,,31,34,,,,1*74

$GBRMC,120708.518,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120708.518,0.000,758.930,758.930,694.060,2097152,2097152,2097152*6C

[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:31][COMM]crc 108B
[D][05:18:31][COMM]flash test ok
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:31][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_en

2025-07-31 20:07:04:865 ==>> d].l:[863].recv ok
[D][05:18:31][COMM]accel parse set 0
[D][05:18:31][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:07:05:079 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 20:07:05:357 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:07:05:362 ==>> 检测【打开喇叭声音】
2025-07-31 20:07:05:370 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:07:06:176 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:32][COMM]file:A20 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:A20 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:32][COMM]read file, len:15228, num:4
[D][05:18:32][COMM]--->crc16:0x419c
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec80

2025-07-31 20:07:06:281 ==>> 0m_audio_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
$GBGGA,120709.518,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,42,,,41,3,,,40,60,,,40,1*44

$GBGSV,7,2,26,14,,,40,59,,,40,24,,,40,25,,,40,1*7F

$GBGSV,7,3,26,13,,,38,1,,,38,39,,,38,38,,,37,1*4B

$GBGSV,7,4,26,16,,,37,2,,,36,40,,,36,8,,,35,1*7A

$GBGSV,7,5,26,26,,,35,9,,,35,7,,,34,5,,,34,1*4F

$GBGSV,7,6,26,6,,,34,41,,,34,4,,,33,10,,,32,1*74

$GBGSV,7,7,26,21,,,31,34,,,,1*74

$GBRMC,120709.518,V,,,,,,,

2025-07-31 20:07:06:386 ==>> 310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120709.518,0.000,761.414,761.414,696.331,2097152,2097152,2097152*68

[D][05:18:32][COMM]43470 imu init OK
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec

2025-07-31 20:07:06:420 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:07:06:431 ==>> 检测【打开大灯控制】
2025-07-31 20:07:06:451 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:07:06:491 ==>> 800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:0 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:2
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900005]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]t

2025-07-31 20:07:06:596 ==>> x ret[17] >>> AT+QISEND=0,198

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B59B7148884DF687382B2516DCFA6ADD744E32EF39F576BAADD34F1F1AF60DC126297E8490E2674091F0B37EB6C569ED9671E5C1BAEC2D43DA46FDC0804278E984FB5CE42B0EBF27518BD697F911EDF40FDDFD
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 20:07:06:701 ==>>                                               SA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7

2025-07-31 20:07:06:776 ==>> ,1,26,33,,,42,59,,,41,42,,,41,3,,,40,1*4F

$GBGSV,7,2,26,60,,,40,14,,,40,24,,,40,25,,,40,1*75

$GBGSV,7,3,26,13,,,38,1,,,38,39,,,38,2,,,37,1*72

$GBGSV,7,4,26,38,,,37,16,,,37,40,,,36,8,,,35,1*42

$GBGSV,7,5,26,26,,,35,9,,,35,6,,,35,7,,,34,1*4D

$GBGSV,7,6,26,5,,,34,41,,,34,4,,,33,10,,,32,1*77

$GBGSV,7,7,26,21,,,31,34,,,,1*74

$GBRMC,120710.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120710.518,0.000,763.897,763.897,698.602,2097152,2097152,2097152*6B

[W][05:18:33][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:07:06:946 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:07:06:952 ==>> 检测【关闭仪表供电3】
2025-07-31 20:07:06:960 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:07:07:113 ==>> [D][05:18:33][COMM]read battery soc:255
[W][05:18:33][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:33][COMM]set POWER 0


2025-07-31 20:07:07:221 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:07:07:227 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:07:07:234 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:07:07:420 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:07:07:503 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:07:07:509 ==>> 检测【读大灯电压】
2025-07-31 20:07:07:514 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:07:07:834 ==>> $GBGGA,120707.525,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,81,218,40,33,62,310,42,3,61,191,40,24,59,359,41,1*4E

$GBGSV,7,2,26,42,56,158,41,59,52,129,41,16,52,17,37,39,52,2,38,1*49

$GBGSV,7,3,26,6,51,29,35,1,48,126,38,2,46,238,37,60,41,238,40,1*79

$GBGSV,7,4,26,9,38,312,35,25,38,266,40,7,36,184,34,38,34,191,37,1*76

$GBGSV,7,5,26,21,33,326,31,4,32,112,33,13,32,211,38,8,29,205,35,1*79

$GBGSV,7,6,26,40,29,160,36,10,26,193,32,5,22,257,34,26,18,227,35,1*4F

$GBGSV,7,7,26,34,7,162,,41,,,34,1*75

$GBRMC,120707.525,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,120707.525,0.423,0.156,0.153,0.241,3.785,4.891,18*59

[W][05:18:34][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:34][COMM]arm_hub read adc[5],val[33270]


2025-07-31 20:07:08:037 ==>> 【读大灯电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:07:08:045 ==>> 检测【关闭大灯控制2】
2025-07-31 20:07:08:053 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:07:08:110 ==>> +WIFISCAN:4,0,F88C21BCF57D,-35
+WIFISCAN:4,1,F42A7D1297A3,-62
+WIFISCAN:4,2,74C330CCAB10,-67
+WIFISCAN:4,3,CC057790A640,-67

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 20:07:08:215 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:07:08:315 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:07:08:321 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:07:08:353 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:07:08:552 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:35][COMM]arm_hub read adc[5],val[115]
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:07:08:585 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:07:08:593 ==>> 检测【打开WIFI(4)】
2025-07-31 20:07:08:615 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:07:09:418 ==>> $GBGGA,120708.505,2301.2579568,N,11421.9417096,E,1,08,1.42,70.619,M,-1.770,M,,*59

$GBGSA,A,3,33,24,42,39,13,25,38,40,,,,,3.80,1.42,3.53,4*01

$GBGSV,7,1,25,14,81,218,40,33,62,310,42,3,61,191,40,24,59,359,41,1*4D

$GBGSV,7,2,25,42,56,158,41,59,52,129,40,16,52,17,37,39,52,2,38,1*4B

$GBGSV,7,3,25,6,51,29,35,13,49,221,38,1,48,126,38,2,46,238,37,1*71

$GBGSV,7,4,25,60,41,238,40,9,38,312,35,25,38,266,41,7,36,184,34,1*7B

$GBGSV,7,5,25,38,34,191,37,21,33,326,31,4,32,112,33,8,29,205,35,1*71

$GBGSV,7,6,25,40,29,160,36,10,26,193,32,5,22,257,34,26,18,227,35,1*4C

$GBGSV,7,7,25,41,,,34,1*73

$GBGSV,2,1,07,33,62,310,42,24,59,359,41,42,56,158,41,39,52,2,39,5*7F

$GBGSV,2,2,07,25,38,266,39,38,34,191,36,40,29,160,33,5*49

$GBRMC,120708.505,A,2301.2579568,N,11421.9417096,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

[D][05:18:35][GNSS]HD8040 GPS
[D][05:18:35][GNSS]GPS diff_sec 124008513, report 0x42 frame
$GBGST,120708.505,1.370,0.216,0.207,0.337,2.027,2.566,9.410*77

[W][05:18:35][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D

2025-07-31 20:07:09:522 ==>> ][05:18:35][CAT1]<<< 
OK

[D][05:18:35][CAT1]exec over: func id: 12, ret: 6
[D][05:18:35][COMM]Main Task receive event:131
[D][05:18:35][COMM]index:0,power_mode:0xFF
[D][05:18:35][COMM]index:1,sound_mode:0xFF
[D][05:18:35][COMM]index:2,gsensor_mode:0xFF
[D][05:18:35][COMM]index:3,report_freq_mode:0xFF
[D][05:18:35][COMM]index:4,report_period:0xFF
[D][05:18:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:35][COMM]index:6,normal_reset_period:0xFF
[D][05:18:35][COMM]index:7,spock_over_speed:0xFF
[D][05:18:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:35][COMM]index:16,imu_config_params:0xFF
[D][05:18:35][COMM]index:17,long_connect_params:0xFF
[D][05:18:35][COMM]index:18,detain_mark:0xFF
[D][05:18:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:35][COMM]index:20,lock_pos_report_interval:0xFF

2025-07-31 20:07:09:530 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:07:09:536 ==>> 检测【EC800M模组版本】
2025-07-31 20:07:09:547 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:07:09:627 ==>> 
[D][05:18:35][COMM]index:21,mc_mode:0xFF
[D][05:18:35][COMM]index:22,S_mode:0xFF
[D][05:18:35][COMM]index:23,overweight:0xFF
[D][05:18:35][COMM]index:24,standstill_mode:0xFF
[D][05:18:35][COMM]index:25,night_mode:0xFF
[D][05:18:35][COMM]index:26,experiment1:0xFF
[D][05:18:35][COMM]index:27,experiment2:0xFF
[D][05:18:35][COMM]index:28,experiment3:0xFF
[D][05:18:35][COMM]index:29,experiment4:0xFF
[D][05:18:35][COMM]index:30,night_mode_start:0xFF
[D][05:18:35][COMM]index:31,night_mode_end:0xFF
[D][05:18:35][COMM]index:33,park_report_minutes:0xFF
[D][05:18:35][COMM]index:34,park_report_mode:0xFF
[D][05:18:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:35][COMM]index:38,charge_battery_para: FF
[D][05:18:35][COMM]index:39,multirider_mode:0xFF
[D][05:18:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:35][COMM]index:44,riding_duration_config:0xFF
[D][05:18:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:35][COMM]index:47,bat_in

2025-07-31 20:07:09:732 ==>> fo_rep_cfg:0xFF
[D][05:18:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:35][COMM]index:49,mc_load_startup:0xFF
[D][05:18:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:35][COMM]index:52,traffic_mode:0xFF
[D][05:18:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:35][COMM]index:63,experiment5:0xFF
[D][05:18:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:35][COMM]i

2025-07-31 20:07:09:837 ==>> ndex:72,experiment6:0xFF
[D][05:18:35][COMM]index:73,experiment7:0xFF
[D][05:18:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:35][COMM]index:75,zero_value_from_server:-1
[D][05:18:35][COMM]index:76,multirider_threshold:255
[D][05:18:35][COMM]index:77,experiment8:255
[D][05:18:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:35][COMM]index:83,loc_report_interval:255
[D][05:18:35][COMM]index:84,multirider_threshold_p2:255
[D][05:18:35][COMM]index:85,multirider_strategy:255
[D][05:18:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:35][COMM]index:90,weight_param:0xFF
[D][05:18:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:35][COMM]index:95,current_limit:0xFF
[D][05:18:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:35][COMM]index:100,location_mode:0xFF

[W][05:18:35][PROT]remove success[16299

2025-07-31 20:07:09:943 ==>> 55115],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[4205],priority[0],index[2],used[1]
[D][05:18:35][COMM]Main Task receive event:131 finished processing
[D][05:18:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:35][GNSS]recv submsg id[3]
[D][05:18:35][COMM]read battery soc:255
$GBGGA,120709.005,2301.2580557,N,11421.9417836,E,1,11,1.16,70.377,M,-1.770,M,,*51

$GBGSA,A,3,33,24,42,39,59,13,01,60,25,38,40,,3.64,1.16,3.45,4*06

$GBGSV,7,1,25,14,81,218,40,33,62,310,42,3,61,191,40,24,59,359,41,1*4D

$GBGSV,7,2,25,42,56,158,41,16,52,17,37,39,52,2,39,6,51,29,35,1*40

$GBGSV,7,3,25,59,51,128,41,13,49,221,38,1,46,124,38,2,46,238,37,1*74

$GBGSV,7,4,25,60,43,241,40,9,38,312,35,25,38,266,41,7,36,184,34,1*77

$GBGSV,7,5,25,38,34,191,37,21,33,326,31,4,32,112,33,8,29,205,35,1*71

$GBGSV,7,6,25,40,29,160,36,10,26,193,32,5,22,257,34,26,18,227,36,1*4F

$GBGSV,7,7,25,41,,,34,1*73

$GBGSV,2,1,07,33,62,310,42,24,59,359,42,42,56,158,42,39,52,2,39,5*7F

$GBGSV,2,2,07,25,38,266,39,38,34,191,37,40,29,160,33,5*48

$GBRMC,120709.005,A,2301.2580557,N,11421.9

2025-07-31 20:07:09:972 ==>> 417836,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120709.005,1.251,0.246,0.257,0.425,1.517,1.803,7.135*71



2025-07-31 20:07:10:002 ==>>                                                                                                   

2025-07-31 20:07:10:107 ==>>                     >>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:36][CAT1]gsm read msg sub id: 12
[D][05:18:36][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:36][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:36][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:07:10:321 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:07:10:334 ==>> 检测【配置蓝牙地址】
2025-07-31 20:07:10:344 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:07:10:363 ==>> $GBGGA,120710.000,2301.2579768,N,11421.9416547,E,1,12,1.10,71.373,M,-1.770,M,,*5E

$GBGSA,A,3,14,33,24,42,39,59,13,01,60,25,38,40,2.90,1.10,2.68,4*01

$GBGSV,7,1,25,14,81,218,40,33,62,310,42,3,61,191,40,24,59,359,41,1*4D

$GBGSV,7,2,25,42,56,158,41,16,52,17,37,39,52,2,38,6,51,29,35,1*41

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,1,46,124,37,2,46,238,37,1*7A

$GBGSV,7,4,25,60,43,241,40,9,38,312,35,25,38,266,40,7,36,184,34,1*76

$GBGSV,7,5,25,38,34,191,37,21,33,326,31,4,32,112,33,8,29,205,35,1*71

$GBGSV,7,6,25,40,29,160,36,10,26,193,32,5,22,257,34,26,18,227,35,1*4C

$GBGSV,7,7,25,41,,,34,1*73

$GBGSV,2,1,07,33,62,310,43,24,59,359,43,42,56,158,42,39,52,2,40,5*71

$GBGSV,2,2,07,25,38,266,39,38,34,191,37,40,29,160,34,5*4F

$GBRMC,120710.000,A,2301.2579768,N,11421.9416547,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120710.000,1.558,0.513,0.532,0.860,1.524,1.779,5.889*72



2025-07-31 20:07:10:529 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:CD5E73B20727>】
2025-07-31 20:07:10:574 ==>> [W][05:18:37][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:37][COMM]48344 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:07:10:740 ==>> recv ble 1
recv ble 2
ble set mac ok :cd,5e,73,b2,7,27
enable filters ret : 0

2025-07-31 20:07:10:805 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:07:10:811 ==>> 检测【BLETEST】
2025-07-31 20:07:10:820 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:07:10:937 ==>> 4A A4 01 A4 4A 


2025-07-31 20:07:11:042 ==>> recv ble 1
recv ble 2
<BSJ*MAC:CD5E73B20727*RSSI:-20*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9CD5E73B2072799999OVER 150


2025-07-31 20:07:11:455 ==>> [D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:37][COMM]accel parse set 0
[D][05:18:37][COMM][Audio].l:[1012].open hexlog save
[D][05:18:37][COMM]read battery soc:255
[D][05:18:37][PROT]CLEAN,SEND:0
[D][05:18:37][PROT]index:0 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:4
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:1
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900005]
[D][05:18:37][PROT]

2025-07-31 20:07:11:560 ==>> Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B5B0BC4FB6737DDE084163479E67F4C87AF4A71E7DE0603DE089CE11B69D21C0DC2D5157BB69FC961600CC6778239CBA85CF68016056D2D369D7FED0596C80E781E93D589631397795E3C7E64E3CEF0A270BCB
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,120711.000,2301.2578380,N,11421.9415587,E,1,14,1.01,72.720,M,-1.770,M,,*54

$GBGSA,A,3,14,33,24,42,16,39,59,13,09,01,60,25,2.78,1.01,2.

2025-07-31 20:07:11:665 ==>> 60,4*0E

$GBGSA,A,3,38,40,,,,,,,,,,,2.78,1.01,2.60,4*0E

$GBGSV,7,1,25,14,81,218,40,33,62,310,42,3,61,191,40,24,59,359,41,1*4D

$GBGSV,7,2,25,42,56,158,41,16,53,342,37,39,52,2,38,6,51,29,35,1*73

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,37,60,43,241,40,25,38,266,40,7,36,184,34,1*7F

$GBGSV,7,5,25,38,34,191,38,21,33,326,31,4,32,112,34,8,29,205,35,1*79

$GBGSV,7,6,25,40,29,160,36,10,26,193,32,5,22,257,34,26,18,227,35,1*4C

$GBGSV,7,7,25,41,,,35,1*72

$GBGSV,2,1,07,33,62,310,43,24,59,359,43,42,56,158,43,39,52,2,40,5*70

$GBGSV,2,2,07,25,38,266,40,38,34,191,37,40,29,160,34,5*41

$GBRMC,120711.000,A,2301.2578380,N,11421.9415587,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
$GBGST,120711.000,1.529,0.195,0.192,0.304,1.398,1.630,5.118*74

[D][05:18:38][PROT]M2M Send ok [1629955118]
                                      

2025-07-31 20:07:11:835 ==>> 【BLETEST】通过,【-20dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:07:11:845 ==>> 该项需要延时执行
2025-07-31 20:07:12:375 ==>> $GBGGA,120712.000,2301.2578156,N,11421.9415062,E,1,18,0.94,73.186,M,-1.770,M,,*5A

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,2.29,0.94,2.09,4*0C

$GBGSA,A,3,60,25,38,07,40,10,,,,,,,2.29,0.94,2.09,4*0F

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,158,41,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,36,60,43,241,40,25,38,266,40,38,34,191,37,1*47

$GBGSV,7,5,25,21,33,326,31,7,33,175,34,4,32,112,34,8,29,205,35,1*44

$GBGSV,7,6,25,40,29,160,37,10,25,186,32,5,22,257,34,26,18,227,36,1*49

$GBGSV,7,7,25,41,,,35,1*72

$GBGSV,2,1,07,33,62,310,43,24,59,359,43,42,56,158,42,39,52,2,40,5*71

$GBGSV,2,2,07,25,38,266,40,38,34,191,37,40,29,160,34,5*41

$GBRMC,120712.000,A,2301.2578156,N,11421.9415062,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120712.000,1.482,0.226,0.215,0.340,1.299,1.508,4.561*73



2025-07-31 20:07:13:120 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:07:13:363 ==>> $GBGGA,120713.000,2301.2578137,N,11421.9415042,E,1,19,0.77,73.235,M,-1.770,M,,*59

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.69,0.77,1.51,4*08

$GBGSA,A,3,60,25,38,07,40,10,26,,,,,,1.69,0.77,1.51,4*0F

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,158,41,6,53,338,35,16,53,342,37,39,52,2,39,1*43

$GBGSV,7,3,25,59,51,128,41,13,49,221,38,9,49,314,35,1,46,124,38,1*7D

$GBGSV,7,4,25,2,46,238,37,60,43,241,41,25,38,266,41,38,34,191,38,1*49

$GBGSV,7,5,25,21,33,326,32,7,33,175,34,4,32,112,34,8,29,205,35,1*47

$GBGSV,7,6,25,40,29,160,37,10,25,186,32,5,22,257,34,26,20,43,36,1*72

$GBGSV,7,7,25,41,,,35,1*72

$GBGSV,2,1,07,33,62,310,43,24,59,359,43,42,56,158,42,39,52,2,40,5*71

$GBGSV,2,2,07,25,38,266,40,38,34,191,37,40,29,160,34,5*41

$GBRMC,120713.000,A,2301.2578137,N,11421.9415042,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120713.000,1.332,0.260,0.236,0.373,1.133,1.321,4.006*77



2025-07-31 20:07:14:383 ==>> $GBGGA,120714.000,2301.2578174,N,11421.9415549,E,1,19,0.77,73.228,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.69,0.77,1.51,4*08

$GBGSA,A,3,60,25,38,07,40,10,26,,,,,,1.69,0.77,1.51,4*0F

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,41,1*4D

$GBGSV,7,2,25,42,56,158,41,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,36,60,43,241,40,25,38,266,40,38,34,191,38,1*48

$GBGSV,7,5,25,21,33,326,32,7,33,175,34,4,32,112,33,8,29,205,35,1*40

$GBGSV,7,6,25,40,29,160,37,10,25,186,32,5,22,257,34,26,20,43,36,1*72

$GBGSV,7,7,25,41,,,35,1*72

$GBGSV,2,1,08,33,62,310,43,24,59,359,43,42,56,158,43,39,52,2,40,5*7F

$GBGSV,2,2,08,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*4A

$GBRMC,120714.000,A,2301.2578174,N,11421.9415549,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120714.000,1.618,0.245,0.223,0.353,1.314,1.466,3.725*7E



2025-07-31 20:07:15:116 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:07:15:374 ==>> $GBGGA,120715.000,2301.2578483,N,11421.9416010,E,1,21,0.75,73.017,M,-1.770,M,,*5A

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.48,0.75,1.28,4*07

$GBGSA,A,3,08,60,25,38,07,40,10,26,21,,,,1.48,0.75,1.28,4*0B

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,158,41,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,37,8,45,206,35,60,43,241,40,25,38,266,41,1*7D

$GBGSV,7,5,25,38,34,191,38,7,33,175,34,4,32,112,33,40,29,160,37,1*75

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,2,1,08,33,62,310,43,24,59,359,43,42,56,158,42,39,52,2,40,5*7E

$GBGSV,2,2,08,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*4A

$GBRMC,120715.000,A,2301.2578483,N,11421.9416010,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,120715.000,1.957,0.235,0.215,0.326,1.531,1.656,3.612*7E



2025-07-31 20:07:15:633 ==>> +WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,F42A7D1297A3,-59
+WIFISCAN:4,2,F62A7D2297A3,-61
+WIFISCAN:4,3,CC057790A740,-66

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 20:07:16:079 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 20:07:16:599 ==>> $GBGGA,120716.000,2301.2578284,N,11421.9415907,E,1,22,0.69,73.296,M,-1.770,M,,*51

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,158,41,6,53,338,35,16,53,342,37,39,52,2,39,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,37,1*73

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,41,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,37,1*7A

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,36,41,13,323,35,1*7D

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,158,42,39,52,2,40,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,26,21,7,148,31,5*49

$GBRMC,120716.000,A,2301.2578284,N,11421.9415907,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,120716.000,2.882,0.386,0.343,0.491,2.091,2.184,3.775*70

[D][05:18:43][PROT]CLEAN,SEND:0
[D][05:18:43][PROT]CLEAN:0
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][P

2025-07-31 20:07:16:704 ==>> ROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:3
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A3C89C8906980220
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[134]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:43][CAT1]Send Data To Server[134][137] ... ->:
0043B68C113311331133113311331B88B317F218E65D6109C45A6B769

2025-07-31 20:07:16:779 ==>> 34C27DF3838390D3AE2159A39342458D67B75771EE26375BDF89B20053E4ACB024D09EC80FD87
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 20:07:17:134 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:07:17:375 ==>> $GBGGA,120717.000,2301.2578227,N,11421.9415793,E,1,22,0.69,73.394,M,-1.770,M,,*59

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,159,41,6,53,338,35,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,41,13,49,221,38,9,49,314,35,1,46,124,38,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,41,25,38,266,41,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,37,1*7A

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,42,39,52,2,41,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,27,21,7,148,32,5*4B

$GBRMC,120717.000,A,2301.2578227,N,11421.9415793,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120717.000,2.997,0.270,0.247,0.349,2.146,2.228,3.666*76



2025-07-31 20:07:18:399 ==>> $GBGGA,120718.000,2301.2578260,N,11421.9415852,E,1,22,0.69,73.364,M,-1.770,M,,*58

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,159,41,6,53,338,35,16,53,342,37,39,52,2,39,1*42

$GBGSV,7,3,25,59,51,128,41,13,49,221,38,9,49,314,35,1,46,124,38,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,36,60,43,241,41,25,38,266,41,1*7E

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,37,1*7A

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120718.000,A,2301.2578260,N,11421.9415852,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,120718.000,3.191,0.223,0.208,0.292,2.246,2.319,3.635*7A



2025-07-31 20:07:19:143 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:07:19:399 ==>> $GBGGA,120719.000,2301.2578276,N,11421.9415891,E,1,22,0.69,73.347,M,-1.770,M,,*50

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,159,41,6,53,338,35,16,53,342,38,39,52,2,39,1*4D

$GBGSV,7,3,25,59,51,128,41,13,49,221,38,9,49,314,35,1,46,124,38,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,41,25,38,266,41,1*7D

$GBGSV,7,5,25,38,34,191,38,7,33,175,34,4,32,112,33,40,29,160,37,1*75

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,36,41,13,323,35,1*7D

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,42,39,52,2,40,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120719.000,A,2301.2578276,N,11421.9415891,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120719.000,3.268,0.248,0.228,0.324,2.281,2.347,3.568*76



2025-07-31 20:07:20:399 ==>> $GBGGA,120720.000,2301.2578291,N,11421.9415952,E,1,22,0.69,73.409,M,-1.770,M,,*50

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,159,41,6,53,338,35,16,53,342,38,39,52,2,38,1*4C

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,38,7,33,175,34,4,32,112,33,40,29,160,36,1*74

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,36,41,13,323,35,1*7D

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,42,39,52,2,40,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,32,5*45

$GBRMC,120720.000,A,2301.2578291,N,11421.9415952,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,120720.000,3.332,0.229,0.211,0.301,2.310,2.371,3.515*7E



2025-07-31 20:07:20:594 ==>> [D][05:18:47][COMM]IMU: [5,7,-1048] ret=38 AWAKE!


2025-07-31 20:07:21:145 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:07:21:401 ==>> $GBGGA,120721.000,2301.2578251,N,11421.9416104,E,1,22,0.69,73.467,M,-1.770,M,,*5D

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,42,24,59,359,41,1*4E

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,41,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,34,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,33,5*44

$GBRMC,120721.000,A,2301.2578251,N,11421.9416104,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,120721.000,3.090,0.238,0.218,0.312,2.173,2.231,3.342*79



2025-07-31 20:07:21:779 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]index:1 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:5
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A3C89C8906980220
[D][05:18:48][PROT]sending traceid [9999999999900006]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[134]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18

2025-07-31 20:07:21:809 ==>> :48][CAT1]<<< 
ERROR



2025-07-31 20:07:21:839 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:07:21:846 ==>> 检测【检测WiFi结果】
2025-07-31 20:07:21:853 ==>> WiFi信号:【F42A7D1297A3】,信号值:-62
2025-07-31 20:07:21:873 ==>> WiFi信号:【CC057790A740】,信号值:-66
2025-07-31 20:07:21:879 ==>> WiFi信号:【CC057790A741】,信号值:-66
2025-07-31 20:07:21:888 ==>> WiFi信号:【44A1917CAD81】,信号值:-70
2025-07-31 20:07:21:904 ==>> WiFi信号:【F88C21BCF57D】,信号值:-35
2025-07-31 20:07:21:914 ==>> WiFi信号:【74C330CCAB10】,信号值:-67
2025-07-31 20:07:21:936 ==>> WiFi信号:【CC057790A640】,信号值:-67
2025-07-31 20:07:21:947 ==>> WiFi信号:【F62A7D2297A3】,信号值:-61
2025-07-31 20:07:21:975 ==>> WiFi数量【8】, 最大信号值:-35
2025-07-31 20:07:21:982 ==>> 检测【检测GPS结果】
2025-07-31 20:07:22:009 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 20:07:22:021 ==>> 
北斗星号:【14】,信号值:【40】
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【40】
北斗星号:【24】,信号值:【41】
北斗星号:【42】,信号值:【41】
北斗星号:【59】,信号值:【40】
北斗星号:【16】,信号值:【37】
北斗星号:【39】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【13】,信号值:【38】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【37】
北斗星号:【60】,信号值:【40】
北斗星号:【9】,信号值:【35】
北斗星号:【25】,信号值:【39】
北斗星号:【38】,信号值:【36】
北斗星号:【8】,信号值:【35】
北斗星号:【26】,信号值:【35】

2025-07-31 20:07:22:051 ==>> 检测【CSQ强度】
2025-07-31 20:07:22:081 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:07:22:090 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:07:22:400 ==>> $GBGGA,120722.000,2301.2578386,N,11421.9416203,E,1,22,0.69,73.425,M,-1.770,M,,*57

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,37,1*73

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,41,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,33,5*44

$GBRMC,120722.000,A,2301.2578386,N,11421.9416203,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,120722.000,3.112,0.214,0.199,0.284,2.181,2.236,3.297*78



2025-07-31 20:07:23:137 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:07:23:377 ==>> $GBGGA,120723.000,2301.2578486,N,11421.9416357,E,1,22,0.69,73.421,M,-1.770,M,,*55

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,37,1*73

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,41,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,35,26,20,43,36,5*43

$GBGSV,3,3,10,41,13,323,28,21,7,148,33,5*45

$GBRMC,120723.000,A,2301.2578486,N,11421.9416357,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120723.000,3.202,0.232,0.214,0.305,2.228,2.279,3.291*7C



2025-07-31 20:07:23:930 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:07:24:113 ==>> [D][05:18:50][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:50][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:07:24:385 ==>> $GBGGA,120724.000,2301.2578644,N,11421.9416430,E,1,22,0.69,73.395,M,-1.770,M,,*50

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,37,1*73

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,33,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,32,5*45

$GBRMC,120724.000,A,2301.2578644,N,11421.9416430,E,0.004,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,120724.000,3.062,0.240,0.220,0.314,2.147,2.196,3.182*74



2025-07-31 20:07:25:157 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:07:25:397 ==>> $GBGGA,120725.000,2301.2578729,N,11421.9416446,E,1,22,0.69,73.376,M,-1.770,M,,*57

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,40,1*4C

$GBGSV,7,2,25,42,56,159,41,6,53,338,35,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,38,9,49,314,35,1,46,124,38,1*7C

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,34,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,40,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120725.000,A,2301.2578729,N,11421.9416446,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,120725.000,3.095,0.266,0.242,0.347,2.164,2.210,3.163*78



2025-07-31 20:07:25:994 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:07:26:386 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+CSQ<<<<<
$GBGGA,120726.000,2301.2578719,N,11421.9416517,E,1,22,0.69,73.385,M,-1.770,M,,*5E

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,218,40,3,63,190,40,33,62,310,41,24,59,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,314,35,1,46,124,37,1*7C

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,34,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,33,5*45

$GBRMC,120726.000,A,2301.2578719,N,11421.9416517,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120726.000,3.161,0.203,0.190,0.269,2.198,2.242,3.163*77



2025-07-31 20:07:27:161 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:07:27:400 ==>> $GBGGA,120727.000,2301.2578670,N,11421.9416621,E,1,22,0.69,73.397,M,-1.770,M,,*54

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,39,3,63,190,40,33,62,310,41,24,59,359,40,1*4D

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,314,35,1,46,124,37,1*7C

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,33,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,59,359,43,42,56,159,43,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,33,5*44

$GBRMC,120727.000,A,2301.2578670,N,11421.9416621,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120727.000,3.054,0.230,0.212,0.304,2.137,2.179,3.083*73



2025-07-31 20:07:28:057 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:07:28:407 ==>> $GBGGA,120728.000,2301.2578677,N,11421.9416763,E,1,22,0.69,73.397,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,38,1*72

$GBGSV,7,4,25,2,46,238,37,8,45,206,35,60,43,241,40,25,38,266,40,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,34,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,41,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,29,21,7,148,32,5*45

$GBRMC,120728.000,A,2301.2578677,N,11421.9416763,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,120728.000,2.970,0.223,0.206,0.295,2.087,2.128,3.015*7D

[W][05:18:55][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:07:29:167 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:07:29:407 ==>> $GBGGA,120729.000,2301.2578709,N,11421.9416858,E,1,22,0.69,73.366,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,40,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120729.000,A,2301.2578709,N,11421.9416858,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,120729.000,2.986,0.273,0.247,0.355,2.094,2.134,2.999*7B



2025-07-31 20:07:30:134 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:07:30:438 ==>> $GBGGA,120730.000,2301.2578662,N,11421.9416896,E,1,22,0.69,73.390,M,-1.770,M,,*54

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,42,39,52,2,40,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120730.000,A,2301.2578662,N,11421.9416896,E,0.003,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,120730.000,2.795,0.234,0.215,0.307,1.982,2.021,2.879*7B

[W][05:18:57][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:07:31:376 ==>> [D][05:18:57][COMM]read battery soc:255
$GBGGA,120731.000,2301.2578630,N,11421.9416900,E,1,22,0.69,73.442,M,-1.770,M,,*54

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,39,3,63,190,40,33,62,310,41,24,58,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,42,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,35,26,20,43,36,5*43

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120731.000,A,2301.2578630,N,11421.9416900,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,120731.000,2.757,0.255,0.233,0.333,1.958,1.996,2.839*75



2025-07-31 20:07:31:935 ==>> [D][05:18:58][CAT1]exec over: func id: 15, ret: -93
[D][05:18:58][CAT1]sub id: 15, ret: -93

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:58][SAL ]socket send fail. id[4]
[D][05:18:58][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][M2M ]m2m select fd[4]
[D][05:18:58][M2M ]socket[4] Link is disconnected
[D][05:18:58][M2M ]tcpclient close[4]
[D][05:18:58][SAL ]socket[4] has closed
[D][05:18:58][PROT]protocol read data ok
[E][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:58][PROT]M2M Send Fail [1629955138]
[D][05:18:58][PROT]CLEAN,SEND:1
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:18:58][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][

2025-07-31 20:07:32:025 ==>> 05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][05:18:58][CAT1]gsm read msg sub id: 10
[D][05:18:58][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:58][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:58][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:07:32:293 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 20:07:32:302 ==>> 检测【关闭GSM联网】
2025-07-31 20:07:32:313 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:07:32:417 ==>> [D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 10, ret: 6
[D][05:18:58][CAT1]sub id: 10, ret: 6

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:18:58][M2M ]m2m gsm shut done, ret[0]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:58][SAL ]open socket ind id[4], rst[0]
[D][05:18:58][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:58][SAL ]Cellular task submsg id[8]
[D][05:18:58][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:18:58][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:58][CAT1]gsm read msg sub id: 8
[D][05:18:58][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:59][CAT1]<<< 
+CGATT: 0

OK

$GBGGA,120732.000,2301.2578706,N,11421.9416916,E,1,22,0.69,73.418,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,15

2025-07-31 20:07:32:507 ==>> 9,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,36,41,13,323,35,1*7D

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,40,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120732.000,A,2301.2578706,N,11421.9416916,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:59][CAT1]tx ret[12] >>> AT+CGATT=1

$GBGST,120732.000,2.716,0.201,0.187,0.267,1.932,1.970,2.797*71



2025-07-31 20:07:32:612 ==>>                                          [W][05:18:59][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:59][COMM]GSM test
[D][05:18:59][COMM]GSM test disable


2025-07-31 20:07:32:826 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:07:32:834 ==>> 检测【4G联网测试】
2025-07-31 20:07:32:842 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:07:33:775 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:59][COMM]Main Task receive event:14
[D][05:18:59][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955139, allstateRepSeconds = 0
[D][05:18:59][COMM]index:0,power_mode:0xFF
[D][05:18:59][COMM]index:1,sound_mode:0xFF
[D][05:18:59][COMM]index:2,gsensor_mode:0xFF
[D][05:18:59][COMM]index:3,report_freq_mode:0xFF
[D][05:18:59][COMM]index:4,report_period:0xFF
[D][05:18:59][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:59][COMM]index:6,normal_reset_period:0xFF
[D][05:18:59][COMM]index:7,spock_over_speed:0xFF
[D][05:18:59][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:59][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:59][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:59][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:59][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:59][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:59][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:59][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:59][COMM]index:16,imu_config_params:0xFF
[D][05:18:59][COMM]index:17,long_connect_params:0xFF
[D][05:18:59][COMM]index

2025-07-31 20:07:33:881 ==>> :18,detain_mark:0xFF
[D][05:18:59][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:59][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:59][COMM]index:21,mc_mode:0xFF
[D][05:18:59][COMM]index:22,S_mode:0xFF
[D][05:18:59][COMM]index:23,overweight:0xFF
[D][05:18:59][COMM]index:24,standstill_mode:0xFF
[D][05:18:59][COMM]index:25,night_mode:0xFF
[D][05:18:59][COMM]index:26,experiment1:0xFF
[D][05:18:59][COMM]index:27,experiment2:0xFF
[D][05:18:59][COMM]index:28,experiment3:0xFF
[D][05:18:59][COMM]index:29,experiment4:0xFF
[D][05:18:59][COMM]index:30,night_mode_start:0xFF
[D][05:18:59][COMM]index:31,night_mode_end:0xFF
[D][05:18:59][COMM]index:33,park_report_minutes:0xFF
[D][05:18:59][COMM]index:34,park_report_mode:0xFF
[D][05:18:59][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:59][COMM]index:38,charge_battery_para: FF
[D][05:18:59][COMM]index:39,multirider_mode:0xFF
[D][05:18:59][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:59][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:59][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:59][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:59][COMM]index:44,riding_duration_config:0xFF
[D][05:18:59][C

2025-07-31 20:07:33:986 ==>> OMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:59][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:59][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:59][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:59][COMM]index:49,mc_load_startup:0xFF
[D][05:18:59][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:59][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:59][COMM]index:52,traffic_mode:0xFF
[D][05:18:59][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:59][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:59][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:59][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:59][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:59][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:59][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:59][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:59][COMM]index:63,experiment5:0xFF
[D][05:18:59][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:59][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:59][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:59][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:59][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:59]

2025-07-31 20:07:34:091 ==>> [COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:59][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:59][COMM]index:72,experiment6:0xFF
[D][05:18:59][COMM]index:73,experiment7:0xFF
[D][05:18:59][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:59][COMM]index:75,zero_value_from_server:-1
[D][05:18:59][COMM]index:76,multirider_threshold:255
[D][05:18:59][COMM]index:77,experiment8:255
[D][05:18:59][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:59][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:59][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:59][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:59][COMM]index:83,loc_report_interval:255
[D][05:18:59][COMM]index:84,multirider_threshold_p2:255
[D][05:18:59][COMM]index:85,multirider_strategy:255
[D][05:18:59][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:59][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:59][COMM]index:90,weight_param:0xFF
[D][05:18:59][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:59][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:59][COMM]index:95,current_limit:0xFF
[D][05:18:59][COMM]index:97,panel display thr

2025-07-31 20:07:34:196 ==>> eshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:59][COMM]index:100,location_mode:0xFF

[W][05:18:59][PROT]remove success[1629955139],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:59][PROT]add success [1629955139],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:59][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:59][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:59][COMM]read battery soc:255
$GBGGA,120733.000,2301.2578754,N,11421.9416948,E,1,22,0.69,73.393,M,-1.770,M,,*52

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,39,33,62,310,41,24,58,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,33,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,42,39,52,2,41,5*77

$GBGSV,3

2025-07-31 20:07:34:271 ==>> ,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120733.000,A,2301.2578754,N,11421.9416948,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120733.000,2.613,0.222,0.205,0.293,1.869,1.906,2.724*71

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:00][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:00][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 20:07:34:376 ==>>                    

2025-07-31 20:07:34:482 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:07:34:586 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:07:34:691 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:07:34:796 ==>>                                                                                                                                                                           ,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,42,39,52,2,41,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120734.000,A,2301.2578807,N,11421.9416994,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,120734.000,2.739,0.239,0.219,0.314,1.944,1.979,2.777*76



2025-07-31 20:07:35:385 ==>> [D][05:19:01][COMM]read battery soc:255
$GBGGA,120735.000,2301.2578888,N,11421.9417106,E,1,22,0.69,73.396,M,-1.770,M,,*5C

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,41,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120735.000,A,2301.2578888,N,11421.9417106,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,120735.000,2.715,0.213,0.198,0.282,1.929,1.963,2.750*70



2025-07-31 20:07:36:403 ==>> $GBGGA,120736.000,2301.2578978,N,11421.9417088,E,1,22,0.69,73.389,M,-1.770,M,,*58

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,38,1*72

$GBGSV,7,4,25,2,46,238,37,8,45,206,35,60,43,241,40,25,38,266,40,1*7C

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,33,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,33,26,20,43,35,41,13,323,35,1*79

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,41,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120736.000,A,2301.2578978,N,11421.9417088,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120736.000,2.639,0.197,0.185,0.261,1.882,1.915,2.695*7B



2025-07-31 20:07:37:409 ==>> [D][05:19:03][COMM]read battery soc:255
$GBGGA,120737.000,2301.2578973,N,11421.9417096,E,1,22,0.69,73.399,M,-1.770,M,,*5C

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,40,33,62,310,41,24,58,359,40,1*42

$GBGSV,7,2,25,42,56,159,40,6,53,338,34,16,53,342,37,39,52,2,38,1*43

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,33,4,32,112,33,40,29,160,36,1*7C

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,31,1*7A

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,40,5*77

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120737.000,A,2301.2578973,N,11421.9417096,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,120737.000,2.876,0.231,0.213,0.303,2.023,2.054,2.812*77



2025-07-31 20:07:38:408 ==>> $GBGGA,120738.000,2301.2578968,N,11421.9417056,E,1,22,0.69,73.412,M,-1.770,M,,*51

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.23,0.69,1.02,4*0F

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,21,,,1.23,0.69,1.02,4*06

$GBGSV,7,1,25,14,81,217,40,3,63,190,39,33,62,310,41,24,58,359,40,1*4C

$GBGSV,7,2,25,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,7,3,25,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,7,4,25,2,46,238,36,8,45,206,35,60,43,241,40,25,38,266,40,1*7D

$GBGSV,7,5,25,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,7,6,25,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,35,1*7E

$GBGSV,7,7,25,21,7,148,32,1*79

$GBGSV,3,1,10,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,41,5*76

$GBGSV,3,2,10,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*42

$GBGSV,3,3,10,41,13,323,28,21,7,148,32,5*44

$GBRMC,120738.000,A,2301.2578968,N,11421.9417056,E,0.000,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,120738.000,2.901,0.201,0.188,0.268,2.037,2.067,2.814*74



2025-07-31 20:07:38:868 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:07:39:905 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:05][COMM]Main Task receive event:14
[D][05:19:05][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955145, allstateRepSeconds = 0
[D][05:19:05][COMM]index:0,power_mode:0xFF
[D][05:19:05][COMM]index:1,sound_mode:0xFF
[D][05:19:05][COMM]index:2,gsensor_mode:0xFF
[D][05:19:05][COMM]index:3,report_freq_mode:0xFF
[D][05:19:05][COMM]index:4,report_period:0xFF
[D][05:19:05][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:05][COMM]index:6,normal_reset_period:0xFF
[D][05:19:05][COMM]index:7,spock_over_speed:0xFF
[D][05:19:05][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:05][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:05][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:05][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:05][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:05][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:05][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:05][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:05][COMM]index:16,imu_config_params:0xFF
[D][05:19:05][COMM]index:17,long_connect_params:0xFF
[D][05:19:05][COMM]index:18,detain_mark:0xFF
[D][05:19:05][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:05][COMM]index:20,lock_pos_

2025-07-31 20:07:40:010 ==>> report_interval:0xFF
[D][05:19:05][COMM]index:21,mc_mode:0xFF
[D][05:19:05][COMM]index:22,S_mode:0xFF
[D][05:19:05][COMM]index:23,overweight:0xFF
[D][05:19:05][COMM]index:24,standstill_mode:0xFF
[D][05:19:05][COMM]index:25,night_mode:0xFF
[D][05:19:05][COMM]index:26,experiment1:0xFF
[D][05:19:05][COMM]index:27,experiment2:0xFF
[D][05:19:05][COMM]index:28,experiment3:0xFF
[D][05:19:05][COMM]index:29,experiment4:0xFF
[D][05:19:05][COMM]index:30,night_mode_start:0xFF
[D][05:19:05][COMM]index:31,night_mode_end:0xFF
[D][05:19:05][COMM]index:33,park_report_minutes:0xFF
[D][05:19:05][COMM]index:34,park_report_mode:0xFF
[D][05:19:05][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:05][COMM]index:38,charge_battery_para: FF
[D][05:19:05][COMM]index:39,multirider_mode:0xFF
[D][05:19:05][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:05][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:05][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:05][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:05][COMM]index:44,riding_duration_config:0xFF
[D][05:19:05][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:05][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:05][C

2025-07-31 20:07:40:115 ==>> OMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:05][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:05][COMM]index:49,mc_load_startup:0xFF
[D][05:19:05][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:05][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:05][COMM]index:52,traffic_mode:0xFF
[D][05:19:05][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:05][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:05][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:05][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:05][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:05][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:05][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:05][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:05][COMM]index:63,experiment5:0xFF
[D][05:19:05][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:05][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:05][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:05][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:05][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:05][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:05][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][

2025-07-31 20:07:40:220 ==>> 05:19:05][COMM]index:72,experiment6:0xFF
[D][05:19:05][COMM]index:73,experiment7:0xFF
[D][05:19:05][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:05][COMM]index:75,zero_value_from_server:-1
[D][05:19:05][COMM]index:76,multirider_threshold:255
[D][05:19:05][COMM]index:77,experiment8:255
[D][05:19:05][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:05][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:05][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:05][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:05][COMM]index:83,loc_report_interval:255
[D][05:19:05][COMM]index:84,multirider_threshold_p2:255
[D][05:19:05][COMM]index:85,multirider_strategy:255
[D][05:19:05][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:05][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:05][COMM]index:90,weight_param:0xFF
[D][05:19:05][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:05][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:05][COMM]index:95,current_limit:0xFF
[D][05:19:05][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:05][COMM]index:100,location_mode:0xFF

[W][05:19:05][PROT]remov

2025-07-31 20:07:40:325 ==>> e success[1629955145],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:05][PROT]add success [1629955145],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:05][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:05][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:05][PROT]index:0 1629955145
[D][05:19:05][PROT]is_send:0
[D][05:19:05][PROT]sequence_num:9
[D][05:19:05][PROT]retry_timeout:0
[D][05:19:05][PROT]retry_times:1
[D][05:19:05][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:05][PROT]===========================================================
[W][05:19:05][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][PROT]sending traceid [999999999990000A]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][CAT1]gsm read msg sub id: 13
[D][05:19:05][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[294]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][

2025-07-31 20:07:40:430 ==>> 05:19:05][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:05][CAT1]exec over: func id: 13, ret: 21
[D][05:19:05][M2M ]get csq[20]
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:05][CAT1]Send Data To Server[294][294] ... ->:
0093B98E113311331133113311331B88BC438205762D6EFC4AFD58674E6C4A50B009AFCED88F888B4243B9B9CB6D5E19C672B19B6A5AD7CD01BFE213FE25D8CF8CB0C811AF5D2FC31902A61A76A869831625E0C4395C39D5F1755B77F5D812C73452EEA438B48CE9682AAAAA20E1B06599EAA814503E99EC415F5BD11741F27C6C5CCCD32E77DD633F79D51E00942339EC0E67
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:05][PROT]M2M Send ok [1629955145]
[D][05:19:06][COMM]read battery soc

2025-07-31 20:07:40:535 ==>> :255
$GBGGA,120739.000,2301.2578922,N,11421.9416976,E,1,21,0.70,73.391,M,-1.770,M,,*53

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,01,1.36,0.70,1.17,4*07

$GBGSA,A,3,08,60,25,38,07,40,10,26,41,,,,1.36,0.70,1.17,4*0D

$GBGSV,6,1,24,14,81,217,40,3,63,190,39,33,62,310,41,24,58,359,40,1*4C

$GBGSV,6,2,24,42,56,159,40,6,53,338,35,16,53,342,37,39,52,2,38,1*42

$GBGSV,6,3,24,59,51,128,40,13,49,221,37,9,49,315,35,1,46,124,37,1*7D

$GBGSV,6,4,24,2,46,238,37,8,45,206,35,60,43,241,40,25,38,266,40,1*7C

$GBGSV,6,5,24,38,34,191,37,7,33,175,34,4,32,112,33,40,29,160,36,1*7B

$GBGSV,6,6,24,10,25,186,32,5,22,257,34,26,20,43,35,41,13,323,36,1*7D

$GBGSV,3,1,09,33,62,310,43,24,58,359,43,42,56,159,43,39,52,2,41,5*7E

$GBGSV,3,2,09,25,38,266,40,38,34,191,37,40,29,160,34,26,20,43,36,5*4A

$GBGSV,3,3,09,41,13,323,28,5*44

$GBRMC,120739.000,A,2301.2578922,N,1142                 

2025-07-31 20:07:40:640 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:07:40:900 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:07:40:908 ==>> 检测【关闭GPS】
2025-07-31 20:07:40:919 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:07:41:321 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:07][GNSS]stop locating
[D][05:19:07][GNSS]stop event:8
[D][05:19:07][GNSS]GPS stop. ret=0
[D][05:19:07][GNSS]all continue location stop
[W][05:19:07][GNSS]stop locating
[D][05:19:07][GNSS]all sing location stop
[D][05:19:07][CAT1]gsm read msg sub id: 24
[D][05:19:07][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][COMM]read battery soc:255
[D][05:19:08][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 24, ret: 6
[D][05:19:08][CAT1]sub id: 24, ret: 6



2025-07-31 20:07:41:439 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:07:41:453 ==>> 检测【清空消息队列2】
2025-07-31 20:07:41:479 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:07:41:628 ==>> [D][05:19:08][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:19:08][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:08][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:07:41:723 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:07:41:731 ==>> 检测【轮动检测】
2025-07-31 20:07:41:738 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:07:41:840 ==>> 3A A3 01 00 A3 


2025-07-31 20:07:41:945 ==>> OFF_OUT1
OVER 150


2025-07-31 20:07:42:005 ==>> [D][05:19:08][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:07:42:186 ==>> [D][05:19:08][GNSS]recv submsg id[1]
[D][05:19:08][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:08][GNSS]location stop evt done evt


2025-07-31 20:07:42:231 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:07:42:338 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:07:42:509 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:07:42:517 ==>> 检测【关闭小电池】
2025-07-31 20:07:42:529 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:07:42:645 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:07:42:785 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:07:42:792 ==>> 检测【进入休眠模式】
2025-07-31 20:07:42:805 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:07:42:917 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:07:43:022 ==>> [D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]main task tmp_sleep_event = 8
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:07:43:235 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 20:07:43:841 ==>> [D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[D][05:19:10][M2M ]tcpclient close[4]
[D][05:19:10][SAL ]Cellular task submsg id[12]
[D][05:19:10][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:10][CAT1]gsm read msg sub id: 9
[D][05:19:10][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 9, ret: 6
[D][05:19:10][CAT1]sub id: 9, ret: 6

[D][05:19:10][SAL ]Cellular task submsg id[68]
[D][05:19:10][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:10][SAL ]socket close ind. id[4]
[D][05:19:10][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:10][COMM]1x1 frm_can_tp_send ok
[D][05:19:10][CAT1]pdpdeact urc len[22]


2025-07-31 20:07:44:142 ==>> [E][05:19:10][COMM]1x1 rx timeout
[D][05:19:10][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:44:660 ==>> [E][05:19:11][COMM]1x1 rx timeout
[E][05:19:11][COMM]1x1 tp timeout
[E][05:19:11][COMM]1x1 error -3.
[W][05:19:11][COMM]CAN STOP!
[D][05:19:11][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:11][COMM]------------ready to Power off Acckey 1------------
[D][05:19:11][COMM]------------ready to Power off Acckey 2------------
[D][05:19:11][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:11][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1289
[D][05:19:11][COMM]bat sleep fail, reason:-1
[D][05:19:11][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:11][COMM]accel parse set 0
[D][05:19:11][COMM]imu rest ok. 82346
[D][05:19:11][COMM]imu sleep 0
[W][05:19:11][COMM]now sleep


2025-07-31 20:07:44:858 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:07:44:867 ==>> 检测【检测33V休眠电流】
2025-07-31 20:07:44:879 ==>> 开始33V电流采样
2025-07-31 20:07:44:910 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:44:960 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:07:45:966 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:07:46:013 ==>> Current33V:????:17.73

2025-07-31 20:07:46:468 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:46:476 ==>> 【检测33V休眠电流】通过,【17.73uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:07:46:484 ==>> 该项需要延时执行
2025-07-31 20:07:48:487 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:07:48:499 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:07:48:524 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:07:48:548 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1654mV
Get AD_V4 1mV
Get AD_V5 2763mV
Get AD_V6 2024mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:07:49:508 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:07:49:516 ==>> 检测【打开小电池2】
2025-07-31 20:07:49:539 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:07:49:639 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:07:49:783 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:07:49:792 ==>> 该项需要延时执行
2025-07-31 20:07:50:292 ==>> 此处延时了:【500】毫秒
2025-07-31 20:07:50:305 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:07:50:327 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:07:50:341 ==>> 5A A5 02 5A A5 


2025-07-31 20:07:50:442 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:07:50:566 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:07:50:574 ==>> 该项需要延时执行
2025-07-31 20:07:50:757 ==>> [D][05:19:17][COMM]------------ready to Power on Acckey 1------------
[D][05:19:17][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:17][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:17][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:17][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:17][COMM]----- get Acckey 1 and value:1------------
[W][05:19:17][COMM]CAN START!
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:17][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 88371
[D][05:19:17][COMM][Audio]exec status ready.
[D][05

2025-07-31 20:07:50:817 ==>> :19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 6
[D][05:19:17][COMM]imu wakeup ok. 88386
[D][05:19:17][COMM]imu wakeup 1
[W][05:19:17][COMM]wake up system, wakeupEvt=0x80
[D][05:19:17][COMM]frm_can_weigth_power_set 1
[D][05:19:17][COMM]Clear Sleep Block Evt
[D][05:19:17][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:50:998 ==>> [E][05:19:17][COMM]1x1 rx timeout
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:51:073 ==>> 此处延时了:【500】毫秒
2025-07-31 20:07:51:090 ==>> 检测【进入休眠模式2】
2025-07-31 20:07:51:113 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:07:51:127 ==>>     05:19:17][COMM]msg 02A0 loss. last_tick:88357. cur_tick:88865. period:50
[D][05:19:17][COMM]msg 02A4 loss. last_tick:88357. cur_tick:88866. period:50
[D][05:19:17][COMM]msg 02A5 loss. last_tick:88357. cur_tick:88866. period:50
[D][05:19:17][COMM]msg 02A6 loss. last_tick:88357. cur_tick:88866. period:50
[D][05:19:17][COMM]m

2025-07-31 20:07:51:148 ==>> sg 02A7 loss. last_tick:88357. cur_tick:88867. period:50
[D][05:19:17][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 88867
[D][05:19:17][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 88867


2025-07-31 20:07:51:223 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:07:51:484 ==>> [E][05:19:18][COMM]1x1 rx timeout
[E][05:19:18][COMM]1x1 tp timeout
[E][05:19:18][COMM]1x1 error -3.
[D][05:19:18][COMM]Main Task receive event:28 finished processing
[D][05:19:18][COMM]Main Task receive event:28
[D][05:19:18][COMM]prepare to sleep
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 6
[D][05:19:18][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:18][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:51:798 ==>> [D][05:19:18][COMM]msg 0220 loss. last_tick:88357. cur_tick:89362. period:100
[D][05:19:18][COMM]msg 0221 loss. last_tick:88357. cur_tick:89362. period:100
[D][05:19:18][COMM]msg 0224 loss. last_tick:88357. cur_tick:89363. period:100
[D][05:19:18][COMM]msg 0260 loss. last_tick:88357. cur_tick:89363. period:100
[D][05:19:18][COMM]msg 0280 loss. last_tick:88357. cur_tick:89363. period:100
[D][05:19:18][COMM]msg 02C0 loss. last_tick:88357. cur_tick:89364. period:100
[D][05:19:18][COMM]msg 02C1 loss. last_tick:88357. cur_tick:89364. period:100
[D][05:19:18][COMM]msg 02C2 loss. last_tick:88357. cur_tick:89365. period:100
[D][05:19:18][COMM]msg 02E0 loss. last_tick:88357. cur_tick:89365. period:100
[D][05:19:18][COMM]msg 02E1 loss. last_tick:88357. cur_tick:89365. period:100
[D][05:19:18][COMM]msg 02E2 loss. last_tick:88357. cur_tick:89366. period:100
[D][05:19:18][COMM]msg 0300 loss. last_tick:88357. cur_tick:89366. period:100
[D][05:19:18][COMM]msg 0301 loss. last_tick:88357. cur_tick:89366. period:100
[D][05:19:18][COMM]bat msg 0240 loss. last_tick:88357. cur_tick:89367. period:100. j,i:1 54
[D][05:19:18][COMM]bat msg 0241 loss. last_tick:88357. cur_tick:89367. per

2025-07-31 20:07:51:873 ==>> iod:100. j,i:2 55
[D][05:19:18][COMM]bat msg 0242 loss. last_tick:88357. cur_tick:89367. period:100. j,i:3 56
[D][05:19:18][COMM]bat msg 0244 loss. last_tick:88357. cur_tick:89368. period:100. j,i:5 58
[D][05:19:18][COMM]bat msg 024E loss. last_tick:88357. cur_tick:89368. period:100. j,i:15 68
[D][05:19:18][COMM]bat msg 024F loss. last_tick:88357. cur_tick:89369. period:100. j,i:16 69
[D][05:19:18][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 89369
[D][05:19:18][COMM]CAN message bat fault change: 0x00000000->0x0001802E 89370
[D][05:19:18][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 89370


2025-07-31 20:07:51:903 ==>>                                                                               

2025-07-31 20:07:52:174 ==>> [D][05:19:18][COMM]msg 0222 loss. last_tick:88357. cur_tick:89864. period:150
[D][05:19:18][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 89865
[D][05:19:18][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:18][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:18][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:07:52:279 ==>> [E][05:19:19][COMM]1x1 rx timeout
[E][05:19:19][COMM]1x1

2025-07-31 20:07:52:369 ==>>  tp timeout
[E][05:19:19][COMM]1x1 error -3.
[D][05:19:19][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:19][COMM]CAN STOP!
[D][05:19:19][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:19][COMM]------------ready to Power off Acckey 1------------
[D][05:19:19][COMM]------------ready to Power off Acckey 2------------
[D][05:19:19][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:19][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 101
[D][05:19:19][COMM]bat sleep fail, reason:-1
[D][05:19:19][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:19][COMM]accel parse set 0
[D][05:19:19][COMM]imu rest ok. 90054
[D][05:19:19][COMM]imu sleep 0
[W][05:19:19][COMM]now sleep


2025-07-31 20:07:52:628 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:07:52:636 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:07:52:648 ==>> 开始小电池电流采样
2025-07-31 20:07:52:666 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:52:731 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:07:53:735 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:07:53:780 ==>> CurrentBattery:ƽ��:69.37

2025-07-31 20:07:54:246 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:54:254 ==>> 【检测小电池休眠电流】通过,【69.37uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:07:54:262 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:07:54:285 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:07:54:336 ==>> 5A A5 01 5A A5 


2025-07-31 20:07:54:441 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:07:54:535 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:07:54:548 ==>> 该项需要延时执行
2025-07-31 20:07:54:666 ==>> [D][05:19:21][COMM]------------ready to Power on Acckey 1------------
[D][05:19:21][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:21][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:21][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:21][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:21][COMM]----- get Acckey 1 and value:1------------
[W][05:19:21][COMM]CAN START!
[D][05:19:21][GNSS]handler GSMGet Base timeout
[D][05:19:21][COMM]read battery soc:0
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:21][COMM]CAN message bat fault change: 0x0001802E->0x00000000 92322
[D][05:19:21][COMM][Audio]exec status ready.
[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 6
[D][05:19:21][COMM]imu wakeup ok. 92336
[D][05:19:21][COMM]imu wakeup 1
[W][05:19:21][COMM]wake up system, wakeupEvt=0x80
[D][05:19:21][COMM]frm_can_weigth_power_set 1
[D][05:19:21][COMM]Clear Sleep Block Evt
[D][05:19:21][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:21][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:54:936 ==>> [E][05:19:21][COMM]1x1 rx timeout
[D][05:19:21][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:07:55:041 ==>> 此处延时了:【500】毫秒
2025-07-31 20:07:55:054 ==>> 检测【检测唤醒】
2025-07-31 20:07:55:080 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:07:55:095 ==>> [D][05:19:21][COMM]msg 02A0 loss. last_tick:92304. cur_tick:92815. period:50
[D

2025-07-31 20:07:55:122 ==>> ][05:19:21][COMM]msg 02A4 loss. last_tick:92304. cur_tick:92816. period:50
[D][05:19:21][COMM]msg 02A5 loss. last_tick:92304. cur_tick:92816. period:50
[D][05:19:21][COMM]msg 02A6 loss. last_tick:92304. cur_tick:92817. period:50
[D][05:19:21][COMM]msg 02A7 loss. last_tick:92304. cur_tick:92817. period:50
[D][05:19:21][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 92817
[D][05:19:21][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 92818


2025-07-31 20:07:55:850 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:21][FCTY]==========Modules-nRF5340 ==========
[D][05:19:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:21][FCTY]DeviceID    = 460130071539171
[D][05:19:21][FCTY]HardwareID  = 867222087723807
[D][05:19:22][FCTY]MoBikeID    = 9999999999
[D][05:19:22][FCTY]LockID      = FFFFFFFFFF
[D][05:19:22][FCTY]BLEFWVersion= 105
[D][05:19:22][FCTY]BLEMacAddr   = CD5E73B20727
[D][05:19:22][FCTY]Bat         = 3604 mv
[D][05:19:22][FCTY]Current     = 0 ma
[D][05:19:22][FCTY]VBUS        = 2600 mv
[D][05:19:22][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:22][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:22][FCTY]Ext battery vol = 32, adc = 1291
[D][05:19:22][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 278 mv
[D][05:19:22][FCTY]Bike Type flag is invalied
[D][05:19:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:22][FCTY]CAT1_KERNEL_R

2025-07-31 20:07:55:955 ==>> TK = 1.2.4
[D][05:19:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:22][FCTY]Bat1         = 3725 mv
[D][05:19:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:22][FCTY]==========Modules-nRF5340 ==========
[E][05:19:22][COMM]1x1 rx timeout
[E][05:19:22][COMM]1x1 tp timeout
[E][05:19:22][COMM]1x1 error -3.
[D][05:19:22][COMM]Main Task receive event:28 finished processing
[D][05:19:22][COMM]Main Task receive event:65
[D][05:19:22][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:22][COMM]Main Task receive event:65 finished processing
[D][05:19:22][COMM]Main Task receive event:60
[D][05:19:22][COMM]smart_helmet_vol=255,255
[D][05:19:22][COMM]report elecbike
[W][05:19:22][PROT]remove success[1629955162],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:22][COMM]Main Task receive event:60 finished processing
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:2

2025-07-31 20:07:56:060 ==>> 2][PROT]index:0
[D][05:19:22][PROT]is_send:1
[D][05:19:22][PROT]sequence_num:11
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:3
[D][05:19:22][PROT]send_path:0x3
[D][05:19:22][PROT]msg_type:0x5d03
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]Sending traceid[999999999990000C]
[D][05:19:22][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:22][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:22][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:22][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:22][SAL ]open socket ind id[4], rst[0]
[D][05:19:22][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:22][SAL ]Cellular task submsg id[8]
[D][05:19:22][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:22][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:1

2025-07-31 20:07:56:098 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:07:56:108 ==>> 检测【关机】
2025-07-31 20:07:56:132 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:07:56:165 ==>> 9:22][CAT1]gsm read msg sub id: 8
[D][05:19:22][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:22][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:22][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:22][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:22][CAT1]<<< 
+CME ERROR: 100

[D][05:19:22][COMM]msg 0220 loss. last_tick:92304. cur_tick:93311. period:100
[D][05:19:22][COMM]msg 0221 loss. last_tick:92304. cur_tick:93311. period:100
[D][05:19:22][COMM]msg 0224 loss. last_tick:92304. cur_tick:93312. period:100
[D][05:19:22][COMM]msg 0260 loss. last_tick:92304. cur_tick:93312. period:100
[D][05:19:22][COMM]msg 0280 loss. last_tick:92304. cur_tick:93312. period:100
[D][05:19:22][COMM]msg 02C0 loss. last_tick:92304. cur_tick:93313. period:100
[D][05:19:22][COMM]msg 02C1 loss. last_tick:92304. cur_tick:93313. period:100
[D][05:19:22][COMM]msg 02C2 loss. last_tick:92304. cur_tick:93313. period:100
[D][05:19:22][COMM]msg 02E0 loss. last_tick:92304. cur_tick:93314. period:100
[D][05:19:22][COMM]msg 02E1 loss. last_tick:92304. cur_tick:93314. period:100
[D][05:19:22][COMM]msg 02E2 loss. last_tick:92304. cur_tick:93314. period:100
[

2025-07-31 20:07:56:270 ==>> D][05:19:22][COMM]msg 0300 loss. last_tick:92304. cur_tick:93315. period:100
[D][05:19:22][COMM]msg 0301 loss. last_tick:92304. cur_tick:93315. period:100
[D][05:19:22][COMM]bat msg 0240 loss. last_tick:92304. cur_tick:93316. period:100. j,i:1 54
[D][05:19:22][COMM]bat msg 0241 loss. last_tick:92304. cur_tick:93316. period:100. j,i:2 55
[D][05:19:22][COMM]bat msg 0242 loss. last_tick:92304. cur_tick:93317. period:100. j,i:3 56
[D][05:19:22][COMM]bat msg 0244 loss. last_tick:92304. cur_tick:93317. period:100. j,i:5 58
[D][05:19:22][COMM]bat msg 024E loss. last_tick:92304. cur_tick:93317. period:100. j,i:15 68
[D][05:19:22][COMM]bat msg 024F loss. last_tick:92304. cur_tick:93318. period:100. j,i:16 69
[D][05:19:22][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 93318
[D][05:19:22][COMM]CAN message bat fault change: 0x00000000->0x0001802E 93319
[D][05:19:22][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 93319
[D][05:19:22][COMM]IMU: [12,4,-983] ret=30 AWAKE!


2025-07-31 20:07:56:543 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:07:56:648 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:07:56:753 ==>>                                                                                                               len --- [256]
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][COMM]Receive Bat Lock cmd 0
[D][05:19:22][COMM]VBUS is 1
[W][05:19:22][PROT]remove success[1629955162],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:19:22][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:22][PROT]index:1
[D][05:19:22][PROT]is_send:1
[D][05:19:22][PROT]sequence_num:12
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:3
[D][05:19:22][PROT]send_path:0x3
[D][05:19:22][PROT]msg_type:0x5d03
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]Sending traceid[999999999990000D]
[D][05:19:22][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:22][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:22][PROT]ble is not inited or not connect

2025-07-31 20:07:56:798 ==>> ed or cccd not enabled
[W][05:19:23][PROT]add success [1629955163],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:23][COMM]Main Task receive event:60 finished processing
[D][05:19:23][COMM]Main Task receive ev

2025-07-31 20:07:56:903 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:07:57:008 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:07:57:113 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:07:57:132 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:07:57:218 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:07:57:323 ==>>                                                                           [W][05:19:24][COMM]>

2025-07-31 20:07:57:398 ==>> >>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:24][COMM]arm_hub_enable: hub power: 0
[D][05:19:24][HSDK]hexlog index save 0 5376 153 @ 0 : 0
[D][05:19:24][HSDK]write save hexlog index [0]
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:24][COMM]Main Task receive event:68
[D][05:19:24][COMM]handlerWheelStolen evt type = 2.
[E][05:19:24][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:24][GNSS]stop locating
[D][05:19:24][GNSS]all continue location stop
[D][05:19:24][COMM]Main Task receive event:68 finished processing


2025-07-31 20:07:57:773 ==>> [W][05:19:24][COMM]Power Off


2025-07-31 20:07:57:912 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:07:57:920 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:07:57:943 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:07:58:045 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:07:58:184 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:07:58:192 ==>> 检测【检测小电池关机电流】
2025-07-31 20:07:58:202 ==>> 开始小电池电流采样
2025-07-31 20:07:58:218 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:58:247 ==>> [D][05:19:24][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:24][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:07:58:285 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:07:59:300 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:07:59:345 ==>> CurrentBattery:ƽ��:69.20

2025-07-31 20:07:59:809 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:07:59:817 ==>> 【检测小电池关机电流】通过,【69.2uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:08:00:121 ==>> MES过站成功
2025-07-31 20:08:00:132 ==>> #################### 【测试结束】 ####################
2025-07-31 20:08:00:161 ==>> 关闭5V供电
2025-07-31 20:08:00:180 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:08:00:234 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:08:01:167 ==>> 关闭5V供电成功
2025-07-31 20:08:01:182 ==>> 关闭33V供电
2025-07-31 20:08:01:207 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:08:01:242 ==>> 5A A5 02 5A A5 


2025-07-31 20:08:01:333 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:08:02:168 ==>> 关闭33V供电成功
2025-07-31 20:08:02:183 ==>> 关闭3.7V供电
2025-07-31 20:08:02:210 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:08:02:231 ==>> 6A A6 02 A6 6A 


2025-07-31 20:08:02:336 ==>> Battery OFF
OVER 150


2025-07-31 20:08:03:106 ==>>  

2025-07-31 20:08:03:180 ==>> 关闭3.7V供电成功
