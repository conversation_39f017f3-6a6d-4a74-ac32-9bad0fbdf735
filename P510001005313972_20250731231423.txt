2025-07-31 23:14:23:175 ==>> MES查站成功:
查站序号:P510001005313972验证通过
2025-07-31 23:14:23:187 ==>> 扫码结果:P510001005313972
2025-07-31 23:14:23:188 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:14:23:189 ==>> 测试参数版本:2024.10.11
2025-07-31 23:14:23:190 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:14:23:192 ==>> 检测【打开透传】
2025-07-31 23:14:23:193 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:14:23:325 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:14:23:492 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:14:23:498 ==>> 检测【检测接地电压】
2025-07-31 23:14:23:500 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:14:23:617 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:14:23:796 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:14:23:798 ==>> 检测【打开小电池】
2025-07-31 23:14:23:801 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:14:23:923 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:14:24:102 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:14:24:105 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:14:24:108 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:14:24:223 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:14:24:394 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:14:24:398 ==>> 检测【等待设备启动】
2025-07-31 23:14:24:400 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:14:24:673 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:14:24:868 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 23:14:25:372 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:14:25:432 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:14:25:570 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:14:26:218 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:14:26:308 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM


2025-07-31 23:14:26:475 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:14:26:695 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:14:27:173 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:14:27:282 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:14:27:284 ==>> 检测【产品通信】
2025-07-31 23:14:27:287 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:14:27:505 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:14:27:553 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:14:27:555 ==>> 检测【初始化完成检测】
2025-07-31 23:14:27:558 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:14:27:748 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:14:27:808 ==>>                                                              

2025-07-31 23:14:27:826 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:14:27:829 ==>> 检测【关闭大灯控制1】
2025-07-31 23:14:27:831 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:14:27:990 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:14:28:098 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:14:28:101 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:14:28:102 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:14:28:661 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:14:28:845 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:14:29:134 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:14:29:538 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 23:14:29:921 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:14:30:176 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:14:30:446 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 23:14:31:210 ==>> 未匹配到【打开仪表指令模式1】数据,请核对检查!
2025-07-31 23:14:31:215 ==>> #################### 【测试结束】 ####################
2025-07-31 23:14:31:247 ==>> 关闭5V供电
2025-07-31 23:14:31:249 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:14:31:319 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:14:31:576 ==>> [W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 23:14:32:260 ==>> 关闭5V供电成功
2025-07-31 23:14:32:264 ==>> 关闭33V供电
2025-07-31 23:14:32:267 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:14:32:320 ==>> 5A A5 02 5A A5 


2025-07-31 23:14:32:425 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:14:33:264 ==>> 关闭33V供电成功
2025-07-31 23:14:33:267 ==>> 关闭3.7V供电
2025-07-31 23:14:33:269 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:14:33:325 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:14:33:650 ==>>  

