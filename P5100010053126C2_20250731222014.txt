2025-07-31 22:20:14:850 ==>> MES查站成功:
查站序号:P5100010053126C2验证通过
2025-07-31 22:20:14:853 ==>> 扫码结果:P5100010053126C2
2025-07-31 22:20:14:856 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:20:14:857 ==>> 测试参数版本:2024.10.11
2025-07-31 22:20:14:859 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:20:14:861 ==>> 检测【打开透传】
2025-07-31 22:20:14:862 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:20:14:995 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:20:15:134 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:20:15:141 ==>> 检测【检测接地电压】
2025-07-31 22:20:15:144 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:20:15:192 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:20:15:423 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:20:15:426 ==>> 检测【打开小电池】
2025-07-31 22:20:15:429 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:20:15:493 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:20:15:694 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:20:15:697 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:20:15:699 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:20:15:799 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:20:15:970 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:20:15:972 ==>> 检测【等待设备启动】
2025-07-31 22:20:15:975 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:17:016 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:17:600 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:17:782 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti 

2025-07-31 22:20:18:059 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:18:287 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:18:482 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:20:18:992 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:19:082 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:19:158 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:20:19:674 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:19:857 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:20:20:117 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:20:378 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:20:558 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:21:066 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:21:156 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:21:261 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:21:771 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:21:953 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:22:185 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:22:467 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:22:635 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:23:160 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:23:205 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:23:340 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:20:23:855 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:24:035 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:20:24:233 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:24:550 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:24:747 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:20:25:255 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:25:270 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:20:25:420 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:25:938 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:26:119 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:26:302 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:20:26:305 ==>> #################### 【测试结束】 ####################
2025-07-31 22:20:26:323 ==>> 关闭5V供电
2025-07-31 22:20:26:327 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:20:26:393 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:20:26:634 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:26:816 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:27:331 ==>> 关闭5V供电成功
2025-07-31 22:20:27:334 ==>> 关闭33V供电
2025-07-31 22:20:27:336 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:20:27:339 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:27:391 ==>> 5A A5 02 5A A5 


2025-07-31 22:20:27:496 ==>> CLOSE_POWER_OUT1
OVER 150
*** Booting Zephyr OS build v2.7.9

2025-07-31 22:20:27:526 ==>> 9-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:28:028 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:20:28:194 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:20:28:344 ==>> 关闭33V供电成功
2025-07-31 22:20:28:347 ==>> 关闭3.7V供电
2025-07-31 22:20:28:349 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:20:28:404 ==>> 6A A6 02 A6 6A 


2025-07-31 22:20:28:494 ==>> Battery OFF
OVER 150


2025-07-31 22:20:28:826 ==>>  

