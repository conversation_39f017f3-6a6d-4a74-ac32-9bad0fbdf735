2025-07-31 22:52:42:528 ==>> MES查站成功:
查站序号:P510001005312654验证通过
2025-07-31 22:52:42:536 ==>> 扫码结果:P510001005312654
2025-07-31 22:52:42:538 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:52:42:539 ==>> 测试参数版本:2024.10.11
2025-07-31 22:52:42:541 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:52:42:559 ==>> 检测【打开透传】
2025-07-31 22:52:42:563 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:52:42:618 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:52:42:889 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:52:42:903 ==>> 检测【检测接地电压】
2025-07-31 22:52:42:906 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:52:43:014 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:52:43:198 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:52:43:202 ==>> 检测【打开小电池】
2025-07-31 22:52:43:205 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:52:43:319 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:52:43:497 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:52:43:500 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:52:43:503 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:52:43:609 ==>> 1A A1 00 00 01 
Get AD_V0 1291mV
OVER 150


2025-07-31 22:52:43:802 ==>> 【检测小电池分压(AD_VBAT)】通过,【1291mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:52:43:804 ==>> 检测【等待设备启动】
2025-07-31 22:52:43:806 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:52:44:054 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:52:44:233 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:52:44:833 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:52:44:923 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:52:45:306 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:52:45:784 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:52:45:893 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:52:45:895 ==>> 检测【产品通信】
2025-07-31 22:52:45:897 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:52:46:075 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:52:46:169 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:52:46:173 ==>> 检测【初始化完成检测】
2025-07-31 22:52:46:177 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:52:46:487 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:52:46:702 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:52:46:704 ==>> 检测【关闭大灯控制1】
2025-07-31 22:52:46:705 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:52:47:005 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:52:47:237 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:52:47:241 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:52:47:244 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:52:47:415 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:52:47:518 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:52:47:524 ==>> 检测【关闭仪表供电】
2025-07-31 22:52:47:528 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:52:47:703 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:52:47:802 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:52:47:805 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:52:47:807 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:52:47:883 ==>> [D][05:17:52][COMM]3640 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:52:47:973 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:52:48:087 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:52:48:089 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:52:48:091 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:52:48:279 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:52:48:357 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:52:48:359 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:52:48:361 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:52:48:491 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:52:48:644 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:52:48:648 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:52:48:650 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:52:48:716 ==>> 5A A5 01 5A A5 


2025-07-31 22:52:48:806 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:52:48:911 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 17
[D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:52:48:914 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:52:48:917 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:52:48:919 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:52:48:971 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 22:52:49:016 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:52:49:187 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:52:49:189 ==>> 该项需要延时执行
2025-07-31 22:52:49:427 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5018. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message f

2025-07-31 22:52:49:457 ==>> ault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019


2025-07-31 22:52:49:903 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:52:50:484 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:52:50:994 ==>>  D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[e

2025-07-31 22:52:51:099 ==>> c800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuit

2025-07-31 22:52:51:204 ==>> Volt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][C

2025-07-31 22:52:51:294 ==>> OMM]VBUS is 1
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6673 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                                                                 

2025-07-31 22:52:51:940 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:52:52:986 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 22:52:53:201 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:52:53:205 ==>> 检测【33V输入电压ADC】
2025-07-31 22:52:53:209 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:52:53:517 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:17:58][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:58][COMM]adc read left brake adc:5  volt:6 mv
[D][05:17:58][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:58][COMM]adc read throttle adc:7  volt:9 mv
[D][05:17:58][COMM]adc read battery ts volt:12 mv
[D][05:17:58][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:17:58][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:52:53:731 ==>> 【33V输入电压ADC】通过,【32703mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:52:53:734 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:52:53:737 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:52:53:820 ==>> 1A A1 00 00 FC 
Get AD_V2 1680mV
Get AD_V3 1663mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:52:53:955 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:52:54:004 ==>> 【TP7_VCC3V3(ADV2)】通过,【1680mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:006 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:52:54:049 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:051 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:52:54:053 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 22:52:54:067 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:52:54:070 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:52:54:098 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:52:54:100 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:52:54:124 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:52:54:128 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:52:54:215 ==>> 1A A1 00 00 FC 
Get AD_V2 1681mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2767mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:52:54:305 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 22:52:54:426 ==>> 【TP7_VCC3V3(ADV2)】通过,【1681mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:441 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:52:54:475 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:477 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:52:54:480 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 22:52:54:521 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:52:54:523 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:52:54:563 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:52:54:570 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:52:54:613 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:52:54:615 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:52:54:715 ==>> 1A A1 00 00 FC 
Get AD_V2 1681mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2767mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:52:54:790 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:52:54:919 ==>> 【TP7_VCC3V3(ADV2)】通过,【1681mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:921 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:52:54:965 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:52:54:967 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:52:54:970 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 22:52:55:015 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:52:55:017 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:52:55:061 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:52:55:082 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:52:55:151 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:52:55:156 ==>> 检测【打开WIFI(1)】
2025-07-31 22:52:55:161 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:52:55:166 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,

2025-07-31 22:52:55:201 ==>> 0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:52:55:306 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 22:52:55:366 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                       [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:52:55:430 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:52:55:433 ==>> 检测【清空消息队列(1)】
2025-07-31 22:52:55:434 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:52:55:609 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:52:55:703 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:52:55:705 ==>> 检测【打开GPS(1)】
2025-07-31 22:52:55:708 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:52:55:910 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:52:55:954 ==>>                                          

2025-07-31 22:52:55:972 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:52:55:975 ==>> 检测【打开GSM联网】
2025-07-31 22:52:55:977 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:52:56:194 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:52:56:242 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:52:56:271 ==>> 检测【打开仪表供电1】
2025-07-31 22:52:56:273 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:52:56:405 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:52:56:514 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:52:56:516 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:52:56:519 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:52:56:705 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:52:56:784 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:52:56:787 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:52:56:789 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:52:57:065 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][COMM]arm_hub read adc[3],val[33015]
[D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 22:52:57:310 ==>> 【读取主控ADC采集的仪表电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:52:57:312 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:52:57:315 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:57:627 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087952687

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071541268

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:1

2025-07-31 22:52:57:657 ==>> 8:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:52:57:853 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:52:57:856 ==>> 检测【AD_V20电压】
2025-07-31 22:52:57:859 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:57:964 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:52:58:009 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:52:58:175 ==>> 本次取值间隔时间:204ms
2025-07-31 22:52:58:194 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:58:299 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:52:58:455 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:52:58:772 ==>> 本次取值间隔时间:469ms
2025-07-31 22:52:58:790 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:58:897 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:52:59:019 ==>> [D][05:18:03][COMM]14730 imu init OK
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:52:59:109 ==>> 本次取值间隔时间:203ms
2025-07-31 22:52:59:193 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:59:305 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:52:59:397 ==>> 本次取值间隔时间:79ms
2025-07-31 22:52:59:412 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:52:59:517 ==>> 本次取值间隔时间:108ms
2025-07-31 22:52:59:565 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:52:59:671 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:52:59:851 ==>> 本次取值间隔时间:173ms
2025-07-31 22:52:59:911 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:53:00:048 ==>> 本次取值间隔时间:189ms
2025-07-31 22:53:00:213 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:53:00:472 ==>> 本次取值间隔时间:411ms
2025-07-31 22:53:00:476 ==>> [D][05:18:05][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:05][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:53:00:670 ==>> 本次取值间隔时间:189ms
2025-07-31 22:53:00:674 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:53:00:775 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:53:01:093 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 5, ret: 6
[D][05:18:05][CAT1]sub id: 5, ret: 6

[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:05][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:05][M2M ]M2M_GSM_INIT OK
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:05][GNSS]location recv gms init done evt
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:05][SAL ]open socket ind id[4], rst[0]
[D][05:18:05][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:05][SAL ]Cellular task submsg id[8]
[D][05:18:05][SAL ]cellular OPEN socket size[144], msg->data[0x20053038], socket[0]
[D][05:18:05][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:05][GNSS]GPS start. ret=0
[D][05:18:05][CAT1]gsm read msg sub id: 23
[D][05:18:05][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 22:53:01:108 ==>> 本次取值间隔时间:322ms
2025-07-31 22:53:01:198 ==>>                                                                                                                                 

2025-07-31 22:53:01:228 ==>> 本次取值间隔时间:116ms
2025-07-31 22:53:01:303 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:53:01:667 ==>> 本次取值间隔时间:427ms
2025-07-31 22:53:01:802 ==>> 本次取值间隔时间:132ms
2025-07-31 22:53:01:806 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:53:01:907 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:53:02:012 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:06][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1655mV
OVER 150


2025-07-31 22:53:02:224 ==>> 本次取值间隔时间:304ms
2025-07-31 22:53:02:243 ==>> 【AD_V20电压】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:53:02:246 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:53:02:250 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:53:02:317 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:53:02:520 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:53:02:522 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:53:02:527 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:53:03:027 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:07][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,25,,,40,59,,,40,34,,,39,39,,,39,1*7B

$GBGSV,3,2,09,40,,,37,41,,,35,60,,,41,11,,,36,1*7B

$GBGSV,3,3,09,16,,,36,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1589.178,1589.178,50.782,2097152,2097152,2097152*47

[D][05:18:07][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:07][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 23, ret: 6
[D][05:18:07][CAT1]sub id: 23, ret: 6

[D][05:18:07][CAT1]gsm read msg sub id: 8
[D][05:18:07][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:53:03:060 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:53:03:067 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:53:03:071 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:53:03:132 ==>> 
[D][05:18:07][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:07][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:07][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:07][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:07][CAT1]<<< 
+QIACT: 1,1,1,"10.176.155.136"

OK

[D][05:18:07][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 8, ret: 6
[D][05:18:07][GNSS]recv submsg id[1]
[D][05:18:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
                                                                                                                                                                                                                                                                                                                                                                                                                           3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:53:03:345 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:53:03:348 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:53:03:350 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:53:03:526 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:53:03:620 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:53:03:625 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:53:03:630 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:53:03:842 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,59,,,40,34,,,40,39,,,40,25,,,39,1*7F

$GBGSV,3,2,12,40,,,39,3,,,38,41,,,37,16,,,35,1*42

$GBGSV,3,3,12,11,,,34,5,,,33,60,,,41,43,,,38,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1554.667,1554.667,49.715,2097152,2097152,2097152*41

[D][05:18:08][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:53:03:890 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:53:03:896 ==>> 检测【AD_V21电压】
2025-07-31 22:53:03:918 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:53:04:007 ==>> 1A A1 20 00 00 
Get AD_V21 1023mV
OVER 150


2025-07-31 22:53:04:252 ==>> 本次取值间隔时间:358ms
2025-07-31 22:53:04:279 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:53:04:409 ==>> 1A A1 20 00 00 
Get AD_V21 1647mV
OVER 150


2025-07-31 22:53:04:560 ==>> 本次取值间隔时间:267ms
2025-07-31 22:53:04:578 ==>> 【AD_V21电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:53:04:582 ==>> 检测【关闭仪表供电2】
2025-07-31 22:53:04:584 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:53:04:849 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,59,,,40,34,,,40,39,,,40,40,,,40,1*71

$GBGSV,4,2,16,60,,,39,25,,,39,3,,,39,43,,,38,1*43

$GBGSV,4,3,16,41,,,38,1,,,38,16,,,36,11,,,36,1*45

$GBGSV,4,4,16,2,,,35,5,,,33,4,,,33,44,,,32,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1544.311,1544.311,49.393,2097152,2097152,2097152*4B

[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:53:05:111 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:53:05:116 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:53:05:119 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:53:05:122 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:53:05:308 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:53:05:387 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:53:05:390 ==>> 检测【打开AccKey2供电】
2025-07-31 22:53:05:393 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:53:05:598 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:53:05:678 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:53:05:683 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:53:05:686 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:05:838 ==>> $GBGGA,145309.672,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,41,59,,,40,34,,,40,39,,,40,1*74

$GBGSV,5,2,20,60,,,40,25,,,40,3,,,39,41,,,39,1*44

$GBGSV,5,3,20,43,,,38,1,,,37,16,,,37,11,,,37,1*4C

$GBGSV,5,4,20,24,,,36,23,,,35,2,,,34,5,,,33,1*71

$GBGSV,5,5,20,4,,,33,44,,,32,32,,,31,7,,,42,1*73

$GBRMC,145309.672,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145309.672,0.000,1531.785,1531.785,49.012,2097152,2097152,2097152*56



2025-07-31 22:53:06:048 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:10][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:18:10][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:10][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:10][COMM]adc read battery ts volt:14 mv
[D][05:18:10][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:10][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:53:06:229 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33361mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:53:06:233 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:53:06:239 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:53:06:381 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:53:06:510 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:53:06:514 ==>> 该项需要延时执行
2025-07-31 22:53:06:742 ==>> $GBGGA,145310.572,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,59,,,40,34,,,40,39,,,40,1*74

$GBGSV,6,2,23,60,,,39,25,,,39,3,,,39,41,,,39,1*44

$GBGSV,6,3,23,7,,,38,43,,,38,11,,,38,1,,,37,1*7C

$GBGSV,6,4,23,16,,,37,24,,,36,23,,,36,10,,,35,1*76

$GBGSV,6,5,23,33,,,35,2,,,34,9,,,33,5,,,32,1*4A

$GBGSV,6,6,23,4,,,32,44,,,32,32,,,32,1*43

$GBRMC,145310.572,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145310.572,0.000,1517.737,1517.737,48.557,2097152,2097152,2097152*58



2025-07-31 22:53:07:118 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:53:07:711 ==>> $GBGGA,145311.552,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,6,2,24,60,,,39,25,,,39,3,,,39,41,,,39,1*43

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,36,23,,,36,10,,,36,1*44

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,33,1*4A

$GBGSV,6,6,24,5,,,33,4,,,32,44,,,32,32,,,32,1*71

$GBRMC,145311.552,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145311.552,0.000,1523.590,1523.590,48.739,2097152,2097152,2097152*51



2025-07-31 22:53:08:698 ==>> $GBGGA,145312.532,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,6,2,24,60,,,40,41,,,40,25,,,39,3,,,39,1*43

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,33,1*4A

$GBGSV,6,6,24,5,,,33,44,,,32,32,,,32,4,,,31,1*72

$GBRMC,145312.532,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145312.532,0.000,1527.052,1527.052,48.856,2097152,2097152,2097152*52



2025-07-31 22:53:09:129 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:53:09:524 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:53:09:528 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:53:09:532 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:09:828 ==>> $GBGGA,145313.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,6,2,24,60,,,40,41,,,40,3,,,40,25,,,39,1*4D

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,5,,,33,32,,,33,44,,,32,4,,,31,1*73

$GBRMC,145313.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145313.512,0.000,1532.231,1532.231,49.018,2097152,2097152,2097152*52

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:14][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:14][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:14][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:14][COMM]adc read battery ts volt:13 mv
[D][05:18:14][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub a

2025-07-31 22:53:09:873 ==>> dc read vbat adc:2401  volt:3868 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:53:10:098 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【177mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:53:10:101 ==>> 检测【打开AccKey1供电】
2025-07-31 22:53:10:106 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:53:10:286 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:53:10:412 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:53:10:416 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:53:10:424 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:53:10:512 ==>> 1A A1 00 40 00 
Get AD_V14 2664mV
OVER 150


2025-07-31 22:53:10:617 ==>> $GBGGA,145314.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,39,,

2025-07-31 22:53:10:677 ==>> 原始值:【2664】, 乘以分压基数【2】还原值:【5328】
2025-07-31 22:53:10:683 ==>> ,40,1*73

$GBGSV,6,2,24,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,5,,,33,32,,,32,44,,,32,4,,,32,1*71

$GBRMC,145314.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145314.512,0.000,1527.042,1527.042,48.846,2097152,2097152,2097152*57



2025-07-31 22:53:10:721 ==>> 【读取AccKey1电压(ADV14)前】通过,【5328mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:53:10:727 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:53:10:731 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:11:026 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:15][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:15][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:15][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:15][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:53:11:131 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 22:53:11:280 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5517mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:53:11:286 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:53:11:315 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:53:11:498 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:53:11:584 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:53:11:589 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:53:11:604 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:53:11:679 ==>> $GBGGA,145315.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,6,2,24,60,,,40,41,,,39,3,,,39,25,,,39,1*4D

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,5,,,33,32,,,33,44,,,32,4,,,32,1*70

$GBRMC,145315.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145315.512,0.000,1530.495,1530.495,48.955,2097152,2097152,2097152*55



2025-07-31 22:53:11:709 ==>> 1A A1 00 40 00 
Get AD_V14 2664mV
OVER 150


2025-07-31 22:53:11:846 ==>> 原始值:【2664】, 乘以分压基数【2】还原值:【5328】
2025-07-31 22:53:11:893 ==>> 【读取AccKey1电压(ADV14)后】通过,【5328mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:53:11:897 ==>> 检测【打开WIFI(2)】
2025-07-31 22:53:11:902 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:53:12:141 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:53:12:176 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:53:12:180 ==>> 检测【转刹把供电】
2025-07-31 22:53:12:185 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:53:12:400 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:53:12:449 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:53:12:455 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:53:12:463 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:53:12:552 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:53:12:690 ==>> 1A A1 00 80 00 
Get AD_V15 2403mV
OVER 150
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,145316.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,39,,,40,60,,,40,1*72

$GBGSV,6,2,24,34,,,39,41,,,39,3,,,39,25,,,39,1*42

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,5,,,33,32,,,33,44,,,32,4,,,32,1*70

$GBRMC,145316.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145316.512,0.000,1528.766,1528.766,48.898,2097152,2097152,2097152*56



2025-07-31 22:53:12:705 ==>> 原始值:【2403】, 乘以分压基数【2】还原值:【4806】
2025-07-31 22:53:12:731 ==>> 【读取AD_V15电压(前)】通过,【4806mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:53:12:735 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:53:12:754 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:53:12:840 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:53:12:950 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:53:12:954 ==>> 1A A1 01 00 00 
Get AD_V16 2430mV
OVER 150


2025-07-31 22:53:12:991 ==>> 原始值:【2430】, 乘以分压基数【2】还原值:【4860】
2025-07-31 22:53:13:014 ==>> 【读取AD_V16电压(前)】通过,【4860mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:53:13:018 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:53:13:022 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:13:330 ==>> [D][05:18:17][COMM]read battery soc:255
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:17][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:17][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:17][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:17][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:17][COMM]adc read battery ts volt:14 mv
[D][05:18:17][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
                                    

2025-07-31 22:53:13:360 ==>>                                                                                                                                             

2025-07-31 22:53:13:546 ==>> 【转刹把供电电压(主控ADC)】通过,【5417mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:53:13:550 ==>> 检测【转刹把供电电压】
2025-07-31 22:53:13:553 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:13:836 ==>> $GBGGA,145317.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,39,,,40,60,,,39,1*7C

$GBGSV,6,2,24,34,,,39,41,,,39,3,,,39,25,,,39,1*42

$GBGSV,6,3,24,7,,,39,43,,,38,11,,,38,16,,,37,1*4C

$GBGSV,6,4,24,1,,,37,24,,,37,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,33,1*4A

$GBGSV,6,6,24,32,,,33,5,,,32,44,,,32,4,,,32,1*71

$GBRMC,145317.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145317.512,0.000,1521.860,1521.860,48.681,2097152,2097152,2097152*51

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:18][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:18][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:18][COMM]adc read battery ts volt:13 mv
[D][05:18:18][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3081  volt:5415 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1

2025-07-31 22:53:13:866 ==>> 424  volt:33015 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:53:14:017 ==>> [D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 22:53:14:084 ==>> 【转刹把供电电压】通过,【5415mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:53:14:088 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:53:14:091 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:53:14:291 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:53:14:355 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:53:14:360 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:53:14:367 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:53:14:460 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:53:14:475 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:53:14:520 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:53:14:595 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:53:14:601 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:53:14:609 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:53:14:629 ==>> $GBGGA,145318.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,6,2,24,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,6,3,24,7,,,38,43,,,38,11,,,3

2025-07-31 22:53:14:671 ==>> 8,16,,,37,1*4D

$GBGSV,6,4,24,24,,,37,1,,,36,23,,,36,10,,,36,1*44

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,33,1*4A

$GBGSV,6,6,24,32,,,33,5,,,33,44,,,32,4,,,32,1*70

$GBRMC,145318.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145318.512,0.000,1521.857,1521.857,48.679,2097152,2097152,2097152*59



2025-07-31 22:53:14:701 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:53:14:776 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input comm

2025-07-31 22:53:14:806 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:53:14:811 ==>> and = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:53:14:911 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:53:14:971 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:53:15:017 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:53:15:036 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:53:15:043 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:53:15:064 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:53:15:122 ==>> 3A A3 03 01 A3 
[D][05:18:19][COMM]read battery soc:255


2025-07-31 22:53:15:212 ==>> ON_OUT3
OVER 150


2025-07-31 22:53:15:320 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:53:15:328 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:53:15:350 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:53:15:407 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:53:15:593 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:53:15:597 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:53:15:603 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:53:15:679 ==>> $GBGGA,145319.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,6,2,24,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,6,3,24,7,,,38,43,,,38,11,,,38,16,,,37,1*4D

$GBGSV,6,4,24,24,,,36,1,,,36,23,,,36,10,,,36,1*45

$GBGSV,6,5,24,33,,,36,6,,,35,2,,,34,9,,,33,1*4A

$GBGSV,6,6,24,32,,,33,5,,,33,44,,,32,4,,,32,1*70

$GBRMC,145319.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145319.512,0.000,1520.130,1520.130,48.624,2097152,2097152,2097152*50



2025-07-31 22:53:15:710 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:53:15:870 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:53:15:874 ==>> 检测【左刹电压测试1】
2025-07-31 22:53:15:880 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:16:232 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1723  volt:2271 mv
[D][05:18:20][COMM]adc read right brake adc:1718  volt:2264 mv
[D][05:18:20][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:20][COMM]adc read battery ts volt:13 mv
[D][05:18:20][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:53:16:402 ==>> 【左刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 22:53:16:406 ==>> 检测【右刹电压测试1】
2025-07-31 22:53:16:426 ==>> 【右刹电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 22:53:16:430 ==>> 检测【转把电压测试1】
2025-07-31 22:53:16:448 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 22:53:16:451 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:53:16:454 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:53:16:506 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:53:16:612 ==>> $GBGGA,145320.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,

2025-07-31 22:53:16:673 ==>> ,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,6,2,24,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,6,3,24,7,,,38,43,,,38,11,,,37,16,,,37,1*42

$GBGSV,6,4,24,24,,,37,1,,,36,10,,,36,33,,,36,1*45

$GBGSV,6,5,24,23,,,35,6,,,35,2,,,34,9,,,33,1*48

$GBGSV,6,6,24,32,,,33,5,,,33,44,,,32,4,,,32,1*70

$GBRMC,145320.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145320.512,0.000,1518.403,1518.403,48.569,2097152,2097152,2097152*50



2025-07-31 22:53:16:723 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:53:16:727 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:53:16:733 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:53:16:808 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:53:17:019 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:53:17:023 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:53:17:027 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:53:17:111 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:53:17:156 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:53:17:296 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:53:17:302 ==>> 检测【左刹电压测试2】
2025-07-31 22:53:17:326 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:53:17:711 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:22][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:22][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:22][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:22][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:22][COMM]adc read battery ts volt:14 mv
[D][05:18:22][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,145321.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,38,43,,,38,11,,,38,16,,,38,1*42

$GBGSV,7,4,25,24,,,37,1,,,36,10,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,2,,,34,9,,,34,1*4C

$GBGSV,

2025-07-31 22:53:17:756 ==>> 7,6,25,32,,,33,5,,,33,44,,,32,4,,,32,1*70

$GBGSV,7,7,25,12,,,36,1*77

$GBRMC,145321.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145321.512,0.000,1525.310,1525.310,48.787,2097152,2097152,2097152*53



2025-07-31 22:53:17:836 ==>> 【左刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 22:53:17:840 ==>> 检测【右刹电压测试2】
2025-07-31 22:53:17:857 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 22:53:17:862 ==>> 检测【转把电压测试2】
2025-07-31 22:53:17:876 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 22:53:17:881 ==>> 检测【晶振检测】
2025-07-31 22:53:17:910 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:53:18:095 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 22:53:18:159 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:53:18:163 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:53:18:166 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:53:18:200 ==>> 1A A1 00 00 FC 
Get AD_V2 1679mV
Get AD_V3 1663mV
Get AD_V4 1652mV
Get AD_V5 2765mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150

2025-07-31 22:53:18:229 ==>> 


2025-07-31 22:53:18:430 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:53:18:434 ==>> 检测【检测BootVer】
2025-07-31 22:53:18:439 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:18:840 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071541268
[D][05:18:23][FCTY]HardwareID  = 867222087952687
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
$GBGGA,145322.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,11,,,38,16,,,37,1*4C

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,2,,,35,12,,,34,1*77

$GBGSV,7,6,25,9,,,33,32,,,33,5,,,33,4,,,32,1*48

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145322.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145322.512,0.000,1520.686,1520.686,48.644,2097152,2097152

2025-07-31 22:53:18:930 ==>> ,2097152*5E

[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1285
[D][05:18:23][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 101 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3767 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:53:18:967 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:53:18:971 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:53:18:974 ==>> 检测【检测固件版本】
2025-07-31 22:53:18:988 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:53:18:992 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:53:18:997 ==>> 检测【检测蓝牙版本】
2025-07-31 22:53:19:006 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:53:19:010 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:53:19:016 ==>> 检测【检测MoBikeId】
2025-07-31 22:53:19:036 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:53:19:042 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:53:19:057 ==>> 检测【检测蓝牙地址】
2025-07-31 22:53:19:064 ==>> 取到目标值:F46918A0BBD9
2025-07-31 22:53:19:084 ==>> 【检测蓝牙地址】通过,【F46918A0BBD9】符合目标值【】要求!
2025-07-31 22:53:19:088 ==>> 提取到蓝牙地址:F46918A0BBD9
2025-07-31 22:53:19:092 ==>> 检测【BOARD_ID】
2025-07-31 22:53:19:111 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:53:19:115 ==>> 检测【检测充电电压】
2025-07-31 22:53:19:140 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:53:19:144 ==>> 检测【检测VBUS电压1】
2025-07-31 22:53:19:152 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:53:19:160 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:53:19:164 ==>> 检测【检测充电电流】
2025-07-31 22:53:19:183 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:53:19:191 ==>> 检测【检测IMEI】
2025-07-31 22:53:19:206 ==>> 取到目标值:867222087952687
2025-07-31 22:53:19:212 ==>> 【检测IMEI】通过,【867222087952687】符合目标值【】要求!
2025-07-31 22:53:19:216 ==>> 提取到IMEI:867222087952687
2025-07-31 22:53:19:221 ==>> 检测【检测IMSI】
2025-07-31 22:53:19:231 ==>> 取到目标值:460130071541268
2025-07-31 22:53:19:257 ==>> 【检测IMSI】通过,【460130071541268】符合目标值【】要求!
2025-07-31 22:53:19:261 ==>> 提取到IMSI:460130071541268
2025-07-31 22:53:19:281 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:53:19:287 ==>> 取到目标值:460130071541268
2025-07-31 22:53:19:291 ==>> 【校验网络运营商(移动)】通过,【460130071541268】符合目标值【】要求!
2025-07-31 22:53:19:295 ==>> 检测【打开CAN通信】
2025-07-31 22:53:19:313 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:53:19:417 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:53:19:581 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:53:19:587 ==>> 检测【检测CAN通信】
2025-07-31 22:53:19:601 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:53:19:676 ==>> $GBGGA,145323.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,39,1*7D

$GBGSV,7,2,25,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,38,43,,,38,16,,,38,11,,,37,1*4D

$GBGSV,7,4,25,24,,,37,1,,,36,10,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,2,,,34,12,,,34,1*76

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145323.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145323.512,0.000,1515.707,1515.707,48.481,2097152,2097152,2097152*54



2025-07-31 22:53:19:706 ==>> can send success


2025-07-31 22:53:19:752 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:53:19:811 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35563
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:53:19:862 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:53:19:867 ==>> 检测【关闭CAN通信】
2025-07-31 22:53:19:870 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:53:19:887 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:53:19:916 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:53:20:145 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:53:20:149 ==>> 检测【打印IMU STATE】
2025-07-31 22:53:20:153 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:53:20:306 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:53:20:414 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:53:20:418 ==>> 检测【六轴自检】
2025-07-31 22:53:20:424 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:53:20:715 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBGGA,145324.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,39,1*7D

$GBGSV,7,2,25,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,24,,,36,1,,,36,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,33,32,,,33,5,,,32,4,,,32,1*49

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145324.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145324.512,0.000,1517.368,1517.368,48.537,2097152,2097152,2097152*5F



2025-07-31 22:53:21:157 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 22:53:21:686 ==>> $GBGGA,145325.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,60,,,40,41,,,39,3,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,24,,,37,1,,,36,10,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145325.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145325.512,0.000,1524.002,1524.002,48.750,2097152,2097152,2097152*5D



2025-07-31 22:53:22:322 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:53:22:503 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38240 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-14,-7,4070]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 22:53:22:608 ==>> $GBGGA,145326.

2025-07-31 22:53:22:683 ==>> 512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,60,,,39,41,,,39,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145326.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145326.512,0.000,1524.000,1524.000,48.748,2097152,2097152,2097152*57



2025-07-31 22:53:22:749 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:53:22:753 ==>> 检测【打印IMU STATE2】
2025-07-31 22:53:22:764 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:53:22:908 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:53:23:032 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:53:23:039 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:53:23:046 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:53:23:120 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:53:23:300 ==>> [D][05:18:27][COMM]read battery soc:255
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:53:23:348 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:53:23:355 ==>> 检测【检测VBUS电压2】
2025-07-31 22:53:23:377 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:23:729 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541268
[D][05:18:28][FCTY]HardwareID  = 867222087952687
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:28][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 50 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLAT

2025-07-31 22:53:23:834 ==>> FORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3767 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGGA,145327.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,33,4,,,33,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145327.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145327.512,0.000,1530.631,1530.631,48.957,2097152,2097152,2097152*56



2025-07-31 22:53:23:882 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:24:272 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541268
[D][05:18:28][FCTY]HardwareID  = 867222087952687
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 100 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 176
[D][05:18:28][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 101 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]C

2025-07-31 22:53:24:317 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3767 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:53:24:412 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:24:877 ==>> [D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071541268
[D][05:18:29][FCTY]HardwareID  = 867222087952687
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 100 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
$GBGGA,145328.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,35,1*76

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,33,4,,,33,1*4E

$GBGSV,7,7,25,44,,,33,1*71

$GBRMC,145328.512,V,,,,,,,,0.0,E

2025-07-31 22:53:24:982 ==>> ,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145328.512,0.000,1533.942,1533.942,49.057,2097152,2097152,2097152*51

[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 145
[D][05:18:29][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 151 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3767 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]msg 0601 loss. last_tick:35542. cur_tick:40550. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40551


2025-07-31 22:53:25:197 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:25:622 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]=====

2025-07-31 22:53:25:727 ==>> ======================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]

2025-07-31 22:53:25:833 ==>> sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5B6807E82500974655957D5996C1C12089D687E52E8519279CC46CF936AA50ED6D1EF1D12E707ABF81D25FA0BEBC2467B6C17B1D565A2FEE3452B8AD6C629B6640DF6FAFE62B3DAD53BE0C61F49B75F129508
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle 

2025-07-31 22:53:25:937 ==>> subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071541268
[D][05:18:30][FCTY]HardwareID  = 867222087952687
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:30][FCTY]Bat         = 3804 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 0,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 118
[D][05:18:30][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 75 mv
[D][05:18:30][FCTY]Bike Type flag is invalied


2025-07-31 22:53:25:985 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:53:25:998 ==>> [D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3767 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:53:26:087 ==>>                                                :30][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:30][GNSS]location stop evt done evt


2025-07-31 22:53:26:377 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071541268
[D][05:18:30][FCTY]HardwareID  = 867222087952687
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = F46918A0BBD9
[D][05:18:30][FCTY]Bat         = 3864 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 105
[D][05:18:30][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCT

2025-07-31 22:53:26:406 ==>> Y]Bat1         = 3767 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:53:26:514 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:53:26:523 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:53:26:545 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:53:26:618 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:53:26:694 ==>> [D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:18:31][COMM]read battery soc:255


2025-07-31 22:53:26:792 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:53:26:798 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:53:26:806 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:53:26:920 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:53:27:074 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:53:27:079 ==>> 检测【打开WIFI(3)】
2025-07-31 22:53:27:087 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:53:27:334 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:53:27:608 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:53:27:613 ==>> 检测【扩展芯片hw】
2025-07-31 22:53:27:636 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:53:27:801 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:53:27:882 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:53:27:887 ==>> 检测【扩展芯片boot】
2025-07-31 22:53:27:900 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:53:27:905 ==>> 检测【扩展芯片sw】
2025-07-31 22:53:27:919 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:53:27:925 ==>> 检测【检测音频FLASH】
2025-07-31 22:53:27:954 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:53:28:077 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:53:28:272 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:53:29:013 ==>>                                          poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]more than the number of battery plugs
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:33][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:18:33][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:33][COMM]Bat auth off fail, error:-1
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
+WIFISCAN:4,0,F88C21BCF57D,-37
+WIFISCAN:4,1,CC057790A621,-57
+

2025-07-31 22:53:29:118 ==>> WIFISCAN:4,2,F42A7D1297A3,-72
+WIFISCAN:4,3,44A1917CA62B,-74

[D][05:18:33][CAT1]wifi scan report total[4]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][

2025-07-31 22:53:29:223 ==>> COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get soc error
[E][05:18:33][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][COMM]Receive Bat Lock cmd 0
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]index:1
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999

2025-07-31 22:53:29:328 ==>> 999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]Main Task receive event:61
[D][05:18:33][COMM][D301]:type:3, trace id:280
[D][05:18:33][COMM]id[], hw[000
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]get mcMaincircuitVolt error
[D][05:18:33][COMM]get mcSubcircuitVolt error
[D][05:18:33][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get bat work state err
[W][05:18:33][PROT]remove success[1629955113],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:33][PROT]add success [1

2025-07-31 22:53:29:433 ==>> 629955113],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:33][COMM]Main Task receive event:61 finished processing
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COM

2025-07-31 22:53:29:523 ==>> M]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]read battery soc:255
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 22:53:29:553 ==>>                                       

2025-07-31 22:53:30:667 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]Send Data To Server[198][201]

2025-07-31 22:53:30:743 ==>>  ... ->:
0063B98D113311331133113311331B88B32133051CBE913DFA0BF2CA5A26B55595AA66AEC2791A3E34D23B56CBCBA443D74F8CB93DE8DC05191B9F9BACE8F49523CC545A1017B1EF9CAC487FA585A4C044B2F169C7704687036F446391B7564EC52D48
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]


2025-07-31 22:53:30:772 ==>>                                          

2025-07-31 22:53:31:349 ==>> [D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:53:32:236 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 22:53:32:463 ==>> [D][05:18:37][COMM]48124 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:37][COMM]accel parse set 0
[D][05:18:37][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:53:32:719 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 22:53:32:967 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:53:32:973 ==>> 检测【打开喇叭声音】
2025-07-31 22:53:32:982 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:53:33:712 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18

2025-07-31 22:53:33:762 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:53:33:771 ==>> 检测【打开大灯控制】
2025-07-31 22:53:33:782 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:53:33:817 ==>> :38][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:38][COMM]49137 imu init OK
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2

2025-07-31 22:53:33:922 ==>> 048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:53:33:997 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:53:34:050 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:53:34:056 ==>> 检测【关闭仪表供电3】
2025-07-31 22:53:34:064 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:53:34:193 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 22:53:34:332 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:53:34:337 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:53:34:344 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:53:34:468 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:53:34:611 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:53:34:617 ==>> 检测【读大灯电压】
2025-07-31 22:53:34:622 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:53:34:712 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:53:34:802 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[32969]


2025-07-31 22:53:34:880 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:53:34:886 ==>> 检测【关闭大灯控制2】
2025-07-31 22:53:34:910 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:53:35:077 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:53:35:156 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:53:35:165 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:53:35:185 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:53:35:303 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:53:35:427 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:53:35:436 ==>> 检测【打开WIFI(4)】
2025-07-31 22:53:35:460 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:53:35:840 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:18:40][CAT1]<<< 
+CME ERROR: 100

[D][05:18:40][CAT1]exec over: func id: 12, ret: 19
[D][05:18:40][PROT]CLEAN,SEND:1
[D][05:18:40][PROT]index:1 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:2
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:40][CAT1]gsm read msg 

2025-07-31 22:53:35:870 ==>> sub id: 15
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 22:53:36:056 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:53:36:062 ==>> 检测【EC800M模组版本】
2025-07-31 22:53:36:067 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:53:36:313 ==>> [D][05:18:40][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B31E136A9689BAA763169037B346E9838D3C0503031E84B9AFDF1D254B33477BD4A248582B98836ACD7DD932C09099787B4B791141D6646C674EE049BECE81B1FFD50F0581F8A211C76782727461596C71354C
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]
[W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:53:36:590 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:53:36:596 ==>> 检测【配置蓝牙地址】
2025-07-31 22:53:36:619 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:53:36:774 ==>> [D][05:18:41][COMM]read battery soc:255
[W][05:18:41][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:53:36:804 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:F46918A0BBD9>】
2025-07-31 22:53:37:015 ==>> recv ble 1
recv ble 2
ble set mac ok :f4,69,18,a0,bb,d9
enable filters ret : 0

2025-07-31 22:53:37:082 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:53:37:088 ==>> 检测【BLETEST】
2025-07-31 22:53:37:105 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:53:37:165 ==>> [D][05:18:41][COMM]52925 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:53:37:210 ==>> 4A A4 01 A4 4A 


2025-07-31 22:53:37:422 ==>> recv ble 1
recv ble 2
<BSJ*MAC:F46918A0BBD9*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9F46918A0BBD999999OVER 150


2025-07-31 22:53:38:115 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:53:38:125 ==>> 该项需要延时执行
2025-07-31 22:53:38:183 ==>> [D][05:18:42][COMM]53937 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:53:38:838 ==>> [D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:53:39:170 ==>> [D][05:18:43][COMM]54948 imu init OK


2025-07-31 22:53:40:378 ==>> +WIFISCAN:4,0,CC057790A621,-58
+WIFISCAN:4,1,CC057790A5C0,-82
+WIFISCAN:4,2,CC057790A5C1,-82
+WIFISCAN:4,3,F86FB0660A82,-88

[D][05:18:45][CAT1]wifi scan report total[4]


2025-07-31 22:53:40:727 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:53:41:135 ==>> [D][05:18:45][GNSS]recv submsg id[3]


2025-07-31 22:53:41:540 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3E19C23381EE4232FA6C6D5E30081C56BFC791A6A1B8ECEEED2180B486B56B07A68BB7E37B78D873AE2A59AF7681B4EBB67

2025-07-31 22:53:41:600 ==>> 030F498A5BED98A8A1E7125274956CC5836F807D28EEE0AA58AAC6DF7913E8BDBD
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 22:53:42:732 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:53:44:753 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:53:46:831 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:


2025-07-31 22:53:46:906 ==>> 0063B982113311331133113311331B88B5D689779A5FFFC59E61C42B4CAB24A3D53614F68B36BE32BF30B1BB991C8C99030188114474B87AB6E7E7FADBE8FD91A03CED558314393507A6E46D49A105FDB404C1D2151096BD594D3CE22792B3226AB56E
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
[D][05:18:51][COMM]read battery soc:255


2025-07-31 22:53:48:117 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:53:48:128 ==>> 检测【检测WiFi结果】
2025-07-31 22:53:48:152 ==>> WiFi信号:【F88C21BCF57D】,信号值:-37
2025-07-31 22:53:48:159 ==>> WiFi信号:【CC057790A621】,信号值:-57
2025-07-31 22:53:48:184 ==>> WiFi信号:【44A1917CA62B】,信号值:-74
2025-07-31 22:53:48:197 ==>> WiFi信号:【CC057790A5C0】,信号值:-82
2025-07-31 22:53:48:216 ==>> WiFi信号:【CC057790A5C1】,信号值:-82
2025-07-31 22:53:48:226 ==>> WiFi信号:【F86FB0660A82】,信号值:-88
2025-07-31 22:53:48:232 ==>> WiFi数量【6】, 最大信号值:-37
2025-07-31 22:53:48:242 ==>> 检测【检测GPS结果】
2025-07-31 22:53:48:261 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:53:48:300 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all continue location stop
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all sing location stop


2025-07-31 22:53:48:747 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 22:53:49:121 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:53:49:127 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:49:137 ==>> 定位已等待【1】秒.
2025-07-31 22:53:49:520 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:54][COMM]Open GPS Module...
[D][05:18:54][COMM]LOC_MODEL_CONT
[D][05:18:54][GNSS]start event:8
[D][05:18:54][GNSS]GPS start. ret=0
[W][05:18:54][GNSS]start cont locating
[D][05:18:54][CAT1]gsm read msg sub id: 23
[D][05:18:54][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:54][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:53:50:122 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:50:139 ==>> 定位已等待【2】秒.
2025-07-31 22:53:50:230 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:53:50:753 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:53:51:098 ==>> [D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,40,,,40,34,,,39,59,,,39,41,,,39,1*76

$GBGSV,2,2,07,39,,,32,25,,,19,60,,,41,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1437.459,1437.459,46.218,2097152,2097152,2097152*46

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:55][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 23, ret: 6
[D][05:18:55][CAT1]sub id: 23, ret: 6



2025-07-31 22:53:51:128 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:51:137 ==>> 定位已等待【3】秒.
2025-07-31 22:53:51:173 ==>> [D][05:18:55][GNSS]recv submsg id[1]
[D][05:18:55][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:53:52:072 ==>> [D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ce8] format[0]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B559B5

2025-07-31 22:53:52:132 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:52:142 ==>> 定位已等待【4】秒.
2025-07-31 22:53:52:177 ==>> 306E2495C958705F2EECE66C67047DA128D5E7BFA468FBE43A8016A33B5DB931F53D65DC0D1A849A6E6FECDE735D798E69FEF0625A196995CA6292744601E0A32A4CBBB4A58A69EDE50799D19A7A6886
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,60,,,41,40,,,41,34,,,40,59,,,40,1*7E

$GBGSV,3,2,12,41,,,40,3,,,39,7,,,38,39,,,37,1*7E

$GBGSV,3,3,12,25,,,35,5,,,27,1,,,38,4,,,36,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1567.180,1567.180,50.188,2097152,2097152,2097152*4B



2025-07-31 22:53:52:772 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 22:53:53:031 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,40,,,41,60,,,40,34,,,40,59,,,40,1*7E

$GBGSV,4,2,14,41,,,40,3,,,39,7,,,38,39,,,38,1*70

$GBGSV,4,3,14,43,,,38,25,,,37,1,,,35,2,,,33,1*7E

$GBGSV,4,4,14,4,,,32,5,,,29,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1539.909,1539.909,49.291,2097152,2097152,2097152*48



2025-07-31 22:53:53:136 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:53:149 ==>> 定位已等待【5】秒.
2025-07-31 22:53:54:053 ==>> $GBGGA,145357.902,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,40,59,,,40,60,,,39,34,,,39,1*72

$GBGSV,5,2,18,41,,,39,3,,,39,39,,,39,7,,,38,1*72

$GBGSV,5,3,18,43,,,38,25,,,38,1,,,36,23,,,35,1*4A

$GBGSV,5,4,18,2,,,33,24,,,33,4,,,32,32,,,32,1*7F

$GBGSV,5,5,18,5,,,30,16,,,39,1*44

$GBRMC,145357.902,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145357.902,0.000,1512.026,1512.026,48.388,2097152,2097152,2097152*54



2025-07-31 22:53:54:143 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:54:153 ==>> 定位已等待【6】秒.
2025-07-31 22:53:54:682 ==>> $GBGGA,145358.502,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,40,59,,,40,60,,,39,34,,,39,1*78

$GBGSV,6,2,22,41,,,39,3,,,39,39,,,39,7,,,39,1*79

$GBGSV,6,3,22,43,,,38,25,,,38,16,,,37,11,,,37,1*74

$GBGSV,6,4,22,1,,,36,23,,,35,10,,,35,6,,,35,1*70

$GBGSV,6,5,22,24,,,34,2,,,33,12,,,33,4,,,32,1*70

$GBGSV,6,6,22,32,,,32,5,,,31,1*41

$GBRMC,145358.502,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145358.502,0.000,1507.574,1507.574,48.229,2097152,2097152,2097152*5D



2025-07-31 22:53:54:772 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 22:53:55:147 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:55:156 ==>> 定位已等待【7】秒.
2025-07-31 22:53:55:683 ==>> $GBGGA,145359.502,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,59,,,40,60,,,40,3,,,40,1*4C

$GBGSV,6,2,23,34,,,39,41,,,39,39,,,39,25,,,39,1*7C

$GBGSV,6,3,23,7,,,38,43,,,38,16,,,37,11,,,37,1*45

$GBGSV,6,4,23,1,,,36,10,,,36,23,,,35,6,,,35,1*72

$GBGSV,6,5,23,24,,,35,12,,,34,2,,,33,4,,,32,1*77

$GBGSV,6,6,23,32,,,32,9,,,31,5,,,31,1*7B

$GBRMC,145359.502,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145359.502,0.000,1508.732,1508.732,48.277,2097152,2097152,2097152*57



2025-07-31 22:53:56:155 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:56:165 ==>> 定位已等待【8】秒.
2025-07-31 22:53:56:682 ==>> $GBGGA,145400.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,34,,,40,41,,,40,1*7C

$GBGSV,6,2,24,39,,,40,60,,,39,3,,,39,25,,,39,1*42

$GBGSV,6,3,24,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,6,4,24,1,,,36,10,,,36,24,,,36,23,,,35,1*46

$GBGSV,6,5,24,6,,,35,12,,,34,2,,,33,32,,,33,1*74

$GBGSV,6,6,24,4,,,32,9,,,32,5,,,32,44,,,31,1*4B

$GBRMC,145400.502,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145400.502,0.000,1513.240,1513.240,48.422,2097152,2097152,2097152*5A



2025-07-31 22:53:56:772 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 22:53:57:167 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:57:179 ==>> 定位已等待【9】秒.
2025-07-31 22:53:57:257 ==>> [D][05:19:01][PROT]CLEAN,SEND:0
[D][05:19:01][PROT]CLEAN:0
[D][05:19:01][PROT]index:2 1629955141
[D][05:19:01][PROT]is_send:0
[D][05:19:01][PROT]sequence_num:6
[D][05:19:01][PROT]retry_timeout:0
[D][05:19:01][PROT]retry_times:3
[D][05:19:01][PROT]send_path:0x2
[D][05:19:01][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:01][PROT]===========================================================
[W][05:19:01][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955141]
[D][05:19:01][PROT]===========================================================
[D][05:19:01][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:01][PROT]sending traceid [9999999999900007]
[D][05:19:01][PROT]Send_TO_M2M [1629955141]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:01][SAL ]sock send credit cnt[6]
[D][05:19:01][SAL ]sock send ind credit cnt[6]
[D][05:19:01][M2M ]m2m send data len[134]
[D][05:19:01][SAL ]Cellular task submsg id[10]
[D][05:19:01][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052ce8] format[0]
[D][05:19:01][CAT1]gsm read msg sub id: 15
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:01][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:01][CAT1]Send Dat

2025-07-31 22:53:57:332 ==>> a To Server[134][137] ... ->:
0043B683113311331133113311331B88BE7102B03F6A66179976803281586E60984E311900107EB9D0FB235E347911F29BF19F5CF657360A915D5BC4C37247BF295B92
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:01][PROT]M2M Send ok [1629955141]


2025-07-31 22:53:57:700 ==>> $GBGGA,145401.502,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,41,,,40,1*7C

$GBGSV,7,2,25,39,,,40,3,,,40,60,,,39,25,,,39,1*4C

$GBGSV,7,3,25,7,,,39,43,,,38,16,,,38,11,,,38,1*43

$GBGSV,7,4,25,1,,,37,24,,,37,33,,,37,10,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,32,,,33,1*43

$GBGSV,7,6,25,9,,,33,2,,,32,4,,,32,5,,,32,1*7B

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145401.502,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145401.502,0.000,760.584,760.584,695.572,2097152,2097152,2097152*69



2025-07-31 22:53:58:180 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:58:189 ==>> 定位已等待【10】秒.
2025-07-31 22:53:58:696 ==>> $GBGGA,145402.502,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,7,2,25,34,,,40,41,,,40,7,,,39,3,,,39,1*72

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,33,1*70

$GBGSV,7,6,25,9,,,33,32,,,33,5,,,32,4,,,32,1*49

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145402.502,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145402.502,0.000,759.757,759.757,694.816,2097152,2097152,2097152*64



2025-07-31 22:53:58:771 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 22:53:59:185 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:53:59:194 ==>> 定位已等待【11】秒.
2025-07-31 22:53:59:679 ==>> $GBGGA,145403.502,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,41,,,40,1*7A

$GBGSV,7,2,25,7,,,39,3,,,39,60,,,39,59,,,39,1*7A

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,10,,,36,6,,,36,1,,,36,1*73

$GBGSV,7,5,25,33,,,36,23,,,36,12,,,35,9,,,34,1*49

$GBGSV,7,6,25,2,,,33,32,,,33,5,,,32,4,,,32,1*42

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145403.502,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145403.502,0.000,758.923,758.923,694.052,2097152,2097152,2097152*6D



2025-07-31 22:54:00:187 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:00:199 ==>> 定位已等待【12】秒.
2025-07-31 22:54:00:699 ==>> $GBGGA,145404.502,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,59,,,40,34,,,40,1*73

$GBGSV,7,2,25,41,,,40,7,,,39,3,,,39,60,,,39,1*7D

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,10,,,36,1,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,9,,,34,1*7C

$GBGSV,7,6,25,2,,,33,32,,,33,5,,,32,4,,,32,1*42

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145404.502,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145404.502,0.000,758.925,758.925,694.055,2097152,2097152,2097152*6D



2025-07-31 22:54:00:789 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 22:54:01:189 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:01:201 ==>> 定位已等待【13】秒.
2025-07-31 22:54:01:680 ==>> $GBGGA,145405.502,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145405.502,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145405.502,0.000,761.408,761.408,696.325,2097152,2097152,2097152*6A



2025-07-31 22:54:02:190 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:02:200 ==>> 定位已等待【14】秒.
2025-07-31 22:54:02:435 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052ce8] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 22:54:02:694 ==>> $GBGGA,145402.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,59,,,40,34,,,40,1*73

$GBGSV,7,2,25,41,,,40,7,,,39,60,,,39,3,,,39,1*7D

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,1,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145402.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145402.512,0.923,760.578,760.578,695.566,2097152,2097152,2097152*66



2025-07-31 22:54:02:799 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 22:54:03:200 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:03:210 ==>> 定位已等待【15】秒.
2025-07-31 22:54:03:704 ==>> $GBGGA,145403.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,59,,,40,39,,,40,1*47

$GBGSV,7,2,25,34,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145403.512,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145403.512,0.908,761.408,761.408,696.325,2097152,2097152,2097152*6C



2025-07-31 22:54:04:214 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:04:225 ==>> 定位已等待【16】秒.
2025-07-31 22:54:04:698 ==>> $GBGGA,145404.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,41,,,40,1*71

$GBGSV,7,2,25,7,,,39,3,,,39,60,,,39,25,,,39,1*71

$GBGSV,7,3,25,34,,,39,11,,,38,43,,,38,16,,,37,1*7C

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,145404.512,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145404.512,0.884,758.921,758.921,694.050,2097152,2097152,2097152*6D



2025-07-31 22:54:04:788 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 22:54:05:215 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:05:225 ==>> 定位已等待【17】秒.
2025-07-31 22:54:05:697 ==>> $GBGGA,145405.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,41,,,40,3,,,39,1*40

$GBGSV,7,2,25,60,,,39,59,,,39,25,,,39,34,,,39,1*7E

$GBGSV,7,3,25,7,,,38,11,,,38,43,,,38,16,,,37,1*4D

$GBGSV,7,4,25,24,,,37,10,,,36,1,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,33,32,,,33,5,,,32,4,,,32,1*49

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145405.512,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145405.512,0.872,756.433,756.433,691.775,2097152,2097152,2097152*60



2025-07-31 22:54:06:225 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:06:243 ==>> 定位已等待【18】秒.
2025-07-31 22:54:06:705 ==>> $GBGGA,145406.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,3,,,39,1*49

$GBGSV,7,2,25,60,,,39,25,,,39,34,,,39,41,,,39,1*77

$GBGSV,7,3,25,7,,,38,16,,,38,43,,,38,24,,,37,1*4B

$GBGSV,7,4,25,11,,,37,1,,,36,33,,,36,23,,,36,1*43

$GBGSV,7,5,25,10,,,35,6,,,35,12,,,35,2,,,34,1*74

$GBGSV,7,6,25,9,,,33,32,,,33,5,,,32,4,,,32,1*49

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145406.512,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145406.512,0.868,755.606,755.606,691.019,2097152,2097152,2097152*65



2025-07-31 22:54:06:795 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 22:54:07:240 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:07:250 ==>> 定位已等待【19】秒.
2025-07-31 22:54:07:696 ==>> $GBGGA,145407.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,59,,,40,39,,,40,1*72

$GBGSV,7,2,25,34,,,40,41,,,40,7,,,39,3,,,39,1*72

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,5,,,33,32,,,33,4,,,32,1*4F

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145407.512,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145407.512,0.852,763.056,763.056,697.831,2097152,2097152,2097152*69



2025-07-31 22:54:08:244 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:08:255 ==>> 定位已等待【20】秒.
2025-07-31 22:54:08:695 ==>> $GBGGA,145408.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,60,,,40,59,,,40,1*4B

$GBGSV,7,2,25,39,,,40,34,,,40,41,,,40,7,,,39,1*45

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,10,,,36,1,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,34,5,,,33,4,,,32,1*48

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145408.513,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145408.513,0.835,763.883,763.883,698.588,2097152,2097152,2097152*66



2025-07-31 22:54:08:800 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 22:54:09:256 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:09:268 ==>> 定位已等待【21】秒.
2025-07-31 22:54:09:703 ==>> $GBGGA,145409.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,60,,,40,59,,,40,1*4B

$GBGSV,7,2,25,39,,,40,34,,,40,41,,,40,7,,,39,1*45

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,10,,,36,1,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145409.513,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145409.513,0.818,762.234,762.234,697.080,2097152,2097152,2097152*6A



2025-07-31 22:54:10:266 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:10:278 ==>> 定位已等待【22】秒.
2025-07-31 22:54:10:700 ==>> $GBGGA,145410.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,39,,,40,34,,,40,1*73

$GBGSV,7,2,25,41,,,40,7,,,39,3,,,39,60,,,39,1*7D

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,24,,,37,10,,,36,1,,,36,33,,,36,1*45

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145410.513,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145410.513,0.813,760.575,760.575,695.563,2097152,2097152,2097152*63



2025-07-31 22:54:10:806 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 22:54:11:278 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:11:288 ==>> 定位已等待【23】秒.
2025-07-31 22:54:11:696 ==>> $GBGGA,145411.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,59,,,40,39,,,40,1*72

$GBGSV,7,2,25,34,,,40,41,,,40,7,,,39,3,,,39,1*72

$GBGSV,7,3,25,25,,,39,16,,,38,43,,,38,1,,,37,1*4D

$GBGSV,7,4,25,24,,,37,11,,,37,10,,,36,6,,,36,1*43

$GBGSV,7,5,25,33,,,36,23,,,36,12,,,35,2,,,34,1*42

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145411.513,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145411.513,0.797,762.230,762.230,697.076,2097152,2097152,2097152*62



2025-07-31 22:54:12:291 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:12:301 ==>> 定位已等待【24】秒.
2025-07-31 22:54:12:460 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:17][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:54:12:890 ==>> $GBGGA,145412.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,60,,,40,59,,,40,1*4B

$GBGSV,7,2,25,39,,,40,34,,,40,41,,,40,7,,,39,1*45

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,6,,,36,1*72

$GBGSV,7,5,25,33,,,36,23,,,36,12,,,35,2,,,34,1*42

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145412.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145412.513,0.782,763.887,763.887,698.592,2097152,2097152,2097152*65

[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 10, ret: 6
[D][05:19:17][CAT1]sub id: 10, ret: 6

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:17][M2M ]m2m gsm shut done, ret[0]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[

2025-07-31 22:54:12:949 ==>> 0x20052db0], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:17][COMM]read battery soc:255


2025-07-31 22:54:13:054 ==>> [D][05:19:17][CAT1]pdpdeact urc len[22]


2025-07-31 22:54:13:299 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:13:320 ==>> 定位已等待【25】秒.
2025-07-31 22:54:13:702 ==>> $GBGGA,145413.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,60,,,40,59,,,40,1*4B

$GBGSV,7,2,25,39,,,40,34,,,40,41,,,40,7,,,39,1*45

$GBGSV,7,3,25,25,,,39,16,,,38,11,,,38,43,,,38,1*73

$GBGSV,7,4,25,1,,,37,24,,,37,10,,,36,33,,,36,1*44

$GBGSV,7,5,25,23,,,36,6,,,35,12,,,35,2,,,34,1*77

$GBGSV,7,6,25,9,,,34,32,,,33,5,,,32,4,,,32,1*4E

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,145413.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145413.513,0.766,763.061,763.061,697.836,2097152,2097152,2097152*62



2025-07-31 22:54:14:230 ==>> [D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:18][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:18][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 22:54:14:305 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:14:315 ==>> 定位已等待【26】秒.
2025-07-31 22:54:14:340 ==>>                                                   tx ret[12] >>> AT+QIACT=1

[D][05:19:19

2025-07-31 22:54:14:380 ==>> ][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:54:15:312 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:15:323 ==>> 定位已等待【27】秒.
2025-07-31 22:54:15:417 ==>> [D][05:19:19][CAT1]opened : 0, 0
[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:19][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]index:2 1629955159
[D][05:19:19][PROT]is_send:0
[D][05:19:19][PROT]sequence_num:6
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:1
[D][05:19:19][PROT]send_path:0x2
[D][05:19:19][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:19][PROT]sending traceid [9999999999900007]
[D][05:19:19][PROT]Send_TO_M2M [1629955159]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:19][SAL ]sock send credit cnt[6]
[D][05:19:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[134]
[D][

2025-07-31 22:54:15:522 ==>> 05:19:19][SAL ]Cellular task submsg id[10]
[D][05:19:19][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BEA2B6908641B5090B6D3F2B79F5E39AD43D3480A3F51BA6CF07F16F3C3EF83E533A8516B3E1D0DC18176A1F56DBFB950EBC2C
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]
$GBGGA,145414.509,2301.2564489,N,11421.9430653,E,1,17,0.73,76.744,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,40,40,3,60,190,40,1*4D



2025-07-31 22:54:15:627 ==>> 
$GBGSV,7,2,25,6,60,8,35,16,60,12,38,10,53,212,36,59,52,129,40,1*75

$GBGSV,7,3,25,9,52,344,34,25,50,4,39,1,48,125,36,34,46,94,40,1*41

$GBGSV,7,4,25,2,45,237,34,11,44,131,38,60,41,239,40,41,39,255,40,1*4E

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,36,1*74

$GBRMC,145414.509,A,2301.2564489,N,11421.9430653,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[D][05:19:19][GNSS]HD8040 GPS
[D][05:19:19][GNSS]GPS diff_sec 124018495, report 0x42 frame
$GBGST,145414.509,1.268,0.146,0.150,0.218,2.279,2.441,7.324*76

[D][05:19:19][COMM]Main Task receive event:131
[D][05:19:19][COMM]index:0,power_mode:0xFF
[D][05:19:19][COMM]index:1,sound_mode:0xFF
[D][05:19:19][COMM]index:2,gsensor_mode:0xFF
[D][05:19:19][COMM]index:3,report_freq_mode:0xFF
[D][05:19:19][COMM]index:4,report_period:0xFF
[D][05:19:19][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:19][COMM]index:6,normal_reset_period:0xFF
[D][05:19:19][COMM]index:7,spock_over_speed:0xFF
[D][05:19:19][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:19][COMM]index:9,spock_report_period_unlock:0xFF


2025-07-31 22:54:15:732 ==>> 
[D][05:19:19][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:19][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:19][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:19][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:19][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:19][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:19][COMM]index:16,imu_config_params:0xFF
[D][05:19:19][COMM]index:17,long_connect_params:0xFF
[D][05:19:19][COMM]index:18,detain_mark:0xFF
[D][05:19:19][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:19][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:19][COMM]index:21,mc_mode:0xFF
[D][05:19:19][COMM]index:22,S_mode:0xFF
[D][05:19:19][COMM]index:23,overweight:0xFF
[D][05:19:19][COMM]index:24,standstill_mode:0xFF
[D][05:19:19][COMM]index:25,night_mode:0xFF
[D][05:19:19][COMM]index:26,experiment1:0xFF
[D][05:19:19][COMM]index:27,experiment2:0xFF
[D][05:19:19][COMM]index:28,experiment3:0xFF
[D][05:19:19][COMM]index:29,experiment4:0xFF
[D][05:19:19][COMM]index:30,night_mode_start:0xFF
[D][05:19:19][COMM]index:31,night_mode_end:0xFF
[D][05:19:19][COMM]index:33,park_report_minutes:0xFF
[D][05:19:19][COMM]index:34,park_report_mode:0xFF
[D][05

2025-07-31 22:54:15:837 ==>> :19:19][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:19][COMM]index:38,charge_battery_para: FF
[D][05:19:19][COMM]index:39,multirider_mode:0xFF
[D][05:19:19][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:19][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:19][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:19][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:19][COMM]index:44,riding_duration_config:0xFF
[D][05:19:19][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:19][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:19][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:19][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:19][COMM]index:49,mc_load_startup:0xFF
[D][05:19:19][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:19][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:19][COMM]index:52,traffic_mode:0xFF
[D][05:19:19][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:19][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:19][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:19][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:19][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:19][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][0

2025-07-31 22:54:15:942 ==>> 5:19:19][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:19][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:19][COMM]index:63,experiment5:0xFF
[D][05:19:19][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:19][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:19][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:19][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:19][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:19][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:19][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:19][COMM]index:72,experiment6:0xFF
[D][05:19:19][COMM]index:73,experiment7:0xFF
[D][05:19:19][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:19][COMM]index:75,zero_value_from_server:-1
[D][05:19:19][COMM]index:76,multirider_threshold:255
[D][05:19:19][COMM]index:77,experiment8:255
[D][05:19:19][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:19][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:19][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:19][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:19][COMM]index:83,loc_report_interval:255
[D][05:19:19][COMM]index:84,mul

2025-07-31 22:54:16:047 ==>> tirider_threshold_p2:255
[D][05:19:19][COMM]index:85,multirider_strategy:255
[D][05:19:19][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:19][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:19][COMM]index:90,weight_param:0xFF
[D][05:19:19][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:19][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:19][COMM]index:95,current_limit:0xFF
[D][05:19:19][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:19][COMM]index:100,location_mode:0xFF

[W][05:19:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:19][COMM]Main Task receive event:131 finished processing
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19]

2025-07-31 22:54:16:320 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:54:16:332 ==>> 定位已等待【28】秒.
2025-07-31 22:54:16:355 ==>> $GBGGA,145416.000,2301.2570268,N,11421.9431096,E,1,17,0.73,77.490,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,40,40,3,60,190,40,1*4D

$GBGSV,7,2,25,6,60,8,35,16,60,12,37,10,53,212,36,59,52,129,40,1*7A

$GBGSV,7,3,25,9,52,344,33,25,50,4,39,1,48,125,36,34,46,94,39,1*48

$GBGSV,7,4,25,2,45,237,34,11,44,131,37,60,41,239,39,41,39,255,40,1*4F

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,35,1*77

$GBGSV,3,1,10,40,70,176,40,39,62,40,38,25,50,4,38,34,46,94,40,5*76

$GBGSV,3,2,10,41,39,255,38,43,36,167,37,23,27,303,38,24,22,69,36,5*41

$GBGSV,3,3,10,32,14,310,35,44,12,46,34,5*45

$GBRMC,145416.000,A,2301.2570268,N,11421.9431096,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,145416.000,1.818,0.354,0.375,0.520,1.719,1.713,3.901*73



2025-07-31 22:54:16:832 ==>> [D][05:19:21][COMM]read battery soc:255


2025-07-31 22:54:17:330 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 22:54:17:343 ==>> $GBGGA,145417.000,2301.2572207,N,11421.9432533,E,1,17,0.73,78.150,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,40,7,66,204,38,39,62,40,39,3,60,190,39,1*4D

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,35,59,52,129,40,1*78

$GBGSV,7,3,25,9,52,344,33,25,50,4,39,1,48,125,36,34,46,94,39,1*48

$GBGSV,7,4,25,2,45,237,33,11,44,131,37,60,41,239,39,41,39,255,39,1*46

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,35,1*77

$GBGSV,3,1,10,40,70,176,41,39,62,40,40,25,50,4,40,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,39,43,36,167,37,23,27,303,38,24,22,69,36,5*40

$GBGSV,3,3,10,32,14,310,35,44,12,46,34,5*45

$GBRMC,145417.000,A,2301.2572207,N,11421.9432533,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,145417.000,1.651,0.212,0.222,0.317,1.485,1.491,3.323*76



2025-07-31 22:54:17:352 ==>> 
北斗星号:【40】,信号值:【40】
北斗星号:【7】,信号值:【39】
北斗星号:【39】,信号值:【38】
北斗星号:【3】,信号值:【40】
北斗星号:【6】,信号值:【35】
北斗星号:【16】,信号值:【37】
北斗星号:【10】,信号值:【36】
北斗星号:【59】,信号值:【40】
北斗星号:【25】,信号值:【38】
北斗星号:【1】,信号值:【36】
北斗星号:【34】,信号值:【40】
北斗星号:【11】,信号值:【37】
北斗星号:【60】,信号值:【39】
北斗星号:【41】,信号值:【38】
北斗星号:【43】,信号值:【37】
北斗星号:【12】,信号值:【35】
北斗星号:【23】,信号值:【38】
北斗星号:【24】,信号值:【36】
北斗星号:【32】,信号值:【35】
北斗星号:【33】,信号值:【35】

2025-07-31 22:54:17:377 ==>> 检测【CSQ强度】
2025-07-31 22:54:17:388 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:54:17:526 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:22][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:22][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:54:17:693 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 22:54:17:703 ==>> 检测【关闭GSM联网】
2025-07-31 22:54:17:727 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:54:17:904 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:22][COMM]GSM test
[D][05:19:22][COMM]GSM test disable


2025-07-31 22:54:17:968 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:54:17:983 ==>> 检测【4G联网测试】
2025-07-31 22:54:18:005 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:54:18:950 ==>> $GBGGA,145418.000,2301.2573213,N,11421.9433631,E,1,17,0.73,78.629,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,40,7,66,204,38,39,62,40,39,3,60,190,39,1*4D

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,59,52,129,40,1*7B

$GBGSV,7,3,25,9,52,344,33,25,50,4,39,1,48,125,36,34,46,94,39,1*48

$GBGSV,7,4,25,2,45,237,34,11,44,131,37,60,41,239,39,41,39,255,39,1*41

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,35,1*77

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,40,43,36,167,37,23,27,303,38,24,22,69,36,5*4E

$GBGSV,3,3,10,32,14,310,35,44,12,46,33,5*42

$GBRMC,145418.000,A,2301.2573213,N,11421.9433631,E,0.004,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.004,N,0.008,K,A*23

$GBGST,145418.000,1.574,0.232,0.244,0.344,1.359,1.369,2.976*74

[W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handl

2025-07-31 22:54:19:055 ==>> erPeriodRep, g_elecBatMissedCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:22][COMM]index:21,mc_mode:0xFF
[D

2025-07-31 22:54:19:160 ==>> ][05:19:22][COMM]index:22,S_mode:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:22][COMM]index:48,shl

2025-07-31 22:54:19:265 ==>> mt_sensor_en:0xFF
[D][05:19:22][COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:72,experiment6:0xFF
[D][05:19:22][COMM]in

2025-07-31 22:54:19:370 ==>> dex:73,experiment7:0xFF
[D][05:19:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162],send_path[2],type[0000],priority[0],ind

2025-07-31 22:54:19:475 ==>> ex[0],used[0]
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][PROT]index:0 1629955162
[D][05:19:22][PROT]is_send:0
[D][05:19:22][PROT]sequence_num:8
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:1
[D][05:19:22][PROT]send_path:0x2
[D][05:19:22][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]sending traceid [9999999999900009]
[D][05:19:22][PROT]Send_TO_M2M [1629955162]
[W][05:19:22][PROT]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:23][CAT1]gsm read msg sub id: 13
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:23][SAL ]sock send credit cnt[6]
[D][05:19:23][SAL ]sock send ind credit cnt[6]
[D][05:19:23][M2M ]m2m send data len[294]
[D][05:19:23][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:23][SAL ]Cellular task submsg id[10]
[D][05:19:23][SAL ]cellular SEND socket id[0] type[1], len[294], 

2025-07-31 22:54:19:580 ==>> data[0x20052de8] format[0]
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:23][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:23][CAT1]exec over: func id: 13, ret: 21
[D][05:19:23][M2M ]get csq[25]
[D][05:19:23][CAT1]gsm read msg sub id: 15
[D][05:19:23][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:23][CAT1]<<< 
ERROR

>>>>>RESEND ALLSTATE<<<<<
[D][05:19:23][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:23][PROT]remove success[1629955163],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:23][COMM]------>period, report file manifest
[D][05:19:23][COMM]Main Task receive event:14 finished processing
[D][05:19:23][COMM]read battery soc:255


2025-07-31 22:54:19:685 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:54:20:341 ==>> $GBGGA,145420.000,2301.2574365,N,11421.9434564,E,1,17,0.73,79.082,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,40,40,3,60,190,39,1*43

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,59,52,129,39,1*75

$GBGSV,7,3,25,9,52,344,34,25,50,4,39,1,48,125,36,34,46,94,39,1*4F

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,41,239,39,41,39,255,39,1*40

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,310,35,44,12,46,33,5*42

$GBRMC,145420.000,A,2301.2574365,N,11421.9434564,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145420.000,1.775,0.183,0.190,0.273,1.431,1.438,2.735*79



2025-07-31 22:54:20:838 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 22:54:21:338 ==>> $GBGGA,145421.000,2301.2574789,N,11421.9434887,E,1,17,0.73,79.106,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,40,7,66,204,38,39,62,40,39,3,60,190,39,1*4D

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,59,52,129,40,1*7B

$GBGSV,7,3,25,9,52,344,34,25,50,4,39,1,48,125,36,34,46,94,39,1*4F

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,41,239,39,41,39,255,39,1*40

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,310,33,44,12,46,32,1*4D

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,39,24,22,69,37,5*4F

$GBGSV,3,3,10,32,14,310,35,44,12,46,34,5*45

$GBRMC,145421.000,A,2301.2574789,N,11421.9434887,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,145421.000,1.937,0.155,0.161,0.235,1.524,1.529,2.709*7C



2025-07-31 22:54:22:361 ==>> $GBGGA,145422.000,2301.2575030,N,11421.9435025,E,1,17,0.73,79.082,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,41,7,66,204,38,39,62,40,39,3,60,190,39,1*4C

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,59,52,129,40,1*7B

$GBGSV,7,3,25,9,52,344,34,25,50,4,39,1,48,125,36,34,46,94,39,1*4F

$GBGSV,7,4,25,2,45,237,33,11,44,130,37,60,41,239,40,41,39,255,39,1*49

$GBGSV,7,5,25,43,36,167,38,4,32,111,31,12,30,64,34,23,27,303,36,1*78

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,309,33,44,12,46,32,1*45

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,309,36,44,12,46,34,5*4E

$GBRMC,145422.000,A,2301.2575030,N,11421.9435025,E,0.003,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,145422.000,1.723,0.224,0.234,0.329,1.357,1.361,2.497*72



2025-07-31 22:54:22:852 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 22:54:23:334 ==>> $GBGGA,145423.000,2301.2575204,N,11421.9435131,E,1,17,0.73,79.146,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,11,41,43,1.36,0.73,1.15,4*0F

$GBGSA,A,3,12,23,24,32,44,,,,,,,,1.36,0.73,1.15,4*08

$GBGSV,7,1,25,40,70,176,41,7,66,204,38,39,62,40,40,3,60,190,39,1*42

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,59,52,129,40,1*7B

$GBGSV,7,3,25,9,52,344,33,25,50,4,39,1,48,125,36,34,46,94,40,1*46

$GBGSV,7,4,25,2,45,237,33,11,44,130,37,60,41,239,39,41,39,255,39,1*47

$GBGSV,7,5,25,43,36,167,38,4,32,111,31,12,30,64,35,23,27,303,36,1*79

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,309,33,44,12,46,32,1*45

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,309,35,44,12,46,34,5*4D

$GBRMC,145423.000,A,2301.2575204,N,11421.9435131,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,145423.000,1.684,0.183,0.189,0.269,1.316,1.320,2.400*7F



2025-07-31 22:54:24:028 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:54:25:090 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
$GBGGA,145424.000,2301.2575364,N,11421.9435051,E,1,20,0.67,79.285,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,11,1.32,0.67,1.14,4*02

$GBGSA,A,3,60,41,43,12,23,24,32,44,,,,,1.32,0.67,1.14,4*0C

$GBGSV,7,1,25,40,70,176,41,7,66,204,38,39,62,40,40,3,61,190,39,1*43

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,9,52,344,33,1*43

$GBGSV,7,3,25,25,50,4,39,59,49,130,39,1,48,125,36,34,46,94,39,1*7C

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,42,239,39,41,39,255,39,1*43

$GBGSV,7,5,25,43,36,167,38,4,32,111,31,12,30,64,35,23,27,303,36,1*79

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,309,33,44,12,46,32,1*45

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,309,35,44,12,46,33,5*4A

$GBRMC,145424.000,A,2301.2575364,N,11421.9435051,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,145424.000,1.488,0.178,0.184,0.267,1.155,1.160,2.214*71

[D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100004.

2025-07-31 22:54:25:195 ==>>  period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02A3 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02C3 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02C4 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02C5 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 02E3 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 

2025-07-31 22:54:25:300 ==>> 02E4 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 02E5 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0302 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0303 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 0304 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02E6 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100014. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0

2025-07-31 22:54:25:405 ==>> . cur_tick:100015. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100015. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100016. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100016. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100016. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100017. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100017. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100017
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100018
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100018
[D][05:19:29][COMM]Main Task receive event:14
[D][05:19:29][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955169, allstateRepSeconds = 0
[D][05:19:29][COMM]index:0,power_mode:0xFF
[D][05:19:29][COMM]index:1,sound_mode:0xFF
[D][05:19:29][COMM]index:2,gsensor_mode:0xFF
[D][05:19:29][COMM]index:3,report_freq_mode:0xFF
[D][05:19:29][C

2025-07-31 22:54:25:510 ==>> OMM]index:4,report_period:0xFF
[D][05:19:29][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:29][COMM]index:6,normal_reset_period:0xFF
[D][05:19:29][COMM]index:7,spock_over_speed:0xFF
[D][05:19:29][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:29][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:29][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:29][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:29][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:29][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:29][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:29][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:29][COMM]index:16,imu_config_params:0xFF
[D][05:19:29][COMM]index:17,long_connect_params:0xFF
[D][05:19:29][COMM]index:18,detain_mark:0xFF
[D][05:19:29][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:29][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:29][COMM]index:21,mc_mode:0xFF
[D][05:19:29][COMM]index:22,S_mode:0xFF
[D][05:19:29][COMM]index:23,overweight:0xFF
[D][05:19:29][COMM]index:24,standstill_mode:0xFF
[D][05:19:29][COMM]index:25,night_mode:0xFF
[D][05:19:29][COMM]index:26,experiment1:0xFF
[D][05:19:29][COMM]index:27,experiment2:0xFF
[D][05:

2025-07-31 22:54:25:615 ==>> 19:29][COMM]index:28,experiment3:0xFF
[D][05:19:29][COMM]index:29,experiment4:0xFF
[D][05:19:29][COMM]index:30,night_mode_start:0xFF
[D][05:19:29][COMM]index:31,night_mode_end:0xFF
[D][05:19:29][COMM]index:33,park_report_minutes:0xFF
[D][05:19:29][COMM]index:34,park_report_mode:0xFF
[D][05:19:29][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:29][COMM]index:38,charge_battery_para: FF
[D][05:19:29][COMM]index:39,multirider_mode:0xFF
[D][05:19:29][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:29][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:29][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:29][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:29][COMM]index:44,riding_duration_config:0xFF
[D][05:19:29][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:29][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:29][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:29][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:29][COMM]index:49,mc_load_startup:0xFF
[D][05:19:29][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:29][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:29][COMM]index:52,traffic_mode:0xFF
[D][05:19:29][COMM]index:53,traffic_info_collect_freq:0xFF
[D]

2025-07-31 22:54:25:720 ==>> [05:19:29][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:29][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:29][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:29][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:29][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:29][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:29][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:29][COMM]index:63,experiment5:0xFF
[D][05:19:29][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:29][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:29][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:29][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:29][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:29][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:29][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:29][COMM]index:72,experiment6:0xFF
[D][05:19:29][COMM]index:73,experiment7:0xFF
[D][05:19:29][COMM]index

2025-07-31 22:54:25:825 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 22:54:26:342 ==>> $GBGGA,145426.000,2301.2575363,N,11421.9435087,E,1,20,0.67,79.403,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,11,1.32,0.67,1.14,4*02

$GBGSA,A,3,60,41,43,12,23,24,32,44,,,,,1.32,0.67,1.14,4*0C

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,40,40,3,61,190,39,1*42

$GBGSV,7,2,25,6,60,8,35,16,60,13,37,10,53,212,36,9,52,344,33,1*43

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,1,48,125,36,34,46,94,40,1*7C

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,42,239,40,41,39,255,40,1*43

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,309,33,44,12,46,32,1*45

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,309,35,44,12,46,33,5*4A

$GBRMC,145426.000,A,2301.2575363,N,11421.9435087,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,145426.000,1.510,0.196,0.203,0.293,1.155,1.159,2.132*79

                                

2025-07-31 22:54:26:656 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:31][PROT]remove success[1629955171],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:19:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:31][PROT]add success [1629955171],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:31][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:31][COMM][LOC]wifi scan is already running, error
[D][05:19:31][COMM]Main Task receive event:14 finished processing


2025-07-31 22:54:26:901 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 22:54:27:353 ==>> $GBGGA,145427.000,2301.2575406,N,11421.9435204,E,1,21,0.66,79.483,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.31,0.66,1.13,4*06

$GBGSA,A,3,11,60,41,43,12,23,24,32,44,,,,1.31,0.66,1.13,4*09

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,40,40,3,61,190,39,1*42

$GBGSV,7,2,25,6,60,8,36,16,60,13,37,10,53,212,36,9,52,344,34,1*47

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,37,1*70

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,42,239,40,41,39,255,40,1*43

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,24,22,69,37,5,21,256,32,32,14,309,33,44,12,46,32,1*45

$GBGSV,7,7,25,33,,,36,1*74

$GBGSV,3,1,10,40,70,176,41,39,62,40,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,38,24,22,69,36,5*4F

$GBGSV,3,3,10,32,14,309,36,44,12,46,34,5*4E

$GBRMC,145427.000,A,2301.2575406,N,11421.9435204,E,0.003,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,145427.000,1.764,0.174,0.180,0.265,1.341,1.344,2.257*7D



2025-07-31 22:54:28:476 ==>> $GBGGA,145428.000,2301.2575274,N,11421.9435285,E,1,22,0.61,79.515,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,40,1*4D

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,37,1*70

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,42,239,40,41,39,255,40,1*43

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,32,1*71

$GBGSV,3,1,10,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*76

$GBGSV,3,2,10,41,39,255,41,43,36,167,37,23,27,303,39,24,22,69,36,5*4E

$GBGSV,3,3,10,32,14,309,35,44,12,46,34,5*4D

$GBRMC,145428.000,A,2301.2575274,N,11421.9435285,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145428.000,1.990,0.167,0.169,0.249,1.496,1.499,2.366*75

[D][05:19:33][CAT1]exec over: func id: 15, ret: -93
[D][05:19:33][CAT1]sub id: 15, ret: -93

[D][05:19:33][SAL ]Cellular task submsg id[68]
[D][05:19:33][SAL ]handle subcmd ack sub_id[f], 

2025-07-31 22:54:28:581 ==>> socket[0], result[-93]
[D][05:19:33][SAL ]socket send fail. id[4]
[D][05:19:33][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:33][CAT1]gsm read msg sub id: 21
[D][05:19:33][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:33][M2M ]m2m select fd[4]
[D][05:19:33][M2M ]socket[4] Link is disconnected
[D][05:19:33][M2M ]tcpclient close[4]
[D][05:19:33][SAL ]socket[4] has closed
[D][05:19:33][PROT]protocol read data ok
[E][05:19:33][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:33][PROT]M2M Send Fail [1629955173]
[D][05:19:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]cell info report total[0]
[D][05:19:33][CAT1]exec over: func id: 21, ret: 6
[D][05:19:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:33][CAT1]gsm read msg sub id: 13
[D][05:19:33][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:33][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:33][CAT1]exec over: func id: 13, ret: 21
[D][05:19:33][CAT1]gsm read msg sub id: 10
[D][05:19:33][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:33][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:33][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:54:28:914 ==>> [D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 10, ret: 6
[D][05:19:33][CAT1]sub id: 10, ret: 6

[D][05:19:33][SAL ]Cellular task submsg id[68]
[D][05:19:33][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:33][M2M ]m2m gsm shut done, ret[0]
[D][05:19:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:33][SAL ]open socket ind id[4], rst[0]
[D][05:19:33][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:33][SAL ]Cellular task submsg id[8]
[D][05:19:33][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:33][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:33][CAT1]gsm read msg sub id: 8
[D][05:19:33][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:33][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:33][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:33][CAT1]tx ret[12] >>> AT+CGATT=1

                                         

2025-07-31 22:54:29:095 ==>> [D][05:19:33][CAT1]pdpdeact urc len[22]


2025-07-31 22:54:29:353 ==>> $GBGGA,145429.000,2301.2575190,N,11421.9435462,E,1,22,0.61,79.476,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,39,1*43

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,37,1*70

$GBGSV,7,4,25,2,45,237,34,11,44,130,37,60,42,239,40,41,39,255,40,1*43

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,33,32,14,309,33,1*78

$GBGSV,7,7,25,44,12,46,32,1*71

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,37,5*7F

$GBGSV,3,3,11,24,22,69,37,32,14,309,36,44,12,46,34,5*42

$GBRMC,145429.000,A,2301.2575190,N,11421.9435462,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,145429.000,1.937,0.160,0.162,0.243,1.455,1.458,2.302*7F



2025-07-31 22:54:30:076 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:54:31:117 ==>> [D][05:19:34][CAT1]<<< 
OK

$GBGGA,145430.000,2301.2575117,N,11421.9435422,E,1,22,0.61,79.436,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,40,1*4D

[D][05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,36,1*71

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,32,1*71

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,37,5*7F

$GBGSV,3,3,11,24,22,69,37,32,14,309,36,44,12,46,33,5*45

$GBRMC,145430.000,A,2301.2575117,N,11421.9435422,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,145430.000,1.989,0.193,0.195,0.287,1.487,1.490,2.306*71

[D][05:19:34][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:34][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:35][CA

2025-07-31 22:54:31:222 ==>> T1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:35][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:35][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:35][COMM]Main Task receive event:14
[D][05:19:35][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955175, allstateRepSeconds = 0
[D][05:19:35][COMM]index:0,power_mode:0xFF
[D][05:19:35][COMM]index:1,sound_mode:0xFF
[D][05:19:35][COMM]index:2,gsensor_mode:0xFF
[D][05:19:35][COMM]index:3,report_freq_mode:0xFF
[D][05:19:35][COMM]index:4,report_period:0xFF
[D][05:19:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:35][COMM]index:6,normal_reset_period:0xFF
[D][05:19:35][COMM]index:7,spock_over_speed:0xFF
[D][05:19:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:35][COMM]index:16

2025-07-31 22:54:31:327 ==>> ,imu_config_params:0xFF
[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][COMM]index:17,long_connect_params:0xFF
[D][05:19:35][COMM]index:18,detain_mark:0xFF
[D][05:19:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:35][COMM]index:21,mc_mode:0xFF
[D][05:19:35][COMM]index:22,S_mode:0xFF
[D][05:19:35][COMM]index:23,overweight:0xFF
[D][05:19:35][COMM]index:24,standstill_mode:0xFF
[D][05:19:35][COMM]index:25,night_mode:0xFF
[D][05:19:35][COMM]index:26,experiment1:0xFF
[D][05:19:35][COMM]index:27,experiment2:0xFF
[D][05:19:35][COMM]index:28,experiment3:0xFF
[D][05:19:35][COMM]index:29,experiment4:0xFF
[D][05:19:35][COMM]index:30,night_mode_start:0xFF
[D][05:19:35][COMM]index:31,night_mode_end:0xFF
[D][05:19:35][COMM]index:33,park_report_minutes:0xFF
[D][05:19:35][COMM]index:34,park_report_mode:0xFF
[D][05:19:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:35][COMM]index:38,charge_battery_para: FF
[D][05:19:35][COMM]index:39,multirider_mode:0xFF
[D][05:19:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:35][COMM]index:42,set_time_ble_mode_begin_mi

2025-07-31 22:54:31:432 ==>> n:0xFF
[D][05:19:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:35][COMM]index:44,riding_duration_config:0xFF
[D][05:19:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:35][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:35][COMM]index:49,mc_load_startup:0xFF
[D][05:19:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:35][COMM]index:52,traffic_mode:0xFF
[D][05:19:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:35][COMM]index:63,experiment5:0xFF
[D][05:19:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:35][COMM]index:65,camera_park_fenceline_cfg:0xFF

2025-07-31 22:54:31:537 ==>> 
[D][05:19:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:35][COMM]index:72,experiment6:0xFF
[D][05:19:35][COMM]index:73,experiment7:0xFF
[D][05:19:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:35][COMM]index:75,zero_value_from_server:-1
[D][05:19:35][COMM]index:76,multirider_threshold:255
[D][05:19:35][COMM]index:77,experiment8:255
[D][05:19:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:35][COMM]index:83,loc_report_interval:255
[D][05:19:35][COMM]index:84,multirider_threshold_p2:255
[D][05:19:35][COMM]index:85,multirider_strategy:255
[D][05:19:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:35][COMM]index:90,weight_param:0xFF
[D][05:19:35][

2025-07-31 22:54:31:642 ==>> COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:35][COMM]index:95,current_limit:0xFF
[D][05:19:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:35][COMM]index:100,location_mode:0xFF

[W][05:19:35][PROT]remove success[1629955175],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:35][PROT]add success [1629955175],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 8, ret: 6
[D][05:19:35][CAT1]gsm read msg sub id: 13
[D][05:19:35][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 13, ret: 21
[D][05:19:35][M2M ]get csq[23]
[D][05:19:35][CAT1]opened : 0, 0
[D][05:19:35][SAL ]Cellular task submsg id[68]
[D][05:19:35][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:35][SAL ]socket connect ind. id[4], rst[3]


2025-07-31 22:54:31:747 ==>> 
[D][05:19:35][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:35][M2M ]g_m2m_is_idle become true
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:35][PROT]index:0 1629955175
[D][05:19:35][PROT]is_send:0
[D][05:19:35][PROT]sequence_num:12
[D][05:19:35][PROT]retry_timeout:0
[D][05:19:35][PROT]retry_times:1
[D][05:19:35][PROT]send_path:0x2
[D][05:19:35][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:35][PROT]===========================================================
[W][05:19:35][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955175]
[D][05:19:35][PROT]===========================================================
[D][05:19:35][PROT]sending traceid [999999999990000D]
[D][05:19:35][PROT]Send_TO_M2M [1629955175]
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:35][SAL ]sock send credit cnt[6]
[D][05:19:35][SAL ]sock send ind cre

2025-07-31 22:54:31:852 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:54:32:358 ==>> $GBGGA,145432.000,2301.2574958,N,11421.9435487,E,1,22,0.61,79.418,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,42,7,66,204,39,39,62,41,40,3,61,190,40,1*4E

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,37,1*70

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,37,1*7B

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,33,1*70

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,37,5*7F

$GBGSV,3,3,11,24,22,69,37,32,14,309,36,44,12,46,34,5*42

$GBRMC,145432.000,A,2301.2574958,N,11421.9435487,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,145432.000,2.112,0.186,0.188,0.279,1.563,1.567,2.333*77



2025-07-31 22:54:32:934 ==>> [D][05:19:37][COMM]read battery soc:255


2025-07-31 22:54:33:376 ==>> $GBGGA,145433.000,2301.2574869,N,11421.9435504,E,1,22,0.61,79.422,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,39,1*43

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,36,1*71

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,31,32,14,309,33,1*7A

$GBGSV,7,7,25,44,12,46,33,1*70

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,38,5*70

$GBGSV,3,3,11,24,22,69,37,32,14,309,35,44,12,46,34,5*41

$GBRMC,145433.000,A,2301.2574869,N,11421.9435504,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145433.000,2.007,0.182,0.183,0.271,1.489,1.493,2.249*77



2025-07-31 22:54:34:352 ==>> $GBGGA,145434.000,2301.2574828,N,11421.9435391,E,1,22,0.61,79.433,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,40,1*4D

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,36,1*71

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,31,32,14,309,33,1*7A

$GBGSV,7,7,25,44,12,46,33,1*70

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,38,5*70

$GBGSV,3,3,11,24,22,69,37,32,14,309,36,44,12,46,34,5*42

$GBRMC,145434.000,A,2301.2574828,N,11421.9435391,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145434.000,2.010,0.201,0.202,0.298,1.488,1.493,2.234*78



2025-07-31 22:54:34:957 ==>> [D][05:19:39][COMM]read battery soc:255


2025-07-31 22:54:35:357 ==>> $GBGGA,145435.000,2301.2574863,N,11421.9435339,E,1,22,0.61,79.459,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,40,1*4D

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,39,34,46,94,40,1,45,125,36,1*7F

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,33,1*70

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,37,5*7F

$GBGSV,3,3,11,24,22,69,36,32,14,309,36,44,12,46,34,5*43

$GBRMC,145435.000,A,2301.2574863,N,11421.9435339,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,145435.000,1.851,0.173,0.174,0.260,1.374,1.378,2.116*71



2025-07-31 22:54:36:098 ==>> [D][05:19:40][PROT]CLEAN,SEND:0
[D][05:19:40][PROT]index:1 1629955180
[D][05:19:40][PROT]is_send:0
[D][05:19:40][PROT]sequence_num:13
[D][05:19:40][PROT]retry_timeout:0
[D][05:19:40][PROT]retry_times:1
[D][05:19:40][PROT]send_path:0x2
[D][05:19:40][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:40][PROT]===========================================================
[W][05:19:40][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955180]
[D][05:19:40][PROT]===========================================================
[D][05:19:40][PROT]sending traceid [999999999990000E]
[D][05:19:40][PROT]Send_TO_M2M [1629955180]
[D][05:19:40][PROT]CLEAN:0
[D][05:19:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:40][SAL ]sock send credit cnt[6]
[D][05:19:40][SAL ]sock send ind credit cnt[6]
[D][05:19:40][M2M ]m2m send data len[166]
[D][05:19:40][SAL ]Cellular task submsg id[10]
[D][05:19:40][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:40][CAT1]gsm read msg sub id: 15
[D][05:19:40][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:40][CAT1]Send Data To Server[166][169] ... ->:
0053B984113311331133113311331B88BAE72D55A80CBBBAEEE0E1C54AE9553BB3E1E2C0DACF9F57

2025-07-31 22:54:36:113 ==>> 未匹配到【4G联网测试】数据,请核对检查!
2025-07-31 22:54:36:135 ==>> #################### 【测试结束】 ####################
2025-07-31 22:54:36:160 ==>> 关闭5V供电
2025-07-31 22:54:36:180 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:54:36:189 ==>> 55E0A619BA4015EDCB2922374B040E594CABA53716971C56FB6402C7AF42A54D1E0069C53D753BA5E7A909
[D][05:19:40][CAT1]<<< 
SEND OK

[D][05:19:40][CAT1]exec over: func id: 15, ret: 11
[D][05:19:40][CAT1]sub id: 15, ret: 11

[D][05:19:40][SAL ]Cellular task submsg id[68]
[D][05:19:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:40][M2M ]g_m2m_is_idle become true
[D][05:19:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:40][PROT]M2M Send ok [1629955180]


2025-07-31 22:54:36:220 ==>> 5A A5 04 5A A5 


2025-07-31 22:54:36:308 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,32,1*71

$GBGSV,3,1,11,40,70,176,4

2025-07-31 22:54:36:368 ==>> 1,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,38,33,25,196,37,5*7F

$GBGSV,3,3,11,24,22,69,37,32,14,309,35,44,12,46,34,5*41

$GBRMC,145436.000,A,2301.2574926,N,11421.9435302,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145436.000,1.726,0.161,0.162,0.241,1.280,1.285,2.018*7C

CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:54:36:957 ==>> [D][05:19:41][COMM]read battery soc:255


2025-07-31 22:54:37:157 ==>> 关闭5V供电成功
2025-07-31 22:54:37:165 ==>> 关闭33V供电
2025-07-31 22:54:37:179 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:54:37:217 ==>> 5A A5 02 5A A5 


2025-07-31 22:54:37:323 ==>> $GBGGA,145437.000,2301.2574840,N,11421.9435267,E,1,22,0.61,79.449,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,03,06,16,10,09,25,59,34,01,1.22,0.61,1.05,4*04

$GBGSA,A,3,11,60,41,43,12,23,33,24,32,44,,,1.22,0.61,1.05,4*0B

$GBGSV,7,1,25,40,70,176,41,7,66,204,39,39,62,41,40,3,61,190,40,1*4D

$GBGSV,7,2,25,6,60,8,36,16,60,13,38,10,53,212,36,9,52,344,34,1*48

$GBGSV,7,3,25,25,50,4,39,59,49,130,40,34,46,94,40,1,45,125,37,1*70

$GBGSV,7,4,25,2,45,237,34,11,44,130,38,60,42,239,40,41,39,255,40,1*4C

$GBGSV,7,5,25,43,36,167,38,4,32,111,32,12,30,64,35,23,27,303,36,1*7A

$GBGSV,7,6,25,33,25,196,36,24,22,69,37,5,21,256,32,32,14,309,33,1*79

$GBGSV,7,7,25,44,12,46,33,1*70

$GBGSV,3,1,11,40,70,176,41,39,62,41,41,25,50,4,41,34,46,94,40,5*77

$GBGSV,3,2,11,41,39,255,41,43,36,167,37,23,27,303,39,33,25,196,37,5*7E

$GBGSV,3,3,11,24,22,69,37,32,14,309,35,44,12,4

2025-07-31 22:54:37:367 ==>> 6,34,5*41

$GBRMC,145437.000,A,2301.2574840,N,11421.9435267,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,145437.000,1.773,0.181,0.183,0.272,1.313,1.318,2.037*7F

CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:54:37:473 ==>> [D][05:19:42][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[

2025-07-31 22:54:37:548 ==>> D][05:19:42][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 11


2025-07-31 22:54:38:168 ==>> 关闭33V供电成功
2025-07-31 22:54:38:189 ==>> 关闭3.7V供电
2025-07-31 22:54:38:214 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:54:38:321 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:54:39:128 ==>>  

2025-07-31 22:54:39:173 ==>> 关闭3.7V供电成功
