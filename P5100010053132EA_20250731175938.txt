2025-07-31 17:59:38:140 ==>> MES查站成功:
查站序号:P5100010053132EA验证通过
2025-07-31 17:59:38:157 ==>> 扫码结果:P5100010053132EA
2025-07-31 17:59:38:158 ==>> 当前测试项目:SE51_PCBA
2025-07-31 17:59:38:160 ==>> 测试参数版本:2024.10.11
2025-07-31 17:59:38:162 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 17:59:38:163 ==>> 检测【打开透传】
2025-07-31 17:59:38:165 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 17:59:38:267 ==>> [C128]connect ready
RECVTTL SET SUCCESS
 

2025-07-31 17:59:38:511 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 17:59:38:513 ==>> 检测【检测接地电压】
2025-07-31 17:59:38:514 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 17:59:38:651 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 17:59:38:789 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 17:59:38:792 ==>> 检测【打开小电池】
2025-07-31 17:59:38:795 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 17:59:38:845 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 17:59:39:064 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 17:59:39:066 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 17:59:39:068 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 17:59:39:154 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 17:59:39:340 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 17:59:39:342 ==>> 检测【等待设备启动】
2025-07-31 17:59:39:344 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:59:39:626 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 17:59:39:822 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 17:59:40:371 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:59:40:510 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 17:59:40:888 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 17:59:41:363 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 17:59:41:427 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 17:59:41:429 ==>> 检测【产品通信】
2025-07-31 17:59:41:431 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 17:59:41:638 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 17:59:41:701 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 17:59:41:704 ==>> 检测【初始化完成检测】
2025-07-31 17:59:41:706 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 17:59:41:973 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 17:59:42:034 ==>>                                                              

2025-07-31 17:59:42:237 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 17:59:42:239 ==>> 检测【关闭大灯控制1】
2025-07-31 17:59:42:240 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 17:59:42:434 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:42:512 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 17:59:42:514 ==>> 检测【打开仪表指令模式1】
2025-07-31 17:59:42:515 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 17:59:42:538 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] 

2025-07-31 17:59:42:583 ==>> ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 17:59:42:688 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 17:59:42:782 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 17:59:42:784 ==>> 检测【关闭仪表供电】
2025-07-31 17:59:42:786 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 17:59:42:948 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 17:59:43:062 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 17:59:43:065 ==>> 检测【关闭AccKey2供电1】
2025-07-31 17:59:43:066 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 17:59:43:225 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 17:59:43:338 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 17:59:43:341 ==>> 检测【关闭AccKey1供电1】
2025-07-31 17:59:43:342 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 17:59:43:450 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:43:525 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 17:59:43:619 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 17:59:43:621 ==>> 检测【关闭转刹把供电1】
2025-07-31 17:59:43:622 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 17:59:43:832 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 17:59:43:895 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 17:59:43:898 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 17:59:43:900 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 17:59:43:937 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 17:59:44:042 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
[D][05:17:53][COMM]read battery soc:255


2025-07-31 17:59:44:166 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 17:59:44:168 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 17:59:44:169 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 17:59:44:254 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 17:59:44:440 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 17:59:44:442 ==>> 该项需要延时执行
2025-07-31 17:59:44:468 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:44:987 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x00

2025-07-31 17:59:45:018 ==>> 08F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 17:59:45:470 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:45:636 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 17:59:46:108 ==>>                                                                                                key 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not rea

2025-07-31 17:59:46:214 ==>> dy
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][

2025-07-31 17:59:46:320 ==>> PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][C

2025-07-31 17:59:46:380 ==>> OMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 17:59:46:486 ==>> [D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:46:637 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 17:59:47:498 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:48:048 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 17:59:48:450 ==>> 此处延时了:【4000】毫秒
2025-07-31 17:59:48:454 ==>> 检测【33V输入电压ADC】
2025-07-31 17:59:48:457 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:59:48:512 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:48:753 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:17:57][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:57][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:57][COMM]adc read throttle adc:10  volt:13 mv
[D][05:17:57][COMM]adc read battery ts volt:15 mv
[D][05:17:57][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:17:57][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 17:59:48:984 ==>> 【33V输入电压ADC】通过,【32703mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 17:59:48:987 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 17:59:48:989 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:59:49:056 ==>> 1A A1 00 00 FC 
Get AD_V2 1672mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2770mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 17:59:49:270 ==>> 【TP7_VCC3V3(ADV2)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:49:273 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:59:49:289 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:49:291 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:59:49:294 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 17:59:49:308 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:59:49:310 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:59:49:326 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:59:49:328 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:59:49:350 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:59:49:353 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:59:49:467 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1657mV
Get AD_V4 0mV
Get AD_V5 2770mV
Get AD_V6 2024mV
Get AD_V7 1089mV
OVER 150


2025-07-31 17:59:49:512 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 17:59:49:626 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:49:628 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:59:49:647 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:49:652 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:59:49:655 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 17:59:49:666 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:59:49:668 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:59:49:685 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:59:49:687 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:59:49:708 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:59:49:710 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 17:59:49:904 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020
1A A1 00 00 FC 
Get AD_V2 1672mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2770mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 17:59:49:987 ==>> 【TP7_VCC3V3(ADV2)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:49:989 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 17:59:50:006 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:50:008 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 17:59:50:011 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 17:59:50:026 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 17:59:50:029 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 17:59:50:045 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 17:59:50:047 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 17:59:50:055 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 17:59:50:068 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 17:59:50:086 ==>> 检测【打开WIFI(1)】
2025-07-31 17:59:50:089 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 17:59:50:206 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 17:59:50:311 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 17:59:50:344 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 17:59:50:347 ==>> 检测【清空消息队列(1)】
2025-07-31 17:59:50:372 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 17:59:50:755 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COM

2025-07-31 17:59:50:800 ==>> M]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 17:59:50:879 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 17:59:50:881 ==>> 检测【打开GPS(1)】
2025-07-31 17:59:50:905 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 17:59:51:196 ==>>                                                                                                                                                                                                                  
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087739126

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539093

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 17:59:51:413 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 17:59:51:416 ==>> 检测【打开GSM联网】
2025-07-31 17:59:51:419 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 17:59:51:529 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 17:59:51:634 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 17:59:51:683 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 17:59:51:685 ==>> 检测【打开仪表供电1】
2025-07-31 17:59:51:687 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 17:59:51:844 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 17:59:51:953 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 17:59:51:955 ==>> 检测【打开仪表指令模式2】
2025-07-31 17:59:51:957 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 17:59:52:057 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 17:59:52:162 ==>> [D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 17:59:52:225 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 17:59:52:228 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 17:59:52:230 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 17:59:52:436 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33340]


2025-07-31 17:59:52:500 ==>> 【读取主控ADC采集的仪表电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 17:59:52:503 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 17:59:52:504 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:59:52:649 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:59:52:772 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 17:59:52:775 ==>> 检测【AD_V20电压】
2025-07-31 17:59:52:777 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:59:52:877 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:59:52:953 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 2mV
OVER 150


2025-07-31 17:59:53:337 ==>> 本次取值间隔时间:451ms
2025-07-31 17:59:53:357 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:59:53:398 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 17:59:53:458 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:59:53:579 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13730 imu init OK
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 17:59:53:958 ==>> 本次取值间隔时间:498ms
2025-07-31 17:59:53:977 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 17:59:54:082 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 17:59:54:112 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 17:59:54:157 ==>> 1A A1 10 00 00 
Get AD_V20 1646mV
OVER 150


2025-07-31 17:59:54:397 ==>> 本次取值间隔时间:304ms
2025-07-31 17:59:54:401 ==>>                                                                                                        tch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F3838343E36353107

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f

2025-07-31 17:59:54:416 ==>> 【AD_V20电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:54:418 ==>> 检测【拉低OUTPUT2】
2025-07-31 17:59:54:420 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 17:59:54:457 ==>> , value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.119.62.150"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 17:59:54:697 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 17:59:54:701 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 17:59:54:707 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 17:59:54:792 ==>>                                                                                                                                                                                                  [CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 17:59:54:897 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:59:54:975 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 17:59:54:978 ==>> 检测【拉高OUTPUT2】
2025-07-31 17:59:54:980 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 17:59:55:047 ==>> 3A A3 02 01 A3 


2025-07-31 17:59:55:152 ==>> ON_OUT2
OVER 150


2025-07-31 17:59:55:246 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 17:59:55:248 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 17:59:55:252 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 17:59:55:501 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 17:59:55:775 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 17:59:55:779 ==>> 检测【预留IO LED功能输出】
2025-07-31 17:59:55:782 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 17:59:55:941 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 17:59:56:049 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 17:59:56:052 ==>> 检测【AD_V21电压】
2025-07-31 17:59:56:055 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 17:59:56:153 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

1A A1 20 00 00 
Get AD_V21 1641mV
OVER 150


2025-07-31 17:59:56:258 ==>> 本次取值间隔时间:196ms
2025-07-31 17:59:56:277 ==>> 【AD_V21电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 17:59:56:280 ==>> 检测【关闭仪表供电2】
2025-07-31 17:59:56:283 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 17:59:56:349 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,39,,,41,42,,,41,26,,,40,38,,,40,1*76

$GBGSV,3,2,10,59,,,40,24,,,32,9,,,41,21,,,41,1*43

$GBGSV,3,3,10,14,,,38,13,,,38,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.884,1616.884,51.732,2097152,2097152,2097152*4D

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 17:59:56:439 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 17:59:56:515 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 17:59:56:548 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 17:59:56:552 ==>> 检测【关闭仪表指令模式】
2025-07-31 17:59:56:555 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 17:59:56:739 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 17:59:56:818 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 17:59:56:821 ==>> 检测【打开AccKey2供电】
2025-07-31 17:59:56:825 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 17:59:57:032 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 17:59:57:095 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 17:59:57:098 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 17:59:57:101 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 17:59:57:413 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:06][COMM]adc read out 24v adc:1318  volt:33336 mv
[D][05:18:06][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:06][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:06][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:06][COMM]adc read battery ts volt:12 mv
[D][05:18:06][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:06][COMM]adc read throttle brake in adc:3  volt:5 mv
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,42,,,41,26,,,41,38,,,41,13,,,41,1*7F

$GBGSV,5,2,17,21,,,40,39,,,40,59,,,40,60,,,40,1*74

$GBGSV,5,3,17,8,,,40,16,,,39,3,,,38,14,,,37,1*7E

$GBGSV,5,4,17,24,,,37,9,,,36,6,,,35,4,,,32,1*4A

$GBGSV,5,5,17,2,,,17,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1548.728,1548.728,49.678,2097152,2097152,2097152*4B

[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:06][COMM

2025-07-31 17:59:57:443 ==>> ]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 17:59:57:629 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33336mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 17:59:57:633 ==>> 检测【关闭AccKey2供电2】
2025-07-31 17:59:57:636 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 17:59:57:814 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 17:59:57:913 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 17:59:57:916 ==>> 该项需要延时执行
2025-07-31 17:59:58:104 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 17:59:58:300 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,26,,,41,38,,,41,13,,,41,21,,,41,1*7E

$GBGSV,5,2,20,42,,,40,39,,,40,59,,,40,60,,,40,1*75

$GBGSV,5,3,20,8,,,40,24,,,40,16,,,39,3,,,39,1*78

$GBGSV,5,4,20,1,,,38,14,,,37,9,,,37,6,,,36,1*40

$GBGSV,5,5,20,2,,,36,4,,,33,5,,,33,33,,,36,1*47

$GBRMC,,V,,,,,,,,0.1,E,N,V*51

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1597.212,1597.212,51.072,2097152,2097152,2097152*4E



2025-07-31 17:59:59:305 ==>> $GBGGA,100003.113,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,38,,,42,26,,,42,24,,,42,13,,,41,1*78

$GBGSV,5,2,20,59,,,41,21,,,41,60,,,40,8,,,40,1*42

$GBGSV,5,3,20,3,,,40,39,,,40,42,,,40,16,,,39,1*44

$GBGSV,5,4,20,1,,,38,9,,,37,6,,,37,14,,,37,1*41

$GBGSV,5,5,20,2,,,36,5,,,33,4,,,33,33,,,,1*42

$GBRMC,100003.113,V,,,,,,,,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100003.113,0.000,1612.496,1612.496,51.569,2097152,2097152,2097152*5E



2025-07-31 17:59:59:716 ==>> $GBGGA,100003.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,38,,,42,26,,,42,24,,,42,13,,,41,1*72

$GBGSV,5,2,19,60,,,41,59,,,41,21,,,41,42,,,41,1*76

$GBGSV,5,3,19,8,,,40,3,,,40,39,,,40,16,,,40,1*7E

$GBGSV,5,4,19,9,,,38,1,,,38,6,,,37,14,,,37,1*44

$GBGSV,5,5,19,2,,,36,4,,,34,5,,,33,1*4F

$GBRMC,100003.513,V,,,,,,,,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100003.513,0.000,1623.402,1623.402,51.914,2097152,2097152,2097152*5C



2025-07-31 18:00:00:113 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:00:00:706 ==>> $GBGGA,100004.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,38,,,42,26,,,42,13,,,41,1*73

$GBGSV,5,2,19,60,,,41,59,,,41,21,,,41,42,,,41,1*76

$GBGSV,5,3,19,8,,,40,3,,,40,39,,,40,16,,,40,1*7E

$GBGSV,5,4,19,9,,,38,1,,,38,6,,,37,14,,,37,1*44

$GBGSV,5,5,19,2,,,36,4,,,34,5,,,33,1*4F

$GBRMC,100004.513,V,,,,,,,,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100004.513,0.000,1625.587,1625.587,51.987,2097152,2097152,2097152*51



2025-07-31 18:00:00:920 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:00:00:926 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:00:00:931 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:01:261 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:10][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:10][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:10][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:10][COMM]adc read battery ts volt:11 mv
[D][05:18:10][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:00:01:460 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:00:01:463 ==>> 检测【打开AccKey1供电】
2025-07-31 18:00:01:466 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:00:01:727 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 
$GBGGA,100005.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,13,,,42,38,,,42,26,,,42,1*70

$GBGSV,5,2,19,60,,,41,3,,,41,21,,,41,42,,,41,1*49

$GBGSV,5,3,19,8,,,40,39,,,40,16,,,40,59,,,40,1*41

$GBGSV,5,4,19,9,,,38,1,,,38,6,,,37,14,,,37,1*44

$GBGSV,5,5,19,2,,,36,4,,,34,5,,,33,1*4F

$GBRMC,100005.513,V,,,,,,,,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100005.513,0.000,1627.771,1627.771,52.059,2097152,2097152,2097152*59



2025-07-31 18:00:01:989 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:00:01:992 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:00:01:995 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:00:02:057 ==>> 1A A1 00 40 00 
Get AD_V14 2671mV
OVER 150


2025-07-31 18:00:02:132 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:00:02:253 ==>> 原始值:【2671】, 乘以分压基数【2】还原值:【5342】
2025-07-31 18:00:02:294 ==>> 【读取AccKey1电压(ADV14)前】通过,【5342mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:00:02:298 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:00:02:301 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:02:553 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:6 mv
[D][05:18:11][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:00:02:658 ==>> $GBGGA,100006.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,38,,,43,24,,,43,13,,,42,26,,,42,1*71

$GBGSV,5,2,19,8,,,41,3,,,

2025-07-31 18:00:02:703 ==>> 41,60,,,41,39,,,41,1*7E

$GBGSV,5,3,19,21,,,41,42,,,41,16,,,40,59,,,40,1*76

$GBGSV,5,4,19,9,,,38,1,,,38,6,,,37,14,,,37,1*44

$GBGSV,5,5,19,2,,,36,4,,,34,5,,,33,1*4F

$GBRMC,100006.513,V,,,,,,,,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100006.513,0.000,1634.322,1634.322,52.273,2097152,2097152,2097152*50



2025-07-31 18:00:02:827 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5549mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:00:02:830 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:00:02:833 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:00:03:037 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:00:03:124 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:00:03:128 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:00:03:133 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:00:03:249 ==>> 1A A1 00 40 00 
Get AD_V14 2664mV
OVER 150


2025-07-31 18:00:03:384 ==>> 原始值:【2664】, 乘以分压基数【2】还原值:【5328】
2025-07-31 18:00:03:403 ==>> 【读取AccKey1电压(ADV14)后】通过,【5328mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:00:03:406 ==>> 检测【打开WIFI(2)】
2025-07-31 18:00:03:408 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:00:03:747 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

$GBGGA,100007.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,13,,,42,38,,,42,26,,,42,1*70

$GBGSV,5,2,19,8,,,41,3,,,41,60,,,41,39,,,41,1*7E

$GBGSV,5,3,19,59,,,41,21,,,41,42,,,41,16,,,40,1*77

$GBGSV,5,4,19,9,,,38,1,,,38,14,,,38,6,,,37,1*4B

$GBGSV,5,5,19,2,,,36,4,,,34,5,,,33,1*4F

$GBRMC,100007.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100007.513,0.000,816.715,816.715,746.900,2097152,2097152,2097152*6C

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:00:03:930 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:00:03:934 ==>> 检测【转刹把供电】
2025-07-31 18:00:03:937 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:00:04:138 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:13][COMM]read battery soc:255


2025-07-31 18:00:04:202 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:00:04:206 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:00:04:210 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:00:04:304 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:00:04:546 ==>> +WIFISCAN:4,0,F62A7D2297A3,-66
+WIFISCAN:4,1,44A1917CA62B,-75
+WIFISCAN:4,2,44A1917CAD80,-83
+WIFISCAN:4,3,44A1917CAD81,-83

[D][05:18:13][CAT1]wifi scan report total[4]
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 18:00:04:651 ==>> $GBGGA,100008.513,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBG

2025-07-31 18:00:04:711 ==>> SV,5,1,19,24,,,43,38,,,42,26,,,42,13,,,41,1*73

$GBGSV,5,2,19,60,,,41,3,,,41,21,,,41,42,,,41,1*49

$GBGSV,5,3,19,8,,,40,39,,,40,16,,,40,59,,,40,1*41

$GBGSV,5,4,19,9,,,38,1,,,38,6,,,37,14,,,37,1*44

$GBGSV,5,5,19,2,,,36,5,,,34,4,,,34,1*48

$GBRMC,100008.513,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100008.513,0.000,812.350,812.350,742.908,2097152,2097152,2097152*6F



2025-07-31 18:00:05:252 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:00:05:357 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:00:05:447 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2409mV
OVER 150


2025-07-31 18:00:05:522 ==>> 原始值:【2409】, 乘以分压基数【2】还原值:【4818】
2025-07-31 18:00:05:541 ==>> 【读取AD_V15电压(前)】通过,【4818mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:00:05:544 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:00:05:548 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:00:05:646 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:00:05:768 ==>> $GBGGA,100009.513,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,38,,,42,26,,,42,13,,,41,1*79

$GBGSV,5,2,20,8,,,41,60,,,41,3,,,41,39,,,41,1*74

$GBGSV,5,3,20,21,,,41,42,,,41,16,,,40,59,,,40,1*7C

$GBGSV,5,4,20,9,,,38,6,,,37,1,,,37,14,,,37,1*41

$GBGSV,5,5,20,2,,,36,5,,,34,4,,,34,45,,,31,1*41

$GBRMC,100009.513,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100009.513,0.000,804.867,804.867,736.067,2097152,2097152,2097152*6D

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2443mV
OVER 150


2025-07-31 18:00:05:798 ==>> 原始值:【2443】, 乘以分压基数【2】还原值:【4886】
2025-07-31 18:00:05:822 ==>> 【读取AD_V16电压(前)】通过,【4886mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:00:05:825 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:00:05:830 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:06:211 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:15][COMM]adc read battery ts volt:17 mv
[D][05:18:15][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3113  volt:5472 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2474  volt:3986 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
[D][05:18:15][COMM]read battery soc:255


2025-07-31 18:00:06:349 ==>> 【转刹把供电电压(主控ADC)】通过,【5472mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:00:06:354 ==>> 检测【转刹把供电电压】
2025-07-31 18:00:06:360 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:06:772 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:15][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:15][COMM]adc read battery ts volt:11 mv
[D][05:18:15][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3112  volt:5470 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,100010.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,38,,,42,26,,,42,13,,,41,1*79

$GBGSV,5,2,20,8,,,41,60,,,41,21,,,41,42,,,41,1*48

$GBGSV,5,3,20,3,,,40,39,,,40,16,,,40,59,,,40,1*40

$GBGSV,5,4,20,9,,,38,1,,,38,6,,,37,14,,,37,1*4E

$GBGSV,5,5,20,2,,,36,5,,,34,4,,,34,45,,,32,1*42

$GBRMC,100010.513,V,,,,,,,310725,0.1,E,N,V*4A



2025-07-31 18:00:06:802 ==>> $GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100010.513,0.000,804.857,804.857,736.057,2097152,2097152,2097152*66



2025-07-31 18:00:06:881 ==>> 【转刹把供电电压】通过,【5470mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:00:06:886 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:00:06:897 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:00:07:031 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:00:07:155 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:00:07:159 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:00:07:163 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:00:07:260 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:00:07:307 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:00:07:352 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 18:00:07:385 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:00:07:391 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:00:07:395 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:00:07:487 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:00:07:517 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:00:07:547 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 18:00:07:611 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:00:07:615 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:00:07:623 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:00:07:742 ==>> $GBGGA,100011.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,26,,,42,13,,,41,1*7B

$GBGSV,6,2,21,8,,,41,60,,,41,3,,,41,59,,,41,1*70

$GBGSV,6,3,21,21,,,41,42,,,41,39,,,40,16,,,40,1*78

$GBGSV,6,4,21,9,,,38,1,,,38,6,,,37,14,,,37,1*4C

$GBGSV,6,5,21,2,,,36,5,,,34,4,,,34,45,,,32,1*40

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100011.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100011.513,0.000,796.129,796.129,728.079,2097152,2097152,2097152*64

3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:00:07:880 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:00:07:885 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:00:07:892 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:00:07:952 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:00:08:134 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 18:00:08:152 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:00:08:156 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:00:08:160 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:00:08:239 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:00:08:422 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:00:08:426 ==>> 检测【左刹电压测试1】
2025-07-31 18:00:08:432 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:08:818 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:1736  volt:2288 mv
[D][05:18:17][COMM]adc read right brake adc:1734  volt:2286 mv
[D][05:18:17][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:17][COMM]adc read battery ts volt:19 mv
[D][05:18:17][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:17][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
$GBGGA,100012.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,38,,,42,21,,,42,1*7C

$GBGSV,6,2,22,26,,,42,8,,,41,60,,,41,3,,,41,1*78

$GBGSV,6,3,22,39,,,41,42,,,41,16,,,40,59,,,40,1*74

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,14,,,37,1*4F

$GBGSV,6,5,22,2,,,36,5,,,34,4,,,34,45,,,32,1*43

$GBGSV,6,6,22,10,,,28,12,,,36,1*7B

$GBRMC,100012.513,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100012.513,0.000,798.103,798.103,729.885,2097152,2097152,2097152*6D

[D][05:18:17][COMM]arm_hub adc read vbat adc:2463  volt:3968 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:17][COMM]arm_hub adc read b

2025-07-31 18:00:08:848 ==>> oard id adc:3359  volt:2706 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:00:08:957 ==>> 【左刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 18:00:08:963 ==>> 检测【右刹电压测试1】
2025-07-31 18:00:08:986 ==>> 【右刹电压测试1】通过,【2286】符合目标值【2250】至【2500】要求!
2025-07-31 18:00:08:989 ==>> 检测【转把电压测试1】
2025-07-31 18:00:09:006 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 18:00:09:009 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:00:09:030 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:00:09:152 ==>> 3A A3 03 00 A3 


2025-07-31 18:00:09:242 ==>> OFF_OUT3
OVER 150


2025-07-31 18:00:09:274 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:00:09:278 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:00:09:282 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:00:09:347 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:00:09:546 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:00:09:550 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:00:09:556 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:00:09:649 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:00:09:739 ==>> $GBGGA,100013.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,21,,,42,26,,,42,1*79

$GBGSV,6,2,21,13,,,41,8,,,41,60,,,41,3,,,41,1*7E

$GBGSV,6,3,21,39,,,41,42,,,41,16,,,40,59,,,40,1*77

$GBGSV,6,4,21,9,,,38,1,,,38,2,,,37,6,,,37,1*7B

$GBGSV,6,5,21,14,,,37,5,,,34,4,,,34,45,,,33,1*77

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100013.513,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100013.513,0.000,799.081,799.081,730.779,2097152,2097152,2097152*68



2025-07-31 18:00:09:818 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:00:09:822 ==>> 检测【左刹电压测试2】
2025-07-31 18:00:09:826 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:00:10:167 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:19][COMM]adc read battery ts volt:13 mv
[D][05:18:19][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:19][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2474  volt:3986 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 18:00:10:351 ==>> 【左刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 18:00:10:356 ==>> 检测【右刹电压测试2】
2025-07-31 18:00:10:369 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 18:00:10:373 ==>> 检测【转把电压测试2】
2025-07-31 18:00:10:388 ==>> 【转把电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 18:00:10:391 ==>> 检测【晶振检测】
2025-07-31 18:00:10:394 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:00:10:543 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 18:00:10:666 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:00:10:674 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:00:10:681 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:00:10:754 ==>> $GBGGA,100014.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,38,,,42,21,,,42,1*7F

$GBGSV,6,2,21,26,,,42,8,,,41,60,,,41,3,,,41,1*7B

$GBGSV,6,3,21,39,,,41,42,,,41,59,,,40,16,,,40,1*77

$GBGSV,6,4,21,9,,,38,1,,,38,2,,,37,6,,,37,1*7B

$GBGSV,6,5,21,14,,,37,5,,,34,4,,,34,45,,,33,1*77

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100014.513,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100014.513,0.000,800.068,800.068,731.682,2097152,2097152,2097152*6B

1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1660mV
Get AD_V4 1655mV
Get AD_V5 2772mV
Get AD_V6 2024mV
Get AD_V7 1090mV
OVER 150


2025-07-31 18:00:10:939 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:00:10:944 ==>> 检测【检测BootVer】
2025-07-31 18:00:10:956 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:11:306 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071539093
[D][05:18:20][FCTY]HardwareID  = 867222087739126
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:20][FCTY]Bat         = 4044 mv
[D][05:18:20][FCTY]Current     = 150 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1298
[D][05:18:20][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 202 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20]

2025-07-31 18:00:11:351 ==>> [FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3804 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:11:480 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:00:11:485 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:00:11:517 ==>> 检测【检测固件版本】
2025-07-31 18:00:11:520 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:00:11:525 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:00:11:529 ==>> 检测【检测蓝牙版本】
2025-07-31 18:00:11:536 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:00:11:540 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:00:11:545 ==>> 检测【检测MoBikeId】
2025-07-31 18:00:11:577 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:00:11:581 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:00:11:584 ==>> 检测【检测蓝牙地址】
2025-07-31 18:00:11:597 ==>> 取到目标值:CAC5CC5D9373
2025-07-31 18:00:11:600 ==>> 【检测蓝牙地址】通过,【CAC5CC5D9373】符合目标值【】要求!
2025-07-31 18:00:11:604 ==>> 提取到蓝牙地址:CAC5CC5D9373
2025-07-31 18:00:11:607 ==>> 检测【BOARD_ID】
2025-07-31 18:00:11:616 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:00:11:620 ==>> 检测【检测充电电压】
2025-07-31 18:00:11:636 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:00:11:639 ==>> 检测【检测VBUS电压1】
2025-07-31 18:00:11:655 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:00:11:660 ==>> 检测【检测充电电流】
2025-07-31 18:00:11:673 ==>> 【检测充电电流】通过,【150ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:00:11:682 ==>> 检测【检测IMEI】
2025-07-31 18:00:11:695 ==>> 取到目标值:867222087739126
2025-07-31 18:00:11:700 ==>> 【检测IMEI】通过,【867222087739126】符合目标值【】要求!
2025-07-31 18:00:11:707 ==>> 提取到IMEI:867222087739126
2025-07-31 18:00:11:738 ==>> 检测【检测IMSI】
2025-07-31 18:00:11:742 ==>> 取到目标值:460130071539093
2025-07-31 18:00:11:758 ==>> 【检测IMSI】通过,【460130071539093】符合目标值【】要求!
2025-07-31 18:00:11:762 ==>> $GBGGA,100015.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,26,,,43,24,,,43,13,,,42,38,,,42,1*79

$GBGSV,6,2,21,21,,,42,8,,,41,60,,,41,3,,,41,1*7C

$GBGSV,6,3,21,42,,,41,39,,,40,59,,,40,16,,,40,1*76

$GBGSV,6,4,21,9,,,38,1,,,38,6,,,37,14,,,37,1*4C

$GBGSV,6,5,21,2,,,36,4,,,35,5,,,34,45,,,32,1*41

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100015.513,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100015.513,0.000,799.087,799.087,730.785,2097152,2097152,2097152*6D



2025-07-31 18:00:11:766 ==>> 提取到IMSI:460130071539093
2025-07-31 18:00:11:770 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:00:11:773 ==>> 取到目标值:460130071539093
2025-07-31 18:00:11:777 ==>> 【校验网络运营商(移动)】通过,【460130071539093】符合目标值【】要求!
2025-07-31 18:00:11:795 ==>> 检测【打开CAN通信】
2025-07-31 18:00:11:802 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:00:11:837 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:00:12:038 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:00:12:046 ==>> 检测【检测CAN通信】
2025-07-31 18:00:12:068 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:00:12:169 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:00:12:244 ==>> [D][05:18:21][COMM]read battery soc:255
[D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32421
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:00:12:319 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:00:12:324 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:00:12:327 ==>> 检测【关闭CAN通信】
2025-07-31 18:00:12:350 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:00:12:379 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:00:12:454 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:00:12:631 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:00:12:635 ==>> 检测【打印IMU STATE】
2025-07-31 18:00:12:641 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:00:12:740 ==>> $GBGGA,100016.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,13,,,42,38,,,42,21,,,42,1*7F

$GBGSV,6,2,21,26,,,42,8,,,41,60,,,41,3,,,41,1*7B

$GBGSV,6,3,21,39,,,41,59,,,41,42,,,41,16,,,40,1*76

$GBGSV,6,4,21,9,,,38,1,,,38,6,,,37,14,,,37,1*4C

$GBGSV,6,5,21,2,,,36,4,,,35,5,,,34,45,,,33,1*40

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100016.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100016.513,0.000,801.053,801.053,732.582,2097152,2097152,2097152*69



2025-07-31 18:00:12:845 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:00:12:910 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:00:12:917 ==>> 检测【六轴自检】
2025-07-31 18:00:12:939 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:00:13:134 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:00:13:727 ==>> $GBGGA,100017.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,26,,,42,13,,,41,1*7B

$GBGSV,6,2,21,8,,,41,60,,,41,3,,,41,39,,,41,1*76

$GBGSV,6,3,21,21,,,41,42,,,41,59,,,40,16,,,40,1*7E

$GBGSV,6,4,21,9,,,38,1,,,38,6,,,38,14,,,37,1*43

$GBGSV,6,5,21,2,,,36,4,,,35,5,,,34,45,,,32,1*41

$GBGSV,6,6,21,10,,,28,1*7E

$GBRMC,100017.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100017.513,0.000,798.096,798.096,729.878,2097152,2097152,2097152*6A



2025-07-31 18:00:14:178 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:00:15:549 ==>> $GBGGA,100014.518,2301.2571807,N,11421.9417705,E,1,09,1.56,68.654,M,-1.770,M,,*58

$GBGSA,A,3,13,08,26,16,39,09,24,21,14,,,,3.65,1.56,3.30,4*00

$GBGSV,6,1,21,13,82,270,41,8,82,175,41,26,67,32,42,16,65,307,40,1*78

$GBGSV,6,2,21,39,64,337,41,3,62,191,41,38,60,195,42,9,58,273,38,1*7F

$GBGSV,6,3,21,24,58,225,43,6,55,6,38,59,52,130,40,21,48,105,41,1*41

$GBGSV,6,4,21,1,48,126,38,2,46,239,36,60,41,238,41,14,39,332,37,1*7F

$GBGSV,6,5,21,4,32,112,34,5,22,258,34,45,21,199,33,10,8,192,28,1*41

$GBGSV,6,6,21,42,,,41,1*76

$GBRMC,100014.518,A,2301.2571807,N,11421.9417705,E,0.000,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[D][05:18:23][GNSS]HD8040 GPS
[D][05:18:23][GNSS]GPS diff_sec 124000911, report 0x42 frame
$GBGST,100014.518,0.436,0.213,0.219,0.313,4.117,3.468,15*57

[D][05:18:23][COMM]Main Task receive event:131
[D][05:18:23][COMM]index:0,power_mode:0xFF
[D][05:18:23][COMM]index:1,sound_mode:0xFF
[D][05:18:23][COMM]index:2,gsensor_mode:0xFF
[D][05:18:23][COMM]index:3,report_freq_mode:0xFF
[D][05:18:23][COMM]index:4,report_period:0xFF
[D][05:18:23][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:23][COMM]index:6,normal_reset_period:0xFF

2025-07-31 18:00:15:655 ==>> 
[D][05:18:23][COMM]index:7,spock_over_speed:0xFF
[D][05:18:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:23][COMM]index:16,imu_config_params:0xFF
[D][05:18:23][COMM]index:17,long_connect_params:0xFF
[D][05:18:23][COMM]index:18,detain_mark:0xFF
[D][05:18:23][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:23][COMM]index:21,mc_mode:0xFF
[D][05:18:23][COMM]index:22,S_mode:0xFF
[D][05:18:23][COMM]index:23,overweight:0xFF
[D][05:18:23][COMM]index:24,standstill_mode:0xFF
[D][05:18:23][COMM]index:25,night_mode:0xFF
[D][05:18:23][COMM]index:26,experiment1:0xFF
[D][05:18:23][COMM]index:27,experiment2:0xFF
[D][05:18:23][COMM]index:28,experiment3:0xFF
[D][05:18:23][COMM]index:29,experiment4:0xFF
[D][05:18:23][COMM]index:30,night_mode_start:0xFF
[D][05:1

2025-07-31 18:00:15:760 ==>> 8:23][COMM]index:31,night_mode_end:0xFF
[D][05:18:23][COMM]index:33,park_report_minutes:0xFF
[D][05:18:23][COMM]index:34,park_report_mode:0xFF
[D][05:18:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:23][COMM]index:38,charge_battery_para: FF
[D][05:18:23][COMM]index:39,multirider_mode:0xFF
[D][05:18:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:23][COMM]index:44,riding_duration_config:0xFF
[D][05:18:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:23][COMM]index:49,mc_load_startup:0xFF
[D][05:18:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:23][COMM]index:52,traffic_mode:0xFF
[D][05:18:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:23][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:23][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:23][COMM]index:57,traff

2025-07-31 18:00:15:865 ==>> ic_sens_cycle:0xFF
[D][05:18:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:23][COMM]index:63,experiment5:0xFF
[D][05:18:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:23][COMM]index:72,experiment6:0xFF
[D][05:18:23][COMM]index:73,experiment7:0xFF
[D][05:18:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:23][COMM]index:75,zero_value_from_server:-1
[D][05:18:23][COMM]index:76,multirider_threshold:255
[D][05:18:23][COMM]index:77,experiment8:255
[D][05:18:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:23][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:23][COMM]index:80,temp_park_reminder_timeout_duration:

2025-07-31 18:00:15:971 ==>> 255
[D][05:18:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:23][COMM]index:83,loc_report_interval:255
[D][05:18:23][COMM]index:84,multirider_threshold_p2:255
[D][05:18:23][COMM]index:85,multirider_strategy:255
[D][05:18:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:23][COMM]index:90,weight_param:0xFF
[D][05:18:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:23][COMM]index:95,current_limit:0xFF
[D][05:18:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:23][COMM]index:100,location_mode:0xFF

[W][05:18:23][PROT]remove success[1629955103],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:23][PROT]add success [1629955103],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:23][COMM]Main Task receive event:131 finished processing
[D][05:18:24][PROT]index:0 1629955104
[D][05:18:24][PROT]is_send:0
[D][05:18:24][PROT]sequence_num:4
[D][05:18:24][PROT]retry_timeout:0
[D][05:18:24][PROT]retry_times:1
[D][05:18:24][PROT]send_path:0x2
[D][05:18:24][PROT]min_index:0, type:0x4205, prio

2025-07-31 18:00:16:076 ==>> rity:0
[D][05:18:24][PROT]===========================================================
[W][05:18:24][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955104]
[D][05:18:24][PROT]===========================================================
[D][05:18:24][PROT]sending traceid [9999999999900005]
[D][05:18:24][PROT]Send_TO_M2M [1629955104]
[D][05:18:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:24][SAL ]sock send credit cnt[6]
[D][05:18:24][SAL ]sock send ind credit cnt[6]
[D][05:18:24][M2M ]m2m send data len[294]
[D][05:18:24][SAL ]Cellular task submsg id[10]
[D][05:18:24][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e08] format[0]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6
[D][05:18:24][CAT1]gsm read msg sub id: 15
[D][05:18:24][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:24][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B5EBE0FDA9E0896EDDC62517993887D57BB3A461223A8F7DBA380BCB51BC8F43889FF70F02A22B1D966D24EE528B743668281E80

2025-07-31 18:00:16:181 ==>> 8A62803D205AC95928976841AA14B2B50C7826E8B4FB4CAC04229ECD36C6BA6484F67FA5AE00D4BD93BE7B5CB9BE07650E90BB2402BC39135277D63B78A354D0921D040DBEE82F0DD3458BA3D7771E
[D][05:18:24][CAT1]<<< 
SEND OK

[D][05:18:24][CAT1]exec over: func id: 15, ret: 11
[D][05:18:24][CAT1]sub id: 15, ret: 11

[D][05:18:24][SAL ]Cellular task submsg id[68]
[D][05:18:24][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:24][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:24][M2M ]g_m2m_is_idle become true
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:24][PROT]M2M Send ok [1629955104]
[D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35222 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-21,-11,4057]
[D][05:18:24][COMM]Main Task receive event:142 finished processing
$GBGGA,100015.018,2301.2579757,N,11421.9417094,E,1,09,1.56,72.220,M,-1.770,M,,*5D



2025-07-31 18:00:16:274 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:00:16:279 ==>> 检测【打印IMU STATE2】
2025-07-31 18:00:16:283 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:00:16:291 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:00:16:452 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:00:16:546 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:00:16:551 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:00:16:577 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:00:16:649 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:00:16:754 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt 

2025-07-31 18:00:16:813 ==>> = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:00:16:818 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:00:16:823 ==>> 检测【检测VBUS电压2】
2025-07-31 18:00:16:835 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:17:329 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539093
[D][05:18:26][FCTY]HardwareID  = 867222087739126
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:26][FCTY]Bat         = 4044 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:26][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:2

2025-07-31 18:00:17:435 ==>> 6][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3804 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGGA,100017.000,2301.2578655,N,11421.9417753,E,1,09,1.56,72.608,M,-1.770,M,,*56

$GBGSA,A,3,13,08,26,16,39,09,24,21,14,,,,3.65,1.56,3.30,4*00

$GBGSV,6,1,21,13,82,270,41,8,82,175,40,26,67,32,42,16,65,307,40,1*79

$GBGSV,6,2,21,39,64,337,41,3,62,191,41,38,60,195,42,9,58,273,38,1*7F

$GBGSV,6,3,21,24,58,225,43,6,55,6,37,59,52,130,40,21,48,105,41,1*4E

$GBGSV,6,4,21,1,48,126,37,2,46,239,36,60,41,238,41,14,39,332,37,1*70

$GBGSV,6,5,21,4,32,112,34,5,22,258,34,45,21,199,33,10,8,192,28,1*41

$GBGSV,6,6,21,42,,,41,1*76

$GBGSV,1,1,04,26,67,32,41,39,64,337,41,24,58,225,42,21,48,105,41,5*4B

$GBRMC,100017.000,A,2301.2578655,N,11421.9417753,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100017.000,0.832,0.230,0.238,0.347,1.329,1.163,6.911*70

[D][05:18:26][COMM]msg 0601 loss. last_tick:32410. cur_tick:37416. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E8

2025-07-31 18:00:17:465 ==>> 0C71E2223F->0x0008F80C71E2223F 37417


2025-07-31 18:00:17:606 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:18:012 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539093
[D][05:18:27][FCTY]HardwareID  = 867222087739126
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:27][FCTY]Bat         = 4044 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 127
[D][05:18:27][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465

2025-07-31 18:00:18:058 ==>> b5b1
[D][05:18:27][FCTY]Bat1         = 3804 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:18:135 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:18:298 ==>> $GBGGA,100018.000,2301.2578410,N,11421.9417722,E,1,09,1.56,73.383,M,-1.770,M,,*5B

$GBGSA,A,3,13,08,26,16,39,09,24,21,14,,,,3.65,1.56,3.30,4*00

$GBGSV,6,1,21,13,82,270,41,8,82,175,40,26,67,32,41,16,65,307,40,1*7A

$GBGSV,6,2,21,39,64,337,41,3,62,191,41,38,60,195,42,9,58,273,38,1*7F

$GBGSV,6,3,21,24,58,225,43,6,55,6,37,59,52,130,40,21,48,105,41,1*4E

$GBGSV,6,4,21,1,48,126,38,2,46,239,36,60,41,238,42,14,39,332,37,1*7C

$GBGSV,6,5,21,4,32,112,34,5,22,258,34,45,21,199,33,10,8,192,28,1*41

$GBGSV,6,6,21,42,,,41,1*76

$GBGSV,1,1,04,26,67,32,41,39,64,337,40,24,58,225,42,21,48,105,41,5*4A

$GBRMC,100018.000,A,2301.2578410,N,11421.9417722,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,100018.000,0.723,0.220,0.228,0.331,1.076,0.934,6.151*7F



2025-07-31 18:00:18:571 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539093
[D][05:18:27][FCTY]HardwareID  = 867222087739126
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:27][FCTY]Bat         = 3784 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 9500 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 109
[D][05:18:27][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION 

2025-07-31 18:00:18:616 ==>> = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3804 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:18:664 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:19:011 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               g
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:27][PROT]index:1
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:5
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retr

2025-07-31 18:00:19:116 ==>> y_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900006]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[1],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]=

2025-07-31 18:00:19:221 ==>> =========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539093
[D][05:18:28][FCTY]HardwareID  = 867222087739126
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:28][FCTY]Bat         = 3784 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 9500 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 108
[D][05:18:28][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 202 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3804 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 =======

2025-07-31 18:00:19:251 ==>> ===
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:19:451 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:19:835 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539093
[D][05:18:28][FCTY]HardwareID  = 867222087739126
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:28][FCTY]Bat         = 3784 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 9500 mv
[D][05:18:28][GNSS]recv submsg id[1]
[D][05:18:28][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:28][GNSS]location stop evt done evt
[D][05:18:28][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:28][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]C

2025-07-31 18:00:19:879 ==>> AT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3804 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:19:989 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:20:412 ==>> [D][05:18:29][PROT]CLEAN,SEND:0
[D][05:18:29][PROT]index:1 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900006]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][PROT]CLEAN:0
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B325564DB39

2025-07-31 18:00:20:517 ==>> 85CD4638B766B9A987994343146472F8A5E0B8628B90CD854975F745FDA7981D5166C0C977557FA538F21345C1953A99D4788B893B6792DC8A0847AB6AE481F5FA207AD4C5773CE06AECB92BE17
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539093
[D][05:18:29][FCTY]HardwareID  = 867222087739126
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:29][FCTY]Bat         = 3844 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 9500 mv
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29]

2025-07-31 18:00:20:607 ==>> [M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 104
[D][05:18:29][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3804 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][PROT]M2M Send ok [1629955109]


2025-07-31 18:00:20:777 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:21:113 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539093
[D][05:18:30][FCTY]HardwareID  = 867222087739126
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:30][FCTY]Bat         = 3924 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 9500 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:30][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 25 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:1

2025-07-31 18:00:21:143 ==>> 8:30][FCTY]Bat1         = 3804 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:21:314 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:21:711 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539093
[D][05:18:30][FCTY]HardwareID  = 867222087739126
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:30][FCTY]Bat         = 3924 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 9500 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 91
[D][05:18:30][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNS

2025-07-31 18:00:21:755 ==>> S_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3804 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:21:848 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:22:216 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539093
[D][05:18:31][FCTY]HardwareID  = 867222087739126
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:31][FCTY]Bat         = 3824 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 9500 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 97
[D][05:18:31][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3804 mv
[

2025-07-31 18:00:22:246 ==>> D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:22:386 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:00:22:707 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539093
[D][05:18:31][FCTY]HardwareID  = 867222087739126
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = CAC5CC5D9373
[D][05:18:31][FCTY]Bat         = 3824 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 9500 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:31][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1

2025-07-31 18:00:22:751 ==>> _GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3804 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:00:22:921 ==>> 【检测VBUS电压2】失败,【9500mV】与目标值【4400mV】至【5000mV】不匹配!
2025-07-31 18:00:22:928 ==>> #################### 【测试结束】 ####################
2025-07-31 18:00:22:946 ==>> 关闭5V供电
2025-07-31 18:00:22:954 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:00:23:044 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:00:23:950 ==>> 关闭5V供电成功
2025-07-31 18:00:23:958 ==>> 关闭33V供电
2025-07-31 18:00:23:983 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:00:24:058 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:00:24:955 ==>> 关闭33V供电成功
2025-07-31 18:00:24:960 ==>> 关闭3.7V供电
2025-07-31 18:00:24:968 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:00:25:048 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:00:25:394 ==>>  

2025-07-31 18:00:25:969 ==>> 关闭3.7V供电成功
