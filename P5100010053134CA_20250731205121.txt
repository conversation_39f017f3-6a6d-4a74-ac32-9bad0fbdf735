2025-07-31 20:51:21:851 ==>> MES查站成功:
查站序号:P5100010053134CA验证通过
2025-07-31 20:51:21:868 ==>> 扫码结果:P5100010053134CA
2025-07-31 20:51:21:869 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:51:21:871 ==>> 测试参数版本:2024.10.11
2025-07-31 20:51:21:872 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:51:21:873 ==>> 检测【打开透传】
2025-07-31 20:51:21:875 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:51:21:956 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:51:22:168 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:51:22:175 ==>> 检测【检测接地电压】
2025-07-31 20:51:22:177 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:51:22:259 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:51:22:458 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:51:22:460 ==>> 检测【打开小电池】
2025-07-31 20:51:22:463 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:51:22:564 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:51:22:764 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:51:22:766 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:51:22:768 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:51:22:854 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:51:23:094 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:51:23:097 ==>> 检测【等待设备启动】
2025-07-31 20:51:23:100 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:51:23:288 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:51:23:483 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:51:24:123 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:51:24:127 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:51:24:198 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:51:24:595 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:51:25:072 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:51:25:186 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:51:25:188 ==>> 检测【产品通信】
2025-07-31 20:51:25:190 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:51:25:331 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:51:25:474 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:51:25:477 ==>> 检测【初始化完成检测】
2025-07-31 20:51:25:482 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:51:25:736 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:51:26:016 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:51:26:019 ==>> 检测【关闭大灯控制1】
2025-07-31 20:51:26:020 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:51:26:120 ==>> [D][05:17:51][COMM]2636 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:26:225 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handl

2025-07-31 20:51:26:285 ==>> erVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:51:26:572 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:51:26:575 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:51:26:577 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:51:26:750 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:51:26:872 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:51:26:876 ==>> 检测【关闭仪表供电】
2025-07-31 20:51:26:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:51:27:050 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:51:27:140 ==>> [D][05:17:52][COMM]3647 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:27:173 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:51:27:176 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:51:27:179 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:51:27:335 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:51:27:484 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:51:27:487 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:51:27:519 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:51:27:625 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:51:27:812 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:51:27:817 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:51:27:821 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:51:28:050 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:51:28:123 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:51:28:126 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:51:28:129 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:51:28:155 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:28:260 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:51:28:320 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0


2025-07-31 20:51:28:407 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:51:28:411 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:51:28:415 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:51:28:443 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:51:28:455 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:51:28:560 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5009. period

2025-07-31 20:51:28:665 ==>> :500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5010. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5010. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5011. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5011. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5011. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5012. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5012. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5013. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5013. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5014
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987

2025-07-31 20:51:28:692 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:51:28:694 ==>> 该项需要延时执行
2025-07-31 20:51:28:696 ==>> FE 5014


2025-07-31 20:51:29:160 ==>> [D][05:17:54][COMM]5672 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:29:925 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:51:30:425 ==>>                                               on type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play 

2025-07-31 20:51:30:530 ==>> audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D

2025-07-31 20:51:30:635 ==>> ][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get

2025-07-31 20:51:30:725 ==>>  bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6685 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                         

2025-07-31 20:51:31:187 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7696 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:32:210 ==>> [D][05:17:57][COMM]8707 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:32:420 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:51:32:698 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:51:32:701 ==>> 检测【33V输入电压ADC】
2025-07-31 20:51:32:703 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:32:975 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:17:58][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:58][COMM]adc read left brake adc:15  volt:19 mv
[D][05:17:58][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:58][COMM]adc read throttle adc:17  volt:22 mv
[D][05:17:58][COMM]adc read battery ts volt:16 mv
[D][05:17:58][COMM]adc read in 24v adc:1309  volt:33108 mv
[D][05:17:58][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2453  volt:3952 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:51:33:203 ==>> [D][05:17:58][COMM]9719 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:51:33:259 ==>> 【33V输入电压ADC】通过,【33108mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:51:33:261 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:51:33:263 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:51:33:369 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1669mV
Get AD_V4 0mV
Get AD_V5 2779mV
Get AD_V6 1998mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:51:33:550 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10022


2025-07-31 20:51:33:565 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:33:569 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:51:33:608 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:33:610 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:51:33:611 ==>> 原始值:【2779】, 乘以分压基数【2】还原值:【5558】
2025-07-31 20:51:33:631 ==>> 【TP68_VCC5V5(ADV5)】通过,【5558mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:51:33:633 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:51:33:665 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1998mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:51:33:691 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:51:33:693 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:51:33:695 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:51:33:764 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1671mV
Get AD_V4 0mV
Get AD_V5 2781mV
Get AD_V6 2021mV
Get AD_V7 1086mV
OVER 150


2025-07-31 20:51:33:978 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:33:981 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:51:34:007 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:34:009 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:51:34:027 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 20:51:34:033 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:51:34:037 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:51:34:062 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:51:34:064 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:51:34:088 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:51:34:090 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:51:34:166 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1671mV
Get AD_V4 1mV
Get AD_V5 2781mV
Get AD_V6 1995mV
Get AD_V7 1084mV
OVER 150


2025-07-31 20:51:34:376 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:34:378 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:51:34:417 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:34:420 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:51:34:423 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 20:51:34:447 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:51:34:449 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:51:34:470 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:51:34:474 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:51:34:509 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:51:34:512 ==>> 检测【打开WIFI(1)】
2025-07-31 20:51:34:516 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:51:34:535 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10729 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUC

2025-07-31 20:51:34:562 ==>> FG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:51:34:896 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                [D][05:18:00][CAT1]<<< 
460130071539205

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:51:35:051 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:51:35:054 ==>> 检测【清空消息队列(1)】
2025-07-31 20:51:35:078 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:51:35:262 ==>> [D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:51:35:326 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:51:35:329 ==>> 检测【打开GPS(1)】
2025-07-31 20:51:35:331 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:51:35:861 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port

2025-07-31 20:51:35:965 ==>> [9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:51:36:070 ==>>                                                       ellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:51:36:143 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:51:36:146 ==>> 检测【打开GSM联网】
2025-07-31 20:51:36:148 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:51:36:437 ==>> [D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                         

2025-07-31 20:51:36:687 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:51:36:690 ==>> 检测【打开仪表供电1】
2025-07-31 20:51:36:692 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:51:36:853 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:51:36:964 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:51:36:966 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:51:36:968 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:51:37:175 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:51:37:220 ==>>                                       

2025-07-31 20:51:37:239 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:51:37:242 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:51:37:244 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:51:37:450 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[32992]


2025-07-31 20:51:37:515 ==>> 【读取主控ADC采集的仪表电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:51:37:519 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:51:37:521 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:37:802 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:51:38:043 ==>>                                [D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,33,,,42,42,,,40,39,,,40,40,,,39,1*71

$GBGSV,3,2,10,41,,,38,24,,,33,25,,,17,14,,,42,1*7C

$GBGSV,3,3,10,60,,,42,59,,,41,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1474.993,1474.993,47.441,2097152,2097152,2097152*4D

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:51:38:047 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:51:38:051 ==>> 检测【AD_V20电压】
2025-07-31 20:51:38:053 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:38:148 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:51:38:225 ==>> 本次取值间隔时间:70ms
2025-07-31 20:51:38:270 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 20:51:38:406 ==>> 本次取值间隔时间:172ms
2025-07-31 20:51:38:434 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:38:437 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:51:38:542 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:51:38:666 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 20:51:38:943 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,43,14,,,41,60,,,41,25,,,41,1*72

$GBGSV,5,2,17,42,,,40,39,,,40,40,,,39,41,,,38,1*7B

$GBGSV,5,3,17,7,,,38,24,,,36,6,,,36,9,,,36,1*46

$GBGSV,5,4,17,44,,,33,1,,,42,59,,,41,3,,,41,1*79

$GBGSV,5,5,17,2,,,36,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1600.915,1600.915,51.199,2097152,2097152,2097152*4A



2025-07-31 20:51:39:003 ==>> 本次取值间隔时间:446ms
2025-07-31 20:51:39:026 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:39:127 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:51:39:220 ==>> 本次取值间隔时间:83ms
2025-07-31 20:51:39:359 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:51:39:573 ==>> 本次取值间隔时间:346ms
2025-07-31 20:51:39:667 ==>> 本次取值间隔时间:84ms
2025-07-31 20:51:39:809 ==>> 本次取值间隔时间:132ms
2025-07-31 20:51:39:812 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:39:918 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:51:39:978 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,43,3,,,42,14,,,41,60,,,41,1*43

$GBGSV,6,2,21,59,,,41,25,,,41,1,,,40,42,,,40,1*4D

$GBGSV,6,3,21,39,,,40,40,,,39,41,,,39,24,,,39,1*73

$GBGSV,6,4,21,7,,,37,2,,,36,6,,,36,9,,,36,1*7C

$GBGSV,6,5,21,5,,,35,34,,,35,44,,,34,4,,,34,1*70

$GBGSV,6,6,21,38,,,34,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1585.282,1585.282,50.709,2097152,2097152,2097152*44

[W][05:18:05][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:51:40:113 ==>> 本次取值间隔时间:190ms
2025-07-31 20:51:40:134 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:51:40:238 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:51:40:331 ==>> 本次取值间隔时间:78ms
2025-07-31 20:51:40:361 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1658mV
OVER 150


2025-07-31 20:51:40:436 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:51:40:681 ==>> 本次取值间隔时间:342ms
2025-07-31 20:51:40:700 ==>> 【AD_V20电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:40:703 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:51:40:707 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:51:40:760 ==>> 3A A3 02 00 A3 


2025-07-31 20:51:40:865 ==>> OFF_OUT2
OVER 150


2025-07-31 20:51:40:970 ==>> $GBGGA,125144.773,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,25,,,42,3,,,41,14,,,41,1*47

$GBGSV,6,2,24,60,,,41,59,,,41,42,,,40,39,,,40,1*72

$GBGSV,6,3,24,24,,,40,1,,,39,40,,,39,41,,,39,1*4D

$GBGSV,6,4,24,7,,,37,9,,,37,16,,,37,2,,,36,1*48

$GBGSV,6,5,24,6,,,36,5,,,35,34,,,35,44,,,35,1*74

$GBGSV,6,6,24,38,,,35,4,,,34,10,,,34,13,,,38,1*41

$GBRMC,125144.773,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125144.773,0.000,1580.813,1580.813,50.560,2097152,2097152,2097152*53



2025-07-31 20:51:40:979 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:51:40:982 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:51:40:986 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:51:41:151 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:51:41:261 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:51:41:266 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:51:41:271 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:51:41:364 ==>> 3A A3 02 01 A3 


2025-07-31 20:51:41:454 ==>> ON_OUT2
OVER 150


2025-07-31 20:51:41:549 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:51:41:575 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:51:41:579 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:51:41:819 ==>> $GBGGA,125145.573,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,25,,,42,3,,,41,14,,,41,1*47

$GBGSV,7,2,25,60,,,41,59,,,41,24,,,41,42,,,40,1*7F

$GBGSV,7,3,25,39,,,40,1,,,39,40,,,39,41,,,39,1*41

$GBGSV,7,4,25,7,,,37,9,,,37,16,,,37,2,,,37,1*49

$GBGSV,7,5,25,6,,,37,13,,,36,44,,,36,34,,,35,1*42

$GBGSV,7,6,25,38,,,35,5,,,34,4,,,34,10,,,34,1*7A

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,125145.573,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125145.573,0.000,1565.486,1565.486,50.100,2097152,2097152,2097152*52

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:51:42:085 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:51:42:088 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:51:42:090 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:51:42:250 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:51:42:361 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:51:42:365 ==>> 检测【AD_V21电压】
2025-07-31 20:51:42:369 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:51:42:462 ==>> [D][05:18:07][COMM]read battery soc:255
1A A1 20 00 00 
Get AD_V21 1062mV
OVER 150


2025-07-31 20:51:42:567 ==>> 本次取值间隔时间:194ms
2025-07-31 20:51:42:597 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:51:42:659 ==>> 1A A1 20 00 00 
Get AD_V21 1062mV
OVER 150


2025-07-31 20:51:42:764 ==>> $GBGGA,125146.553,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,14,,,41,1*47

$GBGSV,7,2,26,60,,,41,59,,,41,24,,,41,39,,,40,1*70

$GBGSV,7,3,26,42,,,39,1,,,39,40,,,39,41,,,39,1*40

$GBGSV,7,4,26,7,,,37,9,,,37,16,,,37,2,,,37,1*4A

$GBGSV,7,5,26,6,,,37,13,,,36,44,,,36,34,,,35,1*41

$GBGSV,7,6,26,38,,,35,5,,,34,4,,,34,10,,,34,1*79

$GBGSV,7,7,26,12,,,29,8,,,28,1*48

$GBRMC,125146.553,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125146.553,0.000,1548.344,1548.344,49.567,2097152,2097152,2097152*5E



2025-07-31 20:51:42:932 ==>> 本次取值间隔时间:326ms
2025-07-31 20:51:42:950 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:51:43:024 ==>> 本次取值间隔时间:63ms
2025-07-31 20:51:43:055 ==>> 1A A1 20 00 00 
Get AD_V21 1656mV
OVER 150


2025-07-31 20:51:43:206 ==>> 本次取值间隔时间:169ms
2025-07-31 20:51:43:225 ==>> 【AD_V21电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:43:227 ==>> 检测【关闭仪表供电2】
2025-07-31 20:51:43:231 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:51:43:458 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:51:43:509 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:51:43:511 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:51:43:514 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:51:43:533 ==>> [D][05:18:09][COMM]S->M yaw:INVALID


2025-07-31 20:51:43:759 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!
$GBGGA,125147.533,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,3,,,41,60,,,41,1*45

$GBGSV,7,2,27,59,,,41,24,,,41,14,,,40,39,,,40,1*73

$GBGSV,7,3,27,42,,,39,1,,,39,40,,,39,41,,,39,1*41

$GBGSV,7,4,27,16,,,38,7,,,37,2,,,37,6,,,37,1*4B

$GBGSV,7,5,27,9,,,36,13,,,36,44,,,36,34,,,35,1*4E

$GBGSV,7,6,27,38,,,35,5,,,34,4,,,34,10,,,34,1*78

$GBGSV,7,7,27,23,,,32,12,,,30,8,,,29,1*40

$GBRMC,125147.533,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125147.533,0.000,1541.665,1541.665,49.348,2097152,2097152,2097152*52



2025-07-31 20:51:43:792 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:51:43:796 ==>> 检测【打开AccKey2供电】
2025-07-31 20:51:43:800 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:51:43:925 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:51:44:065 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:51:44:068 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:51:44:072 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:44:364 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3166  volt:5565 mv
[D][05:18:09][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:18:09][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:09][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:09][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:09][COMM]adc read battery ts volt:16 mv
[D][05:18:09][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:09][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2435  volt:3923 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:51:44:454 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:51:44:594 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:51:44:598 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:51:44:600 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:51:44:771 ==>> $GBGGA,125148.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,25,,,41,3,,,41,60,,,41,1*44

$GBGSV,8,2,29,59,,,41,24,,,41,14,,,40,39,,,39,1*7C

$GBGSV,8,3,29,42,,,39,41,,,39,1,,,38,40,,,38,1*40

$GBGSV,8,4,29,16,,,38,7,,,37,2,,,37,6,,,36,1*4B

$GBGSV,8,5,29,9,,,36,13,,,36,44,,,36,34,,,35,1*4F

$GBGSV,8,6,29,38,,,35,5,,,34,4,,,34,10,,,34,1*79

$GBGSV,8,7,29,23,,,32,12,,,31,8,,,31,26,,,38,1*46

$GBGSV,8,8,29,27,,,36,1*7D

$GBRMC,125148.513,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125148.513,0.000,1540.115,1540.115,49.285,2097152,2097152,2097152*5F

[W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:51:44:872 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:51:44:878 ==>> 该项需要延时执行
2025-07-31 20:51:45:398 ==>> [D][05:18:10][COMM]M->S yaw:INVALID


2025-07-31 20:51:45:748 ==>> $GBGGA,125149.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,3,,,41,60,,,41,1*45

$GBGSV,7,2,27,59,,,41,24,,,41,14,,,40,39,,,39,1*7D

$GBGSV,7,3,27,42,,,39,41,,,39,1,,,39,40,,,38,1*40

$GBGSV,7,4,27,16,,,38,7,,,37,2,,,37,6,,,37,1*4B

$GBGSV,7,5,27,9,,,36,13,,,36,44,,,36,38,,,36,1*41

$GBGSV,7,6,27,34,,,35,5,,,34,4,,,34,10,,,34,1*74

$GBGSV,7,7,27,23,,,33,8,,,32,12,,,31,1*4A

$GBRMC,125149.513,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125149.513,0.000,1547.785,1547.785,49.523,2097152,2097152,2097152*55



2025-07-31 20:51:46:467 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:51:46:723 ==>> $GBGGA,125150.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,3,,,41,60,,,41,1*45

$GBGSV,7,2,27,59,,,41,24,,,41,14,,,41,39,,,39,1*7C

$GBGSV,7,3,27,42,,,39,41,,,39,1,,,39,40,,,39,1*41

$GBGSV,7,4,27,16,,,38,7,,,37,9,,,37,13,,,37,1*74

$GBGSV,7,5,27,2,,,36,6,,,36,44,,,36,38,,,36,1*7E

$GBGSV,7,6,27,34,,,35,4,,,35,5,,,34,10,,,34,1*75

$GBGSV,7,7,27,23,,,33,8,,,33,12,,,31,1*4B

$GBRMC,125150.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125150.513,0.000,1553.925,1553.925,49.716,2097152,2097152,2097152*59



2025-07-31 20:51:47:728 ==>> $GBGGA,125151.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,25,,,41,3,,,41,1*49

$GBGSV,7,2,28,60,,,41,59,,,41,14,,,41,39,,,40,1*7D

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,37,13,,,37,2,,,37,1*7E

$GBGSV,7,5,28,6,,,37,7,,,36,44,,,36,34,,,36,1*79

$GBGSV,7,6,28,38,,,35,4,,,34,5,,,34,10,,,34,1*77

$GBGSV,7,7,28,23,,,33,8,,,33,12,,,32,26,,,32,1*42

$GBRMC,125151.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125151.513,0.000,1548.777,1548.777,49.559,2097152,2097152,2097152*51



2025-07-31 20:51:47:879 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:51:47:884 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:51:47:888 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:48:171 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:13][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:13][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:13][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:13][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:13][COMM]adc read battery ts volt:17 mv
[D][05:18:13][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:13][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2434  volt:3921 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:51:48:460 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:51:48:495 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【126mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:51:48:501 ==>> 检测【打开AccKey1供电】
2025-07-31 20:51:48:507 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:51:48:734 ==>> $GBGGA,125152.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,44,24,,,41,25,,,41,3,,,41,1*4D

$GBGSV,7,2,28,60,,,41,59,,,41,14,,,41,39,,,40,1*7D

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,37,6,,,37,7,,,37,1*4F

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,34,,,35,1*4A

$GBGSV,7,6,28,38,,,35,4,,,34,5,,,34,10,,,34,1*77

$GBGSV,7,7,28,8,,,34,23,,,33,12,,,32,26,,,32,1*45

$GBRMC,125152.513,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125152.513,0.000,1547.297,1547.297,49.512,2097152,2097152,2097152*5D

[W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:51:48:785 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:51:48:791 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:51:48:799 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:51:48:839 ==>> 1A A1 00 40 00 
Get AD_V14 2678mV
OVER 150


2025-07-31 20:51:49:050 ==>> 原始值:【2678】, 乘以分压基数【2】还原值:【5356】
2025-07-31 20:51:49:074 ==>> 【读取AccKey1电压(ADV14)前】通过,【5356mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:51:49:078 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:51:49:084 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:49:368 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:14][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:14][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:14][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:14][COMM]adc read battery ts volt:13 mv
[D][05:18:14][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:14][COMM]adc read throttle brake in adc:11  volt:19 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:51:49:624 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:51:49:627 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:51:49:630 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:51:49:731 ==>> $GBGGA,125153.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,41,25,,,41,3,,,41,1*4A

$GBGSV,7,2,28,60,,,41,59,,,41,14,,,40,39,,,39,1*72

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,39,1*4E

$GBGSV,7,4,28,16,,,38,9,,,37,6,,,37,7,,,37,1*4F

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,36,1*45

$GBGSV,7,6,28,34,,,35,4,,,34,5,,,34,10,,,34,1*7B

$GBGSV,7,7,28,8,,,34,23,,,33,26,,,32,12,,,31,1*46

$GBRMC,125153.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125153.513,0.000,1544.332,1544.332,49.414,2097152,2097152,2097152*5B



2025-07-31 20:51:49:821 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:51:49:907 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:51:49:913 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:51:49:918 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:51:49:956 ==>> 1A A1 00 40 00 
Get AD_V14 2681mV
OVER 150


2025-07-31 20:51:50:166 ==>> 原始值:【2681】, 乘以分压基数【2】还原值:【5362】
2025-07-31 20:51:50:189 ==>> 【读取AccKey1电压(ADV14)后】通过,【5362mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:51:50:192 ==>> 检测【打开WIFI(2)】
2025-07-31 20:51:50:198 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:51:50:382 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:51:50:468 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:51:50:472 ==>> 检测【转刹把供电】
2025-07-31 20:51:50:475 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:51:50:487 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:51:50:757 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,125154.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,41,25,,,41,3,,,41,1*4A

$GBGSV,7,2,28,60,,,41,59,,,41,14,,,40,39,,,39,1*72

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,37,6,,,37,7,,,36,1*4E

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,36,1*45

$GBGSV,7,6,28,34,,,35,4,,,34,5,,,34,10,,,34,1*7B

$GBGSV,7,7,28,8,,,34,23,,,33,26,,,32,12,,,31,1*46

$GBRMC,125154.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125154.513,0.000,1541.370,1541.370,49.319,2097152,2097152,2097152*56



2025-07-31 20:51:51:017 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:51:51:022 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:51:51:026 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:51:51:047 ==>> +WIFISCAN:4,0,CC057790A741,-71
+WIFISCAN:4,1,44A1917CAD80,-81
+WIFISCAN:4,2,CC057790A7C0,-82
+WIFISCAN:4,3,CC057790A5C1,-82

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 20:51:51:122 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:51:51:243 ==>> [D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 20:51:51:348 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:51:51:726 ==>> $GBGGA,125155.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,41,25,,,41,3,,,41,1*4A

$GBGSV,7,2,28,60,,,41,59,,,41,14,,,41,39,,,40,1*7D

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,37,6,,,37,7,,,37,1*4F

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,36,1*45

$GBGSV,7,6,28,34,,,35,4,,,34,5,,,34,10,,,34,1*7B

$GBGSV,7,7,28,8,,,34,23,,,33,26,,,32,12,,,32,1*45

$GBRMC,125155.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125155.513,0.000,1547.292,1547.292,49.507,2097152,2097152,2097152*5E



2025-07-31 20:51:52:073 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:51:52:178 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:51:52:223 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:51:52:268 ==>> 1A A1 00 80 00 
Get AD_V15 2417mV
OVER 150


2025-07-31 20:51:52:343 ==>> 原始值:【2417】, 乘以分压基数【2】还原值:【4834】
2025-07-31 20:51:52:363 ==>> 【读取AD_V15电压(前)】通过,【4834mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:51:52:367 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:51:52:371 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:51:52:466 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:51:52:558 ==>> [D][05:18:17][COMM]read battery soc:255
[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2446mV
OVER 150


2025-07-31 20:51:52:618 ==>> 原始值:【2446】, 乘以分压基数【2】还原值:【4892】
2025-07-31 20:51:52:640 ==>> 【读取AD_V16电压(前)】通过,【4892mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:51:52:644 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:51:52:649 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:52:663 ==>> $GBGGA,125156.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,3

2025-07-31 20:51:52:723 ==>> 3,,,43,59,,,42,24,,,41,25,,,41,1*76

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,41,39,,,40,1*42

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,37,7,,,37,6,,,36,1*4E

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,35,1*46

$GBGSV,7,6,28,34,,,35,4,,,35,5,,,34,10,,,34,1*7A

$GBGSV,7,7,28,8,,,34,23,,,33,26,,,32,12,,,32,1*45

$GBRMC,125156.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125156.513,0.000,1547.294,1547.294,49.510,2097152,2097152,2097152*5B



2025-07-31 20:51:52:966 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:18][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:18][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:18][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:18][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:18][COMM]adc read battery ts volt:17 mv
[D][05:18:18][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3106  volt:5459 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1422  volt:32969 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:51:53:208 ==>> 【转刹把供电电压(主控ADC)】通过,【5459mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:51:53:212 ==>> 检测【转刹把供电电压】
2025-07-31 20:51:53:217 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:53:469 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:18][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:18][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:18][COMM]adc read right brake adc:18  volt:23 mv
[D][05:18:18][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:18][COMM]adc read battery ts volt:23 mv
[D][05:18:18][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3102  volt:5452 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:18  volt:14 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2451  volt:3949 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1422  volt:32969 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:51:53:727 ==>> $GBGGA,125157.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,40,39,,,39,1*4D

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,36,7,,,36,6,,,36,1*4E

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,35,1*46

$GBGSV,7,6,28,34,,,35,4,,,35,5,,,34,10,,,34,1*7A

$GBGSV,7,7,28,8,,,34,23,,,33,26,,,32,12,,,32,1*45

$GBRMC,125157.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125157.513,0.000,1539.886,1539.886,49.269,2097152,2097152,2097152*53



2025-07-31 20:51:53:763 ==>> 【转刹把供电电压】通过,【5452mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:51:53:767 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:51:53:773 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:51:53:925 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:51:54:041 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:51:54:045 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:51:54:048 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:51:54:151 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:51:54:256 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:51:54:262 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:51:54:361 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:51:54:454 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:51:54:485 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:20][COMM]read battery soc:255


2025-07-31 20:51:54:495 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:51:54:500 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:51:54:515 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:51:54:604 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:51:54:664 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:51:54:731 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:51:54:736 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:51:54:740 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:51:54:769 ==>> $GBGGA,125158.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,40,39,,,39,1*4D

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,9,,,36,7,,,36,6,,,36,1*4E

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,35,1*46

$GBGSV,7,6,28,34,,,35,4,,,34,5,,,34,10,,,34,1*7B

$GBGSV,7,7,28,8,,,34,23,,,32,26,,,32,12,,,32,1*44

$GBRMC,125158.513,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125158.513,0.000,1536.930,1536.930,49.178,2097152,2097152,2097152*5F

[D][05:18:20][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:20][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:51:54:859 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:51:55:015 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:51:55:018 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:51:55:021 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:51:55:164 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:51:55:288 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:51:55:295 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:51:55:301 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:51:55:359 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:51:55:570 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:51:55:573 ==>> 检测【左刹电压测试1】
2025-07-31 20:51:55:577 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:55:886 ==>> $GBGGA,125159.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,41,60,,,40,14,,,40,39,,,39,1*4C

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,7,,,37,9,,,36,6,,,36,1*4F

$GBGSV,7,5,28,13,,,36,2,,,36,44,,,36,38,,,35,1*46

$GBGSV,7,6,28,34,,,35,4,,,35,5,,,34,10,,,34,1*7A

$GBGSV,7,7,28,8,,,34,26,,,33,23,,,32,12,,,32,1*45

$GBRMC,125159.513,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125159.513,0.000,1539.884,1539.884,49.266,2097152,2097152,2097152*52

[W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:21][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:21][COMM]adc read left brake adc:1732  volt:2283 mv
[D][05:18:21][COMM]adc read right brake adc:1736  volt:2288 mv
[D][05:18:21][COMM]adc read throttle adc:1727  volt:2276 mv
[D][05:18:21][COMM]adc read battery ts volt:21 mv
[D][05:18:21][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:21][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:21][CO

2025-07-31 20:51:55:931 ==>> MM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:51:56:103 ==>> 【左刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 20:51:56:107 ==>> 检测【右刹电压测试1】
2025-07-31 20:51:56:129 ==>> 【右刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 20:51:56:133 ==>> 检测【转把电压测试1】
2025-07-31 20:51:56:151 ==>> 【转把电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 20:51:56:157 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:51:56:162 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:51:56:265 ==>> 3A A3 03 00 A3 


2025-07-31 20:51:56:355 ==>> OFF_OUT3
OVER 150


2025-07-31 20:51:56:438 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:51:56:442 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:51:56:447 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:51:56:460 ==>> [D][05:18:22][COMM]read battery soc:255


2025-07-31 20:51:56:565 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:51:56:670 ==>> $GBGGA,125200.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,41,39,,,40,1*42


2025-07-31 20:51:56:723 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:51:56:728 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:51:56:736 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:51:56:762 ==>> 

$GBGSV,7,3,28,42,,,39,41,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,7,,,37,9,,,37,13,,,37,1*7B

$GBGSV,7,5,28,2,,,37,6,,,36,44,,,36,38,,,36,1*70

$GBGSV,7,6,28,34,,,35,4,,,35,5,,,34,10,,,34,1*7A

$GBGSV,7,7,28,8,,,34,26,,,33,23,,,33,12,,,32,1*44

$GBRMC,125200.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125200.513,0.000,1551.729,1551.729,49.644,2097152,2097152,2097152*59



2025-07-31 20:51:56:835 ==>> 3A A3 05 00 A3

2025-07-31 20:51:56:865 ==>>  
OFF_OUT5
OVER 150


2025-07-31 20:51:57:012 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:51:57:019 ==>> 检测【左刹电压测试2】
2025-07-31 20:51:57:024 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:51:57:368 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:22][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:22][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:22][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:22][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:22][COMM]adc read battery ts volt:16 mv
[D][05:18:22][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:22][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2450  volt:3947 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:51:57:558 ==>> 【左刹电压测试2】通过,【17】符合目标值【0】至【50】要求!
2025-07-31 20:51:57:562 ==>> 检测【右刹电压测试2】
2025-07-31 20:51:57:579 ==>> 【右刹电压测试2】通过,【17】符合目标值【0】至【50】要求!
2025-07-31 20:51:57:583 ==>> 检测【转把电压测试2】
2025-07-31 20:51:57:603 ==>> 【转把电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:51:57:607 ==>> 检测【晶振检测】
2025-07-31 20:51:57:610 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:51:57:777 ==>> $GBGGA,125201.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,41,39,,,40,1*42

$GBGSV,7,3,28,42,,,40,41,,,39,40,,,39,1,,,39,1*40

$GBGSV,7,4,28,16,,,38,7,,,37,9,,,37,13,,,37,1*7B

$GBGSV,7,5,28,2,,,37,6,,,37,44,,,37,38,,,36,1*70

$GBGSV,7,6,28,34,,,35,10,,,35,4,,,34,5,,,34,1*7A

$GBGSV,7,7,28,8,,,34,26,,,34,23,,,33,12,,,32,1*43

$GBRMC,125201.513,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125201.513,0.000,778.108,778.108,711.597,2097152,2097152,2097152*6F

[W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 20:51:57:891 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:51:57:895 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:51:57:898 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:51:57:974 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1668mV
Get AD_V4 1647mV
Get AD_V5 2778mV
Get AD_V6 2022mV
Get AD_V7 1084mV
OVER 150


2025-07-31 20:51:58:175 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:51:58:179 ==>> 检测【检测BootVer】
2025-07-31 20:51:58:182 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:51:58:518 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539205
[D][05:18:23][FCTY]HardwareID  = 867222087691244
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11600 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1303
[D][05:18:23][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 354 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4


2025-07-31 20:51:58:578 ==>> 
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3778 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
                                         

2025-07-31 20:51:58:683 ==>> $GBGGA,125202.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,42

2025-07-31 20:51:58:719 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:51:58:724 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:51:58:732 ==>> 检测【检测固件版本】
2025-07-31 20:51:58:739 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:51:58:742 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:51:58:746 ==>> 检测【检测蓝牙版本】
2025-07-31 20:51:58:749 ==>> ,60,,,41,59,,,41,1*42

$GBGSV,7,2,28,24,,,41,14,,,41,25,,,41,39,,,40,1*76

$GBGSV,7,3,28,40,,,39,42,,,39,41,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,2,,,37,7,,,37,9,,,37,1*4B

$GBGSV,7,5,28,6,,,37,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,7,6,28,34,,,35,4,,,35,26,,,34,8,,,34,1*72

$GBGSV,7,7,28,10,,,34,5,,,34,23,,,33,12,,,32,1*4B

$GBRMC,125202.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125202.513,0.000,775.893,775.893,709.572,2097152,2097152,2097152*6E



2025-07-31 20:51:58:759 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:51:58:774 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:51:58:777 ==>> 检测【检测MoBikeId】
2025-07-31 20:51:58:780 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:51:58:784 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:51:58:787 ==>> 检测【检测蓝牙地址】
2025-07-31 20:51:58:791 ==>> 取到目标值:E5DD8EBABA3E
2025-07-31 20:51:58:800 ==>> 【检测蓝牙地址】通过,【E5DD8EBABA3E】符合目标值【】要求!
2025-07-31 20:51:58:807 ==>> 提取到蓝牙地址:E5DD8EBABA3E
2025-07-31 20:51:58:813 ==>> 检测【BOARD_ID】
2025-07-31 20:51:58:820 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:51:58:824 ==>> 检测【检测充电电压】
2025-07-31 20:51:58:840 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:51:58:843 ==>> 检测【检测VBUS电压1】
2025-07-31 20:51:58:859 ==>> 【检测VBUS电压1】通过,【11600mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:51:58:863 ==>> 检测【检测充电电流】
2025-07-31 20:51:58:878 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:51:58:882 ==>> 检测【检测IMEI】
2025-07-31 20:51:58:885 ==>> 取到目标值:867222087691244
2025-07-31 20:51:58:898 ==>> 【检测IMEI】通过,【867222087691244】符合目标值【】要求!
2025-07-31 20:51:58:905 ==>> 提取到IMEI:867222087691244
2025-07-31 20:51:58:943 ==>> 检测【检测IMSI】
2025-07-31 20:51:58:947 ==>> 取到目标值:460130071539205
2025-07-31 20:51:58:950 ==>> 【检测IMSI】通过,【460130071539205】符合目标值【】要求!
2025-07-31 20:51:58:954 ==>> 提取到IMSI:460130071539205
2025-07-31 20:51:58:958 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:51:58:961 ==>> 取到目标值:460130071539205
2025-07-31 20:51:58:965 ==>> 【校验网络运营商(移动)】通过,【460130071539205】符合目标值【】要求!
2025-07-31 20:51:58:984 ==>> 检测【打开CAN通信】
2025-07-31 20:51:58:987 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:51:59:059 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:51:59:243 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:51:59:247 ==>> 检测【检测CAN通信】
2025-07-31 20:51:59:253 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:51:59:390 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:51:59:449 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35956
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:51:59:509 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:51:59:515 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:51:59:520 ==>> 检测【关闭CAN通信】
2025-07-31 20:51:59:541 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:51:59:585 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:51:59:659 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:51:59:749 ==>> $GBGGA,125203.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,14,,,40,40,,,39,1*77

$GBGSV,7,3,28,39,,,39,42,,,39,41,,,39,1,,,38,1*41

$GBGSV,7,4,28,16,,,38,2,,,37,7,,,37,9,,,37,1*4B

$GBGSV,7,5,28,6,,,37,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,7,6,28,10,,,35,34,,,35,4,,,35,26,,,34,1*4A

$GBGSV,7,7,28,8,,,34,5,,,34,23,,,33,12,,,32,1*72

$GBRMC,125203.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125203.513,0.000,774.408,774.408,708.213,2097152,2097152,2097152*6E



2025-07-31 20:51:59:800 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:51:59:804 ==>> 检测【打印IMU STATE】
2025-07-31 20:51:59:807 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:51:59:960 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:52:00:087 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:52:00:093 ==>> 检测【六轴自检】
2025-07-31 20:52:00:103 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:52:00:245 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:52:00:500 ==>> [D][05:18:26][COMM]read battery soc:255


2025-07-31 20:52:00:755 ==>> $GBGGA,125204.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,14,,,40,40,,,39,1*77

$GBGSV,7,3,28,39,,,39,42,,,39,41,,,39,1,,,38,1*41

$GBGSV,7,4,28,16,,,38,2,,,37,7,,,37,13,,,37,1*70

$GBGSV,7,5,28,9,,,37,6,,,37,38,,,36,44,,,36,1*7A

$GBGSV,7,6,28,34,,,35,4,,,35,26,,,34,8,,,34,1*72

$GBGSV,7,7,28,10,,,34,5,,,34,23,,,33,12,,,32,1*4B

$GBRMC,125204.513,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125204.513,0.000,774.409,774.409,708.214,2097152,2097152,2097152*6E



2025-07-31 20:52:01:753 ==>> $GBGGA,125205.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,14,,,40,40,,,39,1*77

$GBGSV,7,3,28,39,,,39,42,,,39,41,,,39,1,,,38,1*41

$GBGSV,7,4,28,16,,,38,2,,,37,7,,,37,13,,,36,1*71

$GBGSV,7,5,28,38,,,36,44,,,36,9,,,36,6,,,36,1*7A

$GBGSV,7,6,28,34,,,36,26,,,34,8,,,34,10,,,34,1*45

$GBGSV,7,7,28,5,,,34,4,,,34,23,,,33,12,,,32,1*7E

$GBRMC,125205.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125205.513,0.000,772.195,772.195,706.190,2097152,2097152,2097152*6E



2025-07-31 20:52:01:963 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:52:02:068 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38591 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] 

2025-07-31 20:52:02:098 ==>> ACCEL:[-21,-11,4026]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:52:02:177 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:52:02:181 ==>> 检测【打印IMU STATE2】
2025-07-31 20:52:02:189 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:52:02:355 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:52:02:454 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:52:02:460 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:52:02:468 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:52:02:505 ==>> [D][05:18:28][COMM]read battery soc:255


2025-07-31 20:52:02:565 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:52:02:741 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:52:02:749 ==>> 检测【检测VBUS电压2】
2025-07-31 20:52:02:777 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:52:02:806 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 12
$GBGGA,125206.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,41,59,,,41,24,,,41,1*41

$GBGSV,7,2,28,25,,,41,60,,,40,14,,,40,40,,,39,1*76

$GBGSV,7,3,28,39,,,39,42,,,39,41,,,39,1,,,38,1*41

$GBGSV,7,4,28,16,,,38,7,,,37,9,,,37,2,,,36,1*4A

$GBGSV,7,5,28,13,,,36,38,,,36,44,,,36,6,,,36,1*41

$GBGSV,7,6,28,34,,,36,10,,,35,8,,,34,5,,,34,1*75

$GBGSV,7,7,28,4,,,34,26,,,33,23,,,33,12,,,32,1*48

$GBRMC,125206.513,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125206.513,0.000,771.455,771.455,705.513,2097152,2097152,2097152*61



2025-07-31 20:52:03:106 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539205
[D][05:18:28][FCTY]HardwareID  = 867222087691244
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:18:28][FCTY]Bat         = 4044 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 8, adc = 323
[D][05:18:28][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 75 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3778 m

2025-07-31 20:52:03:151 ==>> v
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:52:03:279 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:52:03:620 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539205
[D][05:18:28][FCTY]HardwareID  = 867222087691244
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:18:28][FCTY]Bat         = 4064 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 183
[D][05:18:28][FCTY]Acckey1 vol = 5575 mv, Acckey2 vol = 177 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1

2025-07-31 20:52:03:665 ==>> _GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3778 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:52:03:755 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:52:03:805 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:52:04:115 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539205
[D][05:18:29][FCTY]HardwareID  = 867222087691244
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:18:29][FCTY]Bat         = 4064 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 157
[D][05:18:29][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29]

2025-07-31 20:52:04:160 ==>> [FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3778 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:52:04:344 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:52:04:446 ==>> [D][05:18:29][COMM]msg 0601 loss. last_tick:35945. cur_tick:40956. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40957


2025-07-31 20:52:05:066 ==>>                                                                                                              e_poweroff type 16.... 
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539205
[D][05:18:30][FCTY]HardwareID  = 867222087691244
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 131
[D][05:18:30][FCTY]Acckey1 vol = 5579 mv, Acckey2 vol = 252 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4


2025-07-31 20:52:05:136 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:52:05:143 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:52:05:151 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:52:05:171 ==>> 
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3778 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:6

2025-07-31 20:52:05:277 ==>> 0 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===============================================

2025-07-31 20:52:05:382 ==>> ============
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5B9B5A8759C9159800F6C420296927AF76A

2025-07-31 20:52:05:487 ==>> E0C3FF5A8DF99A07FAC5BFE1CD22AE7C6AD9BF9D1DED8EE4E0F41CE333E06A184ED10B8C213FF78CA8B7FDD503F89D3C7FD7EB3EFEF13B2657E17F18858527E3B0
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150
                                                                                                                                           

2025-07-31 20:52:05:547 ==>>                                                                         

2025-07-31 20:52:05:622 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 20:52:05:688 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:52:05:692 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:52:05:698 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:52:05:758 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:52:05:963 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:52:05:974 ==>> 检测【打开WIFI(3)】
2025-07-31 20:52:06:004 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:52:06:180 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:52:06:248 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:52:06:259 ==>> 检测【扩展芯片hw】
2025-07-31 20:52:06:268 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:52:06:451 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:52:06:535 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:52:06:541 ==>> 检测【扩展芯片boot】
2025-07-31 20:52:06:560 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:52:06:565 ==>> 检测【扩展芯片sw】
2025-07-31 20:52:06:585 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:52:06:594 ==>> 检测【检测音频FLASH】
2025-07-31 20:52:06:598 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:52:06:724 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:52:06:890 ==>> +WIFISCAN:4,0,CC057790A741,-67
+WIFISCAN:4,1,CC057790A740,-72
+WIFISCAN:4,2,CC057790A7C0,-81
+WIFISCAN:4,3,CC057790A5C0,-84

[D][05:18:32][CAT1]wifi scan report total[4]


2025-07-31 20:52:07:132 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:52:07:836 ==>>              [COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_na

2025-07-31 20:52:07:942 ==>> me:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:32][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_s

2025-07-31 20:52:08:046 ==>> end:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec8

2025-07-31 20:52:08:152 ==>> 00m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_aud

2025-07-31 20:52:08:258 ==>> io_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][GNSS]recv submsg id[3]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l

2025-07-31 20:52:08:348 ==>> :[975].hexsend, index:4, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:33][COMM]read battery soc:255


2025-07-31 20:52:09:647 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:52:10:241 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3D02F6CBF661764DA8ECAD0

2025-07-31 20:52:10:316 ==>> A2CBB9DC9255D753E3EC07366001E96DB99551B911D20AD76544BE916529C46C5974A42EE667697BE24898198F7ECDDB484B4883266305ADFA62057C01ABF8B42028E19F81859D
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:52:10:864 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 20:52:11:318 ==>> [D][05:18:36][COMM]47697 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:36][COMM]accel parse set 0
[D][05:18:36][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:52:11:635 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:52:11:660 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:52:11:665 ==>> 检测【打开喇叭声音】
2025-07-31 20:52:11:671 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:52:12:360 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:37][COMM]--->crc16:0x419c
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18

2025-07-31 20:52:12:464 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:52:12:471 ==>> 检测【打开大灯控制】
2025-07-31 20:52:12:477 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:52:12:500 ==>> :37][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:37][COMM]f:[ec800m

2025-07-31 20:52:12:570 ==>> _audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:37][COMM]48708 imu init OK
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:52:12:645 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:52:12:759 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:52:12:765 ==>> 检测【关闭仪表供电3】
2025-07-31 20:52:12:769 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:52:12:930 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 20:52:13:057 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:52:13:063 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:52:13:072 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:52:13:216 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:52:13:359 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:52:13:364 ==>> 检测【读大灯电压】
2025-07-31 20:52:13:369 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:52:13:547 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[32807]


2025-07-31 20:52:13:637 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:52:13:647 ==>> 【读大灯电压】通过,【32807mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:52:13:653 ==>> 检测【关闭大灯控制2】
2025-07-31 20:52:13:670 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:52:13:847 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:52:13:932 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:52:13:938 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:52:13:943 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:52:14:148 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[69]


2025-07-31 20:52:14:218 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:52:14:223 ==>> 检测【打开WIFI(4)】
2025-07-31 20:52:14:233 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:52:14:478 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:39][CAT1]gsm read msg sub id: 12
[D][05:18:39][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:39][CAT1]<<< 
OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:52:14:543 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:52:14:548 ==>> 检测【EC800M模组版本】
2025-07-31 20:52:14:556 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:52:14:751 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:52:14:856 ==>> [D][05:18:40][

2025-07-31 20:52:14:886 ==>> COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:52:14:991 ==>> [D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:52:15:072 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:52:15:078 ==>> 检测【配置蓝牙地址】
2025-07-31 20:52:15:087 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:52:15:281 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E5DD8EBABA3E>】
2025-07-31 20:52:15:416 ==>> [D][05:18:40][PROT]CLEAN,SEND:1
[D][05:18:40][PROT]index:1 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:2
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:40][CAT1]gsm read msg sub id: 15
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][CAT1]Send Data To Server[198][201] ... ->

2025-07-31 20:52:15:506 ==>> :
0063B98C113311331133113311331B88B35CF66ED1BAFBE6860DFA37341978B9091CCB0086DC8714AAB2C2E8931857DEF7E3CE0CF066B3E839EC1B601E5D00696634A61CF59D35187CBEBFE6424BB11DC4741031D3A2C3D292BC36797B9FA8AE2B36E0
[W][05:18:40][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]


2025-07-31 20:52:15:566 ==>> recv ble 1
recv ble 2
ble set mac ok :e5,dd,8e,ba,ba,3e
enable filters ret : 0

2025-07-31 20:52:15:641 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:52:15:847 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:52:15:860 ==>> 检测【BLETEST】
2025-07-31 20:52:15:870 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:52:15:887 ==>> [D][05:18:41][COMM]52402 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:52:15:956 ==>> 4A A4 01 A4 4A 


2025-07-31 20:52:16:061 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E5DD8EBABA3E*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E5DD8EBABA3E99999OVER 150


2025-07-31 20:52:16:882 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:52:16:888 ==>> 该项需要延时执行
2025-07-31 20:52:16:900 ==>> [D][05:18:42][COMM]53414 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:52:17:478 ==>> [D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:52:17:658 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:52:17:899 ==>> [D][05:18:43][COMM]54426 imu init OK


2025-07-31 20:52:19:471 ==>> +WIFISCAN:4,0,CC057790A740,-71
+WIFISCAN:4,1,CC057790A741,-72
+WIFISCAN:4,2,44A1917CAD81,-81
+WIFISCAN:4,3,CC057790A5C0,-83

[D][05:18:44][CAT1]wifi scan report total[4]


2025-07-31 20:52:19:667 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:52:20:639 ==>> [D][05:18:45][GNSS]recv submsg id[3]
[D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:5
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:1
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]sending traceid [9999999999900006]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[198]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... -

2025-07-31 20:52:20:714 ==>> >:
0063B981113311331133113311331B88B3CB4D3C63F7342AAC0A6EB72F9856FB19FC0592CCA3243B414FA11AA461B701D50289A36FF00449B8918922E11B8571AA41558F89D5CF8909872EF5F5F1CAC3EA3C976BA83CD48E4EBD6468E86ECF81A8F124
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 20:52:21:659 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:52:23:657 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:52:25:890 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B546EE335FDDF5428EF91BFD8DB77EB82714F636A2AA49C61E39B6D734883568BD0C60A7A0B270651A4A54E9E490F5176B

2025-07-31 20:52:25:965 ==>> 723FC957361A275B77128F7E7D13E6CE817EC8B5F781C0789A3C66B748810876FD99
[D][05:18:51][COMM]read battery soc:255
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]


2025-07-31 20:52:26:886 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:52:26:892 ==>> 检测【检测WiFi结果】
2025-07-31 20:52:26:897 ==>> WiFi信号:【CC057790A741】,信号值:-71
2025-07-31 20:52:26:921 ==>> WiFi信号:【44A1917CAD80】,信号值:-81
2025-07-31 20:52:26:938 ==>> WiFi信号:【CC057790A7C0】,信号值:-82
2025-07-31 20:52:26:952 ==>> WiFi信号:【CC057790A5C1】,信号值:-82
2025-07-31 20:52:26:967 ==>> WiFi信号:【CC057790A740】,信号值:-72
2025-07-31 20:52:26:981 ==>> WiFi信号:【CC057790A5C0】,信号值:-84
2025-07-31 20:52:26:996 ==>> WiFi信号:【44A1917CAD81】,信号值:-81
2025-07-31 20:52:27:006 ==>> WiFi数量【7】, 最大信号值:-71
2025-07-31 20:52:27:020 ==>> 检测【检测GPS结果】
2025-07-31 20:52:27:041 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:52:27:060 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]all continue location stop
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]all sing location stop


2025-07-31 20:52:27:688 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:52:27:900 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:52:27:912 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:27:935 ==>> 定位已等待【1】秒.
2025-07-31 20:52:28:262 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:53][COMM]Open GPS Module...
[D][05:18:53][COMM]LOC_MODEL_CONT
[D][05:18:53][GNSS]start event:8
[D][05:18:53][GNSS]GPS start. ret=0
[W][05:18:53][GNSS]start cont locating
[D][05:18:53][CAT1]gsm read msg sub id: 23
[D][05:18:53][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:53][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:52:28:914 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:28:925 ==>> 定位已等待【2】秒.
2025-07-31 20:52:28:975 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:52:29:856 ==>> [D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:55][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,33,,,42,41,,,39,42,,,39,39,,,39,1*78

$GBGSV,2,2,08,40,,,34,25,,,33,14,,,39,24,,,37,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1561.608,1561.608,49.968,2097152,2097152,2097152*45

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:55][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 23, ret: 6
[D][05:18:55][CAT1]sub id: 23, ret: 6



2025-07-31 20:52:29:916 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:29:925 ==>> 定位已等待【3】秒.
2025-07-31 20:52:30:421 ==>> [D][05:18:55][GNSS]recv submsg id[1]
[D][05:18:55][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:52:30:769 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,42,60,,,41,14,,,39,41,,,39,1*74

$GBGSV,4,2,13,42,,,39,39,,,39,24,,,39,59,,,39,1*74

$GBGSV,4,3,13,3,,,39,25,,,38,13,,,37,40,,,36,1*41

$GBGSV,4,4,13,16,,,41,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1613.346,1613.346,51.539,2097152,2097152,2097152*44



2025-07-31 20:52:30:919 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:30:930 ==>> 定位已等待【4】秒.
2025-07-31 20:52:31:099 ==>> [D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88

2025-07-31 20:52:31:174 ==>> B5A423D03580EB08D503C0064400154E7AAF330D490B161567862BB06A97BE531856EA16D479C9F0E988718C1F9B78AEA031B489A45F02CB3C2B3EE664AB5C651E9494397370BEB8A83BF07DEEF067CC2C82EE
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 20:52:31:825 ==>> [D][05:18:57][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,43,60,,,40,14,,,40,24,,,40,1*76

$GBGSV,5,2,20,59,,,40,3,,,40,41,,,39,42,,,39,1*4F

$GBGSV,5,3,20,39,,,39,25,,,39,1,,,39,13,,,37,1*42

$GBGSV,5,4,20,40,,,37,2,,,36,44,,,36,5,,,35,1*74

$GBGSV,5,5,20,38,,,34,4,,,34,34,,,34,16,,,39,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1573.212,1573.212,50.306,2097152,2097152,2097152*4F



2025-07-31 20:52:31:930 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:31:939 ==>> 定位已等待【5】秒.
2025-07-31 20:52:32:817 ==>> $GBGGA,125236.621,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,40,14,,,40,24,,,40,1*77

$GBGSV,6,2,23,59,,,40,3,,,40,25,,,40,41,,,39,1*40

$GBGSV,6,3,23,42,,,39,39,,,39,1,,,38,40,,,38,1*4B

$GBGSV,6,4,23,13,,,36,2,,,36,44,,,36,38,,,35,1*4D

$GBGSV,6,5,23,5,,,34,4,,,34,34,,,34,23,,,33,1*74

$GBGSV,6,6,23,16,,,39,9,,,37,7,,,36,1*75

$GBRMC,125236.621,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125236.621,0.000,1560.890,1560.890,49.918,2097152,2097152,2097152*58



2025-07-31 20:52:32:937 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:32:946 ==>> 定位已等待【6】秒.
2025-07-31 20:52:33:754 ==>> $GBGGA,125237.521,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,40,14,,,40,24,,,40,1*70

$GBGSV,7,2,25,59,,,40,3,,,40,25,,,40,41,,,39,1*47

$GBGSV,7,3,25,39,,,39,42,,,38,1,,,38,40,,,38,1*4D

$GBGSV,7,4,25,9,,,36,13,,,36,2,,,36,44,,,36,1*7B

$GBGSV,7,5,25,7,,,36,6,,,36,38,,,35,5,,,34,1*4D

$GBGSV,7,6,25,4,,,34,34,,,34,23,,,33,10,,,33,1*43

$GBGSV,7,7,25,16,,,38,1*7D

$GBRMC,125237.521,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125237.521,0.000,1542.579,1542.579,49.333,2097152,2097152,2097152*59

[D][05:18:59][COMM]read battery soc:255


2025-07-31 20:52:33:952 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:33:962 ==>> 定位已等待【7】秒.
2025-07-31 20:52:34:701 ==>> $GBGGA,125238.501,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,41,25,,,41,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,59,,,40,3,,,40,41,,,39,1*45

$GBGSV,7,3,25,39,,,39,42,,,38,1,,,38,40,,,38,1*4D

$GBGSV,7,4,25,9,,,36,13,,,36,2,,,36,7,,,36,1*4C

$GBGSV,7,5,25,6,,,36,44,,,35,38,,,35,4,,,34,1*78

$GBGSV,7,6,25,34,,,34,10,,,34,5,,,33,23,,,33,1*42

$GBGSV,7,7,25,16,,,38,1*7D

$GBRMC,125238.501,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125238.501,0.000,1546.044,1546.044,49.454,2097152,2097152,2097152*52



2025-07-31 20:52:34:960 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:34:969 ==>> 定位已等待【8】秒.
2025-07-31 20:52:35:753 ==>> $GBGGA,125239.501,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,41,25,,,41,59,,,41,1*78

$GBGSV,7,2,25,60,,,40,14,,,40,3,,,40,41,,,39,1*4F

$GBGSV,7,3,25,39,,,39,42,,,39,40,,,39,1,,,38,1*4D

$GBGSV,7,4,25,9,,,36,13,,,36,2,,,36,7,,,36,1*4C

$GBGSV,7,5,25,6,,,36,44,,,36,38,,,35,4,,,34,1*7B

$GBGSV,7,6,25,34,,,34,10,,,34,5,,,33,23,,,32,1*43

$GBGSV,7,7,25,16,,,38,1*7D

$GBRMC,125239.501,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125239.501,0.000,1551.232,1551.232,49.625,2097152,2097152,2097152*57

[D][05:19:01][COMM]read battery soc:255


2025-07-31 20:52:35:964 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:35:977 ==>> 定位已等待【9】秒.
2025-07-31 20:52:36:348 ==>> [D][05:19:01][PROT]CLEAN,SEND:0
[D][05:19:01][PROT]CLEAN:0
[D][05:19:01][PROT]index:2 1629955141
[D][05:19:01][PROT]is_send:0
[D][05:19:01][PROT]sequence_num:6
[D][05:19:01][PROT]retry_timeout:0
[D][05:19:01][PROT]retry_times:3
[D][05:19:01][PROT]send_path:0x2
[D][05:19:01][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:01][PROT]===========================================================
[D][05:19:01][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:19:01][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955141]
[D][05:19:01][PROT]===========================================================
[D][05:19:01][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980221
[D][05:19:01][PROT]sending traceid [9999999999900007]
[D][05:19:01][PROT]Send_TO_M2M [1629955141]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:01][SAL ]sock send credit cnt[6]
[D][05:19:01][SAL ]sock send ind credit cnt[6]
[D][05:19:01][M2M ]m2m send data len[134]
[D][05:19:01][SAL ]Cellular task submsg id[10]
[D][05:19:01][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:01][CAT1]gsm read msg sub id: 15
[D][05:19:01][CAT1]tx ret[17] >>

2025-07-31 20:52:36:422 ==>> > AT+QISEND=0,134

[D][05:19:01][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BEA2C7D659DE4360C4EEF70F748D73E7E730F6A92E4AAF82FE73456519920E5F2E8A41F85C1BEBB8CF30AB41930A4B435B6C46
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:01][PROT]M2M Send ok [1629955141]


2025-07-31 20:52:36:710 ==>> $GBGGA,125240.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,14,,,41,1*72

$GBGSV,7,2,26,59,,,40,60,,,40,3,,,40,41,,,39,1*45

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,39,1,,,38,1*4E

$GBGSV,7,4,26,9,,,36,13,,,36,2,,,36,7,,,36,1*4F

$GBGSV,7,5,26,6,,,36,44,,,36,38,,,35,34,,,35,1*4A

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,33,23,,,32,1*73

$GBGSV,7,7,26,26,,,31,16,,,38,1*78

$GBRMC,125240.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125240.501,0.000,1542.259,1542.259,49.347,2097152,2097152,2097152*58



2025-07-31 20:52:36:967 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:36:978 ==>> 定位已等待【10】秒.
2025-07-31 20:52:37:749 ==>> $GBGGA,125241.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,14,,,40,1*73

$GBGSV,7,2,26,59,,,40,60,,,40,3,,,40,41,,,39,1*45

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,39,1,,,38,1*4E

$GBGSV,7,4,26,9,,,36,13,,,36,2,,,36,7,,,36,1*4F

$GBGSV,7,5,26,6,,,36,44,,,36,38,,,35,34,,,35,1*4A

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,34,23,,,32,1*74

$GBGSV,7,7,26,26,,,31,16,,,38,1*78

$GBRMC,125241.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125241.501,0.000,1542.254,1542.254,49.343,2097152,2097152,2097152*5D

[D][05:19:03][COMM]read battery soc:255


2025-07-31 20:52:37:979 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:37:988 ==>> 定位已等待【11】秒.
2025-07-31 20:52:38:709 ==>> $GBGGA,125242.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,25,,,41,14,,,40,1*72

$GBGSV,7,2,26,59,,,40,60,,,40,3,,,40,41,,,39,1*45

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,38,1,,,38,1*4F

$GBGSV,7,4,26,9,,,36,13,,,36,2,,,36,7,,,36,1*4F

$GBGSV,7,5,26,6,,,36,44,,,36,38,,,35,34,,,34,1*4B

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,33,23,,,32,1*73

$GBGSV,7,7,26,26,,,31,16,,,38,1*78

$GBRMC,125242.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125242.501,0.000,1535.621,1535.621,49.131,2097152,2097152,2097152*59



2025-07-31 20:52:38:981 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:38:990 ==>> 定位已等待【12】秒.
2025-07-31 20:52:39:777 ==>> $GBGGA,125243.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,60,,,41,1*71

$GBGSV,7,2,26,14,,,40,59,,,40,3,,,40,41,,,39,1*46

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,39,1,,,37,1*41

$GBGSV,7,4,26,9,,,36,13,,,36,2,,,36,7,,,36,1*4F

$GBGSV,7,5,26,6,,,36,44,,,36,38,,,35,34,,,34,1*4B

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,33,23,,,32,1*73

$GBGSV,7,7,26,26,,,32,16,,,38,1*7B

$GBRMC,125243.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125243.501,0.000,1540.598,1540.598,49.292,2097152,2097152,2097152*52

[D][05:19:05][COMM]read battery soc:255


2025-07-31 20:52:39:991 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:40:001 ==>> 定位已等待【13】秒.
2025-07-31 20:52:40:709 ==>> $GBGGA,125244.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,59,,,41,1*7B

$GBGSV,7,2,26,60,,,40,14,,,40,3,,,40,41,,,39,1*4C

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,38,1,,,37,1*40

$GBGSV,7,4,26,9,,,36,13,,,36,7,,,36,6,,,36,1*4B

$GBGSV,7,5,26,44,,,36,2,,,35,38,,,35,34,,,34,1*4C

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,33,23,,,32,1*73

$GBGSV,7,7,26,26,,,32,16,,,37,1*74

$GBRMC,125244.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125244.501,0.000,1537.282,1537.282,49.187,2097152,2097152,2097152*52



2025-07-31 20:52:41:000 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:41:009 ==>> 定位已等待【14】秒.
2025-07-31 20:52:41:527 ==>> [D][05:19:06][PROT]CLEAN,SEND:2
[D][05:19:06][PROT]index:2 1629955146
[D][05:19:06][PROT]is_send:0
[D][05:19:06][PROT]sequence_num:6
[D][05:19:06][PROT]retry_timeout:0
[D][05:19:06][PROT]retry_times:2
[D][05:19:06][PROT]send_path:0x2
[D][05:19:06][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980221
[D][05:19:06][PROT]sending traceid [9999999999900007]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[134]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:06][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:06][CAT1]<<< 
ERROR



2025-07-31 20:52:41:723 ==>> $GBGGA,125245.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,59,,,41,1*7B

$GBGSV,7,2,26,60,,,41,14,,,40,3,,,40,41,,,39,1*4D

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,39,1,,,38,1*4E

$GBGSV,7,4,26,9,,,36,13,,,36,7,,,36,6,,,36,1*4B

$GBGSV,7,5,26,44,,,36,2,,,36,38,,,35,34,,,35,1*4E

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,34,23,,,32,1*74

$GBGSV,7,7,26,26,,,32,16,,,38,1*7B

$GBRMC,125245.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125245.501,0.000,1547.229,1547.229,49.502,2097152,2097152,2097152*5A

                                         

2025-07-31 20:52:42:014 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:42:024 ==>> 定位已等待【15】秒.
2025-07-31 20:52:42:702 ==>> $GBGGA,125246.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,59,,,41,1*7B

$GBGSV,7,2,26,60,,,41,14,,,41,3,,,41,41,,,39,1*4D

$GBGSV,7,3,26,39,,,39,42,,,39,40,,,39,1,,,38,1*4E

$GBGSV,7,4,26,9,,,37,7,,,37,13,,,36,6,,,36,1*4B

$GBGSV,7,5,26,44,,,36,2,,,36,38,,,35,34,,,35,1*4E

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,34,26,,,33,1*70

$GBGSV,7,7,26,23,,,32,16,,,38,1*7E

$GBRMC,125246.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125246.501,0.000,1555.520,1555.520,49.766,2097152,2097152,2097152*59



2025-07-31 20:52:43:025 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:43:035 ==>> 定位已等待【16】秒.
2025-07-31 20:52:43:714 ==>> $GBGGA,125247.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,41,25,,,41,60,,,41,1*70

$GBGSV,7,2,27,3,,,41,59,,,40,14,,,40,41,,,39,1*46

$GBGSV,7,3,27,39,,,39,42,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,27,7,,,37,9,,,36,13,,,36,6,,,36,1*4B

$GBGSV,7,5,27,44,,,36,2,,,36,38,,,35,34,,,35,1*4F

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125247.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125247.501,0.000,1545.121,1545.121,49.432,2097152,2097152,2097152*5A



2025-07-31 20:52:43:744 ==>>                                          

2025-07-31 20:52:44:037 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:44:048 ==>> 定位已等待【17】秒.
2025-07-31 20:52:44:732 ==>> $GBGGA,125244.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,60,,,41,1*71

$GBGSV,7,2,27,3,,,41,59,,,41,14,,,40,41,,,39,1*47

$GBGSV,7,3,27,39,,,39,42,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,27,7,,,37,2,,,37,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125244.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125244.501,0.000,1546.714,1546.714,49.481,2097152,2097152,2097152*51



2025-07-31 20:52:45:039 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:45:049 ==>> 定位已等待【18】秒.
2025-07-31 20:52:45:073 ==>> [D][05:19:10][COMM]IMU: [3,-2,-937] ret=22 AWAKE!


2025-07-31 20:52:45:745 ==>> $GBGGA,125245.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,41,25,,,41,60,,,41,1*70

$GBGSV,7,2,27,3,,,41,59,,,40,14,,,40,41,,,39,1*46

$GBGSV,7,3,27,39,,,39,42,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,27,7,,,37,2,,,36,9,,,36,13,,,36,1*4F

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,35,10,,,34,5,,,34,8,,,34,1*4B

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125245.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125245.501,0.000,1546.714,1546.714,49.481,2097152,2097152,2097152*50

                                         

2025-07-31 20:52:46:053 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:46:066 ==>> 定位已等待【19】秒.
2025-07-31 20:52:46:715 ==>> $GBGGA,125246.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,3,,,41,1*44

$GBGSV,7,2,27,60,,,40,59,,,40,14,,,40,41,,,39,1*72

$GBGSV,7,3,27,39,,,39,42,,,39,40,,,39,1,,,38,1*4F

$GBGSV,7,4,27,7,,,37,2,,,37,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125246.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125246.501,0.000,1543.521,1543.521,49.375,2097152,2097152,2097152*5F



2025-07-31 20:52:47:055 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:47:066 ==>> 定位已等待【20】秒.
2025-07-31 20:52:47:703 ==>> $GBGGA,125247.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,3,,,41,25,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,14,,,40,39,,,39,1*7D

$GBGSV,7,3,27,1,,,39,41,,,38,42,,,38,40,,,38,1*40

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125247.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125247.501,0.000,1535.545,1535.545,49.117,2097152,2097152,2097152*58



2025-07-31 20:52:47:748 ==>>                                          

2025-07-31 20:52:48:056 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:48:066 ==>> 定位已等待【21】秒.
2025-07-31 20:52:48:705 ==>> $GBGGA,125248.501,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,3,,,41,59,,,41,1*4F

$GBGSV,7,2,27,25,,,40,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,1,,,39,41,,,38,42,,,38,40,,,38,1*40

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,33,1*4D

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,37,1*74

$GBRMC,125248.501,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125248.501,0.000,1535.550,1535.550,49.122,2097152,2097152,2097152*51



2025-07-31 20:52:49:057 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:49:067 ==>> 定位已等待【22】秒.
2025-07-31 20:52:49:708 ==>> $GBGGA,125249.501,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,3,,,41,59,,,41,1*4F

$GBGSV,7,2,27,25,,,41,60,,,41,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,41,,,39,42,,,39,40,,,39,1,,,38,1*40

$GBGSV,7,4,27,7,,,37,2,,,36,9,,,36,13,,,36,1*4F

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,37,1*74

$GBRMC,125249.501,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125249.501,0.000,1545.120,1545.120,49.430,2097152,2097152,2097152*56



2025-07-31 20:52:49:738 ==>>                                          

2025-07-31 20:52:50:059 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:50:069 ==>> 定位已等待【23】秒.
2025-07-31 20:52:50:716 ==>> $GBGGA,125250.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,41,,,38,42,,,38,40,,,38,1,,,38,1*41

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,33,1*4D

$GBGSV,7,7,27,26,,,33,23,,,31,16,,,38,1*78

$GBRMC,125250.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125250.501,0.000,1530.767,1530.767,48.970,2097152,2097152,2097152*56



2025-07-31 20:52:51:069 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:51:079 ==>> 定位已等待【24】秒.
2025-07-31 20:52:51:541 ==>> [D][05:19:16][CAT1]exec over: func id: 15, ret: -93
[D][05:19:16][CAT1]sub id: 15, ret: -93

[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:16][SAL ]socket send fail. id[4]
[D][05:19:16][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:16][M2M ]m2m select fd[4]
[D][05:19:16][M2M ]socket[4] Link is disconnected
[D][05:19:16][M2M ]tcpclient close[4]
[D][05:19:16][SAL ]socket[4] has closed
[D][05:19:16][PROT]protocol read data ok
[E][05:19:16][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:16][PROT]M2M Send Fail [1629955156]
[D][05:19:16][PROT]CLEAN,SEND:2
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:16][CAT1]gsm read msg sub id: 10
[D][05:19:16][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:16][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:16][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:52:51:646 ==>> $GBGGA,125251.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,40,1*4E


2025-07-31 20:52:51:706 ==>> 

$GBGSV,7,2,27,25,,,40,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,41,,,38,42,,,38,40,,,38,1,,,38,1*41

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,31,16,,,38,1*78

$GBRMC,125251.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125251.501,0.000,1532.359,1532.359,49.018,2097152,2097152,2097152*51



2025-07-31 20:52:51:962 ==>>                                          [D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 10, ret: 6
[D][05:19:17][CAT1]sub id: 10, ret: 6

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:17][M2M ]m2m gsm shut done, ret[0]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:52:52:082 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:52:094 ==>> 定位已等待【25】秒.
2025-07-31 20:52:52:157 ==>> [D][05:19:17][CAT1]pdpdeact urc len[22]


2025-07-31 20:52:52:724 ==>> $GBGGA,125252.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,41,,,38,42,,,38,40,,,38,1,,,38,1*41

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125252.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125252.501,0.000,1533.950,1533.950,49.065,2097152,2097152,2097152*58



2025-07-31 20:52:53:085 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:53:095 ==>> 定位已等待【26】秒.
2025-07-31 20:52:53:437 ==>> [D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:18][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:18][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:52:53:926 ==>> [D][05:19:19][CAT1]opened : 0, 0
[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:19][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]index:2 1629955159
[D][05:19:19][PROT]is_send:0
[D][05:19:19][PROT]sequence_num:6
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:1
[D][05:19:19][PROT]send_path:0x2
[D][05:19:19][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980221
[D][05:19:19][PROT]sending traceid [9999999999900007]
[D][05:19:19][PROT]Send_TO_M2M [1629955159]
$GBGGA,125253.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,41,1*4F

$GBGSV,7,2,27,25,,,41,60,,,40,14,,,40,39,,,39,1*77

$GBGSV,7,3,27,41,,,39,42,,,39,40,,,38,1,

2025-07-31 20:52:54:031 ==>> ,,38,1*41

$GBGSV,7,4,27,7,,,36,2,,,36,9,,,36,13,,,36,1*4E

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:19][SAL ]sock send credit cnt[6]
[D][05:19:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[134]
[D][05:19:19][SAL ]Cellular task submsg id[10]
[D][05:19:19][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd8] format[0]
$GBRMC,125253.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125253.501,0.000,1540.334,1540.334,49.275,2097152,2097152,2097152*5A

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:19][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BEF9D9ECA0AE46333440F93613CB652766F0E5A14CB4D46148E7611BD02A382E8B1DCF45589E92EEDE5ED456B5745B7346CED2
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellu

2025-07-31 20:52:54:076 ==>> lar task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]
[D][05:19:19][COMM]read battery soc:255


2025-07-31 20:52:54:092 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:54:103 ==>> 定位已等待【27】秒.
2025-07-31 20:52:54:704 ==>> $GBGGA,125254.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,41,1*4F

$GBGSV,7,2,27,25,,,41,60,,,40,14,,,40,39,,,39,1*77

$GBGSV,7,3,27,41,,,39,42,,,39,40,,,39,1,,,38,1*40

$GBGSV,7,4,27,7,,,37,2,,,36,9,,,36,13,,,36,1*4F

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,38,1*7B

$GBRMC,125254.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125254.501,0.000,1543.524,1543.524,49.377,2097152,2097152,2097152*5E



2025-07-31 20:52:55:097 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:55:107 ==>> 定位已等待【28】秒.
2025-07-31 20:52:55:704 ==>> $GBGGA,125255.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,59,,,41,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,7,3,27,41,,,39,42,,,39,40,,,39,1,,,38,1*40

$GBGSV,7,4,27,7,,,37,2,,,36,9,,,36,13,,,36,1*4F

$GBGSV,7,5,27,6,,,36,44,,,36,38,,,35,34,,,35,1*4B

$GBGSV,7,6,27,4,,,34,10,,,34,5,,,34,8,,,34,1*4A

$GBGSV,7,7,27,26,,,33,23,,,32,16,,,,1*70

$GBRMC,125255.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125255.501,0.000,1540.330,1540.330,49.271,2097152,2097152,2097152*58



2025-07-31 20:52:55:764 ==>>                                 soc:255


2025-07-31 20:52:56:112 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:56:123 ==>> 定位已等待【29】秒.
2025-07-31 20:52:56:703 ==>> $GBGGA,125256.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,59,,,41,24,,,41,1*4E

$GBGSV,7,2,26,25,,,41,60,,,41,14,,,40,39,,,39,1*77

$GBGSV,7,3,26,41,,,39,42,,,39,40,,,39,1,,,38,1*41

$GBGSV,7,4,26,7,,,37,9,,,37,2,,,36,13,,,36,1*4F

$GBGSV,7,5,26,6,,,36,44,,,36,38,,,35,34,,,35,1*4A

$GBGSV,7,6,26,4,,,34,10,,,34,5,,,34,8,,,34,1*4B

$GBGSV,7,7,26,26,,,33,23,,,33,1*77

$GBRMC,125256.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125256.501,0.000,1548.305,1548.305,49.528,2097152,2097152,2097152*50



2025-07-31 20:52:57:116 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:57:130 ==>> 定位已等待【30】秒.
2025-07-31 20:52:58:121 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:52:58:132 ==>> 定位已等待【31】秒.
2025-07-31 20:52:58:409 ==>> $GBGGA,125257.508,2301.2572756,N,11421.9421264,E,1,13,0.76,76.819,M,-1.770,M,,*54

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,259,42,3,61,190,41,14,60,189,40,16,55,28,37,1*75

$GBGSV,7,2,27,6,54,40,36,59,52,129,40,39,52,11,39,1,48,126,38,1*71

$GBGSV,7,3,27,24,46,20,41,2,46,238,36,7,44,191,36,25,44,292,40,1*49

$GBGSV,7,4,27,60,41,238,40,40,39,160,39,9,38,321,36,13,37,218,36,1*42

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,32,112,34,41,28,317,39,1*4D

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBRMC,125257.508,A,2301.2572756,N,11421.9421264,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:23][GNSS]HD8040 GPS
[D][05:19:23][GNSS]GPS diff_sec 124011214, report 0x42 frame
$GBGST,125257.508,1.634,0.416,0.407,0.589,2.379,3.151,7.764*71

[D][05:19:23][COMM]read battery soc:255
[D][05:19:23][COMM]Main Task receive event:131
[D][05:19:23][COMM]index:0,power_mode:0xFF
[D][05:19:23][COMM]index:1,sound_mode:0xFF
[D][05:19:23][COMM]index:2,gsensor_mode:0xFF
[D][05:19:23][COMM]index:3,report_freq_mode:0xFF
[D][05:19:23][COMM]index:4,report_period:0xFF
[D][05:19:23][COMM]i

2025-07-31 20:52:58:514 ==>> ndex:5,normal_reset_mode:0xFF
[D][05:19:23][COMM]index:6,normal_reset_period:0xFF
[D][05:19:23][COMM]index:7,spock_over_speed:0xFF
[D][05:19:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:23][COMM]index:16,imu_config_params:0xFF
[D][05:19:23][COMM]index:17,long_connect_params:0xFF
[D][05:19:23][COMM]index:18,detain_mark:0xFF
[D][05:19:23][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:23][COMM]index:21,mc_mode:0xFF
[D][05:19:23][COMM]index:22,S_mode:0xFF
[D][05:19:23][COMM]index:23,overweight:0xFF
[D][05:19:23][COMM]index:24,standstill_mode:0xFF
[D][05:19:23][COMM]index:25,night_mode:0xFF
[D][05:19:23][COMM]index:26,experiment1:0xFF
[D][05:19:23][COMM]index:27,experiment2:0xFF
[D][05:19:23][COMM]index:28,experiment3:0xFF
[D][05:19:23]

2025-07-31 20:52:58:619 ==>> [COMM]index:29,experiment4:0xFF
[D][05:19:23][COMM]index:30,night_mode_start:0xFF
[D][05:19:23][COMM]index:31,night_mode_end:0xFF
[D][05:19:23][COMM]index:33,park_report_minutes:0xFF
[D][05:19:23][COMM]index:34,park_report_mode:0xFF
[D][05:19:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:23][COMM]index:38,charge_battery_para: FF
[D][05:19:23][COMM]index:39,multirider_mode:0xFF
[D][05:19:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:23][COMM]index:44,riding_duration_config:0xFF
[D][05:19:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:23][COMM]index:49,mc_load_startup:0xFF
[D][05:19:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:23][COMM]index:52,traffic_mode:0xFF
[D][05:19:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:23][COMM]index:54,traffic_security_model_cycle

2025-07-31 20:52:58:725 ==>> :0xFF
[D][05:19:23][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:23][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:23][COMM]index:63,experiment5:0xFF
[D][05:19:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:23][COMM]index:72,experiment6:0xFF
[D][05:19:23][COMM]index:73,experiment7:0xFF
[D][05:19:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:23][COMM]index:75,zero_value_from_server:-1
[D][05:19:23][COMM]index:76,multirider_threshold:255
[D][05:19:23][COMM]index:77,experiment8:255
[D][05:19:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:23][COMM]index:79,temp_park_

2025-07-31 20:52:58:829 ==>> tail_light_twinkle_duration:255
[D][05:19:23][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:23][COMM]index:83,loc_report_interval:255
[D][05:19:23][COMM]index:84,multirider_threshold_p2:255
[D][05:19:23][COMM]index:85,multirider_strategy:255
[D][05:19:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:23][COMM]index:90,weight_param:0xFF
[D][05:19:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:23][COMM]index:95,current_limit:0xFF
[D][05:19:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:23][COMM]index:100,location_mode:0xFF

[W][05:19:23][PROT]remove success[1629955163],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:23][COMM]Main Task receive event:131 finished processing
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,125258.008,2301.2574800,N,1142

2025-07-31 20:52:58:934 ==>> 1.9422336,E,1,13,0.76,75.788,M,-1.770,M,,*55

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,259,42,3,61,190,41,14,60,189,40,16,55,28,37,1*75

$GBGSV,7,2,27,6,54,40,36,59,52,129,40,39,52,11,39,1,48,126,38,1*71

$GBGSV,7,3,27,24,46,20,41,2,46,238,36,7,44,191,36,25,44,292,40,1*49

$GBGSV,7,4,27,60,41,238,40,40,39,160,39,9,38,321,36,13,37,218,36,1*42

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,32,112,34,41,28,317,39,1*4D

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBGSV,2,1,06,33,69,259,41,39,52,11,39,24,46,20,40,25,44,292,39,5*72

$GBGSV,2,2,06,40,39,160,37,42,35,166,35,5*7E

$GBRMC,125258.008,A,2301.2574800,N,11421.9422336,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125258.008,1.556,0.827,0.808,1.154,1.683,2.063,5.232*72



2025-07-31 20:52:59:039 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:52:59:129 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 20:52:59:136 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【41】
北斗星号:【14】,信号值:【40】
北斗星号:【16】,信号值:【37】
北斗星号:【6】,信号值:【36】
北斗星号:【59】,信号值:【40】
北斗星号:【39】,信号值:【39】
北斗星号:【1】,信号值:【38】
北斗星号:【24】,信号值:【41】
北斗星号:【2】,信号值:【36】
北斗星号:【7】,信号值:【36】
北斗星号:【25】,信号值:【40】
北斗星号:【60】,信号值:【40】
北斗星号:【40】,信号值:【39】
北斗星号:【9】,信号值:【36】
北斗星号:【13】,信号值:【36】
北斗星号:【42】,信号值:【38】
北斗星号:【41】,信号值:【39】
北斗星号:【38】,信号值:【35】
北斗星号:【44】,信号值:【36】
北斗星号:【34】,信号值:【35】

2025-07-31 20:52:59:149 ==>> 检测【CSQ强度】
2025-07-31 20:52:59:156 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:52:59:405 ==>> $GBGGA,125259.000,2301.2577995,N,11421.9420946,E,1,13,0.76,75.992,M,-1.770,M,,*58

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,259,41,3,61,190,41,14,60,189,40,16,55,28,37,1*76

$GBGSV,7,2,27,6,54,40,36,59,52,129,40,39,52,11,39,1,48,126,38,1*71

$GBGSV,7,3,27,24,46,20,40,2,46,238,36,7,44,191,36,25,44,292,40,1*48

$GBGSV,7,4,27,60,41,238,40,40,39,160,38,9,38,321,36,13,37,218,36,1*43

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,32,112,34,41,28,317,38,1*4C

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,35,1*44

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBGSV,3,1,11,33,69,259,42,39,52,11,40,24,46,20,40,25,44,292,40,5*76

$GBGSV,3,2,11,40,39,160,37,42,35,166,37,41,28,317,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,94,34,34,17,148,30,23,6,257,30,5*4A

$GBRMC,125259.000,A,2301.2577995,N,11421.9420946,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125259.000,2.723,0.194,0.191,0.279,2.173,2.406,4.683*7E

[W][05:19:24][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:52:59:786 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 20:53:00:384 ==>> $GBGGA,125300.000,2301.2579770,N,11421.9418388,E,1,13,0.76,75.957,M,-1.770,M,,*54

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,258,42,3,61,190,41,14,60,189,40,16,55,28,37,1*74

$GBGSV,7,2,27,6,54,40,36,59,52,129,40,39,52,11,38,1,48,126,38,1*70

$GBGSV,7,3,27,24,46,20,40,2,46,238,36,7,44,191,36,25,44,292,40,1*48

$GBGSV,7,4,27,60,41,238,40,40,39,160,38,9,38,321,36,13,37,218,36,1*43

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,32,112,34,41,28,317,38,1*4C

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,35,1*44

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,40,5*77

$GBGSV,3,2,11,40,39,160,38,42,35,166,38,41,28,317,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125300.000,A,2301.2579770,N,11421.9418388,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125300.000,2.733,0.213,0.210,0.304,2.096,2.276,4.152*7F



2025-07-31 20:53:01:209 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:53:01:377 ==>> $GBGGA,125301.000,2301.2580674,N,11421.9416936,E,1,13,0.76,76.061,M,-1.770,M,,*58

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,258,41,3,61,190,41,14,60,189,40,16,55,28,37,1*77

$GBGSV,7,2,27,6,54,40,35,59,52,129,40,39,52,11,38,1,48,126,38,1*73

$GBGSV,7,3,27,24,46,20,40,2,46,238,36,7,44,191,36,25,44,292,40,1*48

$GBGSV,7,4,27,60,41,238,40,40,39,160,38,9,38,321,36,13,37,218,36,1*43

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,32,112,34,41,28,317,38,1*4C

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,35,1*44

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,38,41,28,317,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125301.000,A,2301.2580674,N,11421.9416936,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,125301.000,2.993,0.194,0.192,0.279,2.200,2.339,3.934*7C

                       

2025-07-31 20:53:01:407 ==>>                               

2025-07-31 20:53:01:789 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 20:53:02:388 ==>> $GBGGA,125302.000,2301.2581463,N,11421.9416756,E,1,13,0.76,76.156,M,-1.770,M,,*53

$GBGSA,A,3,33,14,39,24,25,40,13,42,41,38,44,34,1.46,0.76,1.24,4*0A

$GBGSA,A,3,23,,,,,,,,,,,,1.46,0.76,1.24,4*0C

$GBGSV,7,1,27,33,69,258,42,3,61,190,41,14,60,189,40,16,55,28,37,1*74

$GBGSV,7,2,27,6,54,40,36,59,52,129,41,39,52,11,39,1,48,126,38,1*70

$GBGSV,7,3,27,24,46,20,41,2,46,238,36,7,44,191,37,25,44,292,40,1*48

$GBGSV,7,4,27,60,41,238,40,40,39,160,38,9,38,321,36,13,37,218,36,1*43

$GBGSV,7,5,27,42,35,166,39,10,33,189,34,4,32,112,34,41,28,317,39,1*4C

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,33,1*73

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,317,37,38,24,192,34,5*74

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125302.000,A,2301.2581463,N,11421.9416756,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125302.000,3.111,0.176,0.175,0.252,2.241,2.355,3.780*7E



2025-07-31 20:53:03:298 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:53:03:404 ==>> $GBGGA,125303.000,2301.2581716,N,11421.9416833,E,1,19,0.67,76.325,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,39,59,02,24,01,25,60,40,13,1.28,0.67,1.09,4*08

$GBGSA,A,3,42,04,41,38,44,34,23,,,,,,1.28,0.67,1.09,4*00

$GBGSV,7,1,27,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,38,1*78

$GBGSV,7,2,27,6,54,40,36,39,52,11,39,59,50,128,41,2,48,239,36,1*73

$GBGSV,7,3,27,24,46,20,41,1,46,125,39,7,44,191,37,25,44,292,41,1*4A

$GBGSV,7,4,27,60,43,241,40,40,39,160,39,9,38,321,36,13,37,218,36,1*4E

$GBGSV,7,5,27,42,35,166,39,10,33,189,34,4,31,113,34,41,28,317,39,1*4E

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,317,37,38,24,192,34,5*74

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125303.000,A,2301.2581716,N,11421.9416833,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125303.000,2.782,0.185,0.190,0.269,2.036,2.138,3.477*7D



2025-07-31 20:53:03:886 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E4

2025-07-31 20:53:03:991 ==>>  loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100018. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. 

2025-07-31 20:53:04:096 ==>> cur_tick:100019. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100019. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100020. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100020. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100020. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100021. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100021. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100022
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100022
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100022
[W][05:19:29][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:29][COMM]read battery soc:255


2025-07-31 20:53:04:381 ==>> $GBGGA,125304.000,2301.2581895,N,11421.9416734,E,1,22,0.62,76.376,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,01,25,60,1.24,0.62,1.08,4*09

$GBGSA,A,3,07,40,13,42,04,41,38,44,34,23,,,1.24,0.62,1.08,4*09

$GBGSV,7,1,27,33,69,258,43,3,62,190,41,14,60,189,40,16,55,28,38,1*79

$GBGSV,7,2,27,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*41

$GBGSV,7,3,27,9,47,325,36,24,46,20,41,1,46,125,38,25,44,292,41,1*4A

$GBGSV,7,4,27,60,43,241,40,7,42,177,37,40,39,160,39,13,37,218,36,1*4D

$GBGSV,7,5,27,42,35,166,39,10,33,189,34,4,31,113,34,41,28,317,39,1*4E

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,317,37,38,24,192,34,5*74

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125304.000,A,2301.2581895,N,11421.9416734,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125304.000,2.582,0.176,0.176,0.255,1.903,1.996,3.261*71



2025-07-31 20:53:05:365 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:53:05:380 ==>> $GBGGA,125305.000,2301.2582152,N,11421.9416610,E,1,23,0.61,76.420,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,01,25,60,1.24,0.61,1.08,4*0A

$GBGSA,A,3,07,40,13,42,10,04,41,38,44,34,23,,1.24,0.61,1.08,4*0B

$GBGSV,7,1,27,33,69,258,43,3,62,190,41,14,60,189,40,16,55,28,37,1*76

$GBGSV,7,2,27,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*41

$GBGSV,7,3,27,9,47,325,36,24,46,20,41,1,46,125,39,25,44,292,41,1*4B

$GBGSV,7,4,27,60,43,241,40,7,42,177,36,40,39,160,39,13,37,218,36,1*4C

$GBGSV,7,5,27,42,35,166,39,10,33,189,34,4,31,113,34,41,28,317,39,1*4E

$GBGSV,7,6,27,38,24,192,35,5,22,257,34,8,18,203,34,44,18,94,36,1*47

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,317,37,38,24,192,34,5*74

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125305.000,A,2301.2582152,N,11421.9416610,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125305.000,2.558,0.237,0.236,0.344,1.877,1.960,3.149*75



2025-07-31 20:53:05:546 ==>> [W][05:19:31][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:53:05:806 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 20:53:06:405 ==>> $GBGGA,125306.000,2301.2582296,N,11421.9416352,E,1,24,0.59,76.426,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,01,25,60,1.21,0.59,1.06,4*0A

$GBGSA,A,3,07,40,13,42,10,04,41,38,05,44,34,23,1.21,0.59,1.06,4*0E

$GBGSV,7,1,27,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*77

$GBGSV,7,2,27,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*41

$GBGSV,7,3,27,9,47,325,36,24,46,20,41,1,46,125,38,25,44,292,40,1*4B

$GBGSV,7,4,27,60,43,241,40,7,42,177,36,40,39,160,39,13,37,218,36,1*4C

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,31,113,34,41,28,316,39,1*4E

$GBGSV,7,6,27,38,24,192,35,5,24,258,34,8,18,203,34,44,18,94,36,1*4E

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*76

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,316,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125306.000,A,2301.2582296,N,11421.9416352,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125306.000,2.461,0.201,0.202,0.295,1.809,1.885,3.017*7B



2025-07-31 20:53:06:647 ==>> [D][05:19:32][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:53:07:396 ==>> $GBGGA,125307.000,2301.2582314,N,11421.9416309,E,1,25,0.54,76.487,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,01,25,60,1.08,0.54,0.94,4*06

$GBGSA,A,3,07,40,13,42,10,04,41,38,05,44,34,26,1.08,0.54,0.94,4*07

$GBGSA,A,3,23,,,,,,,,,,,,1.08,0.54,0.94,4*0C

$GBGSV,7,1,27,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*77

$GBGSV,7,2,27,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*41

$GBGSV,7,3,27,9,47,325,36,24,46,20,40,1,46,125,38,25,44,292,40,1*4A

$GBGSV,7,4,27,60,43,241,40,7,42,177,36,40,39,160,39,13,37,218,37,1*4D

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,31,113,34,41,28,316,38,1*4F

$GBGSV,7,6,27,38,24,192,35,5,24,258,34,8,18,203,34,44,18,94,36,1*4E

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,11,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,40,5*77

$GBGSV,3,2,11,40,39,160,38,42,35,166,39,41,28,316,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,94,34,34,17,148,31,23,6,257,31,5*4A

$GBRMC,125307.000,A,2301.2582314,N,11421.9416309,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125307.000,2.523,0.209,0.210,0.306,1.840,1.908,2.978*75



2025-07-31 20:53:07:441 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:53:07:654 ==>> [D][05:19:33][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:19:33][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:53:07:805 ==>> [D][05:19:33][COMM]read battery soc:255


2025-07-31 20:53:08:402 ==>> $GBGGA,125308.000,2301.2582368,N,11421.9416231,E,1,25,0.54,76.427,M,-1.770,M,,*54

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,01,25,60,1.08,0.54,0.94,4*06

$GBGSA,A,3,07,40,13,42,10,04,41,38,05,44,34,26,1.08,0.54,0.94,4*07

$GBGSA,A,3,23,,,,,,,,,,,,1.08,0.54,0.94,4*0C

$GBGSV,7,1,27,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*77

$GBGSV,7,2,27,6,52,347,36,39,52,11,39,59,50,128,40,2,48,239,36,1*40

$GBGSV,7,3,27,9,47,325,36,24,46,20,40,1,46,125,38,25,44,292,40,1*4A

$GBGSV,7,4,27,60,43,241,40,7,42,177,36,40,39,160,39,13,37,218,36,1*4C

$GBGSV,7,5,27,42,35,166,38,10,33,189,34,4,31,113,34,41,28,316,38,1*4F

$GBGSV,7,6,27,38,24,192,35,5,24,258,34,8,18,203,34,44,18,94,36,1*4E

$GBGSV,7,7,27,34,17,148,35,26,9,55,33,23,6,257,34,1*74

$GBGSV,3,1,12,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,40,5*74

$GBGSV,3,2,12,40,39,160,38,42,35,166,39,41,28,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,94,34,34,17,148,31,26,9,55,29,23,6,257,31,5*7F

$GBRMC,125308.000,A,2301.2582368,N,11421.9416231,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125308.000,2.581,0.201,0.202,0.289,1.871,1.932,2.946*79



2025-07-31 20:53:09:181 ==>> [D][05:19:34][CAT1]exec over: func id: 15, ret: -93
[D][05:19:34][CAT1]sub id: 15, ret: -93

[D][05:19:34][SAL ]Cellular task submsg id[68]
[D][05:19:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:34][SAL ]socket send fail. id[4]
[D][05:19:34][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:34][M2M ]m2m select fd[4]
[D][05:19:34][M2M ]socket[4] Link is disconnected
[D][05:19:34][M2M ]tcpclient close[4]
[D][05:19:34][SAL ]socket[4] has closed
[D][05:19:34][PROT]protocol read data ok
[E][05:19:34][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:34][PROT]M2M Send Fail [1629955174]
[D][05:19:34][PROT]CLEAN,SEND:0
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:34][PROT]CLEAN:0
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:34][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 21
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:34][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 21
[D][05:19:34][CAT1]gsm read 

2025-07-31 20:53:09:271 ==>> msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:34][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 21
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:34][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 21
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:34][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 21
[D][05:19:34][CAT1]gsm read msg sub id: 10
[D][05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:34][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:34][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:53:09:352 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 20:53:09:382 ==>> 检测【关闭GSM联网】
2025-07-31 20:53:09:398 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:53:09:422 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:53:09:434 ==>>                                                                                                                                                                                                                              2.839*7F



2025-07-31 20:53:09:526 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:53:09:586 ==>>                                                                           [9999] type[1]
[D][05:19:34][CAT1]gsm read msg sub id: 8
[D][05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:34][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:34][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:19:35][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:35][COMM]GSM test
[D][05:19:35][COMM]GSM test disable


2025-07-31 20:53:09:642 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:53:09:650 ==>> 检测【4G联网测试】
2025-07-31 20:53:09:662 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:53:10:463 ==>> [D][05:19:35][CAT1]pdpdeact urc len[22]
[D][05:19:35][COMM]read battery soc:255
[W][05:19:35][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:35][COMM]Main Task receive event:14
[D][05:19:35][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955175, allstateRepSeconds = 0
[D][05:19:35][COMM]index:0,power_mode:0xFF
[D][05:19:35][COMM]index:1,sound_mode:0xFF
[D][05:19:35][COMM]index:2,gsensor_mode:0xFF
[D][05:19:35][COMM]index:3,report_freq_mode:0xFF
[D][05:19:35][COMM]index:4,report_period:0xFF
[D][05:19:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:35][COMM]index:6,normal_reset_period:0xFF
[D][05:19:35][COMM]index:7,spock_over_speed:0xFF
[D][05:19:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:35][COMM]index:16,imu_config_params:0xFF
[D][05:19:35][COMM]index:17,long_connect_params:0xFF
[D][05:19:35][COMM]

2025-07-31 20:53:10:568 ==>> index:18,detain_mark:0xFF
[D][05:19:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:35][COMM]index:21,mc_mode:0xFF
[D][05:19:35][COMM]index:22,S_mode:0xFF
[D][05:19:35][COMM]index:23,overweight:0xFF
[D][05:19:35][COMM]index:24,standstill_mode:0xFF
[D][05:19:35][COMM]index:25,night_mode:0xFF
[D][05:19:35][COMM]index:26,experiment1:0xFF
[D][05:19:35][COMM]index:27,experiment2:0xFF
[D][05:19:35][COMM]index:28,experiment3:0xFF
[D][05:19:35][COMM]index:29,experiment4:0xFF
[D][05:19:35][COMM]index:30,night_mode_start:0xFF
[D][05:19:35][COMM]index:31,night_mode_end:0xFF
[D][05:19:35][COMM]index:33,park_report_minutes:0xFF
[D][05:19:35][COMM]index:34,park_report_mode:0xFF
[D][05:19:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:35][COMM]index:38,charge_battery_para: FF
[D][05:19:35][COMM]index:39,multirider_mode:0xFF
[D][05:19:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:35][COMM]index:44,riding_duration_config:0xFF
[D][05:19

2025-07-31 20:53:10:673 ==>> :35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:35][COMM]index:49,mc_load_startup:0xFF
[D][05:19:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:35][COMM]index:52,traffic_mode:0xFF
[D][05:19:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:35][COMM]index:63,experiment5:0xFF
[D][05:19:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:35][COMM]index:68,camera_park_ps_cfg:0xFFFF


2025-07-31 20:53:10:778 ==>> 
[D][05:19:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:35][COMM]index:72,experiment6:0xFF
[D][05:19:35][COMM]index:73,experiment7:0xFF
[D][05:19:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:35][COMM]index:75,zero_value_from_server:-1
[D][05:19:35][COMM]index:76,multirider_threshold:255
[D][05:19:35][COMM]index:77,experiment8:255
[D][05:19:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:35][COMM]index:83,loc_report_interval:255
[D][05:19:35][COMM]index:84,multirider_threshold_p2:255
[D][05:19:35][COMM]index:85,multirider_strategy:255
[D][05:19:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:35][COMM]index:90,weight_param:0xFF
[D][05:19:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:35][COMM]index:95,current_limit:0xFF
[D][05:19:35][COMM]index:97,pan

2025-07-31 20:53:10:883 ==>> el display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:35][COMM]index:100,location_mode:0xFF

[W][05:19:35][PROT]remove success[1629955175],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:35][PROT]add success [1629955175],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:35][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,125310.000,2301.2582704,N,11421.9416054,E,1,24,0.55,76.338,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,25,60,07,1.10,0.55,0.95,4*09

$GBGSA,A,3,40,13,42,10,04,41,38,05,44,34,26,23,1.10,0.55,0.95,4*08

$GBGSV,7,1,26,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*76

$GBGSV,7,2,26,6,52,347,36,39,52,11,39,59,50,128,40,2,48,239,36,1*41

$GBGSV,7,3,26,9,47,325,36,24,46,20,40,25,44,292,40,60,43,241,41,1*76

$GBGSV,7,4,26,7,42,177,36,40,39,160,39,13,37,218,37,42,35,166,38,1*44

$GBGSV,7,5,26,10,33,189,34,4,31,113,34,41,28,316,38,38,24,192,35,1*45

$GBGSV,7,6,26,5,24,258,33,8,18,203,34,44,18,94,36,34,17,148,35,1*43

$GBGSV,7,7,26,26,9,55,33,23,6,257,34,1*4F

$GBGSV,3,1,12,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*75

$GBGSV,3,2,12,40,39,160

2025-07-31 20:53:10:928 ==>> ,38,42,35,166,39,41,28,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,94,34,34,17,148,31,26,9,55,30,23,6,257,31,5*77

$GBRMC,125310.000,A,2301.2582704,N,11421.9416054,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125310.000,2.704,0.164,0.163,0.234,1.937,1.989,2.914*79



2025-07-31 20:53:11:123 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                        [D][05:19:36][CAT1]<<< 
OK

[D][05:19:36][CAT1]exec over: func id: 8, ret: 6
[D][05:19:36][CAT1]gsm read msg sub id: 13
[D][05:19:36][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:36][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:36][CAT1]exec over: func id: 13, ret: 21
[D][05:19:36][M2M ]get csq[25]


2025-07-31 20:53:11:638 ==>> $GBGGA,125311.000,2301.2582922,N,11421.9416128,E,1,24,0.55,76.285,M,-1.770,M,,*5D

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,25,60,07,1.10,0.55,0.95,4*09

$GBGSA,A,3,40,13,42,10,04,41,38,05,44,34,26,23,1.10,0.55,0.95,4*08

$GBGSV,7,1,26,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*76

$GBGSV,7,2,26,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*40

$GBGSV,7,3,26,9,47,325,36,24,46,20,41,25,44,292,40,60,43,241,41,1*77

$GBGSV,7,4,26,7,42,177,36,40,39,160,39,13,37,218,36,42,35,166,38,1*45

$GBGSV,7,5,26,10,33,189,34,4,31,113,34,41,28,316,38,38,24,192,35,1*45

$GBGSV,7,6,26,5,24,258,34,8,18,203,34,44,18,94,36,34,17,148,35,1*44

$GBGSV,7,7,26,26,9,55,33,23,6,257,34,1*4F

$GBGSV,3,1,12,33,69,258,43,39,52,11,40,24,46,20,41,25,44,292,41,5*75

$GBGSV,3,2,12,40,39,160,38,42,35,166,39,41,28,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,94,34,34,17,148,31,26,9,55,30,23,6,257,31,5*77

$GBRMC,125311.000,A,2301.2582922,N,11421.9416128,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,125311.000,2.799,0.201,0.200,0.286,1.990,2.037,2.927*71

[D][05:19:36][CAT1]opened : 0, 0
[D][05:19:36][SAL ]Cellular task submsg 

2025-07-31 20:53:11:743 ==>> id[68]
[D][05:19:36][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:36][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:36][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:36][M2M ]g_m2m_is_idle become true
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:36][PROT]index:0 1629955176
[D][05:19:36][PROT]is_send:0
[D][05:19:36][PROT]sequence_num:8
[D][05:19:36][PROT]retry_timeout:0
[D][05:19:36][PROT]retry_times:1
[D][05:19:36][PROT]send_path:0x2
[D][05:19:36][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:36][PROT]===========================================================
[W][05:19:36][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955176]
[D][05:19:36][PROT]===========================================================
[D][05:19:36][PROT]sending traceid [9999999999900009]
[D][05:19:36][PROT]Send_TO_M2M [1629955176]
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:36][SAL ]sock send credit cnt[6]
[D][05:19:36][SAL ]sock send ind credit cnt[6]
[D][05:19:36][M2M ]m2m send data len[294]
[D][05:19:36][SAL ]Cellular task submsg id[10]
[D][05:19:36][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd8] fo

2025-07-31 20:53:11:848 ==>> rmat[0]
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:36][CAT1]gsm read msg sub id: 15
[D][05:19:36][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:36][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88B18F66F2D9F5BDC7209650C1F5CDD7475976CA1FA06C645D4BB1B66B2D7F1F884C9AA0AB088F3BCDE3B719ACA07628D058D2DE44B6D19477AEDF7502B2D8BDD649F8AE542487BE0C5CE29C23DC807253B8C70F2D7EEE68E67153D6E49F045FE480405C8BD3D6699B88C45585A8ADEA57B2BE5BCF955B48D7882A734DA67992891B7E6F
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:36][PROT]remove success[1629955176],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:36][PROT]add success [1629955176],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:36][COMM]------>period, report file manifest
[D][05:19:36][COMM]Main Task receive event:14 finished processing
[D][05:19:36][CAT1]<<< 
SEND OK

[D][05:19:36][CAT1]exec over: func id: 15, ret: 11
[D][05:19:36][CAT1]sub id: 15, ret: 11

[D][05:19:36][SAL ]Cellular task submsg id[68]
[D][05:19:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:36][M2M ]m2m_tas

2025-07-31 20:53:11:923 ==>> k: gpc:[0],gpo:[1]
[D][05:19:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:36][CAT1]gsm read msg sub id: 21
[D][05:19:36][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:36][M2M ]g_m2m_is_idle become true
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:36][PROT]M2M Send ok [1629955176]
[D][05:19:36][CAT1]<<< 
OK

[D][05:19:36][CAT1]cell info report total[0]
[D][05:19:36][CAT1]exec over: func id: 21, ret: 6
                                         

2025-07-31 20:53:12:410 ==>> $GBGGA,125312.000,2301.2583140,N,11421.9416151,E,1,24,0.55,76.290,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,06,39,59,02,09,24,25,60,07,1.10,0.55,0.95,4*09

$GBGSA,A,3,40,13,42,10,04,41,38,05,44,34,26,23,1.10,0.55,0.95,4*08

$GBGSV,7,1,26,33,69,258,42,3,62,190,41,14,60,189,40,16,55,28,37,1*76

$GBGSV,7,2,26,6,52,347,36,39,52,11,39,59,50,128,41,2,48,239,36,1*40

$GBGSV,7,3,26,9,47,325,36,24,46,20,41,25,44,292,40,60,43,241,41,1*77

$GBGSV,7,4,26,7,42,177,36,40,39,160,39,13,37,218,36,42,35,166,39,1*44

$GBGSV,7,5,26,10,33,189,34,4,31,113,34,41,28,316,38,38,24,192,35,1*45

$GBGSV,7,6,26,5,24,258,34,8,18,203,34,44,18,94,36,34,17,148,35,1*44

$GBGSV,7,7,26,26,9,55,33,23,6,257,34,1*4F

$GBGSV,3,1,12,33,69,258,43,39,52,11,41,24,46,20,41,25,44,292,41,5*74

$GBGSV,3,2,12,40,39,160,38,42,35,166,39,41,28,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,94,34,34,17,148,31,26,9,55,30,23,6,257,31,5*77

$GBRMC,125312.000,A,2301.2583140,N,11421.9416151,E,0.004,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,125312.000,2.817,0.213,0.212,0.302,1.999,2.043,2.907*7E



2025-07-31 20:53:12:717 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:53:12:731 ==>> 检测【关闭GPS】
2025-07-31 20:53:12:750 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:53:13:125 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[D][05:19:38][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:38][GNSS]stop locating
[D][05:19:38][GNSS]stop event:8
[D][05:19:38][GNSS]GPS stop. ret=0
[D][05:19:38][GNSS]all continue location stop
[W][05:19:38][GNSS]stop locating
[D][05:19:38][GNSS]all sing location stop
[D][05:19:38][CAT1]gsm read msg sub id: 24
[D][05:19:38][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]exec over: func id: 24, ret: 6
[D][05:19:38][CAT1]sub id: 24, ret: 6



2025-07-31 20:53:13:258 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:53:13:267 ==>> 检测【清空消息队列2】
2025-07-31 20:53:13:282 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:53:13:448 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:38][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:53:13:542 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:53:13:569 ==>> 检测【轮动检测】
2025-07-31 20:53:13:586 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:53:13:658 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:53:13:763 ==>> [D][05:19:39][GNSS]recv submsg id[1]
[D][05:19:39][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:39][GNSS]location stop evt done evt
[D][05:19:39][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:53:13:838 ==>> [D][05:19:39][COMM]read battery soc:255


2025-07-31 20:53:14:048 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:53:14:168 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:53:14:322 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:53:14:338 ==>> 检测【关闭小电池】
2025-07-31 20:53:14:369 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:53:14:453 ==>> 6A A6 02 A6 6A 


2025-07-31 20:53:14:558 ==>> Battery OFF
OVER 150


2025-07-31 20:53:14:591 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:53:14:605 ==>> 检测【进入休眠模式】
2025-07-31 20:53:14:618 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:53:14:788 ==>> [W][05:19:40][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:40][COMM]Main Task receive event:28
[D][05:19:40][COMM]main task tmp_sleep_event = 8
[D][05:19:40][COMM]prepare to sleep
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:53:15:600 ==>> [D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 12, ret: 6
[D][05:19:40][M2M ]tcpclient close[4]
[D][05:19:40][SAL ]Cellular task submsg id[12]
[D][05:19:40][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:40][CAT1]gsm read msg sub id: 9
[D][05:19:40][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 9, ret: 6
[D][05:19:40][CAT1]sub id: 9, ret: 6

[D][05:19:40][SAL ]Cellular task submsg id[68]
[D][05:19:40][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:40][SAL ]socket close ind. id[4]
[D][05:19:40][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:40][COMM]1x1 frm_can_tp_send ok
[D][05:19:41][CAT1]pdpdeact urc len[22]


2025-07-31 20:53:15:890 ==>> [D][05:19:41][COMM]read battery soc:255
[E][05:19:41][COMM]1x1 rx timeout
[D][05:19:41][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:53:16:385 ==>> [E][05:19:41][COMM]1x1 rx timeout
[E][05:19:41][COMM]1x1 tp timeout
[E][05:19:41][COMM]1x1 error -3.
[W][05:19:41][COMM]CAN STOP!
[D][05:19:41][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:41][COMM]------------ready to Power off Acckey 1------------
[D][05:19:41][COMM]------------ready to Power off Acckey 2------------
[D][05:19:41][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:41][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1308
[D][05:19:41][COMM]bat sleep fail, reason:-1
[D][05:19:41][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:41][COMM]accel parse set 0
[D][05:19:41][COMM]imu rest ok. 112811
[D][05:19:41][COMM]imu sleep 0
[W][05:19:41][COMM]now sleep


2025-07-31 20:53:16:662 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:53:16:671 ==>> 检测【检测33V休眠电流】
2025-07-31 20:53:16:680 ==>> 开始33V电流采样
2025-07-31 20:53:16:705 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:53:16:763 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:53:17:774 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:53:17:864 ==>> Current33V:????:16.85

2025-07-31 20:53:18:277 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:53:18:285 ==>> 【检测33V休眠电流】通过,【16.85uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:53:18:297 ==>> 该项需要延时执行
2025-07-31 20:53:20:291 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:53:20:305 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:53:20:318 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:53:20:366 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 2021mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:53:21:318 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:53:21:327 ==>> 检测【打开小电池2】
2025-07-31 20:53:21:351 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:53:21:452 ==>> 6A A6 01 A6 6A 


2025-07-31 20:53:21:557 ==>> Battery ON
OVER 150


2025-07-31 20:53:21:608 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:53:21:616 ==>> 该项需要延时执行
2025-07-31 20:53:22:109 ==>> 此处延时了:【500】毫秒
2025-07-31 20:53:22:122 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:53:22:130 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:53:22:156 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:53:22:390 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:53:22:409 ==>> 该项需要延时执行
2025-07-31 20:53:22:459 ==>> [D][05:19:47][COMM]------------ready to Power on Acckey 1------------
[D][05:19:47][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:47][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:47][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:47][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 11
[D][05:19:47][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:47][COMM]----- get Acckey 1 and value:1------------
[W][05:19:47][COMM]CAN START!
[D][05:19:47][CAT1]gsm read msg sub id: 12
[D][05:19:47][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:47][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 118798
[D][05:19:47][COMM][Audio]exec status ready.
[D][05:19:47]

2025-07-31 20:53:22:519 ==>> [CAT1]<<< 
OK

[D][05:19:47][CAT1]exec over: func id: 12, ret: 6
[D][05:19:47][COMM]imu wakeup ok. 118813
[D][05:19:47][COMM]imu wakeup 1
[W][05:19:47][COMM]wake up system, wakeupEvt=0x80
[D][05:19:47][COMM]frm_can_weigth_power_set 1
[D][05:19:47][COMM]Clear Sleep Block Evt
[D][05:19:47][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:47][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:53:22:700 ==>> [E][05:19:48][COMM]1x1 rx timeout
[D][05:19:48][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:53:22:805 ==>>                                            :118783. cur_tick:119292. period:50
[D][05:19:48][COMM]msg 02A4 loss. last_tick:118783. cur_tick:119292. period:50
[D][05:19:48][COMM]msg 02A5 loss. last_tick:118783. cur_tick:119293. period:50
[D][05:19:48][COMM]msg 02A6 loss. last_tick:118783. cur_tick:119293. period:50
[D][05:19:48][COMM

2025-07-31 20:53:22:850 ==>> ]msg 02A7 loss. last_tick:118783. cur_tick:119293. period:50
[D][05:19:48][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 119294
[D][05:19:48][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 119294


2025-07-31 20:53:22:895 ==>> 此处延时了:【500】毫秒
2025-07-31 20:53:22:909 ==>> 检测【进入休眠模式2】
2025-07-31 20:53:22:931 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:53:23:046 ==>> [W][05:19:48][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:53:23:151 ==>>               COMM]1x1 rx timeout
[E][05:19:48][COMM]1x1 tp timeout
[E][05:19:48][COMM]1x1 error -3.
[D][05:19:48][COMM]Main Task receive event:28 finished processing
[D][05:19:48][COMM]Main Task receive event:28
[D][05:19:48][COMM]prepare to sleep
[D][05:19:48][CAT1]gsm read msg sub id: 12
[D][05:19:48][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:48][CAT1]<<< 
OK

[D][05:19:48][CAT1]exec over: func 

2025-07-31 20:53:23:181 ==>> id: 12, ret: 6
[D][05:19:48][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:48][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:53:23:501 ==>> [D][05:19:48][COMM]msg 0220 loss. last_tick:118783. cur_tick:119788. period:100
[D][05:19:48][COMM]msg 0221 loss. last_tick:118783. cur_tick:119789. period:100
[D][05:19:48][COMM]msg 0224 loss. last_tick:118783. cur_tick:119789. period:100
[D][05:19:48][COMM]msg 0260 loss. last_tick:118783. cur_tick:119790. period:100
[D][05:19:48][COMM]msg 0280 loss. last_tick:118783. cur_tick:119790. period:100
[D][05:19:48][COMM]msg 02C0 loss. last_tick:118783. cur_tick:119790. period:100
[D][05:19:48][COMM]msg 02C1 loss. last_tick:118783. cur_tick:119791. period:100
[D][05:19:48][COMM]msg 02C2 loss. last_tick:118783. cur_tick:119791. period:100
[D][05:19:48][COMM]msg 02E0 loss. last_tick:118783. cur_tick:119792. period:100
[D][05:19:48][COMM]msg 02E1 loss. last_tick:118783. cur_tick:119792. period:100
[D][05:19:48][COMM]msg 02E2 loss. last_tick:118783. cur_tick:119792. period:100
[D][05:19:48][COMM]msg 0300 loss. last_tick:118783. cur_tick:119793. period:100
[D][05:19:48][COMM]msg 0301 loss. last_tick:118783. cur_tick:119793. period:100
[D][05:19:48][COMM]bat msg 0240 loss. last_tick:118783. cur_tick:119793. period:100. j,i:1 54
[D][05:19:48][COMM]bat msg 0241 loss. last_tick:118783. cur_tick:1197

2025-07-31 20:53:23:606 ==>> 94. period:100. j,i:2 55
[D][05:19:48][COMM]bat msg 0242 loss. last_tick:118783. cur_tick:119794. period:100. j,i:3 56
[D][05:19:48][COMM]bat msg 0244 loss. last_tick:118783. cur_tick:119794. period:100. j,i:5 58
[D][05:19:48][COMM]bat msg 024E loss. last_tick:118783. cur_tick:119795. period:100. j,i:15 68
[D][05:19:48][COMM]bat msg 024F loss. last_tick:118783. cur_tick:119795. period:100. j,i:16 69
[D][05:19:48][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 119796
[D][05:19:48][COMM]CAN message bat fault change: 0x00000000->0x0001802E 119796
[D][05:19:48][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 119797
                                                                              

2025-07-31 20:53:23:850 ==>> [D][05:19:49][COMM]msg 0222 loss. last_tick:118783. cur_tick:120292. period:150
[D][05:19:49][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 120293
[D][05:19:49][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:49][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:49][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:53:24:060 ==>> [E][05:19:49][COMM]1x1 rx timeout
[D][05:19:49][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[E][05:19:49][COMM]1x1 tp timeout
[E][05:19:49][COMM]1x1 error -3.
[W][05:19:49][COMM]CAN STOP!
[D][05:19:49][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:49][COMM]------------ready to Power off Acckey 1------------
[D][05:19:49][COMM]------------ready to Power off Acckey 2------------
[D][05:19:49][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:49][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 102
[D][05:19:49][COMM]bat sleep fail, reason:-1
[D][05:19:49][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:49][COMM]accel parse set 0
[D][05:19:49][COMM]imu rest ok. 120484
[D][05:19:49][COMM]imu sleep 0
[W][05:19:49][COMM]now sleep


2025-07-31 20:53:24:211 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:53:24:219 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:53:24:233 ==>> 开始小电池电流采样
2025-07-31 20:53:24:262 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:53:24:320 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:53:25:322 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:53:25:399 ==>> CurrentBattery:ƽ��:67.86

2025-07-31 20:53:25:825 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:53:25:834 ==>> 【检测小电池休眠电流】通过,【67.86uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:53:25:845 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:53:25:873 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:53:25:961 ==>> 5A A5 01 5A A5 


2025-07-31 20:53:26:066 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:53:26:119 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:53:26:129 ==>> 该项需要延时执行
2025-07-31 20:53:26:307 ==>> [D][05:19:51][COMM]------------ready to Power on Acckey 1------------
[D][05:19:51][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:51][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:51][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:51][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:51][COMM]----- get Acckey 1 and value:1------------
[W][05:19:51][COMM]CAN START!
[D][05:19:51][CAT1]gsm read msg sub id: 12
[D][05:19:51][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:51][COMM]CAN message bat fault change: 0x0001802E->0x00000000 122672
[D][05:19:51][COMM][Audio]exec status ready.
[D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]exec over: func id: 12, ret: 6
[D][05:19:51][COMM]imu wakeup ok. 122686
[D][05:19:51][COMM]imu wakeup 1
[W][05:19:51][COMM]wake up system, wakeupEvt=0x80
[D][05:19:51][COMM]frm_can_weigth_power_set 1
[D][05:19:51][COMM]Clear Sleep Block Evt
[D][05:19:51][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:51][COMM]1x1 frm_can_tp_send ok
[D][05:19:51][COMM]read battery soc:0


2025-07-31 20:53:26:577 ==>> [D][05:19:52][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[E][05:19:52][COMM]1x1 rx timeout
[D][05:19:52][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:53:26:622 ==>> 此处延时了:【500】毫秒
2025-07-31 20:53:26:637 ==>> 检测【检测唤醒】
2025-07-31 20:53:26:658 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:53:26:682 ==>>                                                                         iod:50
[D][05:19:52][COMM]msg 02A4 loss. last_tick:122654. cur_tick:123166. period:50
[D][05:19:52][COMM]msg 02A5 loss. last_tick:122654. cur_tick:123167. period:50
[D][05:19:52][COMM]msg 02A6 loss. last_tick:122654. cur_tick:123167. p

2025-07-31 20:53:26:727 ==>> eriod:50
[D][05:19:52][COMM]msg 02A7 loss. last_tick:122654. cur_tick:123167. period:50
[D][05:19:52][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 123168
[D][05:19:52][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 123168


2025-07-31 20:53:27:426 ==>> [W][05:19:52][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:52][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:52][FCTY]==========Modules-nRF5340 ==========
[D][05:19:52][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:52][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:52][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:52][FCTY]DeviceID    = 460130071539205
[D][05:19:52][FCTY]HardwareID  = 867222087691244
[D][05:19:52][FCTY]MoBikeID    = 9999999999
[D][05:19:52][FCTY]LockID      = FFFFFFFFFF
[D][05:19:52][FCTY]BLEFWVersion= 105
[D][05:19:52][FCTY]BLEMacAddr   = E5DD8EBABA3E
[D][05:19:52][FCTY]Bat         = 3864 mv
[D][05:19:52][FCTY]Current     = 0 ma
[D][05:19:52][FCTY]VBUS        = 2600 mv
[D][05:19:52][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:52][FCTY]Ext battery vol = 32, adc = 1304
[D][05:19:52][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 177 mv
[D][05:19:52][FCTY]Bike Type flag is invalied
[D][05:19:52][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:52][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:52][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:52][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:52][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:52][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:52][FCTY]Bat1

2025-07-31 20:53:27:531 ==>>          = 3778 mv
[D][05:19:52][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:52][FCTY]==========Modules-nRF5340 ==========
[E][05:19:52][COMM]1x1 rx timeout
[E][05:19:52][COMM]1x1 tp timeout
[E][05:19:52][COMM]1x1 error -3.
[D][05:19:52][COMM]Main Task receive event:28 finished processing
[D][05:19:52][COMM]Main Task receive event:65
[D][05:19:52][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:52][COMM]Main Task receive event:65 finished processing
[D][05:19:52][COMM]Main Task receive event:60
[D][05:19:52][COMM]smart_helmet_vol=255,255
[D][05:19:52][COMM]report elecbike
[W][05:19:52][PROT]remove success[1629955192],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:52][PROT]add success [1629955192],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:52][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:52][PROT]index:0
[D][05:19:52][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:52][PROT]is_send:1
[D][05:19:52][PROT]sequence_num:10
[D][05:19:52][PROT]retry_timeout:0
[D][05:19:52][PROT]retry_times:3
[D][05:19:52][PROT]send_path:0x3

2025-07-31 20:53:27:636 ==>> 
[D][05:19:52][PROT]msg_type:0x5d03
[D][05:19:52][PROT]===========================================================
[W][05:19:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955192]
[D][05:19:52][PROT]===========================================================
[D][05:19:52][PROT]Sending traceid[999999999990000B]
[D][05:19:52][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:52][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:52][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:52][COMM]Main Task receive event:60 finished processing
[D][05:19:52][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:52][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:52][SAL ]open socket ind id[4], rst[0]
[D][05:19:52][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:52][SAL ]Cellular task submsg id[8]
[D][05:19:52][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:52][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:52][CAT1]gsm read msg sub id: 8
[D][05:19:52][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:52][M2M ]m2m switch to: M2M_G

2025-07-31 20:53:27:671 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:53:27:683 ==>> 检测【关机】
2025-07-31 20:53:27:699 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:53:27:741 ==>> SM_SOCKET_OPEN_ACK
[D][05:19:52][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:52][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:52][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:52][CAT1]<<< 
+CME ERROR: 100

[D][05:19:52][COMM]msg 0220 loss. last_tick:122654. cur_tick:123662. period:100
[D][05:19:52][COMM]msg 0221 loss. last_tick:122654. cur_tick:123662. period:100
[D][05:19:52][COMM]msg 0224 loss. last_tick:122654. cur_tick:123663. period:100
[D][05:19:52][COMM]msg 0260 loss. last_tick:122654. cur_tick:123663. period:100
[D][05:19:52][COMM]msg 0280 loss. last_tick:122654. cur_tick:123664. period:100
[D][05:19:52][COMM]msg 02C0 loss. last_tick:122654. cur_tick:123664. period:100
[D][05:19:52][COMM]msg 02C1 loss. last_tick:122654. cur_tick:123664. period:100
[D][05:19:52][COMM]msg 02C2 loss. last_tick:122654. cur_tick:123665. period:100
[D][05:19:52][COMM]msg 02E0 loss. last_tick:122654. cur_tick:123665. period:100
[D][05:19:52][COMM]msg 02E1 loss. last_tick:122654. cur_tick:123666. period:100
[D][05:19:52][COMM]msg 02E2 loss. last_tick:122654. cur_tick:123666. period:100
[D][05:19:52][COMM]msg 0300 loss. last_tick:122654. cur_tick:123666. period:100
[D][05:19:52][COM

2025-07-31 20:53:27:846 ==>> M]msg 0301 loss. last_tick:122654. cur_tick:123666. period:100
[D][05:19:52][COMM]bat msg 0240 loss. last_tick:122654. cur_tick:123667. period:100. j,i:1 54
[D][05:19:52][COMM]bat msg 0241 loss. last_tick:122654. cur_tick:123667. period:100. j,i:2 55
[D][05:19:52][COMM]bat msg 0242 loss. last_tick:122654. cur_tick:123668. period:100. j,i:3 56
[D][05:19:52][COMM]bat msg 0244 loss. last_tick:122654. cur_tick:123668. period:100. j,i:5 58
[D][05:19:52][COMM]bat msg 024E loss. last_tick:122654. cur_tick:123668. period:100. j,i:15 68
[D][05:19:52][COMM]bat msg 024F loss. last_tick:122654. cur_tick:123669. period:100. j,i:16 69
[D][05:19:52][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 123669
[D][05:19:52][COMM]CAN message bat fault change: 0x00000000->0x0001802E 123670
[D][05:19:52][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 123670


2025-07-31 20:53:28:518 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:53:28:623 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               dio].l:[255]. success, file_name:B50, size:10800
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:53][COMM]read file, len:10800, num:3
[D][05:19:53][COMM]Main Task receive event:66 finished processing
[D][05:19:53][COMM]Main Task receive event:60
[D][05:19:53][COMM]smart_helmet_vol=255,255
[D][05:19:53][COMM]BAT CAN get state1 Fail 204
[D][05:19:53][COMM]BAT CAN get soc Fail, 204
[D]

2025-07-31 20:53:28:713 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:53:28:729 ==>> [05:19:53][COMM]BAT CAN get state2 fail 204
[D][05:19:53][COMM]get soh error
[E][05:19:53][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:53][COMM]report elecbike
[D][05:19:53][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[D][05:19:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:53][COMM]Receive Bat Lock cmd 0
[D][05:19:53][COMM]VBUS is 1
[W][05:19:53][PROT]remove success[1629955193],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:19:53][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:53][PROT]index:1
[D][05:19:53][PROT]is_send:1
[D][05:19:53][PROT]sequence_num:11
[D][05:19:53][PROT]retry_timeout:0
[D][05:19:53][PROT]retry_times:3
[D][05:19:53][PROT]send_path:0x3
[D][05:19:53][PROT]msg_type:0x5d03
[D][05:19:53][PROT]===========================================================
[W][05:19:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955193]
[D][05:19:53][PROT]===========================================================
[D][05:19:53][PROT]Sending traceid[999999999990000C]
[D][05:19:53][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[

2025-07-31 20:53:28:833 ==>> D][05:19:53][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:53][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:53][PROT]add success [1629955193],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:53][COMM]Main Task receive event:60 finished processing
[D][05:19:53][COMM]Main Task receive event:61
[D][05:19:53][COMM][D301]:type:3, trace id:280
[D][05:19:53][COMM]id[], hw[000
[D][05:19:53][COMM]get mcMaincircuitVolt error
[D][05:19:53][COMM]get mcSubcircuitVolt error
[D][05:19:53][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:53][COMM]BAT CAN get state1 Fail 204
[D][05:19:53][COMM]BAT CAN get soc Fail, 204
[D][05:19:53][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:53][COMM]BAT CAN get state2 fail 204
[D][05:19:53][COMM]get bat work mode err
[W][05:19:53][PROT]remove success[1629955193],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:19:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:

2025-07-31 20:53:28:938 ==>> 53][COMM]--->crc16:0xb8a
[D][05:19:53][COMM]read file success
[W][05:19:53][COMM][Audio].l:[936].close hexlog save
[D][05:19:53][COMM]accel parse set 1
[D][05:19:53][COMM][Audio]mon:9,05:19:53
[D][05:19:53][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:19:53][PROT]add success [1629955193],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:53][COMM]Main Task receive event:61 finished processing
[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=

2025-07-31 20:53:29:043 ==>> 0,10800,0

[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:19:53][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:53][COMM]arm_hub_enable: hub power: 0
[D][05:19:53][HSDK]hexlog index save 0 5632 136 @ 0 : 0
[D][05:19:53][HSDK]write save hexlog index [0]
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:53][COMM]f:[ec800m_audio_play_p

2025-07-31 20:53:29:118 ==>> rocess].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:53][COMM]read battery soc:255
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:53:29:223 ==>>                               [W][05:19:54][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:54][COMM]arm_hub_enable: hub power: 0
[D][05:19:54][HSDK]hexlog index save 0 5632 136 @ 0 : 0
[D][05:19:54][HSDK]write save hexlog index [0]
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para fla

2025-07-31 20:53:29:233 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 20:53:29:243 ==>> #################### 【测试结束】 ####################
2025-07-31 20:53:29:260 ==>> sh
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:53:29:268 ==>> 关闭5V供电
2025-07-31 20:53:29:276 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:53:29:357 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:53:29:463 ==>> [D][05:19:55][COMM]exit wheel stolen mode.


2025-07-31 20:53:29:568 ==>> [D][05:19:55][COMM]Main Task receive event:68
[D][05:19:55][COMM]handlerWheelStolen evt type = 2.
[E][05:19:55][COMM][MC]exit stolen,get work mode 

2025-07-31 20:53:29:598 ==>> err,rt:-3
[W][05:19:55][GNSS]stop locating
[D][05:19:55][GNSS]all continue location stop
[D][05:19:55][COMM]Main Task receive event:68 finished processing


2025-07-31 20:53:29:704 ==>> [W][05:19:55][COMM]Power Off


2025-07-31 20:53:30:259 ==>> [D][05:19:55][COMM]read battery soc:255


2025-07-31 20:53:30:278 ==>> 关闭5V供电成功
2025-07-31 20:53:30:305 ==>> 关闭33V供电
2025-07-31 20:53:30:321 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:53:30:364 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:53:30:573 ==>> [D][05:19:55][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:55][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:56][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 20:53:31:282 ==>> 关闭33V供电成功
2025-07-31 20:53:31:296 ==>> 关闭3.7V供电
2025-07-31 20:53:31:320 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:53:31:360 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:53:32:172 ==>>  

