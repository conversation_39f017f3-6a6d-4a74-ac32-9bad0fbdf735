2025-07-31 22:43:37:382 ==>> MES查站成功:
查站序号:P510001005313387验证通过
2025-07-31 22:43:37:385 ==>> 扫码结果:P510001005313387
2025-07-31 22:43:37:387 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:43:37:388 ==>> 测试参数版本:2024.10.11
2025-07-31 22:43:37:390 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:43:37:392 ==>> 检测【打开透传】
2025-07-31 22:43:37:393 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:43:37:505 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:43:37:729 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:43:37:733 ==>> 检测【检测接地电压】
2025-07-31 22:43:37:734 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:43:37:811 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:43:38:010 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:43:38:012 ==>> 检测【打开小电池】
2025-07-31 22:43:38:014 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:43:38:112 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:43:38:281 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:43:38:283 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:43:38:286 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:43:38:403 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:43:38:560 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:43:38:562 ==>> 检测【等待设备启动】
2025-07-31 22:43:38:565 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:43:38:849 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:43:39:029 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:43:39:599 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:43:39:725 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:43:40:106 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:43:40:582 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:43:40:671 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:43:40:674 ==>> 检测【产品通信】
2025-07-31 22:43:40:676 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:43:40:873 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:43:40:963 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:43:40:966 ==>> 检测【初始化完成检测】
2025-07-31 22:43:40:970 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:43:41:276 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:43:41:505 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:43:41:508 ==>> 检测【关闭大灯控制1】
2025-07-31 22:43:41:510 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:43:41:798 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:43:42:046 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:43:42:048 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:43:42:050 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:43:42:208 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:43:42:316 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:43:42:319 ==>> 检测【关闭仪表供电】
2025-07-31 22:43:42:321 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:43:42:498 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:43:42:591 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:43:42:594 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:43:42:596 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:43:42:680 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:42:771 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:43:42:869 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:43:42:872 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:43:42:874 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:43:43:076 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:43:43:139 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:43:43:142 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:43:43:145 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:43:43:291 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:43:43:415 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:43:43:417 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:43:43:419 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:43:43:504 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:43:43:579 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0


2025-07-31 22:43:43:684 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:43:705 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:43:43:708 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:43:43:710 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:43:43:789 ==>> 5A A5 03 5A A5 


2025-07-31 22:43:43:894 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 22:43:44:012 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:43:44:014 ==>> 该项需要延时执行
2025-07-31 22:43:44:228 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5018. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5018. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5019. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5019. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5019. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5020. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5020. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5021. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5021. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5022. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5022. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5022. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5023
[D][05:

2025-07-31 22:43:44:258 ==>> 17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5023


2025-07-31 22:43:44:692 ==>> [D][05:17:54][COMM]5663 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:45:172 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:43:45:664 ==>>                                                n type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1

2025-07-31 22:43:45:769 ==>> 
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Ta

2025-07-31 22:43:45:874 ==>> sk receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][

2025-07-31 22:43:45:934 ==>> COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 22:43:45:979 ==>>                                                                                                                                                                                   

2025-07-31 22:43:46:739 ==>> [D][05:17:56][COMM]7686 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:47:733 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8698 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:48:026 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:43:48:031 ==>> 检测【33V输入电压ADC】
2025-07-31 22:43:48:043 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:43:48:318 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:17:58][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:17:58][COMM]adc read left brake adc:8  volt:10 mv
[D][05:17:58][COMM]adc read right brake adc:6  volt:7 mv
[D][05:17:58][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:58][COMM]adc read battery ts volt:13 mv
[D][05:17:58][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:18  volt:14 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2450  volt:3947 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:43:48:567 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:43:48:569 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:43:48:571 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:43:48:717 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1992mV
Get AD_V7 1106mV
OVER 150


2025-07-31 22:43:48:747 ==>> [D][05:17:58][COMM]9709 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:43:48:848 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:48:851 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:43:48:869 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:48:872 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:43:48:875 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 22:43:48:892 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:43:48:894 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:43:48:922 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:43:48:945 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:43:48:952 ==>> 【TP1_VCC12V(ADV7)】通过,【1106mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:43:48:954 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:43:49:022 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1642mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 2024mV
Get AD_V7 1106mV
OVER 150


2025-07-31 22:43:49:097 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10009
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10010


2025-07-31 22:43:49:247 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:49:249 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:43:49:269 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:49:297 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:43:49:299 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 22:43:49:301 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:43:49:303 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:43:49:321 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:43:49:323 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:43:49:344 ==>> 【TP1_VCC12V(ADV7)】通过,【1106mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:43:49:346 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:43:49:419 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1644mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1992mV
Get AD_V7 1106mV
OVER 150


2025-07-31 22:43:49:524 ==>> [D][05:17:5

2025-07-31 22:43:49:554 ==>> 9][CAT1]power_urc_cb ret[76]


2025-07-31 22:43:49:623 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:49:625 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:43:49:641 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:49:643 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:43:49:647 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 22:43:49:660 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:43:49:663 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:43:49:679 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:43:49:681 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:43:49:701 ==>> 【TP1_VCC12V(ADV7)】通过,【1106mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:43:49:704 ==>> 检测【打开WIFI(1)】
2025-07-31 22:43:49:705 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:43:49:969 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10720 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT

2025-07-31 22:43:50:014 ==>> +IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:43:50:231 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:43:50:245 ==>> 检测【清空消息队列(1)】
2025-07-31 22:43:50:248 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:43:50:471 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087519304

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290267

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:43:50:504 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:43:50:507 ==>> 检测【打开GPS(1)】
2025-07-31 22:43:50:512 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:43:50:696 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:43:50:756 ==>>                                          

2025-07-31 22:43:50:774 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:43:50:777 ==>> 检测【打开GSM联网】
2025-07-31 22:43:50:779 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:43:51:000 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:43:51:048 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:43:51:051 ==>> 检测【打开仪表供电1】
2025-07-31 22:43:51:054 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:43:51:196 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:43:51:318 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:43:51:321 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:43:51:324 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:43:51:420 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:43:51:525 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:43:51:588 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:43:51:591 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:43:51:593 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:43:51:675 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:43:51:780 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33664]


2025-07-31 22:43:52:057 ==>> 【读取主控ADC采集的仪表电压】通过,【33664mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:43:52:060 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:43:52:063 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:52:256 ==>> [D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:43:52:355 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:43:52:358 ==>> 检测【AD_V20电压】
2025-07-31 22:43:52:361 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:52:467 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:52:497 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:43:52:602 ==>> 1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 22:43:52:752 ==>> [D][05:18:02][COMM]13732 imu init OK


2025-07-31 22:43:52:842 ==>> 本次取值间隔时间:364ms
2025-07-31 22:43:52:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:52:949 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 22:43:52:979 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:53:115 ==>> 本次取值间隔时间:131ms
2025-07-31 22:43:53:190 ==>> 1A A1 10 00 00 
Get AD_V20 0mV
OVER 150
                                                                                                                                                                                                                                                                                                    rite para2 flash
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task r

2025-07-31 22:43:53:280 ==>> eceive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.1.211.54"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:43:53:385 ==>> [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_sen

2025-07-31 22:43:53:415 ==>> d_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 22:43:53:535 ==>> 本次取值间隔时间:412ms
2025-07-31 22:43:53:554 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:53:657 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:53:702 ==>> 1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 22:43:53:990 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:43:54:140 ==>> 本次取值间隔时间:481ms
2025-07-31 22:43:54:169 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:54:280 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:54:341 ==>> 本次取值间隔时间:52ms
2025-07-31 22:43:54:507 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:43:54:705 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:43:54:795 ==>> 本次取值间隔时间:442ms
2025-07-31 22:43:54:872 ==>> 本次取值间隔时间:74ms
2025-07-31 22:43:55:059 ==>> 本次取值间隔时间:176ms
2025-07-31 22:43:55:063 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:55:165 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:55:559 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[W][05:18:05][COMM]>>>>>Input command = ?<<<<<
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,41,41,,,39,25,,,38,34,,,38,1*73

$GBGSV,2,2,08,39,,,38,60,,,38,33,,,36,24,,,36,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:05][CAT1]<<< 
OK

$GBGST,,0.000,1587.183,1587.183,50.699,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 22:43:55:634 ==>> 本次取值间隔时间:466ms
2025-07-31 22:43:55:771 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:43:56:121 ==>> 本次取值间隔时间:474ms
2025-07-31 22:43:56:484 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,40,41,,,39,25,,,39,34,,,39,1*78

$GBGSV,3,2,12,39,,,39,60,,,39,24,,,36,33,,,36,1*7E

$GBGSV,3,3,12,16,,,36,10,,,31,5,,,17,3,,,38,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1473.833,1473.833,47.324,2097152,2097152,2097152*49



2025-07-31 22:43:56:604 ==>> 本次取值间隔时间:472ms
2025-07-31 22:43:56:786 ==>> 本次取值间隔时间:172ms
2025-07-31 22:43:56:789 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:56:892 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:57:012 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 22:43:57:284 ==>> 本次取值间隔时间:378ms
2025-07-31 22:43:57:307 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:57:407 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:57:544 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,59,,,42,40,,,41,41,,,40,3,,,40,1*46

$GBGSV,5,2,18,25,,,39,34,,,39,39,,,39,60,,,39,1*74

$GBGSV,5,3,18,33,,,37,43,,,37,24,,,36,16,,,36,1*7F

$GBGSV,5,4,18,1,,,36,10,,,33,4,,,33,2,,,33,1*4D

$GBGSV,5,5,18,32,,,33,5,,,31,1*49

$GBRMC,,V,,,,,,,,0.1,E,N,V*51

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1529.365,1529.365,48.939,2097152,2097152,2097152*40

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 22:43:57:620 ==>> 本次取值间隔时间:209ms
2025-07-31 22:43:57:647 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:43:57:755 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:43:57:815 ==>> [D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1636mV
OVER 150


2025-07-31 22:43:57:876 ==>> 本次取值间隔时间:120ms
2025-07-31 22:43:57:894 ==>> 【AD_V20电压】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:57:898 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:43:57:901 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:43:58:013 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:43:58:179 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:43:58:182 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:43:58:185 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:43:58:406 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:43:58:456 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:43:58:461 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:43:58:466 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:43:58:511 ==>> $GBGGA,144402.368,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,41,39,,,40,59,,,40,41,,,40,1*76

$GBGSV,5,2,20,7,,,39,60,,,39,3,,,39,34,,,39,1*76

$GBGSV,5,3,20,25,,,39,16,,,37,33,,,37,43,,,37,1*7B

$GBGSV,5,4,20,24,,,36,1,,,36,2,,,34,10,,,34,1*71

$GBGSV,5,5,20,4,,,33,32,,,33,5,,,31

2025-07-31 22:43:58:556 ==>> ,14,,,38,1*78

$GBRMC,144402.368,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144402.368,0.000,766.631,766.631,701.102,2097152,2097152,2097152*6E

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:43:58:726 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:43:58:730 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:43:58:735 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:43:58:753 ==>> $GBGGA,144402.568,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,7,2,25,41,,,40,7,,,39,3,,,39,34,,,39,1*7C

$GBGSV,7,3,25,25,,,39,16,,,37,33,,,37,43,,,37,1*7C

$GBGSV,7,4,25,24,,,36,1,,,36,6,,,35,10,,,34,1*73

$GBGSV,7,5,25,2,,,34,32,,,33,4,,,32,5,,,31,1*45

$GBGSV,7,6,25,9,,,25,11,,,16,12,,,13,44,,,40,1*4C

$GBGSV,7,7,25,14,,,37,1*70

$GBRMC,144402.568,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144402.568,0.000,713.591,713.591,652.617,2097152,2097152,2097152*6C



2025-07-31 22:43:58:933 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:43:58:999 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:43:59:004 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:43:59:008 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:43:59:205 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:43:59:282 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:43:59:285 ==>> 检测【AD_V21电压】
2025-07-31 22:43:59:288 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:43:59:355 ==>> 本次取值间隔时间:72ms
2025-07-31 22:43:59:415 ==>> 1A A1 20 00 00 
Get AD_V21 1633mV
OVER 150


2025-07-31 22:43:59:490 ==>> 本次取值间隔时间:134ms
2025-07-31 22:43:59:554 ==>> 【AD_V21电压】通过,【1633mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:43:59:558 ==>> 检测【关闭仪表供电2】
2025-07-31 22:43:59:562 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:43:59:828 ==>> $GBGGA,144403.548,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,40,59,,,40,60,,,39,3,,,39,1*4A

$GBGSV,6,2,24,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,6,3,24,7,,,38,16,,,37,33,,,37,43,,,37,1*4D

$GBGSV,6,4,24,24,,,36,1,,,36,11,,,36,10,,,35,1*47

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,33,32,,,33,1*75

$GBGSV,6,6,24,5,,,32,44,,,32,4,,,32,9,,,31,1*4B

$GBRMC,144403.548,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144403.548,0.000,749.163,749.163,685.127,2097152,2097152,2097152*61

[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
[D][05:18:09][COMM]read battery soc:255


2025-07-31 22:44:00:091 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:44:00:095 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:44:00:098 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:44:00:300 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:44:00:363 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:44:00:366 ==>> 检测【打开AccKey2供电】
2025-07-31 22:44:00:371 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:44:00:715 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
$GBGGA,144404.528,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,60,,,39,3,,,39,1*4B

$GBGSV,7,2,25,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,43,,,37,1*7B

$GBGSV,7,4,25,24,,,36,16,,,36,11,,,36,10,,,35,1*71

$GBGSV,7,5,25,6,,,35,12,,,34,2,,,33,44,,,33,1*75

$GBGSV,7,6,25,32,,,33,5,,,32,9,,,32,4,,,32,1*48

$GBGSV,7,7,25,13,,,44,1*73

$GBRMC,144404.528,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144404.528,0.000,750.885,750.885,686.702,2097152,2097152,2097152*62



2025-07-31 22:44:00:901 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:44:00:907 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:44:00:911 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:01:217 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:11][COMM]adc read out 24v adc:1307  volt:33057 mv
[D][05:18:11][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:11][COMM]adc read battery ts volt:9 mv
[D][05:18:11][COMM]adc read in 24v adc:1278  volt:32324 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:7  volt:5 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2426  volt:3909 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1452  volt:33664 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:44:01:439 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33057mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:44:01:444 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:44:01:449 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:44:01:716 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,144405.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,40,59,,,40,60,,,39,3,,,39,1*4A

$GBGSV,7,2,25,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,7,3,25,7,,,38,1,,,37,33,,,37,43,,,37,1*7B

$GBGSV,7,4,25,24,,,36,16,,,36,11,,,36,10,,,35,1*71

$GBGSV,7,5,25,6,,,35,2,,,34,12,,,34,9,,,33,1*4B

$GBGSV,7,6,25,44,,,33,32,,,33,5,,,32,4,,,32,1*70

$GBGSV,7,7,25,13,,,36,1*76

$GBRMC,144405.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144405.508,0.000,751.738,751.738,687.482,2097152,2097152,2097152*6B



2025-07-31 22:44:01:761 ==>>                                          

2025-07-31 22:44:01:981 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:44:01:984 ==>> 该项需要延时执行
2025-07-31 22:44:02:689 ==>> $GBGGA,144406.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,6,2,24,3,,,39,34,,,39,25,,,39,41,,,39,1*42

$GBGSV,6,3,24,7,,,38,16,,,37,1,,,37,33,,,37,1*7B

$GBGSV,6,4,24,43,,,37,24,,,36,11,,,36,10,,,35,1*70

$GBGSV,6,5,24,6,,,35,2,,,34,12,,,34,9,,,33,1*4B

$GBGSV,6,6,24,44,,,33,32,,,33,5,,,32,4,,,32,1*70

$GBRMC,144406.508,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144406.508,0.000,755.195,755.195,690.643,2097152,2097152,2097152*61



2025-07-31 22:44:03:694 ==>> $GBGGA,144407.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,6,2,24,3,,,39,34,,,39,25,,,39,41,,,39,1*42

$GBGSV,6,3,24,7,,,38,24,,,37,16,,,37,1,,,37,1*7D

$GBGSV,6,4,24,11,,,37,33,,,37,43,,,37,10,,,36,1*75

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,4,,,33,32,,,33,5,,,32,1*76

$GBRMC,144407.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144407.508,0.000,761.217,761.217,696.149,2097152,2097152,2097152*6B



2025-07-31 22:44:03:769 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:44:04:694 ==>> $GBGGA,144408.508,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,60,,,40,59,,,40,41,,,40,1*7D

$GBGSV,6,2,24,3,,,39,39,,,39,34,,,39,25,,,39,1*4D

$GBGSV,6,3,24,7,,,38,24,,,37,16,,,37,1,,,37,1*7D

$GBGSV,6,4,24,11,,,37,33,,,37,43,,,37,10,,,36,1*75

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBRMC,144408.508,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144408.508,0.000,760.359,760.359,695.365,2097152,2097152,2097152*6B



2025-07-31 22:44:04:984 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:44:04:988 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:44:04:993 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:05:319 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:15][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:12 mv
[D][05:18:15][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:15][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2464  volt:3970 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1453  volt:33688 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:44:05:545 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:44:05:549 ==>> 检测【打开AccKey1供电】
2025-07-31 22:44:05:551 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:44:05:741 ==>> $GBGGA,144409.508,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,60,,,39,3,,,39,1*4B

$GBGSV,6,2,24,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,6,3,24,7,,,38,24,,,37,16,,,37,1,,,37,1*7D

$GBGSV,6,4,24,11,,,37,33,,,37,43,,,37,10,,,35,1*76

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBRMC,144409.508,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144409.508,0.000,757.769,757.769,692.996,2097152,2097152,2097152*6B

[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:44:05:771 ==>>                                          

2025-07-31 22:44:05:970 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:44:05:975 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:44:05:978 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:44:06:105 ==>> 1A A1 00 40 00 
Get AD_V14 2666mV
OVER 150


2025-07-31 22:44:06:225 ==>> 原始值:【2666】, 乘以分压基数【2】还原值:【5332】
2025-07-31 22:44:06:243 ==>> 【读取AccKey1电压(ADV14)前】通过,【5332mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:44:06:247 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:44:06:253 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:06:526 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:16][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:16][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:16][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2457  volt:3959 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1452  volt:33664 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:44:06:631 ==>> $GBGGA,144410.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,60,,,40,

2025-07-31 22:44:06:692 ==>> 59,,,40,3,,,39,1*45

$GBGSV,6,2,24,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,6,3,24,7,,,38,24,,,37,16,,,37,1,,,37,1*7D

$GBGSV,6,4,24,11,,,37,33,,,37,43,,,37,10,,,35,1*76

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBRMC,144410.508,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144410.508,0.000,758.634,758.634,693.787,2097152,2097152,2097152*6C



2025-07-31 22:44:06:771 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5500mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:44:06:778 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:44:06:786 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:44:06:981 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:44:07:045 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:44:07:049 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:44:07:052 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:44:07:087 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 22:44:07:299 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 22:44:07:317 ==>> 【读取AccKey1电压(ADV14)后】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:44:07:321 ==>> 检测【打开WIFI(2)】
2025-07-31 22:44:07:331 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:44:07:528 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:44:07:607 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:44:07:612 ==>> 检测【转刹把供电】
2025-07-31 22:44:07:617 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:44:07:707 ==>> $GBGGA,144411.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,40,3,,,40,39,,,40,59,,,40,1*46

$GBGSV,6,2,24,60,,,39,34,,,39,25,,,39,41,,,39,1*77

$GBGSV,6,3,24,7,,,38,24,,,37,16,,,37,1,,,37,1*7D

$GBGSV,6,4,24,33,,,37,43,,,37,11,,,36,10,,,35,1*77

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBRMC,144411.508,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144411.508,0.000,757.771,757.771,692.998,2097152,2097152,2097152*6C



2025-07-31 22:44:07:797 ==>> [D][05:18:17][COMM]read battery soc:255
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:44:07:875 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:44:07:879 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:44:07:884 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:44:07:977 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:44:08:176 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:44:08:343 ==>> +WIFISCAN:4,0,F88C21BCF57D,-38
+WIFISCAN:4,1,F42A7D1297A3,-68
+WIFISCAN:4,2,44A1917CAD80,-79
+WIFISCAN:4,3,CC057790A5C0,-82

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 22:44:08:711 ==>> $GBGGA,144412.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,59,,,40,41,,,40,60,,,39,1*73

$GBGSV,6,2,24,3,,,39,39,,,39,34,,,39,25,,,39,1*4D

$GBGSV,6,3,24,7,,,38,16,,,37,33,,,37,43,,,37,1*4D

$GBGSV,6,4,24,10,,,36,24,,,36,1,,,36,11,,,36,1*44

$GBGSV,6,5,24,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,6,6,24,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBRMC,144412.508,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144412.508,0.000,756.909,756.909,692.210,2097152,2097152,2097152*64



2025-07-31 22:44:08:816 ==>> [D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 22:44:08:906 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:44:09:012 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:44:09:102 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2403mV
OVER 150


2025-07-31 22:44:09:177 ==>> 原始值:【2403】, 乘以分压基数【2】还原值:【4806】
2025-07-31 22:44:09:197 ==>> 【读取AD_V15电压(前)】通过,【4806mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:44:09:203 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:44:09:209 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:44:09:312 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:44:09:403 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2433mV
OVER 150


2025-07-31 22:44:09:463 ==>> 原始值:【2433】, 乘以分压基数【2】还原值:【4866】
2025-07-31 22:44:09:498 ==>> 【读取AD_V16电压(前)】通过,【4866mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:44:09:501 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:44:09:504 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:09:872 ==>> $GBGGA,144413.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,40,59,,,40,60,,,39,3,,,39,1*4A

$GBGSV,7,2,25,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,7,3,25,7,,,38,24,,,37,16,,,37,11,,,37,1*4C

$GBGSV,7,4,25,33,,,37,43,,,37,1,,,36,10,,,35,1*46

$GBGSV,7,5,25,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,144413.508,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144413.508,0.000,756.042,756.042,691.417,2097152,2097152,2097152*67

[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:19][COMM]adc read battery ts volt:13 mv
[D][05:18:19][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2436  volt:3925 mv
[D][05:18:19][COMM]arm_h

2025-07-31 22:44:09:917 ==>> ub adc read led yb adc:1452  volt:33664 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:19][COMM]read battery soc:255


2025-07-31 22:44:10:026 ==>> 【转刹把供电电压(主控ADC)】通过,【5414mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:44:10:030 ==>> 检测【转刹把供电电压】
2025-07-31 22:44:10:036 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:10:312 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:20][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:20][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:20][COMM]adc read battery ts volt:5 mv
[D][05:18:20][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3071  volt:5398 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2481  volt:3997 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1452  volt:33664 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:44:10:562 ==>> 【转刹把供电电压】通过,【5398mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:44:10:566 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:44:10:573 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:44:10:703 ==>> $GBGGA,144414.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,40,60,,,39,3,,,39,39,,,39,1*42

$GBGSV,7,2,25,59,,,39,34,,,39,25,,,39,41,,,39,1*7D

$GBGSV,7,3,25,7,,,38,16,,,37,1,,,37,11,,,37,1*7B

$GBGSV,7,4,25,33,,,37,43,,,37,24,,,36,10,,,35,1*71

$GBGSV,7,5,25,6,,,35,12,,,35,9,,,34,44,,,34,1*7F

$GBGSV,7,6,25,2,,,33,32,,,33,5,,,32,4,,,32,1*42

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,144414.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144414.508,0.000,754.318,754.318,689.840,2097152,2097152,2097152*67



2025-07-31 22:44:10:793 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:44:10:885 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:44:10:889 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:44:10:896 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:44:10:992 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:44:11:067 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:44:11:097 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:44:11:112 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:44:11:202 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:44:11:307 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:44:11:353 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:44:11:358 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:44:11:384 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:44:11:460 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:44:11:506 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:44:11:654 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:44:11:660 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:44:11:665 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:44:11:702 ==>> $GBGGA,144415.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,40,60,,,39,3,,,39,39,,,39,1*42

$GBGSV,7,2,25,59,,,39,34,,,39,25,,,39,41,,,39,1*7D

$GBGSV,7,3,25,7,,,38,16,,,37,11,,,37,43,,,37,1*4D

$GBGSV,7,4,25,24,,,36,1,,,36,33,,,36,10,,,35,1*47

$GBGSV,7,5,25,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,144415.508,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144415.508,0.000,753.453,753.453,689.049,2097152,2097152,2097152*67

3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:44:11:792 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:44:11:965 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:44:11:970 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:44:11:992 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:44:12:109 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:44:12:258 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:44:12:262 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:44:12:268 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:44:12:308 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:44:12:539 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:44:12:546 ==>> 检测【左刹电压测试1】
2025-07-31 22:44:12:557 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:12:827 ==>> $GBGGA,144416.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,60,,,39,3,,,39,1*4B

$GBGSV,7,2,25,39,,,39,34,,,39,25,,,39,41,,,39,1*7B

$GBGSV,7,3,25,7,,,38,16,,,37,1,,,37,43,,,37,1*7C

$GBGSV,7,4,25,24,,,36,11,,,36,33,,,36,10,,,35,1*76

$GBGSV,7,5,25,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,144416.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144416.508,0.000,755.183,755.183,690.632,2097152,2097152,2097152*66

[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1721  volt:2268 mv
[D][05:18:22][COMM]adc read right brake adc:1715  volt:2260 mv
[D][05:18:22][COMM]adc read throttle adc:1718  volt:2264 mv
[D][05:18:22][COMM]adc read battery ts volt:6 mv
[D][05:18:22][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:22][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_h

2025-07-31 22:44:12:872 ==>> ub adc read vbat adc:2427  volt:3910 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:44:13:073 ==>> 【左刹电压测试1】通过,【2268】符合目标值【2250】至【2500】要求!
2025-07-31 22:44:13:076 ==>> 检测【右刹电压测试1】
2025-07-31 22:44:13:096 ==>> 【右刹电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 22:44:13:102 ==>> 检测【转把电压测试1】
2025-07-31 22:44:13:116 ==>> 【转把电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 22:44:13:122 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:44:13:134 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:44:13:206 ==>> 3A A3 03 00 A3 


2025-07-31 22:44:13:311 ==>> OFF_OUT3
OVER 150


2025-07-31 22:44:13:389 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:44:13:396 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:44:13:418 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:44:13:509 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:44:13:663 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:44:13:670 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:44:13:679 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:44:13:693 ==>> $GBGGA,144417.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,41,,,40,60,,,39,1*73

$GBGSV,7,2,25,3,,,39,39,,,39,34,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,38,24,,,37,16,,,37,11,,,37,1*4C

$GBGSV,7,4,25,33,,,37,43,,,37,1,,,36,10,,,35,1*46

$GBGSV,7,5,25,6,,,35,12,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,44,,,34,32,,,33,5,,,32,4,,,32,1*77

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,144417.508,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144417.508,0.000,757.772,757.772,692.999,2097152,2097152,2097152*6B



2025-07-31 22:44:13:795 ==>> [D][05:18:23][COMM]read battery soc:255
3A A3 05 00 A3 


2025-07-31 22:44:13:900 ==>> OFF_OUT5
OVER 150


2025-07-31 22:44:13:933 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:44:13:936 ==>> 检测【左刹电压测试2】
2025-07-31 22:44:13:939 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:44:14:222 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3141  volt:5521 mv
[D][05:18:24][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:24][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:24][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:24][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:24][COMM]adc read battery ts volt:8 mv
[D][05:18:24][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:24][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2455  volt:3955 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1451  volt:33641 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:44:14:467 ==>> 【左刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 22:44:14:470 ==>> 检测【右刹电压测试2】
2025-07-31 22:44:14:493 ==>> 【右刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:44:14:499 ==>> 检测【转把电压测试2】
2025-07-31 22:44:14:512 ==>> 【转把电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 22:44:14:518 ==>> 检测【晶振检测】
2025-07-31 22:44:14:541 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:44:15:511 ==>> $GBGGA,144414.515,2301.2572522,N,11421.9430156,E,1,10,1.06,73.691,M,-1.770,M,,*53

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,7,1,25,40,67,173,41,7,64,200,38,39,61,37,39,3,60,190,39,1*4A

$GBGSV,7,2,25,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,36,1*7C

$GBGSV,7,3,25,9,51,343,34,25,50,359,39,1,48,125,36,34,45,101,40,1*70

$GBGSV,7,4,25,2,45,237,34,60,41,239,39,41,40,261,40,4,32,111,32,1*74

$GBGSV,7,5,25,43,32,169,37,33,29,198,37,24,24,65,37,5,23,257,32,1*72

$GBGSV,7,6,25,8,8,202,,44,3,183,34,11,,,37,12,,,35,1*4F

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,144414.515,A,2301.2572522,N,11421.9430156,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:24][GNSS]HD8040 GPS
[D][05:18:24][GNSS]GPS diff_sec 124017950, report 0x42 frame
$GBGST,144414.515,0.631,0.155,0.150,0.241,2.541,3.193,10*57

[W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]
[D][05:18:24][COMM]Main Task receive event:131
[D][05:18:24][COMM]index:0,power_mode:0xFF
[D][05:18:24][COMM]index:1,sound_mode:0xFF
[D][05:18:24][COMM]index:2,gsensor_mode:0xFF
[D][05:18:24][COMM]index:3,report_freq_mode:0xFF
[D][05:18:24][COMM]index:4,r

2025-07-31 22:44:15:570 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:44:15:575 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:44:15:582 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:44:15:616 ==>> eport_period:0xFF
[D][05:18:24][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:24][COMM]index:6,normal_reset_period:0xFF
[D][05:18:24][COMM]index:7,spock_over_speed:0xFF
[D][05:18:24][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:24][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:24][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:24][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:24][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:24][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:24][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:24][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:24][COMM]index:16,imu_config_params:0xFF
[D][05:18:24][COMM]index:17,long_connect_params:0xFF
[D][05:18:24][COMM]index:18,detain_mark:0xFF
[D][05:18:24][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:24][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:24][COMM]index:21,mc_mode:0xFF
[D][05:18:24][COMM]index:22,S_mode:0xFF
[D][05:18:24][COMM]index:23,overweight:0xFF
[D][05:18:24][COMM]index:24,standstill_mode:0xFF
[D][05:18:24][COMM]index:25,night_mode:0xFF
[D][05:18:24][COMM]index:26,experiment1:0xFF
[D][05:18:24][COMM]index:27,experiment2:0xFF
[D][05:18:24

2025-07-31 22:44:15:721 ==>> ][COMM]index:28,experiment3:0xFF
[D][05:18:24][COMM]index:29,experiment4:0xFF
[D][05:18:24][COMM]index:30,night_mode_start:0xFF
[D][05:18:24][COMM]index:31,night_mode_end:0xFF
[D][05:18:24][COMM]index:33,park_report_minutes:0xFF
[D][05:18:24][COMM]index:34,park_report_mode:0xFF
[D][05:18:24][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:24][COMM]index:38,charge_battery_para: FF
[D][05:18:24][COMM]index:39,multirider_mode:0xFF
[D][05:18:24][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:24][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:24][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:24][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:24][COMM]index:44,riding_duration_config:0xFF
[D][05:18:24][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:24][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:24][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:24][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:24][COMM]index:49,mc_load_startup:0xFF
[D][05:18:24][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:24][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:24][COMM]index:52,traffic_mode:0xFF
[D][05:18:24][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:24][COMM

2025-07-31 22:44:15:826 ==>> ]index:54,traffic_security_model_cycle:0xFF
[D][05:18:24][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:24][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:24][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:24][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:24][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:24][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:24][COMM]index:63,experiment5:0xFF
[D][05:18:24][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:24][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:24][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:24][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:24][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:24][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:24][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:24][COMM]index:72,experiment6:0xFF
[D][05:18:24][COMM]index:73,experiment7:0xFF
[D][05:18:24][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:24][COMM]index:75,zero_value_from_server:-1
[D][05:18:24][COMM]index:76,multirider_threshold:255
[D][05:18:24][COMM]index:77,experiment8:255
[D][05:18:24][COMM]index:78,temp_park_audio_play_duration:25

2025-07-31 22:44:15:931 ==>> 5
[D][05:18:24][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:24][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:24][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:24][COMM]index:83,loc_report_interval:255
[D][05:18:24][COMM]index:84,multirider_threshold_p2:255
[D][05:18:24][COMM]index:85,multirider_strategy:255
[D][05:18:24][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:24][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:24][COMM]index:90,weight_param:0xFF
[D][05:18:24][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:24][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:24][COMM]index:95,current_limit:0xFF
[D][05:18:24][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:24][COMM]index:100,location_mode:0xFF

[W][05:18:24][PROT]remove success[1629955104],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:24][PROT]add success [1629955104],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:24][COMM]Main Task receive event:131 finished processing
[D][05:18:24][PROT]index:0 1629955104
[D][05:18:24][PROT]is_send:0
[D][05:18:24][PROT]sequence_num:4
[D][0

2025-07-31 22:44:16:036 ==>> 5:18:24][PROT]retry_timeout:0
[D][05:18:24][PROT]retry_times:1
[D][05:18:24][PROT]send_path:0x2
[D][05:18:24][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:24][PROT]===========================================================
[D][05:18:24][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:24][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955104]
[D][05:18:24][PROT]===========================================================
[D][05:18:24][PROT]sending traceid [9999999999900005]
[D][05:18:24][PROT]Send_TO_M2M [1629955104]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:24][SAL ]sock send credit cnt[6]
[D][05:18:24][SAL ]sock send ind credit cnt[6]
[D][05:18:24][M2M ]m2m send data len[294]
[D][05:18:24][SAL ]Cellular task submsg id[10]
[D][05:18:24][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:24][CAT1]gsm read msg sub id: 15
[D][05:18:24][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:24][CAT1]Send Data To Server[294][2

2025-07-31 22:44:16:141 ==>> 97] ... ->:
0093B98A113311331133113311331B88B5BD52AE96EF076C5102755CA510766F066C0FE095BD7F75AA83C0522804FB6EB0CE1C64A7FFC383E477A11B82E663F0BC6852E80EEE39BAFB75D0275850E202369A4F3D059D682BA2A109E3E128672BF0BCDFBBF20031615FB9A4973880437B4C56E54F35F919BDC7D08FE5327E16EFFDC16297B91514163A7504D8C0ACD089EC7C13
[D][05:18:24][CAT1]<<< 
SEND OK

[D][05:18:24][CAT1]exec over: func id: 15, ret: 11
[D][05:18:24][CAT1]sub id: 15, ret: 11

[D][05:18:24][SAL ]Cellular task submsg id[68]
[D][05:18:24][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:24][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:24][M2M ]g_m2m_is_idle become true
[D][05:18:24][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:24][PROT]M2M Send ok [1629955104]
$GBGGA,144415.015,2301.2571972,N,11421.9433143,E,1,10,1.06,76.220,M,-1.770,M,,*51

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,41A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1642mV
Get AD_V4 1647mV
Get AD_V5 2770mV
Get AD_V6 20

2025-07-31 22:44:16:171 ==>> 24mV
Get AD_V7 1107mV
OVER 150


2025-07-31 22:44:16:276 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  1,43,32,169,35,33,29,198,37,24,24,65,36,5*4C

$GBRMC,144416.000,A,2301.2572581,N,11421.9436615,E,0.001,0.00,310725,,,A,S*31

$GBVTG,

2025-07-31 22:44:16:306 ==>> 0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,144416.000,1.638,0.210,0.201,0.322,1.665,1.789,5.046*7C



2025-07-31 22:44:16:403 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:44:16:411 ==>> 检测【检测BootVer】
2025-07-31 22:44:16:419 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:16:765 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130020290267
[D][05:18:26][FCTY]HardwareID  = 867222087519304
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:26][FCTY]Bat         = 4044 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 12000 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 32, adc = 1289
[D][05:18:26][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1

2025-07-31 22:44:16:810 ==>> _GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3849 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:44:16:968 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:44:16:974 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:44:16:981 ==>> 检测【检测固件版本】
2025-07-31 22:44:17:008 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:44:17:012 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:44:17:020 ==>> 检测【检测蓝牙版本】
2025-07-31 22:44:17:059 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:44:17:064 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:44:17:072 ==>> 检测【检测MoBikeId】
2025-07-31 22:44:17:116 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:44:17:121 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:44:17:128 ==>> 检测【检测蓝牙地址】
2025-07-31 22:44:17:148 ==>> 取到目标值:C2986729C1B3
2025-07-31 22:44:17:219 ==>> 【检测蓝牙地址】通过,【C2986729C1B3】符合目标值【】要求!
2025-07-31 22:44:17:225 ==>> 提取到蓝牙地址:C2986729C1B3
2025-07-31 22:44:17:233 ==>> 检测【BOARD_ID】
2025-07-31 22:44:17:270 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:44:17:281 ==>> 检测【检测充电电压】
2025-07-31 22:44:17:310 ==>> $GBGGA,144417.000,2301.2572371,N,11421.9438866,E,1,10,1.06,79.346,M,-1.770,M,,*56

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,40,7,64,200,38,39,61,37,39,3,60,190,39,1*4B

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,36,1*7C

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,37,34,45,101,40,1*71

$GBGSV,6,4,24,2,45,237,34,60,41,239,40,41,40,261,40,4,32,111,32,1*7A

$GBGSV,6,5,24,43,32,169,37,33,29,198,37,24,24,65,37,5,23,257,32,1*72

$GBGSV,6,6,24,44,3,183,34,11,,,37,12,,,35,32,,,33,1*7E

$GBGSV,2,1,08,40,67,173,41,39,61,37,41,25,50,359,41,34,45,101,40,5*4A

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144417.000,A,2301.2572371,N,11421.9438866,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,144417.000,1.719,0.282,0.269,0.424,1.578,1.639,4.240*7B



2025-07-31 22:44:17:320 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:44:17:325 ==>> 检测【检测VBUS电压1】
2025-07-31 22:44:17:368 ==>> 【检测VBUS电压1】通过,【12000mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:44:17:372 ==>> 检测【检测充电电流】
2025-07-31 22:44:17:420 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:44:17:424 ==>> 检测【检测IMEI】
2025-07-31 22:44:17:432 ==>> 取到目标值:867222087519304
2025-07-31 22:44:17:471 ==>> 【检测IMEI】通过,【867222087519304】符合目标值【】要求!
2025-07-31 22:44:17:476 ==>> 提取到IMEI:867222087519304
2025-07-31 22:44:17:483 ==>> 检测【检测IMSI】
2025-07-31 22:44:17:504 ==>> 取到目标值:460130020290267
2025-07-31 22:44:17:523 ==>> 【检测IMSI】通过,【460130020290267】符合目标值【】要求!
2025-07-31 22:44:17:528 ==>> 提取到IMSI:460130020290267
2025-07-31 22:44:17:537 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:44:17:551 ==>> 取到目标值:460130020290267
2025-07-31 22:44:17:559 ==>> 【校验网络运营商(移动)】通过,【460130020290267】符合目标值【】要求!
2025-07-31 22:44:17:580 ==>> 检测【打开CAN通信】
2025-07-31 22:44:17:584 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:44:17:715 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:44:17:805 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 22:44:17:863 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:44:17:868 ==>> 检测【检测CAN通信】
2025-07-31 22:44:17:874 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:44:18:001 ==>> can send success


2025-07-31 22:44:18:031 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:44:18:091 ==>> [D][05:18:28][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 39053
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:44:18:151 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:44:18:165 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:44:18:170 ==>> 检测【关闭CAN通信】
2025-07-31 22:44:18:174 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:44:18:256 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,144418.000,2301.2572641,N,11421.9439888,E,1,10,1.06,79.775,M,-1.770,M,,*5A

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,40,7,64,200,38,39,61,37,39,3,60,190,40,1*45

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,36,1*7C

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,37,34,45,101,40,1*71

$GBGSV,6,4,24,2,45,237,34,60,41,239,40,41,40,261,40,4,32,111,32,1*7A

$GBGSV,6,5,24,43,32,169,37,33,29,198,37,24,24,65,37,5,23,257,32,1*72

$GBGSV,6,6,24,44,3,183,34,11,,,37,12,,,35,32,,,33,1*7E

$GBGSV,2,1,08,40,67,173,41,39,61,37,41,25,50,359,41,34

2025-07-31 22:44:18:316 ==>> ,45,101,40,5*4A

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144418.000,A,2301.2572641,N,11421.9439888,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144418.000,1.055,0.227,0.217,0.342,0.999,1.044,3.464*73

[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:44:18:464 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:44:18:469 ==>> 检测【打印IMU STATE】
2025-07-31 22:44:18:499 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:44:18:700 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:44:18:770 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:44:18:777 ==>> 检测【六轴自检】
2025-07-31 22:44:18:792 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:44:18:999 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:44:19:530 ==>> $GBGGA,144419.000,2301.2572974,N,11421.9440178,E,1,10,1.06,79.926,M,-1.770,M,,*52

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,40,7,64,200,38,39,61,37,39,3,60,190,39,1*4B

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,36,1*7C

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,36,34,45,100,40,1*71

$GBGSV,6,4,24,2,45,237,34,60,41,239,39,11,41,135,37,41,40,261,40,1*47

$GBGSV,6,5,24,4,32,111,32,43,32,169,37,33,29,198,37,24,24,65,37,1*72

$GBGSV,6,6,24,5,23,257,32,44,3,183,34,12,,,35,32,,,33,1*7F

$GBGSV,2,1,08,40,67,173,41,39,61,37,41,25,50,359,41,34,45,100,40,5*4B

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144419.000,A,2301.2572974,N,11421.9440178,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144419.000,1.098,0.226,0.217,0.342,0.975,1.010,3.137*72



2025-07-31 22:44:19:830 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 22:44:20:298 ==>> $GBGGA,144420.000,2301.2573291,N,11421.9440259,E,1,10,1.06,80.237,M,-1.770,M,,*54

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,40,7,64,200,38,39,61,37,39,3,60,190,39,1*4B

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,36,1*7C

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,37,34,45,100,39,1*7E

$GBGSV,6,4,24,2,45,237,35,60,41,239,39,11,41,135,37,41,40,261,39,1*48

$GBGSV,6,5,24,4,32,111,32,43,32,169,37,33,29,198,37,24,24,65,37,1*72

$GBGSV,6,6,24,5,23,257,32,44,3,183,34,12,,,35,32,,,33,1*7F

$GBGSV,2,1,08,40,67,173,41,39,61,37,42,25,50,359,41,34,45,100,40,5*48

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144420.000,A,2301.2573291,N,11421.9440259,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,144420.000,1.134,0.243,0.233,0.369,0.964,0.994,2.900*7A



2025-07-31 22:44:20:690 ==>> [D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:44:20:904 ==>> [D][05:18:30][COMM]Main Task receive event:142
[D][05:18:30][COMM]###### 41850 imu self test OK ######
[D][05:18:30][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-16,-10,4041]
[D][05:18:30][COMM]Main Task receive event:142 finished processing


2025-07-31 22:44:21:106 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:44:21:112 ==>> 检测【打印IMU STATE2】
2025-07-31 22:44:21:133 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:44:21:373 ==>> $GBGGA,144421.000,2301.2573527,N,11421.9440316,E,1,10,1.06,80.495,M,-1.770,M,,*5B

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,40,7,64,200,38,39,61,37,39,3,60,190,39,1*4B

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,35,1*7F

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,37,34,45,100,39,1*7E

$GBGSV,6,4,24,2,45,237,34,60,41,239,40,11,41,135,37,41,40,261,39,1*47

$GBGSV,6,5,24,4,32,111,32,43,32,169,37,33,29,198,37,24,24,65,37,1*72

$GBGSV,6,6,24,5,23,257,32,44,3,183,34,12,,,35,32,,,33,1*7F

$GBGSV,2,1,08,40,67,173,41,39,61,37,42,25,50,359,41,34,45,100,40,5*48

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144421.000,A,2301.2573527,N,11421.9440316,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144421.000,1.298,0.207,0.199,0.313,1.071,1.094,2.799*7A

[W][05:18:31][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:31][COMM]YAW data: 32763[32763]
[D][05:18:31][COMM]pitch:-66 roll:0
[D][05:18:31][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:44:21:634 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:44:21:644 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:44:21:667 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:44:21:705 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:44:21:810 ==>> [D][05:18:31][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:31][FCTY]get

2025-07-31 22:44:21:870 ==>> _ext_48v_vol retry i = 1,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:44:21:904 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:44:21:908 ==>> 检测【检测VBUS电压2】
2025-07-31 22:44:21:913 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:22:353 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130020290267
[D][05:18:32][FCTY]HardwareID  = 867222087519304
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:32][FCTY]Bat         = 3944 mv
[D][05:18:32][FCTY]Current     = 200 ma
[D][05:18:32][FCTY]VBUS        = 11900 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 7, adc = 310
[D][05:18:32][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4


2025-07-31 22:44:22:440 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:22:458 ==>> 
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3849 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
$GBGGA,144422.000,2301.2573763,N,11421.9440609,E,1,10,1.06,80.679,M,-1.770,M,,*51

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,41,7,64,200,38,39,61,37,40,3,60,190,40,1*4A

$GBGSV,6,2,24,6,59,7,35,16,58,11,37,59,52,129,40,10,52,209,35,1*7F

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,37,34,45,100,39,1*7E

$GBGSV,6,4,24,2,45,237,34,60,41,239,39,11,41,135,37,41,40,261,40,1*47

$GBGSV,6,5,24,4,32,111,32,43,32,169,37,12,30,69,36,33,29,198,37,1*7F

$GBGSV,6,6,24,24,24,65,37,5,23,257,32,44,3,183,34,32,,,33,1*7D

$GBGSV,2,1,08,40,67,173,41,39,61,37,42,25,50,359,41,34,45,100,40,5*48

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144422.000,A,2301.2573763,N,11421.9440609,E,0.004,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,144422.000,1.242,0.231,0.222,0.348,1.004,1.023,2.624*7F



2025-07-31 22:44:22:773 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130020290267
[D][05:18:32][FCTY]HardwareID  = 867222087519304
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:32][FCTY]Bat         = 3944 mv
[D][05:18:32][FCTY]Current     = 200 ma
[D][05:18:32][FCTY]VBUS        = 11900 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 4, adc = 176
[D][05:18:32][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Ba

2025-07-31 22:44:22:803 ==>> t1         = 3849 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:44:22:980 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:23:105 ==>> [D][05:18:33][COMM]msg 0601 loss. last_tick:39045. cur_tick:44056. period:500
[D][05:18:33][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 44057


2025-07-31 22:44:23:452 ==>> $GBGGA,144423.000,2301.2573826,N,11421.9440951,E,1,10,1.06,80.698,M,-1.770,M,,*53

$GBGSA,A,3,40,39,16,10,25,34,41,43,33,24,,,2.35,1.06,2.10,4*04

$GBGSV,6,1,24,40,67,173,41,7,64,200,38,39,61,37,39,3,60,190,39,1*4A

$GBGSV,6,2,24,6,59,7,36,16,58,11,37,59,52,129,40,10,52,209,35,1*7C

$GBGSV,6,3,24,9,51,343,34,25,50,359,39,1,48,125,36,34,45,100,40,1*71

$GBGSV,6,4,24,2,45,237,34,60,41,239,39,11,41,135,37,41,40,261,40,1*47

$GBGSV,6,5,24,4,32,111,32,43,32,169,37,12,30,69,36,33,29,198,37,1*7F

$GBGSV,6,6,24,24,24,65,37,5,23,257,32,44,3,183,34,32,,,33,1*7D

$GBGSV,2,1,08,40,67,173,41,39,61,37,42,25,50,359,41,34,45,100,40,5*48

$GBGSV,2,2,08,41,40,261,42,43,32,169,35,33,29,198,38,24,24,65,36,5*40

$GBRMC,144423.000,A,2301.2573826,N,11421.9440951,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144423.000,1.331,0.199,0.192,0.301,1.060,1.075,2.561*7C

[W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100


2025-07-31 22:44:23:556 ==>> 
[D][05:18:33][FCTY]DeviceID    = 460130020290267
[D][05:18:33][FCTY]HardwareID  = 867222087519304
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:33][FCTY]Bat         = 3804 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 9400 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 3, adc = 142
[D][05:18:33][FCTY]Acckey1 vol = 5514 mv, Acckey2 vol = 0 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3849 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:44:23:772 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:24:249 ==>> [D][05:18:33][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[W][05:18:33][GNSS]stop locating
[D][05:18:33][GNSS]stop event:8
[D][05:18:33][GNSS]GPS stop. ret=0
[D][05:18:33][GNSS]all continue location stop
[D][05:18:33][COMM]report elecbike
[D][05:18:33][CAT1]gsm read msg sub id: 24
[D][05:18:33][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]index:0
[D][05:18:33][PROT]is_send:1
[D][05

2025-07-31 22:44:24:355 ==>> :18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][PROT]index:0 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]===========================================================
[D][05:18:33][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]=

2025-07-31 22:44:24:459 ==>> ==========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 24, ret: 6
[D][05:18:33][CAT1]sub id: 24, ret: 6

[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B3E73DF111A730E228C36355359202FDD752A3F3728F6963A70F79958B9CFFE4D9DB0F6C0D1A62AA95B40581E3B5BB2948A6C611E013EF570037DFE9FA64CEC7475BBE3B46DC1D133554BCF6777AB7693FC4E3
[D][05:18:33][CAT1]<

2025-07-31 22:44:24:564 ==>> << 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]
[D][05:18:33][GNSS]recv submsg id[1]
[D][05:18:33][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:33][GNSS]location stop evt done evt
[W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130020290267
[D][05:18:33][FCTY]HardwareID  = 867222087519304
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:33][FCTY]Bat         =

2025-07-31 22:44:24:654 ==>>  3844 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 4900 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 2, adc = 116
[D][05:18:33][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3849 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:44:24:807 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:44:25:164 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:34][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========
[D][05:18:34][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:34][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:34][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:34][FCTY]DeviceID    = 460130020290267
[D][05:18:34][FCTY]HardwareID  = 867222087519304
[D][05:18:34][FCTY]MoBikeID    = 9999999999
[D][05:18:34][FCTY]LockID      = FFFFFFFFFF
[D][05:18:34][FCTY]BLEFWVersion= 105
[D][05:18:34][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:18:34][FCTY]Bat         = 3864 mv
[D][05:18:34][FCTY]Current     = 0 ma
[D][05:18:34][FCTY]VBUS        = 4900 mv
[D][05:18:34][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:34][FCTY]Ext battery vol = 2, adc = 98
[D][05:18:34][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:34][FCTY]Bike Type flag is invalied
[D][05:18:34][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:34][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:34][FCTY]CA

2025-07-31 22:44:25:209 ==>> T1_GNSS_PLATFORM = C4
[D][05:18:34][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:34][FCTY]Bat1         = 3849 mv
[D][05:18:34][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:44:25:332 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:44:25:346 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:44:25:351 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:44:25:408 ==>> 5A A5 01 5A A5 


2025-07-31 22:44:25:513 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:44:25:573 ==>> [D][05:18:35][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27


2025-07-31 22:44:25:611 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:44:25:616 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:44:25:634 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:44:25:649 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 22:44:25:708 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:44:25:893 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:44:25:899 ==>> 检测【打开WIFI(3)】
2025-07-31 22:44:25:908 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:44:26:122 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:36][CAT1]gsm read msg sub id: 12
[D][05:18:36][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:36][CAT1]<<< 
OK

[D][05:18:36][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:44:26:170 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:44:26:175 ==>> 检测【扩展芯片hw】
2025-07-31 22:44:26:183 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:44:26:397 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:36][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:44:26:445 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:44:26:450 ==>> 检测【扩展芯片boot】
2025-07-31 22:44:26:464 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:44:26:469 ==>> 检测【扩展芯片sw】
2025-07-31 22:44:26:493 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:44:26:503 ==>> 检测【检测音频FLASH】
2025-07-31 22:44:26:508 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:44:26:686 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:44:27:173 ==>> [D][05:18:37][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:37][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:0------------
[D][05:18:37][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:44:27:895 ==>>                                                                                                                  -----------
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]more than the number of battery plugs
[D][05:18:37][COMM]VBUS is 1
[D][05:18:37][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:37][COMM]file:B50 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:37][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:37][COMM]Bat auth off fail, error:-1
[D][05:18:37][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:B50 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:37][COMM]read file,

2025-07-31 22:44:28:000 ==>>  len:10800, num:3
[D][05:18:37][COMM]--->crc16:0xb8a
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:37][COMM]Main Task receive event:65
[D][05:18:37][COMM]main task tmp_sleep_event = 80
[D][05:18:37][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:37][COMM]Main Task receive event:65 finished processing
[D][05:18:37][COMM]Main Task receive event:66
[D][05:18:37][COMM]Try to Auto Lock Bat
[D][05:18:37][COMM]Main Task receive event:66 finished processing
[D][05:18:37][COMM]Main Task receive event:60
[D][05:18:37]

2025-07-31 22:44:28:105 ==>> [COMM]smart_helmet_vol=255,255
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]BAT CAN get state1 Fail 204
[D][05:18:37][COMM]BAT CAN get soc Fail, 204
[D][05:18:37][COMM]get soc error
[E][05:18:37][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:37][COMM]Receive Bat Lock cmd 0
[D][05:18:37][COMM]VBUS is 1
[D][05:18:37][COMM]report elecbike
[W][05:18:37][PROT]remove success[1629955117],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:37][COMM]Main Task receive event:60 finished processing
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:37][COMM]Main Task receive event:61
[D][05:18:37][COMM][D301]:type:3, trace id:280
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][PROT]index:1
[D][05:18:37][M2M ]m2m_task: g

2025-07-31 22:44:28:210 ==>> pc:[0],gpo:[1]
[D][05:18:37][PROT]is_send:1
[D][05:18:37][PROT]sequence_num:6
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x3
[D][05:18:37][PROT]msg_type:0x5d03
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]Sending traceid[9999999999900007]
[D][05:18:37][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:37][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:37][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:37][COMM]id[], hw[000
[D][05:18:37][COMM]get mcMaincircuitVolt error
[D][05:18:37][COMM]get mcSubcircuitVolt error
[D][05:18:37][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:37][COMM]BAT CAN get state1 Fail 204
[D][05:18:37][COMM]BAT CAN get soc Fail, 204
[D][05:18:37][COMM]get bat work state err
[W][05:18:37][PROT]remove success[1629955117],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:37][PROT]add suc

2025-07-31 22:44:28:315 ==>> cess [1629955117],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:37][COMM]Main Task receive event:61 finished processing
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,CC057790A621,-61
+WIFISCAN:4,2,F42A7D1297A3,-69
+WIFISCAN:4,3,74C330CCAB10,-72

[D][05:18:37][CAT1]wifi scan report total[4]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D

2025-07-31 22:44:28:405 ==>> ][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:37][COMM]read battery soc:255
                                      

2025-07-31 22:44:29:264 ==>> [D][05:18:39][PROT]CLEAN,SEND:0
[D][05:18:39][PROT]index:1 1629955119
[D][05:18:39][PROT]is_send:0
[D][05:18:39][PROT]sequence_num:6
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:3
[D][05:18:39][PROT]send_path:0x2
[D][05:18:39][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]sending traceid [9999999999900007]
[D][05:18:39][PROT]Send_TO_M2M [1629955119]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:39][SAL ]sock send credit cnt[6]
[D][05:18:39][SAL ]sock send ind credit cnt[6]
[D][05:18:39][M2M ]m2m send data len[198]
[D][05:18:39][SAL ]Cellular task submsg id[10]
[D][05:18:39][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:39][CAT1]gsm read msg sub id: 15
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:39][CAT1]Send Data To Server[198][201] ...

2025-07-31 22:44:29:339 ==>>  ->:
0063B98C113311331133113311331B88BE28AFFC614A3A8CEE2FD83693102174438139FD8F41914579822DD285342C5C1B6B4B08932B54C41C6F7DA1F3E2B7B92D8D5E6F773535074A57274D24543AD774080764626611C02366B7A7F6DDA55A74ABF2
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 22:44:29:671 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:44:30:250 ==>> [D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:44:30:807 ==>> [D][05:18:40][COMM]crc 108B
[D][05:18:40][COMM]flash test ok


2025-07-31 22:44:31:374 ==>> [D][05:18:41][COMM]52233 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:44:31:574 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:44:31:579 ==>> 检测【打开喇叭声音】
2025-07-31 22:44:31:603 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:44:31:662 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 22:44:32:345 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:41][COMM]file:A20 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:41][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:41][COMM]file:A20 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:41][COMM]read file, len:15228, num:4
[D][05:18:41][COMM]--->crc16:0x419c
[D][05:18:41][COMM]read file success
[W][05:18:41][COMM][Audio].l:[936].close hexlog save
[D][05:18:41][COMM]accel parse set 1
[D][05:18:41][COMM][Audio]mon:9,05:18:41
[D][05:18:41][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m

2025-07-31 22:44:32:450 ==>> _audio_start].l:[691].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4

2025-07-31 22:44:32:555 ==>> , len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:42][COMM]53244 imu init OK


2025-07-31 22:44:32:623 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:44:32:629 ==>> 检测【打开大灯控制】
2025-07-31 22:44:32:634 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:44:32:767 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:44:32:948 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:44:32:963 ==>> 检测【关闭仪表供电3】
2025-07-31 22:44:32:968 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:44:33:100 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:43][COMM]set POWER 0


2025-07-31 22:44:33:248 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:44:33:257 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:44:33:285 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:44:33:386 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:44:33:534 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:44:33:540 ==>> 检测【读大灯电压】
2025-07-31 22:44:33:558 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:44:33:707 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]arm_hub read adc[5],val[33270]


2025-07-31 22:44:33:822 ==>> 【读大灯电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:44:33:843 ==>> 检测【关闭大灯控制2】
2025-07-31 22:44:33:850 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:44:33:966 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:44:34:104 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:44:34:112 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:44:34:135 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:44:34:506 ==>> [D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][PROT]index:1 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:6
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:2
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900007]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:44][COMM]arm_hub read adc[5],val[92]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][CAT1

2025-07-31 22:44:34:596 ==>> ]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BED853429138214BA9C902FDBF548C0043BD296B548CF00B0B5F817ED2CF6E6A636A23884CE7C85A934E0122C71D1F2100A617AD683643D758D1FCDBD4BCBFB730CD6E346F012325E671848002959D55D355D4
[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][05:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]


2025-07-31 22:44:34:630 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:44:34:640 ==>> 检测【打开WIFI(4)】
2025-07-31 22:44:34:661 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:44:34:839 ==>> [D][05:18:44][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:44][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:44:34:973 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:44:34:980 ==>> 检测【EC800M模组版本】
2025-07-31 22:44:34:990 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:44:35:206 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:45][CAT1]gsm read msg sub id: 12
[D][05:18:45][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:44:35:311 ==>> [D][05:18:45][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_O

2025-07-31 22:44:35:341 ==>> CPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:45][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:44:35:514 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:44:35:522 ==>> 检测【配置蓝牙地址】
2025-07-31 22:44:35:532 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:44:35:702 ==>> +WIFISCAN:4,0,F88C21BCF57D,-33
+WIFISCAN:4,1,CC057790A621,-61
+WIFISCAN:4,2,44A1917CAD81,-78
+WIFISCAN:4,3,CC057790A5C1,-81

[D][05:18:45][CAT1]wifi scan report total[4]
[W][05:18:45][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:45][COMM]read battery soc:255


2025-07-31 22:44:35:717 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C2986729C1B3>】
2025-07-31 22:44:35:792 ==>> [D][05:18:45][COMM]56759 imu init OK
[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:44:35:897 ==>> recv ble 1
recv ble 2
ble set mac ok :c2,98,67,29,c1,b3
enable filters ret : 0

2025-07-31 22:44:35:942 ==>> [D][05:18:45][GNSS]recv submsg id[3]


2025-07-31 22:44:35:991 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:44:35:997 ==>> 检测【BLETEST】
2025-07-31 22:44:36:002 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:44:36:109 ==>> 4A A4 01 A4 4A 


2025-07-31 22:44:36:214 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C2986729C1B3*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C2986729C1B399999

2025-07-31 22:44:36:304 ==>> OVER 150


2025-07-31 22:44:36:807 ==>> [D][05:18:46][COMM]57771 imu init OK
[D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:44:37:031 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:44:37:038 ==>> 该项需要延时执行
2025-07-31 22:44:37:437 ==>> [D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:47][COMM]accel parse set 0
[D][05:18:47][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:44:37:692 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:44:37:797 ==>> [D][05:18:47][COMM]58783 imu init OK


2025-07-31 22:44:39:730 ==>> [D][05:18:49][PROT]CLEAN,SEND:1
[D][05:18:49][PROT]index:1 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:6
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:1
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900007]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88BE649A3EE743E9D386699FCEF18C5

2025-07-31 22:44:39:835 ==>> 4B094BFD3392C61823FA70544AF3E18AE8DDC6364F03715167FE8E9794E0C770135C865F5673AC74E1AF164C4AB1E13E8C61EC558A73308B269587B39680C13A63DB1F96A
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]
                                         

2025-07-31 22:44:41:715 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 22:44:43:728 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 22:44:44:967 ==>> [D][05:18:54][PROT]CLEAN,SEND:1
[D][05:18:54][PROT]CLEAN:1
[D][05:18:54][PROT]index:0 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:5
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:2
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900006]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]Send Data To Server[198][201] ... ->:
00

2025-07-31 22:44:45:042 ==>> 63B98E113311331133113311331B88B34130B577D86C7B777E4FABF4703DE5215236F205EAAE87464ABE479F039720AD643CE7E7150357D72C67A447D0FCF557A98F93096961BC521D7A978645CAD2F20EB248DFF2B790865E7C256350037B1B9BE0
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]


2025-07-31 22:44:45:725 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:44:47:034 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:44:47:040 ==>> 检测【检测WiFi结果】
2025-07-31 22:44:47:051 ==>> WiFi信号:【F88C21BCF57D】,信号值:-38
2025-07-31 22:44:47:057 ==>> WiFi信号:【F42A7D1297A3】,信号值:-68
2025-07-31 22:44:47:062 ==>> WiFi信号:【44A1917CAD80】,信号值:-79
2025-07-31 22:44:47:081 ==>> WiFi信号:【CC057790A5C0】,信号值:-82
2025-07-31 22:44:47:088 ==>> WiFi信号:【CC057790A621】,信号值:-61
2025-07-31 22:44:47:098 ==>> WiFi信号:【74C330CCAB10】,信号值:-72
2025-07-31 22:44:47:114 ==>> WiFi信号:【44A1917CAD81】,信号值:-78
2025-07-31 22:44:47:119 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 22:44:47:126 ==>> WiFi数量【8】, 最大信号值:-38
2025-07-31 22:44:47:136 ==>> 检测【检测GPS结果】
2025-07-31 22:44:47:150 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 22:44:47:163 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【38】
北斗星号:【39】,信号值:【39】
北斗星号:【3】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【16】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【10】,信号值:【36】
北斗星号:【25】,信号值:【39】
北斗星号:【1】,信号值:【36】
北斗星号:【34】,信号值:【40】
北斗星号:【60】,信号值:【39】
北斗星号:【41】,信号值:【40】
北斗星号:【43】,信号值:【37】
北斗星号:【33】,信号值:【37】
北斗星号:【24】,信号值:【37】
北斗星号:【11】,信号值:【37】
北斗星号:【12】,信号值:【35】

2025-07-31 22:44:47:174 ==>> 检测【CSQ强度】
2025-07-31 22:44:47:195 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:44:47:220 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:57][CAT1]gsm read msg sub id: 12
[D][05:18:57][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:57][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:57][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:44:47:345 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 22:44:47:351 ==>> 检测【关闭GSM联网】
2025-07-31 22:44:47:360 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:44:47:510 ==>> [D][05:18:57][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:57][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:57][COMM]GSM test
[D][05:18:57][COMM]GSM test disable


2025-07-31 22:44:47:619 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:44:47:626 ==>> 检测【4G联网测试】
2025-07-31 22:44:47:635 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:44:48:571 ==>> [D][05:18:57][COMM]read battery soc:255
[W][05:18:57][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:57][COMM]Main Task receive event:14
[D][05:18:57][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955137, allstateRepSeconds = 0
[D][05:18:57][COMM]index:0,power_mode:0xFF
[D][05:18:57][COMM]index:1,sound_mode:0xFF
[D][05:18:57][COMM]index:2,gsensor_mode:0xFF
[D][05:18:57][COMM]index:3,report_freq_mode:0xFF
[D][05:18:57][COMM]index:4,report_period:0xFF
[D][05:18:57][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:57][COMM]index:6,normal_reset_period:0xFF
[D][05:18:57][COMM]index:7,spock_over_speed:0xFF
[D][05:18:57][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:57][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:57][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:57][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:57][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:57][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:57][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:57][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:57][COMM]index:16,imu_config_params:0xFF
[D][05:18:57][COMM]index:17,long_connect_params:0xFF
[D][05:18:57][COMM]index:18,detain_mark:0xFF
[D][05:18:57][COMM

2025-07-31 22:44:48:676 ==>> ]index:19,lock_pos_report_count:0xFF
[D][05:18:57][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:57][COMM]index:21,mc_mode:0xFF
[D][05:18:57][COMM]index:22,S_mode:0xFF
[D][05:18:57][COMM]index:23,overweight:0xFF
[D][05:18:57][COMM]index:24,standstill_mode:0xFF
[D][05:18:57][COMM]index:25,night_mode:0xFF
[D][05:18:57][COMM]index:26,experiment1:0xFF
[D][05:18:57][COMM]index:27,experiment2:0xFF
[D][05:18:57][COMM]index:28,experiment3:0xFF
[D][05:18:57][COMM]index:29,experiment4:0xFF
[D][05:18:57][COMM]index:30,night_mode_start:0xFF
[D][05:18:57][COMM]index:31,night_mode_end:0xFF
[D][05:18:57][COMM]index:33,park_report_minutes:0xFF
[D][05:18:57][COMM]index:34,park_report_mode:0xFF
[D][05:18:57][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:57][COMM]index:38,charge_battery_para: FF
[D][05:18:57][COMM]index:39,multirider_mode:0xFF
[D][05:18:57][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:57][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:57][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:57][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:57][COMM]index:44,riding_duration_config:0xFF
[D][05:18:57][COMM]index:45,camera_park_angle_cf

2025-07-31 22:44:48:781 ==>> g:0xFF
[D][05:18:57][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:57][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:57][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:57][COMM]index:49,mc_load_startup:0xFF
[D][05:18:57][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:57][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:57][COMM]index:52,traffic_mode:0xFF
[D][05:18:57][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:57][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:57][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:57][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:57][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:57][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:57][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:57][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:57][COMM]index:63,experiment5:0xFF
[D][05:18:57][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:57][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:57][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:57][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:57][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:57][COMM]index:70,camera_park_lig

2025-07-31 22:44:48:886 ==>> ht_cfg:0xFF
[D][05:18:57][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:57][COMM]index:72,experiment6:0xFF
[D][05:18:57][COMM]index:73,experiment7:0xFF
[D][05:18:57][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:57][COMM]index:75,zero_value_from_server:-1
[D][05:18:57][COMM]index:76,multirider_threshold:255
[D][05:18:57][COMM]index:77,experiment8:255
[D][05:18:57][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:57][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:57][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:57][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:57][COMM]index:83,loc_report_interval:255
[D][05:18:57][COMM]index:84,multirider_threshold_p2:255
[D][05:18:57][COMM]index:85,multirider_strategy:255
[D][05:18:57][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:57][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:57][COMM]index:90,weight_param:0xFF
[D][05:18:57][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:57][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:57][COMM]index:95,current_limit:0xFF
[D][05:18:57][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0x

2025-07-31 22:44:48:991 ==>> FF 
[D][05:18:57][COMM]index:100,location_mode:0xFF

[W][05:18:57][PROT]remove success[1629955137],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:57][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:57][PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:8
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900009]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[W][05:18:57][PROT]add success [1629955137],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:57][CAT1]gsm read msg sub id: 13
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[294]
[D][05:18:57]

2025-07-31 22:44:49:096 ==>> [SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:57][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:57][CAT1]exec over: func id: 13, ret: 21
[D][05:18:57][M2M ]get csq[25]
[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:57][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B138B00A952E74869EF338B2FAA1B32DEDDA207677711D1CEF0DEE3FB7C110B1A8C498536EB9E05B5B8433BC07B8E71350CC5C0764B25729471678CAC416205638FD41A22A6C1ACD584AB007C07D1D12C0DD21E439AD976034708D1B899D2D16C271ADDA856D86E90345BC6E8BE118BE126F2EB9431239F12D18F1BFB286AA15D689BC
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 22:44:49:186 ==>> [D][05:18:57][PROT]M2M Send ok [1629955137]
>>>>>RESEND ALLSTATE<<<<<
[W][05:18:58][PROT]remove success[1629955138],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:18:58][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:58][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:58][PROT]add success [1629955138],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:18:58][COMM]------>period, report file manifest
[D][05:18:58][COMM]Main Task receive event:14 finished processing
[D][05:18:58][CAT1]gsm read msg sub id: 21
[D][05:18:58][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]cell info report total[0]
[D][05:18:58][CAT1]exec over: func id: 21, ret: 6


2025-07-31 22:44:49:652 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:44:49:659 ==>> 检测【关闭GPS】
2025-07-31 22:44:49:683 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:44:49:805 ==>> [D][05:18:59][COMM]read battery soc:255
[W][05:18:59][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:59][GNSS]stop locating
[D][05:18:59][GNSS]all continue location stop
[W][05:18:59][GNSS]stop locating
[D][05:18:59][GNSS]all sing location stop


2025-07-31 22:44:49:930 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:44:49:937 ==>> 检测【清空消息队列2】
2025-07-31 22:44:49:946 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:44:50:109 ==>> [D][05:19:00][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:44:50:203 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:44:50:209 ==>> 检测【轮动检测】
2025-07-31 22:44:50:229 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:44:50:308 ==>> 3A A3 01 00 A3 


2025-07-31 22:44:50:413 ==>> OFF_OUT1
OVER 150


2025-07-31 22:44:50:473 ==>> [D][05:19:00][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 22:44:50:716 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:44:50:809 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:44:51:008 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:44:51:015 ==>> 检测【关闭小电池】
2025-07-31 22:44:51:022 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:44:51:102 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:44:51:287 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:44:51:295 ==>> 检测【进入休眠模式】
2025-07-31 22:44:51:320 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:44:51:571 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:01][COMM]Main Task receive event:28
[D][05:19:01][COMM]main task tmp_sleep_event = 8
[D][05:19:01][COMM]prepare to sleep
[D][05:19:01][CAT1]gsm read msg sub id: 12
[D][05:19:01][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:44:51:752 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 22:44:52:177 ==>> [D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 12, ret: 6
[D][05:19:01][M2M ]tcpclient close[4]
[D][05:19:01][SAL ]Cellular task submsg id[12]
[D][05:19:01][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:01][CAT1]gsm read msg sub id: 9
[D][05:19:01][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:02][CAT1]<<< 
OK

[D][05:19:02][CAT1]exec over: func id: 9, ret: 6
[D][05:19:02][CAT1]sub id: 9, ret: 6

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:02][SAL ]socket close ind. id[4]
[D][05:19:02][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:02][COMM]1x1 frm_can_tp_send ok
[D][05:19:02][CAT1]pdpdeact urc len[22]


2025-07-31 22:44:52:437 ==>> [E][05:19:02][COMM]1x1 rx timeout
[D][05:19:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:44:52:943 ==>> [E][05:19:02][COMM]1x1 rx timeout
[E][05:19:02][COMM]1x1 tp timeout
[E][05:19:02][COMM]1x1 error -3.
[W][05:19:02][COMM]CAN STOP!
[D][05:19:02][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:02][COMM]------------ready to Power off Acckey 1------------
[D][05:19:02][COMM]------------ready to Power off Acckey 2------------
[D][05:19:02][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:02][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1289
[D][05:19:02][COMM]bat sleep fail, reason:-1
[D][05:19:02][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:02][COMM]accel parse set 0
[D][05:19:02][COMM]imu rest ok. 73827
[D][05:19:02][COMM]imu sleep 0
[W][05:19:02][COMM]now sleep


2025-07-31 22:44:53:127 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:44:53:134 ==>> 检测【检测33V休眠电流】
2025-07-31 22:44:53:148 ==>> 开始33V电流采样
2025-07-31 22:44:53:159 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:44:53:233 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:44:54:246 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:44:54:291 ==>> Current33V:????:16.90

2025-07-31 22:44:54:748 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:44:54:756 ==>> 【检测33V休眠电流】通过,【16.9uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:44:54:780 ==>> 该项需要延时执行
2025-07-31 22:44:56:766 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:44:56:777 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:44:56:787 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:44:56:921 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1646mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 2046mV
Get AD_V7 1108mV
OVER 150


2025-07-31 22:44:57:809 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:44:57:820 ==>> 检测【打开小电池2】
2025-07-31 22:44:57:838 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:44:57:907 ==>> 6A A6 01 A6 6A 


2025-07-31 22:44:58:012 ==>> Battery ON
OVER 150


2025-07-31 22:44:58:120 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:44:58:131 ==>> 该项需要延时执行
2025-07-31 22:44:58:628 ==>> 此处延时了:【500】毫秒
2025-07-31 22:44:58:639 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:44:58:666 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:44:58:706 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:44:58:927 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:44:58:950 ==>> 该项需要延时执行
2025-07-31 22:44:59:054 ==>> [D][05:19:08][COMM]------------ready to Power on Acckey 1------------
[D][05:19:08][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 4,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 5,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 6,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 7,volt = 10
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 8,volt = 10
[D][05:19:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 10
[D][05:19:08][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:08][COMM]----- get Acckey 1 and value:1------------
[W][05:19:08][COMM]CAN START!
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:08][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 79848
[D][05:19:08][COMM][Audio]exec status ready.
[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:1

2025-07-31 22:44:59:099 ==>> 9:08][COMM]imu wakeup ok. 79862
[D][05:19:08][COMM]imu wakeup 1
[W][05:19:08][COMM]wake up system, wakeupEvt=0x80
[D][05:19:08][COMM]frm_can_weigth_power_set 1
[D][05:19:08][COMM]Clear Sleep Block Evt
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:44:59:432 ==>> 此处延时了:【500】毫秒
2025-07-31 22:44:59:443 ==>> 检测【进入休眠模式2】
2025-07-31 22:44:59:466 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:44:59:477 ==>> [E][05:19:09][COMM]1x1 rx timeout
[D][05:19:09][COMM]1x1 frm_can_tp_send ok
[D][05:19:09][COMM]msg 02A0 loss. last_tick:79833. cur_tick:80342. period:50
[D][05:19:09][COMM]msg 02A4 loss. last_tick:79833. cur_tick:80342. period:50
[D][05:19:09][COMM]msg 02A5 loss. last_tick:79833. cur_tick:80343. period:50
[D][05:19:09][COMM]msg 02A6 loss. last_tick:79833. cur_tick:80343. period:50
[D][05:19:09][COMM]msg 02A7 loss. last_tick:79833. cur_tick:80344. period:50
[D][05:19:09][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 80344
[D][05:19:09][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 80344


2025-07-31 22:44:59:567 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:44:59:792 ==>> [E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[D][05:19:09][COMM]Main Task receive event:28 finished processing
[D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:45:00:092 ==>> [D][05:19:09][COMM]msg 0220 loss. last_tick:79833. cur_tick:80838. period:100
[D][05:19:09][COMM]msg 0221 loss. last_tick:79833. cur_tick:80838. period:100
[D][05:19:09][COMM]msg 0224 loss. last_tick:79833. cur_tick:80839. period:100
[D][05:19:09][COMM]msg 0260 loss. last_tick:79833. cur_tick:80839. period:100
[D][05:19:09][COMM]msg 0280 loss. last_tick:79833. cur_tick:80839. period:100
[D][05:19:09][COMM]msg 02C0 loss. last_tick:79833. cur_tick:80840. period:100
[D][05:19:09][COMM]msg 02C1 loss. last_tick:79833. cur_tick:80840. period:100
[D][05:19:09][COMM]msg 02C2 loss. last_tick:79833. cur_tick:80841. period:100
[D][05:19:09][COMM]msg 02E0 loss. last_tick:79833. cur_tick:80841. period:100
[D][05:19:09][COMM]msg 02E1 loss. last_tick:79833. cur_tick:80841. period:100
[D][05:19:09][COMM]msg 02E2 loss. last_tick:79833. cur_tick:80842. period:100
[D][05:19:09][COMM]msg 0300 loss. last_tick:79833. cur_tick:80842. period:100
[D][05:19:09][COMM]msg 0301 loss. last_tick:79833. cur_tick:80842. period:100
[D][05:19:09][COMM]bat msg 0240 loss. last_tick:79834. cur_tick:80843. period:100. j,i:1 54
[D][05:19:09][COMM]bat msg 0241 loss. last_tick:79834. cur_tick:80843. period:100. j,i:2 55
[D][05:19:09][COMM]bat msg 0242 loss.

2025-07-31 22:45:00:197 ==>>  last_tick:79834. cur_tick:80843. period:100. j,i:3 56
[D][05:19:09][COMM]bat msg 0244 loss. last_tick:79834. cur_tick:80844. period:100. j,i:5 58
[D][05:19:09][COMM]bat msg 024E loss. last_tick:79834. cur_tick:80844. period:100. j,i:15 68
[D][05:19:09][COMM]bat msg 024F loss. last_tick:79834. cur_tick:80845. period:100. j,i:16 69
[D][05:19:09][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 80845
[D][05:19:09][COMM]CAN message bat fault change: 0x00000000->0x0001802E 80846
[D][05:19:09][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 80846
                                                                              

2025-07-31 22:45:00:378 ==>> [D][05:19:10][COMM]msg 0222 loss. last_tick:79833. cur_tick:81340. period:150
[D][05:19:10][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 81341


2025-07-31 22:45:00:467 ==>>                                                                                     COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------


2025-07-31 22:45:00:677 ==>> [E][05:19:10][COMM]1x1 rx timeout
[E][05:19:10][COMM]1x1 tp timeout
[E][05:19:10][COMM]1x1 error -3.
[W][05:19:10][COMM]CAN STOP!
[D][05:19:10][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 1------------
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------
[D][05:19:10][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:10][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 101
[D][05:19:10][COMM]bat sleep fail, reason:-1
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:10][COMM]accel parse set 0
[D][05:19:10][COMM]imu rest ok. 81530
[D][05:19:10][COMM]imu sleep 0
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:10][COMM]now sleep


2025-07-31 22:45:00:728 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:45:00:742 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:45:00:772 ==>> 开始小电池电流采样
2025-07-31 22:45:00:779 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:45:00:842 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:45:01:854 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:45:01:929 ==>> CurrentBattery:ƽ��:69.28

2025-07-31 22:45:02:362 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:45:02:373 ==>> 【检测小电池休眠电流】通过,【69.28uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:45:02:395 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:45:02:402 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:45:02:516 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:45:02:669 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:45:02:682 ==>> 该项需要延时执行
2025-07-31 22:45:02:726 ==>> [D][05:19:12][COMM]------------ready to Power on Acckey 1------------
[D][05:19:12][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:12][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:12][COMM]----- get Acckey 1 and value:1------------
[W][05:19:12][COMM]CAN START!
[D][05:19:12][COMM]read battery soc:0
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:12][COMM]CAN message bat fault change: 0x0001802E->0x00000000 83570
[D][05:19:12][COMM][Audio]exec status ready.
[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]imu wakeup ok. 83584
[D][05:19:12][COMM]imu wakeup 1
[W][05:19:12][COMM]wake up system, wakeupEvt=0x80
[D][05:19:12][COMM]frm_can_weigth_power_set 1
[D][05:19:12][COMM]Clear Sleep Block Evt
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:45:03:013 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:45:03:178 ==>> 此处延时了:【500】毫秒
2025-07-31 22:45:03:191 ==>> 检测【检测唤醒】
2025-07-31 22:45:03:216 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:45:03:231 ==>> [D][05:19:13][COMM]msg 02A0 loss. last_tick:83553. cur_tick:84064. period:50
[D][05:19:13][COMM]msg 02A4 loss. last_tick:83553. cur_tick:84065. period:50
[D][05:19:13][COMM]msg 02A5 loss. last_tick:83553. cur_tick:84065. period:50
[D][05:19:13][COMM]msg 02A6 loss. last_tick:83553. cur_tick:84066. period:50
[D][05:19:13][COMM]msg 02A7 loss. last_tick:83553. cur_tick:84066. period:50
[D][05:19:13][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 84066
[D][05:19:13][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 84067
[D][05:19:13][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 22:45:03:954 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:13][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[D][05:19:13][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:13][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:13][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:13][FCTY]DeviceID    = 460130020290267
[D][05:19:13][FCTY]HardwareID  = 867222087519304
[D][05:19:13][FCTY]MoBikeID    = 9999999999
[D][05:19:13][FCTY]LockID      = FFFFFFFFFF
[D][05:19:13][FCTY]BLEFWVersion= 105
[D][05:19:13][FCTY]BLEMacAddr   = C2986729C1B3
[D][05:19:13][FCTY]Bat         = 3864 mv
[D][05:19:13][FCTY]Current     = 0 ma
[D][05:19:13][FCTY]VBUS        = 2600 mv
[D][05:19:13][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:13][FCTY]Ext battery vol = 32, adc = 1287
[D][05:19:13][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 25 mv
[D][05:19:13][FCTY]Bike Type flag is invalied
[D][05:19:13][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:13][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:13][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:13][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:13][FCTY]

2025-07-31 22:45:04:059 ==>> Bat1         = 3849 mv
[D][05:19:13][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[D][05:19:13][COMM]Main Task receive event:28 finished processing
[D][05:19:13][COMM]Main Task receive event:65
[D][05:19:13][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:13][COMM]Main Task receive event:65 finished processing
[D][05:19:13][COMM]Main Task receive event:60
[D][05:19:13][COMM]smart_helmet_vol=255,255
[D][05:19:13][COMM]report elecbike
[W][05:19:13][PROT]remove success[1629955153],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:13][COMM]Main Task receive event:60 finished processing
[D][05:19:13][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:13][PROT]index:0
[D][05:19:13][PROT]is_send:1
[D][05:19:13][PROT]sequence_num:10
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:3
[D][05:19:13][PROT]send_path:0x3
[D][05:19:13][PROT]msg_type:0x5d03
[D][05:19:

2025-07-31 22:45:04:164 ==>> 13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][PROT]Sending traceid[999999999990000B]
[D][05:19:13][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:13][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:13][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:13][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:13][SAL ]open socket ind id[4], rst[0]
[D][05:19:13][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:13][SAL ]Cellular task submsg id[8]
[D][05:19:13][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:13][CAT1]gsm read msg sub id: 8
[D][05:19:13][CAT1]tx re

2025-07-31 22:45:04:269 ==>> t[11] >>> AT+CGATT?

[D][05:19:13][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:13][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:13][CAT1]<<< 
+CME ERROR: 100

[D][05:19:13][COMM]msg 0220 loss. last_tick:83552. cur_tick:84562. period:100
[D][05:19:13][COMM]msg 0221 loss. last_tick:83552. cur_tick:84562. period:100
[D][05:19:13][COMM]msg 0224 loss. last_tick:83552. cur_tick:84563. period:100
[D][05:19:13][COMM]msg 0260 loss. last_tick:83553. cur_tick:84563. period:100
[D][05:19:13][COMM]msg 0280 loss. last_tick:83553. cur_tick:84563. period:100
[D][05:19:13][COMM]msg 02C0 loss. last_tick:83553. cur_tick:84564. period:100
[D][05:19:13][COMM]msg 02C1 loss. last_tick:83553. cur_tick:84564. period:100
[D][05:19:13][COMM]msg 02C2 loss. last_tick:83553. cur_tick:84565. period:100
[D][05:19:13][COMM]msg 02E0 loss. last_tick:83553. cur_tick:84565. period:100
[D][05:19:13][COMM]msg 02E1 loss. last_tick:83553. cur_tick:84565. period:100
[D][05:19:13][COMM]msg 02E2 loss. last_tick:83553. cur_tick:84566. period:100
[D][05:19:13][COMM]msg 0300 loss. last_tick:83553. cur_tick:84566. period:100
[D][05:19:13][COMM]msg 0301 loss. last_tic

2025-07-31 22:45:04:359 ==>> k:83553. cur_tick:84566. period:100
[D][05:19:13][COMM]bat msg 0240 loss. last_tick:83553. cur_tick:84567. period:100. j,i:1 54
[D][05:19:13][COMM]bat msg 0241 loss. last_tick:83553. cur_tick:84567. period:100. j,i:2 55
[D][05:19:13][COMM]bat msg 0242 loss. last_tick:83553. cur_tick:84567. period:100. j,i:3 56
[D][05:19:13][COMM]bat msg 0244 loss. last_tick:83553. cur_tick:84568. period:100. j,i:5 58
[D][05:19:13][COMM]bat msg 024E loss. last_tick:83553. cur_tick:84568. period:100. j,i:15 68
[D][05:19:13][COMM]bat msg 024F loss. last_tick:83553. cur_tick:84569. period:100. j,i:16 69
[D][05:19:13][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 84569
[D][05:19:13][COMM]CAN message bat fault change: 0x00000000->0x0001802E 84570
[D][05:19:13][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 84570


2025-07-31 22:45:04:444 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:45:04:452 ==>> 检测【关机】
2025-07-31 22:45:04:463 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:45:04:695 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:45:04:800 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:45:04:905 ==>>                                                                                                                                                                                                                                                                                     OMM]Main Task receive event:65 finished processing
[D][05:19:14][COMM]Main Task receive event:66
[D][05:19:14][COMM]Try to Auto Lock Bat
[D][05:19:14][COMM]Main Task receive event:66 finished processing
[D][05:19:14][COMM]Main Task receive event:60
[D][05:19:14][COMM]smart_helmet_vol=255,255
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]BAT CAN get state2 fail 204
[D][05:19:14][COMM]get soh error
[E][05:19:14][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:14][COMM]report elecbike
[W][05:19:14][PROT]remove success[1629955154],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:14][PRO

2025-07-31 22:45:05:010 ==>> T]add success [1629955154],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:14][COMM]Main Task receive event:60 finished processing
[D][05:19:14][COMM]Main Task receive event:61
[D][05:19:14][COMM][D301]:type:3, trace id:280
[D][05:19:14][COMM]id[], hw[000
[D][05:19:14][COMM]get mcMaincircuitVolt error
[D][05:19:14][COMM]get mcSubcircuitVolt error
[D][05:19:14][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:14][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:14][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:14][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:14][PROT]index:1
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:14][PROT]is_send:1
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:14][PROT]sequence_num:11
[D][05:19:14][PROT]retry_timeout:0


2025-07-31 22:45:05:115 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 22:45:05:220 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 22:45:05:310 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 22:45:05:462 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:45:05:740 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:15][COMM]arm_hub_enable: hub power: 0
[D][05:19:15][HSDK]hexlog index save 0 3328 220 @ 0 : 0
[D][05:19:15][HSDK]write save hexlog index [0]
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:45:06:213 ==>> [D][05:19:16][COMM]exit wheel stolen mode.
[D][05:19:16][COMM]Main Task receive event:68
[D][05:19:16][COMM]handlerWheelStolen evt type = 2.
[E][05:19:16][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:16][GNSS]stop locating
[D][05:19:16][GNSS]all continue location stop
[D][05:19:16][COMM]Main Task receive event:68 finished processing
[W][05:19:16][COMM]Power Off


2025-07-31 22:45:06:261 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:45:06:289 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:45:06:304 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:45:06:410 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:45:06:515 ==>> [D][05:19:16][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 2,volt = 19
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 3,volt =  

2025-07-31 22:45:06:535 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:45:06:543 ==>> 检测【检测小电池关机电流】
2025-07-31 22:45:06:549 ==>> 开始小电池电流采样
2025-07-31 22:45:06:577 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:45:06:650 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:45:07:659 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:45:07:689 ==>> CurrentBattery:ƽ��:68.00

2025-07-31 22:45:08:165 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:45:08:177 ==>> 【检测小电池关机电流】通过,【68uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:45:08:445 ==>> MES过站成功
2025-07-31 22:45:08:454 ==>> #################### 【测试结束】 ####################
2025-07-31 22:45:08:473 ==>> 关闭5V供电
2025-07-31 22:45:08:486 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:45:08:613 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:45:09:477 ==>> 关闭5V供电成功
2025-07-31 22:45:09:491 ==>> 关闭33V供电
2025-07-31 22:45:09:514 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:45:09:604 ==>> 5A A5 02 5A A5 


2025-07-31 22:45:09:709 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:45:10:480 ==>> 关闭33V供电成功
2025-07-31 22:45:10:492 ==>> 关闭3.7V供电
2025-07-31 22:45:10:505 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:45:10:603 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:45:11:483 ==>> 关闭3.7V供电成功
