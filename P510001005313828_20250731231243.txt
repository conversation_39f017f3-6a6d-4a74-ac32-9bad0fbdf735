2025-07-31 23:12:43:372 ==>> MES查站成功:
查站序号:P510001005313828验证通过
2025-07-31 23:12:43:384 ==>> 扫码结果:P510001005313828
2025-07-31 23:12:43:385 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:12:43:386 ==>> 测试参数版本:2024.10.11
2025-07-31 23:12:43:388 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:12:43:389 ==>> 检测【打开透传】
2025-07-31 23:12:43:391 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:12:43:527 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:12:43:733 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:12:43:738 ==>> 检测【检测接地电压】
2025-07-31 23:12:43:739 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:12:43:819 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:12:44:028 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:12:44:030 ==>> 检测【打开小电池】
2025-07-31 23:12:44:033 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:12:44:126 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:12:44:324 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:12:44:327 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:12:44:330 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:12:44:415 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:12:44:609 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:12:44:613 ==>> 检测【等待设备启动】
2025-07-31 23:12:44:617 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:12:44:861 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:12:45:042 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:12:45:640 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:12:45:686 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:12:45:746 ==>>    [05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:12:46:141 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:12:46:614 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:12:46:694 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:12:46:697 ==>> 检测【产品通信】
2025-07-31 23:12:46:699 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:12:46:903 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:12:46:966 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:12:46:968 ==>> 检测【初始化完成检测】
2025-07-31 23:12:46:970 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:12:47:144 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:12:47:240 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:12:47:243 ==>> 检测【关闭大灯控制1】
2025-07-31 23:12:47:244 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:12:47:251 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:12:47:355 ==>> [

2025-07-31 23:12:47:385 ==>> W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:12:47:509 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:12:47:512 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:12:47:514 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:12:47:731 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:12:47:779 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:12:47:782 ==>> 检测【关闭仪表供电】
2025-07-31 23:12:47:783 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:12:47:835 ==>>                                                                                                                                                                                                                                                                                 ity[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:12:47:940 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:12:48:056 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:12:48:058 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:12:48:059 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:12:48:183 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:12:48:345 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:12:48:348 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:12:48:368 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:12:48:477 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:12:48:633 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:12:48:636 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:12:48:638 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:12:48:687 ==>> [D][05:17:52][COMM]3636 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:12:48:793 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:12:48:943 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:12:48:946 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:12:48:948 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:12:49:023 ==>> 5A A5 01 5A A5 


2025-07-31 23:12:49:113 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:12:49:203 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31


2025-07-31 23:12:49:232 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:12:49:234 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:12:49:235 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:12:49:263 ==>>           53][COMM]read battery soc:255


2025-07-31 23:12:49:323 ==>> 5A A5 03 5A A5 


2025-07-31 23:12:49:413 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 23:12:49:515 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:12:49:519 ==>> 该项需要延时执行
2025-07-31 23:12:49:683 ==>> [D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:12:50:227 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 23:12:50:813 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:12:51:279 ==>>                                                                                                                                                                                          --
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].

2025-07-31 23:12:51:384 ==>> echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]

2025-07-31 23:12:51:489 ==>> 
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1

2025-07-31 23:12:51:564 ==>>  Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
                                         

2025-07-31 23:12:51:729 ==>> [D][05:17:55][COMM]6669 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:12:51:804 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:12:52:723 ==>> [D][05:17:56][COMM]7680 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:12:53:260 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:12:53:519 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:12:53:522 ==>> 检测【33V输入电压ADC】
2025-07-31 23:12:53:525 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:12:53:838 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:17:57][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read left brake adc:5  volt:6 mv
[D][05:17:57][COMM]adc read right brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read throttle adc:12  volt:15 mv
[D][05:17:57][COMM]adc read battery ts volt:8 mv
[D][05:17:57][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:17:57][COMM]8691 imu init OK
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 23:12:54:132 ==>> 【33V输入电压ADC】通过,【32552mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:12:54:135 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:12:54:136 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:12:54:233 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1647mV
Get AD_V4 1mV
Get AD_V5 2770mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:12:54:410 ==>> 【TP7_VCC3V3(ADV2)】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:54:412 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:12:54:429 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:54:431 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:12:54:435 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 23:12:54:448 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:12:54:451 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:12:54:467 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:12:54:469 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:12:54:492 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:12:54:494 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:12:54:624 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1647mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1988mV
Get AD_V7 1091mV
OVER 150


2025-07-31 23:12:54:729 ==>> [D][05:17:58][COMM]9702 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:12:54:769 ==>> 【TP7_VCC3V3(ADV2)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:54:771 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:12:54:796 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:54:798 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:12:54:800 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 23:12:54:825 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:12:54:827 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:12:54:855 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:12:54:857 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:12:54:894 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:12:54:896 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:12:55:034 ==>> 1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1650mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1989mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:12:55:109 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 23:12:55:178 ==>> 【TP7_VCC3V3(ADV2)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:55:181 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:12:55:197 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:55:200 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:12:55:202 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 23:12:55:217 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:12:55:220 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:12:55:236 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:12:55:238 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:12:55:259 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:12:55:262 ==>> 检测【打开WIFI(1)】
2025-07-31 23:12:55:264 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:12:55:276 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:12:55:381 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:12:55:486 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:12:55:544 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:12:55:548 ==>> 检测【清空消息队列(1)】
2025-07-31 23:12:55:552 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:12:55:957 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10713 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished pro

2025-07-31 23:12:56:016 ==>> cessing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 23:12:56:111 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:12:56:138 ==>> 检测【打开GPS(1)】
2025-07-31 23:12:56:140 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:12:56:445 ==>>                                              CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087618403

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539533

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:12:56:644 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:12:56:647 ==>> 检测【打开GSM联网】
2025-07-31 23:12:56:651 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:12:56:808 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 23:12:56:916 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:12:56:919 ==>> 检测【打开仪表供电1】
2025-07-31 23:12:56:921 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:12:57:113 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:12:57:192 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:12:57:195 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:12:57:198 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:12:57:435 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:12:57:468 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:12:57:481 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:12:57:483 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:12:57:600 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33409]


2025-07-31 23:12:57:738 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:12:57:742 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:12:57:745 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:12:57:915 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:12:58:023 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:12:58:026 ==>> 检测【AD_V20电压】
2025-07-31 23:12:58:037 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:12:58:127 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:12:58:218 ==>> 本次取值间隔时间:83ms
2025-07-31 23:12:58:221 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:12:58:550 ==>> 本次取值间隔时间:326ms
2025-07-31 23:12:58:569 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:12:58:674 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:12:58:779 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13725 imu init OK


2025-07-31 23:12:58:824 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:12:58:914 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:12:59:112 ==>> 本次取值间隔时间:428ms
2025-07-31 23:12:59:133 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:12:59:249 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:12:59:323 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
1A A1 10 00 00 
Get AD_V20 1639mV
OVER 150


2025-07-31 23:12:59:428 ==>> 本次取值间隔时间:179ms
2025-07-31 23:12:59:461 ==>> 【AD_V20电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:12:59:464 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:12:59:467 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:12:59:522 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:12:59:627 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03]

2025-07-31 23:12:59:732 ==>> [CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 23:12:59:741 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:12:59:744 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:12:59:748 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:13:00:049 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       3][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_D

2025-07-31 23:13:00:154 ==>> ISPLAY=GPIO,0<<<<<
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:13:00:281 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:13:00:285 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:13:00:306 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:13:00:425 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:13:00:563 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:13:00:592 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:13:00:597 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:13:00:757 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:13:00:839 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:13:00:842 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:13:00:844 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:13:01:012 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:13:01:122 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:13:01:126 ==>> 检测【AD_V21电压】
2025-07-31 23:13:01:149 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:13:01:222 ==>> 1A A1 20 00 00 
Get AD_V21 1636mV
OVER 150


2025-07-31 23:13:01:389 ==>> 本次取值间隔时间:257ms
2025-07-31 23:13:01:404 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:13:01:408 ==>> 【AD_V21电压】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:13:01:410 ==>> 检测【关闭仪表供电2】
2025-07-31 23:13:01:414 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:13:01:676 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,40,,,42,39,,,41,25,,,40,34,,,40,1*79

$GBGSV,3,2,11,41,,,40,59,,,40,33,,,38,10,,,41,1*71

$GBGSV,3,3,11,60,,,40,11,,,36,23,,,36,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1664.160,1664.160,53.142,2097152,2097152,2097152*4E

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:13:01:766 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:13:01:935 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:13:01:938 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:13:01:941 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:13:02:116 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:13:02:243 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:13:02:246 ==>> 检测【打开AccKey2供电】
2025-07-31 23:13:02:250 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:13:02:407 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:13:02:512 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,40,,,42,39,,,41,34,,,41,59,,,41,1*71

$GBGSV,4,2,15,60,,,41,25,,,40,41,,,40,11,,,40,1*71

$GBGSV,4,3,15,23,,,3

2025-07-31 23:13:02:541 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:13:02:545 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:13:02:549 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:02:574 ==>> 9,10,,,38,7,,,38,33,,,37,1*4C

$GBGSV,4,4,15,5,,,30,2,,,43,1,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1620.063,1620.063,51.824,2097152,2097152,2097152*45



2025-07-31 23:13:02:826 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:06][COMM]adc read out 24v adc:1305  volt:33007 mv
[D][05:18:06][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:06][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:06][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:06][COMM]adc read battery ts volt:8 mv
[D][05:18:06][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:06][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 23:13:03:076 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33007mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:13:03:079 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:13:03:082 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:13:03:278 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:13:03:368 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 23:13:03:379 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:13:03:400 ==>> 该项需要延时执行
2025-07-31 23:13:03:563 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,39,,,41,34,,,41,59,,,41,1*7D

$GBGSV,5,2,18,25,,,41,3,,,41,60,,,40,41,,,40,1*4F

$GBGSV,5,3,18,11,,,40,23,,,39,7,,,39,10,,,38,1*41

$GBGSV,5,4,18,43,,,38,1,,,37,33,,,36,2,,,34,1*77

$GBGSV,5,5,18,5,,,31,32,,,30,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1586.955,1586.955,50.789,2097152,2097152,2097152*4C



2025-07-31 23:13:04:587 ==>> $GBGGA,151308.432,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,39,,,41,34,,,41,59,,,41,1*77

$GBGSV,6,2,22,25,,,41,3,,,41,60,,,40,41,,,40,1*45

$GBGSV,6,3,22,11,,,40,7,,,40,23,,,39,43,,,39,1*42

$GBGSV,6,4,22,16,,,39,10,,,38,1,,,37,33,,,36,1*43

$GBGSV,6,5,22,9,,,35,2,,,34,5,,,32,24,,,32,1*4C

$GBGSV,6,6,22,32,,,31,6,,,17,1*45

$GBRMC,151308.432,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151308.432,0.000,1537.855,1537.855,49.322,2097152,2097152,2097152*54



2025-07-31 23:13:05:365 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 23:13:05:560 ==>> $GBGGA,151309.412,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,41,59,,,41,1*76

$GBGSV,6,2,23,25,,,41,3,,,41,60,,,40,41,,,40,1*44

$GBGSV,6,3,23,11,,,40,7,,,40,23,,,39,43,,,39,1*43

$GBGSV,6,4,23,16,,,39,10,,,38,1,,,37,33,,,36,1*42

$GBGSV,6,5,23,9,,,36,6,,,36,2,,,35,24,,,34,1*4E

$GBGSV,6,6,23,5,,,33,4,,,33,32,,,32,1*76

$GBRMC,151309.412,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151309.412,0.000,1575.419,1575.419,50.401,2097152,2097152,2097152*59



2025-07-31 23:13:06:387 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:13:06:391 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:13:06:395 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:06:568 ==>> $GBGGA,151310.392,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,41,25,,,41,1*7D

$GBGSV,6,2,23,3,,,41,59,,,40,60,,,40,41,,,40,1*4E

$GBGSV,6,3,23,11,,,40,7,,,40,43,,,39,16,,,39,1*45

$GBGSV,6,4,23,23,,,38,10,,,38,1,,,38,33,,,36,1*4A

$GBGSV,6,5,23,9,,,36,6,,,36,2,,,35,24,,,35,1*4F

$GBGSV,6,6,23,5,,,33,4,,,33,32,,,33,1*77

$GBRMC,151310.392,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151310.392,0.000,1577.212,1577.212,50.449,2097152,2097152,2097152*52



2025-07-31 23:13:06:838 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:10][COMM]adc read battery ts volt:7 mv
[D][05:18:10][COMM]adc read in 24v adc:1282  volt:32425 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
$GBGGA,151310.592,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,41,25,,,41,1*7D

$GBGSV,6,2,23,3,,,41,59,,,41,60,,,40,41,,,40,1*4F

$GBGSV,6,3,23,11,,,40,7,,,40,43,,,40,16,,,39,1*4B

$GBGSV,6,4,23,23,,,38,10,,,38,1,,,37,33,,,36,1*45

$GBGSV,6,5,23,9,,,36,6,,,36,2,,,35,24,,,35,1*4F

$GBGSV,6,6,23,5,,,33,4,,,33,32,,,33,1*77

$GBRMC,151310.592,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151310.592,0.000,1579.017,1579.017,50.510,2097152,2097152,2097152*59

[D][05:18:10][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv

2025-07-31 23:13:06:883 ==>> 
[D][05:18:10][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:13:06:924 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:13:06:927 ==>> 检测【打开AccKey1供电】
2025-07-31 23:13:06:931 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:13:07:110 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:13:07:204 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:13:07:208 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:13:07:212 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:13:07:320 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 23:13:07:380 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 23:13:07:455 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 23:13:07:474 ==>> 【读取AccKey1电压(ADV14)前】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:13:07:480 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:13:07:484 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:07:836 ==>> $GBGGA,151311.572,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,41,25,,,41,1*7D

$GBGSV,6,2,23,59,,,41,3,,,40,60,,,40,41,,,40,1*4E

$GBGSV,6,3,23,11,,,40,7,,,40,43,,,39,16,,,39,1*45

$GBGSV,6,4,23,23,,,39,10,,,38,1,,,38,33,,,36,1*4B

$GBGSV,6,5,23,9,,,36,6,,,36,2,,,35,24,,,35,1*4F

$GBGSV,6,6,23,5,,,33,4,,,33,32,,,33,1*77

$GBRMC,151311.572,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151311.572,0.000,1579.014,1579.014,50.506,2097152,2097152,2097152*51

[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:11][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:11][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:11][COMM]adc read battery ts volt:10 mv
[D][05:18:11][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:11][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read v

2025-07-31 23:13:07:881 ==>> bat adc:2412  volt:3886 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:13:08:005 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:13:08:009 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:13:08:013 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:13:08:213 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:13:08:277 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:13:08:280 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:13:08:285 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:13:08:423 ==>> 1A A1 00 40 00 
Get AD_V14 2666mV
OVER 150


2025-07-31 23:13:08:528 ==>> 原始值:【2666】, 乘以分压基数【2】还原值:【5332】
2025-07-31 23:13:08:547 ==>> 【读取AccKey1电压(ADV14)后】通过,【5332mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:13:08:550 ==>> 检测【打开WIFI(2)】
2025-07-31 23:13:08:552 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:13:08:787 ==>> $GBGGA,151312.552,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,3,,,40,60,,,40,41,,,40,1*49

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,39,16,,,39,1*42

$GBGSV,6,4,24,23,,,39,10,,,38,1,,,38,33,,,36,1*4C

$GBGSV,6,5,24,9,,,36,6,,,36,24,,,36,2,,,35,1*4B

$GBGSV,6,6,24,5,,,33,4,,,33,32,,,33,44,,,29,1*7B

$GBRMC,151312.552,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151312.552,0.000,1565.068,1565.068,50.084,2097152,2097152,2097152*5F

[W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:13:08:820 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:13:08:824 ==>> 检测【转刹把供电】
2025-07-31 23:13:08:826 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:13:08:999 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:13:09:095 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:13:09:099 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:13:09:103 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:13:09:196 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:13:09:288 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:13:09:318 ==>> 00 00 00 80 00 
head err!


2025-07-31 23:13:09:378 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 23:13:09:728 ==>> +WIFISCAN:4,0,F42A7D1297A3,-67
+WIFISCAN:4,1,CC057790A7C1,-76
+WIFISCAN:4,2,CC057790A5C1,-78
+WIFISCAN:4,3,F86FB0660A82,-85

[D][05:18:13][CAT1]wifi scan report total[4]
$GBGGA,151313.532,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,3,,,40,60,,,40,41,,,40,1*49

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,6,4,24,23,,,38,10,,,38,1,,,37,33,,,36,1*42

$GBGSV,6,5,24,9,,,36,6,,,36,24,,,36,2,,,35,1*4B

$GBGSV,6,6,24,5,,,33,4,,,33,32,,,33,44,,,30,1*73

$GBRMC,151313.532,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151313.532,0.000,1565.064,1565.064,50.079,2097152,2097152,2097152*5A



2025-07-31 23:13:09:803 ==>> [D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 23:13:10:145 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:13:10:257 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:13:10:318 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 23:13:10:408 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 23:13:10:426 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:13:10:429 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:13:10:433 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:13:10:528 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:13:10:679 ==>> $GBGGA,151314.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,59,,,41,1*71

$GBGSV,6,2,24,25,,,40,3,,,40,60,,,40,41,,,40,1*43

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,6,4,24,23,,,38,10,,,38,1,,,37,33,,,36,1*42

$GBGSV,6,5,24,9,,,36,6,,,36,24,,,36,2,,,35,1*4B

$GBGSV,6,6,24,5,,,34,4,,,33,32,,,33,44,,,31,1*75

$GBRMC,151314.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151314.512,0.000,1566.782,1566.782,50.125,2097152,2097152,2097152*57

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2417mV
OVER 150


2025-07-31 23:13:10:844 ==>> 原始值:【2417】, 乘以分压基数【2】还原值:【4834】
2025-07-31 23:13:10:871 ==>> 【读取AD_V16电压(前)】通过,【4834mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:13:10:880 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:13:10:906 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:11:129 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:14][COMM]adc read battery ts volt:12 mv
[D][05:18:14][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3088  volt:5428 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:13:11:388 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 23:13:11:429 ==>> 【转刹把供电电压(主控ADC)】通过,【5428mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:13:11:434 ==>> 检测【转刹把供电电压】
2025-07-31 23:13:11:465 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:11:779 ==>> $GBGGA,151315.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,40,3,,,40,60,,,40,41,,,40,1*48

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,39,16,,,39,1*42

$GBGSV,6,4,24,23,,,38,10,,,38,1,,,37,33,,,36,1*42

$GBGSV,6,5,24,9,,,36,6,,,36,24,,,36,2,,,35,1*4B

$GBGSV,6,6,24,5,,,34,4,,,33,32,,,33,44,,,31,1*75

$GBRMC,151315.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151315.512,0.000,1565.053,1565.053,50.068,2097152,2097152,2097152*5E

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:15][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:15][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:15][COMM]adc read battery ts volt:7 mv
[D][05:18:15][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3088  volt:5428 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2413  volt:388

2025-07-31 23:13:11:824 ==>> 8 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:13:11:964 ==>> 【转刹把供电电压】通过,【5428mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:13:11:969 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:13:11:974 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:13:12:083 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:13:12:249 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:13:12:253 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:13:12:256 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:13:12:356 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:13:12:416 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:13:12:491 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:13:12:496 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:13:12:501 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:13:12:599 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:13:12:706 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:13:12:751 ==>> $GBGGA,151316.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,60,,,41,3,,,40,41,,,40,1*48

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,6,4,24,23,,,38,10,,,38,1,,,37,33,,,36,1*42

$GBGSV,6,5,24,9,,,36,6,,,36,24,,,36,2,,,35,1*4B

$GBGSV,6,6,24,5,,,33,4,,,33,32,,,33,44,,,31,1*72

$GBRMC,151316.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151316.512,0.000,1568.516,1568.516,50.186,2097152,2097152,2097152*5C

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:13:12:811 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:13:12:826 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:13:12:916 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:13:12:922 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:13:13:021 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:13:13:127 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:13:13:148 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:13:13:153 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:13:13:157 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:13:13:217 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 23:13:13:400 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 23:13:13:422 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:13:13:426 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:13:13:429 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:13:13:505 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:13:13:610 ==>> $GBGGA,151317.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,

2025-07-31 23:13:13:670 ==>> ,,42,39,,,41,34,,,41,25,,,40,1*7B

$GBGSV,6,2,24,59,,,40,60,,,40,3,,,40,41,,,40,1*48

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,39,16,,,39,1*42

$GBGSV,6,4,24,23,,,38,10,,,37,1,,,37,6,,,36,1*7B

$GBGSV,6,5,24,24,,,36,33,,,35,9,,,35,2,,,35,1*7D

$GBGSV,6,6,24,5,,,33,4,,,33,32,,,33,44,,,31,1*72

$GBRMC,151317.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151317.512,0.000,1556.420,1556.420,49.797,2097152,2097152,2097152*53



2025-07-31 23:13:13:705 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:13:13:710 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:13:13:716 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:13:13:820 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:13:13:975 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:13:13:979 ==>> 检测【左刹电压测试1】
2025-07-31 23:13:13:989 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:14:331 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1718  volt:2264 mv
[D][05:18:18][COMM]adc read right brake adc:1724  volt:2272 mv
[D][05:18:18][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:18][COMM]adc read battery ts volt:6 mv
[D][05:18:18][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:18][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:13:14:533 ==>> 【左刹电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 23:13:14:537 ==>> 检测【右刹电压测试1】
2025-07-31 23:13:14:580 ==>> 【右刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 23:13:14:584 ==>> 检测【转把电压测试1】
2025-07-31 23:13:14:626 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 23:13:14:630 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:13:14:633 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:13:14:679 ==>> $GBGGA,151318.512,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,40,1*7B

$GBGSV,6,2,24,59,,,40,60,,,40,3,,,40,41,,,40,1*48

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,39,16,,,39,1*42

$GBGSV,6,4,24,23,,,38,10,,,37,1,,,37,6,,,36,1*7B

$GBGSV,6,5,24,24,,,36,33,,,36,9,,,35,2,,,35,1*7E

$GBGSV,6,6,24,5,,,33,4,,,33,32,,,33,44,,,31,1*72

$GBRMC,151318.512,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151318.512,0.000,1558.146,1558.146,49.851,2097152,2097152,2097152*59



2025-07-31 23:13:14:724 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:13:14:934 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:13:14:942 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:13:14:959 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:13:15:014 ==>> 3A A3 04 00 A3 


2025-07-31 23:13:15:119 ==>> OFF_OUT4
OVER 150


2025-07-31 23:13:15:258 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:13:15:265 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:13:15:287 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:13:15:317 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:13:15:407 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 23:13:15:556 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:13:15:563 ==>> 检测【左刹电压测试2】
2025-07-31 23:13:15:571 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:13:15:663 ==>> $GBGGA,151319.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,41,34,,,41,25,,,41,1*7B

$GBGSV,6,2,24,59,,,41,60,,,41,3,,,40,41,,,40,1*48

$GBGSV,6,3,24,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,6,4,24,23,,,39,10,,,38,1,,,37,6,,,36,1*75

$GBGSV,6,5,24,24,,,36,33,,,36,9,,,35,2,,,35,1*7E

$GBGSV,6,6,24,5,,,34,4,,,33,32,,,33,44,,,32,1*76

$GBRMC,151319.512,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151319.512,0.000,1573.695,1573.695,50.350,2097152,2097152,2097152*5A



2025-07-31 23:13:15:873 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:19][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:19][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:19][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:19][COMM]adc read battery ts volt:7 mv
[D][05:18:19][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:13:16:093 ==>> 【左刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 23:13:16:097 ==>> 检测【右刹电压测试2】
2025-07-31 23:13:16:111 ==>> 【右刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 23:13:16:115 ==>> 检测【转把电压测试2】
2025-07-31 23:13:16:132 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 23:13:16:135 ==>> 检测【晶振检测】
2025-07-31 23:13:16:139 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:13:16:301 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 23:13:16:410 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:13:16:416 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:13:16:443 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:13:16:529 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1650mV
Get AD_V4 1650mV
Get AD_V5 2770mV
Get AD_V6 1990mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:13:16:634 ==>> $GBGGA,151320.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,60,,,40,3,,,40,41,,,40,1*49

$GBGSV,7,3,25,11,,,40,7,,,4

2025-07-31 23:13:16:694 ==>> 0,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,37,1*7A

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,2,,,35,1*7D

$GBGSV,7,6,25,12,,,35,5,,,34,4,,,33,32,,,33,1*72

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151320.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151320.512,0.000,1567.127,1567.127,50.136,2097152,2097152,2097152*52



2025-07-31 23:13:16:698 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:13:16:702 ==>> 检测【检测BootVer】
2025-07-31 23:13:16:705 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:17:089 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071539533
[D][05:18:20][FCTY]HardwareID  = 867222087618403
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1287
[D][05:18:20][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 25 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3830 mv
[D][05:18:20][FCTY]=======

2025-07-31 23:13:17:119 ==>> ============= E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:17:320 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:13:17:324 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:13:17:330 ==>> 检测【检测固件版本】
2025-07-31 23:13:17:379 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:13:17:383 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:13:17:388 ==>> 检测【检测蓝牙版本】
2025-07-31 23:13:17:400 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 23:13:17:424 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:13:17:431 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:13:17:444 ==>> 检测【检测MoBikeId】
2025-07-31 23:13:17:469 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:13:17:473 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:13:17:477 ==>> 检测【检测蓝牙地址】
2025-07-31 23:13:17:482 ==>> 取到目标值:FE6D905731CD
2025-07-31 23:13:17:527 ==>> 【检测蓝牙地址】通过,【FE6D905731CD】符合目标值【】要求!
2025-07-31 23:13:17:531 ==>> 提取到蓝牙地址:FE6D905731CD
2025-07-31 23:13:17:537 ==>> 检测【BOARD_ID】
2025-07-31 23:13:17:573 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:13:17:577 ==>> 检测【检测充电电压】
2025-07-31 23:13:17:614 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:13:17:620 ==>> 检测【检测VBUS电压1】
2025-07-31 23:13:17:660 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:13:17:665 ==>> 检测【检测充电电流】
2025-07-31 23:13:17:672 ==>> $GBGGA,151321.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,41,34,,,41,25,,,41,1*7B

$GBGSV,7,2,25,59,,,41,60,,,40,3,,,40,41,,,40,1*49

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,39,10,,,38,1,,,38,6,,,37,1*7B

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,2,,,35,1*7D

$GBGSV,7,6,25,12,,,35,5,,,34,4,,,33,32,,,33,1*72

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151321.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151321.512,0.000,1570.448,1570.448,50.245,2097152,2097152,2097152*54



2025-07-31 23:13:17:709 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:13:17:714 ==>> 检测【检测IMEI】
2025-07-31 23:13:17:718 ==>> 取到目标值:867222087618403
2025-07-31 23:13:17:755 ==>> 【检测IMEI】通过,【867222087618403】符合目标值【】要求!
2025-07-31 23:13:17:759 ==>> 提取到IMEI:867222087618403
2025-07-31 23:13:17:791 ==>> 检测【检测IMSI】
2025-07-31 23:13:17:799 ==>> 取到目标值:460130071539533
2025-07-31 23:13:17:804 ==>> 【检测IMSI】通过,【460130071539533】符合目标值【】要求!
2025-07-31 23:13:17:821 ==>> 提取到IMSI:460130071539533
2025-07-31 23:13:17:826 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:13:17:830 ==>> 取到目标值:460130071539533
2025-07-31 23:13:17:844 ==>> 【校验网络运营商(移动)】通过,【460130071539533】符合目标值【】要求!
2025-07-31 23:13:17:851 ==>> 检测【打开CAN通信】
2025-07-31 23:13:17:857 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:13:17:928 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:13:18:150 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:13:18:155 ==>> 检测【检测CAN通信】
2025-07-31 23:13:18:160 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:13:18:240 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:13:18:330 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33260
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:13:18:390 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:13:18:424 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:13:18:432 ==>> 检测【关闭CAN通信】
2025-07-31 23:13:18:453 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:13:18:459 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:13:18:525 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:13:18:630 ==>> $GBGGA,151322.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,40,60,,,40,3,,,40,4

2025-07-31 23:13:18:690 ==>> 1,,,40,1*48

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,39,16,,,39,1*42

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,37,1*7A

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,12,,,36,1*4F

$GBGSV,7,6,25,2,,,35,5,,,34,4,,,33,32,,,33,1*43

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151322.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151322.512,0.000,1565.465,1565.465,50.079,2097152,2097152,2097152*5A



2025-07-31 23:13:18:695 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:13:18:698 ==>> 检测【打印IMU STATE】
2025-07-31 23:13:18:702 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:13:18:919 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:13:18:968 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:13:18:976 ==>> 检测【六轴自检】
2025-07-31 23:13:18:997 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:13:19:114 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:13:19:419 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 23:13:19:690 ==>> $GBGGA,151323.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,60,,,40,3,,,40,41,,,40,1*49

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,37,1*7A

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,12,,,36,1*4F

$GBGSV,7,6,25,2,,,35,5,,,34,4,,,33,32,,,33,1*43

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151323.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151323.512,0.000,1568.784,1568.784,50.187,2097152,2097152,2097152*5B



2025-07-31 23:13:20:694 ==>> $GBGGA,151324.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,3,,,41,60,,,40,41,,,40,1*48

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,37,1*7A

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,12,,,35,1*4C

$GBGSV,7,6,25,2,,,35,5,,,33,4,,,33,32,,,33,1*44

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151324.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151324.512,0.000,1567.132,1567.132,50.141,2097152,2097152,2097152*56



2025-07-31 23:13:20:799 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1

2025-07-31 23:13:20:829 ==>> ]exec over: func id: 12, ret: 6


2025-07-31 23:13:20:934 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35890 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-3,-2,4043]
[D][05:18:24][COMM]Main Task receive even

2025-07-31 23:13:20:967 ==>> t:142 finished processing


2025-07-31 23:13:21:043 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:13:21:049 ==>> 检测【打印IMU STATE2】
2025-07-31 23:13:21:070 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:13:21:219 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:13:21:313 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:13:21:318 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:13:21:341 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:13:21:430 ==>> [D][05:18:25][COMM]read battery soc:255
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:13:21:590 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:13:21:597 ==>> 检测【检测VBUS电压2】
2025-07-31 23:13:21:604 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:21:716 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11
$GBGGA,151325.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,40,1*7B

$GBGSV,7,2,25,59,,,40,3,,,40,60,,,40,41,,,40,1*48

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,36,1*7B

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,12,,,35,1*4C

$GBGSV,7,6,25,2,,,35,5,,,33,4,,,33,32,,,33,1*44

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,151325.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151325.512,0.000,1560.494,1560.494,49.924,2097152,2097152,2097152*54



2025-07-31 23:13:22:020 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539533
[D][05:18:25][FCTY]HardwareID  = 867222087618403
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11700 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 7, adc = 295
[D][05:18:25][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL

2025-07-31 23:13:22:065 ==>> _RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3830 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:22:129 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:22:480 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539533
[D][05:18:26][FCTY]HardwareID  = 867222087618403
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:26][FCTY]Bat         = 3924 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 171
[D][05:18:26][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26

2025-07-31 23:13:22:525 ==>> ][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3830 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:22:630 ==>> $GBGGA,151326.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,40,3,,,40,60,,,40,41,,,40,

2025-07-31 23:13:22:666 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:22:691 ==>> 1*48

$GBGSV,7,3,25,11,,,40,7,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,23,,,38,10,,,38,1,,,38,6,,,36,1*7B

$GBGSV,7,5,25,24,,,36,33,,,36,9,,,36,12,,,35,1*4C

$GBGSV,7,6,25,2,,,35,5,,,33,4,,,33,32,,,33,1*44

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,151326.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151326.512,0.000,1563.809,1563.809,50.028,2097152,2097152,2097152*5A



2025-07-31 23:13:22:975 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539533
[D][05:18:26][FCTY]HardwareID  = 867222087618403
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:26][FCTY]Bat         = 3924 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 150
[D][05:18:26][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 75 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.

2025-07-31 23:13:23:020 ==>> 2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3830 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:23:205 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:23:291 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33246. cur_tick:38247. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38247


2025-07-31 23:13:23:960 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539533
[D][05:18:27][FCTY]HardwareID  = 867222087618403
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:27][FCTY]Bat         = 3744 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 6300 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 119
[D][05:18:27][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT

2025-07-31 23:13:23:991 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:24:062 ==>> 1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3830 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [162995

2025-07-31 23:13:24:167 ==>> 5107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority

2025-07-31 23:13:24:272 ==>> :3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CA

2025-07-31 23:13:24:363 ==>> T1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5BC3898B3CF4FB6F049F6041B89871D14687EF2E4CA10BCEAA6202A84A88450A1271F929AA56CF67F04FC470F5F5FDAECBD57650D448DEEA7223EC040F48CCE7EBF42ACFC2C3BC0E098C97517595A10E01D14
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
[D][05:18:27][GNSS]recv submsg id[1]
[D][05:18:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:27][GNSS]location stop evt done evt


2025-07-31 23:13:24:650 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539533
[D][05:18:28][FCTY]HardwareID  = 867222087618403
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:28][FCTY]Bat         = 3724 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:28][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 101 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3830 mv
[D][05:18:28][FCTY]=============

2025-07-31 23:13:24:679 ==>> ======= E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:24:782 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:13:25:181 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539533
[D][05:18:28][FCTY]HardwareID  = 867222087618403
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FE6D905731CD
[D][05:18:28][FCTY]Bat         = 3784 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:28][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS

2025-07-31 23:13:25:227 ==>> _VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3830 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:13:25:389 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:13:25:394 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:13:25:401 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:13:25:528 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:13:25:588 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 16


2025-07-31 23:13:25:648 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 23:13:25:760 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:13:25:765 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:13:25:773 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:13:25:829 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:13:26:043 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:13:26:048 ==>> 检测【打开WIFI(3)】
2025-07-31 23:13:26:056 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:13:26:205 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 23:13:26:313 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:13:26:318 ==>> 检测【扩展芯片hw】
2025-07-31 23:13:26:323 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:13:26:917 ==>> [D][05:18:30][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:27:188 ==>> [D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:13:27:340 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:13:27:738 ==>>                           ipheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:1080

2025-07-31 23:13:27:843 ==>> 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:31][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]index:1
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[

2025-07-31 23:13:27:948 ==>> D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:

2025-07-31 23:13:28:053 ==>> 31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]read battery soc:255


2025-07-31 23:13:28:083 ==>>                                                                                                   

2025-07-31 23:13:28:372 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:13:28:387 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:32][COMM]f:[drv_audio_ack_receive].wait ack timeout!![43290]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 23:13:28:493 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:13:28:647 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:13:28:654 ==>> 检测【扩展芯片boot】
2025-07-31 23:13:28:667 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:13:28:672 ==>> 检测【扩展芯片sw】
2025-07-31 23:13:28:686 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:13:28:691 ==>> 检测【检测音频FLASH】
2025-07-31 23:13:28:697 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:13:29:018 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:32][COMM]43904 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:29:232 ==>> [D][05:18:33][CAT1]SEND RAW data timeout
[D][05:18:33][CAT1]exec over: func id: 12, ret: -52
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 23:13:29:397 ==>> [D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44324]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 23:13:29:667 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 23:13:29:962 ==>> [D][05:18:33][COMM]44916 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:30:424 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45353]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:13:30:967 ==>> [D][05:18:34][COMM]45927 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:31:687 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 23:13:31:993 ==>> [D][05:18:35][COMM]46939 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:32:811 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 23:13:33:007 ==>> [D][05:18:36][COMM]47950 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:33:682 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 23:13:33:730 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:13:33:736 ==>> 检测【打开喇叭声音】
2025-07-31 23:13:33:746 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:13:33:926 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 23:13:34:001 ==>> [D][05:18:37][COMM]48961 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:34:011 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:13:34:021 ==>> 检测【打开大灯控制】
2025-07-31 23:13:34:035 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:13:34:182 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:13:34:291 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:13:34:296 ==>> 检测【关闭仪表供电3】
2025-07-31 23:13:34:305 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:13:34:504 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 23:13:34:568 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:13:34:573 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:13:34:584 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:13:34:686 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:13:34:854 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:13:34:861 ==>> 检测【读大灯电压】
2025-07-31 23:13:34:885 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:13:35:033 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[33409]
[D][05:18:38][COMM]49974 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:35:127 ==>> 【读大灯电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:13:35:132 ==>> 检测【关闭大灯控制2】
2025-07-31 23:13:35:137 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:13:35:306 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:13:35:400 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:13:35:408 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:13:35:414 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:13:35:610 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:13:35:673 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:13:35:705 ==>> 检测【打开WIFI(4)】
2025-07-31 23:13:35:732 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:13:35:737 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 23:13:35:882 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:13:36:004 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:13:36:013 ==>> 检测【EC800M模组版本】
2025-07-31 23:13:36:035 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:36:040 ==>> [D][05:18:39][COMM]50985 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:37:026 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:37:041 ==>> [D][05:18:40][COMM]51998 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:13:37:694 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 23:13:37:938 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:38:044 ==>> [D][05:18:41][COMM]imu error,enter wait


2025-07-31 23:13:38:059 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:39:092 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:39:380 ==>> [D][05:18:43][CAT1]exec over: func id: 15, ret: -93
[D][05:18:43][CAT1]sub id: 15, ret: -93

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:43][SAL ]socket send fail. id[4]
[D][05:18:43][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:43][CAT1]gsm read msg sub id: 12
[D][05:18:43][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:43][M2M ]m2m select fd[4]
[D][05:18:43][M2M ]socket[4] Link is disconnected
[D][05:18:43][M2M ]tcpclient close[4]
[D][05:18:43][SAL ]socket[4] has closed
[D][05:18:43][PROT]protocol read data ok
[E][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:43][PROT]M2M Send Fail [1629955123]
[D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 23:13:39:819 ==>> [D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54600]
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1032].open hexlog save
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]file:A20 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:43][COMM]read file, len:15228, num:4
[D][05:18:43][COMM]--->crc16:0x419c
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel parse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:43][COMM]read battery soc:255


2025-07-31 23:13:39:986 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:40:119 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:40:767 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55700]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:13:41:147 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:41:810 ==>> [D][05:18:45][COMM]read battery soc:255
[D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56727]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:13:42:036 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:42:187 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:42:279 ==>> [D][05:18:46][CAT1]SEND RAW data timeout
[D][05:18:46][CAT1]exec over: func id: 12, ret: -52
[W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:13:42:819 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57757]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:13:43:181 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:43:226 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:43:717 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 23:13:44:272 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:44:303 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:45:299 ==>> [D][05:18:49][CAT1]SEND RAW data timeout
[D][05:18:49][CAT1]exec over: func id: 12, ret: -52
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:49][CAT1]gsm read msg sub id: 10
[D][05:18:49][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:13:45:314 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:45:721 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:49][COMM]read battery soc:255


2025-07-31 23:13:46:353 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:47:325 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:47:401 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:47:738 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 23:13:48:184 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:48:444 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:49:361 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:49:482 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:49:742 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 23:13:50:531 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:50:684 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:51:421 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:51:574 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:51:740 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 23:13:52:606 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:53:177 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:53:282 ==>> [D][05:18:57][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:13:53:464 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:53:632 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:53:767 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 23:13:54:669 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:55:162 ==>> [D][05:18:59][COMM]f:[drv_audio_ack_receive].wait ack timeout!![70100]
[D][05:18:59][COMM]accel parse set 0
[D][05:18:59][COMM][Audio].l:[1032].open hexlog save
[D][05:18:59][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 23:13:55:531 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:55:681 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:55:711 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:55:756 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 23:13:56:758 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:57:567 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:57:777 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 23:13:57:807 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:58:191 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:13:58:850 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:13:59:630 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:13:59:780 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 23:13:59:885 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:00:680 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:14:00:906 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:01:297 ==>> [D][05:19:05][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:14:01:665 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:14:01:770 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 23:14:01:936 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:02:967 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:03:177 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:14:03:728 ==>> [D][05:19:07][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:14:03:788 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 23:14:04:001 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:05:033 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:05:689 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:14:05:794 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:09][COMM]read battery soc:255


2025-07-31 23:14:06:070 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:14:07:122 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 23:14:07:132 ==>> #################### 【测试结束】 ####################
2025-07-31 23:14:07:219 ==>> 关闭5V供电
2025-07-31 23:14:07:229 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:14:07:323 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:14:07:825 ==>> [D][05:19:11][COMM]read battery soc:255
[W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:14:08:188 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:14:08:233 ==>> 关闭5V供电成功
2025-07-31 23:14:08:241 ==>> 关闭33V供电
2025-07-31 23:14:08:250 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:14:08:323 ==>> 5A A5 02 5A A5 


2025-07-31 23:14:08:428 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:14:08:608 ==>> [D][05:19:12][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 23:14:09:242 ==>> 关闭33V供电成功
2025-07-31 23:14:09:249 ==>> 关闭3.7V供电
2025-07-31 23:14:09:257 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:14:09:321 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:14:09:426 ==>> [D][05:19:13][CAT1]exec over: func id: 10, ret: -43
[D][05:19:13][CAT1]sub id: 10, ret: -43

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
[D][05:19:13][M2M ]m2m gsm shut done, ret[1]
[D][05:19:13][CAT1]gsm read msg sub id: 12
[D][05:19:13][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
[D][05:19:13][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
[D][05:19:13][M2M ]g_m2m_is_idle become true
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:13][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
[D][05:19:13][PROT]index:1 1629955153
[D][05:19:13][PROT]is_send:0
[D][05:19:13][PROT]sequence_num:5
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:1

2025-07-31 23:14:09:456 ==>> 3][PROT]retry_times:2
[D][05:19:13][PROT]send_p

2025-07-31 23:14:09:787 ==>>  

2025-07-31 23:14:10:246 ==>> 关闭3.7V供电成功
