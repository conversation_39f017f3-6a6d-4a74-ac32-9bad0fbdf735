2025-07-31 23:27:14:213 ==>> MES查站成功:
查站序号:P51000100531394F验证通过
2025-07-31 23:27:14:232 ==>> 扫码结果:P51000100531394F
2025-07-31 23:27:14:234 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:27:14:235 ==>> 测试参数版本:2024.10.11
2025-07-31 23:27:14:237 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:27:14:239 ==>> 检测【打开透传】
2025-07-31 23:27:14:241 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:27:14:321 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:27:14:511 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:27:14:540 ==>> 检测【检测接地电压】
2025-07-31 23:27:14:541 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:27:14:626 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:27:14:813 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:27:14:815 ==>> 检测【打开小电池】
2025-07-31 23:27:14:819 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:27:14:929 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:27:15:082 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:27:15:085 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:27:15:088 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:27:15:234 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:27:15:357 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:27:15:360 ==>> 检测【等待设备启动】
2025-07-31 23:27:15:363 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:27:15:685 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:27:15:868 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:27:16:387 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:27:16:493 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:27:16:568 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:27:16:964 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:27:17:424 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:27:17:439 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:27:17:743 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:27:17:745 ==>> 检测【产品通信】
2025-07-31 23:27:17:747 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:27:17:912 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 23:27:18:094 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:27:18:096 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:27:18:097 ==>> 检测【初始化完成检测】
2025-07-31 23:27:18:099 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:27:18:353 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:27:18:488 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:18:627 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:27:18:631 ==>> 检测【关闭大灯控制1】
2025-07-31 23:27:18:635 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:27:18:668 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:27:18:773 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:27:18:896 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:27:18:899 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:27:18:901 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:27:19:118 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:27:19:204 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:27:19:206 ==>> 检测【关闭仪表供电】
2025-07-31 23:27:19:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:27:19:424 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:27:19:514 ==>> [D][05:17:52][COMM]3640 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:19:516 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:27:19:518 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:27:19:520 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:27:19:694 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:27:19:842 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:27:19:844 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:27:19:847 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:27:19:996 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:27:20:137 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:27:20:141 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:27:20:143 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:27:20:295 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:27:20:479 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:27:20:481 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:27:20:482 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:27:20:508 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:20:613 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:27:20:703 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30


2025-07-31 23:27:20:793 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 23:27:20:808 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:27:20:810 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:27:20:812 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:27:20:929 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:27:21:034 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5004. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5004. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5005. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5006. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5007. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick

2025-07-31 23:27:21:079 ==>> :0. cur_tick:5007. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5007
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5008


2025-07-31 23:27:21:095 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:27:21:099 ==>> 该项需要延时执行
2025-07-31 23:27:21:535 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:22:312 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:27:22:814 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:

2025-07-31 23:27:22:919 ==>> 55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincir

2025-07-31 23:27:23:025 ==>> cuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][

2025-07-31 23:27:23:100 ==>> PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6673 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][CAT1]power_urc_cb ret[5]
                                         

2025-07-31 23:27:23:547 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:24:571 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:24:813 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:27:25:106 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:27:25:109 ==>> 检测【33V输入电压ADC】
2025-07-31 23:27:25:112 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:25:432 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:17:58][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:58][COMM]adc read left brake adc:20  volt:26 mv
[D][05:17:58][COMM]adc read right brake adc:13  volt:17 mv
[D][05:17:58][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:58][COMM]adc read battery ts volt:21 mv
[D][05:17:58][COMM]adc read in 24v adc:1314  volt:33234 mv
[D][05:17:58][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:27:25:568 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:27:25:633 ==>> 【33V输入电压ADC】通过,【33234mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:27:25:635 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:27:25:638 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:27:25:732 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1673mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1991mV
Get AD_V7 1089mV
OVER 150


2025-07-31 23:27:25:907 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:25:909 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:27:25:934 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1673mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:25:936 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:27:25:938 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 23:27:25:944 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10016
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10016


2025-07-31 23:27:25:952 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:27:25:954 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:27:25:970 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:27:25:972 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:27:25:995 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:27:25:997 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:27:26:141 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1669mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 2023mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:27:26:231 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:27:26:269 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:26:272 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:27:26:287 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:26:289 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:27:26:292 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 23:27:26:303 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:27:26:305 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:27:26:322 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2023mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:27:26:338 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:27:26:344 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:27:26:346 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:27:26:430 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1990mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:27:26:621 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:26:623 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:27:26:639 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:26:641 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:27:26:643 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 23:27:26:657 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:27:26:661 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:27:26:676 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:27:26:679 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:27:26:701 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:27:26:704 ==>> 检测【打开WIFI(1)】
2025-07-31 23:27:26:707 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:27:26:825 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200

2025-07-31 23:27:26:870 ==>> ,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:27:27:253 ==>>                                                                                                                                                                                                                                                   T1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
867222087631489

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539517

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:27:27:524 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:27:27:527 ==>> 检测【清空消息队列(1)】
2025-07-31 23:27:27:531 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:27:27:591 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:27:27:696 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol

2025-07-31 23:27:27:726 ==>>  queue cleaned by AT_CMD!


2025-07-31 23:27:27:843 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:27:27:846 ==>> 检测【打开GPS(1)】
2025-07-31 23:27:27:849 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:27:27:936 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:27:28:041 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 23:27:28:142 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:27:28:145 ==>> 检测【打开GSM联网】
2025-07-31 23:27:28:147 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:27:28:316 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:27:28:441 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:27:28:445 ==>> 检测【打开仪表供电1】
2025-07-31 23:27:28:449 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:27:28:620 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:27:28:781 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:27:28:784 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:27:28:786 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:27:28:801 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 23:27:29:028 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:27:29:083 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:27:29:087 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:27:29:094 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:27:29:314 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33062]


2025-07-31 23:27:29:363 ==>> 【读取主控ADC采集的仪表电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:27:29:368 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:27:29:372 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:29:527 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:27:29:586 ==>> [D][05:18:02][COMM]13729 imu init OK


2025-07-31 23:27:29:635 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:27:29:638 ==>> 检测【AD_V20电压】
2025-07-31 23:27:29:662 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:29:737 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:29:827 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 23:27:29:932 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:27:30:128 ==>> 本次取值间隔时间:389ms
2025-07-31 23:27:30:146 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:30:191 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:27:30:251 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:30:357 ==>> 1A A1 10 00 00 
Get AD_V20 0mV
OVER 150
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:27:30:688 ==>> 本次取值间隔时间:434ms
2025-07-31 23:27:30:691 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 23:27:30:706 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:30:808 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:30:988 ==>>                        GSM_INIT OK
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F38393436333F3E07

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03]

2025-07-31 23:27:31:063 ==>> [CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6
[D][05:18:04][COMM]read battery soc:255
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:27:31:154 ==>>                                                                                                                                                            ][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:27:31:244 ==>> 本次取值间隔时间:430ms
2025-07-31 23:27:31:263 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:31:364 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:31:424 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:27:31:808 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:27:31:823 ==>> 本次取值间隔时间:450ms
2025-07-31 23:27:31:841 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:31:945 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:32:022 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 23:27:32:189 ==>> 本次取值间隔时间:238ms
2025-07-31 23:27:32:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:27:32:313 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:27:32:433 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1660mV
OVER 150


2025-07-31 23:27:32:479 ==>> 本次取值间隔时间:160ms
2025-07-31 23:27:32:507 ==>> 【AD_V20电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:32:510 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:27:32:512 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:27:32:524 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:27:32:629 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:27:32:798 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:27:32:801 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:27:32:805 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:27:32:886 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 23:27:33:037 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:27:33:080 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:27:33:087 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:27:33:090 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:27:33:188 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:27:33:233 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:27:33:339 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,39,,,42,40,,,42,59,,,42,34,,,39,1*74

$GBGSV,3,2,09,60,,,38,41,,,34,25,,,42,10,,,40,1*75



2025-07-31 23:27:33:361 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:27:33:364 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:27:33:366 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:27:33:415 ==>> $GBGSV,3,3,09,23,,,37,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.600,1637.600,52.382,2097152,2097152,2097152*41

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 23:27:33:611 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:27:33:647 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:27:33:650 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:27:33:652 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:27:33:854 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:27:33:917 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:27:33:920 ==>> 检测【AD_V21电压】
2025-07-31 23:27:33:924 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:27:34:020 ==>> 1A A1 20 00 00 
Get AD_V21 1037mV
OVER 150


2025-07-31 23:27:34:325 ==>> 本次取值间隔时间:405ms
2025-07-31 23:27:34:340 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,3,,,43,39,,,42,40,,,42,25,,,41,1*4C

$GBGSV,4,2,16,59,,,41,34,,,40,60,,,39,23,,,39,1*7A

$GBGSV,4,3,16,11,,,39,16,,,39,10,,,38,7,,,37,1*48

$GBGSV,4,4,16,41,,,36,5,,,36,1,,,37,2,,,36,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1634.599,1634.599,52.247,2097152,2097152,2097152*49



2025-07-31 23:27:34:368 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:27:34:430 ==>> 1A A1 20 00 00 
Get AD_V21 1656mV
OVER 150


2025-07-31 23:27:34:865 ==>> 本次取值间隔时间:490ms
2025-07-31 23:27:34:883 ==>> 【AD_V21电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:34:886 ==>> 检测【关闭仪表供电2】
2025-07-31 23:27:34:891 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:27:34:912 ==>> [D][05:18:08][COMM]read battery soc:255


2025-07-31 23:27:35:124 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:27:35:157 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:27:35:160 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:27:35:164 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:27:35:349 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,39,,,42,40,,,42,3,,,41,25,,,41,1*4E

$GBGSV,5,2,17,59,,,41,34,,,40,60,,,40,11,,,40,1*7B

$GBGSV,5,3,17,23,,,39,16,,,39,10,,,38,7,,,38,1*46

$GBGSV,5,4,17,1,,,37,41,,,37,2,,,35,5,,,34,1*43

$GBGSV,5,5,17,4,,,32,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1599.794,1599.794,51.168,2097152,2097152,2097152*44

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:27:35:429 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:27:35:434 ==>> 检测【打开AccKey2供电】
2025-07-31 23:27:35:439 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:27:35:590 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:27:35:712 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:27:35:715 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:27:35:718 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:36:039 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:09][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:18:09][COMM]adc read left brake adc:22  volt:29 mv
[D][05:18:09][COMM]adc read right brake adc:20  volt:26 mv
[D][05:18:09][COMM]adc read throttle adc:24  volt:31 mv
[D][05:18:09][COMM]adc read battery ts volt:25 mv
[D][05:18:09][COMM]adc read in 24v adc:1313  volt:33209 mv
[D][05:18:09][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:18  volt:14 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2446  volt:3941 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:27:36:242 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33260mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:27:36:246 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:27:36:250 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:27:36:360 ==>> $GBGGA,152740.211,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,42,39,,,41,3,,,41,25,,,41,1*43

$GBGSV,5,2,19,59,,,41,34,,,40,60,,,40,11,,,40,1*75

$GBGSV,5,3,19,23,,,39,16,,,39,7,,,39,10,,,38,1*49

$GBGSV,5,4,19,1,,,38,41,,,37,2,,,35,5,,,34,1*42

$GBGSV,5,5,19,4,,,32,24,,,31,6,,,37,1*7D

$GBRMC,152740.211,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152740.211,0.000,1584.639,1584.639,50.702,2097152,2097152,2097152*56



2025-07-31 23:27:36:450 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:27:36:524 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:27:36:528 ==>> 该项需要延时执行
2025-07-31 23:27:36:675 ==>> $GBGGA,152740.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,39,,,41,3,,,41,25,,,41,1*4B

$GBGSV,6,2,21,59,,,41,34,,,40,60,,,40,11,,,40,1*7D

$GBGSV,6,3,21,23,,,39,16,,,39,7,,,39,10,,,38,1*41

$GBGSV,6,4,21,1,,,38,41,,,38,6,,,37,2,,,35,1*45

$GBGSV,6,5,21,5,,,33,4,,,33,24,,,32,9,,,17,1*4F

$GBGSV,6,6,21,43,,,42,1*74

$GBRMC,152740.511,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152740.511,0.000,1542.377,1542.377,49.466,2097152,2097152,2097152*58



2025-07-31 23:27:36:915 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 23:27:37:664 ==>> $GBGGA,152741.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,43,,,41,39,,,41,25,,,41,1*7C

$GBGSV,6,2,22,59,,,41,60,,,41,3,,,40,34,,,40,1*4C

$GBGSV,6,3,22,11,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,22,10,,,38,1,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,2,,,35,12,,,35,9,,,35,5,,,33,1*4E

$GBGSV,6,6,22,4,,,33,24,,,33,1*44

$GBRMC,152741.511,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152741.511,0.000,1582.951,1582.951,50.636,2097152,2097152,2097152*56



2025-07-31 23:27:38:670 ==>> $GBGGA,152742.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,43,,,41,39,,,41,25,,,41,1*7C

$GBGSV,6,2,22,59,,,41,60,,,41,3,,,41,34,,,40,1*4D

$GBGSV,6,3,22,11,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,22,10,,,38,1,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,2,,,35,12,,,35,9,,,35,5,,,33,1*4E

$GBGSV,6,6,22,4,,,33,24,,,33,1*44

$GBRMC,152742.511,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152742.511,0.000,1584.837,1584.837,50.698,2097152,2097152,2097152*51



2025-07-31 23:27:38:912 ==>> [D][05:18:12][COMM]read battery soc:255


2025-07-31 23:27:39:537 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:27:39:542 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:27:39:546 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:39:674 ==>> $GBGGA,152743.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,43,,,41,39,,,41,25,,,41,1*7C

$GBGSV,6,2,22,59,,,41,3,,,41,34,,,41,11,,,41,1*4A

$GBGSV,6,3,22,60,,,40,7,,,40,23,,,39,16,,,39,1*44

$GBGSV,6,4,22,10,,,38,1,,,38,41,,,38,6,,,37,1*78

$GBGSV,6,5,22,2,,,36,9,,,36,12,,,35,5,,,34,1*49

$GBGSV,6,6,22,4,,,34,24,,,34,1*44

$GBRMC,152743.511,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152743.511,0.000,1596.128,1596.128,51.043,2097152,2097152,2097152*51



2025-07-31 23:27:39:885 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3149  volt:5535 mv
[D][05:18:12][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:12][COMM]adc read left brake adc:18  volt:23 mv
[D][05:18:12][COMM]adc read right brake adc:22  volt:29 mv
[D][05:18:12][COMM]adc read throttle adc:20  volt:26 mv
[D][05:18:12][COMM]adc read battery ts volt:29 mv
[D][05:18:12][COMM]adc read in 24v adc:1313  volt:33209 mv
[D][05:18:12][COMM]adc read throttle brake in adc:16  volt:28 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2445  volt:3939 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:27:40:079 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【404mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:27:40:083 ==>> 检测【打开AccKey1供电】
2025-07-31 23:27:40:086 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:27:40:313 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:27:40:361 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:27:40:365 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:27:40:368 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:27:40:418 ==>> 1A A1 00 40 00 
Get AD_V14 2653mV
OVER 150


2025-07-31 23:27:40:618 ==>> 原始值:【2653】, 乘以分压基数【2】还原值:【5306】
2025-07-31 23:27:40:636 ==>> 【读取AccKey1电压(ADV14)前】通过,【5306mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:27:40:641 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:27:40:647 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:40:678 ==>> $GBGGA,152744.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,42,43,,,41,25,,,41,1*7E

$GBGSV,6,2,23,59,,,41,34,,,41,11,,,41,3,,,40,1*4A

$GBGSV,6,3,23,60,,,40,7,,,40,23,,,39,16,,,39,1*45

$GBGSV,6,4,23,10,,,38,1,,,38,41,,,38,6,,,37,1*79

$GBGSV,6,5,23,9,,,36,2,,,35,12,,,34,5,,,34,1*4A

$GBGSV,6,6,23,24,,,34,4,,,33,33,,,30,1*41

$GBRMC,152744.511,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152744.511,0.000,1575.432,1575.432,50.414,2097152,2097152,2097152*51



2025-07-31 23:27:40:983 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:13][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:13][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:13][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:13][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:13][COMM]adc read battery ts volt:23 mv
[D][05:18:13][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:13][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2440  volt:3931 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:14][COMM]read battery soc:255


2025-07-31 23:27:41:173 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5533mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:27:41:177 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:27:41:180 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:27:41:318 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:27:41:470 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:27:41:474 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:27:41:477 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:27:41:531 ==>> 1A A1 00 40 00 
Get AD_V14 2659mV
OVER 150


2025-07-31 23:27:41:636 ==>> $GBGGA,152745.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,42,43,,,41,25,,,41,1*79

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,36,12,,,34,5,,,34,1*4E

$GBGSV,6,6,24,24,,,34,4,,,34,33,,,31,44,,,29,1*4B

$GBRMC,152745

2025-07-31 23:27:41:666 ==>> .511,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152745.511,0.000,1565.080,1565.080,50.096,2097152,2097152,2097152*5E



2025-07-31 23:27:41:726 ==>> 原始值:【2659】, 乘以分压基数【2】还原值:【5318】
2025-07-31 23:27:41:744 ==>> 【读取AccKey1电压(ADV14)后】通过,【5318mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:27:41:748 ==>> 检测【打开WIFI(2)】
2025-07-31 23:27:41:752 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:27:41:942 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:27:42:020 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:27:42:023 ==>> 检测【转刹把供电】
2025-07-31 23:27:42:028 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:27:42:216 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:27:42:294 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:27:42:298 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:27:42:301 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:27:42:398 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:27:42:734 ==>> $GBGGA,152746.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,40,16,,,39,1*4C

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,35,12,,,34,24,,,34,1*7E

$GBGSV,6,6,24,4,,,34,5,,,33,33,,,32,44,,,30,1*74

$GBRMC,152746.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152746.511,0.000,1568.534,1568.534,50.205,2097152,2097152,2097152*55

[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,CC057790A5C0,-83
+WIFISCAN:4,1,CC057790A821,-84
+WIFISCAN:4,2,F86FB0660A82,-85
+WIFISCAN:4,3,CC057790A820,-86

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 23:27:42:914 ==>> [D][05:18:16][COMM]read battery soc:255


2025-07-31 23:27:43:347 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:27:43:457 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:27:43:533 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2399mV
OVER 150


2025-07-31 23:27:43:609 ==>> 原始值:【2399】, 乘以分压基数【2】还原值:【4798】
2025-07-31 23:27:43:639 ==>> $GBGGA,152747.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,40,16,,,40,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,35,12,,,34,24,,,34,1*7E

$GBGSV,6,6,24,4,,,34,5,,,33,33,,,32,44,,,30,1*74

$GBRMC,152747.511,V,,,,,,,,0.0,E,N,V*49


2025-07-31 23:27:43:669 ==>> 
$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152747.511,0.000,1570.262,1570.262,50.261,2097152,2097152,2097152*56

[D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 23:27:43:679 ==>> 【读取AD_V15电压(前)】通过,【4798mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:27:43:684 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:27:43:703 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:27:43:790 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:27:44:007 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:27:44:671 ==>> $GBGGA,152748.511,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,40,16,,,40,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,38,1*71

$GBGSV,6,5,24,9,,,36,2,,,36,24,,,35,12,,,34,1*7C

$GBGSV,6,6,24,4,,,34,5,,,33,33,,,33,44,,,31,1*74

$GBRMC,152748.511,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152748.511,0.000,1578.887,1578.887,50.524,2097152,2097152,2097152*5F



2025-07-31 23:27:44:716 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:27:44:823 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:27:44:931 ==>> 1A A1 01 00 00 
Get AD_V16 2428mV
OVER 150
[W][05:18:18][COMM]>>>>>Input command = ?<<<<<
[D][05:18:18][COMM]read battery soc:255


2025-07-31 23:27:44:976 ==>> 原始值:【2428】, 乘以分压基数【2】还原值:【4856】
2025-07-31 23:27:44:999 ==>> 【读取AD_V16电压(前)】通过,【4856mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:27:45:002 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:27:45:005 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:45:342 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:18][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:18][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:18][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:18][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:18][COMM]adc read battery ts volt:19 mv
[D][05:18:18][COMM]adc read in 24v adc:1314  volt:33234 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3087  volt:5426 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:27:45:606 ==>> 【转刹把供电电压(主控ADC)】通过,【5426mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:27:45:610 ==>> 检测【转刹把供电电压】
2025-07-31 23:27:45:615 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:45:665 ==>> $GBGGA,152749.511,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,38,1*71

$GBGSV,6,5,24,9,,,36,2,,,36,24,,,34,12,,,34,1*7D

$GBGSV,6,6,24,4,,,34,5,,,33,33,,,33,44,,,31,1*74

$GBRMC,152749.511,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152749.511,0.000,1573.705,1573.705,50.359,2097152,2097152,2097152*52



2025-07-31 23:27:45:891 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:18][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:18][COMM]adc read left brake adc:20  volt:26 mv
[D][05:18:18][COMM]adc read right brake adc:26  volt:34 mv
[D][05:18:18][COMM]adc read throttle adc:23  volt:30 mv
[D][05:18:18][COMM]adc read battery ts volt:23 mv
[D][05:18:18][COMM]adc read in 24v adc:1307  volt:33057 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3089  volt:5429 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2450  volt:3947 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:27:46:141 ==>> 【转刹把供电电压】通过,【5429mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:27:46:146 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:27:46:154 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:27:46:310 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:27:46:419 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:27:46:423 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:27:46:427 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:27:46:523 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:27:46:631 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:27:46:691 ==>> $GBGGA,152750.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,36,12,,,35,24,,,34,1*7C

$GBGSV,6,6,24,4,,,33,5,,,33,33,,,33,44,,,31,1*73

$GBRMC,152750.511,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152750.511,0.000,1571.979,1571.979,50.305,2097152,2097152,2097152*53

[D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:27:46:736 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:27:46:751 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:27:46:826 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:27:46:859 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:27:46:863 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:27:46:868 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:27:46:931 ==>> [D][05:18:20][COMM]read battery soc:255


2025-07-31 23:27:46:961 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:27:47:022 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:27:47:085 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:27:47:093 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:27:47:115 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:27:47:220 ==>> 3A A3 03 01 A3 


2025-07-31 23:27:47:325 ==>> ON_OUT3
OVER 150


2025-07-31 23:27:47:358 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:27:47:362 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:27:47:365 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:27:47:430 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:27:47:634 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:27:47:638 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:27:47:645 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:27:47:673 ==>> $GBGGA,152751.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,2,,,35,12,,,35,24,,,34,1*7F

$GBGSV,7,6,25,4,,,33,5,,,33,33,,,33,44,,,31,1*73

$GBGSV,7,7,25,32,,,36,1*75

$GBRMC,152751.511,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152751.511,0.000,1566.793,1566.793,50.136,2097152,2097152,2097152*50



2025-07-31 23:27:47:718 ==>> 3A A3 05 01 A3 


2025-07-31 23:27:47:823 ==>> ON_OUT5
OVER 150


2025-07-31 23:27:47:916 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:27:47:920 ==>> 检测【左刹电压测试1】
2025-07-31 23:27:47:926 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:48:236 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:18:21][COMM]adc read out 24v adc:14  volt:354 mv
[D][05:18:21][COMM]adc read left brake adc:1739  volt:2292 mv
[D][05:18:21][COMM]adc read right brake adc:1739  volt:2292 mv
[D][05:18:21][COMM]adc read throttle adc:1735  volt:2287 mv
[D][05:18:21][COMM]adc read battery ts volt:26 mv
[D][05:18:21][COMM]adc read in 24v adc:1313  volt:33209 mv
[D][05:18:21][COMM]adc read throttle brake in adc:20  volt:35 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:18  volt:14 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2449  volt:3946 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:27:48:453 ==>> 【左刹电压测试1】通过,【2292】符合目标值【2250】至【2500】要求!
2025-07-31 23:27:48:457 ==>> 检测【右刹电压测试1】
2025-07-31 23:27:48:476 ==>> 【右刹电压测试1】通过,【2292】符合目标值【2250】至【2500】要求!
2025-07-31 23:27:48:479 ==>> 检测【转把电压测试1】
2025-07-31 23:27:48:494 ==>> 【转把电压测试1】通过,【2287】符合目标值【2250】至【2500】要求!
2025-07-31 23:27:48:498 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:27:48:517 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:27:48:681 ==>> $GBGGA,152752.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,6,2,24,59,,,41,34,,,41,11,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,35,12,,,35,24,,,34,1*7F

$GBGSV,6,6,24,4,,,33,5,,,33,33,,,33,44,,,32,1*70

$GBRMC,152752.511,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152752.511,0.000,1568.516,1568.516,50.187,2097152,2097152,2097152*59

3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:27:48:776 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:27:48:782 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:27:48:787 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:27:48:832 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:27:48:922 ==>> [D][05:18:22][COMM]read battery soc:255


2025-07-31 23:27:49:046 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:27:49:050 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:27:49:055 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:27:49:135 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:27:49:326 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:27:49:330 ==>> 检测【左刹电压测试2】
2025-07-31 23:27:49:334 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:27:49:704 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:22][COMM]adc read out 24v adc:17  volt:429 mv
[D][05:18:22][COMM]adc read left brake adc:22  volt:29 mv
[D][05:18:22][COMM]adc read right brake adc:18  volt:23 mv
[D][05:18:22][COMM]adc read throttle adc:24  volt:31 mv
[D][05:18:22][COMM]adc read battery ts volt:26 mv
[D][05:18:22][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:22][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:19  volt:15 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2450  volt:3947 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,152753.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,6,2,24,11,,,41,3,,,41,59,,,40,34,,,40,1*4C

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,9,,,36,2,,,35,12,,,3

2025-07-31 23:27:49:749 ==>> 4,24,,,34,1*7E

$GBGSV,6,6,24,4,,,33,5,,,33,33,,,33,44,,,32,1*70

$GBRMC,152753.511,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152753.511,0.000,1565.062,1565.062,50.077,2097152,2097152,2097152*56



2025-07-31 23:27:49:858 ==>> 【左刹电压测试2】通过,【29】符合目标值【0】至【50】要求!
2025-07-31 23:27:49:861 ==>> 检测【右刹电压测试2】
2025-07-31 23:27:49:876 ==>> 【右刹电压测试2】通过,【23】符合目标值【0】至【50】要求!
2025-07-31 23:27:49:879 ==>> 检测【转把电压测试2】
2025-07-31 23:27:49:894 ==>> 【转把电压测试2】通过,【31】符合目标值【0】至【50】要求!
2025-07-31 23:27:49:898 ==>> 检测【晶振检测】
2025-07-31 23:27:49:901 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:27:50:086 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 23:27:50:164 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:27:50:168 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:27:50:173 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:27:50:236 ==>> 1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1672mV
Get AD_V4 1647mV
Get AD_V5 2760mV
Get AD_V6 2022mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:27:50:438 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:27:50:442 ==>> 检测【检测BootVer】
2025-07-31 23:27:50:454 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:50:822 ==>> $GBGGA,152754.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,11,,,41,3,,,41,59,,,41,34,,,41,1*4C

$GBGSV,6,3,24,60,,,40,7,,,40,16,,,40,23,,,39,1*4C

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,38,1*71

$GBGSV,6,5,24,9,,,36,2,,,36,12,,,35,24,,,35,1*7D

$GBGSV,6,6,24,4,,,33,5,,,33,33,,,33,44,,,32,1*70

$GBRMC,152754.511,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152754.511,0.000,1580.611,1580.611,50.577,2097152,2097152,2097152*54

[W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539517
[D][05:18:23][FCTY]HardwareID  = 867222087631489
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = D5BD9

2025-07-31 23:27:50:912 ==>> D17E3CF
[D][05:18:23][FCTY]Bat         = 4044 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 33, adc = 1316
[D][05:18:23][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 177 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3775 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:27:50:942 ==>>                                          

2025-07-31 23:27:51:042 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:27:51:047 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:27:51:051 ==>> 检测【检测固件版本】
2025-07-31 23:27:51:097 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:27:51:102 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:27:51:106 ==>> 检测【检测蓝牙版本】
2025-07-31 23:27:51:159 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:27:51:162 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:27:51:166 ==>> 检测【检测MoBikeId】
2025-07-31 23:27:51:196 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:27:51:199 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:27:51:203 ==>> 检测【检测蓝牙地址】
2025-07-31 23:27:51:210 ==>> 取到目标值:D5BD9D17E3CF
2025-07-31 23:27:51:230 ==>> 【检测蓝牙地址】通过,【D5BD9D17E3CF】符合目标值【】要求!
2025-07-31 23:27:51:234 ==>> 提取到蓝牙地址:D5BD9D17E3CF
2025-07-31 23:27:51:240 ==>> 检测【BOARD_ID】
2025-07-31 23:27:51:326 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:27:51:330 ==>> 检测【检测充电电压】
2025-07-31 23:27:51:392 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:27:51:396 ==>> 检测【检测VBUS电压1】
2025-07-31 23:27:51:468 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:27:51:473 ==>> 检测【检测充电电流】
2025-07-31 23:27:51:524 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:27:51:528 ==>> 检测【检测IMEI】
2025-07-31 23:27:51:531 ==>> 取到目标值:867222087631489
2025-07-31 23:27:51:622 ==>> 【检测IMEI】通过,【867222087631489】符合目标值【】要求!
2025-07-31 23:27:51:626 ==>> 提取到IMEI:867222087631489
2025-07-31 23:27:51:629 ==>> 检测【检测IMSI】
2025-07-31 23:27:51:633 ==>> 取到目标值:460130071539517
2025-07-31 23:27:51:666 ==>> $GBGGA,152755.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,42,43,,,41,25,,,41,1*78

$GBGSV,6,2,24,11,,,41,59,,,41,34,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,16,,,40,23,,,40,1*42

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,38,1*71

$GBGSV,6,5,24,9,,,36,2,,,36,12,,,35,24,,,35,1*7D

$GBGSV,6,6,24,5,,,34,4,,,33,33,,,33,44,,,32,1*77

$GBRMC,152755.511,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152755.511,0.000,1582.335,1582.335,50.628,2097152,2097152,2097152*5C



2025-07-31 23:27:51:724 ==>> 【检测IMSI】通过,【460130071539517】符合目标值【】要求!
2025-07-31 23:27:51:728 ==>> 提取到IMSI:460130071539517
2025-07-31 23:27:51:732 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:27:51:735 ==>> 取到目标值:460130071539517
2025-07-31 23:27:51:816 ==>> 【校验网络运营商(移动)】通过,【460130071539517】符合目标值【】要求!
2025-07-31 23:27:51:822 ==>> 检测【打开CAN通信】
2025-07-31 23:27:51:827 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:27:51:926 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:27:52:097 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:27:52:102 ==>> 检测【检测CAN通信】
2025-07-31 23:27:52:108 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:27:52:220 ==>> can send success


2025-07-31 23:27:52:250 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:27:52:325 ==>> [D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36443
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:27:52:373 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:27:52:379 ==>> 检测【关闭CAN通信】
2025-07-31 23:27:52:388 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:27:52:394 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:27:52:430 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:27:52:646 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:27:52:651 ==>> 检测【打印IMU STATE】
2025-07-31 23:27:52:673 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:27:52:677 ==>> $GBGGA,152756.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,42,43,,,41,25,,,41,1*79

$GBGSV,6,2,24,11,,,41,59,,,41,34,,,41,3,,,40,1*4D

$GBGSV,6,3,24,60,,,40,7,,,40,23,,,40,16,,,39,1*4C

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,38,1*71

$GBGSV,6,5,24,9,,,36,2,,,36,12,,,35,24,,,35,1*7D

$GBGSV,6,6,24,5,,,33,4,,,33,33,,,33,44,,,32,1*70

$GBRMC,152756.511,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152756.511,0.000,1577.152,1577.152,50.462,2097152,2097152,2097152*53



2025-07-31 23:27:52:822 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:27:52:916 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:27:52:924 ==>> 检测【六轴自检】
2025-07-31 23:27:52:945 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:27:52:951 ==>> [D][05:18:26][COMM]read battery soc:255


2025-07-31 23:27:53:122 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:27:53:674 ==>> $GBGGA,152757.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,7,2,25,11,,,41,59,,,41,34,,,41,3,,,41,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,2,,,35,12,,,35,24,,,35,1*7E

$GBGSV,7,6,25,4,,,34,5,,,33,33,,,33,44,,,32,1*77

$GBGSV,7,7,25,32,,,36,1*75

$GBRMC,152757.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152757.511,0.000,1573.695,1573.695,50.349,2097152,2097152,2097152*5C



2025-07-31 23:27:54:679 ==>> $GBGGA,152758.511,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,11,,,40,34,,,40,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,2,,,35,12,,,34,24,,,34,1*7E

$GBGSV,7,6,25,4,,,33,5,,,33,33,,,33,44,,,31,1*73

$GBGSV,7,7,25,32,,,36,1*75

$GBRMC,152758.511,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152758.511,0.000,1561.610,1561.610,49.969,2097152,2097152,2097152*53



2025-07-31 23:27:54:830 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:27:54:995 ==>> [D][05:18:28][COMM]read battery soc:255
[D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39107 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-4,-5,4048]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 23:27:55:030 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:27:55:034 ==>> 检测【打印IMU STATE2】
2025-07-31 23:27:55:037 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:27:55:224 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:27:55:302 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:27:55:306 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:27:55:313 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:27:55:421 ==>> 5A A5 02 5A A5 


2025-07-31 23:27:55:526 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:27:55:574 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:27:55:582 ==>> 检测【检测VBUS电压2】
2025-07-31 23:27:55:604 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:55:891 ==>> $GBGGA,152759.511,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,43,,,41,25,,,41,1*7A

$GBGSV,7,2,25,59,,,41,11,,,41,34,,,41,3,,,40,1*4D

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,2,,,36,24,,,35,12,,,34,1*7C

$GBGSV,7,6,25,4,,,33,5,,,33,33,,,32,44,,,31,1*72

$GBGSV,7,7,25,32,,,36,1*75

$GBRMC,152759.511,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152759.511,0.000,1566.795,1566.795,50.138,2097152,2097152,2097152*56

[D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]

2025-07-31 23:27:55:996 ==>> ==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539517
[D][05:18:28][FCTY]HardwareID  = 867222087631489
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 9, adc = 388
[D][05:18:28][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 278 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3775 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========

2025-07-31 23:27:56:057 ==>> 
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
                                                                       

2025-07-31 23:27:56:105 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:56:485 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539517
[D][05:18:29][FCTY]HardwareID  = 867222087631489
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 9000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 188
[D][05:18:29][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 202 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = 

2025-07-31 23:27:56:530 ==>> C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3775 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:27:56:635 ==>>            00.511,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,41,43,,,41,25,,,41,1*7B

$GBGSV,7,2,25,59,,,41,11,,,41,34,,,41,3,,,41,1*4C

$GBGSV,7,3,25,60,,,40,7,,,40,23,,,39,16,,,39,1*42

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,2,,,35,24,,,35,12,,,35,1*7E

$GBGSV,7,6,25,4,,,33,5,,,33,33,,,33,44,,,31,1*7

2025-07-31 23:27:56:648 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:56:680 ==>> 3

$GBGSV,7,7,25,32,,,36,1*75

$GBRMC,152800.511,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152800.511,0.000,1571.978,1571.978,50.304,2097152,2097152,2097152*58



2025-07-31 23:27:57:013 ==>> [D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539517
[D][05:18:29][FCTY]HardwareID  = 867222087631489
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 9000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:29][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 202 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][

2025-07-31 23:27:57:058 ==>> 05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3775 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:27:57:240 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:57:319 ==>> [D][05:18:30][COMM]msg 0601 loss. last_tick:36431. cur_tick:41444. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41444


2025-07-31 23:27:57:949 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539517
[D][05:18:30][FCTY]HardwareID  = 867222087631489
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 9000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 58,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 135
[D][05:18:30][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 252 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSI

2025-07-31 23:27:58:043 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:58:055 ==>> ON = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3775 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:

2025-07-31 23:27:58:159 ==>> 30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 

2025-07-31 23:27:58:264 ==>> [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5E0AFE1DA104B48B5124ADCA9E7DBC25D212399D16575F6E4B58C69C495BE0

2025-07-31 23:27:58:339 ==>> 6EA016FDEC6562F04D6961C34E2307134C470550DE029764D356B88524778B8A194B6A36593F5449CCA330A529A474A5B794856
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][GNSS]recv submsg id[1]
[D][05:18:30][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:30][GNSS]location stop evt done evt
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]


2025-07-31 23:27:58:627 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539517
[D][05:18:31][FCTY]HardwareID  = 867222087631489
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:31][FCTY]Bat         = 3844 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 5000 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 115
[D][05:18:31][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 278 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]C

2025-07-31 23:27:58:672 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3775 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:27:58:827 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:27:59:192 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071539517
[D][05:18:32][FCTY]HardwareID  = 867222087631489
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:18:32][FCTY]Bat         = 3864 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 5000 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 2, adc = 111
[D][05:18:32][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 278 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         =

2025-07-31 23:27:59:222 ==>>  3775 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:27:59:362 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:27:59:367 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:27:59:375 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:27:59:420 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:27:59:510 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32


2025-07-31 23:27:59:571 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:27:59:638 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:27:59:643 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:27:59:665 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:27:59:721 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:27:59:921 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:27:59:925 ==>> 检测【打开WIFI(3)】
2025-07-31 23:27:59:929 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:28:00:151 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:28:00:199 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:28:00:205 ==>> 检测【扩展芯片hw】
2025-07-31 23:28:00:213 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:28:00:427 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:28:00:476 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:28:00:481 ==>> 检测【扩展芯片boot】
2025-07-31 23:28:00:495 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:28:00:499 ==>> 检测【扩展芯片sw】
2025-07-31 23:28:00:566 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:28:00:571 ==>> 检测【检测音频FLASH】
2025-07-31 23:28:00:579 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:28:00:719 ==>> [D][05:18:33][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:28:00:946 ==>> +WIFISCAN:4,0,F88C21BCF57D,-29
+WIFISCAN:4,1,CC057790A7C1,-72
+WIFISCAN:4,2,CC057790A5C0,-80
+WIFISCAN:4,3,F86FB0660A82,-83

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 23:28:01:111 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:28:01:804 ==>>                                                                                                 ey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:108

2025-07-31 23:28:01:909 ==>> 00
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task rec

2025-07-31 23:28:02:015 ==>> eive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_se

2025-07-31 23:28:02:120 ==>> rvice_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd se

2025-07-31 23:28:02:225 ==>> nd:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18

2025-07-31 23:28:02:300 ==>> :34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255
[D][05:18:34][GNSS]recv submsg id[3]


2025-07-31 23:28:03:092 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B37EA717D3BE2910739DBFEED15C3B8895292262F09D44E48593A5A5136EE1BC3C7FA04196

2025-07-31 23:28:03:167 ==>> 2BF9355A42C9C9855270E47402C8C481AA5C2B3FFC3170B62885D799699B38D8D9F30AA8832B3273AD143F3566E2
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 23:28:03:592 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 23:28:04:004 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:28:04:186 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:28:04:841 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 23:28:05:314 ==>> [D][05:18:38][COMM]49326 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:28:05:589 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 23:28:05:674 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:28:05:680 ==>> 检测【打开喇叭声音】
2025-07-31 23:28:05:688 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:28:06:336 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:38][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[D][05:18:38][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_st

2025-07-31 23:28:06:441 ==>> art].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991].

2025-07-31 23:28:06:470 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:28:06:477 ==>> 检测【打开大灯控制】
2025-07-31 23:28:06:484 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:28:06:546 ==>>  send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]50338 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 23:28:06:636 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:28:06:745 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:28:06:751 ==>> 检测【关闭仪表供电3】
2025-07-31 23:28:06:756 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:28:06:892 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 23:28:07:016 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:28:07:024 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:28:07:050 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:28:07:183 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:28:07:298 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:28:07:304 ==>> 检测【读大灯电压】
2025-07-31 23:28:07:308 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:28:07:516 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[32783]


2025-07-31 23:28:07:579 ==>> 【读大灯电压】通过,【32783mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:28:07:584 ==>> 检测【关闭大灯控制2】
2025-07-31 23:28:07:589 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:28:07:606 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:28:07:786 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:28:07:850 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:28:07:855 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:28:07:864 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:28:08:016 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:28:08:135 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:28:08:141 ==>> 检测【打开WIFI(4)】
2025-07-31 23:28:08:154 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:28:08:316 ==>>               PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B366B20A7209CE9419E8FD40EBAF0821612392422F86C5498D56F16A4D4136B5067A10DD10D6D4BBD6B6FE8649786ACDE2856E58BCFE6A57E2D

2025-07-31 23:28:08:376 ==>> CA1F584C507A3FC40E2EBC66F41A95F41E8BFD1E5EB23036B5B
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 23:28:08:481 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: fu

2025-07-31 23:28:08:511 ==>> nc id: 12, ret: 6


2025-07-31 23:28:08:759 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:28:08:765 ==>> 检测【EC800M模组版本】
2025-07-31 23:28:08:770 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:28:08:802 ==>> [D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:28:08:907 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:28:09:012 ==>> [D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 23:28:09:289 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:28:09:295 ==>> 检测【配置蓝牙地址】
2025-07-31 23:28:09:303 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:28:09:493 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D5BD9D17E3CF>】
2025-07-31 23:28:09:508 ==>> +WIFISCAN:4,0,F88C21BCF57D,-35
+WIFISCAN:4,1,F42A7D1297A3,-69
+WIFISCAN:4,2,CC057790A5C1,-81
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:42][CAT1]wifi scan report total[4]
[W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:28:09:613 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 23:28:09:719 ==>> [D][05:18:42][GNSS]recv submsg id[3]
recv ble 1
recv ble 2
ble set mac ok :d5,bd,9d,17,e3,cf
enable filters ret : 0

2025-07-31 23:28:09:781 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:28:09:792 ==>> 检测【BLETEST】
2025-07-31 23:28:09:812 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:28:09:821 ==>> [D][05:18:42][COMM]53948 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:28:09:913 ==>> 4A A4 01 A4 4A 


2025-07-31 23:28:10:018 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D5BD9D17E3CF*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D5BD9D17E3CF99999OVER 150


2025-07-31 23:28:10:827 ==>> [D][05:18:43][COMM]54959 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:28:10:833 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:28:10:844 ==>> 该项需要延时执行
2025-07-31 23:28:11:451 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:28:11:601 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:28:11:831 ==>> [D][05:18:44][COMM]55972 imu init OK


2025-07-31 23:28:13:552 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3F108C50FACEF736C4A87D126B804C7B73EA6021B8D2C8A0E612561A6AA944CF7A823

2025-07-31 23:28:13:642 ==>> 55B3367D27C8A70ABB9257792D5F4BD9D18B3D409C0957AA5854DA43C9F384679B56ED63D7D666F1B3B391F805E0EF9B
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
                                         

2025-07-31 23:28:15:628 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:28:17:624 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:28:18:790 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5FE7CDC35EB038AFF5969C

2025-07-31 23:28:18:865 ==>> 4295C00A7AB69DD585DB6F09F23CF4CA029D738D21F83CA70F8D78C257F43D016DCC299A6A6D73DB659FE94814910F21EB958037E81F7D399029CF987A11D487B07CC3323E2EBD1
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]


2025-07-31 23:28:19:620 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 23:28:20:846 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:28:20:852 ==>> 检测【检测WiFi结果】
2025-07-31 23:28:20:857 ==>> WiFi信号:【CC057790A5C0】,信号值:-83
2025-07-31 23:28:20:880 ==>> WiFi信号:【CC057790A821】,信号值:-84
2025-07-31 23:28:20:885 ==>> WiFi信号:【F86FB0660A82】,信号值:-85
2025-07-31 23:28:20:899 ==>> WiFi信号:【CC057790A820】,信号值:-86
2025-07-31 23:28:20:926 ==>> WiFi信号:【F88C21BCF57D】,信号值:-29
2025-07-31 23:28:20:940 ==>> WiFi信号:【CC057790A7C1】,信号值:-72
2025-07-31 23:28:20:949 ==>> WiFi信号:【F42A7D1297A3】,信号值:-69
2025-07-31 23:28:20:958 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 23:28:20:982 ==>> WiFi数量【8】, 最大信号值:-29
2025-07-31 23:28:20:994 ==>> 检测【检测GPS结果】
2025-07-31 23:28:21:016 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:28:21:027 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 23:28:21:646 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:28:21:857 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:28:21:866 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:21:891 ==>> 定位已等待【1】秒.
2025-07-31 23:28:22:233 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:28:22:860 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:22:869 ==>> 定位已等待【2】秒.
2025-07-31 23:28:22:952 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:28:23:874 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:23:883 ==>> 定位已等待【3】秒.
2025-07-31 23:28:24:010 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:56][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

[D][05:18:56][CAT1]<<< 
OK

$GBGSV,3,1,09,25,,,42,40,,,42,39,,,41,23,,,40,1*74

$GBGSV,3,2,09,34,,,16,60,,,44,59,,,40,11,,,38,1*7B

$GBGSV,3,3,09,32,,,38,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1501.156,1501.156,48.378,2097152,2097152,2097152*4F

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 23, ret: 6
[D][05:18:56][CAT1]sub id: 23, ret: 6

[D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================

2025-07-31 23:28:24:115 ==>> ================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5CFDB6FE08AE6A706DBACCBAF8EE99760B339B9D73EE9310DE85B22C11BA724A77786B3284E41ADC004CC08FBB8BCDF0AB5994DC2480998002A3C56337DF5B9C8C533CBDBDF117F4487BB352556E4C1CF529C
[D][05:18:56][GNSS]recv submsg id[1]
[D][05:18:56][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT

2025-07-31 23:28:24:161 ==>> 1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 23:28:24:762 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,60,,,42,40,,,42,25,,,41,39,,,41,1*74

$GBGSV,5,2,18,43,,,41,7,,,40,23,,,39,11,,,39,1*48

$GBGSV,5,3,18,10,,,38,34,,,38,1,,,37,6,,,37,1*78

$GBGSV,5,4,18,4,,,36,2,,,36,41,,,67,59,,,40,1*74

$GBGSV,5,5,18,32,,,38,3,,,38,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1619.789,1619.789,51.771,2097152,2097152,2097152*4A



2025-07-31 23:28:24:882 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:24:891 ==>> 定位已等待【4】秒.
2025-07-31 23:28:25:764 ==>> [D][05:18:58][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,42,60,,,41,25,,,41,39,,,41,1*76

$GBGSV,5,2,19,43,,,41,3,,,41,59,,,40,7,,,40,1*76

$GBGSV,5,3,19,11,,,40,34,,,40,41,,,39,23,,,39,1*7B

$GBGSV,5,4,19,10,,,38,1,,,37,6,,,37,4,,,35,1*40

$GBGSV,5,5,19,2,,,35,32,,,33,5,,,32,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1597.231,1597.231,51.090,2097152,2097152,2097152*42



2025-07-31 23:28:25:884 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:25:893 ==>> 定位已等待【5】秒.
2025-07-31 23:28:26:787 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,60,,,41,25,,,41,39,,,41,1*7E

$GBGSV,6,2,21,43,,,41,3,,,40,59,,,40,7,,,40,1*7F

$GBGSV,6,3,21,11,,,40,34,,,40,23,,,39,41,,,38,1*72

$GBGSV,6,4,21,10,,,38,16,,,38,1,,,37,6,,,37,1*76

$GBGSV,6,5,21,2,,,35,4,,,34,32,,,34,5,,,32,1*43

$GBGSV,6,6,21,9,,,38,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1591.988,1591.988,50.917,2097152,2097152,2097152*45



2025-07-31 23:28:26:892 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:26:902 ==>> 定位已等待【6】秒.
2025-07-31 23:28:27:817 ==>> [D][05:19:00][COMM]read battery soc:255
$GBGGA,152831.638,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,60,,,41,25,,,41,39,,,41,1*7D

$GBGSV,6,2,22,43,,,41,3,,,41,11,,,41,34,,,41,1*41

$GBGSV,6,3,22,59,,,40,7,,,40,23,,,40,16,,,39,1*40

$GBGSV,6,4,22,41,,,38,10,,,38,1,,,37,6,,,37,1*77

$GBGSV,6,5,22,2,,,35,32,,,35,9,,,34,4,,,33,1*4C

$GBGSV,6,6,22,5,,,32,12,,,32,1*40

$GBRMC,152831.638,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152831.638,0.000,789.070,789.070,721.622,2097152,2097152,2097152*62



2025-07-31 23:28:27:907 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:27:918 ==>> 定位已等待【7】秒.
2025-07-31 23:28:28:717 ==>> $GBGGA,152832.538,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,3,,,41,39,,,41,59,,,41,1*45

$GBGSV,6,2,24,34,,,41,25,,,41,43,,,41,7,,,40,1*45

$GBGSV,6,3,24,60,,,40,11,,,40,23,,,40,16,,,39,1*7B

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,2,,,35,9,,,35,32,,,35,44,,,33,1*7F

$GBGSV,6,6,24,4,,,33,12,,,33,5,,,32,26,,,39,1*7D

$GBRMC,152832.538,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152832.538,0.000,786.246,786.246,719.040,2097152,2097152,2097152*6B



2025-07-31 23:28:28:913 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:28:923 ==>> 定位已等待【8】秒.
2025-07-31 23:28:29:246 ==>> [D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980221
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]tx

2025-07-31 23:28:29:321 ==>>  ret[17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BEEEEA26B9735CDEDF78776ECD234F9AE2D453F6C6820D2B7EF8FEC52850E83260B0CC1BDB8BF0D79156EBD4D9DFFAEABFBBCB
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]


2025-07-31 23:28:29:721 ==>> $GBGGA,152833.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,43,,,41,7,,,40,3,,,40,60,,,40,1*70

$GBGSV,7,3,25,59,,,40,11,,,40,16,,,39,23,,,39,1*7F

$GBGSV,7,4,25,10,,,38,41,,,38,6,,,37,1,,,37,1*71

$GBGSV,7,5,25,32,,,36,2,,,35,9,,,35,4,,,33,1*48

$GBGSV,7,6,25,12,,,33,5,,,32,44,,,32,33,,,32,1*47

$GBGSV,7,7,25,26,,,36,1*70

$GBRMC,152833.518,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152833.518,0.000,777.632,777.632,711.162,2097152,2097152,2097152*61

[D][05:19:02][COMM]read battery soc:255


2025-07-31 23:28:29:916 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:29:926 ==>> 定位已等待【9】秒.
2025-07-31 23:28:30:691 ==>> $GBGGA,152834.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,25,,,41,1*71

$GBGSV,7,2,25,43,,,41,7,,,40,3,,,40,60,,,40,1*70

$GBGSV,7,3,25,34,,,40,11,,,40,16,,,39,23,,,39,1*74

$GBGSV,7,4,25,10,,,38,1,,,38,6,,,37,41,,,37,1*71

$GBGSV,7,5,25,32,,,36,2,,,35,9,,,35,4,,,33,1*48

$GBGSV,7,6,25,12,,,33,33,,,33,5,,,32,44,,,32,1*46

$GBGSV,7,7,25,26,,,,1*75

$GBRMC,152834.518,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152834.518,0.000,778.490,778.490,711.947,2097152,2097152,2097152*69



2025-07-31 23:28:30:920 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:30:929 ==>> 定位已等待【10】秒.
2025-07-31 23:28:31:719 ==>> $GBGGA,152835.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,25,,,41,43,,,41,1*7A

$GBGSV,6,2,24,7,,,40,3,,,40,60,,,40,59,,,40,1*7A

$GBGSV,6,3,24,34,,,40,11,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,32,,,36,2,,,35,9,,,35,12,,,34,1*78

$GBGSV,6,6,24,4,,,33,33,,,33,5,,,32,44,,,31,1*72

$GBRMC,152835.518,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152835.518,0.000,778.490,778.490,711.947,2097152,2097152,2097152*68

[D][05:19:04][COMM]read battery soc:255


2025-07-31 23:28:31:931 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:31:941 ==>> 定位已等待【11】秒.
2025-07-31 23:28:32:717 ==>> $GBGGA,152832.518,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,25,,,41,43,,,41,7,,,40,60,,,40,1*45

$GBGSV,7,3,25,3,,,40,11,,,40,23,,,40,16,,,39,1*4E

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,12,,,34,1*7B

$GBGSV,7,6,25,24,,,33,4,,,33,33,,,33,5,,,32,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152832.518,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152832.518,139458,777.979,777.979,711.481,2097152,2097152,2097152*44



2025-07-31 23:28:32:944 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:32:955 ==>> 定位已等待【12】秒.
2025-07-31 23:28:33:715 ==>> $GBGGA,152833.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,25,,,41,43,,,41,7,,,40,3,,,40,1*70

$GBGSV,7,3,25,60,,,40,11,,,40,23,,,40,16,,,39,1*7B

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,24,,,34,1*7E

$GBGSV,7,6,25,12,,,34,4,,,33,33,,,33,5,,,32,1*74

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152833.518,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152833.518,2266250,778.804,778.804,712.235,2097152,2097152,2097152*7A

[D][05:19:06][COMM]read battery soc:255


2025-07-31 23:28:33:960 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:33:971 ==>> 定位已等待【13】秒.
2025-07-31 23:28:34:414 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980221
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 23:28:34:699 ==>> $GBGGA,152834.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,2,25,59,,,40,34,,,40,25,,,40,11,,,40,1*78

$GBGSV,7,3,25,43,,,40,7,,,39,16,,,39,23,,,39,1*4D

$GBGSV,7,4,25,10,,,38,1,,,38,6,,,37,41,,,37,1*71

$GBGSV,7,5,25,2,,,35,9,,,35,32,,,35,24,,,34,1*7E

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152834.518,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152834.518,2266197,772.173,772.173,706.170,2097152,2097152,2097152*72



2025-07-31 23:28:34:972 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:34:983 ==>> 定位已等待【14】秒.
2025-07-31 23:28:35:735 ==>> $GBGGA,152835.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,2,25,59,,,40,34,,,40,25,,,40,11,,,40,1*78

$GBGSV,7,3,25,43,,,40,7,,,39,16,,,39,23,,,39,1*4D

$GBGSV,7,4,25,10,,,38,1,,,38,6,,,37,41,,,37,1*71

$GBGSV,7,5,25,32,,,36,2,,,35,9,,,35,24,,,34,1*7D

$GBGSV,7,6,25,12,,,33,4,,,33,33,,,33,5,,,32,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152835.518,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152835.518,2266146,771.350,771.350,705.418,2097152,2097152,2097152*77

[D][05:19:08][COMM]read battery soc:255


2025-07-31 23:28:35:976 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:35:988 ==>> 定位已等待【15】秒.
2025-07-31 23:28:36:717 ==>> $GBGGA,152836.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,25,,,41,43,,,41,7,,,40,3,,,40,1*70

$GBGSV,7,3,25,60,,,40,11,,,40,16,,,39,23,,,39,1*75

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,32,,,36,2,,,35,9,,,35,24,,,34,1*7D

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152836.518,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152836.518,2310358,777.974,777.974,711.475,2097152,2097152,2097152*77



2025-07-31 23:28:36:989 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:36:999 ==>> 定位已等待【16】秒.
2025-07-31 23:28:37:707 ==>> $GBGGA,152837.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,43,,,41,1*7A

$GBGSV,7,2,25,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,7,3,25,34,,,40,11,,,40,16,,,39,23,,,39,1*74

$GBGSV,7,4,25,10,,,38,1,,,38,6,,,37,41,,,37,1*71

$GBGSV,7,5,25,32,,,36,2,,,35,24,,,35,9,,,35,1*7C

$GBGSV,7,6,25,12,,,34,4,,,33,33,,,33,5,,,32,1*74

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152837.518,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152837.518,2310281,775.489,775.489,709.202,2097152,2097152,2097152*7C

                                         

2025-07-31 23:28:38:000 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:38:010 ==>> 定位已等待【17】秒.
2025-07-31 23:28:38:700 ==>> $GBGGA,152838.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,7,3,25,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,7,4,25,10,,,38,41,,,38,6,,,37,1,,,37,1*71

$GBGSV,7,5,25,2,,,35,24,,,35,9,,,35,32,,,35,1*7F

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152838.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152838.518,2310202,775.487,775.487,709.201,2097152,2097152,2097152*7B



2025-07-31 23:28:39:000 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:39:011 ==>> 定位已等待【18】秒.
2025-07-31 23:28:39:706 ==>> $GBGGA,152839.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,43,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,7,3,25,59,,,40,11,,,40,16,,,39,23,,,39,1*7F

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152839.518,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152839.518,2310125,777.973,777.973,711.474,2097152,2097152,2097152*71

                                         

2025-07-31 23:28:40:011 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:40:024 ==>> 定位已等待【19】秒.
2025-07-31 23:28:40:699 ==>> $GBGGA,152840.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,25,,,41,43,,,41,7,,,40,60,,,40,1*45

$GBGSV,7,3,25,3,,,40,11,,,40,23,,,40,16,,,39,1*4E

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,5,,,33,12,,,33,4,,,33,33,,,33,1*72

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152840.518,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152840.518,2310046,778.806,778.806,712.237,2097152,2097152,2097152*79



2025-07-31 23:28:41:017 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:41:027 ==>> 定位已等待【20】秒.
2025-07-31 23:28:41:699 ==>> $GBGGA,152841.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,7,2,25,43,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,7,3,25,59,,,40,11,,,40,23,,,40,16,,,39,1*71

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,5,,,33,12,,,33,4,,,33,33,,,33,1*72

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152841.518,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152841.518,2120230,777.977,777.977,711.478,2097152,2097152,2097152*74



2025-07-31 23:28:41:729 ==>>                                          

2025-07-31 23:28:42:030 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:42:043 ==>> 定位已等待【21】秒.
2025-07-31 23:28:42:692 ==>> $GBGGA,152842.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,25,,,41,1*71

$GBGSV,7,2,25,34,,,41,43,,,41,7,,,40,3,,,40,1*70

$GBGSV,7,3,25,60,,,40,11,,,40,16,,,39,23,,,39,1*75

$GBGSV,7,4,25,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,24,,,35,1*7F

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152842.518,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152842.518,2120156,778.802,778.802,712.233,2097152,2097152,2097152*7E



2025-07-31 23:28:43:041 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:43:052 ==>> 定位已等待【22】秒.
2025-07-31 23:28:43:699 ==>> $GBGGA,152843.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,25,,,41,1*71

$GBGSV,7,2,25,34,,,41,43,,,41,7,,,40,3,,,40,1*70

$GBGSV,7,3,25,60,,,40,11,,,40,16,,,39,23,,,39,1*75

$GBGSV,7,4,25,10,,,38,1,,,38,6,,,37,41,,,37,1*71

$GBGSV,7,5,25,2,,,35,9,,,35,24,,,35,32,,,35,1*7F

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,33,,,33,1*75

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152843.518,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152843.518,2120083,776.323,776.323,709.965,2097152,2097152,2097152*74



2025-07-31 23:28:43:729 ==>>                                          

2025-07-31 23:28:44:045 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:44:056 ==>> 定位已等待【23】秒.
2025-07-31 23:28:44:435 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:28:45:052 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:45:063 ==>> 定位已等待【24】秒.
2025-07-31 23:28:45:469 ==>> [D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 10, ret: 6
[D][05:19:17][CAT1]sub id: 10, ret: 6

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:17][M2M ]m2m gsm shut done, ret[0]
$GBGGA,152844.525,2301.2570460,N,11421.9425508,E,1,13,1.06,78.711,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.06,1.91,4*06

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.06,1.91,4*0D

$GBGSV,7,1,25,40,77,194,42,7,70,221,40,39,67,53,41,6,65,15,37,1*71

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,35,59,52,129,41,25,51,26,41,43,50,159,41,1*73

$GBGSV,7,4,25,1,48,125,38,34,46,72,40,2,45,236,35,60,41,239,40,1*4B

$GBGSV,7,5,25,23,33,317,39,4,32,111,33,41,31,239,37,24,22,280,35,1*48

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,30,33,6,322,33,1*7F

$GBGSV,7,7,25,12,,,33,1*72

$GBRMC,152844.525,A,2301.2570460,N,11421.9425508,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

[D][05:19:17][GNSS]HD8040 GPS
[D][05:19:17][GNSS]GPS diff_sec 124020567, report 0x42 frame
$GBGST,152844.525,0.688,0.348,0.339,0.498,1.707,1.769,6.625*

2025-07-31 23:28:45:574 ==>> 73

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][COMM]Main Task receive event:131
[D][05:19:17][COMM]index:0,power_mode:0xFF
[D][05:19:17][COMM]index:1,sound_mode:0xFF
[D][05:19:17][COMM]index:2,gsensor_mode:0xFF
[D][05:19:17][COMM]index:3,report_freq_mode:0xFF
[D][05:19:17][COMM]index:4,report_period:0xFF
[D][05:19:17][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:17][COMM]index:6,normal_reset_period:0xFF
[D][05:19:17][COMM]index:7,spock_over_speed:0xFF
[D][05:19:17][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:17][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:17][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:1

2025-07-31 23:28:45:679 ==>> 9:17][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:17][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:17][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:17][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:17][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:17][COMM]index:16,imu_config_params:0xFF
[D][05:19:17][COMM]index:17,long_connect_params:0xFF
[D][05:19:17][COMM]index:18,detain_mark:0xFF
[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:17][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:17][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:17][COMM]index:21,mc_mode:0xFF
[D][05:19:17][COMM]index:22,S_mode:0xFF
[D][05:19:17][COMM]index:23,overweight:0xFF
[D][05:19:17][COMM]index:24,standstill_mode:0xFF
[D][05:19:17][COMM]index:25,night_mode:0xFF
[D][05:19:17][COMM]index:26,experiment1:0xFF
[D][05:19:17][COMM]index:27,experiment2:0xFF
[D][05:19:17][COMM]index:28,experiment3:0xFF
[D][05:19:17][COMM]index:29,experiment4:0xFF
[D][05:19:17][COMM]index:30,night_mode_start:0xFF
[D][05:19:17][COMM]index:31,night_mode_end:0xFF
[D][05:19:17][COMM]index:33,park_report_minutes:0xFF
[D][05:19:17][COMM]index:34,park_report_mode:0xFF
[D][05:19:17][COMM]index:35,mc_undervo

2025-07-31 23:28:45:784 ==>> ltage_protection:0xFF
[D][05:19:17][COMM]index:38,charge_battery_para: FF
[D][05:19:17][COMM]index:39,multirider_mode:0xFF
[D][05:19:17][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:17][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:17][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:17][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:17][COMM]index:44,riding_duration_config:0xFF
[D][05:19:17][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:17][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:17][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:17][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:17][COMM]index:49,mc_load_startup:0xFF
[D][05:19:17][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:17][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:17][COMM]index:52,traffic_mode:0xFF
[D][05:19:17][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:17][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:17][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:17][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:17][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:17][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:17][COMM]index:60,traff

2025-07-31 23:28:45:889 ==>> ic_road_threshold:0xFF
[D][05:19:17][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:17][COMM]index:63,experiment5:0xFF
[D][05:19:17][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:17][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:17][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:17][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:17][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:17][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:17][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:17][COMM]index:72,experiment6:0xFF
[D][05:19:17][COMM]index:73,experiment7:0xFF
[D][05:19:17][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:17][COMM]index:75,zero_value_from_server:-1
[D][05:19:17][COMM]index:76,multirider_threshold:255
[D][05:19:17][COMM]index:77,experiment8:255
[D][05:19:17][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:17][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:17][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:17][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:17][COMM]index:83,loc_report_interval:255
[D][05:19:17][COMM]index:84,multirider_threshold_p2:255
[D

2025-07-31 23:28:45:994 ==>> ][05:19:17][COMM]index:85,multirider_strategy:255
[D][05:19:17][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:17][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:17][COMM]index:90,weight_param:0xFF
[D][05:19:17][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:17][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:17][COMM]index:95,current_limit:0xFF
[D][05:19:17][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:17][COMM]index:100,location_mode:0xFF

[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:17][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][COMM]Main Task receive event:131 finished processing
[D][05:19:18][CAT1]pdpdeact urc len[22]
$GBGGA,152845.025,2301.2571148,N,11421.9426536,E,1,13,1.06,79.761,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.06,1.91,4*06

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.0

2025-07-31 23:28:46:054 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:46:067 ==>> 定位已等待【25】秒.
2025-07-31 23:28:46:089 ==>> 6,1.91,4*0D

$GBGSV,7,1,25,40,77,194,42,7,70,221,40,39,67,53,41,6,65,15,37,1*71

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,35,59,52,129,41,25,51,26,41,43,50,159,41,1*73

$GBGSV,7,4,25,1,48,125,37,34,46,72,40,2,45,236,35,60,41,239,39,1*4A

$GBGSV,7,5,25,23,33,317,39,4,32,111,33,41,31,239,37,24,22,280,35,1*48

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,30,33,6,322,33,1*7F

$GBGSV,7,7,25,12,,,33,1*72

$GBGSV,2,1,08,40,77,194,39,39,67,53,36,25,51,26,39,43,50,159,39,5*7B

$GBGSV,2,2,08,34,46,72,40,23,33,317,38,41,31,239,38,32,17,296,37,5*4B

$GBRMC,152845.025,A,2301.2571148,N,11421.9426536,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152845.025,2.401,0.735,0.711,1.060,2.266,2.283,5.472*74



2025-07-31 23:28:46:495 ==>>                                          $GBGGA,152846.005,2301.2573579,N,11421.9426433,E,1,13,1.06,81.337,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.06,1.91,4*06

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.06,1.91,4*0D

$GBGSV,7,1,25,40,77,194,42,7,70,221,40,39,67,53,41,6,65,15,37,1*71

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,36,59,52,129,40,25,51,26,41,43,50,159,41,1*71

$GBGSV,7,4,25,1,48,125,37,34,46,72,41,2,45,236,35,60,41,239,40,1*45

$GBGSV,7,5,25,23,33,317,40,4,32,111,33,41,31,239,37,24,22,280,35,1*46

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,29,33,6,322,33,1*77

$GBGSV,7,7,25,12,,,34,1*75

$GBGSV,2,1,08,40,77,194,40,39,67,53,39,25,51,26,40,43,50,159,40,5*7A

$GBGSV,2,2,08,34,46,72,40,23,33,317,39,41,31,239,38,32,17,296,36,5*4B

$GBRMC,152846.005,A,2301.2573579,N,11421.9426433,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152846.005,2.209,0.542,0.525,0.781,1.966,1.991,4.561*74

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:1

2025-07-31 23:28:46:540 ==>> 9][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 8, ret: 6


2025-07-31 23:28:46:876 ==>> [D][05:19:19][CAT1]opened : 0, 0
[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:19][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]index:2 1629955159
[D][05:19:19][PROT]is_send:0
[D][05:19:19][PROT]sequence_num:6
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:1
[D][05:19:19][PROT]send_path:0x2
[D][05:19:19][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980221
[D][05:19:19][PROT]sending traceid [9999999999900007]
[D][05:19:19][PROT]Send_TO_M2M [1629955159]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:19][SAL ]sock send credit cnt[6]
[D][05:19:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[134]
[D][05:19:19][SAL ]Cellular task submsg id[10]
[D][05:19:19][

2025-07-31 23:28:46:981 ==>> SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BEAD0A6699AACE49A6897891D623580DC0FC23FD0AC90DC2A1E9C3B594AA8EF8EDB3A37CD3C3952B325D3B605554CACC5C57BF
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]


2025-07-31 23:28:47:055 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:28:47:067 ==>> 定位已等待【26】秒.
2025-07-31 23:28:47:312 ==>> $GBGGA,152847.000,2301.2574885,N,11421.9426427,E,1,13,1.07,81.847,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.07,1.91,4*07

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.07,1.91,4*0C

$GBGSV,7,1,25,40,77,194,43,7,70,221,40,39,67,53,41,6,65,15,37,1*70

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,36,59,52,129,41,25,51,26,41,43,50,159,41,1*70

$GBGSV,7,4,25,1,48,125,38,34,46,72,41,2,45,236,35,60,41,239,40,1*4A

$GBGSV,7,5,25,23,33,317,39,4,32,111,33,41,31,239,38,24,22,280,35,1*47

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,29,33,6,322,33,1*77

$GBGSV,7,7,25,12,,,34,1*75

$GBGSV,2,1,08,40,77,194,41,39,67,53,41,25,51,26,41,43,50,159,40,5*75

$GBGSV,2,2,08,34,46,72,40,23,33,317,39,41,31,239,38,32,17,296,36,5*4B

$GBRMC,152847.000,A,2301.2574885,N,11421.9426427,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152847.000,2.163,0.318,0.310,0.457,1.843,1.870,4.049*79



2025-07-31 23:28:47:770 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 23:28:48:062 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 23:28:48:068 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【37】
北斗星号:【16】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【10】,信号值:【38】
北斗星号:【11】,信号值:【40】
北斗星号:【9】,信号值:【36】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【43】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【34】,信号值:【40】
北斗星号:【2】,信号值:【35】
北斗星号:【60】,信号值:【40】
北斗星号:【23】,信号值:【39】
北斗星号:【41】,信号值:【38】
北斗星号:【24】,信号值:【35】
北斗星号:【32】,信号值:【36】

2025-07-31 23:28:48:094 ==>> 检测【CSQ强度】
2025-07-31 23:28:48:101 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:28:48:323 ==>> $GBGGA,152848.000,2301.2575268,N,11421.9426209,E,1,13,1.07,82.311,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.07,1.91,4*07

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.07,1.91,4*0C

$GBGSV,7,1,25,40,77,194,43,7,70,221,40,39,67,53,41,6,65,15,37,1*70

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,36,59,52,129,41,25,51,26,41,43,50,159,41,1*70

$GBGSV,7,4,25,1,48,125,38,34,46,72,41,2,45,236,35,60,41,239,40,1*4A

$GBGSV,7,5,25,23,33,317,40,4,32,111,33,41,31,239,38,24,22,280,35,1*49

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,29,33,6,322,33,1*77

$GBGSV,7,7,25,12,,,34,1*75

$GBGSV,2,1,08,40,77,194,41,39,67,53,41,25,51,26,41,43,50,159,40,5*75

$GBGSV,2,2,08,34,46,72,40,23,33,317,39,41,31,239,38,32,17,296,36,5*4B

$GBRMC,152848.000,A,2301.2575268,N,11421.9426209,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,152848.000,1.952,0.229,0.225,0.330,1.650,1.675,3.624*70

[W][05:19:21][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:21][CAT1]<<< 
+CSQ: 27,9

2025-07-31 23:28:48:353 ==>> 9

OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 21


2025-07-31 23:28:48:673 ==>> 【CSQ强度】通过,【27】符合目标值【18】至【31】要求!
2025-07-31 23:28:48:680 ==>> 检测【关闭GSM联网】
2025-07-31 23:28:48:706 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:28:48:799 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:21][COMM]GSM test
[D][05:19:21][COMM]GSM test disable


2025-07-31 23:28:48:954 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:28:48:963 ==>> 检测【4G联网测试】
2025-07-31 23:28:48:976 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:28:50:003 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
$GBGGA,152849.000,2301.2576112,N,11421.9425855,E,1,13,1.07,82.795,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.07,1.91,4*07

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.07,1.91,4*0C

$GBGSV,7,1,25,40,77,194,42,7,70,221,40,39,67,53,41,6,65,15,37,1*71

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,40,1*7A

$GBGSV,7,3,25,9,53,333,36,59,52,129,41,25,51,26,41,43,50,159,41,1*70

$GBGSV,7,4,25,1,48,125,38,34,46,72,41,2,45,236,35,60,41,239,40,1*4A

$GBGSV,7,5,25,23,33,317,40,4,32,111,33,41,31,239,38,24,22,280,35,1*49

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,29,33,6,322,32,1*76

$GBGSV,7,7,25,12,,,34,1*75

$GBGSV,2,1,08,40,77,194,41,39,67,53,42,25,51,26,41,43,50,159,40,5*76

$GBGSV,2,2,08,34,46,72,40,23,33,317,39,41,31,239,38,32,17,296,36,5*4B

$GBRMC,152849.000,A,2301.2576112,N,11421.9425855,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152849.000,1.799,0.232,0.228,0.334,1.505,1.529,3.309*78

[D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handlerPeriodRep, g_elecBatMisse

2025-07-31 23:28:50:108 ==>> dCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:22][COMM]index:21,mc_mode:0xFF
[D][05:19:22][COMM]index:22,S_mod

2025-07-31 23:28:50:213 ==>> e:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:22][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:22]

2025-07-31 23:28:50:318 ==>> [COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:72,experiment6:0xFF
[D][05:19:22][COMM]index:73,experiment7:0xFF
[D][05:1

2025-07-31 23:28:50:423 ==>> 9:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:22][PROT

2025-07-31 23:28:50:528 ==>> ]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:22][PROT]index:0 1629955162
[D][05:19:22][PROT]is_send:0
[D][05:19:22][PROT]sequence_num:8
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:1
[D][05:19:22][PROT]send_path:0x2
[D][05:19:22][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]sending traceid [9999999999900009]
[D][05:19:22][PROT]Send_TO_M2M [1629955162]
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:22][SAL ]sock send credit cnt[6]
[D][05:19:22][SAL ]sock send ind credit cnt[6]
[D][05:19:22][M2M ]m2m send data len[294]
[D][05:19:22][CAT1]gsm read msg sub id: 13
[D][05:19:22][SAL ]Cellular task submsg id[10]
[D][05:19:22][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:22][CAT1]tx ret[8] >>> AT+CSQ

[D][05:

2025-07-31 23:28:50:633 ==>> 19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:22][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:19:22][CAT1]exec over: func id: 13, ret: 21
[D][05:19:22][M2M ]get csq[27]
[D][05:19:22][CAT1]gsm read msg sub id: 15
[D][05:19:22][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:22][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88B1EABB6096761A3021CF134A5E5308DAF0FF72F4EB468B4FBB2BF22FF53194B4E1B0889E83DC83E8A5E08C0A1D02ABC125A71B71D9BF8D3252F4B8104D87A9CB8D22E4394B4FA64478B3A8151A6EE335F77A45E084A09E3B3E0B4AA4B24C7E07B8F1E57764E845037F16B5948CF10271116DC263FE50988663526287FD79F28BD01D18
[D][05:19:22][CAT1]<<< 
SEND OK

[D][05:19:22][CAT1]exec over: func id: 15, ret: 11
[D][05:19:22][CAT1]sub id: 15, ret: 11

[D][05:19:22][SAL ]Cellular task submsg id[68]
[D][05:19:22][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:22][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:22][M2M ]g_m2m_is_idle become true
[D][05:19:22][M

2025-07-31 23:28:50:738 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:28:51:011 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:28:51:024 ==>> 检测【关闭GPS】
2025-07-31 23:28:51:050 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:28:51:417 ==>> $GBGGA,152851.000,2301.2576629,N,11421.9426093,E,1,13,1.07,82.974,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,2.19,1.07,1.91,4*07

$GBGSA,A,3,32,,,,,,,,,,,,2.19,1.07,1.91,4*0C

$GBGSV,7,1,25,40,77,194,42,7,70,221,40,39,67,53,41,6,65,15,37,1*71

$GBGSV,7,2,25,16,65,19,39,3,60,190,40,10,58,224,38,11,53,110,41,1*7B

$GBGSV,7,3,25,9,53,333,36,59,52,129,41,25,51,26,41,43,50,159,41,1*70

$GBGSV,7,4,25,1,48,125,38,34,46,72,41,2,45,236,35,60,41,239,40,1*4A

$GBGSV,7,5,25,23,33,317,40,4,32,111,33,41,31,239,38,24,22,280,35,1*49

$GBGSV,7,6,25,5,21,255,33,32,17,296,35,44,14,172,29,33,6,322,32,1*76

$GBGSV,7,7,25,12,,,34,1*75

$GBGSV,2,1,08,40,77,194,41,39,67,53,42,25,51,26,41,43,50,159,40,5*76

$GBGSV,2,2,08,34,46,72,40,23,33,317,39,41,31,239,38,32,17,296,36,5*4B

$GBRMC,152851.000,A,2301.2576629,N,11421.9426093,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152851.000,1.741,0.230,0.225,0.330,1.411,1.431,2.950*74

[W][05:19:24][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:24][GNSS]stop locating
[D][05:19:24][GNSS]stop event:8
[D][05:19:24][GNSS]GPS stop. ret=0
[D][05:19:24][GNSS]all continue location stop
[W][05:19:24][GNSS]stop locating
[D][05:19:24][CAT1]gsm read msg sub id: 24
[D][05:19:24][GNSS]all sing location stop
[D][0

2025-07-31 23:28:51:462 ==>> 5:19:24][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]exec over: func id: 24, ret: 6
[D][05:19:24][CAT1]sub id: 24, ret: 6



2025-07-31 23:28:51:541 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:28:51:549 ==>> 检测【清空消息队列2】
2025-07-31 23:28:51:572 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:28:51:719 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:24][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:28:51:794 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 23:28:51:871 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:28:51:884 ==>> 检测【轮动检测】
2025-07-31 23:28:51:906 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:28:51:930 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 23:28:52:020 ==>> [D][05:19:25][GNSS]recv submsg id[1]
[D][05:19:25][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:25][GNSS]location stop evt done evt
[D][05:19:25][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:28:52:373 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:28:52:418 ==>> 3A A3 01 01 A3 


2025-07-31 23:28:52:523 ==>> ON_OUT1
OVER 150


2025-07-31 23:28:52:647 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:28:52:655 ==>> 检测【关闭小电池】
2025-07-31 23:28:52:669 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:28:52:734 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:28:52:922 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:28:52:936 ==>> 检测【进入休眠模式】
2025-07-31 23:28:52:953 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:28:53:166 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:26][COMM]Main Task receive event:28
[D][05:19:26][COMM]main task tmp_sleep_event = 8
[D][05:19:26][COMM]prepare to sleep
[D][05:19:26][CAT1]gsm read msg sub id: 12
[D][05:19:26][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:28:53:992 ==>> [D][05:19:26][COMM]read battery soc:255
[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 12, ret: 6
[D][05:19:26][M2M ]tcpclient close[4]
[D][05:19:26][SAL ]Cellular task submsg id[12]
[D][05:19:26][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:26][CAT1]gsm read msg sub id: 9
[D][05:19:26][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]exec over: func id: 9, ret: 6
[D][05:19:27][CAT1]sub id: 9, ret: 6

[D][05:19:27][SAL ]Cellular task submsg id[68]
[D][05:19:27][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:27][SAL ]socket close ind. id[4]
[D][05:19:27][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:27][COMM]1x1 frm_can_tp_send ok
[D][05:19:27][CAT1]pdpdeact urc len[22]


2025-07-31 23:28:54:270 ==>> [E][05:19:27][COMM]1x1 rx timeout
[D][05:19:27][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:28:54:792 ==>> [E][05:19:27][COMM]1x1 rx timeout
[E][05:19:27][COMM]1x1 tp timeout
[E][05:19:27][COMM]1x1 error -3.
[W][05:19:27][COMM]CAN STOP!
[D][05:19:27][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:27][COMM]------------ready to Power off Acckey 1------------
[D][05:19:27][COMM]------------ready to Power off Acckey 2------------
[D][05:19:27][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:27][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1311
[D][05:19:27][COMM]bat sleep fail, reason:-1
[D][05:19:27][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:27][COMM]accel parse set 0
[D][05:19:27][COMM]imu rest ok. 98838
[D][05:19:27][COMM]imu sleep 0
[W][05:19:27][COMM]now sleep


2025-07-31 23:28:55:011 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:28:55:025 ==>> 检测【检测33V休眠电流】
2025-07-31 23:28:55:044 ==>> 开始33V电流采样
2025-07-31 23:28:55:051 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:28:55:113 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:28:56:127 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:28:56:189 ==>> Current33V:????:15.47

2025-07-31 23:28:56:639 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:28:56:647 ==>> 【检测33V休眠电流】通过,【15.47uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:28:56:672 ==>> 该项需要延时执行
2025-07-31 23:28:58:655 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:28:58:680 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:28:58:707 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:28:58:733 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1675mV
Get AD_V4 0mV
Get AD_V5 2752mV
Get AD_V6 1934mV
Get AD_V7 1088mV
OVER 150


2025-07-31 23:28:59:682 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:28:59:694 ==>> 检测【打开小电池2】
2025-07-31 23:28:59:721 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:28:59:822 ==>> 6A A6 01 A6 6A 


2025-07-31 23:28:59:927 ==>> Battery ON
OVER 150


2025-07-31 23:28:59:965 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:28:59:974 ==>> 该项需要延时执行
2025-07-31 23:29:00:474 ==>> 此处延时了:【500】毫秒
2025-07-31 23:29:00:505 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:29:00:534 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:29:00:547 ==>> 5A A5 02 5A A5 


2025-07-31 23:29:00:624 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:29:00:748 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:29:00:761 ==>> 该项需要延时执行
2025-07-31 23:29:00:970 ==>> [D][05:19:33][COMM]------------ready to Power on Acckey 1------------
[D][05:19:33][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:33][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 11
[D][05:19:33][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:33][COMM]----- get Acckey 1 and value:1------------
[W][05:19:33][COMM]CAN START!
[D][05:19:33][CAT1]gsm read msg sub id: 12
[D][05:19:33][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:33][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 104917
[D][05:19:33][COMM][Audio]exec status ready.
[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over:

2025-07-31 23:29:01:030 ==>>  func id: 12, ret: 6
[D][05:19:33][COMM]imu wakeup ok. 104931
[D][05:19:33][COMM]imu wakeup 1
[W][05:19:33][COMM]wake up system, wakeupEvt=0x80
[D][05:19:33][COMM]frm_can_weigth_power_set 1
[D][05:19:33][COMM]Clear Sleep Block Evt
[D][05:19:33][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:33][COMM]1x1 frm_can_tp_send ok
[D][05:19:33][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 23:29:01:195 ==>> [E][05:19:34][COMM]1x1 rx timeout
[D][05:19:34][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:29:01:255 ==>> 此处延时了:【500】毫秒
2025-07-31 23:29:01:264 ==>> 检测【进入休眠模式2】
2025-07-31 23:29:01:277 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:29:01:307 ==>> [D][05:19:34][COMM]msg 02A0 loss. last_tick:104902. cur_tick:105410. period:50
[D][05:19:34][COMM]msg 02A4 loss. last_tick:104902. cur_tick:105411. period:50
[D][05:19:34][COMM]msg 02A5 loss. last_tick:104902. 

2025-07-31 23:29:01:360 ==>> cur_tick:105411. period:50
[D][05:19:34][COMM]msg 02A6 loss. last_tick:104902. cur_tick:105412. period:50
[D][05:19:34][COMM]msg 02A7 loss. last_tick:104902. cur_tick:105412. period:50
[D][05:19:34][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 105413
[D][05:19:34][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 105413


2025-07-31 23:29:01:435 ==>> [W][05:19:34][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:29:01:691 ==>> [E][05:19:34][COMM]1x1 rx timeout
[E][05:19:34][COMM]1x1 tp timeout
[D][05:19:34][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[E][05:19:34][COMM]1x1 error -3.
[D][05:19:34][COMM]Main Task receive event:28 finished processing
[D][05:19:34][COMM]Main Task receive event:28
[D][05:19:34][COMM]prepare to sleep
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:34][CAT1]<<< 
OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 6
[D][05:19:34][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:34][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:29:01:991 ==>> [D][05:19:34][COMM]msg 0220 loss. last_tick:104902. cur_tick:105907. period:100
[D][05:19:34][COMM]msg 0221 loss. last_tick:104902. cur_tick:105908. period:100
[D][05:19:34][COMM]msg 0224 loss. last_tick:104902. cur_tick:105908. period:100
[D][05:19:34][COMM]msg 0260 loss. last_tick:104902. cur_tick:105908. period:100
[D][05:19:34][COMM]msg 0280 loss. last_tick:104902. cur_tick:105909. period:100
[D][05:19:34][COMM]msg 02C0 loss. last_tick:104902. cur_tick:105909. period:100
[D][05:19:34][COMM]msg 02C1 loss. last_tick:104902. cur_tick:105909. period:100
[D][05:19:34][COMM]msg 02C2 loss. last_tick:104902. cur_tick:105910. period:100
[D][05:19:34][COMM]msg 02E0 loss. last_tick:104902. cur_tick:105910. period:100
[D][05:19:34][COMM]msg 02E1 loss. last_tick:104902. cur_tick:105910. period:100
[D][05:19:34][COMM]msg 02E2 loss. last_tick:104902. cur_tick:105911. period:100
[D][05:19:34][COMM]msg 0300 loss. last_tick:104902. cur_tick:105911. period:100
[D][05:19:34][COMM]msg 0301 loss. last_tick:104902. cur_tick:105912. period:100
[D][05:19:34][COMM]bat msg 0240 loss. last_tick:104902. cur_tick:105912. period:100. j,i:1 54
[D][05:19:34][COMM]bat msg 0241 loss. last_tick:104902. cur_tick:105912. period:100. j,i:2 55
[D][05:19:34][CO

2025-07-31 23:29:02:096 ==>> MM]bat msg 0242 loss. last_tick:104902. cur_tick:105913. period:100. j,i:3 56
[D][05:19:34][COMM]bat msg 0244 loss. last_tick:104902. cur_tick:105913. period:100. j,i:5 58
[D][05:19:34][COMM]bat msg 024E loss. last_tick:104902. cur_tick:105913. period:100. j,i:15 68
[D][05:19:34][COMM]bat msg 024F loss. last_tick:104902. cur_tick:105914. period:100. j,i:16 69
[D][05:19:34][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 105914
[D][05:19:34][COMM]CAN message bat fault change: 0x00000000->0x0001802E 105915
[D][05:19:34][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 105915
                                                                              

2025-07-31 23:29:02:353 ==>> [D][05:19:35][COMM]msg 0222 loss. last_tick:104902. cur_tick:106409. period:150
[D][05:19:35][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 106410
[D][05:19:35][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:35][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:35][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:29:02:563 ==>> [E][05:19:35][COMM]1x1 rx timeout
[E][05:19:35][COMM]1x1 tp timeout
[E][05:19:35][COMM]1x1 error -3.
[W][05:19:35][COMM]CAN STOP!
[D][05:19:35][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:35][COMM]------------ready to Power off Acckey 1------------
[D][05:19:35][COMM]------------ready to Power off Acckey 2------------
[D][05:19:35][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:35][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 103
[D][05:19:35][COMM]bat sleep fail, reason:-1
[D][05:19:35][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:35][COMM]accel parse set 0
[D][05:19:35][COMM]imu rest ok. 106597
[D][05:19:35][COMM]imu sleep 0
[W][05:19:35][COMM]now sleep


2025-07-31 23:29:02:813 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:29:02:821 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:29:02:841 ==>> 开始小电池电流采样
2025-07-31 23:29:02:856 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:29:02:915 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:29:03:925 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:29:03:986 ==>> CurrentBattery:ƽ��:69.54

2025-07-31 23:29:04:432 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:29:04:452 ==>> 【检测小电池休眠电流】通过,【69.54uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:29:04:460 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:29:04:482 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:29:04:522 ==>> 5A A5 01 5A A5 


2025-07-31 23:29:04:627 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:29:04:736 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:29:04:750 ==>> 该项需要延时执行
2025-07-31 23:29:04:868 ==>> [D][05:19:37][COMM]------------ready to Power on Acckey 1------------
[D][05:19:37][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:37][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:37][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:37][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:37][COMM]----- get Acckey 1 and value:1------------
[W][05:19:37][COMM]CAN START!
[D][05:19:37][CAT1]gsm read msg sub id: 12
[D][05:19:37][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:37][COMM]CAN message bat fault change: 0x0001802E->0x00000000 108857
[D][05:19:37][COMM][Audio]exec status ready.
[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]exec over: func id: 12, ret: 6
[D][05:19:37][COMM]imu wakeup ok. 108871
[D][05:19:37][COMM]imu wakeup 1
[W][05:19:37][COMM]wake up system, wakeupEvt=0x80
[D][05:19:37][COMM]frm_can_weigth_power_set 1
[D][05:19:37][COMM]Clear Sleep Block Evt
[D][05:19:37][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:37][COMM]1x1 frm_can_tp_send ok
[D][05:19:37][COMM]read battery soc:0


2025-07-31 23:29:05:128 ==>> [E][05:19:38][COMM]1x1 rx timeout
[D][05:19:38][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:29:05:233 ==>> [D][05:19:38][COMM]msg 02A0 loss. last_tick:108839. cur_tick:109351. period:50
[D][05:19:38][COMM]msg 02A4 loss. last_tick:108839. 

2025-07-31 23:29:05:248 ==>> 此处延时了:【500】毫秒
2025-07-31 23:29:05:264 ==>> 检测【检测唤醒】
2025-07-31 23:29:05:288 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:29:05:313 ==>> cur_tick:109351. period:50
[D][05:19:38][COMM]msg 02A5 loss. last_tick:108839. cur_tick:109352. period:50
[D][05:19:38][COMM]msg 02A6 loss. last_tick:108839. cur_tick:109352. period:50
[D][05:19:38][COMM]msg 02A7 loss. last_tick:108839. cur_tick:109352. period:50
[D][05:19:38][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 109353
[D][05:19:38][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 109353


2025-07-31 23:29:06:009 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:38][FCTY]==========Modules-nRF5340 ==========
[D][05:19:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:38][FCTY]DeviceID    = 460130071539517
[D][05:19:38][FCTY]HardwareID  = 867222087631489
[D][05:19:38][FCTY]MoBikeID    = 9999999999
[D][05:19:38][FCTY]LockID      = FFFFFFFFFF
[D][05:19:38][FCTY]BLEFWVersion= 105
[D][05:19:38][FCTY]BLEMacAddr   = D5BD9D17E3CF
[D][05:19:38][FCTY]Bat         = 3844 mv
[D][05:19:38][FCTY]Current     = 0 ma
[D][05:19:38][FCTY]VBUS        = 2600 mv
[D][05:19:38][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:38][FCTY]Ext battery vol = 33, adc = 1311
[D][05:19:38][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 177 mv
[D][05:19:38][FCTY]Bike Type flag is invalied
[D][05:19:38][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:38][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:38][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:38][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:38][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:38][FCTY]CAT1_GNSS_VERS

2025-07-31 23:29:06:044 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:29:06:054 ==>> 检测【关机】
2025-07-31 23:29:06:069 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:29:06:114 ==>> ION = V3465b5b1
[D][05:19:38][FCTY]Bat1         = 3775 mv
[D][05:19:38][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:38][FCTY]==========Modules-nRF5340 ==========
[E][05:19:38][COMM]1x1 rx timeout
[E][05:19:38][COMM]1x1 tp timeout
[E][05:19:38][COMM]1x1 error -3.
[D][05:19:38][COMM]Main Task receive event:28 finished processing
[D][05:19:38][COMM]Main Task receive event:65
[D][05:19:38][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:38][COMM]Main Task receive event:65 finished processing
[D][05:19:38][COMM]Main Task receive event:60
[D][05:19:38][COMM]smart_helmet_vol=255,255
[D][05:19:38][COMM]report elecbike
[W][05:19:38][PROT]remove success[1629955178],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:38][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:38][PROT]index:0
[D][05:19:38][PROT]is_send:1
[D][05:19:38][PROT]sequence_num:10
[D][05:19:38][PROT]retry_timeout:0
[D][05:19:38][PROT]retry_times:3
[D][05:19:38][PROT]send_path:0x3
[D][05:19:38][PROT]msg_type:0x5d03
[D][05:19:38][PROT]=====

2025-07-31 23:29:06:220 ==>> ======================================================
[W][05:19:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955178]
[D][05:19:38][PROT]===========================================================
[D][05:19:38][PROT]Sending traceid[999999999990000B]
[D][05:19:38][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:38][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:38][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:38][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:19:38][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:38][SAL ]open socket ind id[4], rst[0]
[D][05:19:38][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:38][SAL ]Cellular task submsg id[8]
[D][05:19:38][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:38][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:38][CAT1]gsm read msg sub id: 8
[D][05:19:38][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:38][HSDK][0] flush to flash addr:[

2025-07-31 23:29:06:324 ==>> 0xE41E00] --- write len --- [256]
[W][05:19:38][PROT]add success [1629955178],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:38][COMM]Main Task receive event:60 finished processing
[D][05:19:38][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:38][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:38][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:38][CAT1]<<< 
+CME ERROR: 100

[D][05:19:38][COMM]msg 0220 loss. last_tick:108839. cur_tick:109847. period:100
[D][05:19:38][COMM]msg 0221 loss. last_tick:108839. cur_tick:109848. period:100
[D][05:19:38][COMM]msg 0224 loss. last_tick:108839. cur_tick:109848. period:100
[D][05:19:38][COMM]msg 0260 loss. last_tick:108839. cur_tick:109848. period:100
[D][05:19:38][COMM]msg 0280 loss. last_tick:108839. cur_tick:109849. period:100
[D][05:19:38][COMM]msg 02C0 loss. last_tick:108839. cur_tick:109849. period:100
[D][05:19:38][COMM]msg 02C1 loss. last_tick:108839. cur_tick:109849. period:100
[D][05:19:38][COMM]msg 02C2 loss. last_tick:108839. cur_tick:109850. period:100
[D][05:19:38][COMM]msg 02E0 loss. last_tick:108839. cur_tick:109850. period:100
[D][05:19:38][COMM]msg 02E1 loss. last_tick:108839. cur_tick:109850. period:1

2025-07-31 23:29:06:429 ==>> 00
[D][05:19:38][COMM]msg 02E2 loss. last_tick:108839. cur_tick:109851. period:100
[D][05:19:38][COMM]msg 0300 loss. last_tick:108839. cur_tick:109851. period:100
[D][05:19:38][COMM]msg 0301 loss. last_tick:108839. cur_tick:109851. period:100
[D][05:19:38][COMM]bat msg 0240 loss. last_tick:108839. cur_tick:109852. period:100. j,i:1 54
[D][05:19:38][COMM]bat msg 0241 loss. last_tick:108839. cur_tick:109852. period:100. j,i:2 55
[D][05:19:38][COMM]bat msg 0242 loss. last_tick:108839. cur_tick:109853. period:100. j,i:3 56
[D][05:19:38][COMM]bat msg 0244 loss. last_tick:108839. cur_tick:109853. period:100. j,i:5 58
[D][05:19:38][COMM]bat msg 024E loss. last_tick:108840. cur_tick:109854. period:100. j,i:15 68
[D][05:19:38][COMM]bat msg 024F loss. last_tick:108840. cur_tick:109854. period:100. j,i:16 69
[D][05:19:38][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 109854
[D][05:19:38][COMM]CAN message bat fault change: 0x00000000->0x0001802E 109855
[D][05:19:38][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 109855


2025-07-31 23:29:07:093 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:29:07:123 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:29:07:229 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               55]. success, file_name:B50, size:10800
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:39][COMM]read file, len:10800, num:3
[D][05:19:39][COMM]Main Task receive event:60
[D][05:19:39][COMM]smart_helmet_vol=255,255
[D][05:19:39][COMM]BAT CAN get state1 Fail 204
[D][05:19:39]

2025-07-31 23:29:07:333 ==>> [COMM]BAT CAN get soc Fail, 204
[D][05:19:39][COMM]BAT CAN get state2 fail 204
[D][05:19:39][COMM]get soh error
[E][05:19:39][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:39][COMM]report elecbike
[W][05:19:39][PROT]remove success[1629955179],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:39][PROT]add success [1629955179],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:39][COMM]Main Task receive event:60 finished processing
[D][05:19:39][COMM]Main Task receive event:61
[D][05:19:39][COMM][D301]:type:3, trace id:280
[D][05:19:39][COMM]id[], hw[000
[D][05:19:39][COMM]get mcMaincircuitVolt error
[D][05:19:39][COMM]get mcSubcircuitVolt error
[D][05:19:39][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:39][COMM]BAT CAN get state1 Fail 204
[D][05:19:39][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:39][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:39][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:39][PROT]index:1
[D][05:19:39][PROT]is_send:1
[D][05:19:39][PROT]sequence_num:11
[D][05:19:39][PROT]retry_timeout:0
[D][05:19:39][PROT]retry_times:3
[D][05:19:39][PROT]send_path:0x3
[D][05:19:39][PROT]m

2025-07-31 23:29:07:438 ==>> sg_type:0x5d03
[D][05:19:39][PROT]===========================================================
[W][05:19:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955179]
[D][05:19:39][PROT]===========================================================
[D][05:19:39][PROT]Sending traceid[999999999990000C]
[D][05:19:39][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:39][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:39][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:39][COMM]BAT CAN get soc Fail, 204
[D][05:19:39][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:39][COMM]BAT CAN get state2 fail 204
[D][05:19:39][COMM]get bat work mode err
[W][05:19:39][PROT]remove success[1629955179],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:39][PROT]add success [1629955179],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:39][COMM]Main Task receive event:61 finished processing
[D][05:19:39][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19

2025-07-31 23:29:07:543 ==>> :39][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:39][COMM]--->crc16:0xb8a
[D][05:19:39][COMM]read file success
[W][05:19:39][COMM][Audio].l:[936].close hexlog save
[D][05:19:39][COMM]accel parse set 1
[D][05:19:39][COMM][Audio]mon:9,05:19:39
[D][05:19:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:39][COM

2025-07-31 23:29:07:648 ==>> M]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:19:39][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:39][COMM]arm_hub_enable: hub power: 0
[D][05:19:39][HSDK]hexlog index save 0 3840 209 @ 0 : 0
[D][05:19:39][HSDK]write save hexlog index [0]
[D][05:19:39][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:39][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048


2025-07-31 23:29:07:723 ==>> 
[D][05:19:40][COMM]read battery soc:255
[D][05:19:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:40][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:40][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 23:29:07:828 ==>> [W][05:19:40][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:40][COMM]arm_hub_enable: hub power: 0
[D][05:19:40][HSDK]hexlog index save 0 3840 209 @ 0 : 0
[D][05:19:40][HSDK]write save hexlog index [0]
[D][05:19:40][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:40][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:29:07:933 ==>>                                                                                                                                                                                                                                                               SS]all continue location stop
[D][05:19:41][COMM]Main Task receive event:68 finished processing


2025-07-31 23:29:08:115 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:29:08:355 ==>> [W][05:19:41][COMM]Power Off
[W][05:19:41][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:41][COMM]arm_hub_enable: hub power: 0
[D][05:19:41][HSDK]hexlog index save 0 3840 209 @ 0 : 0
[D][05:19:41][HSDK]write save hexlog index [0]
[D][05:19:41][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:41][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:29:08:401 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:29:08:410 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:29:08:439 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:29:08:522 ==>> 5A A5 02 5A A5 


2025-07-31 23:29:08:627 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:29:08:675 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:29:08:704 ==>> 检测【检测小电池关机电流】
2025-07-31 23:29:08:711 ==>> 开始小电池电流采样
2025-07-31 23:29:08:737 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:29:08:777 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:29:09:791 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:29:09:851 ==>> CurrentBattery:ƽ��:67.73

2025-07-31 23:29:10:298 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:29:10:309 ==>> 【检测小电池关机电流】通过,【67.73uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:29:10:691 ==>> MES过站成功
2025-07-31 23:29:10:705 ==>> #################### 【测试结束】 ####################
2025-07-31 23:29:10:736 ==>> 关闭5V供电
2025-07-31 23:29:10:753 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:29:10:825 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:29:11:737 ==>> 关闭5V供电成功
2025-07-31 23:29:11:751 ==>> 关闭33V供电
2025-07-31 23:29:11:774 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:29:11:830 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:29:12:739 ==>> 关闭33V供电成功
2025-07-31 23:29:12:751 ==>> 关闭3.7V供电
2025-07-31 23:29:12:769 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:29:12:831 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:29:13:752 ==>> 关闭3.7V供电成功
