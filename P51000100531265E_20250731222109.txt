2025-07-31 22:21:09:352 ==>> MES查站成功:
查站序号:P51000100531265E验证通过
2025-07-31 22:21:09:356 ==>> 扫码结果:P51000100531265E
2025-07-31 22:21:09:357 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:21:09:359 ==>> 测试参数版本:2024.10.11
2025-07-31 22:21:09:360 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:21:09:362 ==>> 检测【打开透传】
2025-07-31 22:21:09:363 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:21:09:498 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:21:09:709 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:21:09:720 ==>> 检测【检测接地电压】
2025-07-31 22:21:09:722 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:21:09:802 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:21:10:002 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:21:10:005 ==>> 检测【打开小电池】
2025-07-31 22:21:10:008 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:21:10:094 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:21:10:277 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:21:10:280 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:21:10:283 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:21:10:398 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:21:10:553 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:21:10:556 ==>> 检测【等待设备启动】
2025-07-31 22:21:10:559 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:11:596 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:12:649 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:13:697 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:14:747 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:15:788 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:16:836 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:17:874 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:18:904 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:19:935 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:20:968 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:21:20:970 ==>> #################### 【测试结束】 ####################
2025-07-31 22:21:20:991 ==>> 关闭5V供电
2025-07-31 22:21:20:993 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:21:21:107 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:21:22:002 ==>> 关闭5V供电成功
2025-07-31 22:21:22:005 ==>> 关闭33V供电
2025-07-31 22:21:22:008 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:21:22:095 ==>> 5A A5 02 5A A5 


2025-07-31 22:21:22:200 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:21:23:012 ==>> 关闭33V供电成功
2025-07-31 22:21:23:015 ==>> 关闭3.7V供电
2025-07-31 22:21:23:017 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:21:23:102 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:21:23:223 ==>>  

