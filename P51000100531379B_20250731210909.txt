2025-07-31 21:09:09:081 ==>> MES查站成功:
查站序号:P51000100531379B验证通过
2025-07-31 21:09:09:089 ==>> 扫码结果:P51000100531379B
2025-07-31 21:09:09:091 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:09:09:092 ==>> 测试参数版本:2024.10.11
2025-07-31 21:09:09:093 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:09:09:095 ==>> 检测【打开透传】
2025-07-31 21:09:09:097 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:09:09:175 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:09:09:493 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:09:09:503 ==>> 检测【检测接地电压】
2025-07-31 21:09:09:505 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:09:09:568 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:09:09:798 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:09:09:800 ==>> 检测【打开小电池】
2025-07-31 21:09:09:802 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:09:09:871 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:09:10:108 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:09:10:111 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:09:10:113 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:09:10:174 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:09:10:403 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:09:10:405 ==>> 检测【等待设备启动】
2025-07-31 21:09:10:407 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:10:644 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:10:809 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:09:11:324 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:11:429 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:11:490 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 21:09:12:006 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:12:186 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 21:09:12:456 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:12:686 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:12:866 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 21:09:13:357 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:13:492 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:13:554 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti 

2025-07-31 21:09:14:058 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:14:223 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:09:14:524 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:14:742 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:14:909 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:09:15:427 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:15:562 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:15:607 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 21:09:16:102 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:16:284 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:09:16:589 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:16:785 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:16:965 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:09:17:478 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:17:614 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:17:644 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 21:09:18:158 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:18:356 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 21:09:18:644 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:18:846 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:19:028 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 21:09:19:529 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:19:681 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:09:19:711 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:09:20:197 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:20:394 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 21:09:20:711 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:09:20:713 ==>> #################### 【测试结束】 ####################
2025-07-31 21:09:20:787 ==>> 关闭5V供电
2025-07-31 21:09:20:791 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:09:20:897 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
*** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:21:063 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:09:21:588 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:21:770 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:09:21:800 ==>> 关闭5V供电成功
2025-07-31 21:09:21:803 ==>> 关闭33V供电
2025-07-31 21:09:21:805 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:09:21:875 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:09:22:268 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:22:450 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 21:09:22:803 ==>> 关闭33V供电成功
2025-07-31 21:09:22:806 ==>> 关闭3.7V供电
2025-07-31 21:09:22:821 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:09:22:866 ==>> 6A A6 02 A6 6A 


2025-07-31 21:09:22:941 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:09:22:971 ==>> Battery OFF
OVER 150


2025-07-31 21:09:23:031 ==>>  

