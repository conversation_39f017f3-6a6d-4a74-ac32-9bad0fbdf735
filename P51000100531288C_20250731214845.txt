2025-07-31 21:48:45:230 ==>> MES查站成功:
查站序号:P51000100531288C验证通过
2025-07-31 21:48:45:234 ==>> 扫码结果:P51000100531288C
2025-07-31 21:48:45:236 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:48:45:237 ==>> 测试参数版本:2024.10.11
2025-07-31 21:48:45:254 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:48:45:256 ==>> 检测【打开透传】
2025-07-31 21:48:45:258 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:48:45:285 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:48:45:614 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:48:45:639 ==>> 检测【检测接地电压】
2025-07-31 21:48:45:640 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:48:45:789 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:48:45:916 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:48:45:918 ==>> 检测【打开小电池】
2025-07-31 21:48:45:921 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:48:45:986 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:48:46:202 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:48:46:205 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:48:46:207 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:48:46:288 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:48:46:479 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:48:46:481 ==>> 检测【等待设备启动】
2025-07-31 21:48:46:484 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:46:748 ==>>   

2025-07-31 21:48:47:500 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:47:639 ==>>  

2025-07-31 21:48:48:532 ==>>  

2025-07-31 21:48:48:547 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:49:585 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:50:627 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:51:663 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:52:693 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:53:729 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:54:765 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:55:789 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:48:56:830 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:48:56:834 ==>> #################### 【测试结束】 ####################
2025-07-31 21:48:56:894 ==>> 关闭5V供电
2025-07-31 21:48:56:896 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:48:56:982 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:48:57:905 ==>> 关闭5V供电成功
2025-07-31 21:48:57:907 ==>> 关闭33V供电
2025-07-31 21:48:57:910 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:48:57:980 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:48:58:906 ==>> 关闭33V供电成功
2025-07-31 21:48:58:909 ==>> 关闭3.7V供电
2025-07-31 21:48:58:911 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:48:58:983 ==>> 6A A6 02 A6 6A 


2025-07-31 21:48:59:088 ==>> Battery OFF
OVER 150


2025-07-31 21:48:59:424 ==>>  

