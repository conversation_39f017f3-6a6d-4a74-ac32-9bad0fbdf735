2025-07-31 21:22:53:729 ==>> MES查站成功:
查站序号:P510001005313266验证通过
2025-07-31 21:22:53:738 ==>> 扫码结果:P510001005313266
2025-07-31 21:22:53:739 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:22:53:740 ==>> 测试参数版本:2024.10.11
2025-07-31 21:22:53:742 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:22:53:743 ==>> 检测【打开透传】
2025-07-31 21:22:53:744 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:22:53:867 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:22:55:114 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:22:55:137 ==>> 检测【检测接地电压】
2025-07-31 21:22:55:139 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:22:55:278 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:22:55:438 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:22:55:442 ==>> 检测【打开小电池】
2025-07-31 21:22:55:445 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:22:55:566 ==>> 6A A6 01 A6 6A 


2025-07-31 21:22:55:671 ==>> Battery ON
OVER 150


2025-07-31 21:22:55:739 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:22:55:741 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:22:55:743 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:22:55:882 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:22:56:032 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:22:56:034 ==>> 检测【等待设备启动】
2025-07-31 21:22:56:036 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:22:56:432 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:22:56:626 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:22:57:071 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:22:57:304 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:22:57:718 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:22:58:115 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:22:58:176 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:22:58:429 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:22:58:431 ==>> 检测【产品通信】
2025-07-31 21:22:58:433 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:22:58:662 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:22:58:762 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:22:58:765 ==>> 检测【初始化完成检测】
2025-07-31 21:22:58:768 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:22:58:843 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:22:58:948 ==>> [

2025-07-31 21:22:59:008 ==>> D][05:17:51][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:22:59:093 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:22:59:095 ==>> 检测【关闭大灯控制1】
2025-07-31 21:22:59:098 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:22:59:250 ==>> [D][05:17:51][COMM]2627 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:22:59:355 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17

2025-07-31 21:22:59:385 ==>> :51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:22:59:491 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:22:59:496 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:22:59:498 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:22:59:670 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:22:59:814 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:22:59:817 ==>> 检测【关闭仪表供电】
2025-07-31 21:22:59:819 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:22:59:961 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:23:00:140 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:23:00:146 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:23:00:151 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:23:00:263 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:00:353 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:23:00:482 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:23:00:485 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:23:00:486 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:23:00:640 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:23:00:806 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:23:00:809 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:23:00:811 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:23:00:930 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:23:01:115 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:23:01:118 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:23:01:120 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:23:01:172 ==>> 5A A5 01 5A A5 


2025-07-31 21:23:01:277 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:23:01:337 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 19


2025-07-31 21:23:01:417 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:23:01:422 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:23:01:425 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:23:01:472 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:23:01:717 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:23:01:721 ==>> 该项需要延时执行
2025-07-31 21:23:01:802 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010


2025-07-31 21:23:02:274 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:02:961 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:23:03:458 ==>>                                                                                                                                                                               1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:

2025-07-31 21:23:03:563 ==>> [1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], 

2025-07-31 21:23:03:668 ==>> hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BL

2025-07-31 21:23:03:743 ==>> E_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:23:04:304 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:05:357 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 21:23:05:722 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:23:05:726 ==>> 检测【33V输入电压ADC】
2025-07-31 21:23:05:728 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:05:980 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:17:58][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:58][COMM]adc read left brake adc:18  volt:23 mv
[D][05:17:58][COMM]adc read right brake adc:15  volt:19 mv
[D][05:17:58][COMM]adc read throttle adc:18  volt:23 mv
[D][05:17:58][COMM]adc read battery ts volt:19 mv
[D][05:17:58][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:17:58][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2472  volt:3983 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:23:06:278 ==>> 【33V输入电压ADC】通过,【32855mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:23:06:281 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:23:06:283 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:23:06:315 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:06:390 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1646mV
Get AD_V4 0mV
Get AD_V5 2747mV
Get AD_V6 2023mV
Get AD_V7 1084mV
OVER 150


2025-07-31 21:23:06:576 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:06:579 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:23:06:625 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:06:627 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:23:06:629 ==>> 原始值:【2747】, 乘以分压基数【2】还原值:【5494】
2025-07-31 21:23:06:658 ==>> 【TP68_VCC5V5(ADV5)】通过,【5494mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:23:06:661 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:23:06:692 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10019
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020


2025-07-31 21:23:06:718 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2023mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:23:06:720 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:23:06:764 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:23:06:766 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:23:06:887 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1651mV
Get AD_V4 0mV
Get AD_V5 2750mV
Get AD_V6 1985mV
Get AD_V7 1086mV
OVER 150


2025-07-31 21:23:07:061 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:07:063 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:23:07:093 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:07:095 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:23:07:097 ==>> 原始值:【2750】, 乘以分压基数【2】还原值:【5500】
2025-07-31 21:23:07:125 ==>> 【TP68_VCC5V5(ADV5)】通过,【5500mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:23:07:127 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:23:07:157 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1985mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:23:07:160 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:23:07:195 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:23:07:197 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:23:07:281 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1647mV
Get AD_V4 0mV
Get AD_V5 2749mV
Get AD_V6 2020mV
Get AD_V7 1086mV
OVER 150


2025-07-31 21:23:07:486 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:07:489 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:23:07:521 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:07:525 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:23:07:526 ==>> 原始值:【2749】, 乘以分压基数【2】还原值:【5498】
2025-07-31 21:23:07:553 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx r

2025-07-31 21:23:07:556 ==>> 【TP68_VCC5V5(ADV5)】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:23:07:569 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:23:07:589 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2020mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:23:07:592 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:23:07:598 ==>> et[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:23:07:628 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:23:07:631 ==>> 检测【打开WIFI(1)】
2025-07-31 21:23:07:634 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:23:08:068 ==>>                                                                                                                           32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087569689

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx 

2025-07-31 21:23:08:098 ==>> ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:23:08:178 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:23:08:181 ==>> 检测【清空消息队列(1)】
2025-07-31 21:23:08:183 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:23:08:370 ==>> [D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:23:08:499 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:23:08:503 ==>> 检测【打开GPS(1)】
2025-07-31 21:23:08:505 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:23:08:670 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:23:08:830 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:23:08:832 ==>> 检测【打开GSM联网】
2025-07-31 21:23:08:835 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:23:09:054 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:23:09:202 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:23:09:223 ==>> 检测【打开仪表供电1】
2025-07-31 21:23:09:226 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:23:09:378 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 21:23:09:531 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:23:09:534 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:23:09:547 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:23:09:775 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:23:09:879 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:23:09:882 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:23:09:884 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:23:10:067 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33479]


2025-07-31 21:23:10:210 ==>> 【读取主控ADC采集的仪表电压】通过,【33479mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:23:10:213 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:23:10:217 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:23:10:278 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:23:10:383 ==>>                                       [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:23:10:543 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:23:10:546 ==>> 检测【AD_V20电压】
2025-07-31 21:23:10:549 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:23:10:653 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:23:10:774 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1mV
OVER 150


2025-07-31 21:23:11:097 ==>> 本次取值间隔时间:441ms
2025-07-31 21:23:11:177 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:23:11:202 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][

2025-07-31 21:23:11:261 ==>> 05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:23:11:291 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:23:11:366 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:23:11:591 ==>>                                                                                                                     on recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]

2025-07-31 21:23:11:636 ==>> <<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:23:11:681 ==>> 本次取值间隔时间:388ms
2025-07-31 21:23:11:729 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:23:11:831 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:23:12:061 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:23:12:274 ==>> 本次取值间隔时间:437ms
2025-07-31 21:23:12:289 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:23:12:730 ==>> 本次取值间隔时间:441ms
2025-07-31 21:23:12:824 ==>> 本次取值间隔时间:93ms
2025-07-31 21:23:12:961 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:23:12:976 ==>> 本次取值间隔时间:142ms
2025-07-31 21:23:12:981 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:23:13:081 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:23:13:157 ==>> 本次取值间隔时间:69ms
2025-07-31 21:23:13:201 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,59,,,41,33,,,41,25,,,40,24,,,39,1*7E

$GBGSV,2,2,08,16,,,37,3,,,50,4,,,45,60,,,37,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1641.673,1641.673,52.442,2097152,2097152,2097152*4A

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1640mV
OVER 150


2025-07-31 21:23:13:321 ==>> 本次取值间隔时间:151ms
2025-07-31 21:23:13:381 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:05][COMM]read battery soc:255


2025-07-31 21:23:13:397 ==>> 【AD_V20电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:13:400 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:23:13:403 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:23:13:471 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:23:13:714 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:23:13:717 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:23:13:721 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:23:13:896 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:23:14:067 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:23:14:071 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:23:14:076 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:23:14:091 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,3,,,41,33,,,41,25,,,41,59,,,40,1*48

$GBGSV,4,2,13,24,,,40,60,,,40,16,,,37,4,,,33,1*45

$GBGSV,4,3,13,44,,,33,5,,,32,39,,,32,1,,,38,1*76

$GBGSV,4,4,13,41,,,36,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1545.308,1545.308,49.478,2097152,2097152,2097152*49



2025-07-31 21:23:14:181 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:23:14:395 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:23:14:398 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:23:14:401 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:23:14:564 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:23:14:747 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:23:14:750 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:23:14:752 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:23:14:963 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:23:15:051 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:23:15:054 ==>> 检测【AD_V21电压】
2025-07-31 21:23:15:056 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:23:15:068 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,3,,,41,25,,,41,59,,,40,1*4E

$GBGSV,5,2,17,24,,,40,60,,,40,41,,,39,40,,,39,1*76

$GBGSV,5,3,17,1,,,37,16,,,37,39,,,36,44,,,34,1*48

$GBGSV,

2025-07-31 21:23:15:113 ==>> 5,4,17,4,,,33,5,,,32,38,,,32,2,,,31,1*4B

$GBGSV,5,5,17,23,,,37,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1539.174,1539.174,49.273,2097152,2097152,2097152*44

1A A1 20 00 00 
Get AD_V21 1042mV
OVER 150


2025-07-31 21:23:15:278 ==>> 本次取值间隔时间:216ms
2025-07-31 21:23:15:313 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:23:15:383 ==>> [D][05:18:07][COMM]read battery soc:255
1A A1 20 00 00 
Get AD_V21 1635mV
OVER 150


2025-07-31 21:23:15:488 ==>> 本次取值间隔时间:160ms
2025-07-31 21:23:15:537 ==>> 【AD_V21电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:15:540 ==>> 检测【关闭仪表供电2】
2025-07-31 21:23:15:542 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:23:15:773 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:23:15:823 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:23:15:826 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:23:15:829 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:23:15:998 ==>> [D][05:18:08][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:08][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:23:16:103 ==>> $GBGGA,132319.951,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,3,,,41,25,,,41,59,,,40,1*40

$GBGSV,5,2,19,24,,,40,60,,,40,41,,,39,40,,,39,1*78

$GBGSV,5,3,19,39,,,38,1,,,37,16,,,37,34,,,37,1*4C

$GBGSV,5,4,19,23,,,34,44,,,34,4,,,33,38,,,33,1*41

$GBGSV,5,5,19,5,,,32,2,,,28,10,,,37,1*77

$GBRMC,132319.951,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.

2025-07-31 21:23:16:120 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:23:16:125 ==>> 检测【打开AccKey2供电】
2025-07-31 21:23:16:127 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:23:16:133 ==>> 000,N,0.000,K,N*20

$GBGST,132319.951,0.000,1531.697,1531.697,49.042,2097152,2097152,2097152*5C



2025-07-31 21:23:16:238 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:23:16:407 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:23:16:410 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:23:16:413 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:16:769 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:08][COMM]adc read out 24v adc:1324  volt:33487 mv
[D][05:18:08][COMM]adc read left brake adc:21  volt:27 mv
[D][05:18:08][COMM]adc read right brake adc:23  volt:30 mv
[D][05:18:08][COMM]adc read throttle adc:17  volt:22 mv
[D][05:18:08][COMM]adc read battery ts volt:26 mv
[D][05:18:08][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:08][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,132320.551,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,3,,,40,59,,,40,1*4C

$GBGSV,6,2,24,24,,,40,60,,,40,41,,,39,40,,,39,1*75

$GBGSV,6,3,24,39,,,38,1,,,37,16,,,37,34,,,36,1*40

$GBGSV,6,4,24,7,,,36,23,,,34,44,,,34,9,,,34,1*7F

$GBGSV,6,5,

2025-07-31 21:23:16:814 ==>> 24,38,,,34,4,,,33,6,,,33,10,,,32,1*7D

$GBGSV,6,6,24,5,,,32,12,,,30,2,,,27,13,,,20,1*73

$GBRMC,132320.551,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132320.551,0.000,1464.966,1464.966,46.970,2097152,2097152,2097152*5D



2025-07-31 21:23:16:956 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33487mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:23:16:959 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:23:16:964 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:23:17:149 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:23:17:266 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:23:17:270 ==>> 该项需要延时执行
2025-07-31 21:23:17:389 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:23:17:740 ==>> $GBGGA,132321.531,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,3,,,40,59,,,40,1*4C

$GBGSV,7,2,25,24,,,40,60,,,40,41,,,39,40,,,38,1*74

$GBGSV,7,3,25,39,,,38,16,,,37,1,,,36,34,,,36,1*41

$GBGSV,7,4,25,7,,,36,42,,,36,23,,,35,13,,,35,1*40

$GBGSV,7,5,25,44,,,34,9,,,34,38,,,34,6,,,34,1*77

$GBGSV,7,6,25,4,,,33,10,,,33,5,,,32,12,,,32,1*73

$GBGSV,7,7,25,2,,,27,1*46

$GBRMC,132321.531,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132321.531,0.000,1495.845,1495.845,47.883,2097152,2097152,2097152*56



2025-07-31 21:23:18:724 ==>> $GBGGA,132322.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,7,2,26,24,,,40,60,,,40,41,,,39,39,,,39,1*78

$GBGSV,7,3,26,40,,,38,16,,,37,1,,,36,34,,,36,1*4C

$GBGSV,7,4,26,7,,,36,42,,,35,23,,,35,13,,,35,1*40

$GBGSV,7,5,26,9,,,35,44,,,34,38,,,34,6,,,34,1*75

$GBGSV,7,6,26,10,,,34,4,,,33,5,,,32,12,,,32,1*77

$GBGSV,7,7,26,8,,,29,2,,,27,1*76

$GBRMC,132322.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132322.511,0.000,1484.561,1484.561,47.527,2097152,2097152,2097152*54



2025-07-31 21:23:19:138 ==>> [D][05:18:11][COMM]S->M yaw:INVALID


2025-07-31 21:23:19:198 ==>> [D][05:18:11][COMM]IMU: [-7,3,-995] ret=35 AWAKE!


2025-07-31 21:23:19:393 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:23:19:716 ==>> $GBGGA,132323.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,60,,,40,1*47

$GBGSV,7,2,26,59,,,39,24,,,39,41,,,39,39,,,38,1*73

$GBGSV,7,3,26,40,,,38,16,,,37,1,,,36,34,,,36,1*4C

$GBGSV,7,4,26,7,,,36,23,,,36,42,,,35,9,,,35,1*78

$GBGSV,7,5,26,6,,,35,13,,,34,44,,,34,38,,,34,1*4E

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,32,5,,,32,1*77

$GBGSV,7,7,26,8,,,30,2,,,25,1*7C

$GBRMC,132323.511,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132323.511,0.000,1479.779,1479.779,47.376,2097152,2097152,2097152*57



2025-07-31 21:23:20:188 ==>> [D][05:18:12][COMM]M->S yaw:INVALID


2025-07-31 21:23:20:278 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:23:20:284 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:23:20:289 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:20:586 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:12][COMM]adc read out 24v adc:14  volt:354 mv
[D][05:18:12][COMM]adc read left brake adc:20  volt:26 mv
[D][05:18:12][COMM]adc read right brake adc:24  volt:31 mv
[D][05:18:12][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:12][COMM]adc read battery ts volt:26 mv
[D][05:18:12][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:12][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2489  volt:4010 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:23:20:691 ==>>                                                                                                                                                                                                                              ,,,37,1*7C

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,34,44,,,34,38,,,34,1*4E

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,32,5,,,32,1*77

$GBGSV,7,7,26,8,,,31,2,,,26,1*7E

$GBRMC,132324.511,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.0

2025-07-31 21:23:20:721 ==>> 00,N,0.000,K,N*20

$GBGST,132324.511,0.000,1490.939,1490.939,47.732,2097152,2097152,2097152*54



2025-07-31 21:23:20:828 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【354mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:23:20:834 ==>> 检测【打开AccKey1供电】
2025-07-31 21:23:20:838 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:23:21:058 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:23:21:121 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:23:21:125 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:23:21:138 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:23:21:163 ==>> 1A A1 00 40 00 
Get AD_V14 2640mV
OVER 150


2025-07-31 21:23:21:378 ==>> 原始值:【2640】, 乘以分压基数【2】还原值:【5280】
2025-07-31 21:23:21:393 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:23:21:414 ==>> 【读取AccKey1电压(ADV14)前】通过,【5280mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:23:21:417 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:23:21:439 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:21:759 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:13][COMM]adc read out 24v adc:13  volt:328 mv
[D][05:18:13][COMM]adc read left brake adc:19  volt:25 mv
[D][05:18:13][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:13][COMM]adc read throttle adc:18  volt:23 mv
[D][05:18:13][COMM]adc read battery ts volt:21 mv
[D][05:18:13][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:13][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2475  volt:3988 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,132325.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,3,,,40,24,,,40,1*45

$GBGSV,7,2,26,60,,,39,59,,,39,41,,,39,39,,,39,1*72

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,37,1,,,36,1*7D

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,36,9,,,35,1*4B

$GBGSV,7,5,26,6,,,35,13,,,34,44,,,34,38,,,34,1*4E

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,32,5,,,32,1*77

$GBGSV,7,7,26,8,,,31,2

2025-07-31 21:23:21:804 ==>> ,,,26,1*7E

$GBRMC,132325.511,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132325.511,0.000,1490.939,1490.939,47.731,2097152,2097152,2097152*56



2025-07-31 21:23:21:960 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:23:21:963 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:23:21:966 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:23:22:168 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:23:22:250 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:23:22:253 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:23:22:260 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:23:22:380 ==>> 1A A1 00 40 00 
Get AD_V14 2643mV
OVER 150


2025-07-31 21:23:22:515 ==>> 原始值:【2643】, 乘以分压基数【2】还原值:【5286】
2025-07-31 21:23:22:551 ==>> 【读取AccKey1电压(ADV14)后】通过,【5286mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:23:22:555 ==>> 检测【打开WIFI(2)】
2025-07-31 21:23:22:559 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:23:22:728 ==>> $GBGGA,132326.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,3,,,40,60,,,40,1*44

$GBGSV,7,2,26,59,,,40,24,,,39,41,,,39,39,,,38,1*7D

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,36,1,,,36,1*7C

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,34,44,,,34,38,,,34,1*4E

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,32,5,,,32,1*77

$GBGSV,7,7,26,8,,,32,2,,,25,1*7E

$GBRMC,132326.511,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132326.511,0.000,1486.157,1486.157,47.580,2097152,2097152,2097152*5D



2025-07-31 21:23:22:788 ==>>                                                                                                                                                                                                                                                       

2025-07-31 21:23:23:414 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:23:23:505 ==>> +WIFISCAN:4,0,CC057790A741,-73
+WIFISCAN:4,1,CC057790A740,-74
+WIFISCAN:4,2,CC057790A5C0,-79
+WIFISCAN:4,3,CC057790A5C1,-79

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 21:23:23:579 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:23:23:700 ==>> $GBGGA,132327.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,7,2,26,60,,,39,24,,,39,41,,,38,39,,,38,1*78

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,36,1,,,36,1*7C

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,34,44,,,34,38,,,34,1*4E

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,33,5,,,32,1*76

$GBGSV,7,7,26,8,,,32,2,,,26,1*7D

$GBRMC,132327.511,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132327.511,0.000,1484.548,1484.548,47.514,2097152,2097152,2097152*51



2025-07-31 21:23:23:806 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][

2025-07-31 21:23:23:835 ==>> 05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:23:23:895 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:23:23:902 ==>> 检测【转刹把供电】
2025-07-31 21:23:23:915 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:23:24:060 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:23:24:189 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:23:24:194 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:23:24:198 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:23:24:305 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:23:24:382 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:16][GNSS]recv submsg id[3]
1A A1 00 80 00 
Get AD_V15 2387mV
OVER 150


2025-07-31 21:23:24:457 ==>> 原始值:【2387】, 乘以分压基数【2】还原值:【4774】
2025-07-31 21:23:24:492 ==>> 【读取AD_V15电压(前)】通过,【4774mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:23:24:497 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:23:24:501 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:23:24:547 ==>> +WIFISCAN:4,0,CC057790A740,-74
+WIFISCAN:4,1,44A1917CA62B,-75
+WIFISCAN:4,2,CC057790A5C1,-78
+WIFISCAN:4,3,CC057790A5C0,-79

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 21:23:24:607 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:23:24:652 ==>> $GBGGA,132328.511,,,,,0,00,,,M,,M,,*66

$GBGS

2025-07-31 21:23:24:742 ==>> A,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,7,2,26,60,,,40,24,,,39,41,,,39,39,,,38,1*77

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,36,1,,,36,1*7C

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,35,44,,,34,38,,,34,1*4F

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,33,5,,,32,1*76

$GBGSV,7,7,26,8,,,32,2,,,26,1*7D

$GBRMC,132328.511,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132328.511,0.000,1489.334,1489.334,47.670,2097152,2097152,2097152*5F

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2418mV
OVER 150


2025-07-31 21:23:24:772 ==>> 原始值:【2418】, 乘以分压基数【2】还原值:【4836】
2025-07-31 21:23:24:811 ==>> 【读取AD_V16电压(前)】通过,【4836mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:23:24:815 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:23:24:818 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:25:089 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:17][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:17][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:17][COMM]adc read right brake adc:21  volt:27 mv
[D][05:18:17][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:17][COMM]adc read battery ts volt:24 mv
[D][05:18:17][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3079  volt:5412 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2478  volt:3992 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:23:25:364 ==>> 【转刹把供电电压(主控ADC)】通过,【5412mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:23:25:370 ==>> 检测【转刹把供电电压】
2025-07-31 21:23:25:375 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:25:411 ==>> [D][05:18:17][GNSS]recv submsg id[3]
[D][05:18:17][COMM]read battery soc:255


2025-07-31 21:23:25:756 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:17][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:17][COMM]adc read left brake adc:25  volt:32 mv
[D][05:18:17][COMM]adc read right brake adc:23  volt:30 mv
[D][05:18:17][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:17][COMM]adc read battery ts volt:26 mv
[D][05:18:17][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2492  volt:4015 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,132329.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,26,60,,,40,24,,,40,41,,,39,39,,,39,1*78

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,37,1,,,37,1*7C

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,35,44,,,34,38,,,34,1*4F

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,33,5,,

2025-07-31 21:23:25:801 ==>> ,32,1*76

$GBGSV,7,7,26,8,,,32,2,,,29,1*72

$GBRMC,132329.511,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132329.511,0.000,1502.081,1502.081,48.068,2097152,2097152,2097152*5E



2025-07-31 21:23:25:910 ==>> 【转刹把供电电压】通过,【5407mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:23:25:914 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:23:25:918 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:23:26:059 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:23:26:207 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:23:26:211 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:23:26:216 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:23:26:317 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:23:26:377 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 3mV
OVER 150


2025-07-31 21:23:26:480 ==>> 【读取AD_V15电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:23:26:487 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:23:26:492 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:23:26:587 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:23:26:693 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:23:26:723 ==>> $GBGGA,132330.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,26,60,,,40,24,,,39,41,,,39,39,,,38,1*77

$GBGSV,7,3,26,40,,,38,16,,,37,7,,,37,1,,,36,1*7D

$GBGSV,7,4,26,34,,,36,23,,,36,42,,,35,9,,,35,1*48

$GBGSV,7,5,26,6,,,35,13,,,35,44,,,34,38,,,34,1*4F

$GBGSV,7,6,26,10,,,34,12,,,33,4,,,33,5,,,32,1*76

$GBGSV,7,7,26,8,,,32,2,,,31,1*7B

$GBRMC,132330.511,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132330.511,0.000,1500.475,1500.475,48.006,2097152,2097152,2097152*5E

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:23:26:798 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:23:26:828 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:23:26:873 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:23:26:945 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:23:26:949 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:23:26:954 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:23:27:069 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:23:27:236 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:23:27:243 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:23:27:270 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:23:27:377 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:23:27:437 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:23:27:559 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:23:27:563 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:23:27:568 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:23:27:667 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:23:27:742 ==>> $GBGGA,132331.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,59,,,40,1*4F

$GBGSV,7,2,27,60,,,40,24,,,39,41,,,39,39,,,39,1*77

$GBGSV,7,3,27,40,,,38,16,,,37,7,,,36,1,,,36,1*7D

$GBGSV,7,4,27,34,,,36,14,,,36,23,,,35,42,,,35,1*75

$GBGSV,7,5,27,9,,,35,6,,,35,13,,,34,44,,,34,1*7C

$GBGSV,7,6,27,38,,,34,10,,,34,12,,,33,2,,,33,1*49

$GBGSV,7,7,27,4,,,32,5,,,32,8,,,32,1*4B

$GBRMC,132331.511,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132331.511,0.000,747.920,747.920,683.991,2097152,2097152,2097152*69



2025-07-31 21:23:27:878 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:23:27:881 ==>> 检测【左刹电压测试1】
2025-07-31 21:23:27:884 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:28:198 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:20][COMM]adc read out 24v adc:14  volt:354 mv
[D][05:18:20][COMM]adc read left brake adc:1738  volt:2291 mv
[D][05:18:20][COMM]adc read right brake adc:1740  volt:2293 mv
[D][05:18:20][COMM]adc read throttle adc:1732  volt:2283 mv
[D][05:18:20][COMM]adc read battery ts volt:25 mv
[D][05:18:20][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:20][COMM]adc read throttle brake in adc:17  volt:29 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2455  volt:3955 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:23:28:564 ==>> 【左刹电压测试1】通过,【2291】符合目标值【2250】至【2500】要求!
2025-07-31 21:23:28:567 ==>> 检测【右刹电压测试1】
2025-07-31 21:23:28:647 ==>> 【右刹电压测试1】通过,【2293】符合目标值【2250】至【2500】要求!
2025-07-31 21:23:28:653 ==>> 检测【转把电压测试1】
2025-07-31 21:23:28:724 ==>> 【转把电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 21:23:28:732 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:23:28:739 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:23:28:743 ==>> $GBGGA,132332.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,39,,,39,41,,,39,1*76

$GBGSV,7,3,27,40,,,38,16,,,37,14,,,37,7,,,36,1*48

$GBGSV,7,4,27,1,,,36,34,,,36,9,,,35,6,,,35,1*49

$GBGSV,7,5,27,42,,,35,23,,,35,2,,,34,10,,,34,1*45

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,33,1*7F

$GBGSV,7,7,27,8,,,32,5,,,32,4,,,32,1*4B

$GBRMC,132332.511,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132332.511,0.000,749.451,749.451,685.391,2097152,2097152,2097152*66



2025-07-31 21:23:28:772 ==>> 3A A3 03 00 A3 


2025-07-31 21:23:28:877 ==>> OFF_OUT3
OVER 150


2025-07-31 21:23:29:061 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:23:29:065 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:23:29:070 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:23:29:179 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:23:29:386 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:23:29:390 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:23:29:396 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:23:29:435 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:23:29:480 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:23:29:720 ==>> $GBGGA,132333.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,59,,,40,40,,,39,24,,,39,39,,,39,1*7C

$GBGSV,7,3,27,41,,,39,7,,,37,1,,,37,16,,,37,1*7D

$GBGSV,7,4,27,14,,,37,34,,,36,9,,,35,6,,,35,1*7C

$GBGSV,7,5,27,42,,,35,23,,,35,2,,,34,10,,,34,1*45

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,33,1*7F

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,132333.511,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132333.511,0.000,753.284,753.284,688.896,2097152,2097152,2097152*66



2025-07-31 21:23:29:727 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:23:29:730 ==>> 检测【左刹电压测试2】
2025-07-31 21:23:29:733 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:23:30:080 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:22][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:22][COMM]adc read left brake adc:21  volt:27 mv
[D][05:18:22][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:22][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:22][COMM]adc read battery ts volt:28 mv
[D][05:18:22][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:22][COMM]adc read throttle brake in adc:15  volt:26 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2491  volt:4013 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:23:30:343 ==>> 【左刹电压测试2】通过,【27】符合目标值【0】至【50】要求!
2025-07-31 21:23:30:346 ==>> 检测【右刹电压测试2】
2025-07-31 21:23:30:421 ==>> 【右刹电压测试2】通过,【21】符合目标值【0】至【50】要求!
2025-07-31 21:23:30:426 ==>> 检测【转把电压测试2】
2025-07-31 21:23:30:515 ==>> 【转把电压测试2】通过,【25】符合目标值【0】至【50】要求!
2025-07-31 21:23:30:521 ==>> 检测【晶振检测】
2025-07-31 21:23:30:526 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:23:30:748 ==>> $GBGGA,132334.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,59,,,40,24,,,39,39,,,39,41,,,39,1*7D

$GBGSV,7,3,27,40,,,38,7,,,37,16,,,37,14,,,37,1*49

$GBGSV,7,4,27,1,,,36,34,,,36,23,,,36,13,,,35,1*46

$GBGSV,7,5,27,9,,,35,6,,,35,42,,,35,2,,,34,1*4B

$GBGSV,7,6,27,10,,,34,38,,,34,44,,,34,5,,,33,1*4A

$GBGSV,7,7,27,12,,,33,4,,,33,8,,,32,1*7D

$GBRMC,132334.511,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132334.511,0.000,754.045,754.045,689.591,2097152,2097152,2097152*6A

[W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 21:23:30:851 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:23:30:859 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:23:30:864 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:23:30:991 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1651mV
Get AD_V4 1651mV
Get AD_V5 2752mV
Get AD_V6 1989mV
Get AD_V7 1086mV
OVER 150


2025-07-31 21:23:31:319 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:23:31:324 ==>> 检测【检测BootVer】
2025-07-31 21:23:31:329 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:31:641 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = ***************
[D][05:18:23][FCTY]HardwareID  = 867222087569689
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:23][FCTY]Bat         = 4044 mv
[D][05:18:23][FCTY]Current     = 150 ma
[D][05:18:23][FCTY]VBUS        = 11600 mv
[D][05:18:23][COMM]read battery soc:255
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:23][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 227 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18

2025-07-31 21:23:31:746 ==>> :23][FCTY]Bat1         = 3838 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:23:31:875 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:23:31:878 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:23:31:883 ==>> 检测【检测固件版本】
2025-07-31 21:23:31:908 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:23:31:913 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:23:31:919 ==>> 检测【检测蓝牙版本】
2025-07-31 21:23:31:940 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:23:31:945 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:23:31:948 ==>> 检测【检测MoBikeId】
2025-07-31 21:23:31:978 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:23:31:983 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:23:31:989 ==>> 检测【检测蓝牙地址】
2025-07-31 21:23:31:993 ==>> 取到目标值:F231E10459F2
2025-07-31 21:23:32:010 ==>> 【检测蓝牙地址】通过,【F231E10459F2】符合目标值【】要求!
2025-07-31 21:23:32:014 ==>> 提取到蓝牙地址:F231E10459F2
2025-07-31 21:23:32:021 ==>> 检测【BOARD_ID】
2025-07-31 21:23:32:049 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:23:32:054 ==>> 检测【检测充电电压】
2025-07-31 21:23:32:101 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:23:32:105 ==>> 检测【检测VBUS电压1】
2025-07-31 21:23:32:142 ==>> 【检测VBUS电压1】通过,【11600mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:23:32:147 ==>> 检测【检测充电电流】
2025-07-31 21:23:32:186 ==>> 【检测充电电流】通过,【150ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:23:32:194 ==>> 检测【检测IMEI】
2025-07-31 21:23:32:221 ==>> 取到目标值:867222087569689
2025-07-31 21:23:32:250 ==>> 【检测IMEI】通过,【867222087569689】符合目标值【】要求!
2025-07-31 21:23:32:254 ==>> 提取到IMEI:867222087569689
2025-07-31 21:23:32:260 ==>> 检测【检测IMSI】
2025-07-31 21:23:32:269 ==>> 取到目标值:***************
2025-07-31 21:23:32:304 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 21:23:32:308 ==>> 提取到IMSI:***************
2025-07-31 21:23:32:312 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:23:32:316 ==>> 取到目标值:***************
2025-07-31 21:23:32:352 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 21:23:32:359 ==>> 检测【打开CAN通信】
2025-07-31 21:23:32:366 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:23:32:479 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:23:32:650 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:23:32:654 ==>> 检测【检测CAN通信】
2025-07-31 21:23:32:661 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:23:32:779 ==>> $GBGGA,132336.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,41,,,39,1*73

$GBGSV,7,3,27,40,,,38,7,,,37,16,,,37,14,,,37,1*49

$GBGSV,7,4,27,1,,,36,34,,,36,13,,,35,9,,,35,1*7D

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,2,,,34,1*73

$GBGSV,7,6,27,10,,,34,38,,,34,44,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,132336.511,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132336.511,0.000,750.978,750.978,686.787,2097152,2097152,2097152*62

can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:23:32:854 ==>> [D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36236
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:23:32:914 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:23:32:947 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:23:32:952 ==>> 检测【关闭CAN通信】
2025-07-31 21:23:32:958 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:23:32:977 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:23:33:034 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:23:33:079 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:23:33:234 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:23:33:245 ==>> 检测【打印IMU STATE】
2025-07-31 21:23:33:279 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:23:33:475 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
[D][05:18:25][COMM]read battery soc:255


2025-07-31 21:23:33:572 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:23:33:577 ==>> 检测【六轴自检】
2025-07-31 21:23:33:584 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:23:33:730 ==>> $GBGGA,132337.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,39,,,39,41,,,39,1*76

$GBGSV,7,3,27,40,,,38,7,,,37,16,,,37,14,,,37,1*49

$GBGSV,7,4,27,1,,,36,34,,,36,13,,,35,9,,,35,1*7D

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,2,,,34,1*73

$GBGSV,7,6,27,10,,,34,38,,,34,44,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,132337.511,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132337.511,0.000,752.511,752.511,688.188,2097152,2097152,2097152*64



2025-07-31 21:23:33:820 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:23:34:727 ==>> $GBGGA,132338.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,41,,,39,40,,,38,1*7C

$GBGSV,7,3,27,39,,,38,7,,,37,1,,,37,16,,,37,1*73

$GBGSV,7,4,27,14,,,37,34,,,36,13,,,35,9,,,35,1*48

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,2,,,34,1*73

$GBGSV,7,6,27,10,,,34,38,,,34,44,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,132338.511,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132338.511,0.000,751.741,751.741,687.484,2097152,2097152,2097152*6D



2025-07-31 21:23:35:455 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:23:35:515 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:23:35:757 ==>> $GBGGA,132339.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,7,,,37,1,,,37,16,,,37,1*7C

$GBGSV,7,4,27,14,,,37,34,,,36,2,,,35,9,,,35,1*78

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,10,,,34,1*40

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,33,1*7F

$GBGSV,7,7,27,5,,,32,8,,,32,4,,,32,1*4B

$GBRMC,132339.511,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132339.511,0.000,750.213,750.213,686.087,2097152,2097152,2097152*6A

[D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39060 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-9,-13,4017]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 21:23:35:956 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:23:35:960 ==>> 检测【打印IMU STATE2】
2025-07-31 21:23:35:966 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:23:36:170 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:1
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:23:36:251 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:23:36:255 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:23:36:262 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:23:36:365 ==>> 5A A5 02 5A A5 


2025-07-31 21:23:36:470 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:23:36:586 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:23:36:590 ==>> 检测【检测VBUS电压2】
2025-07-31 21:23:36:597 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:36:758 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11
$GBGGA,132340.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,41,,,39,1*7C

$GBGSV,7,3,27,40,,,38,7,,,37,1,,,37,16,,,37,1*7D

$GBGSV,7,4,27,14,,,37,34,,,36,2,,,35,13,,,35,1*43

$GBGSV,7,5,27,9,,,35,6,,,35,42,,,35,23,,,35,1*79

$GBGSV,7,6,27,10,,,34,38,,,34,44,,,34,12,,,33,1*7C

$GBGSV,7,7,27,5,,,32,8,,,32,4,,,32,1*4B

$GBRMC,132340.511,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132340.511,0.000,752.514,752.514,688.192,2097152,2097152,2097152*6F



2025-07-31 21:23:37:060 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = ***************
[D][05:18:29][FCTY]HardwareID  = 867222087569689
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 7, adc = 312
[D][05:18:29][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 354 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_G

2025-07-31 21:23:37:106 ==>> NSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3838 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:23:37:177 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:37:534 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = ***************
[D][05:18:29][FCTY]HardwareID  = 867222087569689
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 183
[D][05:18:29][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 429 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CA

2025-07-31 21:23:37:579 ==>> T1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3838 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:23:37:684 ==>>                                    *69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,59,,,40,24,,,40,1*4E

$GBGSV,7,2,27,25,,,40,60,,,39,41,,,39,40,,,38,1*79

$GBGSV,7,3,27,39,,,38,1,,,37,16,,,37,14,,,37,1*41

$GBGSV,7,4,27,7,,,36,34,,,36,2,,,35,9,,,35,1*4B

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,10,,,34,1*40

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,33,1*7F

$GBGSV,7,7,27,5,,

2025-07-31 21:23:37:729 ==>> ,32,8,,,32,4,,,32,1*4B

$GBRMC,132341.511,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132341.511,0.000,750.215,750.215,686.089,2097152,2097152,2097152*6B



2025-07-31 21:23:37:767 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:37:834 ==>> [D][05:18:30][COMM]msg 0601 loss. last_tick:36230. cur_tick:41231. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41231


2025-07-31 21:23:38:134 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = ***************
[D][05:18:30][FCTY]HardwareID  = 867222087569689
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 150 ma
[D][05:18:30][FCTY]VBUS        = 11700 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 159
[D][05:18:30][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 227 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNS

2025-07-31 21:23:38:179 ==>> S_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3838 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:23:38:352 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:38:424 ==>> [D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 21:23:39:009 ==>>                                                                              mp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0

2025-07-31 21:23:39:114 ==>> x5d03
[D][05:18:30][PROT]===========================================================
[D][05:18:30][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[D][05:18:30][CAT1]gsm read msg sub id: 24
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][0

2025-07-31 21:23:39:219 ==>> 5:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = ***************
[D][05:18:30][FCTY]HardwareID  = 867222087569689
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS      

2025-07-31 21:23:39:324 ==>>   = 4900 mv
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 131
[D][05:18:30][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 252 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3838 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 24, ret: 6
[D][05:18:31][CAT1]sub id: 24, ret: 6

[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5A7DA0DC5B64776102BF13B8764EBD7FF450CB5BF617A53

2025-07-31 21:23:39:400 ==>> 5B75BED7C4110F643ACB871E7104FA379E9FBBE5906F722ACC67B8417906C47FC9B679D8A8AC12A31722E5A13F74EEDF001453460DA426D6DE513C
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 21:23:39:449 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:23:39:459 ==>>                                                         ]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:31][GNSS]location stop evt done evt


2025-07-31 21:23:39:834 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = ***************
[D][05:18:32][FCTY]HardwareID  = 867222087569689
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = F231E10459F2
[D][05:18:32][FCTY]Bat         = 3884 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 4900 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 2, adc = 113
[D][05:18:32][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 328 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_

2025-07-31 21:23:39:879 ==>> VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3838 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:23:40:052 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:23:40:058 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:23:40:062 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:23:40:179 ==>> 5A A5 01 5A A5 


2025-07-31 21:23:40:269 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:23:40:374 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18:32][COMM]read battery soc:255


2025-07-31 21:23:40:429 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:23:40:437 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:23:40:444 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:23:40:574 ==>> 5A A5 04 5A A5 


2025-07-31 21:23:40:679 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:23:40:744 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:23:40:749 ==>> 检测【打开WIFI(3)】
2025-07-31 21:23:40:756 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:23:40:995 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:23:41:078 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:23:41:083 ==>> 检测【扩展芯片hw】
2025-07-31 21:23:41:089 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:23:41:265 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:23:41:376 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:23:41:384 ==>> 检测【扩展芯片boot】
2025-07-31 21:23:41:418 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:23:41:424 ==>> 检测【扩展芯片sw】
2025-07-31 21:23:41:469 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:23:41:475 ==>> 检测【检测音频FLASH】
2025-07-31 21:23:41:483 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:23:41:655 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:23:41:976 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------
+WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,CC057790A741,-75
+WIFISCAN:4,2,CC057790A740,-75
+WIFISCAN:4,3,CC057790A5C1,-77

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 21:23:42:661 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                          50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len

2025-07-31 21:23:42:766 ==>> :10800, num:3
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]f:[ec800m_audio_start

2025-07-31 21:23:42:871 ==>> ].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2

2025-07-31 21:23:42:976 ==>> M_GSM_POWER_ON]
[D][05:18:34][PROT]index:1
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],se

2025-07-31 21:23:43:082 ==>> nd_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_au

2025-07-31 21:23:43:156 ==>> dio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255
[D][05:18:34][GNSS]recv submsg id[3]


2025-07-31 21:23:44:078 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B385451AEF8D490C9BC5B2713E93858A3FD67E54CF13649BFA99A902928

2025-07-31 21:23:44:153 ==>> 8D1EED42B316E5B296F50F3FD592D2F9E514BB25D972B51300F7C410F6AC8EF7E3E7EF4BDE3A2D61E03766FDE7B57289CE618D9FD9D
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 21:23:44:414 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 21:23:44:751 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:23:44:979 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:45:781 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 21:23:46:145 ==>> [D][05:18:38][COMM]49383 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:23:46:420 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 21:23:46:561 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:23:46:573 ==>> 检测【打开喇叭声音】
2025-07-31 21:23:46:590 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:23:47:279 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIO

2025-07-31 21:23:47:381 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:23:47:389 ==>> 检测【打开大灯控制】
2025-07-31 21:23:47:398 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:23:47:421 ==>> SEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:39][COMM]50394 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]

2025-07-31 21:23:47:490 ==>> . send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 21:23:47:550 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:23:47:682 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:23:47:692 ==>> 检测【关闭仪表供电3】
2025-07-31 21:23:47:719 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:23:47:843 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 21:23:47:987 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:23:47:995 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:23:48:014 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:23:48:136 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:23:48:279 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:23:48:284 ==>> 检测【读大灯电压】
2025-07-31 21:23:48:294 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:23:48:466 ==>> [D][05:18:40][COMM]read battery soc:255
[W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33154]


2025-07-31 21:23:48:574 ==>> 【读大灯电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:23:48:580 ==>> 检测【关闭大灯控制2】
2025-07-31 21:23:48:585 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:23:48:740 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:23:48:871 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:23:48:877 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:23:48:885 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:23:49:301 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]
[D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C11331133

2025-07-31 21:23:49:376 ==>> 1133113311331B88B315B12A7FCA0020A824D3A1F9C82F92C00F05F5CF20E4E7DA5CE400D4E217D76DEEC8D6B1447C4CECB15B48538A31EAD85EE4178DB1D1871CBDB7C5229A2B4EE4EB0A3BCA28488629C9026CA98D47FA919471
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 21:23:49:440 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:23:49:446 ==>> 检测【打开WIFI(4)】
2025-07-31 21:23:49:455 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:23:49:698 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:23:49:773 ==>> [D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:49:779 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:23:49:785 ==>> 检测【EC800M模组版本】
2025-07-31 21:23:49:790 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:23:49:968 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:23:50:211 ==>> [D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 21:23:50:362 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 21:23:50:372 ==>> 检测【配置蓝牙地址】
2025-07-31 21:23:50:395 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 21:23:50:436 ==>> +WIFISCAN:4,0,44A1917CA62B,-78
+WIFISCAN:4,1,CC057790A5C0,-81
+WIFISCAN:4,2,CC057790A4A1,-84
+WIFISCAN:4,3,CC057790A4A0,-84

[D][05:18:42][CAT1]wifi scan report total[4]
[D][05:18:42][COMM]read battery soc:255


2025-07-31 21:23:50:542 ==>> [D][05:18:42][GNSS]recv submsg id[3]
[W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 21:23:50:572 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:F231E10459F2>】
2025-07-31 21:23:50:782 ==>> [D][05:18:43][COMM]54169 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init
recv ble 1
recv ble 2
ble set mac ok :f2,31,e1,4,59,f2
enable filters ret : 0

2025-07-31 21:23:50:939 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 21:23:50:944 ==>> 检测【BLETEST】
2025-07-31 21:23:50:952 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 21:23:51:070 ==>> 4A A4 01 A4 4A 


2025-07-31 21:23:51:282 ==>> recv ble 1
recv ble 2
<BSJ*MAC:F231E10459F2*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9F231E10459F299999OVER 150


2025-07-31 21:23:51:800 ==>> [D][05:18:44][COMM]55181 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:23:52:024 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 21:23:52:030 ==>> 该项需要延时执行
2025-07-31 21:23:52:435 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save
                                         

2025-07-31 21:23:52:801 ==>> [D][05:18:45][COMM]56192 imu init OK


2025-07-31 21:23:54:531 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063

2025-07-31 21:23:54:606 ==>> B981113311331133113311331B88B3CE588838D46430646A7A0AAF23C0C7D1092435A4517717DF18BC975B7EE4800520B77DF73A7A415CB86A2BBC8346FA002BF566DA7C6CF6BFC35A4518D64D07B72EE0A73AA23EE9EEFFC3FCC6F24E7DBDDB1A
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
[D][05:18:46][COMM]read battery soc:255


2025-07-31 21:23:56:430 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 21:23:58:457 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 21:23:59:781 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[D][05:18:51][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B98211331133113

2025-07-31 21:23:59:856 ==>> 3113311331B88B56AA5F1AD99F060E97E6B3EB078B6F604DEB75814BE1414BDC4134930FC5B613DBE7D22EF6FDBE9BC7B9CE6C30181651F9C48C5527338538FCF36032E09D22D1B3360F4C74CB6311317A7AF6C48B4F61B5DB7
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 21:24:00:454 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 21:24:02:033 ==>> 此处延时了:【10000】毫秒
2025-07-31 21:24:02:039 ==>> 检测【检测WiFi结果】
2025-07-31 21:24:02:044 ==>> WiFi信号:【CC057790A741】,信号值:-73
2025-07-31 21:24:02:049 ==>> WiFi信号:【CC057790A740】,信号值:-74
2025-07-31 21:24:02:055 ==>> WiFi信号:【CC057790A5C0】,信号值:-79
2025-07-31 21:24:02:082 ==>> WiFi信号:【CC057790A5C1】,信号值:-79
2025-07-31 21:24:02:090 ==>> WiFi信号:【44A1917CA62B】,信号值:-75
2025-07-31 21:24:02:105 ==>> WiFi信号:【F88C21BCF57D】,信号值:-32
2025-07-31 21:24:02:110 ==>> WiFi信号:【CC057790A4A1】,信号值:-84
2025-07-31 21:24:02:135 ==>> WiFi信号:【CC057790A4A0】,信号值:-84
2025-07-31 21:24:02:143 ==>> WiFi数量【8】, 最大信号值:-32
2025-07-31 21:24:02:153 ==>> 检测【检测GPS结果】
2025-07-31 21:24:02:180 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:24:02:265 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 21:24:02:460 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 21:24:03:041 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:24:03:049 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:03:058 ==>> 定位已等待【1】秒.
2025-07-31 21:24:03:473 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:24:04:056 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:04:069 ==>> 定位已等待【2】秒.
2025-07-31 21:24:04:206 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:24:04:451 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 21:24:05:063 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:05:074 ==>> 定位已等待【3】秒.
2025-07-31 21:24:05:183 ==>> [D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:4
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900005]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[198]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,33,,,42,25,,,41,24,,,40,4,,,38,1*49

$GBGSV,2,2,05,14,,,38,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20


2025-07-31 21:24:05:288 ==>> 
$GBGST,,0.000,1699.678,1699.678,54.260,2097152,2097152,2097152*4A

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:57][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:57][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 23, ret: 6
[D][05:18:57][CAT1]sub id: 23, ret: 6

[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B596114FA2E2093D9CCD524709A155E8ED80A42BCDCA0E7765974A8AABA0CAEF4F200D5C0AF4A4A9BE4EE808C72A02156A5510206DD99A5D9C420A35BF53CCADDF192B51B253FC36946FC5CEB2B3547C70E3FD
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m 

2025-07-31 21:24:05:318 ==>> switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 21:24:05:559 ==>> [D][05:18:57][GNSS]recv submsg id[1]
[D][05:18:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:24:06:011 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,42,3,,,41,25,,,40,24,,,40,1*47

$GBGSV,4,2,14,59,,,39,39,,,39,14,,,38,60,,,38,1*70

$GBGSV,4,3,14,7,,,35,1,,,35,13,,,33,44,,,33,1*70

$GBGSV,4,4,14,5,,,32,4,,,36,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1546.729,1546.729,49.496,2097152,2097152,2097152*49



2025-07-31 21:24:06:071 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:06:080 ==>> 定位已等待【4】秒.
2025-07-31 21:24:06:474 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 21:24:07:007 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,3,,,41,25,,,40,24,,,40,1*4A

$GBGSV,5,2,18,59,,,39,39,,,39,60,,,39,14,,,37,1*73

$GBGSV,5,3,18,40,,,37,41,,,37,7,,,36,1,,,36,1*7E

$GBGSV,5,4,18,4,,,35,2,,,35,13,,,33,44,,,33,1*7A

$GBGSV,5,5,18,5,,,32,34,,,32,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1527.055,1527.055,48.859,2097152,2097152,2097152*47



2025-07-31 21:24:07:082 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:07:091 ==>> 定位已等待【5】秒.
2025-07-31 21:24:08:042 ==>> $GBGGA,132411.849,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,3,,,40,25,,,40,24,,,40,1*43

$GBGSV,6,2,23,59,,,39,39,,,39,60,,,39,14,,,38,1*77

$GBGSV,6,3,23,40,,,38,41,,,38,16,,,37,7,,,36,1*42

$GBGSV,6,4,23,1,,,36,2,,,34,13,,,34,34,,,34,1*71

$GBGSV,6,5,23,10,,,34,4,,,33,44,,,33,23,,,33,1*47

$GBGSV,6,6,23,5,,,32,12,,,40,6,,,37,1*76

$GBRMC,132411.849,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132411.849,0.000,1516.187,1516.187,48.503,2097152,2097152,2097152*5A



2025-07-31 21:24:08:087 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:08:098 ==>> 定位已等待【6】秒.
2025-07-31 21:24:08:473 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 21:24:08:818 ==>> $GBGGA,132412.549,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,40,25,,,40,59,,,40,1*4E

$GBGSV,6,2,24,24,,,39,39,,,39,60,,,39,40,,,38,1*7B

$GBGSV,6,3,24,41,,,38,14,,,37,16,,,37,7,,,36,1*4B

$GBGSV,6,4,24,1,,,36,6,,,35,2,,,34,13,,,34,1*46

$GBGSV,6,5,24,34,,,34,10,,,34,23,,,34,9,,,34,1*4D

$GBGSV,6,6,24,4,,,33,44,,,33,5,,,32,8,,,31,1*4A

$GBRMC,132412.549,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132412.549,0.000,1499.409,1499.409,47.969,2097152,2097152,2097152*5B



2025-07-31 21:24:09:090 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:09:102 ==>> 定位已等待【7】秒.
2025-07-31 21:24:09:739 ==>> $GBGGA,132413.529,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,25,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,39,39,,,39,60,,,39,40,,,38,1*79

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,6,,,35,34,,,35,9,,,35,1*4A

$GBGSV,7,5,27,2,,,34,13,,,34,10,,,34,23,,,34,1*41

$GBGSV,7,6,27,44,,,34,42,,,34,38,,,34,4,,,32,1*4D

$GBGSV,7,7,27,5,,,32,12,,,32,8,,,31,1*7F

$GBRMC,132413.529,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132413.529,0.000,1490.967,1490.967,47.702,2097152,2097152,2097152*5F



2025-07-31 21:24:10:095 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:10:107 ==>> 定位已等待【8】秒.
2025-07-31 21:24:10:450 ==>> [D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm

2025-07-31 21:24:10:555 ==>>  read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE3912ED2EED3103E812AF19A85D77E321B61472484C54FD2A658260E8E12FF8AA70E040A98D2C7BDD57BF8109773E44E41900
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]
                                         

2025-07-31 21:24:10:660 ==>> $GBGGA,132414.509,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,25,,,40,59,,,40,1*4C

$G

2025-07-31 21:24:10:720 ==>> BGSV,7,2,27,24,,,40,60,,,40,39,,,39,40,,,38,1*79

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,37,1,,,37,1*4E

$GBGSV,7,4,27,7,,,36,6,,,35,34,,,35,9,,,35,1*4C

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,32,5,,,32,8,,,32,1*4B

$GBRMC,132414.509,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132414.509,0.000,1500.173,1500.173,47.990,2097152,2097152,2097152*5F



2025-07-31 21:24:11:105 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:11:117 ==>> 定位已等待【9】秒.
2025-07-31 21:24:11:721 ==>> $GBGGA,132415.509,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,25,,,40,24,,,40,1*46

$GBGSV,7,2,27,59,,,39,60,,,39,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,37,1,,,37,1*4E

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,31,1*48

$GBRMC,132415.509,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132415.509,0.000,1497.101,1497.101,47.890,2097152,2097152,2097152*5F



2025-07-31 21:24:12:115 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:12:126 ==>> 定位已等待【10】秒.
2025-07-31 21:24:12:502 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 21:24:12:727 ==>> $GBGGA,132416.509,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,40,,,38,1*7D

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,37,1,,,37,1*4E

$GBGSV,7,4,27,7,,,37,34,,,36,6,,,35,9,,,35,1*4E

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132416.509,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132416.509,0.000,1506.317,1506.317,48.188,2097152,2097152,2097152*53



2025-07-31 21:24:13:120 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:13:130 ==>> 定位已等待【11】秒.
2025-07-31 21:24:13:726 ==>> $GBGGA,132417.509,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,25,,,40,3,,,40,24,,,40,1*46

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,41,,,39,1*7D

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,31,1*48

$GBRMC,132417.509,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132417.509,0.000,1500.172,1500.172,47.988,2097152,2097152,2097152*55



2025-07-31 21:24:14:127 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:14:138 ==>> 定位已等待【12】秒.
2025-07-31 21:24:14:500 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 21:24:14:727 ==>> $GBGGA,132418.509,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,25,,,40,3,,,40,60,,,40,1*46

$GBGSV,7,2,27,24,,,39,59,,,39,39,,,39,41,,,38,1*72

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,31,1*48

$GBRMC,132418.509,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132418.509,0.000,1497.097,1497.097,47.887,2097152,2097152,2097152*54



2025-07-31 21:24:15:130 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:15:141 ==>> 定位已等待【13】秒.
2025-07-31 21:24:15:755 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:07][CAT

2025-07-31 21:24:15:860 ==>> 1]Send Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BE6E8B7AA1D224281EFB8244F5935A3ECA21E152542DE2E687B8ADE3D36F9FAE573FB89DF1C98F79E3EFA8A709FA801A0C9338
[D][05:19:07][CAT1]<<< 
SEND OK

[D][05:19:07][CAT1]exec over: func id: 15, ret: 11
[D][05:19:07][CAT1]sub id: 15, ret: 11

[D][05:19:07][SAL ]Cellular task submsg id[68]
[D][05:19:07][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:07][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:07][M2M ]g_m2m_is_idle become true
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:07][PROT]M2M Send ok [1629955147]
$GBGGA,132419.509,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,60,,,40,1*45

$GBGSV,7,2,27,24,,,40,59,,,40,39,,,39,41,,,39,1*73

$GBGSV,7,3,27,40,,,38,14,,,37,7,,,37,16,,,36,1*48

$GBGSV,7,4,27,1,,,36,34,,,36,6,,,35,9,,,35,1*49

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,31,1*48

$GBRMC,132419.509,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132419.509,0.000,1504.784,1504.784,48.141,2097152,2097152,20

2025-07-31 21:24:15:894 ==>> 97152*59



2025-07-31 21:24:16:137 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:16:147 ==>> 定位已等待【14】秒.
2025-07-31 21:24:16:494 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 21:24:16:704 ==>> $GBGGA,132420.509,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,41,,,39,1*7D

$GBGSV,7,3,27,40,,,39,14,,,38,1,,,37,7,,,36,1*70

$GBGSV,7,4,27,16,,,36,34,,,36,6,,,35,9,,,35,1*7F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132420.509,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132420.509,0.000,1507.852,1507.852,48.236,2097152,2097152,2097152*50



2025-07-31 21:24:17:152 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:17:162 ==>> 定位已等待【15】秒.
2025-07-31 21:24:17:720 ==>> $GBGGA,132421.509,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,41,,,39,1*7D

$GBGSV,7,3,27,40,,,39,14,,,37,16,,,37,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132421.509,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132421.509,0.000,1506.315,1506.315,48.186,2097152,2097152,2097152*59



2025-07-31 21:24:18:160 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:18:171 ==>> 定位已等待【16】秒.
2025-07-31 21:24:18:501 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 21:24:18:696 ==>> $GBGGA,132422.509,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132422.509,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132422.509,0.000,1500.172,1500.172,47.988,2097152,2097152,2097152*53



2025-07-31 21:24:19:169 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:19:179 ==>> 定位已等待【17】秒.
2025-07-31 21:24:19:707 ==>> $GBGGA,132423.509,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,38,41,,,38,1*73

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132423.509,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132423.509,0.000,1497.098,1497.098,47.887,2097152,2097152,2097152*5C



2025-07-31 21:24:20:183 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:20:193 ==>> 定位已等待【18】秒.
2025-07-31 21:24:20:510 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 21:24:20:857 ==>> $GBGGA,132424.509,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,41,,,38,1*72

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,32,8,,,32,5,,,32,1*4B

$GBRMC,132424.509,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132424.509,0.000,1498.635,1498.635,47.938,2097152,2097152,2097152*5E

[D][05:19:13][PROT]CLEAN,SEND:2
[D][05:19:13][PROT]index:2 1629955153
[D][05:19:13][PROT]is_send:0
[D][05:19:13][PROT]sequence_num:6
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:1
[D][05:19:13][PROT]send_path:0x2
[D][05:19:13][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:13][PR

2025-07-31 21:24:20:931 ==>> OT]sending traceid [9999999999900007]
[D][05:19:13][PROT]Send_TO_M2M [1629955153]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:13][SAL ]sock send credit cnt[6]
[D][05:19:13][SAL ]sock send ind credit cnt[6]
[D][05:19:13][M2M ]m2m send data len[134]
[D][05:19:13][SAL ]Cellular task submsg id[10]
[D][05:19:13][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:13][CAT1]gsm read msg sub id: 15
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:13][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:13][CAT1]<<< 
ERROR



2025-07-31 21:24:21:193 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:21:203 ==>> 定位已等待【19】秒.
2025-07-31 21:24:21:717 ==>> $GBGGA,132425.509,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,39,41,,,39,1*73

$GBGSV,7,3,27,40,,,38,14,,,38,7,,,37,16,,,36,1*47

$GBGSV,7,4,27,1,,,36,34,,,36,6,,,35,9,,,35,1*49

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,33,1*7C

$GBGSV,7,7,27,4,,,33,8,,,32,5,,,32,1*4A

$GBRMC,132425.509,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132425.509,0.000,1503.242,1503.242,48.086,2097152,2097152,2097152*5C



2025-07-31 21:24:22:194 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:22:204 ==>> 定位已等待【20】秒.
2025-07-31 21:24:22:520 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 21:24:22:700 ==>> $GBGGA,132426.509,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,41,,,39,1*7D

$GBGSV,7,3,27,40,,,38,14,,,38,16,,,37,7,,,36,1*47

$GBGSV,7,4,27,1,,,36,34,,,36,6,,,35,9,,,35,1*49

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,8,,,32,5,,,32,1*4A

$GBRMC,132426.509,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132426.509,0.000,1507.848,1507.848,48.232,2097152,2097152,2097152*52



2025-07-31 21:24:23:195 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:23:205 ==>> 定位已等待【21】秒.
2025-07-31 21:24:23:716 ==>> $GBGGA,132427.509,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,59,,,39,60,,,39,39,,,39,41,,,38,1*72

$GBGSV,7,3,27,40,,,38,14,,,38,16,,,37,1,,,37,1*40

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,8,,,32,5,,,32,1*4A

$GBRMC,132427.509,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132427.509,0.000,1506.308,1506.308,48.180,2097152,2097152,2097152*59



2025-07-31 21:24:24:202 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:24:212 ==>> 定位已等待【22】秒.
2025-07-31 21:24:24:530 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 21:24:24:635 ==>> $GBGGA,132428.509,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14



2025-07-31 21:24:24:710 ==>> $GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,39,1*7C

$GBGSV,7,3,27,41,,,38,14,,,38,16,,,37,1,,,36,1*40

$GBGSV,7,4,27,7,,,36,34,,,36,6,,,35,9,,,35,1*4F

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,8,,,32,5,,,32,1*4A

$GBRMC,132428.509,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132428.509,0.000,1509.386,1509.386,48.284,2097152,2097152,2097152*51



2025-07-31 21:24:25:216 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:25:226 ==>> 定位已等待【23】秒.
2025-07-31 21:24:25:718 ==>> $GBGGA,132429.509,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,39,1*7C

$GBGSV,7,3,27,41,,,39,14,,,38,16,,,37,1,,,37,1*40

$GBGSV,7,4,27,7,,,37,34,,,36,6,,,35,9,,,35,1*4E

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,35,13,,,34,1*47

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,33,5,,,33,8,,,32,1*4B

$GBRMC,132429.509,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132429.509,0.000,1515.526,1515.526,48.478,2097152,2097152,2097152*55



2025-07-31 21:24:26:229 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:26:239 ==>> 定位已等待【24】秒.
2025-07-31 21:24:26:535 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 21:24:26:730 ==>> $GBGGA,132430.509,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,39,1*7C

$GBGSV,7,3,27,41,,,39,14,,,37,16,,,37,1,,,37,1*4F

$GBGSV,7,4,27,7,,,37,34,,,36,6,,,35,9,,,35,1*4E

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,4,,,32,5,,,32,8,,,32,1*4B

$GBRMC,132430.509,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132430.509,0.000,1509.390,1509.390,48.288,2097152,2097152,2097152*54



2025-07-31 21:24:27:240 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:27:250 ==>> 定位已等待【25】秒.
2025-07-31 21:24:27:749 ==>> $GBGGA,132431.509,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,3,,,40,24,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,38,1*7D

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,37,1,,,37,1*4E

$GBGSV,7,4,27,7,,,37,34,,,36,6,,,35,9,,,35,1*4E

$GBGSV,7,5,27,23,,,35,42,,,35,2,,,34,13,,,34,1*46

$GBGSV,7,6,27,10,,,34,44,,,34,38,,,34,12,,,34,1*7B

$GBGSV,7,7,27,5,,,33,4,,,32,8,,,32,1*4A

$GBRMC,132431.509,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132431.509,0.000,751.744,751.744,687.488,2097152,2097152,2097152*66



2025-07-31 21:24:28:253 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:28:264 ==>> 定位已等待【26】秒.
2025-07-31 21:24:28:541 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 21:24:28:736 ==>> $GBGGA,132432.509,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,60,,,39,39,,,39,41,,,39,1*77

$GBGSV,7,3,27,40,,,38,14,,,38,7,,,37,1,,,37,1*70

$GBGSV,7,4,27,16,,,37,34,,,36,9,,,35,6,,,35,1*7E

$GBGSV,7,5,27,42,,,35,23,,,35,2,,,34,10,,,34,1*45

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,34,1*78

$GBGSV,7,7,27,5,,,32,8,,,32,4,,,32,1*4B

$GBRMC,132432.509,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132432.509,0.000,753.284,753.284,688.896,2097152,2097152,2097152*69



2025-07-31 21:24:29:263 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:29:273 ==>> 定位已等待【27】秒.
2025-07-31 21:24:29:723 ==>> $GBGGA,132433.509,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,25,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,60,,,39,39,,,39,40,,,38,1*77

$GBGSV,7,3,27,14,,,38,41,,,38,16,,,37,7,,,36,1*46

$GBGSV,7,4,27,1,,,36,34,,,36,2,,,35,9,,,35,1*4D

$GBGSV,7,5,27,6,,,35,42,,,35,23,,,35,10,,,34,1*40

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,34,1*78

$GBGSV,7,7,27,5,,,32,8,,,32,4,,,32,1*4B

$GBRMC,132433.509,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132433.509,0.000,750.980,750.980,686.789,2097152,2097152,2097152*67



2025-07-31 21:24:30:270 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:30:281 ==>> 定位已等待【28】秒.
2025-07-31 21:24:30:556 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 21:24:30:872 ==>> $GBGGA,132434.509,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,25,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,60,,,39,39,,,39,40,,,38,1*77

$GBGSV,7,3,27,14,,,38,41,,,38,7,,,37,16,,,37,1*47

$GBGSV,7,4,27,1,,,36,34,,,36,9,,,35,6,,,35,1*49

$GBGSV,7,5,27,42,,,35,23,,,35,2,,,34,10,,,34,1*45

$GBGSV,7,6,27,13,,,34,38,,,34,44,,,34,12,,,34,1*78

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,32,1*4A

$GBRMC,132434.509,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132434.509,0.000,751.745,751.745,687.488,2097152,2097152,2097152*63

[D][05:19:23][CAT1]exec over: func id: 15, ret: -93
[D][05:19:23][CAT1]sub id: 15, ret: -93

[D][05:19:23][SAL ]Cellular task submsg id[68]
[D][05:19:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:23][SAL ]socket send fail. id[4]
[D][05:19:23][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:23][M2M ]m2m select fd[4]
[D][05:19:23][M2M ]socket[4] Link is disconnected
[D][05:19:23][M2M ]tcpclient close[4]
[D][05:19:23][SAL ]socket[4] has closed
[D][05:19:23][PROT]protocol read data ok
[E][05:19:23][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[

2025-07-31 21:24:30:934 ==>> E][05:19:23][PROT]M2M Send Fail [1629955163]
[D][05:19:23][PROT]CLEAN,SEND:2
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:23][CAT1]gsm read msg sub id: 10
[D][05:19:23][PROT]CLEAN:2
[D][05:19:23][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:23][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:23][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 21:24:31:206 ==>> [D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]exec over: func id: 10, ret: 6
[D][05:19:23][CAT1]sub id: 10, ret: 6

[D][05:19:23][SAL ]Cellular task submsg id[68]
[D][05:19:23][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:23][M2M ]m2m gsm shut done, ret[0]
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:23][SAL ]open socket ind id[4], rst[0]
[D][05:19:23][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:23][SAL ]Cellular task submsg id[8]
[D][05:19:23][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:23][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:23][CAT1]gsm read msg sub id: 8
[D][05:19:23][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:23][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:23][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:23][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:24:31:281 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:31:292 ==>> 定位已等待【29】秒.
2025-07-31 21:24:31:436 ==>> [D][05:19:23][CAT1]pdpdeact urc len[22]


2025-07-31 21:24:31:726 ==>> $GBGGA,132435.509,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,25,,,40,24,,,40,1*4A

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,14,,,38,41,,,38,7,,,37,16,,,37,1*48

$GBGSV,7,4,28,1,,,36,34,,,36,2,,,35,9,,,35,1*42

$GBGSV,7,5,28,6,,,35,42,,,35,23,,,35,10,,,34,1*4F

$GBGSV,7,6,28,13,,,34,38,,,34,44,,,34,12,,,34,1*77

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,28,1*4F

$GBRMC,132435.509,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132435.509,0.000,745.603,745.603,681.873,2097152,2097152,2097152*6C



2025-07-31 21:24:32:296 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:32:307 ==>> 定位已等待【30】秒.
2025-07-31 21:24:32:792 ==>> [D][05:19:24][COMM]read battery soc:255
[D][05:19:24][CAT1]<<< 
OK

[D][05:19:25][CAT1]tx ret[11] >>> AT+CGATT?

$GBGGA,132436.509,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,25,,,40,24,,,40,1*4A

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,7,,,37,14,,,37,1,,,36,1*70

$GBGSV,7,4,28,16,,,36,34,,,36,9,,,35,6,,,35,1*70

$GBGSV,7,5,28,42,,,35,23,,,35,2,,,34,10,,,34,1*4A

$GBGSV,7,6,28,13,,,34,38,,,34,44,,,34,12,,,34,1*77

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,29,1*4E

$GBRMC,132436.509,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132436.509,0.000,744.121,744.121,680.517,2097152,2097152,2097152*61

[D][05:19:25][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:25][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:25][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:25][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 21:24:32:882 ==>>                                                                                                                                                                                                      [D][05:19:25][CAT1]<<< 
OK

[D][05:19:25][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:24:33:109 ==>> [D][05:19:25][CAT1]opened : 0, 0
[D][05:19:25][SAL ]Cellular task submsg id[68]
[D][05:19:25][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:25][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:25][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:25][M2M ]g_m2m_is_idle become true
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:24:33:307 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:33:318 ==>> 定位已等待【31】秒.
2025-07-31 21:24:33:720 ==>> $GBGGA,132437.509,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,25,,,40,59,,,40,1*41

$GBGSV,7,2,28,24,,,40,60,,,39,40,,,39,39,,,39,1*79

$GBGSV,7,3,28,41,,,39,14,,,38,7,,,37,16,,,37,1*49

$GBGSV,7,4,28,1,,,36,34,,,36,2,,,35,9,,,35,1*42

$GBGSV,7,5,28,6,,,35,42,,,35,23,,,35,10,,,34,1*4F

$GBGSV,7,6,28,13,,,34,38,,,34,44,,,34,12,,,34,1*77

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,132437.509,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132437.509,0.000,750.036,750.036,685.926,2097152,2097152,2097152*6B



2025-07-31 21:24:34:309 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:34:320 ==>> 定位已等待【32】秒.
2025-07-31 21:24:34:554 ==>> [D][05:19:26][COMM]read battery soc:255


2025-07-31 21:24:34:659 ==>> $GBGGA,132438.509,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,60,,,40,3,,,40,25,,,40,1*4

2025-07-31 21:24:34:719 ==>> A

$GBGSV,7,2,28,59,,,39,24,,,39,39,,,39,41,,,39,1*7C

$GBGSV,7,3,28,40,,,38,7,,,37,14,,,37,1,,,36,1*71

$GBGSV,7,4,28,16,,,36,34,,,36,2,,,35,9,,,35,1*74

$GBGSV,7,5,28,6,,,35,42,,,35,23,,,35,10,,,34,1*4F

$GBGSV,7,6,28,13,,,34,38,,,34,44,,,34,12,,,34,1*77

$GBGSV,7,7,28,5,,,32,8,,,32,4,,,32,11,,,30,1*47

$GBRMC,132438.509,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132438.509,0.000,745.597,745.597,681.867,2097152,2097152,2097152*64



2025-07-31 21:24:35:311 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:35:323 ==>> 定位已等待【33】秒.
2025-07-31 21:24:35:719 ==>> $GBGGA,132439.509,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,25,,,40,60,,,39,1*44

$GBGSV,7,2,28,59,,,39,24,,,39,41,,,39,40,,,38,1*73

$GBGSV,7,3,28,39,,,38,1,,,37,14,,,37,7,,,36,1*7F

$GBGSV,7,4,28,16,,,36,34,,,36,9,,,35,6,,,35,1*70

$GBGSV,7,5,28,42,,,35,23,,,35,2,,,34,10,,,34,1*4A

$GBGSV,7,6,28,13,,,34,38,,,34,44,,,34,12,,,34,1*77

$GBGSV,7,7,28,5,,,32,8,,,32,4,,,32,11,,,30,1*47

$GBRMC,132439.509,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,132439.509,0.000,743.378,743.378,679.838,2097152,2097152,2097152*68



2025-07-31 21:24:36:321 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:36:335 ==>> 定位已等待【34】秒.
2025-07-31 21:24:37:036 ==>> [D][05:19:28][COMM]read battery soc:255
[D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg

2025-07-31 21:24:37:141 ==>>  02E3 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:

2025-07-31 21:24:37:246 ==>> 100027. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100028. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100028. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100028. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100029. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100029. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100029. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100030. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100030
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100030
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100031
$GBGGA,132436.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,61,227,42,3,61,190,40,39,59,8,39,16,58,36,37,1*7B

$GBGSV,7,2,28,6,56,49,35,40,55,180,39,59,52,129,39,7,48,181,37,1*4C

$GBGSV,7,3,28,25,47,310,40,1,46,125,36,2,45,237,35,1

2025-07-31 21:24:37:306 ==>> 4,45,186,38,1*79

$GBGSV,7,4,28,60,41,238,40,9,40,325,35,10,39,204,34,24,39,33,40,1*7C

$GBGSV,7,5,28,4,32,112,33,13,29,217,34,5,22,256,32,44,21,80,34,1*4A

$GBGSV,7,6,28,38,14,198,34,8,12,201,32,42,12,322,35,41,,,39,1*72

$GBGSV,7,7,28,34,,,36,23,,,35,12,,,34,11,,,30,1*7E

$GBRMC,132436.515,V,,,,,,,310725,1.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,132436.515,0.083,0.282,0.260,0.431,1.394,2.739,11*5A



2025-07-31 21:24:37:336 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:37:349 ==>> 定位已等待【35】秒.
2025-07-31 21:24:38:337 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:24:38:350 ==>> 定位已等待【36】秒.
2025-07-31 21:24:38:532 ==>> $GBGGA,132437.515,2301.2577647,N,11421.9415810,E,1,09,1.12,76.228,M,-1.770,M,,*54

$GBGSA,A,3,33,39,07,40,25,14,24,13,44,,,,2.72,1.12,2.48,4*0C

$GBGSV,7,1,28,33,61,227,42,3,61,190,40,16,58,36,37,6,56,49,35,1*71

$GBGSV,7,2,28,39,53,18,39,59,52,129,40,7,48,181,37,40,47,162,39,1*7C

$GBGSV,7,3,28,25,47,310,40,1,46,125,36,2,45,237,35,14,45,186,38,1*79

$GBGSV,7,4,28,60,41,238,39,9,40,325,35,10,39,204,35,24,39,33,40,1*73

$GBGSV,7,5,28,41,37,306,39,4,32,112,33,13,29,217,34,34,26,138,36,1*41

$GBGSV,7,6,28,5,22,256,32,44,21,80,34,38,14,198,34,8,12,201,32,1*44

$GBGSV,7,7,28,42,12,322,35,23,,,35,12,,,34,11,,,30,1*4C

$GBGSV,1,1,04,33,61,227,42,25,47,310,40,24,39,33,39,44,21,80,35,5*79

$GBRMC,132437.515,A,2301.2577647,N,11421.9415810,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:19:30][GNSS]HD8040 GPS
[D][05:19:30][GNSS]GPS diff_sec 124013107, report 0x42 frame
$GBGST,132437.515,0.959,0.380,0.337,0.573,1.349,2.043,7.753*70

[D][05:19:30][COMM]Main Task receive event:131
[D][05:19:30][COMM]index:0,power_mode:0xFF
[D][05:19:30][COMM]index:1,sound_mode:0xFF
[D][05:19:30][COMM]index:2,gsensor_mode:0xFF
[D][05:19:

2025-07-31 21:24:38:637 ==>> 30][COMM]index:3,report_freq_mode:0xFF
[D][05:19:30][COMM]index:4,report_period:0xFF
[D][05:19:30][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:30][COMM]index:6,normal_reset_period:0xFF
[D][05:19:30][COMM]index:7,spock_over_speed:0xFF
[D][05:19:30][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:30][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:30][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:30][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:30][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:30][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:30][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:30][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:30][COMM]index:16,imu_config_params:0xFF
[D][05:19:30][COMM]index:17,long_connect_params:0xFF
[D][05:19:30][COMM]index:18,detain_mark:0xFF
[D][05:19:30][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:30][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:30][COMM]index:21,mc_mode:0xFF
[D][05:19:30][COMM]index:22,S_mode:0xFF
[D][05:19:30][COMM]index:23,overweight:0xFF
[D][05:19:30][COMM]index:24,standstill_mode:0xFF
[D][05:19:30][COMM]index:25,night_mode:0xFF
[D][05:19:30][COMM]index:26,experiment1:0xF

2025-07-31 21:24:38:742 ==>> F
[D][05:19:30][COMM]index:27,experiment2:0xFF
[D][05:19:30][COMM]index:28,experiment3:0xFF
[D][05:19:30][COMM]index:29,experiment4:0xFF
[D][05:19:30][COMM]index:30,night_mode_start:0xFF
[D][05:19:30][COMM]index:31,night_mode_end:0xFF
[D][05:19:30][COMM]index:33,park_report_minutes:0xFF
[D][05:19:30][COMM]index:34,park_report_mode:0xFF
[D][05:19:30][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:30][COMM]index:38,charge_battery_para: FF
[D][05:19:30][COMM]index:39,multirider_mode:0xFF
[D][05:19:30][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:30][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:30][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:30][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:30][COMM]index:44,riding_duration_config:0xFF
[D][05:19:30][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:30][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:30][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:30][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:30][COMM]index:49,mc_load_startup:0xFF
[D][05:19:30][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:30][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:30][COMM]index:52,traffic_mode:0xFF
[D][05:1

2025-07-31 21:24:38:847 ==>> 9:30][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:30][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:30][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:30][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:30][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:30][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:30][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:30][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:30][COMM]index:63,experiment5:0xFF
[D][05:19:30][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:30][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:30][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:30][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:30][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:30][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:30][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:30][COMM]index:72,experiment6:0xFF
[D][05:19:30][COMM]index:73,experiment7:0xFF
[D][05:19:30][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:30][COMM]index:75,zero_value_from_server:-1
[D][05:19:30][COMM]index:76,multirider_threshold:255
[D][05:19:30][COMM]index:77,experimen

2025-07-31 21:24:38:952 ==>> t8:255
[D][05:19:30][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:30][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:30][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:30][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:30][COMM]index:83,loc_report_interval:255
[D][05:19:30][COMM]index:84,multirider_threshold_p2:255
[D][05:19:30][COMM]index:85,multirider_strategy:255
[D][05:19:30][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:30][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:30][COMM]index:90,weight_param:0xFF
[D][05:19:30][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:30][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:30][COMM]index:95,current_limit:0xFF
[D][05:19:30][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:30][COMM]index:100,location_mode:0xFF

[W][05:19:30][PROT]remove success[1629955170],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:30][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:30][PROT]add success [1629955170],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:30][COMM]Main Task receiv

2025-07-31 21:24:39:057 ==>> e event:131 finished processing
[D][05:19:30][PROT]index:0 1629955170
[D][05:19:30][PROT]is_send:0
[D][05:19:30][PROT]sequence_num:7
[D][05:19:30][PROT]retry_timeout:0
[D][05:19:30][PROT]retry_times:1
[D][05:19:30][PROT]send_path:0x2
[D][05:19:30][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:30][PROT]===========================================================
[W][05:19:30][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955170]
[D][05:19:30][PROT]===========================================================
[D][05:19:30][PROT]sending traceid [9999999999900008]
[D][05:19:30][PROT]Send_TO_M2M [1629955170]
[D][05:19:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:30][SAL ]sock send credit cnt[6]
[D][05:19:30][SAL ]sock send ind credit cnt[6]
[D][05:19:30][M2M ]m2m send data len[294]
[D][05:19:30][SAL ]Cellular task submsg id[10]
[D][05:19:30][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:30][CAT1]gsm read msg sub id: 15
[D][05:19:30][CAT1]tx ret[17] >>> A

2025-07-31 21:24:39:162 ==>> T+QISEND=0,294

[D][05:19:30][CAT1]Send Data To Server[294][294] ... ->:
0093B989113311331133113311331B88B22246F234F532C93ECDA795D11ED147CA1AC1F4B91091ED850B8C8599C7AE2016779F40D0937688A25C493280ECEA31C2A1C7BF5649A24EFE6EFA22EC08C020C7AAF543FE9B488E44554E94D9BC721F5585E4EFA2BC1350F95B09B33F38E75C90DF95A58DBB087B965430090D9B4C20A6F0C4E4BBC9C6D3E0625222325F2B0EE68380
[D][05:19:30][CAT1]<<< 
SEND OK

[D][05:19:30][CAT1]exec over: func id: 15, ret: 11
[D][05:19:30][CAT1]sub id: 15, ret: 11

[D][05:19:30][SAL ]Cellular task submsg id[68]
[D][05:19:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:30][M2M ]g_m2m_is_idle become true
[D][05:19:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:30][PROT]M2M Send ok [1629955170]
$GBGGA,132438.015,2301.2580738,N,11421.9414688,E,1,11,0.89,76.152,M,-1.770,M,,*55

$GBGSA,A,3,33,39,07,40,25,14,24,41,13,34,44,,2.37,0.89,2.19,4*08



2025-07-31 21:24:39:342 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 21:24:39:350 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【40】
北斗星号:【16】,信号值:【37】
北斗星号:【6】,信号值:【35】
北斗星号:【39】,信号值:【39】
北斗星号:【59】,信号值:【40】
北斗星号:【7】,信号值:【37】
北斗星号:【40】,信号值:【39】
北斗星号:【25】,信号值:【40】
北斗星号:【1】,信号值:【36】
北斗星号:【2】,信号值:【35】
北斗星号:【14】,信号值:【38】
北斗星号:【60】,信号值:【39】
北斗星号:【9】,信号值:【35】
北斗星号:【10】,信号值:【35】
北斗星号:【24】,信号值:【39】
北斗星号:【41】,信号值:【39】
北斗星号:【34】,信号值:【36】
北斗星号:【44】,信号值:【35】
北斗星号:【42】,信号值:【35】
北斗星号:【23】,信号值:【35】

2025-07-31 21:24:39:357 ==>> 检测【CSQ强度】
2025-07-31 21:24:39:370 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:24:39:396 ==>>                                                                                                                                                                                                                                                                                           1,226,42,6,56,49,35,39,53,18,39,1*77

$GBGSV,7,2,28,16,52,357,37,59,50,129,40,7,48,181,37,2,47,238,35,1*72

$GBGSV,7,3,28,40,47,162,38,25,47,310,40,1,46,125,36,14,45,186,38,1*43

$GBGSV,7,4,28,60,42,240,40,9,40,325,35,10,39,204,34,24,39,33,40,1*70

$GBGSV,7,5,28,41,37,306,39,4,32,112,32,13,29,217,34,34,26,138,36,1*40

$GBGSV,7,6,28,5,22,256,33,44,21,80,34,38,14,198,34,8,12,201,32,1*45

$GBGSV,7,7,28,42,12,322,35,23,,,35,12,,,34,11,,,31,1*4D

$GBGSV,2,1,08,33,61,226,42,39,53,18,40,40,47,162,39,25,47,310,40,5*45

$GBGSV,2,2,08,24,39,33,40,41,37,306,40,34,26,138,36,44,21,80,35,5*73

$GBRMC,132439.000,A,2301.2582199,N,11421.9415076,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,132439.000,1.371,0.168,0.165,0.265,1.210,1.427,4.744*72



2025-07-31 21:24:39:477 ==>> [W][05:19:31][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:31][CAT1]gsm read msg sub id: 12
[D][

2025-07-31 21:24:39:507 ==>> 05:19:31][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:31][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:31][CAT1]exec over: func id: 12, ret: 21


2025-07-31 21:24:39:837 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 21:24:39:845 ==>> 检测【关闭GSM联网】
2025-07-31 21:24:39:853 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 21:24:40:061 ==>> [W][05:19:32][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:32][COMM]GSM test
[D][05:19:32][COMM]GSM test disable


2025-07-31 21:24:40:157 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 21:24:40:171 ==>> 检测【4G联网测试】
2025-07-31 21:24:40:189 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 21:24:40:411 ==>> $GBGGA,132440.000,2301.2582586,N,11421.9415028,E,1,23,0.64,76.535,M,-1.770,M,,*51

$GBGSA,A,3,03,33,39,06,16,59,07,02,09,40,25,01,1.53,0.64,1.39,4*0C

$GBGSA,A,3,14,60,10,24,41,13,34,08,42,44,38,,1.53,0.64,1.39,4*07

$GBGSV,7,1,28,3,62,190,40,33,61,226,42,39,53,18,39,6,53,353,35,1*4A

$GBGSV,7,2,28,16,52,357,37,59,50,129,40,7,48,181,37,2,47,238,35,1*72

$GBGSV,7,3,28,9,47,331,35,40,47,162,38,25,47,310,40,1,46,125,36,1*7E

$GBGSV,7,4,28,14,45,186,37,60,42,240,40,10,39,192,34,24,39,33,40,1*4C

$GBGSV,7,5,28,41,37,306,39,4,32,112,32,13,29,217,34,34,26,138,36,1*40

$GBGSV,7,6,28,8,25,206,31,42,22,167,35,5,22,256,32,44,21,80,34,1*4D

$GBGSV,7,7,28,38,18,192,34,23,12,268,35,12,,,34,11,,,31,1*7D

$GBGSV,2,1,08,33,61,226,43,39,53,18,41,40,47,162,39,25,47,310,40,5*45

$GBGSV,2,2,08,24,39,33,40,41,37,306,40,34,26,138,36,44,21,80,35,5*73

$GBRMC,132440.000,A,2301.2582586,N,11421.9415028,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,132440.000,1.997,0.230,0.218,0.345,1.596,1.742,4.400*70

[W][05:19:32][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 21:24:41:213 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     _period_unlock:0xFF
[D][05:19:32][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:32][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:32][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:32][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:32][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:32][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:32][COMM]index:16,imu_config_params:0xFF
[D][05:19:32][COMM]index:17,long_connect_params:0xFF
[D][05:19:32][COMM]index:18,detain_mark:0xFF
[D][05:19:32][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:32][CO

2025-07-31 21:24:41:318 ==>> MM]index:20,lock_pos_report_interval:0xFF
[D][05:19:32][COMM]index:21,mc_mode:0xFF
[D][05:19:32][COMM]index:22,S_mode:0xFF
[D][05:19:32][COMM]index:23,overweight:0xFF
[D][05:19:32][COMM]index:24,standstill_mode:0xFF
[D][05:19:32][COMM]index:25,night_mode:0xFF
[D][05:19:32][COMM]index:26,experiment1:0xFF
[D][05:19:32][COMM]index:27,experiment2:0xFF
[D][05:19:32][COMM]index:28,experiment3:0xFF
[D][05:19:32][COMM]index:29,experiment4:0xFF
[D][05:19:32][COMM]index:30,night_mode_start:0xFF
[D][05:19:32][COMM]index:31,night_mode_end:0xFF
[D][05:19:32][COMM]index:33,park_report_minutes:0xFF
[D][05:19:32][COMM]index:34,park_report_mode:0xFF
[D][05:19:32][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:32][COMM]index:38,charge_battery_para: FF
[D][05:19:32][COMM]index:39,multirider_mode:0xFF
[D][05:19:32][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:32][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:32][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:32][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:32][COMM]index:44,riding_duration_config:0xFF
[D][05:19:32][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:32][COMM]index:46,camera_park_type_cfg

2025-07-31 21:24:41:423 ==>> :0xFF
[D][05:19:32][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:32][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:32][COMM]index:49,mc_load_startup:0xFF
[D][05:19:32][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:32][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:32][COMM]index:52,traffic_mode:0xFF
[D][05:19:32][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:32][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:32][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:32][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:32][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:32][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:32][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:32][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:32][COMM]index:63,experiment5:0xFF
[D][05:19:32][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:32][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:32][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:32][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:32][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:32][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:32][COMM]index:71,camera_park_sel

2025-07-31 21:24:41:528 ==>> f_check_cfg:0xFF
[D][05:19:32][COMM]index:72,experiment6:0xFF
[D][05:19:32][COMM]index:73,experiment7:0xFF
[D][05:19:32][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:32][COMM]index:75,zero_value_from_server:-1
[D][05:19:32][COMM]index:76,multirider_threshold:255
[D][05:19:32][COMM]index:77,experiment8:255
[D][05:19:32][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:32][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:32][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:32][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:32][COMM]index:83,loc_report_interval:255
[D][05:19:32][COMM]index:84,multirider_threshold_p2:255
[D][05:19:32][COMM]index:85,multirider_strategy:255
[D][05:19:32][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:32][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:32][COMM]index:90,weight_param:0xFF
[D][05:19:32][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:32][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:32][COMM]index:95,current_limit:0xFF
[D][05:19:32][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:32][COMM]index:100,location_mode:0xFF

[

2025-07-31 21:24:41:633 ==>> W][05:19:32][PROT]remove success[1629955172],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:32][PROT]add success [1629955172],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:32][PROT]index:0 1629955172
[D][05:19:32][PROT]is_send:0
[D][05:19:32][PROT]sequence_num:8
[D][05:19:32][PROT]retry_timeout:0
[D][05:19:32][PROT]retry_times:1
[D][05:19:32][PROT]send_path:0x2
[D][05:19:32][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:32][PROT]===========================================================
[W][05:19:32][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955172]
[D][05:19:32][PROT]===========================================================
[D][05:19:32][PROT]sending traceid [9999999999900009]
[D][05:19:32][PROT]Send_TO_M2M [1629955172]
[D][05:19:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:32][SAL ]sock send credit cnt[6]
[D][05:19:32][SAL ]sock send ind credit cnt[6]
[D][05:19:32][M2M ]m2m send data len[294]
[D][05:19:32][CAT1]gsm read msg sub id: 13
[D][05:19:32][SAL ]Cellular task submsg id[10]
[D][05:19:32][SAL ]cel

2025-07-31 21:24:41:738 ==>> lular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:32][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:32][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:32][CAT1]exec over: func id: 13, ret: 21
[D][05:19:32][M2M ]get csq[21]
[D][05:19:32][CAT1]gsm read msg sub id: 15
[D][05:19:32][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:32][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88B12A0C67417367CE5E7FAF4CEA8669173DC62B00189B41C5D85C4086B1EA6608F0E13C40C18E1EF51AF523300CA0F7D1A6989857E9CF6E3F0C89A2C55E10D0C4C35886E01D8229265EEA2D5A44E7A0BBFDA01F62D234C95DD449AE1E8A0D9C61986DB00DD6D388CBDDEB1B99D0A6BBD12D110D3B9DE65B9A90DE2630B41ECE235B9D33
[D][05:19:32][CAT1]<<< 
SEND OK

[D][05:19:32][CAT1]exec over: func id: 15, ret: 11
[D][05:19:32][CAT1]sub id: 15, ret: 11

[D][05:19:32][SAL ]Cellular task submsg id[68]
[D][05:19:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:32][M2M ]g_m2m_is_idle become true
[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:32][PROT]M2M Send ok [1629955172]
[D][05:19:32][

2025-07-31 21:24:41:844 ==>> COMM]read battery soc:255
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:33][PROT]remove success[1629955173],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:19:33][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[D][05:19:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:33][PROT]add success [1629955173],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:33][COMM]------>period, report file manifest
[D][05:19:33][COMM]Main Task receive event:14 finished processing
[D][05:19:33][CAT1]gsm read msg sub id: 21
[D][05:19:33][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]cell info report total[0]
[D][05:19:33][CAT1]exec over: func id: 21, ret: 6
                                                                                                                                                                                

2025-07-31 21:24:41:949 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 21:24:42:232 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 21:24:42:241 ==>> 检测【关闭GPS】
2025-07-31 21:24:42:260 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:24:42:390 ==>> $GBGGA,132442.000,2301.2582471,N,11421.9414263,E,1,25,0.57,75.871,M,-1.770,M,,*5E

$GBGSA,A,3,03,33,39,06,16,59,07,02,09,40,25,01,1.26,0.57,1.13,4*06

$GBGSA,A,3,14,60,10,24,41,13,34,08,12,42,44,38,1.26,0.57,1.13,4*0E

$GBGSA,A,3,23,,,,,,,,,,,,1.26,0.57,1.13,4*0D

$GBGSV,7,1,28,3,62,190,40,33,61,226,42,39,53,18,39,6,53,353,35,1*4A

$GBGSV,7,2,28,16,52,357,37,59,50,129,40,7,48,181,37,2,47,238,35,1*72

$GBGSV,7,3,28,9,47,331,35,40,47,162,38,25,47,310,40,1,46,125,37,1*7F

$GBGSV,7,4,28,14,45,186,38,60,42,240,39,10,39,192,34,24,39,33,40,1*4D

$GBGSV,7,5,28,41,37,306,39,4,32,112,33,13,28,217,34,34,26,138,36,1*40

$GBGSV,7,6,28,8,25,206,32,12,24,107,34,42,22,167,35,5,22,256,32,1*76

$GBGSV,7,7,28,44,21,80,34,38,18,192,34,23,12,268,35,11,,,31,1*75

$GBGSV,3,1,11,33,61,226,43,39,53,18,41,40,47,162,39,25,47,310,41,5*4D

$GBGSV,3,2,11,24,39,33,40,41,37,306,41,34,26,138,36,42,22,167,32,5*41

$GBGSV,3,3,11,44,21,80,35,38,18,192,32,23,12,268,33,5*78

$GBRMC,132442.000,A,2301.2582471,N,11421.9414263,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,132442.000,2.721,0.159,0.158,0.233,1.996,2.082,3.991*7F



2025-07-31 21:24:42:690 ==>> [W][05:19:34][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:34][GNSS]stop locating
[D][05:19:34][GNSS]stop event:8
[D][05:19:34][GNSS]GPS stop. ret=0
[D][05:19:34][GNSS]all continue location stop
[W][05:19:34][GNSS]stop locating
[D][05:19:34][GNSS]all sing location stop
[D][05:19:34][CAT1]gsm read msg sub id: 24
[D][05:19:34][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:34][CAT1]<<< 
OK

[D][05:19:34][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:34][CAT1]<<< 
OK

[D][05:19:34][COMM]read battery soc:255
[D][05:19:35][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 24, ret: 6
[D][05:19:35][CAT1]sub id: 24, ret: 6



2025-07-31 21:24:42:765 ==>>                                                             _SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:35][GNSS]location stop evt done evt


2025-07-31 21:24:42:935 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 21:24:42:943 ==>> 检测【清空消息队列2】
2025-07-31 21:24:42:987 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:24:43:168 ==>> [W][05:19:35][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:35][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:24:43:258 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:24:43:271 ==>> 检测【轮动检测】
2025-07-31 21:24:43:298 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 21:24:43:379 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 21:24:43:439 ==>> [D][05:19:35][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 21:24:43:772 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 21:24:43:878 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 21:24:44:067 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 21:24:44:079 ==>> 检测【关闭小电池】
2025-07-31 21:24:44:092 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:24:44:169 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:24:44:347 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 21:24:44:356 ==>> 检测【进入休眠模式】
2025-07-31 21:24:44:369 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:24:44:634 ==>> [W][05:19:36][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:36][COMM]Main Task receive event:28
[D][05:19:36][COMM]main task tmp_sleep_event = 8
[D][05:19:36][COMM]prepare to sleep
[D][05:19:36][CAT1]gsm read msg sub id: 12
[D][05:19:36][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:36][COMM]read battery soc:255


2025-07-31 21:24:45:461 ==>> [D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]exec over: func id: 12, ret: 6
[D][05:19:37][M2M ]tcpclient close[4]
[D][05:19:37][SAL ]Cellular task submsg id[12]
[D][05:19:37][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:37][CAT1]gsm read msg sub id: 9
[D][05:19:37][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]exec over: func id: 9, ret: 6
[D][05:19:37][CAT1]sub id: 9, ret: 6

[D][05:19:37][SAL ]Cellular task submsg id[68]
[D][05:19:37][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:37][SAL ]socket close ind. id[4]
[D][05:19:37][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:37][COMM]1x1 frm_can_tp_send ok
[D][05:19:37][CAT1]pdpdeact urc len[22]


2025-07-31 21:24:45:765 ==>> [E][05:19:38][COMM]1x1 rx timeout
[D][05:19:38][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:24:46:273 ==>> [E][05:19:38][COMM]1x1 rx timeout
[E][05:19:38][COMM]1x1 tp timeout
[E][05:19:38][COMM]1x1 error -3.
[W][05:19:38][COMM]CAN STOP!
[D][05:19:38][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:38][COMM]------------ready to Power off Acckey 1------------
[D][05:19:38][COMM]------------ready to Power off Acckey 2------------
[D][05:19:38][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:38][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1304
[D][05:19:38][COMM]bat sleep fail, reason:-1
[D][05:19:38][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:38][COMM]accel parse set 0
[D][05:19:38][COMM]imu rest ok. 109556
[D][05:19:38][COMM]imu sleep 0
[W][05:19:38][COMM]now sleep


2025-07-31 21:24:46:457 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:24:46:466 ==>> 检测【检测33V休眠电流】
2025-07-31 21:24:46:488 ==>> 开始33V电流采样
2025-07-31 21:24:46:501 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:24:46:558 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 21:24:47:560 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 21:24:47:636 ==>> Current33V:????:15.60

2025-07-31 21:24:48:074 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:24:48:083 ==>> 【检测33V休眠电流】通过,【15.6uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:24:48:091 ==>> 该项需要延时执行
2025-07-31 21:24:50:093 ==>> 此处延时了:【2000】毫秒
2025-07-31 21:24:50:106 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 21:24:50:118 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:24:50:185 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1653mV
Get AD_V4 0mV
Get AD_V5 2741mV
Get AD_V6 2021mV
Get AD_V7 1086mV
OVER 150


2025-07-31 21:24:51:142 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:24:51:157 ==>> 检测【打开小电池2】
2025-07-31 21:24:51:177 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:24:51:271 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:24:51:481 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:24:51:491 ==>> 该项需要延时执行
2025-07-31 21:24:51:987 ==>> 此处延时了:【500】毫秒
2025-07-31 21:24:52:002 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 21:24:52:023 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:24:52:077 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:24:52:380 ==>> [D][05:19:44][COMM]------------ready to Power on Acckey 1------------
[D][05:19:44][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:44][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 0,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 1,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 2,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 3,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 4,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 5,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 6,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 7,volt = 9
[D][05:19:44][FCTY]get_ext_48v_vol retry i = 8,volt = 9
[D][05:19:44][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 9
[D][05:19:44][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[W][05:19:44][COMM]CAN START!
[D][05:19:44][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:44][COMM]1x1 frm_can_tp_send ok
[D][05:19:44][CAT1]gsm read msg sub id: 12
[D][05:19:44][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:44][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 115593
[D][05:19:44][COMM][Audio]exec status ready.
[D][05:19:44][CAT1]<<<

2025-07-31 21:24:52:425 ==>>  
OK

[D][05:19:44][CAT1]exec over: func id: 12, ret: 6
[D][05:19:44][COMM]imu wakeup ok. 115608
[D][05:19:44][COMM]imu wakeup 1
[W][05:19:44][COMM]wake up system, wakeupEvt=0x80
[D][05:19:44][COMM]frm_can_weigth_power_set 1
[D][05:19:44][COMM]Clear Sleep Block Evt
[D][05:19:44][COMM]Main Task receive event:28 finished processing


2025-07-31 21:24:52:576 ==>> [E][05:19:44][COMM]1x1 rx timeout
[D][05:19:44][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:24:52:787 ==>> [D][05:19:45][COMM]msg 02A0 loss. last_tick:115578. cur_tick:116087. period:50
[D][05:19:45][COMM]msg 02A4 loss. last_tick:115578. cur_tick:116088. period:50
[D][05:19:45][COMM]msg 02A5 loss. last_tick:115578. cur_tick:116088. period:50
[D][05:19:45][COMM]msg 02A6 loss. last_tick:115578. cur_tick:116088. period:50
[D][05:19:45][COMM]msg 02A7 loss. last_tick:115578. cur_tick:116089. period:50
[D][05:19:45][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 116089
[D][05:19:45][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 116090


2025-07-31 21:24:52:997 ==>> [E][05:19:45][COMM]1x1 rx timeout
[E][05:19:45][COMM]1x1 tp timeout
[E][05:19:45][COMM]1x1 error -3.


2025-07-31 21:24:53:422 ==>> [D][05:19:45][COMM]msg 0220 loss. last_tick:115578. cur_tick:116584. period:100
[D][05:19:45][COMM]msg 0221 loss. last_tick:115578. cur_tick:116584. period:100
[D][05:19:45][COMM]msg 0224 loss. last_tick:115578. cur_tick:116584. period:100
[D][05:19:45][COMM]msg 0260 loss. last_tick:115578. cur_tick:116585. period:100
[D][05:19:45][COMM]msg 0280 loss. last_tick:115578. cur_tick:116585. period:100
[D][05:19:45][COMM]msg 02C0 loss. last_tick:115578. cur_tick:116585. period:100
[D][05:19:45][COMM]msg 02C1 loss. last_tick:115578. cur_tick:116586. period:100
[D][05:19:45][COMM]msg 02C2 loss. last_tick:115578. cur_tick:116586. period:100
[D][05:19:45][COMM]msg 02E0 loss. last_tick:115578. cur_tick:116587. period:100
[D][05:19:45][COMM]msg 02E1 loss. last_tick:115578. cur_tick:116587. period:100
[D][05:19:45][COMM]msg 02E2 loss. last_tick:115578. cur_tick:116588. period:100
[D][05:19:45][COMM]msg 0300 loss. last_tick:115578. cur_tick:116588. period:100
[D][05:19:45][COMM]msg 0301 loss. last_tick:115578. cur_tick:116588. period:100
[D][05:19:45][COMM]bat msg 0240 loss. last_tick:115578. cur_tick:116589. period:100. j,i:1 54
[D][05:19:45][COMM]bat msg 0241 loss. last_tick:115578. cur_tick:116589. period:100. j,i:2 55
[D][05:19:45][COMM]bat msg 0242 loss. last_tick:115578. cur_tic

2025-07-31 21:24:53:497 ==>> k:116589. period:100. j,i:3 56
[D][05:19:45][COMM]bat msg 0244 loss. last_tick:115578. cur_tick:116590. period:100. j,i:5 58
[D][05:19:45][COMM]bat msg 024E loss. last_tick:115579. cur_tick:116590. period:100. j,i:15 68
[D][05:19:45][COMM]bat msg 024F loss. last_tick:115579. cur_tick:116590. period:100. j,i:16 69
[D][05:19:45][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 116591
[D][05:19:45][COMM]CAN message bat fault change: 0x00000000->0x0001802E 116591
[D][05:19:45][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 116592


2025-07-31 21:24:53:707 ==>> [D][05:19:46][COMM]msg 0222 loss. last_tick:115578. cur_tick:117086. period:150
[D][05:19:46][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 117087


2025-07-31 21:24:54:120 ==>>                                                                                                                          ype 16.... 
[D][05:19:46][COMM]------------ready to Power off Acckey 2------------
[D][05:19:46][COMM]Main Task receive event:65
[D][05:19:46][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:46][COMM]Main Task receive event:65 finished processing
[D][05:19:46][COMM]Main Task receive event:60
[D][05:19:46][COMM]smart_helmet_vol=255,255
[D][05:19:46][COMM]BAT CAN get state1 Fail 204
[D][05:19:46][COMM]BAT CAN get soc Fail, 204
[D][05:19:46][COMM]report elecbike
[D][05:19:46][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:19:46][PROT]remove success[1629955186],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:46][PROT]add success [1629955186],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:46][COMM]Main Task receive event:60 finished processing
[D][05:19:46][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:46][PROT]index:0
[D][05:19:46][PROT]is_send:1
[D][05:19:46][PROT]sequence_num:10
[D][05:19:46][PROT]retry_timeout:0
[D][05:19:46][PROT]retry_times:3
[D][05:19:46][PROT]send_path:0x3
[D][05:19:46][PROT]msg_type:0x5d03
[D][05:19:46][PROT]==============

2025-07-31 21:24:54:225 ==>> =============================================
[W][05:19:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955186]
[D][05:19:46][PROT]===========================================================
[D][05:19:46][PROT]Sending traceid[999999999990000B]
[D][05:19:46][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:46][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:46][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:46][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:46][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:46][SAL ]open socket ind id[4], rst[0]
[D][05:19:46][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:46][SAL ]Cellular task submsg id[8]
[D][05:19:46][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:46][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:46][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:46][CAT1]gsm read msg sub id: 8
[D][05:19:46][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:46][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:46][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:46][CAT1]TES

2025-07-31 21:24:54:255 ==>> T for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:46][CAT1]<<< 
+CME ERROR: 100



2025-07-31 21:24:54:711 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:24:54:721 ==>> 该项需要延时执行
2025-07-31 21:24:55:226 ==>> 此处延时了:【500】毫秒
2025-07-31 21:24:55:239 ==>> 检测【进入休眠模式2】
2025-07-31 21:24:55:263 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:24:55:456 ==>> [W][05:19:47][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:24:55:531 ==>> [D][05:19:47][COMM]Main Task receive event:28
[D][05:19:47][COMM]prepare to sleep


2025-07-31 21:24:57:373 ==>> [D][05:19:49][COMM]msg 0601 loss. last_tick:115578. cur_tick:120587. period:500
[D][05:19:49][COMM]msg 02AC loss. last_tick:115578. cur_tick:120587. period:500
[D][05:19:49][COMM]bat msg 0243 loss. last_tick:115578. cur_tick:120588. period:500. j,i:4 57
[D][05:19:49][COMM]bat msg 0245 loss. last_tick:115578. cur_tick:120588. period:500. j,i:6 59
[D][05:19:49][COMM]bat msg 0246 loss. last_tick:115578. cur_tick:120588. period:500. j,i:7 60
[D][05:19:49][COMM]bat msg 0247 loss. last_tick:115578. cur_tick:120589. period:500. j,i:8 61
[D][05:19:49][COMM]bat msg 0248 loss. last_tick:115578. cur_tick:120589. period:500. j,i:9 62
[D][05:19:49][COMM]bat msg 0249 loss. last_tick:115578. cur_tick:120590. period:500. j,i:10 63
[D][05:19:49][COMM]bat msg 0252 loss. last_tick:115579. cur_tick:120590. period:500. j,i:19 72
[D][05:19:49][COMM]bat msg 0257 loss. last_tick:115579. cur_tick:120590. period:500. j,i:20 73
[D][05:19:49][COMM]bat msg 0258 loss. last_tick:115579. cur_tick:120591. period:500. j,i:21 74
[D][05:19:49][COMM]bat msg 025B loss. last_tick:115579. cur_tick:120591. period:500. j,i:23 76
[D][05:19:49][COMM]bat msg 025C loss. last_tick:115579. cur_tick:120592. period:

2025-07-31 21:24:57:418 ==>> 500. j,i:24 77
[D][05:19:49][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 120592
[D][05:19:49][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 120592


2025-07-31 21:24:57:601 ==>> [D][05:19:49][M2M ]tcpclient close[4]
[D][05:19:49][SAL ]Cellular task submsg id[12]
[D][05:19:49][SAL ]cellular CLOSE socket size[4], msg->data[0x20052dd0], socket[0]


2025-07-31 21:24:58:620 ==>> [D][05:19:51][COMM]exit wheel stolen mode.


2025-07-31 21:24:59:684 ==>> [D][05:19:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc


2025-07-31 21:25:00:443 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:25:00:458 ==>> [D][05:19:52][COMM]set_ext_bat_state EXT_BAT_STATE_POWERON_TIMEOUT, Ext48v = 1.


2025-07-31 21:25:00:639 ==>> [W][05:19:53][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:25:01:237 ==>> [D][05:19:53][GNSS]handler GSMGet Base timeout


2025-07-31 21:25:01:945 ==>> [D][05:19:54][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:54][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:54][CAT1]<<< 
+CME ERROR: 100



2025-07-31 21:25:02:246 ==>> [D][05:19:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc
[D][05:19:54][COMM]msg 0223 loss. last_tick:115578. cur_tick:125597. period:1000
[D][05:19:54][COMM]msg 0225 loss. last_tick:115578. cur_tick:125598. period:1000
[D][05:19:54][COMM]msg 0229 loss. last_tick:115578. cur_tick:125598. period:1000
[D][05:19:54][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 125599
[D][05:19:54][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 125599


2025-07-31 21:25:04:682 ==>> [D][05:19:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc


2025-07-31 21:25:05:655 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:25:05:841 ==>> [W][05:19:58][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:25:07:175 ==>> [D][05:19:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc


2025-07-31 21:25:09:676 ==>> [D][05:20:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc


2025-07-31 21:25:09:965 ==>> [D][05:20:02][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:20:02][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:20:02][CAT1]<<< 
+CME ERROR: 100



2025-07-31 21:25:10:828 ==>> 未匹配到【进入休眠模式2】数据,请核对检查!
2025-07-31 21:25:10:843 ==>> #################### 【测试结束】 ####################
2025-07-31 21:25:10:891 ==>> 关闭5V供电
2025-07-31 21:25:10:907 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:25:10:970 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:25:11:898 ==>> 关闭5V供电成功
2025-07-31 21:25:11:912 ==>> 关闭33V供电
2025-07-31 21:25:11:926 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:25:11:973 ==>> 5A A5 02 5A A5 


2025-07-31 21:25:12:078 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:25:12:183 ==>> [D][05:20:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c6bc


2025-07-31 21:25:12:911 ==>> 关闭33V供电成功
2025-07-31 21:25:12:926 ==>> 关闭3.7V供电
2025-07-31 21:25:12:941 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:25:12:973 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:25:13:309 ==>>  

2025-07-31 21:25:13:926 ==>> 关闭3.7V供电成功
