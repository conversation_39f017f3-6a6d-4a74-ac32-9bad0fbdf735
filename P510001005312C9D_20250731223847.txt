2025-07-31 22:38:47:258 ==>> MES查站成功:
查站序号:P510001005312C9D验证通过
2025-07-31 22:38:47:276 ==>> 扫码结果:P510001005312C9D
2025-07-31 22:38:47:278 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:38:47:280 ==>> 测试参数版本:2024.10.11
2025-07-31 22:38:47:281 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:38:47:283 ==>> 检测【打开透传】
2025-07-31 22:38:47:285 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:38:47:409 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:38:47:564 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:38:47:569 ==>> 检测【检测接地电压】
2025-07-31 22:38:47:571 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:38:47:712 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:38:47:866 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:38:47:868 ==>> 检测【打开小电池】
2025-07-31 22:38:47:871 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:38:48:003 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:38:48:148 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:38:48:150 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:38:48:152 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:38:48:201 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:38:48:419 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:38:48:421 ==>> 检测【等待设备启动】
2025-07-31 22:38:48:423 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:48:799 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:48:981 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti?

2025-07-31 22:38:49:455 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:49:500 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:49:665 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:38:50:188 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:50:353 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:38:50:489 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:50:856 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:51:053 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:38:51:527 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:51:542 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:51:722 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 22:38:52:214 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:52:409 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 22:38:52:560 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:52:908 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:53:090 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 22:38:53:592 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:53:607 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:53:772 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC] 

2025-07-31 22:38:54:284 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:54:451 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC] 

2025-07-31 22:38:54:616 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:54:964 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:55:130 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC] 

2025-07-31 22:38:55:634 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:55:649 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:55:829 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC] 

2025-07-31 22:38:56:400 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:38:56:582 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC] 

2025-07-31 22:38:56:687 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:57:734 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:38:58:761 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:38:58:765 ==>> #################### 【测试结束】 ####################
2025-07-31 22:38:58:783 ==>> 关闭5V供电
2025-07-31 22:38:58:787 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:38:58:911 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:38:59:790 ==>> 关闭5V供电成功
2025-07-31 22:38:59:793 ==>> 关闭33V供电
2025-07-31 22:38:59:796 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:38:59:912 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:39:00:802 ==>> 关闭33V供电成功
2025-07-31 22:39:00:805 ==>> 关闭3.7V供电
2025-07-31 22:39:00:808 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:39:00:909 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:39:01:029 ==>>  

