2025-07-31 22:27:10:420 ==>> MES查站成功:
查站序号:P5100010053126BB验证通过
2025-07-31 22:27:10:435 ==>> 扫码结果:P5100010053126BB
2025-07-31 22:27:10:437 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:27:10:438 ==>> 测试参数版本:2024.10.11
2025-07-31 22:27:10:440 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:27:10:442 ==>> 检测【打开透传】
2025-07-31 22:27:10:444 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:27:10:495 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:27:10:856 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:27:10:860 ==>> 检测【检测接地电压】
2025-07-31 22:27:10:862 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:27:10:999 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:27:11:178 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:27:11:180 ==>> 检测【打开小电池】
2025-07-31 22:27:11:183 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:27:11:301 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:27:11:483 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:27:11:485 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:27:11:487 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:27:11:603 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:27:11:771 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:27:11:773 ==>> 检测【等待设备启动】
2025-07-31 22:27:11:776 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:27:12:063 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:27:12:243 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:27:12:817 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:27:12:832 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:27:13:027 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:27:13:718 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:27:13:853 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:27:14:101 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:27:14:571 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:27:14:649 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:27:14:652 ==>> 检测【产品通信】
2025-07-31 22:27:14:654 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:27:14:782 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:27:14:925 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:27:14:927 ==>> 检测【初始化完成检测】
2025-07-31 22:27:14:930 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:27:15:295 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:27:15:466 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:27:15:468 ==>> 检测【关闭大灯控制1】
2025-07-31 22:27:15:469 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:27:15:493 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:27:16:136 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<


2025-07-31 22:27:16:182 ==>>                                         

2025-07-31 22:27:16:499 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:27:16:576 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:27:16:682 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:27:16:775 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:27:16:778 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:27:16:779 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:27:17:060 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:27:17:811 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:27:17:962 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 22:27:18:222 ==>> [W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 22:27:18:849 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:27:18:984 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 22:27:19:885 ==>> 未匹配到【打开仪表指令模式1】数据,请核对检查!
2025-07-31 22:27:19:891 ==>> #################### 【测试结束】 ####################
2025-07-31 22:27:19:911 ==>> 关闭5V供电
2025-07-31 22:27:19:913 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:27:20:004 ==>> 5A A5 04 5A A5 


2025-07-31 22:27:20:094 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:27:20:912 ==>> 关闭5V供电成功
2025-07-31 22:27:20:915 ==>> 关闭33V供电
2025-07-31 22:27:20:918 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:27:21:002 ==>> 5A A5 02 5A A5 


2025-07-31 22:27:21:107 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:27:21:917 ==>> 关闭33V供电成功
2025-07-31 22:27:21:919 ==>> 关闭3.7V供电
2025-07-31 22:27:21:922 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:27:22:007 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:27:22:339 ==>>  

