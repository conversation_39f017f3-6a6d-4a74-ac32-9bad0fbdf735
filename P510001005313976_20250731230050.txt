2025-07-31 23:00:50:675 ==>> MES查站成功:
查站序号:P510001005313976验证通过
2025-07-31 23:00:50:679 ==>> 扫码结果:P510001005313976
2025-07-31 23:00:50:680 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:00:50:682 ==>> 测试参数版本:2024.10.11
2025-07-31 23:00:50:684 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:00:50:685 ==>> 检测【打开透传】
2025-07-31 23:00:50:687 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:00:50:809 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:00:51:044 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:00:51:048 ==>> 检测【检测接地电压】
2025-07-31 23:00:51:050 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:00:51:119 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:00:51:318 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:00:51:320 ==>> 检测【打开小电池】
2025-07-31 23:00:51:323 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:00:51:419 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:00:51:587 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:00:51:590 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:00:51:592 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:00:51:719 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:00:51:859 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:00:51:861 ==>> 检测【等待设备启动】
2025-07-31 23:00:51:864 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:00:52:178 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:00:52:360 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:00:52:893 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:00:53:048 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:00:53:432 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:00:53:915 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:00:53:950 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:00:53:952 ==>> 检测【产品通信】
2025-07-31 23:00:53:954 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:00:54:080 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:00:54:220 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:00:54:222 ==>> 检测【初始化完成检测】
2025-07-31 23:00:54:224 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:00:54:448 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:00:54:497 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:00:54:500 ==>> 检测【关闭大灯控制1】
2025-07-31 23:00:54:502 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:00:54:600 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:00:54:674 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:00:54:767 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:00:54:770 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:00:54:771 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:00:55:167 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:00:55:307 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:00:55:309 ==>> 检测【关闭仪表供电】
2025-07-31 23:00:55:310 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:00:55:503 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:00:55:584 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:00:55:585 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:00:55:587 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:00:55:790 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:00:55:858 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:00:55:860 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:00:55:862 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:00:56:019 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3648 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:00:56:128 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:00:56:131 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:00:56:133 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:00:56:279 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:00:56:412 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:00:56:414 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:00:56:416 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:00:56:509 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:00:56:599 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 25
[D][05:17:53][COMM]read battery soc:255


2025-07-31 23:00:56:692 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:00:56:705 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:00:56:707 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:00:56:809 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:00:56:964 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:00:56:966 ==>> 该项需要延时执行
2025-07-31 23:00:57:021 ==>> [D][05:17:53][COMM]4659 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:00:57:560 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5011. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802

2025-07-31 23:00:57:591 ==>> E->0x01B987FE 5013


2025-07-31 23:00:58:048 ==>> [D][05:17:54][COMM]5670 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:00:58:153 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][

2025-07-31 23:00:58:182 ==>> COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:00:58:675 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not r

2025-07-31 23:00:58:780 ==>> eady
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05

2025-07-31 23:00:58:885 ==>> :17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204


2025-07-31 23:00:58:945 ==>> 
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:00:59:035 ==>> [D][05:17:55][COMM]6682 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:00:59:140 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:01:00:065 ==>> [D][05:17:56][COMM]7692 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:00:591 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:01:00:974 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:01:00:977 ==>> 检测【33V输入电压ADC】
2025-07-31 23:01:00:980 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:01:083 ==>> [D][05:17:57][COMM]8703 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:01:323 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:17:57][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:57][COMM]adc read left brake adc:12  volt:15 mv
[D][05:17:57][COMM]adc read right brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read throttle adc:6  volt:7 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:01:01:510 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:01:01:513 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:01:01:515 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:01:01:631 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 23:01:01:785 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:01:787 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:01:01:803 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:01:805 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:01:01:807 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 23:01:01:823 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:01:01:825 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:01:01:844 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:01:01:846 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:01:01:874 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:01:01:876 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:01:02:029 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1995mV
Get AD_V7 1090mV
OVER 150


2025-07-31 23:01:02:075 ==>> [D][05:17:58][COMM]9714 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:02:160 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:02:165 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:01:02:179 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:02:183 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:01:02:186 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 23:01:02:198 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:01:02:218 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:01:02:220 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:01:02:222 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:01:02:241 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:01:02:244 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:01:02:332 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 23:01:02:422 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 23:01:02:522 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:02:524 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:01:02:542 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:02:547 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:01:02:550 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 23:01:02:561 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:01:02:563 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:01:02:590 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:01:02:593 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:01:02:613 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:01:02:615 ==>> 检测【打开WIFI(1)】
2025-07-31 23:01:02:621 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:01:02:625 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:01:02:835 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:01:02:883 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:01:02:887 ==>> 检测【清空消息队列(1)】
2025-07-31 23:01:02:911 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:01:03:296 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10725 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:

2025-07-31 23:01:03:356 ==>> 59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:01:03:421 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:01:03:423 ==>> 检测【打开GPS(1)】
2025-07-31 23:01:03:426 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:01:03:776 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087569523

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539483

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"c

2025-07-31 23:01:03:806 ==>> miot","","",0



2025-07-31 23:01:03:960 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:01:03:965 ==>> 检测【打开GSM联网】
2025-07-31 23:01:03:988 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:01:04:106 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:01:04:230 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:01:04:233 ==>> 检测【打开仪表供电1】
2025-07-31 23:01:04:237 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:01:04:406 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:01:04:517 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:01:04:521 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:01:04:524 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:01:04:757 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:01:04:792 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:01:04:794 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:01:04:798 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:01:04:999 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33386]


2025-07-31 23:01:05:072 ==>> 【读取主控ADC采集的仪表电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:01:05:077 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:01:05:091 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:01:05:304 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:01:05:353 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:01:05:356 ==>> 检测【AD_V20电压】
2025-07-31 23:01:05:358 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:01:05:455 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:01:05:515 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:01:05:713 ==>> 本次取值间隔时间:243ms
2025-07-31 23:01:05:734 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:01:05:837 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:01:05:929 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1655mV
OVER 150


2025-07-31 23:01:06:124 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][COMM]13738 imu init OK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:01:06:350 ==>> 本次取值间隔时间:498ms
2025-07-31 23:01:06:368 ==>> 【AD_V20电压】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:06:370 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:01:06:374 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:01:06:515 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:01:06:620 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 23:01:06:643 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:01:06:646 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:01:06:649 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:01:06:936 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:01:07:041 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 23:01:07:087 ==>>                                                                                                                                                                                                                                                                                                                                      nc id: 8, ret: 6


2025-07-31 23:01:07:179 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:01:07:182 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:01:07:184 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:01:07:371 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                   ][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:01:07:449 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:01:07:454 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:01:07:478 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:01:07:616 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:01:07:725 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:01:07:729 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:01:07:732 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:01:07:918 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:01:07:997 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:01:08:003 ==>> 检测【AD_V21电压】
2025-07-31 23:01:08:025 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:01:08:028 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:01:08:114 ==>> 1A A1 20 00 00 
Get AD_V21 1074mV
OVER 150


2025-07-31 23:01:08:324 ==>> 本次取值间隔时间:312ms
2025-07-31 23:01:08:342 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:01:08:414 ==>> 1A A1 20 00 00 
Get AD_V21 1653mV
OVER 150


2025-07-31 23:01:08:583 ==>> 本次取值间隔时间:228ms
2025-07-31 23:01:08:601 ==>> 【AD_V21电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:08:604 ==>> 检测【关闭仪表供电2】
2025-07-31 23:01:08:606 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:01:08:707 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:01:08:811 ==>> [D][05:18:05

2025-07-31 23:01:08:916 ==>> ][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,39,,,40,34,,,39,41,,,39,40,,,39,1*71

$GBGSV,2,2,06,25,,,39,24,,,37,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1627.111,1627.111,51.926,2097152,2097152,2097152*46

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 23:01:09:098 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:01:09:131 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:01:09:134 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:01:09:137 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:01:09:308 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:01:09:401 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:01:09:405 ==>> 检测【打开AccKey2供电】
2025-07-31 23:01:09:408 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:01:09:583 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:01:09:676 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:01:09:680 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:01:09:683 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:09:825 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,34,,,40,40,,,40,39,,,39,25,,,39,1*7A

$GBGSV,3,2,11,41,,,39,60,,,39,7,,,39,24,,,36,1*4A

$GBGSV,3,3,11,11,,,35,1,,,35,5,,,42,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1579.509,1579.509,50.477,2097152,2097152,2097152*4E



2025-07-31 23:01:10:050 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:06][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:18:06][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:06][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:06][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:06][COMM]adc read battery ts volt:12 mv
[D][05:18:06][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:06][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:01:10:205 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:01:10:208 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:01:10:210 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:01:10:382 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:01:10:481 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:01:10:484 ==>> 该项需要延时执行
2025-07-31 23:01:10:658 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 23:01:10:885 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,59,,,42,40,,,41,34,,,40,39,,,40,1*72

$GBGSV,5,2,17,41,,,40,25,,,39,60,,,39,7,,,39,1*4A

$GBGSV,5,3,17,3,,,39,43,,,39,11,,,37,24,,,36,1*45

$GBGSV,5,4,17,1,,,36,2,,,34,5,,,33,4,,,32,1*70

$GBGSV,5,5,17,32,,,32,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1555.915,1555.915,49.783,2097152,2097152,2097152*4E



2025-07-31 23:01:11:853 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,59,,,41,40,,,41,34,,,40,39,,,39,1*7B

$GBGSV,5,2,20,41,,,39,25,,,39,60,,,39,7,,,39,1*40

$GBGSV,5,3,20,3,,,39,43,,,38,11,,,37,16,,,37,1*40

$GBGSV,5,4,20,24,,,36,1,,,36,2,,,35,5,,,33,1*43

$GBGSV,5,5,20,23,,,33,4,,,32,32,,,32,22,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1538.320,1538.320,49.211,2097152,2097152,2097152*40



2025-07-31 23:01:12:682 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 23:01:12:877 ==>> $GBGGA,150116.710,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,59,,,40,34,,,40,39,,,40,1*74

$GBGSV,6,2,23,41,,,40,3,,,40,25,,,39,60,,,39,1*44

$GBGSV,6,3,23,7,,,39,43,,,38,11,,,38,16,,,37,1*4B

$GBGSV,6,4,23,1,,,37,10,,,37,24,,,36,6,,,36,1*75

$GBGSV,6,5,23,2,,,35,23,,,35,33,,,35,12,,,34,1*45

$GBGSV,6,6,23,5,,,33,4,,,32,32,,,32,1*77

$GBRMC,150116.710,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150116.710,0.000,1537.552,1537.552,49.178,2097152,2097152,2097152*56



2025-07-31 23:01:13:488 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:01:13:494 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:01:13:521 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:13:829 ==>> $GBGGA,150117.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,25,,,39,60,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,24,,,36,6,,,36,1*72

$GBGSV,7,5,25,23,,,36,2,,,35,33,,,35,44,,,34,1*42

$GBGSV,7,6,25,12,,,34,5,,,32,4,,,32,32,,,32,1*75

$GBGSV,7,7,25,9,,,31,1*4A

$GBRMC,150117.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150117.510,0.000,1524.012,1524.012,48.759,2097152,2097152,2097152*51

[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:10][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:10][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:10][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:10][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:10][COMM]adc read battery ts volt:11 mv
[D][05:18:10][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:10][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:10][COMM]arm_hub adc read bat

2025-07-31 23:01:13:874 ==>> _id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:01:14:108 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:01:14:114 ==>> 检测【打开AccKey1供电】
2025-07-31 23:01:14:123 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:01:14:305 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:01:14:486 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:01:14:492 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:01:14:505 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:01:14:620 ==>> 1A A1 00 40 00 
Get AD_V14 2670mV
OVER 150


2025-07-31 23:01:14:727 ==>> $GBGGA,150118.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,40,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,38,11,,,38,16,,,37,1*4C

$GBGSV,7,4,25,1,,,37,10,,,37,24,,,36,6,,,36,1*72

$GBGSV,7,5,25,23,,,36,2,,,35,33,,,35,12,,,35,1*40

$GBGSV,7,6,25,44,,,33,5,,,32,4,,,32,32,,,32,1*71

$GBGSV,7,7,25,9,,,32,1*49

$GBRMC,150118.510,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150118.510,0.000,1525.668,1525.668,48.810,2097152,2097152,2097152*5C

[D][05:18:11][COMM]read battery soc:255


2025-07-31 23:01:14:747 ==>> 原始值:【2670】, 乘以分压基数【2】还原值:【5340】
2025-07-31 23:01:14:817 ==>> 【读取AccKey1电压(ADV14)前】通过,【5340mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:01:14:823 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:01:14:851 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:15:128 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:11][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:11][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:11][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:11][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:11][COMM]adc read battery ts volt:14 mv
[D][05:18:11][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:11][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:01:15:416 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5545mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:01:15:421 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:01:15:436 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:01:15:693 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 
$GBGGA,150119.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,41,34,,,40,39,,,40,1*72

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,40,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,36,1*44

$GBGSV,7,5,25,6,,,36,2,,,36,33,,,35,12,,,35,1*74

$GBGSV,7,6,25,9,,,34,44,,,33,5,,,32,4,,,32,1*4F

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150119.510,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150119.510,0.000,1535.614,1535.614,49.124,2097152,2097152,2097152*52



2025-07-31 23:01:15:954 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:01:15:959 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:01:15:969 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:01:16:011 ==>> 1A A1 00 40 00 
Get AD_V14 2666mV
OVER 150


2025-07-31 23:01:16:205 ==>> 原始值:【2666】, 乘以分压基数【2】还原值:【5332】
2025-07-31 23:01:16:224 ==>> 【读取AccKey1电压(ADV14)后】通过,【5332mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:01:16:228 ==>> 检测【打开WIFI(2)】
2025-07-31 23:01:16:234 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:01:16:432 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:01:16:495 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:01:16:499 ==>> 检测【转刹把供电】
2025-07-31 23:01:16:503 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:01:16:748 ==>> $GBGGA,150120.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,36,1*44

$GBGSV,7,5,25,6,,,36,2,,,36,33,,,36,12,,,35,1*77

$GBGSV,7,6,25,9,,,34,44,,,33,4,,,33,5,,,32,1*4E

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150120.510,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150120.510,0.000,1535.605,1535.605,49.115,2097152,2097152,2097152*5A

[D][05:18:13][COMM]read battery soc:255
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:01:17:023 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:01:17:027 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:01:17:033 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:01:17:128 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:01:17:220 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2405mV
OVER 150


2025-07-31 23:01:17:280 ==>> 原始值:【2405】, 乘以分压基数【2】还原值:【4810】
2025-07-31 23:01:17:299 ==>> 【读取AD_V15电压(前)】通过,【4810mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:01:17:303 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:01:17:308 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:01:17:325 ==>> +WIFISCAN:4,0,F62A7D2297A3,-66
+WIFISCAN:4,1,CC057790A641,-70
+WIFISCAN:4,2,CC057790A640,-73
+WIFISCAN:4,3,CC057790A821,-87

[D][05:18:13][CAT1]wifi scan report total[4]


2025-07-31 23:01:17:400 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:01:17:520 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 23:01:17:564 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 23:01:17:589 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:01:17:593 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:01:17:599 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:17:624 ==>> $GBGGA,150121.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,40,25,

2025-07-31 23:01:17:685 ==>> ,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,36,1*44

$GBGSV,7,5,25,6,,,36,2,,,36,33,,,36,12,,,35,1*77

$GBGSV,7,6,25,9,,,35,4,,,33,5,,,33,44,,,32,1*4F

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150121.510,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150121.510,0.000,1538.921,1538.921,49.221,2097152,2097152,2097152*5F



2025-07-31 23:01:17:940 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:18:14][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:14][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:14][COMM]adc read battery ts volt:13 mv
[D][05:18:14][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3094  volt:5438 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:01:18:123 ==>> 【转刹把供电电压(主控ADC)】通过,【5438mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:01:18:127 ==>> 检测【转刹把供电电压】
2025-07-31 23:01:18:133 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:18:140 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 23:01:18:423 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:14][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:14][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:14][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:14][COMM]adc read battery ts volt:10 mv
[D][05:18:14][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3098  volt:5445 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:01:18:654 ==>> 【转刹把供电电压】通过,【5445mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:01:18:659 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:01:18:665 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:01:18:698 ==>> $GBGGA,150122.510,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,40,34,,,40,39,,,40,1*70

$GBGSV,7,2,25,41,,,40,60,,,40,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,37,1*45

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,35,12,,,35,1*74

$GBGSV,7,6,25,9,,,35,4,,,33,5,,,33,44,,,32,1*4F

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150122.510,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150122.510,0.000,1538.924,1538.924,49.223,2097152,2097152,2097152*5E

                                         

2025-07-31 23:01:18:800 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:01:19:006 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:01:19:048 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:01:19:051 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:01:19:121 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:01:19:213 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:01:19:246 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:01:19:250 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:01:19:254 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:01:19:348 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:01:19:378 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:01:19:423 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:01:19:482 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:01:19:486 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:01:19:489 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:01:19:680 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150
$GBGGA,150123.510,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,60,,,40,3,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,37,1*45

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,36,12,,,35,1*77

$GBGSV,7,6,25,9,,,35,4,,,33,5,,,33,32,,,33,1*4F

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150123.510,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150123.510,0.000,1543.896,1543.896,49.379,2097152,2097152,2097152*51



2025-07-31 23:01:19:759 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:01:19:763 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:01:19:766 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:01:19:815 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:01:20:028 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:01:20:032 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:01:20:036 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:01:20:115 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:01:20:298 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:01:20:303 ==>> 检测【左刹电压测试1】
2025-07-31 23:01:20:307 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:20:722 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:17][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:17][COMM]adc read left brake adc:1733  volt:2284 mv
[D][05:18:17][COMM]adc read right brake adc:1729  volt:2279 mv
[D][05:18:17][COMM]adc read throttle adc:1725  volt:2274 mv
[D][05:18:17][COMM]adc read battery ts volt:16 mv
[D][05:18:17][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:17][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,150124.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,40,34,,,40,39,,,40,1*70

$GBGSV,7,2,25,41,,,40,60,,,39,3,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,10,,,37,23,,,37,24,,,37,1*45

$GBGSV,7,5,25,6,,,36,2,,,36,33,,,35,12,,,35,1*74

$GBGSV,7,6,25,9,,,35,4,,,33,5,,,33,32,,,33,1*4F

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,

2025-07-31 23:01:20:782 ==>> 150124.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150124.510,0.000,1538.918,1538.918,49.218,2097152,2097152,2097152*50

                                         

2025-07-31 23:01:20:830 ==>> 【左刹电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 23:01:20:834 ==>> 检测【右刹电压测试1】
2025-07-31 23:01:20:853 ==>> 【右刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 23:01:20:858 ==>> 检测【转把电压测试1】
2025-07-31 23:01:20:873 ==>> 【转把电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 23:01:20:877 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:01:20:890 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:01:21:009 ==>> 3A A3 03 00 A3 


2025-07-31 23:01:21:114 ==>> OFF_OUT3
OVER 150


2025-07-31 23:01:21:147 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:01:21:151 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:01:21:156 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:01:21:219 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:01:21:417 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:01:21:421 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:01:21:447 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:01:21:507 ==>> 3A A3 05 00 A3 


2025-07-31 23:01:21:612 ==>> OFF_OUT5
OVER 150
$GBGGA,150125.510,,,,,0,00

2025-07-31 23:01:21:687 ==>> ,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,3,,,40,60,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,36,24,,,36,1*45

$GBGSV,7,5,25,6,,,36,2,,,36,33,,,35,9,,,35,1*4E

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,32,,,32,1*75

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150125.510,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150125.510,0.000,1535.613,1535.613,49.122,2097152,2097152,2097152*5B



2025-07-31 23:01:21:692 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:01:21:703 ==>> 检测【左刹电压测试2】
2025-07-31 23:01:21:707 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:01:22:022 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:18][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:18][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:18][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1310  volt:33133 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:01:22:224 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 23:01:22:228 ==>> 检测【右刹电压测试2】
2025-07-31 23:01:22:244 ==>> 【右刹电压测试2】通过,【19】符合目标值【0】至【50】要求!
2025-07-31 23:01:22:247 ==>> 检测【转把电压测试2】
2025-07-31 23:01:22:263 ==>> 【转把电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 23:01:22:267 ==>> 检测【晶振检测】
2025-07-31 23:01:22:270 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:01:22:388 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 23:01:22:542 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:01:22:546 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:01:22:572 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:01:22:690 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1664mV
Get AD_V4 1647mV
Get AD_V5 2771mV
Get AD_V6 1995mV
Get AD_V7 1090mV
OVER 150
$GBGGA,150126.510,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,3,,,39,60,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,36,24,,,36,1*45

$GBGSV,7,5,25,6,,,36,2,,,35,33,,,35,9,,,35,1*4D

$GBGSV,7,6,25,12,,,35,4,,,34,5,,,33,32,,,32,1*73

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150126.510,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150126.510,0.000,1535.607,1535.607,49.117,2097152,2097152,2097152*5E

                                         

2025-07-31 23:01:22:813 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:01:22:820 ==>> 检测【检测BootVer】
2025-07-31 23:01:22:841 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:23:179 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:19][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
[D][05:18:19][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:19][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:19][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:19][FCTY]DeviceID    = 460130071539483
[D][05:18:19][FCTY]HardwareID  = 867222087569523
[D][05:18:19][FCTY]MoBikeID    = 9999999999
[D][05:18:19][FCTY]LockID      = FFFFFFFFFF
[D][05:18:19][FCTY]BLEFWVersion= 105
[D][05:18:19][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:19][FCTY]Bat         = 3924 mv
[D][05:18:19][FCTY]Current     = 0 ma
[D][05:18:19][FCTY]VBUS        = 11700 mv
[D][05:18:19][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:19][FCTY]Ext battery vol = 33, adc = 1308
[D][05:18:19][FCTY]Acckey1 vol = 5535 mv, Acckey2 vol = 202 mv
[D][05:18:19][FCTY]Bike Type flag is invalied
[D][05:18:19][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:19][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:19][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:19][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][0

2025-07-31 23:01:23:224 ==>> 5:18:19][FCTY]Bat1         = 3794 mv
[D][05:18:19][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:01:23:375 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:01:23:379 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:01:23:383 ==>> 检测【检测固件版本】
2025-07-31 23:01:23:420 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:01:23:425 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:01:23:440 ==>> 检测【检测蓝牙版本】
2025-07-31 23:01:23:486 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:01:23:491 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:01:23:503 ==>> 检测【检测MoBikeId】
2025-07-31 23:01:23:577 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:01:23:581 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:01:23:584 ==>> 检测【检测蓝牙地址】
2025-07-31 23:01:23:598 ==>> 取到目标值:E7FEB0B4361A
2025-07-31 23:01:23:622 ==>> 【检测蓝牙地址】通过,【E7FEB0B4361A】符合目标值【】要求!
2025-07-31 23:01:23:626 ==>> 提取到蓝牙地址:E7FEB0B4361A
2025-07-31 23:01:23:645 ==>> 检测【BOARD_ID】
2025-07-31 23:01:23:667 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:01:23:673 ==>> 检测【检测充电电压】
2025-07-31 23:01:23:687 ==>> $GBGGA,150127.510,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,39,3,,,39,60,,,39,25,,,39,1*43

$GBGSV,7,3,25,7,,,39,43,,,38,11,,,38,16,,,37,1*4C

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,36,24,,,36,1*45

$GBGSV,7,5,25,6,,,36,2,,,35,33,,,35,9,,,35,1*4D

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,32,,,32,1*75

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150127.510,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150127.510,0.000,1525.653,1525.653,48.795,2097152,2097152,2097152*52



2025-07-31 23:01:23:717 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:01:23:722 ==>> 检测【检测VBUS电压1】
2025-07-31 23:01:23:762 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:01:23:766 ==>> 检测【检测充电电流】
2025-07-31 23:01:23:807 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:01:23:814 ==>> 检测【检测IMEI】
2025-07-31 23:01:23:819 ==>> 取到目标值:867222087569523
2025-07-31 23:01:23:858 ==>> 【检测IMEI】通过,【867222087569523】符合目标值【】要求!
2025-07-31 23:01:23:862 ==>> 提取到IMEI:867222087569523
2025-07-31 23:01:23:868 ==>> 检测【检测IMSI】
2025-07-31 23:01:23:876 ==>> 取到目标值:460130071539483
2025-07-31 23:01:23:908 ==>> 【检测IMSI】通过,【460130071539483】符合目标值【】要求!
2025-07-31 23:01:23:913 ==>> 提取到IMSI:460130071539483
2025-07-31 23:01:23:935 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:01:23:941 ==>> 取到目标值:460130071539483
2025-07-31 23:01:23:958 ==>> 【校验网络运营商(移动)】通过,【460130071539483】符合目标值【】要求!
2025-07-31 23:01:23:962 ==>> 检测【打开CAN通信】
2025-07-31 23:01:23:967 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:01:24:024 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:01:24:321 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:01:24:326 ==>> 检测【检测CAN通信】
2025-07-31 23:01:24:348 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:01:24:409 ==>> can send success


2025-07-31 23:01:24:439 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:01:24:499 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32130
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:01:24:559 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:01:24:632 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:01:24:638 ==>> 检测【关闭CAN通信】
2025-07-31 23:01:24:666 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:01:24:670 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,150128.510,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,40,34,,,40,39,,,40,1*73

$GBGSV,7,2,25,41,,,40,3,,,39,60,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,38,11,,,38,16,,,37,1*4C

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,36,24,,,36,1*45

$GBGSV,7,5,25,6,,,36,2,,,35,33,,,35,9,,,35,1*4D

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,33,32,,,32,1*75

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150128.510,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150128.510,0.000,1527.313,1527.313,48.850,2097152,2097152,2097152*5B



2025-07-31 23:01:24:724 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
                                         [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:01:24:990 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:01:24:994 ==>> 检测【打印IMU STATE】
2025-07-31 23:01:24:997 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:01:25:215 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:0
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:01:25:275 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:01:25:295 ==>> 检测【六轴自检】
2025-07-31 23:01:25:322 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:01:25:520 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:01:25:964 ==>> $GBGGA,150129.510,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,34,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,3,,,39,60,,,39,25,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,38,16,,,37,1*4D

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,37,24,,,37,1*45

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,35,9,,,35,1*4E

$GBGSV,7,6,25,12,,,35,4,,,33,5,,,33,32,,,32,1*74

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150129.510,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150129.510,0.000,1538.924,1538.924,49.224,2097152,2097152,2097152*52



2025-07-31 23:01:26:720 ==>> $GBGGA,150130.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,59,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,3,,,40,25,,,40,60,,,39,1*4D

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,39,16,,,37,1*4C

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,37,24,,,37,1*45

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,36,9,,,35,1*4D

$GBGSV,7,6,25,12,,,35,4,,,33,5,,,33,32,,,32,1*74

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150130.510,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150130.510,0.000,1545.560,1545.560,49.438,2097152,2097152,2097152*51

                                         

2025-07-31 23:01:27:202 ==>> [D][05:18:23][CAT1]<<< 
OK

[D][05:18:23][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:01:27:352 ==>> [D][05:18:23][COMM]Main Task receive event:142
[D][05:18:23][COMM]###### 34969 imu self test OK ######
[D][05:18:23][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-6,-6,4053]
[D][05:18:23][COMM]Main Task receive event:142 finished processing


2025-07-31 23:01:27:371 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:01:27:375 ==>> 检测【打印IMU STATE2】
2025-07-31 23:01:27:378 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:01:27:728 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,150131.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,59,,,40,39,,,40,1*71

$GBGSV,7,2,25,41,,,40,3,,,40,25,,,40,60,,,40,1*43

$GBGSV,7,3,25,7,,,39,43,,,39,11,,,39,23,,,38,1*45

$GBGSV,7,4,25,16,,,37,1,,,37,10,,,37,24,,,37,1*43

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,36,9,,,35,1*4D

$GBGSV,7,6,25,12,,,35,4,,,34,5,,,33,32,,,32,1*73

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150131.510,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150131.510,0.000,773.815,773.815,707.670,2097152,2097152,2097152*63



2025-07-31 23:01:27:896 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:01:27:901 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:01:27:906 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:01:28:014 ==>> 5A A5 02 5A A5 


2025-07-31 23:01:28:119 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:01:28:168 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:01:28:173 ==>> 检测【检测VBUS电压2】
2025-07-31 23:01:28:177 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:28:224 ==>> [D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 

2025-07-31 23:01:28:284 ==>> 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:01:28:599 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539483
[D][05:18:25][FCTY]HardwareID  = 867222087569523
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 150 ma
[D][05:18:25][FCTY]VBUS        = 10000 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 8, adc = 343
[D][05:18:25][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 25 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:2

2025-07-31 23:01:28:697 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:28:706 ==>> 5][FCTY]Bat1         = 3794 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 23:01:29:082 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539483
[D][05:18:25][FCTY]HardwareID  = 867222087569523
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 150 ma
[D][05:18:25][FCTY]VBUS        = 10000 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 189
[D][05:18:25][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 101 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3794 mv

2025-07-31 23:01:29:112 ==>> 
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:01:29:237 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:29:603 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539483
[D][05:18:26][FCTY]HardwareID  = 867222087569523
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:26][FCTY]Bat         = 3864 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 10000 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:26][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 25 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3794 mv
[D][05:18:2

2025-07-31 23:01:29:648 ==>> 6][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]msg 0601 loss. last_tick:32123. cur_tick:37127. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 37127


2025-07-31 23:01:29:723 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 23:01:29:771 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:30:531 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539483
[D][05:18:26][FCTY]HardwareID  = 867222087569523
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:26][FCTY]Bat         = 3864 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 10000 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 140
[D][05:18:26][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 151 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][

2025-07-31 23:01:30:571 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:30:636 ==>> 05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3794 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:26][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:26][COMM]Main Task receive event:65
[D][05:18:26][COMM]main task tmp_sleep_event = 80
[D][05:18:26][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:26][COMM]Main Task receive event:65 finished processing
[D][05:18:26][COMM]Main Task receive event:60
[D][05:18:26][COMM]smart_helmet_vol=255,255
[D][05:18:26][COMM]BAT CAN get state1 Fail 204
[D][05:18:26][COMM]BAT CAN get soc Fail, 204
[W][05:18:26][GNSS]stop locating
[D][05:18:26][GNSS]stop event:8
[D][05:18:26][GNSS]GPS stop. ret=0
[D][05:18:26][GNSS]all continue location stop
[D][05:18:26][COMM]report elecbike
[W][05:18:26][PROT]remove success[1629955106],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[3],type[5D03],priority[3],index[0],used[1

2025-07-31 23:01:30:741 ==>> ]
[D][05:18:26][COMM]Main Task receive event:60 finished processing
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]index:0
[D][05:18:26][PROT]is_send:1
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x3
[D][05:18:26][PROT]msg_type:0x5d03
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]Sending traceid[9999999999900005]
[D][05:18:26][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:26][PROT]index:0 1629955106
[D][05:18:26][PROT]is_send:0
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x2
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]=======================================

2025-07-31 23:01:30:846 ==>> ====================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]sending traceid [9999999999900005]
[D][05:18:26][PROT]Send_TO_M2M [1629955106]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:26][SAL ]sock send credit cnt[6]
[D][05:18:26][SAL ]sock send ind credit cnt[6]
[D][05:18:26][M2M ]m2m send data len[198]
[D][05:18:26][CAT1]gsm read msg sub id: 24
[D][05:18:26][SAL ]Cellular task submsg id[10]
[D][05:18:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:26][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 24, ret: 6
[D][05:18:26][CAT1]sub id: 24, ret: 6

[D][05:18:26][CAT1]gsm read msg sub id: 15
[D][05:18:26][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:26][CAT1]Send Data To Server[198][201] ... ->:
0063B98F11331133

2025-07-31 23:01:30:921 ==>> 1133113311331B88B52961A3C97FEECE878E3CF9E6A89A5065CBE107CE15A7F95F06716432430D5312FEE6285933FFE741D417F7C1778AC86E8A494419FA9DAA0EE2882BF4028DB0BD89DE1998BA0438E0FDC202E7C12D27C4F006
[D][05:18:26][CAT1]<<< 
SEND OK

[D][05:18:26][CAT1]exec over: func id: 15, ret: 11
[D][05:18:26][CAT1]sub id: 15, ret: 11

[D][05:18:26][SAL ]Cellular task submsg id[68]
[D][05:18:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]


2025-07-31 23:01:31:191 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539483
[D][05:18:27][FCTY]HardwareID  = 867222087569523
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:27][FCTY]Bat         = 3844 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 110
[D][05:18:27][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][F

2025-07-31 23:01:31:266 ==>> CTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3794 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                           

2025-07-31 23:01:31:359 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:01:31:694 ==>> [D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539483
[D][05:18:28][FCTY]HardwareID  = 867222087569523
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = E7FEB0B4361A
[D][05:18:28][FCTY]Bat         = 3844 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 5000 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 104
[D][05:18:28][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 126 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18

2025-07-31 23:01:31:739 ==>> :28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3794 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:01:31:892 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:01:31:897 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:01:31:906 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:01:32:009 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:01:32:114 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw

2025-07-31 23:01:32:144 ==>> 3 EXT_BAT_STATE_POWERON, 27
[D][05:18:28][COMM]read battery soc:255


2025-07-31 23:01:32:171 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:01:32:192 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:01:32:196 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:01:32:309 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:01:32:459 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:01:32:466 ==>> 检测【打开WIFI(3)】
2025-07-31 23:01:32:473 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:01:32:631 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:01:32:740 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:01:32:745 ==>> 检测【扩展芯片hw】
2025-07-31 23:01:32:769 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:01:32:907 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:01:33:021 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:01:33:027 ==>> 检测【扩展芯片boot】
2025-07-31 23:01:33:043 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:01:33:051 ==>> 检测【扩展芯片sw】
2025-07-31 23:01:33:073 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:01:33:078 ==>> 检测【检测音频FLASH】
2025-07-31 23:01:33:089 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:01:33:282 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:01:33:387 ==>> +WIFISCAN:4,0,CC057790A641,-70
+WIFISCAN:4,1,CC057790A640,-72
+WIFISCAN:4,2,F86FB0660A82,-86
+WIFISCAN:4,3,CC057790A821,-87

[D][05:18:30][CAT1]wifi scan report total[4]


2025-07-31 23:01:33:687 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:01:34:403 ==>>                                                                                                    1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, 

2025-07-31 23:01:34:508 ==>> num:3
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][M2M ]m2m_task: control_queue 

2025-07-31 23:01:34:613 ==>> type:[M2M_GSM_POWER_ON]
[D][05:18:30][PROT]index:1
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state er

2025-07-31 23:01:34:718 ==>> r
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:

2025-07-31 23:01:34:824 ==>> 18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_

2025-07-31 23:01:34:898 ==>> process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255
[D][05:18:30][GNSS]recv submsg id[3]


2025-07-31 23:01:35:760 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3F790372BD3F7857B84C86B7F4D0B507EEFD9FFD2D668BF28C1913225610

2025-07-31 23:01:35:835 ==>> EA644A43BD00554BF968CE4938162BE28042CBAAF71DEC9CEE22938FAE5E699823927034B087651F9828E87124E2581CBE352A8D3
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 23:01:36:155 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:01:36:719 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:37:417 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 23:01:37:720 ==>> [D][05:18:34][COMM]45359 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:37:826 ==>> [D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].

2025-07-31 23:01:37:885 ==>> Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:01:38:156 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:01:38:230 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:01:38:238 ==>> 检测【打开喇叭声音】
2025-07-31 23:01:38:244 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:01:38:917 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:35][COMM]read file, len:15228, num:4
[D][05:18:35][COMM]--->crc16:0x419c
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:35][COMM]f:[ec8

2025-07-31 23:01:39:022 ==>> 00m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_proce

2025-07-31 23:01:39:030 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:01:39:053 ==>> 检测【打开大灯控制】
2025-07-31 23:01:39:058 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:01:39:127 ==>> ss].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]46370 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 23:01:39:202 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:01:39:326 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:01:39:332 ==>> 检测【关闭仪表供电3】
2025-07-31 23:01:39:337 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:01:39:508 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 23:01:39:602 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:01:39:608 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:01:39:617 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:01:39:798 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:01:39:881 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:01:39:887 ==>> 检测【读大灯电压】
2025-07-31 23:01:39:895 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:01:40:105 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[32899]


2025-07-31 23:01:40:176 ==>> 【读大灯电压】通过,【32899mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:01:40:182 ==>> 检测【关闭大灯控制2】
2025-07-31 23:01:40:188 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:01:40:199 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 23:01:40:377 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:01:40:502 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:01:40:508 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:01:40:516 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:01:40:972 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[92]
[D][05:18:37][PROT]CLEAN,SEND:1
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M

2025-07-31 23:01:41:060 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:01:41:066 ==>> 检测【打开WIFI(4)】
2025-07-31 23:01:41:082 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:01:41:099 ==>> 2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3E2B17E0BC2B66490BA9AD2BA71C6B343944F2FA3890568BE1E33941536D0AEC564872ADDF9BE0B34CB2DA787C04415A1A33F863E9FCAB342E03CB8BB9DCFE3059AFE2DCCBC47C8FF0F37731778F5F56AA8BE
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 23:01:41:231 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:01:41:410 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:01:41:415 ==>> 检测【EC800M模组版本】
2025-07-31 23:01:41:421 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:01:41:429 ==>> [D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:41:609 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:01:41:714 ==>> [D][05:18:38][CAT1]<<< 
+GETVERSION: 

2025-07-31 23:01:41:744 ==>> "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 23:01:41:908 ==>> +WIFISCAN:4,0,CC057790A640,-73
+WIFISCAN:4,1,44A1917CA62F,-75
+WIFISCAN:4,2,44A1917CA62B,-75
+WIFISCAN:4,3,CC057790A821,-87

[D][05:18:38][CAT1]wifi scan report total[4]


2025-07-31 23:01:41:970 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:01:41:976 ==>> 检测【配置蓝牙地址】
2025-07-31 23:01:41:982 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:01:42:184 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E7FEB0B4361A>】
2025-07-31 23:01:42:192 ==>> [D][05:18:38][COMM]read battery soc:255
[W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:01:42:272 ==>> [D][05:18:38][GNSS]recv submsg id[3]


2025-07-31 23:01:42:438 ==>> [D][05:18:39][COMM]50057 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
recv ble 1
recv ble 2
ble set mac ok :e7,fe,b0,b4,36,1a
enable filters ret : 0

2025-07-31 23:01:42:741 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:01:42:751 ==>> 检测【BLETEST】
2025-07-31 23:01:42:765 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:01:42:821 ==>> 4A A4 01 A4 4A 


2025-07-31 23:01:43:032 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E7FEB0B4361A*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E7FEB0B4361A99999OVER 150


2025-07-31 23:01:43:447 ==>> [D][05:18:40][COMM]51068 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:01:43:774 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:01:43:780 ==>> 该项需要延时执行
2025-07-31 23:01:44:034 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:01:44:169 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:01:44:429 ==>> [D][05:18:41][COMM]52079 imu init OK


2025-07-31 23:01:46:193 ==>> [D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][PROT]index:1 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:1
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->:
0063B98

2025-07-31 23:01:46:267 ==>> 1113311331133113311331B88B35A4436B1F67A46996BFC2136890B617FE72A4AD5CB0D47EFA51058CEF0E218615AEC0F7BDEF03857685C61AFDF313C7CCF2BFDE6FF3E13585EA4FC1A6A8F2B29F22EB6485A69240E7B4BAE95AEE6147D4B15
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]


2025-07-31 23:01:46:297 ==>>                                          

2025-07-31 23:01:48:198 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:01:50:194 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:01:51:422 ==>> [D][05:18:47][PROT]CLEAN,SEND:1
[D][05:18:47][PROT]CLEAN:1
[D][05:18:47][PROT]index:0 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:4
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:2
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900005]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B523F768159340916033CC428E34EB306F5568D01F8B4D518D610FFE38DD

2025-07-31 23:01:51:498 ==>> BC4590922AC208CB35006E5A5B9358426B373600E53EA4BAF3DFC948E41C1940FE5D86861319047380AC5AE2AD8A130004E06961DB
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]


2025-07-31 23:01:52:223 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:01:53:783 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:01:53:792 ==>> 检测【检测WiFi结果】
2025-07-31 23:01:53:819 ==>> WiFi信号:【F62A7D2297A3】,信号值:-66
2025-07-31 23:01:53:824 ==>> WiFi信号:【CC057790A641】,信号值:-70
2025-07-31 23:01:53:834 ==>> WiFi信号:【CC057790A640】,信号值:-73
2025-07-31 23:01:53:852 ==>> WiFi信号:【CC057790A821】,信号值:-87
2025-07-31 23:01:53:869 ==>> WiFi信号:【F86FB0660A82】,信号值:-86
2025-07-31 23:01:53:878 ==>> WiFi信号:【44A1917CA62F】,信号值:-75
2025-07-31 23:01:53:901 ==>> WiFi信号:【44A1917CA62B】,信号值:-75
2025-07-31 23:01:53:930 ==>> WiFi数量【7】, 最大信号值:-66
2025-07-31 23:01:53:939 ==>> 检测【检测GPS结果】
2025-07-31 23:01:53:965 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:01:54:017 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all continue location stop
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all sing location stop


2025-07-31 23:01:54:217 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:01:54:784 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:01:54:804 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:54:813 ==>> 定位已等待【1】秒.
2025-07-31 23:01:55:221 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:51][COMM]Open GPS Module...
[D][05:18:51][COMM]LOC_MODEL_CONT
[D][05:18:51][GNSS]start event:8
[D][05:18:51][GNSS]GPS start. ret=0
[W][05:18:51][GNSS]start cont locating
[D][05:18:51][CAT1]gsm read msg sub id: 23
[D][05:18:51][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:51][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:01:55:792 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:55:801 ==>> 定位已等待【2】秒.
2025-07-31 23:01:55:951 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:01:56:211 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 23:01:56:577 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
                                                                                    

2025-07-31 23:01:56:792 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:56:802 ==>> 定位已等待【3】秒.
2025-07-31 23:01:56:897 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,39,,,41,40,,,41,34,,,40,41,,,40,1*7E

$GBGSV,2,2,07,25,,,38,24,,,22,60,,,39,1*77

[D][05:18:53][CAT1]<<< 
OK

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1534.153,1534.153,49.268,2097152,2097152,2097152*4E

[D][05:18:53][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:53][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 23, ret: 6
[D][05:18:53][CAT1]sub id: 23, ret: 6

[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B5CA336CD2A8A4584129548F781A61309F9CF3A1610CB52DF224EA3792F685039E2061FB84937B7C418A41C00D7DC5F9BC5C18D160E7EBB5A48FDA5AB2AC03CB16D672D8C688BA847C7905569ED722BC3E7A42
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Ce

2025-07-31 23:01:56:942 ==>> llular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 23:01:57:344 ==>> [D][05:18:53][GNSS]recv submsg id[1]
[D][05:18:53][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:01:57:729 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,40,,,41,60,,,41,59,,,41,39,,,40,1*74

$GBGSV,4,2,13,34,,,40,41,,,40,25,,,39,11,,,39,1*77

$GBGSV,4,3,13,7,,,39,16,,,38,24,,,36,1,,,40,1*74

$GBGSV,4,4,13,43,,,37,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1635.641,1635.641,52.247,2097152,2097152,2097152*49



2025-07-31 23:01:57:804 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:57:814 ==>> 定位已等待【4】秒.
2025-07-31 23:01:58:222 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:01:58:759 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,42,59,,,41,60,,,40,39,,,40,1*73

$GBGSV,4,2,16,34,,,40,41,,,40,25,,,40,1,,,39,1*4D

$GBGSV,4,3,16,11,,,39,7,,,39,43,,,39,3,,,39,1*75

$GBGSV,4,4,16,16,,,38,24,,,37,2,,,36,5,,,31,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1606.483,1606.483,51.365,2097152,2097152,2097152*4B

[D][05:18:55][COMM]S->M yaw:INVALID


2025-07-31 23:01:58:819 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:58:828 ==>> 定位已等待【5】秒.
2025-07-31 23:01:59:782 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,59,,,41,60,,,40,39,,,40,1*7C

$GBGSV,5,2,18,34,,,40,41,,,40,25,,,40,11,,,39,1*73

$GBGSV,5,3,18,7,,,39,43,,,39,3,,,39,1,,,38,1*4A

$GBGSV,5,4,18,16,,,38,24,,,37,23,,,37,2,,,35,1*41

$GBGSV,5,5,18,32,,,33,5,,,32,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1586.914,1586.914,50.748,2097152,2097152,2097152*41

[D][05:18:56][COMM]M->S yaw:INVALID


2025-07-31 23:01:59:827 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:01:59:836 ==>> 定位已等待【6】秒.
2025-07-31 23:02:00:230 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 23:02:00:783 ==>> $GBGGA,150204.617,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,59,,,40,60,,,40,39,,,40,1*74

$GBGSV,6,2,21,34,,,40,41,,,40,25,,,40,3,,,40,1*47

$GBGSV,6,3,21,11,,,39,7,,,39,43,,,39,1,,,38,1*70

$GBGSV,6,4,21,16,,,38,24,,,37,23,,,37,6,,,37,1*4E

$GBGSV,6,5,21,10,,,37,2,,,35,12,,,35,32,,,33,1*43

$GBGSV,6,6,21,5,,,33,1*40

$GBRMC,150204.617,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150204.617,0.000,1577.363,1577.363,50.434,2097152,2097152,2097152*55



2025-07-31 23:02:00:828 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:00:839 ==>> 定位已等待【7】秒.
2025-07-31 23:02:01:681 ==>> $GBGGA,150205.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,59,,,40,39,,,40,34,,,40,1*74

$GBGSV,6,2,23,41,,,40,25,,,40,3,,,40,60,,,39,1*4A

$GBGSV,6,3,23,11,,,39,7,,,39,43,,,39,1,,,38,1*72

$GBGSV,6,4,23,16,,,38,24,,,37,23,,,37,6,,,37,1*4C

$GBGSV,6,5,23,10,,,37,2,,,35,12,,,34,32,,,33,1*40

$GBGSV,6,6,23,5,,,33,4,,,32,33,,,42,1*71

$GBRMC,150205.517,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150205.517,0.000,1560.325,1560.325,49.901,2097152,2097152,2097152*54



2025-07-31 23:02:01:831 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:01:842 ==>> 定位已等待【8】秒.
2025-07-31 23:02:02:200 ==>> [D][05:18:58][PROT]CLEAN,SEND:0
[D][05:18:58][PROT]CLEAN:0
[D][05:18:58][PROT]index:2 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:3
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:18:58][PROT]sending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[134]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Send Da

2025-07-31 23:02:02:290 ==>> ta To Server[134][137] ... ->:
0043B683113311331133113311331B88BE2E40F7B793A841D52024DB3C3537EC0679AA7B5453F6052D5AD702581CFC415F3AFC295C3EF5A1033EDA33EE632BED06B5FD
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]
                                         

2025-07-31 23:02:02:675 ==>> $GBGGA,150206.517,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,59,,,40,39,,,40,34,,,40,1*77

$GBGSV,6,2,23,41,,,40,25,,,40,3,,,40,60,,,39,1*4A

$GBGSV,6,3,23,11,,,39,7,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,1,,,37,24,,,37,23,,,37,10,,,37,1*42

$GBGSV,6,5,23,6,,,36,2,,,35,33,,,34,12,,,34,1*70

$GBGSV,6,6,23,32,,,33,5,,,33,4,,,32,1*76

$GBRMC,150206.517,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150206.517,0.000,1551.975,1551.975,49.641,2097152,2097152,2097152*5C



2025-07-31 23:02:02:842 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:02:852 ==>> 定位已等待【9】秒.
2025-07-31 23:02:03:718 ==>> $GBGGA,150207.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,59,,,40,39,,,40,34,,,40,1*74

$GBGSV,6,2,23,41,,,40,25,,,40,3,,,40,60,,,39,1*4A

$GBGSV,6,3,23,11,,,39,7,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,23,,,38,1,,,37,24,,,37,10,,,37,1*4D

$GBGSV,6,5,23,6,,,36,2,,,35,33,,,35,12,,,34,1*71

$GBGSV,6,6,23,32,,,34,5,,,33,4,,,32,1*71

$GBRMC,150207.517,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150207.517,0.000,776.323,776.323,709.963,2097152,2097152,2097152*61



2025-07-31 23:02:03:854 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:03:863 ==>> 定位已等待【10】秒.
2025-07-31 23:02:04:242 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 23:02:04:703 ==>> $GBGGA,150208.517,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,40,3,,,40,39,,,40,1*4A

$GBGSV,6,2,23,59,,,40,34,,,40,41,,,40,7,,,39,1*44

$GBGSV,6,3,23,25,,,39,11,,,39,43,,,39,16,,,38,1*74

$GBGSV,6,4,23,10,,,37,24,,,37,1,,,37,23,,,37,1*42

$GBGSV,6,5,23,6,,,36,2,,,35,33,,,35,12,,,34,1*71

$GBGSV,6,6,23,32,,,34,5,,,33,4,,,31,1*72

$GBRMC,150208.517,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150208.517,0.000,774.529,774.529,708.323,2097152,2097152,2097152*61



2025-07-31 23:02:04:869 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:04:879 ==>> 定位已等待【11】秒.
2025-07-31 23:02:05:695 ==>> $GBGGA,150209.517,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,60,,,40,3,,,40,1*48

$GBGSV,6,2,23,59,,,40,34,,,40,25,,,40,41,,,40,1*7A

$GBGSV,6,3,23,7,,,39,11,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,1,,,38,10,,,37,24,,,37,23,,,37,1*4D

$GBGSV,6,5,23,6,,,36,33,,,36,2,,,35,12,,,35,1*73

$GBGSV,6,6,23,32,,,34,5,,,33,4,,,32,1*71

$GBRMC,150209.517,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150209.517,0.000,780.823,780.823,714.078,2097152,2097152,2097152*60



2025-07-31 23:02:05:877 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:05:887 ==>> 定位已等待【12】秒.
2025-07-31 23:02:06:251 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:02:06:708 ==>> $GBGGA,150210.517,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,60,,,40,3,,,40,39,,,40,1*49

$GBGSV,6,2,23,59,,,40,34,,,40,25,,,40,41,,,40,1*7A

$GBGSV,6,3,23,7,,,39,11,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,1,,,38,10,,,37,24,,,37,23,,,37,1*4D

$GBGSV,6,5,23,6,,,36,2,,,35,12,,,35,33,,,35,1*70

$GBGSV,6,6,23,32,,,34,5,,,33,4,,,32,1*71

$GBRMC,150210.517,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150210.517,0.000,779.023,779.023,712.433,2097152,2097152,2097152*65



2025-07-31 23:02:06:890 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:06:899 ==>> 定位已等待【13】秒.
2025-07-31 23:02:07:371 ==>> [D][05:19:03][PROT]CLEAN,SEND:2
[D][05:19:03][PROT]index:2 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:6
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:2
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:03][PROT]===========================================================
[D][05:19:03][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:03][CAT1]gsm rea

2025-07-31 23:02:07:401 ==>> d msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]<<< 
ERROR



2025-07-31 23:02:07:706 ==>> $GBGGA,150211.517,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,60,,,40,3,,,40,39,,,40,1*49

$GBGSV,6,2,23,59,,,40,34,,,40,25,,,40,41,,,40,1*7A

$GBGSV,6,3,23,7,,,39,11,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,1,,,38,10,,,37,24,,,37,23,,,37,1*4D

$GBGSV,6,5,23,6,,,36,2,,,35,12,,,35,33,,,35,1*70

$GBGSV,6,6,23,32,,,34,5,,,33,4,,,32,1*71

$GBRMC,150211.517,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150211.517,0.000,779.023,779.023,712.432,2097152,2097152,2097152*65



2025-07-31 23:02:07:903 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:07:914 ==>> 定位已等待【14】秒.
2025-07-31 23:02:08:259 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 23:02:08:705 ==>> $GBGGA,150212.517,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,60,,,40,3,,,40,39,,,40,1*49

$GBGSV,6,2,23,59,,,40,34,,,40,25,,,40,41,,,40,1*7A

$GBGSV,6,3,23,7,,,39,11,,,39,43,,,39,16,,,38,1*44

$GBGSV,6,4,23,1,,,38,10,,,37,23,,,37,24,,,36,1*4C

$GBGSV,6,5,23,6,,,36,33,,,36,2,,,35,12,,,35,1*73

$GBGSV,6,6,23,5,,,34,32,,,34,4,,,32,1*76

$GBRMC,150212.517,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150212.517,0.000,779.919,779.919,713.251,2097152,2097152,2097152*64



2025-07-31 23:02:08:903 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:08:914 ==>> 定位已等待【15】秒.
2025-07-31 23:02:09:700 ==>> $GBGGA,150213.517,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,60,,,40,1*71

$GBGSV,6,2,24,3,,,40,34,,,40,25,,,40,41,,,40,1*42

$GBGSV,6,3,24,7,,,39,11,,,39,43,,,39,16,,,38,1*43

$GBGSV,6,4,24,1,,,38,23,,,38,10,,,37,24,,,37,1*45

$GBGSV,6,5,24,2,,,36,6,,,36,33,,,36,12,,,35,1*77

$GBGSV,6,6,24,5,,,34,32,,,34,4,,,33,8,,,,1*48

$GBRMC,150213.517,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150213.517,0.000,785.312,785.312,718.182,2097152,2097152,2097152*63



2025-07-31 23:02:09:913 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:09:926 ==>> 定位已等待【16】秒.
2025-07-31 23:02:10:255 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 23:02:10:699 ==>> $GBGGA,150214.517,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,60,,,40,3,,,40,1*4F

$GBGSV,6,2,24,59,,,40,34,,,40,25,,,40,41,,,40,1*7D

$GBGSV,6,3,24,7,,,39,11,,,39,43,,,39,16,,,38,1*43

$GBGSV,6,4,24,1,,,38,10,,,37,24,,,37,23,,,37,1*4A

$GBGSV,6,5,24,6,,,36,33,,,36,2,,,35,12,,,35,1*74

$GBGSV,6,6,24,5,,,34,32,,,34,4,,,33,8,,,,1*48

$GBRMC,150214.517,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150214.517,0.000,782.614,782.614,715.715,2097152,2097152,2097152*61



2025-07-31 23:02:10:928 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:10:938 ==>> 定位已等待【17】秒.
2025-07-31 23:02:11:705 ==>> $GBGGA,150215.517,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,60,,,40,39,,,40,59,,,40,1*71

$GBGSV,6,2,24,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,6,3,24,3,,,39,11,,,39,43,,,39,16,,,38,1*47

$GBGSV,6,4,24,10,,,37,24,,,37,1,,,37,23,,,37,1*45

$GBGSV,6,5,24,6,,,36,33,,,36,2,,,35,12,,,35,1*74

$GBGSV,6,6,24,32,,,34,5,,,33,4,,,33,8,,,,1*4F

$GBRMC,150215.517,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150215.517,0.000,779.016,779.016,712.425,2097152,2097152,2097152*67



2025-07-31 23:02:11:937 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:11:948 ==>> 定位已等待【18】秒.
2025-07-31 23:02:12:262 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 23:02:12:706 ==>> $GBGGA,150216.517,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,3,,,40,39,,,40,1*4E

$GBGSV,7,2,25,59,,,40,34,,,40,25,,,40,41,,,40,1*7D

$GBGSV,7,3,25,7,,,39,11,,,39,43,,,39,16,,,38,1*43

$GBGSV,7,4,25,10,,,37,24,,,37,1,,,37,23,,,37,1*45

$GBGSV,7,5,25,6,,,36,33,,,36,2,,,35,12,,,35,1*74

$GBGSV,7,6,25,32,,,34,5,,,33,4,,,33,9,,,,1*4E

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,150216.517,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150216.517,0.000,779.917,779.917,713.249,2097152,2097152,2097152*69



2025-07-31 23:02:12:951 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:12:960 ==>> 定位已等待【19】秒.
2025-07-31 23:02:13:701 ==>> $GBGGA,150217.517,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,39,,,40,59,,,40,1*71

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,3,,,39,11,,,39,43,,,39,16,,,38,1*47

$GBGSV,7,4,25,10,,,37,24,,,37,1,,,37,23,,,37,1*45

$GBGSV,7,5,25,6,,,36,2,,,35,33,,,35,12,,,34,1*76

$GBGSV,7,6,25,32,,,34,5,,,33,4,,,33,9,,,,1*4E

$GBGSV,7,7,25,8,,,,1*49

$GBRMC,150217.517,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150217.517,0.000,777.221,777.221,710.784,2097152,2097152,2097152*6F



2025-07-31 23:02:13:961 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:13:972 ==>> 定位已等待【20】秒.
2025-07-31 23:02:14:273 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 23:02:14:791 ==>> [D][05:19:11][COMM]S->M yaw:INVALID
$GBGGA,150214.524,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,71,179,41,16,70,76,38,7,67,207,39,39,63,43,40,1*40

$GBGSV,7,2,25,6,61,10,36,3,60,190,39,10,55,214,37,59,52,129,40,1*41

$GBGSV,7,3,25,9,52,345,36,25,51,9,40,1,48,125,37,34,47,89,40,1*4C

$GBGSV,7,4,25,2,45,237,35,60,41,239,40,41,37,251,40,4,32,111,33,1*79

$GBGSV,7,5,25,5,21,256,33,24,21,72,36,8,5,200,,11,,,39,1*44

$GBGSV,7,6,25,43,,,39,23,,,37,33,,,35,12,,,34,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBRMC,150214.524,V,,,,,,,310725,1.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,150214.524,0.685,0.173,0.197,0.265,3.331,2.223,7.722*76

[D][05:19:11][COMM]IMU: [4,-1,-1000] ret=67 AWAKE!


2025-07-31 23:02:14:972 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:14:982 ==>> 定位已等待【21】秒.
2025-07-31 23:02:15:762 ==>> $GBGGA,150215.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,71,179,41,16,70,76,38,7,67,207,39,39,63,43,40,1*40

$GBGSV,6,2,24,6,61,10,36,3,60,190,39,10,55,214,37,59,52,129,40,1*41

$GBGSV,6,3,24,9,52,345,,25,51,9,40,1,48,125,37,34,47,89,40,1*49

$GBGSV,6,4,24,2,45,237,35,60,41,239,40,41,37,251,40,4,32,111,33,1*79

$GBGSV,6,5,24,5,21,256,33,24,21,72,37,11,,,39,43,,,39,1*77

$GBGSV,6,6,24,23,,,37,33,,,35,12,,,35,32,,,34,1*70

$GBGSV,2,1,06,40,71,179,40,39,63,43,41,25,51,9,40,34,47,89,37,5*7C

$GBGSV,2,2,06,41,37,251,37,24,21,72,36,5*42

$GBRMC,150215.504,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,150215.504,1.811,0.190,0.220,0.293,2.584,2.097,5.437*7D

[D][05:19:12][COMM]M->S yaw:INVALID


2025-07-31 23:02:15:976 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:15:986 ==>> 定位已等待【22】秒.
2025-07-31 23:02:16:272 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 23:02:16:985 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:02:16:995 ==>> 定位已等待【23】秒.
2025-07-31 23:02:17:401 ==>> $GBGGA,150216.504,2301.2575714,N,11421.9444400,E,1,06,1.65,82.291,M,-1.770,M,,*5F

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,40,39,63,43,40,1*4D

$GBGSV,6,2,24,6,61,10,37,3,60,190,40,10,55,214,37,59,52,129,41,1*4F

$GBGSV,6,3,24,9,52,345,36,25,51,9,40,1,48,125,37,34,47,89,40,1*4C

$GBGSV,6,4,24,2,45,237,35,60,41,239,40,41,37,251,40,4,32,111,33,1*79

$GBGSV,6,5,24,5,21,256,33,24,21,72,37,11,,,39,43,,,39,1*77

$GBGSV,6,6,24,23,,,37,33,,,36,12,,,35,32,,,34,1*73

$GBGSV,2,1,06,40,71,179,41,39,63,43,41,25,51,9,41,34,47,89,39,5*72

$GBGSV,2,2,06,41,37,251,39,24,21,72,36,5*4C

$GBRMC,150216.504,A,2301.2575714,N,11421.9444400,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:13][GNSS]HD8040 GPS
[D][05:19:13][GNSS]GPS diff_sec 124018983, report 0x42 frame
$GBGST,150216.504,1.930,0.301,0.355,0.447,2.225,1.910,4.471*70

[D][05:19:13][COMM]Main Task receive event:131
[D][05:19:13][COMM]index:0,power_mode:0xFF
[D][05:19:13][COMM]index:1,sound_mode:0xFF
[D][05:19:13][COMM]index:2,gsensor_mode:0xFF
[D][05:19:13][COMM]index:3,report_freq_mode:0xFF
[D][05:19:13][COMM]index:4,report_period:0xFF
[D][05:19:13][COMM]index:5,normal_reset_mode:0xFF
[D

2025-07-31 23:02:17:506 ==>> ][05:19:13][COMM]index:6,normal_reset_period:0xFF
[D][05:19:13][COMM]index:7,spock_over_speed:0xFF
[D][05:19:13][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:13][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:13][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:13][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:13][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:13][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:13][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:13][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:13][COMM]index:16,imu_config_params:0xFF
[D][05:19:13][COMM]index:17,long_connect_params:0xFF
[D][05:19:13][COMM]index:18,detain_mark:0xFF
[D][05:19:13][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:13][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:13][COMM]index:21,mc_mode:0xFF
[D][05:19:13][COMM]index:22,S_mode:0xFF
[D][05:19:13][COMM]index:23,overweight:0xFF
[D][05:19:13][COMM]index:24,standstill_mode:0xFF
[D][05:19:13][COMM]index:25,night_mode:0xFF
[D][05:19:13][COMM]index:26,experiment1:0xFF
[D][05:19:13][COMM]index:27,experiment2:0xFF
[D][05:19:13][COMM]index:28,experiment3:0xFF
[D][05:19:13][COMM]index:29,experiment4:0xFF
[D

2025-07-31 23:02:17:611 ==>> ][05:19:13][COMM]index:30,night_mode_start:0xFF
[D][05:19:13][COMM]index:31,night_mode_end:0xFF
[D][05:19:13][COMM]index:33,park_report_minutes:0xFF
[D][05:19:13][COMM]index:34,park_report_mode:0xFF
[D][05:19:13][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:13][COMM]index:38,charge_battery_para: FF
[D][05:19:13][COMM]index:39,multirider_mode:0xFF
[D][05:19:13][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:13][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:13][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:13][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:13][COMM]index:44,riding_duration_config:0xFF
[D][05:19:13][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:13][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:13][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:13][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:13][COMM]index:49,mc_load_startup:0xFF
[D][05:19:13][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:13][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:13][COMM]index:52,traffic_mode:0xFF
[D][05:19:13][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:13][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:13][COMM]inde

2025-07-31 23:02:17:716 ==>> x:55,wheel_alarm_play_switch:255
[D][05:19:13][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:13][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:13][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:13][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:13][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:13][COMM]index:63,experiment5:0xFF
[D][05:19:13][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:13][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:13][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:13][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:13][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:13][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:13][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:13][COMM]index:72,experiment6:0xFF
[D][05:19:13][COMM]index:73,experiment7:0xFF
[D][05:19:13][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:13][COMM]index:75,zero_value_from_server:-1
[D][05:19:13][COMM]index:76,multirider_threshold:255
[D][05:19:13][COMM]index:77,experiment8:255
[D][05:19:13][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:13][COMM]index:79,temp_park_tail_light_twinkle_duration:255

2025-07-31 23:02:17:821 ==>> 
[D][05:19:13][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:13][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:13][COMM]index:83,loc_report_interval:255
[D][05:19:13][COMM]index:84,multirider_threshold_p2:255
[D][05:19:13][COMM]index:85,multirider_strategy:255
[D][05:19:13][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:13][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:13][COMM]index:90,weight_param:0xFF
[D][05:19:13][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:13][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:13][COMM]index:95,current_limit:0xFF
[D][05:19:13][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:13][COMM]index:100,location_mode:0xFF

[W][05:19:13][PROT]remove success[1629955153],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:13][COMM]Main Task receive event:131 finished processing
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,150217.004,2301.2574740,N,11421.9445686,E,1,06,1.65,82.532,M,-

2025-07-31 23:02:17:927 ==>> 1.770,M,,*58

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,40,39,63,43,41,1*4C

$GBGSV,6,2,24,6,61,10,37,3,60,190,40,10,55,214,37,59,52,129,41,1*4F

$GBGSV,6,3,24,9,52,345,36,25,51,9,40,1,48,125,38,34,47,89,40,1*43

$GBGSV,6,4,24,2,45,237,35,60,41,239,40,41,37,251,41,4,32,111,33,1*78

$GBGSV,6,5,24,5,21,256,33,24,21,72,37,11,,,39,43,,,39,1*77

$GBGSV,6,6,24,23,,,37,33,,,36,12,,,35,32,,,34,1*73

$GBGSV,2,1,06,40,71,179,41,39,63,43,41,25,51,9,41,34,47,89,39,5*72

$GBGSV,2,2,06,41,37,251,39,24,21,72,36,5*4C

$GBRMC,150217.004,A,2301.2574740,N,11421.9445686,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,150217.004,1.556,0.183,0.208,0.278,1.789,1.535,3.783*71

[D][05:19:13][CAT1]exec over: func id: 15, ret: -93
[D][05:19:13][CAT1]sub id: 15, ret: -93

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:13][SAL ]socket send fail. id[4]
[D][05:19:13][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:13][M2M ]m2m select fd[4]
[D][05:19:13][M2M ]socket[4] Link is disconnected
[D][05:19:13][M2M 

2025-07-31 23:02:17:986 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 23:02:17:999 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【16】,信号值:【38】
北斗星号:【7】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【37】
北斗星号:【3】,信号值:【40】
北斗星号:【10】,信号值:【37】
北斗星号:【59】,信号值:【41】
北斗星号:【9】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【37】
北斗星号:【34】,信号值:【39】
北斗星号:【2】,信号值:【35】
北斗星号:【60】,信号值:【40】
北斗星号:【41】,信号值:【39】
北斗星号:【24】,信号值:【36】
北斗星号:【11】,信号值:【39】
北斗星号:【43】,信号值:【39】
北斗星号:【23】,信号值:【37】
北斗星号:【33】,信号值:【36】
北斗星号:【12】,信号值:【35】

2025-07-31 23:02:18:017 ==>> 检测【CSQ强度】
2025-07-31 23:02:18:026 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:02:18:047 ==>> ]tcpclient close[4]
[D][05:19:13][SAL ]socket[4] has closed
[D][05:19:13][PROT]protocol read data ok
[E][05:19:13][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:13][PROT]M2M Send Fail [1629955153]
[D][05:19:13][PROT]CLEAN,SEND:2
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:13][CAT1]gsm read msg sub id: 10
[D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:13][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=0

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:02:18:091 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:02:18:197 ==>>                                                                      

2025-07-31 23:02:18:286 ==>>                                                                                                                                                                  61,10,36,3,60,190,40,10,55,214,37,59,52,129,41,1*4E

$GBGSV,6,3,24,9,52,345,36,25,51,9,40,1,48,125,38,34,47,89,40,1*43

$GBGSV,6,4,24,2,45,237,35,60,41,239,40,41,37,251,41,4,32,111,33,1*78

$GBGSV,6,5,24,5,21,256,33,24,21,72,37,11,,,39,43,,,39,1*77

$GBGSV,6,6,24,23,,,37,33,,,36,12,,,35,32,,,34,1*73

$GBGSV,2,1,06,40,71,179,41,39,63,43,41,25,51,9,41,34,47,89,40,5*7C

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150218.000,A,2301.2574683,N,11421.9446849,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,150218.000,1.303,0.230,0.266,0.343,1.484,1.269,3.381*70

[W][05:19:14][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:02:18:317 ==>>                                          

2025-07-31 23:02:19:476 ==>> $GBGGA,150219.000,2301.2575241,N,11421.9447575,E,1,06,1.65,83.484,M,-1.770,M,,*57

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,40,39,63,43,40,1*4D

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,37,59,52,129,41,1*4E

$GBGSV,6,3,24,9,52,345,36,25,51,9,40,1,48,125,38,34,47,89,40,1*43

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,41,1*40

$GBGSV,6,5,24,4,32,111,33,5,21,256,34,24,21,72,37,43,,,39,1*7E

$GBGSV,6,6,24,23,,,37,33,,,36,12,,,35,32,,,34,1*73

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150219.000,A,2301.2575241,N,11421.9447575,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150219.000,1.174,0.172,0.194,0.261,1.294,1.109,3.101*71

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:15][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:15][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:15][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:15][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:15][CAT1]<<< 

2025-07-31 23:02:19:536 ==>> 
OK

[D][05:19:15][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]exec over: func id: 8, ret: 6
[D][05:19:15][CAT1]gsm read msg sub id: 12
[D][05:19:15][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:16][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:16][CAT1]exec over: func id: 12, ret: 21


2025-07-31 23:02:19:657 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 23:02:19:669 ==>> 检测【关闭GSM联网】
2025-07-31 23:02:19:694 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:02:19:858 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            [PROT]retry_times:1
[D][05:19:16][PROT]send_path:0x2
[D][05:19:16][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:16][PROT]sending traceid [9999999999900007]
[D][05:19:16][PROT]Send_TO_M2M [1629955156]
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:16][SAL ]sock send credit cnt[6]
[D][05:19:16][SAL ]sock send ind credit cnt[6]
[D][05:19:16][M2M ]m2m send

2025-07-31 23:02:19:963 ==>>  data len[134]
[D][05:19:16][SAL ]Cellular task submsg id[10]
[D][05:19:16][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:16][CAT1]gsm read msg sub id: 15
[D][05:19:16][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:16][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BE72A8FD1FB4835D0C32E39D829D08B0A2F98A792FF122A866F6F6B9BBFDD5775E94ED2BD2C626E7AAABED64722F4C5B9E8EA6
[D][05:19:16][CAT1]<<< 
SEND OK

[D][05:19:16][CAT1]exec over: func id: 15, ret: 11
[D][05:19:16][CAT1]sub id: 15, ret: 11

[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:16][M2M ]g_m2m_is_idle become true
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]M2M Send ok [1629955156]
[W][05:19:16][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:16][COMM]GSM test
[D][05:19:16][COMM]GSM test disable


2025-07-31 23:02:20:196 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:02:20:204 ==>> 检测【4G联网测试】
2025-07-31 23:02:20:222 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:02:20:281 ==>> $GBGGA,150220.000,2301.2575095,N,11421.9448094,E,1,06,1.65,83.766,M,-1.770,M,,*5C

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,40,39,63,43,40,1*4D

$GBGSV,6,2,24,6,61,10,37,3,60,190,40,10,55,214,37,59,52,129,41,1*4F

$GBGSV,6,3,24,9,52,345,36,25,51,9,40,1,48,125,38,34,47,89,41,1*42

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,41,1*40

$GBGSV,6,5,24,4,32,111,33,5,21,256,34,24,21,72,37,43,,,39,1*7E

$GBGSV,6,6,24,23,,,38,33,,,36,12,,,35,32,,,34,1*7C

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150220.000,A,2301.2575095,N,11421.9448094,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,150220.000,1.206,0.270,0.316,0.402,1.247,1.089,2.942*7E

                                         

2025-07-31 23:02:21:031 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:16][COMM]Main Task receive event:14
[D][05:19:16][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955156, allstateRepSeconds = 0
[D][05:19:16][COMM]index:0,power_mode:0xFF
[D][05:19:16][COMM]index:1,sound_mode:0xFF
[D][05:19:16][COMM]index:2,gsensor_mode:0xFF
[D][05:19:16][COMM]index:3,report_freq_mode:0xFF
[D][05:19:16][COMM]index:4,report_period:0xFF
[D][05:19:16][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:16][COMM]index:6,normal_reset_period:0xFF
[D][05:19:16][COMM]index:7,spock_over_speed:0xFF
[D][05:19:16][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:16][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:16][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:16][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:16][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:16][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:16][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:16][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:16][COMM]index:16,imu_config_params:0xFF
[D][05:19:16][COMM]index:17,long_connect_params:0xFF
[D][05:19:16][COMM]in

2025-07-31 23:02:21:136 ==>> dex:18,detain_mark:0xFF
[D][05:19:16][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:16][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:16][COMM]index:21,mc_mode:0xFF
[D][05:19:16][COMM]index:22,S_mode:0xFF
[D][05:19:16][COMM]index:23,overweight:0xFF
[D][05:19:16][COMM]index:24,standstill_mode:0xFF
[D][05:19:16][COMM]index:25,night_mode:0xFF
[D][05:19:16][COMM]index:26,experiment1:0xFF
[D][05:19:16][COMM]index:27,experiment2:0xFF
[D][05:19:16][COMM]index:28,experiment3:0xFF
[D][05:19:16][COMM]index:29,experiment4:0xFF
[D][05:19:16][COMM]index:30,night_mode_start:0xFF
[D][05:19:16][COMM]index:31,night_mode_end:0xFF
[D][05:19:17][COMM]index:33,park_report_minutes:0xFF
[D][05:19:17][COMM]index:34,park_report_mode:0xFF
[D][05:19:17][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:17][COMM]index:38,charge_battery_para: FF
[D][05:19:17][COMM]index:39,multirider_mode:0xFF
[D][05:19:17][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:17][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:17][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:17][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:17][COMM]index:44,riding_duration_config:0xFF
[D][05:

2025-07-31 23:02:21:241 ==>> 19:17][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:17][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:17][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:17][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:17][COMM]index:49,mc_load_startup:0xFF
[D][05:19:17][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:17][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:17][COMM]index:52,traffic_mode:0xFF
[D][05:19:17][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:17][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:17][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:17][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:17][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:17][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:17][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:17][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:17][COMM]index:63,experiment5:0xFF
[D][05:19:17][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:17][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:17][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:17][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:17][COMM]index:68,camera_park_ps_cfg:0xFFFF


2025-07-31 23:02:21:346 ==>> [D][05:19:17][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:17][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:17][COMM]index:72,experiment6:0xFF
[D][05:19:17][COMM]index:73,experiment7:0xFF
[D][05:19:17][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:17][COMM]index:75,zero_value_from_server:-1
[D][05:19:17][COMM]index:76,multirider_threshold:255
[D][05:19:17][COMM]index:77,experiment8:255
[D][05:19:17][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:17][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:17][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:17][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:17][COMM]index:83,loc_report_interval:255
[D][05:19:17][COMM]index:84,multirider_threshold_p2:255
[D][05:19:17][COMM]index:85,multirider_strategy:255
[D][05:19:17][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:17][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:17][COMM]index:90,weight_param:0xFF
[D][05:19:17][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:17][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:17][COMM]index:95,current_limit:0xFF
[D][05:19:17][COMM]index:97,pa

2025-07-31 23:02:21:451 ==>> nel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:17][COMM]index:100,location_mode:0xFF

[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:17][CAT1]gsm read msg sub id: 13
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:17][PROT]index:0 1629955157
[D][05:19:17][PROT]is_send:0
[D][05:19:17][PROT]sequence_num:8
[D][05:19:17][PROT]retry_timeout:0
[D][05:19:17][PROT]retry_times:1
[D][05:19:17][PROT]send_path:0x2
[D][05:19:17][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:17][PROT]===========================================================
[D][05:19:17][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:17][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955157]
[D][05:19:17][PROT]===========================================================
[D][05:19:17][PROT]sending traceid [9999999999900009]
[D][05:19:17][PROT]Send_TO_M2M [1629955157]
[D][05:19:17][M2M ]m2m switch to: M2M_

2025-07-31 23:02:21:556 ==>> GSM_SOCKET_SEND
[D][05:19:17][SAL ]sock send credit cnt[6]
[D][05:19:17][SAL ]sock send ind credit cnt[6]
[D][05:19:17][M2M ]m2m send data len[294]
[D][05:19:17][SAL ]Cellular task submsg id[10]
[D][05:19:17][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:17][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:17][CAT1]exec over: func id: 13, ret: 21
[D][05:19:17][M2M ]get csq[20]
[D][05:19:17][CAT1]gsm read msg sub id: 15
[D][05:19:17][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:17][CAT1]<<< 
ERROR

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:17][COMM]------>period, report file manifest
[D][05:19:17][COMM]Main Task receive event:14 finished processing
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][COMM]IMU: [-20,14,-913] ret=27 AWAKE!
[D][05:19:17][COMM]S->M yaw:INVALID
[D][05:19:17][COMM]IMU: [0,8,-998] ret=41 AWAKE

2025-07-31 23:02:21:661 ==>> !
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 23:02:21:796 ==>> [D][05:19:18][COMM]M->S yaw:INVALID


2025-07-31 23:02:22:294 ==>> $GBGGA,150222.000,2301.2574811,N,11421.9448754,E,1,06,1.65,84.048,M,-1.770,M,,*5C

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,39,39,63,43,40,1*43

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,37,59,52,129,41,1*4E

$GBGSV,6,3,24,9,52,345,,25,51,9,40,1,48,125,37,34,47,89,40,1*49

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,40,1*41

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,34,24,21,72,36,1*78

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,36,32,,,34,1*7B

$GBGSV,2,1,06,40,71,179,41,39,63,43,41,25,51,9,41,34,47,89,40,5*7C

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150222.000,A,2301.2574811,N,11421.9448754,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150222.000,1.074,0.177,0.201,0.270,1.045,0.918,2.615*76

                                         

2025-07-31 23:02:22:835 ==>> [D][05:19:19][COMM]S->M yaw:INVALID


2025-07-31 23:02:23:260 ==>> $GBGGA,150223.000,2301.2574582,N,11421.9448758,E,1,06,1.65,84.156,M,-1.770,M,,*58

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,39,39,63,43,40,1*43

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,36,59,52,129,40,1*4E

$GBGSV,6,3,24,9,52,345,,25,51,9,40,1,48,125,37,34,47,89,40,1*49

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,40,1*41

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,36,1*7F

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,35,32,,,34,1*78

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150223.000,A,2301.2574582,N,11421.9448758,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150223.000,1.480,0.177,0.199,0.267,1.327,1.227,2.744*78



2025-07-31 23:02:23:980 ==>> [D][05:19:20][COMM]M->S yaw:INVALID


2025-07-31 23:02:24:295 ==>> $GBGGA,150224.000,2301.2574862,N,11421.9448551,E,1,06,1.65,84.247,M,-1.770,M,,*54

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.74,1.65,2.18,4*0D

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,39,39,63,43,40,1*43

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,37,59,52,129,40,1*4F

$GBGSV,6,3,24,9,52,345,,25,51,9,40,1,48,125,37,34,47,89,40,1*49

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,40,1*41

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,36,1*7F

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,35,32,,,34,1*78

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150224.000,A,2301.2574862,N,11421.9448551,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150224.000,1.465,0.245,0.284,0.365,1.292,1.201,2.659*7C

                                         

2025-07-31 23:02:25:270 ==>> $GBGGA,150225.000,2301.2574926,N,11421.9448767,E,1,06,1.65,84.286,M,-1.770,M,,*5E

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.73,1.65,2.18,4*0A

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,39,39,63,43,40,1*43

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,37,59,52,129,40,1*4F

$GBGSV,6,3,24,9,52,345,,25,51,9,40,1,48,125,37,34,47,89,40,1*49

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,40,1*41

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,37,1*7E

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,35,32,,,34,1*78

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7E

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150225.000,A,2301.2574926,N,11421.9448767,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150225.000,1.175,0.168,0.189,0.255,1.043,0.952,2.414*70



2025-07-31 23:02:26:253 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:02:26:273 ==>> $GBGGA,150226.000,2301.2574965,N,11421.9448942,E,1,06,1.65,84.303,M,-1.770,M,,*5F

$GBGSA,A,3,40,39,25,34,41,24,,,,,,,2.73,1.65,2.18,4*0A

$GBGSV,6,1,24,40,71,179,42,16,70,76,38,7,67,207,39,39,63,43,40,1*43

$GBGSV,6,2,24,6,61,10,36,3,60,190,40,10,55,214,37,59,52,129,41,1*4E

$GBGSV,6,3,24,9,52,345,35,25,51,9,40,1,48,125,37,34,47,89,40,1*4F

$GBGSV,6,4,24,11,46,127,39,2,45,237,35,60,41,239,40,41,37,251,40,1*41

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,37,1*7E

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,35,32,,,34,1*78

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,41,24,21,72,37,5*42

$GBRMC,150226.000,A,2301.2574965,N,11421.9448942,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150226.000,1.103,0.208,0.238,0.311,0.963,0.877,2.292*7B



2025-07-31 23:02:26:313 ==>>                                          

2025-07-31 23:02:26:403 ==>> [W][05:19:23][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:02:26:991 ==>> [D][05:19:23][COMM]Main Task receive event:14
[D][05:19:23][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955163, allstateRepSeconds = 0
[D][05:19:23][COMM]index:0,power_mode:0xFF
[D][05:19:23][COMM]index:1,sound_mode:0xFF
[D][05:19:23][COMM]index:2,gsensor_mode:0xFF
[D][05:19:23][COMM]index:3,report_freq_mode:0xFF
[D][05:19:23][COMM]index:4,report_period:0xFF
[D][05:19:23][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:23][COMM]index:6,normal_reset_period:0xFF
[D][05:19:23][COMM]index:7,spock_over_speed:0xFF
[D][05:19:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:23][COMM]index:16,imu_config_params:0xFF
[D][05:19:23][COMM]index:17,long_connect_params:0xFF
[D][05:19:23][COMM]index:18,detain_mark:0xFF
[D][05:19:23][COMM]index:19,lock_

2025-07-31 23:02:27:096 ==>> pos_report_count:0xFF
[D][05:19:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:23][COMM]index:21,mc_mode:0xFF
[D][05:19:23][COMM]index:22,S_mode:0xFF
[D][05:19:23][COMM]index:23,overweight:0xFF
[D][05:19:23][COMM]index:24,standstill_mode:0xFF
[D][05:19:23][COMM]index:25,night_mode:0xFF
[D][05:19:23][COMM]index:26,experiment1:0xFF
[D][05:19:23][COMM]index:27,experiment2:0xFF
[D][05:19:23][COMM]index:28,experiment3:0xFF
[D][05:19:23][COMM]index:29,experiment4:0xFF
[D][05:19:23][COMM]index:30,night_mode_start:0xFF
[D][05:19:23][COMM]index:31,night_mode_end:0xFF
[D][05:19:23][COMM]index:33,park_report_minutes:0xFF
[D][05:19:23][COMM]index:34,park_report_mode:0xFF
[D][05:19:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:23][COMM]index:38,charge_battery_para: FF
[D][05:19:23][COMM]index:39,multirider_mode:0xFF
[D][05:19:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:23][COMM]index:44,riding_duration_config:0xFF
[D][05:19:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:

2025-07-31 23:02:27:201 ==>> 23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:23][COMM]index:49,mc_load_startup:0xFF
[D][05:19:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:23][COMM]index:52,traffic_mode:0xFF
[D][05:19:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:23][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:23][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:23][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:23][COMM]index:63,experiment5:0xFF
[D][05:19:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][

2025-07-31 23:02:27:306 ==>> 05:19:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:23][COMM]index:72,experiment6:0xFF
[D][05:19:23][COMM]index:73,experiment7:0xFF
[D][05:19:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:23][COMM]index:75,zero_value_from_server:-1
[D][05:19:23][COMM]index:76,multirider_threshold:255
[D][05:19:23][COMM]index:77,experiment8:255
[D][05:19:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:23][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:23][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:23][COMM]index:83,loc_report_interval:255
[D][05:19:23][COMM]index:84,multirider_threshold_p2:255
[D][05:19:23][COMM]index:85,multirider_strategy:255
[D][05:19:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:23][COMM]index:90,weight_param:0xFF
[D][05:19:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:23][COMM]index:95,current_limit:0xFF
[D][05:19:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:23][

2025-07-31 23:02:27:411 ==>> COMM]index:100,location_mode:0xFF

[W][05:19:23][PROT]remove success[1629955163],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 23:02:27:441 ==>>                                                                                                                                                             

2025-07-31 23:02:28:282 ==>> $GBGGA,150228.000,2301.2574377,N,11421.9449299,E,1,09,1.23,84.326,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,16,25,34,11,41,24,,,,2.37,1.23,2.02,4*03

$GBGSV,6,1,24,40,71,179,41,7,67,207,40,39,63,43,40,6,61,10,36,1*71

$GBGSV,6,2,24,16,61,14,38,3,60,190,40,10,55,214,37,59,52,129,41,1*75

$GBGSV,6,3,24,9,52,345,35,25,51,9,40,1,48,125,37,34,47,89,40,1*4F

$GBGSV,6,4,24,11,46,126,39,2,45,237,35,60,41,239,40,41,37,251,40,1*40

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,37,1*7E

$GBGSV,6,6,24,43,,,39,23,,,38,33,,,35,32,,,34,1*77

$GBGSV,2,1,06,40,71,179,41,39,63,43,42,25,51,9,41,34,47,89,40,5*7F

$GBGSV,2,2,06,41,37,251,41,24,21,72,37,5*42

$GBRMC,150228.000,A,2301.2574377,N,11421.9449299,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150228.000,1.575,0.174,0.182,0.266,1.302,1.240,2.452*72



2025-07-31 23:02:28:312 ==>>                                          

2025-07-31 23:02:28:557 ==>> [D][05:19:25][M2M ]get csq[-1]


2025-07-31 23:02:28:903 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:25][PROT]remove success[1629955165],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:25][PROT]add success [1629955165],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:25][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:25][COMM][LOC]wifi scan is already running, error
[D][05:19:25][COMM]Main Task receive event:14 finished processing
[D][05:19:25][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:25][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 23:02:29:271 ==>> $GBGGA,150229.000,2301.2573792,N,11421.9449385,E,1,09,1.23,84.295,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,25,34,11,41,24,,,,2.37,1.23,2.02,4*03

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,16,61,14,38,3,60,190,40,10,55,214,37,59,52,129,40,1*74

$GBGSV,6,3,24,9,52,345,35,25,51,9,40,1,48,125,37,34,47,89,40,1*4F

$GBGSV,6,4,24,11,46,126,39,2,45,237,35,60,41,239,40,41,37,251,40,1*40

$GBGSV,6,5,24,4,32,111,33,12,29,60,35,5,23,256,33,24,21,72,37,1*7E

$GBGSV,6,6,24,43,,,39,23,,,37,33,,,35,32,,,34,1*78

$GBGSV,2,1,06,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7D

$GBGSV,2,2,06,41,37,251,41,24,21,72,37,5*42

$GBRMC,150229.000,A,2301.2573792,N,11421.9449385,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,150229.000,1.565,0.203,0.214,0.310,1.279,1.224,2.394*7F



2025-07-31 23:02:30:306 ==>> $GBGGA,150230.000,2301.2573590,N,11421.9449459,E,1,13,0.99,84.302,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,03,16,25,59,34,11,01,60,41,2.12,0.99,1.88,4*0B

$GBGSA,A,3,24,,,,,,,,,,,,2.12,0.99,1.88,4*0E

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,36,60,42,239,40,43,39,165,39,1*79

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,5,23,256,33,1*49

$GBGSV,6,6,24,24,21,72,37,32,15,307,34,23,,,37,33,,,35,1*41

$GBGSV,2,1,06,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7D

$GBGSV,2,2,06,41,37,251,40,24,21,72,36,5*42

$GBRMC,150230.000,A,2301.2573590,N,11421.9449459,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150230.000,1.372,0.200,0.213,0.321,1.117,1.064,2.232*71



2025-07-31 23:02:30:336 ==>>                                          

2025-07-31 23:02:30:639 ==>> [D][05:19:27][CAT1]exec over: func id: 15, ret: -93
[D][05:19:27][CAT1]sub id: 15, ret: -93

[D][05:19:27][SAL ]Cellular task submsg id[68]
[D][05:19:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:27][SAL ]socket send fail. id[4]
[D][05:19:27][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:27][CAT1]gsm read msg sub id: 21
[D][05:19:27][M2M ]m2m select fd[4]
[D][05:19:27][M2M ]socket[4] Link is disconnected
[D][05:19:27][M2M ]tcpclient close[4]
[D][05:19:27][SAL ]socket[4] has closed
[D][05:19:27][PROT]protocol read data ok
[E][05:19:27][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:27][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:27][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:27][PROT]M2M Send Fail [1629955167]
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]cell info report total[0]
[D][05:19:27][CAT1]exec over: func id: 21, ret: 6
[D][05:19:27][CAT1]gsm read msg sub id: 13
[D][05:19:27][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:27][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:27][CAT1]exec over: func id: 13, ret: 21
[D][0

2025-07-31 23:02:30:684 ==>> 5:19:27][CAT1]gsm read msg sub id: 10
[D][05:19:27][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:27][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:27][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:02:31:018 ==>> [D][05:19:27][CAT1]<<< 
OK

[D][05:19:27][CAT1]exec over: func id: 10, ret: 6
[D][05:19:27][CAT1]sub id: 10, ret: 6

[D][05:19:27][SAL ]Cellular task submsg id[68]
[D][05:19:27][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:27][M2M ]m2m gsm shut done, ret[0]
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:27][SAL ]open socket ind id[4], rst[0]
[D][05:19:27][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:27][SAL ]Cellular task submsg id[8]
[D][05:19:27][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:27][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:27][CAT1]gsm read msg sub id: 8
[D][05:19:27][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:27][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:27][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:27][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:02:31:411 ==>> [D][05:19:27][CAT1]pdpdeact urc len[22]
$GBGGA,150231.000,2301.2573524,N,11421.9449380,E,1,18,0.74,84.301,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.51,0.74,1.31,4*0A

$GBGSA,A,3,60,43,41,12,24,32,,,,,,,1.51,0.74,1.31,4*0D

$GBGSV,6,1,24,40,72,179,42,7,67,207,39,39,63,43,40,6,61,10,36,1*7F

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,35,60,42,239,40,43,39,165,39,1*7A

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,37,1*76

$GBGSV,6,6,24,5,23,256,33,24,21,72,37,32,15,307,34,33,,,35,1*41

$GBGSV,2,1,08,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*73

$GBGSV,2,2,08,43,39,165,38,41,37,251,40,24,21,72,36,32,15,307,34,5*4E

$GBRMC,150231.000,A,2301.2573524,N,11421.9449380,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150231.000,2.179,0.173,0.180,0.268,1.677,1.643,2.662*7B



2025-07-31 23:02:32:301 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:02:32:316 ==>> $GBGGA,150232.000,2301.2573703,N,11421.9448976,E,1,19,0.71,84.360,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.47,0.71,1.29,4*01

$GBGSA,A,3,60,43,41,12,23,24,32,,,,,,1.47,0.71,1.29,4*07

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,39,16,61,14,38,10,55,214,37,9,52,345,35,1*44

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,35,60,42,239,40,43,39,165,39,1*7A

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,37,1*76

$GBGSV,6,6,24,5,23,256,33,24,21,72,36,32,15,307,34,33,,,35,1*40

$GBGSV,3,1,09,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*73

$GBGSV,3,2,09,43,39,165,38,41,37,251,40,23,28,306,36,24,21,72,36,5*43

$GBGSV,3,3,09,32,15,307,34,5*4D

$GBRMC,150232.000,A,2301.2573703,N,11421.9448976,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150232.000,2.453,0.199,0.212,0.314,1.839,1.814,2.774*7D



2025-07-31 23:02:33:559 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                     0261 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_ti

2025-07-31 23:02:33:664 ==>> ck:100015. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM

2025-07-31 23:02:33:769 ==>> ]msg 02AD loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100021. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100022. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100022. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100022. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100023. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100023. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100023. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100024. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100024
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100025
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100025
[D][05:19:29][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:29][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:29][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:29][

2025-07-31 23:02:33:874 ==>> CAT1]<<< 
OK

[D][05:19:29][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:19:29][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:29][COMM]Main Task receive event:14
[D][05:19:29][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955169, allstateRepSeconds = 0
[D][05:19:29][COMM]index:0,power_mode:0xFF
[D][05:19:29][COMM]index:1,sound_mode:0xFF
[D][05:19:29][COMM]index:2,gsensor_mode:0xFF
[D][05:19:29][COMM]index:3,report_freq_mode:0xFF
[D][05:19:29][COMM]index:4,report_period:0xFF
[D][05:19:29][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:29][COMM]index:6,normal_reset_period:0xFF
[D][05:19:29][COMM]index:7,spock_over_speed:0xFF
[D][05:19:29][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:29][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:29][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:29][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:29][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:29][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:29][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:29][COMM]index:15,bat_auth_mode:0xFF

2025-07-31 23:02:33:979 ==>> 
[D][05:19:29][COMM]index:16,imu_config_params:0xFF
[D][05:19:29][COMM]index:17,long_connect_params:0xFF
[D][05:19:29][COMM]index:18,detain_mark:0xFF
[D][05:19:29][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:29][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:29][COMM]index:21,mc_mode:0xFF
[D][05:19:29][COMM]index:22,S_mode:0xFF
[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]exec over: func id: 8, ret: 6
[D][05:19:29][COMM]index:23,overweight:0xFF
[D][05:19:29][COMM]index:24,standstill_mode:0xFF
[D][05:19:29][COMM]index:25,night_mode:0xFF
[D][05:19:29][COMM]index:26,experiment1:0xFF
[D][05:19:29][COMM]index:27,experiment2:0xFF
[D][05:19:29][COMM]index:28,experiment3:0xFF
[D][05:19:29][COMM]index:29,experiment4:0xFF
[D][05:19:29][COMM]index:30,night_mode_start:0xFF
[D][05:19:29][COMM]index:31,night_mode_end:0xFF
[D][05:19:29][COMM]index:33,park_report_minutes:0xFF
[D][05:19:29][COMM]index:34,park_report_mode:0xFF
[D][05:19:29][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:29][COMM]index:38,charge_battery_para: FF
[D][05:19:29][COMM]index:39,multirider_mode:0xFF
[D][05:19:29][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:29][COMM]index:41,head_l

2025-07-31 23:02:34:084 ==>> ight_enable_mode:0xFF
[D][05:19:29][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:29][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:29][COMM]index:44,riding_duration_config:0xFF
[D][05:19:29][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:29][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:29][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:29][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:29][COMM]index:49,mc_load_startup:0xFF
[D][05:19:29][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:29][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:29][COMM]index:52,traffic_mode:0xFF
[D][05:19:29][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:29][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:29][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:29][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:29][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:29][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:29][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:29][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:29][COMM]index:63,experiment5:0xFF
[D][05:19:29][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:29][COMM]i

2025-07-31 23:02:34:189 ==>> ndex:65,camera_park_fenceline_cfg:0xFF
[D][05:19:29][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:29][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:29][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:29][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:29][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:29][COMM]index:72,experiment6:0xFF
[D][05:19:29][COMM]index:73,experiment7:0xFF
[D][05:19:29][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:29][COMM]index:75,zero_value_from_server:-1
[D][05:19:29][COMM]index:76,multirider_threshold:255
[D][05:19:29][COMM]index:77,experiment8:255
[D][05:19:29][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:29][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:29][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:29][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:29][COMM]index:83,

2025-07-31 23:02:34:294 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:02:34:339 ==>>                                                                                                                                                                                                                                                                                           0,2.865*77



2025-07-31 23:02:34:383 ==>>                                          

2025-07-31 23:02:35:314 ==>> $GBGGA,150235.000,2301.2573939,N,11421.9447887,E,1,20,0.66,84.021,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.33,0.66,1.16,4*08

$GBGSA,A,3,60,43,41,12,23,33,24,32,,,,,1.33,0.66,1.16,4*0E

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,39,16,61,14,38,10,55,214,37,9,52,345,35,1*44

$GBGSV,6,3,24,25,51,9,40,59,49,130,41,34,47,89,40,11,46,126,39,1*41

$GBGSV,6,4,24,1,45,125,37,2,45,237,34,60,42,239,40,43,39,165,39,1*7B

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,37,1*76

$GBGSV,6,6,24,5,23,256,33,33,22,194,35,24,21,72,36,32,15,307,34,1*7C

$GBGSV,3,1,10,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7B

$GBGSV,3,2,10,43,39,165,38,41,37,251,40,23,28,306,38,33,22,194,34,5*7B

$GBGSV,3,3,10,24,21,72,36,32,15,307,34,5*40

$GBRMC,150235.000,A,2301.2573939,N,11421.9447887,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150235.000,2.571,0.176,0.180,0.269,1.886,1.874,2.749*75



2025-07-31 23:02:36:320 ==>> $GBGGA,150236.000,2301.2573997,N,11421.9447589,E,1,20,0.66,83.961,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.33,0.66,1.16,4*08

$GBGSA,A,3,60,43,41,12,23,33,24,32,,,,,1.33,0.66,1.16,4*0E

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,35,60,42,239,40,43,39,165,39,1*7A

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,37,1*76

$GBGSV,6,6,24,5,23,256,33,33,22,194,35,24,21,72,36,32,15,306,34,1*7D

$GBGSV,3,1,10,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7B

$GBGSV,3,2,10,43,39,165,39,41,37,251,40,23,28,306,38,33,22,194,34,5*7A

$GBGSV,3,3,10,24,21,72,36,32,15,306,34,5*41

$GBRMC,150236.000,A,2301.2573997,N,11421.9447589,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,150236.000,2.623,0.156,0.158,0.240,1.913,1.903,2.753*79



2025-07-31 23:02:36:380 ==>> [D][05:19:33][COMM]read battery soc:255


2025-07-31 23:02:37:331 ==>> $GBGGA,150237.000,2301.2574098,N,11421.9447174,E,1,20,0.66,83.890,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.33,0.66,1.16,4*08

$GBGSA,A,3,60,43,41,12,23,33,24,32,,,,,1.33,0.66,1.16,4*0E

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,35,60,42,239,40,43,39,165,39,1*7A

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,38,1*79

$GBGSV,6,6,24,5,23,256,33,33,22,194,35,24,21,72,36,32,15,306,34,1*7D

$GBGSV,3,1,10,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7B

$GBGSV,3,2,10,43,39,165,39,41,37,251,40,23,28,306,38,33,22,194,34,5*7A

$GBGSV,3,3,10,24,21,72,36,32,15,306,34,5*41

$GBRMC,150237.000,A,2301.2574098,N,11421.9447174,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150237.000,2.650,0.160,0.163,0.247,1.924,1.917,2.747*72



2025-07-31 23:02:38:335 ==>> 未匹配到【4G联网测试】数据,请核对检查!
2025-07-31 23:02:38:347 ==>> [D][05:19:34][PROT]CLEAN,SEND:0
[D][05:19:34][PROT]index:1 1629955174
[D][05:19:34][PROT]is_send:0
[D][05:19:34][PROT]sequence_num:13
[D][05:19:34][PROT]retry_timeout:0
[D][05:19:34][PROT]retry_times:1
[D][05:19:34][PROT]send_path:0x2
[D][05:19:34][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:34][PROT]===========================================================
[W][05:19:34][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955174]
[D][05:19:34][PROT]===========================================================
[D][05:19:34][PROT]sending traceid [999999999990000E]
[D][05:19:34][PROT]Send_TO_M2M [1629955174]
[D][05:19:34][PROT]CLEAN:0
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:34][SAL ]sock send credit cnt[6]
[D][05:19:34][SAL ]sock send ind credit cnt[6]
[D][05:19:34][M2M ]m2m send data len[166]
[D][05:19:34][SAL ]Cellular task submsg id[10]
[D][05:19:34][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:34][CAT1]gsm read msg sub id: 15
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:34][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:34][CAT1]Send Data To Server[166][169] ... ->:
00

2025-07-31 23:02:38:359 ==>> #################### 【测试结束】 ####################
2025-07-31 23:02:38:381 ==>> 关闭5V供电
2025-07-31 23:02:38:391 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:02:38:440 ==>> 53B984113311331133113311331B88BAC68F4B8D09F5FF9B9EA3E2D7ED4E2F637F1199DFF79E25B58C15274C052E5BC7485F98125208C544E0851E2A95AC601F1F736BE8F6955967228FB0D6BC92FF3DD235
[D][05:19:34][CAT1]<<< 
SEND OK

[D][05:19:34][CAT1]exec over: func id: 15, ret: 11
[D][05:19:34][CAT1]sub id: 15, ret: 11

[D][05:19:34][SAL ]Cellular task submsg id[68]
[D][05:19:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:34][M2M ]g_m2m_is_idle become true
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:34][PROT]M2M Send ok [1629955174]
$GBGGA,150238.000,2301.2574294,N,11421.9446798,E,1,20,0.66,83.836,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,01,1.33,0.66,1.16,4*08

$GBGSA,A,3,60,43,41,12,23,33,24,32,,,,,1.33,0.66,1.16,4*0E

$GBGSV,6,1,24,40,72,179,41,7,67,207,39,39,63,43,40,6,61,10,36,1*7C

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,1,45,125,37,2,45,237,34,60,42,239,40,43,39,165,39,1*7B

$GBGSV,6,5,24,41,37,251,40,4,32,111,33,12,29,60,35,23,28,306,37,1*76

$GBGSV,6,6,24,5,23,256,33,33

2025-07-31 23:02:38:515 ==>> ,22,194,35,24,21,72,36,32,15,306,34,1*7D

$GBGSV,3,1,10,40,72,179,41,39,63,43,41,25,51,9,41,34,47,89,41,5*78

$GBGSV,3,2,10,43,39,165,39,41,37,251,40,23,28,306,38,33,22,194,34,5*7A

$GBGSV,3,3,10,24,21,72,36,32,15,306,34,5*41

$GBRMC,150238.000,A,2301.2574294,N,11421.9446798,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,150238.000,2.542,0.207,0.211,0.315,1.854,1.848,2.667*71

5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
                                         

2025-07-31 23:02:39:339 ==>> $GBGGA,150239.000,2301.2574525,N,11421.9446513,E,1,21,0.65,83.745,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,02,1.32,0.65,1.15,4*0A

$GBGSA,A,3,01,60,43,41,12,23,33,24,32,,,,1.32,0.65,1.15,4*0E

$GBGSV,6,1,24,40,72,179,42,7,67,207,39,39,63,43,40,6,61,10,36,1*7F

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,2,46,237,35,1,45,125,37,60,42,239,40,43,39,165,39,1*79

$GBGSV,6,5,24,41,37,251,40,4,32,111,32,12,29,60,35,23,28,306,37,1*77

$GBGSV,6,6,24,5,23,256,33,33,22,194,35,24,21,72,37,32,15,306,34,1*7C

$GBGSV,3,1,10,40,72,179,41,39,63,43,41,25,51,9,41,34,47,89,40,5*79

$GBGSV,3,2,10,43,39,165,39,41,37,251,41,23,28,306,38,33,22,194,34,5*7B

$GBGSV,3,3,10,24,21,72,36,32,15,306,34,5*41

$GBRMC,150239.000,A,2301.2574525,N,11421.9446513,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150239.000,2.650,0.178,0.182,0.273,1.917,1.912,2.709*72



2025-07-31 23:02:39:384 ==>> 关闭5V供电成功
2025-07-31 23:02:39:397 ==>> 关闭33V供电
2025-07-31 23:02:39:424 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:02:39:519 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:02:39:624 ==>> [D][05:19:36][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry

2025-07-31 23:02:39:684 ==>>  i = 2,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:36][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 12


2025-07-31 23:02:40:335 ==>> $GBGGA,150240.000,2301.2574495,N,11421.9446215,E,1,21,0.65,83.610,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,03,16,10,25,59,34,11,02,1.32,0.65,1.15,4*0A

$GBGSA,A,3,01,60,43,41,12,23,33,24,32,,,,1.32,0.65,1.15,4*0E

$GBGSV,6,1,24,40,72,179,42,7,67,207,39,39,63,43,40,6,61,10,36,1*7F

$GBGSV,6,2,24,3,61,190,40,16,61,14,38,10,55,214,37,9,52,345,35,1*4A

$GBGSV,6,3,24,25,51,9,40,59,49,130,40,34,47,89,40,11,46,126,39,1*40

$GBGSV,6,4,24,2,46,237,35,1,45,125,37,60,42,239,40,43,39,165,39,1*79

$GBGSV,6,5,24,41,37,251,41,4,32,111,32,12,29,60,35,23,28,307,38,1*78

$GBGSV,6,6,24,5,23,256,34,33,22,194,35,24,21,72,36,32,15,306,34,1*7A

$GBGSV,3,1,10,40,72,179,41,39,63,43,42,25,51,9,41,34,47,89,41,5*7B

$GBGSV,3,2,10,43,39,165,39,41,37,251,40,23,28,307,38,33,22,194,34,5*7B

$GBGSV,3,3,10,24,21,72,36,32,15,306,34,5*41

$GBRMC,150240.000,A,2301.2574495,N,11421.9446215,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150240.000,2.574,0.149,0.152,0.229,1.868,1.864,2.650*7D



2025-07-31 23:02:40:395 ==>> 关闭33V供电成功
2025-07-31 23:02:40:410 ==>> 关闭3.7V供电
2025-07-31 23:02:40:423 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:02:40:515 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:02:40:961 ==>>  

