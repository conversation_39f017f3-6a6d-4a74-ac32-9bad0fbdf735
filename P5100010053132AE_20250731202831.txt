2025-07-31 20:28:31:452 ==>> MES查站成功:
查站序号:P5100010053132AE验证通过
2025-07-31 20:28:31:459 ==>> 扫码结果:P5100010053132AE
2025-07-31 20:28:31:461 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:28:31:463 ==>> 测试参数版本:2024.10.11
2025-07-31 20:28:31:464 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:28:31:466 ==>> 检测【打开透传】
2025-07-31 20:28:31:467 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:28:31:555 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:28:31:811 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:28:31:816 ==>> 检测【检测接地电压】
2025-07-31 20:28:31:818 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:28:31:955 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:28:32:094 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:28:32:097 ==>> 检测【打开小电池】
2025-07-31 20:28:32:100 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:28:32:151 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:28:32:390 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:28:32:392 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:28:32:396 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:28:32:454 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:28:32:691 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:28:32:693 ==>> 检测【等待设备启动】
2025-07-31 20:28:32:695 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:28:32:930 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:28:33:095 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 20:28:33:632 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:28:33:722 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:28:33:814 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 20:28:34:320 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:28:34:487 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti 

2025-07-31 20:28:34:762 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:28:35:006 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:28:35:189 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer s

2025-07-31 20:28:35:704 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:28:35:794 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:28:35:899 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:28:36:544 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:28:36:604 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:28:36:831 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:28:37:014 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:28:37:485 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:28:37:632 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:28:37:635 ==>> 检测【产品通信】
2025-07-31 20:28:37:636 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:28:37:834 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:28:37:912 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:28:37:914 ==>> 检测【初始化完成检测】
2025-07-31 20:28:37:917 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:28:38:179 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:28:38:477 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:28:38:480 ==>> 检测【关闭大灯控制1】
2025-07-31 20:28:38:495 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:28:38:509 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:38:720 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:28:38:754 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:28:38:756 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:28:38:757 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:28:38:945 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:28:39:024 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:28:39:027 ==>> 检测【关闭仪表供电】
2025-07-31 20:28:39:028 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:28:39:250 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:28:39:299 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:28:39:302 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:28:39:304 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:28:39:416 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:28:39:521 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:39:605 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:28:39:628 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:28:39:629 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:28:39:813 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:28:39:910 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:28:39:913 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:28:39:916 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:28:40:128 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:28:40:224 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:28:40:227 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:28:40:229 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:28:40:353 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:28:40:413 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 16


2025-07-31 20:28:40:518 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto i

2025-07-31 20:28:40:548 ==>> nit


2025-07-31 20:28:40:550 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:28:40:553 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:28:40:555 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:28:40:653 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:28:41:002 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:28:41:007 ==>> 该项需要延时执行
2025-07-31 20:28:41:063 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN m

2025-07-31 20:28:41:093 ==>> essage fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 20:28:41:558 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:42:017 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:28:42:507 ==>>                      m_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904

2025-07-31 20:28:42:612 ==>> ].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[

2025-07-31 20:28:42:717 ==>> D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get st

2025-07-31 20:28:42:777 ==>> ate1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 20:28:42:822 ==>>                                                                                                                                           

2025-07-31 20:28:43:571 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:44:509 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:28:44:599 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:45:007 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:28:45:010 ==>> 检测【33V输入电压ADC】
2025-07-31 20:28:45:013 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:28:45:358 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:17:58][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:58][COMM]adc read left brake adc:6  volt:7 mv
[D][05:17:58][COMM]adc read right brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read throttle adc:14  volt:18 mv
[D][05:17:58][COMM]adc read battery ts volt:11 mv
[D][05:17:58][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:17:58][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:28:45:567 ==>> 【33V输入电压ADC】通过,【32982mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:28:45:570 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:28:45:572 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:28:45:599 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:28:45:658 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1993mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:28:45:858 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:45:861 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:28:45:893 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:45:897 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:28:45:905 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:28:45:927 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:28:45:934 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:28:45:958 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10016
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10016


2025-07-31 20:28:45:962 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:28:45:966 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:28:46:002 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:28:46:006 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:28:46:153 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1994mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:28:46:310 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:46:312 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:28:46:349 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:46:367 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:28:46:370 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:28:46:388 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:28:46:391 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:28:46:407 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:28:46:410 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:28:46:432 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:28:46:434 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:28:46:563 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1992mV
Get AD_V7 1086mV
OVER 150


2025-07-31 20:28:46:709 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:46:713 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:28:46:730 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:46:745 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:28:46:748 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:28:46:751 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:28:46:754 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:28:46:771 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:28:46:775 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:28:46:798 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:28:46:802 ==>> 检测【打开WIFI(1)】
2025-07-31 20:28:46:805 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:28:46:809 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10718 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to

2025-07-31 20:28:46:848 ==>> : M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 20:28:47:365 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087738268

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539133

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QS

2025-07-31 20:28:47:395 ==>> CLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:28:47:607 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:28:47:610 ==>> 检测【清空消息队列(1)】
2025-07-31 20:28:47:612 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:28:47:614 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:28:47:848 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:28:47:882 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:28:47:886 ==>> 检测【打开GPS(1)】
2025-07-31 20:28:47:891 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:28:48:043 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 20:28:48:152 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:28:48:154 ==>> 检测【打开GSM联网】
2025-07-31 20:28:48:156 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:28:48:334 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:28:48:436 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:28:48:441 ==>> 检测【打开仪表供电1】
2025-07-31 20:28:48:443 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:28:48:446 ==>>                                [D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:28:48:530 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:28:48:634 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:28:48:721 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:28:48:724 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:28:48:729 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:28:48:952 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:28:49:021 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:28:49:024 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:28:49:028 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:28:49:327 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:02

2025-07-31 20:28:49:417 ==>> ][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][COMM]arm_hub read adc[3],val[33456]
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:28:49:573 ==>> 【读取主控ADC采集的仪表电压】通过,【33456mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:28:49:577 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:28:49:580 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:49:582 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:28:49:855 ==>>                                                                                                                                                                                                                                                                                                                           05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:28:50:102 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:28:50:107 ==>> 检测【AD_V20电压】
2025-07-31 20:28:50:111 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:50:132 ==>> [D][05:18:03][COMM]S->M yaw:INVALID


2025-07-31 20:28:50:203 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:50:447 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:50:552 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:28:50:612 ==>> 本次取值间隔时间:400ms
2025-07-31 20:28:50:869 ==>> 本次取值间隔时间:252ms
2025-07-31 20:28:51:216 ==>> [D][05:18:04][COMM]M->S yaw:INVALID
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:28:51:246 ==>> 本次取值间隔时间:371ms
2025-07-31 20:28:51:412 ==>> 本次取值间隔时间:159ms
2025-07-31 20:28:51:416 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:51:442 ==>> [D][05:18:04][COMM]S->M yaw:INVALID
[D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,42,33,,,42,25,,,41,24,,,40,1*71

$GBGSV,3,2,09,38,,,39,16,,,36,14,,,41,13,,,38,1*74

$GBGSV,3,3,09,7,,,36,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1658.285,1658.285,53.001,2097152,2097152,2097152*48

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 20:28:51:517 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:51:637 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:28:51:742 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:28:51:939 ==>> 本次取值间隔时间:419ms
2025-07-31 20:28:52:120 ==>> 本次取值间隔时间:177ms
2025-07-31 20:28:52:364 ==>> 本次取值间隔时间:237ms
2025-07-31 20:28:52:379 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,3,,,42,59,,,41,25,,,41,1*43

$GBGSV,5,2,18,24,,,41,60,,,41,39,,,40,1,,,39,1*4D

$GBGSV,5,3,18,38,,,38,14,,,37,13,,,37,16,,,37,1*7D

$GBGSV,5,4,18,44,,,35,2,,,35,7,,,33,5,,,33,1*4E

$GBGSV,5,5,18,40,,,39,42,,,37,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1585.783,1585.783,50.732,2097152,2097152,2097152*4C



2025-07-31 20:28:52:546 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:28:52:816 ==>> 本次取值间隔时间:450ms
2025-07-31 20:28:52:821 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:52:925 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:53:018 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:28:53:048 ==>> 00 00 00 00 00 
head err!


2025-07-31 20:28:53:183 ==>> 本次取值间隔时间:251ms
2025-07-31 20:28:53:383 ==>> 本次取值间隔时间:198ms
2025-07-31 20:28:53:398 ==>> [D][05:18:06][COMM]M->S yaw:INVALID
$GBGGA,122857.194,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,43,3,,,41,59,,,41,25,,,41,1*4A

$GBGSV,5,2,20,24,,,41,60,,,41,42,,,40,39,,,39,1*71

$GBGSV,5,3,20,1,,,39,14,,,39,38,,,38,13,,,37,1*40

$GBGSV,5,4,20,16,,,37,44,,,36,2,,,36,40,,,35,1*46

$GBGSV,5,5,20,7,,,35,34,,,34,5,,,33,4,,,31,1*46

$GBRMC,122857.194,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122857.194,0.000,1569.207,1569.207,50.209,2097152,2097152,2097152*58



2025-07-31 20:28:53:793 ==>> $GBGGA,122857.594,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,43,3,,,42,59,,,41,25,,,41,1*49

$GBGSV,5,2,20,24,,,41,60,,,41,42,,,40,14,,,40,1*70

$GBGSV,5,3,20,39,,,39,1,,,39,38,,,38,13,,,37,1*4F

$GBGSV,5,4,20,16,,,37,44,,,36,2,,,36,40,,,36,1*45

$GBGSV,5,5,20,7,,,35,34,,,34,5,,,33,4,,,32,1*45

$GBRMC,122857.594,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122857.594,0.000,1577.495,1577.495,50.471,2097152,2097152,2097152*55



2025-07-31 20:28:53:808 ==>> 本次取值间隔时间:416ms
2025-07-31 20:28:54:247 ==>> 本次取值间隔时间:430ms
2025-07-31 20:28:54:251 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:54:355 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:54:445 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:28:54:490 ==>> 本次取值间隔时间:134ms
2025-07-31 20:28:54:551 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:28:54:569 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:54:670 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:54:834 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
$GBGGA,122858.574,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,24,,,42,3,,,41,59,,,41,1*4F

$GBGSV,6,2,24,25,,,41,60,,,41,14,,,41,42,,,40,1*77

$GBGSV,6,3,24,39,,,40,1,,,38,16,,,38,38,,,37,1*42

$GBGSV,6,4,24,13,,,37,40,,,37,2,,,36,7,,,36,1*71

$GBGSV,6,5,24,9,,,36,44,,,35,34,,,34,5,,,33,1*7C

$GBGSV,6,6,24,26,,,32,4,,,32,10,,,32,6,,,16,1*71

$GBRMC,122858.574,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122858.574,0.000,1518.539,1518.539,48.704,2097152,2097152,2097152*5C

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:55:059 ==>> 本次取值间隔时间:388ms
2025-07-31 20:28:55:106 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:55:213 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:55:274 ==>> 本次取值间隔时间:51ms
2025-07-31 20:28:55:439 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:55:725 ==>> 本次取值间隔时间:441ms
2025-07-31 20:28:55:785 ==>> [D][05:18:08][COMM]S->M yaw:INVALID
$GBGGA,122859.554,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,59,,,41,25,,,41,1*78

$GBGSV,7,2,26,60,,,41,14,,,41,3,,,40,42,,,40,1*41

$GBGSV,7,3,26,39,,,39,1,,,38,16,,,38,38,,,37,1*4F

$GBGSV,7,4,26,13,,,37,40,,,37,41,,,37,2,,,36,1*41

$GBGSV,7,5,26,7,,,36,9,,,36,8,,,36,44,,,35,1*45

$GBGSV,7,6,26,6,,,35,34,,,33,26,,,33,5,,,32,1*74

$GBGSV,7,7,26,4,,,32,10,,,32,1*47

$GBRMC,122859.554,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122859.554,0.000,1543.541,1543.541,49.395,2097152,2097152,2097152*52



2025-07-31 20:28:55:997 ==>> 本次取值间隔时间:268ms
2025-07-31 20:28:56:316 ==>> 本次取值间隔时间:312ms
2025-07-31 20:28:56:320 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:56:426 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:56:566 ==>> [W][05:18:09][COMM]>>>>>Input command = ?<<<<<
[D][05:18:09][COMM]read battery soc:255
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:28:56:611 ==>> 本次取值间隔时间:175ms
2025-07-31 20:28:56:634 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:28:56:746 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:28:56:761 ==>> $GBGGA,122900.534,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,59,,,41,25,,,41,1*7B

$GBGSV,7,2,26,60,,,41,14,,,41,3,,,41,42,,,40,1*40

$GBGSV,7,3,26,39,,,39,1,,,39,16,,,38,38,,,37,1*4E

$GBGSV,7,4,26,13,,,37,40,,,37,41,,,37,2,,,36,1*41

$GBGSV,7,5,26,7,,,36,9,,,36,8,,,36,6,,,36,1*70

$GBGSV,7,6,26,44,,,35,26,,,34,34,,,33,4,,,33,1*45

$GBGSV,7,7,26,10,,,33,5,,,32,1*47

$GBRMC,122900.534,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122900.534,0.000,1551.504,1551.504,49.639,2097152,2097152,2097152*5A



2025-07-31 20:28:56:866 ==>> 1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:09][COMM]oneline display ALL on 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:57:001 ==>> 本次取值间隔时间:254ms
2025-07-31 20:28:57:020 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:28:57:023 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:28:57:025 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:28:57:091 ==>> [D][05:18:10][COMM]M->S yaw:INVALID


2025-07-31 20:28:57:151 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:28:57:295 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:28:57:298 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:28:57:303 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:28:57:439 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:57:754 ==>> $GBGGA,122901.514,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,41,25,,,41,14,,,41,1*72

$GBGSV,7,2,26,3,,,41,59,,,40,60,,,40,42,,,40,1*49

$GBGSV,7,3,26,39,,,39,1,,,39,16,,,38,38,,,37,1*4E

$GBGSV,7,4,26,13,,,37,40,,,37,41,,,37,2,,,36,1*41

$GBGSV,7,5,26,7,,,36,9,,,36,6,,,36,8,,,35,1*73

$GBGSV,7,6,26,44,,,35,26,,,34,34,,,33,4,,,33,1*45

$GBGSV,7,7,26,10,,,33,5,,,32,1*47

$GBRMC,122901.514,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122901.514,0.000,771.915,771.915,705.934,2097152,2097152,2097152*64



2025-07-31 20:28:58:336 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:28:58:445 ==>> [D][05:18:11][COMM]S->M yaw:INVALID


2025-07-31 20:28:58:550 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:11][COMM]oneline display read state:1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:11][COMM]read battery soc:255


2025-07-31 20:28:58:746 ==>> $GBGGA,122902.514,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,7,2,27,24,,,41,14,,,41,25,,,41,42,,,40,1*75

$GBGSV,7,3,27,1,,,39,39,,,39,38,,,38,16,,,38,1*40

$GBGSV,7,4,27,13,,,37,40,,,37,41,,,37,2,,,36,1*40

$GBGSV,7,5,27,7,,,36,9,,,36,44,,,36,6,,,36,1*49

$GBGSV,7,6,27,8,,,35,26,,,34,10,,,34,4,,,33,1*7D

$GBGSV,7,7,27,34,,,33,5,,,32,12,,,27,1*46

$GBRMC,122902.514,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122902.514,0.000,767.107,767.107,701.539,2097152,2097152,2097152*62



2025-07-31 20:28:59:376 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:28:59:577 ==>> [D][05:18:12][COMM]M->S yaw:INVALID
[D][05:18:12][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:12][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:12][COMM]oneline display read state:1
[D][05:18:12][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:28:59:681 ==>> $GBGGA,122903.514,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,14,,,41,25,,,41,42,,,40,1*75

$GBGSV,7,3,27,1,,,3

2025-07-31 20:28:59:741 ==>> 9,39,,,39,16,,,38,13,,,37,1*46

$GBGSV,7,4,27,38,,,37,40,,,37,41,,,37,2,,,36,1*49

$GBGSV,7,5,27,7,,,36,9,,,36,44,,,36,6,,,36,1*49

$GBGSV,7,6,27,8,,,35,26,,,34,10,,,34,5,,,33,1*7C

$GBGSV,7,7,27,4,,,33,34,,,33,12,,,28,1*49

$GBRMC,122903.514,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122903.514,0.000,768.635,768.635,702.936,2097152,2097152,2097152*63



2025-07-31 20:29:00:413 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:29:00:417 ==>> #################### 【测试结束】 ####################
2025-07-31 20:29:00:474 ==>> 关闭5V供电
2025-07-31 20:29:00:479 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:29:00:568 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
[D][05:18:13][COMM]read battery soc:255


2025-07-31 20:29:00:673 ==>> $GBGGA,122904.514,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,41,

2025-07-31 20:29:00:734 ==>> 3,,,41,24,,,41,1*45

$GBGSV,7,2,27,14,,,41,25,,,41,59,,,40,42,,,40,1*7E

$GBGSV,7,3,27,1,,,39,39,,,39,16,,,38,13,,,37,1*46

$GBGSV,7,4,27,38,,,37,40,,,37,41,,,37,2,,,36,1*49

$GBGSV,7,5,27,7,,,36,44,,,36,9,,,36,6,,,36,1*49

$GBGSV,7,6,27,8,,,35,26,,,34,10,,,34,5,,,33,1*7C

$GBGSV,7,7,27,4,,,33,34,,,33,12,,,28,1*49

$GBRMC,122904.514,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122904.514,0.000,767.097,767.097,701.530,2097152,2097152,2097152*6D



2025-07-31 20:29:01:481 ==>> 关闭5V供电成功
2025-07-31 20:29:01:487 ==>> 关闭33V供电
2025-07-31 20:29:01:492 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:29:01:543 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:29:01:828 ==>> [D][05:18:14][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 8,volt = 11
$GBGGA,122905.514,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,41,3,,,41,24,,,41,1*45

$GBGSV,7,2,27,14,,,41,25,,,41,59,,,40,42,,,40,1*7E

$GBGSV,7,3,27,39,,,39,1,,,38,16,,,38,13,,,37,1*47

$GBGSV,7,4,27,38,,,37,40,,,37,41,,,37,2,,,36,1*49

$GBGSV,7,5,27,7,,,36,9,,,36,6,,,36,26,,,35,1*4E

$GBGSV,7,6,27,8,,,35,44,,,35,10,,,34,4,,,34,1*7F

$GBGSV,7,7,27,5,,,33,34,,,33,12,,,29,1*49

$GBRMC,122905.514,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122905.514,0.000,767.855,767.855,702.222,2097152,2097152,2097152*6B



2025-07-31 20:29:01:934 ==>> [D][05:18:15][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:29:02:485 ==>> 关闭33V供电成功
2025-07-31 20:29:02:491 ==>> 关闭3.7V供电
2025-07-31 20:29:02:496 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:29:02:545 ==>> 6A A6 02 A6 6A 


2025-07-31 20:29:02:650 ==>> Battery OFF
OVER 150


2025-07-31 20:29:02:755 ==>> $GBGGA,122906.514,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,27,14,,,41,25,,,41,59,,,40,42,,,40,1*7E

$GBGSV,7,3,27,39,,,39,1,,,38,16,,,38,13,,,37,1*47

$GBGSV,7,4,27,38,,,37,40,,,37,41,,,37,2,,,36,1*49

$GBGSV,7,5,27,7,,,36,9,,,36,6,,,36,26,,,35,1*4E

$GBGSV,7,6,27,8,,,35,44,,,35,10,,,34,4,,,34,1*7F

$GBGSV,7,7,27,5,,,33,34,,,33,12,,,29,1*49

$GBRMC,122906.514,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122906.514,0.000,768.624,768.624,702.926,2097152,2097152,2097152*67



2025-07-31 20:29:03:182 ==>>  

