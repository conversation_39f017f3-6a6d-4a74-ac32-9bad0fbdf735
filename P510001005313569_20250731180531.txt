2025-07-31 18:05:31:196 ==>> MES查站成功:
查站序号:P510001005313569验证通过
2025-07-31 18:05:31:200 ==>> 扫码结果:P510001005313569
2025-07-31 18:05:31:202 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:05:31:204 ==>> 测试参数版本:2024.10.11
2025-07-31 18:05:31:205 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:05:31:207 ==>> 检测【打开透传】
2025-07-31 18:05:31:209 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:05:31:254 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:05:31:549 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:05:31:553 ==>> 检测【检测接地电压】
2025-07-31 18:05:31:555 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:05:31:655 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:05:31:823 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:05:31:826 ==>> 检测【打开小电池】
2025-07-31 18:05:31:829 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:05:31:961 ==>> 6A A6 01 A6 6A 


2025-07-31 18:05:32:051 ==>> Battery ON
OVER 150


2025-07-31 18:05:32:158 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:05:32:161 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:05:32:163 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:05:32:261 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:05:32:467 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:05:32:470 ==>> 检测【等待设备启动】
2025-07-31 18:05:32:473 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:05:32:833 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:05:33:028 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:05:33:507 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:05:33:706 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:05:34:091 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:05:34:550 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:05:34:566 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:05:34:824 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:05:34:826 ==>> 检测【产品通信】
2025-07-31 18:05:34:827 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:05:35:036 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 18:05:35:102 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:05:35:104 ==>> 检测【初始化完成检测】
2025-07-31 18:05:35:107 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:05:35:296 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:05:35:376 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:05:35:378 ==>> 检测【关闭大灯控制1】
2025-07-31 18:05:35:380 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:05:35:521 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:05:35:660 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:05:35:662 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:05:35:665 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:05:35:778 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:05:35:883 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:05:35:932 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:05:35:935 ==>> 检测【关闭仪表供电】
2025-07-31 18:05:35:937 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:05:36:157 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:05:36:207 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:05:36:209 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:05:36:211 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:05:36:431 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:05:36:485 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:05:36:487 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:05:36:490 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:05:36:661 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:36:765 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:05:36:768 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:05:36:771 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:05:36:920 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:05:37:093 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:05:37:096 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:05:37:098 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:05:37:162 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:05:37:237 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26
[D][05:17:53][COMM]read battery soc:255


2025-07-31 18:05:37:421 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:05:37:424 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:05:37:427 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:05:37:558 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:05:37:663 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:37:729 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:05:37:732 ==>> 该项需要延时执行
2025-07-31 18:05:38:213 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5017. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5018. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5018. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5018. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5019. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5019. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5019. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5020. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5020. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5020. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x

2025-07-31 18:05:38:243 ==>> 0008F00C71E22217 5021
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5021


2025-07-31 18:05:38:707 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:38:812 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D]

2025-07-31 18:05:38:842 ==>> [05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:05:39:335 ==>>                                                                                                                   ----------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready


2025-07-31 18:05:39:441 ==>> 
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]

2025-07-31 18:05:39:545 ==>> :type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W]

2025-07-31 18:05:39:605 ==>> [05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 18:05:39:695 ==>> [D][05:17:55][COMM]6675 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:39:770 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 18:05:40:718 ==>> [D][05:17:56][COMM]7686 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:41:259 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:05:41:720 ==>> [D][05:17:57][COMM]8696 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:41:735 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:05:41:738 ==>> 检测【33V输入电压ADC】
2025-07-31 18:05:41:740 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:05:42:073 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3105  volt:5458 mv
[D][05:17:57][COMM]adc read out 24v adc:1295  volt:32754 mv
[D][05:17:57][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read right brake adc:2  volt:2 mv
[D][05:17:57][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:57][COMM]adc read battery ts volt:7 mv
[D][05:17:57][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:05:42:330 ==>> 【33V输入电压ADC】通过,【32476mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:05:42:332 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:05:42:335 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:05:42:468 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:05:42:636 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:42:638 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:05:42:676 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:42:679 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:05:42:682 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 18:05:42:712 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:05:42:718 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:05:42:740 ==>> [D][05:17:58][COMM]9708 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:05:42:767 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:05:42:771 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:05:42:815 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:05:42:818 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:05:42:971 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2761mV
Get AD_V6 1989mV
Get AD_V7 1096mV
OVER 150


2025-07-31 18:05:43:076 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10009
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10010


2025-07-31 18:05:43:121 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:43:124 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:05:43:212 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:43:215 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:05:43:217 ==>> 原始值:【2761】, 乘以分压基数【2】还原值:【5522】
2025-07-31 18:05:43:254 ==>> 【TP68_VCC5V5(ADV5)】通过,【5522mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:05:43:257 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:05:43:275 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:05:43:343 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:05:43:346 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:05:43:388 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:05:43:391 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:05:43:470 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 18:05:43:696 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:43:701 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:05:43:736 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:43:739 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:05:43:743 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 18:05:43:783 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:05:43:803 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:05:43:806 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:05:43:808 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:05:43:830 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:05:43:833 ==>> 检测【打开WIFI(1)】
2025-07-31 18:05:43:834 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:05:43:954 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10721 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,

2025-07-31 18:05:43:999 ==>> 1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:05:44:422 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087872109

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290675

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:05:44:631 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:05:44:633 ==>> 检测【清空消息队列(1)】
2025-07-31 18:05:44:635 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:05:44:759 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:05:44:849 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:05:44:915 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:05:44:918 ==>> 检测【打开GPS(1)】
2025-07-31 18:05:44:921 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:05:45:166 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 18:05:45:271 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 18:05:45:440 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:05:45:443 ==>> 检测【打开GSM联网】
2025-07-31 18:05:45:445 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:05:45:637 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 18:05:45:715 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:05:45:718 ==>> 检测【打开仪表供电1】
2025-07-31 18:05:45:722 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:05:45:956 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:05:45:990 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:05:45:993 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:05:45:996 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:05:46:151 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:05:46:270 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:05:46:274 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:05:46:276 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:05:46:438 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33340]


2025-07-31 18:05:46:553 ==>> 【读取主控ADC采集的仪表电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:05:46:556 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:05:46:560 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:05:46:771 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13734 imu init OK


2025-07-31 18:05:46:835 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:05:46:838 ==>> 检测【AD_V20电压】
2025-07-31 18:05:46:840 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:05:46:922 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 18:05:46:937 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:05:47:027 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:

2025-07-31 18:05:47:057 ==>> 0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 18:05:47:102 ==>> 本次取值间隔时间:157ms
2025-07-31 18:05:47:147 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:05:47:185 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:47:188 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:05:47:191 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:05:47:252 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150
[D][05:18:03][COMM]read 

2025-07-31 18:05:47:282 ==>> battery soc:255


2025-07-31 18:05:47:756 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 18:05:47:889 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:05:47:892 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:05:47:896 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:05:48:137 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        :18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.134.3.127"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]

2025-07-31 18:05:48:242 ==>> <<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:05:48:272 ==>>  

2025-07-31 18:05:48:806 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:05:48:896 ==>> [D][05:18:04][COMM]IMU: [6,5,-983] ret=21 AWAKE!


2025-07-31 18:05:49:322 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 18:05:49:458 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:05:49:654 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,45,26,,,43,21,,,42,38,,,40,1*79

$GBGSV,3,2,09,42,,,40,39,,,39,24,,,20,13,,,40,1*7E

$GBGSV,3,3,09,9,,,38,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1593.425,1593.425,51.206,2097152,2097152,2097152*4F

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 18:05:49:819 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:05:49:924 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:05:49:929 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:05:49:957 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:05:50:048 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:05:50:204 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:05:50:207 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:05:50:212 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:05:50:351 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:05:50:481 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:05:50:485 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:05:50:501 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:05:50:591 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,26,,,42,59,,,41,21,,,41,38,,,41,1*75

$GBGSV,4,2,14,24,,,41,42,,,40,8,,,40,39,,,39,1*48

$GBGSV,4,3,14,16,,,38,14,,,38,13,,,37,3,,,37,1*47

$GBGSV,4,4,14,9,,,35,2,,,36,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1626.392,1626.392,51.980,2097152,2097152,2097152*4A



2025-07-31 18:05:50:696 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:05:50:761 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:05:50:764 ==>> 检测【AD_V21电压】
2025-07-31 18:05:50:766 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:05:50:862 ==>> 1A A1 20 00 00 
Get AD_V21 1046mV
OVER 150


2025-07-31 18:05:50:952 ==>> 本次取值间隔时间:181ms
2025-07-31 18:05:50:970 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:05:51:060 ==>> 1A A1 20 00 00 
Get AD_V21 1046mV
OVER 150


2025-07-31 18:05:51:245 ==>> 本次取值间隔时间:265ms
2025-07-31 18:05:51:263 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:05:51:321 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:05:51:351 ==>> 1A A1 20 00 00 
Get AD_V21 1642mV
OVER 150


2025-07-31 18:05:51:381 ==>> 本次取值间隔时间:105ms
2025-07-31 18:05:51:401 ==>> 【AD_V21电压】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:05:51:404 ==>> 检测【关闭仪表供电2】
2025-07-31 18:05:51:406 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:05:51:624 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,26,,,42,24,,,42,21,,,41,38,,,41,1*71

$GBGSV,5,2,18,42,,,41,59,,,40,8,,,40,39,,,40,1*41

$GBGSV,5,3,18,16,,,39,13,,,39,3,,,38,1,,,38,1*7E

$GBGSV,5,4,18,14,,,37,9,,,36,2,,,35,4,,,35,1*45

$GBGSV,5,5,18,5,,,33,33,,,36,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1602.223,1602.223,51.237,2097152,2097152,2097152*4D



2025-07-31 18:05:51:672 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:05:51:677 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:05:51:681 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:05:51:849 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:05:51:943 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:05:51:946 ==>> 检测【打开AccKey2供电】
2025-07-31 18:05:51:950 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:05:52:111 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:05:52:220 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:05:52:224 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:05:52:226 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:05:52:567 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3106  volt:5459 mv
[D][05:18:08][COMM]adc read out 24v adc:1295  volt:32754 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:08][COMM]adc read battery ts volt:4 mv
[D][05:18:08][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:05:52:642 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 18:05:52:808 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32754mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:05:52:812 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:05:52:814 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:05:53:024 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:05:53:121 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:05:53:125 ==>> 该项需要延时执行
2025-07-31 18:05:53:331 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:05:53:620 ==>> $GBGGA,100557.412,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,26,,,42,38,,,42,21,,,41,1*78

$GBGSV,6,2,23,42,,,41,60,,,41,13,,,41,59,,,40,1*7C

$GBGSV,6,3,23,8,,,40,39,,,40,16,,,39,3,,,39,1*74

$GBGSV,6,4,23,1,,,38,14,,,37,9,,,37,6,,,37,1*41

$GBGSV,6,5,23,33,,,35,2,,,35,4,,,34,5,,,33,1*40

$GBGSV,6,6,23,7,,,29,44,,,36,45,,,36,1*4A

$GBRMC,100557.412,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100557.412,0.000,1587.286,1587.286,50.801,2097152,2097152,2097152*5C



2025-07-31 18:05:54:616 ==>> $GBGGA,100558.392,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,26,,,42,38,,,42,21,,,41,1*7A

$GBGSV,6,2,21,42,,,41,60,,,41,13,,,41,59,,,40,1*7E

$GBGSV,6,3,21,8,,,40,39,,,40,3,,,40,16,,,39,1*78

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,6,,,37,1*43

$GBGSV,6,5,21,2,,,36,33,,,35,4,,,34,5,,,33,1*41

$GBGSV,6,6,21,7,,,29,1*49

$GBRMC,100558.392,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100558.392,0.000,1591.233,1591.233,50.926,2097152,2097152,2097152*58



2025-07-31 18:05:54:796 ==>> $GBGGA,100558.592,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,26,,,42,38,,,42,21,,,42,1*79

$GBGSV,6,2,21,42,,,41,60,,,41,13,,,41,59,,,40,1*7E

$GBGSV,6,3,21,8,,,40,39,,,40,3,,,40,16,,,39,1*78

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,6,,,37,1*43

$GBGSV,6,5,21,2,,,36,33,,,35,4,,,34,5,,,33,1*41

$GBGSV,6,6,21,7,,,29,1*49

$GBRMC,100558.592,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100558.592,0.000,1593.209,1593.209,50.991,2097152,2097152,2097152*52



2025-07-31 18:05:55:339 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:05:55:780 ==>> $GBGGA,100559.572,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,26,,,42,38,,,42,21,,,42,1*79

$GBGSV,6,2,21,42,,,41,60,,,41,13,,,41,59,,,40,1*7E

$GBGSV,6,3,21,8,,,40,39,,,40,3,,,40,16,,,39,1*78

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,6,,,37,1*43

$GBGSV,6,5,21,2,,,36,33,,,35,4,,,34,5,,,33,1*41

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,100559.572,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100559.572,0.000,1595.177,1595.177,51.048,2097152,2097152,2097152*51



2025-07-31 18:05:56:133 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:05:56:138 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:05:56:151 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:05:56:469 ==>> [D][05:18:12][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3113  volt:5472 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:12 mv
[D][05:18:12][COMM]adc read in 24v adc:1272  volt:32172 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:05:56:674 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:05:56:679 ==>> 检测【打开AccKey1供电】
2025-07-31 18:05:56:682 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:05:56:756 ==>> $GBGGA,100600.552,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,26,,,42,38,,,42,21,,,41,1*7A

$GBGSV,6,2,21,42,,,41,60,,,41,13,,,41,59,,,41,1*7F

$GBGSV,6,3,21,3,,,41,8,,,40,39,,,40,16,,,39,1*79

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,6,,,37,1*43

$GBGSV,6,5,21,2,,,36,33,,,35,4,,,34,5,,,33,1*41

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,100600.552,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100600.552,0.000,1597.152,1597.152,51.112,2097152,2097152,2097152*52



2025-07-31 18:05:56:846 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:05:56:967 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:05:56:972 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:05:56:977 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:05:57:060 ==>> 1A A1 00 40 00 
Get AD_V14 2651mV
OVER 150


2025-07-31 18:05:57:226 ==>> 原始值:【2651】, 乘以分压基数【2】还原值:【5302】
2025-07-31 18:05:57:268 ==>> 【读取AccKey1电压(ADV14)前】通过,【5302mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:05:57:272 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:05:57:275 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:05:57:333 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 18:05:57:559 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3106  volt:5459 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:7 mv
[D][05:18:13][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:05:57:754 ==>> $GBGGA,100601.532,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,21,,,42,26,,,41,1*7A

$GBGSV,6,2,21,42,,,41,60,,,41,13,,,41,3,,,41,1*40

$GBGSV,6,3,21,59,,,40,8,,,40,39,,,40,16,,,39,1*47

$GBGSV,6,4,21,1,,,38,9,,,38,14,,,37,6,,,37,1*4C

$GBGSV,6,5,21,2,,,36,33,,,35,4,,,34,5,,,34,1*46

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,100601.532,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100601.532,0.000,798.073,798.073,729.855,2097152,2097152,2097152*67



2025-07-31 18:05:57:832 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5459mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:05:57:836 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:05:57:839 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:05:58:045 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:05:58:111 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:05:58:115 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:05:58:118 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:05:58:258 ==>> 1A A1 00 40 00 
Get AD_V14 2649mV
OVER 150


2025-07-31 18:05:58:363 ==>> 原始值:【2649】, 乘以分压基数【2】还原值:【5298】
2025-07-31 18:05:58:383 ==>> 【读取AccKey1电压(ADV14)后】通过,【5298mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:05:58:387 ==>> 检测【打开WIFI(2)】
2025-07-31 18:05:58:390 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:05:58:578 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:05:58:658 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:05:58:666 ==>> 检测【转刹把供电】
2025-07-31 18:05:58:672 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:05:58:684 ==>> $GBGGA,100602.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,21,,,42,26,,,42,1*7A

$GBGSV,6,2,22,13,,,41,3,,,41,59,,,41,42,,,41,1*49

$GBGSV,6,3,22,60,,,40,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,14,,,37,1*4F

$GBGSV,6,5,22,2,,,36,33,,,35,4,,,34,5,,,33,1*42

$GBGSV,6,6,22,45,,,33,7,,,31,1*42

$GBR

2025-07-31 18:05:58:713 ==>> MC,100602.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100602.512,0.000,793.781,793.781,725.930,2097152,2097152,2097152*68



2025-07-31 18:05:58:818 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:05:58:942 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:05:58:948 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:05:58:971 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:05:59:047 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:05:59:155 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2396mV
OVER 150


2025-07-31 18:05:59:200 ==>> 原始值:【2396】, 乘以分压基数【2】还原值:【4792】
2025-07-31 18:05:59:219 ==>> 【读取AD_V15电压(前)】通过,【4792mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:05:59:223 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:05:59:228 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:05:59:322 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:05:59:353 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 18:05:59:428 ==>>                      297A3,-71
+WIFISCAN:4,1,CC057790A7C1,-79
+WIFISCAN:4,2,CC057790A7C0,-79
+WIFISCAN:4,3,44A1917CAD81,-84

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 18:05:59:518 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:05:59:744 ==>> $GBGGA,100603.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,13,,,41,1*78

$GBGSV,6,2,22,3,,,41,59,,,41,21,,,41,42,,,41,1*48

$GBGSV,6,3,22,8,,,40,60,,,40,39,,,40,16,,,39,1*4E

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100603.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100603.512,0.000,790.964,790.964,723.355,2097152,2097152,2097152*66



2025-07-31 18:05:59:879 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 18:06:00:262 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:06:00:367 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:06:00:413 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:06:00:458 ==>> 1A A1 01 00 00 
Get AD_V16 2431mV
OVER 150


2025-07-31 18:06:00:518 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 18:06:00:553 ==>> 【读取AD_V16电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:06:00:556 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:06:00:559 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:06:00:881 ==>> $GBGGA,100604.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,21,,,42,26,,,42,1*7A

$GBGSV,6,2,22,13,,,41,60,,,41,3,,,41,59,,,41,1*49

$GBGSV,6,3,22,42,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,14,,,37,1*4F

$GBGSV,6,5,22,2,,,36,4,,,35,33,,,35,5,,,34,1*44

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100604.512,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100604.512,0.000,794.726,794.726,726.795,2097152,2097152,2097152*6C

[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3110  volt:5466 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3063  volt:5384 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:1

2025-07-31 18:06:00:911 ==>> 6][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:06:01:081 ==>> 【转刹把供电电压(主控ADC)】通过,【5384mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:06:01:085 ==>> 检测【转刹把供电电压】
2025-07-31 18:06:01:090 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:06:01:369 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3115  volt:5475 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:17][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:17][COMM]adc read battery ts volt:0 mv
[D][05:18:17][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3059  volt:5377 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:06:01:399 ==>>                                          

2025-07-31 18:06:01:616 ==>> 【转刹把供电电压】通过,【5377mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:06:01:620 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:06:01:625 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:06:01:736 ==>> $GBGGA,100605.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,21,,,42,26,,,42,1*7A

$GBGSV,6,2,22,13,,,41,60,,,41,3,,,41,59,,,41,1*49

$GBGSV,6,3,22,42,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,4,,,35,33,,,35,5,,,34,1*44

$GBGSV,6,6,22,45,,,32,7,,,31,1*43

$GBRMC,100605.512,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100605.512,0.000,794.721,794.721,726.790,2097152,2097152,2097152*68



2025-07-31 18:06:01:826 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:06:01:976 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:06:01:979 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:06:01:984 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:06:02:085 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:06:02:116 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:06:02:162 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 18:06:02:241 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:06:02:244 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:06:02:249 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:06:02:357 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:06:02:417 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:06:02:463 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:06:02:468 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:06:02:568 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:06:02:661 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:06:02:713 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:06:02:719 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:06:02:726 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:06:02:750 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,100606.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,13,,,41,60,,,41,1*79

$GBGSV,6,2,22,3,,,41,21,,,41,26,,,41,42,,,41,1*40

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,2,,,37,9,,,37,6,,,37,1*77

$GBGSV,6,5,22,14,,,37,33,,,35,5,,,34,4,,,34,1*73

$GBGSV,6,6,22,45,,,32,7,,,31,1*43

$GBRMC,100606.512,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100606.512,0.000,791.896,791.896,724.206,2097152,2097152,2097152*63



2025-07-31 18:06:02:855 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:06:02:994 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:06:02:998 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:06:03:003 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:06:03:053 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:06:03:269 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:06:03:274 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:06:03:279 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:06:03:359 ==>> [D][05:18:19][COMM]read battery soc:255
3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:06:03:545 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:06:03:552 ==>> 检测【左刹电压测试1】
2025-07-31 18:06:03:560 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:06:03:874 ==>> $GBGGA,100607.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,13,,,41,60,,,41,1*79

$GBGSV,6,2,22,3,,,41,21,,,41,26,,,41,42,,,41,1*40

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,31,1*43

$GBRMC,100607.512,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100607.512,0.000,790.956,790.956,723.347,2097152,2097152,2097152*61

[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3099  volt:5447 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1718  volt:2264 mv
[D][05:18:19][COMM]adc read right brake adc:1709  volt:2253 mv
[D][05:18:19][COMM]adc read throttle adc:1707  volt:2250 mv
[D][05:18:19][COMM]adc read battery ts volt:14 mv
[D][05:18:19][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:24

2025-07-31 18:06:03:919 ==>> 79  volt:3994 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:06:04:083 ==>> 【左刹电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 18:06:04:087 ==>> 检测【右刹电压测试1】
2025-07-31 18:06:04:131 ==>> 【右刹电压测试1】通过,【2253】符合目标值【2250】至【2500】要求!
2025-07-31 18:06:04:135 ==>> 检测【转把电压测试1】
2025-07-31 18:06:04:149 ==>> 【转把电压测试1】通过,【2250】符合目标值【2250】至【2500】要求!
2025-07-31 18:06:04:153 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:06:04:158 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:06:04:255 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:06:04:425 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:06:04:431 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:06:04:438 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:06:04:560 ==>> 3A A3 04 00 A3 


2025-07-31 18:06:04:651 ==>> OFF_OUT4
OVER 150


2025-07-31 18:06:04:714 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:06:04:720 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:06:04:723 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:06:04:741 ==>> $GBGGA,100608.512,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,41,60,,,41,38,,,41,1*7A

$GBGSV,6,2,22,3,,,41,21,,,41,26,,,41,42,,,41,1*40

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,2,,,36,1*79

$GBGSV,6,5,22,14,,,36,33,,,35,5,,,34,4,,,34,1*72

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100608.512,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100608.512,0.000,789.079,789.079,721.631,2097152,2097152,2097152*68



2025-07-31 18:06:04:846 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:06:04:986 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:06:04:990 ==>> 检测【左刹电压测试2】
2025-07-31 18:06:04:996 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:06:05:261 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3101  volt:5450 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:21][COMM]adc read battery ts volt:7 mv
[D][05:18:21][COMM]adc read in 24v adc:1271  volt:32147 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:06:05:367 ==>> [D][05:18:21][COMM]read battery soc:255
[D][05:18:21][COMM]IMU: [11,10,-992] ret=29 AWAKE!


2025-07-31 18:06:05:547 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 18:06:05:554 ==>> 检测【右刹电压测试2】
2025-07-31 18:06:05:588 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 18:06:05:592 ==>> 检测【转把电压测试2】
2025-07-31 18:06:05:654 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 18:06:05:658 ==>> 检测【晶振检测】
2025-07-31 18:06:05:663 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:06:05:747 ==>> $GBGGA,100609.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,13,,,41,60,,,41,1*79

$GBGSV,6,2,22,3,,,41,59,,,41,21,,,41,26,,,41,1*4A

$GBGSV,6,3,22,42,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100609.512,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100609.512,0.000,790.963,790.963,723.354,2097152,2097152,2097152*6D



2025-07-31 18:06:05:822 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 18:06:05:967 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:06:05:971 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:06:05:976 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:06:06:067 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1660mV
Get AD_V4 1653mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:06:06:282 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:06:06:287 ==>> 检测【检测BootVer】
2025-07-31 18:06:06:292 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:06:643 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130020290675
[D][05:18:22][FCTY]HardwareID  = 867222087872109
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:18:22][FCTY]Bat         = 3944 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1276
[D][05:18:22][FCTY]Acckey1 vol = 5449 mv, Acckey2 vol = 0 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3822 mv
[D][05:18

2025-07-31 18:06:06:734 ==>> :22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 18:06:06:827 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:06:06:831 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:06:06:843 ==>> 检测【检测固件版本】
2025-07-31 18:06:06:858 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:06:06:864 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:06:06:867 ==>> 检测【检测蓝牙版本】
2025-07-31 18:06:06:925 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:06:06:930 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:06:06:934 ==>> 检测【检测MoBikeId】
2025-07-31 18:06:07:046 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:06:07:054 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:06:07:063 ==>> 检测【检测蓝牙地址】
2025-07-31 18:06:07:081 ==>> 取到目标值:D6BECC3E0B41
2025-07-31 18:06:07:105 ==>> 【检测蓝牙地址】通过,【D6BECC3E0B41】符合目标值【】要求!
2025-07-31 18:06:07:112 ==>> 提取到蓝牙地址:D6BECC3E0B41
2025-07-31 18:06:07:120 ==>> 检测【BOARD_ID】
2025-07-31 18:06:07:137 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:06:07:141 ==>> 检测【检测充电电压】
2025-07-31 18:06:07:162 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:06:07:169 ==>> 检测【检测VBUS电压1】
2025-07-31 18:06:07:188 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:06:07:199 ==>> 检测【检测充电电流】
2025-07-31 18:06:07:211 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:06:07:218 ==>> 检测【检测IMEI】
2025-07-31 18:06:07:231 ==>> 取到目标值:867222087872109
2025-07-31 18:06:07:236 ==>> 【检测IMEI】通过,【867222087872109】符合目标值【】要求!
2025-07-31 18:06:07:239 ==>> 提取到IMEI:867222087872109
2025-07-31 18:06:07:242 ==>> 检测【检测IMSI】
2025-07-31 18:06:07:250 ==>> 取到目标值:460130020290675
2025-07-31 18:06:07:265 ==>> 【检测IMSI】通过,【460130020290675】符合目标值【】要求!
2025-07-31 18:06:07:270 ==>> 提取到IMSI:460130020290675
2025-07-31 18:06:07:276 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:06:07:304 ==>> 取到目标值:460130020290675
2025-07-31 18:06:07:309 ==>> 【校验网络运营商(移动)】通过,【460130020290675】符合目标值【】要求!
2025-07-31 18:06:07:313 ==>> 检测【打开CAN通信】
2025-07-31 18:06:07:317 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:06:07:378 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS
[D][05:18:23][COMM]read battery soc:255


2025-07-31 18:06:07:592 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:06:07:599 ==>> 检测【检测CAN通信】
2025-07-31 18:06:07:603 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:06:07:683 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:06:07:788 ==>> $GBGGA,100611.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,38,,,42,24,,,42,13,,,41,60,,,41,1*78

$GBGSV,6,2,22,26,,,41,21,,,41,42,,,41,8,,,40,1*4A

$GBGSV,6,3,22,3,,,40,39,,,40,59,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,4,,,34,5,,,33,1*42

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100611.512,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100611.512,0.000,787.199,787.199,719.912,2097152,2097152,2097152*65

[D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34714
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:06:07:819 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:06:07:877 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:06:07:881 ==>> 检测【关闭CAN通信】
2025-07-31 18:06:07:885 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:06:07:891 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:06:07:955 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:06:08:164 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:06:08:168 ==>> 检测【打印IMU STATE】
2025-07-31 18:06:08:174 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:06:08:383 ==>> [D][05:18:24][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:06:08:447 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:06:08:455 ==>> 检测【六轴自检】
2025-07-31 18:06:08:476 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:06:08:749 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBGGA,100612.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,60,,,41,38,,,41,1*7B

$GBGSV,6,2,22,26,,,41,21,,,41,42,,,41,8,,,40,1*4A

$GBGSV,6,3,22,3,,,40,39,,,40,59,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100612.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100612.512,0.000,787.193,787.193,719.906,2097152,2097152,2097152*63



2025-07-31 18:06:09:383 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 18:06:09:743 ==>> $GBGGA,100613.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,60,,,41,38,,,41,3,,,41,1*4A

$GBGSV,6,2,22,26,,,41,21,,,41,13,,,40,8,,,40,1*4F

$GBGSV,6,3,22,59,,,40,42,,,40,39,,,39,16,,,39,1*74

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100613.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100613.512,0.000,785.310,785.310,718.184,2097152,2097152,2097152*61



2025-07-31 18:06:10:361 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:06:10:511 ==>> [D][05:18:26][COMM]Main Task receive event:142
[D][05:18:26][COMM]###### 37472 imu self test OK ######
[D][05:18:26][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-13,2,4062]
[D][05:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 18:06:10:735 ==>> $GBGGA,100614.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,60,,,41,38,,,41,1*7B

$GBGSV,6,2,22,3,,,41,59,,,41,26,,,41,21,,,41,1*4A

$GBGSV,6,3,22,42,,,41,8,,,40,39,,,40,16,,,39,1*4F

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,14,,,37,1*4F

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100614.512,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100614.512,0.000,790.017,790.017,722.489,2097152,2097152,2097152*67



2025-07-31 18:06:10:769 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:06:10:779 ==>> 检测【打印IMU STATE2】
2025-07-31 18:06:10:799 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:06:10:946 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:06:11:039 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:06:11:044 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:06:11:051 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:06:11:157 ==>> 5A A5 02 5A A5 


2025-07-31 18:06:11:262 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:06:11:311 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:06:11:316 ==>> 检测【检测VBUS电压2】
2025-07-31 18:06:11:319 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:11:442 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 10
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 10


2025-07-31 18:06:11:778 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130020290675
[D][05:18:27][FCTY]HardwareID  = 867222087872109
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 336
[D][05:18:27][FCTY]Acckey1 vol = 5466 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V

2025-07-31 18:06:11:868 ==>> 3465b5b1
[D][05:18:27][FCTY]Bat1         = 3822 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,100615.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGSV,6,1,22,24,,,42,13,,,41,60,,,41,38,,,41,1*7B

$GBGSV,6,2,22,3,,,41,26,,,41,21,,,41,42,,,41,1*40

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,9,,,38,1,,,38,6,,,37,14,,,37,1*4F

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100615.512,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100615.512,0.000,789.075,789.075,721.627,2097152,2097152,2097152*63



2025-07-31 18:06:11:875 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:12:213 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130020290675
[D][05:18:27][FCTY]HardwareID  = 867222087872109
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 173
[D][05:18:28][FCTY]Acckey1 vol = 5466 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNS

2025-07-31 18:06:12:258 ==>> S_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3822 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:06:12:511 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:12:945 ==>> $GBGGA,100616.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,13,,,41,3,,,41,1*4C

$GBGSV,6,2,22,26,,,41,21,,,41,42,,,41,60,,,40,1*74

$GBGSV,6,3,22,8,,,40,59,,,40,39,,,39,16,,,39,1*4A

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,35,5,,,34,4,,,34,1*45

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100616.512,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100616.512,0.000,788.138,788.138,720.770,2097152,2097152,2097152*62

[D][05:18:28][COMM]msg 0601 loss. last_tick:34701. cur_tick:39713. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39714
[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130020290675
[D][05:18:28][FCTY]HardwareID  = 867222087872109
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D6B

2025-07-31 18:06:13:035 ==>> ECC3E0B41
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 141
[D][05:18:28][FCTY]Acckey1 vol = 5461 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3822 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:06:13:232 ==>> [D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 18:06:13:314 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:13:841 ==>> [D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PR

2025-07-31 18:06:13:946 ==>> OT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cn

2025-07-31 18:06:14:051 ==>> t[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6

[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B586961C8E37CBA78A077B068080C868CBF2290D11591CCC84653151D6346CAEA3EC90ADE4C04B362FC4EE952991B884D2C600D506B9F27089FAAB6055A25829DBE38D31F9737B5C9ACD02781789B70964561F
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GS

2025-07-31 18:06:14:156 ==>> M_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130020290675
[D][05:18:29][FCTY]HardwareID  = 867222087872109
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:18:29][FCTY]Bat         = 3844 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 6100 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 107
[D][05:18:29][FCTY]Acckey1 vol = 5458 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KE

2025-07-31 18:06:14:246 ==>> RNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3822 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                           

2025-07-31 18:06:14:355 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:06:14:715 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130020290675
[D][05:18:30][FCTY]HardwareID  = 867222087872109
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:18:30][FCTY]Bat         = 3884 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 96
[D][05:18:30][FCTY]Acckey1 vol = 5463 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D]

2025-07-31 18:06:14:759 ==>> [05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3822 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:06:14:883 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:06:14:889 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:06:14:912 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:06:14:957 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:06:15:017 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 18:06:15:092 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 18:06:15:160 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:06:15:165 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:06:15:172 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:06:15:260 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:06:15:430 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:06:15:439 ==>> 检测【打开WIFI(3)】
2025-07-31 18:06:15:462 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:06:15:668 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:06:15:747 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:06:15:756 ==>> 检测【扩展芯片hw】
2025-07-31 18:06:15:776 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:06:15:955 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:06:16:094 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:06:16:102 ==>> 检测【扩展芯片boot】
2025-07-31 18:06:16:171 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:06:16:179 ==>> 检测【扩展芯片sw】
2025-07-31 18:06:16:225 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:06:16:234 ==>> 检测【检测音频FLASH】
2025-07-31 18:06:16:251 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:06:16:341 ==>> +WIFISCAN:4,0,CC057790A7C1,-75
+WIFISCAN:4,1,CC057790A5C1,-81
+WIFISCAN:4,2,CC057790A5C0,-81
+WIFISCAN:4,3,646E97BD0450,-84

[D][05:18:32][CAT1]wifi scan report total[4]


2025-07-31 18:06:16:446 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:06:16:628 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:06:17:321 ==>>                                                                                                                                                    --- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:

2025-07-31 18:06:17:426 ==>> 18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]repor

2025-07-31 18:06:17:531 ==>> t elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or 

2025-07-31 18:06:17:636 ==>> cccd not enabled
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]f:[ec800m_audio_response_process].

2025-07-31 18:06:17:741 ==>> audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[

2025-07-31 18:06:17:816 ==>> D][05:18:32][GNSS]recv submsg id[3]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:33][COMM]read battery soc:255


2025-07-31 18:06:18:894 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:34][CAT1]Send Data To Server[198][201] ... ->:
0063B98D11331133113311

2025-07-31 18:06:18:969 ==>> 3311331B88B3952C1A5F258FA63D3A5FF1A3AA5A48453B491326A5A4053B76D2595C4A8602DB4B22C6867B2A9E78DE77EFDDFEBC2280371743A8CDBDC3DE9E3DF7F84662108F59D7D6FD2A58E6DF7B11CA735F4E9E92DD94
[D][05:18:34][CAT1]<<< 
SEND OK

[D][05:18:34][CAT1]exec over: func id: 15, ret: 11
[D][05:18:34][CAT1]sub id: 15, ret: 11

[D][05:18:34][SAL ]Cellular task submsg id[68]
[D][05:18:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:34][M2M ]g_m2m_is_idle become true
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:34][PROT]M2M Send ok [1629955114]


2025-07-31 18:06:19:120 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 18:06:19:692 ==>> [D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:06:20:555 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 18:06:20:810 ==>> [D][05:18:36][COMM]47677 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:36][COMM]accel parse set 0
[D][05:18:36][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:06:21:110 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 18:06:21:293 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:06:21:302 ==>> 检测【打开喇叭声音】
2025-07-31 18:06:21:324 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:06:21:952 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:37][COMM]--->crc16:0x419c
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][COMM]f:[e

2025-07-31 18:06:22:058 ==>> c800m_audio_start].l:[691].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, in

2025-07-31 18:06:22:091 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:06:22:098 ==>> 检测【打开大灯控制】
2025-07-31 18:06:22:105 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:06:22:162 ==>> dex:4, len:2048
[D][05:18:37][COMM]48688 imu init OK
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:06:22:252 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:06:22:476 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:06:22:482 ==>> 检测【关闭仪表供电3】
2025-07-31 18:06:22:491 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:06:22:648 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 18:06:22:786 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:06:22:794 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:06:22:808 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:06:22:920 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:06:23:099 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:06:23:105 ==>> 检测【读大灯电压】
2025-07-31 18:06:23:131 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:06:23:140 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 18:06:23:235 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[33224]


2025-07-31 18:06:23:409 ==>> 【读大灯电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:06:23:420 ==>> 检测【关闭大灯控制2】
2025-07-31 18:06:23:438 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:06:23:611 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:06:23:714 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:06:23:722 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:06:23:730 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:06:24:139 ==>> [D][05:18:39][PROT]CLEAN,SEND:1
[D][05:18:39][PROT]index:1 1629955119
[D][05:18:39][PROT]is_send:0
[D][05:18:39][PROT]sequence_num:5
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:2
[D][05:18:39][PROT]send_path:0x2
[D][05:18:39][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]sending traceid [9999999999900006]
[D][05:18:39][PROT]Send_TO_M2M [1629955119]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:39][SAL ]sock send credit cnt[6]
[D][05:18:39][SAL ]sock send ind credit cnt[6]
[D][05:18:39][M2M ]m2m send data len[198]
[D][05:18:39][SAL ]Cellular task submsg id[10]
[D][05:18:39][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:39][CAT1]gsm read msg sub id: 15
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][CAT1]Send Data To Server[198][201] ... ->:
0063B98C11331133

2025-07-31 18:06:24:214 ==>> 1133113311331B88B3F7E80F62F34D58ABB4CB0BC8916DA5727B9F5DD94A778655D359D7C1AA4607FEB2A42187DED5E694C4324E6E069B857D6816714866CCDD8BF578E3ACCD0CE81BC48E91DFDA73BEA8F8FBF9C3CC3E25600414
[D][05:18:39][COMM]arm_hub read adc[5],val[92]
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 18:06:24:255 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:06:24:266 ==>> 检测【打开WIFI(4)】
2025-07-31 18:06:24:281 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:06:24:484 ==>> [D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:06:24:578 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:06:24:585 ==>> 检测【EC800M模组版本】
2025-07-31 18:06:24:605 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:06:24:741 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 18:06:24:985 ==>> [D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:06:25:110 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:06:25:121 ==>> 检测【配置蓝牙地址】
2025-07-31 18:06:25:143 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:06:25:151 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 18:06:25:319 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D6BECC3E0B41>】
2025-07-31 18:06:25:327 ==>> [W][05:18:41][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:06:25:425 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,603A7CF67DD4,-77
+WIFISCAN:4,2,CC057790A5C0,-81
+WIFISCAN:4,3,646E97BD0450,-86

[D][05:18:41][CAT1]wifi scan report total[4]
[D][05:18:41][COMM]52397 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:06:25:560 ==>> recv ble 1
recv ble 2
ble set mac ok :d6,be,cc,3e,b,41
enable filters ret : 0

2025-07-31 18:06:25:594 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:06:25:602 ==>> 检测【BLETEST】
2025-07-31 18:06:25:636 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:06:25:651 ==>> 4A A4 01 A4 4A 


2025-07-31 18:06:25:755 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D6BECC3E0B41*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D6BECC3E0B4199999OVER 150


2025-07-31 18:06:26:011 ==>> [D][05:18:41][GNSS]recv submsg id[3]


2025-07-31 18:06:26:437 ==>> [D][05:18:42][COMM]53408 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:06:26:622 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:06:26:628 ==>> 该项需要延时执行
2025-07-31 18:06:27:080 ==>> [D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:42][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:06:27:140 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 18:06:27:431 ==>> [D][05:18:43][COMM]54420 imu init OK


2025-07-31 18:06:29:363 ==>> [D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:5
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:1
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]sending traceid [9999999999900006]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[198]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B33160E657C56986894DF489E911E58F48900DFCDD539AA2CF18F82CBAF9CCC708E6D971A12069C97C66E10E3B9AF

2025-07-31 18:06:29:438 ==>> E3E5E7F9EE3BF2A1EEF4AD3F18597C695657959AD05C22EFD742F6C62272CB8AE65DC734F
[D][05:18:45][COMM]read battery soc:255
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]


2025-07-31 18:06:31:155 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 18:06:33:161 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 18:06:34:572 ==>> [D][05:18:50][PROT]CLEAN,SEND:1
[D][05:18:50][PROT]CLEAN:1
[D][05:18:50][PROT]index:0 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:4
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900005]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[198]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:50][CAT1]Se

2025-07-31 18:06:34:647 ==>> nd Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B527E94BA76C9F1C4F5CC9C704A81BF20351D5C1207778CD50C37B2F5A8EBB61C910B6F5AF250D95FE54269F1F02BF3E8E9D675B949B779D3C67739AC780EA8F463328200BE8DA357940583129824D59CFFE6A
[D][05:18:50][CAT1]<<< 
SEND OK

[D][05:18:50][CAT1]exec over: func id: 15, ret: 11
[D][05:18:50][CAT1]sub id: 15, ret: 11

[D][05:18:50][SAL ]Cellular task submsg id[68]
[D][05:18:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:50][M2M ]g_m2m_is_idle become true
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:50][PROT]M2M Send ok [1629955130]


2025-07-31 18:06:35:169 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 18:06:36:634 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:06:36:644 ==>> 检测【检测WiFi结果】
2025-07-31 18:06:36:653 ==>> WiFi信号:【CC057790A7C1】,信号值:-79
2025-07-31 18:06:36:662 ==>> WiFi信号:【CC057790A7C0】,信号值:-79
2025-07-31 18:06:36:686 ==>> WiFi信号:【44A1917CAD81】,信号值:-84
2025-07-31 18:06:36:694 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 18:06:36:717 ==>> WiFi信号:【CC057790A5C0】,信号值:-81
2025-07-31 18:06:36:722 ==>> WiFi信号:【646E97BD0450】,信号值:-84
2025-07-31 18:06:36:731 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 18:06:36:740 ==>> WiFi信号:【603A7CF67DD4】,信号值:-77
2025-07-31 18:06:36:750 ==>> WiFi数量【8】, 最大信号值:-34
2025-07-31 18:06:36:764 ==>> 检测【检测GPS结果】
2025-07-31 18:06:36:775 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:06:36:854 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]all continue location stop
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]all sing location stop


2025-07-31 18:06:37:178 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 18:06:37:640 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:06:37:649 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:37:673 ==>> 定位已等待【1】秒.
2025-07-31 18:06:38:086 ==>> [D][05:18:53][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:53][COMM]Open GPS Module...
[D][05:18:53][COMM]LOC_MODEL_CONT
[D][05:18:53][GNSS]start event:8
[D][05:18:53][GNSS]GPS start. ret=0
[W][05:18:53][GNSS]start cont locating
[D][05:18:53][CAT1]gsm read msg sub id: 23
[D][05:18:53][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:53][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:06:38:643 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:38:653 ==>> 定位已等待【2】秒.
2025-07-31 18:06:38:784 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:06:39:199 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 18:06:39:458 ==>> [D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:06:39:656 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:39:665 ==>> 定位已等待【3】秒.
2025-07-31 18:06:39:823 ==>>                                                                      GSV,3,1,11,60,,,44,24,,,42,26,,,41,38,,,41,1*7D

$GBGSV,3,2,11,42,,,41,59,,,40,39,,,39,14,,,49,1*74

$GBGSV,3,3,11,16,,,39,21,,,38,13,,,37,1*75

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1705.632,1705.632,54.481,2097152,2097152,2097152*43

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:55][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:55][PROT]CLEAN,SEND:0
[D][05:18:55][PROT]index:0 1629955135
[D][05:18:55][PROT]is_send:0
[D][05:18:55][PROT]sequence_num:4
[D][05:18:55][PROT]retry_timeout:0
[D][05:18:55][PROT]retry_times:1
[D][05:18:55][PROT]send_path:0x2
[D][05:18:55][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:55][PROT]===========================================================
[W][05:18:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955135]
[D][05:18:55][PROT]===========================================================
[D][05:18:55][PROT]sending traceid [9999999999900005]
[D][05:18:55][PROT]Send_TO_M2M [1629955135]
[D][05:

2025-07-31 18:06:39:928 ==>> 18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:55][SAL ]sock send credit cnt[6]
[D][05:18:55][SAL ]sock send ind credit cnt[6]
[D][05:18:55][M2M ]m2m send data len[198]
[D][05:18:55][SAL ]Cellular task submsg id[10]
[D][05:18:55][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 23, ret: 6
[D][05:18:55][CAT1]sub id: 23, ret: 6

[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:55][CAT1]gsm read msg sub id: 15
[D][05:18:55][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:55][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B54F74606519DB3E7CA24905E6B5AC15C0A7728D663A4FD7DF935544B7FBED55AB3D9CAF677C16783F379CA6782E8B0E04DE6AE1D2324EA7AADB8F6C1B549B99031562F9D7E5250F86C9297A41A3ECF09FB3CF
[D][05:18:55][CAT1]<<< 
SEND OK

[D][05:18:55][CAT1]exec over: func id: 15, ret: 11
[D][05:18:55][CAT1]sub id: 15, ret: 11

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:55][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:55][M2M ]g_m2m_is_idle become true
[D][

2025-07-31 18:06:39:958 ==>> 05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:55][PROT]M2M Send ok [1629955135]


2025-07-31 18:06:40:108 ==>> [D][05:18:56][GNSS]recv submsg id[1]
[D][05:18:56][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:06:40:587 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,24,,,42,60,,,41,26,,,41,38,,,41,1*7D

$GBGSV,4,2,13,42,,,41,59,,,40,21,,,40,13,,,40,1*78

$GBGSV,4,3,13,8,,,40,39,,,39,14,,,37,16,,,35,1*4F

$GBGSV,4,4,13,4,,,15,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1569.234,1569.234,50.390,2097152,2097152,2097152*40



2025-07-31 18:06:40:662 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:40:671 ==>> 定位已等待【4】秒.
2025-07-31 18:06:41:193 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 18:06:41:610 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,43,38,,,42,60,,,41,26,,,41,1*7A

$GBGSV,5,2,17,42,,,41,59,,,41,21,,,41,13,,,41,1*7C

$GBGSV,5,3,17,3,,,41,8,,,40,39,,,40,1,,,38,1*48

$GBGSV,5,4,17,2,,,38,14,,,37,16,,,37,5,,,34,1*78

$GBGSV,5,5,17,4,,,34,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1633.924,1633.924,52.247,2097152,2097152,2097152*49



2025-07-31 18:06:41:670 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:41:680 ==>> 定位已等待【5】秒.
2025-07-31 18:06:42:610 ==>> $GBGGA,100646.420,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,38,,,42,26,,,42,60,,,41,1*77

$GBGSV,5,2,19,42,,,41,21,,,41,13,,,41,3,,,41,1*4D

$GBGSV,5,3,19,59,,,40,8,,,40,39,,,40,1,,,38,1*78

$GBGSV,5,4,19,16,,,38,2,,,37,14,,,37,5,,,34,1*76

$GBGSV,5,5,19,4,,,34,33,,,37,9,,,36,1*75

$GBRMC,100646.420,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100646.420,0.000,1633.925,1633.925,52.248,2097152,2097152,2097152*5B



2025-07-31 18:06:42:685 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:42:694 ==>> 定位已等待【6】秒.
2025-07-31 18:06:43:214 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 18:06:43:598 ==>> $GBGGA,100647.400,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,38,,,42,26,,,42,60,,,41,1*7D

$GBGSV,5,2,20,42,,,41,21,,,41,13,,,41,3,,,41,1*47

$GBGSV,5,3,20,59,,,40,8,,,40,39,,,40,16,,,39,1*45

$GBGSV,5,4,20,1,,,38,14,,,37,9,,,37,2,,,36,1*44

$GBGSV,5,5,20,33,,,36,6,,,35,5,,,34,4,,,34,1*40

$GBRMC,100647.400,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100647.400,0.000,1612.717,1612.717,51.579,2097152,2097152,2097152*5E



2025-07-31 18:06:43:688 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:43:697 ==>> 定位已等待【7】秒.
2025-07-31 18:06:43:794 ==>> $GBGGA,100647.600,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,38,,,42,26,,,42,60,,,41,1*7D

$GBGSV,5,2,20,42,,,41,21,,,41,13,,,41,3,,,41,1*47

$GBGSV,5,3,20,59,,,40,8,,,40,39,,,40,16,,,39,1*45

$GBGSV,5,4,20,1,,,38,14,,,37,9,,,37,2,,,36,1*44

$GBGSV,5,5,20,33,,,36,6,,,35,5,,,35,4,,,34,1*41

$GBRMC,100647.600,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100647.600,0.000,1614.785,1614.785,51.641,2097152,2097152,2097152*54



2025-07-31 18:06:44:698 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:44:721 ==>> 定位已等待【8】秒.
2025-07-31 18:06:44:774 ==>> $GBGGA,100648.580,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,26,,,42,60,,,41,1*7F

$GBGSV,6,2,21,42,,,41,21,,,41,13,,,41,3,,,41,1*45

$GBGSV,6,3,21,59,,,40,8,,,40,39,,,40,16,,,39,1*47

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,33,,,37,1*75

$GBGSV,6,5,21,2,,,36,6,,,36,5,,,34,4,,,34,1*73

$GBGSV,6,6,21,7,,,31,1*40

$GBRMC,100648.580,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100648.580,0.000,1601.087,1601.087,51.224,2097152,2097152,2097152*57



2025-07-31 18:06:45:062 ==>>                                                                                                                                                        quence_num:6
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:3
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:00][PROT]===========================================================
[W][05:19:00][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:19:00][PROT]sending traceid [9999999999900007]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[134]
[D][05:19:00][SAL ]Cellular task submsg id[10]
[D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:00][CAT1]Send Data To S

2025-07-31 18:06:45:137 ==>> erver[134][137] ... ->:
0043B683113311331133113311331B88BE602B8CBD70E90A4432A511986865C8E4DF8805B22D6BBBE6CEEE39DC20852BBCAA3ED4C87F7D4F83FEACCEC24712CCB95258
[D][05:19:00][CAT1]<<< 
SEND OK

[D][05:19:00][CAT1]exec over: func id: 15, ret: 11
[D][05:19:00][CAT1]sub id: 15, ret: 11

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:00][M2M ]g_m2m_is_idle become true
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:00][PROT]M2M Send ok [1629955140]


2025-07-31 18:06:45:212 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 18:06:45:708 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:45:719 ==>> 定位已等待【9】秒.
2025-07-31 18:06:45:784 ==>> $GBGGA,100649.560,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,42,26,,,41,60,,,41,1*7D

$GBGSV,6,2,21,42,,,41,21,,,41,13,,,41,3,,,41,1*45

$GBGSV,6,3,21,59,,,40,8,,,40,39,,,40,16,,,39,1*47

$GBGSV,6,4,21,1,,,38,14,,,37,9,,,37,33,,,37,1*75

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,100649.560,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100649.560,0.000,1597.138,1597.138,51.098,2097152,2097152,2097152*5D



2025-07-31 18:06:46:709 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:46:721 ==>> 定位已等待【10】秒.
2025-07-31 18:06:46:754 ==>> $GBGGA,100650.540,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,43,38,,,42,26,,,42,60,,,41,1*7F

$GBGSV,6,2,21,42,,,41,21,,,41,13,,,41,3,,,41,1*45

$GBGSV,6,3,21,59,,,40,8,,,40,39,,,40,16,,,39,1*47

$GBGSV,6,4,21,1,,,38,9,,,38,14,,,37,33,,,37,1*7A

$GBGSV,6,5,21,6,,,37,2,,,36,5,,,34,4,,,34,1*72

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,100650.540,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100650.540,0.000,1603.065,1603.065,51.291,2097152,2097152,2097152*5C



2025-07-31 18:06:47:233 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 18:06:47:723 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:47:733 ==>> 定位已等待【11】秒.
2025-07-31 18:06:47:757 ==>> $GBGGA,100651.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,26,,,41,60,,,41,1*7D

$GBGSV,6,2,22,21,,,41,42,,,40,13,,,40,3,,,40,1*47

$GBGSV,6,3,22,59,,,40,8,,,40,39,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,36,5,,,34,4,,,34,1*71

$GBGSV,6,6,22,7,,,30,45,,,27,1*46

$GBRMC,100651.520,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100651.520,0.000,1567.917,1567.917,50.196,2097152,2097152,2097152*5E



2025-07-31 18:06:48:731 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:48:740 ==>> 定位已等待【12】秒.
2025-07-31 18:06:48:750 ==>> $GBGGA,100652.520,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,26,,,41,60,,,41,1*7D

$GBGSV,6,2,22,3,,,41,21,,,40,42,,,40,13,,,40,1*47

$GBGSV,6,3,22,59,,,40,39,,,40,8,,,39,16,,,39,1*4A

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,22,7,,,31,45,,,30,1*41

$GBRMC,100652.520,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100652.520,0.000,1575.429,1575.429,50.411,2097152,2097152,2097152*57



2025-07-31 18:06:49:241 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 18:06:49:718 ==>> $GBGGA,100653.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,26,,,41,60,,,41,1*7D

$GBGSV,6,2,22,3,,,41,21,,,41,59,,,41,42,,,40,1*49

$GBGSV,6,3,22,13,,,40,39,,,40,8,,,40,16,,,39,1*4A

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,22,7,,,31,45,,,31,1*40

$GBRMC,100653.520,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100653.520,0.000,1582.966,1582.966,50.651,2097152,2097152,2097152*50



2025-07-31 18:06:49:733 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:49:742 ==>> 定位已等待【13】秒.
2025-07-31 18:06:50:303 ==>> [D][05:19:06][PROT]CLEAN,SEND:2
[D][05:19:06][PROT]index:2 1629955146
[D][05:19:06][PROT]is_send:0
[D][05:19:06][PROT]sequence_num:6
[D][05:19:06][PROT]retry_timeout:0
[D][05:19:06][PROT]retry_times:2
[D][05:19:06][PROT]send_path:0x2
[D][05:19:06][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:19:06][PROT]sending traceid [9999999999900007]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[134]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:06][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:06][CAT1]Send Data To Server[134][137] ... ->:
0043B6851133113311

2025-07-31 18:06:50:378 ==>> 33113311331B88BEB0D345FC248445590D840516006DF7E754C78C47CEF0E682A67029B6FBBA76D6F2459AB89BE2694D0CA5DA1143ED37DB7DF9
[D][05:19:06][CAT1]<<< 
SEND OK

[D][05:19:06][CAT1]exec over: func id: 15, ret: 11
[D][05:19:06][CAT1]sub id: 15, ret: 11

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:06][PROT]M2M Send ok [1629955146]


2025-07-31 18:06:50:715 ==>> $GBGGA,100654.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,26,,,41,21,,,41,1*78

$GBGSV,6,2,22,13,,,41,60,,,40,3,,,40,59,,,40,1*48

$GBGSV,6,3,22,42,,,40,39,,,40,8,,,40,16,,,39,1*4E

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,35,4,,,34,1*71

$GBGSV,6,6,22,45,,,32,7,,,31,1*43

$GBRMC,100654.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100654.520,0.000,1582.955,1582.955,50.639,2097152,2097152,2097152*59



2025-07-31 18:06:50:745 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:50:755 ==>> 定位已等待【14】秒.
2025-07-31 18:06:51:257 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 18:06:51:738 ==>> $GBGGA,100655.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,41,26,,,41,21,,,41,1*78

$GBGSV,6,2,22,13,,,41,60,,,41,42,,,41,3,,,40,1*42

$GBGSV,6,3,22,59,,,40,39,,,40,8,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100655.520,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100655.520,0.000,1582.966,1582.966,50.651,2097152,2097152,2097152*56



2025-07-31 18:06:51:753 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:51:763 ==>> 定位已等待【15】秒.
2025-07-31 18:06:52:727 ==>> $GBGGA,100656.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,21,,,41,1*79

$GBGSV,6,2,22,13,,,41,42,,,41,3,,,41,59,,,41,1*49

$GBGSV,6,3,22,60,,,40,39,,,40,8,,,40,16,,,40,1*40

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,36,5,,,34,4,,,34,1*71

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100656.520,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100656.520,0.000,1590.515,1590.515,50.903,2097152,2097152,2097152*5D



2025-07-31 18:06:52:757 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:52:766 ==>> 定位已等待【16】秒.
2025-07-31 18:06:53:283 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 18:06:53:713 ==>> $GBGGA,100657.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,21,,,41,1*79

$GBGSV,6,2,22,13,,,41,42,,,41,3,,,41,59,,,41,1*49

$GBGSV,6,3,22,60,,,41,39,,,40,8,,,40,16,,,39,1*4F

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,33,,,37,1*76

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,35,4,,,34,1*71

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100657.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100657.520,0.000,1594.280,1594.280,51.020,2097152,2097152,2097152*55



2025-07-31 18:06:53:758 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:53:768 ==>> 定位已等待【17】秒.
2025-07-31 18:06:54:721 ==>> $GBGGA,100658.520,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,38,,,42,26,,,42,21,,,41,1*79

$GBGSV,6,2,22,13,,,41,42,,,41,3,,,41,60,,,41,1*43

$GBGSV,6,3,22,59,,,40,39,,,40,8,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,9,,,38,6,,,38,14,,,37,1*40

$GBGSV,6,5,22,33,,,37,2,,,37,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,45,,,32,7,,,30,1*42

$GBRMC,100658.520,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100658.520,0.000,1594.280,1594.280,51.020,2097152,2097152,2097152*5A



2025-07-31 18:06:54:766 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:54:776 ==>> 定位已等待【18】秒.
2025-07-31 18:06:55:524 ==>> [D][05:19:11][PROT]CLEAN,SEND:2
[D][05:19:11][COMM]read battery soc:255
[D][05:19:11][PROT]index:2 1629955151
[D][05:19:11][PROT]is_send:0
[D][05:19:11][PROT]sequence_num:6
[D][05:19:11][PROT]retry_timeout:0
[D][05:19:11][PROT]retry_times:1
[D][05:19:11][PROT]send_path:0x2
[D][05:19:11][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:11][PROT]===========================================================
[W][05:19:11][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955151]
[D][05:19:11][PROT]===========================================================
[D][05:19:11][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:19:11][PROT]sending traceid [9999999999900007]
[D][05:19:11][PROT]Send_TO_M2M [1629955151]
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:11][SAL ]sock send credit cnt[6]
[D][05:19:11][SAL ]sock send ind credit cnt[6]
[D][05:19:11][M2M ]m2m send data len[134]
[D][05:19:11][SAL ]Cellular task submsg id[10]
[D][05:19:11][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:11][CAT1]gsm read msg sub id: 15
[D][05:19:11][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:11][CAT1]Send Data To Server[134

2025-07-31 18:06:55:599 ==>> ][137] ... ->:
0043B686113311331133113311331B88BE23698C47E999239D43740A314AE382177ABF97F6D3A2A2908D31D270EE6C5516422D6F8A22DD00EE8F0BA7B02EF9B5765F31
[D][05:19:11][CAT1]<<< 
SEND OK

[D][05:19:11][CAT1]exec over: func id: 15, ret: 11
[D][05:19:11][CAT1]sub id: 15, ret: 11

[D][05:19:11][SAL ]Cellular task submsg id[68]
[D][05:19:11][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:11][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:11][M2M ]g_m2m_is_idle become true
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:11][PROT]M2M Send ok [1629955151]


2025-07-31 18:06:55:704 ==>>                                                                                                                                                             ,41,3,,,41,60,,,41,1*42

$GBGSV,6,3,23,8,,,41,59,,,40,39,,,40,16,,,39,1*44

$GBGSV,6,4,23,1,,,38,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,23,33,,,37,2,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,23,45,,,33,40,,,31,7,,,30,1*4

2025-07-31 18:06:55:734 ==>> 4

$GBRMC,100659.520,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100659.520,0.000,1584.463,1584.463,50.720,2097152,2097152,2097152*5D



2025-07-31 18:06:55:779 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:55:791 ==>> 定位已等待【19】秒.
2025-07-31 18:06:56:742 ==>> $GBGGA,100700.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,21,,,41,1*78

$GBGSV,6,2,23,13,,,41,42,,,41,3,,,41,60,,,41,1*42

$GBGSV,6,3,23,59,,,41,8,,,40,39,,,40,16,,,39,1*44

$GBGSV,6,4,23,1,,,39,9,,,38,6,,,38,14,,,37,1*40

$GBGSV,6,5,23,33,,,37,2,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,23,45,,,32,40,,,31,7,,,30,1*45

$GBRMC,100700.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100700.520,0.000,1582.662,1582.662,50.664,2097152,2097152,2097152*51



2025-07-31 18:06:56:787 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:56:797 ==>> 定位已等待【20】秒.
2025-07-31 18:06:57:301 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 18:06:57:746 ==>> $GBGGA,100701.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,21,,,41,1*78

$GBGSV,6,2,23,13,,,41,42,,,41,3,,,41,60,,,41,1*42

$GBGSV,6,3,23,59,,,41,8,,,40,39,,,40,16,,,39,1*44

$GBGSV,6,4,23,1,,,38,9,,,38,6,,,38,14,,,37,1*41

$GBGSV,6,5,23,33,,,37,2,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,23,45,,,32,40,,,31,7,,,30,1*45

$GBRMC,100701.520,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100701.520,0.000,788.971,788.971,721.533,2097152,2097152,2097152*60



2025-07-31 18:06:57:791 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:57:801 ==>> 定位已等待【21】秒.
2025-07-31 18:06:58:738 ==>> $GBGGA,100702.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,13,,,41,60,,,41,1*78

$GBGSV,6,2,23,3,,,41,26,,,41,21,,,41,42,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,9,,,38,1,,,38,2,,,37,6,,,37,1*79

$GBGSV,6,5,23,14,,,37,33,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100702.520,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100702.520,0.000,788.062,788.062,720.702,2097152,2097152,2097152*62



2025-07-31 18:06:58:798 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:58:808 ==>> 定位已等待【22】秒.
2025-07-31 18:06:59:294 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 18:06:59:742 ==>> $GBGGA,100703.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,42,26,,,42,24,,,42,13,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,59,,,41,21,,,41,1*49

$GBGSV,6,3,23,42,,,41,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,6,4,23,9,,,38,1,,,38,2,,,37,6,,,37,1*79

$GBGSV,6,5,23,14,,,37,33,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100703.520,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100703.520,0.000,788.963,788.963,721.525,2097152,2097152,2097152*65



2025-07-31 18:06:59:802 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:06:59:812 ==>> 定位已等待【23】秒.
2025-07-31 18:07:00:515 ==>> [D][05:19:16][PROT]CLEAN,SEND:2
[D][05:19:16][PROT]CLEAN:2


2025-07-31 18:07:00:740 ==>> $GBGGA,100704.520,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,43,38,,,42,26,,,42,13,,,41,1*7E

$GBGSV,6,2,24,60,,,41,3,,,41,59,,,41,21,,,41,1*4E

$GBGSV,6,3,24,42,,,41,8,,,40,39,,,40,16,,,39,1*49

$GBGSV,6,4,24,9,,,38,1,,,38,6,,,38,2,,,37,1*71

$GBGSV,6,5,24,14,,,37,33,,,37,5,,,34,4,,,34,1*77

$GBGSV,6,6,24,45,,,32,7,,,31,40,,,31,10,,,36,1*47

$GBRMC,100704.520,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100704.520,0.000,790.764,790.764,723.172,2097152,2097152,2097152*66



2025-07-31 18:07:00:815 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:00:826 ==>> 定位已等待【24】秒.
2025-07-31 18:07:01:291 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 18:07:01:749 ==>> $GBGGA,100705.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,38,,,42,24,,,42,13,,,41,60,,,41,1*79

$GBGSV,6,2,23,3,,,41,59,,,41,26,,,41,21,,,41,1*4B

$GBGSV,6,3,23,42,,,41,8,,,40,39,,,40,16,,,39,1*4E

$GBGSV,6,4,23,1,,,38,6,,,38,9,,,37,14,,,37,1*4E

$GBGSV,6,5,23,33,,,37,2,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100705.520,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100705.520,0.000,787.163,787.163,719.879,2097152,2097152,2097152*6C



2025-07-31 18:07:01:824 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:01:834 ==>> 定位已等待【25】秒.
2025-07-31 18:07:02:742 ==>> $GBGGA,100706.520,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,13,,,41,1*79

$GBGSV,6,2,23,60,,,41,3,,,41,59,,,41,21,,,41,1*49

$GBGSV,6,3,23,42,,,41,8,,,40,39,,,40,16,,,40,1*40

$GBGSV,6,4,23,1,,,38,6,,,38,9,,,37,14,,,37,1*4E

$GBGSV,6,5,23,33,,,37,2,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100706.520,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100706.520,0.000,789.867,789.867,722.352,2097152,2097152,2097152*65



2025-07-31 18:07:02:832 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:02:844 ==>> 定位已等待【26】秒.
2025-07-31 18:07:03:312 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 18:07:03:742 ==>> $GBGGA,100707.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,21,,,42,1*7B

$GBGSV,6,2,23,13,,,41,8,,,41,60,,,41,3,,,41,1*7C

$GBGSV,6,3,23,59,,,41,42,,,41,39,,,40,16,,,40,1*75

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,33,,,38,1*4B

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,34,1*43

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100707.520,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100707.520,0.000,793.467,793.467,725.645,2097152,2097152,2097152*60



2025-07-31 18:07:03:847 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:03:858 ==>> 定位已等待【27】秒.
2025-07-31 18:07:04:747 ==>> $GBGGA,100708.520,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,42,,,42,1*7E

$GBGSV,6,2,23,13,,,41,8,,,41,60,,,41,3,,,41,1*7C

$GBGSV,6,3,23,59,,,41,21,,,41,39,,,40,16,,,40,1*70

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,33,,,38,1*4B

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,34,1*43

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100708.520,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100708.520,0.000,793.467,793.467,725.645,2097152,2097152,2097152*6F



2025-07-31 18:07:04:852 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:04:862 ==>> 定位已等待【28】秒.
2025-07-31 18:07:05:346 ==>> [D][05:19:21][COMM]IMU: [20,-7,-1033] ret=22 AWAKE!
[D][05:19:21][COMM]read battery soc:255


2025-07-31 18:07:05:451 ==>> [D][05:19:21][COMM]IMU: [2,-1,-974] ret=23 AWAKE!


2025-07-31 18:07:05:737 ==>> $GBGGA,100709.520,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,13,,,41,1*79

$GBGSV,6,2,23,8,,,41,60,,,41,3,,,41,59,,,41,1*72

$GBGSV,6,3,23,21,,,41,42,,,41,39,,,40,16,,,40,1*7A

$GBGSV,6,4,23,1,,,38,6,,,38,33,,,38,9,,,37,1*44

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,34,1*43

$GBGSV,6,6,23,45,,,32,7,,,31,40,,,31,1*44

$GBRMC,100709.520,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100709.520,0.000,791.667,791.667,723.998,2097152,2097152,2097152*67



2025-07-31 18:07:05:857 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:05:868 ==>> 定位已等待【29】秒.
2025-07-31 18:07:06:756 ==>> $GBGGA,100710.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,26,,,42,13,,,41,1*79

$GBGSV,6,2,23,60,,,41,3,,,41,21,,,41,42,,,41,1*43

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,40,1*4B

$GBGSV,6,4,23,9,,,38,1,,,38,6,,,38,33,,,38,1*4B

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,34,1*43

$GBGSV,6,6,23,45,,,32,40,,,31,7,,,30,1*45

$GBRMC,100710.520,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100710.520,0.000,789.869,789.869,722.355,2097152,2097152,2097152*65



2025-07-31 18:07:06:861 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:06:874 ==>> 定位已等待【30】秒.
2025-07-31 18:07:07:339 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 18:07:07:738 ==>> $GBGGA,100711.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,38,,,42,13,,,41,60,,,41,1*78

$GBGSV,6,2,23,3,,,41,26,,,41,21,,,41,42,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,9,,,37,6,,,37,14,,,37,1*41

$GBGSV,6,5,23,33,,,37,2,,,36,4,,,34,5,,,33,1*41

$GBGSV,6,6,23,45,,,32,7,,,30,40,,,30,1*44

$GBRMC,100711.520,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100711.520,0.000,783.578,783.578,716.602,2097152,2097152,2097152*64



2025-07-31 18:07:07:874 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:07:887 ==>> 定位已等待【31】秒.
2025-07-31 18:07:08:736 ==>> $GBGGA,100712.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,43,38,,,42,13,,,41,60,,,41,1*7F

$GBGSV,6,2,24,3,,,41,59,,,41,26,,,41,21,,,41,1*4C

$GBGSV,6,3,24,42,,,41,8,,,40,39,,,40,16,,,40,1*47

$GBGSV,6,4,24,1,,,38,9,,,37,6,,,37,14,,,37,1*46

$GBGSV,6,5,24,33,,,37,2,,,36,5,,,34,4,,,34,1*41

$GBGSV,6,6,24,45,,,32,7,,,31,40,,,30,11,,,46,1*40

$GBRMC,100712.520,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100712.520,0.000,787.171,787.171,719.888,2097152,2097152,2097152*64



2025-07-31 18:07:08:886 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:08:897 ==>> 定位已等待【32】秒.
2025-07-31 18:07:09:363 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 18:07:09:742 ==>> $GBGGA,100713.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,43,38,,,42,13,,,41,60,,,41,1*7F

$GBGSV,6,2,24,3,,,41,26,,,41,21,,,41,42,,,41,1*46

$GBGSV,6,3,24,8,,,40,59,,,40,39,,,40,16,,,39,1*42

$GBGSV,6,4,24,1,,,38,9,,,37,6,,,37,14,,,37,1*46

$GBGSV,6,5,24,33,,,37,2,,,36,5,,,34,4,,,34,1*41

$GBGSV,6,6,24,45,,,32,7,,,30,40,,,30,11,,,40,1*47

$GBRMC,100713.520,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100713.520,0.000,784.475,784.475,717.422,2097152,2097152,2097152*67



2025-07-31 18:07:09:893 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:09:906 ==>> 定位已等待【33】秒.
2025-07-31 18:07:10:898 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:07:10:918 ==>> 定位已等待【34】秒.
2025-07-31 18:07:11:567 ==>> $GBGGA,100710.526,2301.2568617,N,11421.9406503,E,1,10,1.54,66.101,M,-1.770,M,,*56

$GBGSA,A,3,13,08,42,16,26,38,39,24,21,14,,,3.86,1.54,3.54,4*09

$GBGSV,6,1,24,13,81,259,41,8,80,183,40,42,65,2,41,16,65,310,39,1*47

$GBGSV,6,2,24,26,64,30,42,38,63,167,42,39,63,339,40,3,62,191,41,1*76

$GBGSV,6,3,24,24,61,229,43,6,54,7,37,59,52,130,40,1,48,126,38,1*70

$GBGSV,6,4,24,21,46,109,41,2,46,239,36,9,42,282,37,14,42,333,37,1*74

$GBGSV,6,5,24,60,41,238,41,4,32,112,34,45,24,199,32,5,22,258,34,1*76

$GBGSV,6,6,24,7,15,181,30,40,11,169,30,33,,,37,11,,,37,1*41

$GBRMC,100710.526,A,2301.2568617,N,11421.9406503,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:26][GNSS]HD8040 GPS
[D][05:19:26][GNSS]GPS diff_sec 124001264, report 0x42 frame
$GBGST,100710.526,0.628,0.197,0.192,0.283,3.190,4.076,16*54

[D][05:19:26][COMM]Main Task receive event:131
[D][05:19:26][COMM]index:0,power_mode:0xFF
[D][05:19:26][COMM]index:1,sound_mode:0xFF
[D][05:19:26][COMM]index:2,gsensor_mode:0xFF
[D][05:19:26][COMM]index:3,report_freq_mode:0xFF
[D][05:19:26][COMM]index:4,report_period:0xFF
[D][05:19:26][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:26][COMM]index:6,normal_reset_period:0xFF
[D][05:19:26][COMM]index:7,spock_over_speed:0xFF
[D][05:19:26][COMM]index:8,spock_limi

2025-07-31 18:07:11:672 ==>> t_speed:0xFF
[D][05:19:26][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:26][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:26][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:26][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:26][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:26][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:26][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:26][COMM]index:16,imu_config_params:0xFF
[D][05:19:26][COMM]index:17,long_connect_params:0xFF
[D][05:19:26][COMM]index:18,detain_mark:0xFF
[D][05:19:26][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:26][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:26][COMM]index:21,mc_mode:0xFF
[D][05:19:26][COMM]index:22,S_mode:0xFF
[D][05:19:26][COMM]index:23,overweight:0xFF
[D][05:19:26][COMM]index:24,standstill_mode:0xFF
[D][05:19:26][COMM]index:25,night_mode:0xFF
[D][05:19:26][COMM]index:26,experiment1:0xFF
[D][05:19:26][COMM]index:27,experiment2:0xFF
[D][05:19:26][COMM]index:28,experiment3:0xFF
[D][05:19:26][COMM]index:29,experiment4:0xFF
[D][05:19:26][COMM]index:30,night_mode_start:0xFF
[D][05:19:26][COMM]index:31,night_mode_end:0xFF
[D][05:19:26][COMM]index:33,park_report_mi

2025-07-31 18:07:11:777 ==>> nutes:0xFF
[D][05:19:26][COMM]index:34,park_report_mode:0xFF
[D][05:19:26][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:26][COMM]index:38,charge_battery_para: FF
[D][05:19:26][COMM]index:39,multirider_mode:0xFF
[D][05:19:26][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:26][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:26][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:26][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:26][COMM]index:44,riding_duration_config:0xFF
[D][05:19:26][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:26][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:26][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:26][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:26][COMM]index:49,mc_load_startup:0xFF
[D][05:19:26][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:26][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:26][COMM]index:52,traffic_mode:0xFF
[D][05:19:26][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:26][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:26][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:26][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:26][COMM]index:58,traffic_light_threshold:0xFF

2025-07-31 18:07:11:882 ==>> 
[D][05:19:26][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:26][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:26][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:26][COMM]index:63,experiment5:0xFF
[D][05:19:26][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:26][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:26][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:26][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:26][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:26][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:26][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:26][COMM]index:72,experiment6:0xFF
[D][05:19:26][COMM]index:73,experiment7:0xFF
[D][05:19:26][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:26][COMM]index:75,zero_value_from_server:-1
[D][05:19:26][COMM]index:76,multirider_threshold:255
[D][05:19:26][COMM]index:77,experiment8:255
[D][05:19:26][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:26][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:26][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:26][COMM]index:82,loc_report_low_speed_thr:255
[D][05:

2025-07-31 18:07:11:912 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 18:07:11:919 ==>> 
北斗星号:【13】,信号值:【41】
北斗星号:【8】,信号值:【40】
北斗星号:【42】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【26】,信号值:【42】
北斗星号:【38】,信号值:【42】
北斗星号:【39】,信号值:【40】
北斗星号:【3】,信号值:【41】
北斗星号:【24】,信号值:【43】
北斗星号:【6】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【21】,信号值:【41】
北斗星号:【2】,信号值:【36】
北斗星号:【9】,信号值:【37】
北斗星号:【14】,信号值:【37】
北斗星号:【60】,信号值:【41】
北斗星号:【33】,信号值:【37】
北斗星号:【11】,信号值:【37】

2025-07-31 18:07:11:935 ==>> 检测【CSQ强度】
2025-07-31 18:07:11:943 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:07:11:987 ==>> 19:26][COMM]index:83,loc_report_interval:255
[D][05:19:26][COMM]index:84,multirider_threshold_p2:255
[D][05:19:26][COMM]index:85,multirider_strategy:255
[D][05:19:26][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:26][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:26][COMM]index:90,weight_param:0xFF
[D][05:19:26][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:26][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:26][COMM]index:95,current_limit:0xFF
[D][05:19:26][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:26][COMM]index:100,location_mode:0xFF

[W][05:19:26][PROT]remove success[1629955166],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:26][PROT]add success [1629955166],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:26][COMM]Main Task receive event:131 finished processing
[D][05:19:26][PROT]index:0 1629955166
[D][05:19:26][PROT]is_send:0
[D][05:19:26][PROT]sequence_num:7
[D][05:19:26][PROT]retry_timeout:0
[D][05:19:26][PROT]retry_times:1
[D][05:19:26][PROT]send_path:0x2
[D][05:19:26][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:26][PROT]=========================================

2025-07-31 18:07:12:092 ==>> ==================
[W][05:19:26][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955166]
[D][05:19:26][PROT]===========================================================
[D][05:19:26][PROT]sending traceid [9999999999900008]
[D][05:19:26][PROT]Send_TO_M2M [1629955166]
[D][05:19:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:26][SAL ]sock send credit cnt[6]
[D][05:19:26][SAL ]sock send ind credit cnt[6]
[D][05:19:26][M2M ]m2m send data len[294]
[D][05:19:26][SAL ]Cellular task submsg id[10]
[D][05:19:26][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:26][CAT1]gsm read msg sub id: 15
[D][05:19:26][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:26][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88B2C6F59CFB26180BC3665AC7AFA872DF554325C3D6F288E23449413FADF4D50ED97A9F4F552B28675F2424DB3ECF4C7BA0F402D3C1D6139802EB64DB7593BE58006CEDFBF705AA614B5E629C40794640A4EF998739F4840F43A7E5A9FD8DFF66FB431E346961E276F472204747A1FCA36ED3C7AF772AE0E5DA901877

2025-07-31 18:07:12:182 ==>> A7C804F49BF7B7
[D][05:19:26][CAT1]<<< 
SEND OK

[D][05:19:26][CAT1]exec over: func id: 15, ret: 11
[D][05:19:26][CAT1]sub id: 15, ret: 11

[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:26][M2M ]g_m2m_is_idle become true
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:26][PROT]M2M Send ok [1629955166]
$GBGGA,100711.026,2301.2573775,N,11421.9409407,E,1,10,1.54,67.796,M,-1.770,M,,*5E

$GBGSA,A,3,13,08,42,16,26,38,39,24,21,14,,,3.86,1.54,3.54,4*09

$GBGSV,6,1,24,13,81,259,41,8,80,183,40,42,65,2,41,16,65,310,39,1*47

$GBGSV,6,2,24,26,64,30,41,38,63,167,41,39,63,339,40,3,62,191,41,1*76

$GBGSV,6,3,24,24,61,229,42,6,54,7,37,59,52,130,40,1,48,126,38,1*71

$GBGSV,6,4

2025-07-31 18:07:12:287 ==>>                                                                                                                                                                                

2025-07-31 18:07:12:392 ==>>                                                                                                                                                                                                                                                                                                                                                                                                               24,7,15,181,30,40,11,169,30,33,,,37,11,,,,1*45

$GBGSV,2,1,06,42,65,2,41,26,64,30,41,38,63,167,39,39,63,339,40,5*43

$GBGSV,2,2,06,24,61,229,42,21,46,109,40,5*77

$GBRMC,100712.006,A,2301.2576209,N,11421.9411631,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,100712.006,0.865,0.193,0.193,0.297,1.209,1.579,8.362*7E

[W][05:19:28][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:28][CAT1]gsm read msg sub id: 12
[D][05:19:28][CAT1]SEND RAW data >>> AT+CSQ



2025-07-31 18:07:13:481 ==>> [D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:2

2025-07-31 18:07:13:586 ==>> 9][COMM]msg 02E4 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100023. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A 

2025-07-31 18:07:13:691 ==>> loss. last_tick:0. cur_tick:100023. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100024. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100024. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100024. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100025. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100025. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100025. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100026
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100026
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100026
$GBGGA,100713.000,2301.2577128,N,11421.9412883,E,1,14,1.08,70.267,M,-1.770,M,,*58

$GBGSA,A,3,13,08,42,16,26,03,38,39,24,59,01,21,2.88,1.08,2.67,4*05

$GBGSA,A,3,60,14,,,,,,,,,,,2.88,1.08,2.67,4*03

$GBGSV,6,1,23,13,81,258,40,8,80,183,40,42,65,2,40,16,65,310,39,1*41

$GBGSV,6,2,23,26,64,30,41,3,64,190,41,38,63,167,41,39,63,3

2025-07-31 18:07:13:766 ==>> 39,39,1*78

$GBGSV,6,3,23,24,61,229,42,6,54,7,37,59,52,126,40,1,47,123,37,1*74

$GBGSV,6,4,23,21,46,109,41,2,46,239,36,60,44,243,41,9,42,282,37,1*71

$GBGSV,6,5,23,14,42,333,36,4,32,112,34,45,24,199,32,5,22,258,34,1*7B

$GBGSV,6,6,23,7,15,181,30,40,11,169,30,33,,,37,1*42

$GBGSV,2,1,06,42,65,2,42,26,64,30,41,38,63,167,40,39,63,339,41,5*4F

$GBGSV,2,2,06,24,61,229,42,21,46,109,40,5*77

$GBRMC,100713.000,A,2301.2577128,N,11421.9412883,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100713.000,0.846,0.241,0.240,0.374,0.996,1.295,7.234*71

[D][05:19:29][COMM]read battery soc:255


2025-07-31 18:07:13:976 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:07:14:396 ==>> $GBGGA,100714.000,2301.2576535,N,11421.9413351,E,1,15,1.04,70.652,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,42,16,26,03,38,39,24,09,59,01,2.87,1.04,2.67,4*0C

$GBGSA,A,3,21,60,14,,,,,,,,,,2.87,1.04,2.67,4*03

$GBGSV,6,1,23,13,81,258,41,8,80,183,40,42,65,2,40,16,65,310,39,1*40

$GBGSV,6,2,23,26,64,30,41,3,64,190,41,38,63,167,41,39,63,339,40,1*76

$GBGSV,6,3,23,24,61,229,42,9,58,276,37,6,54,7,37,59,52,126,40,1*71

$GBGSV,6,4,23,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*74

$GBGSV,6,5,23,14,42,333,36,4,32,112,33,45,24,199,31,5,22,258,33,1*78

$GBGSV,6,6,23,33,17,322,37,7,15,181,30,40,11,169,30,1*77

$GBGSV,2,1,06,42,65,2,43,26,64,30,41,38,63,167,40,39,63,339,41,5*4E

$GBGSV,2,2,06,24,61,229,42,21,46,109,41,5*76

$GBRMC,100714.000,A,2301.2576535,N,11421.9413351,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100714.000,0.981,0.226,0.228,0.344,0.997,1.227,6.467*79

[W][05:19:30][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 18:07:15:459 ==>> $GBGGA,100715.000,2301.2576084,N,11421.9413488,E,1,20,0.76,70.981,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,23,13,81,258,40,8,80,183,40,42,65,2,40,16,65,310,39,1*41

$GBGSV,6,2,23,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*42

$GBGSV,6,3,23,39,63,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*49

$GBGSV,6,4,23,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*74

$GBGSV,6,5,23,14,42,333,37,4,32,112,33,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,23,7,15,177,30,45,8,39,31,40,8,166,30,1*49

$GBGSV,2,1,07,42,65,2,43,26,64,30,41,38,63,167,40,39,63,339,40,5*4E

$GBGSV,2,2,07,24,61,229,42,21,46,109,41,33,17,322,32,5*43

$GBRMC,100715.000,A,2301.2576084,N,11421.9413488,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,100715.000,1.658,0.472,0.431,0.552,1.444,1.580,5.469*7F

[D][05:19:31][CAT1]SEND RAW data timeout
[D][05:19:31][CAT1]exec over: func id: 12, ret: -52
[D][05:19:31][CAT1]gsm read msg sub id: 12
[D][05:19:31][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:31][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:31][CAT1]exec over: func id: 12, ret: 21
[D

2025-07-31 18:07:15:489 ==>> ][05:19:31][COMM]read battery soc:255


2025-07-31 18:07:15:612 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 18:07:15:620 ==>> 检测【关闭GSM联网】
2025-07-31 18:07:15:645 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:07:15:839 ==>> [W][05:19:31][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:31][COMM]GSM test
[D][05:19:31][COMM]GSM test disable


2025-07-31 18:07:15:887 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:07:15:895 ==>> 检测【4G联网测试】
2025-07-31 18:07:15:902 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:07:16:050 ==>> [D][05:19:32][PROT]CLEAN,SEND:0
[D][05:19:32][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:32][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:32][PROT]CLEAN:0


2025-07-31 18:07:16:878 ==>> [D][05:19:32][COMM]Main Task receive event:14
[D][05:19:32][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955172, allstateRepSeconds = 0
[D][05:19:32][COMM]index:0,power_mode:0xFF
[D][05:19:32][COMM]index:1,sound_mode:0xFF
[D][05:19:32][COMM]index:2,gsensor_mode:0xFF
[D][05:19:32][COMM]index:3,report_freq_mode:0xFF
[D][05:19:32][COMM]index:4,report_period:0xFF
[D][05:19:32][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:32][COMM]index:6,normal_reset_period:0xFF
[D][05:19:32][COMM]index:7,spock_over_speed:0xFF
[D][05:19:32][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:32][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:32][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:32][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:32][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:32][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:32][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:32][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:32][COMM]index:16,imu_config_params:0xFF
[D][05:19:32][COMM]index:17,long_connect_params:0xFF
[D][05:19:32][COMM]index:18,detain_mark:0xFF
[D][05:19:32][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:32][COMM]index:20,lock_pos_r

2025-07-31 18:07:16:983 ==>> eport_interval:0xFF
[D][05:19:32][COMM]index:21,mc_mode:0xFF
[D][05:19:32][COMM]index:22,S_mode:0xFF
[D][05:19:32][COMM]index:23,overweight:0xFF
[D][05:19:32][COMM]index:24,standstill_mode:0xFF
[D][05:19:32][COMM]index:25,night_mode:0xFF
[D][05:19:32][COMM]index:26,experiment1:0xFF
[D][05:19:32][COMM]index:27,experiment2:0xFF
[D][05:19:32][COMM]index:28,experiment3:0xFF
[D][05:19:32][COMM]index:29,experiment4:0xFF
[D][05:19:32][COMM]index:30,night_mode_start:0xFF
[D][05:19:32][COMM]index:31,night_mode_end:0xFF
[D][05:19:32][COMM]index:33,park_report_minutes:0xFF
[D][05:19:32][COMM]index:34,park_report_mode:0xFF
[D][05:19:32][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:32][COMM]index:38,charge_battery_para: FF
[D][05:19:32][COMM]index:39,multirider_mode:0xFF
[D][05:19:32][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:32][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:32][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:32][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:32][COMM]index:44,riding_duration_config:0xFF
[D][05:19:32][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:32][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:32][C

2025-07-31 18:07:17:088 ==>> OMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:32][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:32][COMM]index:49,mc_load_startup:0xFF
[D][05:19:32][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:32][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:32][COMM]index:52,traffic_mode:0xFF
[D][05:19:32][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:32][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:32][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:32][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:32][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:32][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:32][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:32][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:32][COMM]index:63,experiment5:0xFF
[D][05:19:32][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:32][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:32][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:32][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:32][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:32][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:32][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:

2025-07-31 18:07:17:193 ==>> 19:32][COMM]index:72,experiment6:0xFF
[D][05:19:32][COMM]index:73,experiment7:0xFF
[D][05:19:32][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:32][COMM]index:75,zero_value_from_server:-1
[D][05:19:32][COMM]index:76,multirider_threshold:255
[D][05:19:32][COMM]index:77,experiment8:255
[D][05:19:32][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:32][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:32][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:32][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:32][COMM]index:83,loc_report_interval:255
[D][05:19:32][COMM]index:84,multirider_threshold_p2:255
[D][05:19:32][COMM]index:85,multirider_strategy:255
[D][05:19:32][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:32][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:32][COMM]index:90,weight_param:0xFF
[D][05:19:32][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:32][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:32][COMM]index:95,current_limit:0xFF
[D][05:19:32][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:32][COMM]index:100,location_mode:0xFF

[W][05:19:32][PROT]remov

2025-07-31 18:07:17:298 ==>> e success[1629955172],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:32][PROT]add success [1629955172],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:32][PROT]index:0 1629955172
[D][05:19:32][PROT]is_send:0
[D][05:19:32][PROT]sequence_num:8
[D][05:19:32][PROT]retry_timeout:0
[D][05:19:32][PROT]retry_times:1
[D][05:19:32][PROT]send_path:0x2
[D][05:19:32][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:32][PROT]===========================================================
[W][05:19:32][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955172]
[D][05:19:32][PROT]===========================================================
[D][05:19:32][PROT]sending traceid [9999999999900009]
[D][05:19:32][PROT]Send_TO_M2M [1629955172]
[D][05:19:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:32][SAL ]sock send credit cnt[6]
[D][05:19:32][SAL ]sock send ind credit cnt[6]
[D][05:19:32][M2M ]m2m send data len[294]
[D][05:19:32][CAT1]gsm read msg sub id: 13
[D][05:19:32][SAL ]Cellular task submsg id[10]
[D][05:19:32][SAL ]cellular SEND socket id[

2025-07-31 18:07:17:403 ==>> 0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:32][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:32][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:32][CAT1]exec over: func id: 13, ret: 21
[D][05:19:32][M2M ]get csq[20]
[D][05:19:32][CAT1]gsm read msg sub id: 15
[D][05:19:32][CAT1]tx ret[17] >>> AT+QISEND=0,294

$GBGGA,100716.000,2301.2575992,N,11421.9413526,E,1,20,0.76,71.126,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,23,13,81,258,41,8,80,183,40,42,65,2,40,16,65,310,39,1*40

$GBGSV,6,2,23,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*43

$GBGSV,6,3,23,39,63,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*49

$GBGSV,6,4,23,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*74

$GBGSV,6,5,23,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7B

$GBGSV,6,6,23,7,15,177,31,45,8,39,31,40,8,166,31,1*49

$GBGSV,3,1,09,42,65,2,43,26,64,30,41,38,63,167,40,39,63,339,40,5*41

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,32,45,8,39,32,5*7E

$GBGSV,3,3,09,40,8,166,28,5*7C

$GBRMC,100716.000,A,23

2025-07-31 18:07:17:478 ==>> 01.2575992,N,11421.9413526,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100716.000,1.981,0.287,0.270,0.349,1.623,1.719,4.854*76

[D][05:19:32][CAT1]<<< 
ERROR

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:32][PROT]remove success[1629955172],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:32][PROT]add success [1629955172],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:32][COMM]------>period, report file manifest
[D][05:19:32][COMM]Main Task receive event:14 finished processing
[D][05:19:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:32][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 18:07:17:583 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 18:07:18:373 ==>> $GBGGA,100718.000,2301.2576060,N,11421.9413324,E,1,20,0.76,71.657,M,-1.770,M,,*51

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,39,42,65,2,40,16,65,310,39,1*48

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,39,24,61,229,41,9,58,276,37,59,52,126,40,1*4D

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,30,45,8,39,32,40,8,166,31,1*4E

$GBGSV,3,1,09,42,65,2,43,26,64,30,41,38,63,167,41,39,63,339,41,5*41

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,32,45,8,39,33,5*7E

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100718.000,A,2301.2576060,N,11421.9413324,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,100718.000,2.124,0.238,0.228,0.295,1.667,1.730,4.130*75



2025-07-31 18:07:19:380 ==>> $GBGGA,100719.000,2301.2575958,N,11421.9413586,E,1,20,0.76,71.730,M,-1.770,M,,*5F

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,41,8,80,183,40,42,65,2,41,16,65,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,34,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,30,10,9,191,30,45,8,39,32,40,8,166,31,1*4E

$GBGSV,3,1,09,42,65,2,43,26,64,30,42,38,63,167,41,39,63,339,40,5*43

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100719.000,A,2301.2575958,N,11421.9413586,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100719.000,1.779,0.277,0.262,0.338,1.416,1.475,3.738*75

                                         

2025-07-31 18:07:20:385 ==>> $GBGGA,100720.000,2301.2576027,N,11421.9413699,E,1,20,0.76,71.826,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,41,8,80,183,40,42,65,2,41,16,65,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,34,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,32,40,8,166,31,1*47

$GBGSV,3,1,09,42,65,2,43,26,64,30,42,38,63,167,41,39,63,339,40,5*43

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100720.000,A,2301.2576027,N,11421.9413699,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100720.000,1.688,0.225,0.217,0.281,1.336,1.389,3.487*70



2025-07-31 18:07:21:385 ==>> $GBGGA,100721.000,2301.2576002,N,11421.9413746,E,1,20,0.76,71.779,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,41,8,80,183,40,42,65,3,40,16,65,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,37,60,44,243,41,1*7D

$GBGSV,6,5,24,14,42,333,37,4,32,112,33,5,22,258,34,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,63,339,40,5*41

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,33,5*7E

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100721.000,A,2301.2576002,N,11421.9413746,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100721.000,1.655,0.248,0.237,0.306,1.300,1.347,3.298*79



2025-07-31 18:07:21:415 ==>>                                          

2025-07-31 18:07:21:931 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:07:22:777 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:38][COMM]Main Task receive event:14
[D][05:19:38][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955178, allstateRepSeconds = 0
[D][05:19:38][COMM]index:0,power_mode:0xFF
[D][05:19:38][COMM]index:1,sound_mode:0xFF
[D][05:19:38][COMM]index:2,gsensor_mode:0xFF
[D][05:19:38][COMM]index:3,report_freq_mode:0xFF
[D][05:19:38][COMM]index:4,report_period:0xFF
[D][05:19:38][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:38][COMM]index:6,normal_reset_period:0xFF
[D][05:19:38][COMM]index:7,spock_over_speed:0xFF
[D][05:19:38][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:38][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:38][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:38][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:38][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:38][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:38][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:38][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:38][COMM]index:16,imu_config_params:0xFF
[D][05:19:38][COMM]index:17,long_connect_params:0xFF
[D][05:19:38][COMM]index:18,detain_mark:0xFF
[D][05:19:38][COMM]ind

2025-07-31 18:07:22:882 ==>> ex:19,lock_pos_report_count:0xFF
[D][05:19:38][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:38][COMM]index:21,mc_mode:0xFF
[D][05:19:38][COMM]index:22,S_mode:0xFF
[D][05:19:38][COMM]index:23,overweight:0xFF
[D][05:19:38][COMM]index:24,standstill_mode:0xFF
[D][05:19:38][COMM]index:25,night_mode:0xFF
[D][05:19:38][COMM]index:26,experiment1:0xFF
[D][05:19:38][COMM]index:27,experiment2:0xFF
[D][05:19:38][COMM]index:28,experiment3:0xFF
[D][05:19:38][COMM]index:29,experiment4:0xFF
[D][05:19:38][COMM]index:30,night_mode_start:0xFF
[D][05:19:38][COMM]index:31,night_mode_end:0xFF
[D][05:19:38][COMM]index:33,park_report_minutes:0xFF
[D][05:19:38][COMM]index:34,park_report_mode:0xFF
[D][05:19:38][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:38][COMM]index:38,charge_battery_para: FF
[D][05:19:38][COMM]index:39,multirider_mode:0xFF
[D][05:19:38][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:38][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:38][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:38][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:38][COMM]index:44,riding_duration_config:0xFF
[D][05:19:38][COMM]index:45,camera_park_angle_cfg:0xFF
[D][0

2025-07-31 18:07:22:988 ==>> 5:19:38][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:38][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:38][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:38][COMM]index:49,mc_load_startup:0xFF
[D][05:19:38][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:38][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:38][COMM]index:52,traffic_mode:0xFF
[D][05:19:38][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:38][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:38][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:38][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:38][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:38][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:38][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:38][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:38][COMM]index:63,experiment5:0xFF
[D][05:19:38][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:38][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:38][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:38][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:38][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:38][COMM]index:70,camera_park_light_cfg:0xFF
[D

2025-07-31 18:07:23:092 ==>> ][05:19:38][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:38][COMM]index:72,experiment6:0xFF
[D][05:19:38][COMM]index:73,experiment7:0xFF
[D][05:19:38][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:38][COMM]index:75,zero_value_from_server:-1
[D][05:19:38][COMM]index:76,multirider_threshold:255
[D][05:19:38][COMM]index:77,experiment8:255
[D][05:19:38][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:38][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:38][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:38][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:38][COMM]index:83,loc_report_interval:255
[D][05:19:38][COMM]index:84,multirider_threshold_p2:255
[D][05:19:38][COMM]index:85,multirider_strategy:255
[D][05:19:38][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:38][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:38][COMM]index:90,weight_param:0xFF
[D][05:19:38][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:38][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:38][COMM]index:95,current_limit:0xFF
[D][05:19:38][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:

2025-07-31 18:07:23:198 ==>> 19:38][COMM]index:100,location_mode:0xFF

[W][05:19:38][PROT]remove success[1629955178],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:38][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:38][PROT]add success [1629955178],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:38][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,100722.000,2301.2576024,N,11421.9413569,E,1,20,0.76,71.840,M,-1.770,M,,*5F

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,41,16,65,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*45

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,37,60,44,243,41,1*72

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,34,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,32,40,8,166,31,1*47

$GBGSV,3,1,09,42,65,3,43,26,64,30,42,38,63,167,41,39,63,339,40,5*42

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,33,5*7E

$

2025-07-31 18:07:23:243 ==>> GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100722.000,A,2301.2576024,N,11421.9413569,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,100722.000,1.616,0.248,0.236,0.306,1.261,1.304,3.141*7A



2025-07-31 18:07:23:348 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 18:07:23:378 ==>>                                                                                                                                

2025-07-31 18:07:23:438 ==>>                                          

2025-07-31 18:07:24:375 ==>> $GBGGA,100724.000,2301.2576029,N,11421.9413864,E,1,20,0.76,71.990,M,-1.770,M,,*58

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,41,16,64,310,39,1*47

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*45

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,40,1*7D

$GBGSV,6,5,24,14,42,333,37,4,32,112,33,5,22,258,34,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,40,39,63,339,40,5*40

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,33,5*7E

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100724.000,A,2301.2576029,N,11421.9413864,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,100724.000,1.694,0.248,0.236,0.306,1.304,1.339,2.974*75

[D][05:19:40][M2M ]get csq[-1]


2025-07-31 18:07:24:586 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:40][PROT]remove success[1629955180],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:40][PROT]add success [1629955180],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:40][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:40][COMM][LOC]wifi scan is already running, error
[D][05:19:40][COMM]Main Task receive event:14 finished processing
[D][05:19:40][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:40][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 18:07:25:374 ==>> $GBGGA,100725.000,2301.2576106,N,11421.9413963,E,1,20,0.76,72.050,M,-1.770,M,,*55

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*45

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,32,40,8,166,31,1*47

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,63,339,40,5*41

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,34,5*79

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100725.000,A,2301.2576106,N,11421.9413963,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,100725.000,1.795,0.278,0.263,0.339,1.372,1.404,2.947*73



2025-07-31 18:07:25:434 ==>>                                          

2025-07-31 18:07:26:497 ==>> [D][05:19:42][CAT1]exec over: func id: 15, ret: -93
[D][05:19:42][CAT1]sub id: 15, ret: -93

[D][05:19:42][SAL ]Cellular task submsg id[68]
[D][05:19:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:42][SAL ]socket send fail. id[4]
[D][05:19:42][SAL ]select read evt socket_id[4], p_data[0] len[0]
$GBGGA,100726.000,2301.2576015,N,11421.9413941,E,1,20,0.76,72.036,M,-1.770,M,,*55

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,41,16,64,310,39,1*47

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,32,40,8,166,31,1*47

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,40,39,63,339,41,5*41

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,34,5*79

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100726.000,A,2301.2576015,N,11421.9413941,E,0.001,0.00,3

2025-07-31 18:07:26:602 ==>> 10725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:19:42][CAT1]gsm read msg sub id: 21
[D][05:19:42][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:42][M2M ]m2m select fd[4]
[D][05:19:42][M2M ]socket[4] Link is disconnected
[D][05:19:42][M2M ]tcpclient close[4]
[D][05:19:42][SAL ]socket[4] has closed
[D][05:19:42][PROT]protocol read data ok
[E][05:19:42][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
$GBGST,100726.000,2.034,0.242,0.231,0.299,1.535,1.564,3.008*7B

[E][05:19:42][PROT]M2M Send Fail [1629955182]
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]cell info report total[0]
[D][05:19:42][CAT1]exec over: func id: 21, ret: 6
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:42][CAT1]gsm read msg sub id: 13
[D][05:19:42][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:42][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:42][CAT1]exec over: func id: 13, ret: 21
[D][05:19:42][CAT1]gsm read msg sub id: 10
[D][05:19:42][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:42][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:42][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 18:07:26:707 ==>>                                                                                                                                                                              

2025-07-31 18:07:26:797 ==>>                                                                                                                                                                                  9:42][SAL ]open socket ind id[4], rst[0]
[D][05:19:42][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:42][SAL ]Cellular task submsg id[8]
[D][05:19:42][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:42][CAT1]gsm read msg sub id: 8
[D][05:19:42][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:42][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:42][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:07:26:993 ==>> [D][05:19:42][CAT1]pdpdeact urc len[22]


2025-07-31 18:07:27:375 ==>> $GBGGA,100727.000,2301.2575998,N,11421.9414067,E,1,20,0.76,72.051,M,-1.770,M,,*50

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,41,16,64,310,39,1*47

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,63,339,40,24,61,229,42,9,58,276,37,59,52,126,40,1*40

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,34,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,32,40,8,166,31,1*47

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,63,339,40,5*41

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,34,5*78

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100727.000,A,2301.2575998,N,11421.9414067,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100727.000,1.990,0.229,0.220,0.286,1.500,1.528,2.925*74



2025-07-31 18:07:27:435 ==>> [D][05:19:43][COMM]read battery soc:255


2025-07-31 18:07:27:982 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:07:29:018 ==>> [D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:43][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:43][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:43][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:43][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:44][CAT1]<<< 
OK

[D][05:19:44][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:44][CAT1]<<< 
OK

[D][05:19:44][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:44][CAT1]<<< 
OK

[D][05:19:44][CAT1]exec over: func id: 8, ret: 6
[W][05:19:44][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:44][COMM]Main Task receive event:14
[D][05:19:44][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955184, allstateRepSeconds = 0
[D][05:19:44][COMM]index:0,power_mode:0xFF
[D][05:19:44][COMM]index:1,sound_mode:0xFF
[D][05:19:44][COMM]index:2,gsensor_mode:0xFF
[D][05:19:44][COMM]index:3,report_freq_mode:0xFF
[D][05:19:44][COMM]index:4,report_period:0xFF
[D][05:19:44][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:44][COMM]index:6,normal_reset_period:0xFF
[D][05:19:44][COMM]index:7,spock_over_speed:0xFF
[D][05:19:44][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:44][COMM]i

2025-07-31 18:07:29:122 ==>> ndex:9,spock_report_period_unlock:0xFF
[D][05:19:44][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:44][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:44][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:44][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:44][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:44][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:44][COMM]index:16,imu_config_params:0xFF
[D][05:19:44][COMM]index:17,long_connect_params:0xFF
[D][05:19:44][COMM]index:18,detain_mark:0xFF
[D][05:19:44][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:44][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:44][COMM]index:21,mc_mode:0xFF
[D][05:19:44][COMM]index:22,S_mode:0xFF
[D][05:19:44][COMM]index:23,overweight:0xFF
[D][05:19:44][COMM]index:24,standstill_mode:0xFF
[D][05:19:44][COMM]index:25,night_mode:0xFF
[D][05:19:44][COMM]index:26,experiment1:0xFF
[D][05:19:44][COMM]index:27,experiment2:0xFF
[D][05:19:44][COMM]index:28,experiment3:0xFF
[D][05:19:44][COMM]index:29,experiment4:0xFF
[D][05:19:44][COMM]index:30,night_mode_start:0xFF
[D][05:19:44][COMM]index:31,night_mode_end:0xFF
[D][05:19:44][COMM]index:33,park_report_minutes:0xFF
[D][05:19:44][COMM]

2025-07-31 18:07:29:227 ==>> index:34,park_report_mode:0xFF
[D][05:19:44][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:44][COMM]index:38,charge_battery_para: FF
[D][05:19:44][COMM]index:39,multirider_mode:0xFF
[D][05:19:44][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:44][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:44][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:44][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:44][COMM]index:44,riding_duration_config:0xFF
[D][05:19:44][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:44][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:44][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:44][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:44][COMM]index:49,mc_load_startup:0xFF
[D][05:19:44][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:44][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:44][COMM]index:52,traffic_mode:0xFF
[D][05:19:44][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:44][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:44][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:44][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:44][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:44][COMM]index:59,t

2025-07-31 18:07:29:332 ==>> raffic_retrograde_threshold:0xFF
[D][05:19:44][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:44][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:44][COMM]index:63,experiment5:0xFF
[D][05:19:44][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:44][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:44][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:44][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:44][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:44][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:44][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:44][COMM]index:72,experiment6:0xFF
[D][05:19:44][COMM]index:73,experiment7:0xFF
[D][05:19:44][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:44][COMM]index:75,zero_value_from_server:-1
[D][05:19:44][COMM]index:76,multirider_threshold:255
[D][05:19:44][COMM]index:77,experiment8:255
[D][05:19:44][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:44][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:44][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:44][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:44][COMM]index:83,loc_report_inte

2025-07-31 18:07:29:437 ==>> rval:255
[D][05:19:44][COMM]index:84,multirider_threshold_p2:255
[D][05:19:44][COMM]index:85,multirider_strategy:255
[D][05:19:44][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:44][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:44][COMM]index:90,weight_param:0xFF
[D][05:19:44][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:44][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:44][COMM]index:95,current_limit:0xFF
[D][05:19:44][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:44][COMM]index:100,location_mode:0xFF

[W][05:19:44][PROT]remove success[1629955184],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:44][PROT]add success [1629955184],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][CAT1]gsm read msg sub id: 13
[D][05:19:44][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:44][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:44][CAT1]exec over: func id: 13, ret: 21
[D][05:19:44][M2M ]get csq[20]
$GBGGA,100728.000,2301.2576034,N,11421.9414017,E,1,20,0.76,72.073,M,-1.770,M,,*54

$GBGSA,A,

2025-07-31 18:07:29:542 ==>> 3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,38,1*47

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*45

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,36,4,32,112,33,5,22,258,33,33,17,322,37,1*7A

$GBGSV,6,6,24,7,15,177,31,10,9,191,29,45,8,39,31,40,8,166,31,1*44

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,62,339,41,5*41

$GBGSV,3,2,09,24,61,229,42,21,46,109,41,33,17,322,33,45,8,39,33,5*7E

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100728.000,A,2301.2576034,N,11421.9414017,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100728.000,2.062,0.255,0.243,0.313,1.546,1.571,2.900*76

[D][05:19:44][CAT1]opened : 0, 0
[D][05:19:44][SAL ]Cellular task submsg id[68]
[D][05:19:44][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:44][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:44][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:44][M2M ]g_m2m_

2025-07-31 18:07:29:647 ==>> is_idle become true
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:44][PROT]index:0 1629955184
[D][05:19:44][PROT]is_send:0
[D][05:19:44][PROT]sequence_num:12
[D][05:19:44][PROT]retry_timeout:0
[D][05:19:44][PROT]retry_times:1
[D][05:19:44][PROT]send_path:0x2
[D][05:19:44][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:44][PROT]===========================================================
[W][05:19:44][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955184]
[D][05:19:44][PROT]===========================================================
[D][05:19:44][PROT]sending traceid [999999999990000D]
[D][05:19:44][PROT]Send_TO_M2M [1629955184]
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:44][SAL ]sock send credit cnt[6]
[D][05:19:44][SAL ]sock send ind credit cnt[6]
[D][05:19:44][M2M ]m2m send data len[294]
[D][05:19:44][SAL ]Cellular task submsg id[10]
[D][05:19:44][SAL ]cellular SEND socket

2025-07-31 18:07:29:752 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 18:07:30:375 ==>> $GBGGA,100730.000,2301.2576107,N,11421.9413855,E,1,20,0.76,72.183,M,-1.770,M,,*5B

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,40,39,62,339,41,5*40

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100730.000,A,2301.2576107,N,11421.9413855,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100730.000,2.095,0.257,0.245,0.316,1.562,1.584,2.818*72



2025-07-31 18:07:31:379 ==>> $GBGGA,100731.000,2301.2576081,N,11421.9413680,E,1,20,0.76,72.222,M,-1.770,M,,*5B

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,41,16,64,310,39,1*47

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,62,339,41,5*41

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100731.000,A,2301.2576081,N,11421.9413680,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,100731.000,2.167,0.254,0.242,0.312,1.608,1.628,2.812*7F



2025-07-31 18:07:31:484 ==>> [D][05:19:47][COMM]read battery soc:255


2025-07-31 18:07:32:416 ==>> $GBGGA,100732.000,2301.2575987,N,11421.9413646,E,1,20,0.76,72.211,M,-1.770,M,,*5E

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,38,21,46,109,41,2,46,239,36,60,44,243,41,1*7C

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,33,33,17,322,37,1*7C

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,62,339,40,5*40

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100732.000,A,2301.2575987,N,11421.9413646,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100732.000,2.392,0.231,0.222,0.287,1.751,1.770,2.894*73



2025-07-31 18:07:33:394 ==>> $GBGGA,100733.000,2301.2576038,N,11421.9413736,E,1,20,0.76,72.179,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,41,38,63,167,41,1*44

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,37,4,32,112,34,5,22,258,34,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,30,10,9,191,29,45,8,39,32,40,8,166,31,1*46

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,62,339,40,5*40

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100733.000,A,2301.2576038,N,11421.9413736,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,100733.000,2.449,0.265,0.252,0.324,1.786,1.803,2.894*7C



2025-07-31 18:07:33:484 ==>> [D][05:19:49][COMM]read battery soc:255


2025-07-31 18:07:33:803 ==>> [D][05:19:49][PROT]CLEAN,SEND:0
[D][05:19:49][PROT]index:1 1629955189
[D][05:19:49][PROT]is_send:0
[D][05:19:49][PROT]sequence_num:13
[D][05:19:49][PROT]retry_timeout:0
[D][05:19:49][PROT]retry_times:1
[D][05:19:49][PROT]send_path:0x2
[D][05:19:49][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:49][PROT]===========================================================
[W][05:19:49][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955189]
[D][05:19:49][PROT]===========================================================
[D][05:19:49][PROT]sending traceid [999999999990000E]
[D][05:19:49][PROT]Send_TO_M2M [1629955189]
[D][05:19:49][PROT]CLEAN:0
[D][05:19:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:49][SAL ]sock send credit cnt[6]
[D][05:19:49][SAL ]sock send ind credit cnt[6]
[D][05:19:49][M2M ]m2m send data len[166]
[D][05:19:49][SAL ]Cellular task submsg id[10]
[D][05:19:49][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:49][CAT1]gsm read msg sub id: 15
[D][05:19:49][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:49][CAT1]Send Data To Server[166][169] ... ->:
0053B98B113311331133113311331B88BADFBEF7AFD47C52A5EE69F8AB9ADFA87799D79C2A9A7B

2025-07-31 18:07:33:878 ==>> FEB846329630440AB8E626E0366BA62DC926E19F2188E7A7B06E0D2DE0E54C78A08ACFCAE5B7A854D4F8D273
[D][05:19:49][CAT1]<<< 
SEND OK

[D][05:19:49][CAT1]exec over: func id: 15, ret: 11
[D][05:19:49][CAT1]sub id: 15, ret: 11

[D][05:19:49][SAL ]Cellular task submsg id[68]
[D][05:19:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:49][M2M ]g_m2m_is_idle become true
[D][05:19:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:49][PROT]M2M Send ok [1629955189]


2025-07-31 18:07:34:058 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:07:34:072 ==>> 检测【关闭GPS】
2025-07-31 18:07:34:098 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:07:34:457 ==>> $GBGGA,100734.000,2301.2576076,N,11421.9413740,E,1,20,0.76,72.216,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,42,16,06,26,03,38,39,24,09,59,1.14,0.76,0.85,4*08

$GBGSA,A,3,01,21,60,14,33,07,45,40,,,,,1.14,0.76,0.85,4*03

$GBGSV,6,1,24,13,81,258,40,8,80,183,40,42,65,3,40,16,64,310,39,1*46

$GBGSV,6,2,24,6,64,304,37,26,64,30,41,3,64,190,40,38,63,167,41,1*45

$GBGSV,6,3,24,39,62,339,39,24,61,229,42,9,58,276,37,59,52,126,40,1*4F

$GBGSV,6,4,24,1,47,123,37,21,46,109,41,2,46,239,36,60,44,243,41,1*73

$GBGSV,6,5,24,14,42,333,37,4,32,112,33,5,22,258,33,33,17,322,37,1*7B

$GBGSV,6,6,24,7,15,177,30,10,9,191,30,45,8,39,32,40,8,166,31,1*4E

$GBGSV,3,1,09,42,65,3,43,26,64,30,41,38,63,167,41,39,62,339,40,5*40

$GBGSV,3,2,09,24,61,229,43,21,46,109,41,33,17,322,33,45,8,39,33,5*7F

$GBGSV,3,3,09,40,8,166,29,5*7D

$GBRMC,100734.000,A,2301.2576076,N,11421.9413740,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,100734.000,2.188,0.262,0.249,0.321,1.615,1.632,2.723*7F

[W][05:19:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:50][GNSS]stop locating
[D][05:19:50][GNSS]stop event:8
[D][05:19:50][GNSS]GPS stop. ret=0
[D][05:19:50][GNSS]all continue locatio

2025-07-31 18:07:34:532 ==>> n stop
[W][05:19:50][GNSS]stop locating
[D][05:19:50][GNSS]all sing location stop
[D][05:19:50][CAT1]gsm read msg sub id: 24
[D][05:19:50][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:50][CAT1]<<< 
OK

[D][05:19:50][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:50][CAT1]<<< 
OK

[D][05:19:50][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:50][CAT1]<<< 
OK

[D][05:19:50][CAT1]exec over: func id: 24, ret: 6
[D][05:19:50][CAT1]sub id: 24, ret: 6

[D][05:19:50][GNSS]recv submsg id[1]
[D][05:19:50][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:50][GNSS]location stop evt done evt


2025-07-31 18:07:34:616 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:07:34:625 ==>> 检测【清空消息队列2】
2025-07-31 18:07:34:638 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:07:34:850 ==>> [W][05:19:50][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:50][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:07:35:268 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:07:35:280 ==>> 检测【轮动检测】
2025-07-31 18:07:35:298 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:07:35:359 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 18:07:35:509 ==>> [D][05:19:51][COMM]Wheel signal detected, lock state = 2, singal = 1
[D][05:19:51][COMM]read battery soc:255


2025-07-31 18:07:35:779 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:07:35:854 ==>> 3A A3 01 01 A3 


2025-07-31 18:07:35:959 ==>> ON_OUT1
OVER 150


2025-07-31 18:07:36:060 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:07:36:077 ==>> 检测【关闭小电池】
2025-07-31 18:07:36:099 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:07:36:154 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:07:36:352 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:07:36:366 ==>> 检测【进入休眠模式】
2025-07-31 18:07:36:397 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:07:36:538 ==>> [W][05:19:52][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:07:36:628 ==>>                       n Task receive event:28
[D][05:19:52][COMM]main task tmp_sleep_event = 8
[D][05:19:52][COMM]prepare to sleep
[D][05:19:52][CAT1]gsm read msg sub id: 12
[D][05:19:52][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:07:37:446 ==>> [D][05:19:53][CAT1]<<< 
OK

[D][05:19:53][CAT1]exec over: func id: 12, ret: 6
[D][05:19:53][M2M ]tcpclient close[4]
[D][05:19:53][SAL ]Cellular task submsg id[12]
[D][05:19:53][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:53][CAT1]gsm read msg sub id: 9
[D][05:19:53][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:53][CAT1]<<< 
OK

[D][05:19:53][CAT1]exec over: func id: 9, ret: 6
[D][05:19:53][CAT1]sub id: 9, ret: 6

[D][05:19:53][SAL ]Cellular task submsg id[68]
[D][05:19:53][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:53][SAL ]socket close ind. id[4]
[D][05:19:53][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:53][COMM]1x1 frm_can_tp_send ok
[D][05:19:53][GNSS]handler GSMGet Base timeout
[D][05:19:53][CAT1]pdpdeact urc len[22]


2025-07-31 18:07:37:491 ==>>                    read battery soc:255


2025-07-31 18:07:37:716 ==>> [E][05:19:53][COMM]1x1 rx timeout
[D][05:19:53][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:07:38:230 ==>> [E][05:19:54][COMM]1x1 rx timeout
[E][05:19:54][COMM]1x1 tp timeout
[E][05:19:54][COMM]1x1 error -3.
[W][05:19:54][COMM]CAN STOP!
[D][05:19:54][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:54][COMM]------------ready to Power off Acckey 1------------
[D][05:19:54][COMM]------------ready to Power off Acckey 2------------
[D][05:19:54][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:54][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1275
[D][05:19:54][COMM]bat sleep fail, reason:-1
[D][05:19:54][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:54][COMM]accel parse set 0
[D][05:19:54][COMM]imu rest ok. 125123
[D][05:19:54][COMM]imu sleep 0
[W][05:19:54][COMM]now sleep


2025-07-31 18:07:38:430 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:07:38:445 ==>> 检测【检测33V休眠电流】
2025-07-31 18:07:38:465 ==>> 开始33V电流采样
2025-07-31 18:07:38:480 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:38:534 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:07:39:539 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:07:39:599 ==>> Current33V:????:18.42

2025-07-31 18:07:40:050 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:40:060 ==>> 【检测33V休眠电流】通过,【18.42uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:07:40:082 ==>> 该项需要延时执行
2025-07-31 18:07:42:063 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:07:42:078 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:07:42:099 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:07:42:172 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1663mV
Get AD_V4 1mV
Get AD_V5 2747mV
Get AD_V6 1923mV
Get AD_V7 1095mV
OVER 150


2025-07-31 18:07:43:090 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:07:43:099 ==>> 检测【打开小电池2】
2025-07-31 18:07:43:123 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:07:43:161 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:07:43:364 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:07:43:375 ==>> 该项需要延时执行
2025-07-31 18:07:43:872 ==>> 此处延时了:【500】毫秒
2025-07-31 18:07:43:894 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:07:43:925 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:07:43:963 ==>> 5A A5 02 5A A5 


2025-07-31 18:07:44:052 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:07:44:145 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:07:44:154 ==>> 该项需要延时执行
2025-07-31 18:07:44:647 ==>> 此处延时了:【500】毫秒
2025-07-31 18:07:44:663 ==>> 检测【进入休眠模式2】
2025-07-31 18:07:44:684 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:07:44:752 ==>> [D][05:20:00][COMM]------------ready to Power on Acckey 1------------
[D][05:20:00][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:20:00][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:20:00][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:20:00][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:20:00][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:20:00][COMM]----- get Acckey 1 and value:1------------
[W][05:20:00][COMM]CAN START!
[D][05:20:00][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:20:00][COMM]1x1 frm_can_tp_send ok
[D][05:20:00][CAT1]gsm read msg sub id: 12
[D][05:20:00][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:20:00][COM

2025-07-31 18:07:44:812 ==>> M]CAN message bat fault change: 0x01FFFFFF->0x00000000 131560
[D][05:20:00][COMM][Audio]exec status ready.
[D][05:20:00][CAT1]<<< 
OK

[D][05:20:00][CAT1]exec over: func id: 12, ret: 6
[D][05:20:00][COMM]imu wakeup ok. 131574
[D][05:20:00][COMM]imu wakeup 1
[W][05:20:00][COMM]wake up system, wakeupEvt=0x80
[D][05:20:00][COMM]frm_can_weigth_power_set 1
[D][05:20:00][COMM]Clear Sleep Block Evt
[D][05:20:00][COMM]Main Task receive event:28 finished processing


2025-07-31 18:07:45:085 ==>> [W][05:20:00][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:20:00][COMM]Main Task receive event:28
[D][05:20:00][COMM]prepare to sleep
[D][05:20:00][CAT1]gsm read msg sub id: 12
[D][05:20:00][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:20:00][CAT1]<<< 
OK

[D][05:20:00][CAT1]exec over: func id: 12, ret: 6
[D][05:20:00][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:20:00][COMM]CAN STOP!
[D][05:20:00][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:20:00][COMM]------------ready to Power off Acckey 1------------
[D][05:20:00][COMM]------------ready to Power off Acckey 2------------
[D][05:20:00][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:20:00][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 126
[D][05:20:00][COMM]bat sleep fail, reason:-1
[D][05:20:00][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:20:00][COMM]accel parse set 0
[D][05:20:00][COMM]imu rest ok. 131936
[E][05:20:00][COMM]1x1 rx timeout
[D][05:20:00][COMM]imu sleep 0
[W][05:20:00][COMM]now sleep


2025-07-31 18:07:45:185 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:07:45:195 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:07:45:224 ==>> 开始小电池电流采样
2025-07-31 18:07:45:233 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:45:299 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:07:46:305 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:07:46:380 ==>> CurrentBattery:ƽ��:69.06

2025-07-31 18:07:46:818 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:46:828 ==>> 【检测小电池休眠电流】通过,【69.06uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:07:46:850 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:07:46:865 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:07:46:953 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:07:47:107 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:07:47:122 ==>> 该项需要延时执行
2025-07-31 18:07:47:193 ==>> [D][05:20:02][COMM]------------ready to Power on Acckey 1------------
[D][05:20:02][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:20:02][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:20:02][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:20:02][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:20:02][COMM]----- get Acckey 1 and value:1------------
[W][05:20:02][COMM]CAN START!
[D][05:20:02][COMM]1x1 frm_can_tp_send ok
[D][05:20:03][CAT1]gsm read msg sub id: 12
[D][05:20:03][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:20:03][COMM][Audio]exec status ready.
[D][05:20:03][CAT1]<<< 
OK

[D][05:20:03][CAT1]exec over: func id: 12, ret: 6
[D][05:20:03][COMM]imu wakeup ok. 134041
[D][05:20:03][COMM]imu wakeup 1
[W][05:20:03][COMM]wake up system, wakeupEvt=0x80
[D][05:20:03][COMM]frm_can_weigth_power_set 1
[D][05:20:03][COMM]Clear Sleep Block Evt
[D][05:20:03][COMM]Main Task receive event:28 finished processing
[D][05:20:03][COMM]read battery soc:0


2025-07-31 18:07:47:418 ==>> [E][05:20:03][COMM]1x1 rx timeout
[E][05:20:03][COMM]1x1 tp timeout
[E][05:20:03][COMM]1x1 error -3.


2025-07-31 18:07:47:614 ==>> 此处延时了:【500】毫秒
2025-07-31 18:07:47:629 ==>> 检测【检测唤醒】
2025-07-31 18:07:47:643 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:07:47:652 ==>> [D][05:20:03][COMM]msg 02A0 loss. last_tick:134010. cur_tick:134521. period:50
[D][05:20:03][COMM]msg 02A4 loss. last_tick:134010. cur_tick:134521. period:50
[D][05:20:03][COMM]msg 02A5 loss. last_tick:134010. cur_tick:134522. period:50
[D][05:20:03][COMM]msg 02A6 loss. last_tick:134010. cur_tick:134522. period:50
[D][05:20:03][COMM]msg 02A7 loss. last_tick:134010. cur_tick:134523. period:50
[D][05:20:03][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 134523
[D][05:20:03][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 134523


2025-07-31 18:07:48:023 ==>> [W][05:20:03][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:20:03][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:03][FCTY]==========Modules-nRF5340 ==========
[D][05:20:03][FCTY]BootVersion = SA_BOOT_V109
[D][05:20:03][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:20:03][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:20:03][FCTY]DeviceID    = 460130020290675
[D][05:20:03][FCTY]HardwareID  = 867222087872109
[D][05:20:03][FCTY]MoBikeID    = 9999999999
[D][05:20:03][FCTY]LockID      = FFFFFFFFFF
[D][05:20:03][FCTY]BLEFWVersion= 105
[D][05:20:03][FCTY]BLEMacAddr   = D6BECC3E0B41
[D][05:20:03][FCTY]Bat         = 3884 mv
[D][05:20:03][FCTY]Current     = 0 ma
[D][05:20:03][FCTY]VBUS        = 2600 mv
[D][05:20:03][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:20:03][FCTY]Ext battery vol = 32, adc = 1279
[D][05:20:03][FCTY]Acckey1 vol = 5463 mv, Acckey2 vol = 0 mv
[D][05:20:03][FCTY]Bike Type flag is invalied
[D][05:20:03][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:20:03][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:20:03][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:20:03][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:20:03][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:20:03][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:20:03][FCTY]Bat1         =

2025-07-31 18:07:48:053 ==>>  3822 mv
[D][05:20:03][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:03][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:07:48:147 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:07:48:156 ==>> 检测【关机】
2025-07-31 18:07:48:179 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:07:48:278 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             0. cur_tick:135031. period:100
[D][05:20:04][COMM]msg 0301 loss. last_tick:134010. cur_tick:135032. period:100
[D][05:20:04][COMM]bat msg 0240 loss. last_tick:134010. cur_tick:135032. period:100. j,i:1 54
[D][05:20:04][COMM]bat msg 0241 loss. last_tick:134010. cur_tick:135

2025-07-31 18:07:48:353 ==>> 033. period:100. j,i:2 55
[D][05:20:04][COMM]bat msg 0242 loss. last_tick:134010. cur_tick:135033. period:100. j,i:3 56
[D][05:20:04][COMM]bat msg 0244 loss. last_tick:134010. cur_tick:135033. period:100. j,i:5 58
[D][05:20:04][COMM]bat msg 024E loss. last_tick:134010. cur_tick:135034. period:100. j,i:15 68
[D][05:20:04][COMM]bat msg 024F loss. last_tick:134010. cur_tick:135034. period:100. j,i:16 69
[D][05:20:04][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 135035
[D][05:20:04][COMM]CAN message bat fault change: 0x00000000->0x0001802E 135035
[D][05:20:04][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 135035


2025-07-31 18:07:48:443 ==>> [W][05:20:04][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 18:07:48:549 ==>> [D][05:20:04][COMM]arm_hub_enable: hub power: 0
[D][05:20:04][HSDK]hexlog index save 0 3584 131 @ 0 : 0
[D][05:20:04][HSDK]write save hexlog index [0]
[D][05:20:04][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:04][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:07:48:653 ==>> [D][05:20:04][COMM]msg 0222 loss. last_tick:134010. cur_tick:135516. period:150
[D][05:20:04][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 135518


2025-07-31 18:07:48:758 ==>> [D][05:20:04][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1


2025-07-31 18:07:48:803 ==>> [D][05:20:04][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:20:04][COMM]----- get Acckey 1 and value:1------------
[D][05:20:04][COMM]----- get Acckey 2 and value:0------------
[D][05:20:04][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:07:49:172 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:07:49:645 ==>> [D][05:20:04][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:20:04][COMM]----- get Acckey 1 and value:1------------
[D][05:20:04][COMM]----- get Acckey 2 and value:1------------
[D][05:20:04][COMM]more than the number of battery plugs
[D][05:20:04][COMM]VBUS is 1
[D][05:20:04][COMM]verify_batlock_state ret -516, soc 0
[D][05:20:04][COMM]file:B50 exist
[D][05:20:04][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:20:04][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:20:04][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:20:04][COMM]Bat auth off fail, error:-1
[D][05:20:04][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:20:04][COMM]----- get Acckey 1 and value:1------------
[D][05:20:04][COMM]----- get Acckey 2 and value:1------------
[D][05:20:04][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:20:04][COMM]----- get Acckey 1 and value:1------------
[D][05:20:04][COMM]----- get Acckey 2 and value:1------------
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:20:04][COMM]file:B50 exist
[D][05:20:04][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:20:04][COMM]read file, len

2025-07-31 18:07:49:750 ==>> :10800, num:3
[D][05:20:04][COMM]--->crc16:0xb8a
[D][05:20:04][COMM]read file success
[D][05:20:04][COMM]accel parse set 1
[D][05:20:04][COMM][Audio]mon:9,05:20:04
[D][05:20:04][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:20:04][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:20:04][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:20:04][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:20:04][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:20:04][COMM]Main Task receive event:65
[D][05:20:04][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:20:04][COMM]Main Task receive event:65 finished processing
[D][05:20:04][COMM]Main Task receive event:66
[D][05:20:04][COMM]Try to Auto Lock Bat
[D][05:20:04][COMM]Main Task receive event:66 finished processing
[D][05:20:04][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:04][COMM]Main Task receive event:60
[D][05:20:04][COMM]smart_he

2025-07-31 18:07:49:854 ==>> lmet_vol=255,255
[D][05:20:04][COMM]BAT CAN get state1 Fail 204
[D][05:20:04][COMM]BAT CAN get soc Fail, 204
[D][05:20:04][COMM]BAT CAN get state2 fail 204
[D][05:20:04][COMM]get soh error
[E][05:20:04][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:20:04][COMM]report elecbike
[W][05:20:04][PROT]remove success[1629955204],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:20:04][PROT]add success [1629955204],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:20:04][COMM]Receive Bat Lock cmd 0
[D][05:20:04][COMM]VBUS is 1
[D][05:20:04][COMM]Main Task receive event:60 finished processing
[D][05:20:04][COMM]Main Task receive event:61
[D][05:20:04][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:20:04][COMM][D301]:type:3, trace id:280
[D][05:20:04][PROT]min_index:0, type:0x5D03, priority:4
[D][05:20:04][PROT]index:0
[D][05:20:04][PROT]is_send:1
[D][05:20:04][PROT]sequence_num:14
[D][05:20:04][PROT]retry_timeout:0
[D][05:20:04][PROT]retry_times:3
[D][05:20:04][P

2025-07-31 18:07:49:960 ==>> ROT]send_path:0x3
[D][05:20:04][PROT]msg_type:0x5d03
[D][05:20:04][PROT]===========================================================
[W][05:20:04][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955204]
[D][05:20:04][PROT]===========================================================
[D][05:20:04][PROT]Sending traceid[999999999990000F]
[D][05:20:04][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:20:04][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:20:04][PROT]ble is not inited or not connected or cccd not enabled
[D][05:20:04][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:04][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:20:04][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:20:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:20:04][SAL ]open socket ind id[4], rst[0]
[D][05:20:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:20:04][SAL ]Cellular task submsg id[8]
[D][05:20:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:20:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:20:04][COMM]id[], hw[000
[D][05:20:04][COMM]get mcMain

2025-07-31 18:07:50:064 ==>> circuitVolt error
[D][05:20:04][COMM]get mcSubcircuitVolt error
[D][05:20:04][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:20:04][COMM]BAT CAN get state1 Fail 204
[D][05:20:04][COMM]BAT CAN get soc Fail, 204
[D][05:20:04][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:20:04][COMM]BAT CAN get state2 fail 204
[D][05:20:04][COMM]get bat work mode err
[W][05:20:04][PROT]remove success[1629955204],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:20:04][PROT]add success [1629955204],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:20:04][COMM]Main Task receive event:61 finished processing
[D][05:20:04][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:20:04][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:04][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:20:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:20:04][CAT1]gsm read msg sub id: 8
[D][05:20:04][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D

2025-07-31 18:07:50:170 ==>> ][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:20:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:20:04][CAT1]<<< 
+CGATT: 0

OK

[D][05:20:04][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:04][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:20:05][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:20:05][CAT1]<<< 
+CME ERROR: 100

[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:20:05][COMM]Power Off
[D][05:20:05][COMM]read battery soc:255
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:05][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:20:05][COMM]f:[ec800m_a

2025-07-31 18:07:50:223 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:07:50:251 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:07:50:282 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:07:50:312 ==>> udio_play_process].l:[991]. send ret: 0
[D][05:20:05][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:05][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:20:05][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:20:05][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:05][COMM]arm_hub_enable: hub power: 0
[D][05:20:05][HSDK]hexlog index save 0 3584 131 @ 0 : 0
[D][05:20:05][HSDK]write save hexlog index [0]
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:05][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:07:50:342 ==>>                               

2025-07-31 18:07:50:365 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:07:50:504 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:07:50:515 ==>> 检测【检测小电池关机电流】
2025-07-31 18:07:50:534 ==>> 开始小电池电流采样
2025-07-31 18:07:50:564 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:50:606 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:07:51:610 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:07:51:686 ==>> CurrentBattery:ƽ��:68.70

2025-07-31 18:07:52:114 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:07:52:124 ==>> 【检测小电池关机电流】通过,【68.7uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:07:52:549 ==>> MES过站成功
2025-07-31 18:07:52:559 ==>> #################### 【测试结束】 ####################
2025-07-31 18:07:52:578 ==>> 关闭5V供电
2025-07-31 18:07:52:592 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:07:52:665 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:07:53:586 ==>> 关闭5V供电成功
2025-07-31 18:07:53:602 ==>> 关闭33V供电
2025-07-31 18:07:53:626 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:07:53:664 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:07:54:591 ==>> 关闭33V供电成功
2025-07-31 18:07:54:612 ==>> 关闭3.7V供电
2025-07-31 18:07:54:635 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:07:54:660 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:07:55:444 ==>>  

2025-07-31 18:07:55:594 ==>> 关闭3.7V供电成功
