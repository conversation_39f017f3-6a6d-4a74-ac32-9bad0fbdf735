2025-07-31 20:21:55:074 ==>> MES查站成功:
查站序号:P5100010053128F7验证通过
2025-07-31 20:21:55:096 ==>> 扫码结果:P5100010053128F7
2025-07-31 20:21:55:097 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:21:55:099 ==>> 测试参数版本:2024.10.11
2025-07-31 20:21:55:100 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:21:55:102 ==>> 检测【打开透传】
2025-07-31 20:21:55:104 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:21:55:154 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:21:55:373 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:21:55:379 ==>> 检测【检测接地电压】
2025-07-31 20:21:55:380 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:21:55:443 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:21:55:660 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:21:55:662 ==>> 检测【打开小电池】
2025-07-31 20:21:55:665 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:21:55:750 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:21:55:948 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:21:55:950 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:21:55:953 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:21:56:043 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:21:56:230 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:21:56:233 ==>> 检测【等待设备启动】
2025-07-31 20:21:56:235 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:21:56:475 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:21:56:671 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:21:57:272 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:21:57:381 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:21:57:766 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:21:58:242 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:21:58:336 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:21:58:338 ==>> 检测【产品通信】
2025-07-31 20:21:58:340 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:21:58:533 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:21:58:651 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:21:58:654 ==>> 检测【初始化完成检测】
2025-07-31 20:21:58:658 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:21:58:918 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:21:59:198 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:21:59:200 ==>> 检测【关闭大灯控制1】
2025-07-31 20:21:59:202 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:21:59:317 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:59:423 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2

2025-07-31 20:21:59:467 ==>> ],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:21:59:929 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:21:59:932 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:21:59:935 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:22:00:146 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:22:00:225 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:22:00:228 ==>> 检测【关闭仪表供电】
2025-07-31 20:22:00:230 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:22:00:326 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:22:00:431 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:22:00:504 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:22:00:507 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:22:00:508 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:22:00:724 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:22:00:826 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:22:00:828 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:22:00:831 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:22:01:001 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:22:01:118 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:22:01:121 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:22:01:124 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:22:01:339 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:22:01:388 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:22:01:391 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:22:01:394 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:22:01:444 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:22:01:534 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 24


2025-07-31 20:22:01:609 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:22:01:658 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:22:01:662 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:22:01:665 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:22:01:744 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:22:01:849 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5014. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5016. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5017. period:500

2025-07-31 20:22:01:894 ==>> . j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5018
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019


2025-07-31 20:22:01:943 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:22:01:945 ==>> 该项需要延时执行
2025-07-31 20:22:02:358 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:22:03:132 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:22:03:623 ==>>                                                                                                          value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Au

2025-07-31 20:22:03:728 ==>> dio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, ty

2025-07-31 20:22:03:833 ==>> pe:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:5

2025-07-31 20:22:03:923 ==>> 5][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                         

2025-07-31 20:22:04:370 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:22:05:368 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:22:05:612 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:22:05:951 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:22:05:955 ==>> 检测【33V输入电压ADC】
2025-07-31 20:22:05:958 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:22:06:258 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:17:58][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:12  volt:15 mv
[D][05:17:58][COMM]adc read throttle adc:10  volt:13 mv
[D][05:17:58][COMM]adc read battery ts volt:18 mv
[D][05:17:58][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:58][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:22:06:363 ==>> [D][05:17:58][COMM]9706 i

2025-07-31 20:22:06:393 ==>> mu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:22:06:509 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:22:06:511 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:22:06:515 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:22:06:653 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:22:06:743 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 20:22:06:792 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:06:796 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:22:06:811 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:06:813 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:22:06:816 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:22:06:829 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:22:06:831 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:22:06:847 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:22:06:850 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:22:06:870 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:22:06:880 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:22:06:955 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 1991mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:22:07:148 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:07:150 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:22:07:168 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:07:170 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:22:07:172 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:22:07:187 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:22:07:189 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:22:07:206 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:22:07:208 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:22:07:229 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:22:07:231 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:22:07:356 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1992mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:22:07:526 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:07:529 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:22:07:559 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:07:562 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:22:07:565 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 20:22:07:578 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:22:07:581 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:22:07:597 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:22:07:600 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:22:07:621 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:22:07:624 ==>> 检测【打开WIFI(1)】
2025-07-31 20:22:07:627 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:22:07:629 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59

2025-07-31 20:22:07:659 ==>> ][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:22:07:749 ==>>                                          [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:22:07:904 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:22:07:908 ==>> 检测【清空消息队列(1)】
2025-07-31 20:22:07:911 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:22:08:364 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087966562

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541421

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx r

2025-07-31 20:22:08:424 ==>> et[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

                                         

2025-07-31 20:22:08:467 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:22:08:470 ==>> 检测【打开GPS(1)】
2025-07-31 20:22:08:472 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:22:08:637 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:22:08:797 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:22:08:800 ==>> 检测【打开GSM联网】
2025-07-31 20:22:08:802 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:22:08:911 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:22:09:190 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:22:09:193 ==>> 检测【打开仪表供电1】
2025-07-31 20:22:09:195 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:22:09:344 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:22:09:490 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:22:09:495 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:22:09:498 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:22:09:512 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:22:09:661 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:22:09:847 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:22:09:850 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:22:09:852 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:22:10:031 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33247]


2025-07-31 20:22:10:304 ==>> 【读取主控ADC采集的仪表电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:22:10:307 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:22:10:309 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:10:630 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc]

2025-07-31 20:22:10:735 ==>> .L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][COMM]13728 imu init OK
[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TES

2025-07-31 20:22:10:780 ==>> T,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:22:10:829 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:22:10:833 ==>> 检测【AD_V20电压】
2025-07-31 20:22:10:837 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:10:857 ==>>                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 20:22:10:930 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:11:052 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:22:11:250 ==>> 本次取值间隔时间:311ms
2025-07-31 20:22:11:268 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:11:311 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:22:11:372 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:11:476 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE42C00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:22:11:628 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:22:11:674 ==>> 本次取值间隔时间:295ms
2025-07-31 20:22:11:699 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:11:811 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:12:061 ==>> 本次取值间隔时间:235ms
2025-07-31 20:22:12:213 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,42,25,,,41,59,,,40,60,,,40,1*73

$GBGSV,3,2,09,24,,,37,14,,,44,4,,,39,13,,,37,1*41

$GBGSV,3,3,09,6,,,36,1*4C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:04][CAT1]<<< 
OK

$GBGST,,0.000,1658.265,1658.265,52.980,2097152,2097152,2097152*49

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 20:22:12:380 ==>> 本次取值间隔时间:317ms
2025-07-31 20:22:12:395 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:22:12:547 ==>> 本次取值间隔时间:161ms
2025-07-31 20:22:12:828 ==>> 本次取值间隔时间:271ms
2025-07-31 20:22:12:833 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:12:935 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:13:012 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:22:13:042 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:22:13:087 ==>> 本次取值间隔时间:137ms
2025-07-31 20:22:13:105 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:13:147 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,14,,,41,25,,,41,42,,,41,1*73

$GBGSV,5,2,17,59,,,40,60,,,40,24,,,39,13,,,37,1*77

$GBGSV,5,3,17,2,,,36,6,,,35,9,,,35,8,,,35,1*70

$GBGSV,5,4,17,39,,,34,5,,,31,4,,,30,3,,,41,1*4A

$GBGSV,5,5,17,40,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1539.525,1539.525,49.289,2097152,2097152,2097152*41



2025-07-31 20:22:13:207 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:13:437 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:22:13:620 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:22:13:650 ==>> 本次取值间隔时间:440ms
2025-07-31 20:22:13:850 ==>> 本次取值间隔时间:186ms
2025-07-31 20:22:14:143 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,14,,,41,25,,,41,42,,,40,1*76

$GBGSV,5,2,20,59,,,40,60,,,40,24,,,40,13,,,37,1*7D

$GBGSV,5,3,20,1,,,37,40,,,36,6,,,36,39,,,36,1*7A

$GBGSV,5,4,20,2,,,35,9,,,35,8,,,35,38,,,32,1*4A

$GBGSV,5,5,20,5,,,32,4,,,31,3,,,40,34,,,38,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1533.978,1533.978,49.093,2097152,2097152,2097152*48



2025-07-31 20:22:14:263 ==>> 本次取值间隔时间:404ms
2025-07-31 20:22:14:371 ==>> 本次取值间隔时间:105ms
2025-07-31 20:22:14:376 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:22:14:481 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:22:14:511 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:22:14:541 ==>> 本次取值间隔时间:55ms
2025-07-31 20:22:14:545 ==>> 1A A1 10 00 00 
Get AD_V20 1651mV
OVER 150


2025-07-31 20:22:14:565 ==>> 【AD_V20电压】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:22:14:570 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:22:14:574 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:22:14:646 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:22:14:852 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:22:14:855 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:22:14:859 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:22:15:046 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:22:15:151 ==>>                                                                                                                                                                                                                        ,,37,40,,,36,1*40

$GBGSV,6,4,22,6,,,36,16,,,36,2,,,35,9,,,35,1*4E

$GBGSV,6,5,22,8,,,35,38,,,34,26,,,33,5,,,31,1*74

$GBGSV,6,6,22,4,,,31,10,,,41,1*44

$GBRMC,122218.963,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122218.963,0.000,1541.878,1541.878,49.348,2097152,2097152,2097152*55



2025-07-31 20:22:15:771 ==>> [D][05:18:07][COMM]read battery soc:255
$GBGGA,122219.563,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,14,,,41,25,,,41,3,,,41,1*43

$GBGSV,6,2,22,24,,,41,60,,,40,42,,,40,59,,,40,1*79

$GBGSV,6,3,22,39,,,38,13,,,37,1,,,37,40,,,37,1*41

$GBGSV,6,4,22,6,,,36,16,,,36,9,,,36,2,,,35,1*4D

$GBGSV,6,5,22,8,,,35,38,,,35,26,,,34,10,,,33,1*44

$GBGSV,6,6,22,5,,,32,4,,,31,1*74

$GBRMC,122219.563,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122219.563,0.000,1541.509,1541.509,49.327,2097152,2097152,2097152*51



2025-07-31 20:22:15:876 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:22:16:047 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:22:16:757 ==>> $GBGGA,122220.543,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,25,,,41,3,,,41,1*45

$GBGSV,7,2,25,24,,,41,60,,,40,42,,,40,59,,,40,1*7F

$GBGSV,7,3,25,39,,,39,13,,,37,1,,,37,40,,,37,1*46

$GBGSV,7,4,25,16,,,37,6,,,36,9,,,36,38,,,36,1*70

$GBGSV,7,5,25,2,,,35,8,,,34,26,,,34,41,,,34,1*79

$GBGSV,7,6,25,7,,,34,10,,,33,5,,,31,4,,,31,1*40

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,122220.543,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122220.543,0.000,1522.375,1522.375,48.728,2097152,2097152,2097152*53



2025-07-31 20:22:16:924 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:22:17:141 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:22:17:756 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,122221.523,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,25,,,41,24,,,41,1*70

$GBGSV,7,2,25,60,,,41,3,,,40,42,,,40,59,,,40,1*4A

$GBGSV,7,3,25,39,,,39,13,,,37,1,,,37,40,,,37,1*46

$GBGSV,7,4,25,16,,,37,6,,,36,9,,,36,38,,,36,1*70

$GBGSV,7,5,25,2,,,35,8,,,35,41,,,35,26,,,34,1*79

$GBGSV,7,6,25,7,,,34,10,,,33,5,,,32,44,,,32,1*74

$GBGSV,7,7,25,4,,,31,1*47

$GBRMC,122221.523,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122221.523,0.000,1528.999,1528.999,48.930,2097152,2097152,2097152*53



2025-07-31 20:22:17:969 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:22:17:974 ==>> #################### 【测试结束】 ####################
2025-07-31 20:22:18:031 ==>> 关闭5V供电
2025-07-31 20:22:18:036 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:22:18:140 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:22:18:727 ==>> $GBGGA,122222.503,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,25,,,41,24,,,41,1*70

$GBGSV,7,2,25,3,,,41,60,,,40,42,,,40,59,,,40,1*4A

$GBGSV,7,3,25,39,,,39,13,,,37,1,,,37,40,,,37,1*46

$GBGSV,7,4,25,16,,,37,38,,,37,6,,,36,9,,,36,1*71

$GBGSV,7,5,25,2,,,35,41,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,25,8,,,34,10,,,33,44,,,33,5,,,31,1*79

$GBGSV,7,7,25,4,,,31,1*47

$GBRMC,122222.503,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122222.503,0.000,1532.314,1532.314,49.035,2097152,2097152,2097152*5F



2025-07-31 20:22:19:036 ==>> 关闭5V供电成功
2025-07-31 20:22:19:041 ==>> 关闭33V供电
2025-07-31 20:22:19:045 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:22:19:145 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:22:19:250 ==>> [D][05:18:11][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 1,volt 

2025-07-31 20:22:19:310 ==>> = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:22:19:554 ==>> [D][05:18:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 20:22:19:734 ==>> $GBGGA,122223.503,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,25,,,41,24,,,41,1*70

$GBGSV,7,2,25,3,,,41,60,,,40,42,,,40,59,,,40,1*4A

$GBGSV,7,3,25,39,,,39,13,,,37,1,,,37,40,,,37,1*46

$GBGSV,7,4,25,16,,,37,38,,,37,6,,,36,9,,,36,1*71

$GBGSV,7,5,25,2,,,35,41,,,35,26,,,35,7,,,35,1*77

$GBGSV,7,6,25,8,,,34,10,,,33,44,,,33,5,,,32,1*7A

$GBGSV,7,7,25,4,,,31,1*47

$GBRMC,122223.503,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122223.503,0.000,1533.969,1533.969,49.084,2097152,2097152,2097152*54



2025-07-31 20:22:20:042 ==>> 关闭33V供电成功
2025-07-31 20:22:20:047 ==>> 关闭3.7V供电
2025-07-31 20:22:20:052 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:22:20:151 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:22:20:757 ==>>  

