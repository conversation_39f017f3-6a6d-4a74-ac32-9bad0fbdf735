2025-07-31 20:36:45:319 ==>> MES查站成功:
查站序号:P5100010053135BF验证通过
2025-07-31 20:36:45:326 ==>> 扫码结果:P5100010053135BF
2025-07-31 20:36:45:327 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:36:45:329 ==>> 测试参数版本:2024.10.11
2025-07-31 20:36:45:330 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:36:45:331 ==>> 检测【打开透传】
2025-07-31 20:36:45:333 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:36:45:458 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:36:45:665 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:36:45:673 ==>> 检测【检测接地电压】
2025-07-31 20:36:45:676 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:36:45:759 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:36:45:954 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:36:45:957 ==>> 检测【打开小电池】
2025-07-31 20:36:45:960 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:36:46:060 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:36:46:252 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:36:46:256 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:36:46:259 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:36:46:348 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:36:46:583 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:36:46:587 ==>> 检测【等待设备启动】
2025-07-31 20:36:46:591 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:46:847 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:47:045 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:36:47:624 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:47:671 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:36:47:715 ==>>                                                    

2025-07-31 20:36:48:111 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:36:48:582 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:36:48:676 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:36:48:678 ==>> 检测【产品通信】
2025-07-31 20:36:48:679 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:36:48:839 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:36:48:948 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:36:48:950 ==>> 检测【初始化完成检测】
2025-07-31 20:36:48:954 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:36:49:175 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:36:49:235 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:36:49:246 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:36:49:248 ==>> 检测【关闭大灯控制1】
2025-07-31 20:36:49:249 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:36:49:429 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:36:49:550 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:36:49:554 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:36:49:557 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:36:49:653 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:49:848 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:36:50:099 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:36:50:102 ==>> 检测【关闭仪表供电】
2025-07-31 20:36:50:105 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:36:50:247 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:36:50:393 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:36:50:396 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:36:50:399 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:36:50:518 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:36:50:685 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:50:696 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:36:50:698 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:36:50:699 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:36:50:791 ==>> [W][05:17:52][COMM]>>>>>Input comman

2025-07-31 20:36:50:821 ==>> d = AT+ACCKEY1=0<<<<<


2025-07-31 20:36:50:975 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:36:50:978 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:36:50:980 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:36:51:110 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:36:51:328 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:36:51:330 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:36:51:333 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:36:51:451 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:36:51:555 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 18
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:36:51:630 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:36:51:635 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:36:51:641 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:36:51:661 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:51:751 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:36:51:931 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:36:51:934 ==>> 该项需要延时执行
2025-07-31 20:36:52:219 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017


2025-07-31 20:36:52:705 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:53:115 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:36:53:662 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][CO

2025-07-31 20:36:53:766 ==>> MM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D

2025-07-31 20:36:53:872 ==>> ][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]r

2025-07-31 20:36:53:962 ==>> emove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                                                         

2025-07-31 20:36:54:723 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:55:575 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:36:55:725 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:55:938 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:36:55:941 ==>> 检测【33V输入电压ADC】
2025-07-31 20:36:55:944 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:36:56:262 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:17:58][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:4 mv
[D][05:17:58][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3361  volt:2707 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:36:56:509 ==>> 【33V输入电压ADC】通过,【32400mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:36:56:511 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:36:56:513 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:36:56:655 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:36:56:745 ==>> [D][05:17:58][COMM]9703 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:36:56:804 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:56:806 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:36:56:984 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:56:988 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:36:56:989 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 20:36:57:028 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:36:57:031 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:36:57:074 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:36:57:076 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:36:57:092 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 20:36:57:125 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:36:57:127 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:36:57:256 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:36:57:424 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:57:427 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:36:57:451 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:36:57:482 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:57:485 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:36:57:487 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:36:57:556 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:36:57:607 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:36:57:609 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:36:57:661 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:36:57:664 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:36:57:703 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:36:57:711 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:36:57:864 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:36:57:970 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10713 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] 

2025-07-31 20:36:58:003 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:58:006 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:36:58:014 ==>> >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:36:58:033 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:36:58:036 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:36:58:045 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 20:36:58:052 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:36:58:055 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:36:58:070 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:36:58:073 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:36:58:094 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:36:58:096 ==>> 检测【打开WIFI(1)】
2025-07-31 20:36:58:099 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:36:58:412 ==>>                                                                                                                            2
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087939320

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539002

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:36:58:640 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:36:58:644 ==>> 检测【清空消息队列(1)】
2025-07-31 20:36:58:655 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:36:58:759 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:36:58:849 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:36:58:933 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:36:58:937 ==>> 检测【打开GPS(1)】
2025-07-31 20:36:58:957 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:36:59:153 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 20:36:59:220 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:36:59:223 ==>> 检测【打开GSM联网】
2025-07-31 20:36:59:226 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:36:59:350 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:36:59:441 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:36:59:489 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:36:59:492 ==>> 检测【打开仪表供电1】
2025-07-31 20:36:59:495 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:36:59:654 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:36:59:770 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:36:59:773 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:36:59:778 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:36:59:945 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:37:00:065 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:37:00:068 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:37:00:072 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:37:00:237 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33062]


2025-07-31 20:37:00:345 ==>> 【读取主控ADC采集的仪表电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:37:00:348 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:37:00:350 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:00:542 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:37:00:629 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:37:00:631 ==>> 检测【AD_V20电压】
2025-07-31 20:37:00:636 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:00:738 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:00:768 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:02][COMM]13727 imu init OK


2025-07-31 20:37:00:920 ==>> 本次取值间隔时间:174ms
2025-07-31 20:37:00:950 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:37:01:132 ==>> 本次取值间隔时间:210ms
2025-07-31 20:37:01:502 ==>> 本次取值间隔时间:358ms
2025-07-31 20:37:01:506 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:37:01:607 ==>>                                                                                                                                                                                                                                 

2025-07-31 20:37:01:698 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 [D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:37:02:002 ==>> 本次取值间隔时间:492ms
2025-07-31 20:37:02:006 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:02:017 ==>>                                                                                                                                                                                                                                                                                 [D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:37:02:108 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:02:123 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:37:02:228 ==>> 本次取值间隔时间:116ms
2025-07-31 20:37:02:233 ==>> 1A

2025-07-31 20:37:02:259 ==>>  A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:37:02:659 ==>> 本次取值间隔时间:427ms
2025-07-31 20:37:02:704 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:37:02:752 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:02:854 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:02:961 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:37:03:313 ==>> 本次取值间隔时间:444ms
2025-07-31 20:37:03:332 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:03:376 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:37:03:435 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:03:615 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,33,,,41,24,,,40,25,,,39,40,,,36,1*78

$GBGSV,2,2,06,14,,,40,59,,,36,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.819,1616.819,51.668,2097152,2097152,2097152*43

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
                                         

2025-07-31 20:37:03:720 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:37:03:935 ==>> 本次取值间隔时间:491ms
2025-07-31 20:37:03:959 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:04:060 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:04:152 ==>> 本次取值间隔时间:82ms
2025-07-31 20:37:04:155 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:37:04:173 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:04:288 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:04:349 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:37:04:424 ==>> 本次取值间隔时间:133ms
2025-07-31 20:37:04:453 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:04:459 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1

2025-07-31 20:37:04:499 ==>> ,12,33,,,42,24,,,40,25,,,40,3,,,40,1*47

$GBGSV,3,2,12,14,,,39,59,,,39,60,,,39,40,,,37,1*71

$GBGSV,3,3,12,39,,,33,4,,,33,1,,,43,38,,,37,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1583.704,1583.704,50.659,2097152,2097152,2097152*40



2025-07-31 20:37:04:559 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:04:649 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:37:04:953 ==>> 本次取值间隔时间:392ms
2025-07-31 20:37:04:972 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:05:075 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:05:150 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:37:05:532 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,24,,,40,25,,,40,3,,,40,1*44

$GBGSV,4,2,16,14,,,40,60,,,40,59,,,39,40,,,37,1*72

$GBGSV,4,3,16,38,,,36,41,,,36,1,,,35,39,,,35,1*43

$GBGSV,4,4,16,44,,,34,2,,,34,4,,,32,5,,,31,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1531.383,1531.383,49.006,2097152,2097152,2097152*44



2025-07-31 20:37:05:547 ==>> 本次取值间隔时间:458ms
2025-07-31 20:37:05:565 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:37:05:607 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:37:05:667 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:37:05:787 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 20:37:05:802 ==>> 本次取值间隔时间:132ms
2025-07-31 20:37:05:821 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:37:05:824 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:37:05:827 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:37:05:953 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:37:06:097 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:37:06:100 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:37:06:104 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:37:06:242 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:37:06:527 ==>> $GBGGA,123710.352,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,5,2,19,14,,,40,59,,,40,60,,,39,40,,,37,1*7C

$GBGSV,5,3,19,41,,,37,39,,,37,38,,,36,1,,,35,1*4E

$GBGSV,5,4,19,44,,,34,2,,,34,16,,,34,4,,,32,1*78

$GBGSV,5,5,19,5,,,31,10,,,42,13,,,38,1*47

$GBRMC,123710.352,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123710.352,0.000,1531.532,1531.532,49.008,2097152,2097152,2097152*56



2025-07-31 20:37:06:737 ==>> $GBGGA,123710.552,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,24,,,40,25,,,40,3,,,40,1*40

$GBGSV,5,2,20,14,,,40,59,,,39,60,,,39,40,,,37,1*78

$GBGSV,5,3,20,41,,,37,39,,,37,38,,,36,1,,,35,1*44

$GBGSV,5,4,20,2,,,34,16,,,34,13,,,33,44,,,33,1*42

$GBGSV,5,5,20,4,,,31,5,,,31,7,,,44,6,,,39,1*7E

$GBRMC,123710.552,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123710.552,0.000,1515.553,1515.553,48.505,2097152,2097152,2097152*59



2025-07-31 20:37:07:130 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:37:07:361 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:37:07:739 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,123711.532,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,41,25,,,40,3,,,40,1*46

$GBGSV,7,2,25,14,,,40,59,,,39,60,,,39,39,,,38,1*7E

$GBGSV,7,3,25,40,,,37,41,,,37,42,,,37,38,,,36,1*78

$GBGSV,7,4,25,1,,,36,7,,,35,16,,,35,2,,,34,1*43

$GBGSV,7,5,25,13,,,34,6,,,33,44,,,33,8,,,33,1*78

$GBGSV,7,6,25,10,,,32,5,,,31,34,,,31,4,,,30,1*75

$GBGSV,7,7,25,9,,,36,1*4D

$GBRMC,123711.532,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123711.532,0.000,1490.799,1490.799,47.720,2097152,2097152,2097152*54



2025-07-31 20:37:08:166 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:37:08:362 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:37:08:715 ==>> $GBGGA,123712.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,25,,,40,3,,,40,1*47

$GBGSV,7,2,25,14,,,40,60,,,40,59,,,39,39,,,38,1*70

$GBGSV,7,3,25,40,,,37,41,,,37,42,,,37,38,,,36,1*78

$GBGSV,7,4,25,1,,,36,16,,,36,7,,,35,13,,,35,1*71

$GBGSV,7,5,25,2,,,34,6,,,34,44,,,33,8,,,33,1*4F

$GBGSV,7,6,25,10,,,32,5,,,31,34,,,31,4,,,31,1*74

$GBGSV,7,7,25,9,,,36,1*4D

$GBRMC,123712.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123712.512,0.000,1497.702,1497.702,47.934,2097152,2097152,2097152*5E



2025-07-31 20:37:09:205 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:37:09:210 ==>> #################### 【测试结束】 ####################
2025-07-31 20:37:09:234 ==>> 关闭5V供电
2025-07-31 20:37:09:240 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:37:09:360 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:37:09:722 ==>> $GBGGA,123713.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,25,,,40,3,,,40,1*45

$GBGSV,7,2,26,14,,,40,60,,,39,59,,,39,39,,,39,1*7C

$GBGSV,7,3,26,42,,,38,40,,,37,41,,,37,38,,,36,1*74

$GBGSV,7,4,26,1,,,36,16,,,36,9,,,35,7,,,35,1*49

$GBGSV,7,5,26,13,,,35,2,,,34,6,,,34,44,,,33,1*70

$GBGSV,7,6,26,8,,,33,10,,,33,5,,,31,34,,,31,1*78

$GBGSV,7,7,26,4,,,31,26,,,28,1*4A

$GBRMC,123713.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123713.512,0.000,1487.750,1487.750,47.629,2097152,2097152,2097152*5C

[D][05:18:11][COMM]read battery soc:255


2025-07-31 20:37:10:249 ==>> 关闭5V供电成功
2025-07-31 20:37:10:254 ==>> 关闭33V供电
2025-07-31 20:37:10:258 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:37:10:358 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:37:10:463 ==>> [D][05:18:12][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:18:

2025-07-31 20:37:10:508 ==>> 12][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:37:10:811 ==>> $GBGGA,123714.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,25,,,40,3,,,40,1*44

$GBGSV,7,2,26,14,,,40,60,,,40,59,,,39,39,,,39,1*72

$GBGSV,7,3,26,42,,,38,40,,,37,41,,,37,38,,,36,1*74

$GBGSV,7,4,26,1,,,36,16,,,36,9,,,35,7,,,35,1*49

$GBGSV,7,5,26,13,,,35,6,,,35,2,,,34,44,,,33,1*71

$GBGSV,7,6,26,8,,,33,10,,,33,5,,,31,34,,,31,1*78

$GBGSV,7,7,26,4,,,31,26,,,29,1*4B

$GBRMC,123714.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123714.512,0.000,1490.933,1490.933,47.725,2097152,2097152,2097152*56

[D][05:18:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:37:11:258 ==>> 关闭33V供电成功
2025-07-31 20:37:11:263 ==>> 关闭3.7V供电
2025-07-31 20:37:11:291 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:37:11:351 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:37:11:917 ==>>  

