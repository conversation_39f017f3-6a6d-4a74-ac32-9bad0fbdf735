2025-07-31 23:05:07:783 ==>> MES查站成功:
查站序号:P51000100531397A验证通过
2025-07-31 23:05:07:787 ==>> 扫码结果:P51000100531397A
2025-07-31 23:05:07:788 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:05:07:790 ==>> 测试参数版本:2024.10.11
2025-07-31 23:05:07:792 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:05:07:793 ==>> 检测【打开透传】
2025-07-31 23:05:07:795 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:05:07:915 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:05:08:150 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:05:08:182 ==>> 检测【检测接地电压】
2025-07-31 23:05:08:185 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:05:08:313 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 23:05:08:463 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:05:08:466 ==>> 检测【打开小电池】
2025-07-31 23:05:08:468 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:05:08:525 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:05:08:740 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:05:08:743 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:05:08:746 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:05:08:813 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:05:09:022 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:05:09:026 ==>> 检测【等待设备启动】
2025-07-31 23:05:09:029 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:05:09:308 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:05:09:489 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:05:10:058 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:05:10:195 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:05:10:581 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:05:11:039 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:05:11:102 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:05:11:105 ==>> 检测【产品通信】
2025-07-31 23:05:11:106 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:05:11:309 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:05:11:418 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:05:11:420 ==>> 检测【初始化完成检测】
2025-07-31 23:05:11:423 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:05:11:648 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:05:11:723 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:05:11:747 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:05:11:749 ==>> 检测【关闭大灯控制1】
2025-07-31 23:05:11:750 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:05:11:874 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:05:12:056 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:05:12:058 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:05:12:060 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:05:12:119 ==>> [D][05:17:51][COMM]2634 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:12:224 ==>>              [COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfig

2025-07-31 23:05:12:300 ==>> ReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:05:13:143 ==>> [D][05:17:52][COMM]3646 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:13:191 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:05:13:193 ==>> 检测【关闭仪表供电】
2025-07-31 23:05:13:198 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:05:13:416 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:05:13:466 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:05:13:468 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:05:13:472 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:05:13:598 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:05:13:750 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:05:13:753 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:05:13:755 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:05:13:873 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:05:14:050 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:05:14:052 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:05:14:054 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:05:14:194 ==>> [D][05:17:53][COMM]4656 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:05:14:332 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:05:14:335 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:05:14:337 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:05:14:419 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:05:14:605 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:05:14:609 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:05:14:611 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:05:14:706 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017
[D][05:17:54][COMM]VBUS Ins

2025-07-31 23:05:14:751 ==>> ert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:54][COMM]read battery soc:255
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:05:14:874 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:05:14:876 ==>> 该项需要延时执行
2025-07-31 23:05:15:167 ==>> [D][05:17:54][COMM]5667 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:16:108 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:05:16:600 ==>>                                                                [D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]6680 imu init OK
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not rea

2025-07-31 23:05:16:706 ==>> dy
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:

2025-07-31 23:05:16:810 ==>> 17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol

2025-07-31 23:05:16:886 ==>> [0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][CAT1]power_urc_cb ret[5]
[D][05:17:56][COMM]read battery soc:255


2025-07-31 23:05:17:207 ==>> [D][05:17:56][COMM]7695 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:18:202 ==>> [D][05:17:57][COMM]8707 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:18:534 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 23:05:18:889 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:05:18:892 ==>> 检测【33V输入电压ADC】
2025-07-31 23:05:18:894 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:19:302 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:17:58][COMM]adc read out 24v adc:1299  volt:32855 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:2 mv
[D][05:17:58][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv
[D][05:17:58][COMM]9718 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:05:19:425 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:05:19:432 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:05:19:435 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:05:19:559 ==>> 1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1652mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150
[D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10006
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 23:05:19:698 ==>> 【TP7_VCC3V3(ADV2)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:19:700 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:05:19:716 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:19:720 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:05:19:724 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 23:05:19:734 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:05:19:738 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:05:19:752 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:05:19:754 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:05:19:775 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:05:19:777 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:05:19:925 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1652mV
Get AD_V4 1mV
Get AD_V5 2775mV
Get AD_V6 1988mV
Get AD_V7 1093mV
OVER 150


2025-07-31 23:05:20:015 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:05:20:052 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:20:054 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:05:20:074 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:20:078 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:05:20:082 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 23:05:20:092 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:05:20:096 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:05:20:126 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:05:20:128 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:05:20:149 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:05:20:152 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:05:20:226 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1652mV
Get AD_V4 0mV
Get AD_V5 2774mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 23:05:20:332 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10

2025-07-31 23:05:20:434 ==>> 【TP7_VCC3V3(ADV2)】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:20:439 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:05:20:444 ==>> 729 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:05:20:459 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:20:465 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:05:20:483 ==>> 原始值:【2774】, 乘以分压基数【2】还原值:【5548】
2025-07-31 23:05:20:489 ==>> 【TP68_VCC5V5(ADV5)】通过,【5548mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:05:20:493 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:05:20:502 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:05:20:505 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:05:20:525 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:05:20:527 ==>> 检测【打开WIFI(1)】
2025-07-31 23:05:20:529 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:05:20:893 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][COMM]read battery soc:255
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087569663

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:05:21:074 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:05:21:081 ==>> 检测【清空消息队列(1)】
2025-07-31 23:05:21:103 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:05:21:223 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:05:21:313 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:05:21:369 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:05:21:375 ==>> 检测【打开GPS(1)】
2025-07-31 23:05:21:390 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:05:21:619 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 23:05:21:679 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:05:21:682 ==>> 检测【打开GSM联网】
2025-07-31 23:05:21:685 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:05:21:919 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:05:21:976 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:05:21:981 ==>> 检测【打开仪表供电1】
2025-07-31 23:05:21:984 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:05:22:207 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:05:22:272 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:05:22:275 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:05:22:277 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:05:22:555 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]read battery soc:255


2025-07-31 23:05:22:816 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:05:22:819 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:05:22:823 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:05:23:009 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33549]


2025-07-31 23:05:23:106 ==>> 【读取主控ADC采集的仪表电压】通过,【33549mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:05:23:108 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:05:23:112 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:05:23:329 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][COMM]13741 imu init OK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:05:23:382 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:05:23:386 ==>> 检测【AD_V20电压】
2025-07-31 23:05:23:389 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:05:23:496 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:05:23:619 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:05:23:937 ==>> 本次取值间隔时间:432ms
2025-07-31 23:05:23:970 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:05:24:042 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 23:05:24:072 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:05:24:222 ==>> 1A A1 10 00 00 
Get AD_V20 1643mV
OVER 150


2025-07-31 23:05:24:282 ==>> 本次取值间隔时间:203ms
2025-07-31 23:05:24:318 ==>> 【AD_V20电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:24:321 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:05:24:325 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:05:24:494 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:

2025-07-31 23:05:24:600 ==>> 18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
3A A3 02 00 A3 
OFF_OUT2
OVER 150
                                         

2025-07-31 23:05:24:852 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:05:24:856 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:05:24:859 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:05:25:082 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:04][COMM]oneline display read state:255
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:05:25:142 ==>>                                          1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:05:25:826 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:05:25:887 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:05:25:932 ==>> [

2025-07-31 23:05:26:021 ==>> D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,40,,,42,34,,,39,41,,,39,25,,,38,1*7C

$GBGSV,2,2,05,39,,,37,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.809,1616.809,51.658,2097152,2097152,2097152*40

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 23:05:26:249 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:05][COMM]oneline display read state:255
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:05:26:595 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 23:05:26:916 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:05:26:946 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,42,34,,,40,3,,,40,41,,,39,1*4E

$GBGSV,3,2,12,25,,,39,39,,,39,7,,,39,11,,,38,1*4F

$GBGSV,3,3,12,16,,,38,1,,,30,4,,,28,43,,,37,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1552.865,1552.865,49.737,2097152,2097152,2097152*41



2025-07-31 23:05:27:111 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:05:27:234 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:05:27:237 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:05:27:240 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:05:27:324 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:05:27:521 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:05:27:525 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:05:27:528 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:05:27:708 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:05:27:812 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:05:27:815 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:05:27:819 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:05:28:054 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,40,,,42,59,,,42,60,,,41,34,,,40,1*7C

$GBGSV,5,2,17,3,,,40,41,,,40,25,,,40,39,,,40,1*4C

$GBGSV,5,3,17,7,,,39,43,,,39,11,,,38,16,,,38,1*41

$GBGSV,5,4,17,2,,,35,32,,,35,1,,,33,5,,,31,1*44

$GBGSV,5,5,17,4,,,29,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1565.701,1565.701,50.126,2097152,2097152,2097152*4F

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:05:28:092 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:05:28:096 ==>> 检测【AD_V21电压】
2025-07-31 23:05:28:100 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:05:28:221 ==>> 1A A1 20 00 00 
Get AD_V21 1047mV
OVER 150


2025-07-31 23:05:28:508 ==>> 本次取值间隔时间:405ms
2025-07-31 23:05:28:526 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:05:28:613 ==>> [D][05:18:08][COMM]read battery soc:255
1A A1 20 00 00 
Get AD_V21 1639mV
OVER 150


2025-07-31 23:05:28:823 ==>> 本次取值间隔时间:284ms
2025-07-31 23:05:28:843 ==>> 【AD_V21电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:28:846 ==>> 检测【关闭仪表供电2】
2025-07-31 23:05:28:848 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:05:29:053 ==>> $GBGGA,150532.838,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,59,,,40,60,,,40,34,,,40,1*7B

$GBGSV,5,2,20,3,,,40,41,,,40,25,,,40,39,,,40,1*48

$GBGSV,5,3,20,7,,,39,43,,,39,11,,,39,16,,,38,1*44

$GBGSV,5,4,20,23,,,37,24,,,36,2,,,34,1,,,34,1*70

$GBGSV,5,5,20,32,,,33,5,,,31,4,,,30,12,,,36,1*73

$GBRMC,150532.838,V,,,,,,,,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150532.838,0.000,1553.620,1553.620,49.724,2097152,2097152,2097152*5E

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:05:29:119 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:05:29:121 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:05:29:126 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:05:29:308 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:05:29:402 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:05:29:405 ==>> 检测【打开AccKey2供电】
2025-07-31 23:05:29:407 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:05:29:704 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
$GBGGA,150533.538,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,40,3,,,40,39,,,40,1*4A

$GBGSV,6,2,23,59,,,40,34,,,40,25,,,40,41,,,40,1*7A

$GBGSV,6,3,23,7,,,39,43,,,39,16,,,38,11,,,38,1*45

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,36,6,,,35,1*47

$GBGSV,6,5,23,1,,,35,2,,,34,9,,,34,12,,,32,1*4A

$GBGSV,6,6,23,32,,,32,5,,,31,4,,,31,1*76

$GBRMC,150533.538,V,,,,,,,,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150533.538,0.000,1528.568,1528.568,48.918,2097152,2097152,2097152*52



2025-07-31 23:05:29:932 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:05:29:938 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:05:29:962 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:30:222 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:09][COMM]adc read out 24v adc:1300  volt:32880 mv
[D][05:18:09][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:09][COMM]adc read battery ts volt:0 mv
[D][05:18:09][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:09][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:05:30:469 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32880mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:05:30:474 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:05:30:496 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:05:30:690 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,150534.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,3,,,40,39,,,40,34,,,40,1*4C

$GBGSV,6,2,24,7,,,39,60,,,39,59,,,39,25,,,39,1*4E

$GBGSV,6,3,24,41,,,39,16,,,38,11,,,38,43,,,38,1*71

$GBGSV,6,4,24,23,,,37,10,,,36,24,,,36,6,,,35,1*40

$GBGSV,6,5,24,1,,,35,2,,,34,9,,,34,44,,,33,1*4F

$GBGSV,6,6,24,12,,,33,5,,,31,4,,,31,32,,,31,1*71

$GBRMC,150534.518,V,,,,,,,,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150534.518,0.000,1513.240,1513.240,48.422,2097152,2097152,2097152*53

[D][05:18:10][COMM]read battery soc:255


2025-07-31 23:05:30:744 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:05:30:747 ==>> 该项需要延时执行
2025-07-31 23:05:31:690 ==>> $GBGGA,150535.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,3,,,40,39,,,40,1*4D

$GBGSV,7,2,25,59,,,40,34,,,40,7,,,39,25,,,39,1*4F

$GBGSV,7,3,25,41,,,39,11,,,38,43,,,38,16,,,37,1*7E

$GBGSV,7,4,25,23,,,37,10,,,36,24,,,36,1,,,36,1*44

$GBGSV,7,5,25,6,,,35,9,,,35,33,,,35,12,,,34,1*7E

$GBGSV,7,6,25,2,,,33,44,,,32,5,,,31,4,,,31,1*42

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150535.518,V,,,,,,,,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150535.518,0.000,1514.071,1514.071,48.450,2097152,2097152,2097152*57



2025-07-31 23:05:32:709 ==>> $GBGGA,150536.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,41,,,40,1*7A

$GBGSV,7,2,25,60,,,39,7,,,39,3,,,39,59,,,39,1*7A

$GBGSV,7,3,25,25,,,39,43,,,39,16,,,38,11,,,38,1*72

$GBGSV,7,4,25,23,,,37,10,,,36,24,,,36,6,,,36,1*43

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,5,,,32,44,,,31,4,,,31,1*74

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150536.518,V,,,,,,,,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150536.518,0.000,1517.382,1517.382,48.551,2097152,2097152,2097152*54

[D][05:18:12][COMM]read battery soc:255


2025-07-31 23:05:33:701 ==>> $GBGGA,150537.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,3,,,40,39,,,40,34,,,40,1*4F

$GBGSV,7,2,25,25,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,59,,,39,11,,,39,43,,,39,16,,,38,1*78

$GBGSV,7,4,25,23,,,37,10,,,36,24,,,36,6,,,36,1*43

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGSV,7,6,25,2,,,34,5,,,31,44,,,31,4,,,31,1*46

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150537.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150537.518,0.000,760.597,760.597,695.585,2097152,2097152,2097152*6A



2025-07-31 23:05:33:746 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:05:33:751 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:05:33:765 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:34:035 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:0 mv
[D][05:18:13][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:05:34:291 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:05:34:295 ==>> 检测【打开AccKey1供电】
2025-07-31 23:05:34:300 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:05:34:512 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:05:34:567 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:05:34:571 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:05:34:590 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:05:34:617 ==>> 1A A1 00 40 00 
Get AD_V14 2673mV
OVER 150


2025-07-31 23:05:34:707 ==>> $GBGGA,150538.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,59,,,40,34,,,40,1*73

$GBGSV,7,2,25,25,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,3,,,39,11,,,39,43,,,39,16,,,38,1*47

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,5,,,32,44,,,31,4,,,31,1*74

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150538.518,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150538.518,0.000,760.592,760.592,695.580,2097152,2097152,2097152*60

[D][05:18:14][COMM]read battery soc:255


2025-07-31 23:05:34:827 ==>> 原始值:【2673】, 乘以分压基数【2】还原值:【5346】
2025-07-31 23:05:34:851 ==>> 【读取AccKey1电压(ADV14)前】通过,【5346mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:05:34:859 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:05:34:862 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:35:134 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:0 mv
[D][05:18:14][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:05:35:395 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5494mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:05:35:398 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:05:35:401 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:05:35:718 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 
$GBGGA,150539.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,40,59,,,40,34,,,40,1*70

$GBGSV,7,2,25,25,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,3,,,39,43,,,39,16,,,38,11,,,38,1*46

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,5,,,32,4,,,32,44,,,31,1*77

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150539.518,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150539.518,0.000,761.418,761.418,696.336,2097152,2097152,2097152*69



2025-07-31 23:05:36:000 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:05:36:006 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:05:36:010 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:05:36:117 ==>> 1A A1 00 40 00 
Get AD_V14 2672mV
OVER 150


2025-07-31 23:05:36:253 ==>> 原始值:【2672】, 乘以分压基数【2】还原值:【5344】
2025-07-31 23:05:36:272 ==>> 【读取AccKey1电压(ADV14)后】通过,【5344mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:05:36:277 ==>> 检测【打开WIFI(2)】
2025-07-31 23:05:36:287 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:05:36:532 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:05:36:637 ==>> $GBGGA,150540.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,

2025-07-31 23:05:36:712 ==>> ,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,40,59,,,40,34,,,40,1*70

$GBGSV,7,2,25,25,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,3,,,39,11,,,39,43,,,39,16,,,38,1*47

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGSV,7,6,25,2,,,34,5,,,32,32,,,32,44,,,31,1*73

$GBGSV,7,7,25,4,,,31,1*47

$GBRMC,150540.518,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150540.518,0.000,763.073,763.073,697.849,2097152,2097152,2097152*65

[D][05:18:16][COMM]read battery soc:255


2025-07-31 23:05:36:805 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:05:36:809 ==>> 检测【转刹把供电】
2025-07-31 23:05:36:814 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:05:37:000 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:05:37:079 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:05:37:082 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:05:37:087 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:05:37:181 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:05:37:466 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F88C21BCF57D,-36
+WIFISCAN:4,1,F42A7D1297A3,-63
+WIFISCAN:4,2,CC057790A7C1,-71
+WIFISCAN:4,3,F86FB0660A82,-83

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 23:05:37:709 ==>> $GBGGA,150541.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,41,,,40,60,,,39,7,,,39,1*47

$GBGSV,7,3,25,25,,,39,43,,,39,16,,,38,11,,,38,1*72

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,12,,,34,1*7A

$GBGSV,7,6,25,2,,,33,5,,,32,4,,,32,44,,,31,1*41

$GBGSV,7,7,25,32,,,31,1*72

$GBRMC,150541.518,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150541.518,0.000,759.762,759.762,694.821,2097152,2097152,2097152*69



2025-07-31 23:05:38:153 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:05:38:262 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:05:38:280 ==>> [D][05:18:17][GNSS]recv submsg id[3]
[W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:05:38:322 ==>> 1A A1 00 80 00 
Get AD_V15 2410mV
OVER 150


2025-07-31 23:05:38:427 ==>> 原始值:【2410】, 乘以分压基数【2】还原值:【4820】
2025-07-31 23:05:38:712 ==>> $GBGGA,150542.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,3,,,40,39,,,40,59,,,40,1*44

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,60,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGSV,7,6,25,2,,,34,5,,,32,4,,,32,32,,,32,1*44

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150542.518,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150542.518,0.000,764.726,764.726,699.360,2097152,2097152,2097152*69

[D][05:18:18][COMM]read battery soc:255


2025-07-31 23:05:38:957 ==>> 【读取AD_V15电压(前)】通过,【4820mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:05:38:963 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:05:38:969 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:05:39:068 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:05:39:113 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2440mV
OVER 150


2025-07-31 23:05:39:233 ==>> 原始值:【2440】, 乘以分压基数【2】还原值:【4880】
2025-07-31 23:05:39:273 ==>> 【读取AD_V16电压(前)】通过,【4880mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:05:39:276 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:05:39:282 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:39:736 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:18][COMM]adc read battery ts volt:0 mv
[D][05:18:18][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3073  volt:5401 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,150543.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,7,,,40,3,,,40,1*7E

$GBGSV,7,2,25,59,,,40,34,,,40,25,,,40,41,,,40,1*7D

$GBGSV,7,3,25,60,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,7,4,25,23,,,38,10,,,37,24,,,36,6,,,36,1*4D

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGS

2025-07-31 23:05:39:781 ==>> V,7,6,25,2,,,34,4,,,33,5,,,32,32,,,32,1*45

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150543.518,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150543.518,0.000,768.037,768.037,702.388,2097152,2097152,2097152*6D



2025-07-31 23:05:39:842 ==>> 【转刹把供电电压(主控ADC)】通过,【5401mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:05:39:846 ==>> 检测【转刹把供电电压】
2025-07-31 23:05:39:851 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:40:128 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:0 mv
[D][05:18:19][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3078  volt:5410 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:05:40:372 ==>> 【转刹把供电电压】通过,【5410mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:05:40:376 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:05:40:379 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:05:40:715 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,150544.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,60,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGSV,7,6,25,2,,,34,4,,,33,5,,,32,32,,,32,1*45

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150544.518,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150544.518,0.000,764.720,764.720,699.354,2097152,2097152,2097152*68

[D][05:18:20][COMM]read battery soc:255


2025-07-31 23:05:40:900 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:05:40:905 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:05:40:911 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:05:41:001 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:05:41:108 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:05:41:123 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:05:41:213 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:05:41:288 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:05:41:319 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:05:41:336 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:05:41:342 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:05:41:365 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:05:41:438 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:05:41:513 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:05:41:561 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:05:41:565 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:05:41:571 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:05:41:618 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 23:05:41:708 ==>> $GBGGA,150545.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,7,2,25,34,,,40,7,,,39,3,,,39,25,,,39,1*7E

$GBGSV,7,3,25,11,,,39,43,,,39,41,,,39,10,,,37,1*78

$GBGSV,7,4,25,16,,,37,23,,,37,24,,,36,6,,,36,1*44

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,5,,,32,4,,,32,32,,,32,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150545.518,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150545.518,0.000,760.583,760.583,695.571,2097152,2097152,2097152*64



2025-07-31 23:05:41:831 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:05:41:835 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:05:41:840 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:05:41:918 ==>> 3A A3 04 01 A3 


2025-07-31 23:05:42:023 ==>> ON_OUT4
OVER 150


2025-07-31 23:05:42:110 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:05:42:115 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:05:42:120 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:05:42:218 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:05:42:388 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:05:42:392 ==>> 检测【左刹电压测试1】
2025-07-31 23:05:42:416 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:42:803 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1703  volt:2245 mv
[D][05:18:22][COMM]adc read right brake adc:1707  volt:2250 mv
[D][05:18:22][COMM]adc read throttle adc:1708  volt:2251 mv
[D][05:18:22][COMM]adc read battery ts volt:3 mv
[D][05:18:22][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
$GBGGA,150546.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,60,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,7,4,25,23,,,37,10,,,36,24,,,36,6,,,36,1*43

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,32,44,,,32,1*75

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150546.518,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150546.518,0.000,763.891,763.891,698.596,2097152,2097152,2097152*63

[D][05:18:22][COMM]arm_hub adc read vbat adc:2414  volt:3889 mv
[D][05:18:22][COMM]arm_hub adc read

2025-07-31 23:05:42:849 ==>>  led yb adc:1447  volt:33549 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:22][COMM]read battery soc:255


2025-07-31 23:05:42:937 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:43:224 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1705  volt:2247 mv
[D][05:18:22][COMM]adc read right brake adc:1709  volt:2253 mv
[D][05:18:22][COMM]adc read throttle adc:1706  volt:2249 mv
[D][05:18:22][COMM]adc read battery ts volt:0 mv
[D][05:18:22][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:05:43:472 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:43:839 ==>> $GBGGA,150547.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,60,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,9,,,35,12,,,35,33,,,35,1*7B

$GBGSV,7,6,25,2,,,34,5,,,32,44,,,32,4,,,32,1*45

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150547.518,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150547.518,0.000,764.719,764.719,699.353,2097152,2097152,2097152*6C

[W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:1708  volt:2251 mv
[D][05:18:23][COMM]adc read right brake adc:1711  volt:2255 mv
[D][05:18:23][COMM]adc read throttle adc:1710  volt:2254 mv
[D][05:18:23][COMM]adc read battery ts volt:1 mv
[D][05:18:23][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2413  volt

2025-07-31 23:05:43:884 ==>> :3888 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:05:44:007 ==>> 【左刹电压测试1】通过,【2251】符合目标值【2250】至【2500】要求!
2025-07-31 23:05:44:013 ==>> 检测【右刹电压测试1】
2025-07-31 23:05:44:030 ==>> 【右刹电压测试1】通过,【2255】符合目标值【2250】至【2500】要求!
2025-07-31 23:05:44:034 ==>> 检测【转把电压测试1】
2025-07-31 23:05:44:049 ==>> 【转把电压测试1】通过,【2254】符合目标值【2250】至【2500】要求!
2025-07-31 23:05:44:053 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:05:44:059 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:05:44:111 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:05:44:327 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:05:44:331 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:05:44:337 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:05:44:416 ==>> 3A A3 04 00 A3 


2025-07-31 23:05:44:521 ==>> OFF_OUT4
OVER 150


2025-07-31 23:05:44:611 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:05:44:618 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:05:44:630 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:05:44:776 ==>> $GBGGA,150544.524,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,32,5,21,256,32,1*7B

$GBGSV,7,5,25,24,19,271,36,44,8,177,32,34,,,40,25,,,40,1*44

$GBGSV,7,6,25,43,,,39,23,,,37,12,,,35,33,,,35,1*7B

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150544.524,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,150544.524,0.308,0.190,0.197,0.303,2.934,2.605,11*58

[D][05:18:24][COMM]read battery soc:255
3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:05:44:888 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:05:44:896 ==>> 检测【左刹电压测试2】
2025-07-31 23:05:44:914 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:05:45:234 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:24][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:24][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:24][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:24][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:24][COMM]adc read battery ts volt:0 mv
[D][05:18:24][COMM]adc read in 24v adc:1282  volt:32425 mv
[D][05:18:24][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:05:45:427 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:05:45:431 ==>> 检测【右刹电压测试2】
2025-07-31 23:05:45:457 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:05:45:460 ==>> 检测【转把电压测试2】
2025-07-31 23:05:45:477 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:05:45:481 ==>> 检测【晶振检测】
2025-07-31 23:05:45:487 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:05:45:765 ==>> $GBGGA,150545.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,37,3,60,190,39,59,52,129,40,10,51,234,37,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,39,4,32,111,32,5,21,256,32,1*7B

$GBGSV,7,5,25,24,19,271,36,44,8,177,32,34,,,40,25,,,39,1*4A

$GBGSV,7,6,25,43,,,39,23,,,37,33,,,35,12,,,35,1*7B

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,40,39,64,44,42,41,36,249,39,5*77

$GBRMC,150545.504,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150545.504,1.592,0.173,0.178,0.274,2.656,2.527,8.494*75

[W][05:18:25][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:25][COMM][lf state:1][hf state:1]


2025-07-31 23:05:46:009 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:05:46:014 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:05:46:022 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:05:46:125 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1653mV
Get AD_V4 1643mV
Get AD_V5 2775mV
Get AD_V6 1988mV
Get AD_V7 1094mV
OVER 150


2025-07-31 23:05:46:279 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:05:46:283 ==>> 检测【检测BootVer】
2025-07-31 23:05:46:288 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:05:46:756 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = ***************
[D][05:18:25][FCTY]HardwareID  = 867222087569663
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 32, adc = 1284
[D][05:18:26][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D

2025-07-31 23:05:46:850 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:05:46:856 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:05:46:877 ==>> 检测【检测固件版本】
2025-07-31 23:05:46:880 ==>> ][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3828 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
$GBGGA,150546.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,40,4,32,111,32,5,21,256,32,1*75

$GBGSV,7,5,25,24,19,271,36,44,8,177,32,33,0,325,35,34,,,40,1*45

$GBGSV,7,6,25,25,,,40,43,,,39,23,,,37,12,,,35,1*7E

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150546.504,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,150546.504,1.370,0.627,0.662,1.026,2.139,2.073,6.960*70

[D][05:18:26][COMM]read battery soc:255


2025-07-31 23:05:46:908 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:05:46:911 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:05:46:918 ==>> 检测【检测蓝牙版本】
2025-07-31 23:05:46:939 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:05:46:944 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:05:46:948 ==>> 检测【检测MoBikeId】
2025-07-31 23:05:46:967 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:05:46:971 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:05:46:974 ==>> 检测【检测蓝牙地址】
2025-07-31 23:05:46:978 ==>> 取到目标值:FAF334157BCC
2025-07-31 23:05:46:986 ==>> 【检测蓝牙地址】通过,【FAF334157BCC】符合目标值【】要求!
2025-07-31 23:05:46:989 ==>> 提取到蓝牙地址:FAF334157BCC
2025-07-31 23:05:47:003 ==>> 检测【BOARD_ID】
2025-07-31 23:05:47:006 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:05:47:010 ==>> 检测【检测充电电压】
2025-07-31 23:05:47:045 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:05:47:048 ==>> 检测【检测VBUS电压1】
2025-07-31 23:05:47:052 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:05:47:064 ==>> 检测【检测充电电流】
2025-07-31 23:05:47:074 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:05:47:078 ==>> 检测【检测IMEI】
2025-07-31 23:05:47:085 ==>> 取到目标值:867222087569663
2025-07-31 23:05:47:098 ==>> 【检测IMEI】通过,【867222087569663】符合目标值【】要求!
2025-07-31 23:05:47:102 ==>> 提取到IMEI:867222087569663
2025-07-31 23:05:47:110 ==>> 检测【检测IMSI】
2025-07-31 23:05:47:139 ==>> 取到目标值:***************
2025-07-31 23:05:47:143 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 23:05:47:159 ==>> 提取到IMSI:***************
2025-07-31 23:05:47:166 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:05:47:196 ==>> 取到目标值:***************
2025-07-31 23:05:47:201 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 23:05:47:208 ==>> 检测【打开CAN通信】
2025-07-31 23:05:47:227 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:05:47:235 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:05:47:437 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:05:47:442 ==>> 检测【检测CAN通信】
2025-07-31 23:05:47:448 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:05:47:510 ==>> can send success


2025-07-31 23:05:47:540 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:05:47:600 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:05:47:705 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38104
$GBGGA,150547.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,40,4,32,111,33,5,21,256,32,1*74

$GBGSV,7,5,25,24,19,271,36,44,8,177,32,33,0,325,35,34,,,40,1*45

$GBGSV,7,6,25,25,,,40,43,,,39,23,,,37,12,,,35,1*7E

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1

2025-07-31 23:05:47:721 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:05:47:727 ==>> 检测【关闭CAN通信】
2025-07-31 23:05:47:733 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:05:47:750 ==>> ,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150547.504,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,150547.504,1.278,0.219,0.227,0.348,1.844,1.803,6.028*77

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:05:47:825 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:05:47:994 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:05:48:002 ==>> 检测【打印IMU STATE】
2025-07-31 23:05:48:028 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:05:48:209 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:05:48:273 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:05:48:279 ==>> 检测【六轴自检】
2025-07-31 23:05:48:300 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:05:48:512 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:05:48:677 ==>> [D][05:18:28][COMM]read battery soc:255


2025-07-31 23:05:49:658 ==>> $GBGGA,150548.504,2301.2563978,N,11421.9451241,E,1,06,1.70,90.034,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.70,3.74,4*01

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,37,1*73

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,40,4,32,111,33,5,21,256,32,1*74

$GBGSV,7,5,25,24,19,271,36,44,8,177,33,33,0,325,35,34,,,40,1*44

$GBGSV,7,6,25,25,,,40,43,,,39,23,,,37,12,,,35,1*7E

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150548.504,A,2301.2563978,N,11421.9451241,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:18:28][GNSS]HD8040 GPS
[D][05:18:28][GNSS]GPS diff_sec 124019240, report 0x42 frame
$GBGST,150548.504,1.251,0.177,0.182,0.280,1.668,1.640,5.401*74

[D][05:18:28][COMM]Main Task receive event:131
[D][05:18:28][COMM]index:0,power_mode:0xFF
[D][05:18:28][COMM]index:1,sound_mode:0xFF
[D][05:18:28][COMM]index:2,gsensor_mode:0xFF
[D][05:18:28][COMM]index:3,report_freq_mode:0xFF
[D][05:18:28][COMM]index:4,report_period:0xFF
[D][05:18

2025-07-31 23:05:49:763 ==>> :28][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:28][COMM]index:6,normal_reset_period:0xFF
[D][05:18:28][COMM]index:7,spock_over_speed:0xFF
[D][05:18:28][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:28][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:28][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:28][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:28][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:28][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:28][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:28][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:28][COMM]index:16,imu_config_params:0xFF
[D][05:18:28][COMM]index:17,long_connect_params:0xFF
[D][05:18:28][COMM]index:18,detain_mark:0xFF
[D][05:18:28][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:28][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:28][COMM]index:21,mc_mode:0xFF
[D][05:18:28][COMM]index:22,S_mode:0xFF
[D][05:18:28][COMM]index:23,overweight:0xFF
[D][05:18:28][COMM]index:24,standstill_mode:0xFF
[D][05:18:28][COMM]index:25,night_mode:0xFF
[D][05:18:28][COMM]index:26,experiment1:0xFF
[D][05:18:28][COMM]index:27,experiment2:0xFF
[D][05:18:28][COMM]index:28,experiment3:0xFF
[D][05:

2025-07-31 23:05:49:868 ==>> 18:28][COMM]index:29,experiment4:0xFF
[D][05:18:28][COMM]index:30,night_mode_start:0xFF
[D][05:18:28][COMM]index:31,night_mode_end:0xFF
[D][05:18:28][COMM]index:33,park_report_minutes:0xFF
[D][05:18:28][COMM]index:34,park_report_mode:0xFF
[D][05:18:28][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:28][COMM]index:38,charge_battery_para: FF
[D][05:18:28][COMM]index:39,multirider_mode:0xFF
[D][05:18:28][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:28][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:28][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:28][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:28][COMM]index:44,riding_duration_config:0xFF
[D][05:18:28][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:28][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:28][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:28][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:28][COMM]index:49,mc_load_startup:0xFF
[D][05:18:28][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:28][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:28][COMM]index:52,traffic_mode:0xFF
[D][05:18:28][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:28][COMM]index:54,traffic_security_model

2025-07-31 23:05:49:973 ==>> _cycle:0xFF
[D][05:18:28][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:28][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:28][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:28][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:28][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:28][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:28][COMM]index:63,experiment5:0xFF
[D][05:18:28][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:28][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:28][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:28][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:28][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:28][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:28][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:28][COMM]index:72,experiment6:0xFF
[D][05:18:28][COMM]index:73,experiment7:0xFF
[D][05:18:28][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:28][COMM]index:75,zero_value_from_server:-1
[D][05:18:28][COMM]index:76,multirider_threshold:255
[D][05:18:28][COMM]index:77,experiment8:255
[D][05:18:28][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:28][COMM]index:79,temp

2025-07-31 23:05:50:078 ==>> _park_tail_light_twinkle_duration:255
[D][05:18:28][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:28][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:28][COMM]index:83,loc_report_interval:255
[D][05:18:28][COMM]index:84,multirider_threshold_p2:255
[D][05:18:28][COMM]index:85,multirider_strategy:255
[D][05:18:28][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:28][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:28][COMM]index:90,weight_param:0xFF
[D][05:18:28][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:28][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:28][COMM]index:95,current_limit:0xFF
[D][05:18:28][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:28][COMM]index:100,location_mode:0xFF

[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:131 finished processing
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][

2025-07-31 23:05:50:183 ==>> 05:18:28][PROT]retry_times:1
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:28][PROT]===========================================================
[D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:28][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[294]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e20] format[0]
$GBGGA,150549.004,2301.2564146,N,11421.9451404,E,1,06,1.70,90.082,M,-1.770,M,,*51

[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.70,3.74,4*01

$GBGSV,7,1,25,40,72,181,41,7,67,20

2025-07-31 23:05:50:258 ==>> 9,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,37,1*73

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,40,4,32,111,32,5,21,256,32,1*75

$GBGSV,7,5,25,24,19,271,36,44,8,177,33,33,0,325,35,34,,,40,1*44

$GBGSV,7,6,25,25,,,40,43,,,39,23,,,37,12,,,35,1*7E

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150549.004,A,2301.2564146,N,11421.9451404,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,150549.004,1.352,0.271,0.282,0.434,1.622,1.602,4.976*79



2025-07-31 23:05:50:363 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:05:50:468 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:05:50:513 ==>>                                                                                                                 [D][05:18:29][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-23,-10,4060]
[D][05:18:29][COMM]Main Task receive event:142 finished processing


2025-07-31 23:05:50:678 ==>> [D][05:18:30][COMM]read battery soc:255


2025-07-31 23:05:51:265 ==>> $GBGGA,150551.000,2301.2564285,N,11421.9450737,E,1,06,1.71,90.413,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,39,10,51,234,36,1*7C

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,38,2,45,237,34,1*4E

$GBGSV,7,4,25,60,41,239,39,41,36,249,39,4,32,111,33,5,21,256,32,1*74

$GBGSV,7,5,25,24,19,271,36,44,8,177,33,33,0,325,35,34,,,40,1*44

$GBGSV,7,6,25,25,,,39,43,,,39,23,,,37,12,,,35,1*70

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150551.000,A,2301.2564285,N,11421.9450737,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150551.000,1.157,0.180,0.185,0.288,1.339,1.328,4.282*79



2025-07-31 23:05:52:270 ==>> $GBGGA,150552.000,2301.2564058,N,11421.9450934,E,1,06,1.71,90.307,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,39,10,51,234,36,1*7C

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,32,12,28,59,34,1*7F

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,33,33,0,325,35,1*41

$GBGSV,7,6,25,34,,,40,25,,,39,43,,,38,23,,,37,1*77

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150552.000,A,2301.2564058,N,11421.9450934,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,150552.000,1.404,0.173,0.178,0.272,1.463,1.453,4.143*7F



2025-07-31 23:05:52:602 ==>> [D][05:18:32][COMM]msg 0601 loss. last_tick:38096. cur_tick:43116. period:500
[D][05:18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43117


2025-07-31 23:05:52:692 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:05:53:268 ==>> $GBGGA,150553.000,2301.2563842,N,11421.9450871,E,1,06,1.71,90.252,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,39,59,52,129,39,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,32,12,28,59,35,1*7E

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,33,33,0,325,35,1*41

$GBGSV,7,6,25,34,,,40,25,,,40,43,,,38,23,,,37,1*79

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150553.000,A,2301.2563842,N,11421.9450871,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,150553.000,1.263,0.195,0.202,0.311,1.318,1.310,3.889*78



2025-07-31 23:05:54:270 ==>> $GBGGA,150554.000,2301.2563981,N,11421.9450840,E,1,06,1.71,90.198,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,39,10,51,234,36,1*7C

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,32,12,28,59,34,1*7F

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,32,33,0,325,35,1*40

$GBGSV,7,6,25,34,,,40,25,,,40,43,,,39,23,,,37,1*78

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150554.000,A,2301.2563981,N,11421.9450840,E,0.000,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,150554.000,1.042,0.261,0.272,0.417,1.109,1.103,3.624*7B



2025-07-31 23:05:54:694 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:05:55:262 ==>> $GBGGA,150555.000,2301.2564320,N,11421.9450555,E,1,06,1.71,90.244,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,32,12,28,59,34,1*7F

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,32,33,0,325,35,1*40

$GBGSV,7,6,25,34,,,40,25,,,40,43,,,39,23,,,37,1*78

$GBGSV,7,7,25,32,,,33,1*70

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150555.000,A,2301.2564320,N,11421.9450555,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,150555.000,1.104,0.219,0.227,0.348,1.125,1.120,3.503*72



2025-07-31 23:05:55:412 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]CLEAN:0


2025-07-31 23:05:56:272 ==>> $GBGGA,150556.000,2301.2564631,N,11421.9450541,E,1,06,1.71,90.245,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,39,41,36,249,40,4,32,111,33,12,28,59,34,1*7E

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,33,33,0,325,35,1*41

$GBGSV,7,6,25,34,,,40,25,,,40,43,,,38,23,,,37,1*79

$GBGSV,7,7,25,32,,,32,1*71

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150556.000,A,2301.2564631,N,11421.9450541,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,150556.000,1.218,0.202,0.209,0.321,1.187,1.183,3.426*71



2025-07-31 23:05:56:693 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 23:05:57:259 ==>> $GBGGA,150557.000,2301.2564806,N,11421.9450803,E,1,06,1.71,90.172,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,60,190,40,59,52,129,40,10,51,234,36,1*72

$GBGSV,7,3,25,9,50,333,35,1,48,125,36,11,47,125,39,2,45,237,34,1*4F

$GBGSV,7,4,25,60,41,239,40,41,36,249,39,4,32,111,32,12,28,59,34,1*7F

$GBGSV,7,5,25,5,21,256,32,24,19,271,36,44,8,177,32,33,0,325,35,1*40

$GBGSV,7,6,25,34,,,40,25,,,40,43,,,38,23,,,37,1*79

$GBGSV,7,7,25,32,,,33,1*70

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150557.000,A,2301.2564806,N,11421.9450803,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150557.000,1.591,0.199,0.205,0.314,1.440,1.437,3.486*73



2025-07-31 23:05:58:273 ==>> $GBGGA,150558.000,2301.2564458,N,11421.9451128,E,1,06,1.71,90.092,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,16,11,41,,,,,,,4.11,1.71,3.74,4*00

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,37,3,60,190,40,59,52,129,39,10,51,234,36,1*73

$GBGSV,7,3,25,25,51,11,40,9,50,333,35,1,48,125,36,11,47,125,39,1*4A

$GBGSV,7,4,25,34,47,86,40,2,45,237,34,60,41,239,40,43,41,165,39,1*7B

$GBGSV,7,5,25,41,36,249,40,4,32,111,32,12,28,59,35,5,21,256,32,1*48

$GBGSV,7,6,25,24,19,271,36,32,15,305,33,44,8,177,32,33,0,325,35,1*76

$GBGSV,7,7,25,23,,,37,1*74

$GBGSV,1,1,03,40,72,181,41,39,64,44,42,41,36,249,40,5*78

$GBRMC,150558.000,A,2301.2564458,N,11421.9451128,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150558.000,1.352,0.216,0.223,0.343,1.248,1.243,3.277*74



2025-07-31 23:05:58:579 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:05:58:717 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 23:05:58:822 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:05:59:348 ==>> $GBGGA,150559.000,2301.2565426,N,11421.9450477,E,1,10,1.10,90.238,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,25,11,34,43,41,32,,,2.26,1.10,1.97,4*0C

$GBGSV,7,1,25,40,72,181,41,7,67,209,38,6,65,92,36,39,64,44,40,1*7A

$GBGSV,7,2,25,16,61,15,37,3,60,190,40,59,52,129,40,10,51,234,36,1*7D

$GBGSV,7,3,25,25,51,11,39,9,50,333,35,1,48,125,36,11,47,125,39,1*44

$GBGSV,7,4,25,34,47,86,40,2,45,237,33,60,41,239,40,43,41,165,39,1*7C

$GBGSV,7,5,25,41,36,249,39,4,32,111,32,12,28,59,34,5,21,256,32,1*47

$GBGSV,7,6,25,24,19,271,36,32,15,305,33,44,8,177,32,33,0,325,35,1*76

$GBGSV,7,7,25,23,,,37,1*74

$GBGSV,2,1,06,40,72,181,41,39,64,44,42,25,51,11,40,34,47,86,37,5*4C

$GBGSV,2,2,06,43,41,165,38,41,36,249,40,5*74

$GBRMC,150559.000,A,2301.2565426,N,11421.9450477,E,0.000,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,150559.000,2.776,0.172,0.167,0.253,2.124,2.124,3.711*78



2025-07-31 23:06:00:337 ==>> [D][05:18:39][CAT1]<<< 
OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 6
$GBGGA,150600.000,2301.2565876,N,11421.9450752,E,1,16,0.81,90.013,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,03,10,25,59,11,34,01,60,1.63,0.81,1.41,4*06

$GBGSA,A,3,43,41,24,32,,,,,,,,,1.63,0.81,1.41,4*04

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,6,65,92,36,39,64,44,40,1*7B

$GBGSV,7,2,25,16,61,15,38,3,61,190,40,10,55,216,36,25,51,11,40,1*45

$GBGSV,7,3,25,9,50,333,35,59,49,130,40,11,47,125,39,34,47,86,40,1*7A

$GBGSV,7,4,25,1,45,125,36,2,45,237,34,60,42,239,40,43,41,165,38,1*74

$GBGSV,7,5,25,41,36,249,39,4,32,111,32,23,29,308,37,12,28,59,34,1*74

$GBGSV,7,6,25,5,21,256,32,24,20,74,36,32,15,305,33,44,8,177,32,1*4F

$GBGSV,7,7,25,33,0,325,35,1*73

$GBGSV,2,1,07,40,72,181,41,39,64,44,42,25,51,11,41,34,47,86,39,5*42

$GBGSV,2,2,07,43,41,165,38,41,36,249,40,32,15,305,34,5*41

$GBRMC,150600.000,A,2301.2565876,N,11421.9450752,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,150600.000,3.345,0.211,0.224,0.320,2.407,2.406,3.791*7C

[D][05:18:39][COMM]Main Task receive event:142
[D][05:18:39][COMM]###### 50771 imu self test OK ######
[D][05:18:39][COMM]imu selftes

2025-07-31 23:06:00:367 ==>> t. GYRO:[0,0,0] ACCEL:[2028,-8,0]
[D][05:18:39][COMM]Main Task receive event:142 finished processing


2025-07-31 23:06:00:417 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:06:00:424 ==>> 检测【打印IMU STATE2】
2025-07-31 23:06:00:431 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:06:00:607 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:40][COMM]YAW data: 32763[32763]
[D][05:18:40][COMM]pitch:-66 roll:0
[D][05:18:40][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:06:00:700 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:06:00:706 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:06:00:729 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:06:00:733 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:06:00:817 ==>> 5A A5 02 5A A5 


2025-07-31 23:06:00:922 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:06:00:970 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:06:00:976 ==>> 检测【检测VBUS电压2】
2025-07-31 23:06:00:984 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:01:027 ==>> [D][05:18:40][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 2,volt = 11

2025-07-31 23:06:01:087 ==>> 
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:40][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:06:01:485 ==>> $GBGGA,150601.000,2301.2566697,N,11421.9450784,E,1,21,0.67,89.737,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,16,03,10,09,25,59,11,34,1.29,0.67,1.10,4*0C

$GBGSA,A,3,01,60,43,41,23,12,24,32,44,,,,1.29,0.67,1.10,4*03

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,39,64,44,40,6,61,10,36,1*75

$GBGSV,7,2,25,16,61,15,38,3,61,190,40,10,55,216,37,9,53,346,35,1*4B

$GBGSV,7,3,25,25,51,11,39,59,49,130,39,11,47,125,39,34,47,86,40,1*74

$GBGSV,7,4,25,1,45,125,36,2,45,237,34,60,42,239,40,43,41,165,38,1*74

$GBGSV,7,5,25,41,36,249,39,4,32,111,32,23,29,308,37,12,28,59,34,1*74

$GBGSV,7,6,25,5,21,256,32,24,20,74,36,32,15,305,33,44,9,43,32,1*78

$GBGSV,7,7,25,33,0,325,35,1*73

$GBGSV,3,1,09,40,72,181,41,39,64,44,42,25,51,11,41,34,47,86,40,5*43

$GBGSV,3,2,09,43,41,165,39,41,36,249,40,23,29,308,37,24,20,74,35,5*4F

$GBGSV,3,3,09,32,15,305,34,5*4F

$GBRMC,150601.000,A,2301.2566697,N,11421.9450784,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,150601.000,3.664,0.216,0.227,0.322,2.550,2.550,3.803*78

[W][05:18:40][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:40][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========
[D][05:18:40][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:40][F

2025-07-31 23:06:01:590 ==>> CTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:40][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:40][FCTY]DeviceID    = ***************
[D][05:18:40][FCTY]HardwareID  = 867222087569663
[D][05:18:40][FCTY]MoBikeID    = 9999999999
[D][05:18:40][FCTY]LockID      = FFFFFFFFFF
[D][05:18:40][FCTY]BLEFWVersion= 105
[D][05:18:40][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:40][FCTY]Bat         = 3944 mv
[D][05:18:40][FCTY]Current     = 0 ma
[D][05:18:40][FCTY]VBUS        = 11800 mv
[D][05:18:40][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:40][FCTY]Ext battery vol = 8, adc = 329
[D][05:18:40][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:40][FCTY]Bike Type flag is invalied
[D][05:18:40][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:40][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:40][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:40][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:40][FCTY]Bat1         = 3828 mv
[D][05:18:40][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========
[D][05:18:40][COMM]VBUS Insert EXTI C

2025-07-31 23:06:01:620 ==>> ome sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:06:01:759 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:02:075 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:41][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========
[D][05:18:41][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:41][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:41][FCTY]DeviceID    = ***************
[D][05:18:41][FCTY]HardwareID  = 867222087569663
[D][05:18:41][FCTY]MoBikeID    = 9999999999
[D][05:18:41][FCTY]LockID      = FFFFFFFFFF
[D][05:18:41][FCTY]BLEFWVersion= 105
[D][05:18:41][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:41][FCTY]Bat         = 3944 mv
[D][05:18:41][FCTY]Current     = 150 ma
[D][05:18:41][FCTY]VBUS        = 11800 mv
[D][05:18:41][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:41][FCTY]Ext battery vol = 4, adc = 160
[D][05:18:41][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:41][FCTY]Bike Type flag is invalied
[D][05:18:41][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:41][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:41][FCTY]CAT1_GNSS_PLATFORM = 

2025-07-31 23:06:02:120 ==>> C4
[D][05:18:41][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:41][FCTY]Bat1         = 3828 mv
[D][05:18:41][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:06:02:289 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:02:360 ==>> $GBGGA,150602.000,2301.2567621,N,11421.9450664,E,1,21,0.67,89.382,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,03,10,09,25,59,11,34,1.29,0.67,1.10,4*0C

$GBGSA,A,3,01,60,43,41,23,12,24,32,44,,,,1.29,0.67,1.10,4*03

$GBGSV,7,1,25,40,72,181,41,7,67,209,39,39,64,44,40,6,61,10,36,1*75

$GBGSV,7,2,25,16,61,15,38,3,61,190,40,10,55,216,37,9,53,346,35,1*4B

$GBGSV,7,3,25,25,51,11,40,59,49,130,40,11,47,125,39,34,47,86,40,1*74

$GBGSV,7,4,25,1,45,125,37,2,45,237,34,60,42,239,39,43,41,165,38,1*7B

$GBGSV,7,5,25,41,36,249,40,4,32,111,32,23,29,308,37,12,28,59,35,1*7B

$GBGSV,7,6,25,5,21,256,32,33,21,194,35,24,20,74,36,32,15,305,33,1*7E

$GBGSV,7,7,25,44,9,43,32,1*4E

$GBGSV,3,1,10,40,72,181,41,39,64,44,42,25,51,11,41,34,47,86,40,5*4B

$GBGSV,3,2,10,43,41,165,39,41,36,249,40,23,29,308,38,24,20,74,36,5*4B

$GBGSV,3,3,10,32,15,305,34,44,9,43,33,5*79

$GBRMC,150602.000,A,2301.2567621,N,11421.9450664,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,150602.000,3.805,0.187,0.195,0.278,2.607,2.606,3.756*73



2025-07-31 23:06:02:646 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:41][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========
[D][05:18:41][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:41][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:41][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:41][FCTY]DeviceID    = ***************
[D][05:18:41][FCTY]HardwareID  = 867222087569663
[D][05:18:41][FCTY]MoBikeID    = 9999999999
[D][05:18:41][FCTY]LockID      = FFFFFFFFFF
[D][05:18:41][FCTY]BLEFWVersion= 105
[D][05:18:41][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:41][FCTY]Bat         = 3664 mv
[D][05:18:41][FCTY]Current     = 0 ma
[D][05:18:41][FCTY]VBUS        = 7400 mv
[D][05:18:41][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:41][FCTY]Ext battery vol = 3, adc = 127
[D][05:18:41][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 0 mv
[D][05:18:41][FCTY]Bike Type flag is invalied
[D][05:18:41][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:41][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:41][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:41][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:41][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:41][FCTY]Bat1         = 3828 mv
[D][05:18:41][FCTY]=====

2025-07-31 23:06:02:676 ==>> =============== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:41][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:06:02:819 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:03:395 ==>> [D][05:18:42][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:42][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:42][COMM]Main Task receive event:65
[D][05:18:42][COMM]main task tmp_sleep_event = 80
[D][05:18:42][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:42][COMM]Main Task receive event:65 finished processing
[D][05:18:42][COMM]Main Task receive event:60
[D][05:18:42][COMM]smart_helmet_vol=255,255
[D][05:18:42][COMM]BAT CAN get state1 Fail 204
[D][05:18:42][COMM]BAT CAN get soc Fail, 204
[W][05:18:42][GNSS]stop locating
[D][05:18:42][GNSS]stop event:8
[D][05:18:42][GNSS]GPS stop. ret=0
[D][05:18:42][GNSS]all continue location stop
[D][05:18:42][COMM]report elecbike
[W][05:18:42][PROT]remove success[1629955122],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:42][PROT]add success [1629955122],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:42][COMM]Main Task receive event:60 finished processing
[D][05:18:42][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:42][PROT]index:0
[D][05:18:42][PROT]is_send:1
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]re

2025-07-31 23:06:03:501 ==>> try_timeout:0
[D][05:18:42][PROT]retry_times:3
[D][05:18:42][PROT]send_path:0x3
[D][05:18:42][PROT]msg_type:0x5d03
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]Sending traceid[9999999999900006]
[D][05:18:42][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:42][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:42][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:42][PROT]index:0 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:3
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][

2025-07-31 23:06:03:605 ==>> PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][CAT1]gsm read msg sub id: 24
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:42][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:42][CAT1]tx ret[12] >>> AT+GPSDR=0

[W][05:18:42][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:42][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:42][FCTY]==========Modules-nRF5340 ==========
[D][05:18:42][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:42][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:42][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:42][FCTY]DeviceID    = ***************
[D][05:18:42][FCTY]HardwareID  = 867222087569663
[D][05:18:42]

2025-07-31 23:06:03:710 ==>> [FCTY]MoBikeID    = 9999999999
[D][05:18:42][FCTY]LockID      = FFFFFFFFFF
[D][05:18:42][FCTY]BLEFWVersion= 105
[D][05:18:42][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:42][FCTY]Bat         = 3644 mv
[D][05:18:42][FCTY]Current     = 0 ma
[D][05:18:42][FCTY]VBUS        = 4900 mv
[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 24, ret: 6
[D][05:18:42][CAT1]sub id: 24, ret: 6

[D][05:18:42][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:42][FCTY]Ext battery vol = 2, adc = 104
[D][05:18:42][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 0 mv
[D][05:18:42][FCTY]Bike Type flag is invalied
[D][05:18:42][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:42][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:42][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:42][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:42][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:42][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:42][FCTY]Bat1         = 3828 mv
[D][05:18:42][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:42][FCTY]==========Modules-nRF5340 ==========
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send

2025-07-31 23:06:03:749 ==>>  Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B3A12C86DF7D095F73B42505E318FCE299F05608B6F8CD70189588280C7BE7F5CB1C8F2338CD505879041A5D78379A1A961A0A27AF25DF501A5316CBE04B3B77F5B71E428FCA2C86A4C9CE96768E4728FC2614
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack s

2025-07-31 23:06:03:785 ==>> ub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]


2025-07-31 23:06:03:830 ==>>                                                                                                                                            

2025-07-31 23:06:03:863 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:04:180 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:43][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:43][FCTY]==========Modules-nRF5340 ==========
[D][05:18:43][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:43][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:43][FCTY]DeviceID    = ***************
[D][05:18:43][FCTY]HardwareID  = 867222087569663
[D][05:18:43][FCTY]MoBikeID    = 9999999999
[D][05:18:43][FCTY]LockID      = FFFFFFFFFF
[D][05:18:43][FCTY]BLEFWVersion= 105
[D][05:18:43][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:18:43][FCTY]Bat         = 3644 mv
[D][05:18:43][FCTY]Current     = 0 ma
[D][05:18:43][FCTY]VBUS        = 4900 mv
[D][05:18:43][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:43][FCTY]Ext battery vol = 2, adc = 90
[D][05:18:43][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 0 mv
[D][05:18:43][FCTY]Bike Type flag is invalied
[D][05:18:43][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:43][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:43][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:43][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:43][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:43][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[

2025-07-31 23:06:04:225 ==>> D][05:18:43][FCTY]Bat1         = 3828 mv
[D][05:18:43][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:43][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:06:04:393 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:06:04:399 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:06:04:404 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:06:04:513 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:06:04:589 ==>> [D][05:18:44][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 20


2025-07-31 23:06:04:634 ==>>                                          

2025-07-31 23:06:04:671 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:06:04:677 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:06:04:698 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:06:04:814 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:06:04:954 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:06:04:960 ==>> 检测【打开WIFI(3)】
2025-07-31 23:06:04:966 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:06:05:149 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:44][CAT1]<<< 
OK

[D][05:18:44][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:06:05:228 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:06:05:237 ==>> 检测【扩展芯片hw】
2025-07-31 23:06:05:258 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:06:05:408 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:44][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:06:05:501 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:06:05:507 ==>> 检测【扩展芯片boot】
2025-07-31 23:06:05:520 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:06:05:526 ==>> 检测【扩展芯片sw】
2025-07-31 23:06:05:539 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:06:05:549 ==>> 检测【检测音频FLASH】
2025-07-31 23:06:05:563 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:06:05:679 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:06:06:876 ==>> [D][05:18:45][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:45][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:45][COMM]----- get Acckey 1 and value:1------------
[D][05:18:45][COMM]----- get Acckey 2 and value:0------------
[D][05:18:45][COMM]------------ready to Power on Acckey 2------------
+WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,44A1917CA62B,-74
+WIFISCAN:4,2,44A1917CAD81,-82
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:45][CAT1]wifi scan report total[4]
[D][05:18:45][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:45][COMM]----- get Acckey 1 and value:1------------
[D][05:18:45][COMM]----- get Acckey 2 and value:1------------
[D][05:18:45][COMM]more than the number of battery plugs
[D][05:18:45][COMM]VBUS is 1
[D][05:18:45][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:45][COMM]file:B50 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:45][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:45][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:45][COMM]Bat auth off fail, error:-1
[D][05:18:45][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18

2025-07-31 23:06:06:982 ==>> :45][COMM]----- get Acckey 1 and value:1------------
[D][05:18:45][COMM]----- get Acckey 2 and value:1------------
[D][05:18:45][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:45][COMM]----- get Acckey 1 and value:1------------
[D][05:18:45][COMM]----- get Acckey 2 and value:1------------
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:45][COMM]file:B50 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:45][COMM]read file, len:10800, num:3
[D][05:18:45][COMM]Main Task receive event:65
[D][05:18:45][COMM]main task tmp_sleep_event = 80
[D][05:18:45][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:45][COMM]Main Task receive event:65 finished processing
[D][05:18:45][COMM]--->crc16:0xb8a
[D][05:18:45][COMM]read file success
[W][05:18:45][COMM][Audio].l:[936].close hexlog save
[D][05:18:45][COMM]accel parse set 1
[D][05:18:45][COMM][Audio]mon:9,05:18:45
[D][05:18:45][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:45

2025-07-31 23:06:07:087 ==>> ][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:45][COMM]Main Task receive event:66
[D][05:18:45][COMM]Try to Auto Lock Bat
[D][05:18:45][COMM]Main Task receive event:66 finished processing
[D][05:18:45][COMM]Main Task receive event:60
[D][05:18:45][COMM]smart_helmet_vol=255,255
[D][05:18:45][COMM]BAT CAN get state1 Fail 204
[D][05:18:45][COMM]BAT CAN get soc Fail, 204
[D][05:18:45][COMM]get soc error
[E][05:18:45][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:45][COMM]report elecbike
[W][05:18:45][PROT]remove success[1629955125],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:45][PROT]add success [1629955125],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:45][COMM]Main Task receive event:60 finished processing
[D][05:18:45][COMM]Main Task receive event:61
[D][05:18:45][COMM][D301]:type:3, trace id:280
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]index:1
[D][05:18:45][PROT]is_send:1
[D][05:18:45][PROT]sequence_num:6
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:3
[D][05:18:45][PROT]send_path:0x3
[D][05:18:45][PROT]msg_typ

2025-07-31 23:06:07:193 ==>> e:0x5d03
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]Sending traceid[9999999999900007]
[D][05:18:45][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:45][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:45][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:45][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:45][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:45][COMM]Receive Bat Lock cmd 0
[D][05:18:45][COMM]VBUS is 1
[D][05:18:45][COMM]id[], hw[000
[D][05:18:45][COMM]get mcMaincircuitVolt error
[D][05:18:45][COMM]get mcSubcircuitVolt error
[D][05:18:45][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:45][COMM]BAT CAN get state1 Fail 204
[D][05:18:45][COMM]BAT CAN get soc Fail, 204
[D][05:18:45][COMM]get bat work state err
[W][05:18:45][PROT]remove 

2025-07-31 23:06:07:299 ==>> success[1629955125],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:45][PROT]add success [1629955125],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:45][COMM]Main Task receive event:61 finished processing
[D][05:18:45][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:45][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:45][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:45][C

2025-07-31 23:06:07:404 ==>> OMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:45][GNSS]recv submsg id[3]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:46][COMM]read battery soc:255


2025-07-31 23:06:08:482 ==>> [D][05:18:47][PROT]CLEAN,SEND:0
[D][05:18:47][PROT]index:1 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:6
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:3
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900007]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE8238A29F3DBA6286A67CB0B021BF8387ED49C03625898ABD4E09E70F1FAC2BC

2025-07-31 23:06:08:557 ==>> 91C3F28FEE89E827CB8FDC5EFF01EBA58669BF21ACEE538DF05294FD3AE546D9A012DFA2723EE15ECE094E4E0F63C643301E6
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]


2025-07-31 23:06:08:662 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:06:09:260 ==>> [D][05:18:48][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:06:09:799 ==>> [D][05:18:49][COMM]crc 108B
[D][05:18:49][COMM]flash test ok


2025-07-31 23:06:10:366 ==>> [D][05:18:49][COMM]60771 imu init OK
[D][05:18:49][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:49][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:49][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:49][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:49][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:49][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:49][COMM]accel parse set 0
[D][05:18:49][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:06:10:595 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:06:10:602 ==>> 检测【打开喇叭声音】
2025-07-31 23:06:10:627 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:06:10:666 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:06:11:334 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:50][COMM]file:A20 exist
[D][05:18:50][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:50][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:50][COMM]file:A20 exist
[D][05:18:50][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:50][COMM]read file, len:15228, num:4
[D][05:18:50][COMM]--->crc16:0x419c
[D][05:18:50][COMM]read file success
[W][05:18:50][COMM][Audio].l:[936].close hexlog save
[D][05:18:50][COMM]accel parse set 1
[D][05:18:50][COMM][Audio]mon:9,05:18:50
[D][05:18:50][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:50][COMM]f:

2025-07-31 23:06:11:383 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:06:11:395 ==>> 检测【打开大灯控制】
2025-07-31 23:06:11:414 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:06:11:440 ==>> [ec800m_audio_start].l:[691].recv ok
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:50][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, ind

2025-07-31 23:06:11:544 ==>> ex:4, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:50][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:50][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:50][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:50][COMM]61783 imu init OK


2025-07-31 23:06:11:619 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:06:11:664 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:06:11:671 ==>> 检测【关闭仪表供电3】
2025-07-31 23:06:11:677 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:06:11:801 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:51][COMM]set POWER 0


2025-07-31 23:06:11:940 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:06:11:955 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:06:11:973 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:06:12:078 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:06:12:221 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:06:12:232 ==>> 检测【读大灯电压】
2025-07-31 23:06:12:250 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:06:12:400 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:51][COMM]arm_hub read adc[5],val[33340]


2025-07-31 23:06:12:494 ==>> 【读大灯电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:06:12:505 ==>> 检测【关闭大灯控制2】
2025-07-31 23:06:12:525 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:06:12:701 ==>> [D][05:18:52][COMM]read battery soc:255
[W][05:18:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:06:12:764 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:06:12:771 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:06:12:795 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:06:12:899 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:52][COMM]arm_hub read adc[5],val[115]


2025-07-31 23:06:13:039 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:06:13:049 ==>> 检测【打开WIFI(4)】
2025-07-31 23:06:13:074 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:06:13:236 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 12
[D][05:18:52][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:06:13:360 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:06:13:367 ==>> 检测【EC800M模组版本】
2025-07-31 23:06:13:377 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:06:13:635 ==>> [D][05:18:52][PROT]CLEAN,SEND:1
[D][05:18:52][PROT]index:1 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:6
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900007]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:52][CAT1]gsm read msg sub id: 15
[W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 23:06:13:922 ==>>                                                                                                                                                                                                                           F3FE2B7D9A8E929FDC7CD85DA761648CD60BDC
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]
[D][05:18:53][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:53][CAT1]exec over: func id: 12, ret: 132
[D][05:18:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:06:14:156 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:06:14:163 ==>> 检测【配置蓝牙地址】
2025-07-31 23:06:14:173 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:06:14:277 ==>> [W][05:18:53][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:06:14:367 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FAF334157BCC>】
2025-07-31 23:06:14:522 ==>> recv ble 1
recv ble 2
ble set mac ok :fa,f3,34,15,7b,cc
enable filters ret : 0

2025-07-31 23:06:14:646 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:06:14:652 ==>> 检测【BLETEST】
2025-07-31 23:06:14:660 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:06:14:672 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:06:14:717 ==>> 4A A4 01 A4 4A 


2025-07-31 23:06:14:822 ==>> recv ble 1
recv ble 2
<BSJ*MAC:FAF334157BCC*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FAF334157BCC99999OVER 150


2025-07-31 23:06:14:867 ==>> [D][05:18:54][COMM]65362 imu init OK
[D][05:18:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:06:15:674 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:06:15:682 ==>> 该项需要延时执行
2025-07-31 23:06:15:859 ==>> [D][05:18:55][COMM]66373 imu init OK
[D][05:18:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:06:16:439 ==>> [D][05:18:55][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:55][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:55][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:55][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:55][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:55][COMM]accel parse set 0
[D][05:18:55][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:06:16:679 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 23:06:16:862 ==>> [D][05:18:56][COMM]67384 imu init OK


2025-07-31 23:06:18:111 ==>> +WIFISCAN:4,0,F88C21BCF57D,-36
+WIFISCAN:4,1,F62A7D2297A3,-67
+WIFISCAN:4,2,44A1917CA62B,-75
+WIFISCAN:4,3,CC057790A7C1,-77

[D][05:18:57][CAT1]wifi scan report total[4]


2025-07-31 23:06:18:464 ==>> [D][05:18:57][GNSS]recv submsg id[3]


2025-07-31 23:06:18:693 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 23:06:19:116 ==>> [D][05:18:58][PROT]CLEAN,SEND:1
[D][05:18:58][PROT]index:1 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:1
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][PROT]sending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[198]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331

2025-07-31 23:06:19:191 ==>> B88BE99FB6EB42C4C81316DD0801BAA474F3D24EE70789ABCD83F8958624A87F434D63717BF0DBDD086DA63CBF7061C6DB1A2D4AC0226F01E7AC1BB6498D2D40C40CF46A92D7F37214A55351C6AD99EFF7DD38C30
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 23:06:20:698 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 23:06:22:703 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:06:24:362 ==>> [D][05:19:03][PROT]CLEAN,SEND:1
[D][05:19:03][PROT]CLEAN:1
[D][05:19:03][PROT]index:0 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:5
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:2
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][PROT]sending traceid [9999999999900006]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[198]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B3E2950159477F424832A5CB06F36B2

2025-07-31 23:06:24:437 ==>> 3CB58073B25CB4D7061ECD560C373B096D78465716E878495E31D8B8F2A3F723CC3DC213AE1286723AB2BB3F5EF654E0CC8CD463E479B4C7B201874F1F9DA1AA93EC7FB
[D][05:19:03][CAT1]<<< 
SEND OK

[D][05:19:03][CAT1]exec over: func id: 15, ret: 11
[D][05:19:03][CAT1]sub id: 15, ret: 11

[D][05:19:03][SAL ]Cellular task submsg id[68]
[D][05:19:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:03][M2M ]g_m2m_is_idle become true
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:03][PROT]M2M Send ok [1629955143]


2025-07-31 23:06:24:712 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 23:06:25:685 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:06:25:694 ==>> 检测【检测WiFi结果】
2025-07-31 23:06:25:702 ==>> WiFi信号:【F88C21BCF57D】,信号值:-36
2025-07-31 23:06:25:710 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 23:06:25:718 ==>> WiFi信号:【CC057790A7C1】,信号值:-71
2025-07-31 23:06:25:736 ==>> WiFi信号:【F86FB0660A82】,信号值:-83
2025-07-31 23:06:25:752 ==>> WiFi信号:【44A1917CA62B】,信号值:-74
2025-07-31 23:06:25:769 ==>> WiFi信号:【44A1917CAD81】,信号值:-82
2025-07-31 23:06:25:775 ==>> WiFi信号:【F62A7D2297A3】,信号值:-67
2025-07-31 23:06:25:798 ==>> WiFi数量【7】, 最大信号值:-36
2025-07-31 23:06:25:808 ==>> 检测【检测GPS结果】
2025-07-31 23:06:25:830 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 23:06:25:841 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【39】,信号值:【42】
北斗星号:【16】,信号值:【38】
北斗星号:【3】,信号值:【40】
北斗星号:【59】,信号值:【40】
北斗星号:【10】,信号值:【37】
北斗星号:【9】,信号值:【35】
北斗星号:【1】,信号值:【36】
北斗星号:【11】,信号值:【39】
北斗星号:【60】,信号值:【40】
北斗星号:【41】,信号值:【40】
北斗星号:【24】,信号值:【36】
北斗星号:【33】,信号值:【35】
北斗星号:【34】,信号值:【40】
北斗星号:【25】,信号值:【40】
北斗星号:【43】,信号值:【39】
北斗星号:【23】,信号值:【37】
北斗星号:【12】,信号值:【35】

2025-07-31 23:06:25:862 ==>> 检测【CSQ强度】
2025-07-31 23:06:25:869 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:06:25:934 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:05][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:05][CAT1]exec over: func id: 12, ret: 21


2025-07-31 23:06:25:994 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 23:06:26:000 ==>> 检测【关闭GSM联网】
2025-07-31 23:06:26:014 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:06:26:209 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:05][COMM]GSM test
[D][05:19:05][COMM]GSM test disable


2025-07-31 23:06:26:272 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:06:26:279 ==>> 检测【4G联网测试】
2025-07-31 23:06:26:288 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:06:26:513 ==>> [D][05:19:05][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:05][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:06:27:346 ==>>                           sk receive event:14
[D][05:19:06][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955146, allstateRepSeconds = 0
[D][05:19:06][COMM]index:0,power_mode:0xFF
[D][05:19:06][COMM]index:1,sound_mode:0xFF
[D][05:19:06][COMM]index:2,gsensor_mode:0xFF
[D][05:19:06][COMM]index:3,report_freq_mode:0xFF
[D][05:19:06][COMM]index:4,report_period:0xFF
[D][05:19:06][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:06][COMM]index:6,normal_reset_period:0xFF
[D][05:19:06][COMM]index:7,spock_over_speed:0xFF
[D][05:19:06][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:06][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:06][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:06][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:06][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:06][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:06][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:06][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:06][COMM]index:16,imu_config_params:0xFF
[D][05:19:06][COMM]index:17,long_connect_params:0xFF
[D][05:19:06][COMM]index:18,detain_mark:0xFF
[D][05:19:06][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:06][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:06][COMM]index:21,mc_mode:0xFF
[D][05:19:06][COMM]index:22,

2025-07-31 23:06:27:452 ==>> S_mode:0xFF
[D][05:19:06][COMM]index:23,overweight:0xFF
[D][05:19:06][COMM]index:24,standstill_mode:0xFF
[D][05:19:06][COMM]index:25,night_mode:0xFF
[D][05:19:06][COMM]index:26,experiment1:0xFF
[D][05:19:06][COMM]index:27,experiment2:0xFF
[D][05:19:06][COMM]index:28,experiment3:0xFF
[D][05:19:06][COMM]index:29,experiment4:0xFF
[D][05:19:06][COMM]index:30,night_mode_start:0xFF
[D][05:19:06][COMM]index:31,night_mode_end:0xFF
[D][05:19:06][COMM]index:33,park_report_minutes:0xFF
[D][05:19:06][COMM]index:34,park_report_mode:0xFF
[D][05:19:06][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:06][COMM]index:38,charge_battery_para: FF
[D][05:19:06][COMM]index:39,multirider_mode:0xFF
[D][05:19:06][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:06][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:06][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:06][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:06][COMM]index:44,riding_duration_config:0xFF
[D][05:19:06][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:06][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:06][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:06][COMM]index:48,shlmt_sensor_en:0xFF
[D][

2025-07-31 23:06:27:557 ==>> 05:19:06][COMM]index:49,mc_load_startup:0xFF
[D][05:19:06][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:06][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:06][COMM]index:52,traffic_mode:0xFF
[D][05:19:06][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:06][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:06][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:06][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:06][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:06][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:06][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:06][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:06][COMM]index:63,experiment5:0xFF
[D][05:19:06][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:06][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:06][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:06][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:06][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:06][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:06][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:06][COMM]index:72,experiment6:0xFF
[D][05:19:06][COMM]index:73,experiment7:0xFF
[D][

2025-07-31 23:06:27:662 ==>> 05:19:06][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:06][COMM]index:75,zero_value_from_server:-1
[D][05:19:06][COMM]index:76,multirider_threshold:255
[D][05:19:06][COMM]index:77,experiment8:255
[D][05:19:06][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:06][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:06][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:06][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:06][COMM]index:83,loc_report_interval:255
[D][05:19:06][COMM]index:84,multirider_threshold_p2:255
[D][05:19:06][COMM]index:85,multirider_strategy:255
[D][05:19:06][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:06][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:06][COMM]index:90,weight_param:0xFF
[D][05:19:06][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:06][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:06][COMM]index:95,current_limit:0xFF
[D][05:19:06][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:06][COMM]index:100,location_mode:0xFF

[W][05:19:06][PROT]remove success[1629955146],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:06

2025-07-31 23:06:27:766 ==>> ][PROT]add success [1629955146],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:06][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:06][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:06][CAT1]gsm read msg sub id: 13
[D][05:19:06][PROT]index:0 1629955146
[D][05:19:06][PROT]is_send:0
[D][05:19:06][PROT]sequence_num:8
[D][05:19:06][PROT]retry_timeout:0
[D][05:19:06][PROT]retry_times:1
[D][05:19:06][PROT]send_path:0x2
[D][05:19:06][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][PROT]sending traceid [9999999999900009]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[294]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:06][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[

2025-07-31 23:06:27:871 ==>> 0]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:06][CAT1]exec over: func id: 13, ret: 21
[D][05:19:06][M2M ]get csq[21]
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:06][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B15C54FDB82B46D7F8602B1C106B8AA671FFFC15431E6CC88BCC6CA3D669652D35644155DA509DBA858AABFE78E471C11FB5931635788ECAAF15F24890760A4CF1B1B4E879FF545405B05CF742E4AE366C52B835CB630AB3A6182A92647FC6C6249A3161AE0E22C60BF5C29FC0877FCED91B3871C4E148F053D7F98474637C8BA5E5EF
[D][05:19:06][CAT1]<<< 
SEND OK

[D][05:19:06][CAT1]exec over: func id: 15, ret: 11
[D][05:19:06][CAT1]sub id: 15, ret: 11

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:06][PROT]M2M Send ok [1629955146]
[D][05:19:06][COMM]read battery soc:255
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:06][PROT]remove success[1629955146],send_path[2]

2025-07-31 23:06:27:946 ==>> ,type[0000],priority[0],index[1],used[0]
[W][05:19:06][PROT]add success [1629955146],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:06][COMM]------>period, report file manifest
[D][05:19:06][COMM]Main Task receive event:14 finished processing
[D][05:19:06][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:06][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:06][CAT1]gsm read msg sub id: 21
[D][05:19:06][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]cell info report total[0]
[D][05:19:06][CAT1]exec over: func id: 21, ret: 6


2025-07-31 23:06:28:319 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:06:28:349 ==>> 检测【关闭GPS】
2025-07-31 23:06:28:355 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:06:28:518 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:08][GNSS]stop locating
[D][05:19:08][GNSS]all continue location stop
[W][05:19:08][GNSS]stop locating
[D][05:19:08][GNSS]all sing location stop


2025-07-31 23:06:28:607 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:06:28:614 ==>> 检测【清空消息队列2】
2025-07-31 23:06:28:628 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:06:28:714 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 23:06:28:804 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:08][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:06:28:883 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:06:28:895 ==>> 检测【轮动检测】
2025-07-31 23:06:28:913 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:06:29:018 ==>> 3A A3 01 00 A3 


2025-07-31 23:06:29:123 ==>> OFF_OUT1
OVER 150


2025-07-31 23:06:29:183 ==>> [D][05:19:08][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:06:29:395 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:06:29:515 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 23:06:29:741 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:06:29:748 ==>> 检测【关闭小电池】
2025-07-31 23:06:29:766 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:06:29:821 ==>> 6A A6 02 A6 6A 


2025-07-31 23:06:29:911 ==>> Battery OFF
OVER 150


2025-07-31 23:06:30:028 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:06:30:046 ==>> 检测【进入休眠模式】
2025-07-31 23:06:30:072 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:06:30:244 ==>> [D][05:19:09][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:09][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]main task tmp_sleep_event = 8
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:06:30:740 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 23:06:31:045 ==>> [D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[D][05:19:10][M2M ]tcpclient close[4]
[D][05:19:10][SAL ]Cellular task submsg id[12]
[D][05:19:10][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c98], socket[0]
[D][05:19:10][CAT1]gsm read msg sub id: 9
[D][05:19:10][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 9, ret: 6
[D][05:19:10][CAT1]sub id: 9, ret: 6

[D][05:19:10][SAL ]Cellular task submsg id[68]
[D][05:19:10][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:10][SAL ]socket close ind. id[4]
[D][05:19:10][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:10][COMM]1x1 frm_can_tp_send ok
[D][05:19:10][CAT1]pdpdeact urc len[22]


2025-07-31 23:06:31:320 ==>> [E][05:19:10][COMM]1x1 rx timeout
[D][05:19:10][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:06:31:839 ==>> [E][05:19:11][COMM]1x1 rx timeout
[E][05:19:11][COMM]1x1 tp timeout
[E][05:19:11][COMM]1x1 error -3.
[W][05:19:11][COMM]CAN STOP!
[D][05:19:11][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:11][COMM]------------ready to Power off Acckey 1------------
[D][05:19:11][COMM]------------ready to Power off Acckey 2------------
[D][05:19:11][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:11][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1280
[D][05:19:11][COMM]bat sleep fail, reason:-1
[D][05:19:11][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:11][COMM]accel parse set 0
[D][05:19:11][COMM]imu rest ok. 82251
[D][05:19:11][COMM]imu sleep 0
[W][05:19:11][COMM]now sleep


2025-07-31 23:06:32:144 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:06:32:159 ==>> 检测【检测33V休眠电流】
2025-07-31 23:06:32:166 ==>> 开始33V电流采样
2025-07-31 23:06:32:195 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:32:253 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:06:33:255 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:06:33:301 ==>> Current33V:????:14.47

2025-07-31 23:06:33:769 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:33:778 ==>> 【检测33V休眠电流】通过,【14.47uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:06:33:801 ==>> 该项需要延时执行
2025-07-31 23:06:35:785 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:06:35:797 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:06:35:820 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:06:35:920 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1654mV
Get AD_V4 1mV
Get AD_V5 2759mV
Get AD_V6 2021mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:06:36:838 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:06:36:846 ==>> 检测【打开小电池2】
2025-07-31 23:06:36:854 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:06:36:923 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:06:37:188 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:06:37:195 ==>> 该项需要延时执行
2025-07-31 23:06:37:694 ==>> 此处延时了:【500】毫秒
2025-07-31 23:06:37:725 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:06:37:736 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:06:37:815 ==>> 5A A5 02 5A A5 


2025-07-31 23:06:37:920 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:06:37:983 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:06:37:991 ==>> 该项需要延时执行
2025-07-31 23:06:38:489 ==>> 此处延时了:【500】毫秒
2025-07-31 23:06:38:501 ==>> 检测【进入休眠模式2】
2025-07-31 23:06:38:529 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:06:38:595 ==>> [D][05:19:17][COMM]------------ready to Power on Acckey 1------------
[D][05:19:17][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:17][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:17][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:17][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:17][COMM]----- get Acckey 1 and value:1------------
[W][05:19:17][COMM]CAN START!
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:17][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 88941
[D][05:19:17][COMM][Audio]exec status ready.
[D][0

2025-07-31 23:06:38:655 ==>> 5:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 6
[D][05:19:17][COMM]imu wakeup ok. 88956
[D][05:19:17][COMM]imu wakeup 1
[W][05:19:17][COMM]wake up system, wakeupEvt=0x80
[D][05:19:17][COMM]frm_can_weigth_power_set 1
[D][05:19:17][COMM]Clear Sleep Block Evt
[D][05:19:17][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:06:38:716 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:06:39:004 ==>> [E][05:19:18][COMM]1x1 rx timeout
[D][05:19:18][COMM]1x1 frm_can_tp_send ok
[D][05:19:18][COMM]msg 02A0 loss. last_tick:88926. cur_tick:89435. period:50
[D][05:19:18][COMM]msg 02A4 loss. last_tick:88926. cur_tick:89436. period:50
[D][05:19:18][COMM]msg 02A5 loss. last_tick:88926. cur_tick:89436. period:50
[D][05:19:18][COMM]msg 02A6 loss. last_tick:88926. cur_tick:89436. period:50
[D][05:19:18][COMM]msg 02A7 loss. last_tick:88926. cur_tick:89437. period:50
[D][05:19:18][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 89437
[D][05:19:18][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 89438


2025-07-31 23:06:39:337 ==>> [E][05:19:18][COMM]1x1 rx timeout
[E][05:19:18][COMM]1x1 tp timeout
[E][05:19:18][COMM]1x1 error -3.
[D][05:19:18][COMM]Main Task receive event:28 finished processing
[D][05:19:18][COMM]Main Task receive event:28
[D][05:19:18][COMM]prepare to sleep
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 6
[D][05:19:18][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:18][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:06:39:640 ==>> [D][05:19:18][COMM]msg 0220 loss. last_tick:88926. cur_tick:89932. period:100
[D][05:19:18][COMM]msg 0221 loss. last_tick:88926. cur_tick:89932. period:100
[D][05:19:18][COMM]msg 0224 loss. last_tick:88926. cur_tick:89933. period:100
[D][05:19:18][COMM]msg 0260 loss. last_tick:88926. cur_tick:89933. period:100
[D][05:19:18][COMM]msg 0280 loss. last_tick:88926. cur_tick:89933. period:100
[D][05:19:18][COMM]msg 02C0 loss. last_tick:88926. cur_tick:89934. period:100
[D][05:19:18][COMM]msg 02C1 loss. last_tick:88926. cur_tick:89934. period:100
[D][05:19:18][COMM]msg 02C2 loss. last_tick:88926. cur_tick:89934. period:100
[D][05:19:18][COMM]msg 02E0 loss. last_tick:88926. cur_tick:89935. period:100
[D][05:19:18][COMM]msg 02E1 loss. last_tick:88926. cur_tick:89935. period:100
[D][05:19:18][COMM]msg 02E2 loss. last_tick:88926. cur_tick:89936. period:100
[D][05:19:18][COMM]msg 0300 loss. last_tick:88926. cur_tick:89936. period:100
[D][05:19:18][COMM]msg 0301 loss. last_tick:88926. cur_tick:89936. period:100
[D][05:19:18][COMM]bat msg 0240 loss. last_tick:88926. cur_tick:89937. period:100. j,i:1 54
[D][05:19:18][COMM]bat msg 0241 loss. last_tick:88926. cur_tick:89937. period:100. j,i:2 55
[D][05:19:18][COMM]bat msg

2025-07-31 23:06:39:745 ==>>  0242 loss. last_tick:88926. cur_tick:89937. period:100. j,i:3 56
[D][05:19:18][COMM]bat msg 0244 loss. last_tick:88926. cur_tick:89938. period:100. j,i:5 58
[D][05:19:18][COMM]bat msg 024E loss. last_tick:88926. cur_tick:89938. period:100. j,i:15 68
[D][05:19:18][COMM]bat msg 024F loss. last_tick:88926. cur_tick:89938. period:100. j,i:16 69
[D][05:19:18][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 89939
[D][05:19:18][COMM]CAN message bat fault change: 0x00000000->0x0001802E 89939
[D][05:19:18][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 89940
                                                                              

2025-07-31 23:06:40:003 ==>> [D][05:19:19][COMM]msg 0222 loss. last_tick:88926. cur_tick:90435. period:150
[D][05:19:19][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 90436
[D][05:19:19][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:19][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:19][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:06:40:214 ==>> [E][05:19:19][COMM]1x1 rx timeout
[E][05:19:19][COMM]1x1 tp timeout
[E][05:19:19][COMM]1x1 error -3.
[W][05:19:19][COMM]CAN STOP!
[D][05:19:19][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:19][COMM]------------ready to Power off Acckey 1------------
[D][05:19:19][COMM]------------ready to Power off Acckey 2------------
[D][05:19:19][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:19][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 86
[D][05:19:19][COMM]bat sleep fail, reason:-1
[D][05:19:19][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:19][COMM]accel parse set 0
[D][05:19:19][COMM]imu rest ok. 90624
[D][05:19:19][COMM]imu sleep 0
[W][05:19:19][COMM]now sleep


2025-07-31 23:06:40:308 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:06:40:319 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:06:40:339 ==>> 开始小电池电流采样
2025-07-31 23:06:40:350 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:40:409 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:06:41:409 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:06:41:488 ==>> CurrentBattery:ƽ��:67.32

2025-07-31 23:06:41:923 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:41:930 ==>> 【检测小电池休眠电流】通过,【67.32uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:06:41:955 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:06:41:962 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:06:42:015 ==>> 5A A5 01 5A A5 


2025-07-31 23:06:42:120 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:06:42:262 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:06:42:275 ==>> 该项需要延时执行
2025-07-31 23:06:42:361 ==>> [D][05:19:21][COMM]------------ready to Power on Acckey 1------------
[D][05:19:21][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:21][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:21][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6
[D][05:19:21][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:21][COMM]----- get Acckey 1 and value:1------------
[W][05:19:21][COMM]CAN START!
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:21][COMM]CAN message bat fault change: 0x0001802E->0x00000000 92720
[D][05:19:21][COMM][Audio]exec status ready.
[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 6
[D][05:19:21][COMM]imu wakeup ok. 92734
[D][05:19:21][COMM]imu wakeup 1
[W][05:19:21][COMM]wake up system, wakeupEvt=0x80
[D][05:19:21][COMM]frm_can_weigth_power_set 1
[D][05:19:21][COMM]Clear Sleep Block Evt
[D][05:19:21][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:21][COMM]1x1 frm_can_tp_send ok
[D][05:19:21][COMM]read battery soc:0


2025-07-31 23:06:42:513 ==>> [D][05:19:21][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 23:06:42:772 ==>> 此处延时了:【500】毫秒
2025-07-31 23:06:42:792 ==>> 检测【检测唤醒】
2025-07-31 23:06:42:817 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:06:42:825 ==>> [D][05:19:22][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:22][COMM]1x1 rx timeout
[D][05:19:22][COMM]1x1 frm_can_tp_send ok
[D][05:19:22][COMM]msg 02A0 loss. last_tick:92702. cur_tick:93213. period:50
[D][05:19:22][COMM]msg 02A4 loss. last_tick:92702. cur_tick:93213. period:50
[D][05:19:22][COMM]msg 02A5 loss. last_tick:92702. cur_tick:93214. period:50
[D][05:19:22][COMM]msg 02A6 loss. last_tick:92702. cur_tick:93214. period:50
[D][05:19:22][COMM]msg 02A7 loss. last_tick:92702. cur_tick:93215. period:50
[D][05:19:22][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 93215
[D][05:19:22][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 93216


2025-07-31 23:06:43:557 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:22][FCTY]==========Modules-nRF5340 ==========
[D][05:19:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:22][FCTY]DeviceID    = ***************
[D][05:19:22][FCTY]HardwareID  = 867222087569663
[D][05:19:22][FCTY]MoBikeID    = 9999999999
[D][05:19:22][FCTY]LockID      = FFFFFFFFFF
[D][05:19:22][FCTY]BLEFWVersion= 105
[D][05:19:22][FCTY]BLEMacAddr   = FAF334157BCC
[D][05:19:22][FCTY]Bat         = 3724 mv
[D][05:19:22][FCTY]Current     = 0 ma
[D][05:19:22][FCTY]VBUS        = 2600 mv
[D][05:19:22][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:22][FCTY]Ext battery vol = 32, adc = 1288
[D][05:19:22][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 0 mv
[D][05:19:22][FCTY]Bike Type flag is invalied
[D][05:19:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:22][FCTY]CAT1_GNSS_PLATFORM = C4
[

2025-07-31 23:06:43:662 ==>> D][05:19:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:22][FCTY]Bat1         = 3828 mv
[D][05:19:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:22][FCTY]==========Modules-nRF5340 ==========
[E][05:19:22][COMM]1x1 rx timeout
[E][05:19:22][COMM]1x1 tp timeout
[E][05:19:22][COMM]1x1 error -3.
[D][05:19:22][COMM]Main Task receive event:28 finished processing
[D][05:19:22][COMM]Main Task receive event:65
[D][05:19:22][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:22][COMM]Main Task receive event:65 finished processing
[D][05:19:22][COMM]Main Task receive event:60
[D][05:19:22][COMM]smart_helmet_vol=255,255
[D][05:19:22][COMM]report elecbike
[W][05:19:22][PROT]remove success[1629955162],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:22][COMM]Main Task receive event:60 finished processing
[D][05:19:22][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:22][PROT]index:0
[D][05:19:22][PROT]is_send:1
[D][05:19:22][PROT]sequence_num:10
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:3
[D][05:

2025-07-31 23:06:43:767 ==>> 19:22][PROT]send_path:0x3
[D][05:19:22][PROT]msg_type:0x5d03
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]Sending traceid[999999999990000B]
[D][05:19:22][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:22][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:22][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:22][SAL ]open socket ind id[4], rst[0]
[D][05:19:22][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:22][SAL ]Cellular task submsg id[8]
[D][05:19:22][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:22][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK


2025-07-31 23:06:43:831 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:06:43:841 ==>> 检测【关机】
2025-07-31 23:06:43:856 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:06:43:883 ==>> [D][05:19:22][CAT1]gsm read msg sub id: 8
[D][05:19:22][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:22][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:22][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:22][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:22][CAT1]<<< 
+CME ERROR: 100

[D][05:19:22][COMM]msg 0220 loss. last_tick:92702. cur_tick:93710. period:100
[D][05:19:22][COMM]msg 0221 loss. last_tick:92702. cur_tick:93711. period:100
[D][05:19:22][COMM]msg 0224 loss. last_tick:92702. cur_tick:93711. period:100
[D][05:19:22][COMM]msg 0260 loss. last_tick:92702. cur_tick:93711. period:100
[D][05:19:22][COMM]msg 0280 loss. last_tick:92702. cur_tick:93712. period:100
[D][05:19:22][COMM]msg 02C0 loss. last_tick:92702. cur_tick:93712. period:100
[D][05:19:22][COMM]msg 02C1 loss. last_tick:92702. cur_tick:93712. period:100
[D][05:19:22][COMM]msg 02C2 loss. last_tick:92702. cur_tick:93713. period:100
[D][05:19:22][COMM]msg 02E0 loss. last_tick:92702. cur_tick:93713. period:100
[D][05:19:22][COMM]msg 02E1 loss. last_tick:92702. cur_tick:93714. period:100
[D][05:19:22][COMM]msg 02E2 loss. last_tick:92702. cur_tick:93714. period:100
[D][05:19:22][COMM]msg 0300 loss. last_tick:92702. 

2025-07-31 23:06:43:977 ==>> cur_tick:93714. period:100
[D][05:19:22][COMM]msg 0301 loss. last_tick:92702. cur_tick:93715. period:100
[D][05:19:22][COMM]bat msg 0240 loss. last_tick:92702. cur_tick:93715. period:100. j,i:1 54
[D][05:19:22][COMM]bat msg 0241 loss. last_tick:92702. cur_tick:93716. period:100. j,i:2 55
[D][05:19:22][COMM]bat msg 0242 loss. last_tick:92702. cur_tick:93716. period:100. j,i:3 56
[D][05:19:22][COMM]bat msg 0244 loss. last_tick:92702. cur_tick:93716. period:100. j,i:5 58
[D][05:19:22][COMM]bat msg 024E loss. last_tick:92702. cur_tick:93717. period:100. j,i:15 68
[D][05:19:22][COMM]bat msg 024F loss. last_tick:92702. cur_tick:93717. period:100. j,i:16 69
[D][05:19:22][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 93717
[D][05:19:22][COMM]CAN message bat fault change: 0x00000000->0x0001802E 93718
[D][05:19:22][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 93718


2025-07-31 23:06:44:578 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:06:44:682 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:06:44:787 ==>>                                                                                                                                                                                                                                                                                                                                                                                       event:65 finished processing
[D][05:19:23][COMM]Main Task receive event:66
[D][05:19:23][COMM]Try to Auto Lock Bat
[D][05:19:23][COMM]Main Task receive event:66 finished processing
[D][05:19:23][COMM]Main Task receive event:60
[D][05:19:23][COMM]smart_helmet_vol=255,255
[D][05:19:23][COMM]BAT CAN get state1 Fail 204
[D][05:19:23][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:23][COMM]BAT CAN get soc Fail, 204
[D][05:19:23][COMM]BAT CAN get state2 fail 204
[D][05:19:23][COMM]get soh error
[E][05:19:23][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:23][COMM]report elecbike
[W][05:19:23][PROT]remove success[16299551

2025-07-31 23:06:44:863 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:06:44:892 ==>> 63],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:23][COMM]Main Task receive event:60 finished processing
[D][05:19:23][COMM]Main Task receive event:61
[D][05:19:23][COMM][D301]:type:3, trace id:280
[D][05:19:23][COMM]id[], hw[000
[D][05:19:23][COMM]get mcMaincircuitVolt error
[D][05:19:23][COMM]get mcSubcircuitVolt error
[D][05:19:23][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:23][COMM]BAT CAN get state1 Fail 204
[D][05:19:23][COMM]BAT CAN get soc Fail, 204
[D][05:19:23][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][PROT]index:1
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:23][PROT]is_send:1
[D][05:19:23][PROT]sequence_num:11
[D][05:19:23][PROT]retry_timeout:0
[D][05:19:23][PROT]retry_times:3
[D][05:19:23][PROT]send_path:0x3
[D][05:19:23][PROT]msg_type:0x5d03
[D][05:19:23][PROT]===========================================================
[W][05:19:23][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955163]
[D][05:19:23][PROT]================================

2025-07-31 23:06:44:997 ==>> ===========================
[D][05:19:23][PROT]Sending traceid[999999999990000C]
[D][05:19:23][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:23][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:23][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:23][COMM]Receive Bat Lock cmd 0
[D][05:19:23][COMM]VBUS is 1
[D][05:19:23][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:23][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:23][COMM]BAT CAN get state2 fail 204
[D][05:19:23][COMM]get bat work mode err
[W][05:19:23][PROT]remove success[1629955163],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:23][PROT]add success [1629955163],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:23][COMM]Main Task receive event:61 finished processing
[D][05:19:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_start].l:[756]

2025-07-31 23:06:45:102 ==>> .recv >
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:23][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:23][COMM]arm_hub_enable: hub power: 0
[D][05:19:23][HSDK]hexlog index save 0 3328 188 @ 0 : 0
[D][05:19:23][HSDK]write save hexlog index [0]
[D][05:19:23][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:23][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:23][COMM]read battery soc:255
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D]

2025-07-31 23:06:45:207 ==>> [05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:23][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:23][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:23][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:19:24][COMM]exit wheel stolen mode.
                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:06:45:312 ==>>                                                                                                                                                                     : 0
[D][05:19:24][HSDK]write save hexlog index [0]
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:06:45:761 ==>> [W][05:19:25][COMM]Power Off


2025-07-31 23:06:45:914 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:06:45:927 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:06:45:947 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:06:46:021 ==>> 5A A5 02 5A A5 


2025-07-31 23:06:46:111 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:06:46:195 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:06:46:203 ==>> 检测【检测小电池关机电流】
2025-07-31 23:06:46:215 ==>> 开始小电池电流采样
2025-07-31 23:06:46:241 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:46:249 ==>> [D][05:19:25][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:19:25][FCTY]get_ext_48v_vol retry i = 

2025-07-31 23:06:46:306 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:06:47:314 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:06:47:361 ==>> CurrentBattery:ƽ��:66.93

2025-07-31 23:06:47:825 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:06:47:834 ==>> 【检测小电池关机电流】通过,【66.93uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:06:48:178 ==>> MES过站成功
2025-07-31 23:06:48:186 ==>> #################### 【测试结束】 ####################
2025-07-31 23:06:48:205 ==>> 关闭5V供电
2025-07-31 23:06:48:218 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:06:48:319 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:06:49:220 ==>> 关闭5V供电成功
2025-07-31 23:06:49:234 ==>> 关闭33V供电
2025-07-31 23:06:49:249 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:06:49:313 ==>> 5A A5 02 5A A5 


2025-07-31 23:06:49:418 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:06:50:226 ==>> 关闭33V供电成功
2025-07-31 23:06:50:237 ==>> 关闭3.7V供电
2025-07-31 23:06:50:262 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:06:50:319 ==>> 6A A6 02 A6 6A 


2025-07-31 23:06:50:424 ==>> Battery OFF
OVER 150


2025-07-31 23:06:51:229 ==>> 关闭3.7V供电成功
