2025-07-31 20:02:20:773 ==>> MES查站成功:
查站序号:P510001005312CAE验证通过
2025-07-31 20:02:20:781 ==>> 扫码结果:P510001005312CAE
2025-07-31 20:02:20:783 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:02:20:784 ==>> 测试参数版本:2024.10.11
2025-07-31 20:02:20:786 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:02:20:788 ==>> 检测【打开透传】
2025-07-31 20:02:20:790 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:02:20:830 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:02:21:133 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:02:21:138 ==>> 检测【检测接地电压】
2025-07-31 20:02:21:140 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:02:21:242 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:02:21:410 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:02:21:412 ==>> 检测【打开小电池】
2025-07-31 20:02:21:414 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:02:21:542 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:02:21:681 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:02:21:685 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:02:21:689 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:02:21:740 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:02:21:954 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:02:21:956 ==>> 检测【等待设备启动】
2025-07-31 20:02:21:959 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:02:22:409 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:02:22:606 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 20:02:22:987 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:02:23:111 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:02:23:308 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:02:24:005 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:02:24:020 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:02:24:401 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:02:24:869 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:02:25:073 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:02:25:076 ==>> 检测【产品通信】
2025-07-31 20:02:25:078 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:02:25:598 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:02:25:779 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:02:26:116 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:02:26:469 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 20:02:26:855 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:02:27:163 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:02:27:363 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:02:27:408 ==>>                                                                                                  

2025-07-31 20:02:27:442 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:02:27:444 ==>> 检测【初始化完成检测】
2025-07-31 20:02:27:446 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:02:27:665 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 20:02:27:715 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:02:27:717 ==>> 检测【关闭大灯控制1】
2025-07-31 20:02:27:720 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:02:27:894 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:02:27:984 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:02:27:988 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:02:27:991 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:02:27:992 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:02:28:226 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:02:28:265 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:02:28:267 ==>> 检测【关闭仪表供电】
2025-07-31 20:02:28:269 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:02:28:561 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:02:28:794 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:02:28:796 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:02:28:798 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:02:28:994 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:02:29:078 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:02:29:080 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:02:29:081 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:02:29:221 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:02:29:360 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:02:29:363 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:02:29:365 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:29:419 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:02:29:524 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:02:29:633 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:02:29:635 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:02:29:637 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:02:29:736 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:02:29:796 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27


2025-07-31 20:02:29:901 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:02:29:905 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:02:29:907 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:02:29:909 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:02:30:036 ==>> 5A A5 03 5A A5 


2025-07-31 20:02:30:141 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:02:30:175 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:02:30:178 ==>> 该项需要延时执行
2025-07-31 20:02:30:430 ==>> [D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:02:30:956 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5010. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5010. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5011. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5011. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5011. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5012. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5013. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217

2025-07-31 20:02:30:986 ==>>  5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5014


2025-07-31 20:02:31:896 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------
[D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acc

2025-07-31 20:02:32:001 ==>> key 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],typ

2025-07-31 20:02:32:106 ==>> e[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected

2025-07-31 20:02:32:211 ==>>  or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
                                         

2025-07-31 20:02:32:436 ==>> [D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:02:32:602 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:02:33:470 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:02:33:898 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:02:34:187 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:02:34:192 ==>> 检测【33V输入电压ADC】
2025-07-31 20:02:34:195 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:34:442 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:17:57][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:57][COMM]adc read battery ts volt:2 mv
[D][05:17:57][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:02:34:487 ==>>                                                                                                  

2025-07-31 20:02:34:717 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:02:34:720 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:02:34:723 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:02:34:850 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2791mV
Get AD_V6 1989mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:02:34:989 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:34:994 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:02:35:011 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:35:013 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:02:35:015 ==>> 原始值:【2791】, 乘以分压基数【2】还原值:【5582】
2025-07-31 20:02:35:030 ==>> 【TP68_VCC5V5(ADV5)】通过,【5582mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:02:35:033 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:02:35:048 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:02:35:062 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:02:35:070 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:02:35:072 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:02:35:155 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2791mV
Get AD_V6 1987mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:02:35:344 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:35:347 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:02:35:364 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:35:366 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:02:35:370 ==>> 原始值:【2791】, 乘以分压基数【2】还原值:【5582】
2025-07-31 20:02:35:383 ==>> 【TP68_VCC5V5(ADV5)】通过,【5582mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:02:35:387 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:02:35:402 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:02:35:404 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:02:35:425 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:02:35:427 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:02:35:477 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:02:35:552 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2792mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:02:35:712 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:35:715 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:02:35:731 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:35:737 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:02:35:740 ==>> 原始值:【2792】, 乘以分压基数【2】还原值:【5584】
2025-07-31 20:02:35:749 ==>> 【TP68_VCC5V5(ADV5)】通过,【5584mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:02:35:753 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:02:35:767 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:02:35:771 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:02:35:794 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:02:35:796 ==>> 检测【打开WIFI(1)】
2025-07-31 20:02:35:799 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:02:35:836 ==>> [D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E22237 10002
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F80C71E22237->0x0008F80C71E2223F 10023
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10024


2025-07-31 20:02:35:926 ==>> [D][05:17:59][COMM]read battery soc:255
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:02:36:065 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:02:36:071 ==>> 检测【清空消息队列(1)】
2025-07-31 20:02:36:074 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:02:36:196 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:02:36:271 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:02:36:335 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:02:36:337 ==>> 检测【打开GPS(1)】
2025-07-31 20:02:36:339 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:02:36:712 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[W][05:17:59][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:17:59][COMM]Open GPS Module...
[D][05:17:59][COMM]LOC_MODEL_CONT
[D][05:17:59][GNSS]start event:8
[W][05:17:59][GNSS]start cont locating
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set ad

2025-07-31 20:02:36:772 ==>> dress
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:02:36:865 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:02:36:869 ==>> 检测【打开GSM联网】
2025-07-31 20:02:36:871 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:02:37:164 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087739241

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539103

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:02:37:390 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:02:37:393 ==>> 检测【打开仪表供电1】
2025-07-31 20:02:37:395 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:02:37:492 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:02:37:597 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[

2025-07-31 20:02:37:627 ==>> D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:02:37:660 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:02:37:663 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:02:37:665 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:02:37:837 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:02:37:913 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:02:37:932 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:02:37:934 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:02:37:938 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:02:38:138 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33502]
[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:02:38:201 ==>> 【读取主控ADC采集的仪表电压】通过,【33502mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:02:38:204 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:02:38:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:38:427 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:02:38:475 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:02:38:478 ==>> 检测【AD_V20电压】
2025-07-31 20:02:38:480 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:38:577 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:38:652 ==>> [D][05:18:01][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:02:38:712 ==>> 本次取值间隔时间:124ms
2025-07-31 20:02:38:732 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:38:833 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:38:941 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:02:39:295 ==>> 本次取值间隔时间:457ms
2025-07-31 20:02:39:313 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:39:421 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:39:575 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][COMM]13727 imu init OK
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:02:39:785 ==>> 本次取值间隔时间:350ms
2025-07-31 20:02:39:803 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:39:907 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:39:922 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:02:40:213 ==>> 本次取值间隔时间:304ms
2025-07-31 20:02:40:273 ==>> 本次取值间隔时间:51ms
2025-07-31 20:02:40:424 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

2025-07-31 20:02:40:469 ==>> 

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][COMM]Main Task receive event:4


2025-07-31 20:02:40:484 ==>> 本次取值间隔时间:206ms
2025-07-31 20:02:40:805 ==>> 本次取值间隔时间:311ms
2025-07-31 20:02:40:809 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:40:820 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  [D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05

2025-07-31 20:02:40:880 ==>> :18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:02:40:911 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:40:956 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:02:41:031 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:02:41:257 ==>> 本次取值间隔时间:345ms
2025-07-31 20:02:41:301 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:41:407 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:41:422 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:02:41:622 ==>> 本次取值间隔时间:209ms
2025-07-31 20:02:41:637 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:02:41:962 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:02:41:977 ==>> 本次取值间隔时间:353ms
2025-07-31 20:02:42:098 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:02:42:102 ==>> 本次取值间隔时间:111ms
2025-07-31 20:02:42:293 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,25,,,42,33,,,41,59,,,40,38,,,39,1*70

$GBGSV,2,2,08,39,,,38,42,,,36,13,,,38,14,,,37,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1630.642,1630.642,52.113,2097152,2097152,2097152*4B

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 20:02:42:506 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:02:42:566 ==>> 本次取值间隔时间:465ms
2025-07-31 20:02:42:570 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:02:42:673 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:02:42:703 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:02:42:733 ==>> 1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150


2025-07-31 20:02:42:869 ==>> 本次取值间隔时间:193ms
2025-07-31 20:02:42:904 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:42:907 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:02:42:911 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:02:43:039 ==>> 3A A3 02 00 A3 


2025-07-31 20:02:43:144 ==>> OFF_OUT2
OVER 150


2025-07-31 20:02:43:203 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:02:43:207 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:02:43:209 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:02:43:234 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,42,14,,,42,24,,,42,25,,,41,1*76

$GBGSV,4,2,13,59,,,41,60,,,41,3,,,41,38,,,39,1*4F

$GBGSV,4,3,13,39,,,39,42,,,39,13,,,38,16,,,38,1*7A

$GBGSV,4,4,13,40,,,39,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1668.619,1668.619,53.301,2097152,2097152,2097152*4B



2025-07-31 20:02:43:430 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:02:43:479 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:02:43:484 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:02:43:488 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:02:43:535 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:02:43:752 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:02:43:755 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:02:43:760 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:02:43:995 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:07][COMM]read battery soc:255


2025-07-31 20:02:44:029 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:02:44:035 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:02:44:040 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:02:44:297 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,24,,,42,14,,,41,25,,,41,1*70

$GBGSV,5,2,17,59,,,41,3,,,41,60,,,40,42,,,40,1*48

$GBGSV,5,3,17,1,,,40,38,,,39,39,,,39,13,,,39,1*4A

$GBGSV,5,4,17,40,,,38,16,,,38,5,,,37,2,,,36,1*74

$GBGSV,5,5,17,4,,,33,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1626.593,1626.593,51.998,2097152,2097152,2097152*43

[D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:02:44:562 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:02:44:568 ==>> 检测【AD_V21电压】
2025-07-31 20:02:44:577 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:02:44:636 ==>> 1A A1 20 00 00 
Get AD_V21 1560mV
OVER 150


2025-07-31 20:02:44:772 ==>> 本次取值间隔时间:198ms
2025-07-31 20:02:44:791 ==>> 【AD_V21电压】通过,【1560mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:44:794 ==>> 检测【关闭仪表供电2】
2025-07-31 20:02:44:796 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:02:45:036 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:02:45:069 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:02:45:073 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:02:45:075 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:02:45:293 ==>> $GBGGA,120249.066,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,24,,,42,14,,,41,25,,,41,1*74

$GBGSV,5,2,20,59,,,41,42,,,41,3,,,40,60,,,40,1*4C

$GBGSV,5,3,20,1,,,39,38,,,39,39,,,39,13,,,39,1*40

$GBGSV,5,4,20,40,,,38,16,,,38,2,,,36,5,,,35,1*72

$GBGSV,5,5,20,4,,,33,41,,,32,10,,,40,9,,,37,1*7C

$GBRMC,120249.066,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120249.066,0.000,1603.049,1603.049,51.276,2097152,2097152,2097152*5A

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:02:45:342 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:02:45:347 ==>> 检测【打开AccKey2供电】
2025-07-31 20:02:45:352 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:02:45:507 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:02:45:616 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:02:45:620 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:02:45:622 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:45:749 ==>> $GBGGA,120249.566,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,24,,,42,42,,,42,14,,,41,1*7C

$GBGSV,5,2,19,25,,,41,59,,,41,3,,,41,60,,,40,1*46

$GBGSV,5,3,19,1,,,39,38,,,39,39,,,39,13,,,39,1*4A

$GBGSV,5,4,19,40,,,38,16,,,38,2,,,36,5,,,35,1*78

$GBGSV,5,5,19,4,,,33,41,,,32,9,,,20,1*75

$GBRMC,120249.566,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120249.566,0.000,1566.796,1566.796,50.227,2097152,2097152,2097152*5A



2025-07-31 20:02:45:976 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:09][COMM]adc read out 24v adc:1307  volt:33057 mv
[D][05:18:09][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:09][COMM]adc read battery ts volt:7 mv
[D][05:18:09][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:09][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 20:02:46:164 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33057mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:02:46:167 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:02:46:170 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:02:46:298 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:02:46:448 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:02:46:452 ==>> 该项需要延时执行
2025-07-31 20:02:46:759 ==>> $GBGGA,120250.546,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,24,,,42,42,,,42,14,,,41,1*76

$GBGSV,6,2,23,25,,,41,59,,,41,60,,,41,3,,,40,1*4C

$GBGSV,6,3,23,1,,,39,38,,,39,39,,,39,13,,,39,1*40

$GBGSV,6,4,23,16,,,39,40,,,38,2,,,36,9,,,36,1*7C

$GBGSV,6,5,23,5,,,35,7,,,34,10,,,33,4,,,33,1*42

$GBGSV,6,6,23,41,,,32,6,,,29,8,,,38,1*7D

$GBRMC,120250.546,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120250.546,0.000,1566.030,1566.030,50.133,2097152,2097152,2097152*56



2025-07-31 20:02:47:736 ==>> $GBGGA,120251.526,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,42,42,,,42,14,,,41,1*71

$GBGSV,6,2,24,25,,,41,59,,,41,60,,,40,3,,,40,1*4A

$GBGSV,6,3,24,1,,,39,38,,,39,39,,,39,13,,,39,1*47

$GBGSV,6,4,24,16,,,39,40,,,38,2,,,36,9,,,36,1*7B

$GBGSV,6,5,24,26,,,36,8,,,34,5,,,34,7,,,34,1*4F

$GBGSV,6,6,24,10,,,33,4,,,33,41,,,33,6,,,31,1*74

$GBRMC,120251.526,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120251.526,0.000,1558.163,1558.163,49.868,2097152,2097152,2097152*5E



2025-07-31 20:02:47:983 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:02:48:723 ==>> $GBGGA,120252.506,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,42,42,,,42,14,,,41,1*71

$GBGSV,6,2,24,25,,,41,59,,,41,3,,,41,60,,,40,1*4B

$GBGSV,6,3,24,1,,,39,39,,,39,13,,,39,16,,,39,1*4B

$GBGSV,6,4,24,38,,,38,40,,,37,2,,,36,9,,,36,1*79

$GBGSV,6,5,24,26,,,36,8,,,35,5,,,34,7,,,34,1*4E

$GBGSV,6,6,24,10,,,33,4,,,33,41,,,33,6,,,33,1*76

$GBRMC,120252.506,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120252.506,0.000,1561.610,1561.610,49.970,2097152,2097152,2097152*57



2025-07-31 20:02:49:452 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:02:49:457 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:02:49:463 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:49:804 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:5 mv
[D][05:18:12][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
$GBGGA,120253.506,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,42,42,,,42,14,,,41,1*71

$GBGSV,6,2,24,25,,,41,59,,,41,3,,,41,60,,,41,1*4A

$GBGSV,6,3,24,39,,,39,13,,,39,38,,,39,1,,,38,1*46

$GBGSV,6,4,24,16,,,38,40,,,38,2,,,36,9,,,36,1*7A

$GBGSV,6,5,24,26,,,36,8,,,36,7,,,35,6,,,35,1*4E

$GBGSV,6,6,24,5,,,34,10,,,33,4,,,33,41,,,33,1*72

$GBRMC,120253.506,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120253.506,0.000,1570.239,1570.239,50.238,2097152,2097152,2097152*59

[D][05:18:12][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:12][COMM]arm_hub adc read 

2025-07-31 20:02:49:834 ==>> board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:02:49:989 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:02:49:994 ==>> 检测【打开AccKey1供电】
2025-07-31 20:02:49:999 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:02:50:002 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:02:50:230 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:02:50:264 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:02:50:268 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:02:50:270 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:02:50:335 ==>> 1A A1 00 40 00 
Get AD_V14 2606mV
OVER 150


2025-07-31 20:02:50:518 ==>> 原始值:【2606】, 乘以分压基数【2】还原值:【5212】
2025-07-31 20:02:50:537 ==>> 【读取AccKey1电压(ADV14)前】通过,【5212mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:02:50:540 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:02:50:544 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:50:857 ==>> $GBGGA,120254.506,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,42,42,,,41,14,,,41,1*72

$GBGSV,6,2,24,25,,,41,59,,,41,3,,,41,60,,,41,1*4A

$GBGSV,6,3,24,39,,,39,13,,,39,38,,,39,1,,,39,1*47

$GBGSV,6,4,24,16,,,39,40,,,38,2,,,36,9,,,36,1*7B

$GBGSV,6,5,24,26,,,36,8,,,36,7,,,35,6,,,35,1*4E

$GBGSV,6,6,24,5,,,35,4,,,34,10,,,33,41,,,33,1*74

$GBRMC,120254.506,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120254.506,0.000,1575.414,1575.414,50.396,2097152,2097152,2097152*5B

[W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:13][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:13][COMM]adc read battery ts volt:14 mv
[D][05:18:13][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:13][COMM

2025-07-31 20:02:50:902 ==>> ]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:02:51:072 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:02:51:075 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:02:51:080 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:02:51:237 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:02:51:345 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:02:51:349 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:02:51:352 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:02:51:433 ==>> 1A A1 00 40 00 
Get AD_V14 2606mV
OVER 150


2025-07-31 20:02:51:602 ==>> 原始值:【2606】, 乘以分压基数【2】还原值:【5212】
2025-07-31 20:02:51:621 ==>> 【读取AccKey1电压(ADV14)后】通过,【5212mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:02:51:625 ==>> 检测【打开WIFI(2)】
2025-07-31 20:02:51:629 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:02:51:724 ==>> $GBGGA,120255.506,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,42,42,,,42,14,,,41,1*71

$GBGSV,6,2,24,25,,,41,59,,,41,3,,,41,60,,,41,1*4A

$GBGSV,6,3,24,39,,,39,13,,,39,38,,,39,1,,,39,1*47

$GBGSV,6,4,24,16,,,39,40,,,38,8,,,37,2,,,36,1*7B

$GBGSV,6,5,24,9,,,36,26,,,36,6,,,36,7,,,35,1*4C

$GBGSV,6,6,24,5,,,35,4,,,34,41,,,34,10,,,33,1*73

$GBRMC,120255.506,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120255.506,0.000,1582.319,1582.319,50.612,2097152,2097152,2097152*53



2025-07-31 20:02:51:829 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK



2025-07-31 20:02:51:859 ==>> [D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:02:51:892 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:02:51:896 ==>> 检测【转刹把供电】
2025-07-31 20:02:51:899 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:02:51:994 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:02:52:099 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:02:52:174 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:02:52:179 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:02:52:182 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:02:52:282 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:52:297 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:02:52:342 ==>> 1A A1 00 80 00 
Get AD_V15 2431mV
OVER 150


2025-07-31 20:02:52:447 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 20:02:52:466 ==>> 【读取AD_V15电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:02:52:470 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:02:52:475 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:02:52:571 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:02:52:632 ==>> 1A A1 01 00 00 
Get AD_V16 2458mV
OVER 150


2025-07-31 20:02:52:722 ==>> 原始值:【2458】, 乘以分压基数【2】还原值:【4916】
2025-07-31 20:02:52:737 ==>> +WIFISCAN:4,0,CC057790A740,-72
+WIFISCAN:4,1,CC057790A6E1,-79
+WIFISCAN:4,2,CC057790A7C0,-79
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:15][CAT1]wifi scan report total[4]
[D][05:18:15][GNSS]recv submsg id[3]
[D][05:18:15][COMM]S->M yaw:INVALID
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,120256.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,14,,,41,1*71

$GBGSV,7,2,25,25,,,41,59,,,41,3,,,41,60,,,41,1*4A

$GBGSV,7,3,25,39,,,39,13,,,39,38,,,39,1,,,39,1*47

$GBGSV,7,4,25,16,,,39,40,,,38,8,,,37,9,,,37,1*71

$GBGSV,7,5,25,2,,,36,26,,,36,6,,,36,7,,,35,1*47

$GBGSV,7,6,25,5,,,35,4,,,33,41,,,33,10,,,33,1*73

$GBGSV,7,7,25,21,,,33,1*72

$GBRMC,120256.506,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120256.506,0.000,1572.105,1572.105,50.297,2097152,2097152,2097152*59



2025-07-31 20:02:52:745 ==>> 【读取AD_V16电压(前)】通过,【4916mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:02:52:748 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:02:52:751 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:53:041 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:5 mv
[D][05:18:16][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3112  volt:5470 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:02:53:289 ==>> 【转刹把供电电压(主控ADC)】通过,【5470mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:02:53:292 ==>> 检测【转刹把供电电压】
2025-07-31 20:02:53:298 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:53:547 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:8 mv
[D][05:18:16][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3106  volt:5459 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:02:53:727 ==>> $GBGGA,120257.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,42,1*7B

$GBGSV,7,2,25,14,,,41,25,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,,40,13,,,39,38,,,39,1,,,39,1*49

$GBGSV,7,4,25,16,,,39,40,,,38,8,,,37,9,,,37,1*71

$GBGSV,7,5,25,2,,,36,26,,,36,6,,,36,7,,,35,1*47

$GBGSV,7,6,25,5,,,35,4,,,33,41,,,33,10,,,33,1*73

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120257.506,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120257.506,0.000,1573.770,1573.770,50.357,2097152,2097152,2097152*55



2025-07-31 20:02:53:821 ==>> 【转刹把供电电压】通过,【5459mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:02:53:827 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:02:53:830 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:53:835 ==>> [D][05:18:17][COMM]M->S yaw:INVALID


2025-07-31 20:02:53:998 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:17][COMM]read battery soc:255


2025-07-31 20:02:54:091 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:02:54:098 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:02:54:102 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:54:196 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:54:305 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:54:413 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:54:519 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:54:579 ==>> 00 00 00 00 00 
head err!
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = ?<<<<
[D][05:18:17][COMM]S->M yaw:INVALID


2025-07-31 20:02:54:624 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:54:729 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:54:774 ==>> $GBGGA,120258.506,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,41,59,,,41,1*7B

$GBGSV,7,2,25,14,,,41,25,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,,39,13,,,39,38,,,39,1,,,39,1*47

$GBGSV,7,4,25,16,,,39,40,,,38,8,,,36,9,,,36,1*71

$GBGSV,7,5,25,2,,,36,26,,,36,6,,,36,7,,,35,1*47

$GBGSV,7,6,25,5,,,35,4,,,33,41,,,33,10,,,33,1*73

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120258.506,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120258.506,0.000,1565.475,1565.475,50.089,2097152,2097152,2097152*5A

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:02:54:834 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:54:879 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:02:54:939 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:54:945 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:02:55:044 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:02:55:105 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:02:55:135 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:02:55:170 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:02:55:174 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:02:55:178 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:02:55:271 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:02:55:301 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:02:55:331 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:02:55:394 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:02:55:399 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:02:55:404 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:02:55:544 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:02:55:668 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:02:55:672 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:02:55:678 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:02:55:754 ==>> $GBGGA,120259.506,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,42,1*7B

$GBGSV,7,2,25,14,,,41,25,,,41,3,,,41,60,,,40,1*42

$GBGSV,7,3,25,39,,,39,13,,,39,38,,,39,1,,,39,1*47

$GBGSV,7,4,25,16,,,39,40,,,38,8,,,37,2,,,37,1*7A

$GBGSV,7,5,25,9,,,36,26,,,36,6,,,36,7,,,35,1*4C

$GBGSV,7,6,25,5,,,35,41,,,34,4,,,33,10,,,33,1*74

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120259.506,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120259.506,0.000,1572.106,1572.106,50.299,2097152,2097152,2097152*58

[D][05:18:18][COMM]M->S yaw:INVALID
3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:02:55:940 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:02:55:945 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:02:55:951 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:02:56:031 ==>> [D][05:18:19][COMM]read battery soc:255
3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:02:56:217 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:02:56:223 ==>> 检测【左刹电压测试1】
2025-07-31 20:02:56:234 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:56:541 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:19][COMM]adc read right brake adc:1730  volt:2280 mv
[D][05:18:19][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:19][COMM]adc read battery ts volt:4 mv
[D][05:18:19][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:02:56:721 ==>> $GBGGA,120300.506,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,41,1*78

$GBGSV,7,2,25,14,,,41,25,,,41,3,,,41,60,,,40,1*42

$GBGSV,7,3,25,39,,,39,13,,,39,16,,,39,38,,,38,1*70

$GBGSV,7,4,25,1,,,38,40,,,38,2,,,37,8,,,36,1*4C

$GBGSV,7,5,25,9,,,36,26,,,36,6,,,36,7,,,35,1*4C

$GBGSV,7,6,25,5,,,35,41,,,34,4,,,33,10,,,33,1*74

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120300.506,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120300.506,0.000,1565.471,1565.471,50.085,2097152,2097152,2097152*5A



2025-07-31 20:02:56:756 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:02:56:760 ==>> 检测【右刹电压测试1】
2025-07-31 20:02:56:789 ==>> 【右刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 20:02:56:792 ==>> 检测【转把电压测试1】
2025-07-31 20:02:56:807 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 20:02:56:811 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:02:56:815 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:02:56:935 ==>> 3A A3 03 00 A3 


2025-07-31 20:02:57:040 ==>> OFF_OUT3
OVER 150


2025-07-31 20:02:57:088 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:02:57:092 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:02:57:095 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:02:57:130 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:02:57:362 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:02:57:366 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:02:57:373 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:02:57:436 ==>> 3A A3 05 00 A3 


2025-07-31 20:02:57:541 ==>> OFF_OUT5
OVER 150


2025-07-31 20:02:57:634 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:02:57:638 ==>> 检测【左刹电压测试2】
2025-07-31 20:02:57:643 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:02:57:752 ==>> $GBGGA,120301.506,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,41,1*78

$GBGSV,7,2,25,14,,,41,25,,,41,3,,,41,60,,,40,1*42

$GBGSV,7,3,25,39,,,39,13,,,39,16,,,39,38,,,39,1*71

$GBGSV,7,4,25,1,,,39,40,,,38,2,,,37,8,,,37,1*4C

$GBGSV,7,5,25,9,,,36,26,,,36,6,,,36,7,,,35,1*4C

$GBGSV,7,6,25,5,,,35,41,,,34,4,,,33,10,,,33,1*74

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120301.506,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120301.506,0.000,783.758,783.758,716.763,2097152,2097152,2097152*61



2025-07-31 20:02:57:856 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_AL

2025-07-31 20:02:57:946 ==>> L<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:21][COMM]adc read battery ts volt:0 mv
[D][05:18:21][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:02:58:021 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:02:58:160 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:02:58:166 ==>> 检测【右刹电压测试2】
2025-07-31 20:02:58:179 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:02:58:185 ==>> 检测【转把电压测试2】
2025-07-31 20:02:58:198 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:02:58:204 ==>> 检测【晶振检测】
2025-07-31 20:02:58:223 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:02:58:421 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 20:02:58:470 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:02:58:474 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:02:58:480 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:02:58:526 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1656mV
Get AD_V4 1652mV
Get AD_V5 2793mV
Get AD_V6 1992mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:02:58:738 ==>> $GBGGA,120302.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,1,,,39,39,,,39,1*44

$GBGSV,7,4,26,16,,,39,40,,,38,2,,,37,8,,,37,1*79

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,35,4,,,34,41,,,34,10,,,33,1*70

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120302.506,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120302.506,0.000,785.411,785.411,718.275,2097152,2097152,2097152*6E



2025-07-31 20:02:58:742 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:02:58:746 ==>> 检测【检测BootVer】
2025-07-31 20:02:58:750 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:02:59:120 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071539103
[D][05:18:22][FCTY]HardwareID  = 867222087739241
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:22][FCTY]Bat         = 3924 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1281
[D][05:18:22][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 0 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D]

2025-07-31 20:02:59:165 ==>> [05:18:22][FCTY]Bat1         = 3723 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:02:59:288 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:02:59:293 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:02:59:299 ==>> 检测【检测固件版本】
2025-07-31 20:02:59:310 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:02:59:315 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:02:59:319 ==>> 检测【检测蓝牙版本】
2025-07-31 20:02:59:330 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:02:59:337 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:02:59:365 ==>> 检测【检测MoBikeId】
2025-07-31 20:02:59:393 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:02:59:396 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:02:59:400 ==>> 检测【检测蓝牙地址】
2025-07-31 20:02:59:404 ==>> 取到目标值:FBC92982C45C
2025-07-31 20:02:59:408 ==>> 【检测蓝牙地址】通过,【FBC92982C45C】符合目标值【】要求!
2025-07-31 20:02:59:412 ==>> 提取到蓝牙地址:FBC92982C45C
2025-07-31 20:02:59:415 ==>> 检测【BOARD_ID】
2025-07-31 20:02:59:421 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:02:59:441 ==>> 检测【检测充电电压】
2025-07-31 20:02:59:445 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:02:59:449 ==>> 检测【检测VBUS电压1】
2025-07-31 20:02:59:476 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:02:59:480 ==>> 检测【检测充电电流】
2025-07-31 20:02:59:497 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:02:59:501 ==>> 检测【检测IMEI】
2025-07-31 20:02:59:505 ==>> 取到目标值:867222087739241
2025-07-31 20:02:59:515 ==>> 【检测IMEI】通过,【867222087739241】符合目标值【】要求!
2025-07-31 20:02:59:520 ==>> 提取到IMEI:867222087739241
2025-07-31 20:02:59:524 ==>> 检测【检测IMSI】
2025-07-31 20:02:59:528 ==>> 取到目标值:460130071539103
2025-07-31 20:02:59:543 ==>> 【检测IMSI】通过,【460130071539103】符合目标值【】要求!
2025-07-31 20:02:59:547 ==>> 提取到IMSI:460130071539103
2025-07-31 20:02:59:552 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:02:59:580 ==>> 取到目标值:460130071539103
2025-07-31 20:02:59:584 ==>> 【校验网络运营商(移动)】通过,【460130071539103】符合目标值【】要求!
2025-07-31 20:02:59:588 ==>> 检测【打开CAN通信】
2025-07-31 20:02:59:592 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:02:59:641 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:02:59:747 ==>> $GBGGA,120303.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,42,,,42,3,,,41,1*45

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,1,,,39,39,,,39,1*44

$GBGSV,7,4,26,16,,,39,40,,,38,2,,,37,8,,,37,1*79

$GBGSV,7,5,26,9,,,37,26,,,36,6,,,36,7,,,35,1*4E

$GBGSV,7,6,26,5,,,34,41,,,34,10,,,33,4,,,33,1*76

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120303.506,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120303.506,0.000,785.418,785.418,718.283,2097152,2097152,2097152*66



2025-07-31 20:02:59:856 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:02:59:860 ==>> 检测【检测CAN通信】
2025-07-31 20:02:59:866 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:02:59:943 ==>> can send success


2025-07-31 20:02:59:973 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:03:00:033 ==>> [D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34249
[D][05:18:23][COMM]read battery soc:255
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:03:00:093 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:03:00:127 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:03:00:132 ==>> 检测【关闭CAN通信】
2025-07-31 20:03:00:139 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:03:00:153 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:03:00:245 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:03:00:401 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:03:00:406 ==>> 检测【打印IMU STATE】
2025-07-31 20:03:00:409 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:03:00:765 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,120304.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,1,,,39,39,,,39,1*44

$GBGSV,7,4,26,16,,,39,40,,,38,8,,,37,2,,,36,1*78

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,34,41,,,34,10,,,33,4,,,33,1*76

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120304.506,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120304.506,0.000,782.935,782.935,716.011,2097152,2097152,2097152*66



2025-07-31 20:03:00:945 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:03:00:950 ==>> 检测【六轴自检】
2025-07-31 20:03:00:956 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:03:01:134 ==>> [D][05:18:24][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:03:01:747 ==>> $GBGGA,120305.506,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,39,,,39,16,,,39,1*72

$GBGSV,7,4,26,40,,,38,1,,,38,2,,,37,8,,,37,1*4E

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,34,4,,,34,41,,,34,10,,,33,1*71

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120305.506,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120305.506,0.000,783.758,783.758,716.764,2097152,2097152,2097152*62



2025-07-31 20:03:02:038 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:03:02:763 ==>> $GBGGA,120306.506,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,33,,,42,60,,,41,59,,,41,1*78

$GBGSV,7,2,26,42,,,41,14,,,41,25,,,41,3,,,40,1*41

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,16,,,39,1*48

$GBGSV,7,4,26,38,,,38,40,,,38,8,,,37,2,,,36,1*75

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,34,4,,,34,41,,,34,10,,,33,1*71

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120306.506,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120306.506,0.000,781.272,781.272,714.490,2097152,2097152,2097152*6B



2025-07-31 20:03:02:838 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:03:02:942 ==>> [D][05:18:26][COMM]Main Task receive event:142
[D][05:18:26][COMM]###### 37177 imu self test OK ######
[D][05:18:26][COMM]im

2025-07-31 20:03:02:973 ==>> u selftest. GYRO:[0,0,0] ACCEL:[-10,-4,4049]
[D][05:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 20:03:03:051 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:03:03:055 ==>> 检测【打印IMU STATE2】
2025-07-31 20:03:03:062 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:03:03:233 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:03:03:326 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:03:03:333 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:03:03:341 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:03:03:444 ==>> [D][05:18:26][COMM]S->M yaw:INVALID
5A A5 02 5A A5 


2025-07-31 20:03:03:535 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:03:03:598 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:03:03:606 ==>> 检测【检测VBUS电压2】
2025-07-31 20:03:03:609 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:03:776 ==>> [D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 3,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 16
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 16
$GBGGA,120307.506,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,33,,,42,3,,,41,60,,,41,1*47

$GBGSV,7,2,26,59,,,41,42,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,16,,,39,1*48

$GBGSV,7,4,26,38,,,38,40,,,38,8,,,37,2,,,36,1*75

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,35,4,,,34,41,,,34,10,,,33,1*70

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120307.506,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120307.506,0.000,782.927,782.927,716.003,2097152,2097152,2097152*66



2025-07-31 20:03:04:080 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539103
[D][05:18:27][FCTY]HardwareID  = 867222087739241
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:27][FCTY]Bat         = 3924 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 324
[D][05:18:27][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b

2025-07-31 20:03:04:125 ==>> 1
[D][05:18:27][FCTY]Bat1         = 3723 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:03:04:134 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:04:500 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539103
[D][05:18:27][FCTY]HardwareID  = 867222087739241
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:27][FCTY]Bat         = 3924 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 180
[D][05:18:27][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1


2025-07-31 20:03:04:560 ==>> 
[D][05:18:27][FCTY]Bat1         = 3723 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                     

2025-07-31 20:03:04:653 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:04:740 ==>> $GBGGA,120308.506,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,16,,,39,1*48

$GBGSV,7,4,26,38,,,38,40,,,38,2,,,36,8,,,36,1*74

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,35,4,,,34,41,,,34,10,,,33,1*70

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120308.506,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120308.506,0.000,782.930,782.930,716.007,2097152,2097152,2097152*6D



2025-07-31 20:03:05:024 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539103
[D][05:18:28][FCTY]HardwareID  = 867222087739241
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 50 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 106
[D][05:18:28][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3723 mv
[D][05:18:

2025-07-31 20:03:05:084 ==>> 28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                          

2025-07-31 20:03:05:178 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:05:525 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539103
[D][05:18:28][FCTY]HardwareID  = 867222087739241
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 50 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:28][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3723 mv
[D][05:18:28][FCTY]=

2025-07-31 20:03:05:555 ==>> =================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:05:709 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:06:124 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              [0],index[0],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]index:0
[D][05:18:28][PROT]is_send:1
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg

2025-07-31 20:03:06:229 ==>> _type:0x5d03
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900005]
[D][05:18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]

2025-07-31 20:03:06:334 ==>> sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[198]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][CAT1]gsm read msg sub id: 15
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B578F9AFD83BADC331B490D9C7ACD0151BB12A7E2E9B660935574EBDC41123D32216C09C9CF96BFC97F04F4941B2F278330CEAE67C020BAF84200A0E3C164B9BDB9081CDEF51F35BD7EE787B516D0876762640
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,120309.506,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,59,,,41,14,,,41,25,,,41,1*7F

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,16,,,39,1*48

$GBGS

2025-07-31 20:03:06:439 ==>> V,7,4,26,38,,,38,40,,,38,8,,,37,2,,,36,1*75

$GBGSV,7,5,26,26,,,36,9,,,36,6,,,36,7,,,35,1*4F

$GBGSV,7,6,26,5,,,35,4,,,34,41,,,34,10,,,33,1*70

$GBGSV,7,7,26,21,,,32,23,,,,1*71

$GBRMC,120309.506,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120309.506,0.000,783.757,783.757,716.763,2097152,2097152,2097152*69

[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539103
[D][05:18:29][FCTY]HardwareID  = 867222087739241
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:29][FCTY]Bat         = 3484 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05

2025-07-31 20:03:06:529 ==>> :18:29][FCTY]VBUS        = 5200 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 93
[D][05:18:29][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3723 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:06:760 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:07:125 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539103
[D][05:18:30][FCTY]HardwareID  = 867222087739241
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:30][FCTY]Bat         = 3444 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5200 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 88
[D][05:18:30][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION

2025-07-31 20:03:07:170 ==>>  = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3723 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:07:294 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:07:700 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539103
[D][05:18:30][FCTY]HardwareID  = 867222087739241
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:30][FCTY]Bat         = 3444 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5200 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 91
[D][05:18:30][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:

2025-07-31 20:03:07:730 ==>> 18:30][FCTY]Bat1         = 3723 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:07:824 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:08:199 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539103
[D][05:18:31][FCTY]HardwareID  = 867222087739241
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:31][FCTY]Bat         = 3744 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 5200 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 92
[D][05:18:31][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V

2025-07-31 20:03:08:244 ==>> 3465b5b1
[D][05:18:31][FCTY]Bat1         = 3723 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:08:353 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:08:713 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539103
[D][05:18:31][FCTY]HardwareID  = 867222087739241
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:31][FCTY]Bat         = 3744 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 5200 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 89
[D][05:18:31][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY

2025-07-31 20:03:08:758 ==>> ]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3723 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:08:881 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:03:09:216 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071539103
[D][05:18:32][FCTY]HardwareID  = 867222087739241
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = FBC92982C45C
[D][05:18:32][FCTY]Bat         = 3744 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 5200 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 2, adc = 88
[D][05:18:32][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05

2025-07-31 20:03:09:261 ==>> :18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3723 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:03:09:415 ==>> 【检测VBUS电压2】失败,【5200mV】与目标值【4400mV】至【5000mV】不匹配!
2025-07-31 20:03:09:421 ==>> #################### 【测试结束】 ####################
2025-07-31 20:03:09:440 ==>> 关闭5V供电
2025-07-31 20:03:09:448 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:03:09:531 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:03:09:636 ==>> [D][05:18:32][COMM]43856 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:10:449 ==>> 关闭5V供电成功
2025-07-31 20:03:10:458 ==>> 关闭33V供电
2025-07-31 20:03:10:479 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:03:10:541 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:03:10:646 ==>> [D][05:18:33][COMM]44867 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:10:978 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:0 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:4
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:2
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900005]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 20:03:11:462 ==>> 关闭33V供电成功
2025-07-31 20:03:11:470 ==>> 关闭3.7V供电
2025-07-31 20:03:11:480 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:03:11:539 ==>> 6A A6 02 A6 6A 


2025-07-31 20:03:11:629 ==>> Battery OFF
OVER 150


2025-07-31 20:03:11:660 ==>> [D][05:18:34][COMM]45878 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:12:004 ==>>  

2025-07-31 20:03:12:411 ==>>  

