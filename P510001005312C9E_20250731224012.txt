2025-07-31 22:40:12:836 ==>> MES查站成功:
查站序号:P510001005312C9E验证通过
2025-07-31 22:40:12:843 ==>> 扫码结果:P510001005312C9E
2025-07-31 22:40:12:845 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:40:12:847 ==>> 测试参数版本:2024.10.11
2025-07-31 22:40:12:849 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:40:12:851 ==>> 检测【打开透传】
2025-07-31 22:40:12:853 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:40:12:905 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:40:13:220 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:40:13:225 ==>> 检测【检测接地电压】
2025-07-31 22:40:13:227 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:40:13:300 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 22:40:13:498 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:40:13:503 ==>> 检测【打开小电池】
2025-07-31 22:40:13:531 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:40:13:606 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:40:13:775 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:40:13:778 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:40:13:780 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:40:13:906 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:40:14:044 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:40:14:046 ==>> 检测【等待设备启动】
2025-07-31 22:40:14:048 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:40:14:377 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:40:14:573 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:40:15:083 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:40:15:221 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:40:15:266 ==>>                                                    

2025-07-31 22:40:15:661 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:40:16:112 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:40:16:142 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:40:16:387 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:40:16:389 ==>> 检测【产品通信】
2025-07-31 22:40:16:392 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:40:16:593 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 22:40:16:656 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:40:16:658 ==>> 检测【初始化完成检测】
2025-07-31 22:40:16:661 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:40:16:836 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:40:16:929 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:40:16:932 ==>> 检测【关闭大灯控制1】
2025-07-31 22:40:16:934 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:40:17:076 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:40:17:182 ==>> [D][05:17:51][COMM]2624 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:17:199 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:40:17:201 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:40:17:203 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:40:17:286 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51

2025-07-31 22:40:17:346 ==>> ][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:40:17:437 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:40:17:471 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:40:17:473 ==>> 检测【关闭仪表供电】
2025-07-31 22:40:17:475 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:40:17:698 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:40:17:745 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:40:17:748 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:40:17:749 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:40:17:861 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:40:18:021 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:40:18:023 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:40:18:025 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:40:18:213 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3635 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:18:291 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:40:18:293 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:40:18:294 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:40:18:471 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:40:18:563 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:40:18:565 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:40:18:591 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:40:18:701 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:40:18:790 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 10
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:40:18:838 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:40:18:841 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:40:18:843 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:40:18:895 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:40:19:130 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:40:19:133 ==>> 该项需要延时执行
2025-07-31 22:40:19:236 ==>> [D][05:17:53][COMM]4646 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:19:772 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5011. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5012. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5013. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5014. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5015. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5016
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B98

2025-07-31 22:40:19:802 ==>> 7FE 5017


2025-07-31 22:40:20:232 ==>> [D][05:17:54][COMM]5657 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:20:337 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1---

2025-07-31 22:40:20:366 ==>> ---------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:40:20:874 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail


2025-07-31 22:40:20:979 ==>> 
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:

2025-07-31 22:40:21:085 ==>> 0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][P

2025-07-31 22:40:21:145 ==>> ROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 22:40:21:251 ==>> [D][05:17:55][COMM]6668 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:21:311 ==>>                                        


2025-07-31 22:40:22:259 ==>> [D][05:17:56][COMM]7679 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:22:814 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:40:23:137 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:40:23:140 ==>> 检测【33V输入电压ADC】
2025-07-31 22:40:23:142 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:23:428 ==>> [D][05:17:57][COMM]8691 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:17:57][COMM]adc read out 24v adc:1323  volt:33462 mv
[D][05:17:57][COMM]adc read left brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read right brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:57][COMM]adc read battery ts volt:11 mv
[D][05:17:57][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:40:23:676 ==>> 【33V输入电压ADC】通过,【32830mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:40:23:690 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:40:23:692 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:40:23:812 ==>> 1A A1 00 00 FC 
Get AD_V2 1648mV
Get AD_V3 1643mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1996mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:40:23:950 ==>> 【TP7_VCC3V3(ADV2)】通过,【1648mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:23:953 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:40:23:969 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:23:971 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:40:23:973 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 22:40:23:987 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:40:23:989 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:40:24:005 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:40:24:008 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:40:24:029 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:40:24:032 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:40:24:119 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2770mV
Get AD_V6 1995mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:40:24:270 ==>> [D][05:17:58][COMM]9701 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:40:24:308 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:24:310 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:40:24:327 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:24:329 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:40:24:331 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 22:40:24:346 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:40:24:363 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:40:24:381 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:40:24:384 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:40:24:402 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:40:24:404 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:40:24:515 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1642mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 1994mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:40:24:621 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10005


2025-07-31 22:40:24:698 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:24:701 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:40:24:721 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:24:724 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:40:24:726 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 22:40:24:750 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:40:24:752 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:40:24:779 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:40:24:782 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:40:24:804 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:40:24:815 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:40:24:818 ==>> 检测【打开WIFI(1)】
2025-07-31 22:40:24:835 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:40:25:000 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:40:25:098 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:40:25:100 ==>> 检测【清空消息队列(1)】
2025-07-31 22:40:25:102 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:40:25:535 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10712 imu init OK
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:1

2025-07-31 22:40:25:580 ==>> 7:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:40:25:628 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:40:25:631 ==>> 检测【打开GPS(1)】
2025-07-31 22:40:25:633 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:40:25:962 ==>>                                                                                                                                                                                                                                                                                             1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087518488

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

2025-07-31 22:40:25:993 ==>> 

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:40:26:163 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:40:26:166 ==>> 检测【打开GSM联网】
2025-07-31 22:40:26:168 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:40:26:284 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:40:26:390 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:40:26:438 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:40:26:441 ==>> 检测【打开仪表供电1】
2025-07-31 22:40:26:443 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:40:26:603 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:40:26:712 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:40:26:714 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:40:26:716 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:40:26:802 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:40:26:907 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1

2025-07-31 22:40:26:938 ==>> 
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:40:26:988 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:40:26:991 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:40:26:993 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:40:27:196 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33549]


2025-07-31 22:40:27:261 ==>> 【读取主控ADC采集的仪表电压】通过,【33549mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:40:27:267 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:40:27:271 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:40:27:504 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:40:27:538 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:40:27:541 ==>> 检测【AD_V20电压】
2025-07-31 22:40:27:543 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:40:27:639 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:40:27:716 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:40:27:946 ==>> 本次取值间隔时间:300ms
2025-07-31 22:40:27:964 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:40:27:991 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:40:28:067 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:40:28:328 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13725 imu init OK


2025-07-31 22:40:28:388 ==>>                               VALID


2025-07-31 22:40:28:449 ==>> 本次取值间隔时间:377ms
2025-07-31 22:40:28:787 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 22:40:28:848 ==>> 本次取值间隔时间:397ms
2025-07-31 22:40:28:999 ==>> 本次取值间隔时间:149ms
2025-07-31 22:40:29:044 ==>>                                                                                                                                       st[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F383A363F333F3F07

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][C

2025-07-31 22:40:29:089 ==>> AT1]<<< 
+CSQ: 21,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.127.128.247"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:40:29:335 ==>> 本次取值间隔时间:333ms
2025-07-31 22:40:29:339 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:40:29:445 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:40:29:566 ==>> [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:03][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get A

2025-07-31 22:40:29:596 ==>> 本次取值间隔时间:142ms
2025-07-31 22:40:29:599 ==>> D_V20 1634mV
OVER 150


2025-07-31 22:40:29:701 ==>> 本次取值间隔时间:91ms
2025-07-31 22:40:29:719 ==>> 【AD_V20电压】通过,【1634mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:29:722 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:40:29:724 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:40:29:809 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:40:29:999 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:40:30:002 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:40:30:005 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:40:30:286 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:04][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:04][COMM]oneline display read state:255
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:40:30:870 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:05][

2025-07-31 22:40:30:888 ==>> COMM]read battery soc:255


2025-07-31 22:40:31:038 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:40:31:083 ==>>                                $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,40,,,42,39,,,40,25,,,38,33,,,38,1*76

$GBGSV,3,2,09,34,,,37,59,,,44,6,,,39,7,,,39,1*70

$GBGSV,3,3,09,16,,,37,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1616.815,1616.815,51.663,2097152,2097152,2097152*48

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 22:40:31:310 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:05][COMM]oneline display read state:255
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:40:32:038 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,40,,,41,39,,,40,59,,,39,25,,,39,1*73

$GBGSV,4,2,15,34,,,39,60,,,39,7,,,38,33,,,38,1*42

$GBGSV,4,3,15,43,,,38,16,,,37,6,,,36,2,,,35,1*7D

$GBGSV,4,4,15,5,,,34,44,,,32,41,,,40,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1554.661,1554.661,49.710,2097152,2097152,2097152*44



2025-07-31 22:40:32:083 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:40:32:295 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:40:32:358 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:40:32:361 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:40:32:363 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:40:32:400 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:40:32:633 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:40:32:638 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:40:32:647 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:40:32:816 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:40:32:891 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 22:40:32:909 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:40:32:912 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:40:32:914 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:40:32:996 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,3,,,41,39,,,40,41,,,40,1*4B

$GBGSV,5,2,20,59,,,40,25,,,40,34,,,40,60,,,40,1*79



2025-07-31 22:40:33:041 ==>> 
$GBGSV,5,3,20,7,,,39,33,,,38,43,,,38,16,,,38,1*44

$GBGSV,5,4,20,1,,,38,6,,,36,2,,,35,5,,,34,1*7A

$GBGSV,5,5,20,44,,,33,4,,,33,23,,,33,38,,,30,1*49

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1550.558,1550.558,49.621,2097152,2097152,2097152*47



2025-07-31 22:40:33:147 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:40:33:180 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:40:33:183 ==>> 检测【AD_V21电压】
2025-07-31 22:40:33:185 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:40:33:311 ==>> 1A A1 20 00 00 
Get AD_V21 1631mV
OVER 150


2025-07-31 22:40:33:587 ==>> 本次取值间隔时间:393ms
2025-07-31 22:40:33:605 ==>> 【AD_V21电压】通过,【1631mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:33:610 ==>> 检测【关闭仪表供电2】
2025-07-31 22:40:33:613 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:40:33:821 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:40:33:884 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:40:33:887 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:40:33:889 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:40:34:141 ==>> $GBGGA,144037.896,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,3,,,40,39,,,40,41,,,40,1*49

$GBGSV,6,2,23,59,,,40,25,,,40,34,,,40,60,,,40,1*79

$GBGSV,6,3,23,7,,,39,33,,,38,43,,,38,16,,,38,1*44

$GBGSV,6,4,23,1,,,37,12,,,37,6,,,36,24,,,36,1*77

$GBGSV,6,5,23,2,,,35,5,,,34,23,,,34,44,,,33,1*74

$GBGSV,6,6,23,4,,,33,38,,,30,10,,,19,1*42

$GBRMC,144037.896,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144037.896,0.000,1510.617,1510.617,48.417,2097152,2097152,2097152*5D

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:40:34:420 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:40:34:423 ==>> 检测【打开AccKey2供电】
2025-07-31 22:40:34:425 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:40:34:573 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:40:34:696 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:40:34:699 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:40:34:710 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:34:769 ==>> $GBGGA,144038.596,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,3,,,40,39,,,40,1*45

$GBGSV,6,2,24,41,,,40,25,,,40,34,,,40,60,,,40,1*77

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,37,24,,,37,10,,,37,12,,,36,1*46

$GBGSV,6,5,24,6,,,36,23,,,35,2,,,34,5,,,34,1*40

$GBGSV,6,6,24,44,,,33,4,,,33,38,,,30,9,,,29,1*7E

$GBRMC,144038.596,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144038.596,0.000,1532.255,1532.255,49.042,2097152,2097152,2097152*5A



2025-07-31 22:40:35:027 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:09][COMM]adc read out 24v adc:1324  volt:33487 mv
[D][05:18:09][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:09][COMM]adc read battery ts volt:5 mv
[D][05:18:09][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:09][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:09][COMM]read battery soc:255
[D][05:18:09][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:40:35:228 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33487mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:40:35:232 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:40:35:235 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:40:35:365 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:40:35:511 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:40:35:514 ==>> 该项需要延时执行
2025-07-31 22:40:35:750 ==>> $GBGGA,144039.576,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,3,,,40,39,,,40,1*45

$GBGSV,6,2,24,41,,,40,25,,,40,34,,,40,60,,,40,1*77

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,38,24,,,37,10,,,37,12,,,36,1*49

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,5,,,34,1*42

$GBGSV,6,6,24,44,,,34,4,,,33,9,,,33,38,,,30,1*72

$GBRMC,144039.576,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144039.576,0.000,1546.052,1546.052,49.462,2097152,2097152,2097152*53



2025-07-31 22:40:36:726 ==>> $GBGGA,144040.556,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,39,,,41,3,,,40,1*44

$GBGSV,6,2,24,41,,,40,25,,,40,34,,,40,60,,,40,1*77

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,1,,,38,24,,,38,10,,,36,12,,,36,1*47

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,5,,,34,1*42

$GBGSV,6,6,24,44,,,34,9,,,34,4,,,33,38,,,30,1*75

$GBRMC,144040.556,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144040.556,0.000,1549.507,1549.507,49.572,2097152,2097152,2097152*5F



2025-07-31 22:40:36:892 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:40:37:718 ==>> $GBGGA,144041.536,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,39,,,41,41,,,41,1*73

$GBGSV,6,2,24,3,,,40,25,,,40,34,,,40,60,,,40,1*41

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,24,,,38,1,,,37,10,,,36,12,,,36,1*48

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,29,1*71

$GBRMC,144041.536,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144041.536,0.000,1546.063,1546.063,49.473,2097152,2097152,2097152*58



2025-07-31 22:40:38:523 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:40:38:528 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:40:38:532 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:38:830 ==>> $GBGGA,144042.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,3,,,41,39,,,40,1*44

$GBGSV,6,2,24,41,,,40,25,,,40,34,,,40,60,,,40,1*77

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,24,,,38,1,,,37,10,,,36,12,,,36,1*48

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,29,1*71

$GBRMC,144042.516,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144042.516,0.000,1544.333,1544.333,49.415,2097152,2097152,2097152*59

[W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:13][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:13][COMM]adc read battery ts volt:4 mv
[D][05:18:13][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1448  volt:

2025-07-31 22:40:38:860 ==>> 33572 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:40:38:905 ==>>                                          

2025-07-31 22:40:39:060 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:40:39:065 ==>> 检测【打开AccKey1供电】
2025-07-31 22:40:39:069 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:40:39:290 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:40:39:340 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:40:39:344 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:40:39:348 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:40:39:396 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 22:40:39:595 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 22:40:39:613 ==>> 【读取AccKey1电压(ADV14)前】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:40:39:616 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:40:39:622 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:39:686 ==>> $GBGGA,144043.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,41,,,41,34,,,41,1*7E

$GBGSV,6,2,24,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,24,,,38,1,,,38,10,,,37,12,,,36,1*46

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,29,1*71

$GBRMC,144043.516,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144043.516,0.000,1549.517,1549.517,49.582,2097152,2097152,2097152*57



2025-07-31 22:40:39:913 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:7 mv
[D][05:18:14][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:40:40:190 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:40:40:194 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:40:40:197 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:40:40:377 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:40:40:471 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:40:40:475 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:40:40:477 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:40:40:606 ==>> 1A A1 00 40 00 
Get AD_V14 2670mV
OVER 150


2025-07-31 22:40:40:697 ==>> $GBGGA,144044.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,41,,,40,34,,,40,1*7E

$GBGSV,6,2,24,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,6,3,24,7,,,39,33,,,38,43,,,38,16,,,38,1*43

$GBGSV,6,4,24,24,,,38,1,,,37,10,,,37,12,,,36,1*49

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,30,1*79

$GBRMC,144044.516,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144044.516,0.000,1546.053,1546.053,49.462,2097152,2097152,2097152*5F



2025-07-31 22:40:40:729 ==>> 原始值:【2670】, 乘以分压基数【2】还原值:【5340】
2025-07-31 22:40:40:745 ==>> 【读取AccKey1电压(ADV14)后】通过,【5340mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:40:40:749 ==>> 检测【打开WIFI(2)】
2025-07-31 22:40:40:776 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:40:40:926 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6
[D][05:18:15][COMM]read battery soc:255


2025-07-31 22:40:41:023 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:40:41:027 ==>> 检测【转刹把供电】
2025-07-31 22:40:41:029 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:40:41:200 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:40:41:299 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:40:41:303 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:40:41:306 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:40:41:400 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:40:41:476 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:40:41:506 ==>> 1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 22:40:41:552 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 22:40:41:573 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:40:41:578 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:40:41:581 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:40:41:687 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:40:41:733 ==>> $GBGGA,144045.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,41,,,40,34,,,40,1*7E

$GBGSV,6,2,24,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,6,3,24,7,,,39,33,,,38,16,,,38,24,,,38,1*42

$GBGSV,6,4,24,43,,,37,1,,,37,10,,,36,12,,,36,1*46

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,30,1*79

$GBRMC,144045.516,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144045.516,0.000,1542.598,1542.598,49.353,2097152,2097152,2097152*5B

+WIFISCAN:4,0,CC057790A641,-73
+WIFISCAN:4,1,CC057790A7C0,-74
+WIFISCAN:4,2,CC057790A640,-74
+WIFISCAN:4,3,CC057790A7C1,-75

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 22:40:41:868 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:40:42:313 ==>> [D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 22:40:42:604 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:40:42:696 ==>> $GBGGA,144046.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,59,,,41,41,,,40,34,,,40,1*7E

$GBGSV,6,2,24,3,,,40,39,,,40,25,,,40,60,,,39,1*42

$GBGSV,6,3,24,7,,,39,16,,,38,24,,,38,43,,,38,1*45

$GBGSV,6,4,24,33,,,37,1,,,37,10,,,36,12,,,36,1*41

$GBGSV,6,5,24,6,,,36,23,,,36,2,,,35,9,,,35,1*4F

$GBGSV,6,6,24,5,,,33,44,,,33,4,,,33,38,,,29,1*71

$GBRMC,144046.516,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144046.516,0.000,1539.147,1539.147,49.246,2097152,2097152,2097152*5D



2025-07-31 22:40:42:711 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:40:42:772 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:40:42:802 ==>> 1A A1 01 00 00 
Get AD_V16 2440mV
OVER 150


2025-07-31 22:40:42:862 ==>> 原始值:【2440】, 乘以分压基数【2】还原值:【4880】
2025-07-31 22:40:42:907 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:40:43:698 ==>> $GBGGA,144047.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,40,41,,,40,34,,,40,1*7F

$GBGSV,7,2,25,3,,,40,39,,,40,25,,,40,60,,,39,1*42

$GBGSV,7,3,25,7,,,39,16,,,38,24,,,38,43,,,38,1*45

$GBGSV,7,4,25,33,,,38,1,,,37,11,,,37,10,,,36,1*4C

$GBGSV,7,5,25,12,,,36,6,,,36,23,,,36,2,,,35,1*76

$GBGSV,7,6,25,9,,,35,5,,,33,44,,,33,4,,,33,1*4E

$GBGSV,7,7,25,38,,,30,1*79

$GBRMC,144047.516,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144047.516,0.000,1540.588,1540.588,49.282,2097152,2097152,2097152*54



2025-07-31 22:40:43:842 ==>> 【读取AD_V16电压(前)】通过,【4880mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:40:43:847 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:40:43:851 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:44:111 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:18][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:18][COMM]adc read battery ts volt:4 mv
[D][05:18:18][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3111  volt:5468 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:40:44:374 ==>> 【转刹把供电电压(主控ADC)】通过,【5468mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:40:44:381 ==>> 检测【转刹把供电电压】
2025-07-31 22:40:44:403 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:44:772 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:19][COMM]adc read battery ts volt:6 mv
[D][05:18:19][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3111  volt:5468 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
$GBGGA,144048.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,41,,,41,59,,,40,34,,,40,1*7E

$GBGSV,7,2,25,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,7,3,25,7,,,39,16,,,38,24,,,38,43,,,38,1*45

$GBGSV,7,4,25,33,,,38,11,,,38,1,,,37,10,,,36,1*43

$GBGSV,7,5,25,12,,,36,6,,,36,23,,,36,2,,,35,1*76

$GBGSV,7,6,25,9,,,35,4,,,34,5,,,33,44,,,33,1*49

$GBGSV,7,7,25,38,,,30,1*79

$GBRMC,144048.516,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144048.516,0.000,1547.222,1547.222,49.495,2097152,2097152,2097152*5B

[D][05:18:19][COMM]arm_hub adc read led yb 

2025-07-31 22:40:44:802 ==>> adc:1448  volt:33572 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:40:44:908 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:40:44:911 ==>> 【转刹把供电电压】通过,【5468mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:40:44:915 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:40:44:918 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:40:45:091 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:40:45:184 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:40:45:190 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:40:45:212 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:40:45:290 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:40:45:397 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:40:45:412 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:40:45:503 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:40:45:610 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:40:45:630 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:40:45:635 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:40:45:639 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:40:45:701 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,144049.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,41,,,40,34,,,40,1*7E

$GBGSV,7,2,25,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,7,3,25,7,,,39,16,,,38,24,,,38,43,,,38,1*45

$GBGSV,7,4,25,33,,,38,11,,,38,1,,,37,10,,,36,1*43

$GBGSV,7,5,25,12,,,36,6,,,36,23,,,36,2,,,35,1*76

$GBGSV,7,6,25,9,,,35,44,,,34,4,,,33,5,,,33,1*49

$GBGSV,7,7,25,38,,,30,1*79

$GBRMC,144049.516,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144049.516,0.000,1547.222,1547.222,49.495,2097152,2097152,2097152*5A



2025-07-31 22:40:45:746 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:40:45:806 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:40:45:870 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:40:45:874 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:40:45:877 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:40:46:004 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:40:46:142 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:40:46:146 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:40:46:150 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:40:46:200 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:40:46:418 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:40:46:422 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:40:46:425 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:40:46:507 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:40:46:687 ==>> $GBGGA,144050.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,41,,,40,34,,,40,1*7E

$GBGSV,7,2,25,3,,,40,39,,,40,25,,,40,60,,,40,1*4C

$GBGSV,7,3,25,7,,,39,16,,,38,24,,,38,43,,,38,1*45

$GBGSV,7,4,25,33,,,38,11,,,38,1,,,37,10,,,36,1*43

$GBGSV,7,5,25,12,,,36,6,,,36,23,,,36,2,,,35,1*76

$GBGSV,7,6,25,9,,,35,44,,,34,4,,,33,5,,,33,1*49

$GBGSV,7,7,25,38,,,30,1*79

$GBRMC,144050.516,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144050.516,0.000,1547.222,1547.222,49.495,2097152,2097152,2097152*52



2025-07-31 22:40:46:690 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:40:46:694 ==>> 检测【左刹电压测试1】
2025-07-31 22:40:46:697 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:47:024 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:1729  volt:2279 mv
[D][05:18:21][COMM]adc read right brake adc:1730  volt:2280 mv
[D][05:18:21][COMM]adc read throttle adc:1724  volt:2272 mv
[D][05:18:21][COMM]adc read battery ts volt:5 mv
[D][05:18:21][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:21][COMM]read battery soc:255
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:40:47:226 ==>> 【左刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 22:40:47:230 ==>> 检测【右刹电压测试1】
2025-07-31 22:40:47:248 ==>> 【右刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 22:40:47:251 ==>> 检测【转把电压测试1】
2025-07-31 22:40:47:266 ==>> 【转把电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 22:40:47:272 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:40:47:286 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:40:47:406 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:40:47:546 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:40:47:550 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:40:47:555 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:40:47:604 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:40:47:694 ==>> $GBGGA,144051.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,41,,,40,34,,,40,1*7D

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,1,,,38,33,,,37,10,,,36,1*40

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,44,,,33,4,,,33,5,,,33,1*4D

$GBGSV,7,7,26,32,,,31,38,,,30,1*79

$GBRMC,144051.516,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144051.516,0.000,1535.563,1535.563,49.135,2097152,2097152,2097152*5C



2025-07-31 22:40:47:826 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:40:47:830 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:40:47:836 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:40:47:910 ==>> 3A A3 05 00 A3 


2025-07-31 22:40:48:000 ==>> OFF_OUT5
OVER 150


2025-07-31 22:40:48:122 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:40:48:126 ==>> 检测【左刹电压测试2】
2025-07-31 22:40:48:154 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:40:48:414 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:22][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:22][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:22][COMM]adc read battery ts volt:7 mv
[D][05:18:22][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:40:48:662 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:40:48:669 ==>> 检测【右刹电压测试2】
2025-07-31 22:40:48:681 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:40:48:686 ==>> 检测【转把电压测试2】
2025-07-31 22:40:48:691 ==>> $GBGGA,144052.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,41,,,41,34,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,1,,,38,33,,,38,10,,,36,1*4F

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,5,,,34,44,,,33,4,,,33,1*4A

$GBGSV,7,7,26,32,,,32,38,,,30,1*7A

$GBRMC,144052.516,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144052.516,0.000,1541.938,1541.938,49.335,2097152,2097152,2097152*5D



2025-07-31 22:40:48:700 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:40:48:703 ==>> 检测【晶振检测】
2025-07-31 22:40:48:709 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:40:48:935 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]
[D][05:18:23][COMM]read battery soc:255


2025-07-31 22:40:49:008 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:40:49:012 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:40:49:015 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:40:49:117 ==>> 1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1643mV
Get AD_V4 1652mV
Get AD_V5 2768mV
Get AD_V6 1996mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:40:49:335 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:40:49:339 ==>> 检测【检测BootVer】
2025-07-31 22:40:49:342 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:49:732 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = ***************
[D][05:18:23][FCTY]HardwareID  = 867222087518488
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1295
[D][05:18:23][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1   

2025-07-31 22:40:49:822 ==>>       = 3846 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
$GBGGA,144053.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,34,,,41,41,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,37,10,,,36,1*40

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,5,,,33,44,,,33,4,,,33,1*4D

$GBGSV,7,7,26,32,,,33,38,,,30,1*7B

$GBRMC,144053.516,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144053.516,0.000,1540.342,1540.342,49.283,2097152,2097152,2097152*50



2025-07-31 22:40:49:901 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:40:49:905 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:40:49:928 ==>> 检测【检测固件版本】
2025-07-31 22:40:49:942 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:40:49:946 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:40:49:953 ==>> 检测【检测蓝牙版本】
2025-07-31 22:40:49:984 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:40:49:991 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:40:50:007 ==>> 检测【检测MoBikeId】
2025-07-31 22:40:50:021 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:40:50:025 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:40:50:029 ==>> 检测【检测蓝牙地址】
2025-07-31 22:40:50:053 ==>> 取到目标值:E4D7E5FEF2C9
2025-07-31 22:40:50:063 ==>> 【检测蓝牙地址】通过,【E4D7E5FEF2C9】符合目标值【】要求!
2025-07-31 22:40:50:067 ==>> 提取到蓝牙地址:E4D7E5FEF2C9
2025-07-31 22:40:50:072 ==>> 检测【BOARD_ID】
2025-07-31 22:40:50:105 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:40:50:108 ==>> 检测【检测充电电压】
2025-07-31 22:40:50:147 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:40:50:151 ==>> 检测【检测VBUS电压1】
2025-07-31 22:40:50:191 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:40:50:195 ==>> 检测【检测充电电流】
2025-07-31 22:40:50:234 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:40:50:239 ==>> 检测【检测IMEI】
2025-07-31 22:40:50:244 ==>> 取到目标值:867222087518488
2025-07-31 22:40:50:277 ==>> 【检测IMEI】通过,【867222087518488】符合目标值【】要求!
2025-07-31 22:40:50:281 ==>> 提取到IMEI:867222087518488
2025-07-31 22:40:50:288 ==>> 检测【检测IMSI】
2025-07-31 22:40:50:304 ==>> 取到目标值:***************
2025-07-31 22:40:50:324 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 22:40:50:328 ==>> 提取到IMSI:***************
2025-07-31 22:40:50:334 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:40:50:341 ==>> 取到目标值:***************
2025-07-31 22:40:50:378 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 22:40:50:385 ==>> 检测【打开CAN通信】
2025-07-31 22:40:50:397 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:40:50:504 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:40:50:700 ==>> $GBGGA,144054.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,34,,,41,41,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,38,10,,,37,1*4E

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,5,,,34,44,,,34,4,,,33,1*4D

$GBGSV,7,7,26,32,,,33,38,,,30,1*7B

$GBRMC,144054.516,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144054.516,0.000,1546.715,1546.715,49.481,2097152,2097152,2097152*53



2025-07-31 22:40:50:729 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:40:50:733 ==>> 检测【检测CAN通信】
2025-07-31 22:40:50:762 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:40:50:804 ==>> can send success
标准帧 ID:00000600 DATA:51 52

2025-07-31 22:40:50:834 ==>>  53 54 55 56 57 58 


2025-07-31 22:40:50:940 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36320
[D][05:18:25][COMM]read battery soc:255


2025-07-31 22:40:50:969 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:40:51:011 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:40:51:015 ==>> 检测【关闭CAN通信】
2025-07-31 22:40:51:021 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:40:51:032 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:40:51:105 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:40:51:303 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:40:51:308 ==>> 检测【打印IMU STATE】
2025-07-31 22:40:51:322 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:40:51:506 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:40:51:603 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:40:51:607 ==>> 检测【六轴自检】
2025-07-31 22:40:51:614 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:40:51:701 ==>> $GBGGA,144055.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,40,34,,,40,41,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,38,10,,,36,1*4F

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,44,,,34,32,,,34,5,,,33,1*78

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,144055.516,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144055.516,0.000,1541.928,1541.928,49.325,2097152,2097152,2097152*5B



2025-07-31 22:40:51:806 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:40:52:702 ==>> $GBGGA,144056.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,40,34,,,40,41,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,37,1,,,37,10,,,36,1*4F

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,35,2,,,35,1*76

$GBGSV,7,6,26,9,,,35,32,,,34,44,,,33,5,,,33,1*7F

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,144056.516,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144056.516,0.000,1535.553,1535.553,49.125,2097152,2097152,2097152*5A



2025-07-31 22:40:52:946 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 22:40:53:504 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:40:53:730 ==>> $GBGGA,144057.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,59,,,41,34,,,40,41,,,40,1*7E

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,37,10,,,36,1*40

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,32,,,34,44,,,34,5,,,33,1*78

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,144057.516,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144057.516,0.000,1540.332,1540.332,49.273,2097152,2097152,2097152*5B

[D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39090 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,-9,4076]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 22:40:53:976 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:40:53:984 ==>> 检测【打印IMU STATE2】
2025-07-31 22:40:54:009 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:40:54:208 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:40:54:257 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:40:54:261 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:40:54:268 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:40:54:313 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:40:54:418 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCT

2025-07-31 22:40:54:463 ==>> Y]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:40:54:545 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:40:54:552 ==>> 检测【检测VBUS电压2】
2025-07-31 22:40:54:560 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:54:876 ==>> $GBGGA,144058.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,41,,,41,34,,,40,1*7C

$GBGSV,7,2,26,3,,,40,39,,,40,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,38,10,,,36,1*4F

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,32,,,34,44,,,33,5,,,33,1*7F

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,144058.516,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144058.516,0.000,1543.529,1543.529,49.383,2097152,2097152,2097152*5A

[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = ***************
[D][05:18:29][FCTY]HardwareID  = 867222087518488
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS

2025-07-31 22:40:54:966 ==>>         = 11800 mv
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:29][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 7, adc = 303
[D][05:18:29][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3846 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:40:55:089 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:55:488 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = ***************
[D][05:18:29][FCTY]HardwareID  = 867222087518488
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 168
[D][05:18:29][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_

2025-07-31 22:40:55:533 ==>> VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3846 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:40:55:626 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:55:638 ==>> $GBGGA,144059.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,41,,,41,34,,,41,1*7D

$GBGSV,7,2,26,39,,,41,3,,,40,25,,,40,60,,,40,1*4E

$GBG

2025-07-31 22:40:55:698 ==>> SV,7,3,26,7,,,39,16,,,38,24,,,38,43,,,38,1*46

$GBGSV,7,4,26,11,,,38,33,,,38,1,,,38,10,,,37,1*4E

$GBGSV,7,5,26,12,,,36,6,,,36,23,,,36,2,,,35,1*75

$GBGSV,7,6,26,9,,,35,32,,,34,44,,,34,5,,,33,1*78

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,144059.516,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144059.516,0.000,1549.908,1549.908,49.587,2097152,2097152,2097152*59



2025-07-31 22:40:55:988 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = ***************
[D][05:18:30][FCTY]HardwareID  = 867222087518488
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:30][FCTY]Bat         = 3864 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 8700 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 0,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 141
[D][05:18:30][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS

2025-07-31 22:40:56:048 ==>> _VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3846 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]msg 0601 loss. last_tick:36306. cur_tick:41317. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41318


2025-07-31 22:40:56:221 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:56:805 ==>> [D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]r

2025-07-31 22:40:56:910 ==>> etry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30]

2025-07-31 22:40:57:015 ==>> [PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = ***************
[D][05:18:30][FCTY]HardwareID  = 867222087518488
[D][05:18:30][FCTY]

2025-07-31 22:40:57:120 ==>> MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 8700 mv
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 122
[D][05:18:30][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 25 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3846 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][CAT1]Send Data T

2025-07-31 22:40:57:195 ==>> o Server[198][201] ... ->:
0063B98F113311331133113311331B88B595DEF9DA0DAC9A1905AE5258C7F3253720A96D5EAAF10AEFB55ACD0CED7671263A7009252E100E81625F71F6212DC316C06CC2D7166A46E868C89D257925F4D41FE37215EB9BA3D1009F787696EF57E30537
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]


2025-07-31 22:40:57:274 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:40:57:377 ==>> [D][05:18:31][GNSS]recv submsg id[1]
[D][05:18:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:31][GNSS]location stop evt done evt


2025-07-31 22:40:57:665 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = ***************
[D][05:18:31][FCTY]HardwareID  = 867222087518488
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:18:31][FCTY]Bat         = 3884 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:31][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4


2025-07-31 22:40:57:710 ==>> [D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3846 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:40:57:803 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:40:57:809 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:40:57:815 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:40:57:909 ==>> 5A A5 01 5A A5 


2025-07-31 22:40:58:014 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:40:58:074 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 22:40:58:084 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:40:58:090 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:40:58:105 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:40:58:164 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 22:40:58:209 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:40:58:363 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:40:58:371 ==>> 检测【打开WIFI(3)】
2025-07-31 22:40:58:376 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:40:58:638 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:40:58:899 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:40:58:905 ==>> 检测【扩展芯片hw】
2025-07-31 22:40:58:928 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:40:59:096 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:40:59:174 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:40:59:180 ==>> 检测【扩展芯片boot】
2025-07-31 22:40:59:193 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:40:59:197 ==>> 检测【扩展芯片sw】
2025-07-31 22:40:59:212 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:40:59:217 ==>> 检测【检测音频FLASH】
2025-07-31 22:40:59:225 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:40:59:325 ==>> +WIFISCAN:4,0,CC057790A641,-73
+WIFISCAN:4,1,CC057790A7C1,-75
+WIFISCAN:4,2,44A1917CA62B,-75
+WIFISCAN:4,3,CC057790A7C0,-75

[D][05:18:33][CAT1]wifi scan report total[4]


2025-07-31 22:40:59:399 ==>>                                       [W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:40:59:678 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:41:00:371 ==>>                                                                                                                 ------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B5

2025-07-31 22:41:00:476 ==>> 0'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT 

2025-07-31 22:41:00:581 ==>> CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][PROT]index:1
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][PROT]is_send:1
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[999999999990

2025-07-31 22:41:00:686 ==>> 0006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]f:[ec800m_audio_respo

2025-07-31 22:41:00:791 ==>> nse_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:

2025-07-31 22:41:00:866 ==>> 5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255


2025-07-31 22:41:01:841 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]Send Data To Server[

2025-07-31 22:41:01:916 ==>> 198][201] ... ->:
0063B98D113311331133113311331B88B30F10B13BCA6BCB56092B11C5F3DC134469D5B41B80D0DC6CDD32E3794B145EA7DB8A88A04B4CA3193EDB8D304703393AB4F08C521964758DC731F0F1FCBA531F49539DF41C71AA014F4398741D58A84E9B37
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 22:41:02:192 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 22:41:02:752 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:41:03:523 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 22:41:03:874 ==>> [D][05:18:38][COMM]49182 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:41:04:182 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 22:41:04:261 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:41:04:267 ==>> 检测【打开喇叭声音】
2025-07-31 22:41:04:272 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:41:05:012 ==>> [D][05:18:38][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:38][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].r

2025-07-31 22:41:05:062 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:41:05:070 ==>> 检测【打开大灯控制】
2025-07-31 22:41:05:082 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:41:05:117 ==>> ecv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:

2025-07-31 22:41:05:222 ==>> 39][COMM]50193 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:41:05:312 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:41:05:356 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:41:05:362 ==>> 检测【关闭仪表供电3】
2025-07-31 22:41:05:367 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:41:05:492 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 22:41:05:631 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:41:05:640 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:41:05:663 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:41:05:768 ==>> [D][05:18:40][COMM]M->S yaw:INVALID
[W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:41:05:911 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:41:05:917 ==>> 检测【读大灯电压】
2025-07-31 22:41:05:925 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:41:06:087 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33363]


2025-07-31 22:41:06:183 ==>> 【读大灯电压】通过,【33363mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:41:06:189 ==>> 检测【关闭大灯控制2】
2025-07-31 22:41:06:211 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:41:06:219 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 22:41:06:390 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:41:06:460 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:41:06:466 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:41:06:477 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:41:06:697 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:41:06:742 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:41:06:751 ==>> 检测【打开WIFI(4)】
2025-07-31 22:41:06:774 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:41:07:110 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3B7E1711877D7B0F6BF0D06350C6F8632BAE38926C8E1D5989

2025-07-31 22:41:07:215 ==>> F232B0BD3EE2F2B5F5020E075610D511B2FF39B2101BBAC33958BD380276A1FEA7E5E99C9BAD187BA9BDB22F8EA6741A2B6BFFA44A2007C7CF6
[W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]
[D][05:18:41][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:18:41][CAT1]<<< 
+CME ERROR: 100

[D][05:18:41][CAT1]exec over: func id: 12, ret: 19


2025-07-31 22:41:07:370 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:41:07:380 ==>> 检测【EC800M模组版本】
2025-07-31 22:41:07:403 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:41:07:509 ==>> [D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:41:07:674 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:41:07:907 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:41:07:917 ==>> 检测【配置蓝牙地址】
2025-07-31 22:41:07:940 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:41:08:073 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:41:08:118 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E4D7E5FEF2C9>】
2025-07-31 22:41:08:208 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 22:41:08:313 ==>> recv ble 1
recv ble 2
ble set mac ok :e4,d7,e5,fe,f2,c9
enable filters ret : 0

2025-07-31 22:41:08:391 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:41:08:397 ==>> 检测【BLETEST】
2025-07-31 22:41:08:406 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:41:08:511 ==>> [D][05:18:42][COMM]53946 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init
4A A4 01 A4 4A 


2025-07-31 22:41:08:616 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E4D7E5FEF2C9*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E4D7E5FEF2C999999OVER 150


2025-07-31 22:41:09:426 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:41:09:432 ==>> 该项需要延时执行
2025-07-31 22:41:09:530 ==>> [D][05:18:43][COMM]54957 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:41:10:135 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:41:10:210 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 22:41:10:532 ==>> [D][05:18:44][COMM]55968 imu init OK
[D][05:18:44][COMM]S->M yaw:INVALID


2025-07-31 22:41:11:656 ==>> +WIFISCAN:4,0,CC057790A7C0,-72
+WIFISCAN:4,1,CC057790A7C1,-74
+WIFISCAN:4,2,44A1917CA62F,-76
+WIFISCAN:4,3,44A1917CA62B,-76

[D][05:18:46][CAT1]wifi scan report total[4]


2025-07-31 22:41:12:306 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][198] ... ->:
0063B981113311331133113311331B88B3919A

2025-07-31 22:41:12:381 ==>> AECD610C79BEBC2069126A35D71ED42AB4D84D4A998205F5E966B0C46F64A2ED74DAE315360923DC8E57AC5BE8C1122689D24BF9C8CD397899D7AD3D9948FD8BF6E98C381F20394436941AB07FA12D54
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
[D][05:18:46][COMM]read battery soc:255


2025-07-31 22:41:12:426 ==>>                                       

2025-07-31 22:41:14:227 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 22:41:16:232 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:41:17:550 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5BEA5F2F32676D76A4

2025-07-31 22:41:17:625 ==>> FF6DE529A0C44A7E4FC2F4A7465C31B83A6A9E854BFFAD8691A36BBC69B32FBF8092D8D7E1A58D26450CBF31364649E91BED250371572F57271D7791DEC4F1B0D1B34D5F4E47073B8F2
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]


2025-07-31 22:41:18:230 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 22:41:19:429 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:41:19:445 ==>> 检测【检测WiFi结果】
2025-07-31 22:41:19:450 ==>> WiFi信号:【CC057790A641】,信号值:-73
2025-07-31 22:41:19:459 ==>> WiFi信号:【CC057790A7C0】,信号值:-74
2025-07-31 22:41:19:479 ==>> WiFi信号:【CC057790A640】,信号值:-74
2025-07-31 22:41:19:485 ==>> WiFi信号:【CC057790A7C1】,信号值:-75
2025-07-31 22:41:19:494 ==>> WiFi信号:【44A1917CA62B】,信号值:-75
2025-07-31 22:41:19:525 ==>> WiFi信号:【44A1917CA62F】,信号值:-76
2025-07-31 22:41:19:531 ==>> WiFi数量【6】, 最大信号值:-73
2025-07-31 22:41:19:556 ==>> 检测【检测GPS结果】
2025-07-31 22:41:19:569 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:41:19:601 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 22:41:20:232 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 22:41:20:446 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:41:20:454 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:20:465 ==>> 定位已等待【1】秒.
2025-07-31 22:41:20:814 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:41:21:449 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:21:459 ==>> 定位已等待【2】秒.
2025-07-31 22:41:21:542 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,38,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:41:22:187 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:41:22:397 ==>>                                          $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,34,,,40,39,,,40,40,,,40,59,,,39,1*76

$GBGSV,3,2,09,25,,,39,24,,,33,7,,,41,60,,,38,1*4A

$GBGSV,3,3,09,16,,,37,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.122,1596.122,51.038,2097152,2097152,2097152*40

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 23, ret: 6
[D][05:18:56][CAT1]sub id: 23, ret: 6



2025-07-31 22:41:22:457 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:22:466 ==>> 定位已等待【3】秒.
2025-07-31 22:41:22:762 ==>> [D][05:18:56][GNSS]recv submsg id[1]
[D][05:18:56][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][M

2025-07-31 22:41:22:852 ==>> 2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5BA2FA6A38326028C1FC1D5798966B5364797851BD6414A97831B697623B89F1BB30D7435058A015C9CBDA5966050A39892A42CA13AA4D1649A359D29B6EBFF40E30E59385CA3832F3A5B2BC7CB84F9B64000
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 22:41:23:309 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,41,,,43,40,,,41,34,,,40,39,,,40,1*79

$GBGSV,3,2,12,59,,,40,7,,,39,25,,,39,60,,,39,1*40

$GBGSV,3,3,12,3,,,39,16,,,37,24,,,35,1,,,36,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1628.129,1628.129,52.033,2097152,2097152,2097152*48



2025-07-31 22:41:23:462 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:23:471 ==>> 定位已等待【4】秒.
2025-07-31 22:41:24:352 ==>> [D][05:18:58][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,41,,,41,40,,,41,34,,,40,39,,,40,1*78

$GBGSV,5,2,17,59,,,40,60,,,40,1,,,40,7,,,39,1*75

$GBGSV,5,3,17,25,,,39,3,,,39,16,,,37,24,,,36,1*42

$GBGSV,5,4,17,2,,,36,4,,,34,5,,,33,44,,,33,1*40

$GBGSV,5,5,17,32,,,33,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1563.220,1563.220,50.006,2097152,2097152,2097152*4C



2025-07-31 22:41:24:472 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:24:481 ==>> 定位已等待【5】秒.
2025-07-31 22:41:25:363 ==>> $GBGGA,144129.201,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,41,41,,,40,34,,,40,39,,,40,1*7D

$GBGSV,5,2,20,59,,,40,60,,,40,7,,,39,25,,,39,1*49

$GBGSV,5,3,20,3,,,39,1,,,38,16,,,37,24,,,37,1*70

$GBGSV,5,4,20,2,,,34,32,,,34,23,,,34,4,,,33,1*74

$GBGSV,5,5,20,44,,,33,5,,,32,12,,,36,10,,,36,1*42

$GBRMC,144129.201,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144129.201,0.000,1543.174,1543.174,49.371,2097152,2097152,2097152*51



2025-07-31 22:41:25:483 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:25:493 ==>> 定位已等待【6】秒.
2025-07-31 22:41:25:664 ==>> $GBGGA,144129.501,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,41,,,40,34,,,40,39,,,40,1*7F

$GBGSV,6,2,21,59,,,40,60,,,40,25,,,40,7,,,39,1*45

$GBGSV,6,3,21,3,,,39,1,,,38,16,,,37,24,,,37,1*72

$GBGSV,6,4,21,12,,,34,2,,,34,32,,,34,23,,,34,1*46

$GBGSV,6,5,21,4,,,33,44,,,33,5,,,32,43,,,39,1*7B

$GBGSV,6,6,21,10,,,36,1*71

$GBRMC,144129.501,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144129.501,0.000,1538.328,1538.328,49.219,2097152,2097152,2097152*59



2025-07-31 22:41:26:254 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:41:26:485 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:26:494 ==>> 定位已等待【7】秒.
2025-07-31 22:41:26:670 ==>> $GBGGA,144130.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,40,34,,,40,39,,,40,1*7A

$GBGSV,6,2,24,59,,,40,60,,,40,25,,,40,3,,,40,1*4A

$GBGSV,6,3,24,7,,,39,16,,,38,24,,,38,11,,,38,1*42

$GBGSV,6,4,24,1,,,37,43,,,36,10,,,36,6,,,36,1*72

$GBGSV,6,5,24,12,,,35,23,,,35,9,,,35,2,,,34,1*7B

$GBGSV,6,6,24,32,,,34,44,,,33,4,,,32,5,,,32,1*77

$GBRMC,144130.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144130.501,0.000,1535.680,1535.680,49.123,2097152,2097152,2097152*5B



2025-07-31 22:41:27:495 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:27:505 ==>> 定位已等待【8】秒.
2025-07-31 22:41:27:690 ==>> $GBGGA,144131.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,40,34,,,40,39,,,40,1*7A

$GBGSV,6,2,24,59,,,40,60,,,40,25,,,40,3,,,39,1*44

$GBGSV,6,3,24,7,,,39,11,,,38,1,,,38,16,,,37,1*7A

$GBGSV,6,4,24,24,,,37,43,,,37,10,,,36,6,,,36,1*44

$GBGSV,6,5,24,12,,,35,23,,,35,9,,,35,2,,,34,1*7B

$GBGSV,6,6,24,32,,,34,44,,,33,4,,,32,5,,,32,1*77

$GBRMC,144131.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144131.501,0.000,765.538,765.538,700.101,2097152,2097152,2097152*60



2025-07-31 22:41:27:995 ==>>                                  [D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311

2025-07-31 22:41:28:071 ==>> 331133113311331B88BE7A0AE8A010E6D0C5E5F7F13C41BAEC5B09163E029C8888F1E7CC03F99DF58C3329205A3A98C29DCBD50C5B68DCC5DFB47934
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]


2025-07-31 22:41:28:268 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:41:28:510 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:28:521 ==>> 定位已等待【9】秒.
2025-07-31 22:41:28:691 ==>> $GBGGA,144132.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,59,,,40,39,,,40,1*72

$GBGSV,7,2,25,34,,,40,25,,,40,41,,,40,7,,,39,1*48

$GBGSV,7,3,25,3,,,39,11,,,38,24,,,37,1,,,37,1*70

$GBGSV,7,4,25,16,,,37,43,,,37,33,,,37,10,,,36,1*72

$GBGSV,7,5,25,6,,,36,12,,,36,9,,,35,23,,,35,1*7E

$GBGSV,7,6,25,2,,,34,32,,,34,44,,,33,5,,,32,1*77

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,144132.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144132.501,0.000,765.534,765.534,700.097,2097152,2097152,2097152*6D



2025-07-31 22:41:29:521 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:29:531 ==>> 定位已等待【10】秒.
2025-07-31 22:41:29:701 ==>> $GBGGA,144133.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,3,,,40,59,,,40,1*4B

$GBGSV,7,2,25,39,,,40,34,,,40,41,,,40,7,,,39,1*45

$GBGSV,7,3,25,25,,,39,11,,,38,24,,,37,1,,,37,1*44

$GBGSV,7,4,25,16,,,37,43,,,37,33,,,37,10,,,36,1*72

$GBGSV,7,5,25,6,,,36,12,,,36,9,,,35,23,,,35,1*7E

$GBGSV,7,6,25,2,,,34,32,,,34,44,,,33,4,,,33,1*77

$GBGSV,7,7,25,5,,,32,1*45

$GBRMC,144133.501,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144133.501,0.000,766.358,766.358,700.850,2097152,2097152,2097152*6F



2025-07-31 22:41:30:265 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:41:30:535 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:30:550 ==>> 定位已等待【11】秒.
2025-07-31 22:41:30:700 ==>> $GBGGA,144134.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,3,,,40,59,,,40,34,,,40,1*4A

$GBGSV,7,2,25,25,,,40,41,,,40,7,,,39,60,,,39,1*47

$GBGSV,7,3,25,39,,,39,11,,,38,24,,,37,16,,,37,1*7F

$GBGSV,7,4,25,1,,,37,33,,,37,43,,,37,10,,,36,1*44

$GBGSV,7,5,25,6,,,36,12,,,36,23,,,36,9,,,35,1*7D

$GBGSV,7,6,25,2,,,34,32,,,34,44,,,33,5,,,32,1*77

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,144134.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144134.501,0.000,765.530,765.530,700.094,2097152,2097152,2097152*68



2025-07-31 22:41:31:543 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:31:553 ==>> 定位已等待【12】秒.
2025-07-31 22:41:31:693 ==>> $GBGGA,144135.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,59,,,41,60,,,40,3,,,40,1*4A

$GBGSV,7,2,25,39,,,40,34,,,40,25,,,40,41,,,40,1*7B

$GBGSV,7,3,25,7,,,39,1,,,38,11,,,38,24,,,37,1*7B

$GBGSV,7,4,25,16,,,37,33,,,37,43,,,37,10,,,36,1*72

$GBGSV,7,5,25,6,,,36,12,,,36,23,,,36,9,,,35,1*7D

$GBGSV,7,6,25,2,,,34,32,,,34,44,,,33,4,,,33,1*77

$GBGSV,7,7,25,5,,,32,1*45

$GBRMC,144135.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,144135.501,0.000,769.671,769.671,703.880,2097152,2097152,2097152*67



2025-07-31 22:41:32:265 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:41:32:555 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:41:32:566 ==>> 定位已等待【13】秒.
2025-07-31 22:41:33:424 ==>> $GBGGA,144132.507,2301.2561271,N,11421.9430919,E,1,08,1.29,76.360,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,35,1*4A

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,32,44,15,49,34,24,14,261,38,11,,,38,1*4D

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBRMC,144132.507,A,2301.2561271,N,11421.9430919,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:07][GNSS]HD8040 GPS
[D][05:19:07][GNSS]GPS diff_sec 124017745, report 0x42 frame
$GBGST,144132.507,1.644,0.152,0.155,0.232,3.789,3.652,13*5E

[D][05:19:07][COMM]Main Task receive event:131
[D][05:19:07][COMM]index:0,power_mode:0xFF
[D][05:19:07][COMM]index:1,sound_mode:0xFF
[D][05:19:07][COMM]index:2,gsensor_mode:0xFF
[D][05:19:07][COMM]index:3,report_freq_mode:0xFF
[D][05:19:07][COMM]index:4,report_p

2025-07-31 22:41:33:528 ==>> eriod:0xFF
[D][05:19:07][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:07][COMM]index:6,normal_reset_period:0xFF
[D][05:19:07][COMM]index:7,spock_over_speed:0xFF
[D][05:19:07][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:07][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:07][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:07][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:07][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:07][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:07][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:07][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:07][COMM]index:16,imu_config_params:0xFF
[D][05:19:07][COMM]index:17,long_connect_params:0xFF
[D][05:19:07][COMM]index:18,detain_mark:0xFF
[D][05:19:07][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:07][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:07][COMM]index:21,mc_mode:0xFF
[D][05:19:07][COMM]index:22,S_mode:0xFF
[D][05:19:07][COMM]index:23,overweight:0xFF
[D][05:19:07][COMM]index:24,standstill_mode:0xFF
[D][05:19:07][COMM]index:25,night_mode:0xFF
[D][05:19:07][COMM]index:26,experiment1:0xFF
[D][05:19:07][COMM]index:27,experiment2:0xFF
[D][05:19:07][COMM]index:28,experiment3:0xFF
[D][05:19:07]

2025-07-31 22:41:33:558 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 22:41:33:568 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【39】,信号值:【40】
北斗星号:【3】,信号值:【40】
北斗星号:【16】,信号值:【38】
北斗星号:【59】,信号值:【40】
北斗星号:【25】,信号值:【40】
北斗星号:【10】,信号值:【36】
北斗星号:【1】,信号值:【37】
北斗星号:【9】,信号值:【35】
北斗星号:【2】,信号值:【35】
北斗星号:【34】,信号值:【40】
北斗星号:【60】,信号值:【40】
北斗星号:【41】,信号值:【40】
北斗星号:【24】,信号值:【38】
北斗星号:【11】,信号值:【38】
北斗星号:【43】,信号值:【38】
北斗星号:【33】,信号值:【37】
北斗星号:【12】,信号值:【36】
北斗星号:【23】,信号值:【36】

2025-07-31 22:41:33:576 ==>> 检测【CSQ强度】
2025-07-31 22:41:33:589 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:41:33:633 ==>> [COMM]index:29,experiment4:0xFF
[D][05:19:07][COMM]index:30,night_mode_start:0xFF
[D][05:19:07][COMM]index:31,night_mode_end:0xFF
[D][05:19:07][COMM]index:33,park_report_minutes:0xFF
[D][05:19:07][COMM]index:34,park_report_mode:0xFF
[D][05:19:07][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:07][COMM]index:38,charge_battery_para: FF
[D][05:19:07][COMM]index:39,multirider_mode:0xFF
[D][05:19:07][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:07][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:07][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:07][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:07][COMM]index:44,riding_duration_config:0xFF
[D][05:19:07][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:07][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:07][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:07][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:07][COMM]index:49,mc_load_startup:0xFF
[D][05:19:07][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:07][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:07][COMM]index:52,traffic_mode:0xFF
[D][05:19:07][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:07][COMM]index:54,traffic_security_model_cyc

2025-07-31 22:41:33:738 ==>> le:0xFF
[D][05:19:07][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:07][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:07][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:07][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:07][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:07][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:07][COMM]index:63,experiment5:0xFF
[D][05:19:07][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:07][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:07][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:07][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:07][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:07][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:07][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:07][COMM]index:72,experiment6:0xFF
[D][05:19:07][COMM]index:73,experiment7:0xFF
[D][05:19:07][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:07][COMM]index:75,zero_value_from_server:-1
[D][05:19:07][COMM]index:76,multirider_threshold:255
[D][05:19:07][COMM]index:77,experiment8:255
[D][05:19:07][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:07][COMM]index:79,temp_pa

2025-07-31 22:41:33:843 ==>> rk_tail_light_twinkle_duration:255
[D][05:19:07][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:07][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:07][COMM]index:83,loc_report_interval:255
[D][05:19:07][COMM]index:84,multirider_threshold_p2:255
[D][05:19:07][COMM]index:85,multirider_strategy:255
[D][05:19:07][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:07][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:07][COMM]index:90,weight_param:0xFF
[D][05:19:07][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:07][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:07][COMM]index:95,current_limit:0xFF
[D][05:19:07][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:07][COMM]index:100,location_mode:0xFF

[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:07][COMM]Main Task receive event:131 finished processing
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:07][PROT]CLEAN,SEND:2
[D

2025-07-31 22:41:33:948 ==>> ][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[D][05:19:07][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:1

2025-07-31 22:41:34:053 ==>> 9:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:07][CAT1]<<< 
ERROR

$GBGGA,144133.007,2301.2569660,N,11421.9441017,E,1,08,1.29,78.776,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,35,1*4A

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,11,,,38,1*43

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,05,40,66,172,39,39,61,37,41,25,50,357,39,34,45,102,36,5*4B

$GBGSV,2,2,05,41,41,262,40,5*45

$GBRMC,144133.007,A,2301.2569660,N,11421.9441017,E,0.000,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,144133.007,1.216,1.045,1.081,1.476,2.088,2.139,8.145*79



2025-07-31 22:41:34:356 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+CSQ<<<<<
$GBGGA,144134.000,2301.2570288,N,11421.9438917,E,1,08,1.29,80.133,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,40,25,50,357,40,1*70

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,35,1*4A

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,11,,,38,1*43

$GBGSV,7,6,25,33,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,41,25,50,357,40,34,45,102,38,5*47

$GBGSV,2,2,06,41,41,262,41,44,15,49,34,5*49

$GBRMC,144134.000,A,2301.2570288,N,11421.9438917,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,144134.000,1.649,0.262,0.269,0.383,1.877,1.863,6.581*74

[D][05:19:08][COMM]read battery soc:255


2025-07-31 22:41:35:283 ==>> $GBGGA,144135.000,2301.2572334,N,11421.9439429,E,1,08,1.29,80.937,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,38,6,63,79,36,39,61,37,40,1*7F

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,40,25,50,357,39,1*7E

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,11,,,38,1*43

$GBGSV,7,6,25,33,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,39,5*45

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144135.000,A,2301.2572334,N,11421.9439429,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144135.000,1.056,0.260,0.266,0.378,1.265,1.237,5.457*7F



2025-07-31 22:41:35:620 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:41:36:304 ==>> $GBGGA,144136.000,2301.2573257,N,11421.9439743,E,1,08,1.29,81.409,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,40,25,50,357,40,1*70

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,32,1*4A

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,11,,,38,1*43

$GBGSV,7,6,25,33,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144136.000,A,2301.2573257,N,11421.9439743,E,0.000,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,144136.000,1.096,0.220,0.225,0.321,1.166,1.136,4.838*79

[W][05:19:10][COMM]>>>>>Input command = AT+CSQ<<<<<
                                   

2025-07-31 22:41:36:334 ==>>       

2025-07-31 22:41:37:276 ==>> $GBGGA,144137.000,2301.2574165,N,11421.9439743,E,1,08,1.29,81.659,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.42,1.29,2.04,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,40,25,50,357,40,1*70

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,38,11,,,38,1*4C

$GBGSV,7,6,25,33,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,41,34,45,102,40,5*4A

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144137.000,A,2301.2574165,N,11421.9439743,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144137.000,0.868,0.278,0.285,0.402,0.892,0.862,4.310*7A



2025-07-31 22:41:37:690 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:41:37:705 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:41:38:311 ==>> $GBGGA,144138.000,2301.2574579,N,11421.9439775,E,1,08,1.29,82.023,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.29,2.04,4*08

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,32,44,15,49,34,24,14,261,38,11,,,38,1*4D

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,35,1*79

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144138.000,A,2301.2574579,N,11421.9439775,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,144138.000,0.805,0.274,0.281,0.395,0.769,0.743,3.939*7E

[W][05:19:12][COMM]>>>>>Input command = AT+CSQ<<<<<
                                         

2025-07-31 22:41:39:283 ==>> $GBGGA,144139.000,2301.2574830,N,11421.9439511,E,1,08,1.29,82.281,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.29,2.04,4*08

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,32,44,15,49,34,24,14,261,38,11,,,38,1*4D

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,35,1*79

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144139.000,A,2301.2574830,N,11421.9439511,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,144139.000,0.872,0.204,0.208,0.298,0.784,0.761,3.692*78



2025-07-31 22:41:39:759 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:41:40:367 ==>> $GBGGA,144140.000,2301.2575003,N,11421.9439455,E,1,08,1.29,82.611,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.29,2.04,4*08

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,11,,,38,1*43

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144140.000,A,2301.2575003,N,11421.9439455,E,0.000,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,144140.000,1.002,0.163,0.166,0.243,0.865,0.846,3.526*71

[D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:19:14][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:14][COMM]read battery soc:255


2025-07-31 22:41:41:282 ==>> $GBGGA,144141.000,2301.2575248,N,11421.9439378,E,1,08,1.30,82.709,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.30,2.04,4*00

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,40,25,50,357,40,1*7F

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,38,11,,,38,1*4C

$GBGSV,7,6,25,43,,,38,33,,,37,12,,,36,23,,,36,1*7A

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,41,34,45,102,40,5*4A

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144141.000,A,2301.2575248,N,11421.9439378,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,144141.000,1.069,0.214,0.219,0.313,0.898,0.881,3.374*79



2025-07-31 22:41:41:830 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:41:42:286 ==>> $GBGGA,144142.000,2301.2575224,N,11421.9439307,E,1,08,1.30,82.886,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.30,2.04,4*00

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,38,59,52,129,41,25,50,357,40,1*7E

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,38,33,,,38,1*4C

$GBGSV,7,6,25,11,,,38,43,,,38,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,41,34,45,102,40,5*4A

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144142.000,A,2301.2575224,N,11421.9439307,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144142.000,1.439,0.210,0.215,0.307,1.181,1.168,3.394*77



2025-07-31 22:41:42:331 ==>>                                                                                               

2025-07-31 22:41:42:710 ==>> [D][05:19:17][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:41:43:373 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

$GBGGA,144143.000,2301.25751

2025-07-31 22:41:43:478 ==>> 77,N,11421.9439296,E,1,08,1.30,82.803,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.30,2.04,4*00

$GBGSV,7,1,25,40,66,172,41,7,63,199,38,6,63,79,36,39,61,37,40,1*7F

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,41,25,50,357,40,1*71

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,34,1*4B

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,32,44,15,49,34,24,14,261,37,33,,,37,1*4D

$GBGSV,7,6,25,11,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144143.000,A,2301.2575177,N,11421.9439296,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,144143.000,1.588,0.232,0.238,0.337,1.280,1.268,3.338*76

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17

2025-07-31 22:41:43:497 ==>> 【CSQ强度】通过,【22】符合目标值【18】至【31】要求!
2025-07-31 22:41:43:507 ==>> 检测【关闭GSM联网】
2025-07-31 22:41:43:515 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:41:43:543 ==>> ][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:41:43:763 ==>>                                                                             t: 6
[D][05:19:18][CAT1]sub id: 10, ret: 6

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:18][M2M ]m2m gsm shut done, ret[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][CAT1]gsm read msg sub id: 8
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:19:18][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:18][COMM]GSM test
[D][05:19:18][COMM]GSM test disable


2025-07-31 22:41:43:929 ==>> [D][05:19:18][CAT1]pdpdeact urc len[22]


2025-07-31 22:41:44:034 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:41:44:042 ==>> 检测【4G联网测试】
2025-07-31 22:41:44:054 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:41:44:752 ==>> $GBGGA,144144.000,2301.2575141,N,11421.9439193,E,1,08,1.30,82.878,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,16,25,34,41,44,,,,,2.41,1.30,2.04,4*00

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,6,63,79,36,39,61,37,40,1*7E

$GBGSV,7,2,25,3,60,190,40,16,58,10,37,59,52,129,40,25,50,357,40,1*70

$GBGSV,7,3,25,10,49,225,36,1,48,125,37,9,47,333,35,2,45,237,35,1*4A

$GBGSV,7,4,25,34,45,102,40,60,41,238,40,41,41,262,40,4,32,111,33,1*4B

$GBGSV,7,5,25,5,21,256,33,44,15,49,34,24,14,261,37,33,,,37,1*4C

$GBGSV,7,6,25,11,,,37,43,,,37,12,,,36,23,,,35,1*76

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,66,172,41,39,61,37,42,25,50,357,40,34,45,102,40,5*4B

$GBGSV,2,2,06,41,41,262,42,44,15,49,34,5*4A

$GBRMC,144144.000,A,2301.2575141,N,11421.9439193,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,144144.000,1.629,0.224,0.229,0.328,1.299,1.287,3.250*76

[W][05:19:18][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:18][COMM]Main Task receive event:14
[D][05:19:18][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955158, allstateRepSeconds = 0
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]ind

2025-07-31 22:41:44:857 ==>> ex:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18

2025-07-31 22:41:44:962 ==>> ][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]ind

2025-07-31 22:41:45:067 ==>> ex:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][

2025-07-31 22:41:45:172 ==>> 05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][CO

2025-07-31 22:41:45:202 ==>> MM]read battery soc:255


2025-07-31 22:41:45:307 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:41:45:397 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:41:45:698 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             9:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[294]
[D][05:19:19][SAL ]Cellular task submsg id[10]
[D][05:19:19][SAL ]cellular SEND socket id[0]

2025-07-31 22:41:45:802 ==>>  type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:19][CAT1]Send Data To Server[294][294] ... ->:
0093B986113311331133113311331B88B1C644D055F783AB7782AB725C2167578629B682A475CB01DF35A84493A3AE32362F49EE8A271C19E9A0142443A044F8698D28C2335CFEF28D1BD4A3001CE18FEDF3D1F10DD7FA3B1E08207E41BC8F470ADAD4C126190FCA260BB5C9787C9EFF28B6D6CA30DC32FFE9F51C1B161F3129CC13DB485BAEECEB03257929A2592F409D07B4
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:19][COMM]------>period, report file manifest
[D][05:19:19][COMM]Main Task receive event:14 finished processing
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:1

2025-07-31 22:41:45:862 ==>> 9:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][CAT1]gsm read msg sub id: 21
[D][05:19:19][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]
[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]cell info report total[0]
[D][05:19:19][CAT1]exec over: func id: 21, ret: 6


2025-07-31 22:41:46:065 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:41:46:073 ==>> 检测【关闭GPS】
2025-07-31 22:41:46:084 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:41:46:486 ==>> $GBGGA,144146.000,2301.2575189,N,11421.9439206,E,1,17,0.70,82.548,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,43,1.35,0.70,1.16,4*0C

$GBGSA,A,3,12,23,24,44,32,,,,,,,,1.35,0.70,1.16,4*0B

$GBGSV,7,1,25,40,66,172,41,7,63,199,39,39,61,37,40,3,60,190,40,1*4F

$GBGSV,7,2,25,6,58,6,36,16,58,10,38,59,52,129,40,10,52,208,36,1*70

$GBGSV,7,3,25,9,50,343,35,25,50,357,40,1,48,125,37,2,45,237,35,1*40

$GBGSV,7,4,25,34,45,102,40,60,41,238,39,41,41,262,40,11,40,136,38,1*7A

$GBGSV,7,5,25,4,32,111,33,43,31,169,37,12,31,70,36,23,24,298,35,1*79

$GBGSV,7,6,25,24,24,64,37,5,21,256,33,44,15,49,34,32,12,314,34,1*4C

$GBGSV,7,7,25,33,,,37,1*75

$GBGSV,3,1,10,40,66,172,41,39,61,37,42,25,50,357,41,34,45,102,40,5*4C

$GBGSV,3,2,10,41,41,262,42,43,31,169,34,23,24,298,37,24,24,64,35,5*4A

$GBGSV,3,3,10,44,15,49,34,32,12,314,31,5*4B

$GBRMC,144146.000,A,2301.2575189,N,11421.9439206,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,144146.000,2.700,0.169,0.172,0.251,1.982,1.973,3.447*74

[W][05:19:20][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]stop event:8
[D][05:19:20][GNSS]GPS 

2025-07-31 22:41:46:561 ==>> stop. ret=0
[D][05:19:20][GNSS]all continue location stop
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]all sing location stop
[D][05:19:20][CAT1]gsm read msg sub id: 24
[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:20][COMM]read battery soc:255
[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 24, ret: 6
[D][05:19:20][CAT1]sub id: 24, ret: 6



2025-07-31 22:41:46:591 ==>>                                                                                                                                            

2025-07-31 22:41:46:602 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:41:46:609 ==>> 检测【清空消息队列2】
2025-07-31 22:41:46:627 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:41:46:801 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:21][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:41:46:882 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:41:46:889 ==>> 检测【轮动检测】
2025-07-31 22:41:46:912 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:41:46:999 ==>> 3A A3 01 00 A3 


2025-07-31 22:41:47:104 ==>> OFF_OUT1
OVER 150


2025-07-31 22:41:47:164 ==>> [D][05:19:21][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 22:41:47:393 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:41:47:501 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:41:47:671 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:41:47:683 ==>> 检测【关闭小电池】
2025-07-31 22:41:47:697 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:41:47:801 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:41:47:955 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:41:47:963 ==>> 检测【进入休眠模式】
2025-07-31 22:41:47:987 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:41:48:071 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:41:48:161 ==>> [D][05:19:22][COMM]Main Task receive event:28
[D][05:19:22][COMM]main task tmp_sleep_event = 8
[D][05:19:22][COMM]prepare to sleep
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:41:48:327 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 22:41:48:948 ==>> [D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]exec over: func id: 12, ret: 6
[D][05:19:23][M2M ]tcpclient close[4]
[D][05:19:23][SAL ]Cellular task submsg id[12]
[D][05:19:23][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:23][CAT1]gsm read msg sub id: 9
[D][05:19:23][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]exec over: func id: 9, ret: 6
[D][05:19:23][CAT1]sub id: 9, ret: 6

[D][05:19:23][SAL ]Cellular task submsg id[68]
[D][05:19:23][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:23][SAL ]socket close ind. id[4]
[D][05:19:23][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:23][COMM]1x1 frm_can_tp_send ok
[D][05:19:23][CAT1]pdpdeact urc len[22]


2025-07-31 22:41:49:252 ==>> [E][05:19:23][COMM]1x1 rx timeout
[D][05:19:23][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:41:49:749 ==>> [E][05:19:24][COMM]1x1 rx timeout
[E][05:19:24][COMM]1x1 tp timeout
[E][05:19:24][COMM]1x1 error -3.
[W][05:19:24][COMM]CAN STOP!
[D][05:19:24][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:24][COMM]------------ready to Power off Acckey 1------------
[D][05:19:24][COMM]------------ready to Power off Acckey 2------------
[D][05:19:24][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:24][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1303
[D][05:19:24][COMM]bat sleep fail, reason:-1
[D][05:19:24][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:24][COMM]accel parse set 0
[D][05:19:24][COMM]imu rest ok. 95089
[D][05:19:24][COMM]imu sleep 0
[W][05:19:24][COMM]now sleep


2025-07-31 22:41:50:026 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:41:50:035 ==>> 检测【检测33V休眠电流】
2025-07-31 22:41:50:042 ==>> 开始33V电流采样
2025-07-31 22:41:50:072 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:41:50:130 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:41:51:139 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:41:51:169 ==>> Current33V:????:16.40

2025-07-31 22:41:51:647 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:41:51:654 ==>> 【检测33V休眠电流】通过,【16.4uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:41:51:662 ==>> 该项需要延时执行
2025-07-31 22:41:53:662 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:41:53:675 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:41:53:699 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:41:53:812 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1644mV
Get AD_V4 1mV
Get AD_V5 2759mV
Get AD_V6 2021mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:41:54:683 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:41:54:711 ==>> 检测【打开小电池2】
2025-07-31 22:41:54:719 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:41:54:814 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:41:54:958 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:41:54:965 ==>> 该项需要延时执行
2025-07-31 22:41:55:473 ==>> 此处延时了:【500】毫秒
2025-07-31 22:41:55:493 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:41:55:513 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:41:55:612 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:41:55:752 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:41:55:761 ==>> 该项需要延时执行
2025-07-31 22:41:56:263 ==>> 此处延时了:【500】毫秒
2025-07-31 22:41:56:276 ==>> 检测【进入休眠模式2】
2025-07-31 22:41:56:309 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:41:56:331 ==>> [D][05:19:30][COMM]------------ready to Power on Acckey 1------------
[D][05:19:30][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:30][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:30][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:30][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:30][COMM]----- get Acckey 1 and value:1------------
[W][05:19:30][COMM]CAN START!
[D][05:19:30][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:30][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 101550
[

2025-07-31 22:41:56:368 ==>> D][05:19:30][COMM][Audio]exec status ready.
[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 6
[D][05:19:30][COMM]imu wakeup ok. 101564
[D][05:19:30][COMM]imu wakeup 1
[W][05:19:30][COMM]wake up system, wakeupEvt=0x80
[D][05:19:30][COMM]frm_can_weigth_power_set 1
[D][05:19:30][COMM]Clear Sleep Block Evt
[D][05:19:30][COMM]Main Task receive event:28 finished processing


2025-07-31 22:41:56:685 ==>> [D][05:19:30][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:30][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:30][COMM]1x1 rx timeout
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][COMM]Main Task receive event:28
[D][05:19:30][COMM]prepare to sleep
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 6
[W][05:19:30][COMM]CAN STOP!
[D][05:19:30][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:30][COMM]------------ready to Power off Acckey 1------------
[D][05:19:30][COMM]------------ready to Power off Acckey 2------------
[D][05:19:30][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:30][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 129
[D][05:19:30][COMM]bat sleep fail, reason:-1
[D][05:19:30][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:30][COMM]accel parse set 0
[D][05:19:30][COMM]imu rest ok. 102010
[D][05:19:31][COMM]imu sleep 0
[W][05:19:31][COMM]now sleep


2025-07-31 22:41:56:810 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:41:56:836 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:41:56:849 ==>> 开始小电池电流采样
2025-07-31 22:41:56:875 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:41:56:911 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:41:57:922 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:41:57:999 ==>> CurrentBattery:ƽ��:69.16

2025-07-31 22:41:58:424 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:41:58:432 ==>> 【检测小电池休眠电流】通过,【69.16uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:41:58:440 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:41:58:459 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:41:58:514 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:41:58:713 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:41:58:722 ==>> 该项需要延时执行
2025-07-31 22:41:58:730 ==>> [D][05:19:32][COMM]------------ready to Power on Acckey 1------------
[D][05:19:33][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:33][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:33][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:33][COMM]----- get Acckey 1 and value:1------------
[W][05:19:33][COMM]CAN START!
[E][05:19:33][COMM]1x1 rx timeout
[E][05:19:33][COMM]1x1 tp timeout
[E][05:19:33][COMM]1x1 error -3.
[D][05:19:33][COMM]read battery soc:0
[D][05:19:33][CAT1]gsm read msg sub id: 12
[D][05:19:33][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:33][COMM][Audio]exec status ready.
[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 12, ret: 6
[D][05:19:33][COMM]imu wakeup ok. 104047
[D][05:19:33][COMM]imu wakeup 1
[W][05:19:33][COMM]wake up system, wakeupEvt=0x80
[D][05:19:33][COMM]frm_can_weigth_power_set 1
[D][05:19:33][COMM]Clear Sleep Block Evt
[D][05:19:33][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:41:59:012 ==>> [E][05:19:33][COMM]1x1 rx timeout
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:41:59:117 ==>> [D][05:19:33][COMM]msg 02A0 loss. last_tick:104015. cur_tick:104526. period:50
[D][05:19:33][COMM]msg 02A4 loss. last_tick:104015. cur_tick:104527. period:50


2025-07-31 22:41:59:177 ==>> [D][05:19:33][COMM]msg 02A5 loss. last_tick:104015. cur_tick:104527. period:50
[D][05:19:33][COMM]msg 02A6 loss. last_tick:104015. cur_tick:104528. period:50
[D][05:19:33][COMM]msg 02A7 loss. last_tick:104015. cur_tick:104528. period:50
[D][05:19:33][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 104528
[D][05:19:33][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 104529


2025-07-31 22:41:59:222 ==>> 此处延时了:【500】毫秒
2025-07-31 22:41:59:230 ==>> 检测【检测唤醒】
2025-07-31 22:41:59:257 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:41:59:588 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[D][05:19:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:33][FCTY]DeviceID    = ***************
[D][05:19:33][FCTY]HardwareID  = 867222087518488
[D][05:19:33][FCTY]MoBikeID    = 9999999999
[D][05:19:33][FCTY]LockID      = FFFFFFFFFF
[D][05:19:33][FCTY]BLEFWVersion= 105
[D][05:19:33][FCTY]BLEMacAddr   = E4D7E5FEF2C9
[D][05:19:33][FCTY]Bat         = 3904 mv
[D][05:19:33][FCTY]Current     = 0 ma
[D][05:19:33][FCTY]VBUS        = 2600 mv
[D][05:19:33][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:33][FCTY]Ext battery vol = 32, adc = 1302
[D][05:19:33][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 0 mv
[D][05:19:33][FCTY]Bike Type flag is invalied
[D][05:19:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:33][FCTY]CA

2025-07-31 22:41:59:648 ==>> T1_GNSS_VERSION = V3465b5b1
[D][05:19:33][FCTY]Bat1         = 3846 mv
[D][05:19:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[E][05:19:33][COMM]1x1 rx timeout
[E][05:19:33][COMM]1x1 tp timeout
[E][05:19:33][COMM]1x1 error -3.
[D][05:19:33][COMM]Main Task receive event:28 finished processing


2025-07-31 22:41:59:753 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:41:59:763 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:41:59:785 ==>> 检测【关机】
2025-07-31 22:41:59:804 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:41:59:858 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           :34][COMM]bat msg 024E loss. last_tick:104015. cur_tick:105031. period:100. j,i:15 68
[D][05:19:34][COMM]bat msg 024F loss. last_tick:104015. cur_tick:105031. period:100. j,i:16 69
[D][05:19:34][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 105032
[D][05:19:34][COMM]CAN message bat fault change: 0x00000000->0x0001802E 105032

2025-07-31 22:41:59:888 ==>> 
[D][05:19:34][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 105033


2025-07-31 22:41:59:978 ==>> [W][05:19:34][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 22:42:00:083 ==>> [D][05:19:34][COMM]arm_hub_enable: hub power: 0
[D][05:19:34][HSDK]hexlog index save 0 3328 200 @ 0 : 0
[D][05:19:34][HSDK]write save hexlog index [0]
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:42:00:173 ==>> [D][05:19:34][COMM]msg 0222 loss. last_tick:104015. cur_tick:105606. period:150
[D][05:19:34][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 105608


2025-07-31 22:42:00:353 ==>> [D][05:19:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:0------------
[D][05:19:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:42:00:792 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:42:01:177 ==>>                                                                                                                  -----------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]more than the number of battery plugs
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:34][COMM]Bat auth off fail, error:-1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:34][COMM]read file, len:10800, num:3
[D][05:19:

2025-07-31 22:42:01:282 ==>> 34][COMM]--->crc16:0xb8a
[D][05:19:34][COMM]read file success
[D][05:19:34][COMM]accel parse set 1
[D][05:19:34][COMM][Audio]mon:9,05:19:34
[D][05:19:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:34][COMM]Main Task receive event:65
[D][05:19:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:34][COMM]Main Task receive event:65 finished processing
[D][05:19:34][COMM]Main Task receive event:66
[D][05:19:34][COMM]Try to Auto Lock Bat
[D][05:19:34][COMM]Main Task receive event:66 finished processing
[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:34][COMM]Main Task receive event:60
[D][05:19:34][COMM]smart_helmet_vol=255,255
[D][05:1

2025-07-31 22:42:01:387 ==>> 9:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get soh error
[E][05:19:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:34][COMM]report elecbike
[W][05:19:34][PROT]remove success[1629955174],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:34][COMM]Main Task receive event:60 finished processing
[D][05:19:34][COMM]Receive Bat Lock cmd 0
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]Main Task receive event:61
[D][05:19:34][COMM][D301]:type:3, trace id:280
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:34][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:34][PROT]index:0
[D][05:19:34][PROT]is_send:1
[D][05:19:34][PROT]sequence_num:10
[D][05:19:34][PROT]retry_timeout:0
[D][05:19:34][PROT]retry_times:3
[D][05:19:34][PROT]send_path:0x3
[D][05:19:34][

2025-07-31 22:42:01:492 ==>> PROT]msg_type:0x5d03
[D][05:19:34][PROT]===========================================================
[W][05:19:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955174]
[D][05:19:34][PROT]===========================================================
[D][05:19:34][PROT]Sending traceid[999999999990000B]
[D][05:19:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:34][SAL ]open socket ind id[4], rst[0]
[D][05:19:34][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:34][SAL ]Cellular task submsg id[8]
[D][05:19:34][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:34][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:34][COMM]id[], hw[000
[D][05:19:34][COMM]get mcMaincircuitVolt error
[D][05:19:34]

2025-07-31 22:42:01:597 ==>> [COMM]get mcSubcircuitVolt error
[D][05:19:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get bat work mode err
[W][05:19:34][PROT]remove success[1629955174],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:34][COMM]Main Task receive event:61 finished processing
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:34][CAT1]gsm read msg sub id: 8
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:34][COMM]f:[ec800m_aud

2025-07-31 22:42:01:702 ==>> io_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:34][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:34][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:35][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:35][CAT1]<<< 
+CME ERROR: 100

[D][05:19:35][COMM]read battery soc:255
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:35][COMM]Power Off
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:35][COMM]f:[ec800m_audio_play_process].l:[991]. s

2025-07-31 22:42:01:792 ==>> end ret: 0
[D][05:19:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:35][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:35][COMM]arm_hub_enable: hub power: 0
[D][05:19:35][HSDK]hexlog index save 0 3328 200 @ 0 : 0
[D][05:19:35][HSDK]write save hexlog index [0]
[D][05:19:35][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:35][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
                              

2025-07-31 22:42:01:856 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:42:01:864 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:42:01:878 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:42:01:903 ==>> 5A A5 02 5A A5 


2025-07-31 22:42:02:002 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:42:02:107 ==>> [D][05:19:36][FCTY]get_ext_48v_vol retry i = 0,

2025-07-31 22:42:02:146 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:42:02:156 ==>> 检测【检测小电池关机电流】
2025-07-31 22:42:02:165 ==>> 开始小电池电流采样
2025-07-31 22:42:02:198 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:42:02:228 ==>> volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:36][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 22:42:02:259 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:42:03:261 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:42:03:307 ==>> CurrentBattery:ƽ��:68.79

2025-07-31 22:42:03:772 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:42:03:781 ==>> 【检测小电池关机电流】通过,【68.79uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:42:04:189 ==>> MES过站成功
2025-07-31 22:42:04:197 ==>> #################### 【测试结束】 ####################
2025-07-31 22:42:04:223 ==>> 关闭5V供电
2025-07-31 22:42:04:236 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:42:04:304 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:42:05:232 ==>> 关闭5V供电成功
2025-07-31 22:42:05:244 ==>> 关闭33V供电
2025-07-31 22:42:05:274 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:42:05:310 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:42:06:236 ==>> 关闭33V供电成功
2025-07-31 22:42:06:250 ==>> 关闭3.7V供电
2025-07-31 22:42:06:275 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:42:06:311 ==>> 6A A6 02 A6 6A 


2025-07-31 22:42:06:401 ==>> Battery OFF
OVER 150


2025-07-31 22:42:07:225 ==>>  

