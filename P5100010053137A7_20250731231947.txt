2025-07-31 23:19:47:289 ==>> MES查站成功:
查站序号:P5100010053137A7验证通过
2025-07-31 23:19:47:305 ==>> 扫码结果:P5100010053137A7
2025-07-31 23:19:47:308 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:19:47:311 ==>> 测试参数版本:2024.10.11
2025-07-31 23:19:47:314 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:19:47:316 ==>> 检测【打开透传】
2025-07-31 23:19:47:318 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:19:47:431 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:19:47:659 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:19:47:679 ==>> 检测【检测接地电压】
2025-07-31 23:19:47:683 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:19:47:829 ==>> 1A A1 40 00 00 
Get AD_V22 236mV
OVER 150


2025-07-31 23:19:47:968 ==>> 【检测接地电压】通过,【236mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:19:47:970 ==>> 检测【打开小电池】
2025-07-31 23:19:47:973 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:19:48:026 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:19:48:240 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:19:48:244 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:19:48:248 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:19:48:328 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:19:48:512 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:19:48:518 ==>> 检测【等待设备启动】
2025-07-31 23:19:48:522 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:19:48:776 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:19:48:971 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:19:49:539 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:19:49:615 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:19:49:660 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:19:50:070 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:19:50:543 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:19:50:601 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:19:50:604 ==>> 检测【产品通信】
2025-07-31 23:19:50:607 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:19:50:813 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:19:50:876 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:19:50:878 ==>> 检测【初始化完成检测】
2025-07-31 23:19:50:881 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:19:51:147 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:19:51:192 ==>>                                                              

2025-07-31 23:19:51:405 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:19:51:408 ==>> 检测【关闭大灯控制1】
2025-07-31 23:19:51:409 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:19:51:615 ==>> [D][05:17:51][COMM]2627 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:51:678 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:19:51:680 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:19:51:681 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:19:51:720 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2

2025-07-31 23:19:51:765 ==>> ],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:19:51:855 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:19:51:979 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:19:51:981 ==>> 检测【关闭仪表供电】
2025-07-31 23:19:51:983 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:19:52:222 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:19:52:287 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:19:52:313 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:19:52:315 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:19:52:480 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:19:52:583 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:19:52:587 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:19:52:588 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:19:52:590 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error

2025-07-31 23:19:52:615 ==>> :[-1]. goto init


2025-07-31 23:19:52:781 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:19:52:894 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:19:52:896 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:19:52:899 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:19:53:115 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:19:53:163 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:19:53:165 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:19:53:166 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:19:53:220 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:19:53:310 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
[D][05:17:53][COMM]read battery soc:255


2025-07-31 23:19:53:433 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:19:53:435 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:19:53:437 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:19:53:520 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:19:53:625 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:53:705 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:19:53:707 ==>> 该项需要延时执行
2025-07-31 23:19:54:171 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 23:19:54:656 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:54:911 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:19:55:384 ==>>                                                                                                                                                            Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:

2025-07-31 23:19:55:489 ==>> 17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PR

2025-07-31 23:19:55:594 ==>> OT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:

2025-07-31 23:19:55:654 ==>> 55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:19:55:684 ==>>                                                                                                  

2025-07-31 23:19:55:909 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:19:56:675 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:57:304 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:19:57:673 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:57:719 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:19:57:735 ==>> 检测【33V输入电压ADC】
2025-07-31 23:19:57:737 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:19:58:033 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:13 mv
[D][05:17:57][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:19:58:249 ==>> 【33V输入电压ADC】通过,【32779mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:19:58:251 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:19:58:253 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:19:58:335 ==>> 1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1994mV
Get AD_V7 1086mV
OVER 150


2025-07-31 23:19:58:520 ==>> 【TP7_VCC3V3(ADV2)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:58:524 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:19:58:539 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:58:541 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:19:58:543 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 23:19:58:557 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:19:58:561 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:19:58:578 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:19:58:581 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:19:58:602 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:19:58:604 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:19:58:684 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:19:58:729 ==>> 1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1993mV
Get AD_V7 1086mV
OVER 150


2025-07-31 23:19:58:888 ==>> 【TP7_VCC3V3(ADV2)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:58:890 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:19:58:907 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:58:911 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:19:58:939 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 23:19:58:941 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:19:58:944 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:19:58:958 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:19:58:960 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:19:58:981 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:19:58:983 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:19:59:045 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020
1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1657mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1993mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:19:59:262 ==>> 【TP7_VCC3V3(ADV2)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:59:265 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:19:59:281 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:19:59:283 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:19:59:286 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 23:19:59:299 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:19:59:302 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:19:59:318 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:19:59:320 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:19:59:329 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:19:59:340 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:19:59:342 ==>> 检测【打开WIFI(1)】
2025-07-31 23:19:59:360 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:19:59:480 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:19:59:628 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:19:59:631 ==>> 检测【清空消息队列(1)】
2025-07-31 23:19:59:637 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:20:00:091 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:1

2025-07-31 23:20:00:195 ==>> 7:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION

2025-07-31 23:20:00:240 ==>>  i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 23:20:00:427 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:20:00:431 ==>> 检测【打开GPS(1)】
2025-07-31 23:20:00:433 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:20:00:620 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 23:20:00:680 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:20:00:709 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:20:00:712 ==>> 检测【打开GSM联网】
2025-07-31 23:20:00:715 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:20:00:907 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 23:20:00:991 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:20:00:994 ==>> 检测【打开仪表供电1】
2025-07-31 23:20:00:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:20:01:211 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:20:01:275 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:20:01:278 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:20:01:281 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:20:01:316 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 23:20:01:421 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:20:01:545 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:20:01:547 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:20:01:549 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:20:01:707 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33572]


2025-07-31 23:20:01:812 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 23:20:01:816 ==>> 【读取主控ADC采集的仪表电压】通过,【33572mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:20:01:818 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:20:01:820 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:20:02:401 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087631265

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539523

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05

2025-07-31 23:20:02:431 ==>> :18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:20:02:605 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:20:02:609 ==>> 检测【AD_V20电压】
2025-07-31 23:20:02:613 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:20:02:717 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:20:02:822 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150


2025-07-31 23:20:02:912 ==>> 本次取值间隔时间:187ms
2025-07-31 23:20:02:931 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:20:02:934 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:20:02:937 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:20:03:017 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:20:03:213 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:20:03:216 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:20:03:220 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:20:03:323 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 23:20:03:428 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:20:03:493 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:20:03:496 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:20:03:498 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:20:03:623 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:20:03:683 ==>> [D][05:18:03][COMM]14729 imu init OK


2025-07-31 23:20:03:776 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:20:03:779 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:20:03:781 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:20:04:045 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:20:04:304 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:20:04:308 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:20:04:311 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:20:04:650 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, po

2025-07-31 23:20:04:680 ==>> wer:1
[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 23:20:04:834 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:20:04:837 ==>> 检测【AD_V21电压】
2025-07-31 23:20:04:841 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:20:04:950 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK

2025-07-31 23:20:04:995 ==>> =FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

1A A1 20 00 00 
Get AD_V21 1641mV
OVER 150


2025-07-31 23:20:05:085 ==>> 本次取值间隔时间:243ms
2025-07-31 23:20:05:103 ==>> 【AD_V21电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:20:05:106 ==>> 检测【关闭仪表供电2】
2025-07-31 23:20:05:108 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:20:05:315 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:20:05:377 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:20:05:381 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:20:05:385 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:20:05:406 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 23:20:05:646 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:20:05:907 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:20:05:910 ==>> 检测【打开AccKey2供电】
2025-07-31 23:20:05:914 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:20:06:102 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:20:06:180 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:20:06:183 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:20:06:186 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:06:300 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:20:06:619 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,42,39,,,41,34,,,40,60,,,40,1*71

$GBGSV,2,2,08,23,,,38,41,,,37,25,,,18,59,,,39,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1516.435,1516.435,48.750,2097152,2097152,2097152*41

[D][05:18:06][CAT1]<<< 
OK

[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:06][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:18:06][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:06][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:06][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:06][COMM]adc read battery ts volt:10 mv
[D][05:18:06][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:06][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][CAT1]opened : 0, 0
[D][05:18:06][SAL ]Cellular task submsg id[68]
[D][05:18:06][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:06][SAL ]socket

2025-07-31 23:20:06:716 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33083mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:20:06:722 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:20:06:727 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:20:06:732 ==>>  connect ind. id[4], rst[3]
[D][05:18:06][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:06][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:06][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:06][M2M ]g_m2m_is_idle become true
[D][05:18:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:06][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

                                                                                            

2025-07-31 23:20:06:905 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:20:07:004 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:20:07:007 ==>> 该项需要延时执行
2025-07-31 23:20:07:440 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,40,,,42,39,,,41,34,,,40,59,,,40,1*77

$GBGSV,4,2,13,60,,,39,7,,,39,23,,,38,41,,,38,1*47

$GBGSV,4,3,13,25,,,35,5,,,31,1,,,37,2,,,37,1*46

$GBGSV,4,4,13,4,,,37,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1587.858,1587.858,50.800,2097152,2097152,2097152*42

[D][05:18:07][COMM]read battery soc:255


2025-07-31 23:20:08:459 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,41,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,4,2,16,59,,,39,60,,,39,7,,,39,23,,,38,1*4A

$GBGSV,4,3,16,41,,,38,1,,,37,25,,,37,43,,,35,1*4F

$GBGSV,4,4,16,32,,,34,4,,,33,2,,,32,5,,,31,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1536.556,1536.556,49.163,2097152,2097152,2097152*46



2025-07-31 23:20:09:492 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,152013.330,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,41,39,,,40,34,,,40,3,,,40,1*40

$GBGSV,5,2,18,59,,,39,60,,,39,7,,,39,25,,,39,1*42

$GBGSV,5,3,18,23,,,38,41,,,38,16,,,38,43,,,37,1*72

$GBGSV,5,4,18,1,,,36,32,,,34,4,,,32,2,,,32,1*4A

$GBGSV,5,5,18,5,,,31,24,,,18,1*47

$GBRMC,152013.330,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152013.330,0.000,1499.533,1499.533,48.092,2097152,2097152,2097152*52



2025-07-31 23:20:09:657 ==>> $GBGGA,152013.530,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,41,39,,,40,34,,,40,3,,,40,1*4B

$GBGSV,5,2,20,59,,,39,60,,,39,7,,,39,25,,,39,1*49

$GBGSV,5,3,20,41,,,39,23,,,38,16,,,38,43,,,37,1*78

$GBGSV,5,4,20,1,,,36,32,,,34,4,,,32,2,,,32,1*41

$GBGSV,5,5,20,5,,,31,24,,,30,11,,,43,12,,,36,1*47

$GBRMC,152013.530,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152013.530,0.000,1529.378,1529.378,48.953,2097152,2097152,2097152*50



2025-07-31 23:20:10:010 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:20:10:014 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:20:10:019 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:10:343 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:10][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:10][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:10][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:10][COMM]adc read battery ts volt:15 mv
[D][05:18:10][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:20:10:546 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:20:10:549 ==>> 检测【打开AccKey1供电】
2025-07-31 23:20:10:551 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:20:10:663 ==>> $GBGGA,152014.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,6,2,24,25,,,40,11,,,39,59,,,39,60,,,39,1*77

$GBGSV,6,3,24,7,,,39,41,,,39,43,,,39,23,,,38,1*40

$GBGSV,6,4,24,16,,,38,1,,,36,10,,,35,12,,,34,1*49

$GBGSV,6,5,24,32,,,34,24,,,34,2,,,33,4,,,32,1*73

$GBGSV,6,6,24,5,,,32,6,,,32,9,,,31,33,,,37,1*4C

$GBRMC,152014.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152014.510,0.000,1523.162,1523.162,48.747,2097152,2097152,2097152*5E



2025-07-31 23:20:10:753 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:20:10:822 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:20:10:826 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:20:10:830 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:20:10:921 ==>> 1A A1 00 40 00 
Get AD_V14 2551mV
OVER 150


2025-07-31 23:20:11:087 ==>> 原始值:【2551】, 乘以分压基数【2】还原值:【5102】
2025-07-31 23:20:11:106 ==>> 【读取AccKey1电压(ADV14)前】通过,【5102mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:20:11:109 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:20:11:112 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:11:475 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:11][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:11][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:11][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:11][COMM]adc read battery ts volt:10 mv
[D][05:18:11][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:11][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:11][COMM]read battery soc:255


2025-07-31 23:20:11:645 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5537mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:20:11:649 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:20:11:651 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:20:11:671 ==>> $GBGGA,152015.510,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,6,2,24,25,,,40,59,,,40,11,,,39,60,,,39,1*79

$GBGSV,6,3,24,7,,,39,41,,,39,43,,,39,23,,,38,1*40

$GBGSV,6,4,24,16,,,38,1,,,36,10,,,36,33,,,34,1*49

$GBGSV,6,5,24,32,,,34,24,,,34,6,,,34,12,,,33,1*46

$GBGSV,6,6,24,2,,,33,4,,,32,5,,,32,9,,,32,1*7B

$GBRMC,152015.510,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152015.510,0.000,1525.334,1525.334,48.810,2097152,2097152,2097152*52



2025-07-31 23:20:11:807 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:20:11:915 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:20:11:918 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:20:11:921 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:20:12:019 ==>> 1A A1 00 40 00 
Get AD_V14 2548mV
OVER 150


2025-07-31 23:20:12:170 ==>> 原始值:【2548】, 乘以分压基数【2】还原值:【5096】
2025-07-31 23:20:12:197 ==>> 【读取AccKey1电压(ADV14)后】通过,【5096mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:20:12:202 ==>> 检测【打开WIFI(2)】
2025-07-31 23:20:12:204 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:20:12:448 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:20:12:674 ==>> $GBGGA,152016.510,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,6,2,24,25,,,40,59,,,40,43,,,40,11,,,39,1*76

$GBGSV,6,3,24,60,,,39,7,,,39,41,,,39,23,,,38,1*41

$GBGSV,6,4,24,16,,,38,1,,,36,10,,,36,24,,,35,1*4E

$GBGSV,6,5,24,6,,,35,33,,,34,32,,,34,2,,,33,1*70

$GBGSV,6,6,24,9,,,33,12,,,32,4,,,32,5,,,32,1*4A

$GBRMC,152016.510,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152016.510,0.000,1530.515,1530.515,48.974,2097152,2097152,2097152*52



2025-07-31 23:20:12:723 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:20:12:727 ==>> 检测【转刹把供电】
2025-07-31 23:20:12:729 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:20:12:903 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:20:12:997 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:20:13:001 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:20:13:005 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:20:13:102 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:20:13:225 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2400mV
OVER 150


2025-07-31 23:20:13:255 ==>> 原始值:【2400】, 乘以分压基数【2】还原值:【4800】
2025-07-31 23:20:13:274 ==>> 【读取AD_V15电压(前)】通过,【4800mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:20:13:278 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:20:13:288 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:20:13:376 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:20:13:407 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 23:20:13:512 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:20:13:617 ==>> $GBGGA,152017.510,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,6,2,24,25,,,40,59,,,39,43,,,39,11,,,39,1*76

$GBGSV,6,3,24,60,,,39,7,,,39,41,,,39,23

2025-07-31 23:20:13:662 ==>> ,,,38,1*41

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,24,,,35,1*4F

$GBGSV,6,5,24,6,,,35,33,,,34,32,,,34,9,,,34,1*7C

$GBGSV,6,6,24,2,,,33,12,,,32,4,,,32,5,,,31,1*42

$GBRMC,152017.510,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152017.510,0.000,1528.785,1528.785,48.917,2097152,2097152,2097152*56



2025-07-31 23:20:14:013 ==>> +WIFISCAN:4,0,F42A7D1297A3,-70
+WIFISCAN:4,1,74C330CCAB10,-72
+WIFISCAN:4,2,CC057790A641,-72
+WIFISCAN:4,3,CC057790A4A1,-79

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 23:20:14:318 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:20:14:427 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:20:14:488 ==>> [W][05:18:14][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:20:14:518 ==>> 1A A1 01 00 00 
Get AD_V16 2431mV
OVER 150


2025-07-31 23:20:14:579 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 23:20:14:624 ==>> $GBGGA,152018.510,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,

2025-07-31 23:20:14:640 ==>> 【读取AD_V16电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:20:14:646 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:20:14:670 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:14:683 ==>> ,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,40,34,,,40,3,,,40,1*4F

$GBGSV,7,2,25,25,,,40,59,,,40,43,,,39,11,,,39,1*78

$GBGSV,7,3,25,60,,,39,7,,,39,41,,,39,23,,,38,1*41

$GBGSV,7,4,25,16,,,38,10,,,37,1,,,36,24,,,35,1*4F

$GBGSV,7,5,25,6,,,35,33,,,34,32,,,34,9,,,34,1*7C

$GBGSV,7,6,25,2,,,33,12,,,32,4,,,32,5,,,32,1*41

$GBGSV,7,7,25,13,,,47,1*70

$GBRMC,152018.510,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152018.510,0.000,1532.238,1532.238,49.025,2097152,2097152,2097152*50



2025-07-31 23:20:14:939 ==>> [D][05:18:14][GNSS]recv submsg id[3]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:14][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:14][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:14][COMM]adc read battery ts volt:14 mv
[D][05:18:14][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3088  volt:5428 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:20:15:194 ==>> 【转刹把供电电压(主控ADC)】通过,【5428mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:20:15:198 ==>> 检测【转刹把供电电压】
2025-07-31 23:20:15:201 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:15:552 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:15][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:15][COMM]adc read battery ts volt:12 mv
[D][05:18:15][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3088  volt:5428 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]read battery soc:255
[D][05:18:15][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 23:20:15:657 ==>>                                                                                                                                                       0,3,,,39,43,,,39,11,,,39,1*42

$GBGSV,7,3,25,60,,,39,7,,,39,41,,,39,23,,,38,1*41

$GBGSV,7,4,25,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,7,5,25,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,7,6,25,32,,,33,12,,,33,4,,,32,5,,,32,1*73

$GBGSV,7,7,25,13,,,39,1*79

$GBRMC,152019.510,V,,,,,,,,0.0,

2025-07-31 23:20:15:687 ==>> E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152019.510,0.000,1533.959,1533.959,49.074,2097152,2097152,2097152*55



2025-07-31 23:20:15:724 ==>> 【转刹把供电电压】通过,【5428mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:20:15:727 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:20:15:730 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:20:15:884 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:20:15:993 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:20:15:999 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:20:16:008 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:20:16:099 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:20:16:188 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:20:16:203 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:20:16:219 ==>> 00 00 00 80 00 
head err!


2025-07-31 23:20:16:309 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:20:16:416 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:20:16:421 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:20:16:521 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:20:16:647 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:20:16:651 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:20:16:656 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:20:16:672 ==>> $GBGGA,152020.510,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,7,2,25,3,,,40,59,,,39,43,,,39,11,,,39,1*42

$GBGSV,7,3,25,60,,,39,7,,,39,41,,,39,23,,,38,1*41

$GBGSV,7,4,25,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,7,5,25,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,7,6,25,32,,,33,12,,,33,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,13,,,36,1*76

$GBRMC,152020.510,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152020.510,0.000,1530.512,1530.512,48.972,2097152,2097152,2097152*51

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:20:16:763 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:20:16:822 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:20:16:885 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:20:16:889 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:20:16:899 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:20:17:019 ==>> 3A A3 03 01 A3 


2025-07-31 23:20:17:124 ==>> ON_OUT3
OVER 150


2025-07-31 23:20:17:158 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:20:17:163 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:20:17:168 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:20:17:230 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:20:17:411 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 23:20:17:430 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:20:17:435 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:20:17:445 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:20:17:517 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:20:17:623 ==>> $GBGGA,152021.510,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,7,2,25,3,,,40,59,,,39,43,,,39,60,,,39,1*44

$GBGSV,7,3,25,7,,,39,41,,,39,11,,,38,23,,,38,1*46

$GBGSV,7,4,25,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$

2025-07-31 23:20:17:668 ==>> GBGSV,7,5,25,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,7,6,25,32,,,33,12,,,33,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,13,,,,1*73

$GBRMC,152021.510,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152021.510,0.000,1527.053,1527.053,48.857,2097152,2097152,2097152*56



2025-07-31 23:20:17:701 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:20:17:706 ==>> 检测【左刹电压测试1】
2025-07-31 23:20:17:711 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:18:030 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:17][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:17][COMM]adc read left brake adc:1728  volt:2278 mv
[D][05:18:17][COMM]adc read right brake adc:1725  volt:2274 mv
[D][05:18:17][COMM]adc read throttle adc:1731  volt:2282 mv
[D][05:18:17][COMM]adc read battery ts volt:10 mv
[D][05:18:17][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:17][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:20:18:231 ==>> 【左刹电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 23:20:18:234 ==>> 检测【右刹电压测试1】
2025-07-31 23:20:18:253 ==>> 【右刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 23:20:18:256 ==>> 检测【转把电压测试1】
2025-07-31 23:20:18:272 ==>> 【转把电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 23:20:18:277 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:20:18:291 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:20:18:319 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:20:18:550 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:20:18:557 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:20:18:581 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:20:18:669 ==>> $GBGGA,152022.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,6,2,24,3,,,40,59,,,39,43,,,39,60,,,39,1*44

$GBGSV,6,3,24,7,,,39,41,,,39,11,,,39,23,,,38,1*47

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,31,1*70

$GBRMC,152022.510,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152022.510,0.000,1532.236,1532.236,49.023,2097152,2097152,2097152*5F

3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:20:18:858 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:20:18:862 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:20:18:868 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:20:18:928 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:20:19:149 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:20:19:152 ==>> 检测【左刹电压测试2】
2025-07-31 23:20:19:157 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:20:19:505 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:19][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:19][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:19][COMM]adc read battery ts volt:11 mv
[D][05:18:19][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:19][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:19][COMM]read battery soc:255


2025-07-31 23:20:19:610 ==>> $GBGGA,152023.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,59,,,40,1*70

2025-07-31 23:20:19:670 ==>> 

$GBGSV,6,2,24,25,,,39,3,,,39,43,,,39,60,,,39,1*41

$GBGSV,6,3,24,7,,,39,41,,,39,11,,,39,23,,,38,1*47

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,31,1*70

$GBRMC,152023.510,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152023.510,0.000,1530.506,1530.506,48.966,2097152,2097152,2097152*57



2025-07-31 23:20:19:698 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 23:20:19:702 ==>> 检测【右刹电压测试2】
2025-07-31 23:20:19:787 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 23:20:19:792 ==>> 检测【转把电压测试2】
2025-07-31 23:20:19:873 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 23:20:19:877 ==>> 检测【晶振检测】
2025-07-31 23:20:19:883 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:20:19:985 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 23:20:20:176 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:20:20:180 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:20:20:187 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:20:20:338 ==>> 1A A1 00 00 FC 
Get AD_V2 1644mV
Get AD_V3 1656mV
Get AD_V4 1651mV
Get AD_V5 2765mV
Get AD_V6 1993mV
Get AD_V7 1086mV
OVER 150


2025-07-31 23:20:20:464 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:20:20:469 ==>> 检测【检测BootVer】
2025-07-31 23:20:20:474 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:20:820 ==>> $GBGGA,152024.510,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,59,,,40,1*70

$GBGSV,6,2,24,25,,,40,43,,,40,3,,,39,60,,,39,1*41

$GBGSV,6,3,24,7,,,39,41,,,39,11,,,39,23,,,38,1*47

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,32,1*73

$GBRMC,152024.510,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152024.510,0.000,1535.688,1535.688,49.131,2097152,2097152,2097152*5B

[W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071539523
[D][05:18:20][FCTY]HardwareID  = 867222087631265
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = C821C4F48795


2025-07-31 23:20:20:910 ==>> 
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1296
[D][05:18:20][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 202 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3796 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:20:21:001 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:20:21:006 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:20:21:012 ==>> 检测【检测固件版本】
2025-07-31 23:20:21:026 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:20:21:029 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:20:21:033 ==>> 检测【检测蓝牙版本】
2025-07-31 23:20:21:049 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:20:21:053 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:20:21:057 ==>> 检测【检测MoBikeId】
2025-07-31 23:20:21:078 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:20:21:083 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:20:21:086 ==>> 检测【检测蓝牙地址】
2025-07-31 23:20:21:092 ==>> 取到目标值:C821C4F48795
2025-07-31 23:20:21:100 ==>> 【检测蓝牙地址】通过,【C821C4F48795】符合目标值【】要求!
2025-07-31 23:20:21:103 ==>> 提取到蓝牙地址:C821C4F48795
2025-07-31 23:20:21:107 ==>> 检测【BOARD_ID】
2025-07-31 23:20:21:119 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:20:21:122 ==>> 检测【检测充电电压】
2025-07-31 23:20:21:139 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:20:21:143 ==>> 检测【检测VBUS电压1】
2025-07-31 23:20:21:158 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:20:21:162 ==>> 检测【检测充电电流】
2025-07-31 23:20:21:183 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:20:21:190 ==>> 检测【检测IMEI】
2025-07-31 23:20:21:200 ==>> 取到目标值:867222087631265
2025-07-31 23:20:21:205 ==>> 【检测IMEI】通过,【867222087631265】符合目标值【】要求!
2025-07-31 23:20:21:209 ==>> 提取到IMEI:867222087631265
2025-07-31 23:20:21:216 ==>> 检测【检测IMSI】
2025-07-31 23:20:21:234 ==>> 取到目标值:460130071539523
2025-07-31 23:20:21:238 ==>> 【检测IMSI】通过,【460130071539523】符合目标值【】要求!
2025-07-31 23:20:21:242 ==>> 提取到IMSI:460130071539523
2025-07-31 23:20:21:260 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:20:21:264 ==>> 取到目标值:460130071539523
2025-07-31 23:20:21:269 ==>> 【校验网络运营商(移动)】通过,【460130071539523】符合目标值【】要求!
2025-07-31 23:20:21:289 ==>> 检测【打开CAN通信】
2025-07-31 23:20:21:292 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:20:21:333 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:20:21:423 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 23:20:21:549 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:20:21:553 ==>> 检测【检测CAN通信】
2025-07-31 23:20:21:558 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:20:21:678 ==>> $GBGGA,152025.510,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,6,2,24,59,,,39,43,,,39,3,,,39,60,,,39,1*4A

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,37,10,,,37,1,,,36,6,,,36,1*73

$GBGSV,6,5,24,24,,,34,33,,,34,9,,,34,2,,,34,1*7E

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,31,1*70

$GBRMC,152025.510,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152025.510,0.000,1523.594,1523.594,48.742,2097152,2097152,2097152*59

can send success


2025-07-31 23:20:21:708 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:20:21:768 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32786
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:20:21:828 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:20:21:832 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:20:21:859 ==>> 检测【关闭CAN通信】
2025-07-31 23:20:21:864 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:20:21:889 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:20:21:933 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:20:22:101 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:20:22:105 ==>> 检测【打印IMU STATE】
2025-07-31 23:20:22:108 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:20:22:330 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:20:22:379 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:20:22:383 ==>> 检测【六轴自检】
2025-07-31 23:20:22:386 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:20:22:705 ==>> $GBGGA,152026.510,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,6,2,24,60,,,40,59,,,39,43,,,39,3,,,39,1*44

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,37,10,,,37,1,,,36,6,,,36,1*73

$GBGSV,6,5,24,24,,,34,33,,,34,9,,,34,2,,,33,1*79

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,31,1*70

$GBRMC,152026.510,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152026.510,0.000,1523.598,1523.598,48.747,2097152,2097152,2097152*5F

[W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:20:23:418 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 23:20:23:675 ==>> $GBGGA,152027.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,6,2,24,3,,,40,60,,,39,59,,,39,43,,,39,1*44

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,37,10,,,37,1,,,36,6,,,36,1*73

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,6,6,24,32,,,33,12,,,33,5,,,32,4,,,32,1*73

$GBRMC,152027.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152027.510,0.000,1528.772,1528.772,48.904,2097152,2097152,2097152*57



2025-07-31 23:20:24:315 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:20:24:420 ==>> [D][05:18:24][COMM]Main 

2025-07-31 23:20:24:465 ==>> Task receive event:142
[D][05:18:24][COMM]###### 35471 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-10,-1,4050]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 23:20:24:675 ==>> $GBGGA,152028.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,6,2,24,43,,,40,3,,,39,60,,,39,59,,,39,1*44

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,32,,,33,5,,,32,4,,,32,1*74

$GBRMC,152028.510,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152028.510,0.000,1533.955,1533.955,49.071,2097152,2097152,2097152*52



2025-07-31 23:20:24:715 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:20:24:719 ==>> 检测【打印IMU STATE2】
2025-07-31 23:20:24:724 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:20:24:919 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:20:25:008 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:20:25:016 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:20:25:029 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:20:25:129 ==>> 5A A5 02 5A A5 


2025-07-31 23:20:25:219 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:20:25:283 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:20:25:288 ==>> 检测【检测VBUS电压2】
2025-07-31 23:20:25:294 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:25:325 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:25][

2025-07-31 23:20:25:384 ==>> FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 13


2025-07-31 23:20:25:748 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539523
[D][05:18:25][FCTY]HardwareID  = 867222087631265
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C821C4F48795
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11600 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 8, adc = 337
[D][05:18:25][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 25 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Ba

2025-07-31 23:20:25:811 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:25:838 ==>> t1         = 3796 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,152029.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,6,2,24,3,,,40,43,,,39,60,,,39,59,,,39,1*44

$GBGSV,6,3,24,7,,,39,41,,,39,11,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,36,6,,,36,1*7C

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,12,,,34,1*4E

$GBGSV,6,6,24,2,,,33,32,,,33,5,,,32,4,,,32,1*42

$GBRMC,152029.510,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152029.510,0.000,1532.231,1532.231,49.018,2097152,2097152,2097152*5C

[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:20:26:183 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539523
[D][05:18:26][FCTY]HardwareID  = 867222087631265
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C821C4F48795
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11600 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 179
[D][05:18:26][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26

2025-07-31 23:20:26:228 ==>> ][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3796 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:20:26:336 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:26:747 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539523
[D][05:18:26][FCTY]HardwareID  = 867222087631265
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C821C4F48795
[D][05:18:26][FCTY]Bat         = 3824 mv
[D][05:18:26][FCTY]Current     = 100 ma
[D][05:18:26][FCTY]VBUS        = 11600 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 160
[D][05:18:26][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 126 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D]

2025-07-31 23:20:26:852 ==>> [05:18:26][FCTY]Bat1         = 3796 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
$GBGGA,152030.510,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,6,2,24,3,,,39,43,,,39,60,,,39,59,,,39,1*4A

$GBGSV,6,3,24,7,,,39,41,,,39,11,,,38,23,,,38,1*46

$GBGSV,6,4,24,16,,,37,10,,,37,1,,,36,6,,,36,1*73

$GBGSV,6,5,24,24,,,35,33,,,34,9,,,34,12,,,34,1*4E

$GBGSV,6,6,24,2,,,34,32,,,33,5,,,32,4,,,32,1*45

$GBRMC,152030.510,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152030.510,0.000,1528.767,1528.767,48.900,2097152,2097152,2097152*55

                                                                                                                                                                          

2025-07-31 23:20:26:871 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:27:228 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539523
[D][05:18:27][FCTY]HardwareID  = 867222087631265
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C821C4F48795
[D][05:18:27][FCTY]Bat         = 3824 mv
[D][05:18:27][FCTY]Current     = 100 ma
[D][05:18:27][FCTY]VBUS        = 11600 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 135
[D][05:18:27][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FC

2025-07-31 23:20:27:273 ==>> TY]Bat1         = 3796 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 23:20:27:396 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:20:27:792 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  7][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1

2025-07-31 23:20:27:897 ==>> 629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND


2025-07-31 23:20:28:002 ==>> 
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B51B0C2BC5CB1FBEFBCBA84F1727FAFF98B3E4BEC050F72EE19F89A728A9DBD157B4FF7A429FEABC7CAC6E53F5E15F7073DC488F3430793D682341B94AD7DCCE229A6963C5CD1128F3831435AAC06573093A56
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0

2025-07-31 23:20:28:107 ==>> ], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539523
[D][05:18:27][FCTY]HardwareID  = 867222087631265
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C821C4F48795
[D][05:18:27][FCTY]Bat         = 3824 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 112
[D][05:18:27][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 75 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_B

2025-07-31 23:20:28:185 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:20:28:190 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:20:28:198 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:20:28:217 ==>> OOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3796 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                           

2025-07-31 23:20:28:302 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:20:28:452 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 23
[D][05:18:28][COMM]read battery soc:255


2025-07-31 23:20:28:471 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:20:28:476 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:20:28:481 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:20:28:527 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:20:28:740 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:20:28:749 ==>> 检测【打开WIFI(3)】
2025-07-31 23:20:28:772 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:20:28:948 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:20:29:012 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:20:29:017 ==>> 检测【扩展芯片hw】
2025-07-31 23:20:29:025 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:20:29:221 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:20:29:284 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:20:29:289 ==>> 检测【扩展芯片boot】
2025-07-31 23:20:29:303 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:20:29:308 ==>> 检测【扩展芯片sw】
2025-07-31 23:20:29:324 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:20:29:330 ==>> 检测【检测音频FLASH】
2025-07-31 23:20:29:335 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:20:29:508 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:20:29:659 ==>> +WIFISCAN:4,0,CC057790A620,-60
+WIFISCAN:4,1,CC057790A621,-60
+WIFISCAN:4,2,CC057790A5C0,-75
+WIFISCAN:4,3,CC057790A5C1,-77

[D][05:18:29][CAT1]wifi scan report total[4]


2025-07-31 23:20:29:838 ==>> [D][05:18:29][GNSS]recv submsg id[3]


2025-07-31 23:20:29:943 ==>> [D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0


2025-07-31 23:20:29:988 ==>> 
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:20:30:684 ==>>                                                                                                                                          0][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audi

2025-07-31 23:20:30:789 ==>> o_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30]

2025-07-31 23:20:30:894 ==>> [COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][PROT]index:1
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE

2025-07-31 23:20:30:999 ==>> _WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05

2025-07-31 23:20:31:104 ==>> :18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_p

2025-07-31 23:20:31:179 ==>> rocess].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255


2025-07-31 23:20:32:465 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:20:32:827 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B32599E0F5F832DC5E66F6AF1A05C19CEA459C24612006F6B95

2025-07-31 23:20:32:902 ==>> 68F02874BACBBD522D5592413BA31F034CB2C72037F13AA0789347A828B4409AF2886E82264D9C6BF2F6B5CFBC969EE255506DC96F10DA27185
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 23:20:33:007 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:20:33:626 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 23:20:34:181 ==>> [D][05:18:34][COMM]45069 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:20:34:395 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:20:34:403 ==>> 检测【打开喇叭声音】
2025-07-31 23:20:34:425 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:20:34:483 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:20:35:140 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio

2025-07-31 23:20:35:217 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:20:35:226 ==>> 检测【打开大灯控制】
2025-07-31 23:20:35:251 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:20:35:258 ==>> _start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:204

2025-07-31 23:20:35:351 ==>> 8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]46080 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 23:20:35:441 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:20:35:492 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:20:35:498 ==>> 检测【关闭仪表供电3】
2025-07-31 23:20:35:507 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:20:35:713 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0


2025-07-31 23:20:35:776 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:20:35:781 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:20:35:787 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:20:35:989 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:20:36:057 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:20:36:063 ==>> 检测【读大灯电压】
2025-07-31 23:20:36:086 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:20:36:217 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[33224]


2025-07-31 23:20:36:339 ==>> 【读大灯电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:20:36:345 ==>> 检测【关闭大灯控制2】
2025-07-31 23:20:36:354 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:20:36:507 ==>> [D][05:18:36][COMM]read battery soc:255
[W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:20:36:618 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:20:36:625 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:20:36:646 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:20:36:814 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:20:36:894 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:20:36:900 ==>> 检测【打开WIFI(4)】
2025-07-31 23:20:36:909 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:20:37:136 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:20:37:220 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:20:37:226 ==>> 检测【EC800M模组版本】
2025-07-31 23:20:37:245 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:20:37:412 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:20:37:668 ==>> [D][05:18:37][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 132
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:20:37:750 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:20:37:760 ==>> 检测【配置蓝牙地址】
2025-07-31 23:20:37:778 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:20:37:955 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C821C4F48795>】
2025-07-31 23:20:38:073 ==>> [D][05:18:37][PROT]CLEAN,SEND:1
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B35A9714D0A8DA04F1685E482D6003E9669902AA6704A587317F9F2A5803355DE8BEC3244BA21752C29230427910502B958E34F17C230F2B9CDDAD792ECAA

2025-07-31 23:20:38:148 ==>> 69755D3DD28A58E96E7F9B539157A70747424856D
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[W][05:18:37][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 23:20:38:224 ==>> recv ble 1
recv ble 2
ble set mac ok :c8,21,c4,f4,87,95
enable filters ret : 0

2025-07-31 23:20:38:483 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 23:20:38:489 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:20:38:496 ==>> 检测【BLETEST】
2025-07-31 23:20:38:516 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:20:38:648 ==>> 4A A4 01 A4 4A 
[D][05:18:38][COMM]49674 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:20:38:739 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C821C4F48795*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C821C4F4879599999OVER 150


2025-07-31 23:20:39:537 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:20:39:545 ==>> 该项需要延时执行
2025-07-31 23:20:39:663 ==>> [D][05:18:39][COMM]50685 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:20:40:262 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:20:40:503 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:20:40:669 ==>> [D][05:18:40][COMM]51696 imu init OK


2025-07-31 23:20:42:129 ==>> +WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,CC057790A621,-61
+WIFISCAN:4,2,CC057790A5C0,-77
+WIFISCAN:4,3,CC057790A5C1,-77

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 23:20:42:506 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 23:20:42:907 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 23:20:43:288 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B37320EFFF79DC28C304B972060D6061BF2531A09BAE190AAA8002FF18465E

2025-07-31 23:20:43:363 ==>> 834C47988FFB25DC842D86D4FD58E2A8390332D3D171DD087969B60B87D661FE0EAE8E575D7928EB6049863F662B736451DD4A08
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 23:20:44:498 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:20:46:528 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:20:48:520 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Data To Ser

2025-07-31 23:20:48:595 ==>> ver[198][201] ... ->:
0063B982113311331133113311331B88B51FAB98DA2B189C973CC37EA69BD525CDB84FD58126538E6B777EF64F5FBBB8DB5824FCFCA4DBAABD1B0970E9EF2F0D09320ECCC075D234D5C5EFF830297A414AB80802EB2A94555CCBAEB7A5C202A2734BF8
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 23:20:48:625 ==>>                                          

2025-07-31 23:20:49:545 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:20:49:551 ==>> 检测【检测WiFi结果】
2025-07-31 23:20:49:556 ==>> WiFi信号:【F42A7D1297A3】,信号值:-70
2025-07-31 23:20:49:578 ==>> WiFi信号:【74C330CCAB10】,信号值:-72
2025-07-31 23:20:49:587 ==>> WiFi信号:【CC057790A641】,信号值:-72
2025-07-31 23:20:49:610 ==>> WiFi信号:【CC057790A4A1】,信号值:-79
2025-07-31 23:20:49:628 ==>> WiFi信号:【CC057790A620】,信号值:-60
2025-07-31 23:20:49:654 ==>> WiFi信号:【CC057790A621】,信号值:-60
2025-07-31 23:20:49:663 ==>> WiFi信号:【CC057790A5C0】,信号值:-75
2025-07-31 23:20:49:688 ==>> WiFi信号:【CC057790A5C1】,信号值:-77
2025-07-31 23:20:49:706 ==>> WiFi数量【8】, 最大信号值:-60
2025-07-31 23:20:49:731 ==>> 检测【检测GPS结果】
2025-07-31 23:20:49:750 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:20:49:759 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all continue location stop
[D][05:18:49][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all sing location stop


2025-07-31 23:20:50:520 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:20:50:565 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:20:50:574 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:50:598 ==>> 定位已等待【1】秒.
2025-07-31 23:20:50:929 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:50][COMM]Open GPS Module...
[D][05:18:50][COMM]LOC_MODEL_CONT
[D][05:18:50][GNSS]start event:8
[D][05:18:50][GNSS]GPS start. ret=0
[W][05:18:50][GNSS]start cont locating
[D][05:18:50][CAT1]gsm read msg sub id: 23
[D][05:18:50][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:50][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:20:51:576 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:51:585 ==>> 定位已等待【2】秒.
2025-07-31 23:20:51:636 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:20:52:308 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:20:52:518 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,40,,,42,34,,,41,39,,,41,23,,,38,1*78

$GBGSV,2,2,08,41,,,37,59,,,34,25,,,42,11,,,38,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

[D][05:18:52][CAT1]<<< 
OK

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1609.956,1609.956,51.493,2097152,2097152,2097152*45

[D][05:18:52][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:52][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 23, ret: 6
[D][05:18:52][CAT1]sub id: 23, ret: 6

                                         

2025-07-31 23:20:52:578 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:52:586 ==>> 定位已等待【3】秒.
2025-07-31 23:20:52:943 ==>> [D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:20:53:439 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,42,34,,,40,39,,,40,11,,,39,1*71

$GBGSV,4,2,16,23,,,38,41,,,38,60,,,38,7,,,38,1*42

$GBGSV,4,3,16,25,,,37,59,,,37,10,,,37,3,,,37,1*4F

$GBGSV,4,4,16,1,,,33,5,,,31,2,,,30,4,,,30,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1515.853,1515.853,48.527,2097152,2097152,2097152*43



2025-07-31 23:20:53:589 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:53:599 ==>> 定位已等待【4】秒.
2025-07-31 23:20:53:740 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B9

2025-07-31 23:20:53:815 ==>> 8E113311331133113311331B88B53CE78D8705AB438FAAF96479B3164F5723B281AB94302EF985091C5305280A1FDFE8A4F06FA19E87DED8EC1AC249E56101E415AEB0B5F399644D45DA99E45B077B90317280EC7CDCE7E4FDC964D88C56FD0D
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 23:20:54:469 ==>> $GBGGA,152058.328,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,40,,,42,34,,,40,39,,,40,11,,,39,1*71

$GBGSV,5,2,17,60,,,39,7,,,39,25,,,39,3,,,39,1*72

$GBGSV,5,3,17,23,,,38,41,,,38,59,,,38,43,,,38,1*79

$GBGSV,5,4,17,10,,,37,1,,,35,5,,,31,2,,,31,1*44

$GBGSV,5,5,17,4,,,30,1*47

$GBRMC,152058.328,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152058.328,0.000,1543.736,1543.736,49.408,2097152,2097152,2097152*52



2025-07-31 23:20:54:604 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:54:613 ==>> 定位已等待【5】秒.
2025-07-31 23:20:54:664 ==>> [D][05:18:54][COMM]read battery soc:255
$GBGGA,152058.528,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,40,,,42,34,,,40,39,,,40,11,,,39,1*71

$GBGSV,5,2,17,60,,,39,7,,,39,25,,,39,3,,,39,1*72

$GBGSV,5,3,17,23,,,38,41,,,38,59,,,38,43,,,38,1*79

$GBGSV,5,4,17,10,,,37,1,,,35,5,,,31,2,,,31,1*44

$GBGSV,5,5,17,4,,,31,1*46

$GBRMC,152058.528,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152058.528,0.000,1546.168,1546.168,49.480,2097152,2097152,2097152*54



2025-07-31 23:20:55:613 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:55:623 ==>> 定位已等待【6】秒.
2025-07-31 23:20:55:673 ==>> $GBGGA,152059.508,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,34,,,40,39,,,40,60,,,39,1*71

$GBGSV,6,2,21,7,,,39,25,,,39,3,,,39,59,,,39,1*7E

$GBGSV,6,3,21,43,,,39,11,,,38,23,,,38,41,,,38,1*72

$GBGSV,6,4,21,16,,,38,10,,,37,1,,,35,24,,,34,1*48

$GBGSV,6,5,21,2,,,32,5,,,31,4,,,31,12,,,52,1*40

$GBGSV,6,6,21,6,,,36,1*46

$GBRMC,152059.508,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152059.508,0.000,1544.877,1544.877,49.431,2097152,2097152,2097152*5D



2025-07-31 23:20:56:627 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:56:642 ==>> 定位已等待【7】秒.
2025-07-31 23:20:56:672 ==>> [D][05:18:56][COMM]read battery soc:255
$GBGGA,152100.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,34,,,40,39,,,40,25,,,40,1*7E

$GBGSV,6,2,21,60,,,39,7,,,39,3,,,39,59,,,39,1*7F

$GBGSV,6,3,21,43,,,39,41,,,39,11,,,38,23,,,38,1*73

$GBGSV,6,4,21,16,,,38,10,,,37,6,,,36,1,,,35,1*7A

$GBGSV,6,5,21,24,,,34,9,,,33,2,,,32,4,,,32,1*48

$GBGSV,6,6,21,5,,,31,1*42

$GBRMC,152100.508,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152100.508,0.000,1539.888,1539.888,49.270,2097152,2097152,2097152*53



2025-07-31 23:20:57:640 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:57:652 ==>> 定位已等待【8】秒.
2025-07-31 23:20:57:685 ==>> $GBGGA,152101.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,34,,,40,39,,,40,25,,,40,1*7C

$GBGSV,6,2,23,3,,,40,59,,,40,60,,,39,7,,,39,1*7D

$GBGSV,6,3,23,43,,,39,11,,,39,41,,,38,23,,,38,1*71

$GBGSV,6,4,23,16,,,38,10,,,37,6,,,36,1,,,35,1*78

$GBGSV,6,5,23,33,,,35,24,,,34,9,,,33,2,,,33,1*78

$GBGSV,6,6,23,12,,,33,4,,,32,5,,,32,1*75

$GBRMC,152101.508,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152101.508,0.000,766.450,766.450,700.937,2097152,2097152,2097152*60



2025-07-31 23:20:58:653 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:58:660 ==>> 定位已等待【9】秒.
2025-07-31 23:20:58:686 ==>> [D][05:18:58][COMM]read battery soc:255
$GBGGA,152102.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,3,,,40,60,,,40,39,,,40,1*4E

$GBGSV,6,2,24,59,,,40,34,,,40,25,,,40,43,,,40,1*7F

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,34,33,,,34,24,,,34,2,,,33,1*79

$GBGSV,6,6,24,12,,,33,5,,,32,4,,,32,44,,,28,1*78

$GBRMC,152102.508,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152102.508,0.000,762.128,762.128,696.987,2097152,2097152,2097152*66



2025-07-31 23:20:58:971 ==>>                                                              [D][05:18:58][PROT]index:2 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:3
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:18:58][PROT]sending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[134]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Send Data To Server[134][137] ... ->:
0043B

2025-07-31 23:20:59:047 ==>> 683113311331133113311331B88BED4ECC2437CA608930D0344B57F92C035F61CFE4455CA394EEACB68FAD672B50B80579F5C93948C10031CE964DDE1105AE8E9
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 23:20:59:667 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:20:59:677 ==>> 定位已等待【10】秒.
2025-07-31 23:20:59:700 ==>> $GBGGA,152103.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,39,,,40,1*4E

$GBGSV,6,2,24,59,,,40,25,,,40,43,,,40,7,,,39,1*41

$GBGSV,6,3,24,60,,,39,11,,,39,23,,,39,41,,,39,1*77

$GBGSV,6,4,24,16,,,38,10,,,37,6,,,36,1,,,36,1*7C

$GBGSV,6,5,24,24,,,35,9,,,34,33,,,34,2,,,33,1*78

$GBGSV,6,6,24,12,,,33,5,,,32,4,,,32,44,,,29,1*79

$GBRMC,152103.508,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152103.508,0.000,764.709,764.709,699.346,2097152,2097152,2097152*6F



2025-07-31 23:21:00:671 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:00:682 ==>> 定位已等待【11】秒.
2025-07-31 23:21:00:704 ==>> [D][05:19:00][COMM]read battery soc:255
$GBGGA,152104.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,60,,,40,1*42

$GBGSV,6,2,24,39,,,40,59,,,40,25,,,40,43,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,34,2,,,33,1*79

$GBGSV,6,6,24,12,,,33,5,,,32,4,,,32,44,,,30,1*71

$GBRMC,152104.508,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152104.508,0.000,766.428,766.428,700.917,2097152,2097152,2097152*67



2025-07-31 23:21:01:668 ==>> $GBGGA,152105.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,60,,,40,1*42

$GBGSV,6,2,24,39,,,40,59,,,40,25,,,40,43,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,2,,,34,33,,,34,1*7E

$GBGSV,6,6,24,12,,,33,5,,,32,4,,,32,44,,,30,1*71

$GBRMC,152105.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152105.508,0.000,767.288,767.288,701.703,2097152,2097152,2097152*6C



2025-07-31 23:21:01:683 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:01:693 ==>> 定位已等待【12】秒.
2025-07-31 23:21:02:696 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:02:705 ==>> [D][05:19:02][COMM]read battery soc:255
$GBGGA,152106.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,3,,,40,39,,,40,59,,,40,1*47

$GBGSV,6,2,24,34,,,40,25,,,40,7,,,39,60,,,39,1*45

$GBGSV,6,3,24,11,,,39,43,,,39,41,,,39,16,,,38,1*71

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,24,,,35,2,,,34,9,,,34,33,,,34,1*7F

$GBGSV,6,6,24,5,,,33,12,,,33,4,,,32,44,,,30,1*70

$GBRMC,152106.508,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152106.508,0.000,763.830,763.830,698.540,2097152,2097152,2097152*6B



2025-07-31 23:21:02:715 ==>> 定位已等待【13】秒.
2025-07-31 23:21:03:687 ==>> $GBGGA,152107.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,3,,,40,60,,,40,39,,,40,1*4E

$GBGSV,6,2,24,59,,,40,34,,,40,25,,,40,43,,,40,1*7F

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,2,,,34,12,,,34,24,,,34,1*7C

$GBGSV,6,6,24,33,,,34,5,,,33,4,,,32,44,,,30,1*74

$GBRMC,152107.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152107.508,0.000,767.282,767.282,701.697,2097152,2097152,2097152*62



2025-07-31 23:21:03:702 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:03:711 ==>> 定位已等待【14】秒.
2025-07-31 23:21:04:131 ==>> [D][05:19:03][PROT]CLEAN,SEND:2
[D][05:19:03][PROT]index:2 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:6
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:2
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]<<< 
ERROR



2025-07-31 23:21:04:676 ==>> [D][05:19:04][COMM]read battery soc:255
$GBGGA,152108.508,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,7,,,40,1*4B

$GBGSV,6,2,24,3,,,40,59,,,40,25,,,40,43,,,40,1*4B

$GBGSV,6,3,24,60,,,39,11,,,39,23,,,39,41,,,39,1*77

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,37,6,,,36,1*7D

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,30,1*76

$GBRMC,152108.508,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152108.508,0.000,771.596,771.596,705.643,2097152,2097152,2097152*60



2025-07-31 23:21:04:706 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:04:715 ==>> 定位已等待【15】秒.
2025-07-31 23:21:05:685 ==>> $GBGGA,152109.508,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,3,,,40,1*4F

$GBGSV,6,2,24,60,,,40,59,,,40,25,,,40,43,,,40,1*7E

$GBGSV,6,3,24,7,,,39,11,,,39,23,,,39,41,,,39,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,37,6,,,36,1*7D

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,30,1*76

$GBRMC,152109.508,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152109.508,0.000,771.596,771.596,705.643,2097152,2097152,2097152*61



2025-07-31 23:21:05:715 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:05:724 ==>> 定位已等待【16】秒.
2025-07-31 23:21:06:700 ==>> [D][05:19:06][COMM]read battery soc:255
$GBGGA,152110.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,60,,,40,1*42

$GBGSV,6,2,24,39,,,40,59,,,40,25,,,40,43,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,39,23,,,39,41,,,39,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,6,,,36,1,,,36,1*7C

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,30,1*76

$GBRMC,152110.508,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152110.508,0.000,769.870,769.870,704.065,2097152,2097152,2097152*6A



2025-07-31 23:21:06:730 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:06:739 ==>> 定位已等待【17】秒.
2025-07-31 23:21:07:663 ==>> $GBGGA,152111.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,3,,,40,1*4F

$GBGSV,6,2,24,60,,,40,59,,,40,25,,,40,43,,,40,1*7E

$GBGSV,6,3,24,7,,,39,11,,,39,23,,,39,41,,,39,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,6,,,36,1,,,36,1*7C

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,30,1*76

$GBRMC,152111.508,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152111.508,0.000,770.734,770.734,704.855,2097152,2097152,2097152*60



2025-07-31 23:21:07:738 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:07:744 ==>> 定位已等待【18】秒.
2025-07-31 23:21:08:674 ==>> [D][05:19:08][COMM]read battery soc:255
$GBGGA,152112.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,7,,,40,1*4B

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,25,,,40,1*4A

$GBGSV,6,3,24,43,,,40,11,,,39,23,,,39,16,,,38,1*7B

$GBGSV,6,4,24,41,,,38,10,,,37,6,,,36,1,,,36,1*7E

$GBGSV,6,5,24,9,,,35,24,,,35,2,,,34,12,,,34,1*7D

$GBGSV,6,6,24,33,,,34,5,,,32,4,,,32,44,,,31,1*74

$GBRMC,152112.508,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152112.508,0.000,770.733,770.733,704.853,2097152,2097152,2097152*65



2025-07-31 23:21:08:749 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:08:759 ==>> 定位已等待【19】秒.
2025-07-31 23:21:09:670 ==>> $GBGGA,152113.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,3,,,41,39,,,41,34,,,41,1*4E

$GBGSV,6,2,24,7,,,40,60,,,40,59,,,40,25,,,40,1*4E

$GBGSV,6,3,24,43,,,40,11,,,39,41,,,39,16,,,38,1*7F

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,2,,,34,12,,,34,1*7D

$GBGSV,6,6,24,33,,,34,5,,,32,4,,,32,44,,,30,1*75

$GBRMC,152113.508,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152113.508,0.000,770.739,770.739,704.860,2097152,2097152,2097152*64



2025-07-31 23:21:09:760 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:09:770 ==>> 定位已等待【20】秒.
2025-07-31 23:21:10:689 ==>> [D][05:19:10][COMM]read battery soc:255
$GBGGA,152114.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,60,,,40,1*42

$GBGSV,6,2,24,39,,,40,59,,,40,25,,,40,43,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,30,1*76

$GBRMC,152114.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152114.508,0.000,769.008,769.008,703.276,2097152,2097152,2097152*69



2025-07-31 23:21:10:764 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:10:776 ==>> 定位已等待【21】秒.
2025-07-31 23:21:11:686 ==>> $GBGGA,152115.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,34,,,41,3,,,40,60,,,40,1*42

$GBGSV,6,2,24,39,,,40,59,,,40,25,,,40,43,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,39,41,,,39,16,,,38,1*41

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,2,,,34,12,,,34,1*7D

$GBGSV,6,6,24,33,,,34,5,,,32,4,,,32,44,,,30,1*75

$GBRMC,152115.508,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152115.508,0.000,768.147,768.147,702.489,2097152,2097152,2097152*6F



2025-07-31 23:21:11:776 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:11:786 ==>> 定位已等待【22】秒.
2025-07-31 23:21:12:698 ==>> $GBGGA,152116.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,3,,,40,1*4F

$GBGSV,6,2,24,60,,,40,59,,,40,25,,,40,43,,,40,1*7E

$GBGSV,6,3,24,7,,,39,11,,,39,23,,,39,41,,,39,1*46

$GBGSV,6,4,24,16,,,38,10,,,37,1,,,37,6,,,36,1*7D

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,31,1*77

$GBRMC,152116.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152116.508,0.000,772.453,772.453,706.427,2097152,2097152,2097152*6C

[D][05:19:12][COMM]read battery soc:255


2025-07-31 23:21:12:788 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:12:798 ==>> 定位已等待【23】秒.
2025-07-31 23:21:13:696 ==>> $GBGGA,152117.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,7,,,40,1*4B

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,25,,,40,1*4A

$GBGSV,6,3,24,43,,,40,11,,,39,41,,,39,16,,,38,1*7F

$GBGSV,6,4,24,23,,,38,10,,,37,6,,,36,1,,,36,1*7A

$GBGSV,6,5,24,9,,,35,24,,,35,33,,,35,2,,,34,1*7F

$GBGSV,6,6,24,12,,,34,5,,,32,4,,,32,44,,,31,1*77

$GBRMC,152117.508,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152117.508,0.000,771.593,771.593,705.640,2097152,2097152,2097152*6D



2025-07-31 23:21:13:801 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:13:808 ==>> 定位已等待【24】秒.
2025-07-31 23:21:14:125 ==>> [D][05:19:14][CAT1]exec over: func id: 15, ret: -93
[D][05:19:14][CAT1]sub id: 15, ret: -93

[D][05:19:14][SAL ]Cellular task submsg id[68]
[D][05:19:14][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:14][SAL ]socket send fail. id[4]
[D][05:19:14][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:14][M2M ]m2m select fd[4]
[D][05:19:14][M2M ]socket[4] Link is disconnected
[D][05:19:14][M2M ]tcpclient close[4]
[D][05:19:14][SAL ]socket[4] has closed
[D][05:19:14][PROT]protocol read data ok
[E][05:19:14][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:14][PROT]M2M Send Fail [1629955154]
[D][05:19:14][PROT]CLEAN,SEND:2
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:14][CAT1]gsm read msg sub id: 10
[D][05:19:14][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:14][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:21:14:500 ==>> [D][05:19:14][CAT1]<<< 
OK

[D][05:19:14][CAT1]exec over: func id: 10, ret: 6
[D][05:19:14][CAT1]sub id: 10, ret: 6

[D][05:19:14][SAL ]Cellular task submsg id[68]
[D][05:19:14][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:14][M2M ]m2m gsm shut done, ret[0]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:14][SAL ]open socket ind id[4], rst[0]
[D][05:19:14][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:14][SAL ]Cellular task submsg id[8]
[D][05:19:14][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:14][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:14][CAT1]gsm read msg sub id: 8
[D][05:19:14][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:14][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:21:14:606 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 23:21:14:803 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:14:823 ==>> 定位已等待【25】秒.
2025-07-31 23:21:15:405 ==>> [D][05:19:14][CAT1]pdpdeact urc len[22]
$GBGGA,152114.513,2301.2564207,N,11421.9435569,E,1,14,0.89,77.741,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,69,217,39,39,66,50,40,6,64,13,36,1*7A

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,35,1*44

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,40,1*79

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,79,34,44,12,173,31,33,4,323,35,12,,,34,1*46

$GBRMC,152114.513,A,2301.2564207,N,11421.9435569,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:19:14][GNSS]HD8040 GPS
[D][05:19:14][GNSS]GPS diff_sec 124020120, report 0x42 frame
$GBGST,152114.513,1.653,0.428,0.444,0.623,2.095,2.147,6.474*73

[D][05:19:14][COMM]Main Task receive event:131
[D][05:19:14][COMM]index:0,power_mode:0xFF
[D][05:19:14][COMM]index:1,sound_mode:0xFF
[D][05:19:14][COMM]index:2,gsensor_mode:0xFF
[D][05:19:14][COMM]index:3,report_freq_mode:0xFF
[D][05:19:14][COMM]index:4,report_

2025-07-31 23:21:15:510 ==>> period:0xFF
[D][05:19:14][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:14][COMM]index:6,normal_reset_period:0xFF
[D][05:19:14][COMM]index:7,spock_over_speed:0xFF
[D][05:19:14][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:14][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:14][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:14][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:14][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:14][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:14][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:14][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:14][COMM]index:16,imu_config_params:0xFF
[D][05:19:14][COMM]index:17,long_connect_params:0xFF
[D][05:19:14][COMM]index:18,detain_mark:0xFF
[D][05:19:14][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:14][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:14][COMM]index:21,mc_mode:0xFF
[D][05:19:14][COMM]index:22,S_mode:0xFF
[D][05:19:14][COMM]index:23,overweight:0xFF
[D][05:19:14][COMM]index:24,standstill_mode:0xFF
[D][05:19:14][COMM]index:25,night_mode:0xFF
[D][05:19:14][COMM]index:26,experiment1:0xFF
[D][05:19:14][COMM]index:27,experiment2:0xFF
[D][05:19:14][COMM]index:28,experi

2025-07-31 23:21:15:616 ==>> ment3:0xFF
[D][05:19:14][COMM]index:29,experiment4:0xFF
[D][05:19:14][COMM]index:30,night_mode_start:0xFF
[D][05:19:14][COMM]index:31,night_mode_end:0xFF
[D][05:19:14][COMM]index:33,park_report_minutes:0xFF
[D][05:19:14][COMM]index:34,park_report_mode:0xFF
[D][05:19:14][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:14][COMM]index:38,charge_battery_para: FF
[D][05:19:14][COMM]index:39,multirider_mode:0xFF
[D][05:19:14][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:14][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:14][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:14][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:14][COMM]index:44,riding_duration_config:0xFF
[D][05:19:14][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:14][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:14][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:14][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:14][COMM]index:49,mc_load_startup:0xFF
[D][05:19:14][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:14][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:14][COMM]index:52,traffic_mode:0xFF
[D][05:19:14][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:14][COMM]index:54,traffic_se

2025-07-31 23:21:15:721 ==>> curity_model_cycle:0xFF
[D][05:19:14][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:14][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:14][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:14][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:14][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:14][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:14][COMM]index:63,experiment5:0xFF
[D][05:19:14][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:14][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:14][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:14][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:14][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:14][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:14][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:14][COMM]index:72,experiment6:0xFF
[D][05:19:14][COMM]index:73,experiment7:0xFF
[D][05:19:14][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:14][COMM]index:75,zero_value_from_server:-1
[D][05:19:14][COMM]index:76,multirider_threshold:255
[D][05:19:14][COMM]index:77,experiment8:255
[D][05:19:14][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:14]

2025-07-31 23:21:15:812 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:15:819 ==>> 定位已等待【26】秒.
2025-07-31 23:21:15:829 ==>> [COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:14][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:14][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:14][COMM]index:83,loc_report_interval:255
[D][05:19:14][COMM]index:84,multirider_threshold_p2:255
[D][05:19:14][COMM]index:85,multirider_strategy:255
[D][05:19:14][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:14][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:14][COMM]index:90,weight_param:0xFF
[D][05:19:14][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:14][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:14][COMM]index:95,current_limit:0xFF
[D][05:19:14][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:14][COMM]index:100,location_mode:0xFF

[W][05:19:14][PROT]remove success[1629955154],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:14][PROT]add success [1629955154],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:14][COMM]Main Task receive event:131 finished processing
[D][05:19:14][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,152115.013,2301.256793

2025-07-31 23:21:15:933 ==>> 8,N,11421.9434110,E,1,14,0.89,78.956,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,69,217,39,39,66,50,40,6,64,13,36,1*7A

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,34,1*45

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,40,2,45,236,34,60,41,239,40,1*78

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,79,35,44,12,173,31,33,4,323,35,12,,,34,1*47

$GBGSV,2,1,08,40,76,189,40,39,66,50,40,25,51,21,40,43,47,161,39,5*7F

$GBGSV,2,2,08,34,46,76,39,41,33,242,39,23,32,314,37,24,18,79,36,5*79

$GBRMC,152115.013,A,2301.2567938,N,11421.9434110,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152115.013,2.382,0.814,0.849,1.189,2.190,2.200,5.052*7E

                                                                                                                                                                            

2025-07-31 23:21:15:993 ==>>                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 23:21:16:403 ==>> [D][05:19:16][CAT1]opened : 0, 0
[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:16][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:16][M2M ]g_m2m_is_idle become true
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]index:2 1629955156
[D][05:19:16][PROT]is_send:0
[D][05:19:16][PROT]sequence_num:6
[D][05:19:16][PROT]retry_timeout:0
[D][05:19:16][PROT]retry_times:1
[D][05:19:16][PROT]send_path:0x2
[D][05:19:16][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:16][PROT]sending traceid [9999999999900007]
[D][05:19:16][PROT]Send_TO_M2M [1629955156]
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:16][SAL ]sock send credit cnt[6]
[D][05:19:16][SAL ]sock send ind credit cnt[6]
[D]

2025-07-31 23:21:16:509 ==>> [05:19:16][M2M ]m2m send data len[134]
[D][05:19:16][SAL ]Cellular task submsg id[10]
[D][05:19:16][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:16][CAT1]gsm read msg sub id: 15
[D][05:19:16][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:16][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BE3E85CE3155DC90E99CAD91B33A8AECCF752865FB03864EE252059F5B907F4811AE4BD74CE71A342F78299F6DC42D4ACD98A6
$GBGGA,152116.000,2301.2571536,N,11421.9433487,E,1,14,0.89,80.524,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,69,217,39,39,66,50,40,6,64,13,36,1*7A

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,34,1*45

$GBGSV,6,3,24,59,52,129,39,11,52,115,39,25,51,21,40,1,48,125,36,1*7C

$GBGSV,6,4,24,43,47,161,40,34,46,76,40,2,45,236,34,60,41,239,39,1*76

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,79,34,44,12,173,30,33,4,323,34,12,,,34,1*46

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,40,

2025-07-31 23:21:16:599 ==>> 43,47,161,39,5*7F

$GBGSV,2,2,08,34,46,76,40,41,33,242,39,23,32,314,38,24,18,79,36,5*78

[D][05:19:16][CAT1]<<< 
SEND OK

[D][05:19:16][CAT1]exec over: func id: 15, ret: 11
[D][05:19:16][CAT1]sub id: 15, ret: 11

[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
$GBRMC,152116.000,A,2301.2571536,N,11421.9433487,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:19:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGST,152116.000,2.326,0.432,0.448,0.630,2.008,2.015,4.258*7F

[D][05:19:16][M2M ]g_m2m_is_idle become true
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]M2M Send ok [1629955156]
                                         

2025-07-31 23:21:16:827 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:21:16:839 ==>> 定位已等待【27】秒.
2025-07-31 23:21:17:309 ==>> $GBGGA,152117.000,2301.2573134,N,11421.9433477,E,1,14,0.89,81.257,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,69,217,39,39,66,50,40,6,64,13,36,1*7A

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,34,1*45

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,39,1*77

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152117.000,A,2301.2573134,N,11421.9433477,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152117.000,2.253,0.186,0.190,0.272,1.876,1.887,3.790*71



2025-07-31 23:21:17:839 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 23:21:17:850 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【39】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【38】
北斗星号:【3】,信号值:【40】
北斗星号:【10】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【11】,信号值:【39】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【36】
北斗星号:【43】,信号值:【39】
北斗星号:【34】,信号值:【41】
北斗星号:【60】,信号值:【39】
北斗星号:【41】,信号值:【39】
北斗星号:【23】,信号值:【38】
北斗星号:【24】,信号值:【36】

2025-07-31 23:21:17:861 ==>> 检测【CSQ强度】
2025-07-31 23:21:17:888 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:21:18:039 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 21


2025-07-31 23:21:18:299 ==>> $GBGGA,152118.000,2301.2574187,N,11421.9433695,E,1,14,0.89,81.426,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,69,217,39,39,66,50,40,6,64,13,36,1*7A

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,35,1*44

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,33,60,41,239,40,1*7E

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,5,21,255,32,1*75

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152118.000,A,2301.2574187,N,11421.9433695,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152118.000,2.040,0.207,0.212,0.302,1.687,1.699,3.413*72



2025-07-31 23:21:18:339 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 23:21:18:351 ==>> 检测【关闭GSM联网】
2025-07-31 23:21:18:378 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:21:18:523 ==>> [D][05:19:18][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:18][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:18][COMM]GSM test
[D][05:19:18][COMM]GSM test disable


2025-07-31 23:21:18:629 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 23:21:18:646 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:21:18:658 ==>> 检测【4G联网测试】
2025-07-31 23:21:18:679 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:21:18:780 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:21:19:643 ==>> [D][05:19:18][COMM]Main Task receive event:14
[D][05:19:18][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955158, allstateRepSeconds = 0
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]i

2025-07-31 23:21:19:748 ==>> ndex:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF

2025-07-31 23:21:19:853 ==>> 
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_se

2025-07-31 23:21:19:958 ==>> lf_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W]

2025-07-31 23:21:20:063 ==>> [05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][CAT1]gsm read msg sub id: 13
[D][05:19:18][PROT]index:0 1629955158
[D][05:19:18][PROT]is_send:0
[D][05:19:18][PROT]sequence_num:8
[D][05:19:18][PROT]retry_timeout:0
[D][05:19:18][PROT]retry_times:1
[D][05:19:18][PROT]send_path:0x2
[D][05:19:18][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:18][PROT]===========================================================
[W][05:19:18][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]sending traceid [9999999999900009]
[D][05:19:18][PROT]Send_TO_M2M [1629955158]
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:18][SAL ]sock send credit cnt[6]
[D][05:19:18][SAL ]sock send ind credit cnt[6]
[D][05:19:18][M2M ]m2m send data len[294]
[D][05:19:18][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:18][SAL ]Cellular 

2025-07-31 23:21:20:168 ==>> task submsg id[10]
[D][05:19:18][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:18][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:18][CAT1]exec over: func id: 13, ret: 21
[D][05:19:18][M2M ]get csq[23]
[D][05:19:18][CAT1]gsm read msg sub id: 15
[D][05:19:18][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:19][CAT1]<<< 
ERROR

$GBGGA,152119.000,2301.2574890,N,11421.9433506,E,1,14,0.89,81.647,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,41,6,64,13,36,1*73

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,35,1*44

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,40,2,45,236,33,60,41,239,40,1*7F

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,5,21,255,32,1*75

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F



2025-07-31 23:21:20:273 ==>> $GBRMC,152119.000,A,2301.2574890,N,11421.9433506,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152119.000,1.993,0.168,0.171,0.246,1.618,1.631,3.184*75

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:19][COMM]------>period, report file manifest
[D][05:19:19][COMM]Main Task receive event:14 finished processing
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 23:21:20:348 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:21:20:622 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 23:21:21:301 ==>> $GBGGA,152121.000,2301.2576346,N,11421.9433210,E,1,14,0.89,82.305,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.75,0.89,1.50,4*0C

$GBGSA,A,3,23,24,,,,,,,,,,,1.75,0.89,1.50,4*09

$GBGSV,6,1,24,40,76,189,42,7,70,217,40,39,66,50,40,6,64,13,36,1*7C

$GBGSV,6,2,24,16,63,18,38,3,60,190,39,10,57,221,37,9,55,347,35,1*4A

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,39,1*77

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152121.000,A,2301.2576346,N,11421.9433210,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152121.000,1.977,0.213,0.219,0.313,1.560,1.573,2.906*7D



2025-07-31 23:21:22:308 ==>> $GBGGA,152122.000,2301.2576739,N,11421.9433313,E,1,14,0.89,82.451,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.74,0.89,1.50,4*0D

$GBGSA,A,3,23,24,,,,,,,,,,,1.74,0.89,1.50,4*08

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,35,1*44

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,39,1*77

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,5,21,255,32,1*75

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,42,39,66,50,41,25,51,21,41,43,47,161,39,5*7D

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152122.000,A,2301.2576739,N,11421.9433313,E,0.003,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,152122.000,1.956,0.190,0.195,0.278,1.531,1.543,2.799*71



2025-07-31 23:21:22:631 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 23:21:23:310 ==>> $GBGGA,152123.000,2301.2577360,N,11421.9433285,E,1,14,0.89,82.537,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,06,16,10,09,11,25,43,34,41,1.74,0.89,1.50,4*0D

$GBGSA,A,3,23,24,,,,,,,,,,,1.74,0.89,1.50,4*08

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,41,6,64,13,36,1*73

$GBGSV,6,2,24,16,63,18,38,3,60,190,40,10,57,221,37,9,55,347,35,1*44

$GBGSV,6,3,24,59,52,129,40,11,52,115,39,25,51,21,40,1,48,125,36,1*72

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,39,1*77

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,5,21,255,32,1*75

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152123.000,A,2301.2577360,N,11421.9433285,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152123.000,1.966,0.188,0.193,0.276,1.524,1.536,2.726*70



2025-07-31 23:21:24:299 ==>> $GBGGA,152124.000,2301.2577740,N,11421.9433155,E,1,17,0.80,82.590,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.66,0.80,1.46,4*0D

$GBGSA,A,3,34,60,41,23,24,,,,,,,,1.66,0.80,1.46,4*01

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,39,25,51,21,40,59,49,131,40,1,48,125,36,1*71

$GBGSV,6,4,24,43,47,161,40,34,46,76,41,2,45,236,34,60,41,239,39,1*77

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,5,21,255,32,1*75

$GBGSV,6,6,24,24,18,80,34,44,12,173,31,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152124.000,A,2301.2577740,N,11421.9433155,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152124.000,2.060,0.193,0.197,0.289,1.578,1.589,2.715*78



2025-07-31 23:21:24:638 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 23:21:24:683 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:21:25:519 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:24][COMM]Main Task receive event:14
[D][05:19:24][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955164, allstateRepSeconds = 0
[D][05:19:24][COMM]index:0,power_mode:0xFF
[D][05:19:24][COMM]index:1,sound_mode:0xFF
[D][05:19:24][COMM]index:2,gsensor_mode:0xFF
[D][05:19:24][COMM]index:3,report_freq_mode:0xFF
[D][05:19:24][COMM]index:4,report_period:0xFF
[D][05:19:24][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:24][COMM]index:6,normal_reset_period:0xFF
[D][05:19:24][COMM]index:7,spock_over_speed:0xFF
[D][05:19:24][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:24][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:24][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:24][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:24][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:24][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:24][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:24][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:24][COMM]index:16,imu_config_params:0xFF
[D][05:19:24][COMM]index:17,long_connect_params:0xFF
[D][05:19:24]

2025-07-31 23:21:25:624 ==>> [COMM]index:18,detain_mark:0xFF
[D][05:19:24][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:24][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:24][COMM]index:21,mc_mode:0xFF
[D][05:19:24][COMM]index:22,S_mode:0xFF
[D][05:19:24][COMM]index:23,overweight:0xFF
[D][05:19:24][COMM]index:24,standstill_mode:0xFF
[D][05:19:24][COMM]index:25,night_mode:0xFF
[D][05:19:24][COMM]index:26,experiment1:0xFF
[D][05:19:24][COMM]index:27,experiment2:0xFF
[D][05:19:24][COMM]index:28,experiment3:0xFF
[D][05:19:24][COMM]index:29,experiment4:0xFF
[D][05:19:24][COMM]index:30,night_mode_start:0xFF
[D][05:19:24][COMM]index:31,night_mode_end:0xFF
[D][05:19:24][COMM]index:33,park_report_minutes:0xFF
[D][05:19:24][COMM]index:34,park_report_mode:0xFF
[D][05:19:24][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:24][COMM]index:38,charge_battery_para: FF
[D][05:19:24][COMM]index:39,multirider_mode:0xFF
[D][05:19:24][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:24][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:24][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:24][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:24][COMM]index:44,riding_duration_config:0xFF
[D]

2025-07-31 23:21:25:729 ==>> [05:19:24][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:24][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:24][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:24][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:24][COMM]index:49,mc_load_startup:0xFF
[D][05:19:24][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:24][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:24][COMM]index:52,traffic_mode:0xFF
[D][05:19:24][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:24][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:24][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:24][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:24][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:24][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:24][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:24][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:24][COMM]index:63,experiment5:0xFF
[D][05:19:24][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:24][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:24][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:24][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:24][COMM]index:68,camera_park_ps_cfg:0xFFFF

2025-07-31 23:21:25:834 ==>> 
[D][05:19:24][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:24][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:24][COMM]index:72,experiment6:0xFF
[D][05:19:24][COMM]index:73,experiment7:0xFF
[D][05:19:24][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:24][COMM]index:75,zero_value_from_server:-1
[D][05:19:24][COMM]index:76,multirider_threshold:255
[D][05:19:24][COMM]index:77,experiment8:255
[D][05:19:24][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:24][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:24][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:24][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:24][COMM]index:83,loc_report_interval:255
[D][05:19:24][COMM]index:84,multirider_threshold_p2:255
[D][05:19:24][COMM]index:85,multirider_strategy:255
[D][05:19:24][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:24][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:24][COMM]index:90,weight_param:0xFF
[D][05:19:24][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:24][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:24][COMM]index:95,current_limit:0xFF
[D][05:19:24][COMM]index:97,pa

2025-07-31 23:21:25:939 ==>> nel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:24][COMM]index:100,location_mode:0xFF

[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:24][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:24][PROT]add success [1629955164],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,152125.000,2301.2577882,N,11421.9432952,E,1,17,0.80,82.684,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.66,0.80,1.46,4*0D

$GBGSA,A,3,34,60,41,23,24,,,,,,,,1.66,0.80,1.46,4*01

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,39,25,51,21,40,59,49,131,40,1,48,125,36,1*71

$GBGSV,6,4,24,43,47,161,39,34,46,76,41,2,45,236,34,60,41,239,39,1*79

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,80,35,44,12,173,31,33,4,323,34,12,,,34,1*40

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,

2025-07-31 23:21:25:984 ==>> 2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152125.000,A,2301.2577882,N,11421.9432952,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,152125.000,2.047,0.224,0.230,0.338,1.561,1.571,2.652*7B



2025-07-31 23:21:26:304 ==>> $GBGGA,152126.000,2301.2577994,N,11421.9432870,E,1,17,0.80,82.736,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.66,0.80,1.46,4*0D

$GBGSA,A,3,34,60,41,23,24,,,,,,,,1.66,0.80,1.46,4*01

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,39,10,57,221,37,9,55,347,35,1*4B

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,1,48,125,36,1*70

$GBGSV,6,4,24,43,47,161,39,34,46,76,40,2,45,236,34,60,41,239,39,1*78

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,80,34,44,12,173,31,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,40,41,33,242,39,23,32,314,38,24,18,80,36,5*7E

$GBRMC,152126.000,A,2301.2577994,N,11421.9432870,E,0.004,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.004,N,0.008,K,A*23

$GBGST,152126.000,1.940,0.177,0.180,0.268,1.480,1.491,2.543*7E



2025-07-31 23:21:26:670 ==>> [D][05:19:26][COMM]read battery soc:255


2025-07-31 23:21:27:009 ==>> [D][05:19:27][M2M ]get csq[-1]


2025-07-31 23:21:27:392 ==>> $GBGGA,152127.000,2301.2578036,N,11421.9432841,E,1,18,0.77,82.772,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.65,0.77,1.46,4*06

$GBGSA,A,3,34,01,60,41,23,24,,,,,,,1.65,0.77,1.46,4*0B

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,39,1*46

$GBGSV,6,4,24,34,46,76,40,1,45,126,36,2,45,236,34,60,41,239,39,1*40

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,5,21,255,32,1*74

$GBGSV,6,6,24,24,18,80,35,44,12,173,30,33,4,323,34,12,,,34,1*41

$GBGSV,2,1,08,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,2,2,08,34,46,76,40,41,33,242,39,23,32,314,38,24,18,80,36,5*7E

$GBRMC,152127.000,A,2301.2578036,N,11421.9432841,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152127.000,1.803,0.201,0.207,0.307,1.377,1.387,2.419*7E

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:27][PROT]remove success[1629955167],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:27][PROT]add success [1629955167],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:27][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:

2025-07-31 23:21:27:436 ==>> 19:27][COMM][LOC]wifi scan is already running, error
[D][05:19:27][COMM]Main Task receive event:14 finished processing
[D][05:19:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:27][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 23:21:28:304 ==>> $GBGGA,152128.000,2301.2578004,N,11421.9432665,E,1,18,0.77,82.808,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.65,0.77,1.46,4*06

$GBGSA,A,3,34,01,60,41,23,24,,,,,,,1.65,0.77,1.46,4*0B

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,39,1*46

$GBGSV,6,4,24,34,46,76,40,1,45,126,36,2,45,236,34,60,41,239,39,1*40

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,32,24,18,80,35,33,16,190,34,44,12,173,30,1*79

$GBGSV,2,1,08,40,76,189,41,39,66,50,42,25,51,21,41,43,47,161,39,5*7D

$GBGSV,2,2,08,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBRMC,152128.000,A,2301.2578004,N,11421.9432665,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,152128.000,1.748,0.201,0.206,0.304,1.331,1.341,2.346*76



2025-07-31 23:21:28:658 ==>> [D][05:19:28][COMM]read battery soc:255


2025-07-31 23:21:29:628 ==>> [D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:

2025-07-31 23:21:29:733 ==>> 29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100030. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100030. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A

2025-07-31 23:21:29:838 ==>>  loss. last_tick:0. cur_tick:100031. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100031. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100031. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100032. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100032. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100032. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100033. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100033
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100033
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100034
[D][05:19:29][CAT1]exec over: func id: 15, ret: -93
[D][05:19:29][CAT1]sub id: 15, ret: -93

[D][05:19:29][SAL ]Cellular task submsg id[68]
[D][05:19:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:29][SAL ]socket send fail. id[4]
[D][05:19:29][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05

2025-07-31 23:21:29:943 ==>> :19:29][CAT1]gsm read msg sub id: 21
[D][05:19:29][M2M ]m2m select fd[4]
[D][05:19:29][M2M ]socket[4] Link is disconnected
[D][05:19:29][M2M ]tcpclient close[4]
[D][05:19:29][SAL ]socket[4] has closed
[D][05:19:29][PROT]protocol read data ok
[E][05:19:29][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:29][CAT1]tx ret[15] >>> AT+QCELLINFO?

[E][05:19:29][PROT]M2M Send Fail [1629955169]
[D][05:19:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]cell info report total[0]
[D][05:19:29][CAT1]exec over: func id: 21, ret: 6
[D][05:19:29][CAT1]gsm read msg sub id: 13
[D][05:19:29][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:29][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:29][CAT1]exec over: func id: 13, ret: 21
[D][05:19:29][CAT1]gsm read msg sub id: 10
[D][05:19:29][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:29][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:29][CAT1]tx ret[12] >>> AT+CGATT=0

$GBGGA,152129.000,2301.2578073,N,11421.9432599,E,1,20,0.70,82.828,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.35,0.70,1.15,4*02

$GBGSA,A,3,34,01,60,41,23,12,24,33,,,,,1.35,0.

2025-07-31 23:21:30:048 ==>> 70,1.15,4*0C

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,39,1*46

$GBGSV,6,4,24,34,46,76,40,1,45,126,36,2,45,236,33,60,41,239,39,1*47

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,32,24,18,80,35,33,16,190,34,44,12,173,30,1*79

$GBGSV,3,1,09,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,3,2,09,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBGSV,3,3,09,33,16,190,34,5*43

$GBRMC,152129.000,A,2301.2578073,N,11421.9432599,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152129.000,2.781,0.196,0.196,0.282,2.001,2.008,2.893*74

[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]exec over: func id: 10, ret: 6
[D][05:19:29][CAT1]sub id: 10, ret: 6

[D][05:19:29][SAL ]Cellular task submsg id[68]
[D][05:19:29][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:29][M2M ]m2m gsm shut done, ret[0]
[D][05:19:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:29][SAL ]open socket ind id[4], rst[0]
[D]

2025-07-31 23:21:30:123 ==>> [05:19:29][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:29][SAL ]Cellular task submsg id[8]
[D][05:19:29][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:29][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:29][CAT1]gsm read msg sub id: 8
[D][05:19:29][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:29][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:29][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:29][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:21:30:228 ==>>                                                                                                           

2025-07-31 23:21:30:333 ==>>                                                                                                                                                                                                                               $GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,39,1*46

$GBGSV,6,4,24,34,46,76,40,1,45,126,36,2,45,236,34,60,41,239,39,1*40

$GBGSV,6,5,24,41,33,242,39,4,32,111,32,23,32,314,38,12,25,52,34,1*74

$GBGSV,6,6,24,5,21,255,32,24,18,80,34,33,16,190,34,44,12,173,30,1*78

$GBGSV,3,1,09,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

$GBGSV,3,2,09,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBGSV,3,3,09,33,16,190,34,5*43

$GBRMC,152130.000,A,2301.2578095,N,11421.9432548,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152130.000,2.975,0.199,0.199,0.286,2.110,2.117,2.967*79



2025-07-31 23:21:30:686 ==>> [D][05:19:30][COMM]read battery soc:255


2025-07-31 23:21:30:716 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:21:31:864 ==>> [D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:30][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:30][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:30][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:30][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:30][CAT1]<<< 
OK

[W][05:19:30][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:30][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 8, ret: 6
[D][05:19:31][COMM]Main Task receive event:14
[D][05:19:31][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955171, allstateRepSeconds = 0
[D][05:19:31][COMM]index:0,power_mode:0xFF
[D][05:19:31][COMM]index:1,sound_mode:0xFF
[D][05:19:31][COMM]index:2,gsensor_mode:0xFF
[D][05:19:31][COMM]index:3,report_freq_mode:0xFF
[D][05:19:31][COMM]index:4,report_period:0xFF
[D][05:19:31][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:31][COMM]index:6,normal_reset_period:0xFF
[D][05:19:31][COMM]index:7,spock_over_speed:0xFF
[D][05:19:31][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:31][COMM]index

2025-07-31 23:21:31:969 ==>> :9,spock_report_period_unlock:0xFF
[D][05:19:31][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:31][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:31][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:31][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:31][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:31][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:31][COMM]index:16,imu_config_params:0xFF
[D][05:19:31][COMM]index:17,long_connect_params:0xFF
[D][05:19:31][COMM]index:18,detain_mark:0xFF
[D][05:19:31][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:31][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:31][COMM]index:21,mc_mode:0xFF
[D][05:19:31][COMM]index:22,S_mode:0xFF
[D][05:19:31][COMM]index:23,overweight:0xFF
[D][05:19:31][COMM]index:24,standstill_mode:0xFF
[D][05:19:31][COMM]index:25,night_mode:0xFF
[D][05:19:31][COMM]index:26,experiment1:0xFF
[D][05:19:31][COMM]index:27,experiment2:0xFF
[D][05:19:31][COMM]index:28,experiment3:0xFF
[D][05:19:31][COMM]index:29,experiment4:0xFF
[D][05:19:31][COMM]index:30,night_mode_start:0xFF
[D][05:19:31][COMM]index:31,night_mode_end:0xFF
[D][05:19:31][COMM]index:33,park_report_minutes:0xFF
[D][05:19:31][COMM]index

2025-07-31 23:21:32:075 ==>> :34,park_report_mode:0xFF
[D][05:19:31][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:31][COMM]index:38,charge_battery_para: FF
[D][05:19:31][COMM]index:39,multirider_mode:0xFF
[D][05:19:31][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:31][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:31][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:31][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:31][COMM]index:44,riding_duration_config:0xFF
[D][05:19:31][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:31][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:31][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:31][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:31][COMM]index:49,mc_load_startup:0xFF
[D][05:19:31][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:31][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:31][COMM]index:52,traffic_mode:0xFF
[D][05:19:31][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:31][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:31][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:31][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:31][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:31][COMM]index:59,traffic_retr

2025-07-31 23:21:32:180 ==>> ograde_threshold:0xFF
[D][05:19:31][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:31][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:31][COMM]index:63,experiment5:0xFF
[D][05:19:31][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:31][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:31][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:31][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:31][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:31][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:31][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:31][COMM]index:72,experiment6:0xFF
[D][05:19:31][COMM]index:73,experiment7:0xFF
[D][05:19:31][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:31][COMM]index:75,zero_value_from_server:-1
[D][05:19:31][COMM]index:76,multirider_threshold:255
[D][05:19:31][COMM]index:77,experiment8:255
[D][05:19:31][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:31][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:31][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:31][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:31][COMM]index:83,loc_report_interval:255
[

2025-07-31 23:21:32:285 ==>> D][05:19:31][COMM]index:84,multirider_threshold_p2:255
[D][05:19:31][COMM]index:85,multirider_strategy:255
[D][05:19:31][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:31][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:31][COMM]index:90,weight_param:0xFF
[D][05:19:31][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:31][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:31][COMM]index:95,current_limit:0xFF
[D][05:19:31][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:31][COMM]index:100,location_mode:0xFF

[W][05:19:31][PROT]remove success[1629955171],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:31][PROT]add success [1629955171],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:31][CAT1]gsm read msg sub id: 13
[D][05:19:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:31][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:31][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:31][CAT1]exec over: func id: 13, ret: 21
[D][05:19:31][M2M ]get csq[24]
$GBGGA,152131.000,2301.2578208,N,11421.9432582,E,1,21,0.68,82.877,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06

2025-07-31 23:21:32:390 ==>> ,16,03,10,09,11,25,59,43,1.18,0.68,0.96,4*0E

$GBGSA,A,3,34,01,60,41,23,12,24,33,44,,,,1.18,0.68,0.96,4*00

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,39,1*46

$GBGSV,6,4,24,34,46,76,40,1,45,126,36,2,45,236,33,60,41,239,39,1*47

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,32,24,18,80,34,33,16,190,34,44,5,39,30,1*71

$GBGSV,3,1,09,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*7E

[D][05:19:31][CAT1]opened : 0, 0
[D][05:19:31][SAL ]Cellular task submsg id[68]
[D][05:19:31][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:31][SAL ]socket connect ind. id[4], rst[3]
$GBGSV,3,2,09,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*7F

$GBGSV,3,3,09,33,16,190,34,5*43

$GBRMC,152131.000,A,2301.2578208,N,11421.9432582,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,152131.000,3.034,0.193,0.190,0.266,2.140,2.147,2.974*7A

[D][05:19:31][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:31][M2M ]g_m2m_is_idle b

2025-07-31 23:21:32:495 ==>> ecome true
[D][05:19:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:31][PROT]index:0 1629955171
[D][05:19:31][PROT]is_send:0
[D][05:19:31][PROT]sequence_num:12
[D][05:19:31][PROT]retry_timeout:0
[D][05:19:31][PROT]retry_times:1
[D][05:19:31][PROT]send_path:0x2
[D][05:19:31][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:31][PROT]===========================================================
[W][05:19:31][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955171]
[D][05:19:31][PROT]===========================================================
[D][05:19:31][PROT]sending traceid [999999999990000D]
[D][05:19:31][PROT]Send_TO_M2M [1629955171]
[D][05:19:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:31][SAL ]sock send credit cnt[6]
[D][05:19:31][SAL ]sock send ind credit cnt[6]
[D][05:19:31][M2M ]m2m send data len[294]
[D][05:19:31][SAL ]Cellular task submsg id[10]
[D][05:19:31][SAL ]cellular SEND soc

2025-07-31 23:21:32:600 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 23:21:32:690 ==>> [D][05:19:32][COMM]read battery soc:255


2025-07-31 23:21:33:352 ==>> $GBGGA,152133.000,2301.2578445,N,11421.9432482,E,1,22,0.67,82.866,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.17,0.67,0.96,4*0E

$GBGSA,A,3,34,02,01,60,41,23,12,24,33,44,,,1.17,0.67,0.96,4*02

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,40,1*48

$GBGSV,6,4,24,34,46,76,41,2,46,236,33,1,45,126,36,60,41,239,39,1*45

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,31,24,18,80,35,33,16,190,34,44,5,39,30,1*73

$GBGSV,3,1,10,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*76

$GBGSV,3,2,10,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*77

$GBGSV,3,3,10,33,16,190,34,44,5,39,32,5*75

$GBRMC,152133.000,A,2301.2578445,N,11421.9432482,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152133.000,2.856,0.185,0.184,0.255,2.033,2.040,2.841*73



2025-07-31 23:21:34:326 ==>> $GBGGA,152134.000,2301.2578547,N,11421.9432412,E,1,22,0.67,82.853,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.17,0.67,0.96,4*0E

$GBGSA,A,3,34,02,01,60,41,23,12,24,33,44,,,1.17,0.67,0.96,4*02

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,40,1*48

$GBGSV,6,4,24,34,46,76,40,2,46,236,33,1,45,126,36,60,41,239,39,1*44

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,31,24,18,80,34,33,16,190,34,44,5,39,30,1*72

$GBGSV,3,1,10,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*76

$GBGSV,3,2,10,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*77

$GBGSV,3,3,10,33,16,190,34,44,5,39,32,5*75

$GBRMC,152134.000,A,2301.2578547,N,11421.9432412,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152134.000,2.986,0.211,0.210,0.289,2.106,2.112,2.892*76



2025-07-31 23:21:34:680 ==>> [D][05:19:34][COMM]read battery soc:255


2025-07-31 23:21:35:326 ==>> $GBGGA,152135.000,2301.2578593,N,11421.9432364,E,1,22,0.67,82.844,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.17,0.67,0.96,4*0E

$GBGSA,A,3,34,02,01,60,41,23,12,24,33,44,,,1.17,0.67,0.96,4*02

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,35,1*45

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,40,1*48

$GBGSV,6,4,24,34,46,76,41,2,46,236,33,1,45,126,36,60,41,239,39,1*45

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,32,24,18,80,34,33,16,190,34,44,5,39,30,1*71

$GBGSV,3,1,10,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*76

$GBGSV,3,2,10,34,46,76,41,41,33,242,39,23,32,314,38,24,18,80,36,5*77

$GBGSV,3,3,10,33,16,190,34,44,5,39,32,5*75

$GBRMC,152135.000,A,2301.2578593,N,11421.9432364,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152135.000,2.914,0.203,0.202,0.280,2.063,2.069,2.839*7B



2025-07-31 23:21:36:321 ==>> $GBGGA,152136.000,2301.2578598,N,11421.9432239,E,1,22,0.67,82.856,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.17,0.67,0.96,4*0E

$GBGSA,A,3,34,02,01,60,41,23,12,24,33,44,,,1.17,0.67,0.96,4*02

$GBGSV,6,1,24,40,76,189,42,7,70,217,39,39,66,50,40,6,64,13,36,1*72

$GBGSV,6,2,24,16,63,18,38,3,61,190,40,10,57,221,37,9,55,347,34,1*44

$GBGSV,6,3,24,11,52,115,38,25,51,21,40,59,49,131,40,43,47,161,40,1*48

$GBGSV,6,4,24,34,46,76,40,2,46,236,33,1,45,126,36,60,41,239,39,1*44

$GBGSV,6,5,24,41,33,242,38,4,32,111,32,23,32,314,38,12,25,52,34,1*75

$GBGSV,6,6,24,5,21,255,32,24,18,80,34,33,16,190,34,44,5,39,30,1*71

$GBGSV,3,1,10,40,76,189,41,39,66,50,41,25,51,21,41,43,47,161,39,5*76

$GBGSV,3,2,10,34,46,76,40,41,33,242,39,23,32,314,38,24,18,80,36,5*76

$GBGSV,3,3,10,33,16,190,34,44,5,39,32,5*75

$GBRMC,152136.000,A,2301.2578598,N,11421.9432239,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152136.000,2.922,0.193,0.193,0.268,2.066,2.072,2.829*74



2025-07-31 23:21:36:640 ==>> [D][05:19:36][PROT]CLEAN,SEND:0
[D][05:19:36][PROT]index:1 1629955176
[D][05:19:36][PROT]is_send:0
[D][05:19:36][PROT]sequence_num:13
[D][05:19:36][PROT]retry_timeout:0
[D][05:19:36][PROT]retry_times:1
[D][05:19:36][PROT]send_path:0x2
[D][05:19:36][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:36][PROT]===========================================================
[W][05:19:36][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955176]
[D][05:19:36][PROT]===========================================================
[D][05:19:36][PROT]sending traceid [999999999990000E]
[D][05:19:36][PROT]Send_TO_M2M [1629955176]
[D][05:19:36][PROT]CLEAN:0
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:36][SAL ]sock send credit cnt[6]
[D][05:19:36][SAL ]sock send ind credit cnt[6]
[D][05:19:36][M2M ]m2m send data len[166]
[D][05:19:36][SAL ]Cellular task submsg id[10]
[D][05:19:36][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:36][CAT1]gsm read msg sub id: 15
[D][05:19:36][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:36][CAT1]Send

2025-07-31 23:21:36:715 ==>>  Data To Server[166][169] ... ->:
0053B984113311331133113311331B88BA27204F426AA25D8E095E2A6E589FAA7452F6338E49996EC70A28218EAC7C5E4C3D1D8CBAB7E70056402E0742797C007C7E655D075E58BDC17CE9E2BC16CB26A98DC4
[D][05:19:36][CAT1]<<< 
SEND OK

[D][05:19:36][CAT1]exec over: func id: 15, ret: 11
[D][05:19:36][CAT1]sub id: 15, ret: 11

[D][05:19:36][SAL ]Cellular task submsg id[68]
[D][05:19:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:36][M2M ]g_m2m_is_idle become true
[D][05:19:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:36][PROT]M2M Send ok [1629955176]


2025-07-31 23:21:36:745 ==>>                                          

2025-07-31 23:21:36:788 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:21:36:798 ==>> 检测【关闭GPS】
2025-07-31 23:21:36:812 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:21:37:206 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:37][GNSS]stop locating
[D][05:19:37][GNSS]stop event:8
[D][05:19:37][GNSS]GPS stop. ret=0
[D][05:19:37][GNSS]all continue location stop
[W][05:19:37][GNSS]stop locating
[D][05:19:37][GNSS]all sing location stop
[D][05:19:37][CAT1]gsm read msg sub id: 24
[D][05:19:37][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]exec over: func id: 24, ret: 6
[D][05:19:37][CAT1]sub id: 24, ret: 6



2025-07-31 23:21:37:360 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:21:37:375 ==>> 检测【清空消息队列2】
2025-07-31 23:21:37:408 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:21:37:515 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:37][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:21:37:649 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:21:37:664 ==>> 检测【轮动检测】
2025-07-31 23:21:37:673 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:21:37:727 ==>> 3A A3 01 00 A3 


2025-07-31 23:21:37:817 ==>> OFF_OUT1
OVER 150


2025-07-31 23:21:37:892 ==>> [D][05:19:37][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:21:38:152 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:21:38:166 ==>> [D][05:19:38][GNSS]recv submsg id[1]
[D][05:19:38][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:38][GNSS]location stop evt done evt


2025-07-31 23:21:38:226 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 23:21:38:429 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:21:38:444 ==>> 检测【关闭小电池】
2025-07-31 23:21:38:464 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:21:38:518 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:21:38:701 ==>> [D][05:19:38][COMM]read battery soc:255


2025-07-31 23:21:38:710 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:21:38:732 ==>> 检测【进入休眠模式】
2025-07-31 23:21:38:762 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:21:38:962 ==>> [W][05:19:38][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:38][COMM]Main Task receive event:28
[D][05:19:38][COMM]main task tmp_sleep_event = 8
[D][05:19:38][COMM]prepare to sleep
[D][05:19:38][CAT1]gsm read msg sub id: 12
[D][05:19:38][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:21:39:770 ==>> [D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 12, ret: 6
[D][05:19:39][M2M ]tcpclient close[4]
[D][05:19:39][SAL ]Cellular task submsg id[12]
[D][05:19:39][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:39][CAT1]gsm read msg sub id: 9
[D][05:19:39][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 9, ret: 6
[D][05:19:39][CAT1]sub id: 9, ret: 6

[D][05:19:39][SAL ]Cellular task submsg id[68]
[D][05:19:39][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:39][SAL ]socket close ind. id[4]
[D][05:19:39][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:39][COMM]1x1 frm_can_tp_send ok
[D][05:19:39][CAT1]pdpdeact urc len[22]


2025-07-31 23:21:40:045 ==>> [E][05:19:40][COMM]1x1 rx timeout
[D][05:19:40][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:21:40:150 ==>> [D][05:19:40][GNSS]handler GSMGet Base timeout


2025-07-31 23:21:40:565 ==>> [E][05:19:40][COMM]1x1 rx timeout
[E][05:19:40][COMM]1x1 tp timeout
[E][05:19:40][COMM]1x1 error -3.
[W][05:19:40][COMM]CAN STOP!
[D][05:19:40][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:40][COMM]------------ready to Power off Acckey 1------------
[D][05:19:40][COMM]------------ready to Power off Acckey 2------------
[D][05:19:40][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:40][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1294
[D][05:19:40][COMM]bat sleep fail, reason:-1
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:40][COMM]accel parse set 0
[D][05:19:40][COMM]imu rest ok. 111501
[D][05:19:40][COMM]imu sleep 0
[W][05:19:40][COMM]now sleep


2025-07-31 23:21:40:800 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:21:40:814 ==>> 检测【检测33V休眠电流】
2025-07-31 23:21:40:845 ==>> 开始33V电流采样
2025-07-31 23:21:40:872 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:40:902 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:21:41:910 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:21:41:973 ==>> Current33V:????:18.06

2025-07-31 23:21:42:419 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:42:428 ==>> 【检测33V休眠电流】通过,【18.06uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:21:42:438 ==>> 该项需要延时执行
2025-07-31 23:21:44:435 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:21:44:450 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:21:44:471 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:21:44:527 ==>> 1A A1 00 00 FC 
Get AD_V2 1643mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2747mV
Get AD_V6 2022mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:21:45:471 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:21:45:486 ==>> 检测【打开小电池2】
2025-07-31 23:21:45:513 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:21:45:527 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:21:45:751 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:21:45:760 ==>> 该项需要延时执行
2025-07-31 23:21:46:258 ==>> 此处延时了:【500】毫秒
2025-07-31 23:21:46:273 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:21:46:300 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:21:46:325 ==>> 5A A5 02 5A A5 


2025-07-31 23:21:46:424 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:21:46:537 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:21:46:547 ==>> 该项需要延时执行
2025-07-31 23:21:47:043 ==>> 此处延时了:【500】毫秒
2025-07-31 23:21:47:058 ==>> 检测【进入休眠模式2】
2025-07-31 23:21:47:079 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:21:47:110 ==>> [D][05:19:46][COMM]------------ready to Power on Acckey 1------------
[D][05:19:46][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:46][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:46][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:46][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:46][COMM]----- get Acckey 1 and value:1------------
[W][05:19:46][COMM]CAN START!
[D][05:19:46][CAT1]gsm read msg sub id: 12
[D][05:19:46][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:46][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 117964
[D][05:19:46][COMM][Audio]exec status ready.
[D][05:19:46][CAT1]<<< 
OK


2025-07-31 23:21:47:163 ==>> 

[D][05:19:46][CAT1]exec over: func id: 12, ret: 6
[D][05:19:46][COMM]imu wakeup ok. 117978
[D][05:19:46][COMM]imu wakeup 1
[W][05:19:46][COMM]wake up system, wakeupEvt=0x80
[D][05:19:46][COMM]frm_can_weigth_power_set 1
[D][05:19:46][COMM]Clear Sleep Block Evt
[D][05:19:46][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:46][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:21:47:253 ==>> [D][05:19:47][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:47][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:21:47:343 ==>> [E][05:19:47][COMM]1x1 rx timeout
[D][05:19:47][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:21:47:449 ==>> [D][05:19:47][COMM]msg 02A0 loss. last_tick:117949. cur_tick:118457. period:50
[D][05:19:47][COMM]msg 

2025-07-31 23:21:47:508 ==>> 02A4 loss. last_tick:117949. cur_tick:118457. period:50
[D][05:19:47][COMM]msg 02A5 loss. last_tick:117949. cur_tick:118458. period:50
[D][05:19:47][COMM]msg 02A6 loss. last_tick:117949. cur_tick:118458. period:50
[D][05:19:47][COMM]msg 02A7 loss. last_tick:117949. cur_tick:118458. period:50
[D][05:19:47][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 118459
[D][05:19:47][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 118459


2025-07-31 23:21:47:844 ==>> [E][05:19:47][COMM]1x1 rx timeout
[E][05:19:47][COMM]1x1 tp timeout
[E][05:19:47][COMM]1x1 error -3.
[D][05:19:47][COMM]Main Task receive event:28 finished processing
[D][05:19:47][COMM]Main Task receive event:28
[D][05:19:47][COMM]prepare to sleep
[D][05:19:47][CAT1]gsm read msg sub id: 12
[D][05:19:47][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:47][CAT1]<<< 
OK

[D][05:19:47][CAT1]exec over: func id: 12, ret: 6
[D][05:19:47][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:47][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:21:48:148 ==>> [D][05:19:47][COMM]msg 0220 loss. last_tick:117949. cur_tick:118953. period:100
[D][05:19:47][COMM]msg 0221 loss. last_tick:117949. cur_tick:118953. period:100
[D][05:19:47][COMM]msg 0224 loss. last_tick:117949. cur_tick:118954. period:100
[D][05:19:47][COMM]msg 0260 loss. last_tick:117949. cur_tick:118954. period:100
[D][05:19:47][COMM]msg 0280 loss. last_tick:117949. cur_tick:118954. period:100
[D][05:19:47][COMM]msg 02C0 loss. last_tick:117949. cur_tick:118955. period:100
[D][05:19:47][COMM]msg 02C1 loss. last_tick:117949. cur_tick:118955. period:100
[D][05:19:47][COMM]msg 02C2 loss. last_tick:117949. cur_tick:118955. period:100
[D][05:19:47][COMM]msg 02E0 loss. last_tick:117949. cur_tick:118956. period:100
[D][05:19:47][COMM]msg 02E1 loss. last_tick:117949. cur_tick:118956. period:100
[D][05:19:47][COMM]msg 02E2 loss. last_tick:117949. cur_tick:118957. period:100
[D][05:19:47][COMM]msg 0300 loss. last_tick:117949. cur_tick:118957. period:100
[D][05:19:47][COMM]msg 0301 loss. last_tick:117949. cur_tick:118957. period:100
[D][05:19:47][COMM]bat msg 0240 loss. last_tick:117949. cur_tick:118958. period:100. j,i:1 54
[D][05:19:47][COMM]bat msg 0241 loss. last_tick:117949. cur_tick:118958. period:100. j,i:2 55
[D][05:19:47][COMM]bat

2025-07-31 23:21:48:253 ==>>  msg 0242 loss. last_tick:117949. cur_tick:118958. period:100. j,i:3 56
[D][05:19:47][COMM]bat msg 0244 loss. last_tick:117949. cur_tick:118959. period:100. j,i:5 58
[D][05:19:47][COMM]bat msg 024E loss. last_tick:117949. cur_tick:118959. period:100. j,i:15 68
[D][05:19:47][COMM]bat msg 024F loss. last_tick:117949. cur_tick:118960. period:100. j,i:16 69
[D][05:19:47][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 118960
[D][05:19:47][COMM]CAN message bat fault change: 0x00000000->0x0001802E 118960
[D][05:19:47][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 118961
                                                                              

2025-07-31 23:21:48:435 ==>> [D][05:19:48][COMM]msg 0222 loss. last_tick:117949. cur_tick:119455. period:150
[D][05:19:48][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 119456


2025-07-31 23:21:48:525 ==>>                                                                                      OMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:48][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:21:48:720 ==>> [E][05:19:48][COMM]1x1 rx timeout
[E][05:19:48][COMM]1x1 tp timeout
[E][05:19:48][COMM]1x1 error -3.
[W][05:19:48][COMM]CAN STOP!
[D][05:19:48][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:48][COMM]------------ready to Power off Acckey 1------------
[D][05:19:48][COMM]------------ready to Power off Acckey 2------------
[D][05:19:48][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:48][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 99
[D][05:19:48][COMM]bat sleep fail, reason:-1
[D][05:19:48][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:48][COMM]accel parse set 0
[D][05:19:48][COMM]imu rest ok. 119656
[D][05:19:48][COMM]imu sleep 0
[W][05:19:48][COMM]now sleep


2025-07-31 23:21:48:859 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:21:48:868 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:21:48:893 ==>> 开始小电池电流采样
2025-07-31 23:21:48:908 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:48:962 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:21:49:973 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:21:50:019 ==>> CurrentBattery:ƽ��:70.04

2025-07-31 23:21:50:482 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:50:491 ==>> 【检测小电池休眠电流】通过,【70.04uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:21:50:518 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:21:50:549 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:21:50:632 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:21:50:831 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:21:50:843 ==>> 该项需要延时执行
2025-07-31 23:21:50:859 ==>> [D][05:19:50][COMM]------------ready to Power on Acckey 1------------
[D][05:19:50][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:50][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:50][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 12
[D][05:19:50][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:50][COMM]----- get Acckey 1 and value:1------------
[W][05:19:50][COMM]CAN START!
[D][05:19:50][CAT1]gsm read msg sub id: 12
[D][05:19:50][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:50][COMM]CAN message bat fault change: 0x0001802E->0x00000000 121746
[D][05:19:50][COMM][Audio]exec status ready.
[D][05:19:50][CAT1]<<< 
OK

[D][05:19:50][CAT1]exec over: func id: 12, ret: 6
[D][05:19:50][COMM]imu wakeup ok. 121760
[D][05:19:50][COMM]imu wakeup 1
[W][05:19:50][COMM]wake up system, wakeupEvt=0x80
[D][05:19:50][COMM]frm_can_weigth_power_set 1
[D][05:19:50][COMM]Clear Sleep Block Evt
[D][05:19:50][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:50][COMM]1x1 frm_can_tp_send ok
[D][05:19:50][COMM]read battery soc:0


2025-07-31 23:21:51:134 ==>> [E][05:19:51][COMM]1x1 rx timeout
[D][05:19:51][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:21:51:239 ==>> [D][05:19:51][COMM]msg 02A0 loss. last_tick:121728. cur_tick:122240. period:50
[D][05:19:51][COMM]msg 02A4 loss. last_tick:121728. cur_tick:122241. period:50
[D][05:19:51][COMM]msg 02A5 loss. last_tick:121728. cur_tick:122241. perio

2025-07-31 23:21:51:284 ==>> d:50
[D][05:19:51][COMM]msg 02A6 loss. last_tick:121728. cur_tick:122242. period:50
[D][05:19:51][COMM]msg 02A7 loss. last_tick:121728. cur_tick:122242. period:50
[D][05:19:51][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 122242
[D][05:19:51][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 122243


2025-07-31 23:21:51:344 ==>> 此处延时了:【500】毫秒
2025-07-31 23:21:51:359 ==>> 检测【检测唤醒】
2025-07-31 23:21:51:386 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:21:52:087 ==>> [W][05:19:51][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:51][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:51][FCTY]==========Modules-nRF5340 ==========
[D][05:19:51][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:51][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:51][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:51][FCTY]DeviceID    = 460130071539523
[D][05:19:51][FCTY]HardwareID  = 867222087631265
[D][05:19:51][FCTY]MoBikeID    = 9999999999
[D][05:19:51][FCTY]LockID      = FFFFFFFFFF
[D][05:19:51][FCTY]BLEFWVersion= 105
[D][05:19:51][FCTY]BLEMacAddr   = C821C4F48795
[D][05:19:51][FCTY]Bat         = 3784 mv
[D][05:19:51][FCTY]Current     = 0 ma
[D][05:19:51][FCTY]VBUS        = 2600 mv
[D][05:19:51][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:51][FCTY]Ext battery vol = 32, adc = 1298
[D][05:19:51][FCTY]Acckey1 vol = 5528 mv, Acckey2 vol = 0 mv
[D][05:19:51][FCTY]Bike Type flag is invalied
[D][05:19:51][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:51][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:51][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:51][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:51][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:51][FCTY]CAT1_GNSS_VERSION = V3465b5

2025-07-31 23:21:52:150 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:21:52:178 ==>> 检测【关机】
2025-07-31 23:21:52:198 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:21:52:208 ==>> b1
[D][05:19:51][FCTY]Bat1         = 3796 mv
[D][05:19:51][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:51][FCTY]==========Modules-nRF5340 ==========
[E][05:19:51][COMM]1x1 rx timeout
[E][05:19:51][COMM]1x1 tp timeout
[E][05:19:51][COMM]1x1 error -3.
[D][05:19:51][COMM]Main Task receive event:28 finished processing
[D][05:19:51][COMM]Main Task receive event:65
[D][05:19:51][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:51][COMM]Main Task receive event:65 finished processing
[D][05:19:51][COMM]Main Task receive event:60
[D][05:19:51][COMM]smart_helmet_vol=255,255
[D][05:19:51][COMM]report elecbike
[W][05:19:51][PROT]remove success[1629955191],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:51][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:19:51][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:51][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:51][PROT]index:0
[D][05:19:51][PROT]is_send:1
[D][05:19:51][PROT]sequence_num:14
[D][05:19:51][PROT]retry_timeout:0
[D][05:19:51][PROT]retry_times:3
[D][05:19:51][PROT]send_path:0x3
[D][05:19

2025-07-31 23:21:52:297 ==>> :51][PROT]msg_type:0x5d03
[D][05:19:51][PROT]===========================================================
[D][05:19:51][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:19:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955191]
[D][05:19:51][PROT]===========================================================
[D][05:19:51][PROT]Sending traceid[999999999990000F]
[D][05:19:51][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:51][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:51][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:51][PROT]add success [1629955191],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:51][COMM]Main Task receive event:60 finished processing
[D][05:19:51][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:51][SAL ]open socket ind id[4], rst[0]
[D][05:19:51][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:51][SAL ]Cellular task submsg id[8]
[D][05:19:51][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:51][SAL ]domai

2025-07-31 23:21:52:402 ==>> n[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:51][CAT1]gsm read msg sub id: 8
[D][05:19:51][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:51][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:51][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:51][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:51][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:51][CAT1]<<< 
+CME ERROR: 100

[D][05:19:51][COMM]msg 0220 loss. last_tick:121728. cur_tick:122738. period:100
[D][05:19:51][COMM]msg 0221 loss. last_tick:121728. cur_tick:122739. period:100
[D][05:19:51][COMM]msg 0224 loss. last_tick:121728. cur_tick:122739. period:100
[D][05:19:51][COMM]msg 0260 loss. last_tick:121728. cur_tick:122740. period:100
[D][05:19:51][COMM]msg 0280 loss. last_tick:121728. cur_tick:122740. period:100
[D][05:19:51][COMM]msg 02C0 loss. last_tick:121728. cur_tick:122740. period:100
[D][05:19:51][COMM]msg 02C1 loss. last_tick:121728. cur_tick:122741. period:100
[D][05:19:51][COMM]msg 02C2 loss. last_tick:121728. cur_tick:122741. period:100
[D][05:19:51][COMM]msg 02E0 loss. last_tick:121728. cur_tick:122741. period:100
[D][05:19:51][COMM]msg 02E1 loss. last_tick:121728. cur_tick:122742. period:100
[D][05:19:

2025-07-31 23:21:52:507 ==>> 51][COMM]msg 02E2 loss. last_tick:121728. cur_tick:122742. period:100
[D][05:19:51][COMM]msg 0300 loss. last_tick:121728. cur_tick:122742. period:100
[D][05:19:51][COMM]msg 0301 loss. last_tick:121728. cur_tick:122742. period:100
[D][05:19:51][COMM]bat msg 0240 loss. last_tick:121728. cur_tick:122743. period:100. j,i:1 54
[D][05:19:51][COMM]bat msg 0241 loss. last_tick:121728. cur_tick:122744. period:100. j,i:2 55
[D][05:19:51][COMM]bat msg 0242 loss. last_tick:121728. cur_tick:122744. period:100. j,i:3 56
[D][05:19:51][COMM]bat msg 0244 loss. last_tick:121728. cur_tick:122744. period:100. j,i:5 58
[D][05:19:51][COMM]bat msg 024E loss. last_tick:121728. cur_tick:122745. period:100. j,i:15 68
[D][05:19:51][COMM]bat msg 024F loss. last_tick:121728. cur_tick:122745. period:100. j,i:16 69
[D][05:19:51][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 122745
[D][05:19:51][COMM]CAN message bat fault change: 0x00000000->0x0001802E 122746
[D][05:19:51][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 122746


2025-07-31 23:21:53:108 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 23:21:53:168 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:21:53:214 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:21:53:318 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    [PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955192]
[D][05:19:52][PROT]===========================================================
[D][05:19:52][PROT]Sending traceid[9999999999900010]
[D][05:19:52][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:52][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:52][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:52][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D

2025-07-31 23:21:53:423 ==>> ][05:19:52][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:52][COMM]Receive Bat Lock cmd 0
[D][05:19:52][COMM]VBUS is 1
[D][05:19:52][COMM]Main Task receive event:61
[D][05:19:52][COMM][D301]:type:3, trace id:280
[D][05:19:52][COMM]id[], hw[000
[D][05:19:52][COMM]get mcMaincircuitVolt error
[D][05:19:52][COMM]get mcSubcircuitVolt error
[D][05:19:52][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:52][COMM]BAT CAN get state1 Fail 204
[D][05:19:52][COMM]BAT CAN get soc Fail, 204
[D][05:19:52][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:52][COMM]BAT CAN get state2 fail 204
[D][05:19:52][COMM]get bat work mode err
[W][05:19:52][PROT]remove success[1629955192],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:19:52][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:52][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:52][PROT]add success [1629955192],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:52][COMM]Main Task receive event:61 finished processing
[D][05:19:52][COMM]--->crc16:0xb8a
[D][05:19:52][COMM]

2025-07-31 23:21:53:528 ==>> read file success
[W][05:19:52][COMM][Audio].l:[936].close hexlog save
[D][05:19:52][COMM]accel parse set 1
[D][05:19:52][COMM][Audio]mon:9,05:19:52
[D][05:19:52][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:52][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:52][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:52][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:52][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:52][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:52][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:52][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:52][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:52][COMM]f:[ec800m_audio_pla

2025-07-31 23:21:53:633 ==>> y_process].l:[968].pkg_num:6
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:52][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:52][COMM]arm_hub_enable: hub power: 0
[D][05:19:52][HSDK]hexlog index save 0 3840 250 @ 0 : 0
[D][05:19:52][HSDK]write save hexlog index [0]
[D][05:19:52][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:52][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:52][COMM]read battery soc:255
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l

2025-07-31 23:21:53:708 ==>> :[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:52][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:52][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:52][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:52][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 23:21:53:813 ==>> [W][05:19:53][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:53][COMM]arm_hub_enable: hub power: 0
[D][05:19:53][HSDK]hexlog index save 0 3840 250 @ 0 : 0
[D][05:19:53][HSDK]write save hexlog index [0]
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:21:54:088 ==>> [D][05:19:54][COMM]exit wheel stolen mode.
[D][05:19:54][COMM]Main Task receive event:68
[D][05:19:54][COMM]handlerWheelStolen evt type = 2.
[E][05:19:54][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:54][GNSS]stop locating
[D][05:19:54][GNSS]all continue location stop
[D][05:19:54][COMM]Main Task receive event:68 finished processing


2025-07-31 23:21:54:193 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:21:54:268 ==>> [W][05:19:54][COMM]Power Off


2025-07-31 23:21:54:449 ==>> [W][05:19:54][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:54][COMM]arm_hub_enable: hub power: 0
[D][05:19:54][HSDK]hexlog index save 0 3840 250 @ 0 : 0
[D][05:19:54][HSDK]write save hexlog index [0]
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:21:54:488 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:21:54:510 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:21:54:526 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:21:54:615 ==>> 5A A5 02 5A A5 


2025-07-31 23:21:54:720 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:21:54:769 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:21:54:796 ==>> 检测【检测小电池关机电流】
2025-07-31 23:21:54:813 ==>> 开始小电池电流采样
2025-07-31 23:21:54:830 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:54:856 ==>> [D][05:19:54][FCTY]get_ext_48v_vol retry i =  

2025-07-31 23:21:54:872 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:21:55:878 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:21:55:940 ==>> CurrentBattery:ƽ��:69.35

2025-07-31 23:21:56:387 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:21:56:397 ==>> 【检测小电池关机电流】通过,【69.35uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:21:56:777 ==>> MES过站成功
2025-07-31 23:21:56:789 ==>> #################### 【测试结束】 ####################
2025-07-31 23:21:56:808 ==>> 关闭5V供电
2025-07-31 23:21:56:823 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:21:56:925 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:21:57:810 ==>> 关闭5V供电成功
2025-07-31 23:21:57:843 ==>> 关闭33V供电
2025-07-31 23:21:57:853 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:21:57:920 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:21:58:824 ==>> 关闭33V供电成功
2025-07-31 23:21:58:839 ==>> 关闭3.7V供电
2025-07-31 23:21:58:854 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:21:58:932 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:21:59:694 ==>>  

2025-07-31 23:21:59:831 ==>> 关闭3.7V供电成功
