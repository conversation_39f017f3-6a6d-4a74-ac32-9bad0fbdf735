2025-07-31 17:56:11:906 ==>> MES查站成功:
查站序号:P510001005313481验证通过
2025-07-31 17:56:11:910 ==>> 扫码结果:P510001005313481
2025-07-31 17:56:11:912 ==>> 当前测试项目:SE51_PCBA
2025-07-31 17:56:11:913 ==>> 测试参数版本:2024.10.11
2025-07-31 17:56:11:915 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 17:56:11:916 ==>> 检测【打开透传】
2025-07-31 17:56:11:918 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 17:56:12:049 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 17:56:12:259 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 17:56:12:272 ==>> 检测【检测接地电压】
2025-07-31 17:56:12:274 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 17:56:12:339 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 17:56:12:555 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 17:56:12:557 ==>> 检测【打开小电池】
2025-07-31 17:56:12:559 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 17:56:12:646 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 17:56:12:830 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 17:56:12:832 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 17:56:12:834 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 17:56:12:947 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 17:56:13:101 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 17:56:13:103 ==>> 检测【等待设备启动】
2025-07-31 17:56:13:106 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:14:147 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:15:194 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:16:247 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:17:296 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:18:346 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:19:389 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:20:431 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:21:482 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:22:532 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 17:56:23:584 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 17:56:23:601 ==>> #################### 【测试结束】 ####################
2025-07-31 17:56:23:620 ==>> 关闭5V供电
2025-07-31 17:56:23:622 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 17:56:23:738 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 17:56:24:623 ==>> 关闭5V供电成功
2025-07-31 17:56:24:626 ==>> 关闭33V供电
2025-07-31 17:56:24:628 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 17:56:24:744 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 17:56:25:629 ==>> 关闭33V供电成功
2025-07-31 17:56:25:632 ==>> 关闭3.7V供电
2025-07-31 17:56:25:634 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 17:56:25:750 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 17:56:25:900 ==>>  

