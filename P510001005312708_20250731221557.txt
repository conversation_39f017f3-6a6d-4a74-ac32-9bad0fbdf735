2025-07-31 22:15:57:415 ==>> 扫码结果:P510001005312708
2025-07-31 22:15:57:424 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:15:57:426 ==>> 测试参数版本:2024.10.11
2025-07-31 22:15:57:427 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:15:57:429 ==>> 检测【打开透传】
2025-07-31 22:15:57:430 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:15:57:495 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:15:57:824 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:15:57:834 ==>> 检测【检测接地电压】
2025-07-31 22:15:57:836 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:15:57:903 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:15:58:121 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:15:58:124 ==>> 检测【打开小电池】
2025-07-31 22:15:58:126 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:15:58:205 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:15:58:404 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:15:58:407 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:15:58:409 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:15:58:491 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:15:58:676 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:15:58:679 ==>> 检测【等待设备启动】
2025-07-31 22:15:58:681 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:15:58:991 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:15:59:173 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:15:59:709 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:00:736 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:00:906 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:01:105 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:16:01:609 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:01:774 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:16:01:776 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:02:285 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:02:480 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 22:16:02:802 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:02:997 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:03:162 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:16:03:665 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:03:832 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:03:862 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:16:04:346 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:04:541 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:16:04:867 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:05:054 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:05:220 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:16:05:725 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:05:908 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:05:923 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:16:06:427 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:06:594 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 22:16:06:943 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:07:109 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:07:306 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 22:16:07:792 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:07:972 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:16:07:974 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 22:16:08:492 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:08:658 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 22:16:09:010 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:16:09:017 ==>> #################### 【测试结束】 ####################
2025-07-31 22:16:09:164 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:09:217 ==>> 关闭5V供电
2025-07-31 22:16:09:220 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:16:09:299 ==>> 5A A5 04 5A A5 


2025-07-31 22:16:09:359 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 22:16:09:389 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:16:09:876 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:10:042 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 22:16:10:222 ==>> 关闭5V供电成功
2025-07-31 22:16:10:226 ==>> 关闭33V供电
2025-07-31 22:16:10:228 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:16:10:298 ==>> 5A A5 02 5A A5 


2025-07-31 22:16:10:389 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:16:10:556 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:10:737 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 22:16:11:223 ==>> 关闭33V供电成功
2025-07-31 22:16:11:225 ==>> 关闭3.7V供电
2025-07-31 22:16:11:228 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:16:11:238 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:16:11:298 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:16:11:665 ==>>  

2025-07-31 22:16:12:230 ==>> 关闭3.7V供电成功
