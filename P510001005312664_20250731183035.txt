2025-07-31 18:30:35:576 ==>> MES查站成功:
查站序号:P510001005312664验证通过
2025-07-31 18:30:35:580 ==>> 扫码结果:P510001005312664
2025-07-31 18:30:35:582 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:30:35:583 ==>> 测试参数版本:2024.10.11
2025-07-31 18:30:35:585 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:30:35:586 ==>> 检测【打开透传】
2025-07-31 18:30:35:588 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:30:35:684 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:30:35:881 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:30:35:900 ==>> 检测【检测接地电压】
2025-07-31 18:30:35:902 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:30:36:005 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150
 

2025-07-31 18:30:36:214 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:30:36:216 ==>> 检测【打开小电池】
2025-07-31 18:30:36:218 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:30:36:278 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:30:36:498 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:30:36:500 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:30:36:503 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:30:36:585 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:30:36:775 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:30:36:778 ==>> 检测【等待设备启动】
2025-07-31 18:30:36:781 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:37:817 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:38:861 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:39:900 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:40:952 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:41:989 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:43:020 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:44:065 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:45:098 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:46:132 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:30:47:170 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 18:30:47:172 ==>> #################### 【测试结束】 ####################
2025-07-31 18:30:47:202 ==>> 关闭5V供电
2025-07-31 18:30:47:207 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:30:47:276 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:30:48:209 ==>> 关闭5V供电成功
2025-07-31 18:30:48:212 ==>> 关闭33V供电
2025-07-31 18:30:48:215 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:30:48:284 ==>> 5A A5 02 5A A5 


2025-07-31 18:30:48:374 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:30:49:216 ==>> 关闭33V供电成功
2025-07-31 18:30:49:219 ==>> 关闭3.7V供电
2025-07-31 18:30:49:221 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:30:49:278 ==>> 6A A6 02 A6 6A 


2025-07-31 18:30:49:383 ==>> Battery OFF
OVER 150


2025-07-31 18:30:49:751 ==>>  

2025-07-31 18:30:50:224 ==>> 关闭3.7V供电成功
