2025-07-31 21:40:00:994 ==>> MES查站成功:
查站序号:P51000100531288C验证通过
2025-07-31 21:40:00:997 ==>> 扫码结果:P51000100531288C
2025-07-31 21:40:00:999 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:40:01:000 ==>> 测试参数版本:2024.10.11
2025-07-31 21:40:01:002 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:40:01:003 ==>> 检测【打开透传】
2025-07-31 21:40:01:005 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:40:01:084 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:40:01:390 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:40:01:445 ==>> 检测【检测接地电压】
2025-07-31 21:40:01:447 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:40:01:576 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:40:01:725 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:40:01:727 ==>> 检测【打开小电池】
2025-07-31 21:40:01:730 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:40:01:775 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:40:02:021 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:40:02:024 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:40:02:026 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:40:02:084 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:40:02:313 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:40:02:316 ==>> 检测【等待设备启动】
2025-07-31 21:40:02:318 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:40:03:358 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:40:04:403 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:40:04:590 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:40:04:787 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:40:05:423 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:40:05:438 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:40:05:498 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:40:05:900 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:40:06:374 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:40:06:521 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:40:06:524 ==>> 检测【产品通信】
2025-07-31 21:40:06:527 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:40:06:665 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:40:06:813 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:40:06:816 ==>> 检测【初始化完成检测】
2025-07-31 21:40:06:819 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:40:07:033 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:40:07:105 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:40:07:108 ==>> 检测【关闭大灯控制1】
2025-07-31 21:40:07:111 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:40:07:246 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:40:07:393 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:40:07:396 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:40:07:398 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:40:07:428 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:40:07:623 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:40:07:688 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:40:07:691 ==>> 检测【关闭仪表供电】
2025-07-31 21:40:07:692 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:40:07:881 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:40:07:969 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:40:07:972 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:40:07:977 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:40:08:139 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:40:08:329 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:40:08:331 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:40:08:332 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:40:08:445 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:40:08:669 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:40:08:671 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:40:08:674 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:40:08:844 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:40:09:026 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:40:09:029 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:40:09:030 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:40:09:087 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:40:09:147 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 21


2025-07-31 21:40:09:222 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 21:40:09:314 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:40:09:316 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:40:09:317 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:40:09:372 ==>> 5A A5 03 5A A5 


2025-07-31 21:40:09:477 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:40:09:602 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:40:09:605 ==>> 该项需要延时执行
2025-07-31 21:40:09:977 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E0

2025-07-31 21:40:10:008 ==>> 0C71E22217->0x0008F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 21:40:10:450 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:40:10:750 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:40:11:249 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17

2025-07-31 21:40:11:354 ==>> :55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05

2025-07-31 21:40:11:459 ==>> :17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][P

2025-07-31 21:40:11:549 ==>> ROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
                                                                                                                                          

2025-07-31 21:40:12:494 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 21:40:13:240 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:40:13:480 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:40:13:615 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:40:13:619 ==>> 检测【33V输入电压ADC】
2025-07-31 21:40:13:622 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:40:13:893 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:17:57][COMM]adc read out 24v adc:1318  volt:33336 mv
[D][05:17:57][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3361  volt:2707 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:40:14:170 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:40:14:173 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:40:14:176 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:40:14:292 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1989mV
Get AD_V7 1091mV
OVER 150


2025-07-31 21:40:14:458 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:14:461 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:40:14:489 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:14:492 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:40:14:495 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 21:40:14:503 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:40:14:521 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:40:14:524 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:40:14:551 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:40:14:554 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:40:14:587 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:40:14:589 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:40:14:698 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2763mV
Get AD_V6 1989mV
Get AD_V7 1090mV
OVER 150


2025-07-31 21:40:14:804 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][CO

2025-07-31 21:40:14:848 ==>> MM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10023


2025-07-31 21:40:14:883 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:14:894 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:40:14:914 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:14:916 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:40:14:918 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 21:40:14:945 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:40:14:948 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:40:14:974 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:40:14:976 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:40:15:005 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:40:15:007 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:40:15:092 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2763mV
Get AD_V6 1990mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:40:15:245 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 21:40:15:295 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:15:297 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:40:15:326 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:15:329 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:40:15:330 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 21:40:15:357 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:40:15:360 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:40:15:387 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:40:15:390 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:40:15:422 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:40:15:424 ==>> 检测【打开WIFI(1)】
2025-07-31 21:40:15:426 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:40:15:769 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:

2025-07-31 21:40:15:799 ==>> 59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:40:15:968 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:40:15:971 ==>> 检测【清空消息队列(1)】
2025-07-31 21:40:15:972 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:40:16:260 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088082104

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541519

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][HSDK][0

2025-07-31 21:40:16:305 ==>> ] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:40:16:395 ==>>                                                                               [D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:40:16:500 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:40:16:560 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:40:16:563 ==>> 检测【打开GPS(1)】
2025-07-31 21:40:16:565 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:40:16:775 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:40:16:906 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:40:16:910 ==>> 检测【打开GSM联网】
2025-07-31 21:40:16:912 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:40:17:261 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] re

2025-07-31 21:40:17:366 ==>> ady to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6
                                         

2025-07-31 21:40:17:463 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:40:17:466 ==>> 检测【打开仪表供电1】
2025-07-31 21:40:17:470 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:40:17:747 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPW

2025-07-31 21:40:17:777 ==>> R=1



2025-07-31 21:40:18:008 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:40:18:011 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:40:18:013 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:40:18:183 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:40:18:309 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:40:18:312 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:40:18:314 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:40:18:531 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33386]
[D][05:18:02][COMM]13729 imu init OK


2025-07-31 21:40:18:600 ==>> 【读取主控ADC采集的仪表电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:40:18:605 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:40:18:607 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:18:773 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:40:18:898 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:40:18:901 ==>> 检测【AD_V20电压】
2025-07-31 21:40:18:903 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:19:004 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:40:19:109 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:40:19:319 ==>>                                                                                              

$GBGSV,2,1,07,33,,,41,60,,,40,25,,,40,16,,,38,1*7A

$GBGSV,2,2,07,5,,,42,7,,,39,59,,,38,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1647.872,1647.872,52.620,2097152,2097152,2097152*4C

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

[D][05:18:03][COMM]read battery soc:255


2025-07-31 21:40:19:424 ==>> 本次取值间隔时间:417ms
2025-07-31 21:40:19:458 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:19:501 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:40:19:559 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:40:19:709 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:40:19:964 ==>> 本次取值间隔时间:392ms
2025-07-31 21:40:19:994 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:20:099 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:40:20:249 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,33,,,41,60,,,40,25,,,40,59,,,40,1*7B

$GBGSV,4,2,15,40,,,39,39,,,38,1,,,38,16,,,37,1*42

$GBGSV,4,3,15,14,,,37,41,,,37,7,,,36,44,,,36,1*42

$GBGSV,4,4,15,6,,,36,2,,,35,4,,,34,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1558.787,1558.787,49.822,2097152,2097152,2097152*4A



2025-07-31 21:40:20:354 ==>> 本次取值间隔时间:250ms
2025-07-31 21:40:20:384 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:20:489 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:40:20:579 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:40:20:639 ==>> 本次取值间隔时间:136ms
2025-07-31 21:40:20:697 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:40:20:807 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:40:20:882 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 21:40:21:004 ==>> 本次取值间隔时间:195ms
2025-07-31 21:40:21:097 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:40:21:100 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:40:21:104 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:40:21:175 ==>> 3A A3 02 00 A3 


2025-07-31 21:40:21:280 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,41,60,,,40,25,,,40,59,,,40,1*7E

$GBGSV,6,2,21,3,,,40,39,,,39,40,,,38,1,,,38,1*73

$GBGSV,6,3,21,41,,,38,14,,,37,7,,,37,16,,,36,1*4E

$GBGSV,6,4,21,44,,,36,6,,,35,2,,,35,34,,,35,1*77

$GBGSV,6,5,21,4,,,34,5,,,32,38,,,31,23,,,45,1*78

$GBGSV,6,6,21,24,,,37,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1531.769,1531.769,48.997,2097152,2097152,2097152*44

[D][05:18:05][CO

2025-07-31 21:40:21:311 ==>> MM]read battery soc:255
OFF_OUT2
OVER 150


2025-07-31 21:40:21:392 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:40:21:395 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:40:21:398 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:40:21:582 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:40:22:291 ==>> $GBGGA,134026.085,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,60,,,40,25,,,40,59,,,40,1*7C

$GBGSV,6,2,23,3,,,40,39,,,39,24,,,39,40,,,38,1*47

$GBGSV,6,3,23,1,,,38,41,,,38,14,,,37,7,,,37,1*74

$GBGSV,6,4,23,16,,,37,23,,,36,44,,,36,6,,,35,1*47

$GBGSV,6,5,23,2,,,35,34,,,35,12,,,35,4,,,33,1*70

$GBGSV,6,6,23,5,,,32,38,,,32,9,,,36,1*75

$GBRMC,134026.085,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134026.085,0.000,1532.059,1532.059,48.998,2097152,2097152,2097152*5A



2025-07-31 21:40:22:426 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:40:22:576 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:40:22:771 ==>> $GBGGA,134026.585,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,59,,,41,60,,,40,25,,,40,1*7A

$GBGSV,6,2,24,3,,,40,39,,,39,24,,,39,40,,,38,1*40

$GBGSV,6,3,24,1,,,38,41,,,38,14,,,37,7,,,37,1*73

$GBGSV,6,4,24,16,,,37,23,,,37,44,,,36,34,,,36,1*73

$GBGSV,6,5,24,9,,,35,6,,,35,2,,,35,12,,,35,1*4D

$GBGSV,6,6,24,10,,,34,4,,,33,38,,,33,5,,,32,1*7D

$GBRMC,134026.585,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134026.585,0.000,1530.486,1530.486,48.946,2097152,2097152,2097152*5C



2025-07-31 21:40:23:256 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 21:40:23:451 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:40:23:807 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,134027.565,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,59,,,41,60,,,40,25,,,40,1*79

$GBGSV,7,2,26,3,,,40,39,,,39,24,,,39,41,,,39,1*43

$GBGSV,7,3,26,40,,,38,1,,,38,14,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,23,,,37,44,,,36,34,,,36,1*70

$GBGSV,7,5,26,9,,,35,6,,,35,2,,,35,12,,,35,1*4E

$GBGSV,7,6,26,10,,,34,13,,,34,38,,,33,4,,,32,1*4E

$GBGSV,7,7,26,5,,,32,8,,,29,1*75

$GBRMC,134027.565,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134027.565,0.000,1513.235,1513.235,48.418,2097152,2097152,2097152*55



2025-07-31 21:40:24:478 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 21:40:24:482 ==>> #################### 【测试结束】 ####################
2025-07-31 21:40:24:550 ==>> 关闭5V供电
2025-07-31 21:40:24:555 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:40:24:749 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150
$GBGGA,134028.545,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,59,,,40,60,,,40,25,,,40,1*78

$GBGSV,7,2,26,3,,,40,39,,,39,24,,,39,41,,,39,1*43

$GBGSV,7,3,26,40,,,38,1,,,38,14,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,23,,,37,44,,,36,34,,,36,1*70

$GBGSV,7,5,26,9,,,35,6,,,35,2,,,35,12,,,35,1*4E

$GBGSV,7,6,26,10,,,34,13,,,34,38,,,33,4,,,32,1*4E

$GBGSV,7,7,26,5,,,32,8,,,29,1*75

$GBRMC,134028.545,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134028.545,0.000,1511.639,1511.639,48.364,2097152,2097152,2097152*54



2025-07-31 21:40:25:266 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:40:25:556 ==>> 关闭5V供电成功
2025-07-31 21:40:25:561 ==>> 关闭33V供电
2025-07-31 21:40:25:589 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:40:25:741 ==>> $GBGGA,134029.525,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,59,,,41,60,,,40,25,,,40,1*79

$GBGSV,7,2,26,3,,,40,39,,,39,24,,,39,41,,,39,1*43

$GBGSV,7,3,26,40,,,39,1,,,38,14,,,37,7,,,37,1*70

$GBGSV,7,4,26,16,,,37,23,,,37,44,,,37,34,,,36,1*71

$GBGSV,7,5,26,9,,,35,6,,,35,2,,,35,12,,,35,1*4E

$GBGSV,7,6,26,10,,,34,13,,,34,38,,,34,4,,,32,1*49

$GBGSV,7,7,26,5,,,32,8,,,30,1*7D

$GBRMC,134029.525,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134029.525,0.000,1519.608,1519.608,48.616,2097152,2097152,2097152*53

5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:40:25:846 ==>> [D][05:18:10][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05

2025-07-31 21:40:25:906 ==>> :18:10][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:10][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 21:40:26:151 ==>> [D][05:18:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:40:26:562 ==>> 关闭33V供电成功
2025-07-31 21:40:26:567 ==>> 关闭3.7V供电
2025-07-31 21:40:26:573 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:40:26:712 ==>> $GBGGA,134030.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,59,,,41,60,,,40,25,,,40,1*79

$GBGSV,7,2,26,3,,,40,39,,,39,24,,,39,41,,,39,1*43

$GBGSV,7,3,26,40,,,39,1,,,38,14,,,37,7,,,37,1*70

$GBGSV,7,4,26,16,,,37,23,,,37,44,,,36,34,,,36,1*70

$GBGSV,7,5,26,9,,,35,6,,,35,2,,,35,12,,,35,1*4E

$GBGSV,7,6,26,10,,,34,13,,,34,38,,,33,4,,,32,1*4E

$GBGSV,7,7,26,5,,,32,8,,,30,1*7D

$GBRMC,134030.505,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134030.505,0.000,1516.421,1516.421,48.516,2097152,2097152,2097152*5A

6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:40:27:310 ==>>  

