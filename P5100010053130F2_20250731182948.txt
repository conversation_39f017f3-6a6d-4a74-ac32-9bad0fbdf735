2025-07-31 18:29:48:728 ==>> MES查站成功:
查站序号:P5100010053130F2验证通过
2025-07-31 18:29:48:731 ==>> 扫码结果:P5100010053130F2
2025-07-31 18:29:48:733 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:29:48:735 ==>> 测试参数版本:2024.10.11
2025-07-31 18:29:48:737 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:29:48:738 ==>> 检测【打开透传】
2025-07-31 18:29:48:740 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:29:48:787 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:29:49:074 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:29:49:079 ==>> 检测【检测接地电压】
2025-07-31 18:29:49:082 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:29:49:188 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:29:49:356 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:29:49:358 ==>> 检测【打开小电池】
2025-07-31 18:29:49:362 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:29:49:474 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:29:49:633 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:29:49:636 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:29:49:639 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:29:49:780 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:29:49:913 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:29:49:916 ==>> 检测【等待设备启动】
2025-07-31 18:29:49:920 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:29:50:244 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:29:50:409 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 18:29:50:911 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:29:50:941 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:29:51:108 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:29:51:759 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:29:51:804 ==>>                                                    

2025-07-31 18:29:51:969 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:29:52:204 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:29:52:678 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:29:52:782 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:29:52:784 ==>> 检测【产品通信】
2025-07-31 18:29:52:786 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:29:52:937 ==>> AT+PWD=6789


2025-07-31 18:29:53:396 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:29:53:594 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:29:53:824 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:29:54:861 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:29:54:891 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFF

2025-07-31 18:29:54:996 ==>> FFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]netcore_ver 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA

2025-07-31 18:29:55:101 ==>> _BOOT_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = D7DE8EE79C13
[D][05:17:49][FCTY]Bat         = 3764 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash
[D][05:17:49][COMM][CHG]ext_48v_vol:0, disable charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD2
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 0
[D][05:17:49][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_

2025-07-31 18:29:55:166 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:29:55:169 ==>> 检测【初始化完成检测】
2025-07-31 18:29:55:172 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:29:55:206 ==>> KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]CAT1_GNSS_VERSION =
[D][05:17:49][FCTY]Bat1         = 3706 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi scan.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][CAT1]gsm read msg sub id: 1
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17

2025-07-31 18:29:55:311 ==>> :50][COMM]msg 0220 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0221 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0224 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0260 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E2 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick:0. cur_tick:1018. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:1019. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1019. period:100

2025-07-31 18:29:55:386 ==>> . j,i:3 56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1019. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1020. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1020. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1021
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1021
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1022
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:29:55:722 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 18:29:55:827 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 18:29:55:932 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 18:29:56:037 ==>>                                                                                                                                                                                                                                                                                                        c_cfg:0xFFFFFFFF
[D][05:17:50][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:17:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:17:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:2

2025-07-31 18:29:56:142 ==>> 55
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1617 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1

2025-07-31 18:29:56:172 ==>>  and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 18:29:56:217 ==>>                                                                                                                                                               

2025-07-31 18:29:56:322 ==>>                  M]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:29:57:234 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:29:57:421 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:29:57:425 ==>> 检测【关闭大灯控制1】
2025-07-31 18:29:57:428 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:29:57:570 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:29:57:723 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:29:57:726 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:29:57:727 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:29:57:873 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:53][COMM][oneline_display]: command mode, ON!
[D][05:17:53][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:29:58:034 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:29:58:036 ==>> 检测【关闭仪表供电】
2025-07-31 18:29:58:038 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:29:58:290 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]
[W][05:17:53][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:53][COMM]set POWER 0
[D][05:17:53][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:29:58:767 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5016. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5017. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5018
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B

2025-07-31 18:29:58:797 ==>> 987FE 5019


2025-07-31 18:29:59:246 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:29:59:350 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:29:59:352 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:29:59:354 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:29:59:537 ==>> [W][05:17:54][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:29:59:645 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:29:59:648 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:29:59:650 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:29:59:844 ==>> [W][05:17:55][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:29:59:939 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:29:59:942 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:29:59:944 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:30:00:166 ==>> [D][05:17:55][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:17:55][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:30:00:232 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:30:00:235 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:30:00:237 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:30:00:241 ==>> [D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:30:00:286 ==>> 5A A5 01 5A A5 


2025-07-31 18:30:00:376 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:30:00:451 ==>> [D][05:17:55][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 20


2025-07-31 18:30:00:526 ==>> [D][05:17:55][COMM]read battery soc:255


2025-07-31 18:30:00:528 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:30:00:531 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:30:00:543 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:30:00:586 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:30:00:821 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:30:00:824 ==>> 该项需要延时执行
2025-07-31 18:30:01:295 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 18:30:02:052 ==>> [D][05:17:57][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:57][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:0------------
[D][05:17:57][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:30:02:528 ==>>                                                                                              cckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:1------------
[D][05:17:57][COMM]more than the number of battery plugs
[D][05:17:57][COMM]VBUS is 1
[D][05:17:57][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:57][COMM]file:B50 exist
[D][05:17:57][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:57][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:57][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:57][COMM]Bat auth off fail, error:-1
[D][05:17:57][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:1------------
[D][05:17:57][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:57][COMM]----- get Acckey 1 and value:1------------
[D][05:17:57][COMM]----- get Acckey 2 and value:1------------
[D][05:17:57][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:57][COMM][Audio].l:[904].echo is not ready
[D][05:17:57][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:57][COMM]Main Task receive event:65
[D][05:17:57][COMM]main task t

2025-07-31 18:30:02:633 ==>> mp_sleep_event = 80
[D][05:17:57][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:57][COMM]Main Task receive event:65 finished processing
[D][05:17:57][COMM]Main Task receive event:66
[D][05:17:57][COMM]Try to Auto Lock Bat
[D][05:17:57][COMM]Main Task receive event:66 finished processing
[D][05:17:57][COMM]Receive Bat Lock cmd 0
[D][05:17:57][COMM]VBUS is 1
[D][05:17:57][COMM]Main Task receive event:60
[D][05:17:57][COMM]smart_helmet_vol=255,255
[D][05:17:57][COMM]BAT CAN get state1 Fail 204
[D][05:17:57][COMM]BAT CAN get soc Fail, 204
[D][05:17:57][COMM]get soc error
[E][05:17:57][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:57][COMM]report elecbike
[W][05:17:57][PROT]remove success[1629955077],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:57][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:57][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:57][PROT]index:2
[D][05:17:57][PROT]is_send:1
[D][05:17:57][PROT]sequence_num:2
[D][05:17:57][PROT]retry_timeout:0
[D][05:17:57][PROT]retry_times:3
[D][05:17:57][PROT]send_path:0x3
[D][05:17:57][PROT]msg_type:0

2025-07-31 18:30:02:738 ==>> x5d03
[D][05:17:57][PROT]===========================================================
[W][05:17:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955077]
[D][05:17:57][PROT]===========================================================
[D][05:17:57][PROT]Sending traceid[9999999999900003]
[D][05:17:57][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:57][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:57][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:57][PROT]add success [1629955077],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:57][COMM]Main Task receive event:60 finished processing
[D][05:17:57][COMM]Main Task receive event:61
[D][05:17:57][COMM][D301]:type:3, trace id:280
[D][05:17:57][COMM]id[], hw[000
[D][05:17:57][COMM]get mcMaincircuitVolt error
[D][05:17:57][COMM]get mcSubcircuitVolt error
[D][05:17:57][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:57][COMM]BAT CAN get state1 Fail 204
[D][05:17:57][COMM]BAT CAN get soc Fail, 204
[D][05:17:57][COMM]get bat work state err
[W][05:17:57][PROT]remove success[1629955077],send_path[2],type[0000],priority[0

2025-07-31 18:30:02:799 ==>> ],index[3],used[0]
[W][05:17:57][PROT]add success [1629955077],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:57][COMM]Main Task receive event:61 finished processing
[D][05:17:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:57][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
                                         

2025-07-31 18:30:03:291 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:30:03:630 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10005


2025-07-31 18:30:04:496 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31


2025-07-31 18:30:04:555 ==>> 
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
                                         

2025-07-31 18:30:04:660 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10



2025-07-31 18:30:04:827 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:30:04:831 ==>> 检测【33V输入电压ADC】
2025-07-31 18:30:04:834 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:30:05:261 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087724300

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071536191

OK

[W][05:18:00][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:00][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:00][COMM]adc read out 24v adc:1299  volt:32855 mv
[D][05:18:00][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:00][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:00][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:00][COMM]adc read battery ts volt:2 mv
[D][05:18:00][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:00][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:00][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][COMM]arm_hub adc read board id adc:3359  vol

2025-07-31 18:30:05:366 ==>> t:2706 mv
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][COMM]arm_hub adc read front lamp adc:0  volt:0 mv
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

                                         

2025-07-31 18:30:05:682 ==>> 【33V输入电压ADC】通过,【32602mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:30:05:686 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:30:05:690 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:30:05:796 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 1992mV
Get AD_V7 1085mV
OVER 150


2025-07-31 18:30:05:976 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:05:979 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:30:05:996 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:05:998 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:30:06:002 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 18:30:06:016 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:30:06:021 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:30:06:037 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:30:06:039 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:30:06:063 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:30:06:066 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:30:06:197 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 1991mV
Get AD_V7 1085mV
OVER 150


2025-07-31 18:30:06:342 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:06:345 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:30:06:362 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:06:366 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:30:06:368 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 18:30:06:381 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:30:06:385 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:30:06:402 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:30:06:404 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:30:06:440 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:30:06:442 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:30:06:611 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1992mV
Get AD_V7 1086mV
OVER 150


2025-07-31 18:30:06:747 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:06:750 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:30:06:781 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:06:784 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:30:06:787 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 18:30:06:808 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:30:06:811 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:30:06:846 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:30:06:851 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:30:06:883 ==>> 【TP1_VCC12V(ADV7)】通过,【1086mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:30:06:885 ==>> 检测【打开WIFI(1)】
2025-07-31 18:30:06:890 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:30:07:073 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:30:07:182 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:30:07:187 ==>> 检测【清空消息队列(1)】
2025-07-31 18:30:07:190 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:30:07:642 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]


2025-07-31 18:30:07:747 ==>> 
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][COMM]13731 imu init OK
[D][05:18:02][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.111.90.254"

OK

[W][05:18:02][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:02][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

2025-07-31 18:30:07:838 ==>> 

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 18:30:08:033 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:30:08:038 ==>> 检测【打开GPS(1)】
2025-07-31 18:30:08:041 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:30:08:176 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:03][COMM]Open GPS Module...
[D][05:18:03][COMM]LOC_MODEL_CONT
[W][05:18:03][GNSS]gps already running
[D][05:18:03][GNSS]start event:8
[W][05:18:03][GNSS]start cont locating


2025-07-31 18:30:08:326 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:30:08:393 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:30:08:397 ==>> 检测【打开GSM联网】
2025-07-31 18:30:08:418 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:30:08:570 ==>> [D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:03][COMM]GSM test
[D][05:18:03][COMM]GSM test enable


2025-07-31 18:30:08:706 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:30:08:709 ==>> 检测【打开仪表供电1】
2025-07-31 18:30:08:713 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:30:08:875 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:04][COMM]set POWER 1
[D][05:18:04][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:30:08:980 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:30:09:010 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:30:09:013 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:30:09:016 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:30:09:250 ==>> [D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,38,,,41,60,,,40,33,,,39,39,,,39,1*73

$GBGSV,3,2,10,24,,,38,42,,,36,26,,,44,13,,,40,1*7A

$GBGSV,3,3,10,16,,,36,21,,,36,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1609.895,1609.895,51.432,2097152,2097152,2097152*4E

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:04][COMM][oneline_display]: command mode, ON!
[D][05:18:04][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:30:09:310 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:30:09:316 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:30:09:342 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:30:09:345 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:30:09:475 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:04][COMM]arm_hub read adc[3],val[33270]


2025-07-31 18:30:09:622 ==>> 【读取主控ADC采集的仪表电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:30:09:625 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:30:09:630 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:09:780 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:09:938 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:30:09:944 ==>> 检测【AD_V20电压】
2025-07-31 18:30:09:949 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:10:042 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:10:137 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,3,,,42,26,,,41,38,,,41,60,,,41,1*4D

$GBGSV,5,2,17,24,,,41,21,,,41,59,,,41,39,,,40,1*75

$GBGSV,5,3,17,8,,,40,42,,,39,1,,,39,33,,,38,1*76

$GBGSV,5,4,17,14,,,38,9,,,37,13,,,36,16,,,36,1*47

$GBGSV,5,5,17,2,,,35,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1624.146,1624.146,51.912,2097152,2097152,2097152*41



2025-07-31 18:30:10:300 ==>> [D][05:18:05][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:10:405 ==>> 本次取值间隔时间:350ms
2025-07-31 18:30:10:543 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 18:30:10:602 ==>> 本次取值间隔时间:188ms
2025-07-31 18:30:10:754 ==>> 本次取值间隔时间:151ms
2025-07-31 18:30:11:096 ==>> 本次取值间隔时间:335ms
2025-07-31 18:30:11:101 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:11:156 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,3,,,41,26,,,41,38,,,41,1*43

$GBGSV,5,2,19,60,,,41,59,,,41,42,,,41,21,,,40,1*77

$GBGSV,5,3,19,39,,,40,8,,,40,1,,,39,33,,,39,1*7B

$GBGSV,5,4,19,14,,,39,13,,,39,9,,,38,16,,,38,1*46

$GBGSV,5,5,19,2,,,36,5,,,35,4,,,32,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1621.211,1621.211,51.835,2097152,2097152,2097152*45



2025-07-31 18:30:11:201 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:11:246 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:30:11:276 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 18:30:11:584 ==>> 本次取值间隔时间:380ms
2025-07-31 18:30:11:632 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:11:740 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:11:943 ==>> 本次取值间隔时间:194ms
2025-07-31 18:30:11:973 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:12:169 ==>> 本次取值间隔时间:217ms
2025-07-31 18:30:12:173 ==>> $GBGGA,103015.955,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,59,,,42,42,,,42,3,,,41,1*4D

$GBGSV,5,2,20,26,,,41,38,,,41,60,,,41,13,,,41,1*78

$GBGSV,5,3,20,21,,,40,39,,,40,8,,,40,1,,,39,1*7C

$GBGSV,5,4,20,14,,,39,16,,,39,33,,,38,9,,,38,1*4E

$GBGSV,5,5,20,2,,,36,5,,,35,4,,,33,25,,,33,1*43

$GBRMC,103015.955,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103015.955,0.000,1621.011,1621.011,51.846,2097152,2097152,2097152*50



2025-07-31 18:30:12:229 ==>> 本次取值间隔时间:57ms
2025-07-31 18:30:12:444 ==>> 本次取值间隔时间:212ms
2025-07-31 18:30:12:448 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:12:538 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:30:12:553 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:12:811 ==>> $GBGGA,103016.555,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,59,,,41,3,,,41,1*4F

$GBGSV,6,2,22,26,,,41,38,,,41,60,,,41,13,,,41,1*79

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,40,1*73

$GBGSV,6,4,22,14,,,39,16,,,39,33,,,38,9,,,38,1*4F

$GBGSV,6,5,22,2,,,36,6,,,36,5,,,35,4,,,33,1*76

$GBGSV,6,6,22,25,,,33,7,,,31,1*44

$GBRMC,103016.555,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103016.555,0.000,1599.925,1599.925,51.192,2097152,2097152,2097152*5F

[W][05:18:08][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:30:12:886 ==>> 本次取值间隔时间:332ms
2025-07-31 18:30:13:214 ==>> 本次取值间隔时间:317ms
2025-07-31 18:30:13:509 ==>> 本次取值间隔时间:286ms
2025-07-31 18:30:13:771 ==>> $GBGGA,103017.535,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,59,,,41,3,,,41,1*4C

$GBGSV,6,2,22,26,,,41,38,,,41,60,,,41,13,,,41,1*79

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,40,1*73

$GBGSV,6,4,22,14,,,39,16,,,39,33,,,38,9,,,38,1*4F

$GBGSV,6,5,22,2,,,37,6,,,37,5,,,35,4,,,33,1*76

$GBGSV,6,6,22,25,,,33,7,,,31,1*44

$GBRMC,103017.535,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103017.535,0.000,1601.804,1601.804,51.247,2097152,2097152,2097152*53



2025-07-31 18:30:13:862 ==>> 本次取值间隔时间:338ms
2025-07-31 18:30:13:867 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:13:970 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:14:047 ==>> [W][05:18:09][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:30:14:077 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:30:14:243 ==>> 本次取值间隔时间:265ms
2025-07-31 18:30:14:261 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:14:368 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:14:477 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:09][COMM]oneline display ALL on 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:30:14:537 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:30:14:748 ==>> $GBGGA,103018.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,59,,,41,3,,,41,1*4C

$GBGSV,6,2,22,26,,,41,38,,,41,60,,,41,13,,,41,1*79

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,9,,,38,1*4E

$GBGSV,6,5,22,2,,,37,6,,,37,5,,,35,4,,,33,1*76

$GBGSV,6,6,22,25,,,32,7,,,31,1*45

$GBRMC,103018.515,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103018.515,0.000,1596.155,1596.155,51.070,2097152,2097152,2097152*58



2025-07-31 18:30:14:869 ==>> 本次取值间隔时间:490ms
2025-07-31 18:30:14:890 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:30:14:994 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:30:15:057 ==>> 本次取值间隔时间:57ms
2025-07-31 18:30:15:087 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:10][COMM]oneline display ALL on 1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1653mV
OVER 150


2025-07-31 18:30:15:335 ==>> 本次取值间隔时间:277ms
2025-07-31 18:30:15:364 ==>> 【AD_V20电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:30:15:368 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:30:15:370 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:30:15:475 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:30:15:654 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:30:15:657 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:30:15:663 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:30:15:718 ==>> $GBGGA,103019.515,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,42,,,41,59,,,41,1*7E

$GBGSV,6,2,22,3,,,41,26,,,41,38,,,41,60,,,41,1*48

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,9,,,38,1*4E

$GBGSV,6,5,22,6,,,37,2,,,36,5,,,35,4,,,33,1*77

$GBGSV,6,6,22,25,,,32,7,,,32,1*46

$GBRMC,103019.515,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103019.515,0.000,1598.038,1598.038,51.129,2097152,2097152,2097152*54



2025-07-31 18:30:15:824 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:11][COMM]oneline display read state:1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:16:569 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:30:16:674 ==>> $GBGGA,103020.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,41,59,,,41,1*7E

$GBGSV,6,2,22,3,,,41,26,,,41,38,,,41,60,,,41,1*48

$GBGSV,6,3,22,21,,,

2025-07-31 18:30:16:689 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:30:16:720 ==>> 40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,9,,,37,1*41

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,35,4,,,34,1*71

$GBGSV,6,6,22,25,,,32,7,,,31,1*45

$GBRMC,103020.515,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103020.515,0.000,1598.038,1598.038,51.129,2097152,2097152,2097152*5E



2025-07-31 18:30:16:870 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:12][COMM]oneline display read state:1
[D][05:18:12][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:17:738 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:30:17:753 ==>> $GBGGA,103021.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,41,59,,,41,1*7E

$GBGSV,6,2,22,3,,,41,26,,,41,38,,,41,60,,,41,1*48

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,9,,,37,1*41

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,34,4,,,34,1*70

$GBGSV,6,6,22,25,,,32,7,,,31,1*45

$GBRMC,103021.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103021.515,0.000,1596.157,1596.157,51.072,2097152,2097152,2097152*50



2025-07-31 18:30:17:981 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:13][COMM]oneline display read state:1
[D][05:18:13][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:30:18:568 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 18:30:18:749 ==>> $GBGGA,103022.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,3,,,42,1*41

$GBGSV,6,2,22,59,,,41,26,,,41,38,,,41,60,,,41,1*77

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,9,,,38,1*4E

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,35,4,,,34,1*71

$GBGSV,6,6,22,25,,,32,7,,,32,1*46

$GBRMC,103022.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103022.515,0.000,1605.574,1605.574,51.368,2097152,2097152,2097152*5B



2025-07-31 18:30:18:779 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:30:18:785 ==>> #################### 【测试结束】 ####################
2025-07-31 18:30:18:868 ==>> 关闭5V供电
2025-07-31 18:30:18:873 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:30:18:977 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:30:19:719 ==>> $GBGGA,103023.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,3,,,41,1*42

$GBGSV,6,2,22,59,,,41,26,,,41,38,,,41,60,,,41,1*77

$GBGSV,6,3,22,21,,,40,39,,,40,8,,,40,1,,,39,1*7D

$GBGSV,6,4,22,16,,,39,14,,,38,33,,,38,6,,,38,1*41

$GBGSV,6,5,22,9,,,37,2,,,37,5,,,35,4,,,34,1*7E

$GBGSV,6,6,22,25,,,32,7,,,32,1*46

$GBRMC,103023.515,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103023.515,0.000,1603.688,1603.688,51.306,2097152,2097152,2097152*52



2025-07-31 18:30:19:870 ==>> 关闭5V供电成功
2025-07-31 18:30:19:876 ==>> 关闭33V供电
2025-07-31 18:30:19:889 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:30:19:980 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:30:20:085 ==>> [D][05:18:15][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vo

2025-07-31 18:30:20:145 ==>> l retry i = 1,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:15][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 18:30:20:388 ==>> [D][05:18:15][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:30:20:724 ==>> $GBGGA,103024.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,13,,,42,3,,,41,1*42

$GBGSV,6,2,22,59,,,41,26,,,41,38,,,41,60,,,41,1*77

$GBGSV,6,3,22,8,,,41,21,,,40,39,,,40,1,,,39,1*7C

$GBGSV,6,4,22,16,,,39,14,,,39,33,,,38,9,,,38,1*4F

$GBGSV,6,5,22,6,,,37,2,,,37,5,,,35,4,,,34,1*71

$GBGSV,6,6,22,25,,,32,7,,,32,1*46

$GBRMC,103024.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,103024.515,0.000,1607.457,1607.457,51.427,2097152,2097152,2097152*51



2025-07-31 18:30:20:876 ==>> 关闭33V供电成功
2025-07-31 18:30:20:881 ==>> 关闭3.7V供电
2025-07-31 18:30:20:886 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:30:20:984 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:30:21:589 ==>>  

