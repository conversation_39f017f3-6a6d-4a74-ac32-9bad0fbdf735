2025-07-31 22:36:29:797 ==>> MES查站成功:
查站序号:P51000100531286F验证通过
2025-07-31 22:36:29:817 ==>> 扫码结果:P51000100531286F
2025-07-31 22:36:29:819 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:36:29:821 ==>> 测试参数版本:2024.10.11
2025-07-31 22:36:29:823 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:36:29:825 ==>> 检测【打开透传】
2025-07-31 22:36:29:826 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:36:29:904 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:36:30:090 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:36:30:097 ==>> 检测【检测接地电压】
2025-07-31 22:36:30:099 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:36:30:211 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:36:30:365 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:36:30:368 ==>> 检测【打开小电池】
2025-07-31 22:36:30:371 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:36:30:501 ==>> 6A A6 01 A6 6A 


2025-07-31 22:36:30:606 ==>> Battery ON
OVER 150


2025-07-31 22:36:30:639 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:36:30:641 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:36:30:643 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:36:30:711 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:36:30:920 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:36:30:924 ==>> 检测【等待设备启动】
2025-07-31 22:36:30:927 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:36:31:341 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:36:31:536 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:36:31:950 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:36:32:169 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:36:32:244 ==>>                                                    [W][05:17:49][PROT]Low Battery, Will Not Power On GSM


2025-07-31 22:36:32:628 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:36:32:988 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:36:33:108 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:36:33:261 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:36:33:264 ==>> 检测【产品通信】
2025-07-31 22:36:33:266 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:36:33:468 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:36:33:531 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:36:33:533 ==>> 检测【初始化完成检测】
2025-07-31 22:36:33:536 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:36:33:774 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:36:33:814 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:36:33:818 ==>> 检测【关闭大灯控制1】
2025-07-31 22:36:33:820 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:36:33:987 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:36:34:095 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:36:34:098 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:36:34:099 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:36:34:153 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:34:348 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:36:34:666 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:36:34:671 ==>> 检测【关闭仪表供电】
2025-07-31 22:36:34:674 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:36:34:901 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:36:34:950 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:36:34:952 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:36:34:953 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:36:35:068 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:36:35:174 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:35:229 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:36:35:231 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:36:35:233 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:36:35:370 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:36:35:512 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:36:35:515 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:36:35:516 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:35:690 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:36:35:800 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:36:35:803 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:36:35:806 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:36:35:903 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:36:35:993 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29


2025-07-31 22:36:36:053 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 22:36:36:074 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:36:36:077 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:36:36:078 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:36:36:204 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:36:36:369 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:36:36:372 ==>> 该项需要延时执行
2025-07-31 22:36:36:724 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5018. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 50

2025-07-31 22:36:36:754 ==>> 20
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020


2025-07-31 22:36:37:189 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:37:594 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:36:38:070 ==>>                                                                                                            lue:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][0

2025-07-31 22:36:38:175 ==>> 5:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg

2025-07-31 22:36:38:280 ==>> _type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_pat

2025-07-31 22:36:38:355 ==>> h[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
                                                                                                                                          

2025-07-31 22:36:39:028 ==>> [D][05:17:56][CAT1]power_urc_cb ret[5]


2025-07-31 22:36:39:210 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:40:069 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:36:40:235 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:40:370 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:36:40:373 ==>> 检测【33V输入电压ADC】
2025-07-31 22:36:40:376 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:40:721 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:17:58][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:58][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:58][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:58][COMM]adc read battery ts volt:7 mv
[D][05:17:58][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:36:40:917 ==>> 【33V输入电压ADC】通过,【32653mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:36:40:920 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:36:40:947 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:36:41:022 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2744mV
Get AD_V6 1990mV
Get AD_V7 1089mV
OVER 150


2025-07-31 22:36:41:191 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:194 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:36:41:209 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:212 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:36:41:215 ==>> 原始值:【2744】, 乘以分压基数【2】还原值:【5488】
2025-07-31 22:36:41:234 ==>> 【TP68_VCC5V5(ADV5)】通过,【5488mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:36:41:236 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:41:237 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:36:41:252 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:36:41:254 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:36:41:275 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:36:41:277 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:36:41:416 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2745mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 22:36:41:521 ==>> [D][05:17:58][COMM]msg

2025-07-31 22:36:41:560 ==>> 【TP7_VCC3V3(ADV2)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:562 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:36:41:578 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:582 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:36:41:585 ==>> 原始值:【2745】, 乘以分压基数【2】还原值:【5490】
2025-07-31 22:36:41:589 ==>>  0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10005


2025-07-31 22:36:41:612 ==>> 【TP68_VCC5V5(ADV5)】通过,【5490mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:36:41:615 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:36:41:631 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:36:41:633 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:36:41:660 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:36:41:662 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:36:41:821 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1658mV
Get AD_V4 1mV
Get AD_V5 2746mV
Get AD_V6 1991mV
Get AD_V7 1088mV
OVER 150


2025-07-31 22:36:41:935 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:938 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:36:41:954 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:41:957 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:36:41:959 ==>> 原始值:【2746】, 乘以分压基数【2】还原值:【5492】
2025-07-31 22:36:41:972 ==>> 【TP68_VCC5V5(ADV5)】通过,【5492mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:36:41:975 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:36:41:990 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:36:41:992 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:36:42:013 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:36:42:015 ==>> 检测【打开WIFI(1)】
2025-07-31 22:36:42:018 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:36:42:063 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:36:42:259 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]10714 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:36:42:294 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:36:42:296 ==>> 检测【清空消息队列(1)】
2025-07-31 22:36:42:298 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:36:42:486 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:36:42:581 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:36:42:594 ==>> 检测【打开GPS(1)】
2025-07-31 22:36:42:597 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:36:42:683 ==>> [D][05:18:00][CAT1]power_urc_cb ret[76]


2025-07-31 22:36:42:788 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:36:42:906 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:36:42:924 ==>> 检测【打开GSM联网】
2025-07-31 22:36:42:928 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:36:43:253 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[5

2025-07-31 22:36:43:283 ==>> 6] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 22:36:43:488 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:36:43:491 ==>> 检测【打开仪表供电1】
2025-07-31 22:36:43:494 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:36:43:688 ==>>                                                                                                                            [D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088007283

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541609

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:36:43:793 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:36:44:023 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:36:44:036 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:36:44:039 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:36:44:064 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:36:44:169 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneli

2025-07-31 22:36:44:199 ==>> ne_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:36:44:292 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:36:44:295 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:36:44:298 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:36:44:379 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:36:44:484 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33085]


2025-07-31 22:36:44:563 ==>> 【读取主控ADC采集的仪表电压】通过,【33085mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:36:44:567 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:36:44:571 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:36:44:802 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:36:44:836 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:36:44:839 ==>> 检测【AD_V20电压】
2025-07-31 22:36:44:842 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:36:44:937 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:36:45:012 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 22:36:45:102 ==>> 本次取值间隔时间:157ms
2025-07-31 22:36:45:121 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:45:123 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:36:45:127 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:36:45:208 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:36:45:238 ==>> [D][05:18:02][COMM]13726 imu init OK


2025-07-31 22:36:45:343 ==>> [D][05:18:02][COMM]S->M yaw:INVALID


2025-07-31 22:36:45:397 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:36:45:399 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:36:45:402 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:36:45:629 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:36:45:677 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:36:45:682 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:36:45:686 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:36:45:809 ==>> 3A A3 02 01 A3 


2025-07-31 22:36:45:899 ==>> ON_OUT2
OVER 150


2025-07-31 22:36:45:947 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:36:45:951 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:36:45:955 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:36:46:127 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:36:46:221 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:36:46:225 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:36:46:229 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:36:46:412 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:03][COMM]oneline display set 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:36:46:491 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:36:46:496 ==>> 检测【AD_V21电压】
2025-07-31 22:36:46:518 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:36:46:609 ==>> 1A A1 20 00 00 
Get AD_V21 1017mV
OVER 150


2025-07-31 22:36:46:654 ==>> 本次取值间隔时间:156ms
2025-07-31 22:36:46:673 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:36:46:852 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 22:36:47:092 ==>> 本次取值间隔时间:408ms
2025-07-31 22:36:47:107 ==>>                                                                                                                                                         4][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][COMM]Main Task receive event:4
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][GNSS]rtk_id: 303E383D3535373F373F3730353F3407

[D][05:18:04][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][COMM]init key as 
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:04][COMM]Main Task receive event:4 finished processing
[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:04][CAT1

2025-07-31 22:36:47:116 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:36:47:120 ==>> 检测【关闭仪表供电2】
2025-07-31 22:36:47:125 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:36:47:152 ==>> ]<<< 
+QIACT: 1,1,1,"10.178.172.104"

OK

[D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:36:47:482 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:04][COMM]set POWER 0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[

2025-07-31 22:36:47:512 ==>> 19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:36:47:652 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:36:47:657 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:36:47:683 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:36:47:789 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:36:47:935 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:36:47:939 ==>> 检测【打开AccKey2供电】
2025-07-31 22:36:47:945 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:36:48:064 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:36:48:169 ==>> [D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:36:48:219 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:36:48:222 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:36:48:224 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:48:518 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:05][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:05][COMM]adc read out 24v adc:1321  volt:33412 mv
[D][05:18:05][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:05][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:05][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:05][COMM]adc read battery ts volt:14 mv
[D][05:18:05][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:05][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:05][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:05][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:05][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:05][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:05][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:36:48:751 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33412mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:36:48:755 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:36:48:758 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:36:49:069 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,60,,,42,25,,,41,34,,,41,39,,,41,1*7A

$GBGSV,3,2,10,40,,,41,41,,,40,59,,,39,33,,,32,1*71

$GBGSV,3,3,10,11,,,40,9,,,39,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1642.785,1642.785,52.550,2097152,2097152,2097152*48

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 22:36:49:249 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:36:49:289 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:36:49:292 ==>> 该项需要延时执行
2025-07-31 22:36:50:006 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,25,,,41,34,,,41,41,,,41,1*70

$GBGSV,6,2,21,3,,,41,60,,,40,39,,,40,59,,,40,1*43

$GBGSV,6,3,21,2,,,40,7,,,39,11,,,38,16,,,38,1*7C

$GBGSV,6,4,21,24,,,38,1,,,38,9,,,35,33,,,35,1*79

$GBGSV,6,5,21,12,,,35,5,,,35,6,,,34,43,,,40,1*72

$GBGSV,6,6,21,44,,,36,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1595.030,1595.030,51.002,2097152,2097152,2097152*49



2025-07-31 22:36:50:142 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 22:36:51:025 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,34,,,41,41,,,41,3,,,41,1*42

$GBGSV,6,2,24,59,,,41,25,,,40,60,,,40,39,,,40,1*72

$GBGSV,6,3,24,7,,,39,11,,,38,16,,,38,24,,,38,1*42

$GBGSV,6,4,24,1,,,38,43,,,37,2,,,37,33,,,36,1*78

$GBGSV,6,5,24,12,,,36,44,,,35,9,,,35,6,,,35,1*7C

$GBGSV,6,6,24,5,,,34,4,,,34,32,,,34,38,,,27,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1547.795,1547.795,49.532,2097152,2097152,2097152*46



2025-07-31 22:36:52:054 ==>> $GBGGA,143655.856,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,34,,,41,41,,,41,3,,,41,1*42

$GBGSV,7,2,25,59,,,41,25,,,40,60,,,40,39,,,40,1*72

$GBGSV,7,3,25,7,,,39,11,,,38,16,,,38,24,,,38,1*42

$GBGSV,7,4,25,1,,,38,43,,,37,33,,,37,2,,,36,1*78

$GBGSV,7,5,25,12,,,36,9,,,36,6,,,36,23,,,36,1*7E

$GBGSV,7,6,25,44,,,35,5,,,35,4,,,34,32,,,34,1*70

$GBGSV,7,7,25,38,,,27,1*7F

$GBRMC,143655.856,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143655.856,0.000,1550.552,1550.552,49.614,2097152,2097152,2097152*54



2025-07-31 22:36:52:144 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:36:52:296 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:36:52:301 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:36:52:305 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:52:618 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:09][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:09][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:09][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:09][COMM]adc read battery ts volt:5 mv
[D][05:18:09][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:09][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:36:52:723 ==>>                                                                                                                                                                                                                                                                                                                                                                     ,,,36,5,,,34,4,,,34,32,,,34,1*72

$GBGSV,7,7,25,38,,,28,1*70

$GBRMC,143656.556,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143656.556,0.000,1552.202,1552.202,49.659,2097152,2097152,2097152*53



2025-07-31 22:36:52:832 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:36:52:835 ==>> 检测【打开AccKey1供电】
2025-07-31 22:36:52:839 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:36:52:978 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:36:53:100 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:36:53:126 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:36:53:130 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:36:53:205 ==>> 1A A1 00 40 00 
Get AD_V14 2643mV
OVER 150


2025-07-31 22:36:53:357 ==>> 原始值:【2643】, 乘以分压基数【2】还原值:【5286】
2025-07-31 22:36:53:375 ==>> 【读取AccKey1电压(ADV14)前】通过,【5286mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:36:53:378 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:36:53:382 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:53:772 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3118  volt:5480 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:8 mv
[D][05:18:11][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:11][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,143657.536,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,40,59,,,40,1*4A

$GBGSV,7,2,26,34,,,40,25,,,40,60,,,40,39,,,40,1*7B

$GBGSV,7,3,26,7,,,39,11,,,38,16,,,38,24,,,38,1*41

$GBGSV,7,4,26,1,,,38,33,,,38,43,,,37,12,,,36,1*45

$GBGSV,7,5,26,9,,,36,6,,,36,23,,,36,44,,,36,1*7E

$GBGSV,7,6,26,10,,,36,2,,,35,32,,,35,4,,,34,1*77

$GBGSV,7,7,26,5,,,33,38,,,28,1*46

$GBRMC,143657.536,V

2025-07-31 22:36:53:802 ==>> ,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143657.536,0.000,1545.118,1545.118,49.428,2097152,2097152,2097152*50



2025-07-31 22:36:53:910 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5480mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:36:53:914 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:36:53:918 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:36:54:077 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:36:54:167 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:36:54:185 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:36:54:189 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:36:54:195 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:36:54:302 ==>> 1A A1 00 40 00 
Get AD_V14 2644mV
OVER 150


2025-07-31 22:36:54:437 ==>> 原始值:【2644】, 乘以分压基数【2】还原值:【5288】
2025-07-31 22:36:54:456 ==>> 【读取AccKey1电压(ADV14)后】通过,【5288mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:36:54:462 ==>> 检测【打开WIFI(2)】
2025-07-31 22:36:54:469 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:36:54:737 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6
$GBGGA,143658.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,34,,,41,1*7E

$GBGSV,7,2,26,60,,,41,3,,,40,25,,,40,39,,,40,1*4E

$GBGSV,7,3,26,7,,,39,11,,,38,16,,,38,24,,,38,1*41

$GBGSV,7,4,26,1,,,38,33,,,38,43,,,37,12,,,36,1*45

$GBGSV,7,5,26,9,,,36,6,,,36,23,,,36,44,,,36,1*7E

$GBGSV,7,6,26,10,,,36,2,,,36,32,,,34,4,,,34,1*75

$GBGSV,7,7,26,5,,,33,38,,,29,1*47

$GBRMC,143658.516,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143658.516,0.000,1551.497,1551.497,49.633,2097152,2097152,2097152*55



2025-07-31 22:36:54:992 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:36:54:999 ==>> 检测【转刹把供电】
2025-07-31 22:36:55:026 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:36:55:192 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:36:55:282 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:36:55:301 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:36:55:307 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:36:55:387 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:55:462 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:36:55:507 ==>> 1A A1 00 80 00 
Get AD_V15 2384mV
OVER 150


2025-07-31 22:36:55:552 ==>> 原始值:【2384】, 乘以分压基数【2】还原值:【4768】
2025-07-31 22:36:55:583 ==>> 【读取AD_V15电压(前)】通过,【4768mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:36:55:598 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:36:55:602 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:36:55:687 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:36:55:702 ==>> +WIFISCAN:4,0,F42A7D1297A3,-67
+WIFISCAN:4,1,CC057790A641,-71
+WIFISCAN:4,2,CC057790A7C1,-72
+WIFISCAN:4,3,44A1917CAD81,-75

[D][05:18:13][CAT1]wifi scan report total[4]
$GBGGA,143659.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,34,,,41,59,,,40,1*7F

$GBGSV,7,2,26,60,,,40,3,,,40,25,,,40,39,,,40,1*4F

$GBGSV,7,3,26,7,,,39,11,,,38,16,,,38,24,,,38,1*41

$GBGSV,7,4,26,1,,,38,33,,,38,43,,,37,10,,,37,1*46

$GBGSV,7,5,26,12,,,36,9,,,36,6,,,36,23,,,36,1*7D

$GBGSV,7,6,26,44,,,36,2,,,36,32,,,34,4,,,33,1*73

$GBGSV,7,7,26,5,,,33,38,,,29,1*47

$GBRMC,143659.516,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143659.516,0.000,1548.306,1548.306,49.529,2097152,2097152,2097152*5C



2025-07-31 22:36:55:882 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:36:56:159 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:36:56:264 ==>> [D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 22:36:56:635 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:36:56:695 ==>> $GBGGA,143700.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,41,34,,,41,59,,,41,1*7D

$GBGSV,7,2,26,60,,,40,3,,,40,25,,,40,39,,,40,1*4F

$GBGSV,7,3,26,7,,,39,11,,,38,16,,,38,24,,,38,1*41

$GBGSV,7,4,26,1,,,38,33,,,38,43,,,38,10,,,37,1*49

$GBGSV,7,5,26,23,,,37,12,,,36,9,,,36,6,,,36,1*7C

$GBGSV,7,6,26,44,,,36,2,,,36,32,,,35,4,,,33,1*72

$GBGSV,7,7,26,5,,,33,38,,,29,1*47

$GBRMC,143700.516,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143700.516,0.000,1556.280,1556.280,49.786,2097152,2097152,2097152*56



2025-07-31 22:36:56:740 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:36:56:770 ==>> [W][05:18:14][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:36:56:800 ==>> 1A A1 01 00 00 
Get AD_V16 2409mV
OVER 150


2025-07-31 22:36:56:905 ==>> 原始值:【2409】, 乘以分压基数【2】还原值:【4818】
2025-07-31 22:36:56:928 ==>> 【读取AD_V16电压(前)】通过,【4818mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:36:56:932 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:36:56:934 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:57:228 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:14][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:14][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:14][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:14][COMM]adc read battery ts volt:9 mv
[D][05:18:14][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3058  volt:5375 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:36:57:457 ==>> 【转刹把供电电压(主控ADC)】通过,【5375mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:36:57:461 ==>> 检测【转刹把供电电压】
2025-07-31 22:36:57:466 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:36:57:825 ==>> $GBGGA,143701.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,59,,,41,1*7C

$GBGSV,7,2,27,25,,,41,39,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,11,,,38,16,,,38,1*47

$GBGSV,7,4,27,24,,,38,1,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,27,23,,,37,12,,,36,9,,,36,6,,,36,1*7D

$GBGSV,7,6,27,44,,,36,2,,,36,32,,,35,4,,,34,1*74

$GBGSV,7,7,27,5,,,33,13,,,31,38,,,29,1*46

$GBRMC,143701.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143701.516,0.000,774.753,774.753,708.529,2097152,2097152,2097152*62

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:7 mv
[D][05:18:15][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3057  volt:5373 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM

2025-07-31 22:36:57:869 ==>> ]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:36:57:993 ==>> 【转刹把供电电压】通过,【5373mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:36:58:000 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:36:58:005 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:58:184 ==>> [D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:36:58:262 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:36:58:269 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:36:58:291 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:58:365 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:58:471 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:58:575 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:58:680 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:58:710 ==>> $GBGGA,143702.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,39,,,41,34,,,41,1*79

$GBGSV,7,2,27,25,,,41,41,,,41,3,,,40,59,,,40,1*4B

$GBGSV,7,3,27,7,,,39,33,,,39,24,,,38,16,,,38,1*41

$GBGSV,7,4,27,1,,,38,11,,,38,43,,,38,10,,,37,1*48

$GBGSV,7,5,27,23,,,37,2,,,36,6,,,36,9,,,36,1*4C

$GBGSV,7,6,27,44,,,36,12,,,36,32,,,35,5,,,34,1*44

$GBGSV,7,7,27,4,,,34,13,,,31,38,,,29,1*40

$GBRMC,143702.516,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143702.516,0.000,775.517,775.517,709.228,2097152,2097152,2097152*66



2025-07-31 22:36:58:785 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:58:891 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:58:906 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:16][COMM]>>>>>Input command = ?<<<<
[W][05:18:16][COMM]>>>>>Input command = ?<<<<
00 00 00 00 00 
head err!


2025-07-31 22:36:58:997 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:59:103 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:59:108 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:36:59:208 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:36:59:269 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:36:59:298 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:36:59:331 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:36:59:339 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:36:59:361 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:36:59:433 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:36:59:509 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 22:36:59:565 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:36:59:570 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:36:59:587 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:36:59:718 ==>> $GBGGA,143703.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,3,,,41,41,,,41,1*42

$GBGSV,7,2,27,39,,,40,59,,,40,34,,,40,25,,,40,1*70

$GBGSV,7,3,27,7,,,39,24,,,38,16,,,38,1,,,38,1*71

$GBGSV,7,4,27,11,,,38,33,,,38,43,,,38,10,,,37,1*79

$GBGSV,7,5,27,23,,,37,6,,,36,9,,,36,44,,,36,1*7E

$GBGSV,7,6,27,12,,,36,2,,,35,5,,,34,4,,,34,1*41

$GBGSV,7,7,27,32,,,34,13,,,31,38,,,29,1*75

$GBRMC,143703.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143703.516,0.000,771.684,771.684,705.723,2097152,2097152,2097152*65

3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:36:59:855 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:36:59:860 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:36:59:866 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:36:59:899 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:37:00:128 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:37:00:132 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:37:00:138 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:37:00:203 ==>> [D][05:18:17][COMM]read battery soc:255
3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:37:00:404 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:37:00:408 ==>> 检测【左刹电压测试1】
2025-07-31 22:37:00:413 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:37:00:760 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1721  volt:2268 mv
[D][05:18:18][COMM]adc read right brake adc:1724  volt:2272 mv
[D][05:18:18][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:18][COMM]adc read battery ts volt:10 mv
[D][05:18:18][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
$GBGGA,143704.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,60,,,40,3,,,40,1*42

$GBGSV,7,2,27,59,,,40,39,,,40,34,,,40,25,,,40,1*70

$GBGSV,7,3,27,7,,,39,24,,,38,1,,,38,16,,,38,1*71

$GBGSV,7,4,27,11,,,38,43,,,38,33,,,38,10,,,37,1*79

$GBGSV,7,5,27,23,,,37,2,,,36,44,,,36,6,,,36,1*75

$GBGSV,7,6,27,12,

2025-07-31 22:37:00:805 ==>> ,,36,9,,,36,32,,,34,5,,,33,1*7B

$GBGSV,7,7,27,4,,,33,13,,,31,38,,,29,1*47

$GBRMC,143704.516,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143704.516,0.000,769.385,769.385,703.620,2097152,2097152,2097152*66

[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:37:00:944 ==>> 【左刹电压测试1】通过,【2268】符合目标值【2250】至【2500】要求!
2025-07-31 22:37:00:950 ==>> 检测【右刹电压测试1】
2025-07-31 22:37:00:977 ==>> 【右刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 22:37:00:980 ==>> 检测【转把电压测试1】
2025-07-31 22:37:00:995 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 22:37:00:999 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:37:01:005 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:37:01:112 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:37:01:266 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:37:01:270 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:37:01:276 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:37:01:414 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:37:01:555 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:37:01:560 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:37:01:569 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:37:01:610 ==>> 3A A3 05 00 A3 


2025-07-31 22:37:01:715 ==>> $GBGGA,143705.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,59,,,41,34,,,41,25,,,41,1*7E

$GBGSV,7,2,27,41,,,41,60,,,40,7,,,40,3,,,40,1*70

$GBGSV,7,3,27,39,,,40,24,,,38,1,,,38,16,,,38,1*42

$GBGSV,7,4,27,11,,,38,43,,,38,33,,,38,10,,,37,1*79

$GBGSV,7,5,27,23,,,37,2,,,36,44,,,36,6,,,36,1*75

$GBGSV,7,6,27,12,,,36,9,,,36,32,,,35,5,,,34,1*7D

$GBGSV,7,7,27,4,,,33,13,,,30,38,,,29,1*46

$GBRMC,143705.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143705.516,0.000,773.223,773.223,707.131,2097152,2097152,2097152*64

OFF_OUT5
OVER 150


2025-07-31 22:37:01:839 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:37:01:843 ==>> 检测【左刹电压测试2】
2025-07-31 22:37:01:847 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:37:02:123 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3118  volt:5480 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:19][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:19][COMM]adc read battery ts volt:3 mv
[D][05:18:19][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:37:02:184 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 22:37:02:372 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:37:02:377 ==>> 检测【右刹电压测试2】
2025-07-31 22:37:02:395 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 22:37:02:398 ==>> 检测【转把电压测试2】
2025-07-31 22:37:02:414 ==>> 【转把电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 22:37:02:418 ==>> 检测【晶振检测】
2025-07-31 22:37:02:422 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:37:02:569 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 22:37:02:674 ==>> $GBGGA,143706.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,59,,,41,39,,,41,1*47

$GBGSV,7,2,27,34,,,41,25,,,41,41,

2025-07-31 22:37:02:727 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:37:02:731 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:37:02:736 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:37:02:742 ==>> ,,41,60,,,40,1*74

$GBGSV,7,3,27,7,,,39,24,,,38,1,,,38,16,,,38,1*71

$GBGSV,7,4,27,11,,,38,43,,,38,33,,,38,10,,,37,1*79

$GBGSV,7,5,27,23,,,37,2,,,36,44,,,36,6,,,36,1*75

$GBGSV,7,6,27,12,,,36,9,,,36,32,,,35,5,,,34,1*7D

$GBGSV,7,7,27,4,,,33,38,,,30,13,,,29,1*46

$GBRMC,143706.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143706.516,0.000,773.992,773.992,707.834,2097152,2097152,2097152*6B



2025-07-31 22:37:02:809 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1658mV
Get AD_V4 1647mV
Get AD_V5 2747mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 22:37:03:008 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:37:03:013 ==>> 检测【检测BootVer】
2025-07-31 22:37:03:018 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:37:03:362 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071541609
[D][05:18:20][FCTY]HardwareID  = 867222088007283
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:18:20][FCTY]Bat         = 3924 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11700 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1293
[D][05:18:20][FCTY]Acckey1 vol = 5491 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PL

2025-07-31 22:37:03:407 ==>> ATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3818 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:37:03:550 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:37:03:554 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:37:03:559 ==>> 检测【检测固件版本】
2025-07-31 22:37:03:581 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:37:03:584 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:37:03:588 ==>> 检测【检测蓝牙版本】
2025-07-31 22:37:03:603 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:37:03:607 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:37:03:624 ==>> 检测【检测MoBikeId】
2025-07-31 22:37:03:634 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:37:03:639 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:37:03:656 ==>> 检测【检测蓝牙地址】
2025-07-31 22:37:03:665 ==>> 取到目标值:CFCA8FA60A0E
2025-07-31 22:37:03:678 ==>> 【检测蓝牙地址】通过,【CFCA8FA60A0E】符合目标值【】要求!
2025-07-31 22:37:03:686 ==>> 提取到蓝牙地址:CFCA8FA60A0E
2025-07-31 22:37:03:692 ==>> 检测【BOARD_ID】
2025-07-31 22:37:03:699 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:37:03:728 ==>> 检测【检测充电电压】
2025-07-31 22:37:03:733 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:37:03:737 ==>> $GBGGA,143707.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,59,,,41,34,,,41,41,,,41,1*7C

$GBGSV,7,2,27,60,,,40,3,,,40,39,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,24,,,38,1,,,38,16,,,38,1*71

$GBGSV,7,4,27,11,,,38,33,,,38,10,,,37,43,,,37,1*76

$GBGSV,7,5,27,23,,,37,2,,,36,44,,,36,6,,,36,1*75

$GBGSV,7,6,27,12,,,36,9,,,36,32,,,35,5,,,34,1*7D

$GBGSV,7,7,27,4,,,33,38,,,30,13,,,29,1*46

$GBRMC,143707.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143707.516,0.000,770.921,770.921,705.026,2097152,2097152,2097152*63



2025-07-31 22:37:03:759 ==>> 检测【检测VBUS电压1】
2025-07-31 22:37:03:767 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:37:03:773 ==>> 检测【检测充电电流】
2025-07-31 22:37:03:779 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:37:03:803 ==>> 检测【检测IMEI】
2025-07-31 22:37:03:808 ==>> 取到目标值:867222088007283
2025-07-31 22:37:03:812 ==>> 【检测IMEI】通过,【867222088007283】符合目标值【】要求!
2025-07-31 22:37:03:837 ==>> 提取到IMEI:867222088007283
2025-07-31 22:37:03:841 ==>> 检测【检测IMSI】
2025-07-31 22:37:03:864 ==>> 取到目标值:460130071541609
2025-07-31 22:37:03:868 ==>> 【检测IMSI】通过,【460130071541609】符合目标值【】要求!
2025-07-31 22:37:03:871 ==>> 提取到IMSI:460130071541609
2025-07-31 22:37:03:875 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:37:03:895 ==>> 取到目标值:460130071541609
2025-07-31 22:37:03:901 ==>> 【校验网络运营商(移动)】通过,【460130071541609】符合目标值【】要求!
2025-07-31 22:37:03:913 ==>> 检测【打开CAN通信】
2025-07-31 22:37:03:928 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:37:04:016 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:37:04:139 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:37:04:143 ==>> 检测【检测CAN通信】
2025-07-31 22:37:04:149 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:37:04:197 ==>> [D][05:18:21][COMM]read battery soc:255
can send success


2025-07-31 22:37:04:227 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:37:04:302 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32756
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:37:04:347 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:37:04:411 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:37:04:415 ==>> 检测【关闭CAN通信】
2025-07-31 22:37:04:421 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:37:04:439 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:37:04:512 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:37:04:686 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:37:04:693 ==>> 检测【打印IMU STATE】
2025-07-31 22:37:04:700 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:37:04:726 ==>> $GBGGA,143708.516,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,59,,,41,34,,,41,41,,,41,1*73

$GBGSV,7,2,28,60,,,40,3,,,40,39,,,40,25,,,40,1*41

$GBGSV,7,3,28,7,,,39,24,,,39,1,,,38,16,,,38,1*7F

$GBGSV,7,4,28,11,,,38,43,,,38,33,,,38,10,,,37,1*76

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,12,,,36,9,,,36,32,,,35,5,,,34,1*72

$GBGSV,7,7,28,4,,,33,38,,,30,13,,,29,14,,,29,1*47

$GBRMC,143708.516,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143708.516,0.000,766.313,766.313,700.813,2097152,2097152,2097152*67



2025-07-31 22:37:04:902 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:37:04:967 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:37:04:974 ==>> 检测【六轴自检】
2025-07-31 22:37:04:995 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:37:05:194 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:37:05:715 ==>> $GBGGA,143709.516,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,59,,,41,39,,,41,34,,,41,1*7C

$GBGSV,7,2,28,25,,,41,41,,,41,60,,,40,7,,,40,1*4A

$GBGSV,7,3,28,3,,,40,24,,,39,1,,,39,16,,,38,1*74

$GBGSV,7,4,28,11,,,38,43,,,38,33,,,38,10,,,37,1*76

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,12,,,36,9,,,36,32,,,35,5,,,34,1*72

$GBGSV,7,7,28,4,,,33,38,,,30,14,,,30,13,,,29,1*4F

$GBRMC,143709.516,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143709.516,0.000,770.009,770.009,704.193,2097152,2097152,2097152*63



2025-07-31 22:37:06:192 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:37:06:713 ==>> $GBGGA,143710.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,60,,,41,59,,,41,39,,,41,1*7D

$GBGSV,7,2,28,34,,,41,25,,,41,41,,,41,7,,,40,1*4A

$GBGSV,7,3,28,3,,,40,24,,,39,11,,,39,1,,,38,1*73

$GBGSV,7,4,28,16,,,38,43,,,38,33,,,38,10,,,37,1*71

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,9,,,36,12,,,36,32,,,35,5,,,33,1*75

$GBGSV,7,7,28,4,,,33,14,,,31,38,,,30,13,,,29,1*4E

$GBRMC,143710.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143710.516,0.000,770.748,770.748,704.868,2097152,2097152,2097152*66



2025-07-31 22:37:06:911 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:37:07:123 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35581 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,-1,4037]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 22:37:07:331 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:37:07:335 ==>> 检测【打印IMU STATE2】
2025-07-31 22:37:07:357 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:37:07:508 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:37:07:601 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:37:07:606 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:37:07:612 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:37:07:720 ==>> $GBGGA,143711.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,59,,,41,34,,,41,25,,,41,1*71

$GBGSV,7,2,28,41,,,41,60,,,40,7,,,40,3,,,40,1*7F

$GBGSV,7,3,28,39,,,40,24,,,39,33,,,39,1,,,38,1*4A

$GBGSV,7,4,28,16,,,38,11,,,38,43,,,38,10,,,37,1*71

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,9,,,36,12,,,36,32,,,35,5,,,34,1*72

$GBGSV,7,7,28,4,,,33,14,,,32,38,,,30,13,,,29,1*4D

$GBRMC,143711.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143711.516,0.000,770.739,770.739,704.860,2097152,2097152,2097152*6F

5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:37:07:825 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[

2025-07-31 22:37:07:874 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:37:07:878 ==>> 检测【检测VBUS电压2】
2025-07-31 22:37:07:882 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:37:07:903 ==>> D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:37:08:265 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541609
[D][05:18:25][FCTY]HardwareID  = 867222088007283
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:25][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 50 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.

2025-07-31 22:37:08:310 ==>> 4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3818 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:37:08:405 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:37:08:831 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541609
[D][05:18:26][FCTY]HardwareID  = 867222088007283
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:18:26][FCTY]Bat         = 3924 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 176
[D][05:18:26][FCTY]Acckey1 vol = 5487 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3818 m

2025-07-31 22:37:08:921 ==>> v
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
$GBGGA,143712.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,3,,,41,59,,,41,39,,,41,1*48

$GBGSV,7,2,28,34,,,41,41,,,41,60,,,40,25,,,40,1*7A

$GBGSV,7,3,28,7,,,39,24,,,39,11,,,39,1,,,38,1*79

$GBGSV,7,4,28,16,,,38,43,,,38,33,,,38,10,,,37,1*71

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,9,,,36,12,,,36,32,,,35,5,,,34,1*72

$GBGSV,7,7,28,4,,,33,14,,,32,13,,,30,38,,,30,1*45

$GBRMC,143712.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143712.516,0.000,771.474,771.474,705.532,2097152,2097152,2097152*67



2025-07-31 22:37:08:941 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:37:09:269 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541609
[D][05:18:26][FCTY]HardwareID  = 867222088007283
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:18:26][FCTY]Bat         = 3924 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 152
[D][05:18:26][FCTY]Acckey1 vol = 5479 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCT

2025-07-31 22:37:09:299 ==>> Y]Bat1         = 3818 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:37:09:330 ==>>                                                                                                                                                                           

2025-07-31 22:37:09:471 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:37:10:224 ==>> $GBGGA,143713.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,3,,,41,59,,,41,39,,,41,1*48

$GBGSV,7,2,28,34,,,41,25,,,41,41,,,41,60,,,40,1*7B

$GBGSV,7,3,28,7,,,40,24,,,39,1,,,38,16,,,38,1*71

$GBGSV,7,4,28,11,,,38,43,,,38,33,,,38,10,,,37,1*76

$GBGSV,7,5,28,12,,,37,23,,,37,2,,,36,44,,,36,1*4E

$GBGSV,7,6,28,6,,,36,9,,,36,32,,,35,5,,,34,1*47

$GBGSV,7,7,28,4,,,33,14,,,32,13,,,30,38,,,30,1*45

$GBRMC,143713.516,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143713.516,0.000,772.954,772.954,706.885,2097152,2097152,2097152*64

[D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071541609
[D][05:18:27][FCTY]HardwareID  = 867222088007283
[

2025-07-31 22:37:10:329 ==>> D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:18:27][FCTY]Bat         = 3844 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 127
[D][05:18:27][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3818 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing


2025-07-31 22:37:10:434 ==>> [D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]==================================

2025-07-31 22:37:10:513 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:37:10:519 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:37:10:539 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:37:10:544 ==>> =========================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[D][05:18:27][SAL ]Cellular task submsg id[10]


2025-07-31 22:37:10:644 ==>> [D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5594C8A1820CD5296374498EFCBA2CE5B345285D97E8472A6BE2647569D853208D7A42230D1CB6427255E82084B980B0BA82C0CAAC0E885ACB323D0090B9013091720F1835FFBBB693D13510F6D3C5850B9B9
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m sw

2025-07-31 22:37:10:704 ==>> itch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150
                                                                                                                                           

2025-07-31 22:37:10:749 ==>>                                                                         

2025-07-31 22:37:10:788 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:37:10:793 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:37:10:799 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:37:10:809 ==>> [D][05:18:28][COMM]read battery soc:255


2025-07-31 22:37:10:900 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:37:11:071 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:37:11:084 ==>> 检测【打开WIFI(3)】
2025-07-31 22:37:11:088 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:37:11:330 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:37:11:608 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:37:11:614 ==>> 检测【扩展芯片hw】
2025-07-31 22:37:11:625 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:37:11:805 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:37:11:886 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:37:11:891 ==>> 检测【扩展芯片boot】
2025-07-31 22:37:11:905 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:37:11:910 ==>> 检测【扩展芯片sw】
2025-07-31 22:37:11:924 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:37:11:930 ==>> 检测【检测音频FLASH】
2025-07-31 22:37:11:942 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:37:12:081 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:37:13:072 ==>> +WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,CC057790A640,-64
+WIFISCAN:4,2,44A1917CA62B,-72
+WIFISCAN:4,3,44A1917CAD81,-75

[D][05:18:29][CAT1]wifi scan report total[4]
[D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------
[D][05:18:29][GNSS]recv submsg id[3]
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------

2025-07-31 22:37:13:178 ==>> 
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set f

2025-07-31 22:37:13:285 ==>> atal code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][PROT]index:1
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[D][05:18:29][HSDK][0] flush to flash addr:[0xE41900] --- write len --- 

2025-07-31 22:37:13:388 ==>> [256]
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05

2025-07-31 22:37:13:494 ==>> :18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[

2025-07-31 22:37:13:599 ==>> 991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][COMM]read battery soc:255


2025-07-31 22:37:14:842 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 22:37:15:334 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3F8EC56A49C5361B86F66F5FE772C57D9AD164400CABB50C56BBF37A7762E1ABE85B028441C09942EE4218857B1C311810CB034E6371

2025-07-31 22:37:15:394 ==>> 161C335DB52AD78717EA99044BD9A1EB5E90EF153CB6BC445AAACE525
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 22:37:15:469 ==>> [D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:37:16:230 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 22:37:16:552 ==>> [D][05:18:33][COMM]44948 imu init OK
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:33][COMM]accel parse set 0
[D][05:18:33][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:37:16:842 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 22:37:16:983 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:37:16:989 ==>> 检测【打开喇叭声音】
2025-07-31 22:37:17:011 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:37:17:708 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_

2025-07-31 22:37:17:787 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:37:17:794 ==>> 检测【打开大灯控制】
2025-07-31 22:37:17:804 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:37:17:818 ==>> start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991].

2025-07-31 22:37:17:918 ==>>  send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]45959 imu init OK
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:37:18:008 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:37:18:056 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:37:18:062 ==>> 检测【关闭仪表供电3】
2025-07-31 22:37:18:084 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:37:18:298 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0


2025-07-31 22:37:18:332 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:37:18:337 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:37:18:344 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:37:18:466 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:37:18:610 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:37:18:616 ==>> 检测【读大灯电压】
2025-07-31 22:37:18:626 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:37:18:788 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[33409]


2025-07-31 22:37:18:848 ==>>  D][05:18:36][COMM]read battery soc:255


2025-07-31 22:37:18:882 ==>> 【读大灯电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:37:18:891 ==>> 检测【关闭大灯控制2】
2025-07-31 22:37:18:915 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:37:19:062 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:37:19:156 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:37:19:161 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:37:19:171 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:37:19:291 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:37:19:429 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:37:19:437 ==>> 检测【打开WIFI(4)】
2025-07-31 22:37:19:460 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:37:19:629 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:37:19:758 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:37:19:765 ==>> 检测【EC800M模组版本】
2025-07-31 22:37:19:784 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:37:19:998 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:37:20:103 ==>> [D][05:18:37][CAT1]<<< 
+GETVERSION

2025-07-31 22:37:20:133 ==>> : "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:37:20:208 ==>>                                  ork error:[-1]. goto init


2025-07-31 22:37:20:287 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:37:20:293 ==>> 检测【配置蓝牙地址】
2025-07-31 22:37:20:316 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:37:20:498 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:CFCA8FA60A0E>】
2025-07-31 22:37:20:573 ==>> [D][05:18:37][PROT]CLEAN,SEND:1
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
+WIFISCAN:4,0,CC057790A641,-70
+WIFISCAN:4,1,CC057790A640,-70
+WIFISCAN:4,2,44A1917CA62B,-72
+WIFISCAN:4,3,CC057790A7C1,-75

[D][05:18:37][CAT1]wifi scan report total[4]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]S

2025-07-31 22:37:20:663 ==>> end Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B329CABF3637BD3FF28FB200D48E80AA4B68150CDA23A106206514E13A3168028063FF61A39E36EA9599D2431186F58A15566293BAD0E0717C95F8C48AE27DE8DE6DB27663C7518C4BF9AEB671474AED33F3D9
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]
[D][05:18:37][GNSS]recv submsg id[3]
[W][05:18:37][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:37:20:708 ==>> recv ble 1
recv ble 2
ble set mac ok :cf,ca,8f,a6,a,e
enable filters ret : 0

2025-07-31 22:37:20:841 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:37:20:847 ==>> 检测【BLETEST】
2025-07-31 22:37:20:863 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:37:20:871 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 22:37:20:904 ==>> 4A A4 01 A4 4A 


2025-07-31 22:37:21:009 ==>> recv ble 1
recv ble 2
<BSJ*MAC:CFCA8FA60A0E*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9CFCA8FA60A0E99999

2025-07-31 22:37:21:099 ==>> OVER 150


2025-07-31 22:37:21:204 ==>> [D][05:18:38][COMM]49681 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:37:21:873 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:37:21:881 ==>> 该项需要延时执行
2025-07-31 22:37:22:227 ==>> [D][05:18:39][COMM]50692 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:37:22:823 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:37:22:868 ==>>                                          

2025-07-31 22:37:23:234 ==>> [D][05:18:40][COMM]51703 imu init OK


2025-07-31 22:37:24:860 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 22:37:25:794 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B39AB17F1186636A2DB46E0A6FE0EC68CFDBDA72AC1BEA02ADC3C1A4152AC38AF6028BB24D4F10612B1F030F78754CFFD41D8BF541747

2025-07-31 22:37:25:854 ==>> 8EBBA384132FA5825E8C37C905F44589449A74A84B47EDDCE88ECAD47
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 22:37:26:867 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 22:37:28:879 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 22:37:31:029 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5D16BC5D9951CCF79E4EF50

2025-07-31 22:37:31:104 ==>> 75F329A49BE2FE1FD98C2167EBD0F21F4101FDF27991924F007B8BF710561B941ED5D4F2CB8D5AAA8E30244803C603B7080CC88A86F57FFCFA911B694A4E90E2AF4412C1780EE1
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
[D][05:18:48][COMM]read battery soc:255


2025-07-31 22:37:31:874 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:37:31:880 ==>> 检测【检测WiFi结果】
2025-07-31 22:37:31:892 ==>> WiFi信号:【F42A7D1297A3】,信号值:-67
2025-07-31 22:37:31:900 ==>> WiFi信号:【CC057790A641】,信号值:-71
2025-07-31 22:37:31:908 ==>> WiFi信号:【CC057790A7C1】,信号值:-72
2025-07-31 22:37:31:927 ==>> WiFi信号:【44A1917CAD81】,信号值:-75
2025-07-31 22:37:31:942 ==>> WiFi信号:【F88C21BCF57D】,信号值:-32
2025-07-31 22:37:31:958 ==>> WiFi信号:【CC057790A640】,信号值:-64
2025-07-31 22:37:31:967 ==>> WiFi信号:【44A1917CA62B】,信号值:-72
2025-07-31 22:37:31:988 ==>> WiFi数量【7】, 最大信号值:-32
2025-07-31 22:37:31:997 ==>> 检测【检测GPS结果】
2025-07-31 22:37:32:004 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:37:32:106 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all continue location stop
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all sing location stop


2025-07-31 22:37:32:878 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:37:32:888 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:32:912 ==>> 定位已等待【1】秒.
2025-07-31 22:37:32:921 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:37:33:316 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:50][COMM]Open GPS Module...
[D][05:18:50][COMM]LOC_MODEL_CONT
[D][05:18:50][GNSS]start event:8
[D][05:18:50][GNSS]GPS start. ret=0
[W][05:18:50][GNSS]start cont locating
[D][05:18:50][CAT1]gsm read msg sub id: 23
[D][05:18:50][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:50][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:37:33:880 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:33:888 ==>> 定位已等待【2】秒.
2025-07-31 22:37:34:016 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:37:34:667 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:37:34:892 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:34:901 ==>> 定位已等待【3】秒.
2025-07-31 22:37:34:923 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,40,,,42,60,,,42,34,,,41,25,,,40,1*7E

$GBGSV,3,2,09,39,,,40,59,,,39,41,,,34,16,,,17,1*75

$GBGSV,3,3,09,10,,,39,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1529.026,1529.026,49.158,2097152,2097152,2097152*4E

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:52][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 23, ret: 6
[D][05:18:52][CAT1]sub id: 23, ret: 6

                                         

2025-07-31 22:37:35:517 ==>> [D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:37:35:818 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,40,,,42,60,,,41,34,,,41,39,,,41,1*7B

$GBGSV,4,2,15,25,,,40,59,,,40,11,,,38,16,,,38,1*78

$GBGSV,4,3,15,2,,,38,10,,,37,12,,,37,41,,,37,1*4F

$GBGSV,4,4,15,7,,,36,3,,,34,44,,,39,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1599.070,1599.070,51.119,2097152,2097152,2097152*42



2025-07-31 22:37:35:893 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:35:902 ==>> 定位已等待【4】秒.
2025-07-31 22:37:36:243 ==>> [D][05:18:53][PROT]CLEAN,SEND:0
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[D][05:18:53][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_A

2025-07-31 22:37:36:333 ==>> CK
[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B580426B546206F7629800959AD484BD3E9C89E6413F0F27517CE220391898E0D086617E281DDF216B8FCFAA338CE34784A50F7F08A7AF60DB80101EB11378BCB774514E3E802C7DEBEACF910A997AF8152711
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 22:37:36:847 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,60,,,41,34,,,41,39,,,41,1*7C

$GBGSV,5,2,20,59,,,41,25,,,40,41,,,40,1,,,40,1*4D

$GBGSV,5,3,20,11,,,38,16,,,38,10,,,38,7,,,38,1*43

$GBGSV,5,4,20,43,,,38,44,,,37,12,,,37,2,,,36,1*4D

$GBGSV,5,5,20,3,,,36,4,,,34,5,,,34,28,,,36,1*4C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1592.841,1592.841,50.925,2097152,2097152,2097152*44



2025-07-31 22:37:36:907 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:36:916 ==>> 定位已等待【5】秒.
2025-07-31 22:37:36:929 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 22:37:37:876 ==>> $GBGGA,143741.693,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,60,,,41,34,,,41,39,,,41,1*7B

$GBGSV,6,2,24,59,,,41,41,,,41,25,,,40,1,,,39,1*45

$GBGSV,6,3,24,7,,,39,11,,,38,16,,,38,43,,,38,1*43

$GBGSV,6,4,24,10,,,37,12,,,37,3,,,37,9,,,37,1*7A

$GBGSV,6,5,24,44,,,36,2,,,36,23,,,36,6,,,36,1*76

$GBGSV,6,6,24,4,,,34,32,,,34,5,,,33,33,,,38,1*7B

$GBRMC,143741.693,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143741.693,0.000,1571.790,1571.790,50.262,2097152,2097152,2097152*5A



2025-07-31 22:37:37:921 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:37:930 ==>> 定位已等待【6】秒.
2025-07-31 22:37:38:767 ==>> $GBGGA,143742.593,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,60,,,41,34,,,41,39,,,41,1*7B

$GBGSV,6,2,24,59,,,41,41,,,41,25,,,41,7,,,40,1*4C

$GBGSV,6,3,24,11,,,39,16,,,39,3,,,39,1,,,38,1*71

$GBGSV,6,4,24,43,,,38,33,,,38,10,,,37,12,,,37,1*77

$GBGSV,6,5,24,9,,,37,23,,,37,44,,,36,2,,,36,1*79

$GBGSV,6,6,24,6,,,36,4,,,34,5,,,34,32,,,33,1*43

$GBRMC,143742.593,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143742.593,0.000,1582.304,1582.304,50.597,2097152,2097152,2097152*57



2025-07-31 22:37:38:932 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 22:37:38:943 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:38:967 ==>> 定位已等待【7】秒.
2025-07-31 22:37:39:778 ==>> $GBGGA,143743.573,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,41,34,,,41,59,,,41,1*7D

$GBGSV,7,2,25,41,,,41,39,,,40,25,,,40,3,,,40,1*4E

$GBGSV,7,3,25,7,,,39,16,,,39,11,,,38,1,,,38,1*74

$GBGSV,7,4,25,43,,,38,33,,,38,10,,,37,12,,,37,1*77

$GBGSV,7,5,25,23,,,37,9,,,36,44,,,36,2,,,36,1*78

$GBGSV,7,6,25,6,,,36,4,,,34,5,,,34,32,,,33,1*43

$GBGSV,7,7,25,24,,,32,1*76

$GBRMC,143743.573,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143743.573,0.000,1565.453,1565.453,50.067,2097152,2097152,2097152*52



2025-07-31 22:37:39:943 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:39:955 ==>> 定位已等待【8】秒.
2025-07-31 22:37:40:738 ==>> $GBGGA,143744.553,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,41,34,,,41,59,,,41,1*7D

$GBGSV,7,2,25,41,,,41,39,,,41,25,,,41,3,,,41,1*4F

$GBGSV,7,3,25,7,,,40,11,,,39,33,,,39,16,,,38,1*4A

$GBGSV,7,4,25,1,,,38,43,,,38,10,,,37,12,,,37,1*46

$GBGSV,7,5,25,23,,,37,44,,,37,9,,,36,6,,,36,1*7D

$GBGSV,7,6,25,2,,,35,24,,,35,4,,,34,5,,,34,1*45

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,143744.553,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143744.553,0.000,1578.717,1578.717,50.489,2097152,2097152,2097152*53



2025-07-31 22:37:40:934 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 22:37:40:948 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:40:957 ==>> 定位已等待【9】秒.
2025-07-31 22:37:41:489 ==>> [D][05:18:58][PROT]CLEAN,SEND:0
[D][05:18:58][PROT]CLEAN:0
[D][05:18:58][PROT]index:2 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:3
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:18:58][PROT]sending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[134]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CA

2025-07-31 22:37:41:564 ==>> T1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE5EDDEF6D068ABBAC0FBE6125967A672C0968ADCA3288FFA2282A93C1CA562095052768BF76583495F1F9894CDCC9D07181C1
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 22:37:41:669 ==>> $GBGGA,143745.533,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,34,,,42,41,,,42,60,,,41,1*76

$GBGSV,7,2,27,59,,,41,39,,,41,25,,,41,3,,,41,1*44

$GBGSV,7,3,27,7,,,40,11,,,39,33,,,39,16,,,39,1*49

$GBGSV,7,4,27,1,,,39,43,,,38,10,,,38,12,,,37,1*4A

$GBGSV,7,5,27,23,,,37,44,,,37,24,,,37,9,,,36,1*4E


2025-07-31 22:37:41:714 ==>> 

$GBGSV,7,6,27,6,,,36,2,,,36,4,,,34,5,,,34,1*77

$GBGSV,7,7,27,32,,,34,38,,,28,22,,,36,1*71

$GBRMC,143745.533,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143745.533,0.000,1577.024,1577.024,50.462,2097152,2097152,2097152*51



2025-07-31 22:37:41:961 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:41:970 ==>> 定位已等待【10】秒.
2025-07-31 22:37:42:692 ==>> $GBGGA,143746.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,42,34,,,41,59,,,41,1*7E

$GBGSV,7,2,26,39,,,41,25,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,11,,,39,33,,,38,16,,,38,1*48

$GBGSV,7,4,26,1,,,38,43,,,38,10,,,38,12,,,37,1*4A

$GBGSV,7,5,26,44,,,37,24,,,37,23,,,36,9,,,36,1*4E

$GBGSV,7,6,26,6,,,36,2,,,36,4,,,34,5,,,34,1*76

$GBGSV,7,7,26,32,,,34,38,,,28,1*75

$GBRMC,143746.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143746.513,0.000,1567.453,1567.453,50.153,2097152,2097152,2097152*57



2025-07-31 22:37:42:948 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:37:42:963 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:42:972 ==>> 定位已等待【11】秒.
2025-07-31 22:37:43:693 ==>> $GBGGA,143747.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,41,,,41,59,,,41,1*7E

$GBGSV,7,2,26,39,,,41,25,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,11,,,39,33,,,39,16,,,38,1*49

$GBGSV,7,4,26,1,,,38,43,,,38,24,,,38,10,,,37,1*4F

$GBGSV,7,5,26,12,,,37,23,,,37,44,,,36,9,,,36,1*4B

$GBGSV,7,6,26,6,,,36,2,,,36,4,,,34,5,,,34,1*76

$GBGSV,7,7,26,32,,,34,38,,,29,1*74

$GBRMC,143747.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143747.513,0.000,1570.637,1570.637,50.249,2097152,2097152,2097152*5E



2025-07-31 22:37:43:978 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:43:989 ==>> 定位已等待【12】秒.
2025-07-31 22:37:44:687 ==>> $GBGGA,143748.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,41,,,41,59,,,41,1*7D

$GBGSV,7,2,26,39,,,41,3,,,41,60,,,41,25,,,40,1*4E

$GBGSV,7,3,26,7,,,40,33,,,39,1,,,39,11,,,38,1*7F

$GBGSV,7,4,26,16,,,38,43,,,38,24,,,38,10,,,37,1*79

$GBGSV,7,5,26,12,,,37,23,,,37,44,,,37,6,,,37,1*44

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,29,1*74

$GBRMC,143748.513,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143748.513,0.000,1572.227,1572.227,50.296,2097152,2097152,2097152*53



2025-07-31 22:37:44:973 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:37:44:988 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:45:008 ==>> 定位已等待【13】秒.
2025-07-31 22:37:45:692 ==>> $GBGGA,143749.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,41,41,,,41,59,,,41,1*7D

$GBGSV,7,2,26,60,,,41,39,,,40,3,,,40,25,,,40,1*4E

$GBGSV,7,3,26,7,,,40,33,,,38,1,,,38,11,,,38,1*7F

$GBGSV,7,4,26,16,,,38,43,,,38,24,,,38,10,,,37,1*79

$GBGSV,7,5,26,12,,,37,44,,,37,23,,,36,6,,,36,1*44

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,29,1*74

$GBRMC,143749.513,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143749.513,0.000,1562.657,1562.657,49.988,2097152,2097152,2097152*5E



2025-07-31 22:37:45:997 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:46:008 ==>> 定位已等待【14】秒.
2025-07-31 22:37:46:773 ==>> [D][05:19:03][PROT]CLEAN,SEND:2
[D][05:19:03][PROT]index:2 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:6
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:2
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][CAT1]Send Data To Server[134][137] ... ->:
0043B6851133113311331133113

2025-07-31 22:37:46:878 ==>> 31B88BEC3AAE9D4491C0FD1ED3D210F4A881A0F42A05580393F6C85C0F85ED77E61880F906A3545CA10EAE1CA67477A4C77C2819B55
[D][05:19:04][CAT1]<<< 
SEND OK

[D][05:19:04][CAT1]exec over: func id: 15, ret: 11
[D][05:19:04][CAT1]sub id: 15, ret: 11

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:04][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:04][M2M ]g_m2m_is_idle become true
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:04][PROT]M2M Send ok [1629955144]
$GBGGA,143750.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,41,,,41,59,,,41,1*7E

$GBGSV,7,2,26,60,,,41,39,,,41,25,,,41,3,,,40,1*4E

$GBGSV,7,3,26,7,,,40,33,,,39,11,,,39,1,,,38,1*7F

$GBGSV,7,4,26,16,,,38,43,,,38,24,,,38,10,,,37,1*79

$GBGSV,7,5,26,12,,,37,23,,,37,44,,,36,6,,,36,1*44

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,29,1*74

$GBRMC,143750.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143750.513,0.000,1570.637,1570.637,50.249,2097152,2097152,2097152*58



2025-07-31 22:37:46:968 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:37:46:997 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:47:009 ==>> 定位已等待【15】秒.
2025-07-31 22:37:47:688 ==>> $GBGGA,143751.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,34,,,42,41,,,41,59,,,41,1*7E

$GBGSV,7,2,26,39,,,41,25,,,41,60,,,40,3,,,40,1*4F

$GBGSV,7,3,26,7,,,40,33,,,39,16,,,39,11,,,38,1*49

$GBGSV,7,4,26,1,,,38,43,,,38,24,,,38,10,,,37,1*4F

$GBGSV,7,5,26,12,,,37,23,,,37,44,,,37,6,,,37,1*44

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,29,1*74

$GBRMC,143751.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143751.513,0.000,1572.227,1572.227,50.297,2097152,2097152,2097152*5A



2025-07-31 22:37:48:007 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:48:018 ==>> 定位已等待【16】秒.
2025-07-31 22:37:48:700 ==>> $GBGGA,143752.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,42,34,,,41,59,,,41,1*7E

$GBGSV,7,2,26,39,,,41,25,,,41,60,,,40,3,,,40,1*4F

$GBGSV,7,3,26,7,,,40,33,,,39,16,,,38,11,,,38,1*48

$GBGSV,7,4,26,1,,,38,43,,,38,24,,,38,10,,,37,1*4F

$GBGSV,7,5,26,12,,,37,23,,,37,44,,,37,6,,,36,1*45

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,30,1*7C

$GBRMC,143752.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143752.513,0.000,1570.628,1570.628,50.241,2097152,2097152,2097152*52



2025-07-31 22:37:48:987 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:37:49:017 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:49:030 ==>> 定位已等待【17】秒.
2025-07-31 22:37:49:690 ==>> $GBGGA,143753.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,42,34,,,41,59,,,41,1*7E

$GBGSV,7,2,26,39,,,41,25,,,41,60,,,40,3,,,40,1*4F

$GBGSV,7,3,26,7,,,40,33,,,39,16,,,38,11,,,38,1*48

$GBGSV,7,4,26,1,,,38,43,,,38,24,,,38,10,,,37,1*4F

$GBGSV,7,5,26,12,,,37,23,,,36,44,,,36,6,,,36,1*45

$GBGSV,7,6,26,9,,,36,2,,,36,4,,,34,5,,,34,1*79

$GBGSV,7,7,26,32,,,34,38,,,30,1*7C

$GBRMC,143753.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143753.513,0.000,1567.441,1567.441,50.141,2097152,2097152,2097152*50



2025-07-31 22:37:50:025 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:50:037 ==>> 定位已等待【18】秒.
2025-07-31 22:37:50:758 ==>> $GBGGA,143754.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,59,,,41,1*7C

$GBGSV,7,2,27,39,,,41,25,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,3,27,7,,,40,33,,,39,16,,,38,11,,,38,1*49

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,23,,,37,44,,,36,6,,,36,1*45

$GBGSV,7,6,27,9,,,36,2,,,35,4,,,34,5,,,34,1*7B

$GBGSV,7,7,27,32,,,34,14,,,31,38,,,30,1*7A

$GBRMC,143754.513,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143754.513,0.000,1555.463,1555.463,49.768,2097152,2097152,2097152*52



2025-07-31 22:37:51:014 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 22:37:51:029 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:51:039 ==>> 定位已等待【19】秒.
2025-07-31 22:37:51:894 ==>> $GBGGA,143755.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,59,,,41,1*7C

$GBGSV,7,2,27,39,,,41,25,,,41,3,,,41,60,,,40,1*4F

$GBGSV,7,3,27,7,,,39,33,,,38,16,,,38,11,,,38,1*46

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,23,,,37,44,,,36,6,,,36,1*45

$GBGSV,7,6,27,9,,,36,2,,,36,5,,,34,32,,,34,1*4D

$GBGSV,7,7,27,4,,,33,14,,,31,38,,,30,1*48

$GBRMC,143755.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143755.513,0.000,1553.928,1553.928,49.720,2097152,2097152,2097152*5F

[D][05:19:09][PROT]CLEAN,SEND:2
[D][05:19:09][PROT]index:2 1629955149
[D][05:19:09][PROT]is_send:0
[D][05:19:09][PROT]sequence_num:6
[D][05:19:09][PROT]retry_timeout:0
[D][05:19:09][PROT]retry_times:1
[D][05:19:09][PROT]send_path:0x2
[D][05:19:09][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:09][PROT]===========================================================
[W][05:19:09][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955149]
[D][05:19:09][PROT]===========================================================
[D][05:19:09][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:19:09][PROT]sending traceid [9999999999900007]
[D][05:19:09][PROT]Send_TO_

2025-07-31 22:37:51:969 ==>> M2M [1629955149]
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:09][SAL ]sock send credit cnt[6]
[D][05:19:09][SAL ]sock send ind credit cnt[6]
[D][05:19:09][M2M ]m2m send data len[134]
[D][05:19:09][SAL ]Cellular task submsg id[10]
[D][05:19:09][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:09][CAT1]gsm read msg sub id: 15
[D][05:19:09][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:09][CAT1]<<< 
ERROR



2025-07-31 22:37:52:044 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:52:055 ==>> 定位已等待【20】秒.
2025-07-31 22:37:52:699 ==>> $GBGGA,143756.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,39,,,41,1*7A

$GBGSV,7,2,27,3,,,41,59,,,40,25,,,40,60,,,40,1*49

$GBGSV,7,3,27,7,,,40,33,,,38,16,,,38,11,,,38,1*48

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,23,,,36,44,,,36,6,,,36,1*44

$GBGSV,7,6,27,9,,,36,2,,,35,5,,,34,32,,,34,1*4E

$GBGSV,7,7,27,4,,,34,14,,,32,38,,,30,1*4C

$GBRMC,143756.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143756.513,0.000,1552.386,1552.386,49.664,2097152,2097152,2097152*5D



2025-07-31 22:37:53:030 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 22:37:53:045 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:53:064 ==>> 定位已等待【21】秒.
2025-07-31 22:37:53:698 ==>> $GBGGA,143757.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,39,,,41,1*7A

$GBGSV,7,2,27,3,,,40,59,,,40,25,,,40,60,,,40,1*48

$GBGSV,7,3,27,7,,,40,11,,,39,33,,,38,16,,,38,1*49

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,23,,,37,44,,,36,6,,,36,1*45

$GBGSV,7,6,27,9,,,36,2,,,36,5,,,34,32,,,34,1*4D

$GBGSV,7,7,27,4,,,34,14,,,32,38,,,30,1*4C

$GBRMC,143757.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143757.513,0.000,1555.454,1555.454,49.759,2097152,2097152,2097152*53



2025-07-31 22:37:54:049 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:54:060 ==>> 定位已等待【22】秒.
2025-07-31 22:37:54:688 ==>> $GBGGA,143758.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,3,,,41,1*43

$GBGSV,7,2,27,39,,,40,59,,,40,25,,,40,60,,,40,1*71

$GBGSV,7,3,27,7,,,40,11,,,38,33,,,38,16,,,38,1*48

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,23,,,37,12,,,36,44,,,36,6,,,36,1*44

$GBGSV,7,6,27,9,,,36,2,,,36,5,,,34,32,,,34,1*4D

$GBGSV,7,7,27,4,,,34,14,,,31,38,,,30,1*4F

$GBRMC,143758.513,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143758.513,0.000,1550.851,1550.851,49.616,2097152,2097152,2097152*56



2025-07-31 22:37:55:033 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 22:37:55:063 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:55:082 ==>> 定位已等待【23】秒.
2025-07-31 22:37:55:692 ==>> $GBGGA,143759.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,3,,,41,1*43

$GBGSV,7,2,27,39,,,41,59,,,41,25,,,40,60,,,40,1*71

$GBGSV,7,3,27,7,,,40,11,,,38,33,,,38,16,,,38,1*48

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,23,,,37,12,,,36,44,,,36,6,,,36,1*44

$GBGSV,7,6,27,9,,,36,2,,,36,5,,,34,32,,,34,1*4D

$GBGSV,7,7,27,4,,,34,14,,,32,38,,,30,1*4C

$GBRMC,143759.513,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143759.513,0.000,1555.458,1555.458,49.763,2097152,2097152,2097152*54



2025-07-31 22:37:56:074 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:56:086 ==>> 定位已等待【24】秒.
2025-07-31 22:37:56:713 ==>> $GBGGA,143800.513,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,34,,,41,3,,,41,1*43

$GBGSV,7,2,27,39,,,41,59,,,41,25,,,40,60,,,40,1*71

$GBGSV,7,3,27,7,,,40,11,,,39,33,,,38,16,,,38,1*49

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,23,,,37,12,,,36,44,,,36,6,,,36,1*44

$GBGSV,7,6,27,9,,,36,2,,,35,5,,,34,32,,,34,1*4E

$GBGSV,7,7,27,4,,,33,14,,,32,38,,,30,1*4B

$GBRMC,143800.513,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143800.513,0.000,1553.926,1553.926,49.718,2097152,2097152,2097152*5B



2025-07-31 22:37:57:029 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 22:37:57:089 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:57:099 ==>> 定位已等待【25】秒.
2025-07-31 22:37:57:718 ==>> $GBGGA,143801.513,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,34,,,41,3,,,41,1*40

$GBGSV,7,2,27,59,,,41,39,,,40,25,,,40,60,,,40,1*70

$GBGSV,7,3,27,7,,,39,11,,,39,33,,,38,16,,,38,1*47

$GBGSV,7,4,27,1,,,38,43,,,38,24,,,38,10,,,37,1*4E

$GBGSV,7,5,27,23,,,36,12,,,36,44,,,36,6,,,36,1*45

$GBGSV,7,6,27,9,,,36,2,,,35,5,,,34,32,,,34,1*4E

$GBGSV,7,7,27,4,,,33,14,,,32,38,,,30,1*4B

$GBRMC,143801.513,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143801.513,0.000,772.445,772.445,706.418,2097152,2097152,2097152*65



2025-07-31 22:37:58:098 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:58:110 ==>> 定位已等待【26】秒.
2025-07-31 22:37:58:712 ==>> $GBGGA,143802.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,60,,,41,3,,,41,59,,,41,1*44

$GBGSV,7,2,28,39,,,41,34,,,41,41,,,41,7,,,40,1*47

$GBGSV,7,3,28,25,,,40,11,,,39,24,,,38,16,,,38,1*70

$GBGSV,7,4,28,1,,,38,43,,,38,33,,,38,10,,,37,1*47

$GBGSV,7,5,28,6,,,36,9,,,36,44,,,36,12,,,36,1*72

$GBGSV,7,6,28,23,,,36,2,,,35,5,,,34,4,,,34,1*4C

$GBGSV,7,7,28,32,,,34,14,,,32,38,,,30,28,,,,1*7C

$GBRMC,143802.513,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143802.513,0.000,776.281,776.281,709.927,2097152,2097152,2097152*68



2025-07-31 22:37:59:045 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 22:37:59:104 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:37:59:114 ==>> 定位已等待【27】秒.
2025-07-31 22:37:59:719 ==>> $GBGGA,143803.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,60,,,41,3,,,41,59,,,41,1*44

$GBGSV,7,2,28,39,,,41,34,,,41,41,,,41,7,,,40,1*47

$GBGSV,7,3,28,25,,,40,11,,,39,24,,,38,16,,,38,1*70

$GBGSV,7,4,28,1,,,38,43,,,38,33,,,38,10,,,37,1*47

$GBGSV,7,5,28,6,,,36,9,,,36,44,,,36,12,,,36,1*72

$GBGSV,7,6,28,23,,,36,2,,,35,5,,,34,4,,,34,1*4C

$GBGSV,7,7,28,32,,,34,14,,,32,38,,,30,28,,,,1*7C

$GBRMC,143803.513,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143803.513,0.000,776.281,776.281,709.927,2097152,2097152,2097152*69



2025-07-31 22:38:00:108 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:00:119 ==>> 定位已等待【28】秒.
2025-07-31 22:38:00:709 ==>> $GBGGA,143804.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,60,,,41,59,,,41,34,,,41,1*70

$GBGSV,7,2,28,41,,,41,3,,,40,39,,,40,25,,,40,1*43

$GBGSV,7,3,28,7,,,39,11,,,39,24,,,38,16,,,38,1*4E

$GBGSV,7,4,28,1,,,38,43,,,38,33,,,38,10,,,37,1*47

$GBGSV,7,5,28,2,,,36,6,,,36,9,,,36,44,,,36,1*43

$GBGSV,7,6,28,12,,,36,23,,,36,5,,,34,4,,,34,1*7E

$GBGSV,7,7,28,32,,,34,14,,,32,38,,,30,28,,,,1*7C

$GBRMC,143804.513,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143804.513,0.000,774.742,774.742,708.519,2097152,2097152,2097152*6E



2025-07-31 22:38:01:073 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 22:38:01:118 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:01:129 ==>> 定位已等待【29】秒.
2025-07-31 22:38:01:718 ==>> $GBGGA,143805.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,59,,,41,34,,,41,41,,,41,1*70

$GBGSV,7,2,28,60,,,40,3,,,40,39,,,40,25,,,40,1*41

$GBGSV,7,3,28,7,,,39,24,,,38,16,,,38,1,,,38,1*7E

$GBGSV,7,4,28,11,,,38,43,,,38,33,,,38,10,,,37,1*76

$GBGSV,7,5,28,6,,,36,9,,,36,12,,,36,23,,,36,1*73

$GBGSV,7,6,28,2,,,35,44,,,35,5,,,34,32,,,34,1*7B

$GBGSV,7,7,28,4,,,33,14,,,32,38,,,30,28,,,,1*4E

$GBRMC,143805.513,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143805.513,0.000,770.144,770.144,704.314,2097152,2097152,2097152*68



2025-07-31 22:38:01:824 ==>>                                                                                                                                                                                                                                                                               

2025-07-31 22:38:01:914 ==>>                                                                                                                                                                                             05:19:19][SAL ]socket[4] has closed
[D][05:19:19][PROT]protocol read data ok
[E][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:19][PROT]M2M Send Fail [1629955159]
[D][05:19:19][PROT]CLEAN,SEND:2
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:19][PROT]CLEAN:2
[D][05:19:19][CAT1]gsm read msg sub id: 10
[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:38:02:129 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:02:141 ==>> 定位已等待【30】秒.
2025-07-31 22:38:02:219 ==>> [D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 10, ret: 6
[D][05:19:19][CAT1]sub id: 10, ret: 6

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:19][M2M ]m2m gsm shut done, ret[0]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:19][SAL ]open socket ind id[4], rst[0]
[D][05:19:19][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:19][SAL ]Cellular task submsg id[8]
[D][05:19:19][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:19][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:19][CAT1]gsm read msg sub id: 8
[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:19][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:38:02:401 ==>> [D][05:19:19][CAT1]pdpdeact urc len[22]


2025-07-31 22:38:02:720 ==>> $GBGGA,143806.513,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,59,,,41,34,,,41,1*46

$GBGSV,7,2,28,41,,,41,60,,,40,39,,,40,25,,,40,1*76

$GBGSV,7,3,28,7,,,39,24,,,38,16,,,38,1,,,38,1*7E

$GBGSV,7,4,28,11,,,38,43,,,38,33,,,38,10,,,37,1*76

$GBGSV,7,5,28,2,,,36,6,,,36,9,,,36,44,,,36,1*43

$GBGSV,7,6,28,12,,,36,23,,,36,5,,,34,4,,,34,1*7E

$GBGSV,7,7,28,32,,,34,14,,,32,38,,,30,28,,,,1*7C

$GBRMC,143806.513,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143806.513,0.000,773.207,773.207,707.114,2097152,2097152,2097152*6A



2025-07-31 22:38:03:069 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 22:38:03:144 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:03:163 ==>> 定位已等待【31】秒.
2025-07-31 22:38:03:550 ==>> [D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:20][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:38:03:806 ==>> $GBGGA,143807.513,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,59,,,41,34,,,41,1*46

$GBGSV,7,2,28,41,,,41,60,,,40,39,,,40,25,,,40,1*76

$GBGSV,7,3,28,7,,,39,24,,,38,16,,,38,1,,,38,1*7E

$GBGSV,7,4,28,11,,,38,33,,,38,43,,,38,10,,,37,1*76

$GBGSV,7,5,28,9,,,36,6,,,36,12,,,36,23,,,36,1*73

$GBGSV,7,6,28,2,,,35,44,,,35,5,,,34,4,,,34,1*4E

$GBGSV,7,7,28,32,,,34,14,,,32,38,,,30,28,,,,1*7C

$GBRMC,143807.513,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143807.513,0.000,771.677,771.677,705.715,2097152,2097152,2097152*6E

[D][05:19:21][CAT1]opened : 0, 0
[D][05:19:21][SAL ]Cellular task submsg id[68]
[D][05:19:21][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:21][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:21][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:21][M2M ]g_m2m_is_idle become true
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 22:38:04:155 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:04:175 ==>> 定位已等待【32】秒.
2025-07-31 22:38:04:722 ==>> $GBGGA,143808.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,60,,,41,40,,,41,3,,,41,34,,,41,1*4C

$GBGSV,7,2,28,41,,,41,39,,,40,59,,,40,25,,,40,1*7C

$GBGSV,7,3,28,7,,,39,24,,,38,16,,,38,1,,,38,1*7E

$GBGSV,7,4,28,11,,,38,33,,,38,43,,,38,2,,,36,1*44

$GBGSV,7,5,28,10,,,36,9,,,36,6,,,36,44,,,36,1*70

$GBGSV,7,6,28,12,,,36,23,,,36,5,,,34,32,,,34,1*4B

$GBGSV,7,7,28,4,,,33,14,,,32,38,,,31,28,,,,1*4F

$GBRMC,143808.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143808.513,0.000,772.439,772.439,706.412,2097152,2097152,2097152*66



2025-07-31 22:38:05:083 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 22:38:05:158 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:05:173 ==>> 定位已等待【33】秒.
2025-07-31 22:38:05:756 ==>> $GBGGA,143805.520,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,16,67,62,38,40,65,171,41,7,63,198,39,3,61,190,40,1*46

$GBGSV,7,2,28,39,60,36,40,6,58,6,36,59,52,129,40,10,49,224,36,1*74

$GBGSV,7,3,28,2,46,237,36,9,46,333,36,1,45,125,38,34,44,104,41,1*4C

$GBGSV,7,4,28,41,41,264,41,60,41,238,40,4,31,114,33,5,23,257,34,1*75

$GBGSV,7,5,28,14,14,321,31,24,14,260,38,38,5,191,30,44,1,185,36,1*7C

$GBGSV,7,6,28,25,,,40,11,,,38,33,,,38,43,,,37,1*7D

$GBGSV,7,7,28,12,,,36,23,,,36,32,,,34,28,,,,1*72

$GBRMC,143805.520,V,,,,,,,310725,1.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,143805.520,0.000,0.161,0.181,0.277,3.984,1.928,15*5B



2025-07-31 22:38:06:161 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:06:173 ==>> 定位已等待【34】秒.
2025-07-31 22:38:07:167 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:38:07:181 ==>> 定位已等待【35】秒.
2025-07-31 22:38:07:514 ==>> $GBGGA,143806.500,2301.2569941,N,11421.9436615,E,1,09,1.23,80.500,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,10,59,34,60,41,,,,4.11,1.23,3.92,4*03

$GBGSV,7,1,27,40,65,171,41,7,63,198,39,3,61,190,40,39,60,36,40,1*4D

$GBGSV,7,2,27,6,58,6,36,16,58,10,38,10,51,207,37,25,50,355,40,1*7F

$GBGSV,7,3,27,59,49,130,41,2,46,237,36,9,46,333,36,1,45,125,38,1*42

$GBGSV,7,4,27,34,44,104,41,60,42,239,40,41,41,264,41,11,38,137,38,1*7B

$GBGSV,7,5,27,12,31,72,36,4,31,114,33,5,23,257,34,14,14,321,32,1*40

$GBGSV,7,6,27,24,14,260,38,38,5,191,30,44,1,185,36,33,,,38,1*49

$GBGSV,7,7,27,43,,,38,23,,,36,32,,,34,1*7D

$GBGSV,1,1,04,40,65,171,41,39,60,36,41,34,44,104,39,41,41,264,41,5*42

$GBRMC,143806.500,A,2301.2569941,N,11421.9436615,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:19:24][GNSS]HD8040 GPS
[D][05:19:24][GNSS]GPS diff_sec 124017522, report 0x42 frame
$GBGST,143806.500,1.122,0.248,0.263,0.458,2.221,1.600,9.205*72

[D][05:19:24][COMM]Main Task receive event:131
[D][05:19:24][COMM]index:0,power_mode:0xFF
[D][05:19:24][COMM]index:1,sound_mode:0xFF
[D][05:19:24][COMM]index:2,gsensor_mode:0xFF
[D][05:19:24][COMM]index:3,report_freq_mode:0xFF
[D][05:19:24][COMM]index:4,report_period:0xFF
[D][05:19:24][COMM]index:5,normal_reset_mode

2025-07-31 22:38:07:619 ==>> :0xFF
[D][05:19:24][COMM]index:6,normal_reset_period:0xFF
[D][05:19:24][COMM]index:7,spock_over_speed:0xFF
[D][05:19:24][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:24][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:24][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:24][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:24][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:24][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:24][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:24][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:24][COMM]index:16,imu_config_params:0xFF
[D][05:19:24][COMM]index:17,long_connect_params:0xFF
[D][05:19:24][COMM]index:18,detain_mark:0xFF
[D][05:19:24][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:24][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:24][COMM]index:21,mc_mode:0xFF
[D][05:19:24][COMM]index:22,S_mode:0xFF
[D][05:19:24][COMM]index:23,overweight:0xFF
[D][05:19:24][COMM]index:24,standstill_mode:0xFF
[D][05:19:24][COMM]index:25,night_mode:0xFF
[D][05:19:24][COMM]index:26,experiment1:0xFF
[D][05:19:24][COMM]index:27,experiment2:0xFF
[D][05:19:24][COMM]index:28,experiment3:0xFF
[D][05:19:24][COMM]index:29,experiment4:

2025-07-31 22:38:07:724 ==>> 0xFF
[D][05:19:24][COMM]index:30,night_mode_start:0xFF
[D][05:19:24][COMM]index:31,night_mode_end:0xFF
[D][05:19:24][COMM]index:33,park_report_minutes:0xFF
[D][05:19:24][COMM]index:34,park_report_mode:0xFF
[D][05:19:24][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:24][COMM]index:38,charge_battery_para: FF
[D][05:19:24][COMM]index:39,multirider_mode:0xFF
[D][05:19:24][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:24][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:24][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:24][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:24][COMM]index:44,riding_duration_config:0xFF
[D][05:19:24][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:24][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:24][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:24][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:24][COMM]index:49,mc_load_startup:0xFF
[D][05:19:24][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:24][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:24][COMM]index:52,traffic_mode:0xFF
[D][05:19:24][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:24][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:24][COMM]i

2025-07-31 22:38:07:829 ==>> ndex:55,wheel_alarm_play_switch:255
[D][05:19:24][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:24][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:24][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:24][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:24][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:24][COMM]index:63,experiment5:0xFF
[D][05:19:24][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:24][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:24][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:24][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:24][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:24][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:24][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:24][COMM]index:72,experiment6:0xFF
[D][05:19:24][COMM]index:73,experiment7:0xFF
[D][05:19:24][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:24][COMM]index:75,zero_value_from_server:-1
[D][05:19:24][COMM]index:76,multirider_threshold:255
[D][05:19:24][COMM]index:77,experiment8:255
[D][05:19:24][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:24][COMM]index:79,temp_park_tail_light_twinkle_duratio

2025-07-31 22:38:07:934 ==>> n:255
[D][05:19:24][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:24][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:24][COMM]index:83,loc_report_interval:255
[D][05:19:24][COMM]index:84,multirider_threshold_p2:255
[D][05:19:24][COMM]index:85,multirider_strategy:255
[D][05:19:24][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:24][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:24][COMM]index:90,weight_param:0xFF
[D][05:19:24][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:24][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:24][COMM]index:95,current_limit:0xFF
[D][05:19:24][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:24][COMM]index:100,location_mode:0xFF

[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:24][PROT]add success [1629955164],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:24][COMM]Main Task receive event:131 finished processing
[D][05:19:24][PROT]index:0 1629955164
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][PROT]is_send:0
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:

2025-07-31 22:38:08:039 ==>> [1]
[D][05:19:24][PROT]sequence_num:7
[D][05:19:24][PROT]retry_timeout:0
[D][05:19:24][PROT]retry_times:1
[D][05:19:24][PROT]send_path:0x2
[D][05:19:24][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:24][PROT]===========================================================
[W][05:19:24][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955164]
[D][05:19:24][PROT]===========================================================
[D][05:19:24][PROT]sending traceid [9999999999900008]
[D][05:19:24][PROT]Send_TO_M2M [1629955164]
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:24][SAL ]sock send credit cnt[6]
[D][05:19:24][SAL ]sock send ind credit cnt[6]
[D][05:19:24][M2M ]m2m send data len[294]
[D][05:19:24][SAL ]Cellular task submsg id[10]
[D][05:19:24][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:24][CAT1]gsm read msg sub id: 15
[D][05:19:24][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:24][CAT1]Send Data To Server[294][294] ... ->:
0093B989113311331133113311331B88B2A9E039F737880A892A1F048106C93774137F906E93184258D518E0CEB28105D250575745CE214D99B8B20C4DAA91D0798F201C73EA4603F

2025-07-31 22:38:08:129 ==>> 77B7889978281652861D01A40EF0B3D9FF1D4AF413CFB4EAF2DAAEFC5DCA097817BB3185BD74C420AD0B3DB55E9D6DFE4265FE097103CEEC09DABB24490C6314728B501773CF982382724
[D][05:19:24][CAT1]<<< 
SEND OK

[D][05:19:24][CAT1]exec over: func id: 15, ret: 11
[D][05:19:24][CAT1]sub id: 15, ret: 11

[D][05:19:24][SAL ]Cellular task submsg id[68]
[D][05:19:24][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:24][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:24][M2M ]g_m2m_is_idle become true
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:24][PROT]M2M Send ok [1629955164]
$GBGGA,143807.000,2301.2569994,N,11421.9437451,E,1,12,0.94,80.191,M,-1.770,M,,*56

[D][05:19:24][COMM]read battery soc:255
$GBGSA,A,3,40,07,39,16,10,25,59,34,60,41,11,12,2.76,0.94,2.59,4*0B

$GBGSV,7,1,27,40,66,171,41,7,63,198,39,3,61,190,40,39,

2025-07-31 22:38:08:174 ==>> 符合定位需求的卫星数量:【22】
2025-07-31 22:38:08:182 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【38】
北斗星号:【10】,信号值:【37】
北斗星号:【25】,信号值:【40】
北斗星号:【59】,信号值:【41】
北斗星号:【2】,信号值:【36】
北斗星号:【9】,信号值:【36】
北斗星号:【1】,信号值:【38】
北斗星号:【34】,信号值:【39】
北斗星号:【60】,信号值:【40】
北斗星号:【41】,信号值:【41】
北斗星号:【11】,信号值:【38】
北斗星号:【12】,信号值:【36】
北斗星号:【24】,信号值:【38】
北斗星号:【44】,信号值:【36】
北斗星号:【33】,信号值:【38】
北斗星号:【43】,信号值:【38】
北斗星号:【23】,信号值:【36】

2025-07-31 22:38:08:205 ==>> 检测【CSQ强度】
2025-07-31 22:38:08:218 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:38:08:245 ==>>                                                                                                                                                                    

2025-07-31 22:38:08:324 ==>>                                                                                                                                                                                                                                                                                                                                                                                                         *48

$GBGSV,7,6,27,44,15,51,36,14,14,321,31,24,14,260,38,38,5,191,30,1*7D

$GBGSV,7,7,27,33,,,38,23,,,36,32,,,34,1*7A

$GBGSV,2,1,05,40,66,171,41,39,60,36,41,25,50,355,40,34,44,104,40,5*4D

$GBGSV,2,2,05,41,41,264,41,5*42

$GBRMC,143808.000,A,2301.2570180,N,11421.9437826,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,143808.000,1.219,0.194,0.190,0.289,1.294,1.192,5.211*7D



2025-07-31 22:38:08:429 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:25][CAT1]gsm read msg sub id: 12
[D][05:19:25][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:25][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:25][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:38:08:558 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 22:38:08:566 ==>> 检测【关闭GSM联网】
2025-07-31 22:38:08:578 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:38:08:792 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:26][COMM]GSM test
[D][05:19:26][COMM]GSM test disable


2025-07-31 22:38:08:840 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:38:08:848 ==>> 检测【4G联网测试】
2025-07-31 22:38:08:862 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:38:08:973 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 22:38:09:914 ==>> [D][05:19:26][COMM]Main Task receive event:14
[D][05:19:26][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955166, allstateRepSeconds = 0
[D][05:19:26][COMM]index:0,power_mode:0xFF
[D][05:19:26][COMM]index:1,sound_mode:0xFF
[D][05:19:26][COMM]index:2,gsensor_mode:0xFF
[D][05:19:26][COMM]index:3,report_freq_mode:0xFF
[D][05:19:26][COMM]index:4,report_period:0xFF
[D][05:19:26][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:26][COMM]index:6,normal_reset_period:0xFF
[D][05:19:26][COMM]index:7,spock_over_speed:0xFF
[D][05:19:26][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:26][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:26][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:26][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:26][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:26][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:26][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:26][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:26][COMM]index:16,imu_config_params:0xFF
[D][05:19:26][COMM]index:17,long_connect_params:0xFF
[D][05:19:26][COMM]index:18,detain_mark:0xFF
[D][05:19:26][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:26][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:26][COMM]index:21,mc_mode:0xFF
[D][05:19:26][COMM]index:2

2025-07-31 22:38:10:019 ==>> 2,S_mode:0xFF
[D][05:19:26][COMM]index:23,overweight:0xFF
[D][05:19:26][COMM]index:24,standstill_mode:0xFF
[D][05:19:26][COMM]index:25,night_mode:0xFF
[D][05:19:26][COMM]index:26,experiment1:0xFF
[D][05:19:26][COMM]index:27,experiment2:0xFF
[D][05:19:26][COMM]index:28,experiment3:0xFF
[D][05:19:26][COMM]index:29,experiment4:0xFF
[D][05:19:26][COMM]index:30,night_mode_start:0xFF
[D][05:19:26][COMM]index:31,night_mode_end:0xFF
[D][05:19:26][COMM]index:33,park_report_minutes:0xFF
[D][05:19:26][COMM]index:34,park_report_mode:0xFF
[D][05:19:26][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:26][COMM]index:38,charge_battery_para: FF
[D][05:19:26][COMM]index:39,multirider_mode:0xFF
[D][05:19:26][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:26][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:26][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:26][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:26][COMM]index:44,riding_duration_config:0xFF
[D][05:19:26][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:26][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:26][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:26][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:

2025-07-31 22:38:10:124 ==>> 19:26][COMM]index:49,mc_load_startup:0xFF
[D][05:19:26][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:26][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:26][COMM]index:52,traffic_mode:0xFF
[D][05:19:26][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:26][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:26][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:26][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:26][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:26][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:26][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:26][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:26][COMM]index:63,experiment5:0xFF
[D][05:19:26][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:26][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:26][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:26][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:26][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:26][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:26][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:26][COMM]index:72,experiment6:0xFF
[D][05:19:26][COMM]index:73,experiment7:0xFF
[D][

2025-07-31 22:38:10:230 ==>> 05:19:26][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:26][COMM]index:75,zero_value_from_server:-1
[D][05:19:26][COMM]index:76,multirider_threshold:255
[D][05:19:26][COMM]index:77,experiment8:255
[D][05:19:26][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:26][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:26][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:26][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:26][COMM]index:83,loc_report_interval:255
[D][05:19:26][COMM]index:84,multirider_threshold_p2:255
[D][05:19:26][COMM]index:85,multirider_strategy:255
[D][05:19:26][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:26][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:26][COMM]index:90,weight_param:0xFF
[D][05:19:26][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:26][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:26][COMM]index:95,current_limit:0xFF
[D][05:19:26][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:26][COMM]index:100,location_mode:0xFF

[D][05:19:26][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:26][PROT]remove success[1629955

2025-07-31 22:38:10:335 ==>> 166],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:26][PROT]add success [1629955166],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:26][PROT]index:0 1629955166
[D][05:19:26][PROT]is_send:0
[D][05:19:26][PROT]sequence_num:8
[D][05:19:26][PROT]retry_timeout:0
[D][05:19:26][PROT]retry_times:1
[D][05:19:26][PROT]send_path:0x2
[D][05:19:26][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:26][PROT]===========================================================
[W][05:19:26][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955166]
[D][05:19:26][PROT]===========================================================
[D][05:19:26][PROT]sending traceid [9999999999900009]
[D][05:19:26][PROT]Send_TO_M2M [1629955166]
[D][05:19:26][CAT1]gsm read msg sub id: 13
[D][05:19:26][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:26][SAL ]sock send credit cnt[6]
[D][05:19:26][SAL ]sock send ind credit cnt[6]
[D][05:19:26][M2M ]m2m send data len[294]
[D][05:19:26][SAL ]Cellular task submsg id[10]
[D][05:19:26][SAL 

2025-07-31 22:38:10:440 ==>> ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:26][COMM]read battery soc:255
[D][05:19:26][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:26][CAT1]exec over: func id: 13, ret: 21
[D][05:19:26][M2M ]get csq[25]
[D][05:19:26][CAT1]gsm read msg sub id: 15
[D][05:19:26][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:26][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88B1010522905F13320AD44B2375578E00C91D1E6F6F21DFC0FEB330DD2B4CA06DF4EECC391656E04E2D745D218EFD9C8B93E9EC87A6624800B8495B647B98F24BC6D5937B8E53C913116BECE1EF7D222D244A48B7160244FF431D51D633A7142C5DF4B03311E7EF2A780265B752D3F0A638EE6D62B8FC550F32802623CB559914C9D01D
[D][05:19:26][CAT1]<<< 
SEND OK

[D][05:19:26][CAT1]exec over: func id: 15, ret: 11
[D][05:19:26][CAT1]sub id: 15, ret: 11

[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,143809.000,2301.2570610,N,11421.9438172,E,1,17,0.79,80.018,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,03,39,16,10,25,59,02,01,34,60,1.81,0.79,1.62,4*0E

$GB

2025-07-31 22:38:10:530 ==>> GSA,A,3,41,11,12,43,44,,,,,,,,1.81,0.79,1.62,4*0A

$GBGSV,7,1,27,40,66,171,41,7,63,198,39,3,61,190,40,39,60,36,40,1*4E

$GBGSV,7,2,27,6,58,6,36,16,58,10,38,10,51,207,37,25,50,355,40,1*7F

$GBGSV,7,3,27,59,49,130,41,2,46,237,35,9,46,333,36,1,45,125,38,1*41

$GBGSV,7,4,27,34,44,104,41,60,42,239,40,41,41,264,41,11,38,137,38,1*7B

$GBGSV,7,5,27,12,31,72,36,4,31,114,33,43,30,170,38,23,24,297,36,1*75

$GBGSV,7,6,27,5,23,257,33,44,15,51,35,14,14,321,31,24,14,260,38,1*7E

$GBGSV,7,7,27,38,5,191,30,33,,,38,32,,,34,1*7A

$GBGSV,2,1,07,40,66,171,41,39,60,36,42,25,50,355,40,34,44,104,40,5*4C

$GBGSV,2,2,07,41,41,264,41,43,30,170,33,44,15,51,33,5*72

$GBRMC,143809.000,A,2301.2570610,N,11421.9438172,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:26][M2M ]g_m2m_is_idle become true
[D]

2025-07-31 22:38:10:635 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:38:10:883 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:38:10:893 ==>> 检测【关闭GPS】
2025-07-31 22:38:10:907 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:38:11:285 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]stop event:8
[D][05:19:28][GNSS]GPS stop. ret=0
[D][05:19:28][GNSS]all continue location stop
[W][05:19:28][GNSS]stop locating
[D][05:19:28][GNSS]all sing location stop
[D][05:19:28][CAT1]gsm read msg sub id: 24
[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:28][COMM]read battery soc:255
[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]exec over: func id: 24, ret: 6
[D][05:19:28][CAT1]sub id: 24, ret: 6



2025-07-31 22:38:11:409 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:38:11:417 ==>> 检测【清空消息队列2】
2025-07-31 22:38:11:438 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:38:11:915 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100002. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100002. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100002. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 02A3 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02C3 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02C4 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02C5 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02E3 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02E4 loss. last_tick:0. cur_tick:100007. period:100

2025-07-31 22:38:12:020 ==>> 00
[D][05:19:28][COMM]msg 02E5 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0302 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0303 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0304 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 02E6 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 02E7 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0305 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0306 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02A8 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A9 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02AA loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100012. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100013. period:10000. j,i:11 64
[D][05:19

2025-07-31 22:38:12:125 ==>> :29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100013. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100014. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100014. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100014. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100015. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100015. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100015
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100016
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100016
[W][05:19:29][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:29][COMM]Protocol queue cleaned by AT_CMD!
[D][05:19:29][GNSS]recv submsg id[1]
[D][05:19:29][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:29][GNSS]location stop evt done evt


2025-07-31 22:38:12:258 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:38:12:271 ==>> 检测【轮动检测】
2025-07-31 22:38:12:296 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:38:12:401 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:38:12:491 ==>> [D][05:19:29][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 22:38:12:764 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:38:12:899 ==>> 3A A3 01 01 A3 


2025-07-31 22:38:13:004 ==>> ON_OUT1
OVER 150


2025-07-31 22:38:13:039 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:38:13:065 ==>> 检测【关闭小电池】
2025-07-31 22:38:13:079 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:38:13:109 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150
[D][05:19:30][COMM]read battery soc:255


2025-07-31 22:38:13:323 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:38:13:331 ==>> 检测【进入休眠模式】
2025-07-31 22:38:13:356 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:38:13:521 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:30][COMM]Main Task receive event:28
[D][05:19:30][COMM]main task tmp_sleep_event = 8
[D][05:19:30][COMM]prepare to sleep
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 22:38:14:294 ==>> [D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 12, ret: 6
[D][05:19:31][M2M ]tcpclient close[4]
[D][05:19:31][SAL ]Cellular task submsg id[12]
[D][05:19:31][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:31][CAT1]gsm read msg sub id: 9
[D][05:19:31][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:31][CAT1]<<< 
OK

[D][05:19:31][CAT1]exec over: func id: 9, ret: 6
[D][05:19:31][CAT1]sub id: 9, ret: 6

[D][05:19:31][SAL ]Cellular task submsg id[68]
[D][05:19:31][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:31][SAL ]socket close ind. id[4]
[D][05:19:31][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:31][COMM]1x1 frm_can_tp_send ok
[D][05:19:31][CAT1]pdpdeact urc len[22]


2025-07-31 22:38:14:599 ==>> [D][05:19:32][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:32][COMM]1x1 rx timeout
[D][05:19:32][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:38:15:087 ==>> [E][05:19:32][COMM]1x1 rx timeout
[E][05:19:32][COMM]1x1 tp timeout
[E][05:19:32][COMM]1x1 error -3.
[W][05:19:32][COMM]CAN STOP!
[D][05:19:32][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:32][COMM]------------ready to Power off Acckey 1------------
[D][05:19:32][COMM]------------ready to Power off Acckey 2------------
[D][05:19:32][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:32][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1290
[D][05:19:32][COMM]bat sleep fail, reason:-1
[D][05:19:32][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:32][COMM]accel parse set 0
[D][05:19:32][COMM]imu rest ok. 103470
[D][05:19:32][COMM]imu sleep 0
[W][05:19:32][COMM]now sleep


2025-07-31 22:38:15:150 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:38:15:164 ==>> 检测【检测33V休眠电流】
2025-07-31 22:38:15:195 ==>> 开始33V电流采样
2025-07-31 22:38:15:224 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:15:252 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:38:16:259 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:38:16:334 ==>> Current33V:????:1845.81

2025-07-31 22:38:16:761 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:16:866 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:38:17:869 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:38:17:899 ==>> Current33V:????:14.75

2025-07-31 22:38:18:379 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:18:396 ==>> 【检测33V休眠电流】通过,【14.75uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:38:18:403 ==>> 该项需要延时执行
2025-07-31 22:38:20:382 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:38:20:415 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:38:20:445 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:38:20:521 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2730mV
Get AD_V6 1880mV
Get AD_V7 1089mV
OVER 150


2025-07-31 22:38:21:405 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:38:21:422 ==>> 检测【打开小电池2】
2025-07-31 22:38:21:451 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:38:21:505 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:38:21:682 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:38:21:691 ==>> 该项需要延时执行
2025-07-31 22:38:22:186 ==>> 此处延时了:【500】毫秒
2025-07-31 22:38:22:199 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:38:22:222 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:38:22:310 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:38:22:489 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:38:22:497 ==>> 该项需要延时执行
2025-07-31 22:38:22:991 ==>> 此处延时了:【500】毫秒
2025-07-31 22:38:23:006 ==>> 检测【进入休眠模式2】
2025-07-31 22:38:23:027 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:38:23:056 ==>> [D][05:19:40][COMM]------------ready to Power on Acckey 1------------
[D][05:19:40][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:40][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:40][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:40][COMM]----- get Acckey 1 and value:1------------
[W][05:19:40][COMM]CAN START!
[D][05:19:40][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:40][COMM]1x1 frm_can_tp_send ok
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:40][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 111292
[

2025-07-31 22:38:23:087 ==>> D][05:19:40][COMM][Audio]exec status ready.
[D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 12, ret: 6
[D][05:19:40][COMM]imu wakeup ok. 111306
[D][05:19:40][COMM]imu wakeup 1
[W][05:19:40][COMM]wake up system, wakeupEvt=0x80
[D][05:19:40][COMM]frm_can_weigth_power_set 1
[D][05:19:40][COMM]Clear Sleep Block Evt
[D][05:19:40][COMM]Main Task receive event:28 finished processing


2025-07-31 22:38:23:382 ==>> [W][05:19:40][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:40][COMM]1x1 rx timeout
[D][05:19:40][COMM]1x1 frm_can_tp_send ok
[D][05:19:40][COMM]Main Task receive event:28
[D][05:19:40][COMM]prepare to sleep
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 12, ret: 6
[W][05:19:40][COMM]CAN STOP!
[D][05:19:40][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:40][COMM]------------ready to Power off Acckey 1------------
[D][05:19:40][COMM]------------ready to Power off Acckey 2------------
[D][05:19:40][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:40][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 128
[D][05:19:40][COMM]bat sleep fail, reason:-1
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:40][COMM]accel parse set 0
[D][05:19:40][COMM]imu rest ok. 111752
[D][05:19:40][COMM]imu sleep 0
[W][05:19:40][COMM]now sleep


2025-07-31 22:38:23:533 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:38:23:542 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:38:23:566 ==>> 开始小电池电流采样
2025-07-31 22:38:23:582 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:23:642 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:38:24:652 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:38:24:731 ==>> CurrentBattery:ƽ��:68.83

2025-07-31 22:38:25:164 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:25:172 ==>> 【检测小电池休眠电流】通过,【68.83uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:38:25:197 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:38:25:204 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:38:25:301 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:38:25:454 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:38:25:473 ==>> 该项需要延时执行
2025-07-31 22:38:25:526 ==>> [D][05:19:42][COMM]------------ready to Power on Acckey 1------------
[D][05:19:42][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:42][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:42][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:42][COMM]----- get Acckey 1 and value:1------------
[W][05:19:42][COMM]CAN START!
[E][05:19:42][COMM]1x1 rx timeout
[E][05:19:42][COMM]1x1 tp timeout
[E][05:19:42][COMM]1x1 error -3.
[D][05:19:42][COMM]read battery soc:0
[D][05:19:42][CAT1]gsm read msg sub id: 12
[D][05:19:42][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:42][COMM][Audio]exec status ready.
[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 12, ret: 6
[D][05:19:42][COMM]imu wakeup ok. 113888
[D][05:19:42][COMM]imu wakeup 1
[W][05:19:42][COMM]wake up system, wakeupEvt=0x80
[D][05:19:42][COMM]frm_can_weigth_power_set 1
[D][05:19:42][COMM]Clear Sleep Block Evt
[D][05:19:42][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:38:25:616 ==>>                                                   :0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 22:38:25:812 ==>> [E][05:19:43][COMM]1x1 rx timeout
[D][05:19:43][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:38:25:917 ==>> [D][05:19:43][COMM]msg 02A0 loss. last_tick:113856. cur_tick:114367. period:50
[D][05:19:43][COMM]msg 02A4 loss. last_tick:113856. cur_tick:114367. period:50
[D][05:19:43][COMM]msg 02A5 

2025-07-31 22:38:25:962 ==>> 此处延时了:【500】毫秒
2025-07-31 22:38:25:975 ==>> 检测【检测唤醒】
2025-07-31 22:38:26:002 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:38:26:027 ==>> loss. last_tick:113856. cur_tick:114368. period:50
[D][05:19:43][COMM]msg 02A6 loss. last_tick:113856. cur_tick:114368. period:50
[D][05:19:43][COMM]msg 02A7 loss. last_tick:113856. cur_tick:114368. period:50
[D][05:19:43][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 114369
[D][05:19:43][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 114369


2025-07-31 22:38:26:389 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:43][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:43][FCTY]==========Modules-nRF5340 ==========
[D][05:19:43][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:43][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:43][FCTY]DeviceID    = 460130071541609
[D][05:19:43][FCTY]HardwareID  = 867222088007283
[D][05:19:43][FCTY]MoBikeID    = 9999999999
[D][05:19:43][FCTY]LockID      = FFFFFFFFFF
[D][05:19:43][FCTY]BLEFWVersion= 105
[D][05:19:43][FCTY]BLEMacAddr   = CFCA8FA60A0E
[D][05:19:43][FCTY]Bat         = 3884 mv
[D][05:19:43][FCTY]Current     = 0 ma
[D][05:19:43][FCTY]VBUS        = 2600 mv
[D][05:19:43][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:43][FCTY]Ext battery vol = 32, adc = 1295
[D][05:19:43][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 50 mv
[D][05:19:43][FCTY]Bike Type flag is invalied
[D][05:19:43][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:43][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:43][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:43][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:43][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:43][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05

2025-07-31 22:38:26:449 ==>> :19:43][FCTY]Bat1         = 3818 mv
[D][05:19:43][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:43][FCTY]==========Modules-nRF5340 ==========
[E][05:19:43][COMM]1x1 rx timeout
[E][05:19:43][COMM]1x1 tp timeout
[D][05:19:43][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[E][05:19:43][COMM]1x1 error -3.
[D][05:19:43][COMM]Main Task receive event:28 finished processing


2025-07-31 22:38:26:550 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:38:26:560 ==>> 检测【关机】
2025-07-31 22:38:26:574 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:38:26:607 ==>>                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 22:38:26:659 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              024E loss. last_tick:113856. cur_tick:114877. period:100. j,i:15 68
[D][05:19:43][COMM]bat msg 024F loss. last_tick:113856. cur_tick:114877

2025-07-31 22:38:26:704 ==>> . period:100. j,i:16 69
[D][05:19:43][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 114878
[D][05:19:43][COMM]CAN message bat fault change: 0x00000000->0x0001802E 114878
[D][05:19:43][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 114879


2025-07-31 22:38:26:794 ==>> [W][05:19:44][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 22:38:26:899 ==>> [D][05:19:44][COMM]arm_hub_enable: hub power: 0
[D][05:19:44][HSDK]hexlog index save 0 3584 30 @ 0 : 0
[D][05:19:44][HSDK]write save hexlog index [0]
[D][05:19:44][FCTY]F:[syncParaFromRamToFlash].L:[962] ready t

2025-07-31 22:38:26:930 ==>> o read para flash
[D][05:19:44][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:38:27:004 ==>>                                                                                                                                             2213->0x0000E00C71E22217 115372


2025-07-31 22:38:27:109 ==>> [D][05:19:44][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:44][COMM]frm_periphe

2025-07-31 22:38:27:154 ==>> ral_device_poweron type 0.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:0------------
[D][05:19:44][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:38:27:584 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:38:27:971 ==>>                                                                                                              e:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1------------
[D][05:19:44][COMM]more than the number of battery plugs
[D][05:19:44][COMM]VBUS is 1
[D][05:19:44][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:44][COMM]Main Task receive event:65
[D][05:19:44][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:44][COMM]Main Task receive event:65 finished processing
[D][05:19:44][COMM]Main Task receive event:66
[D][05:19:44][COMM]Try to Auto Lock Bat
[D][05:19:44][COMM]file:B50 exist
[D][05:19:44][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:44][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:44][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:44][COMM]Bat auth off fail, error:-1
[D][05:19:44][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1------------
[D][05:19:44][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1-----

2025-07-31 22:38:28:076 ==>> -------
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:44][COMM]file:B50 exist
[D][05:19:44][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:44][COMM]read file, len:10800, num:3
[D][05:19:44][COMM]Main Task receive event:66 finished processing
[D][05:19:44][COMM]Main Task receive event:60
[D][05:19:44][COMM]smart_helmet_vol=255,255
[D][05:19:44][COMM]BAT CAN get state1 Fail 204
[D][05:19:44][COMM]BAT CAN get soc Fail, 204
[D][05:19:44][COMM]BAT CAN get state2 fail 204
[D][05:19:44][COMM]get soh error
[E][05:19:44][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:44][COMM]report elecbike
[W][05:19:44][PROT]remove success[1629955184],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:44][PROT]add success [1629955184],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:44][COMM]Main Task receive event:60 finished processing
[D][05:19:44][COMM]Main Task receive event:61
[D][05:19:44][COMM][D301]:type:3, trace id:280
[D][05:19:44][COMM]id[], hw[000
[D][05:19:44][COMM]get mcMaincircuitVolt error
[D][05:19:44][COMM

2025-07-31 22:38:28:181 ==>> ]get mcSubcircuitVolt error
[D][05:19:44][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:44][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][PROT]index:0
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][PROT]is_send:1
[D][05:19:44][PROT]sequence_num:10
[D][05:19:44][PROT]retry_timeout:0
[D][05:19:44][PROT]retry_times:3
[D][05:19:44][PROT]send_path:0x3
[D][05:19:44][PROT]msg_type:0x5d03
[D][05:19:44][PROT]===========================================================
[W][05:19:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955184]
[D][05:19:44][PROT]===========================================================
[D][05:19:44][PROT]Sending traceid[999999999990000B]
[D][05:19:44][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:44][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:44][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:44][COMM]Receive Bat Lock cmd 0
[D][05:19:44][COMM]VBUS is 1
[D][05:19:44][COMM]BAT CAN get state1 Fail 204
[D][05:19:44][COMM]BAT CAN get soc Fail, 204
[D][05:19:44]

2025-07-31 22:38:28:286 ==>> [COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:44][COMM]BAT CAN get state2 fail 204
[D][05:19:44][COMM]get bat work mode err
[W][05:19:44][PROT]remove success[1629955184],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:44][SAL ]open socket ind id[4], rst[0]
[D][05:19:44][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:44][SAL ]Cellular task submsg id[8]
[D][05:19:44][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:44][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:19:44][PROT]add success [1629955184],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:44][COMM]Main Task receive event:61 finished processing
[D][05:19:44][CAT1]gsm read msg sub id: 8
[D][05:19:44][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:44][M2M ]m2m switch

2025-07-31 22:38:28:391 ==>>  to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:44][COMM]--->crc16:0xb8a
[D][05:19:44][COMM]read file success
[D][05:19:44][COMM]accel parse set 1
[D][05:19:44][COMM][Audio]mon:9,05:19:44
[D][05:19:44][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:44][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:44][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:44][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:44][CAT1]<<< 
+CME ERROR: 100

[D][05:19:44][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd s

2025-07-31 22:38:28:496 ==>> end:AT+AUDIODATAHEX=0,10800,0

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:44][COMM]read battery soc:255
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:44][COMM]Power Off
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:45][COM

2025-07-31 22:38:28:586 ==>> M]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:45][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:45][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:45][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:45][COMM]arm_hub_enable: hub power: 0
[D][05:19:45][HSDK]hexlog index save 0 3584 30 @ 0 : 0
[D][05:19:45][HSDK]write save hexlog index [0]
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
                              

2025-07-31 22:38:28:624 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 22:38:28:632 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 22:38:28:664 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:38:28:691 ==>> 5A A5 02 5A A5 


2025-07-31 22:38:28:796 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:38:28:901 ==>> [D][05:19:46][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = @ <
 <     

2025-07-31 22:38:28:922 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:38:28:931 ==>> 检测【检测小电池关机电流】
2025-07-31 22:38:28:963 ==>> 开始小电池电流采样
2025-07-31 22:38:28:991 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:29:036 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:38:30:050 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:38:30:080 ==>> CurrentBattery:ƽ��:70.34

2025-07-31 22:38:30:559 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:38:30:580 ==>> 【检测小电池关机电流】通过,【70.34uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 22:38:31:091 ==>> MES过站成功
2025-07-31 22:38:31:100 ==>> #################### 【测试结束】 ####################
2025-07-31 22:38:31:118 ==>> 关闭5V供电
2025-07-31 22:38:31:131 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:38:31:201 ==>> 5A A5 04 5A A5 


2025-07-31 22:38:31:306 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:38:32:120 ==>> 关闭5V供电成功
2025-07-31 22:38:32:133 ==>> 关闭33V供电
2025-07-31 22:38:32:161 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:38:32:211 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:38:33:129 ==>> 关闭33V供电成功
2025-07-31 22:38:33:137 ==>> 关闭3.7V供电
2025-07-31 22:38:33:146 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:38:33:206 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:38:34:033 ==>>  

2025-07-31 22:38:34:138 ==>> 关闭3.7V供电成功
