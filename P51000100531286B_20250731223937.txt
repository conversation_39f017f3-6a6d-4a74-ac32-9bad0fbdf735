2025-07-31 22:39:37:494 ==>> MES查站成功:
查站序号:P51000100531286B验证通过
2025-07-31 22:39:37:498 ==>> 扫码结果:P51000100531286B
2025-07-31 22:39:37:500 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:39:37:502 ==>> 测试参数版本:2024.10.11
2025-07-31 22:39:37:504 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:39:37:506 ==>> 检测【打开透传】
2025-07-31 22:39:37:507 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:39:37:610 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:39:37:847 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:39:37:855 ==>> 检测【检测接地电压】
2025-07-31 22:39:37:857 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:39:37:900 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:39:38:133 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:39:38:135 ==>> 检测【打开小电池】
2025-07-31 22:39:38:138 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:39:38:202 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:39:38:416 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:39:38:418 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:39:38:420 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:39:38:507 ==>> 1A A1 00 00 01 
Get AD_V0 1291mV
OVER 150


2025-07-31 22:39:38:732 ==>> 【检测小电池分压(AD_VBAT)】通过,【1291mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:39:38:735 ==>> 检测【等待设备启动】
2025-07-31 22:39:38:737 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:39:778 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:40:824 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:41:869 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:42:903 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:43:938 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:44:973 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:46:017 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:47:056 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:48:085 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:49:127 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:39:49:132 ==>> #################### 【测试结束】 ####################
2025-07-31 22:39:49:151 ==>> 关闭5V供电
2025-07-31 22:39:49:153 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:39:49:204 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:39:50:151 ==>> 关闭5V供电成功
2025-07-31 22:39:50:154 ==>> 关闭33V供电
2025-07-31 22:39:50:157 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:39:50:211 ==>> 5A A5 02 5A A5 


2025-07-31 22:39:50:301 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:39:50:924 ==>>  

2025-07-31 22:39:51:153 ==>> 关闭33V供电成功
2025-07-31 22:39:51:158 ==>> 关闭3.7V供电
2025-07-31 22:39:51:160 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:39:51:213 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


