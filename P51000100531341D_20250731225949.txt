2025-07-31 22:59:49:707 ==>> MES查站成功:
查站序号:P51000100531341D验证通过
2025-07-31 22:59:49:721 ==>> 扫码结果:P51000100531341D
2025-07-31 22:59:49:722 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:59:49:725 ==>> 测试参数版本:2024.10.11
2025-07-31 22:59:49:726 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:59:49:728 ==>> 检测【打开透传】
2025-07-31 22:59:49:730 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:59:49:810 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:59:50:061 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:59:50:071 ==>> 检测【检测接地电压】
2025-07-31 22:59:50:073 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:59:50:221 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 22:59:50:348 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:59:50:350 ==>> 检测【打开小电池】
2025-07-31 22:59:50:353 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:59:50:417 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:59:50:618 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:59:50:620 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:59:50:623 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:59:50:723 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:59:50:893 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:59:50:896 ==>> 检测【等待设备启动】
2025-07-31 22:59:50:899 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:51:169 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:51:350 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:51:930 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:51:945 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:52:141 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:52:648 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:52:832 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 22:59:52:968 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:53:352 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:53:533 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:54:008 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:54:053 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:54:234 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:54:749 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:54:945 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:55:034 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:55:449 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:55:644 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:56:067 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:56:158 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:56:339 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 22:59:56:855 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:57:034 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 22:59:57:095 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:57:552 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:57:719 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 22:59:58:130 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:58:249 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:58:415 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 22:59:58:947 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:59:115 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 22:59:59:160 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:59:59:632 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:59:59:812 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 23:00:00:190 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:00:01:228 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 23:00:01:231 ==>> #################### 【测试结束】 ####################
2025-07-31 23:00:01:251 ==>> 关闭5V供电
2025-07-31 23:00:01:254 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:00:01:318 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:00:02:252 ==>> 关闭5V供电成功
2025-07-31 23:00:02:255 ==>> 关闭33V供电
2025-07-31 23:00:02:259 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:00:02:312 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:00:03:266 ==>> 关闭33V供电成功
2025-07-31 23:00:03:269 ==>> 关闭3.7V供电
2025-07-31 23:00:03:271 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:00:03:311 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:00:03:416 ==>>  

