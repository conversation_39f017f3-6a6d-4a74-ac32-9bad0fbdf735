2025-07-31 20:00:31:472 ==>> MES查站成功:
查站序号:P5100010053134DB验证通过
2025-07-31 20:00:31:481 ==>> 扫码结果:P5100010053134DB
2025-07-31 20:00:31:483 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:00:31:484 ==>> 测试参数版本:2024.10.11
2025-07-31 20:00:31:485 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:00:31:488 ==>> 检测【打开透传】
2025-07-31 20:00:31:490 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:00:31:534 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:00:31:758 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:00:31:762 ==>> 检测【检测接地电压】
2025-07-31 20:00:31:765 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:00:31:843 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:00:32:044 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:00:32:047 ==>> 检测【打开小电池】
2025-07-31 20:00:32:051 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:00:32:131 ==>> 6A A6 01 A6 6A 


2025-07-31 20:00:32:236 ==>> Battery ON
OVER 150


2025-07-31 20:00:32:315 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:00:32:319 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:00:32:322 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:00:32:431 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:00:32:585 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:00:32:588 ==>> 检测【等待设备启动】
2025-07-31 20:00:32:590 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:00:32:971 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:00:33:166 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:00:33:607 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:00:33:808 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:00:33:898 ==>> [W][05:17:49][GNSS]start sing locating


2025-07-31 20:00:34:299 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:00:34:646 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:00:34:767 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:00:34:923 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:00:34:925 ==>> 检测【产品通信】
2025-07-31 20:00:34:928 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:00:35:101 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:00:35:195 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:00:35:198 ==>> 检测【初始化完成检测】
2025-07-31 20:00:35:201 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:00:35:463 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:00:35:722 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:00:35:725 ==>> 检测【关闭大灯控制1】
2025-07-31 20:00:35:727 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:00:35:886 ==>> [D][05:17:51][COMM]2636 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:00:35:961 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:00:35:995 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:00:35:998 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:00:35:999 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:00:36:235 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:00:36:269 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:00:36:271 ==>> 检测【关闭仪表供电】
2025-07-31 20:00:36:274 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:00:36:430 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:00:36:540 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:00:36:542 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:00:36:543 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:00:36:706 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:00:36:796 ==>> [D][05:17:52][COMM]3647 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:00:36:840 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:00:36:843 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:00:36:846 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:00:37:006 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:00:37:143 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:00:37:145 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:00:37:148 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:00:37:327 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:00:37:443 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:00:37:445 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:00:37:447 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:00:37:537 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:00:37:597 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0


2025-07-31 20:00:37:672 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:00:37:749 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:00:37:753 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:00:37:756 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:00:37:837 ==>> [D][05:17:53][COMM]4658 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:00:38:040 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:00:38:043 ==>> 该项需要延时执行
2025-07-31 20:00:38:338 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5004. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5005. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5007. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5007. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5007. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5008. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN mess

2025-07-31 20:00:38:368 ==>> age bat fault change: 0x0001802E->0x01B987FE 5009


2025-07-31 20:00:38:824 ==>> [D][05:17:54][COMM]5669 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:00:39:205 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:00:39:693 ==>>                                            weron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and 

2025-07-31 20:00:39:799 ==>> value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_ta

2025-07-31 20:00:39:903 ==>> sk: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN

2025-07-31 20:00:39:963 ==>>  get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 20:00:40:008 ==>>                                                                                                                                                                                   

2025-07-31 20:00:40:859 ==>> [D][05:17:56][COMM]7692 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:00:41:697 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:00:41:863 ==>> [D][05:17:57][COMM]8703 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:00:42:046 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:00:42:049 ==>> 检测【33V输入电压ADC】
2025-07-31 20:00:42:052 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:00:42:340 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:17:58][COMM]adc read out 24v adc:1328  volt:33589 mv
[D][05:17:58][COMM]adc read left brake adc:6  volt:7 mv
[D][05:17:58][COMM]adc read right brake adc:5  volt:6 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:9 mv
[D][05:17:58][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2427  volt:3910 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 20:00:42:592 ==>> 【33V输入电压ADC】通过,【32830mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:00:42:595 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:00:42:598 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:00:42:741 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1643mV
Get AD_V4 1mV
Get AD_V5 2755mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:00:42:876 ==>> [D][05:17:58][COMM]9714 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:00:42:892 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:42:894 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:00:42:913 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:42:915 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:00:42:916 ==>> 原始值:【2755】, 乘以分压基数【2】还原值:【5510】
2025-07-31 20:00:42:933 ==>> 【TP68_VCC5V5(ADV5)】通过,【5510mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:00:42:935 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:00:42:952 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:00:42:955 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:00:42:977 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:00:42:979 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:00:43:041 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2756mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:00:43:221 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10019
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10019


2025-07-31 20:00:43:263 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:43:267 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:00:43:288 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:43:291 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:00:43:294 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 20:00:43:307 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:00:43:310 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:00:43:330 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:00:43:333 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:00:43:358 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:00:43:360 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:00:43:449 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1644mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:00:43:554 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:00:43:638 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:43:640 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:00:43:665 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:43:669 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:00:43:672 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 20:00:43:688 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:00:43:691 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:00:43:705 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:00:43:711 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:00:43:713 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:00:43:742 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:00:43:744 ==>> 检测【打开WIFI(1)】
2025-07-31 20:00:43:745 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:00:44:071 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10725 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time er

2025-07-31 20:00:44:116 ==>> r 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 20:00:44:269 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:00:44:272 ==>> 检测【清空消息队列(1)】
2025-07-31 20:00:44:275 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:00:44:560 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087723302

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539175

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:00:44:810 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:00:44:812 ==>> 检测【打开GPS(1)】
2025-07-31 20:00:44:814 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:00:44:885 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:00:45:035 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:00:45:084 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:00:45:087 ==>> 检测【打开GSM联网】
2025-07-31 20:00:45:089 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:00:45:231 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:00:45:355 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:00:45:358 ==>> 检测【打开仪表供电1】
2025-07-31 20:00:45:360 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:00:45:537 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:00:45:631 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:00:45:634 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:00:45:636 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:00:45:720 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:00:45:825 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:00:45:904 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:00:45:907 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:00:45:909 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:00:46:117 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33549]


2025-07-31 20:00:46:181 ==>> 【读取主控ADC采集的仪表电压】通过,【33549mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:00:46:183 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:00:46:185 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:00:46:328 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:00:46:452 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:00:46:455 ==>> 检测【AD_V20电压】
2025-07-31 20:00:46:457 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:00:46:559 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:00:46:634 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:00:46:908 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][COMM]13738 imu init OK
[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:00:46:954 ==>> 本次取值间隔时间:392ms
2025-07-31 20:00:46:972 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:00:47:077 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:00:47:137 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:00:47:570 ==>> 本次取值间隔时间:478ms
2025-07-31 20:00:47:588 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:00:47:616 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 20:00:47:691 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:00:47:874 ==>>                                                                                                                                                                                                             mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F3838353434373507

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:03]

2025-07-31 20:00:47:919 ==>> [CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.119.155.160"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:00:48:071 ==>> 本次取值间隔时间:374ms
2025-07-31 20:00:48:162 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         FC0

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx

2025-07-31 20:00:48:191 ==>>  ret[13] >>> AT+GPSPWR=1



2025-07-31 20:00:48:499 ==>> 本次取值间隔时间:418ms
2025-07-31 20:00:48:746 ==>> [D][05:18:04][COMM]S->M yaw:INVALID
[D][05:18:04][COMM]IMU: [41,3,-1068] ret=70 AWAKE!


2025-07-31 20:00:48:750 ==>> 本次取值间隔时间:237ms
2025-07-31 20:00:48:896 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:04][COMM]IMU: [0,0,-976] ret=31 AWAKE!


2025-07-31 20:00:49:032 ==>> 本次取值间隔时间:275ms
2025-07-31 20:00:49:037 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:00:49:138 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:00:49:243 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1635mV
OVER 150


2025-07-31 20:00:49:528 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:00:49:558 ==>> 本次取值间隔时间:419ms
2025-07-31 20:00:49:576 ==>> 【AD_V20电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:49:579 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:00:49:582 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:00:49:633 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150
[D][05:18:05][CAT1]<<< 


2025-07-31 20:00:49:738 ==>> OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,42,60,,,42,59,,,40,42,,,39,1*70

$GBGSV,3,2,11,38,,,38,39,,,38,41,,,36,33,,,34,1*71

$GBGSV,3,3,11,25,,,19,14,,,41,13,,,40,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1511.115,1511.115,48.526,2097152,2097152,2097152*42

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

                                     

2025-07-31 20:00:49:798 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:00:49:851 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:00:49:854 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:00:49:857 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:00:49:903 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:00:50:054 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:00:50:133 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:00:50:136 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:00:50:138 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:00:50:235 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:00:50:409 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:00:50:412 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:00:50:419 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:00:50:687 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,24,,,42,60,,,41,59,,,41,42,,,40,1*79

$GBGSV,4,2,13,25,,,40,13,,,39,39,,,39,38,,,38,1*79

$GBGSV,4,3,13,3,,,38,14,,,37,16,,,37,33,,,37,1*4D

$GBGSV,4,4,13,41,,,36,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1610.437,1610.437,51.460,2097152,2097152,2097152*49

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:00:50:953 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:00:50:956 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:00:50:958 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:00:51:135 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:00:51:229 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:00:51:232 ==>> 检测【AD_V21电压】
2025-07-31 20:00:51:234 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:00:51:330 ==>> 1A A1 20 00 00 
Get AD_V21 1631mV
OVER 150


2025-07-31 20:00:51:451 ==>> 本次取值间隔时间:212ms
2025-07-31 20:00:51:470 ==>> 【AD_V21电压】通过,【1631mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:00:51:482 ==>> 检测【关闭仪表供电2】
2025-07-31 20:00:51:487 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:00:51:710 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,41,60,,,40,59,,,40,42,,,40,1*70

$GBGSV,5,2,18,25,,,40,3,,,39,33,,,39,13,,,38,1*41

$GBGSV,5,3,18,39,,,38,38,,,38,14,,,38,16,,,37,1*75

$GBGSV,5,4,18,1,,,37,40,,,37,41,,,36,5,,,35,1*78

$GBGSV,5,5,18,2,,,35,4,,,32,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1566.167,1566.167,50.068,2097152,2097152,2097152*44

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:00:51:744 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:00:51:747 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:00:51:749 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:00:51:800 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:00:51:905 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][

2025-07-31 20:00:51:935 ==>> COMM][oneline_display]: command mode, OFF!


2025-07-31 20:00:52:013 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:00:52:018 ==>> 检测【打开AccKey2供电】
2025-07-31 20:00:52:021 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:00:52:195 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:00:52:289 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:00:52:293 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:00:52:297 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:00:52:743 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:08][COMM]adc read out 24v adc:1323  volt:33462 mv
[D][05:18:08][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:08][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:08][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:08][COMM]adc read battery ts volt:9 mv
[D][05:18:08][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2428  volt:3912 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,120056.483,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,41,59,,,41,33,,,41,60,,,40,1*76

$GBGSV,5,2,18,42,,,40,25,,,40,3,,,39,13,,,38,1*49

$GBGSV,5,3,18,39,,,38,38,,,38,14,,,38,1,,,38,1*4C

$GBGSV,5,4,18,16,,,37,40,,,37,41,,,36,2,,,36,1*4A

$GBGSV,5,5,18,5,,,34,4,,,33,1*79

$GBRMC,120056.483,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120056.483,0.000,1577.685,1577

2025-07-31 20:00:52:773 ==>> .685,50.437,2097152,2097152,2097152*5B



2025-07-31 20:00:52:821 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33462mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:00:52:825 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:00:52:827 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:00:52:999 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:00:53:098 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:00:53:102 ==>> 该项需要延时执行
2025-07-31 20:00:53:676 ==>> $GBGGA,120057.463,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,41,59,,,41,33,,,41,60,,,40,1*7F

$GBGSV,6,2,21,42,,,40,25,,,40,3,,,39,14,,,39,1*46

$GBGSV,6,3,21,13,,,38,39,,,38,38,,,38,1,,,38,1*42

$GBGSV,6,4,21,16,,,37,40,,,37,41,,,36,2,,,36,1*43

$GBGSV,6,5,21,6,,,35,5,,,34,8,,,34,4,,,33,1*7F

$GBGSV,6,6,21,9,,,31,1*4E

$GBRMC,120057.463,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120057.463,0.000,1551.718,1551.718,49.634,2097152,2097152,2097152*5D



2025-07-31 20:00:53:811 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:00:54:661 ==>> $GBGGA,120058.443,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,24,,,41,59,,,41,60,,,40,1*7F

$GBGSV,6,2,22,42,,,40,25,,,40,3,,,40,14,,,39,1*4B

$GBGSV,6,3,22,39,,,39,13,,,38,38,,,38,1,,,38,1*40

$GBGSV,6,4,22,16,,,37,40,,,37,41,,,37,2,,,36,1*41

$GBGSV,6,5,22,6,,,35,8,,,35,5,,,34,4,,,33,1*7D

$GBGSV,6,6,22,9,,,33,10,,,28,1*44

$GBRMC,120058.443,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120058.443,0.000,1547.167,1547.167,49.513,2097152,2097152,2097152*56



2025-07-31 20:00:55:636 ==>> $GBGGA,120059.423,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,24,,,41,42,,,41,59,,,40,1*7F

$GBGSV,6,2,22,60,,,40,25,,,40,3,,,40,14,,,39,1*4B

$GBGSV,6,3,22,39,,,39,13,,,39,38,,,38,1,,,38,1*41

$GBGSV,6,4,22,16,,,37,40,,,37,41,,,37,2,,,37,1*40

$GBGSV,6,5,22,6,,,36,8,,,35,5,,,34,9,,,34,1*74

$GBGSV,6,6,22,4,,,33,10,,,29,1*48

$GBRMC,120059.423,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120059.423,0.000,1556.578,1556.578,49.803,2097152,2097152,2097152*5D



2025-07-31 20:00:55:816 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:00:56:109 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:00:56:114 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:00:56:117 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:00:56:448 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3122  volt:5487 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:12][COMM]adc read battery ts volt:13 mv
[D][05:18:12][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:12][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:00:56:553 ==>> $GBGGA,120100.403,,,,,0,00

2025-07-31 20:00:56:613 ==>> ,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,24,,,41,42,,,41,59,,,41,1*7F

$GBGSV,6,2,23,60,,,41,25,,,40,3,,,40,14,,,39,1*4B

$GBGSV,6,3,23,39,,,39,13,,,39,1,,,39,38,,,38,1*41

$GBGSV,6,4,23,16,,,37,40,,,37,41,,,37,2,,,36,1*40

$GBGSV,6,5,23,6,,,36,8,,,36,9,,,35,5,,,34,1*77

$GBGSV,6,6,23,4,,,34,10,,,30,7,,,36,1*74

$GBRMC,120100.403,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120100.403,0.000,1567.876,1567.876,50.155,2097152,2097152,2097152*50



2025-07-31 20:00:56:670 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:00:56:673 ==>> 检测【打开AccKey1供电】
2025-07-31 20:00:56:675 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:00:56:808 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:00:56:970 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:00:56:974 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:00:56:980 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:00:57:033 ==>> 1A A1 00 40 00 
Get AD_V14 2573mV
OVER 150


2025-07-31 20:00:57:228 ==>> 原始值:【2573】, 乘以分压基数【2】还原值:【5146】
2025-07-31 20:00:57:273 ==>> 【读取AccKey1电压(ADV14)前】通过,【5146mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:00:57:277 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:00:57:280 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:00:57:653 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:13][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:10 mv
[D][05:18:13][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:13][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,120101.383,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,24,,,41,42,,,41,59,,,41,1*7E

$GBGSV,6,2,22,60,,,40,25,,,40,3,,,40,14,,,40,1*45

$GBGSV,6,3,22,39,,,39,13,,,39,1,,,38,38,,,38,1*41

$GBGSV,6,4,22,16,,,37,40,,,37,41,,,36,2,,,36,1*40

$GBGSV,6,5,22,6,,,36,8,,,36,9,,,35,5,,,34,1*7

2025-07-31 20:00:57:698 ==>> 6

$GBGSV,6,6,22,4,,,34,10,,,30,1*47

$GBRMC,120101.383,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120101.383,0.000,780.591,780.591,713.867,2097152,2097152,2097152*66



2025-07-31 20:00:57:803 ==>>                                                                                                                                                                                                                                                                                                                                     5,,,34,1*76

$GBGSV,6,6,22,4,,,34,10,,,31,1*46

$GBRMC,120101.583,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120101.583,0.000,782.465,782.465,715.580,2097152,2097152,2097152*62



2025-07-31 20:00:57:828 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:00:57:831 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:00:57:834 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:00:57:839 ==>>                                          

2025-07-31 20:00:57:999 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:00:58:115 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:00:58:121 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:00:58:125 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:00:58:230 ==>> 1A A1 00 40 00 
Get AD_V14 2571mV
OVER 150


2025-07-31 20:00:58:380 ==>> 原始值:【2571】, 乘以分压基数【2】还原值:【5142】
2025-07-31 20:00:58:399 ==>> 【读取AccKey1电压(ADV14)后】通过,【5142mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:00:58:402 ==>> 检测【打开WIFI(2)】
2025-07-31 20:00:58:406 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:00:58:656 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:00:58:761 ==>>                                                                                                                           1*7F

$GBGSV,6,2,23,60,,,40,3,,,40,14,,,40,25,,,40,1*44

$GBGSV,6,3,23,13,,,39,1,,,39,39,,,39,38,,,38,1*41

$GBGSV,6,4,23,2,,,37,40,,,37,16,,,37,41,,,37,1*41

$GBGSV,6,5,23,8,,,36,6,,,36,9,,,35,26,,,34,1*46

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,31,1*75

$GBRMC,120102.563,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120102.563,0.000,780.832,780.832,714.087,2097152,2097152,2097152*6C



2025-07-31 20:00:58:930 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:00:58:933 ==>> 检测【转刹把供电】
2025-07-31 20:00:58:936 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:00:59:125 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:00:59:204 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:00:59:213 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:00:59:217 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:00:59:307 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:00:59:352 ==>> +WIFISCAN:4,0,CC057790A5C0,-72
+WIFISCAN:4,1,CC057790A5C1,-75
+WIFISCAN:4,2,CC057790A6E1,-75
+WIFISCAN:4,3,CC057790A7C1,-81

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 20:00:59:532 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:00:59:759 ==>> $GBGGA,120103.543,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,59,,,41,24,,,41,42,,,41,1*7F

$GBGSV,6,2,23,60,,,40,3,,,40,14,,,40,25,,,40,1*44

$GBGSV,6,3,23,13,,,39,39,,,39,38,,,38,1,,,38,1*40

$GBGSV,6,4,23,40,,,37,16,,,37,41,,,37,2,,,36,1*40

$GBGSV,6,5,23,8,,,36,6,,,36,26,,,35,9,,,35,1*47

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,31,1*75

$GBRMC,120103.543,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120103.543,0.000,779.930,779.930,713.262,2097152,2097152,2097152*61



2025-07-31 20:00:59:834 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:00:59:939 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 20:01:00:257 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:01:00:362 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:01:00:392 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:01:00:437 ==>> 1A A1 00 80 00 
Get AD_V15 2393mV
OVER 150


2025-07-31 20:01:00:527 ==>> 原始值:【2393】, 乘以分压基数【2】还原值:【4786】
2025-07-31 20:01:00:546 ==>> 【读取AD_V15电压(前)】通过,【4786mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:01:00:549 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:01:00:553 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:01:00:648 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:01:00:768 ==>> $GBGGA,120104.523,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,41,59,,,41,24,,,41,1*7F

$GBGSV,6,2,23,42,,,41,3,,,40,14,,,40,25,,,40,1*45

$GBGSV,6,3,23,13,,,39,39,,,39,38,,,38,1,,,38,1*40

$GBGSV,6,4,23,40,,,37,16,,,37,41,,,37,2,,,36,1*40

$GBGSV,6,5,23,8,,,36,6,,,36,26,,,35,9,,,35,1*47

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,32,1*76

$GBRMC,120104.523,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120104.523,0.000,781.726,781.726,714.905,2097152,2097152,2097152*6D

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2427mV
OVER 150


2025-07-31 20:01:00:813 ==>> 原始值:【2427】, 乘以分压基数【2】还原值:【4854】
2025-07-31 20:01:00:836 ==>> 【读取AD_V16电压(前)】通过,【4854mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:01:00:840 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:01:00:846 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:01:01:143 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:16][COMM]adc read battery ts volt:13 mv
[D][05:18:16][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3073  volt:5401 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2428  volt:3912 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:01:01:375 ==>> 【转刹把供电电压(主控ADC)】通过,【5401mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:01:01:378 ==>> 检测【转刹把供电电压】
2025-07-31 20:01:01:383 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:01:01:646 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:17][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:17][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:17][COMM]adc read battery ts volt:12 mv
[D][05:18:17][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3090  volt:5431 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2428  volt:3912 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:01:01:721 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:01:01:826 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:01:01:906 ==>> 【转刹把供电电压】通过,【5431mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:01:01:910 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:01:01:913 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:02:117 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:01:02:182 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:01:02:186 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:01:02:189 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:02:297 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:01:02:312 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:01:02:402 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:02:509 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:01:02:600 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:01:02:614 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:02:629 ==>> 00 00 00 00 00 
head err!


2025-07-31 20:01:02:719 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:01:02:734 ==>>               503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,42,42,,,42,33,,,42,60,,,41,1*75

$GBGSV,6,2,23,59,,,41,25,,,41,3,,,40,14,,,40,1*4E

$GBGSV,6,3,23,13,,,39,39,,,39,38,,,38,1,,,38,1*40

$GBGSV,6,4,23,16,,,38,2,,,37,40,,,37,41,,,37,1*4E

$GBGSV,6,5,23,8,,,36,26,,,36,9,,,36,6,,,36,1*47

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,32,1*76

$GBRMC,120106.503,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120106.503,0.000,788.025,788.025,720.665,2097152,2097152,2097152*63



2025-07-31 20:01:02:824 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:02:914 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:01:02:929 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:01:03:004 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:01:03:034 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:01:03:083 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:01:03:086 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:01:03:089 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:01:03:184 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:01:03:229 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:01:03:328 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:01:03:332 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:01:03:340 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:01:03:441 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:01:03:632 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:01:03:636 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:01:03:641 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:01:03:742 ==>> $GBGGA,120107.503,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,41,59,,,41,24,,,41,1*7F

$GBGSV,6,2,23,42,,,41,3,,,40,14,,,40,25,,,40,1*45

$GBGSV,6,3,23,13,,,39,39,,,39,38,,,38,1,,,38,1*40

$GBGSV,6,4,23,2,,,37,40,,,37,16,,,37,41,,,37,1*41

$GBGSV,6,5,23,8,,,36,6,,,36,26,,,35,9,,,35,1*47

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,32,1*76

$GBRMC,120107.503,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120107.503,0.000,782.625,782.625,715.726,2097152,2097152,2097152*62

3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:01:03:847 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:01:03:934 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:01:03:938 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:01:03:941 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:01:04:042 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:01:04:289 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:01:04:295 ==>> 检测【左刹电压测试1】
2025-07-31 20:01:04:300 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:01:04:647 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1725  volt:2274 mv
[D][05:18:20][COMM]adc read right brake adc:1723  volt:2271 mv
[D][05:18:20][COMM]adc read throttle adc:1724  volt:2272 mv
[D][05:18:20][COMM]adc read battery ts volt:13 mv
[D][05:18:20][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2428  volt:3912 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:01:04:737 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:01:04:816 ==>> 【左刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 20:01:04:822 ==>> 检测【右刹电压测试1】
2025-07-31 20:01:04:846 ==>> 【右刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 20:01:04:850 ==>> 检测【转把电压测试1】
2025-07-31 20:01:04:865 ==>> 【转把电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 20:01:04:869 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:01:04:875 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:01:04:935 ==>> 3A A3 03 00 A3 


2025-07-31 20:01:05:040 ==>> OFF_OUT3
OVER 150


2025-07-31 20:01:05:203 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:01:05:206 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:01:05:211 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:01:05:340 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:01:05:522 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:01:05:526 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:01:05:530 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:01:05:632 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:01:05:722 ==>> $GBGGA,120109.503,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,59,,,41,24,,,41,42,,,41,33,,,41,1*7C

$GBGSV,6,2,23,60,,,40,3,,,40,25,,,40,39,,,39,1*45

$GBGSV,6,3,23,14,,,39,13,,,38,38,,,38,1,,,38,1*4E

$GBGSV,6,4,23,40,,,37,16,,,37,41,,,37,2,,,36,1*40

$GBGSV,6,5,23,8,,,36,26,,,35,9,,,35,6,,,35,1*44

$GBGSV,6,6,23,5,,,34,4,,,34,10,,,32,1*76

$GBRMC,120109.503,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120109.503,0.000,777.223,777.223,710.786,2097152,2097152,2097152*63



2025-07-31 20:01:05:853 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:01:05:859 ==>> 检测【左刹电压测试2】
2025-07-31 20:01:05:874 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:01:05:879 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:01:06:142 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:21][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:21][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:21][COMM]adc read battery ts volt:10 mv
[D][05:18:21][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:01:06:389 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:01:06:393 ==>> 检测【右刹电压测试2】
2025-07-31 20:01:06:409 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 20:01:06:412 ==>> 检测【转把电压测试2】
2025-07-31 20:01:06:428 ==>> 【转把电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 20:01:06:433 ==>> 检测【晶振检测】
2025-07-31 20:01:06:445 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:01:06:749 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]
$GBGGA,120110.503,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,59,,,41,24,,,41,42,,,41,33,,,41,1*7B

$GBGSV,6,2,24,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,6,3,24,39,,,39,13,,,38,38,,,38,1,,,38,1*46

$GBGSV,6,4,24,40,,,37,16,,,37,2,,,36,8,,,36,1*7B

$GBGSV,6,5,24,6,,,36,41,,,36,26,,,35,9,,,35,1*7D

$GBGSV,6,6,24,5,,,34,4,,,34,7,,,32,10,,,32,1*47

$GBRMC,120110.503,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120110.503,0.000,773.298,773.298,707.198,2097152,2097152,2097152*64



2025-07-31 20:01:06:962 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:01:06:966 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:01:06:971 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:01:07:051 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1643mV
Get AD_V4 1645mV
Get AD_V5 2756mV
Get AD_V6 1987mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:01:07:235 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:01:07:239 ==>> 检测【检测BootVer】
2025-07-31 20:01:07:246 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:01:07:596 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539175
[D][05:18:23][FCTY]HardwareID  = 867222087723302
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = D17E44DB44D9
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:23][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 75 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:

2025-07-31 20:01:07:641 ==>> 23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3844 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:01:07:746 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 20:01:07:769 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:01:07:773 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:01:07:779 ==>> 检测【检测固件版本】
2025-07-31 20:01:07:788 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:01:07:792 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:01:07:795 ==>> 检测【检测蓝牙版本】
2025-07-31 20:01:07:807 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:01:07:814 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:01:07:819 ==>> 检测【检测MoBikeId】
2025-07-31 20:01:07:830 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:01:07:834 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:01:07:840 ==>> 检测【检测蓝牙地址】
2025-07-31 20:01:07:845 ==>> 取到目标值:D17E44DB44D9
2025-07-31 20:01:07:852 ==>> 【检测蓝牙地址】通过,【D17E44DB44D9】符合目标值【】要求!
2025-07-31 20:01:07:856 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:01:07:859 ==>> 提取到蓝牙地址:D17E44DB44D9
2025-07-31 20:01:07:862 ==>> 检测【BOARD_ID】
2025-07-31 20:01:07:871 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:01:07:875 ==>> 检测【检测充电电压】
2025-07-31 20:01:07:890 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:01:07:897 ==>> 检测【检测VBUS电压1】
2025-07-31 20:01:07:926 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:01:07:930 ==>> 检测【检测充电电流】
2025-07-31 20:01:07:945 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:01:07:951 ==>> 检测【检测IMEI】
2025-07-31 20:01:07:966 ==>> 取到目标值:867222087723302
2025-07-31 20:01:07:973 ==>> 【检测IMEI】通过,【867222087723302】符合目标值【】要求!
2025-07-31 20:01:07:982 ==>> 提取到IMEI:867222087723302
2025-07-31 20:01:08:005 ==>> 检测【检测IMSI】
2025-07-31 20:01:08:010 ==>> 取到目标值:460130071539175
2025-07-31 20:01:08:013 ==>> 【检测IMSI】通过,【460130071539175】符合目标值【】要求!
2025-07-31 20:01:08:018 ==>> 提取到IMSI:460130071539175
2025-07-31 20:01:08:049 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:01:08:052 ==>> 取到目标值:460130071539175
2025-07-31 20:01:08:057 ==>> 【校验网络运营商(移动)】通过,【460130071539175】符合目标值【】要求!
2025-07-31 20:01:08:064 ==>> 检测【打开CAN通信】
2025-07-31 20:01:08:080 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:01:08:135 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:01:08:330 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:01:08:334 ==>> 检测【检测CAN通信】
2025-07-31 20:01:08:338 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:01:08:465 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:01:08:525 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35363
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:01:08:600 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:01:08:608 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:01:08:613 ==>> 检测【关闭CAN通信】
2025-07-31 20:01:08:619 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:01:08:661 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:01:08:750 ==>> $GBGGA,120112.503,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,59,,,41,24,,,41,42,,,41,1*78

$GBGSV,7,2,25,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,7,3,25,13,,,39,39,,,39,38,,,38,1,,,38,1*47

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,8,,,36,1*7B

$GBGSV,7,5,25,6,,,36,41,,,36,26,,,35,9,,,35,1*7D

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,34,10,,,32,1*41

$GBGSV,7,7,25,43,,,39,1*7C

$GBRMC,120112.503,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120112.503,0.000,776.743,776.743,710.348,2097152,2097152,2097152*6F

[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:01:08:889 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:01:08:896 ==>> 检测【打印IMU STATE】
2025-07-31 20:01:08:918 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:01:09:128 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:01:09:227 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:01:09:231 ==>> 检测【六轴自检】
2025-07-31 20:01:09:234 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:01:09:433 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:01:09:933 ==>> $GBGGA,120113.503,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,59,,,41,24,,,41,42,,,41,1*78

$GBGSV,7,2,25,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,7,3,25,13,,,39,39,,,39,38,,,38,1,,,38,1*47

$GBGSV,7,4,25,16,,,38,40,,,37,2,,,36,8,,,36,1*74

$GBGSV,7,5,25,26,,,36,6,,,36,41,,,36,9,,,35,1*7E

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBGSV,7,7,25,43,,,36,1*73

$GBRMC,120113.503,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120113.503,0.000,777.606,777.606,711.137,2097152,2097152,2097152*65

[D][05:18:25][COMM]read battery soc:255


2025-07-31 20:01:10:751 ==>> $GBGGA,120114.503,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,59,,,41,24,,,41,42,,,41,33,,,41,1*7B

$GBGSV,7,2,25,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,7,3,25,13,,,39,39,,,39,38,,,38,1,,,38,1*47

$GBGSV,7,4,25,16,,,38,40,,,37,2,,,36,8,,,36,1*74

$GBGSV,7,5,25,26,,,36,6,,,36,41,,,36,9,,,35,1*7E

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBGSV,7,7,25,43,,,,1*76

$GBRMC,120114.503,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120114.503,0.000,776.741,776.741,710.346,2097152,2097152,2097152*67



2025-07-31 20:01:11:115 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:01:11:312 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38129 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,-9,4050]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:01:11:577 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:01:11:584 ==>> 检测【打印IMU STATE2】
2025-07-31 20:01:11:606 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:01:11:788 ==>> $GBGGA,120115.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,42,,,41,1*78

$GBGSV,6,2,24,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,6,3,24,13,,,39,39,,,39,38,,,38,1,,,38,1*47

$GBGSV,6,4,24,16,,,38,40,,,37,2,,,36,8,,,36,1*74

$GBGSV,6,5,24,26,,,36,6,,,36,41,,,36,9,,,35,1*7E

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBRMC,120115.503,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120115.503,0.000,777.606,777.606,711.137,2097152,2097152,2097152*63

[W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:01:11:852 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:01:11:859 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:01:11:881 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:01:11:887 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 20:01:11:937 ==>> 5A A5 02 5A A5 


2025-07-31 20:01:12:042 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:01:12:122 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:01:12:126 ==>> 检测【检测VBUS电压2】
2025-07-31 20:01:12:131 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:01:12:222 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:01:12:523 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539175
[D][05:18:28][FCTY]HardwareID  = 867222087723302
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D17E44DB44D9
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 8, adc = 348
[D][05:18:28][FCTY]Acckey1 vol = 5494 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3844 mv
[D][05

2025-07-31 20:01:12:569 ==>> :18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:01:12:648 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:01:12:674 ==>> $GBGGA,120116.503,,,,,0,00,,,M,,M,,*69


2025-07-31 20:01:12:749 ==>> 

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,42,,,41,1*78

$GBGSV,6,2,24,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,6,3,24,13,,,39,39,,,39,38,,,38,1,,,38,1*47

$GBGSV,6,4,24,16,,,38,40,,,37,2,,,36,8,,,36,1*74

$GBGSV,6,5,24,26,,,36,6,,,36,41,,,36,9,,,35,1*7E

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBRMC,120116.503,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120116.503,0.000,777.606,777.606,711.137,2097152,2097152,2097152*60



2025-07-31 20:01:13:020 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539175
[D][05:18:28][FCTY]HardwareID  = 867222087723302
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D17E44DB44D9
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 5000 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 184
[D][05:18:28][FCTY]Acckey1 vol = 5493 mv, Acckey2 vol = 50 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:1

2025-07-31 20:01:13:050 ==>> 8:28][FCTY]Bat1         = 3844 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:01:13:174 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:01:13:184 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:01:13:194 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:01:13:232 ==>> 5A A5 01 5A A5 


2025-07-31 20:01:13:336 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:01:13:427 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31


2025-07-31 20:01:13:452 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:01:13:458 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:01:13:468 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:01:13:532 ==>> [D][05:18:29][COMM]msg 0601 loss. last_tick:35349. cur_tick:40364. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40364
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:01:13:728 ==>> $GBGGA,120117.503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,42,33,,,42,59,,,41,42,,,41,1*7B

$GBGSV,6,2,24,60,,,40,3,,,40,14,,,40,25,,,40,1*43

$GBGSV,6,3,24,13,,,39,1,,,39,39,,,39,38,,,38,1*46

$GBGSV,6,4,24,16,,,38,2,,,37,40,,,37,8,,,36,1*75

$GBGSV,6,5,24,26,,,36,6,,,36,41,,,36,9,,,35,1*7E

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,34,10,,,32,1*41

$GBRMC,120117.503,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120117.503,0.000,781.054,781.054,714.290,2097152,2097152,2097152*6A



2025-07-31 20:01:13:735 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:01:13:740 ==>> 检测【打开WIFI(3)】
2025-07-31 20:01:13:745 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:01:13:955 ==>> [D][05:18:29][COMM]read battery soc:255
[W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:01:14:020 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:01:14:027 ==>> 检测【扩展芯片hw】
2025-07-31 20:01:14:048 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:01:14:225 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:01:14:304 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:01:14:311 ==>> 检测【扩展芯片boot】
2025-07-31 20:01:14:325 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:01:14:332 ==>> 检测【扩展芯片sw】
2025-07-31 20:01:14:345 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:01:14:349 ==>> 检测【检测音频FLASH】
2025-07-31 20:01:14:376 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:01:14:528 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:01:14:633 ==>> +WIFISCAN:4,0,CC057790A6E1,-74
+WIFISCAN:4,1,CC057790A7C0,-78
+WIFISCAN:4,2,CC057790A5C1,-79
+WIFISCAN:4,3,F86FB0660A82,-82

[D][05:18:30][CAT1]wifi scan report total[4]


2025-07-31 20:01:16:498 ==>> $GBGGA,120114.510,2301.2574471,N,11421.9430160,E,1,15,1.15,69.633,M,-1.770,M,,*51

$GBGSA,A,3,14,24,33,42,06,16,39,13,09,08,25,38,1.78,1.15,1.35,4*05

$GBGSA,A,3,40,10,41,,,,,,,,,,1.78,1.15,1.35,4*04

$GBGSV,6,1,24,14,83,235,40,3,61,191,40,24,60,355,42,33,60,313,42,1*44

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,59,52,129,41,1*44

$GBGSV,6,3,24,39,52,1,39,13,51,221,39,9,49,313,35,1,48,126,38,1*7E

$GBGSV,6,4,24,8,47,206,36,2,46,238,37,60,41,238,40,25,36,263,41,1*7B

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBRMC,120114.510,A,2301.2574471,N,11421.9430160,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:30][GNSS]HD8040 GPS
[W][05:18:30][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:30][GNSS]GPS diff_sec 124008164, report 0x42 frame
$GBGST,120114.510,0.947,0.269,0.214,0.327,2.014,4.214,8.588*79

[D][05:18:30][COMM]Main Task receive event:131
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][COMM]index:0,power_mode:0xFF
[D][05:18:30][COMM]index:1,sound_mode:0xFF
[D][05:18:30][COMM]index:2,gsensor_mode:

2025-07-31 20:01:16:603 ==>> 0xFF
[D][05:18:30][COMM]index:3,report_freq_mode:0xFF
[D][05:18:30][COMM]index:4,report_period:0xFF
[D][05:18:30][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:30][COMM]index:6,normal_reset_period:0xFF
[D][05:18:30][COMM]index:7,spock_over_speed:0xFF
[D][05:18:30][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:30][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:30][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:30][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:30][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:30][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:30][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:30][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:30][COMM]index:16,imu_config_params:0xFF
[D][05:18:30][COMM]index:17,long_connect_params:0xFF
[D][05:18:30][COMM]index:18,detain_mark:0xFF
[D][05:18:30][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:30][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:30][COMM]index:21,mc_mode:0xFF
[D][05:18:30][COMM]index:22,S_mode:0xFF
[D][05:18:30][COMM]index:23,overweight:0xFF
[D][05:18:30][COMM]index:24,standstill_mode:0xFF
[D][05:18:30][COMM]index:25,night_mode:0xFF
[D][05:18:30][COMM]index:26,exper

2025-07-31 20:01:16:708 ==>> iment1:0xFF
[D][05:18:30][COMM]index:27,experiment2:0xFF
[D][05:18:30][COMM]index:28,experiment3:0xFF
[D][05:18:30][COMM]index:29,experiment4:0xFF
[D][05:18:30][COMM]index:30,night_mode_start:0xFF
[D][05:18:30][COMM]index:31,night_mode_end:0xFF
[D][05:18:30][COMM]index:33,park_report_minutes:0xFF
[D][05:18:30][COMM]index:34,park_report_mode:0xFF
[D][05:18:30][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:30][COMM]index:38,charge_battery_para: FF
[D][05:18:30][COMM]index:39,multirider_mode:0xFF
[D][05:18:30][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:30][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:30][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:30][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:30][COMM]index:44,riding_duration_config:0xFF
[D][05:18:30][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:30][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:30][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:30][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:30][COMM]index:49,mc_load_startup:0xFF
[D][05:18:30][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:30][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:30][COMM]index:52,traffic_mode:0xF

2025-07-31 20:01:16:813 ==>> F
[D][05:18:30][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:30][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:30][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:30][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:30][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:30][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:30][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:30][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:30][COMM]index:63,experiment5:0xFF
[D][05:18:30][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:30][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:30][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:30][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:30][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:30][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:30][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:30][COMM]index:72,experiment6:0xFF
[D][05:18:30][COMM]index:73,experiment7:0xFF
[D][05:18:30][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:30][COMM]index:75,zero_value_from_server:-1
[D][05:18:30][COMM]index:76,multirider_threshold:255
[D][05:18:30][COMM]index:77

2025-07-31 20:01:16:919 ==>> ,experiment8:255
[D][05:18:30][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:30][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:30][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:30][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:30][COMM]index:83,loc_report_interval:255
[D][05:18:30][COMM]index:84,multirider_threshold_p2:255
[D][05:18:30][COMM]index:85,multirider_strategy:255
[D][05:18:30][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:30][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:30][COMM]index:90,weight_param:0xFF
[D][05:18:30][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:30][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:30][COMM]index:95,current_limit:0xFF
[D][05:18:30][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:30][COMM]index:100,location_mode:0xFF

[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:131 finished processing
[D][05:18:30][COMM]Main Task receive e

2025-07-31 20:01:17:024 ==>> vent:20
[D][05:18:30][GNSS]stop event:1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:1
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[294]
[D][05:18:30][

2025-07-31 20:01:17:129 ==>> SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052ef8] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:30][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B50E36DB6696AFF8FC5140E3362AF24376FC81AED2A24BC507A904B2C1B712D907E411046630277B890866D08639E006BE28443A73D566A666E933788DA66B3BC9CD287644495335FDE70E5AE8BE0D593865E2EE73B51D6C6680151B52FAA02295F0325269776667AD510298223AD5C1AE7463752EBAFF2E27A4F276FB562C520B52E1
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_

2025-07-31 20:01:17:384 ==>> $GBGGA,120117.000,2301.2579009,N,11421.9418197,E,1,15,1.15,73.185,M,-1.770,M,,*53

$GBGSA,A,3,14,24,33,42,06,16,39,13,09,08,25,38,1.78,1.15,1.35,4*05

$GBGSA,A,3,40,10,41,,,,,,,,,,1.78,1.15,1.35,4*04

$GBGSV,6,1,24,14,83,234,41,3,61,191,40,24,60,355,42,33,60,313,42,1*44

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,59,52,129,41,1*44

$GBGSV,6,3,24,39,52,1,39,13,51,221,39,9,49,313,35,1,48,126,39,1*7F

$GBGSV,6,4,24,8,47,206,36,2,46,238,36,60,41,238,40,25,36,263,41,1*7A

$GBGSV,6,5,24,38,36,190,38,7,34,184,35,4,32,112,33,40,27,160,38,1*7F

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,36,1*4C

$GBGSV,2,1,08,24,60,355,42,33,60,313,41,42,58,157,41,39,52,1,40,5*7B

$GBGSV,2,2,08,25,36,263,38,38,36,190,38,40,27,160,35,41,11,324,29,5*72

$GBRMC,120117.000,A,2301.2579009,N,11421.9418197,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[W][05:18:33][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120117.000,1.427,0.202,0.173,0.256,1.297,1.838,4.344*7D



2025-07-31 20:01:17:960 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][COMM]read battery soc:255


2025-07-31 20:01:18:380 ==>> $GBGGA,120118.000,2301.2579565,N,11421.9417428,E,1,15,1.15,73.474,M,-1.770,M,,*56

$GBGSA,A,3,14,24,33,42,06,16,39,13,09,08,25,38,1.78,1.15,1.35,4*05

$GBGSA,A,3,40,10,41,,,,,,,,,,1.78,1.15,1.35,4*04

$GBGSV,6,1,24,14,83,234,40,3,61,191,40,24,60,355,42,33,60,313,42,1*45

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,59,52,129,41,1*44

$GBGSV,6,3,24,39,52,1,39,13,51,221,39,9,49,313,35,1,48,126,38,1*7E

$GBGSV,6,4,24,8,47,206,36,2,46,238,37,60,41,238,40,25,36,263,41,1*7B

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,355,43,33,60,313,42,42,58,157,42,39,52,1,40,5*7A

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120118.000,A,2301.2579565,N,11421.9417428,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[W][05:18:34][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120118.000,1.266,0.245,0.205,0.301,1.110,1.548,3.841*7C



2025-07-31 20:01:18:802 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:01:18:982 ==>> [D][05:18:34][COMM]45813 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:01:19:117 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 20:01:19:372 ==>> $GBGGA,120119.000,2301.2579752,N,11421.9417194,E,1,15,1.15,73.537,M,-1.770,M,,*55

$GBGSA,A,3,14,24,33,42,06,16,39,13,09,08,25,38,1.78,1.15,1.36,4*06

$GBGSA,A,3,40,10,41,,,,,,,,,,1.78,1.15,1.36,4*07

$GBGSV,6,1,24,14,83,234,40,3,61,191,40,24,60,355,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,59,52,129,41,1*44

$GBGSV,6,3,24,39,52,1,39,13,51,221,39,9,49,313,35,1,48,126,38,1*7E

$GBGSV,6,4,24,8,47,206,36,2,46,238,37,60,41,238,40,25,36,263,40,1*7A

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,355,43,33,60,313,43,42,58,157,42,39,52,1,40,5*7B

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120119.000,A,2301.2579752,N,11421.9417194,E,0.003,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

[W][05:18:35][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120119.000,1.380,0.245,0.204,0.301,1.156,1.512,3.598*71



2025-07-31 20:01:19:406 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:01:19:412 ==>> 检测【打开喇叭声音】
2025-07-31 20:01:19:435 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:01:19:478 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:01:20:078 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:35][COMM]read file, len:15228, num:4
[D][05:18:35][COMM]--->crc16:0x419c
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:35]

2025-07-31 20:01:20:182 ==>> [COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:35][COMM]f:[ec800m_au

2025-07-31 20:01:20:191 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:01:20:196 ==>> 检测【打开大灯控制】
2025-07-31 20:01:20:212 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:01:20:287 ==>> dio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]read battery soc:255
[D][05:18:35][COMM]46825 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:01:20:542 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:01:20:647 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           5:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88BE8B94521617AD5D87A9BBA5ADC85B49381F28E38853C44827DAE15F5A55B09BBC0F91B1DC065B268215F056E4B13EA545DFFFE20F27AED09C632B260FCD2388EC2C26CB0154A9CEDEAF40BD79F83463D7176E
[D][05:18:36][CAT1]<<< 
SEND 

2025-07-31 20:01:20:707 ==>> OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 20:01:20:755 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:01:20:760 ==>> 检测【关闭仪表供电3】
2025-07-31 20:01:20:768 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:01:20:921 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 20:01:21:056 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:01:21:063 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:01:21:072 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:01:21:373 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,120121.000,2301.2579662,N,11421.9417423,E,1,17,0.94,73.649,M,-1.770,M,,*55

$GBGSA,A,3,14,24,33,42,06,16,39,13,59,09,08,60,1.64,0.94,1.34,4*07

$GBGSA,A,3,25,38,40,10,41,,,,,,,,1.64,0.94,1.34,4*0C

$GBGSV,6,1,24,14,83,234,40,3,61,191,40,24,60,355,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,13,51,221,39,59,51,128,41,9,49,313,35,1,48,126,38,1*7E

$GBGSV,6,4,24,8,47,206,36,2,46,238,36,60,43,241,40,25,36,263,41,1*76

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,355,43,33,60,313,43,42,58,157,43,39,52,1,41,5*7B

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120121.000,A,2301.2579662,N,11421.9417423,E,0.003,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

[W][05:18:37][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120121.000,1.745,0.257,0.224,0.331,1.381,1.616,3.332*7C



2025-07-31 20:01:21:614 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:01:21:620 ==>> 检测【读大灯电压】
2025-07-31 20:01:21:625 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:01:21:827 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33409]


2025-07-31 20:01:21:917 ==>> 【读大灯电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:01:21:924 ==>> 检测【关闭大灯控制2】
2025-07-31 20:01:21:934 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:01:21:977 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:01:22:082 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:01:22:212 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:01:22:222 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:01:22:232 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:01:22:385 ==>> $GBGGA,120122.000,2301.2579571,N,11421.9417380,E,1,17,0.94,73.680,M,-1.770,M,,*5C

$GBGSA,A,3,14,24,33,42,06,16,39,13,59,09,08,60,1.64,0.94,1.34,4*07

$GBGSA,A,3,25,38,40,10,41,,,,,,,,1.64,0.94,1.34,4*0C

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,355,41,33,60,313,42,1*45

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,13,51,221,39,59,51,128,41,9,49,313,35,1,48,126,39,1*7F

$GBGSV,6,4,24,8,47,206,36,2,46,238,37,60,43,241,40,25,36,263,41,1*77

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,355,44,33,60,313,43,42,58,157,43,39,52,1,40,5*7D

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120122.000,A,2301.2579571,N,11421.9417380,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:38][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120122.000,1.581,0.248,0.218,0.320,1.245,1.459,3.115*73



2025-07-31 20:01:22:490 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:01:22:535 ==>>                                                             

2025-07-31 20:01:22:750 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:01:22:756 ==>> 检测【打开WIFI(4)】
2025-07-31 20:01:22:764 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:01:22:957 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:01:23:081 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:01:23:091 ==>> 检测【EC800M模组版本】
2025-07-31 20:01:23:112 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:01:23:392 ==>> $GBGGA,120123.000,2301.2579480,N,11421.9416950,E,1,17,0.94,73.876,M,-1.770,M,,*53

$GBGSA,A,3,14,24,33,42,06,16,39,13,59,09,08,60,1.64,0.94,1.34,4*07

$GBGSA,A,3,25,38,40,10,41,,,,,,,,1.64,0.94,1.34,4*0C

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,13,51,221,39,59,51,128,41,9,49,313,35,1,48,126,38,1*7E

$GBGSV,6,4,24,8,47,206,36,2,46,238,36,60,43,241,40,25,36,263,40,1*77

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,36,1*4C

$GBGSV,2,1,08,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120123.000,A,2301.2579480,N,11421.9416950,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:39][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120123.000,1.518,0.223,0.197,0.291,1.184,1.380,2.968*7E

[W][05:18:39][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:39][CAT1]gsm read msg sub id: 12
[D

2025-07-31 20:01:23:422 ==>> ][05:18:39][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:01:23:467 ==>>                                                                                                                                                                                                                   

2025-07-31 20:01:23:543 ==>> [D][05:18:39][COMM]50389 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:01:23:813 ==>> +WIFISCAN:4,0,F88C21BCF57D,-31
+WIFISCAN:4,1,CC057790A740,-69
+WIFISCAN:4,2,CC057790A7C0,-78
+WIFISCAN:4,3,F86FB0660A82,-79

[D][05:18:39][CAT1]wifi scan report total[4]


2025-07-31 20:01:23:978 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:01:24:038 ==>> [D][05:18:39][GNSS]recv submsg id[3]


2025-07-31 20:01:24:098 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:01:24:437 ==>> $GBGGA,120124.000,2301.2579367,N,11421.9416914,E,1,20,0.81,73.962,M,-1.770,M,,*5E

$GBGSA,A,3,14,03,24,33,42,06,16,39,13,59,09,02,1.52,0.81,1.28,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,41,,,,,1.52,0.81,1.28,4*0F

$GBGSV,6,1,24,14,83,234,41,3,63,190,40,24,60,356,42,33,60,313,42,1*44

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,13,51,221,39,59,51,128,41,9,49,313,35,2,48,240,37,1*71

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,36,263,41,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120124.000,A,2301.2579367,N,11421.9416914,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:40][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120124.000,1.737,0.237,0.216,0.317,1.339,1.504,2.980*7E

[W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSI

2025-07-31 20:01:24:482 ==>> ON=TOTAL

[D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:01:24:557 ==>> [D][05:18:40][COMM]51402 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:01:24:636 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:01:24:646 ==>> 检测【配置蓝牙地址】
2025-07-31 20:01:24:667 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:01:24:797 ==>> [W][05:18:40][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:01:24:842 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D17E44DB44D9>】
2025-07-31 20:01:25:039 ==>> recv ble 1
recv ble 2
ble set mac ok :d1,7e,44,db,44,d9
enable filters ret : 0

2025-07-31 20:01:25:117 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:01:25:124 ==>> 检测【BLETEST】
2025-07-31 20:01:25:134 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:01:25:163 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_h

2025-07-31 20:01:25:204 ==>> exdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:01:25:234 ==>> 4A A4 01 A4 4A 


2025-07-31 20:01:25:339 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            6,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,34,26,16,225,36,41,11,324,36,1*4D

$GBGSV,2,1,08,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,32

2025-07-31 20:01:25:399 ==>> 4,29,5*73

$GBRMC,120125.000,A,2301.2579185,N,11421.9416906,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

[W][05:18:41][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120125.000,1.578,0.274,0.247,0.363,1.212,1.369,2.814*7F

recv ble 1
recv ble 2
<BSJ*MAC:D17E44DB44D9*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D17E44DB44D999999OVER 150


2025-07-31 20:01:25:745 ==>> [D][05:18:41][PROT]CLEAN,SEND:4
[D][05:18:41][PROT]index:4 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:8
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:4, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900007]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88BEA09A4FD4BFBF55072F75FE6B5518A45BCB1CC7052D4257ACF9E1C8B3825698C0067B84C55076A2838D65371CB5DA48880BC147CB3062

2025-07-31 20:01:25:820 ==>> 77666428ACC99710BB566F5B8D83F2BDB90CAD0890656B2CB97AC550
[D][05:18:41][COMM]52413 imu init OK
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 20:01:25:971 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:01:26:142 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:01:26:151 ==>> 该项需要延时执行
2025-07-31 20:01:26:378 ==>> $GBGGA,120126.000,2301.2579010,N,11421.9417009,E,1,20,0.81,74.114,M,-1.770,M,,*55

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.52,0.81,1.28,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,41,,,,,1.52,0.81,1.28,4*0F

$GBGSV,6,1,24,14,83,234,41,3,63,190,41,24,60,356,42,33,60,313,42,1*45

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,36,263,41,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,35,1*4F

$GBGSV,2,1,08,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120126.000,A,2301.2579010,N,11421.9417009,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

[W][05:18:42][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120126.000,1.637,0.235,0.214,0.314,1.250,1.392,2.776*7E



2025-07-31 20:01:27:375 ==>> $GBGGA,120127.000,2301.2578851,N,11421.9417070,E,1,20,0.81,74.134,M,-1.770,M,,*54

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.52,0.81,1.28,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,41,,,,,1.52,0.81,1.28,4*0F

$GBGSV,6,1,24,14,83,234,41,3,63,190,41,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,36,263,41,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,35,1*4F

$GBGSV,2,1,08,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,40,5*79

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120127.000,A,2301.2578851,N,11421.9417070,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:43][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120127.000,1.720,0.242,0.220,0.323,1.306,1.434,2.753*75



2025-07-31 20:01:27:982 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:01:28:374 ==>> $GBGGA,120128.000,2301.2578800,N,11421.9416993,E,1,20,0.81,74.200,M,-1.770,M,,*5E

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.52,0.81,1.28,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,41,,,,,1.52,0.81,1.28,4*0F

$GBGSV,6,1,24,14,83,234,41,3,63,190,41,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,36,263,41,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,35,1*4F

$GBGSV,2,1,08,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,40,5*79

$GBGSV,2,2,08,25,36,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*73

$GBRMC,120128.000,A,2301.2578800,N,11421.9416993,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:44][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120128.000,1.638,0.229,0.209,0.307,1.240,1.362,2.652*75



2025-07-31 20:01:29:437 ==>> $GBGGA,120129.000,2301.2578807,N,11421.9416948,E,1,20,0.81,74.223,M,-1.770,M,,*5F

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.52,0.81,1.28,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,41,,,,,1.52,0.81,1.28,4*0F

$GBGSV,6,1,24,14,83,234,41,3,63,190,40,24,60,356,41,33,60,313,42,1*47

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,37,1*71

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,41,1*74

$GBGSV,6,5,24,38,36,190,38,7,34,184,35,4,32,112,33,40,27,160,37,1*70

$GBGSV,6,6,24,10,24,186,32,5,22,257,35,26,16,225,36,41,11,324,35,1*4F

$GBGSV,2,1,08,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,2,2,08,25,37,263,39,38,36,190,38,40,27,160,35,41,11,324,29,5*72

$GBRMC,120129.000,A,2301.2578807,N,11421.9416948,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[W][05:18:45][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120129.000,1.755,0.221,0.202,0.297,1.323,1.434,2.670*75



2025-07-31 20:01:30:002 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:01:30:379 ==>> $GBGGA,120130.000,2301.2578783,N,11421.9417020,E,1,21,0.70,74.333,M,-1.770,M,,*5D

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,234,41,3,63,190,40,24,60,356,41,33,60,313,42,1*47

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,41,1*74

$GBGSV,6,5,24,38,36,190,38,7,34,184,35,4,32,112,33,40,27,160,37,1*70

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7B

$GBGSV,2,1,08,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,2,2,08,25,37,263,39,38,36,190,38,40,27,160,35,41,11,324,28,5*73

$GBRMC,120130.000,A,2301.2578783,N,11421.9417020,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:46][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120130.000,1.612,0.232,0.216,0.314,1.211,1.318,2.532*7E



2025-07-31 20:01:30:972 ==>> [D][05:18:46][PROT]CLEAN,SEND:4
[D][05:18:46][PROT]index:4 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:8
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:4, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[D][05:18:46][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900007]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]tx ret[17

2025-07-31 20:01:31:062 ==>> ] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BEF95692874EB595BD0AA2BD3EFA6FBF3A38C42DE2DF31E9CF76CDF664CEBF28440DD4F2FBD18BDAE3832E7193754A359ED401FE463434CB23CD6120FCCB52FEFFD7B5B622783ACAF8F15E3914FF452F50E7AE
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 20:01:31:396 ==>> $GBGGA,120131.000,2301.2578918,N,11421.9417132,E,1,21,0.70,74.310,M,-1.770,M,,*53

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,36,2,48,240,36,1*73

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,41,1*74

$GBGSV,6,5,24,38,36,190,38,7,34,184,35,4,32,112,33,40,27,160,38,1*7F

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,28,5*41

$GBRMC,120131.000,A,2301.2578918,N,11421.9417132,E,0.004,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

[W][05:18:47][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120131.000,1.626,0.257,0.235,0.348,1.217,1.315,2.476*79



2025-07-31 20:01:31:993 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:01:32:401 ==>> $GBGGA,120132.000,2301.2578978,N,11421.9417066,E,1,21,0.70,74.301,M,-1.770,M,,*56

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,40,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120132.000,A,2301.2578978,N,11421.9417066,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[W][05:18:48][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120132.000,1.665,0.214,0.199,0.293,1.243,1.334,2.449*76



2025-07-31 20:01:33:392 ==>> $GBGGA,120133.000,2301.2578948,N,11421.9417079,E,1,21,0.70,74.310,M,-1.770,M,,*5A

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,40,1*75

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,28,5*41

$GBRMC,120133.000,A,2301.2578948,N,11421.9417079,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:49][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120133.000,1.692,0.236,0.218,0.321,1.260,1.344,2.420*74



2025-07-31 20:01:33:992 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:01:34:400 ==>> $GBGGA,120134.000,2301.2578921,N,11421.9417090,E,1,21,0.70,74.303,M,-1.770,M,,*57

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,234,40,3,63,190,40,24,60,356,41,33,60,313,42,1*46

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,40,25,37,263,41,1*74

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,38,1*7E

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,3,2,09,25,37,263,40,38,36,190,38,40,27,160,35,26,22,42,36,5*40

$GBGSV,3,3,09,41,11,324,28,5*41

$GBRMC,120134.000,A,2301.2578921,N,11421.9417090,E,0.003,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

[W][05:18:50][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120134.000,1.839,0.220,0.205,0.299,1.366,1.442,2.468*7F



2025-07-31 20:01:35:383 ==>> $GBGGA,120135.000,2301.2579018,N,11421.9417047,E,1,21,0.70,74.255,M,-1.770,M,,*5C

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.34,0.70,1.15,4*04

$GBGSA,A,3,08,01,60,25,38,40,10,26,41,,,,1.34,0.70,1.15,4*0B

$GBGSV,6,1,24,14,83,233,40,3,63,190,40,24,60,356,41,33,60,313,42,1*41

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,38,1*47

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,39,25,37,263,40,1*7B

$GBGSV,6,5,24,38,36,190,38,7,34,184,34,4,32,112,33,40,27,160,37,1*71

$GBGSV,6,6,24,10,24,186,32,26,22,42,36,5,22,257,34,41,11,324,35,1*7A

$GBGSV,3,1,09,24,60,356,43,33,60,313,43,42,58,157,43,39,52,1,41,5*78

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120135.000,A,2301.2579018,N,11421.9417047,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:51][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120135.000,1.873,0.247,0.226,0.336,1.388,1.459,2.452*77



2025-07-31 20:01:36:143 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:01:36:149 ==>> 检测【检测WiFi结果】
2025-07-31 20:01:36:174 ==>> WiFi信号:【CC057790A5C0】,信号值:-72
2025-07-31 20:01:36:182 ==>> WiFi信号:【CC057790A5C1】,信号值:-75
2025-07-31 20:01:36:189 ==>> WiFi信号:【CC057790A6E1】,信号值:-75
2025-07-31 20:01:36:197 ==>> WiFi信号:【CC057790A7C1】,信号值:-81
2025-07-31 20:01:36:219 ==>> WiFi信号:【CC057790A7C0】,信号值:-78
2025-07-31 20:01:36:226 ==>> WiFi信号:【F86FB0660A82】,信号值:-82
2025-07-31 20:01:36:249 ==>> WiFi信号:【F88C21BCF57D】,信号值:-31
2025-07-31 20:01:36:260 ==>> WiFi信号:【CC057790A740】,信号值:-69
2025-07-31 20:01:36:286 ==>> WiFi数量【8】, 最大信号值:-31
2025-07-31 20:01:36:298 ==>> 检测【检测GPS结果】
2025-07-31 20:01:36:314 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 20:01:36:327 ==>> 
北斗星号:【14】,信号值:【40】
北斗星号:【3】,信号值:【40】
北斗星号:【24】,信号值:【42】
北斗星号:【33】,信号值:【42】
北斗星号:【42】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【38】
北斗星号:【59】,信号值:【41】
北斗星号:【39】,信号值:【39】
北斗星号:【13】,信号值:【39】
北斗星号:【9】,信号值:【35】
北斗星号:【1】,信号值:【38】
北斗星号:【8】,信号值:【36】
北斗星号:【2】,信号值:【37】
北斗星号:【60】,信号值:【40】
北斗星号:【25】,信号值:【41】
北斗星号:【38】,信号值:【38】
北斗星号:【40】,信号值:【37】
北斗星号:【26】,信号值:【36】
北斗星号:【41】,信号值:【36】

2025-07-31 20:01:36:344 ==>> 检测【CSQ强度】
2025-07-31 20:01:36:361 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:01:36:373 ==>> [D][05:18:51][PROT]CLEAN,SEND:4
[D][05:18:51][PROT]CLEAN:4
[D][05:18:51][PROT]index:1 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:5
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:10
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900006]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][COMM]read battery soc:255
[D][05:18:51][CAT1]Send Data To Server[198][201] ... -

2025-07-31 20:01:36:389 ==>> >:
0063B981113311331133113311331B88B383AA6F619E567FACC4D9836BBACD0B99411D2A7D46BBEFBC5069F5AC7B5365904EFD0548E49C86416CDE2197EF4050F888507417583E2C0622826B5A04F87DEBE8586977A9F65BF3D9E5CD7DA249BE58D756
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:01:36:428 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:01:37:403 ==>> $GBGGA,120137.000,2301.2579050,N,11421.9417035,E,1,22,0.69,74.211,M,-1.770,M,,*5C

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.32,0.69,1.13,4*0C

$GBGSA,A,3,08,01,60,25,38,07,40,10,26,41,,,1.32,0.69,1.13,4*04

$GBGSV,6,1,24,14,83,233,40,3,63,190,40,24,60,356,41,33,60,313,42,1*41

$GBGSV,6,2,24,42,58,157,41,6,54,336,35,16,53,340,38,39,52,1,38,1*44

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,39,25,37,263,40,1*7B

$GBGSV,6,5,24,38,36,190,38,4,32,112,33,7,32,174,34,40,27,160,37,1*78

$GBGSV,6,6,24,10,25,186,32,26,22,42,36,5,22,257,34,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,40,5*7E

$GBGSV,3,2,09,25,37,263,39,38,36,190,37,40,27,160,35,26,22,42,36,5*41

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120137.000,A,2301.2579050,N,11421.9417035,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[W][05:18:53][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120137.000,1.710,0.205,0.190,0.280,1.264,1.331,2.295*7C



2025-07-31 20:01:38:043 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:01:38:242 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:01:38:393 ==>> $GBGGA,120138.000,2301.2579029,N,11421.9417138,E,1,22,0.69,74.222,M,-1.770,M,,*51

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.32,0.69,1.13,4*0C

$GBGSA,A,3,08,01,60,25,38,07,40,10,26,41,,,1.32,0.69,1.13,4*04

$GBGSV,6,1,24,14,83,233,41,3,63,190,41,24,60,356,41,33,60,313,42,1*41

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,38,1*47

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,39,25,37,263,40,1*7B

$GBGSV,6,5,24,38,36,190,38,4,32,112,33,7,32,174,34,40,27,160,37,1*78

$GBGSV,6,6,24,10,25,186,32,26,22,42,36,5,22,257,35,41,11,324,35,1*7A

$GBGSV,3,1,09,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120138.000,A,2301.2579029,N,11421.9417138,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:54][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120138.000,1.673,0.222,0.205,0.301,1.235,1.299,2.246*7C



2025-07-31 20:01:38:498 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:54][CAT1]gsm read msg s

2025-07-31 20:01:38:528 ==>> ub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:54][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:54][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:01:38:781 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 20:01:38:788 ==>> 检测【关闭GSM联网】
2025-07-31 20:01:38:797 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:01:38:926 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:54][COMM]GSM test
[D][05:18:54][COMM]GSM test disable


2025-07-31 20:01:39:065 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:01:39:073 ==>> 检测【4G联网测试】
2025-07-31 20:01:39:084 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:01:40:079 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:55][COMM]Main Task receive event:14
[D][05:18:55][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955135, allstateRepSeconds = 0
[D][05:18:55][COMM]index:0,power_mode:0xFF
[D][05:18:55][COMM]index:1,sound_mode:0xFF
[D][05:18:55][COMM]index:2,gsensor_mode:0xFF
[D][05:18:55][COMM]index:3,report_freq_mode:0xFF
[D][05:18:55][COMM]index:4,report_period:0xFF
[D][05:18:55][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:55][COMM]index:6,normal_reset_period:0xFF
[D][05:18:55][COMM]index:7,spock_over_speed:0xFF
[D][05:18:55][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:55][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:55][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:55][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:55][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:55][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:55][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:55][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:55][COMM]index:16,imu_config_params:0xFF
[D][05:18:55][COMM]index:17,long_connect_params:0xFF
[D][05:18:55][COMM]index:18,detain_mark:0xFF
[D][05:18:55][COMM]index:19,lock_pos_report_count:0xFF


2025-07-31 20:01:40:184 ==>> 
[D][05:18:55][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:55][COMM]index:21,mc_mode:0xFF
[D][05:18:55][COMM]index:22,S_mode:0xFF
[D][05:18:55][COMM]index:23,overweight:0xFF
[D][05:18:55][COMM]index:24,standstill_mode:0xFF
[D][05:18:55][COMM]index:25,night_mode:0xFF
[D][05:18:55][COMM]index:26,experiment1:0xFF
[D][05:18:55][COMM]index:27,experiment2:0xFF
[D][05:18:55][COMM]index:28,experiment3:0xFF
[D][05:18:55][COMM]index:29,experiment4:0xFF
[D][05:18:55][COMM]index:30,night_mode_start:0xFF
[D][05:18:55][COMM]index:31,night_mode_end:0xFF
[D][05:18:55][COMM]index:33,park_report_minutes:0xFF
[D][05:18:55][COMM]index:34,park_report_mode:0xFF
[D][05:18:55][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:55][COMM]index:38,charge_battery_para: FF
[D][05:18:55][COMM]index:39,multirider_mode:0xFF
[D][05:18:55][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:55][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:55][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:55][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:55][COMM]index:44,riding_duration_config:0xFF
[D][05:18:55][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:55][COMM]index:46,c

2025-07-31 20:01:40:289 ==>> amera_park_type_cfg:0xFF
[D][05:18:55][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:55][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:55][COMM]index:49,mc_load_startup:0xFF
[D][05:18:55][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:55][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:55][COMM]index:52,traffic_mode:0xFF
[D][05:18:55][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:55][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:55][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:55][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:55][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:55][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:55][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:55][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:55][COMM]index:63,experiment5:0xFF
[D][05:18:55][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:55][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:55][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:55][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:55][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:55][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:55][COMM]index:71

2025-07-31 20:01:40:394 ==>> ,camera_park_self_check_cfg:0xFF
[D][05:18:55][COMM]index:72,experiment6:0xFF
[D][05:18:55][COMM]index:73,experiment7:0xFF
[D][05:18:55][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:55][COMM]index:75,zero_value_from_server:-1
[D][05:18:55][COMM]index:76,multirider_threshold:255
[D][05:18:55][COMM]index:77,experiment8:255
[D][05:18:55][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:55][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:55][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:55][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:55][COMM]index:83,loc_report_interval:255
[D][05:18:55][COMM]index:84,multirider_threshold_p2:255
[D][05:18:55][COMM]index:85,multirider_strategy:255
[D][05:18:55][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:55][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:55][COMM]index:90,weight_param:0xFF
[D][05:18:55][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:55][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:55][COMM]index:95,current_limit:0xFF
[D][05:18:55][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:55][COMM]index:100,locat

2025-07-31 20:01:40:499 ==>> ion_mode:0xFF

[W][05:18:55][PROT]remove success[1629955135],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:55][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
$GBGGA,120139.000,2301.2578943,N,11421.9417143,E,1,22,0.69,74.256,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.32,0.69,1.13,4*0C

$GBGSA,A,3,08,01,60,25,38,07,40,10,26,41,,,1.32,0.69,1.13,4*04

$GBGSV,6,1,24,14,83,233,40,3,63,190,40,24,60,356,41,33,60,313,42,1*41

$GBGSV,6,2,24,42,58,157,41,6,54,336,36,16,53,340,38,39,52,1,39,1*46

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,39,25,37,263,40,1*7B

$GBGSV,6,5,24,38,36,190,38,4,32,112,33,7,32,174,35,40,27,160,37,1*79

[D][05:18:55][PROT]index:0 1629955135
[D][05:18:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:55][PROT]is_send:0
[D][05:18:55][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:55][PROT]sequence_num:10
[D][05:18:55][PROT]retry_timeout:0
[D][05:18:55][PROT]retry_times:1
[D][05:18:55][PROT]send_path:0x2
[D][05:18:55][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:55][PROT]=================================

2025-07-31 20:01:40:604 ==>> ==========================
$GBGSV,6,6,24,10,25,186,32,26,22,42,36,5,22,257,34,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,40,5*7E

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120139.000,A,2301.2578943,N,11421.9417143,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:55][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955135]
[D][05:18:55][PROT]===========================================================
[D][05:18:55][PROT]sending traceid [9999999999900009]
[D][05:18:55][PROT]Send_TO_M2M [1629955135]
[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:55][SAL ]sock send credit cnt[6]
[D][05:18:55][SAL ]sock send ind credit cnt[6]
[D][05:18:55][M2M ]m2m send data len[294]
[D][05:18:55][SAL ]Cellular task submsg id[10]
[D][05:18:55][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[W][05:18:55][PROT]add success [1629955135],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:18:55][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120139.000,1.684,0.231,0.211,0.314,1.242,1.30

2025-07-31 20:01:40:709 ==>> 3,2.229*7D

[D][05:18:55][CAT1]gsm read msg sub id: 15
[D][05:18:55][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:55][CAT1]Send Data To Server[294][297] ... ->:
0093B982113311331133113311331B88B10AB546FD1003DEE9FA5A271C9E0797CE70AE7533AB24E71D2A01ADAACA627D05668BC81540210B08EA13F1CC650D8FD5DD8566B37DF27D615C740E30F3CC383D198E85959FEA800858997A8D13757535A9682382031ED324DB7A99C2D58212FDAE40AEC8EF0260AC66B159C3C70DB314666E6C1F1C51B4C9D43800F55E140449431B
[D][05:18:55][CAT1]<<< 
SEND OK

[D][05:18:55][CAT1]exec over: func id: 15, ret: 11
[D][05:18:55][CAT1]sub id: 15, ret: 11

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:55][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:55][CAT1]gsm read msg sub id: 13
[D][05:18:55][CAT1]tx ret[8] >>> AT+CSQ

[D][05:1

2025-07-31 20:01:40:814 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:01:41:121 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:01:41:129 ==>> 检测【关闭GPS】
2025-07-31 20:01:41:139 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:01:41:511 ==>> $GBGGA,120141.000,2301.2578905,N,11421.9417170,E,1,22,0.69,74.245,M,-1.770,M,,*54

$GBGSA,A,3,14,03,24,33,42,06,16,39,59,13,09,02,1.32,0.69,1.13,4*0C

$GBGSA,A,3,08,01,60,25,38,07,40,10,26,41,,,1.32,0.69,1.13,4*04

$GBGSV,6,1,24,14,83,233,41,3,63,190,40,24,60,356,41,33,60,313,42,1*40

$GBGSV,6,2,24,42,58,157,41,6,54,336,35,16,53,340,38,39,52,1,39,1*45

$GBGSV,6,3,24,59,51,128,41,13,51,221,39,9,49,313,35,2,48,240,36,1*70

$GBGSV,6,4,24,8,47,206,36,1,46,124,38,60,43,241,39,25,37,263,40,1*7B

$GBGSV,6,5,24,38,36,190,38,4,32,112,33,7,32,174,35,40,27,160,37,1*79

$GBGSV,6,6,24,10,25,186,32,26,22,42,36,5,22,257,34,41,11,324,35,1*7B

$GBGSV,3,1,09,24,60,356,44,33,60,313,43,42,58,157,43,39,52,1,41,5*7F

$GBGSV,3,2,09,25,37,263,39,38,36,190,38,40,27,160,35,26,22,42,36,5*4E

$GBGSV,3,3,09,41,11,324,29,5*40

$GBRMC,120141.000,A,2301.2578905,N,11421.9417170,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[W][05:18:57][GNSS]single mode encounter continous mode, immediately report.
$GBGST,120141.000,1.725,0.211,0.195,0.287,1.270,1.326,2.214*76

[W][05:18:57][COMM]>>>>>Input command = AT+GPSLO

2025-07-31 20:01:41:586 ==>> G=0<<<<<
[W][05:18:57][GNSS]stop locating
[D][05:18:57][GNSS]stop event:8
[D][05:18:57][GNSS]GPS stop. ret=0
[D][05:18:57][GNSS]all continue location stop
[W][05:18:57][GNSS]sing locating running
[W][05:18:57][GNSS]stop locating
[D][05:18:57][GNSS]all sing location stop
[D][05:18:57][CAT1]gsm read msg sub id: 24
[D][05:18:57][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 24, ret: 6
[D][05:18:57][CAT1]sub id: 24, ret: 6



2025-07-31 20:01:41:650 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:01:41:656 ==>> 检测【清空消息队列2】
2025-07-31 20:01:41:662 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:01:41:831 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:57][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:01:41:925 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:01:41:931 ==>> 检测【轮动检测】
2025-07-31 20:01:41:944 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:01:42:043 ==>> 3A A3 01 00 A3 


2025-07-31 20:01:42:148 ==>> [D][05:18:57][COMM]read battery soc:255
[D][05:18:57][GNSS]recv submsg id[1]
[D][05:18:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:57][GNSS]location stop evt done evt
OFF_OUT1
OVER 150


2025-07-31 20:01:42:223 ==>> [D][05:18:58][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:01:42:438 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:01:42:545 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:01:42:715 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:01:42:727 ==>> 检测【关闭小电池】
2025-07-31 20:01:42:748 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:01:42:837 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:01:42:992 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:01:42:999 ==>> 检测【进入休眠模式】
2025-07-31 20:01:43:006 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:01:43:255 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:18:59][COMM]Main Task receive event:28
[D][05:18:59][COMM]main task tmp_sleep_event = 8
[D][05:18:59][COMM]prepare to sleep
[D][05:18:59][CAT1]gsm read msg sub id: 12
[D][05:18:59][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:01:44:144 ==>> [D][05:18:59][CAT1]<<< 
OK

[D][05:18:59][CAT1]exec over: func id: 12, ret: 6
[D][05:18:59][M2M ]tcpclient close[4]
[D][05:18:59][SAL ]Cellular task submsg id[12]
[D][05:18:59][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:18:59][CAT1]gsm read msg sub id: 9
[D][05:18:59][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:59][CAT1]<<< 
OK

[D][05:18:59][CAT1]exec over: func id: 9, ret: 6
[D][05:18:59][CAT1]sub id: 9, ret: 6

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:59][SAL ]socket close ind. id[4]
[D][05:18:59][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:59][COMM]1x1 frm_can_tp_send ok
[D][05:18:59][CAT1]pdpdeact urc len[22]
[D][05:18:59][COMM]read battery soc:255


2025-07-31 20:01:44:404 ==>> [E][05:19:00][COMM]1x1 rx timeout
[D][05:19:00][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:01:44:923 ==>> [E][05:19:00][COMM]1x1 rx timeout
[D][05:19:00][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:00][COMM]1x1 tp timeout
[E][05:19:00][COMM]1x1 error -3.
[W][05:19:00][COMM]CAN STOP!
[D][05:19:00][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:00][COMM]------------ready to Power off Acckey 1------------
[D][05:19:00][COMM]------------ready to Power off Acckey 2------------
[D][05:19:00][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:00][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1299
[D][05:19:00][COMM]bat sleep fail, reason:-1
[D][05:19:00][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM]imu rest ok. 71659
[D][05:19:00][COMM]imu sleep 0
[W][05:19:00][COMM]now sleep


2025-07-31 20:01:45:095 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:01:45:102 ==>> 检测【检测33V休眠电流】
2025-07-31 20:01:45:113 ==>> 开始33V电流采样
2025-07-31 20:01:45:139 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:01:45:197 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:01:46:198 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:01:46:229 ==>> Current33V:????:18.35

2025-07-31 20:01:46:709 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:01:46:716 ==>> 【检测33V休眠电流】通过,【18.35uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:01:46:722 ==>> 该项需要延时执行
2025-07-31 20:01:48:731 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:01:48:742 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:01:48:768 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:01:48:840 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1643mV
Get AD_V4 1mV
Get AD_V5 2744mV
Get AD_V6 2023mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:01:49:761 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:01:49:775 ==>> 检测【打开小电池2】
2025-07-31 20:01:49:794 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:01:49:835 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:01:50:041 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:01:50:056 ==>> 该项需要延时执行
2025-07-31 20:01:50:551 ==>> 此处延时了:【500】毫秒
2025-07-31 20:01:50:563 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:01:50:583 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:01:50:628 ==>> 5A A5 02 5A A5 


2025-07-31 20:01:50:733 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:01:50:826 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:01:50:834 ==>> 该项需要延时执行
2025-07-31 20:01:51:339 ==>> 此处延时了:【500】毫秒
2025-07-31 20:01:51:354 ==>> 检测【进入休眠模式2】
2025-07-31 20:01:51:365 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:01:51:444 ==>> [D][05:19:07][COMM]------------ready to Power on Acckey 1------------
[D][05:19:07][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:07][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:07][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:07][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:07][COMM]----- get Acckey 1 and value:1------------
[W][05:19:07][COMM]CAN START!
[D][05:19:07][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:07][CAT1]gsm read msg sub id: 12
[D][05:19:07][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:07][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 

2025-07-31 20:01:51:504 ==>> 78091
[D][05:19:07][COMM][Audio]exec status ready.
[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]exec over: func id: 12, ret: 6
[D][05:19:07][COMM]imu wakeup ok. 78106
[D][05:19:07][COMM]imu wakeup 1
[W][05:19:07][COMM]wake up system, wakeupEvt=0x80
[D][05:19:07][COMM]frm_can_weigth_power_set 1
[D][05:19:07][COMM]Clear Sleep Block Evt
[D][05:19:07][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:01:51:579 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:01:51:654 ==>> [E][05:19:07][COMM]1x1 rx timeout
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:01:51:759 ==>> [D][05:19:07][COMM]msg 02A0 loss. last_tick:78077. cur_tick:78585. period:50
[D][05:19:07][COM

2025-07-31 20:01:51:819 ==>> M]msg 02A4 loss. last_tick:78077. cur_tick:78586. period:50
[D][05:19:07][COMM]msg 02A5 loss. last_tick:78077. cur_tick:78586. period:50
[D][05:19:07][COMM]msg 02A6 loss. last_tick:78077. cur_tick:78587. period:50
[D][05:19:07][COMM]msg 02A7 loss. last_tick:78077. cur_tick:78587. period:50
[D][05:19:07][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 78587
[D][05:19:07][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 78588


2025-07-31 20:01:52:153 ==>> [E][05:19:07][COMM]1x1 rx timeout
[E][05:19:07][COMM]1x1 tp timeout
[E][05:19:07][COMM]1x1 error -3.
[D][05:19:07][COMM]Main Task receive event:28 finished processing
[D][05:19:07][COMM]Main Task receive event:28
[D][05:19:07][COMM]prepare to sleep
[D][05:19:07][CAT1]gsm read msg sub id: 12
[D][05:19:07][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]exec over: func id: 12, ret: 6
[D][05:19:07][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:01:52:456 ==>> [D][05:19:08][COMM]msg 0220 loss. last_tick:78076. cur_tick:79081. period:100
[D][05:19:08][COMM]msg 0221 loss. last_tick:78077. cur_tick:79082. period:100
[D][05:19:08][COMM]msg 0224 loss. last_tick:78077. cur_tick:79082. period:100
[D][05:19:08][COMM]msg 0260 loss. last_tick:78077. cur_tick:79083. period:100
[D][05:19:08][COMM]msg 0280 loss. last_tick:78077. cur_tick:79083. period:100
[D][05:19:08][COMM]msg 02C0 loss. last_tick:78077. cur_tick:79083. period:100
[D][05:19:08][COMM]msg 02C1 loss. last_tick:78077. cur_tick:79084. period:100
[D][05:19:08][COMM]msg 02C2 loss. last_tick:78077. cur_tick:79084. period:100
[D][05:19:08][COMM]msg 02E0 loss. last_tick:78077. cur_tick:79085. period:100
[D][05:19:08][COMM]msg 02E1 loss. last_tick:78077. cur_tick:79085. period:100
[D][05:19:08][COMM]msg 02E2 loss. last_tick:78077. cur_tick:79085. period:100
[D][05:19:08][COMM]msg 0300 loss. last_tick:78077. cur_tick:79086. period:100
[D][05:19:08][COMM]msg 0301 loss. last_tick:78077. cur_tick:79086. period:100
[D][05:19:08][COMM]bat msg 0240 loss. last_tick:78077. cur_tick:79086. period:100. j,i:1 54
[D][05:19:08][COMM]bat msg 0241 loss. last_tick:78077. cur_tick:79087. period:100. j,i:2 55

2025-07-31 20:01:52:562 ==>> 
[D][05:19:08][COMM]bat msg 0242 loss. last_tick:78077. cur_tick:79087. period:100. j,i:3 56
[D][05:19:08][COMM]bat msg 0244 loss. last_tick:78077. cur_tick:79088. period:100. j,i:5 58
[D][05:19:08][COMM]bat msg 024E loss. last_tick:78077. cur_tick:79088. period:100. j,i:15 68
[D][05:19:08][COMM]bat msg 024F loss. last_tick:78077. cur_tick:79088. period:100. j,i:16 69
[D][05:19:08][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 79089
[D][05:19:08][COMM]CAN message bat fault change: 0x00000000->0x0001802E 79089
[D][05:19:08][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 79090
                                                                              

2025-07-31 20:01:52:741 ==>> [D][05:19:08][COMM]msg 0222 loss. last_tick:78077. cur_tick:79583. period:150
[D][05:19:08][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 79584


2025-07-31 20:01:52:831 ==>>                                                                                             _peripheral_device_poweroff type 16.... 
[D][05:19:08][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:01:53:043 ==>> [E][05:19:08][COMM]1x1 rx timeout
[E][05:19:08][COMM]1x1 tp timeout
[E][05:19:08][COMM]1x1 error -3.
[W][05:19:08][COMM]CAN STOP!
[D][05:19:08][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:08][COMM]------------ready to Power off Acckey 1------------
[D][05:19:08][COMM]------------ready to Power off Acckey 2------------
[D][05:19:08][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:08][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 106
[D][05:19:08][COMM]bat sleep fail, reason:-1
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:08][COMM]accel parse set 0
[D][05:19:08][COMM]imu rest ok. 79773
[D][05:19:08][COMM]imu sleep 0
[W][05:19:08][COMM]now sleep


2025-07-31 20:01:53:168 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:01:53:175 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:01:53:186 ==>> 开始小电池电流采样
2025-07-31 20:01:53:200 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:01:53:271 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:01:54:281 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:01:54:357 ==>> CurrentBattery:ƽ��:68.70

2025-07-31 20:01:54:783 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:01:54:790 ==>> 【检测小电池休眠电流】通过,【68.7uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:01:54:802 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:01:54:832 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:01:54:935 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:01:55:074 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:01:55:088 ==>> 该项需要延时执行
2025-07-31 20:01:55:175 ==>> [D][05:19:10][COMM]------------ready to Power on Acckey 1------------
[D][05:19:10][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:10][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:10][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:10][COMM]----- get Acckey 1 and value:1------------
[W][05:19:10][COMM]CAN START!
[D][05:19:10][CAT1]gsm read msg sub id: 12
[D][05:19:10][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:10][COMM]CAN message bat fault change: 0x0001802E->0x00000000 81873
[D][05:19:10][COMM][Audio]exec status ready.
[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[D][05:19:10][COMM]imu wakeup ok. 81887
[D][05:19:10][COMM]imu wakeup 1
[W][05:19:10][COMM]wake up system, wakeupEvt=0x80
[D][05:19:10][COMM]frm_can_weigth_power_set 1
[D][05:19:10][COMM]Clear Sleep Block Evt
[D][05:19:10][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:10][COMM]1x1 frm_can_tp_send ok
[D][05:19:10][COMM]read battery soc:0


2025-07-31 20:01:55:445 ==>> [E][05:19:11][COMM]1x1 rx timeout
[D][05:19:11][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:01:55:550 ==>> [D][05:19:11][COMM]msg 02A0 loss. last_tick:81856. cur_tick:82367. period:50
[D][05:19:11][COMM]msg 02A4 loss. last_tick:81856. cur_tick:82367. period:50
[D][05:19:11][COMM]msg 02A5 loss. last_tick:81856. cur_tick:8

2025-07-31 20:01:55:580 ==>> 此处延时了:【500】毫秒
2025-07-31 20:01:55:591 ==>> 检测【检测唤醒】
2025-07-31 20:01:55:616 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:01:55:644 ==>> 2368. period:50
[D][05:19:11][COMM]msg 02A6 loss. last_tick:81856. cur_tick:82368. period:50
[D][05:19:11][COMM]msg 02A7 loss. last_tick:81856. cur_tick:82368. period:50
[D][05:19:11][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 82369
[D][05:19:11][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 82369


2025-07-31 20:01:56:298 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:11][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:11][FCTY]==========Modules-nRF5340 ==========
[D][05:19:11][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:11][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:11][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:11][FCTY]DeviceID    = 460130071539175
[D][05:19:11][FCTY]HardwareID  = 867222087723302
[D][05:19:11][FCTY]MoBikeID    = 9999999999
[D][05:19:11][FCTY]LockID      = FFFFFFFFFF
[D][05:19:11][FCTY]BLEFWVersion= 105
[D][05:19:11][FCTY]BLEMacAddr   = D17E44DB44D9
[D][05:19:11][FCTY]Bat         = 3864 mv
[D][05:19:11][FCTY]Current     = 0 ma
[D][05:19:11][FCTY]VBUS        = 2600 mv
[D][05:19:11][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:11][FCTY]Ext battery vol = 32, adc = 1300
[D][05:19:11][FCTY]Acckey1 vol = 5493 mv, Acckey2 vol = 0 mv
[D][05:19:11][FCTY]Bike Type flag is invalied
[D][05:19:11][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:11][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:11][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:11][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:11][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:11][FCTY]CAT1_GNSS_

2025-07-31 20:01:56:383 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:01:56:393 ==>> 检测【关机】
2025-07-31 20:01:56:401 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:01:56:416 ==>> VERSION = V3465b5b1
[D][05:19:11][FCTY]Bat1         = 3844 mv
[D][05:19:11][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:11][FCTY]==========Modules-nRF5340 ==========
[D][05:19:11][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[E][05:19:11][COMM]1x1 rx timeout
[E][05:19:11][COMM]1x1 tp timeout
[E][05:19:11][COMM]1x1 error -3.
[D][05:19:11][COMM]Main Task receive event:28 finished processing
[D][05:19:11][COMM]Main Task receive event:65
[D][05:19:11][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:11][COMM]Main Task receive event:65 finished processing
[D][05:19:11][COMM]Main Task receive event:60
[D][05:19:11][COMM]smart_helmet_vol=255,255
[D][05:19:11][COMM]report elecbike
[W][05:19:11][PROT]remove success[1629955151],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:11][PROT]add success [1629955151],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:11][COMM]Main Task receive event:60 finished processing
[D][05:19:11][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:11][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:11][PROT]min_index:0, type:0x5D03, priority:3
[D][05:1

2025-07-31 20:01:56:508 ==>> 9:11][PROT]index:0
[D][05:19:11][PROT]is_send:1
[D][05:19:11][PROT]sequence_num:12
[D][05:19:11][PROT]retry_timeout:0
[D][05:19:11][PROT]retry_times:3
[D][05:19:11][PROT]send_path:0x3
[D][05:19:11][PROT]msg_type:0x5d03
[D][05:19:11][PROT]===========================================================
[W][05:19:11][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955151]
[D][05:19:11][PROT]===========================================================
[D][05:19:11][PROT]Sending traceid[999999999990000B]
[D][05:19:11][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:11][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:11][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:11][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:11][SAL ]open socket ind id[4], rst[0]
[D][05:19:11][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:11][SAL ]Cellular task submsg id[8]
[D][05:19:11][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:11][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05

2025-07-31 20:01:56:614 ==>> :19:11][CAT1]gsm read msg sub id: 8
[D][05:19:11][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:11][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:11][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:11][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:11][CAT1]<<< 
+CME ERROR: 100

[D][05:19:11][COMM]msg 0220 loss. last_tick:81855. cur_tick:82863. period:100
[D][05:19:11][COMM]msg 0221 loss. last_tick:81855. cur_tick:82864. period:100
[D][05:19:11][COMM]msg 0224 loss. last_tick:81855. cur_tick:82864. period:100
[D][05:19:11][COMM]msg 0260 loss. last_tick:81856. cur_tick:82864. period:100
[D][05:19:11][COMM]msg 0280 loss. last_tick:81856. cur_tick:82865. period:100
[D][05:19:11][COMM]msg 02C0 loss. last_tick:81856. cur_tick:82865. period:100
[D][05:19:11][COMM]msg 02C1 loss. last_tick:81856. cur_tick:82865. period:100
[D][05:19:11][COMM]msg 02C2 loss. last_tick:81856. cur_tick:82866. period:100
[D][05:19:11][COMM]msg 02E0 loss. last_tick:81856. cur_tick:82866. period:100
[D][05:19:11][COMM]msg 02E1 loss. last_tick:81856. cur_tick:82867. period:100
[D][05:19:11][COMM]msg 02E2 loss. last_tick:81856. cur_tick:82867. period:

2025-07-31 20:01:56:718 ==>> 100
[D][05:19:11][COMM]msg 0300 loss. last_tick:81856. cur_tick:82867. period:100
[D][05:19:11][COMM]msg 0301 loss. last_tick:81856. cur_tick:82868. period:100
[D][05:19:11][COMM]bat msg 0240 loss. last_tick:81856. cur_tick:82868. period:100. j,i:1 54
[D][05:19:11][COMM]bat msg 0241 loss. last_tick:81856. cur_tick:82868. period:100. j,i:2 55
[D][05:19:11][COMM]bat msg 0242 loss. last_tick:81856. cur_tick:82869. period:100. j,i:3 56
[D][05:19:11][COMM]bat msg 0244 loss. last_tick:81856. cur_tick:82869. period:100. j,i:5 58
[D][05:19:11][COMM]bat msg 024E loss. last_tick:81856. cur_tick:82869. period:100. j,i:15 68
[D][05:19:11][COMM]bat msg 024F loss. last_tick:81856. cur_tick:82870. period:100. j,i:16 69
[D][05:19:11][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 82870
[D][05:19:11][COMM]CAN message bat fault change: 0x00000000->0x0001802E 82871
[D][05:19:11][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 82871


2025-07-31 20:01:57:393 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:01:57:423 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:01:57:498 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  19:12][COMM]accel parse set 1
[D][05:19:12][COMM][Audio]mon:9,05:19:12
[D][05:19:12][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:12][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:12][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A3230

2025-07-31 20:01:57:603 ==>> 34380D0A0D0A4F4B0D0A
[D][05:19:12][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:12][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:12][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:12][COMM]Main Task receive event:65
[D][05:19:12][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:12][COMM]Main Task receive event:65 finished processing
[D][05:19:12][COMM]Main Task receive event:66
[D][05:19:12][COMM]Try to Auto Lock Bat
[D][05:19:12][COMM]Main Task receive event:66 finished processing
[D][05:19:12][COMM]Main Task receive event:60
[D][05:19:12][COMM]smart_helmet_vol=255,255
[D][05:19:12][COMM]BAT CAN get state1 Fail 204
[D][05:19:12][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:12][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:12][COMM]BAT CAN get soc Fail, 204
[D][05:19:12][COMM]BAT CAN get state2 fail 204
[D][05:19:12][COMM]get soh error
[E][05:19:12][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:12][COMM]report elecbike
[W][

2025-07-31 20:01:57:708 ==>> 05:19:12][PROT]remove success[1629955152],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:12][PROT]add success [1629955152],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:12][COMM]Main Task receive event:60 finished processing
[D][05:19:12][COMM]Main Task receive event:61
[D][05:19:12][COMM][D301]:type:3, trace id:280
[D][05:19:12][COMM]id[], hw[000
[D][05:19:12][COMM]get mcMaincircuitVolt error
[D][05:19:12][COMM]get mcSubcircuitVolt error
[D][05:19:12][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:12][COMM]BAT CAN get state1 Fail 204
[D][05:19:12][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:12][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:12][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:12][PROT]index:1
[D][05:19:12][PROT]is_send:1
[D][05:19:12][PROT]sequence_num:13
[D][05:19:12][PROT]retry_timeout:0
[D][05:19:12][PROT]retry_times:3
[D][05:19:12][PROT]send_path:0x3
[D][05:19:12][PROT]msg_type:0x5d03
[D][05:19:12][PROT]===========================================================
[W][05:19:12][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955152]
[D][05:19:12][PROT]========================================

2025-07-31 20:01:57:813 ==>> ===================
[D][05:19:12][PROT]Sending traceid[999999999990000C]
[D][05:19:12][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:12][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:12][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:12][COMM]Receive Bat Lock cmd 0
[D][05:19:12][COMM]VBUS is 1
[D][05:19:12][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:12][COMM]BAT CAN get soc Fail, 204
[D][05:19:12][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:12][COMM]BAT CAN get state2 fail 204
[D][05:19:12][COMM]get bat work mode err
[W][05:19:12][PROT]remove success[1629955152],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:12][PROT]add success [1629955152],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:12][COMM]Main Task receive event:61 finished processing
[D][05:19:12][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:12][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:12][COMM]f:[ec

2025-07-31 20:01:57:918 ==>> 800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:19:12][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:12][COMM]arm_hub_enable: hub power: 0
[D][05:19:12][HSDK]hexlog index save 0 3584 155 @ 0 : 0
[D][05:19:12][HSDK]write save hexlog index [0]
[D][05:19:12][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:12][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:12][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05

2025-07-31 20:01:57:993 ==>> :19:12][COMM]read battery soc:255
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:13][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:13][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:13][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 20:01:58:098 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:13][COMM]arm_hub_enable: hub power: 0
[D][05:19:13][HSDK]hexlog index save 0 3584 155 @ 0 : 0
[D][05:19:13][HSDK]write save hexlog index [0]
[D][05:19:13][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:13][F

2025-07-31 20:01:58:128 ==>> CTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:01:58:439 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:01:58:671 ==>> [W][05:19:14][COMM]Power Off
[W][05:19:14][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:14][COMM]arm_hub_enable: hub power: 0
[D][05:19:14][HSDK]hexlog index save 0 3584 155 @ 0 : 0
[D][05:19:14][HSDK]write save hexlog index [0]
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:01:58:720 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:01:58:732 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:01:58:741 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:01:58:836 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:01:58:992 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:01:59:001 ==>> 检测【检测小电池关机电流】
2025-07-31 20:01:59:051 ==>> 开始小电池电流采样
2025-07-31 20:01:59:071 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:01:59:095 ==>> [D][05:19:14][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:01:59:125 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:02:00:107 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:02:00:168 ==>> CurrentBattery:ƽ��:67.25

2025-07-31 20:02:00:611 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:02:00:618 ==>> 【检测小电池关机电流】通过,【67.25uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:02:00:910 ==>> MES过站成功
2025-07-31 20:02:00:923 ==>> #################### 【测试结束】 ####################
2025-07-31 20:02:00:950 ==>> 关闭5V供电
2025-07-31 20:02:00:957 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:02:01:038 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:02:01:947 ==>> 关闭5V供电成功
2025-07-31 20:02:01:960 ==>> 关闭33V供电
2025-07-31 20:02:01:985 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:02:02:040 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:02:02:949 ==>> 关闭33V供电成功
2025-07-31 20:02:02:960 ==>> 关闭3.7V供电
2025-07-31 20:02:02:986 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:02:03:041 ==>> 6A A6 02 A6 6A 


2025-07-31 20:02:03:133 ==>> Battery OFF
OVER 150


2025-07-31 20:02:03:957 ==>> 关闭3.7V供电成功
