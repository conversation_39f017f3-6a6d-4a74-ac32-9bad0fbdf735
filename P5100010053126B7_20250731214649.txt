2025-07-31 21:46:49:784 ==>> MES查站成功:
查站序号:P5100010053126B7验证通过
2025-07-31 21:46:49:793 ==>> 扫码结果:P5100010053126B7
2025-07-31 21:46:49:794 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:46:49:796 ==>> 测试参数版本:2024.10.11
2025-07-31 21:46:49:797 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:46:49:798 ==>> 检测【打开透传】
2025-07-31 21:46:49:800 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:46:49:891 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:46:50:213 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:46:50:230 ==>> 检测【检测接地电压】
2025-07-31 21:46:50:231 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:46:50:283 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:46:50:531 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:46:50:533 ==>> 检测【打开小电池】
2025-07-31 21:46:50:535 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:46:50:584 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:46:51:065 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:46:51:067 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:46:51:070 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:46:51:187 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:46:51:291 ==>> ?% 鳊ting 譟冨搧OS 萞?X擔醚 _19aVa  ?
6+?€7?`纈窤繾餰ss mo鋏

S€ 刪as氨[鐄ccess,   聸 p?€!0
vCW te鄐aon: 538    ?[n瑳?鐆 a 5

2025-07-31 21:46:51:322 ==>> H 躤t耾o?鐆 !  
V6譓禁?mo`e  
 醩A餪_comp躤鬳  

2025-07-31 21:46:51:370 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:46:51:372 ==>> 检测【等待设备启动】
2025-07-31 21:46:51:374 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:51:502 ==>> ?% {ス?竐嗳yr OS @ui躟 鋜\7.99-躢s1-a  繶X
階サ晛骠狒us: 坹	a?5 ? C]ana?€@c 鎢cc錽s.



2025-07-31 21:46:52:135 ==>> }um?:17:49][儶%X€S癬MO [A5A5], 噱Asn[ - ]
[][ 58a7:09][儶U?Cr ?       俒][ 5xa780y][儶照贬?晧附仌躱p 鐄``o怍8  k5


2025-07-31 21:46:52:210 ==>> }um?8q7:49][儶%
佈晛?ow,  C 甶躭 渙笧`錸
 

2025-07-31 21:46:52:408 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:52:604 ==>> um联?郝?鑝
=55u艀?囵 郺meou??

2025-07-31 21:46:53:072 ==>> 鮩?:q7:5 ][儶偱 囵 鄆meoup
[E][ 5xa785 ][僌MM]a餼 ?啾[Y
珷?ㄒ
敢?靘
z55u硼a 寜価?Xg\
玌?
躵5 ][儶A?€u?辠f V卆q 盶\K?!
 珋??!7r5 篬?霑羺?鎥olen,ge?譑?躱`e 屸郒噜湅?玌?
悸?鄊
{55u%闩? 趈Z}MA  鄝[W][ 5x!o85 竅 繭癩鄀move 鎢ccess[al`qq55 7 籋sen?郃嗬[b],饄噱[    篭`pao鈇魕[ ],in`e餥 ]H雜e腫 ]
玌?
紥?tm=u崟屯?l 銀55 7 ]Hs錸?郃嗬[`]H鄖`錥`` j]\`pao鈇鄖[ 籠in`屦[ 籋雜e腫a]


2025-07-31 21:46:53:436 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:54:236 ==>> }05:17:5a][ 繭騟mve 鎢ccess[1l`y955 o!],sen`?亖m佽a€鍋昺?  ?`忾騛魕[ ]H児羗呰q酝?m伕
€[W][ 58!785!籟€??嵟ss 垔 p
靟湑箒齺亖m羃\鄖`e[p   窰`鄎o騛魕[ ]H徂呐餥q]\use繹a]
 

2025-07-31 21:46:54:482 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:55:519 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:56:557 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:57:589 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:58:631 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:46:59:676 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:47:00:721 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:47:01:758 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:47:01:761 ==>> #################### 【测试结束】 ####################
2025-07-31 21:47:01:816 ==>> 关闭5V供电
2025-07-31 21:47:01:819 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:47:01:878 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:47:02:818 ==>> 关闭5V供电成功
2025-07-31 21:47:02:821 ==>> 关闭33V供电
2025-07-31 21:47:02:826 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:47:02:878 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:47:03:824 ==>> 关闭33V供电成功
2025-07-31 21:47:03:829 ==>> 关闭3.7V供电
2025-07-31 21:47:03:832 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:47:03:887 ==>> 6A A6 02 A6 6A 


2025-07-31 21:47:03:977 ==>> Battery OFF
OVER 150


2025-07-31 21:47:04:067 ==>>  

