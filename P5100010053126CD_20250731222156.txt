2025-07-31 22:21:56:621 ==>> MES查站成功:
查站序号:P5100010053126CD验证通过
2025-07-31 22:21:56:626 ==>> 扫码结果:P5100010053126CD
2025-07-31 22:21:56:628 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:21:56:629 ==>> 测试参数版本:2024.10.11
2025-07-31 22:21:56:631 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:21:56:633 ==>> 检测【打开透传】
2025-07-31 22:21:56:635 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:21:56:698 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:21:56:968 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:21:56:996 ==>> 检测【检测接地电压】
2025-07-31 22:21:56:997 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:21:57:101 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 22:21:57:272 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:21:57:274 ==>> 检测【打开小电池】
2025-07-31 22:21:57:277 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:21:57:392 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:21:57:545 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:21:57:548 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:21:57:551 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:21:57:606 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:21:57:822 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:21:57:824 ==>> 检测【等待设备启动】
2025-07-31 22:21:57:826 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:58:148 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:21:58:347 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:21:58:846 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:58:985 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:21:59:060 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM


2025-07-31 22:21:59:463 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:21:59:888 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:21:59:933 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:22:00:170 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:22:00:172 ==>> 检测【产品通信】
2025-07-31 22:22:00:173 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:22:00:363 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 22:22:00:442 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:22:00:444 ==>> 检测【初始化完成检测】
2025-07-31 22:22:00:471 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:22:00:634 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:22:00:712 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:22:00:716 ==>> 检测【关闭大灯控制1】
2025-07-31 22:22:00:719 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:22:00:888 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:22:00:978 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:01:005 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:22:01:008 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:22:01:010 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:22:01:236 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:22:01:283 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:22:01:285 ==>> 检测【关闭仪表供电】
2025-07-31 22:22:01:287 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:22:01:495 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:22:01:558 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:22:01:561 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:22:01:563 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:22:01:754 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:22:01:843 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:22:01:846 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:22:01:847 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:22:01:983 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:02:121 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:22:02:124 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:22:02:127 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:02:289 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:22:02:397 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:22:02:400 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:22:02:401 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:22:02:502 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:22:02:562 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 13


2025-07-31 22:22:02:652 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 22:22:02:670 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:22:02:674 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:22:02:677 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:22:02:802 ==>> 5A A5 03 5A A5 


2025-07-31 22:22:02:892 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 22:22:02:941 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:22:02:944 ==>> 该项需要延时执行
2025-07-31 22:22:02:997 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:03:537 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5014. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5015. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C

2025-07-31 22:22:03:567 ==>> 71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017


2025-07-31 22:22:03:998 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:04:164 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:22:04:644 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:

2025-07-31 22:22:04:749 ==>> 54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircui

2025-07-31 22:22:04:854 ==>> tVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_cu

2025-07-31 22:22:04:914 ==>> rrent_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
                                         

2025-07-31 22:22:05:020 ==>> [D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:05:867 ==>> [D][05:17:56][CAT1]power_urc_cb ret[5]


2025-07-31 22:22:06:032 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:06:685 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:22:06:946 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:22:06:949 ==>> 检测【33V输入电压ADC】
2025-07-31 22:22:06:962 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:07:223 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:2 mv
[D][05:17:57][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:22:07:484 ==>> 【33V输入电压ADC】通过,【32804mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:22:07:487 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:22:07:488 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:22:07:614 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2757mV
Get AD_V6 1990mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:22:07:779 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:07:795 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:22:07:801 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:07:803 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:22:07:806 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 22:22:07:821 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:22:07:823 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:22:07:840 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:22:07:843 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:22:07:864 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:22:07:866 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:22:08:011 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2757mV
Get AD_V6 1989mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:22:08:041 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:08:139 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:08:142 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:22:08:159 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:08:162 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:22:08:165 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 22:22:08:177 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:22:08:180 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:22:08:196 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:22:08:210 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:22:08:221 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:22:08:223 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:22:08:313 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2757mV
Get AD_V6 1990mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:22:08:388 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10004
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10004


2025-07-31 22:22:08:502 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:08:506 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:22:08:521 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:08:523 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:22:08:526 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 22:22:08:539 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:22:08:555 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:22:08:558 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:22:08:562 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:22:08:584 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:22:08:586 ==>> 检测【打开WIFI(1)】
2025-07-31 22:22:08:589 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:22:08:673 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:22:08:763 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:22:08:857 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:22:08:861 ==>> 检测【清空消息队列(1)】
2025-07-31 22:22:08:864 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:22:09:098 ==>> [D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:22:09:132 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:22:09:135 ==>> 检测【打开GPS(1)】
2025-07-31 22:22:09:136 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:22:09:296 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:17:59][COMM]Open GPS Module...
[D][05:17:59][COMM]LOC_MODEL_CONT
[D][05:17:59][GNSS]start event:8
[W][05:17:59][GNSS]start cont locating


2025-07-31 22:22:09:405 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:22:09:408 ==>> 检测【打开GSM联网】
2025-07-31 22:22:09:413 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:22:09:506 ==>> [D][05:18:00][CAT1]power_urc_cb ret[76]


2025-07-31 22:22:09:581 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:22:09:681 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:22:09:684 ==>> 检测【打开仪表供电1】
2025-07-31 22:22:09:688 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:22:10:046 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,

2025-07-31 22:22:10:106 ==>> 1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
                                         

2025-07-31 22:22:10:215 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:22:10:218 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:22:10:223 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:22:10:515 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087846129

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541221

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01

2025-07-31 22:22:10:545 ==>> ][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:22:10:681 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:22:10:744 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:22:10:749 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:22:10:754 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:22:10:893 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33270]


2025-07-31 22:22:11:015 ==>> 【读取主控ADC采集的仪表电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:22:11:018 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:22:11:020 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:22:11:207 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:22:11:285 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:22:11:288 ==>> 检测【AD_V20电压】
2025-07-31 22:22:11:293 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:22:11:390 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:22:11:513 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:22:11:665 ==>> 本次取值间隔时间:262ms
2025-07-31 22:22:11:686 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:22:11:788 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:22:11:898 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 22:22:12:018 ==>> 本次取值间隔时间:228ms
2025-07-31 22:22:12:037 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:12:042 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:22:12:067 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:22:12:069 ==>> [D][05:18:02][COMM]13727 imu init OK


2025-07-31 22:22:12:094 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:22:12:154 ==>> [D][05:18:02][COMM]S->M yaw:INVALID


2025-07-31 22:22:12:312 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:22:12:315 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:22:12:319 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:22:12:499 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:22:12:603 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:22:12:609 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:22:12:614 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:22:12:694 ==>> [D][05:18:03][COMM]read battery soc:255
3A A3 02 01 A3 


2025-07-31 22:22:12:799 ==>> ON_OUT2
OVER 150


2025-07-31 22:22:12:905 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:22:12:911 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:22:12:914 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:22:13:099 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:22:13:212 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:22:13:216 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:22:13:220 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:22:13:249 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:22:13:354 ==>> [W][05

2025-07-31 22:22:13:399 ==>> :18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:22:13:519 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:22:13:523 ==>> 检测【AD_V21电压】
2025-07-31 22:22:13:527 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:22:13:598 ==>> 1A A1 20 00 00 
Get AD_V21 1020mV
OVER 150


2025-07-31 22:22:13:839 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:22:13:944 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 22:22:13:959 ==>> 本次取值间隔时间:439ms
2025-07-31 22:22:14:004 ==>>                                                                                                                                                                                             [D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:22:14:008 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:22:14:094 ==>> 1A A1 20 00 00 
Get AD_V21 1644mV
OVER 150


2025-07-31 22:22:14:335 ==>>                       v submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:22:14:470 ==>> 本次取值间隔时间:457ms
2025-07-31 22:22:14:555 ==>> 【AD_V21电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:14:558 ==>> 检测【关闭仪表供电2】
2025-07-31 22:22:14:580 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:22:14:794 ==>> [D][05:18:05][COMM]read battery soc:255
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:22:14:833 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:22:14:836 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:22:14:840 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:22:15:039 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:22:15:103 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:22:15:116 ==>> 检测【打开AccKey2供电】
2025-07-31 22:22:15:119 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:22:15:269 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:22:15:377 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:22:15:381 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:22:15:386 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:15:764 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:06][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:06][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:06][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:06][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:06][COMM]adc read battery ts volt:0 mv
[D][05:18:06][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:06][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:22:15:869 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   [D

2025-07-31 22:22:15:899 ==>> ][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 22:22:15:914 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:22:15:917 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:22:15:919 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:22:16:096 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:22:16:196 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:22:16:199 ==>> 该项需要延时执行
2025-07-31 22:22:16:838 ==>> [D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,40,,,41,34,,,41,60,,,41,3,,,41,1*40

$GBGSV,4,2,14,33,,,40,25,,,40,59,,,40,41,,,40,1*7B

$GBGSV,4,3,14,39,,,38,24,,,38,7,,,38,23,,,37,1*41

$GBGSV,4,4,14,38,,,40,2,,,36,1*4B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1640.977,1640.977,52.415,2097152,2097152,2097152*48



2025-07-31 22:22:17:894 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,41,41,,,41,34,,,40,60,,,40,1*7B

$GBGSV,5,2,18,3,,,40,33,,,40,25,,,40,59,,,40,1*40

$GBGSV,5,3,18,39,,,39,7,,,39,24,,,38,23,,,37,1*4C

$GBGSV,5,4,18,1,,,37,44,,,35,2,,,34,5,,,34,1*4A

$GBGSV,5,5,18,43,,,34,4,,,32,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1568.499,1568.499,50.170,2097152,2097152,2097152*4C



2025-07-31 22:22:18:884 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,142222.704,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,41,,,41,34,,,41,3,,,41,1*42

$GBGSV,6,2,24,25,,,41,59,,,41,60,,,40,33,,,40,1*79

$GBGSV,6,3,24,39,,,40,7,,,39,1,,,39,24,,,38,1*70

$GBGSV,6,4,24,16,,,38,23,,,37,10,,,37,9,,,37,1*43

$GBGSV,6,5,24,44,,,36,2,,,35,43,,,35,6,,,35,1*73

$GBGSV,6,6,24,5,,,34,4,,,33,32,,,33,12,,,32,1*75

$GBRMC,142222.704,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142222.704,0.000,1561.596,1561.596,49.956,2097152,2097152,2097152*50



2025-07-31 22:22:19:204 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:22:19:208 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:22:19:213 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:19:506 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:10][COMM]adc read battery ts volt:0 mv
[D][05:18:10][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:22:19:687 ==>> $GBGGA,142223.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,34,,,41,3,,,41,1*42

$GBGSV,7,2,25,25,,,40,59,,,40,60,,,40,33,,,40,1*79

$GBGSV,7,3,25,39,,,40,7,,,39,1,,,39,24,,,38,1*70

$GBGSV,7,4,25,16,,,38,23,,,37,10,,,37,9,,,37,1*43

$GBGSV,7,5,25,11,,,37,44,,,36,43,,,36,2,,,35,1*44

$GBGSV,7,6,25,6,,,35,5,,,34,12,,,34,4,,,33,1*42

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,142223.504,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142223.504,0.000,1562.133,1562.133,49.958,2097152,2097152,2097152*5D



2025-07-31 22:22:19:736 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:22:19:741 ==>> 检测【打开AccKey1供电】
2025-07-31 22:22:19:745 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:22:19:881 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:22:20:006 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:22:20:010 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:22:20:015 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:22:20:093 ==>> 1A A1 00 40 00 
Get AD_V14 2649mV
OVER 150


2025-07-31 22:22:20:260 ==>> 原始值:【2649】, 乘以分压基数【2】还原值:【5298】
2025-07-31 22:22:20:279 ==>> 【读取AccKey1电压(ADV14)前】通过,【5298mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:22:20:283 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:22:20:287 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:20:607 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:2 mv
[D][05:18:11][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:22:20:682 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:22:20:787 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:22:20:817 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:22:20:821 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:22:20:824 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:22:20:982 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:22:21:090 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:22:21:094 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:22:21:098 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:22:21:194 ==>> 1A A1 00 40 00 
Get AD_V14 2651mV
OVER 150


2025-07-31 22:22:21:344 ==>> 原始值:【2651】, 乘以分压基数【2】还原值:【5302】
2025-07-31 22:22:21:362 ==>> 【读取AccKey1电压(ADV14)后】通过,【5302mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:22:21:366 ==>> 检测【打开WIFI(2)】
2025-07-31 22:22:21:372 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:22:21:710 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6
$GBGGA,142225.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,34,,,41,25,,,41,1*76

$GBGSV,7,2,25,59,,,41,3,,,40,60,,,40,33,,,40,1*4C

$GBGSV,7,3,25,39,,,40,7,,,39,1,,,39,24,,,39,1*71

$GBGSV,7,4,25,16,,,38,23,,,37,10,,,37,11,,,37,1*7A

$GBGSV,7,5,25,9,,,36,44,,,36,43,,,36,6,,,36,1*7B

$GBGSV,7,6,25,2,,,36,12,,,36,5,,,34,32,,,34,1*75

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,142225.504,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142225.504,0.000,1572.075,1572.075,50.267,2097152,2097152,2097152*54



2025-07-31 22:22:21:948 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:22:21:952 ==>> 检测【转刹把供电】
2025-07-31 22:22:21:968 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:22:22:056 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:22:22:251 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:22:22:255 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:22:22:258 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:22:22:358 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:22:22:723 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F42A7D1297A3,-68
+WIFISCAN:4,1,44A1917CA62B,-73
+WIFISCAN:4,2,CC057790A5C0,-78
+WIFISCAN:4,3,446747A08281,-87

[D][05:18:13][CAT1]wifi scan report total[4]
$GBGGA,142226.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,34,,,40,1*7F

$GBGSV,7,2,26,25,,,40,3,,,40,60,,,40,33,,,40,1*45

$GBGSV,7,3,26,39,,,40,7,,,39,24,,,39,1,,,38,1*73

$GBGSV,7,4,26,16,,,38,10,,,37,11,,,37,23,,,36,1*78

$GBGSV,7,5,26,9,,,36,44,,,36,43,,,36,6,,,36,1*78

$GBGSV,7,6,26,2,,,36,12,,,36,5,,,34,32,,,34,1*76

$GBGSV,7,7,26,4,,,33,38,,,30,1*4E

$GBRMC,142226.504,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142226.504,0.000,1553.085,1553.085,49.677,2097152,2097152,2097152*5A



2025-07-31 22:22:22:783 ==>>                                 soc:255


2025-07-31 22:22:23:093 ==>> [D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 22:22:23:306 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:22:23:411 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:22:23:456 ==>> [W][05:18:14][COMM]>>>>>Input command = ?<<<<


2025-07-31 22:22:23:501 ==>> 1A A1 00 80 00 
Get AD_V15 2387mV
OVER 150


2025-07-31 22:22:23:576 ==>> 原始值:【2387】, 乘以分压基数【2】还原值:【4774】
2025-07-31 22:22:23:599 ==>> 【读取AD_V15电压(前)】通过,【4774mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:22:23:603 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:22:23:608 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:22:23:681 ==>> $GBGGA,142227.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,25,,,41,41,,,40,59,,,40,1*7E

$GBGSV,7,2,26,34,,,40,3,,,40,60,,,40,33,,,40,1*45

$GBGSV,7,3,26,39,,,40,7,,,39,24,,,38,1,,,38,1*72

$GBGSV,7,4,26,16,,,38,10,,,37,11,,,37,23,,,36,1*78

$GBGSV,7,5,26,9,,,36,44,,,36,43,,,36,6,,,36,1*78

$GBGSV,7,6,26,12,,,36,2,,,35,5,,,34,32,,,34,1*75

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,142227.504,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142227.504,0.000,1549.889,1549.889,49.569,2097152,2097152,2097152*57



2025-07-31 22:22:23:711 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:22:23:786 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2420mV
OVER 150


2025-07-31 22:22:23:876 ==>> 原始值:【2420】, 乘以分压基数【2】还原值:【4840】
2025-07-31 22:22:23:912 ==>> 【读取AD_V16电压(前)】通过,【4840mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:22:23:918 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:22:23:940 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:24:217 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:3 mv
[D][05:18:14][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3085  volt:5422 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:22:24:457 ==>> 【转刹把供电电压(主控ADC)】通过,【5422mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:22:24:463 ==>> 检测【转刹把供电电压】
2025-07-31 22:22:24:468 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:24:825 ==>> $GBGGA,142228.504,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,3,,,41,1*4A

$GBGSV,7,2,26,25,,,40,34,,,40,60,,,40,33,,,40,1*71

$GBGSV,7,3,26,39,,,40,7,,,39,1,,,39,24,,,38,1*73

$GBGSV,7,4,26,16,,,38,10,,,37,11,,,37,23,,,36,1*78

$GBGSV,7,5,26,9,,,36,44,,,36,43,,,36,6,,,36,1*78

$GBGSV,7,6,26,12,,,36,2,,,35,5,,,34,32,,,34,1*75

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,142228.504,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142228.504,0.000,1554.678,1554.678,49.726,2097152,2097152,2097152*51

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:7 mv
[D][05:18:15][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3089  volt:5429 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:15][COMM]arm_hub adc read l

2025-07-31 22:22:24:884 ==>> ed yb adc:1436  volt:33293 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 22:22:24:993 ==>> 【转刹把供电电压】通过,【5429mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:22:24:997 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:22:25:001 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:25:176 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:22:25:269 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:22:25:273 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:22:25:299 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:25:371 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:22:25:476 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:25:585 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:22:25:693 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:25:723 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,142229.504,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,3,,,41,1*4A

$GBGSV,7,2,26,25,,,41,34,,,41,60,,,40,33,,,40,1*71

$GBGSV,7,3,26,39,,,40,7,,,39,1,,,39,24,,,38,1*73

$GBGSV,7,4,26,16,,,38,10,,,37,11,,,37,23,,,37,1*79

$GBGSV,7,5,26,12,,,37,9,,,36,44,,,36,43,,,36,1*4C

$GBGSV,7,6,26,6,,,36,2,,,36,5,,,34,32,,,34,1*43

$GBGSV,7,7,26,4,,,34,38,,,31,1*48

$GBRMC,142229.504,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142229.504,0.000,1564.243,1564.243,50.030,2097152,2097152,2097152*58

[W][05:18:16][COMM]>>>>>Input command = ?<<<<


2025-07-31 22:22:25:799 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:22:25:858 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<


2025-07-31 22:22:25:903 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:22:25:928 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:22:25:934 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:22:25:952 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:22:26:039 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:22:26:068 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:22:26:098 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 22:22:26:175 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:22:26:180 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:22:26:186 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:22:26:296 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:22:26:450 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:22:26:456 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:22:26:467 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:22:26:492 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:22:26:687 ==>> $GBGGA,142230.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,59,,,41,3,,,41,1*4A

$GBGSV,7,2,26,25,,,41,34,,,41,60,,,40,33,,,40,1*71

$GBGSV,7,3,26,39,,,40,7,,,39,1,,,39,24,,,38,1*73

$GBGSV,7,4,26,16,,,38,10,,,37,11,,,37,12,,,37,1*7B

$GBGSV,7,5,26,23,,,36,9,,,36,44,,,36,43,,,36,1*4F

$GBGSV,7,6,26,6,,,36,2,,,36,5,,,34,32,,,34,1*43

$GBGSV,7,7,26,4,,,34,38,,,32,1*4B

$GBRMC,142230.504,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142230.504,0.000,1564.239,1564.239,50.026,2097152,2097152,2097152*57



2025-07-31 22:22:26:731 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:22:26:734 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:22:26:737 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:22:26:792 ==>> [D][05:18:17][COMM]read battery soc:255
3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:22:27:011 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:22:27:017 ==>> 检测【左刹电压测试1】
2025-07-31 22:22:27:038 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:27:312 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:1732  volt:2283 mv
[D][05:18:17][COMM]adc read right brake adc:1730  volt:2280 mv
[D][05:18:17][COMM]adc read throttle adc:1722  volt:2270 mv
[D][05:18:17][COMM]adc read battery ts volt:0 mv
[D][05:18:17][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:17][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:22:27:541 ==>> 【左刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 22:22:27:547 ==>> 检测【右刹电压测试1】
2025-07-31 22:22:27:570 ==>> 【右刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 22:22:27:574 ==>> 检测【转把电压测试1】
2025-07-31 22:22:27:588 ==>> 【转把电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 22:22:27:593 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:22:27:602 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:22:27:719 ==>> $GBGGA,142231.504,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,41,3,,,41,1*4B

$GBGSV,7,2,27,25,,,41,34,,,41,60,,,40,33,,,40,1*70

$GBGSV,7,3,27,39,,,40,7,,,39,1,,,39,24,,,39,1*73

$GBGSV,7,4,27,16,,,38,10,,,37,11,,,37,12,,,37,1*7A

$GBGSV,7,5,27,23,,,37,9,,,36,44,,,36,43,,,36,1*4F

$GBGSV,7,6,27,6,,,36,2,,,36,32,,,35,5,,,34,1*43

$GBGSV,7,7,27,4,,,34,38,,,32,14,,,18,1*46

$GBRMC,142231.504,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142231.504,0.000,767.926,767.926,702.292,2097152,2097152,2097152*6B

3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:22:27:857 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:22:27:864 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:22:27:870 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:22:28:006 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:22:28:129 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:22:28:134 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:22:28:140 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:22:28:202 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:22:28:401 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:22:28:415 ==>> 检测【左刹电压测试2】
2025-07-31 22:22:28:421 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:22:28:779 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:10 mv
[D][05:18:19][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
$GBGGA,142232.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,59,,,41,34,,,41,1*46

$GBGSV,7,2,28,25,,,41,41,,,41,60,,,40,39,,,40,1*77

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,35,32,,,35,1*4F

$GBGSV,7,7,28,4,,,34,14,,,33,13,,,32,38,,,32,1*43

$GBRMC,142232.504,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0

2025-07-31 22:22:28:839 ==>> .00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142232.504,0.000,774.407,774.407,708.212,2097152,2097152,2097152*6A

[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
                                         

2025-07-31 22:22:28:933 ==>> 【左刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 22:22:28:937 ==>> 检测【右刹电压测试2】
2025-07-31 22:22:28:962 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:22:28:969 ==>> 检测【转把电压测试2】
2025-07-31 22:22:28:992 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:22:28:996 ==>> 检测【晶振检测】
2025-07-31 22:22:29:007 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:22:29:191 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 22:22:29:270 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:22:29:274 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:22:29:279 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:22:29:416 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1650mV
Get AD_V5 2758mV
Get AD_V6 1987mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:22:29:540 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:22:29:544 ==>> 检测【检测BootVer】
2025-07-31 22:22:29:549 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:29:863 ==>> $GBGGA,142233.504,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,3,,,41,40,,,41,59,,,41,41,,,41,1*44

$GBGSV,7,2,28,60,,,40,39,,,40,34,,,40,25,,,40,1*75

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,9,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,34,1*7A

$GBGSV,7,7,28,32,,,34,14,,,33,13,,,32,38,,,32,1*76

$GBRMC,142233.504,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142233.504,0.000,771.451,771.451,705.509,2097152,2097152,2097152*6B

[W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071541221
[D][05:18:20][FCTY]HardwareID  = 867222087846129
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:20][FCTY]Bat     

2025-07-31 22:22:29:953 ==>>     = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1296
[D][05:18:20][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3817 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:22:30:080 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:22:30:085 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:22:30:091 ==>> 检测【检测固件版本】
2025-07-31 22:22:30:112 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:22:30:117 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:22:30:122 ==>> 检测【检测蓝牙版本】
2025-07-31 22:22:30:181 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:22:30:187 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:22:30:191 ==>> 检测【检测MoBikeId】
2025-07-31 22:22:30:227 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:22:30:232 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:22:30:239 ==>> 检测【检测蓝牙地址】
2025-07-31 22:22:30:260 ==>> 取到目标值:C371E5CCDD92
2025-07-31 22:22:30:264 ==>> 【检测蓝牙地址】通过,【C371E5CCDD92】符合目标值【】要求!
2025-07-31 22:22:30:275 ==>> 提取到蓝牙地址:C371E5CCDD92
2025-07-31 22:22:30:279 ==>> 检测【BOARD_ID】
2025-07-31 22:22:30:291 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:22:30:295 ==>> 检测【检测充电电压】
2025-07-31 22:22:30:300 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:22:30:308 ==>> 检测【检测VBUS电压1】
2025-07-31 22:22:30:319 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:22:30:325 ==>> 检测【检测充电电流】
2025-07-31 22:22:30:338 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:22:30:345 ==>> 检测【检测IMEI】
2025-07-31 22:22:30:353 ==>> 取到目标值:867222087846129
2025-07-31 22:22:30:369 ==>> 【检测IMEI】通过,【867222087846129】符合目标值【】要求!
2025-07-31 22:22:30:373 ==>> 提取到IMEI:867222087846129
2025-07-31 22:22:30:378 ==>> 检测【检测IMSI】
2025-07-31 22:22:30:399 ==>> 取到目标值:460130071541221
2025-07-31 22:22:30:407 ==>> 【检测IMSI】通过,【460130071541221】符合目标值【】要求!
2025-07-31 22:22:30:412 ==>> 提取到IMSI:460130071541221
2025-07-31 22:22:30:441 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:22:30:448 ==>> 取到目标值:460130071541221
2025-07-31 22:22:30:454 ==>> 【校验网络运营商(移动)】通过,【460130071541221】符合目标值【】要求!
2025-07-31 22:22:30:462 ==>> 检测【打开CAN通信】
2025-07-31 22:22:30:486 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:22:30:500 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:22:30:711 ==>> $GBGGA,142234.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,3,,,41,40,,,41,25,,,41,41,,,41,1*4F

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,40,34,,,40,1*7E

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,6,,,36,1*7A

$GBGSV,7,6,28,9,,,36,43,,,36,5,,,34,4,,,34,1*42

$GBGSV,7,7,28,32,,,34,14,,,33,38,,,32,13,,,31,1*75

$GBRMC,142234.504,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142234.504,0.000,771.454,771.454,705.512,2097152,2097152,2097152*66



2025-07-31 22:22:30:760 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:22:30:765 ==>> 检测【检测CAN通信】
2025-07-31 22:22:30:775 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:22:30:816 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:22:30:891 ==>> can send success


2025-07-31 22:22:30:921 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:22:30:981 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32633
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:22:31:041 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:22:31:066 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:22:31:070 ==>> 检测【关闭CAN通信】
2025-07-31 22:22:31:087 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:22:31:101 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:22:31:206 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:22:31:372 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:22:31:387 ==>> 检测【打印IMU STATE】
2025-07-31 22:22:31:392 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:22:31:722 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,142235.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,34,,,41,25,,,41,1*4D

$GBGSV,7,2,28,41,,,41,60,,,40,59,,,40,39,,,40,1*7D

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,2,,,37,12,,,37,1*47

$GBGSV,7,5,28,11,,,37,23,,,37,44,,,36,9,,,36,1*46

$GBGSV,7,6,28,6,,,36,43,,,36,5,,,34,4,,,34,1*4D

$GBGSV,7,7,28,32,,,34,14,,,33,13,,,32,38,,,32,1*76

$GBRMC,142235.504,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142235.504,0.000,773.669,773.669,707.537,2097152,2097152,2097152*62



2025-07-31 22:22:31:933 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:22:31:940 ==>> 检测【六轴自检】
2025-07-31 22:22:31:947 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:22:32:112 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:22:32:731 ==>> $GBGGA,142236.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,25,,,41,41,,,41,60,,,40,1*7B

$GBGSV,7,2,28,3,,,40,59,,,40,39,,,40,34,,,40,1*4B

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,9,,,36,1*75

$GBGSV,7,6,28,6,,,36,43,,,36,5,,,34,4,,,34,1*4D

$GBGSV,7,7,28,32,,,34,14,,,33,13,,,32,38,,,32,1*76

$GBRMC,142236.504,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142236.504,0.000,771.448,771.449,705.506,2097152,2097152,2097152*60



2025-07-31 22:22:32:821 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:22:33:715 ==>> $GBGGA,142237.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,41,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,2,28,59,,,40,39,,,40,34,,,40,25,,,40,1*7F

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,2,,,36,44,,,36,9,,,36,6,,,36,1*43

$GBGSV,7,6,28,43,,,36,23,,,36,5,,,34,4,,,34,1*7A

$GBGSV,7,7,28,32,,,34,14,,,33,38,,,32,13,,,31,1*75

$GBRMC,142237.504,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142237.504,0.000,768.494,768.494,702.805,2097152,2097152,2097152*69



2025-07-31 22:22:33:806 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:22:33:956 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35598 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-12,-11,4051]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 22:22:34:020 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:22:34:049 ==>> 检测【打印IMU STATE2】
2025-07-31 22:22:34:057 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:22:34:196 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:22:34:296 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:22:34:302 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:22:34:310 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:22:34:406 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:22:34:511 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 15
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 15
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 15
[D][05:18:25][FCTY]get_e

2025-07-31 22:22:34:556 ==>> xt_48v_vol retry i = 3,volt = 15
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:22:34:574 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:22:34:579 ==>> 检测【检测VBUS电压2】
2025-07-31 22:22:34:583 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:34:661 ==>>                                                                                                                                                          ,,,40,39,,,40,25,,,40,1*7E

$GBGSV,7,3,28,33,,,40,7,,,39,1,,,39,24,,,38,1*77

$GBGSV,7,4,28,16,,,38,10,,,37,11,,,37,44,,,36,1*77

$GBGSV,7,5,28,9,,,36,6,,,3

2025-07-31 22:22:34:707 ==>> 6,12,,,36,43,,,36,1*75

$GBGSV,7,6,28,23,,,36,2,,,35,5,,,34,4,,,34,1*4C

$GBGSV,7,7,28,32,,,34,14,,,33,13,,,32,38,,,32,1*76

$GBRMC,142238.504,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142238.504,0.000,769.975,769.975,704.159,2097152,2097152,2097152*60



2025-07-31 22:22:34:995 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541221
[D][05:18:25][FCTY]HardwareID  = 867222087846129
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 7, adc = 297
[D][05:18:25][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 2

2025-07-31 22:22:35:040 ==>> 1.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3817 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:22:35:125 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:35:463 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541221
[D][05:18:25][FCTY]HardwareID  = 867222087846129
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 150 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 170
[D][05:18:25][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCT

2025-07-31 22:22:35:493 ==>> Y]Bat1         = 3817 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:22:35:668 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:35:705 ==>> $GBGGA,142239.504,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,41,3,,,41,59,,,41,34,,,41,1*46

$GBGSV,7,2,28,25,,,41,41,,,41,60,,,40,39,,,40,1*77

$GBGSV,7,3,28,33,,,40,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,28,16,,,38,10,,,37,12,,,37,11,,,37,1*75

$GBGSV,7,5,28,23,,,37,2,,,36,44,,,36,9,,,36,1*75

$GBGSV,7,6,28,6,,,36,43,,,36,5,,,34,4,,,34,1*4D

$GBGSV,7,7,28,32,,,34,14,,,33,13,,,32,38,,,32,1*76

$GBRMC,142239.504,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142239.504,0.000,772.932,772.932,706.863,2097152,2097152,2097152*63



2025-07-31 22:22:36:083 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541221
[D][05:18:26][FCTY]HardwareID  = 867222087846129
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 144
[D][05:18:26][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:2

2025-07-31 22:22:36:128 ==>> 6][FCTY]Bat1         = 3817 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]msg 0601 loss. last_tick:32624. cur_tick:37635. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 37635


2025-07-31 22:22:36:208 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:36:907 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071541221
[D][05:18:27][FCTY]HardwareID  = 867222087846129
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:27][FCTY]Bat         = 3864 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 6400 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 119
[D][05:18:27][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCT

2025-07-31 22:22:37:010 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:37:018 ==>> Y]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3817 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]i

2025-07-31 22:22:37:117 ==>> ndex:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[

2025-07-31 22:22:37:222 ==>> W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B58DB96ECDBB5A916CE8B2E82943E137095758B1084E1E29781

2025-07-31 22:22:37:297 ==>> 27E6D48933928897209A7DCC779B54EF559D82AC98A277C13831E1AA85A89067D6D18B42364422DE8F7CFE16EB488C3CDD35FE2B690D9B44487
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]


2025-07-31 22:22:37:567 ==>>                                                                                                                                            [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541221
[D][05:18:28][FCTY]HardwareID  = 867222087846129
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:28][FCTY]Bat         = 3864 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 5000 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 95
[D][05:18:28][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FC

2025-07-31 22:22:37:612 ==>> TY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3817 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:22:37:798 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:22:38:160 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541221
[D][05:18:28][FCTY]HardwareID  = 867222087846129
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C371E5CCDD92
[D][05:18:28][FCTY]Bat         = 3884 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 5000 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 97
[D][05:18:28][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCT

2025-07-31 22:22:38:203 ==>> Y]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3817 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:22:38:333 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:22:38:343 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:22:38:357 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:22:38:398 ==>> 5A A5 01 5A A5 


2025-07-31 22:22:38:503 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:22:38:563 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26


2025-07-31 22:22:38:624 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:22:38:629 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:22:38:634 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:22:38:654 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 22:22:38:698 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:22:38:898 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:22:38:903 ==>> 检测【打开WIFI(3)】
2025-07-31 22:22:38:911 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:22:39:113 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:22:39:177 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:22:39:182 ==>> 检测【扩展芯片hw】
2025-07-31 22:22:39:187 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:22:39:389 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:22:39:452 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:22:39:461 ==>> 检测【扩展芯片boot】
2025-07-31 22:22:39:483 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:22:39:497 ==>> 检测【扩展芯片sw】
2025-07-31 22:22:39:504 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:22:39:528 ==>> 检测【检测音频FLASH】
2025-07-31 22:22:39:532 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:22:39:662 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:22:39:933 ==>> +WIFISCAN:4,0,F42A7D1297A3,-68
+WIFISCAN:4,1,44A1917CA62F,-73
+WIFISCAN:4,2,CC057790A5C0,-78
+WIFISCAN:4,3,446747A08280,-85

[D][05:18:30][CAT1]wifi scan report total[4]


2025-07-31 22:22:40:878 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------
[D][05:18:30][GNSS]recv submsg id[3]
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f

2025-07-31 22:22:40:984 ==>> rm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event

2025-07-31 22:22:41:089 ==>> :66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:3

2025-07-31 22:22:41:194 ==>> 0][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][PROT]index:1
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900006]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][COMM]get bat work state err
[W][05:18

2025-07-31 22:22:41:299 ==>> :30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_pro

2025-07-31 22:22:41:404 ==>> cess].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:31][COMM]read battery soc:255


2025-07-31 22:22:42:042 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B378E87F944D4CD98DEB2CDA9459D7C3B5CE71159

2025-07-31 22:22:42:117 ==>> E57FD228EE239A813548EAA00ED5409D64D9352227146080A1A929FF94D60B2E8836F654B5391632EE5F82A4EB99242ADD74FB48F0C5FF162F9BFD8FAFED4
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 22:22:42:659 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:22:43:219 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:43:817 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 22:22:44:353 ==>> [D][05:18:34][COMM]45891 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:22:44:598 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:22:44:603 ==>> 检测【打开喇叭声音】
2025-07-31 22:22:44:608 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:22:44:684 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 22:22:45:319 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:35][COMM]read file, len:15228, num:4
[D][05:18:35][COMM]--->crc16:0x419c
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:

2025-07-31 22:22:45:383 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:22:45:394 ==>> 检测【打开大灯控制】
2025-07-31 22:22:45:404 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:22:45:428 ==>> 35][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:35][COMM]f:[ec800m_

2025-07-31 22:22:45:529 ==>> audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:35][COMM]46903 imu init OK


2025-07-31 22:22:45:604 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:22:45:653 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:22:45:661 ==>> 检测【关闭仪表供电3】
2025-07-31 22:22:45:683 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:22:45:880 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 22:22:45:928 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:22:45:934 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:22:45:941 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:22:46:060 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:22:46:203 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:22:46:209 ==>> 检测【读大灯电压】
2025-07-31 22:22:46:214 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:22:46:390 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33270]


2025-07-31 22:22:46:487 ==>> 【读大灯电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:22:46:494 ==>> 检测【关闭大灯控制2】
2025-07-31 22:22:46:515 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:22:46:691 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:18:37][COMM]read battery soc:255


2025-07-31 22:22:46:774 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:22:46:782 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:22:46:812 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:22:47:262 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[92]
[D][05:18:37][PROT]CLEAN,SEND:1
[D][05:18:37][PROT]index:1 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900006]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]Send Data To S

2025-07-31 22:22:47:312 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:22:47:321 ==>> 检测【打开WIFI(4)】
2025-07-31 22:22:47:341 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:22:47:348 ==>> erver[198][201] ... ->:
0063B98C113311331133113311331B88B3E68CFDCA15947B852BD87C057971C1C3C218A30A5CEBD0FED8566AB194D1E127F2D4BDF511170108936B5F176F056D5724A723988D31CD1AD366DCFD7AAEC797A4EC8C0060FA0C847C18B0074DFE20137656
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 22:22:47:518 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:22:47:641 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:22:47:650 ==>> 检测【EC800M模组版本】
2025-07-31 22:22:47:674 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:22:47:833 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:48:028 ==>> [D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:22:48:167 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:22:48:173 ==>> 检测【配置蓝牙地址】
2025-07-31 22:22:48:181 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:22:48:315 ==>> +WIFISCAN:4,0,F42A7D1297A3,-66
+WIFISCAN:4,1,44A1917CA62F,-73
+WIFISCAN:4,2,CC057790A5C0,-78
+WIFISCAN:4,3,CC057790A4A1,-84

[D][05:18:38][CAT1]wifi scan report total[4]


2025-07-31 22:22:48:375 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C371E5CCDD92>】
2025-07-31 22:22:48:384 ==>> [W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:22:48:602 ==>> recv ble 1
recv ble 2
ble set mac ok :c3,71,e5,cc,dd,92
enable filters ret : 0

2025-07-31 22:22:48:665 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:22:48:681 ==>> 检测【BLETEST】
2025-07-31 22:22:48:689 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:22:48:711 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:22:48:782 ==>> 4A A4 01 A4 4A 


2025-07-31 22:22:48:842 ==>> [D][05:18:39][COMM]50497 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:49:007 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C371E5CCDD92*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C371E5CCDD9299999

2025-07-31 22:22:49:097 ==>> OVER 150


2025-07-31 22:22:49:202 ==>> [D][05:18:39][GNSS]recv submsg id[3]


2025-07-31 22:22:49:693 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:22:49:699 ==>> 该项需要延时执行
2025-07-31 22:22:49:871 ==>> [D][05:18:40][COMM]51508 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:22:50:418 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:22:50:703 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 22:22:50:838 ==>> [D][05:18:41][COMM]52519 imu init OK


2025-07-31 22:22:52:484 ==>> [D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][PROT]index:1 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:1
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B30F9D5C5512B9AEBBE45DC042CEF27B740022CF729B0C5E74811F1BDE296F974E

2025-07-31 22:22:52:559 ==>> 71888614EDA2DF10D0A7DDC519F12EBBD563B691C8EF79AAE68635B38BD2AD6E4E96AC0791CD453A609206E756A7BB088B96
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]


2025-07-31 22:22:52:694 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 22:22:54:696 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:22:56:693 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:22:57:713 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:

2025-07-31 22:22:57:788 ==>> 48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5E2ABC379C51C3625F2C8A6EA1C2D7F360533D71A7C5F1375617741CAF75CF20391EE63F5FB8B0A3198B52E57B42722BFEFA1230F923AAA11A90B242A2BFF4BDDB25AE64D4A16DFC497CD78935E3C7BC29CEF
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]


2025-07-31 22:22:58:727 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:22:59:702 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:22:59:709 ==>> 检测【检测WiFi结果】
2025-07-31 22:22:59:733 ==>> WiFi信号:【F42A7D1297A3】,信号值:-68
2025-07-31 22:22:59:739 ==>> WiFi信号:【44A1917CA62B】,信号值:-73
2025-07-31 22:22:59:764 ==>> WiFi信号:【CC057790A5C0】,信号值:-78
2025-07-31 22:22:59:778 ==>> WiFi信号:【446747A08281】,信号值:-87
2025-07-31 22:22:59:787 ==>> WiFi信号:【44A1917CA62F】,信号值:-73
2025-07-31 22:22:59:811 ==>> WiFi信号:【446747A08280】,信号值:-85
2025-07-31 22:22:59:827 ==>> WiFi信号:【CC057790A4A1】,信号值:-84
2025-07-31 22:22:59:836 ==>> WiFi数量【7】, 最大信号值:-68
2025-07-31 22:22:59:862 ==>> 检测【检测GPS结果】
2025-07-31 22:22:59:877 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:22:59:899 ==>> [D][05:18:50][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all continue location stop
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all sing location stop


2025-07-31 22:23:00:721 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:23:00:731 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:00:755 ==>> 定位已等待【1】秒.
2025-07-31 22:23:00:764 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 22:23:01:111 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:51][COMM]Open GPS Module...
[D][05:18:51][COMM]LOC_MODEL_CONT
[D][05:18:51][GNSS]start event:8
[D][05:18:51][GNSS]GPS start. ret=0
[W][05:18:51][GNSS]start cont locating
[D][05:18:51][CAT1]gsm read msg sub id: 23
[D][05:18:51][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:51][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:23:01:726 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:01:737 ==>> 定位已等待【2】秒.
2025-07-31 22:23:01:816 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:23:02:484 ==>> [D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:23:02:679 ==>>     GA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,25,,,41,40,,,41,33,,,40,34,,,40,1*76

$GBGSV,2,2,07,41,,,40,39,,,35,11,,,37,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.556,1637.556,52.338,2097152,2097152,2097152*40

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:53][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 23, ret: 6
[D][05:18:53][CAT1]sub id: 23, ret: 6



2025-07-31 22:23:02:739 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:02:751 ==>> 定位已等待【3】秒.
2025-07-31 22:23:02:964 ==>>                                                                                                                                                                                                                                                                                                riority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][COMM]read battery soc:255
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5A136DFCE2837D2DF57F3EED6A8BA9

2025-07-31 22:23:03:039 ==>> 0D9266822BD0362AB751B842ADCBC5D32B8F41B562E18A0DCD391AEEEAA43B6DA6A3396D7381955FB812317413783B2AB58726EF29D6659394B4DDEFC134185E807F6E9
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 22:23:03:311 ==>> [D][05:18:53][GNSS]recv submsg id[1]
[D][05:18:53][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:23:03:609 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,25,,,41,40,,,41,33,,,40,34,,,40,1*75

$GBGSV,4,2,13,41,,,40,59,,,40,7,,,39,39,,,38,1*47

$GBGSV,4,3,13,16,,,38,11,,,37,12,,,36,6,,,36,1*4E

$GBGSV,4,4,13,44,,,17,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1540.495,1540.495,49.435,2097152,2097152,2097152*40



2025-07-31 22:23:03:745 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:03:754 ==>> 定位已等待【4】秒.
2025-07-31 22:23:04:644 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,25,,,41,40,,,41,34,,,41,41,,,41,1*71

$GBGSV,5,2,20,3,,,41,33,,,40,59,,,40,7,,,39,1*74

$GBGSV,5,3,20,39,,,39,60,,,39,16,,,38,1,,,38,1*48

$GBGSV,5,4,20,11,,,37,12,,,36,6,,,36,2,,,36,1*73

$GBGSV,5,5,20,43,,,36,44,,,36,4,,,34,5,,,34,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1581.604,1581.604,50.566,2097152,2097152,2097152*4F



2025-07-31 22:23:04:735 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:23:04:750 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:04:759 ==>> 定位已等待【5】秒.
2025-07-31 22:23:05:659 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,41,,,41,3,,,41,25,,,40,1*44

$GBGSV,6,2,23,34,,,40,33,,,40,59,,,40,39,,,40,1*72

$GBGSV,6,3,23,7,,,39,60,,,39,16,,,38,1,,,38,1*75

$GBGSV,6,4,23,11,,,37,12,,,36,6,,,36,2,,,36,1*73

$GBGSV,6,5,23,43,,,36,44,,,36,23,,,35,4,,,34,1*47

$GBGSV,6,6,23,5,,,34,24,,,34,32,,,33,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1557.372,1557.372,49.803,2097152,2097152,2097152*49



2025-07-31 22:23:05:765 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:05:775 ==>> 定位已等待【6】秒.
2025-07-31 22:23:06:688 ==>> $GBGGA,142310.492,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,41,,,41,3,,,41,25,,,41,1*42

$GBGSV,7,2,25,34,,,41,33,,,40,59,,,40,39,,,40,1*74

$GBGSV,7,3,25,60,,,40,7,,,39,16,,,38,1,,,38,1*7C

$GBGSV,7,4,25,11,,,37,12,,,37,6,,,36,2,,,36,1*75

$GBGSV,7,5,25,44,,,36,23,,,36,24,,,36,10,,,36,1*75

$GBGSV,7,6,25,43,,,35,9,,,35,5,,,34,32,,,34,1*7A

$GBGSV,7,7,25,4,,,33,1*45

$GBRMC,142310.492,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142310.492,0.000,1560.472,1560.472,49.902,2097152,2097152,2097152*5D



2025-07-31 22:23:06:733 ==>>                                          

2025-07-31 22:23:06:779 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:06:791 ==>> 定位已等待【7】秒.
2025-07-31 22:23:07:691 ==>> $GBGGA,142311.492,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,25,,,41,1*41

$GBGSV,7,2,26,34,,,41,33,,,40,59,,,40,39,,,40,1*77

$GBGSV,7,3,26,60,,,40,7,,,39,16,,,38,1,,,38,1*7F

$GBGSV,7,4,26,11,,,37,12,,,37,24,,,37,6,,,36,1*43

$GBGSV,7,5,26,2,,,36,44,,,36,23,,,36,10,,,36,1*42

$GBGSV,7,6,26,43,,,36,9,,,36,5,,,34,32,,,34,1*79

$GBGSV,7,7,26,4,,,34,45,,,37,1*44

$GBRMC,142311.492,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142311.492,0.000,1567.097,1567.097,50.105,2097152,2097152,2097152*5B



2025-07-31 22:23:07:782 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:07:792 ==>> 定位已等待【8】秒.
2025-07-31 22:23:08:180 ==>> [D][05:18:58][PROT]CLEAN,SEND:0
[D][05:18:58][PROT]CLEAN:0
[D][05:18:58][PROT]index:2 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:6
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:3
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:18:58][PROT]sending traceid [9999999999900007]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[134]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+Q

2025-07-31 22:23:08:256 ==>> ISEND=0,134

[D][05:18:58][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE0D39523AE528A0F9AA73A9CEB5BD92DDBF717609557686A847202975A5923B879C57A382297B6DF67673E0601A3311677C3B
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 22:23:08:680 ==>> $GBGGA,142312.492,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,41,1*4A

$GBGSV,7,2,26,25,,,40,34,,,40,33,,,40,39,,,40,1*7D

$GBGSV,7,3,26,60,,,40,7,,,39,1,,,39,16,,,38,1*7E

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,6,,,36,1*4C

$GBGSV,7,5,26,2,,,36,44,,,36,23,,,36,10,,,36,1*42

$GBGSV,7,6,26,43,,,36,9,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,34,13,,,31,1*41

$GBRMC,142312.492,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142312.492,0.000,1559.454,1559.454,49.872,2097152,2097152,2097152*59



2025-07-31 22:23:08:740 ==>>                                          

2025-07-31 22:23:08:785 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:08:794 ==>> 定位已等待【9】秒.
2025-07-31 22:23:09:694 ==>> $GBGGA,142313.492,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,34,,,41,1*41

$GBGSV,7,2,26,59,,,40,25,,,40,33,,,40,39,,,40,1*76

$GBGSV,7,3,26,60,,,40,7,,,39,1,,,39,16,,,38,1*7E

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,23,,,37,1*7A

$GBGSV,7,5,26,10,,,37,6,,,36,2,,,36,44,,,36,1*74

$GBGSV,7,6,26,43,,,36,9,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,33,13,,,32,1*45

$GBRMC,142313.492,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142313.492,0.000,1562.640,1562.640,49.971,2097152,2097152,2097152*5A



2025-07-31 22:23:09:799 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:09:810 ==>> 定位已等待【10】秒.
2025-07-31 22:23:10:684 ==>> $GBGGA,142314.492,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,41,1*4A

$GBGSV,7,2,26,34,,,40,25,,,40,33,,,40,39,,,40,1*7D

$GBGSV,7,3,26,60,,,40,7,,,39,1,,,39,16,,,38,1*7E

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,23,,,37,1*7A

$GBGSV,7,5,26,10,,,37,6,,,36,2,,,36,44,,,36,1*74

$GBGSV,7,6,26,43,,,36,9,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142314.492,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142314.492,0.000,1564.231,1564.231,50.019,2097152,2097152,2097152*52



2025-07-31 22:23:10:744 ==>>         9:01][COMM]read battery soc:255


2025-07-31 22:23:10:804 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:10:815 ==>> 定位已等待【11】秒.
2025-07-31 22:23:11:685 ==>> $GBGGA,142315.492,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,25,,,41,3,,,40,1*40

$GBGSV,7,2,26,59,,,40,34,,,40,33,,,40,39,,,40,1*76

$GBGSV,7,3,26,60,,,40,7,,,39,1,,,38,16,,,38,1*7F

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,23,,,37,1*7A

$GBGSV,7,5,26,10,,,37,6,,,36,2,,,36,44,,,36,1*74

$GBGSV,7,6,26,9,,,36,43,,,35,32,,,35,5,,,34,1*7B

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142315.492,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142315.492,0.000,1559.447,1559.447,49.865,2097152,2097152,2097152*58



2025-07-31 22:23:11:805 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:11:815 ==>> 定位已等待【12】秒.
2025-07-31 22:23:12:674 ==>> $GBGGA,142316.492,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,41,1*4A

$GBGSV,7,2,26,25,,,40,34,,,40,39,,,40,60,,,40,1*7B

$GBGSV,7,3,26,33,,,39,7,,,39,1,,,39,16,,,38,1*76

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,10,,,37,1*7A

$GBGSV,7,5,26,23,,,36,6,,,36,2,,,36,44,,,36,1*75

$GBGSV,7,6,26,9,,,36,43,,,35,32,,,35,5,,,34,1*7B

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142316.492,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142316.492,0.000,1559.449,1559.449,49.867,2097152,2097152,2097152*59



2025-07-31 22:23:12:734 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 22:23:12:809 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:12:819 ==>> 定位已等待【13】秒.
2025-07-31 22:23:13:363 ==>> [D][05:19:03][PROT]CLEAN,SEND:2
[D][05:19:03][PROT]index:2 1629955143
[D][05:19:03][PROT]is_send:0
[D][05:19:03][PROT]sequence_num:6
[D][05:19:03][PROT]retry_timeout:0
[D][05:19:03][PROT]retry_times:2
[D][05:19:03][PROT]send_path:0x2
[D][05:19:03][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][CAT1]<<< 
ERROR



2025-07-31 22:23:13:668 ==>> $GBGGA,142317.492,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,41,1*4A

$GBGSV,7,2,26,60,,,41,25,,,40,34,,,40,39,,,40,1*7A

$GBGSV,7,3,26,33,,,40,7,,,39,1,,,39,16,,,38,1*78

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,10,,,37,1*7A

$GBGSV,7,5,26,23,,,36,6,,,36,2,,,36,44,,,36,1*75

$GBGSV,7,6,26,9,,,36,43,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142317.492,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142317.492,0.000,1564.234,1564.234,50.022,2097152,2097152,2097152*59



2025-07-31 22:23:13:819 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:13:839 ==>> 定位已等待【14】秒.
2025-07-31 22:23:14:686 ==>> $GBGGA,142314.492,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,40,1*4B

$GBGSV,7,2,26,60,,,40,25,,,40,34,,,40,39,,,40,1*7B

$GBGSV,7,3,26,33,,,40,7,,,39,1,,,39,16,,,38,1*78

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,10,,,37,1*7A

$GBGSV,7,5,26,23,,,36,6,,,36,2,,,36,44,,,36,1*75

$GBGSV,7,6,26,9,,,36,43,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142314.492,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142314.492,0.000,1561.041,1561.041,49.916,2097152,2097152,2097152*5C



2025-07-31 22:23:14:761 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 22:23:14:821 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:14:831 ==>> 定位已等待【15】秒.
2025-07-31 22:23:15:690 ==>> $GBGGA,142315.492,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,41,,,41,3,,,41,59,,,41,1*4A

$GBGSV,7,2,26,60,,,40,25,,,40,34,,,40,39,,,40,1*7B

$GBGSV,7,3,26,33,,,40,7,,,39,1,,,39,16,,,38,1*78

$GBGSV,7,4,26,24,,,38,11,,,37,12,,,37,10,,,37,1*7A

$GBGSV,7,5,26,23,,,36,6,,,36,2,,,36,44,,,36,1*75

$GBGSV,7,6,26,9,,,36,43,,,36,32,,,35,5,,,34,1*78

$GBGSV,7,7,26,4,,,34,13,,,32,1*42

$GBRMC,142315.492,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142315.492,0.000,1562.638,1562.638,49.969,2097152,2097152,2097152*55



2025-07-31 22:23:15:826 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:15:836 ==>> 定位已等待【16】秒.
2025-07-31 22:23:16:683 ==>> $GBGGA,142316.492,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,3,,,41,59,,,41,1*4B

$GBGSV,7,2,27,60,,,40,25,,,40,34,,,40,39,,,40,1*7A

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,12,,,37,10,,,37,1*7B

$GBGSV,7,5,27,2,,,37,23,,,36,6,,,36,44,,,36,1*75

$GBGSV,7,6,27,9,,,36,43,,,36,32,,,35,5,,,34,1*79

$GBGSV,7,7,27,4,,,34,13,,,32,14,,,32,1*47

$GBRMC,142316.492,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142316.492,0.000,1555.442,1555.442,49.747,2097152,2097152,2097152*54



2025-07-31 22:23:16:758 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 22:23:16:833 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:16:844 ==>> 定位已等待【17】秒.
2025-07-31 22:23:17:689 ==>> $GBGGA,142317.492,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,27,34,,,41,39,,,41,3,,,40,60,,,40,1*4E

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,12,,,37,10,,,37,1*7B

$GBGSV,7,5,27,2,,,37,23,,,37,6,,,36,44,,,36,1*74

$GBGSV,7,6,27,9,,,36,43,,,36,32,,,35,5,,,34,1*79

$GBGSV,7,7,27,4,,,34,13,,,32,14,,,32,1*47

$GBRMC,142317.492,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142317.492,0.000,1560.051,1560.051,49.897,2097152,2097152,2097152*57



2025-07-31 22:23:17:840 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:17:851 ==>> 定位已等待【18】秒.
2025-07-31 22:23:18:692 ==>> $GBGGA,142318.492,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,59,,,41,25,,,41,1*7F

$GBGSV,7,2,27,34,,,41,39,,,41,3,,,41,60,,,40,1*4F

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,12,,,36,1*7A

$GBGSV,7,5,27,2,,,36,23,,,36,6,,,36,9,,,36,1*4D

$GBGSV,7,6,27,43,,,36,44,,,35,32,,,35,5,,,34,1*43

$GBGSV,7,7,27,4,,,34,13,,,32,14,,,32,1*47

$GBRMC,142318.492,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142318.492,0.000,1555.450,1555.450,49.756,2097152,2097152,2097152*5A



2025-07-31 22:23:18:767 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 22:23:18:842 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:18:853 ==>> 定位已等待【19】秒.
2025-07-31 22:23:19:695 ==>> $GBGGA,142319.492,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,34,,,41,1*74

$GBGSV,7,2,27,39,,,41,3,,,41,59,,,40,60,,,40,1*45

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,38,16,,,38,1*78

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,12,,,36,1*7A

$GBGSV,7,5,27,2,,,36,23,,,36,6,,,36,9,,,36,1*4D

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,13,,,32,14,,,32,1*47

$GBRMC,142319.492,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142319.492,0.000,1553.911,1553.911,49.703,2097152,2097152,2097152*5B



2025-07-31 22:23:19:847 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:19:857 ==>> 定位已等待【20】秒.
2025-07-31 22:23:20:687 ==>> $GBGGA,142320.492,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,34,,,41,1*74

$GBGSV,7,2,27,3,,,41,39,,,40,59,,,40,60,,,40,1*44

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,12,,,36,2,,,36,6,,,36,9,,,36,1*4F

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,13,,,32,14,,,32,1*47

$GBRMC,142320.492,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142320.492,0.000,1555.444,1555.444,49.750,2097152,2097152,2097152*57



2025-07-31 22:23:20:777 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 22:23:20:852 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:20:871 ==>> 定位已等待【21】秒.
2025-07-31 22:23:21:689 ==>> $GBGGA,142321.492,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,34,,,41,1*74

$GBGSV,7,2,27,3,,,40,39,,,40,59,,,40,60,,,40,1*45

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,12,,,37,2,,,36,6,,,36,9,,,36,1*4E

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,14,,,33,13,,,32,1*46

$GBRMC,142321.492,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142321.492,0.000,1556.974,1556.974,49.793,2097152,2097152,2097152*59



2025-07-31 22:23:21:856 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:21:876 ==>> 定位已等待【22】秒.
2025-07-31 22:23:22:665 ==>> $GBGGA,142322.492,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,34,,,41,1*74

$GBGSV,7,2,27,59,,,41,3,,,40,39,,,40,60,,,40,1*44

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,2,,,37,12,,,36,6,,,36,9,,,36,1*4E

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,14,,,33,13,,,32,1*46

$GBRMC,142322.492,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142322.492,0.000,1558.511,1558.511,49.844,2097152,2097152,2097152*5F



2025-07-31 22:23:22:770 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 22:23:22:860 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:22:893 ==>> 定位已等待【23】秒.
2025-07-31 22:23:23:386 ==>> [D][05:19:13][CAT1]exec over: func id: 15, ret: -93
[D][05:19:13][CAT1]sub id: 15, ret: -93

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:13][SAL ]socket send fail. id[4]
[D][05:19:13][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:13][M2M ]m2m select fd[4]
[D][05:19:13][M2M ]socket[4] Link is disconnected
[D][05:19:13][M2M ]tcpclient close[4]
[D][05:19:13][SAL ]socket[4] has closed
[D][05:19:13][PROT]protocol read data ok
[E][05:19:13][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:13][PROT]M2M Send Fail [1629955153]
[D][05:19:13][PROT]CLEAN,SEND:2
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:13][CAT1]gsm read msg sub id: 10
[D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:13][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:23:23:783 ==>> $GBGGA,142323.492,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,34,,,41,1*74

$GBGSV,7,2,27,59,,,41,3,,,40,39,,,40,60,,,40,1*44

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,2,,,37,12,,,37,6,,,36,9,,,36,1*4F

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,13,,,33,14,,,32,1*46

$GBRMC,142323.492,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142323.492,0.000,1560.046,1560.046,49.892,2097152,2097152,2097152*55

[D][05:19:14][CAT1]<<< 
OK

[D][05:19:14][CAT1]exec over: func id: 10, ret: 6
[D][05:19:14][CAT1]sub id: 10, ret: 6

[D][05:19:14][SAL ]Cellular task submsg id[68]
[D][05:19:14][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:14][M2M ]m2m gsm shut done, ret[0]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:14][SAL ]open socket ind id[4], rst[0]
[D][05:19:14][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:14][SAL ]Cellular task submsg id[8]
[D][05:19:14][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:14][SAL ]domain[bikeapi.mobike.com] port

2025-07-31 22:23:23:829 ==>> [9999] type[1]
[D][05:19:14][CAT1]gsm read msg sub id: 8
[D][05:19:14][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:14][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:23:23:873 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:23:893 ==>> 定位已等待【24】秒.
2025-07-31 22:23:23:934 ==>> [D][05:19:14][CAT1]pdpdeact urc len[22]


2025-07-31 22:23:24:691 ==>> $GBGGA,142324.492,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,59,,,41,1*7F

$GBGSV,7,2,27,3,,,41,39,,,41,34,,,40,60,,,40,1*4E

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,2,,,37,12,,,37,6,,,36,9,,,36,1*4F

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,13,,,33,14,,,33,1*47

$GBRMC,142324.492,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142324.492,0.000,1563.115,1563.115,49.988,2097152,2097152,2097152*58



2025-07-31 22:23:24:782 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 22:23:24:887 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:24:898 ==>> 定位已等待【25】秒.
2025-07-31 22:23:25:165 ==>> [D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:15][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:15][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:15][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:15][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 22:23:25:271 ==>>                                                                                [D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1


2025-07-31 22:23:25:301 ==>> 

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:23:25:807 ==>> [D][05:19:16][CAT1]opened : 0, 0
[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:16][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:16][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:16][M2M ]g_m2m_is_idle become true
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]index:2 1629955156
[D][05:19:16][PROT]is_send:0
[D][05:19:16][PROT]sequence_num:6
[D][05:19:16][PROT]retry_timeout:0
[D][05:19:16][PROT]retry_times:1
[D][05:19:16][PROT]send_path:0x2
[D][05:19:16][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A6C89C8906980220
[D][05:19:16][PROT]sending traceid [9999999999900007]
[D][05:19:16][PROT]Send_TO_M2M [1629955156]
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:16][SAL ]sock send credit cnt[6]
[D][05:19:16][SAL ]sock send ind credit cnt[6]
[D][05:19:16][M2M ]m2m send data len[134]
[D][05:19:16][SAL ]Cellular task submsg id[10]
[D][05:

2025-07-31 22:23:25:898 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:25:920 ==>> 定位已等待【26】秒.
2025-07-31 22:23:25:936 ==>> 19:16][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd8] format[0]
[D][05:19:16][CAT1]gsm read msg sub id: 15
[D][05:19:16][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:16][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BEFC3938D920BE02795B3CEF3908358AC0EAC1B658BDB5AC4E310501CA1F628A55FEC180DBCCE5EC26F36AC90113E250AC40B9
[D][05:19:16][CAT1]<<< 
SEND OK

[D][05:19:16][CAT1]exec over: func id: 15, ret: 11
[D][05:19:16][CAT1]sub id: 15, ret: 11

[D][05:19:16][SAL ]Cellular task submsg id[68]
[D][05:19:16][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:16][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,142325.492,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,25,,,41,59,,,41,1*7C

$GBGSV,7,2,27,39,,,41,34,,,41,60,,,41,3,,,40,1*4F

$GBGSV,7,3,27,33,,,40,7,,,39,1,,,39,16,,,38,1*79

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,2,,,37,12,,,37,6,,,36,9,,,36,1*4F

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,14,,,33,13,,,32,1*46

$GBRMC,142325.492,V,,,,,,,,0.0,E,N,V*42

$

2025-07-31 22:23:25:962 ==>> GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142325.492,0.000,1564.658,1564.658,50.045,2097152,2097152,2097152*59

[D][05:19:16][M2M ]g_m2m_is_idle become true
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:16][PROT]M2M Send ok [1629955156]


2025-07-31 22:23:26:684 ==>> $GBGGA,142326.492,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,41,,,41,25,,,41,59,,,41,1*7F

$GBGSV,7,2,27,39,,,41,34,,,41,60,,,40,3,,,40,1*4E

$GBGSV,7,3,27,33,,,40,7,,,40,1,,,39,16,,,38,1*77

$GBGSV,7,4,27,24,,,38,11,,,37,10,,,37,23,,,37,1*79

$GBGSV,7,5,27,2,,,37,12,,,36,6,,,36,9,,,36,1*4E

$GBGSV,7,6,27,43,,,36,44,,,36,32,,,35,5,,,34,1*40

$GBGSV,7,7,27,4,,,34,14,,,33,13,,,32,1*46

$GBRMC,142326.492,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,142326.492,0.000,1561.585,1561.585,49.945,2097152,2097152,2097152*5B



2025-07-31 22:23:26:790 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 22:23:26:911 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:26:922 ==>> 定位已等待【27】秒.
2025-07-31 22:23:27:923 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:23:27:934 ==>> 定位已等待【28】秒.
2025-07-31 22:23:28:257 ==>> $GBGGA,142327.497,2301.2569893,N,11421.9426821,E,1,08,1.28,77.224,M,-1.770,M,,*51

$GBGSA,A,3,40,39,06,16,25,41,34,33,,,,,4.78,1.28,4.60,4*00

$GBGSV,7,1,27,40,62,168,41,3,60,190,41,7,60,217,40,39,59,32,41,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,40,33,37,203,40,1*7A

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,33,13,6,201,32,11,,,37,1*40

$GBGSV,7,7,27,12,,,37,44,,,36,32,,,35,1*76

$GBRMC,142327.497,A,2301.2569893,N,11421.9426821,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:18][GNSS]HD8040 GPS
[D][05:19:18][GNSS]GPS diff_sec 124016649, report 0x42 frame
$GBGST,142327.497,0.768,0.211,0.204,0.386,1.852,2.252,14*5C

[D][05:19:18][COMM]Main Task receive event:131
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:1

2025-07-31 22:23:28:363 ==>> 9:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][

2025-07-31 22:23:28:468 ==>> COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle

2025-07-31 22:23:28:574 ==>> :0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_

2025-07-31 22:23:28:680 ==>> twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][COMM]Main Task receive event:131 finished processing
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,142328.097,2301.2577614,N,11421.9428674,E,1,08,1.2

2025-07-31 22:23:28:785 ==>> 8,79.362,M,-1.770,M,,*59

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:23:28:935 ==>> 符合定位需求的卫星数量:【23】
2025-07-31 22:23:28:951 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【7】,信号值:【40】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【38】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【39】
北斗星号:【10】,信号值:【37】
北斗星号:【2】,信号值:【36】
北斗星号:【9】,信号值:【36】
北斗星号:【41】,信号值:【41】
北斗星号:【34】,信号值:【41】
北斗星号:【60】,信号值:【40】
北斗星号:【33】,信号值:【40】
北斗星号:【43】,信号值:【36】
北斗星号:【23】,信号值:【37】
北斗星号:【24】,信号值:【38】
北斗星号:【11】,信号值:【37】
北斗星号:【12】,信号值:【37】
北斗星号:【44】,信号值:【36】
北斗星号:【32】,信号值:【35】

2025-07-31 22:23:28:962 ==>> 检测【CSQ强度】
2025-07-31 22:23:28:985 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:23:29:132 ==>> [D][05:19:19][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:19][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:19][CAT1]gsm read msg sub id: 12
[D][05:19:19][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:19][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:23:29:282 ==>> 【CSQ强度】通过,【26】符合目标值【18】至【31】要求!
2025-07-31 22:23:29:289 ==>> 检测【关闭GSM联网】
2025-07-31 22:23:29:314 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:23:29:404 ==>> $GBGGA,142329.077,2301.2580292,N,11421.9428450,E,1,08,1.28,81.022,M,-1.770,M,,*50

$GBGSA,A,3,40,39,06,16,25,41,34,33,,,,,4.78,1.28,4.60,4*00

$GBGSV,7,1,27,40,62,168,41,3,60,190,41,7,60,217,39,39,59,32,41,1*4A

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,41,33,37,203,40,1*7B

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,32,13,6,201,32,11,,,37,1*41

$GBGSV,7,7,27,44,,,36,12,,,36,32,,,35,1*77

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,42,5*42

$GBGSV,2,2,06,34,41,113,39,33,37,203,40,5*7E

$GBRMC,142329.077,A,2301.2580292,N,11421.9428450,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,142329.077,0.982,0.341,0.326,0.641,1.091,1.229,7.160*7A



2025-07-31 22:23:29:494 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:20][COMM]GSM test
[D][05:19:20][COMM]GSM test disable


2025-07-31 22:23:29:559 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:23:29:570 ==>> 检测【4G联网测试】
2025-07-31 22:23:29:590 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:23:30:537 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:20][COMM]Main Task receive event:14
[D][05:19:20][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955160, allstateRepSeconds = 0
[D][05:19:20][COMM]index:0,power_mode:0xFF
[D][05:19:20][COMM]index:1,sound_mode:0xFF
[D][05:19:20][COMM]index:2,gsensor_mode:0xFF
[D][05:19:20][COMM]index:3,report_freq_mode:0xFF
[D][05:19:20][COMM]index:4,report_period:0xFF
[D][05:19:20][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:20][COMM]index:6,normal_reset_period:0xFF
[D][05:19:20][COMM]index:7,spock_over_speed:0xFF
[D][05:19:20][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:20][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:20][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:20][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:20][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:20][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:20][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:20][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:20][COMM]index:16,imu_config_params:0xFF
[D][05:19:20][COMM]index:17,long_connect_params:0xFF
[D][05:19:20][COMM]index:18,detain_mark:0xFF


2025-07-31 22:23:30:643 ==>> 
[D][05:19:20][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:20][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:20][COMM]index:21,mc_mode:0xFF
[D][05:19:20][COMM]index:22,S_mode:0xFF
[D][05:19:20][COMM]index:23,overweight:0xFF
[D][05:19:20][COMM]index:24,standstill_mode:0xFF
[D][05:19:20][COMM]index:25,night_mode:0xFF
[D][05:19:20][COMM]index:26,experiment1:0xFF
[D][05:19:20][COMM]index:27,experiment2:0xFF
[D][05:19:20][COMM]index:28,experiment3:0xFF
[D][05:19:20][COMM]index:29,experiment4:0xFF
[D][05:19:20][COMM]index:30,night_mode_start:0xFF
[D][05:19:20][COMM]index:31,night_mode_end:0xFF
[D][05:19:20][COMM]index:33,park_report_minutes:0xFF
[D][05:19:20][COMM]index:34,park_report_mode:0xFF
[D][05:19:20][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:20][COMM]index:38,charge_battery_para: FF
[D][05:19:20][COMM]index:39,multirider_mode:0xFF
[D][05:19:20][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:20][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:20][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:20][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:20][COMM]index:44,riding_duration_config:0xFF
[D][05:19:20][COMM]index:45,camera_park_angle_cfg

2025-07-31 22:23:30:749 ==>> :0xFF
[D][05:19:20][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:20][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:20][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:20][COMM]index:49,mc_load_startup:0xFF
[D][05:19:20][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:20][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:20][COMM]index:52,traffic_mode:0xFF
[D][05:19:20][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:20][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:20][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:20][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:20][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:20][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:20][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:20][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:20][COMM]index:63,experiment5:0xFF
[D][05:19:20][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:20][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:20][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:20][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:20][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:20][COMM]index:70,camera_park_light_c

2025-07-31 22:23:30:854 ==>> fg:0xFF
[D][05:19:20][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:20][COMM]index:72,experiment6:0xFF
[D][05:19:20][COMM]index:73,experiment7:0xFF
[D][05:19:20][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:20][COMM]index:75,zero_value_from_server:-1
[D][05:19:20][COMM]index:76,multirider_threshold:255
[D][05:19:20][COMM]index:77,experiment8:255
[D][05:19:20][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:20][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:20][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:20][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:20][COMM]index:83,loc_report_interval:255
[D][05:19:20][COMM]index:84,multirider_threshold_p2:255
[D][05:19:20][COMM]index:85,multirider_strategy:255
[D][05:19:20][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:20][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:20][COMM]index:90,weight_param:0xFF
[D][05:19:20][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:20][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:20][COMM]index:95,current_limit:0xFF
[D][05:19:20][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D]

2025-07-31 22:23:30:960 ==>> [05:19:20][COMM]index:100,location_mode:0xFF

[W][05:19:20][PROT]remove success[1629955160],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:20][PROT]add success [1629955160],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:20][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:20][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:20][CAT1]gsm read msg sub id: 13
[D][05:19:20][PROT]index:0 1629955160
[D][05:19:20][PROT]is_send:0
[D][05:19:20][PROT]sequence_num:8
[D][05:19:20][PROT]retry_timeout:0
[D][05:19:20][PROT]retry_times:1
[D][05:19:20][PROT]send_path:0x2
[D][05:19:20][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:20][PROT]===========================================================
[W][05:19:20][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955160]
[D][05:19:20][PROT]===========================================================
[D][05:19:20][PROT]sending traceid [9999999999900009]
[D][05:19:20][PROT]Send_TO_M2M [1629955160]
[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:20][SAL ]sock send credit cnt[6]
[D][05:19:20][SAL ]sock send ind credit cnt[6]
[D][05:19:20][M2M ]m2m send data

2025-07-31 22:23:31:065 ==>>  len[294]
[D][05:19:20][SAL ]Cellular task submsg id[10]
[D][05:19:20][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df0] format[0]
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:20][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:20][CAT1]exec over: func id: 13, ret: 21
[D][05:19:20][M2M ]get csq[24]
[D][05:19:20][CAT1]gsm read msg sub id: 15
[D][05:19:20][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:20][CAT1]<<< 
ERROR

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:20][PROT]remove success[1629955160],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:20][PROT]add success [1629955160],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:20][COMM]------>period, report file manifest
[D][05:19:20][COMM]Main Task receive event:14 finished processing
[D][05:19:20][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:20][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,142330.057,2301.2580821,N,11421.9428133,E,1,08,1.28,82.210,M,-1.770,M,,*58

$GBGSA,A,3,40,39,06,16,25,41,34,33,,,,,4.78,1.28,4.60,4*00

$GBGSV,7,1,27,40,62,168,42,3,60,190,40,7,60,217,39,39,59,32,41,1*48

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25

2025-07-31 22:23:31:140 ==>> ,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,37,9,45,332,36,1*44

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,41,33,37,203,40,1*7B

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,32,13,6,201,32,12,,,37,1*42

$GBGSV,7,7,27,11,,,37,44,,,36,32,,,35,1*75

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,42,5*42

$GBGSV,2,2,06,34,41,113,39,33,37,203,40,5*7E

$GBRMC,142330.057,A,2301.2580821,N,11421.9428133,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142330.057,1.039,0.223,0.215,0.407,1.016,1.121,6.082*74



2025-07-31 22:23:31:245 ==>>                                                                                                                           

2025-07-31 22:23:31:335 ==>>                                                                                                                                                                   6,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,40,33,37,203,40,1*7A

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,33,13,6,201,33,12,,,37,1*42

$GBGSV,7,7,27,11,,,37,44,,,36,32,,,35,1*75

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,42,5*43

$GBGSV,2,2,06,34,41,113,40,33,37,203,40,5*70

$GBRMC,142331.037,A,2301.2581575,N,11421.9427935,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,142331.037,1.097,0.177,0.172,0.320,0.992,1.074,5.378*75



2025-07-31 22:23:32:314 ==>> $GBGGA,142332.017,2301.2581830,N,11421.9427931,E,1,08,1.28,82.940,M,-1.770,M,,*54

$GBGSA,A,3,40,39,06,16,25,41,34,33,,,,,4.78,1.28,4.60,4*00

$GBGSV,7,1,27,40,62,168,41,3,60,190,40,7,60,217,39,39,59,32,41,1*4B

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,40,33,37,203,40,1*7A

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,33,13,6,201,32,11,,,37,1*40

$GBGSV,7,7,27,44,,,36,12,,,36,32,,,35,1*77

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,43,5*43

$GBGSV,2,2,06,34,41,113,40,33,37,203,40,5*70

$GBRMC,142332.017,A,2301.2581830,N,11421.9427931,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142332.017,0.996,0.159,0.155,0.279,0.857,0.926,4.832*7A



2025-07-31 22:23:32:818 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 22:23:33:319 ==>> $GBGGA,142333.000,2301.2582034,N,11421.9427989,E,1,08,1.28,83.173,M,-1.770,M,,*56

$GBGSA,A,3,40,39,06,16,25,41,34,33,,,,,4.77,1.28,4.60,4*0F

$GBGSV,7,1,27,40,62,168,42,3,60,190,41,7,60,217,39,39,59,32,41,1*49

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,41,33,37,203,40,1*7B

$GBGSV,7,5,27,4,32,111,34,43,24,172,36,5,22,256,34,23,21,291,37,1*7D

$GBGSV,7,6,27,24,11,255,38,14,9,322,33,13,6,201,33,11,,,37,1*41

$GBGSV,7,7,27,44,,,36,12,,,36,32,,,35,1*77

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*42

$GBGSV,2,2,06,34,41,113,40,33,37,203,40,5*70

$GBRMC,142333.000,A,2301.2582034,N,11421.9427989,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142333.000,1.035,0.222,0.214,0.404,0.856,0.914,4.453*72



2025-07-31 22:23:34:318 ==>> $GBGGA,142334.000,2301.2582176,N,11421.9428071,E,1,09,1.17,83.176,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,25,41,34,33,,,,3.99,1.17,3.81,4*0B

$GBGSV,7,1,27,40,62,168,41,3,60,190,41,7,60,193,39,39,59,32,41,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,41,33,37,203,40,1*7B

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,36,43,24,172,36,1*7A

$GBGSV,7,6,27,5,22,256,34,23,21,291,37,24,11,255,38,14,9,322,33,1*7D

$GBGSV,7,7,27,13,6,201,33,44,,,36,32,,,35,1*76

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*42

$GBGSV,2,2,06,34,41,113,40,33,37,203,40,5*70

$GBRMC,142334.000,A,2301.2582176,N,11421.9428071,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142334.000,1.028,0.168,0.158,0.285,0.820,0.873,4.098*72



2025-07-31 22:23:34:820 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 22:23:35:312 ==>> $GBGGA,142335.000,2301.2582125,N,11421.9428517,E,1,11,0.99,83.378,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,25,41,34,33,11,12,,2.88,0.99,2.70,4*01

$GBGSV,7,1,27,40,62,168,42,3,60,190,41,7,60,193,39,39,59,32,41,1*46

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,52,129,41,25,49,346,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,47,219,37,2,45,237,36,9,45,332,36,1*45

$GBGSV,7,4,27,41,43,273,41,34,41,113,41,60,41,238,41,33,37,203,40,1*7B

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,43,24,172,36,1*7B

$GBGSV,7,6,27,5,22,256,34,23,21,291,37,44,18,56,36,24,11,255,38,1*7D

$GBGSV,7,7,27,14,9,322,33,13,6,201,33,32,,,35,1*7C

$GBGSV,2,1,06,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,43,5*43

$GBGSV,2,2,06,34,41,113,40,33,37,203,40,5*70

$GBRMC,142335.000,A,2301.2582125,N,11421.9428517,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142335.000,1.939,0.163,0.158,0.268,1.511,1.547,4.169*78



2025-07-31 22:23:35:618 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:23:36:422 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:26][COMM]Main Task receive event:14
[D][05:19:26][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955166, allstateRepSeconds = 0
[D][05:19:26][COMM]index:0,power_mode:0xFF
[D][05:19:26][COMM]index:1,sound_mode:0xFF
[D][05:19:26][COMM]index:2,gsensor_mode:0xFF
[D][05:19:26][COMM]index:3,report_freq_mode:0xFF
[D][05:19:26][COMM]index:4,report_period:0xFF
[D][05:19:26][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:26][COMM]index:6,normal_reset_period:0xFF
[D][05:19:26][COMM]index:7,spock_over_speed:0xFF
[D][05:19:26][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:26][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:26][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:26][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:26][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:26][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:26][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:26][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:26][COMM]index:16,imu_config_params:0xFF
[D][05:19:26][COMM]index:17,long_connect_params:0xFF
[D][05:19:26][COMM]index:18,detain_mark:0xFF
[D][05:19:26][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19

2025-07-31 22:23:36:527 ==>> :26][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:26][COMM]index:21,mc_mode:0xFF
[D][05:19:26][COMM]index:22,S_mode:0xFF
[D][05:19:26][COMM]index:23,overweight:0xFF
[D][05:19:26][COMM]index:24,standstill_mode:0xFF
[D][05:19:26][COMM]index:25,night_mode:0xFF
[D][05:19:26][COMM]index:26,experiment1:0xFF
[D][05:19:26][COMM]index:27,experiment2:0xFF
[D][05:19:26][COMM]index:28,experiment3:0xFF
[D][05:19:26][COMM]index:29,experiment4:0xFF
[D][05:19:26][COMM]index:30,night_mode_start:0xFF
[D][05:19:26][COMM]index:31,night_mode_end:0xFF
[D][05:19:26][COMM]index:33,park_report_minutes:0xFF
[D][05:19:26][COMM]index:34,park_report_mode:0xFF
[D][05:19:26][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:26][COMM]index:38,charge_battery_para: FF
[D][05:19:26][COMM]index:39,multirider_mode:0xFF
[D][05:19:26][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:26][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:26][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:26][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:26][COMM]index:44,riding_duration_config:0xFF
[D][05:19:26][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:26][COMM]index:46,camera_park_

2025-07-31 22:23:36:633 ==>> type_cfg:0xFF
[D][05:19:26][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:26][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:26][COMM]index:49,mc_load_startup:0xFF
[D][05:19:26][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:26][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:26][COMM]index:52,traffic_mode:0xFF
[D][05:19:26][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:26][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:26][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:26][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:26][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:26][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:26][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:26][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:26][COMM]index:63,experiment5:0xFF
[D][05:19:26][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:26][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:26][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:26][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:26][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:26][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:26][COMM]index:71,camera_park_

2025-07-31 22:23:36:738 ==>> self_check_cfg:0xFF
[D][05:19:26][COMM]index:72,experiment6:0xFF
[D][05:19:26][COMM]index:73,experiment7:0xFF
[D][05:19:26][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:26][COMM]index:75,zero_value_from_server:-1
[D][05:19:26][COMM]index:76,multirider_threshold:255
[D][05:19:26][COMM]index:77,experiment8:255
[D][05:19:26][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:26][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:26][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:26][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:26][COMM]index:83,loc_report_interval:255
[D][05:19:26][COMM]index:84,multirider_threshold_p2:255
[D][05:19:26][COMM]index:85,multirider_strategy:255
[D][05:19:26][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:26][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:26][COMM]index:90,weight_param:0xFF
[D][05:19:26][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:26][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:26][COMM]index:95,current_limit:0xFF
[D][05:19:26][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:26][COMM]index:100,location_mode:0xFF

[D

2025-07-31 22:23:36:844 ==>> ][05:19:26][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:19:26][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:26][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:26][PROT]remove success[1629955166],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:26][PROT]add success [1629955166],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,142336.000,2301.2582301,N,11421.9428990,E,1,18,0.68,83.157,M,-1.770,M,,*53

$GBGSA,A,3,40,03,07,39,06,16,59,25,01,41,60,34,1.50,0.68,1.34,4*01

$GBGSA,A,3,33,11,12,43,23,44,,,,,,,1.50,0.68,1.34,4*01

$GBGSV,7,1,27,40,62,168,41,3,61,190,40,7,60,193,39,39,59,32,40,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,41,25,49,346,41,1*44

$GBGSV,7,3,27,10,47,219,37,1,45,125,39,2,45,237,36,9,45,332,36,1*48

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,113,41,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,43,24,172,36,1*7B

$GBGSV,7,6,27,5,22,256,34,23,21,291,37,44,18,56,36,24,11,255,38,1*7D

$GBGSV,7,7,27,14,9,322,32,32,8,319,35,13,6,201,32,1*7F

$GBGSV,2,1,07,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,43,5*42

$GBGSV,2,2,07,34,41,113,40,33,37,203,40,44,18,56,35,5*7D



2025-07-31 22:23:36:904 ==>> $GBRMC,142336.000,A,2301.2582301,N,11421.9428990,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142336.000,2.212,0.182,0.181,0.279,1.679,1.708,3.960*73

                                         

2025-07-31 22:23:37:363 ==>> $GBGGA,142337.000,2301.2582266,N,11421.9429305,E,1,22,0.59,82.654,M,-1.770,M,,*5B

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.24,0.59,1.09,4*07

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,32,,,1.24,0.59,1.09,4*08

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,41,25,49,346,41,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,38,2,45,237,36,1*45

$GBGSV,7,4,27,41,43,273,41,60,42,239,40,34,41,113,40,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,24,27,57,38,1*41

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56,36,1*73

$GBGSV,7,7,27,14,9,322,32,32,8,319,35,13,6,201,33,1*7E

$GBGSV,3,1,10,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,43,5*45

$GBGSV,3,2,10,34,41,113,40,33,37,203,40,43,24,172,34,23,21,291,38,5*77

$GBGSV,3,3,10,44,18,56,35,32,8,319,29,5*76

$GBRMC,142337.000,A,2301.2582266,N,11421.9429305,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142337.000,3.240,0.188,0.187,0.277,2.275,2.296,4.021*77



2025-07-31 22:23:37:898 ==>> [D][05:19:28][M2M ]get csq[-1]


2025-07-31 22:23:38:358 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:28][PROT]remove success[1629955168],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:28][PROT]add success [1629955168],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:28][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:28][COMM][LOC]wifi scan is already running, error
[D][05:19:28][COMM]Main Task receive event:14 finished processing
$GBGGA,142338.000,2301.2582449,N,11421.9429621,E,1,22,0.59,82.098,M,-1.770,M,,*5A

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.24,0.59,1.09,4*07

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,32,,,1.24,0.59,1.09,4*08

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,41,25,49,346,41,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,38,2,45,237,36,1*45

$GBGSV,7,4,27,41,43,273,41,60,42,239,40,34,41,113,40,33,37,203,39,1*77

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*40

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56

2025-07-31 22:23:38:418 ==>> ,36,1*73

$GBGSV,7,7,27,14,9,322,32,32,8,319,35,13,6,201,32,1*7F

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,113,40,33,37,203,40,24,27,57,38,43,24,172,33,5*48

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,29,5*45

$GBRMC,142338.000,A,2301.2582449,N,11421.9429621,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,142338.000,3.142,0.207,0.205,0.302,2.212,2.229,3.712*72



2025-07-31 22:23:38:722 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 22:23:38:829 ==>>                                                                                                                                                                                                                                                                                                                  eriod:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100025. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100026. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100027. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100028. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100029. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100029. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg

2025-07-31 22:23:38:933 ==>>  024A loss. last_tick:0. cur_tick:100029. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100030. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100030. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100030. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100031. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100031. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100032. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100032
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100032
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100033
                                         

2025-07-31 22:23:39:354 ==>> $GBGGA,142339.000,2301.2582780,N,11421.9429856,E,1,22,0.59,81.550,M,-1.770,M,,*51

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.24,0.59,1.09,4*07

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,32,,,1.24,0.59,1.09,4*08

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,37,16,56,7,38,59,49,130,41,25,49,346,41,1*45

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,113,41,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,24,27,57,38,1*41

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56,36,1*73

$GBGSV,7,7,27,14,9,322,32,32,8,319,35,13,6,201,32,1*7F

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,42,5*44

$GBGSV,3,2,11,34,41,113,40,33,37,203,40,24,27,57,38,43,24,172,33,5*48

$GBGSV,3,3,11,23,21,291,38,44,18,56,35,32,8,319,29,5*44

$GBRMC,142339.000,A,2301.2582780,N,11421.9429856,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,142339.000,3.194,0.188,0.187,0.275,2.234,2.248,3.563*73



2025-07-31 22:23:40:083 ==>> [D][05:19:30][CAT1]exec over: func id: 15, ret: -93
[D][05:19:30][CAT1]sub id: 15, ret: -93

[D][05:19:30][SAL ]Cellular task submsg id[68]
[D][05:19:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:30][SAL ]socket send fail. id[4]
[D][05:19:30][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:30][CAT1]gsm read msg sub id: 21
[D][05:19:30][M2M ]m2m select fd[4]
[D][05:19:30][M2M ]socket[4] Link is disconnected
[D][05:19:30][M2M ]tcpclient close[4]
[D][05:19:30][SAL ]socket[4] has closed
[D][05:19:30][PROT]protocol read data ok
[E][05:19:30][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:30][CAT1]tx ret[15] >>> AT+QCELLINFO?

[E][05:19:30][PROT]M2M Send Fail [1629955170]
[D][05:19:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]cell info report total[0]
[D][05:19:30][CAT1]exec over: func id: 21, ret: 6
[D][05:19:30][CAT1]gsm read msg sub id: 13
[D][05:19:30][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:30][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:30][CAT1]exec over: func id: 13, ret: 21
[D][05:19:30][CAT1]gsm read msg sub id: 10
[D][05:19:30][CAT1]tx ret[11] >>> AT+CGATT?

[

2025-07-31 22:23:40:113 ==>> D][05:19:30][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:30][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:23:40:384 ==>> $GBGGA,142340.000,2301.2582900,N,11421.9430226,E,1,23,0.57,81.030,M,-1.770,M,,*50

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.15,0.57,0.99,4*03

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,13,32,,1.15,0.57,0.99,4*0E

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,41,1*45

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,112,40,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,24,27,57,38,1*41

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56,36,1*73

$GBGSV,7,7,27,13,15,213,32,14,9,322,32,32,8,319,35,1*4E

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,112,40,33,37,203,40,24,27,57,38,43,24,172,33,5*49

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,29,5*45

$GBRMC,142340.000,A,2301.2582900,N,11421.9430226,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142340.000,3.514,0.185,0.183,0.267,2.401,2.412,3.581*7E



2025-07-31 22:23:40:489 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:23:40:519 ==>>                                               

2025-07-31 22:23:40:701 ==>> [D][05:19:31][CAT1]pdpdeact urc len[22]


2025-07-31 22:23:40:881 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 22:23:41:362 ==>> $GBGGA,142341.000,2301.2582907,N,11421.9430411,E,1,23,0.57,80.706,M,-1.770,M,,*57

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.15,0.57,0.99,4*03

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,13,32,,1.15,0.57,0.99,4*0E

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,41,25,49,346,41,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,112,41,33,37,203,40,1*78

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,24,27,57,38,1*41

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56,36,1*73

$GBGSV,7,7,27,13,15,213,32,14,9,322,33,32,8,319,35,1*4F

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,112,40,33,37,203,40,24,27,57,38,43,24,172,33,5*49

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,28,5*44

$GBRMC,142341.000,A,2301.2582907,N,11421.9430411,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142341.000,2.513,0.189,0.187,0.271,1.827,1.838,3.011*76



2025-07-31 22:23:41:682 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:23:42:777 ==>> [D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:32][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:32][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:32][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:32][CAT1]tx ret[11] >>> AT+QIACT?

[W][05:19:32][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:32][COMM]Main Task receive event:14
[D][05:19:32][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955172, allstateRepSeconds = 0
[D][05:19:32][COMM]index:0,power_mode:0xFF
[D][05:19:32][COMM]index:1,sound_mode:0xFF
[D][05:19:32][COMM]index:2,gsensor_mode:0xFF
[D][05:19:32][COMM]index:3,report_freq_mode:0xFF
[D][05:19:32][COMM]index:4,report_period:0xFF
[D][05:19:32][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:32][COMM]index:6,normal_reset_period:0xFF
[D][05:19:32][COMM]index:7,spock_over_speed:0xFF
[D][05:19:32][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:32][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:32][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:32][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:32][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:32][COMM]index:13,spock_audio_volumn:0xFF

2025-07-31 22:23:42:882 ==>> 
[D][05:19:32][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:32][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:32][COMM]index:16,imu_config_params:0xFF
[D][05:19:32][COMM]index:17,long_connect_params:0xFF
[D][05:19:32][COMM]index:18,detain_mark:0xFF
[D][05:19:32][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:32][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][COMM]index:21,mc_mode:0xFF
[D][05:19:32][COMM]index:22,S_mode:0xFF
[D][05:19:32][COMM]index:23,overweight:0xFF
[D][05:19:32][COMM]index:24,standstill_mode:0xFF
[D][05:19:32][COMM]index:25,night_mode:0xFF
[D][05:19:32][COMM]index:26,experiment1:0xFF
[D][05:19:32][COMM]index:27,experiment2:0xFF
[D][05:19:32][COMM]index:28,experiment3:0xFF
[D][05:19:32][COMM]index:29,experiment4:0xFF
[D][05:19:32][COMM]index:30,night_mode_start:0xFF
[D][05:19:32][COMM]index:31,night_mode_end:0xFF
[D][05:19:32][COMM]index:33,park_report_minutes:0xFF
[D][05:19:32][COMM]index:34,park_report_mode:0xFF
[D][05:19:32][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:32][COMM]index:38,charge_battery_para: FF
[D][05:19:32][COMM]index:39,multirider_mode:0xFF
[D][05:19:32][COMM]index

2025-07-31 22:23:42:987 ==>> :40,mc_launch_mode:0xFF
[D][05:19:32][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:32][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:32][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:32][COMM]index:44,riding_duration_config:0xFF
[D][05:19:32][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:32][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:32][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:32][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:32][COMM]index:49,mc_load_startup:0xFF
[D][05:19:32][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:32][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:32][COMM]index:52,traffic_mode:0xFF
[D][05:19:32][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:32][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:32][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:32][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:32][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:32][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:32][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:32][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:32][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:32][COMM]index:

2025-07-31 22:23:43:092 ==>> 63,experiment5:0xFF
[D][05:19:32][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:32][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:32][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:32][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:32][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:32][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:32][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:32][COMM]index:72,experiment6:0xFF
[D][05:19:32][COMM]index:73,experiment7:0xFF
[D][05:19:32][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:32][COMM]index:75,zero_value_from_server:-1
[D][05:19:32][COMM]index:76,multirider_threshold:255
[D][05:19:32][COMM]index:77,experiment8:255
[D][05:19:32][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:32][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:32][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:32][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:32][COMM]index:83,loc_report_interval:255
[D][05:19:32][COMM]index:84,multirider_threshold_p2:255
[D][05:19:32][COMM]index:85,multirider_strategy:255
[D][05:19:32][COMM]index:81,camera_park_similar_thr_cfg

2025-07-31 22:23:43:197 ==>> :0xFF
[D][05:19:32][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:32][COMM]index:90,weight_param:0xFF
[D][05:19:32][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:32][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:32][COMM]index:95,current_limit:0xFF
[D][05:19:32][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:32][COMM]index:100,location_mode:0xFF

[W][05:19:32][PROT]remove success[1629955172],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:32][PROT]add success [1629955172],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]exec over: func id: 8, ret: 6
[D][05:19:32][CAT1]gsm read msg sub id: 13
[D][05:19:32][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:32][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:32][CAT1]exec over: func id: 13, ret: 21
[D][05:19:32][M2M ]get csq[26]
$GBGGA,142342.000,2301.2582890,N,11421.9430434,E,1,23,0.57,80.572,

2025-07-31 22:23:43:302 ==>> M,-1.770,M,,*5D

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.15,0.57,0.99,4*03

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,13,32,,1.15,0.57,0.99,4*0E

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,41,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,41,25,49,346,41,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,112,40,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,37,24,27,57,38,1*41

$GBGSV,7,6,27,43,24,172,36,5,22,256,34,23,21,291,36,44,18,56,36,1*73

$GBGSV,7,7,27,13,15,213,33,14,9,322,33,32,8,319,35,1*4E

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,112,40,33,37,203,40,24,27,57,38,43,24,172,33,5*49

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,28,5*44

$GBRMC,142342.000,A,2301.2582890,N,11421.9430434,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,142342.000,3.617,0.194,0.191,0.279,2.448,2.456,3.463*70

[D][05:19:32][CAT1]opened : 0, 0
[D][05:19:32][SAL ]Cellular task submsg id[68]
[D][05:19:32][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:32][SAL ]s

2025-07-31 22:23:43:407 ==>> ocket connect ind. id[4], rst[3]
[D][05:19:32][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:32][M2M ]g_m2m_is_idle become true
[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:32][PROT]index:0 1629955172
[D][05:19:32][PROT]is_send:0
[D][05:19:32][PROT]sequence_num:12
[D][05:19:32][PROT]retry_timeout:0
[D][05:19:32][PROT]retry_times:1
[D][05:19:32][PROT]send_path:0x2
[D][05:19:32][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:32][PROT]===========================================================
[W][05:19:32][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955172]
[D][05:19:32][PROT]===========================================================
[D][05:19:32][PROT]sending traceid [999999999990000D]
[D][05:19:32][PROT]Send_TO_M2M [1629955172]
[D][05:19:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:32][SAL ]sock send credit cnt[6]
[D][05:19:32][SAL ]sock send ind credit cnt[6]
[D][05:19:32][M2M ]m2m send data len[

2025-07-31 22:23:43:512 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:23:43:542 ==>>                 

2025-07-31 22:23:44:359 ==>> $GBGGA,142344.000,2301.2582943,N,11421.9430533,E,1,23,0.57,80.245,M,-1.770,M,,*51

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.15,0.57,0.99,4*03

$GBGSA,A,3,60,34,33,11,12,24,43,23,44,13,32,,1.15,0.57,0.99,4*0E

$GBGSV,7,1,27,40,62,168,41,3,61,190,41,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,40,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,112,40,33,37,203,40,1*79

$GBGSV,7,5,27,11,33,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*40

$GBGSV,7,6,27,43,24,172,35,5,22,256,33,23,21,291,36,44,18,56,36,1*77

$GBGSV,7,7,27,13,15,213,32,14,9,322,32,32,8,319,35,1*4E

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,40,41,43,273,43,5*44

$GBGSV,3,2,11,34,41,112,39,33,37,203,40,24,27,57,38,43,24,172,33,5*47

$GBGSV,3,3,11,23,21,291,38,44,18,56,35,32,8,319,29,5*44

$GBRMC,142344.000,A,2301.2582943,N,11421.9430533,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,142344.000,3.510,0.188,0.185,0.271,2.387,2.393,3.309*73



2025-07-31 22:23:44:888 ==>> [D][05:19:35][COMM]read battery soc:255


2025-07-31 22:23:45:384 ==>> $GBGGA,142345.000,2301.2582873,N,11421.9430445,E,1,24,0.55,80.185,M,-1.770,M,,*58

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.11,0.55,0.96,4*0A

$GBGSA,A,3,60,34,33,11,12,24,43,23,14,44,13,32,1.11,0.55,0.96,4*02

$GBGSV,7,1,27,40,62,168,40,3,61,190,41,7,60,193,39,39,59,32,40,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,40,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,36,1,45,125,39,2,45,237,36,1*45

$GBGSV,7,4,27,41,43,273,41,60,42,239,41,34,41,112,40,33,37,203,39,1*77

$GBGSV,7,5,27,11,34,143,37,4,32,111,34,12,31,79,36,24,27,57,37,1*48

$GBGSV,7,6,27,43,24,172,35,5,22,256,34,23,21,291,36,14,20,179,32,1*46

$GBGSV,7,7,27,44,18,56,36,13,15,213,32,32,8,319,35,1*4F

$GBGSV,3,1,11,40,62,168,41,39,59,32,41,25,49,346,41,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,112,40,33,37,203,40,24,27,57,38,43,24,172,33,5*49

$GBGSV,3,3,11,23,21,291,38,44,18,56,34,32,8,319,29,5*45

$GBRMC,142345.000,A,2301.2582873,N,11421.9430445,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142345.000,3.376,0.229,0.222,0.327,2.313,2.319,3.207*70



2025-07-31 22:23:46:376 ==>> $GBGGA,142346.000,2301.2582878,N,11421.9430353,E,1,24,0.55,80.136,M,-1.770,M,,*58

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.11,0.55,0.96,4*0A

$GBGSA,A,3,60,34,33,11,12,24,43,23,14,44,13,32,1.11,0.55,0.96,4*02

$GBGSV,7,1,27,40,62,168,40,3,61,190,41,7,60,193,39,39,59,32,40,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,40,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,36,1,45,125,39,2,45,237,36,1*45

$GBGSV,7,4,27,41,43,273,41,60,42,239,40,34,41,112,40,33,37,203,39,1*76

$GBGSV,7,5,27,11,34,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*47

$GBGSV,7,6,27,43,24,172,35,5,22,256,34,23,21,291,36,14,20,179,32,1*46

$GBGSV,7,7,27,44,18,56,36,13,15,213,32,32,8,319,35,1*4F

$GBGSV,3,1,11,40,62,168,41,39,59,32,40,25,49,346,41,41,43,273,43,5*44

$GBGSV,3,2,11,34,41,112,39,33,37,203,40,24,27,57,38,43,24,172,33,5*47

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,29,5*45

$GBRMC,142346.000,A,2301.2582878,N,11421.9430353,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,142346.000,3.270,0.172,0.167,0.248,2.254,2.259,3.122*70



2025-07-31 22:23:46:888 ==>> [D][05:19:37][COMM]read battery soc:255


2025-07-31 22:23:47:360 ==>> $GBGGA,142347.000,2301.2582863,N,11421.9430307,E,1,24,0.55,80.086,M,-1.770,M,,*58

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.11,0.55,0.96,4*0A

$GBGSA,A,3,60,34,33,11,12,24,43,23,14,44,13,32,1.11,0.55,0.96,4*02

$GBGSV,7,1,27,40,62,168,40,3,61,190,40,7,60,193,39,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,40,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,40,34,41,112,40,33,37,203,39,1*76

$GBGSV,7,5,27,11,34,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*47

$GBGSV,7,6,27,43,24,172,35,5,22,256,34,23,21,291,36,14,20,179,32,1*46

$GBGSV,7,7,27,44,18,56,36,13,15,213,32,32,8,319,35,1*4F

$GBGSV,3,1,11,40,62,168,41,39,59,32,40,25,49,346,41,41,43,273,43,5*44

$GBGSV,3,2,11,34,41,112,39,33,37,203,40,24,27,57,38,43,24,172,33,5*47

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,28,5*44

$GBRMC,142347.000,A,2301.2582863,N,11421.9430307,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142347.000,3.279,0.195,0.190,0.279,2.257,2.262,3.097*7F



2025-07-31 22:23:47:706 ==>> [D][05:19:38][PROT]CLEAN,SEND:0
[D][05:19:38][PROT]index:1 1629955178
[D][05:19:38][PROT]is_send:0
[D][05:19:38][PROT]sequence_num:13
[D][05:19:38][PROT]retry_timeout:0
[D][05:19:38][PROT]retry_times:1
[D][05:19:38][PROT]send_path:0x2
[D][05:19:38][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:38][PROT]===========================================================
[W][05:19:38][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955178]
[D][05:19:38][PROT]===========================================================
[D][05:19:38][PROT]sending traceid [999999999990000E]
[D][05:19:38][PROT]Send_TO_M2M [1629955178]
[D][05:19:38][PROT]CLEAN:0
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:38][SAL ]sock send credit cnt[6]
[D][05:19:38][SAL ]sock send ind credit cnt[6]
[D][05:19:38][M2M ]m2m send data len[166]
[D][05:19:38][SAL ]Cellular task submsg id[10]
[D][05:19:38][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd8] format[0]
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:38][CAT1]gsm read msg sub id: 15
[D][05:19:38][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:38][CAT1]Send Data To Server[166][169] ... ->:
0053B984113311331133113311331B88BA7CD5400369092DAE83D262F837CC5E42D9EB3F3386F000A50D3C7DDAC71

2025-07-31 22:23:47:731 ==>> 未匹配到【4G联网测试】数据,请核对检查!
2025-07-31 22:23:47:745 ==>> #################### 【测试结束】 ####################
2025-07-31 22:23:47:764 ==>> 关闭5V供电
2025-07-31 22:23:47:778 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:23:47:807 ==>> 8ED35C6A0B4F579098562EFD1FA1ECF2980875CC60298B06B4EBECC8464BFC072DAEC5F94
[D][05:19:38][CAT1]<<< 
SEND OK

[D][05:19:38][CAT1]exec over: func id: 15, ret: 11
[D][05:19:38][CAT1]sub id: 15, ret: 11

[D][05:19:38][SAL ]Cellular task submsg id[68]
[D][05:19:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:38][M2M ]g_m2m_is_idle become true
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:38][PROT]M2M Send ok [1629955178]


2025-07-31 22:23:47:902 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:23:48:386 ==>> $GBGGA,142348.000,2301.2582813,N,11421.9430320,E,1,25,0.54,80.034,M,-1.770,M,,*5C

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.09,0.54,0.94,4*00

$GBGSA,A,3,60,34,33,11,12,24,43,05,23,14,44,13,1.09,0.54,0.94,4*0C

$GBGSA,A,3,32,,,,,,,,,,,,1.09,0.54,0.94,4*0D

$GBGSV,7,1,27,40,62,168,41,3,61,190,40,7,60,193,38,39,59,32,40,1*45

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,40,1*44

$GBGSV,7,3,27,9,49,340,36,10,49,203,36,1,45,125,39,2,45,237,36,1*45

$GBGSV,7,4,27,41,43,273,40,60,42,239,40,34,41,112,40,33,37,203,39,1*77

$GBGSV,7,5,27,11,34,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*47

$GBGSV,7,6,27,43,24,172,35,5,23,257,34,23,21,291,36,14,20,179,32,1*46

$GBGSV,7,7,27,44,18,56,36,13,15,213,32,32,8,319,35,1*4F

$GBGSV,3,1,11,40,62,168,41,39,59,32,40,25,49,346,40,41,43,273,43,5*45

$GBGSV,3,2,11,34,41,112,39,33,37,203,40,24,27,57,38,43,24,172,33,5*47

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,29,5*45

$GBRMC,142348.000,A,2301.2582813,N,11421.9430320,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,142348.000,3.030,0.200,0.197,0.292,2.117,2.121,2.945*76



2025-07-31 22:23:48:778 ==>> 关闭5V供电成功
2025-07-31 22:23:48:792 ==>> 关闭33V供电
2025-07-31 22:23:48:820 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:23:48:918 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150
[D][05:19:39][COMM]read battery soc:255


2025-07-31 22:23:49:023 ==>> [D][05:19:39][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 5,

2025-07-31 22:23:49:068 ==>> volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:39][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 4


2025-07-31 22:23:49:387 ==>> $GBGGA,142349.000,2301.2582743,N,11421.9430293,E,1,25,0.54,79.963,M,-1.770,M,,*53

$GBGSA,A,3,40,03,07,39,06,16,59,25,09,10,01,41,1.09,0.54,0.94,4*00

$GBGSA,A,3,60,34,33,11,12,24,43,05,23,14,44,13,1.09,0.54,0.94,4*0C

$GBGSA,A,3,32,,,,,,,,,,,,1.09,0.54,0.94,4*0D

$GBGSV,7,1,27,40,62,168,41,3,61,190,40,7,60,193,39,39,59,32,40,1*44

$GBGSV,7,2,27,6,57,3,36,16,56,7,38,59,49,130,40,25,49,346,41,1*45

$GBGSV,7,3,27,9,49,340,36,10,49,203,37,1,45,125,39,2,45,237,36,1*44

$GBGSV,7,4,27,41,43,273,41,60,42,239,40,34,41,112,40,33,37,203,40,1*78

$GBGSV,7,5,27,11,34,143,37,4,32,111,34,12,31,79,36,24,27,57,38,1*47

$GBGSV,7,6,27,43,24,172,36,5,23,257,34,23,21,291,36,14,20,179,32,1*45

$GBGSV,7,7,27,44,18,56,36,13,15,213,33,32,8,319,35,1*4E

$GBGSV,3,1,11,40,62,168,41,39,59,32,40,25,49,346,41,41,43,273,43,5*44

$GBGSV,3,2,11,34,41,112,39,33,37,203,40,24,27,57,38,43,24,172,33,5*47

$GBGSV,3,3,11,23,21,291,39,44,18,56,35,32,8,319,29,5*45

$GBRMC,142349.000,A,2301.2582743,N,11421.9430293,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,142349.000,3.046,0.182,0.179,0.265,2.125,2.129,2.931*7D



2025-07-31 22:23:49:782 ==>> 关闭33V供电成功
2025-07-31 22:23:49:796 ==>> 关闭3.7V供电
2025-07-31 22:23:49:809 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:23:49:905 ==>> 6A A6 02 A6 6A 


2025-07-31 22:23:49:996 ==>> Battery OFF
OVER 150


2025-07-31 22:23:50:305 ==>>  

2025-07-31 22:23:50:789 ==>> 关闭3.7V供电成功
