2025-07-31 22:50:00:939 ==>> MES查站成功:
查站序号:P51000100531264E验证通过
2025-07-31 22:50:00:943 ==>> 扫码结果:P51000100531264E
2025-07-31 22:50:00:945 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:50:00:947 ==>> 测试参数版本:2024.10.11
2025-07-31 22:50:00:949 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:50:00:951 ==>> 检测【打开透传】
2025-07-31 22:50:00:953 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:50:01:009 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:50:02:826 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:50:02:832 ==>> 检测【检测接地电压】
2025-07-31 22:50:02:837 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:50:02:909 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:50:03:133 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:50:03:135 ==>> 检测【打开小电池】
2025-07-31 22:50:03:138 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:50:03:215 ==>> 6A A6 01 A6 6A 


2025-07-31 22:50:03:305 ==>> Battery ON
OVER 150


2025-07-31 22:50:03:439 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:50:03:442 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:50:03:474 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:50:03:518 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:50:03:724 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:50:03:726 ==>> 检测【等待设备启动】
2025-07-31 22:50:03:728 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:50:04:150 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:50:04:314 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 22:50:04:762 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:50:04:885 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:50:05:065 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:50:05:662 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:50:05:797 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:50:05:857 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:50:06:501 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 22:50:06:546 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:50:06:831 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:50:06:952 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:50:07:420 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:50:07:620 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:50:07:623 ==>> 检测【产品通信】
2025-07-31 22:50:07:625 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:50:08:131 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:50:08:313 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:50:08:647 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:50:08:965 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<


2025-07-31 22:50:09:010 ==>>                       rt sing locating


2025-07-31 22:50:09:437 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:50:09:681 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:50:09:976 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:10:221 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:50:10:236 ==>> 检测【初始化完成检测】
2025-07-31 22:50:10:238 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:50:10:435 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:50:10:499 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:50:10:501 ==>> 检测【关闭大灯控制1】
2025-07-31 22:50:10:503 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:50:10:540 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:50:10:675 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:50:10:768 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:50:10:771 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:50:10:773 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:50:11:141 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:50:11:303 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:50:11:305 ==>> 检测【关闭仪表供电】
2025-07-31 22:50:11:308 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:50:11:506 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:50:11:584 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:50:11:587 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:50:11:589 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:50:11:766 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:50:11:865 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:50:11:867 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:50:11:869 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:50:11:961 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:12:066 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:50:12:144 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:50:12:147 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:50:12:149 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:12:294 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:50:12:418 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:50:12:421 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:50:12:422 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:50:12:507 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:50:12:567 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 16


2025-07-31 22:50:12:627 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 22:50:12:704 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:50:12:706 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:50:12:708 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:50:12:809 ==>> 5A A5 03 5A A5 


2025-07-31 22:50:12:914 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 22:50:12:974 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:12:978 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:50:12:980 ==>> 该项需要延时执行
2025-07-31 22:50:13:490 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011

2025-07-31 22:50:13:520 ==>> 
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 22:50:13:983 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:14:180 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:50:14:662 ==>>       :17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[han

2025-07-31 22:50:14:768 ==>> dlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]=====

2025-07-31 22:50:14:874 ==>> ======================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074]

2025-07-31 22:50:14:918 ==>> ,send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 22:50:14:994 ==>>                                              05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:15:189 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 22:50:15:993 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:16:647 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:50:16:980 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:50:16:997 ==>> 检测【33V输入电压ADC】
2025-07-31 22:50:17:000 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:17:011 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:17:328 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:17:57][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:57][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read right brake adc:1  volt:1 mv
[D][05:17:57][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:57][COMM]adc read battery ts volt:13 mv
[D][05:17:57][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:50:17:513 ==>> 【33V输入电压ADC】通过,【32804mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:50:17:516 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:50:17:519 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:50:17:618 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1655mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1992mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:50:17:798 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:17:800 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:50:17:816 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:17:821 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:50:17:824 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 22:50:17:846 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:50:17:848 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:50:17:864 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:50:17:867 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:50:17:887 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:50:17:890 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:50:18:042 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1655mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150
[D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:18:171 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:18:174 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:50:18:189 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:18:191 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:50:18:195 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 22:50:18:208 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:50:18:210 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:50:18:226 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:50:18:229 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:50:18:249 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:50:18:251 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:50:18:316 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1655mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:50:18:375 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10019
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020


2025-07-31 22:50:18:537 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:18:540 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:50:18:556 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:18:558 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:50:18:560 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 22:50:18:576 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:50:18:580 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:50:18:596 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:50:18:598 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:50:18:618 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:50:18:621 ==>> 检测【打开WIFI(1)】
2025-07-31 22:50:18:635 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:50:18:649 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:50:18:799 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:50:18:892 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:50:18:897 ==>> 检测【清空消息队列(1)】
2025-07-31 22:50:18:921 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:50:19:392 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10718 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first

2025-07-31 22:50:19:425 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:50:19:429 ==>> 检测【打开GPS(1)】
2025-07-31 22:50:19:431 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:50:19:497 ==>>  set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[13] [17] 

2025-07-31 22:50:19:542 ==>> 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 22:50:19:647 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:50:19:695 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:50:19:698 ==>> 检测【打开GSM联网】
2025-07-31 22:50:19:701 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:50:19:902 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 22:50:19:965 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:50:19:967 ==>> 检测【打开仪表供电1】
2025-07-31 22:50:19:969 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:50:20:006 ==>> [

2025-07-31 22:50:20:036 ==>> D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:50:20:201 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:50:20:321 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:50:20:329 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:50:20:334 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:50:20:501 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:50:20:651 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:50:20:687 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:50:20:691 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:50:20:712 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:50:20:894 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33293]


2025-07-31 22:50:20:962 ==>> 【读取主控ADC采集的仪表电压】通过,【33293mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:50:20:965 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:50:20:968 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:50:21:212 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:50:21:245 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:50:21:248 ==>> 检测【AD_V20电压】
2025-07-31 22:50:21:252 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:50:21:348 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:50:21:410 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:50:21:638 ==>> 本次取值间隔时间:288ms
2025-07-31 22:50:21:713 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087958981

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071541276

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02]

2025-07-31 22:50:21:730 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:50:21:743 ==>> [CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:50:21:833 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:50:21:908 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:50:22:304 ==>> 本次取值间隔时间:457ms
2025-07-31 22:50:22:323 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:50:22:425 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:50:22:517 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 22:50:22:667 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 22:50:22:817 ==>> 本次取值间隔时间:390ms
2025-07-31 22:50:22:835 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:50:22:940 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:50:23:047 ==>> 1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]14731 imu init OK


2025-07-31 22:50:23:304 ==>> [D][05:18:03][COMM]S->M yaw:INVALID
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:50:23:364 ==>> 本次取值间隔时间:412ms
2025-07-31 22:50:23:383 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:23:386 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:50:23:389 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:50:23:592 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:50:23:698 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 22:50:23:735 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:50:23:738 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:50:23:742 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:50:23:746 ==>>                                                                                                                                                                  [CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:50:23:907 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:50:24:084 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:50:24:089 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:50:24:108 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:50:24:287 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

3A A3 02 01 A3 
ON_OUT2
OVER 150

2025-07-31 22:50:24:302 ==>> 


2025-07-31 22:50:24:417 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:50:24:422 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:50:24:426 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:50:24:609 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:50:24:700 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:50:24:703 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:50:24:707 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:50:24:714 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 22:50:24:909 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:50:24:978 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:50:24:981 ==>> 检测【AD_V21电压】
2025-07-31 22:50:24:985 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:50:24:990 ==>>             0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:50:25:089 ==>> 1A A1 20 00 00 
Get AD_V21 1043mV
OVE

2025-07-31 22:50:25:104 ==>> R 150


2025-07-31 22:50:25:333 ==>> 本次取值间隔时间:346ms
2025-07-31 22:50:25:351 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:50:25:411 ==>> 1A A1 20 00 00 
Get AD_V21 1641mV
OVER 150


2025-07-31 22:50:25:456 ==>> 本次取值间隔时间:93ms
2025-07-31 22:50:25:474 ==>> 【AD_V21电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:25:479 ==>> 检测【关闭仪表供电2】
2025-07-31 22:50:25:502 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:50:25:880 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

[D][05:18:06][CAT1]<<< 
OK

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,42,25,,,41,39,,,41,40,,,41,1*7B

$GBGSV,3,2,09,33,,,39,34,,,39,60,,,39,41,,,34,1*77

$GBGSV,3,3,09,24,,,38,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.571,1637.571,52.353,2097152,2097152,2097152*4D

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 22:50:26:004 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:50:26:007 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:50:26:012 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:50:26:061 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:50:26:166 ==>> [

2025-07-31 22:50:26:196 ==>> W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:50:26:275 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:50:26:280 ==>> 检测【打开AccKey2供电】
2025-07-31 22:50:26:284 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:50:26:468 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:50:26:550 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:50:26:553 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:50:26:556 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:26:875 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:07][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:07][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:07][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:07][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:07][COMM]adc read battery ts volt:14 mv
[D][05:18:07][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,59,,,42,40,,,42,25,,,41,39,,,41,1*72

$GBGSV,4,2,15,34,,,41,60,,,40,7,,,40,33,,,39,1*4D

$GBGSV,4,3,15,11,,,39,16,,,39,41,,,37,24,,,34,1*72

$GBGSV,4,4,15,6,,,34,3,,,37,5,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1623.232,1623.232,51.906,2097152,2097152,2097152*44

[D][05:18:07][COMM]read battery soc:255
[D][05:18:0

2025-07-31 22:50:26:904 ==>> 7][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:50:27:097 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33184mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:50:27:100 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:50:27:103 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:50:27:267 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:50:27:381 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:50:27:384 ==>> 该项需要延时执行
2025-07-31 22:50:27:812 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,59,,,41,25,,,41,39,,,41,1*76

$GBGSV,5,2,20,34,,,41,60,,,40,7,,,40,33,,,39,1*4A

$GBGSV,5,3,20,11,,,39,16,,,39,41,,,39,3,,,39,1*43

$GBGSV,5,4,20,1,,,39,43,,,38,2,,,37,24,,,36,1*77

$GBGSV,5,5,20,6,,,36,5,,,35,4,,,33,32,,,37,1*45

$GBRMC,,V,,,,,,,310725,0.1,E,N,V*53

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1603.749,1603.749,51.271,2097152,2097152,2097152*4F



2025-07-31 22:50:28:834 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,145032.665,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,39,,,41,59,,,41,34,,,41,1*74

$GBGSV,6,2,21,25,,,41,7,,,40,60,,,40,3,,,40,1*75

$GBGSV,6,3,21,41,,,40,16,,,39,11,,,39,1,,,38,1*4C

$GBGSV,6,4,21,33,,,38,43,,,38,10,,,37,2,,,37,1*43

$GBGSV,6,5,21,24,,,37,6,,,36,5,,,35,4,,,33,1*40

$GBGSV,6,6,21,32,,,,1*74

$GBRMC,145032.665,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145032.665,0.000,799.650,799.650,731.293,2097152,2097152,2097152*68



2025-07-31 22:50:29:737 ==>> $GBGGA,145033.565,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,39,,,42,59,,,41,34,,,41,1*74

$GBGSV,6,2,22,25,,,41,41,,,41,7,,,40,60,,,40,1*41

$GBGSV,6,3,22,3,,,40,16,,,39,11,,,39,43,,,39,1*4E

$GBGSV,6,4,22,1,,,38,33,,,38,10,,,37,24,,,37,1*42

$GBGSV,6,5,22,6,,,37,2,,,36,44,,,36,12,,,36,1*73

$GBGSV,6,6,22,5,,,34,4,,,33,1*70

$GBRMC,145033.565,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145033.565,0.000,796.557,796.557,728.465,2097152,2097152,2097152*6D



2025-07-31 22:50:30:396 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:50:30:401 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:50:30:406 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:30:838 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:11][COMM]adc read battery ts volt:7 mv
[D][05:18:11][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
$GBGGA,145034.545,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,43,39,,,41,59,,,41,34,,,41,1*77

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,44,,,35,1*71

$GBGSV,6,6,23,5,,,34,9,,,33,4,,,33,1*48

$GBRMC,145034.545,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145034.545,0.000,789.823,789.823,722.

2025-07-31 22:50:30:868 ==>> 309,2097152,2097152,2097152*6F

[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:11][COMM]read battery soc:255


2025-07-31 22:50:30:943 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:50:30:949 ==>> 检测【打开AccKey1供电】
2025-07-31 22:50:30:961 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:50:31:096 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:50:31:220 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:50:31:224 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:50:31:229 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:50:31:308 ==>> 1A A1 00 40 00 
Get AD_V14 2665mV
OVER 150


2025-07-31 22:50:31:474 ==>> 原始值:【2665】, 乘以分压基数【2】还原值:【5330】
2025-07-31 22:50:31:497 ==>> 【读取AccKey1电压(ADV14)前】通过,【5330mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:50:31:500 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:50:31:504 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:31:838 ==>> $GBGGA,145035.525,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,43,,,39,1*4F

$GBGSV,6,4,23,1,,,38,33,,,38,10,,,37,24,,,37,1*43

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,44,,,35,1*71

$GBGSV,6,6,23,5,,,34,9,,,34,4,,,33,1*4F

$GBRMC,145035.525,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145035.525,0.000,790.715,790.715,723.124,2097152,2097152,2097152*64

[W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:12][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:12][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:12][COMM]adc read battery ts volt:11 mv
[D][05:18:12][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:12][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:12][COMM]arm_hub adc 

2025-07-31 22:50:31:867 ==>> read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:50:32:037 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5516mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:50:32:046 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:50:32:068 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:50:32:201 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:50:32:308 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:50:32:312 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:50:32:316 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:50:32:413 ==>> 1A A1 00 40 00 
Get AD_V14 2664mV
OVER 150


2025-07-31 22:50:32:562 ==>> 原始值:【2664】, 乘以分压基数【2】还原值:【5328】
2025-07-31 22:50:32:580 ==>> 【读取AccKey1电压(ADV14)后】通过,【5328mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:50:32:584 ==>> 检测【打开WIFI(2)】
2025-07-31 22:50:32:589 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:50:32:700 ==>> $GBGGA,145036.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,3,,,41,39,,,41,59,,,41,1*42

$GBGSV,6,2,23,34,,,41,25,,,41,41,,,41,7,,,40,1*40

$GBGSV,6,3,23,60,,,40,16,,,39,11,,,39,43,,,39,1*7A

$GBGSV,6,4,23,1,,,38,33,,,38,10,,,37,24,,,37,1*43

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145036.505,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145036.505,0.000,792.513,792.513,724.768,2097152,2097152,2097152*6C



2025-07-31 22:50:32:805 ==>>                                          [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][C

2025-07-31 22:50:32:835 ==>> AT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:50:32:911 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:50:32:914 ==>> 检测【转刹把供电】
2025-07-31 22:50:32:923 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:50:33:104 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:50:33:192 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:50:33:196 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:50:33:198 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:50:33:302 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:50:33:378 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:50:33:408 ==>> 1A A1 00 80 00 
Get AD_V15 2395mV
OVER 150


2025-07-31 22:50:33:454 ==>> 原始值:【2395】, 乘以分压基数【2】还原值:【4790】
2025-07-31 22:50:33:495 ==>> 【读取AD_V15电压(前)】通过,【4790mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:50:33:501 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:50:33:506 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:50:33:605 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:50:33:742 ==>> $GBGGA,145037.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145037.505,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145037.505,0.000,790.712,790.712,723.120,2097152,2097152,2097152*60

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2424mV
OVER 150
                                                                                                                                                                                

2025-07-31 22:50:33:757 ==>> 原始值:【2424】, 乘以分压基数【2】还原值:【4848】
2025-07-31 22:50:33:790 ==>> 【读取AD_V16电压(前)】通过,【4848mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:50:33:794 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:50:33:798 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:34:151 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:14][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:14][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:14][COMM]adc read battery ts volt:6 mv
[D][05:18:14][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3072  volt:5400 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 22:50:34:319 ==>> 【转刹把供电电压(主控ADC)】通过,【5400mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:50:34:323 ==>> 检测【转刹把供电电压】
2025-07-31 22:50:34:328 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:34:727 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:15][COMM]adc read battery ts volt:9 mv
[D][05:18:15][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3070  volt:5396 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,145038.505,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,3

2025-07-31 22:50:34:787 ==>> 6,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145038.505,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145038.505,0.000,790.712,790.712,723.121,2097152,2097152,2097152*6E

                                         

2025-07-31 22:50:34:864 ==>> 【转刹把供电电压】通过,【5396mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:50:34:867 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:50:34:872 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:35:091 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:50:35:139 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:50:35:143 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:50:35:152 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:35:241 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:50:35:317 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:50:35:366 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:50:35:369 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:50:35:372 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:35:468 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:50:35:573 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:35:680 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:50:35:685 ==>> $GBGGA,145039.505,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,9,,,36,12,,,36,1*4B

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145039.505,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145039.505,0.000,791.609,791.609,723.941,2097152,2097152,2097152*61



2025-07-31 22:50:35:786 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:35:893 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:50:35:898 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 22:50:35:999 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:50:36:005 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:50:36:104 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:50:36:210 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:50:36:239 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:50:36:244 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:50:36:257 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:50:36:314 ==>> 3A A3 03 01 A3 


2025-07-31 22:50:36:405 ==>> ON_OUT3
OVER 150


2025-07-31 22:50:36:522 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:50:36:527 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:50:36:533 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:50:36:677 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150
$GBGGA,145040.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,3,,,41,39,,,41,59,,,41,1*42

$GBGSV,6,2,23,34,,,41,25,,,41,41,,,41,7,,,40,1*40

$GBGSV,6,3,23,60,,,40,16,,,39,11,,,39,1,,,38,1*4D

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,34,1*70

$GBRMC,145040.505,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145040.505,0.000,792.509,792.509,724.763,2097152,2097152,2097152*66



2025-07-31 22:50:36:768 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:50:36:802 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:50:36:805 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:50:36:810 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:50:36:904 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:50:37:097 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:50:37:100 ==>> 检测【左刹电压测试1】
2025-07-31 22:50:37:118 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:37:434 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:1724  volt:2272 mv
[D][05:18:17][COMM]adc read right brake adc:1726  volt:2275 mv
[D][05:18:17][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:17][COMM]adc read battery ts volt:14 mv
[D][05:18:17][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:17][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:50:37:636 ==>> 【左刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 22:50:37:641 ==>> 检测【右刹电压测试1】
2025-07-31 22:50:37:658 ==>> 【右刹电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 22:50:37:664 ==>> 检测【转把电压测试1】
2025-07-31 22:50:37:676 ==>> $GBGGA,145041.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,3,,,41,39,,,41,59,,,41,1*42

$GBGSV,6,2,23,34,,,41,25,,,41,41,,,41,7,,,40,1*40

$GBGSV,6,3,23,60,,,40,16,,,39,1,,,39,11,,,39,1*4C

$GBGSV,6,4,23,24,,,38,33,,,38,43,,,38,10,,,37,1*7A

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,34,1*70

$GBRMC,145041.505,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145041.505,0.000,794.307,794.307,726.408,2097152,2097152,2097152*6B



2025-07-31 22:50:37:702 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 22:50:37:706 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:50:37:727 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:50:37:812 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:50:37:981 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:50:37:989 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:50:38:010 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:50:38:111 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:50:38:266 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:50:38:271 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:50:38:280 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:50:38:405 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:50:38:568 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:50:38:573 ==>> 检测【左刹电压测试2】
2025-07-31 22:50:38:578 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:50:38:681 ==>> $GBGGA,145042.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,9,,,36,12,,,36,1*4B

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145042.505,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145042.505,0.000,791.609,791.609,723.941,2097152,2097152,2097152*6D



2025-07-31 22:50:38:923 ==>> [D][05:18:19][COMM]read battery soc:255
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:19][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:11 mv
[D][05:18:19][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:50:39:142 ==>> 【左刹电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 22:50:39:148 ==>> 检测【右刹电压测试2】
2025-07-31 22:50:39:179 ==>> 【右刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:50:39:185 ==>> 检测【转把电压测试2】
2025-07-31 22:50:39:220 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:50:39:228 ==>> 检测【晶振检测】
2025-07-31 22:50:39:233 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:50:39:371 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 22:50:39:523 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:50:39:529 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:50:39:542 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:50:39:676 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1656mV
Get AD_V4 1651mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150
$GBGGA,145043.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,41,25,,,41,1*7D

$GBGSV,6,2,23,41,,,41,7,,,40,60,,,40,3,,,40,1*75

$GBGSV,6,3,23,59,,,40,11,,,39,16,,,38,1,,,38,1*46

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145043.505,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145043.505,0.000,788.911,788.911,721.473,2097152,2097152,2097152*62



2025-07-31 22:50:39:821 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:50:39:830 ==>> 检测【检测BootVer】
2025-07-31 22:50:39:843 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:50:40:188 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071541276
[D][05:18:20][FCTY]HardwareID  = 867222087958981
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = E7B48431010F
[D][05:18:20][FCTY]Bat         = 3964 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1292
[D][05:18:20][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_G

2025-07-31 22:50:40:233 ==>> NSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3710 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:50:40:365 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:50:40:384 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:50:40:388 ==>> 检测【检测固件版本】
2025-07-31 22:50:40:393 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:50:40:400 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:50:40:414 ==>> 检测【检测蓝牙版本】
2025-07-31 22:50:40:421 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:50:40:430 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:50:40:436 ==>> 检测【检测MoBikeId】
2025-07-31 22:50:40:445 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:50:40:450 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:50:40:463 ==>> 检测【检测蓝牙地址】
2025-07-31 22:50:40:469 ==>> 取到目标值:E7B48431010F
2025-07-31 22:50:40:479 ==>> 【检测蓝牙地址】通过,【E7B48431010F】符合目标值【】要求!
2025-07-31 22:50:40:487 ==>> 提取到蓝牙地址:E7B48431010F
2025-07-31 22:50:40:510 ==>> 检测【BOARD_ID】
2025-07-31 22:50:40:516 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:50:40:522 ==>> 检测【检测充电电压】
2025-07-31 22:50:40:541 ==>> 【检测充电电压】通过,【3964mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:50:40:545 ==>> 检测【检测VBUS电压1】
2025-07-31 22:50:40:568 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:50:40:572 ==>> 检测【检测充电电流】
2025-07-31 22:50:40:601 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:50:40:607 ==>> 检测【检测IMEI】
2025-07-31 22:50:40:614 ==>> 取到目标值:867222087958981
2025-07-31 22:50:40:640 ==>> 【检测IMEI】通过,【867222087958981】符合目标值【】要求!
2025-07-31 22:50:40:644 ==>> 提取到IMEI:867222087958981
2025-07-31 22:50:40:652 ==>> 检测【检测IMSI】
2025-07-31 22:50:40:675 ==>> 取到目标值:460130071541276
2025-07-31 22:50:40:678 ==>> 【检测IMSI】通过,【460130071541276】符合目标值【】要求!
2025-07-31 22:50:40:686 ==>> 提取到IMSI:460130071541276
2025-07-31 22:50:40:698 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:50:40:702 ==>> 取到目标值:460130071541276
2025-07-31 22:50:40:716 ==>> 【校验网络运营商(移动)】通过,【460130071541276】符合目标值【】要求!
2025-07-31 22:50:40:721 ==>> $GBGGA,145044.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,60,,,40,3,,,40,41,,,40,1*45

$GBGSV,6,3,23,7,,,39,11,,,39,16,,,38,1,,,38,1*73

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145044.505,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145044.505,0.000,788.010,788.010,720.649,2097152,2097152,2097152*6F



2025-07-31 22:50:40:727 ==>> 检测【打开CAN通信】
2025-07-31 22:50:40:732 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:50:40:773 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:50:40:818 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:50:40:971 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:50:40:979 ==>> 检测【检测CAN通信】
2025-07-31 22:50:40:999 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:50:41:104 ==>> can send success


2025-07-31 22:50:41:134 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:50:41:209 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32873
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:50:41:255 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:50:41:258 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:50:41:262 ==>> 检测【关闭CAN通信】
2025-07-31 22:50:41:267 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:50:41:314 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:50:41:529 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:50:41:536 ==>> 检测【打印IMU STATE】
2025-07-31 22:50:41:543 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:50:41:756 ==>> $GBGGA,145045.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,11,,,39,16,,,38,1,,,38,1*79

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,2,,,36,6,,,36,12,,,36,9,,,35,1*49

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145045.505,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145045.505,0.000,788.914,788.914,721.477,2097152,2097152,2097152*60

[W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:50:41:806 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:50:41:812 ==>> 检测【六轴自检】
2025-07-31 22:50:41:834 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:50:41:996 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:50:42:700 ==>> $GBGGA,145046.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,16,,,39,11,,,39,1,,,38,1*78

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145046.505,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145046.505,0.000,790.712,790.712,723.120,2097152,2097152,2097152*66



2025-07-31 22:50:42:790 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:50:43:694 ==>> $GBGGA,145047.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,59,,,41,34,,,41,1*76

$GBGSV,6,2,23,25,,,41,41,,,41,7,,,40,60,,,40,1*40

$GBGSV,6,3,23,3,,,40,11,,,39,16,,,38,1,,,38,1*79

$GBGSV,6,4,23,33,,,38,43,,,38,10,,,37,24,,,37,1*75

$GBGSV,6,5,23,6,,,37,2,,,36,12,,,36,9,,,35,1*48

$GBGSV,6,6,23,44,,,35,5,,,34,4,,,33,1*77

$GBRMC,145047.505,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145047.505,0.000,789.812,789.812,722.298,2097152,2097152,2097152*66

                                                                                  

2025-07-31 22:50:43:923 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35587 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-15,-6,4042]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 22:50:44:152 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:50:44:157 ==>> 检测【打印IMU STATE2】
2025-07-31 22:50:44:165 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:50:44:301 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:50:44:436 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:50:44:441 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:50:44:449 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:50:44:511 ==>> 5A A5 02 5A A5 


2025-07-31 22:50:44:616 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:50:44:716 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:50:44:721 ==>> 检测【检测VBUS电压2】
2025-07-31 22:50:44:727 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:50:45:780 ==>> $GBGGA,145044.512,2301.2562359,N,11421.9430809,E,1,11,1.03,80.277,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,06,16,25,34,11,41,33,24,,2.12,1.03,1.86,4*0F

$GBGSV,6,1,23,40,69,175,42,7,65,202,40,39,62,39,41,3,60,190,40,1*46

$GBGSV,6,2,23,6,60,8,37,16,59,12,39,59,52,129,41,25,50,2,41,1*7C

$GBGSV,6,3,23,10,50,229,37,1,48,125,38,9,48,333,36,34,46,96,41,1*47

$GBGSV,6,4,23,2,45,237,36,11,43,132,39,60,41,239,41,41,39,257,41,1*4C

$GBGSV,6,5,23,4,32,111,33,33,27,197,38,24,23,67,37,5,21,256,34,1*43

$GBGSV,6,6,23,44,4,181,35,43,,,38,12,,,36,1*77

$GBRMC,145044.512,A,2301.2562359,N,11421.9430809,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:18:25][GNSS]HD8040 GPS
[W][05:18:25][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:25][GNSS]GPS diff_sec 124018339, report 0x42 frame
$GBGST,145044.512,0.657,0.130,0.128,0.204,1.493,1.692,6.231*7D

[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 

2025-07-31 22:50:45:885 ==>> 6,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:18:25][COMM]Main Task receive event:131
[D][05:18:25][COMM]index:0,power_mode:0xFF
[D][05:18:25][COMM]index:1,sound_mode:0xFF
[D][05:18:25][COMM]index:2,gsensor_mode:0xFF
[D][05:18:25][COMM]index:3,report_freq_mode:0xFF
[D][05:18:25][COMM]index:4,report_period:0xFF
[D][05:18:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:25][COMM]index:6,normal_reset_period:0xFF
[D][05:18:25][COMM]index:7,spock_over_speed:0xFF
[D][05:18:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:25][COMM]index:16,imu_config_params:0xFF
[D][05:18:25][COMM]index:17,long_connect_params:0xFF
[D][05:18:25][COMM]index:18,detain_mark:0xFF
[D][05:18:25][COMM]index:19,lock_pos_report_count:0xFF


2025-07-31 22:50:45:992 ==>> [D][05:18:25][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:25][COMM]index:21,mc_mode:0xFF
[D][05:18:25][COMM]index:22,S_mode:0xFF
[D][05:18:25][COMM]index:23,overweight:0xFF
[D][05:18:25][COMM]index:24,standstill_mode:0xFF
[D][05:18:25][COMM]index:25,night_mode:0xFF
[D][05:18:25][COMM]index:26,experiment1:0xFF
[D][05:18:25][COMM]index:27,experiment2:0xFF
[D][05:18:25][COMM]index:28,experiment3:0xFF
[D][05:18:25][COMM]index:29,experiment4:0xFF
[D][05:18:25][COMM]index:30,night_mode_start:0xFF
[D][05:18:25][COMM]index:31,night_mode_end:0xFF
[D][05:18:25][COMM]index:33,park_report_minutes:0xFF
[D][05:18:25][COMM]index:34,park_report_mode:0xFF
[D][05:18:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:25][COMM]index:38,charge_battery_para: FF
[D][05:18:25][COMM]index:39,multirider_mode:0xFF
[D][05:18:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:25][COMM]index:44,riding_duration_config:0xFF
[D][05:18:25][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:25][COMM]index:46,camera_park_

2025-07-31 22:50:46:096 ==>> type_cfg:0xFF
[D][05:18:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:25][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:25][COMM]index:49,mc_load_startup:0xFF
[D][05:18:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:25][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:25][COMM]index:52,traffic_mode:0xFF
[D][05:18:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:25][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:25][COMM]index:63,experiment5:0xFF
[D][05:18:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:25][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:25][COMM]index:71,camera_park_self

2025-07-31 22:50:46:202 ==>> _check_cfg:0xFF
[D][05:18:25][COMM]index:72,experiment6:0xFF
[D][05:18:25][COMM]index:73,experiment7:0xFF
[D][05:18:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:25][COMM]index:75,zero_value_from_server:-1
[D][05:18:25][COMM]index:76,multirider_threshold:255
[D][05:18:25][COMM]index:77,experiment8:255
[D][05:18:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:25][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:25][COMM]index:83,loc_report_interval:255
[D][05:18:25][COMM]index:84,multirider_threshold_p2:255
[D][05:18:25][COMM]index:85,multirider_strategy:255
[D][05:18:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:25][COMM]index:90,weight_param:0xFF
[D][05:18:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:25][COMM]index:95,current_limit:0xFF
[D][05:18:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:25][COMM]index:100,location_mode:0xFF

[W][05:

2025-07-31 22:50:46:307 ==>> 18:25][PROT]remove success[1629955105],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:25][PROT]add success [1629955105],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:25][COMM]Main Task receive event:131 finished processing
[D][05:18:25][PROT]index:0 1629955105
[D][05:18:25][PROT]is_send:0
[D][05:18:25][PROT]sequence_num:4
[D][05:18:25][PROT]retry_timeout:0
[D][05:18:25][PROT]retry_times:1
[D][05:18:25][PROT]send_path:0x2
[D][05:18:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:25][PROT]===========================================================
[W][05:18:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955105]
[D][05:18:25][PROT]===========================================================
[D][05:18:25][PROT]sending traceid [9999999999900005]
[D][05:18:25][PROT]Send_TO_M2M [1629955105]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:25][SAL ]sock send credit cnt[6]
[D][05:18:25][SAL ]sock send ind credit cnt[6]
[D][05:18:25][M2M ]m2m send data len[294]
[D][05:18:25][SAL ]Cellular task submsg id[10]
[D][05:18:25][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:25][COMM]Main Task receive even

2025-07-31 22:50:46:397 ==>> t:20
[D][05:18:25][GNSS]stop event:1
[D][05:18:25][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:18:25][COMM]----- get Acckey 1 and value:1------------
[D][05:18:25][COMM]----- get Acckey 2 and value:0------------
[D][05:18:25][COMM]------------ready to Power on Acckey 2------------
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:25][CAT1]gsm read msg sub id: 15
[D][05:18:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:25][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B53690DEF64381CE2133AB6E6DAF7712F8EADA463D3DF7A2A80900253ED49C5918D18D8710D24CF76278F2C60458FA20AD5B5FAD040373A93A88064B7A235CE0D9AB26F04315D32D9EBE0781084613DD2E3F67F6A6E47DE6F4B89425A103D4A78AD001CA5972772A912DD5C2B7F416EB051EFFD66359B6766B2AB7EAD984C985420BA0
[

2025-07-31 22:50:46:442 ==>>                                                                                                                                                                           

2025-07-31 22:50:46:808 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[W][05:18:27][GNSS]sing locating running
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[4],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[4],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][CAT1]gsm read msg sub id: 

2025-07-31 22:50:46:912 ==>> 24
[D][05:18:27][PROT]min_index:4, type:0x5D03, priority:3
[D][05:18:27][PROT]index:4
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:8
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900007]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 22:50:47:290 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:50:47:674 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541276
[D][05:18:28][FCTY]HardwareID  = 867222087958981
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = E7B48431010F
[D][05:18:28][FCTY]Bat         = 3824 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 5000 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 105
[D][05:18:28][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:1

2025-07-31 22:50:47:704 ==>> 8:28][FCTY]Bat1         = 3710 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:50:47:794 ==>> [D][05:18:28][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:47:858 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:50:47:868 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:50:47:879 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:50:47:905 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:50:48:005 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 18
[D][05:18:28][COMM]read battery soc:255


2025-07-31 22:50:48:142 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:50:48:147 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:50:48:161 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:50:48:220 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:50:48:433 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:50:48:443 ==>> 检测【打开WIFI(3)】
2025-07-31 22:50:48:468 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:50:48:618 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 22:50:48:712 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:50:48:718 ==>> 检测【扩展芯片hw】
2025-07-31 22:50:48:723 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:50:48:800 ==>> [D][05:18:29][COMM]40485 imu init OK
[D][05:18:29][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:49:745 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:50:50:140 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].p

2025-07-31 22:50:50:245 ==>> lay audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]Main Task rec

2025-07-31 22:50:50:350 ==>> eive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[5],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[5],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[6],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[6],used[1]
[D][05:18:30][COMM]Main Tas

2025-07-31 22:50:50:455 ==>> k receive event:61 finished processing
[D][05:18:30][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][PROT]index:5
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:9
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900008]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]41496 imu init OK
[D][05:18:30][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:30][PROT]CL

2025-07-31 22:50:50:560 ==>> EAN,SEND:0
[D][05:18:30][PROT]index:5 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:9
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:5, type:0x5D03, priority:4
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900008]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][PROT]CLEAN:0
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][COMM]read battery soc:255


2025-07-31 22:50:50:665 ==>>                                                                                                                                                                                                                               

2025-07-31 22:50:50:740 ==>>                                                                                                                                      ][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:50:50:789 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:50:50:795 ==>> 检测【扩展芯片boot】
2025-07-31 22:50:50:818 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:50:50:823 ==>> 检测【扩展芯片sw】
2025-07-31 22:50:50:832 ==>> [D][05:18:31][COMM]42507 imu init OK
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:50:863 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:50:50:868 ==>> 检测【检测音频FLASH】
2025-07-31 22:50:50:873 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:50:51:091 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:50:51:181 ==>> [D][05:18:31][GNSS]recv submsg id[1]
[D][05:18:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:31][GNSS]stop gps fail


2025-07-31 22:50:51:675 ==>> [D][05:18:32][COMM]f:[drv_audio_ack_receive].wait ack timeout!![43325]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 22:50:51:842 ==>> [D][05:18:32][COMM]43519 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:52:037 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 22:50:52:689 ==>> [D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44353]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:50:52:855 ==>> [D][05:18:33][COMM]44531 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:53:663 ==>> [D][05:18:34][CAT1]SEND RAW data timeout
[D][05:18:34][CAT1]exec over: func id: 12, ret: -52
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 22:50:53:858 ==>> [D][05:18:34][COMM]45542 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:53:948 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:50:54:053 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 22:50:54:875 ==>> [D][05:18:35][COMM]46554 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:54:980 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 22:50:55:875 ==>> [D][05:18:36][COMM]47565 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:55:924 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:50:55:931 ==>> 检测【打开喇叭声音】
2025-07-31 22:50:55:936 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:50:56:118 ==>> [D][05:18:36][COMM]read battery soc:255
[W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 22:50:56:196 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:50:56:206 ==>> 检测【打开大灯控制】
2025-07-31 22:50:56:230 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:50:56:394 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:50:56:473 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:50:56:478 ==>> 检测【关闭仪表供电3】
2025-07-31 22:50:56:489 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:50:56:695 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 22:50:56:742 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:50:56:758 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:50:56:766 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:50:56:907 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:18:37][COMM]48577 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:57:020 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:50:57:025 ==>> 检测【读大灯电压】
2025-07-31 22:50:57:044 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:50:57:196 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33178]


2025-07-31 22:50:57:299 ==>> 【读大灯电压】通过,【33178mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:50:57:305 ==>> 检测【关闭大灯控制2】
2025-07-31 22:50:57:331 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:50:57:488 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:50:57:584 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:50:57:590 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:50:57:598 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:50:57:794 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:50:57:858 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:50:57:867 ==>> 检测【打开WIFI(4)】
2025-07-31 22:50:57:873 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:50:57:899 ==>> [D][05:18:38][COMM]49588 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:50:57:989 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:50:58:050 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 22:50:58:188 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:50:58:194 ==>> 检测【EC800M模组版本】
2025-07-31 22:50:58:217 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:50:58:916 ==>> [D][05:18:39][COMM]imu error,enter wait


2025-07-31 22:50:59:225 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:00:070 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][COMM]read battery soc:255


2025-07-31 22:51:00:253 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:01:290 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:01:461 ==>> [D][05:18:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:02:170 ==>> [D][05:18:42][COMM]f:[drv_audio_ack_receive].wait ack timeout!![53609]
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1032].open hexlog save
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:42][COMM]read file, len:15228, num:4
[D][05:18:42][COMM]--->crc16:0x419c
[D][05:18:42][COMM]read file success
[W][05:18:42][COMM][Audio].l:[936].close hexlog save
[D][05:18:42][COMM]accel parse set 1
[D][05:18:42][COMM][Audio]mon:9,05:18:42
[D][05:18:42][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:42][COMM]read battery soc:255
[W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:02:337 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:03:043 ==>> [D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54709]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 22:51:03:383 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:03:824 ==>> [D][05:18:44][CAT1]exec over: func id: 15, ret: -93
[D][05:18:44][CAT1]sub id: 15, ret: -93

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:44][SAL ]socket send fail. id[4]
[D][05:18:44][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:44][M2M ]m2m select fd[4]
[D][05:18:44][M2M ]socket[4] Link is disconnected
[D][05:18:44][M2M ]tcpclient close[4]
[D][05:18:44][SAL ]socket[4] has closed
[D][05:18:44][PROT]protocol read data ok
[E][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:44][PROT]M2M Send Fail [1629955124]
[D][05:18:44][PROT]CLEAN,SEND:5
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 22:51:04:098 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55738]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:44][COMM]read battery soc:255


2025-07-31 22:51:04:128 ==>>                                                                   

2025-07-31 22:51:04:420 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:05:114 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56766]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 22:51:05:466 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:06:086 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 22:51:06:191 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:06:451 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:06:511 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:06:737 ==>> [D][05:18:47][CAT1]SEND RAW data timeout
[D][05:18:47][CAT1]exec over: func id: 12, ret: -52
[W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:47][CAT1]gsm read msg sub id: 12
[D][05:18:47][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:51:07:553 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:08:093 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 22:51:08:602 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:08:756 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:08:951 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:09:635 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:09:758 ==>> [D][05:18:50][CAT1]SEND RAW data timeout
[D][05:18:50][CAT1]exec over: func id: 12, ret: -52
[W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 22:51:10:092 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 22:51:10:673 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:11:460 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:11:705 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:11:751 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:12:115 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 22:51:12:746 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:12:755 ==>> [D][05:18:53][CAT1]SEND RAW data timeout
[D][05:18:53][CAT1]exec over: func id: 12, ret: -52
[W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 10
[D][05:18:53][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:51:13:781 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:13:949 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:14:115 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 22:51:14:768 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:14:813 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:15:850 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:16:115 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 22:51:16:454 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:16:833 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:16:893 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:17:451 ==>> [D][05:18:58][COMM]f:[drv_audio_ack_receive].wait ack timeout!![69109]
[D][05:18:58][COMM]accel parse set 0
[D][05:18:58][COMM][Audio].l:[1032].open hexlog save
[D][05:18:58][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 22:51:17:938 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:18:126 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 22:51:18:877 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:18:952 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:18:982 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:20:024 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:20:133 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 22:51:20:751 ==>> [D][05:19:01][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:51:20:934 ==>> [D][05:19:01][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:21:054 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:21:459 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:22:087 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:22:134 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 22:51:22:976 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:23:127 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:23:958 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:24:155 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 22:51:24:170 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:25:018 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:25:216 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:26:155 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 22:51:26:260 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:26:460 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:27:072 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:27:302 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:28:155 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 22:51:28:337 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:51:28:754 ==>> [D][05:19:09][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:51:28:952 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:29:119 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:29:376 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 22:51:29:383 ==>> #################### 【测试结束】 ####################
2025-07-31 22:51:29:476 ==>> 关闭5V供电
2025-07-31 22:51:29:486 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:51:29:617 ==>> 5A A5 04 5A A5 


2025-07-31 22:51:29:707 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:51:30:157 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 22:51:30:478 ==>> 关闭5V供电成功
2025-07-31 22:51:30:488 ==>> 关闭33V供电
2025-07-31 22:51:30:509 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:51:30:616 ==>> 5A A5 02 5A A5 


2025-07-31 22:51:30:706 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:51:30:901 ==>> [D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 22:51:31:176 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 22:51:31:450 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:51:31:480 ==>> 关闭33V供电成功
2025-07-31 22:51:31:489 ==>> 关闭3.7V供电
2025-07-31 22:51:31:513 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:51:31:616 ==>> 6A A6 02 A6 6A 


2025-07-31 22:51:31:706 ==>> Battery OFF
OVER 150


2025-07-31 22:51:32:461 ==>>  

2025-07-31 22:51:32:491 ==>> 关闭3.7V供电成功
