2025-07-31 20:31:49:822 ==>> MES查站成功:
查站序号:P5100010053132BC验证通过
2025-07-31 20:31:49:839 ==>> 扫码结果:P5100010053132BC
2025-07-31 20:31:49:840 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:31:49:842 ==>> 测试参数版本:2024.10.11
2025-07-31 20:31:49:844 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:31:49:845 ==>> 检测【打开透传】
2025-07-31 20:31:49:848 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:31:49:952 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:31:50:191 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:31:50:197 ==>> 检测【检测接地电压】
2025-07-31 20:31:50:199 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:31:50:257 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:31:50:471 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:31:50:474 ==>> 检测【打开小电池】
2025-07-31 20:31:50:476 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:31:50:542 ==>> 6A A6 01 A6 6A 


2025-07-31 20:31:50:647 ==>> Battery ON
OVER 150


2025-07-31 20:31:50:740 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:31:50:743 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:31:50:745 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:31:50:858 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:31:51:011 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:31:51:013 ==>> 检测【等待设备启动】
2025-07-31 20:31:51:017 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:31:51:390 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:31:51:572 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:31:52:041 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:31:52:281 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:31:52:667 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:31:53:077 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:31:53:152 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:31:53:350 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:31:53:352 ==>> 检测【产品通信】
2025-07-31 20:31:53:355 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:31:53:516 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:31:53:634 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:31:53:637 ==>> 检测【初始化完成检测】
2025-07-31 20:31:53:645 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:31:53:866 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:31:53:924 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:31:53:927 ==>> 检测【关闭大灯控制1】
2025-07-31 20:31:53:929 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:31:54:112 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:31:54:206 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:31:54:209 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:31:54:211 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:31:54:217 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:31:54:322 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task r

2025-07-31 20:31:54:352 ==>> eceive event:121 finished processing


2025-07-31 20:31:54:442 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:31:54:476 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:31:54:478 ==>> 检测【关闭仪表供电】
2025-07-31 20:31:54:480 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:31:54:639 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:31:54:752 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:31:54:754 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:31:54:758 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:31:54:909 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:31:55:037 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:31:55:040 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:31:55:042 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:31:55:242 ==>> [D][05:17:52][COMM]3640 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:31:55:320 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:31:55:326 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:31:55:330 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:31:55:527 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:31:55:590 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:31:55:592 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:31:55:595 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:31:55:632 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:31:55:737 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31


2025-07-31 20:31:55:797 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:31:55:862 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:31:55:865 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:31:55:874 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:31:55:948 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:31:56:134 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:31:56:137 ==>> 该项需要延时执行
2025-07-31 20:31:56:240 ==>> [D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:31:56:756 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5005. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5007. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5007. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5007. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5008. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E-

2025-07-31 20:31:56:785 ==>> >0x01B987FE 5009


2025-07-31 20:31:57:348 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:31:57:820 ==>>                                                                                                                                                                                               D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:17:54][COMM]Main Task receive event:60
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:

2025-07-31 20:31:57:925 ==>> 1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:

2025-07-31 20:31:58:030 ==>> 17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[

2025-07-31 20:31:58:090 ==>> 1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
                                         

2025-07-31 20:31:58:256 ==>> [D][05:17:55][COMM]6674 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:31:58:331 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:31:59:271 ==>> [D][05:17:56][COMM]7685 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:31:59:807 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:32:00:138 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:32:00:142 ==>> 检测【33V输入电压ADC】
2025-07-31 20:32:00:145 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:00:473 ==>> [D][05:17:57][COMM]8697 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:17:57][COMM]adc read out 24v adc:1309  volt:33108 mv
[D][05:17:57][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:10  volt:13 mv
[D][05:17:57][COMM]adc read battery ts volt:11 mv
[D][05:17:57][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:17:57][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:32:00:675 ==>> 【33V输入电压ADC】通过,【33032mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:32:00:677 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:32:00:680 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:32:00:763 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1988mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:32:00:997 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:01:000 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:32:01:039 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:01:042 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:32:01:044 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:32:01:139 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:32:01:142 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:32:01:181 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:32:01:183 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:32:01:228 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:32:01:230 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:32:01:289 ==>> [D][05:17:58][COMM]9708 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:32:01:364 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2766mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:32:01:532 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:01:535 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:32:01:578 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:01:581 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:32:01:584 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:32:01:615 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:32:01:620 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:32:01:639 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018


2025-07-31 20:32:01:661 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:32:01:664 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:32:01:707 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:32:01:710 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:32:01:865 ==>> [D][05:17:59][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1643mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1991mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:32:01:970 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:32:02:013 ==>> 【TP7_VCC3V3(ADV2)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:02:015 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:32:02:057 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:02:060 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:32:02:075 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:32:02:098 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:32:02:100 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:32:02:188 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:32:02:192 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:32:02:231 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:32:02:249 ==>> 检测【打开WIFI(1)】
2025-07-31 20:32:02:251 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:32:02:527 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10719 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret

2025-07-31 20:32:02:572 ==>> [56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:32:02:757 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:32:02:759 ==>> 检测【清空消息队列(1)】
2025-07-31 20:32:02:762 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:32:03:014 ==>>                                                                                                                   sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087736122

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539121

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:32:03:290 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:32:03:306 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:32:03:311 ==>> 检测【打开GPS(1)】
2025-07-31 20:32:03:315 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:32:03:550 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:32:03:584 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:32:03:587 ==>> 检测【打开GSM联网】
2025-07-31 20:32:03:590 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:32:03:655 ==>>                                [D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:32:03:745 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:32:03:835 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:32:03:853 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:32:03:856 ==>> 检测【打开仪表供电1】
2025-07-31 20:32:03:859 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:32:04:048 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:32:04:127 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:32:04:130 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:32:04:133 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:32:04:354 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:32:04:405 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:32:04:409 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:32:04:412 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:32:04:643 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33572]


2025-07-31 20:32:04:676 ==>> 【读取主控ADC采集的仪表电压】通过,【33572mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:32:04:679 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:32:04:680 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:32:04:856 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:32:04:951 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:32:04:954 ==>> 检测【AD_V20电压】
2025-07-31 20:32:04:955 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:32:05:055 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:32:05:147 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:32:05:297 ==>> [D][05:18:02][COMM]13732 imu init OK


2025-07-31 20:32:05:312 ==>> 本次取值间隔时间:256ms
2025-07-31 20:32:05:330 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:32:05:432 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:32:05:462 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:32:05:674 ==>> 本次取值间隔时间:236ms
2025-07-31 20:32:05:679 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:32:05:825 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:32:06:117 ==>> 本次取值间隔时间:428ms
2025-07-31 20:32:06:252 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:32:06:557 ==>> 本次取值间隔时间:432ms
2025-07-31 20:32:06:587 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.144.13.226"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:

2025-07-31 20:32:06:692 ==>> 03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:32:06:937 ==>> 本次取值间隔时间:372ms
2025-07-31 20:32:06:941 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:32:07:046 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:32:07:155 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:32:07:260 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:32:07:441 ==>> 本次取值间隔时间:391ms
2025-07-31 20:32:07:468 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:32:07:582 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:32:07:658 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1635mV
OVER 150


2025-07-31 20:32:07:944 ==>> [D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:32:07:989 ==>> 本次取值间隔时间:399ms
2025-07-31 20:32:08:007 ==>> 【AD_V20电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:08:010 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:32:08:012 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:32:08:049 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA

2025-07-31 20:32:08:154 ==>> ,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,33,,,42,59,,,41,42,,,38,39,,,38,1*7E

$GBGSV,2,2,08,40,,,37,25,,,35,60,,,39,14,,,36,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.116,1596.116,51.031,2097152,2097152,2097152*49

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:32:08:282 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:32:08:285 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:32:08:289 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:32:08:319 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:32:08:469 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:32:09:086 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,42,59,,,40,60,,,40,24,,,40,1*7F

$GBGSV,4,2,13,14,,,39,3,,,39,42,,,38,39,,,38,1*48

$GBGSV,4,3,13,25,,,38,16,,,37,1,,,37,40,,,36,1*48

$GBGSV,4,4,13,41,,,36,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1602.987,1602.987,51.213,2097152,2097152,2097152*4B



2025-07-31 20:32:09:311 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:32:09:551 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:32:09:885 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:32:10:112 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,59,,,40,24,,,40,60,,,39,1*7B

$GBGSV,5,2,18,14,,,39,3,,,39,25,,,39,42,,,38,1*4E

$GBGSV,5,3,18,39,,,38,1,,,37,41,,,37,16,,,36,1*4E

$GBGSV,5,4,18,40,,,36,38,,,35,44,,,34,2,,,34,1*40

$GBGSV,5,5,18,5,,,34,8,,,31,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1538.554,1538.554,49.210,2097152,2097152,2097152*41



2025-07-31 20:32:10:341 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:32:10:567 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:32:10:616 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:32:10:620 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:32:10:623 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:32:10:747 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:32:10:893 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:32:10:896 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:32:10:901 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:32:11:144 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,123214.925,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,59,,,40,24,,,40,60,,,40,1*7E

$GBGSV,5,2,20,25,,,40,14,,,39,3,,,39,39,,,39,1*46

$GBGSV,5,3,20,42,,,38,1,,,37,41,,,37,16,,,36,1*49

$GBGSV,5,4,20,40,,,36,38,,,36,44,,,34,2,,,34,1*48

$GBGSV,5,5,20,5,,,33,8,,,32,34,,,31,9,,,36,1*41

$GBRMC,123214.925,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123214.925,0.000,1533.964,1533.964,49.080,2097152,2097152,2097152*5D



2025-07-31 20:32:11:420 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:32:11:424 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:32:11:428 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:32:11:754 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,123215.525,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,59,,,40,24,,,40,60,,,40,1*7E

$GBGSV,6,2,23,25,,,40,14,,,40,3,,,39,39,,,39,1*48

$GBGSV,6,3,23,42,,,39,1,,,37,41,,,37,16,,,37,1*49

$GBGSV,6,4,23,7,,,37,40,,,36,38,,,36,6,,,36,1*7A

$GBGSV,6,5,23,13,,,35,9,,,34,44,,,34,2,,,34,1*7C

$GBGSV,6,6,23,5,,,33,8,,,32,34,,,31,1*7E

$GBRMC,123215.525,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123215.525,0.000,1528.551,1528.551,48.901,2097152,2097152,2097152*51



2025-07-31 20:32:11:889 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:32:11:953 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:32:11:957 ==>> 检测【AD_V21电压】
2025-07-31 20:32:11:960 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:32:12:055 ==>> 1A A1 20 00 00 
Get AD_V21 1631mV
OVER 150


2025-07-31 20:32:12:286 ==>> 本次取值间隔时间:318ms
2025-07-31 20:32:12:313 ==>> 【AD_V21电压】通过,【1631mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:12:316 ==>> 检测【关闭仪表供电2】
2025-07-31 20:32:12:320 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:32:12:549 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:32:12:596 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:32:12:599 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:32:12:601 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:32:12:790 ==>> $GBGGA,123216.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,60,,,40,25,,,40,1*72

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,39,39,,,39,1*44

$GBGSV,7,3,25,42,,,39,1,,,38,41,,,37,16,,,37,1*41

$GBGSV,7,4,25,7,,,36,40,,,36,38,,,36,13,,,36,1*48

$GBGSV,7,5,25,6,,,35,9,,,34,44,,,34,2,,,34,1*4F

$GBGSV,7,6,25,5,,,33,8,,,33,4,,,32,34,,,31,1*4D

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123216.505,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123216.505,0.000,1507.450,1507.450,48.251,2097152,2097152,2097152*5E

[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:32:12:882 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:32:12:885 ==>> 检测【打开AccKey2供电】
2025-07-31 20:32:12:888 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:32:13:018 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:32:13:179 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:32:13:183 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:32:13:187 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:13:460 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:10][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:10][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:10][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:10][COMM]adc read battery ts volt:13 mv
[D][05:18:10][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:10][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:32:13:717 ==>> $GBGGA,123217.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,41,,,37,16,,,37,1*4F

$GBGSV,7,4,25,40,,,36,38,,,36,13,,,36,7,,,35,1*4B

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,33,5,,,32,4,,,32,34,,,30,1*4D

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123217.505,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123217.505,0.000,1505.798,1505.798,48.204,2097152,2097152,2097152*5F



2025-07-31 20:32:13:809 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33184mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:32:13:814 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:32:13:818 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:32:13:914 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:32:14:020 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:32:14:113 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:32:14:117 ==>> 该项需要延时执行
2025-07-31 20:32:14:716 ==>> $GBGGA,123218.505,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,40,24,,,40,60,,,40,1*72

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,39,1,,,37,41,,,37,16,,,36,1*4F

$GBGSV,7,4,25,40,,,36,38,,,36,13,,,36,7,,,35,1*4B

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,33,5,,,33,4,,,32,34,,,30,1*4C

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123218.505,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123218.505,0.000,1505.795,1505.795,48.201,2097152,2097152,2097152*55



2025-07-31 20:32:15:732 ==>> $GBGGA,123219.505,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,40,24,,,40,60,,,40,1*72

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,41,,,37,16,,,37,1*4F

$GBGSV,7,4,25,40,,,37,38,,,36,13,,,36,7,,,35,1*4A

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,32,4,,,31,34,,,30,1*49

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123219.505,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123219.505,0.000,1505.797,1505.797,48.203,2097152,2097152,2097152*56



2025-07-31 20:32:15:913 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:32:16:717 ==>> $GBGGA,123220.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,41,,,37,16,,,37,1*4F

$GBGSV,7,4,25,40,,,37,38,,,36,13,,,36,7,,,35,1*4A

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,33,4,,,32,34,,,31,1*4A

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123220.505,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123220.505,0.000,1512.424,1512.424,48.408,2097152,2097152,2097152*51



2025-07-31 20:32:17:121 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:32:17:126 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:32:17:139 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:17:460 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:14][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:14][COMM]adc read battery ts volt:9 mv
[D][05:18:14][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:32:17:664 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:32:17:670 ==>> 检测【打开AccKey1供电】
2025-07-31 20:32:17:677 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:32:17:720 ==>> $GBGGA,123221.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,25,,,40,24,,,40,60,,,40,1*71

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,40,,,37,41,,,36,1*4D

$GBGSV,7,4,25,16,,,36,38,,,36,13,,,36,7,,,35,1*48

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,32,4,,,31,34,,,31,1*48

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123221.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123221.505,0.000,1502.474,1502.474,48.090,2097152,2097152,2097152*55



2025-07-31 20:32:17:825 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:32:17:916 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:32:17:935 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:32:17:940 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:32:17:944 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:32:18:050 ==>> 1A A1 00 40 00 
Get AD_V14 2663mV
OVER 150


2025-07-31 20:32:18:187 ==>> 原始值:【2663】, 乘以分压基数【2】还原值:【5326】
2025-07-31 20:32:18:228 ==>> 【读取AccKey1电压(ADV14)前】通过,【5326mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:32:18:231 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:32:18:235 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:18:562 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:15][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:15][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:15][COMM]adc read battery ts volt:14 mv
[D][05:18:15][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:15][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:32:18:667 ==>> $GBGGA,123222.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,39,1,,,37,40,,,37,41,,,37,1*4D

$GBGSV,7,4,2

2025-07-31 20:32:18:712 ==>> 5,16,,,37,13,,,37,38,,,36,7,,,35,1*48

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,33,4,,,31,34,,,31,1*49

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123222.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123222.505,0.000,1514.086,1514.086,48.465,2097152,2097152,2097152*58



2025-07-31 20:32:18:823 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5519mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:32:18:827 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:32:18:831 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:32:19:033 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:32:19:122 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:32:19:126 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:32:19:129 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:32:19:247 ==>> 1A A1 00 40 00 
Get AD_V14 2662mV
OVER 150


2025-07-31 20:32:19:383 ==>> 原始值:【2662】, 乘以分压基数【2】还原值:【5324】
2025-07-31 20:32:19:428 ==>> 【读取AccKey1电压(ADV14)后】通过,【5324mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:32:19:431 ==>> 检测【打开WIFI(2)】
2025-07-31 20:32:19:436 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:32:19:768 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

$GBGGA,123223.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,39,1,,,37,41,,,37,16,,,37,1*4E

$GBGSV,7,4,25,13,,,37,40,,,36,38,,,36,7,,,35,1*4A

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,33,4,,,31,34,,,31,1*49

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123223.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123223.505,0.000,1512.428,1512.428,48.413,2097152,2097152,2097152*58

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:32:19:934 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:32:19:984 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:32:19:987 ==>> 检测【转刹把供电】
2025-07-31 20:32:19:992 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:32:20:115 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:32:20:253 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:32:20:257 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:32:20:262 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:32:20:360 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:32:20:452 ==>> +WIFISCAN:4,0,CC057790A641,-72
+WIFISCAN:4,1,CC057790A640,-72
+WIFISCAN:4,2,CC057790A7C1,-80
+WIFISCAN:4,3,CC057790A7C0,-80

[D][05:18:17][CAT1]wifi scan report total[4]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 20:32:20:512 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 20:32:20:531 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:32:20:535 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:32:20:540 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:32:20:634 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:32:20:770 ==>> $GBGGA,123224.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,25,,,41,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,41,,,37,16,,,36,1*4E

$GBGSV,7,4,25,13,,,36,40,,,36,38,,,36,7,,,35,1*4B

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,33,4,,,32,34,,,31,1*4A

$GBGSV,7,7,25,12,,,28,1*78

$GBRMC,123224.505,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123224.505,0.000,1507.446,1507.446,48.247,2097152,2097152,2097152*58

[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2435mV
OVER 150


2025-07-31 20:32:20:785 ==>> 原始值:【2435】, 乘以分压基数【2】还原值:【4870】
2025-07-31 20:32:20:808 ==>> 【读取AD_V16电压(前)】通过,【4870mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:32:20:812 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:32:20:814 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:21:160 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:18][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:18][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:18][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3086  volt:5424 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2424  volt:3905 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:32:21:346 ==>> 【转刹把供电电压(主控ADC)】通过,【5424mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:32:21:350 ==>> 检测【转刹把供电电压】
2025-07-31 20:32:21:353 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:21:359 ==>> [D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 20:32:21:756 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:18][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:18][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:18][COMM]adc read battery ts volt:7 mv
[D][05:18:18][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,123225.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,60,,,40,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,25,42,,,38,1,,,37,41,,,37,13,,,37,1*4A

$GBGSV,7,4,25,16,,,36,40,,,36,38,,,36,7,,,35,1*4E

$GBGSV,7,5,25,6,,,35,9,,,35,2,,,35,44,,,34,1*4F

$GBGSV,7,6,25,8,,,34,5,,,33,4,,,32,34,,,31,1*4A

$GBGSV,7,7,25,12,,,28,1*78



2025-07-31 20:32:21:786 ==>> 
$GBRMC,123225.505,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123225.505,0.000,1510.765,1510.765,48.355,2097152,2097152,2097152*5B



2025-07-31 20:32:21:881 ==>> 【转刹把供电电压】通过,【5417mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:32:21:886 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:32:21:890 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:32:21:936 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:32:22:041 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:32:22:165 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:32:22:170 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:32:22:173 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:32:22:271 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:32:22:316 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:32:22:346 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:32:22:394 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:32:22:401 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:32:22:423 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:32:22:498 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:32:22:557 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:32:22:621 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:32:22:627 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:32:22:632 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:32:22:665 ==>> $GBGGA,123226.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,24,,,40,60,,,40,1*72

$GBGSV,7,2,26,14,,,40,3,,,40,59,,,40,39,,,39,1*49

$GBGSV,7,3,2

2025-07-31 20:32:22:722 ==>> 6,42,,,38,1,,,38,41,,,37,16,,,37,1*43

$GBGSV,7,4,26,13,,,36,40,,,36,38,,,36,9,,,36,1*45

$GBGSV,7,5,26,7,,,35,6,,,35,2,,,35,44,,,34,1*42

$GBGSV,7,6,26,8,,,34,26,,,34,5,,,32,4,,,32,1*4E

$GBGSV,7,7,26,34,,,31,12,,,28,1*7E

$GBRMC,123226.505,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123226.505,0.000,1505.276,1505.276,48.176,2097152,2097152,2097152*5B



2025-07-31 20:32:22:752 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:32:22:893 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:32:22:897 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:32:22:900 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:32:22:950 ==>> 3A A3 04 01 A3 


2025-07-31 20:32:23:055 ==>> ON_OUT4
OVER 150


2025-07-31 20:32:23:164 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:32:23:167 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:32:23:170 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:32:23:251 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:32:23:435 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:32:23:440 ==>> 检测【左刹电压测试1】
2025-07-31 20:32:23:445 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:23:827 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:1722  volt:2270 mv
[D][05:18:21][COMM]adc read right brake adc:1717  volt:2263 mv
[D][05:18:21][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:21][COMM]adc read battery ts volt:14 mv
[D][05:18:21][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:21][COMM]adc read throttle brake in adc:8  volt:14 mv
$GBGGA,123227.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,24,,,40,60,,,40,1*71

$GBGSV,7,2,27,14,,,40,3,,,40,59,,,40,39,,,39,1*48

$GBGSV,7,3,27,42,,,39,1,,,37,41,,,37,13,,,37,1*49

$GBGSV,7,4,27,16,,,36,40,,,36,38,,,36,9,,,36,1*41

$GBGSV,7,5,27,7,,,36,6,,,35,2,,,35,26,,,35,1*45

$GBGSV,7,6,27,44,,,34,8,,,34,5,,,33,4,,,32,1*4A

$GBGSV,7,7,27,10,,,32,34,,,31,12,,,28,1*7F

$GBRMC,123227.505,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123227.505,0.000,1506.343,1506.343,48.214,2097152,2097152,2097152*5D

[D][05:18:21][COMM]arm_hub adc r

2025-07-31 20:32:23:872 ==>> ead bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2423  volt:3904 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:32:23:947 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:32:23:966 ==>> 【左刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 20:32:23:972 ==>> 检测【右刹电压测试1】
2025-07-31 20:32:23:989 ==>> 【右刹电压测试1】通过,【2263】符合目标值【2250】至【2500】要求!
2025-07-31 20:32:23:992 ==>> 检测【转把电压测试1】
2025-07-31 20:32:24:008 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 20:32:24:011 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:32:24:014 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:32:24:144 ==>> 3A A3 03 00 A3 


2025-07-31 20:32:24:249 ==>> OFF_OUT3
OVER 150


2025-07-31 20:32:24:346 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:32:24:350 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:32:24:355 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:32:24:446 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:32:24:637 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:32:24:646 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:32:24:654 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:32:24:719 ==>> $GBGGA,123228.505,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,25,,,41,24,,,40,60,,,40,1*72

$GBGSV,7,2,27,14,,,40,3,,,40,59,,,39,39,,,39,1*46

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,13,,,37,1*48

$GBGSV,7,4,27,16,,,36,40,,,36,38,,,36,9,,,35,1*42

$GBGSV,7,5,27,7,,,35,6,,,35,26,,,35,2,,,34,1*47

$GBGSV,7,6,27,44,,,34,8,,,34,5,,,32,4,,,32,1*4B

$GBGSV,7,7,27,10,,,32,34,,,31,12,,,28,1*7F

$GBRMC,123228.505,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123228.505,0.000,1495.593,1495.593,47.869,2097152,2097152,2097152*5D



2025-07-31 20:32:24:749 ==>> 3A A3 05 00 A3 


2025-07-31 20:32:24:854 ==>> OFF_OUT5
OVER 150


2025-07-31 20:32:24:938 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:32:24:942 ==>> 检测【左刹电压测试2】
2025-07-31 20:32:24:948 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:32:25:265 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:22][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:22][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:22][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:22][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:22][COMM]adc read battery ts volt:8 mv
[D][05:18:22][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:22][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2422  volt:3902 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:32:25:473 ==>> 【左刹电压测试2】通过,【13】符合目标值【0】至【50】要求!
2025-07-31 20:32:25:477 ==>> 检测【右刹电压测试2】
2025-07-31 20:32:25:503 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:32:25:507 ==>> 检测【转把电压测试2】
2025-07-31 20:32:25:532 ==>> 【转把电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 20:32:25:539 ==>> 检测【晶振检测】
2025-07-31 20:32:25:544 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:32:25:796 ==>> $GBGGA,123229.505,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,40,24,,,40,60,,,40,1*70

$GBGSV,7,2,27,14,,,40,3,,,40,59,,,40,39,,,39,1*48

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,13,,,36,1*49

$GBGSV,7,4,27,16,,,36,40,,,36,38,,,36,9,,,36,1*41

$GBGSV,7,5,27,7,,,35,6,,,35,26,,,35,2,,,34,1*47

$GBGSV,7,6,27,44,,,34,8,,,34,5,,,32,4,,,32,1*4B

$GBGSV,7,7,27,10,,,32,34,,,30,12,,,28,1*7E

$GBRMC,123229.505,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123229.505,0.000,1495.598,1495.598,47.874,2097152,2097152,2097152*50

[W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 20:32:25:947 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:32:26:071 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:32:26:075 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:32:26:078 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:32:26:159 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 1646mV
Get AD_V5 2765mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:32:26:345 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:32:26:349 ==>> 检测【检测BootVer】
2025-07-31 20:32:26:352 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:26:776 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539121
[D][05:18:23][FCTY]HardwareID  = 867222087736122
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:23][FCTY]Bat         = 3924 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 33, adc = 1306
[D][05:18:23][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 0 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1      

2025-07-31 20:32:26:866 ==>>    = 3843 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
$GBGGA,123230.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,24,,,40,60,,,40,1*71

$GBGSV,7,2,27,14,,,40,3,,,40,59,,,40,39,,,39,1*48

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,38,,,37,1*41

$GBGSV,7,4,27,13,,,36,16,,,36,40,,,36,9,,,35,1*4B

$GBGSV,7,5,27,7,,,35,6,,,35,26,,,34,2,,,34,1*46

$GBGSV,7,6,27,44,,,34,8,,,34,10,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,31,12,,,29,1*4B

$GBRMC,123230.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123230.505,0.000,1500.199,1500.199,48.016,2097152,2097152,2097152*5B



2025-07-31 20:32:26:889 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:32:26:893 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:32:26:896 ==>> 检测【检测固件版本】
2025-07-31 20:32:26:908 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:32:26:912 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:32:26:918 ==>> 检测【检测蓝牙版本】
2025-07-31 20:32:26:927 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:32:26:933 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:32:26:957 ==>> 检测【检测MoBikeId】
2025-07-31 20:32:26:960 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:32:26:963 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:32:26:967 ==>> 检测【检测蓝牙地址】
2025-07-31 20:32:26:970 ==>> 取到目标值:DC0CF37C2595
2025-07-31 20:32:26:977 ==>> 【检测蓝牙地址】通过,【DC0CF37C2595】符合目标值【】要求!
2025-07-31 20:32:26:996 ==>> 提取到蓝牙地址:DC0CF37C2595
2025-07-31 20:32:27:000 ==>> 检测【BOARD_ID】
2025-07-31 20:32:27:003 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:32:27:007 ==>> 检测【检测充电电压】
2025-07-31 20:32:27:016 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:32:27:020 ==>> 检测【检测VBUS电压1】
2025-07-31 20:32:27:035 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:32:27:041 ==>> 检测【检测充电电流】
2025-07-31 20:32:27:055 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:32:27:058 ==>> 检测【检测IMEI】
2025-07-31 20:32:27:064 ==>> 取到目标值:867222087736122
2025-07-31 20:32:27:074 ==>> 【检测IMEI】通过,【867222087736122】符合目标值【】要求!
2025-07-31 20:32:27:080 ==>> 提取到IMEI:867222087736122
2025-07-31 20:32:27:099 ==>> 检测【检测IMSI】
2025-07-31 20:32:27:103 ==>> 取到目标值:460130071539121
2025-07-31 20:32:27:106 ==>> 【检测IMSI】通过,【460130071539121】符合目标值【】要求!
2025-07-31 20:32:27:109 ==>> 提取到IMSI:460130071539121
2025-07-31 20:32:27:114 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:32:27:117 ==>> 取到目标值:460130071539121
2025-07-31 20:32:27:144 ==>> 【校验网络运营商(移动)】通过,【460130071539121】符合目标值【】要求!
2025-07-31 20:32:27:148 ==>> 检测【打开CAN通信】
2025-07-31 20:32:27:151 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:32:27:250 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:32:27:431 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:32:27:435 ==>> 检测【检测CAN通信】
2025-07-31 20:32:27:438 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:32:27:566 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:32:27:641 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:32:27:731 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:32:27:737 ==>> 检测【关闭CAN通信】
2025-07-31 20:32:27:742 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:32:27:756 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36047
$GBGGA,123231.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,24,,,40,60,,,40,1*71

$GBGSV,7,2,27,14,,,40,3,,,40,59,,,40,39,,,39,1*48

$GBGSV,7,3,27,42,,,39,1,,,37,41,,,37,38,,,37,1*40

$GBGSV,7,4,27,13,,,37,16,,,37,40,,,36,9,,,36,1*48

$GBGSV,7,5,27,7,,,35,6,,,35,2,,,35,26,,,34,1*47

$GBGSV,7,6,27,44,,,34,8,,,34,10,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,31,12,,,29,1*4B

$GBRMC,123231.505,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123231.505,0.000,752.540,752.540,688.217,2097152,2097152,2097152*63

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:32:27:821 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:32:27:851 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:32:27:956 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:32:28:004 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:32:28:007 ==>> 检测【打印IMU STATE】
2025-07-31 20:32:28:011 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:32:28:246 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:32:28:290 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:32:28:295 ==>> 检测【六轴自检】
2025-07-31 20:32:28:298 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:32:28:442 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:32:28:928 ==>> $GBGGA,123232.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,40,3,,,40,14,,,40,1*44

$GBGSV,7,2,27,59,,,40,24,,,40,25,,,40,39,,,39,1*7F

$GBGSV,7,3,27,42,,,38,13,,,37,38,,,37,1,,,37,1*46

$GBGSV,7,4,27,16,,,37,41,,,37,40,,,36,2,,,35,1*47

$GBGSV,7,5,27,7,,,35,9,,,35,6,,,35,26,,,34,1*4C

$GBGSV,7,6,27,8,,,34,44,,,34,10,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,31,12,,,29,1*4B

$GBRMC,123232.505,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123232.505,0.000,749.469,749.469,685.409,2097152,2097152,2097152*64



2025-07-31 20:32:29:742 ==>> $GBGGA,123233.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,40,3,,,40,14,,,40,1*44

$GBGSV,7,2,27,24,,,40,25,,,40,59,,,39,39,,,39,1*71

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,13,,,36,1*49

$GBGSV,7,4,27,38,,,36,40,,,36,16,,,36,2,,,35,1*49

$GBGSV,7,5,27,7,,,35,9,,,35,6,,,35,26,,,34,1*4C

$GBGSV,7,6,27,44,,,34,10,,,33,8,,,33,5,,,32,1*78

$GBGSV,7,7,27,4,,,32,34,,,30,12,,,29,1*4A

$GBRMC,123233.505,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123233.505,0.000,744.874,744.874,681.207,2097152,2097152,2097152*69



2025-07-31 20:32:29:954 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 20:32:30:152 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:32:30:257 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38701 imu self test OK ######
[D][05:18:27][COMM]i

2025-07-31 20:32:30:287 ==>> mu selftest. GYRO:[0,0,0] ACCEL:[-14,1,4075]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:32:30:428 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:32:30:433 ==>> 检测【打印IMU STATE2】
2025-07-31 20:32:30:439 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:32:30:763 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,123234.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,40,3,,,40,24,,,40,1*47

$GBGSV,7,2,27,25,,,40,14,,,39,59,,,39,39,,,39,1*7C

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,13,,,36,1*49

$GBGSV,7,4,27,38,,,36,40,,,36,16,,,36,7,,,35,1*4C

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,34,1*48

$GBGSV,7,6,27,44,,,34,10,,,33,8,,,33,5,,,32,1*78

$GBGSV,7,7,27,4,,,32,34,,,30,12,,,28,1*4B

$GBRMC,123234.505,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123234.505,0.000,742.578,742.578,679.108,2097152,2097152,2097152*65



2025-07-31 20:32:31:000 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:32:31:005 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:32:31:011 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:32:31:048 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:32:31:153 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:28][FCTY]get_e

2025-07-31 20:32:31:213 ==>> xt_48v_vol retry i = 1,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 14
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 14


2025-07-31 20:32:31:318 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:32:31:327 ==>> 检测【检测VBUS电压2】
2025-07-31 20:32:31:332 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:31:453 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:32:31:771 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539121
[D][05:18:28][FCTY]HardwareID  = 867222087736122
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 7, adc = 284
[D][05:18:28][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 177 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:2

2025-07-31 20:32:31:861 ==>> 8][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3843 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
$GBGGA,123235.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,40,24,,,40,25,,,40,1*73

$GBGSV,7,2,27,3,,,39,14,,,39,59,,,39,39,,,39,1*46

$GBGSV,7,3,27,42,,,38,1,,,37,13,,,36,38,,,36,1*46

$GBGSV,7,4,27,40,,,36,16,,,36,41,,,36,9,,,35,1*4C

$GBGSV,7,5,27,6,,,35,2,,,34,7,,,34,44,,,34,1*43

$GBGSV,7,6,27,26,,,33,10,,,33,8,,,33,5,,,32,1*7B

$GBGSV,7,7,27,4,,,32,34,,,30,12,,,29,1*4A

$GBRMC,123235.505,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123235.505,0.000,740.275,740.275,677.001,2097152,2097152,2097152*62



2025-07-31 20:32:31:904 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:32:306 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539121
[D][05:18:29][FCTY]HardwareID  = 867222087736122
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:29][FCTY]Bat         = 3924 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 167
[D][05:18:29][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:1

2025-07-31 20:32:32:351 ==>> 8:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3843 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:32:32:506 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:32:918 ==>> [D][05:18:30][COMM]msg 0601 loss. last_tick:36046. cur_tick:41065. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41066
$GBGGA,123236.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,14,,,40,1*47

$GBGSV,7,2,27,59,,,40,24,,,40,25,,,40,39,,,39,1*7F

$GBGSV,7,3,27,42,,,38,1,,,37,41,,,37,13,,,36,1*49

$GBGSV,7,4,27,38,,,36,40,,,36,16,,,36,2,,,35,1*49

$GBGSV,7,5,27,7,,,35,9,,,35,6,,,35,26,,,33,1*4B

$GBGSV,7,6,27,10,,,33,8,,,33,44,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,30,12,,,29,1*4A

$GBRMC,123236.505,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123236.505,0.000,744.882,744.882,681.215,2097152,2097152,2097152*6F

[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539121
[D][05:18:30][FCTY]HardwareID  = 867222087736122
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFF

2025-07-31 20:32:33:023 ==>> FFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:30][FCTY]Bat         = 3824 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 9200 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 140
[D][05:18:30][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 101 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3843 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:32:33:310 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:33:508 ==>>                                                                                                                                                                                                                          ep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finish

2025-07-31 20:32:33:612 ==>> ed processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYP

2025-07-31 20:32:33:717 ==>> E:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5269DD57F908F9B4999CFF4DB4E0CCBE6C1E89624447B941CFBC14E8AC8DB21C7BC65875EF7C227718A33E5370D33DAA550E8EA12CAB1AE0C746CAB0C5B2FEA2777

2025-07-31 20:32:33:792 ==>> 83A43EF22DCAC9678026E57D76E9944D0B
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
[D][05:18:30][GNSS]recv submsg id[1]
[D][05:18:30][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:30][GNSS]location stop evt done evt


2025-07-31 20:32:34:079 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539121
[D][05:18:31][FCTY]HardwareID  = 867222087736122
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:31][FCTY]Bat         = 3824 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 112
[D][05:18:31][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 75 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3843 mv
[D][05:18:31][FCTY]=====

2025-07-31 20:32:34:109 ==>> =============== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:32:34:374 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:32:34:718 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539121
[D][05:18:31][FCTY]HardwareID  = 867222087736122
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:18:31][FCTY]Bat         = 3864 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:31][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3843 mv
[D][05:18:3

2025-07-31 20:32:34:748 ==>> 1][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:32:34:925 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:32:34:930 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:32:34:936 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:32:35:055 ==>> 5A A5 01 5A A5 


2025-07-31 20:32:35:145 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:32:35:220 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27


2025-07-31 20:32:35:231 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:32:35:236 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:32:35:240 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:32:35:265 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 20:32:35:355 ==>> 5A A5 04 5A A5 


2025-07-31 20:32:35:445 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:32:35:533 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:32:35:538 ==>> 检测【打开WIFI(3)】
2025-07-31 20:32:35:544 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:32:35:795 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:32:36:102 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:32:36:107 ==>> 检测【扩展芯片hw】
2025-07-31 20:32:36:114 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:32:36:351 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:32:36:402 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:32:36:407 ==>> 检测【扩展芯片boot】
2025-07-31 20:32:36:443 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:32:36:450 ==>> 检测【扩展芯片sw】
2025-07-31 20:32:36:465 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:32:36:469 ==>> 检测【检测音频FLASH】
2025-07-31 20:32:36:474 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:32:36:609 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:32:36:699 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,44A1917CAD80,-70
+WIFISCAN:4,2,CC057790A640,-72
+WIFISCAN:4,3,44A1917CA62F,-74

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 20:32:36:804 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:32:37:527 ==>>                                                                                             Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. succes

2025-07-31 20:32:37:631 ==>> s, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[D][05:18:34][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W]

2025-07-31 20:32:37:736 ==>> [05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:35

2025-07-31 20:32:37:841 ==>> 7] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][

2025-07-31 20:32:37:946 ==>> 05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process]

2025-07-31 20:32:38:036 ==>> .l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255
[D][05:18:34][GNSS]recv submsg id[3]


2025-07-31 20:32:38:694 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3F15A370A29593343AF9214B1042FABAB8144E0D126DE2E

2025-07-31 20:32:38:769 ==>> 26E51B683677B67376F8A9AC43D36ACC93F782F71C62FCB348A94DB76376B73984AF74B598A05FE03A72AF1D842995CC64022364E91D10A709E101
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]


2025-07-31 20:32:39:310 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 20:32:39:721 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:32:39:917 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:32:40:751 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 20:32:41:022 ==>> [D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]49357 imu init OK
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:32:41:314 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 20:32:41:580 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:32:41:584 ==>> 检测【打开喇叭声音】
2025-07-31 20:32:41:591 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:32:42:252 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:39][

2025-07-31 20:32:42:357 ==>> COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]50368 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048

2025-07-31 20:32:42:377 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:32:42:388 ==>> 检测【打开大灯控制】
2025-07-31 20:32:42:398 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:32:42:462 ==>> 
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:32:42:537 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:32:42:661 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:32:42:671 ==>> 检测【关闭仪表供电3】
2025-07-31 20:32:42:682 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:32:42:837 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 20:32:42:931 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:32:42:946 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:32:42:977 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:32:43:137 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:32:43:205 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:32:43:210 ==>> 检测【读大灯电压】
2025-07-31 20:32:43:214 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:32:43:318 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 20:32:43:423 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33456]


2025-07-31 20:32:43:493 ==>> 【读大灯电压】通过,【33456mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:32:43:498 ==>> 检测【关闭大灯控制2】
2025-07-31 20:32:43:505 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:32:43:606 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:32:43:776 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:32:43:781 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:32:43:790 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:32:43:910 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B

2025-07-31 20:32:43:985 ==>> 88B34E86B34325601D9690425BDAC54333BCC74105A748AA7F3D6CF2EDF6877B24D3C3568BE93B6461E2922F1E43BFE556808980866E43EB3E1D5D868E96DFC81B2506556E1B5C804AAAA3D63F9FBE106467FB74
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 20:32:44:075 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[69]


2025-07-31 20:32:44:303 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:32:44:309 ==>> 检测【打开WIFI(4)】
2025-07-31 20:32:44:314 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:32:44:570 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:32:44:634 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:32:44:643 ==>> 检测【EC800M模组版本】
2025-07-31 20:32:44:664 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:32:44:735 ==>> [D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:32:44:840 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:32:45:084 ==>> [D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:32:45:189 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:32:45:197 ==>> 检测【配置蓝牙地址】
2025-07-31 20:32:45:206 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:32:45:329 ==>> [D][05:18:42][COMM]read battery soc:255
[W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:32:45:404 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:DC0CF37C2595>】
2025-07-31 20:32:45:600 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-61
+WIFISCAN:4,2,44A1917CA62F,-75
+WIFISCAN:4,3,CC057790A641,-81

[D][05:18:43][CAT1]wifi scan report total[4]


2025-07-31 20:32:45:660 ==>> recv ble 1
recv ble 2
ble set mac ok :dc,c,f3,7c,25,95
enable filters ret : 0

2025-07-31 20:32:45:735 ==>> [D][05:18:43][COMM]54167 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:32:45:996 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:32:46:007 ==>> 检测【BLETEST】
2025-07-31 20:32:46:029 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:32:46:056 ==>> 4A A4 01 A4 4A 


2025-07-31 20:32:46:161 ==>> recv ble 1
recv ble 2
<BSJ*MAC:DC0CF37C2595*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9DC0CF37C259599999

2025-07-31 20:32:46:251 ==>> OVER 150


2025-07-31 20:32:46:476 ==>> [D][05:18:43][GNSS]recv submsg id[3]


2025-07-31 20:32:46:764 ==>> [D][05:18:44][COMM]55177 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:32:47:024 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:32:47:033 ==>> 该项需要延时执行
2025-07-31 20:32:47:378 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save
[D][05:18:44][COMM]read battery soc:255


2025-07-31 20:32:47:757 ==>> [D][05:18:45][COMM]56189 imu init OK


2025-07-31 20:32:49:147 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B9

2025-07-31 20:32:49:222 ==>> 81113311331133113311331B88B350055751BA9920B99231533A0FB587815B527313CE9E26F6EECE4DAB5A659B0E33710D887817D49CEB0DE2C5414A2FFE258DE17C075B8BA0A446B99C5C5F69A1D41F4A6086429077CA3EDB922CE555D3FFAF
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 20:32:49:327 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 20:32:51:338 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 20:32:53:338 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 20:32:54:397 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5DC462290AF19F8BB44A9670506C1E5F3A00FFE73043DB88223B2

2025-07-31 20:32:54:473 ==>> 9B32E8ADB8EBDBA6048279FED6023A2E7FFF0FA9C2BA7C3DD206FCDFEDC004A2FFFFF251BDB768C382EF04804F307E297D2E6B244939C83A
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]


2025-07-31 20:32:55:353 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 20:32:57:038 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:32:57:043 ==>> 检测【检测WiFi结果】
2025-07-31 20:32:57:051 ==>> WiFi信号:【CC057790A641】,信号值:-72
2025-07-31 20:32:57:059 ==>> WiFi信号:【CC057790A640】,信号值:-72
2025-07-31 20:32:57:067 ==>> WiFi信号:【CC057790A7C1】,信号值:-80
2025-07-31 20:32:57:090 ==>> WiFi信号:【CC057790A7C0】,信号值:-80
2025-07-31 20:32:57:115 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 20:32:57:123 ==>> WiFi信号:【44A1917CAD80】,信号值:-70
2025-07-31 20:32:57:133 ==>> WiFi信号:【44A1917CA62F】,信号值:-74
2025-07-31 20:32:57:161 ==>> WiFi信号:【F42A7D1297A3】,信号值:-61
2025-07-31 20:32:57:170 ==>> WiFi数量【8】, 最大信号值:-34
2025-07-31 20:32:57:192 ==>> 检测【检测GPS结果】
2025-07-31 20:32:57:211 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:32:57:248 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 20:32:57:353 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 20:32:58:051 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:32:58:061 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:32:58:085 ==>> 定位已等待【1】秒.
2025-07-31 20:32:58:460 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]gsm read msg sub id: 23
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:32:59:058 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:32:59:067 ==>> 定位已等待【2】秒.
2025-07-31 20:32:59:163 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:32:59:539 ==>> [D][05:18:56][COMM]read battery soc:255
[D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 20:32:59:841 ==>> [D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:33:00:069 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:00:079 ==>> 定位已等待【3】秒.
2025-07-31 20:33:00:130 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,42,24,,,41,25,,,41,39,,,39,1*73

$GBGSV,3,2,11,42,,,38,41,,,37,40,,,36,59,,,40,1*72

$GBGSV,3,3,11,60,,,40,13,,,36,14,,,36,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1622.753,1622.753,51.868,2097152,2097152,2097152*4D

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:57][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:57][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 23, ret: 6
[D][05:18:57][CAT1]sub id: 23, ret: 6

[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5BEEFEC3D8850D1A06ED424A4DD4F017BA32F74B56B9C4785907645561A673A19E872AD4ECD0AAE1469ABDDD94C5CA06D8E4BBE78CF97F5582369789E894AB16695FFA3C6A02240E5ED7159519D06CE1B5692
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1

2025-07-31 20:33:00:189 ==>> ]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 20:33:00:573 ==>> [D][05:18:57][GNSS]recv submsg id[1]
[D][05:18:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:33:00:955 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,42,24,,,41,25,,,41,14,,,41,1*76

$GBGSV,4,2,13,59,,,40,39,,,39,3,,,39,42,,,38,1*4E

$GBGSV,4,3,13,41,,,37,13,,,36,40,,,36,4,,,34,1*47

$GBGSV,4,4,13,60,,,40,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1603.026,1603.026,51.253,2097152,2097152,2097152*4F



2025-07-31 20:33:01:075 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:01:084 ==>> 定位已等待【4】秒.
2025-07-31 20:33:01:353 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 20:33:01:983 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,24,,,41,25,,,41,14,,,40,1*72

$GBGSV,5,2,17,60,,,40,59,,,39,39,,,39,3,,,39,1*4A

$GBGSV,5,3,17,42,,,38,41,,,37,13,,,36,40,,,36,1*7C

$GBGSV,5,4,17,1,,,36,2,,,33,44,,,33,4,,,32,1*42

$GBGSV,5,5,17,5,,,31,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1543.732,1543.732,49.404,2097152,2097152,2097152*42



2025-07-31 20:33:02:088 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:02:097 ==>> 定位已等待【5】秒.
2025-07-31 20:33:03:011 ==>> $GBGGA,123306.812,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,24,,,41,25,,,41,14,,,40,1*7C

$GBGSV,5,2,19,60,,,40,3,,,40,59,,,39,39,,,39,1*4A

$GBGSV,5,3,19,42,,,38,41,,,37,40,,,37,13,,,36,1*73

$GBGSV,5,4,19,1,,,36,16,,,36,2,,,33,44,,,33,1*7B

$GBGSV,5,5,19,4,,,32,5,,,31,6,,,41,1*4F

$GBRMC,123306.812,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123306.812,0.000,1545.491,1545.491,49.458,2097152,2097152,2097152*5B



2025-07-31 20:33:03:101 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:03:111 ==>> 定位已等待【6】秒.
2025-07-31 20:33:03:377 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 20:33:03:728 ==>> $GBGGA,123307.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,25,,,41,24,,,40,14,,,40,1*76

$GBGSV,6,2,22,60,,,40,3,,,40,59,,,39,39,,,39,1*41

$GBGSV,6,3,22,42,,,38,41,,,37,40,,,37,16,,,37,1*7C

$GBGSV,6,4,22,13,,,36,1,,,36,2,,,34,26,,,34,1*71

$GBGSV,6,5,22,9,,,34,44,,,33,6,,,32,4,,,32,1*49

$GBGSV,6,6,22,8,,,32,5,,,31,1*78

$GBRMC,123307.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123307.512,0.000,1515.135,1515.135,48.492,2097152,2097152,2097152*50



2025-07-31 20:33:04:115 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:04:124 ==>> 定位已等待【7】秒.
2025-07-31 20:33:04:720 ==>> $GBGGA,123308.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,60,,,40,3,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,41,,,37,40,,,37,16,,,36,1*7B

$GBGSV,6,4,24,13,,,36,1,,,36,2,,,34,26,,,34,1*77

$GBGSV,6,5,24,9,,,34,38,,,34,44,,,33,6,,,33,1*77

$GBGSV,6,6,24,8,,,33,4,,,32,5,,,30,10,,,29,1*41

$GBRMC,123308.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123308.512,0.000,1499.444,1499.444,48.004,2097152,2097152,2097152*54



2025-07-31 20:33:05:115 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:05:125 ==>> 定位已等待【8】秒.
2025-07-31 20:33:05:453 ==>> [D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,1

2025-07-31 20:33:05:528 ==>> 34

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BEEE293AF810998FB39880543781F608FA3B5E0D106A94EBF44813D30795DEAE96B277D5D9F3C2D49C8DD1EC2CEB015923FFEB
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]
[D][05:19:02][COMM]read battery soc:255


2025-07-31 20:33:05:723 ==>> $GBGGA,123309.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,60,,,40,3,,,39,59,,,39,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,41,,,37,40,,,36,16,,,36,1*7A

$GBGSV,6,4,24,13,,,36,1,,,36,9,,,35,38,,,35,1*73

$GBGSV,6,5,24,2,,,34,26,,,34,6,,,34,8,,,34,1*4B

$GBGSV,6,6,24,44,,,33,4,,,32,10,,,31,5,,,30,1*70

$GBRMC,123309.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123309.512,0.000,1504.609,1504.609,48.152,2097152,2097152,2097152*57



2025-07-31 20:33:06:124 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:06:133 ==>> 定位已等待【9】秒.
2025-07-31 20:33:06:746 ==>> $GBGGA,123310.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,7,2,25,60,,,40,3,,,40,59,,,39,39,,,39,1*47

$GBGSV,7,3,25,42,,,38,41,,,37,40,,,37,16,,,36,1*7B

$GBGSV,7,4,25,13,,,36,1,,,36,38,,,36,9,,,35,1*70

$GBGSV,7,5,25,26,,,34,6,,,34,8,,,34,2,,,33,1*4C

$GBGSV,7,6,25,44,,,33,4,,,32,10,,,31,5,,,30,1*70

$GBGSV,7,7,25,21,,,40,1*76

$GBRMC,123310.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123310.512,0.000,1508.067,1508.067,48.265,2097152,2097152,2097152*58



2025-07-31 20:33:07:125 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:07:135 ==>> 定位已等待【10】秒.
2025-07-31 20:33:07:368 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 20:33:07:713 ==>> $GBGGA,123311.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,39,59,,,39,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,1,,,37,41,,,36,40,,,36,1*4C

$GBGSV,6,4,24,16,,,36,13,,,36,38,,,36,9,,,35,1*46

$GBGSV,6,5,24,6,,,34,2,,,34,26,,,33,8,,,33,1*4B

$GBGSV,6,6,24,44,,,33,4,,,32,10,,,32,5,,,30,1*73

$GBRMC,123311.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123311.512,0.000,1504.609,1504.609,48.152,2097152,2097152,2097152*5E



2025-07-31 20:33:08:134 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:08:142 ==>> 定位已等待【11】秒.
2025-07-31 20:33:08:717 ==>> $GBGGA,123312.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,39,59,,,39,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,41,,,37,1,,,36,40,,,36,1*4C

$GBGSV,6,4,24,16,,,36,13,,,36,38,,,36,9,,,35,1*46

$GBGSV,6,5,24,6,,,34,2,,,34,26,,,33,8,,,33,1*4B

$GBGSV,6,6,24,44,,,33,4,,,32,10,,,32,5,,,31,1*72

$GBRMC,123312.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123312.512,0.000,1506.332,1506.332,48.203,2097152,2097152,2097152*5A



2025-07-31 20:33:09:136 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:09:145 ==>> 定位已等待【12】秒.
2025-07-31 20:33:09:367 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 20:33:09:718 ==>> $GBGGA,123313.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,41,,,37,40,,,37,1,,,36,1*4D

$GBGSV,6,4,24,16,,,36,13,,,36,38,,,36,9,,,35,1*46

$GBGSV,6,5,24,6,,,35,2,,,34,26,,,34,8,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,4,,,31,5,,,31,1*71

$GBRMC,123313.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123313.512,0.000,1513.241,1513.241,48.423,2097152,2097152,2097152*5F



2025-07-31 20:33:10:139 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:10:149 ==>> 定位已等待【13】秒.
2025-07-31 20:33:10:610 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134


2025-07-31 20:33:10:640 ==>> 

[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 20:33:10:715 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:33:11:148 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:11:158 ==>> 定位已等待【14】秒.
2025-07-31 20:33:11:389 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 20:33:11:718 ==>> $GBGGA,123315.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,40,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,40,,,37,16,,,37,41,,,37,1*7A

$GBGSV,6,4,24,1,,,37,13,,,36,38,,,36,9,,,35,1*71

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,4,,,32,5,,,32,1*70

$GBRMC,123315.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123315.512,0.000,1523.598,1523.598,48.747,2097152,2097152,2097152*58



2025-07-31 20:33:12:149 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:12:159 ==>> 定位已等待【15】秒.
2025-07-31 20:33:12:709 ==>> $GBGGA,123316.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,40,,,37,16,,,37,41,,,37,1*7A

$GBGSV,6,4,24,1,,,37,13,,,36,38,,,36,9,,,35,1*71

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,31,1*73

$GBRMC,123316.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123316.512,0.000,1520.145,1520.145,48.637,2097152,2097152,2097152*5D



2025-07-31 20:33:13:159 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:13:169 ==>> 定位已等待【16】秒.
2025-07-31 20:33:13:388 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 20:33:13:720 ==>> $GBGGA,123317.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,16,,,37,1,,,37,40,,,36,1*4F

$GBGSV,6,4,24,41,,,36,13,,,36,38,,,36,9,,,35,1*44

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123317.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123317.512,0.000,1518.414,1518.414,48.579,2097152,2097152,2097152*55



2025-07-31 20:33:14:171 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:14:180 ==>> 定位已等待【17】秒.
2025-07-31 20:33:14:713 ==>> $GBGGA,123314.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,40,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,39,16,,,37,1,,,37,40,,,37,1*4F

$GBGSV,6,4,24,41,,,37,38,,,37,13,,,36,9,,,35,1*44

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123314.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123314.512,0.000,1527.054,1527.054,48.858,2097152,2097152,2097152*58



2025-07-31 20:33:15:174 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:15:182 ==>> 定位已等待【18】秒.
2025-07-31 20:33:15:386 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 20:33:15:721 ==>> $GBGGA,123315.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,40,,,37,16,,,36,1,,,36,1*4E

$GBGSV,6,4,24,41,,,36,38,,,36,13,,,36,9,,,35,1*44

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123315.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123315.512,0.000,1516.686,1516.686,48.524,2097152,2097152,2097152*5F



2025-07-31 20:33:16:180 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:16:189 ==>> 定位已等待【19】秒.
2025-07-31 20:33:16:721 ==>> $GBGGA,123316.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,40,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,16,,,37,41,,,37,40,,,36,1*7B

$GBGSV,6,4,24,1,,,36,38,,,36,13,,,36,9,,,35,1*70

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123316.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123316.512,0.000,1521.873,1521.873,48.694,2097152,2097152,2097152*54



2025-07-31 20:33:17:194 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:17:204 ==>> 定位已等待【20】秒.
2025-07-31 20:33:17:391 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 20:33:17:711 ==>> $GBGGA,123317.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,16,,,37,41,,,36,40,,,36,1*7A

$GBGSV,6,4,24,1,,,36,38,,,36,13,,,36,9,,,35,1*70

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,31,1*73

$GBRMC,123317.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123317.512,0.000,1514.963,1514.963,48.472,2097152,2097152,2097152*5F



2025-07-31 20:33:18:203 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:18:214 ==>> 定位已等待【21】秒.
2025-07-31 20:33:18:715 ==>> $GBGGA,123318.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,25,,,41,24,,,40,14,,,40,1*73

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,38,16,,,37,1,,,37,41,,,36,1*4E

$GBGSV,6,4,24,40,,,36,38,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,31,1*73

$GBRMC,123318.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123318.512,0.000,1514.959,1514.959,48.469,2097152,2097152,2097152*5A



2025-07-31 20:33:19:211 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:19:221 ==>> 定位已等待【22】秒.
2025-07-31 20:33:19:413 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 20:33:19:718 ==>> $GBGGA,123319.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,40,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,16,,,37,41,,,37,1,,,36,1*4E

$GBGSV,6,4,24,40,,,36,38,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123319.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123319.512,0.000,1521.873,1521.873,48.694,2097152,2097152,2097152*5B



2025-07-31 20:33:20:215 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:20:224 ==>> 定位已等待【23】秒.
2025-07-31 20:33:20:634 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:17][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:33:20:709 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:33:20:754 ==>>                                      

2025-07-31 20:33:21:028 ==>> [D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 10, ret: 6
[D][05:19:18][CAT1]sub id: 10, ret: 6

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:18][M2M ]m2m gsm shut done, ret[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][CAT1]gsm read msg sub id: 8
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:33:21:210 ==>> [D][05:19:18][CAT1]pdpdeact urc len[22]


2025-07-31 20:33:21:225 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:21:237 ==>> 定位已等待【24】秒.
2025-07-31 20:33:21:407 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 20:33:21:712 ==>> $GBGGA,123321.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,16,,,37,1,,,37,40,,,37,1*4E

$GBGSV,6,4,24,41,,,36,38,,,36,13,,,36,9,,,35,1*44

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,34,10,,,33,5,,,32,4,,,32,1*77

$GBRMC,123321.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123321.512,0.000,1523.595,1523.595,48.744,2097152,2097152,2097152*5C



2025-07-31 20:33:21:787 ==>> [D][05:19:19][COMM]M->S yaw:INVALID


2025-07-31 20:33:22:236 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:22:246 ==>> 定位已等待【25】秒.
2025-07-31 20:33:22:530 ==>> [D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:33:22:958 ==>> $GBGGA,123322.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,41,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,38,16,,,37,1,,,37,40,,,37,1*4E

$GBGSV,6,4,24,41,,,37,38,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,26,,,34,8,,,34,2,,,34,1*4A

$GBGSV,6,6,24,44,,,34,10,,,33,5,,,32,4,,,32,1*77

$GBRMC,123322.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123322.512,0.000,1528.782,1528.782,48.914,2097152,2097152,2097152*54

[D][05:19:20][CAT1]opened : 0, 0
[D][05:19:20][SAL ]Cellular task submsg id[68]
[D][05:19:20][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:20][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:20][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:20][M2M ]g_m2m_is_idle become true
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:20][PROT]index:2 1629955160
[D][05:19:20][PROT]is_send:0
[D][05:19:20][PROT]sequence_num:6
[D][05:19:20][PROT]retry_timeout:0
[D][05:19:20][PROT]retry_times:1
[D][05:19:20][PROT]send_path:0x2
[D][05:19:20][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:20][PROT]===========================================================
[W][05:19:20][PROT]SEND

2025-07-31 20:33:23:063 ==>>  DATA TYPE:D302, SENDPATH:0x2 [1629955160]
[D][05:19:20][PROT]===========================================================
[D][05:19:20][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:20][PROT]sending traceid [9999999999900007]
[D][05:19:20][PROT]Send_TO_M2M [1629955160]
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:20][SAL ]sock send credit cnt[6]
[D][05:19:20][SAL ]sock send ind credit cnt[6]
[D][05:19:20][M2M ]m2m send data len[134]
[D][05:19:20][SAL ]Cellular task submsg id[10]
[D][05:19:20][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:20][CAT1]gsm read msg sub id: 15
[D][05:19:20][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:20][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BE1903F0F74EA2B641006679347E6903238EA96CAF8AD2045A6A81035D4C260F6B81896F292F17E2B5BCF807A5C4654FEB3099
[D][05:19:20][CAT1]<<< 
SEND OK

[D][05:19:20][CAT1]exec over: func id: 15, ret: 11
[D][05:19:20][CAT1]sub id: 15, ret: 11

[D][05:19:20][SAL ]Cellular task submsg id[68]
[D][05:19:20][SAL ]handle 

2025-07-31 20:33:23:108 ==>> subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:20][M2M ]g_m2m_is_idle become true
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:20][PROT]M2M Send ok [1629955160]


2025-07-31 20:33:23:244 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:23:255 ==>> 定位已等待【26】秒.
2025-07-31 20:33:23:414 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 20:33:23:718 ==>> $GBGGA,123323.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,41,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,39,16,,,37,40,,,37,1,,,36,1*4E

$GBGSV,6,4,24,41,,,36,38,,,36,13,,,36,9,,,35,1*44

$GBGSV,6,5,24,6,,,35,2,,,35,26,,,34,8,,,34,1*4B

$GBGSV,6,6,24,44,,,34,10,,,33,5,,,32,4,,,32,1*77

$GBRMC,123323.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123323.512,0.000,1528.782,1528.782,48.914,2097152,2097152,2097152*55



2025-07-31 20:33:24:259 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:24:270 ==>> 定位已等待【27】秒.
2025-07-31 20:33:24:712 ==>> $GBGGA,123324.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,40,14,,,40,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,39,39,,,39,1*47

$GBGSV,6,3,24,42,,,39,16,,,37,40,,,37,1,,,37,1*4F

$GBGSV,6,4,24,38,,,37,41,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,2,,,34,26,,,34,8,,,34,1*4A

$GBGSV,6,6,24,44,,,33,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,123324.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123324.512,0.000,1523.597,1523.597,48.745,2097152,2097152,2097152*58



2025-07-31 20:33:25:269 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:25:283 ==>> 定位已等待【28】秒.
2025-07-31 20:33:25:428 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 20:33:25:717 ==>> $GBGGA,123325.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,40,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,39,16,,,37,40,,,37,1,,,37,1*4F

$GBGSV,6,4,24,38,,,37,41,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,2,,,35,26,,,34,8,,,34,1*4B

$GBGSV,6,6,24,44,,,34,10,,,33,5,,,32,4,,,32,1*77

$GBRMC,123325.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123325.512,0.000,1530.506,1530.506,48.966,2097152,2097152,2097152*56



2025-07-31 20:33:26:270 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:26:283 ==>> 定位已等待【29】秒.
2025-07-31 20:33:26:720 ==>> $GBGGA,123326.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,25,,,41,24,,,41,14,,,41,1*70

$GBGSV,6,2,24,3,,,40,60,,,40,59,,,40,39,,,39,1*49

$GBGSV,6,3,24,42,,,39,16,,,37,40,,,37,1,,,37,1*4F

$GBGSV,6,4,24,38,,,37,41,,,36,13,,,36,9,,,35,1*45

$GBGSV,6,5,24,6,,,35,2,,,35,26,,,34,8,,,34,1*4B

$GBGSV,6,6,24,44,,,34,10,,,33,5,,,32,4,,,32,1*77

$GBRMC,123326.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123326.512,0.000,1532.236,1532.236,49.024,2097152,2097152,2097152*5B



2025-07-31 20:33:27:277 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:27:288 ==>> 定位已等待【30】秒.
2025-07-31 20:33:27:433 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 20:33:28:289 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:33:28:300 ==>> 定位已等待【31】秒.
2025-07-31 20:33:28:473 ==>> $GBGGA,123327.517,2301.2575520,N,11421.9406943,E,1,11,0.94,75.268,M,-1.770,M,,*54

$GBGSA,A,3,14,33,39,16,24,42,13,25,40,41,44,,1.86,0.94,1.60,4*01

$GBGSV,6,1,24,14,69,194,41,33,68,285,42,3,61,191,41,6,53,35,35,1*4B

$GBGSV,6,2,24,59,52,129,40,39,52,7,39,16,52,347,37,24,51,12,41,1*41

$GBGSV,6,3,24,1,48,126,37,2,46,238,35,42,44,164,39,13,42,219,36,1*72

$GBGSV,6,4,24,25,42,280,41,60,41,238,40,9,38,317,36,40,34,160,37,1*40

$GBGSV,6,5,24,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,37,1*47

$GBGSV,6,6,24,8,23,204,34,5,22,257,32,41,22,321,37,44,15,102,34,1*7D

$GBRMC,123327.517,A,2301.2575520,N,11421.9406943,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[D][05:19:25][GNSS]HD8040 GPS
[D][05:19:25][GNSS]GPS diff_sec 124010042, report 0x42 frame
$GBGST,123327.517,1.010,0.236,0.221,0.333,2.204,3.349,8.641*77

[D][05:19:25][COMM]Main Task receive event:131
[D][05:19:25][COMM]index:0,power_mode:0xFF
[D][05:19:25][COMM]index:1,sound_mode:0xFF
[D][05:19:25][COMM]index:2,gsensor_mode:0xFF
[D][05:19:25][COMM]index:3,report_freq_mode:0xFF
[D][05:19:25][COMM]index:4,report_period:0xFF
[D][05:19:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:25][COMM]index:6,

2025-07-31 20:33:28:578 ==>> normal_reset_period:0xFF
[D][05:19:25][COMM]index:7,spock_over_speed:0xFF
[D][05:19:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:25][COMM]index:16,imu_config_params:0xFF
[D][05:19:25][COMM]index:17,long_connect_params:0xFF
[D][05:19:25][COMM]index:18,detain_mark:0xFF
[D][05:19:25][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:25][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:25][COMM]index:21,mc_mode:0xFF
[D][05:19:25][COMM]index:22,S_mode:0xFF
[D][05:19:25][COMM]index:23,overweight:0xFF
[D][05:19:25][COMM]index:24,standstill_mode:0xFF
[D][05:19:25][COMM]index:25,night_mode:0xFF
[D][05:19:25][COMM]index:26,experiment1:0xFF
[D][05:19:25][COMM]index:27,experiment2:0xFF
[D][05:19:25][COMM]index:28,experiment3:0xFF
[D][05:19:25][COMM]index:29,experiment4:0xFF
[D][05:19:25][COMM]index:30,nigh

2025-07-31 20:33:28:683 ==>> t_mode_start:0xFF
[D][05:19:25][COMM]index:31,night_mode_end:0xFF
[D][05:19:25][COMM]index:33,park_report_minutes:0xFF
[D][05:19:25][COMM]index:34,park_report_mode:0xFF
[D][05:19:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:25][COMM]index:38,charge_battery_para: FF
[D][05:19:25][COMM]index:39,multirider_mode:0xFF
[D][05:19:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:25][COMM]index:44,riding_duration_config:0xFF
[D][05:19:25][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:25][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:25][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:25][COMM]index:49,mc_load_startup:0xFF
[D][05:19:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:25][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:25][COMM]index:52,traffic_mode:0xFF
[D][05:19:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:25][COMM]index:55,wheel_alarm_play_switch:255


2025-07-31 20:33:28:788 ==>> 
[D][05:19:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:25][COMM]index:63,experiment5:0xFF
[D][05:19:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:25][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:25][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:25][COMM]index:72,experiment6:0xFF
[D][05:19:25][COMM]index:73,experiment7:0xFF
[D][05:19:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:25][COMM]index:75,zero_value_from_server:-1
[D][05:19:25][COMM]index:76,multirider_threshold:255
[D][05:19:25][COMM]index:77,experiment8:255
[D][05:19:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:25][COMM]index:80,

2025-07-31 20:33:28:893 ==>> temp_park_reminder_timeout_duration:255
[D][05:19:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:25][COMM]index:83,loc_report_interval:255
[D][05:19:25][COMM]index:84,multirider_threshold_p2:255
[D][05:19:25][COMM]index:85,multirider_strategy:255
[D][05:19:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:25][COMM]index:90,weight_param:0xFF
[D][05:19:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:25][COMM]index:95,current_limit:0xFF
[D][05:19:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:25][COMM]index:100,location_mode:0xFF

[W][05:19:25][PROT]remove success[1629955165],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:25][PROT]add success [1629955165],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:25][COMM]Main Task receive event:131 finished processing
[D][05:19:25][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:25][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:25][PROT]CLEAN,SEND:2
[D][05:19:25][PROT]CLEAN:2
[D][05:19:25][PROT]index:0 1629955165


2025-07-31 20:33:28:998 ==>> [D][05:19:25][PROT]is_send:0
[D][05:19:25][PROT]sequence_num:7
[D][05:19:25][PROT]retry_timeout:0
[D][05:19:25][PROT]retry_times:1
[D][05:19:25][PROT]send_path:0x2
[D][05:19:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:25][PROT]===========================================================
[W][05:19:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955165]
[D][05:19:25][PROT]===========================================================
[D][05:19:25][PROT]sending traceid [9999999999900008]
[D][05:19:25][PROT]Send_TO_M2M [1629955165]
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:25][SAL ]sock send credit cnt[6]
[D][05:19:25][SAL ]sock send ind credit cnt[6]
[D][05:19:25][M2M ]m2m send data len[294]
[D][05:19:25][SAL ]Cellular task submsg id[10]
[D][05:19:25][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:25][CAT1]gsm read msg sub id: 15
[D][05:19:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:25][CAT1]<<< 
ERROR

$GBGGA,123328.017,2301.2580664,N,11421.9409133,E,1,11,0.94,75.689,M,-1.770,M,,*5C

$GBGSA,A,3,14,33,39,16,24,42,13,25,40,41,44,,1.86

2025-07-31 20:33:29:088 ==>> ,0.94,1.60,4*01

$GBGSV,6,1,24,14,69,194,41,33,68,285,42,3,61,191,40,6,53,35,35,1*4A

$GBGSV,6,2,24,59,52,129,40,39,52,7,39,16,52,347,37,24,51,12,41,1*41

$GBGSV,6,3,24,1,48,126,37,2,46,238,35,42,44,164,39,13,42,219,36,1*72

$GBGSV,6,4,24,25,42,280,41,60,41,238,40,9,38,317,36,40,34,160,37,1*40

$GBGSV,6,5,24,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,37,1*47

$GBGSV,6,6,24,8,23,204,34,5,22,257,32,41,22,321,37,44,15,102,34,1*7D

$GBGSV,2,1,06,33,68,285,41,39,52,7,40,24,51,12,41,42,44,164,39,5*46

$GBGSV,2,2,06,25,42,280,40,40,34,160,35,5*79

$GBRMC,123328.017,A,2301.2580664,N,11421.9409133,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123328.017,1.041,0.811,0.733,1.107,1.361,1.909,5.686*7F



2025-07-31 20:33:29:299 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 20:33:29:308 ==>> 
北斗星号:【14】,信号值:【41】
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【41】
北斗星号:【6】,信号值:【35】
北斗星号:【59】,信号值:【40】
北斗星号:【39】,信号值:【39】
北斗星号:【16】,信号值:【37】
北斗星号:【24】,信号值:【41】
北斗星号:【1】,信号值:【37】
北斗星号:【2】,信号值:【35】
北斗星号:【42】,信号值:【39】
北斗星号:【13】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【60】,信号值:【40】
北斗星号:【9】,信号值:【36】
北斗星号:【40】,信号值:【37】
北斗星号:【38】,信号值:【37】
北斗星号:【41】,信号值:【37】

2025-07-31 20:33:29:317 ==>> 检测【CSQ强度】
2025-07-31 20:33:29:345 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:33:29:358 ==>> $GBGGA,123329.000,2301.2581993,N,11421.9409243,E,1,11,0.94,75.810,M,-1.770,M,,*57

$GBGSA,A,3,14,33,39,16,24,42,13,25,40,41,44,,1.86,0.94,1.60,4*01

$GBGSV,6,1,24,14,69,194,41,33,68,285,42,3,61,191,41,6,53,35,35,1*4B

$GBGSV,6,2,24,59,52,129,40,39,52,7,39,16,52,347,37,24,51,12,41,1*41

$GBGSV,6,3,24,1,48,126,37,2,46,238,34,42,44,164,39,13,42,219,36,1*73

$GBGSV,6,4,24,25,42,280,41,60,41,238,40,9,38,317,35,40,34,160,37,1*43

$GBGSV,6,5,24,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,37,1*47

$GBGSV,6,6,24,8,23,204,34,5,22,257,32,41,22,321,37,44,15,102,34,1*7D

$GBGSV,2,1,08,33,68,285,42,39,52,7,40,24,51,12,42,42,44,164,40,5*46

$GBGSV,2,2,08,25,42,280,41,40,34,160,34,41,22,321,33,44,15,102,33,5*75

$GBRMC,123329.000,A,2301.2581993,N,11421.9409243,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,123329.000,1.138,0.240,0.226,0.332,1.182,1.557,4.593*73



2025-07-31 20:33:29:434 ==>> [D][05:19:26][COMM]read battery soc:255


2025-07-31 20:33:29:539 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:33:30:327 ==>> $GBGGA,123330.000,2301.2582668,N,11421.9409407,E,1,11,0.94,75.605,M,-1.770,M,,*5B

$GBGSA,A,3,14,33,39,16,24,42,13,25,40,41,44,,1.86,0.94,1.60,4*01

$GBGSV,6,1,24,14,69,194,41,33,68,285,42,3,61,191,41,6,53,35,35,1*4B

$GBGSV,6,2,24,59,52,129,40,39,52,7,39,16,52,347,37,24,51,12,41,1*41

$GBGSV,6,3,24,1,48,126,37,2,46,238,34,42,44,164,39,13,42,219,36,1*73

$GBGSV,6,4,24,25,42,280,41,60,41,238,40,9,38,317,35,40,34,160,37,1*43

$GBGSV,6,5,24,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,36,1*46

$GBGSV,6,6,24,8,23,204,34,5,22,257,32,41,22,321,37,44,15,102,34,1*7D

$GBGSV,2,1,08,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,2,2,08,25,42,280,41,40,34,160,35,41,22,321,33,44,15,102,33,5*74

$GBRMC,123330.000,A,2301.2582668,N,11421.9409407,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,123330.000,1.366,0.179,0.172,0.250,1.246,1.517,4.053*7A



2025-07-31 20:33:31:348 ==>> $GBGGA,123331.000,2301.2583224,N,11421.9409752,E,1,11,0.94,75.634,M,-1.770,M,,*56

$GBGSA,A,3,14,33,39,16,24,42,13,25,40,41,44,,1.86,0.94,1.60,4*01

$GBGSV,7,1,25,14,69,194,41,33,68,285,42,3,61,191,40,6,53,35,35,1*4A

$GBGSV,7,2,25,59,52,129,40,39,52,7,39,16,52,347,37,24,51,12,41,1*41

$GBGSV,7,3,25,1,48,126,37,2,46,238,35,42,44,164,39,13,42,219,36,1*72

$GBGSV,7,4,25,25,42,280,41,60,41,238,40,9,38,317,35,40,34,160,37,1*43

$GBGSV,7,5,25,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,36,1*46

$GBGSV,7,6,25,8,23,204,34,5,22,257,32,41,22,320,37,44,15,102,34,1*7C

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,2,1,08,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,2,2,08,25,42,280,41,40,34,160,36,41,22,320,33,44,15,102,33,5*76

$GBRMC,123331.000,A,2301.2583224,N,11421.9409752,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123331.000,1.369,0.257,0.241,0.354,1.182,1.403,3.623*74



2025-07-31 20:33:31:378 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:33:31:453 ==>> [D][05:19:28][COMM]read battery soc:255


2025-07-31 20:33:31:928 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A3 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100012. period:10000
[D][05

2025-07-31 20:33:32:033 ==>> :19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_ti

2025-07-31 20:33:32:138 ==>> ck:0. cur_tick:100018. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100018. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100019. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100019. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100019. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100020. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100020. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100020. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100021
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100021
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100022
[W][05:19:29][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:33:32:348 ==>>                                                                                                                                                                                                                53,35,35,1*4A

$GBGSV,7,2,25,59,52,129,39,39,52,7,39,16,52,347,37,24,51,12,41,1*4F

$GBGSV,7,3,25,1,48,126,37,2,46,238,34,42,44,164,38,13,42,219,36,1*72

$GBGSV,7,4,25,25,42,280,41,60,41,238,40,9,38,317,35,40,34,160,37,1*43

$GBGSV,7,5,25,4,32,112,32,10,30,196,33,26,26,234,34,38,23,200,36,1*46

$GBGSV,7,6,25,8,23,204,34,5,22,257,32,41,22,320,36,44,15,102,34,1*7D

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,2,1,08,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,2,2,08,25,42,280,41,40,34,160,36,41,22,320,33,44,15,102,33,5*76

$GBRMC,123332.000,A,2301.2583484,N,11421.9409971,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123332.000,1.363,0.202,0.192,0.280,1.136,1.323,3.361*71

[D][05:19:29][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:33:33:369 ==>> $GBGGA,123333.000,2301.2583419,N,11421.9409892,E,1,15,0.81,75.952,M,-1.770,M,,*50

$GBGSA,A,3,14,33,03,39,16,24,59,01,42,60,13,25,1.68,0.81,1.48,4*06

$GBGSA,A,3,40,41,44,,,,,,,,,,1.68,0.81,1.48,4*02

$GBGSV,7,1,25,14,69,194,40,33,68,285,42,3,62,190,40,6,53,35,35,1*49

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,1,46,125,37,2,46,238,34,42,44,164,38,60,43,241,40,1*76

$GBGSV,7,4,25,13,42,219,36,25,42,280,41,9,38,317,35,40,34,160,37,1*46

$GBGSV,7,5,25,4,32,112,32,10,30,196,33,26,26,235,34,38,23,200,36,1*47

$GBGSV,7,6,25,8,23,204,34,5,22,257,32,41,22,320,36,44,15,102,34,1*7D

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,2,1,08,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,2,2,08,25,42,280,41,40,34,160,36,41,22,320,33,44,15,102,33,5*76

$GBRMC,123333.000,A,2301.2583419,N,11421.9409892,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123333.000,1.358,0.215,0.211,0.307,1.102,1.263,3.145*7E



2025-07-31 20:33:33:444 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:33:33:474 ==>> [D][05:19:30][COMM]read battery soc:255


2025-07-31 20:33:33:640 ==>> [W][05:19:31][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:33:34:376 ==>> $GBGGA,123334.000,2301.2583481,N,11421.9410178,E,1,17,0.75,76.046,M,-1.770,M,,*55

$GBGSA,A,3,14,33,03,39,16,24,59,09,01,42,60,13,1.54,0.75,1.35,4*06

$GBGSA,A,3,25,40,41,44,26,,,,,,,,1.54,0.75,1.35,4*0F

$GBGSV,7,1,25,14,69,194,40,33,68,285,42,3,62,190,40,6,53,35,35,1*49

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,9,48,321,35,1,46,125,37,2,46,238,34,42,44,164,38,1*47

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,40,34,160,37,1*75

$GBGSV,7,5,25,4,32,112,32,10,30,196,33,38,23,200,36,8,23,204,34,1*7C

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,14,50,34,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,2,1,08,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,2,2,08,25,42,280,41,40,34,160,37,41,22,320,33,44,15,102,33,5*77

$GBRMC,123334.000,A,2301.2583481,N,11421.9410178,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123334.000,1.264,0.220,0.215,0.313,1.001,1.146,2.907*79



2025-07-31 20:33:34:714 ==>> [D][05:19:32][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:33:35:367 ==>> $GBGGA,123335.000,2301.2583680,N,11421.9410084,E,1,20,0.67,76.035,M,-1.770,M,,*56

$GBGSA,A,3,14,33,03,06,39,16,24,59,09,01,42,60,1.40,0.67,1.23,4*03

$GBGSA,A,3,13,25,08,40,38,41,44,26,,,,,1.40,0.67,1.23,4*0F

$GBGSV,7,1,25,14,69,194,40,33,68,285,42,3,62,190,40,6,52,343,35,1*7A

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,9,48,321,35,1,46,125,36,2,46,238,34,42,44,164,38,1*46

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,8,38,207,34,1*44

$GBGSV,7,5,25,40,34,160,37,4,32,112,32,10,30,196,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,14,50,34,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,09,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*47

$GBGSV,3,2,09,25,42,280,41,40,34,160,37,41,22,320,33,44,15,102,33,5*77

$GBGSV,3,3,09,26,14,50,33,5*7F

$GBRMC,123335.000,A,2301.2583680,N,11421.9410084,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,123335.000,1.587,0.194,0.186,0.274,1.239,1.356,2.916*7D



2025-07-31 20:33:35:472 ==>> [D][05:19:32][COMM]read battery soc:255


2025-07-31 20:33:35:517 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:33:35:733 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:33:36:396 ==>> $GBGGA,123336.000,2301.2583729,N,11421.9409959,E,1,22,0.64,76.082,M,-1.770,M,,*5B

$GBGSA,A,3,14,33,03,06,39,16,24,59,02,09,01,42,1.36,0.64,1.21,4*07

$GBGSA,A,3,60,13,25,08,40,10,38,41,44,26,,,1.36,0.64,1.21,4*08

$GBGSV,7,1,25,14,69,194,40,33,68,285,42,3,62,190,40,6,52,343,35,1*7A

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,2,48,239,34,9,48,321,35,1,46,125,37,42,44,164,38,1*48

$GBGSV,7,4,25,60,43,241,39,13,42,219,36,25,42,280,41,8,38,207,34,1*4A

$GBGSV,7,5,25,40,34,160,37,4,32,112,32,10,30,187,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,14,50,34,1*76

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,68,285,43,39,52,7,40,24,51,12,42,42,44,164,40,5*4F

$GBGSV,3,2,10,25,42,280,41,40,34,160,37,38,28,192,34,41,22,320,33,5*74

$GBGSV,3,3,10,44,15,102,33,26,14,50,33,5*40

$GBRMC,123336.000,A,2301.2583729,N,11421.9409959,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,123336.000,1.621,0.219,0.207,0.310,1.251,1.356,2.800*76



2025-07-31 20:33:37:400 ==>> $GBGGA,123337.000,2301.2584010,N,11421.9410171,E,1,22,0.64,76.210,M,-1.770,M,,*53

$GBGSA,A,3,14,33,03,06,39,16,24,59,02,09,01,42,1.36,0.64,1.21,4*07

$GBGSA,A,3,60,13,25,08,40,10,38,41,44,26,,,1.36,0.64,1.21,4*08

$GBGSV,7,1,25,14,69,194,40,33,68,284,42,3,62,190,40,6,52,343,35,1*7B

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,2,48,239,34,9,48,321,35,1,46,125,36,42,44,164,39,1*48

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,8,38,207,34,1*44

$GBGSV,7,5,25,40,34,160,37,4,32,112,32,10,30,187,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,13,50,34,1*71

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,68,284,43,39,52,7,40,24,51,12,42,42,44,164,40,5*4E

$GBGSV,3,2,10,25,42,280,41,40,34,160,37,38,28,192,34,41,22,320,33,5*74

$GBGSV,3,3,10,44,15,102,33,26,13,50,33,5*47

$GBRMC,123337.000,A,2301.2584010,N,11421.9410171,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,123337.000,1.609,0.227,0.214,0.320,1.231,1.327,2.689*7E



2025-07-31 20:33:37:475 ==>> [D][05:19:34][COMM]read battery soc:255


2025-07-31 20:33:37:580 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:33:37:777 ==>> [W][05:19:35][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:33:38:394 ==>> [D][05:19:35][CAT1]exec over: func id: 15, ret: -93
[D][05:19:35][CAT1]sub id: 15, ret: -93

[D][05:19:35][SAL ]Cellular task submsg id[68]
[D][05:19:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:35][SAL ]socket send fail. id[4]
[D][05:19:35][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:35][M2M ]m2m select fd[4]
[D][05:19:35][M2M ]socket[4] Link is disconnected
[D][05:19:35][M2M ]tcpclient close[4]
[D][05:19:35][SAL ]socket[4] has closed
[D][05:19:35][PROT]protocol read data ok
[E][05:19:35][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:35][PROT]M2M Send Fail [1629955175]
[D][05:19:35][PROT]CLEAN,SEND:0
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:35][PROT]CLEAN:0
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35

2025-07-31 20:33:38:499 ==>> ][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:35][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 10
[D][05:19:35][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:35][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:35][CAT1]tx ret[12] >>> AT+CGATT=0

$GBGGA,123338.000,2301.2584099,N,11421.9410396,E,1,22,0.64,76.171,M,-1.770,M,,*52

$GBGSA,A,3,14,33,03,06,39,16,24,59,02,09,01,42,1.36,0.64,1.21,4*07

$GBGSA,A,3,60,13,25,08,40,10,38,41,44,26,,,1.36,0.64,1.21,4*08

$GBGSV,7,1,25,14,69,194,40,33,68,284,42,3,62,190,40,6,52,343,35,1*7B

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,2,48,239,34,9,48,321,35,1,46,125,36,42,44,164,39,1*48

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,8,38,207,34,1*44

$GBGSV,7

2025-07-31 20:33:38:604 ==>> ,5,25,40,34,160,37,4,32,112,32,10,30,187,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,13,50,34,1*71

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,68,284,43,39,52,7,41,24,51,12,42,42,44,164,40,5*4F

$GBGSV,3,2,10,25,42,280,41,40,34,160,37,38,28,192,35,41,22,320,33,5*75

$GBGSV,3,3,10,44,15,102,33,26,13,50,34,5*40

$GBRMC,123338.000,A,2301.2584099,N,11421.9410396,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,123338.000,1.541,0.182,0.174,0.257,1.170,1.260,2.559*7C

                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:33:38:664 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:33:38:696 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 20:33:38:704 ==>> 检测【关闭GSM联网】
2025-07-31 20:33:38:717 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:33:38:859 ==>> [W][05:19:36][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:36][COMM]GSM test
[D][05:19:36][COMM]GSM test disable
[D][05:19:36][CAT1]pdpdeact urc len[22]


2025-07-31 20:33:38:981 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:33:38:989 ==>> 检测【4G联网测试】
2025-07-31 20:33:39:030 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:33:39:810 ==>> [D][05:19:36][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:36][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:36][COMM]Main Task receive event:14
[D][05:19:36][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955176, allstateRepSeconds = 0
[D][05:19:36][COMM]index:0,power_mode:0xFF
[D][05:19:36][COMM]index:1,sound_mode:0xFF
[D][05:19:36][COMM]index:2,gsensor_mode:0xFF
[D][05:19:36][COMM]index:3,report_freq_mode:0xFF
[D][05:19:36][COMM]index:4,report_period:0xFF
[D][05:19:36][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:36][COMM]index:6,normal_reset_period:0xFF
[D][05:19:36][COMM]index:7,spock_over_speed:0xFF
[D][05:19:36][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:36][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:36][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:36][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:36][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:36][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:36][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:36][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:36][COMM]index:16,imu_config_params:0xFF
[D][05:19:36][COMM]index:17,long_conne

2025-07-31 20:33:39:915 ==>> ct_params:0xFF
[D][05:19:36][COMM]index:18,detain_mark:0xFF
[D][05:19:36][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:36][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:36][COMM]index:21,mc_mode:0xFF
[D][05:19:36][COMM]index:22,S_mode:0xFF
[D][05:19:36][COMM]index:23,overweight:0xFF
[D][05:19:36][COMM]index:24,standstill_mode:0xFF
[D][05:19:36][COMM]index:25,night_mode:0xFF
[D][05:19:36][COMM]index:26,experiment1:0xFF
[D][05:19:36][COMM]index:27,experiment2:0xFF
[D][05:19:36][COMM]index:28,experiment3:0xFF
[D][05:19:36][COMM]index:29,experiment4:0xFF
[D][05:19:36][COMM]index:30,night_mode_start:0xFF
[D][05:19:36][COMM]index:31,night_mode_end:0xFF
[D][05:19:36][COMM]index:33,park_report_minutes:0xFF
[D][05:19:36][COMM]index:34,park_report_mode:0xFF
[D][05:19:36][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:36][COMM]index:38,charge_battery_para: FF
[D][05:19:36][COMM]index:39,multirider_mode:0xFF
[D][05:19:36][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:36][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:36][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:36][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:36][COMM]index:44,r

2025-07-31 20:33:40:020 ==>> iding_duration_config:0xFF
[D][05:19:36][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:36][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:36][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:36][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:36][COMM]index:49,mc_load_startup:0xFF
[D][05:19:36][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:36][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:36][COMM]index:52,traffic_mode:0xFF
[D][05:19:36][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:36][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:36][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:36][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:36][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:36][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:36][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:36][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:36][COMM]index:63,experiment5:0xFF
[D][05:19:36][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:36][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:36][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:36][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:36][COMM

2025-07-31 20:33:40:125 ==>> ]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:36][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:36][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:36][COMM]index:72,experiment6:0xFF
[D][05:19:36][COMM]index:73,experiment7:0xFF
[D][05:19:36][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:36][COMM]index:75,zero_value_from_server:-1
[D][05:19:36][COMM]index:76,multirider_threshold:255
[D][05:19:36][COMM]index:77,experiment8:255
[D][05:19:36][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:36][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:36][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:36][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:36][COMM]index:83,loc_report_interval:255
[D][05:19:36][COMM]index:84,multirider_threshold_p2:255
[D][05:19:36][COMM]index:85,multirider_strategy:255
[D][05:19:36][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:36][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:36][COMM]index:90,weight_param:0xFF
[D][05:19:36][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:36][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:36][COMM]index:95,current_limit:0xF

2025-07-31 20:33:40:230 ==>> F
[D][05:19:36][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:36][COMM]index:100,location_mode:0xFF

[W][05:19:36][PROT]remove success[1629955176],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:36][PROT]add success [1629955176],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,123339.000,2301.2584012,N,11421.9410539,E,1,22,0.64,76.136,M,-1.770,M,,*50

$GBGSA,A,3,14,33,03,06,39,16,24,59,02,09,01,42,1.36,0.64,1.21,4*07

$GBGSA,A,3,60,13,25,08,40,10,38,41,44,26,,,1.36,0.64,1.21,4*08

$GBGSV,7,1,25,14,69,194,40,33,68,284,42,3,62,190,40,6,52,343,35,1*7B

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,41,59,50,128,40,1*42

$GBGSV,7,3,25,2,48,239,34,9,48,321,35,1,46,125,36,42,44,164,39,1*48

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,8,38,207,34,1*44

$GBGSV,7,5,25,40,34,160,37,4,32,112,32,10,30,187,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,36,44,15,102,34,26,13,50,34,1*71

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,68,284,43,39,52,7,40,24,51,12,42,42,44,164,40,5*4E

$GBGSV,3,2,10,25,42,280,41,40,34,160,36,38,28,192,35,41,22,320,33,5*74

$GBGSV,3,3,10,44,15,102,33,26,13,50,34,5*40

$G

2025-07-31 20:33:40:275 ==>> BRMC,123339.000,A,2301.2584012,N,11421.9410539,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:36][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGST,123339.000,1.750,0.229,0.215,0.324,1.322,1.400,2.607*71

[D][05:19:36][COMM]read battery soc:255


2025-07-31 20:33:40:380 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:33:40:485 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   [D][05:19:37][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:37][CAT1]exec over: func id: 13, ret: 21
[D][05:19:37][M2M ]get csq[23]


2025-07-31 20:33:40:817 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       629955177]
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:37][SAL ]sock send credit cnt[6]
[D][05:19:37][SAL ]sock send ind credit cnt[6]
[D][05:19:37][M2M ]m2m send data len[294]
[D][05:19:37][SAL ]Cellular task submsg id[10]
[D][05:19:37][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0]

2025-07-31 20:33:40:922 ==>>  format[0]
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:37][CAT1]gsm read msg sub id: 15
[D][05:19:37][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:37][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88B183429455FE7ECB70FAC196F7A3BAB68E85B9FACDD192409CAF2EF5B1D2CA9D2C7FE96A0C2CDA603A4B8034A943DA61DA79778E7BD1CAA99CB9F2B2678C55505E23CCBEE12E62918DFAEDA548C69ACC8EC204E715375CA4B55EA63BF906A7B0AECBBE4D9F48B5E2B95B867D03EA1A6508745799970B2BBDF2B6D8ECD4CEF15B805285
[D][05:19:38][CAT1]<<< 
SEND OK

[D][05:19:38][CAT1]exec over: func id: 15, ret: 11
[D][05:19:38][CAT1]sub id: 15, ret: 11

[D][05:19:38][SAL ]Cellular task submsg id[68]
[D][05:19:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:38][M2M ]g_m2m_is_idle become true
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:38][PROT]M2M Send ok [1629955178]
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:38][PROT]remove success[1629955178],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:38][PROT]add success [1629955178],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:38][COMM]-

2025-07-31 20:33:40:982 ==>> ----->period, report file manifest
[D][05:19:38][COMM]Main Task receive event:14 finished processing
[D][05:19:38][CAT1]gsm read msg sub id: 21
[D][05:19:38][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]cell info report total[0]
[D][05:19:38][CAT1]exec over: func id: 21, ret: 6


2025-07-31 20:33:41:016 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:33:41:024 ==>> 检测【关闭GPS】
2025-07-31 20:33:41:036 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:33:41:447 ==>> $GBGGA,123341.000,2301.2584092,N,11421.9410719,E,1,22,0.64,76.115,M,-1.770,M,,*56

$GBGSA,A,3,14,33,03,06,39,16,24,59,02,09,01,42,1.37,0.64,1.21,4*06

$GBGSA,A,3,60,13,25,08,40,10,38,41,44,26,,,1.37,0.64,1.21,4*09

$GBGSV,7,1,25,14,69,194,40,33,68,284,42,3,62,190,40,6,52,343,35,1*7B

$GBGSV,7,2,25,39,52,7,39,16,52,347,37,24,51,12,40,59,50,128,39,1*4D

$GBGSV,7,3,25,2,48,239,34,9,48,321,35,1,46,125,36,42,44,164,38,1*49

$GBGSV,7,4,25,60,43,241,40,13,42,219,36,25,42,280,41,8,38,207,34,1*44

$GBGSV,7,5,25,40,34,160,37,4,32,112,32,10,30,187,33,38,28,192,36,1*47

$GBGSV,7,6,25,5,22,257,32,41,22,320,37,44,15,102,34,26,13,50,34,1*70

$GBGSV,7,7,25,34,,,31,1*74

$GBGSV,3,1,10,33,68,284,43,39,52,7,41,24,51,12,42,42,44,164,40,5*4F

$GBGSV,3,2,10,25,42,280,41,40,34,160,37,38,28,192,35,41,22,320,33,5*75

$GBGSV,3,3,10,44,15,102,33,26,13,50,34,5*40

$GBRMC,123341.000,A,2301.2584092,N,11421.9410719,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123341.000,1.849,0.191,0.182,0.271,1.383,1.450,2.547*7C

[W][05:19:38][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:38][GNSS]stop locating
[D][05:19:38]

2025-07-31 20:33:41:537 ==>> [GNSS]stop event:8
[D][05:19:38][GNSS]GPS stop. ret=0
[D][05:19:38][GNSS]all continue location stop
[W][05:19:38][GNSS]stop locating
[D][05:19:38][GNSS]all sing location stop
[D][05:19:38][CAT1]gsm read msg sub id: 24
[D][05:19:38][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:38][CAT1]<<< 
OK

[D][05:19:38][CAT1]exec over: func id: 24, ret: 6
[D][05:19:38][CAT1]sub id: 24, ret: 6

                                         

2025-07-31 20:33:41:546 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:33:41:555 ==>> 检测【清空消息队列2】
2025-07-31 20:33:41:577 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:33:41:797 ==>> [W][05:19:39][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:39][COMM]Protocol queue cleaned by AT_CMD!
[D][05:19:39][GNSS]recv submsg id[1]
[D][05:19:39][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:39][GNSS]location stop evt done evt


2025-07-31 20:33:42:110 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:33:42:118 ==>> 检测【轮动检测】
2025-07-31 20:33:42:132 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:33:42:255 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:33:42:315 ==>> [D][05:19:39][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:33:42:621 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:33:42:744 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:33:42:926 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:33:42:935 ==>> 检测【关闭小电池】
2025-07-31 20:33:42:948 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:33:43:049 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:33:43:232 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:33:43:240 ==>> 检测【进入休眠模式】
2025-07-31 20:33:43:254 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:33:43:521 ==>> [D][05:19:40][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:40][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:40][COMM]Main Task receive event:28
[D][05:19:40][COMM]main task tmp_sleep_event = 8
[D][05:19:40][COMM]prepare to sleep
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:40][COMM]read battery soc:255


2025-07-31 20:33:44:308 ==>> [D][05:19:41][CAT1]<<< 
OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 6
[D][05:19:41][M2M ]tcpclient close[4]
[D][05:19:41][SAL ]Cellular task submsg id[12]
[D][05:19:41][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:41][CAT1]gsm read msg sub id: 9
[D][05:19:41][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:41][CAT1]<<< 
OK

[D][05:19:41][CAT1]exec over: func id: 9, ret: 6
[D][05:19:41][CAT1]sub id: 9, ret: 6

[D][05:19:41][SAL ]Cellular task submsg id[68]
[D][05:19:41][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:41][SAL ]socket close ind. id[4]
[D][05:19:41][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:41][COMM]1x1 frm_can_tp_send ok
[D][05:19:41][CAT1]pdpdeact urc len[22]


2025-07-31 20:33:44:579 ==>> [E][05:19:42][COMM]1x1 rx timeout
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:33:45:096 ==>> [E][05:19:42][COMM]1x1 rx timeout
[E][05:19:42][COMM]1x1 tp timeout
[E][05:19:42][COMM]1x1 error -3.
[W][05:19:42][COMM]CAN STOP!
[D][05:19:42][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:42][COMM]------------ready to Power off Acckey 1------------
[D][05:19:42][COMM]------------ready to Power off Acckey 2------------
[D][05:19:42][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:42][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1295
[D][05:19:42][COMM]bat sleep fail, reason:-1
[D][05:19:42][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:42][COMM]accel parse set 0
[D][05:19:42][COMM]imu rest ok. 113431
[D][05:19:42][COMM]imu sleep 0
[W][05:19:42][COMM]now sleep


2025-07-31 20:33:45:327 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:33:45:342 ==>> 检测【检测33V休眠电流】
2025-07-31 20:33:45:356 ==>> 开始33V电流采样
2025-07-31 20:33:45:377 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:33:45:441 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:33:46:445 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:33:46:521 ==>> Current33V:????:18.35

2025-07-31 20:33:46:949 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:33:46:957 ==>> 【检测33V休眠电流】通过,【18.35uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:33:46:971 ==>> 该项需要延时执行
2025-07-31 20:33:48:952 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:33:48:966 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:33:48:988 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:33:49:057 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1645mV
Get AD_V4 0mV
Get AD_V5 2748mV
Get AD_V6 2021mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:33:49:989 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:33:49:998 ==>> 检测【打开小电池2】
2025-07-31 20:33:50:011 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:33:50:051 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:33:50:273 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:33:50:281 ==>> 该项需要延时执行
2025-07-31 20:33:50:783 ==>> 此处延时了:【500】毫秒
2025-07-31 20:33:50:797 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:33:50:819 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:33:50:858 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:33:51:060 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:33:51:069 ==>> 该项需要延时执行
2025-07-31 20:33:51:529 ==>> [D][05:19:48][COMM]------------ready to Power on Acckey 1------------
[D][05:19:48][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:48][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:48][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:48][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:48][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:48][COMM]----- get Acckey 1 and value:1------------
[W][05:19:48][COMM]CAN START!
[D][05:19:48][CAT1]gsm read msg sub id: 12
[D][05:19:48][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:48][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 119793
[D][05:19:48][COMM][Audio]exec status ready.
[D][05:19:48][

2025-07-31 20:33:51:574 ==>> 此处延时了:【500】毫秒
2025-07-31 20:33:51:587 ==>> 检测【进入休眠模式2】
2025-07-31 20:33:51:610 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:33:51:623 ==>> CAT1]<<< 
OK

[D][05:19:48][CAT1]exec over: func id: 12, ret: 6
[D][05:19:48][COMM]imu wakeup ok. 119808
[D][05:19:48][COMM]imu wakeup 1
[W][05:19:48][COMM]wake up system, wakeupEvt=0x80
[D][05:19:48][COMM]frm_can_weigth_power_set 1
[D][05:19:48][COMM]Clear Sleep Block Evt
[D][05:19:48][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:48][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:33:51:664 ==>> [D][05:19:49][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 20:33:51:949 ==>> [W][05:19:49][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:49][COMM]1x1 rx timeout
[D][05:19:49][COMM]1x1 frm_can_tp_send ok
[D][05:19:49][COMM]msg 02A0 loss. last_tick:119779. cur_tick:120287. period:50
[D][05:19:49][COMM]msg 02A4 loss. last_tick:119779. cur_tick:120287. period:50
[D][05:19:49][COMM]msg 02A5 loss. last_tick:119779. cur_tick:120288. period:50
[D][05:19:49][COMM]msg 02A6 loss. last_tick:119779. cur_tick:120288. period:50
[D][05:19:49][COMM]msg 02A7 loss. last_tick:119779. cur_tick:120289. period:50
[D][05:19:49][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 120289
[D][05:19:49][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 120289


2025-07-31 20:33:52:269 ==>> [E][05:19:49][COMM]1x1 rx timeout
[E][05:19:49][COMM]1x1 tp timeout
[E][05:19:49][COMM]1x1 error -3.
[D][05:19:49][COMM]Main Task receive event:28 finished processing
[D][05:19:49][COMM]Main Task receive event:28
[D][05:19:49][COMM]prepare to sleep
[D][05:19:49][CAT1]gsm read msg sub id: 12
[D][05:19:49][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:49][CAT1]<<< 
OK

[D][05:19:49][CAT1]exec over: func id: 12, ret: 6
[D][05:19:49][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:49][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:33:52:573 ==>> [D][05:19:49][COMM]msg 0220 loss. last_tick:119779. cur_tick:120783. period:100
[D][05:19:49][COMM]msg 0221 loss. last_tick:119779. cur_tick:120784. period:100
[D][05:19:49][COMM]msg 0224 loss. last_tick:119779. cur_tick:120784. period:100
[D][05:19:49][COMM]msg 0260 loss. last_tick:119779. cur_tick:120784. period:100
[D][05:19:49][COMM]msg 0280 loss. last_tick:119779. cur_tick:120785. period:100
[D][05:19:49][COMM]msg 02C0 loss. last_tick:119779. cur_tick:120785. period:100
[D][05:19:49][COMM]msg 02C1 loss. last_tick:119779. cur_tick:120786. period:100
[D][05:19:49][COMM]msg 02C2 loss. last_tick:119779. cur_tick:120786. period:100
[D][05:19:49][COMM]msg 02E0 loss. last_tick:119779. cur_tick:120786. period:100
[D][05:19:49][COMM]msg 02E1 loss. last_tick:119779. cur_tick:120786. period:100
[D][05:19:49][COMM]msg 02E2 loss. last_tick:119779. cur_tick:120787. period:100
[D][05:19:49][COMM]msg 0300 loss. last_tick:119779. cur_tick:120787. period:100
[D][05:19:49][COMM]msg 0301 loss. last_tick:119779. cur_tick:120788. period:100
[D][05:19:49][COMM]bat msg 0240 loss. last_tick:119779. cur_tick:120788. period:100. j,i:1 54
[D][05:19:49][COMM]bat msg 0241 loss. last_tick:119779. cur_tick:120789. per

2025-07-31 20:33:52:678 ==>> iod:100. j,i:2 55
[D][05:19:49][COMM]bat msg 0242 loss. last_tick:119779. cur_tick:120789. period:100. j,i:3 56
[D][05:19:49][COMM]bat msg 0244 loss. last_tick:119779. cur_tick:120789. period:100. j,i:5 58
[D][05:19:49][COMM]bat msg 024E loss. last_tick:119779. cur_tick:120790. period:100. j,i:15 68
[D][05:19:49][COMM]bat msg 024F loss. last_tick:119779. cur_tick:120790. period:100. j,i:16 69
[D][05:19:49][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 120790
[D][05:19:49][COMM]CAN message bat fault change: 0x00000000->0x0001802E 120791
[D][05:19:49][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 120791
                                                                              

2025-07-31 20:33:52:857 ==>> [D][05:19:50][COMM]msg 0222 loss. last_tick:119779. cur_tick:121285. period:150
[D][05:19:50][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 121286


2025-07-31 20:33:52:947 ==>>                                                              ake : 1
[D][05:19:50][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:50][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:33:53:157 ==>> [E][05:19:50][COMM]1x1 rx timeout
[E][05:19:50][COMM]1x1 tp timeout
[E][05:19:50][COMM]1x1 error -3.
[W][05:19:50][COMM]CAN STOP!
[D][05:19:50][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:50][COMM]------------ready to Power off Acckey 1------------
[D][05:19:50][COMM]------------ready to Power off Acckey 2------------
[D][05:19:50][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:50][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 107
[D][05:19:50][COMM]bat sleep fail, reason:-1
[D][05:19:50][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:50][COMM]accel parse set 0
[D][05:19:50][COMM]imu rest ok. 121476
[D][05:19:50][COMM]imu sleep 0
[W][05:19:50][COMM]now sleep


2025-07-31 20:33:53:387 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:33:53:396 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:33:53:404 ==>> 开始小电池电流采样
2025-07-31 20:33:53:418 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:33:53:502 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:33:54:508 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:33:54:554 ==>> CurrentBattery:ƽ��:67.65

2025-07-31 20:33:55:023 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:33:55:033 ==>> 【检测小电池休眠电流】通过,【67.65uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:33:55:044 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:33:55:059 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:33:55:159 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:33:55:323 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:33:55:334 ==>> 该项需要延时执行
2025-07-31 20:33:55:399 ==>> [D][05:19:52][COMM]------------ready to Power on Acckey 1------------
[D][05:19:52][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:52][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:52][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:52][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:52][COMM]----- get Acckey 1 and value:1------------
[W][05:19:52][COMM]CAN START!
[D][05:19:52][CAT1]gsm read msg sub id: 12
[D][05:19:52][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:52][COMM]CAN message bat fault change: 0x0001802E->0x00000000 123672
[D][05:19:52][COMM][Audio]exec status ready.
[D][05:19:52][CAT1]<<< 
OK

[D][05:19:52][CAT1]exec over: func id: 12, ret: 6
[D][05:19:52][COMM]imu wakeup ok. 123686
[D][05:19:52][COMM]imu wakeup 1
[W][05:19:52][COMM]wake up system, wakeupEvt=0x80
[D][05:19:52][COMM]frm_can_weigth_power_set 1
[D][05:19:52][COMM]Clear Sleep Block Evt
[D][05:19:52][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:52][COMM]1x1 frm_can_tp_send ok
[D][05:19:52][COMM]read battery soc:0


2025-07-31 20:33:55:825 ==>> 此处延时了:【500】毫秒
2025-07-31 20:33:55:843 ==>> 检测【检测唤醒】
2025-07-31 20:33:55:865 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:33:55:882 ==>> [D][05:19:53][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[E][05:19:53][COMM]1x1 rx timeout
[D][05:19:53][COMM]1x1 frm_can_tp_send ok
[D][05:19:53][COMM]msg 02A0 loss. last_tick:123655. cur_tick:124165. period:50
[D][05:19:53][COMM]msg 02A4 loss. last_tick:123655. cur_tick:124166. period:50
[D][05:19:53][COMM]msg 02A5 loss. last_tick:123655. cur_tick:124166. period:50
[D][05:19:53][COMM]msg 02A6 loss. last_tick:123655. cur_tick:124167. period:50
[D][05:19:53][COMM]msg 02A7 loss. last_tick:123655. cur_tick:124167. period:50
[D][05:19:53][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 124167
[D][05:19:53][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 124168


2025-07-31 20:33:56:599 ==>> [W][05:19:53][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:53][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:53][FCTY]==========Modules-nRF5340 ==========
[D][05:19:53][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:53][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:53][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:53][FCTY]DeviceID    = 460130071539121
[D][05:19:53][FCTY]HardwareID  = 867222087736122
[D][05:19:53][FCTY]MoBikeID    = 9999999999
[D][05:19:53][FCTY]LockID      = FFFFFFFFFF
[D][05:19:53][FCTY]BLEFWVersion= 105
[D][05:19:53][FCTY]BLEMacAddr   = DC0CF37C2595
[D][05:19:53][FCTY]Bat         = 3844 mv
[D][05:19:53][FCTY]Current     = 0 ma
[D][05:19:53][FCTY]VBUS        = 2600 mv
[D][05:19:53][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:53][FCTY]Ext battery vol = 32, adc = 1304
[D][05:19:53][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 126 mv
[D][05:19:53][FCTY]Bike Type flag is invalied
[D][05:19:53][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:53][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:53][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:53][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:53][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:53][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:53][FCTY]Bat1         = 3843 mv
[D][05:19:

2025-07-31 20:33:56:704 ==>> 53][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:53][FCTY]==========Modules-nRF5340 ==========
[E][05:19:53][COMM]1x1 rx timeout
[E][05:19:53][COMM]1x1 tp timeout
[E][05:19:53][COMM]1x1 error -3.
[D][05:19:53][COMM]Main Task receive event:28 finished processing
[D][05:19:53][COMM]Main Task receive event:65
[D][05:19:53][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:53][COMM]Main Task receive event:65 finished processing
[D][05:19:53][COMM]Main Task receive event:60
[D][05:19:53][COMM]smart_helmet_vol=255,255
[D][05:19:53][COMM]report elecbike
[W][05:19:53][PROT]remove success[1629955193],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:53][PROT]add success [1629955193],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:53][COMM]Main Task receive event:60 finished processing
[D][05:19:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:53][PROT]index:0
[D][05:19:53][PROT]is_send:1
[D][05:19:53][PROT]sequence_num:10
[D][05:19:53][PROT]retry_timeout:0
[D][05:19:53][PROT]retry_times:3
[D][05:19:53][PROT]send_path:0x3
[D][05:19:53][PROT]msg_type:0x5d03
[D][05:19:53][PROT]======================

2025-07-31 20:33:56:809 ==>> =====================================
[W][05:19:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955193]
[D][05:19:53][PROT]===========================================================
[D][05:19:53][PROT]Sending traceid[999999999990000B]
[D][05:19:53][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:53][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:53][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:53][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:53][SAL ]open socket ind id[4], rst[0]
[D][05:19:53][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:53][SAL ]Cellular task submsg id[8]
[D][05:19:53][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:53][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:53][CAT1]gsm read msg sub id: 8
[D][05:19:53][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:

2025-07-31 20:33:56:873 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:33:56:885 ==>> 检测【关机】
2025-07-31 20:33:56:896 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:33:56:915 ==>> 53][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:53][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:53][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:53][CAT1]<<< 
+CME ERROR: 100

[D][05:19:53][COMM]msg 0220 loss. last_tick:123655. cur_tick:124667. period:100
[D][05:19:53][COMM]msg 0221 loss. last_tick:123655. cur_tick:124667. period:100
[D][05:19:53][COMM]msg 0224 loss. last_tick:123655. cur_tick:124668. period:100
[D][05:19:53][COMM]msg 0260 loss. last_tick:123655. cur_tick:124668. period:100
[D][05:19:53][COMM]msg 0280 loss. last_tick:123655. cur_tick:124669. period:100
[D][05:19:53][COMM]msg 02C0 loss. last_tick:123655. cur_tick:124669. period:100
[D][05:19:53][COMM]msg 02C1 loss. last_tick:123655. cur_tick:124669. period:100
[D][05:19:53][COMM]msg 02C2 loss. last_tick:123655. cur_tick:124669. period:100
[D][05:19:53][COMM]msg 02E0 loss. last_tick:123655. cur_tick:124670. period:100
[D][05:19:53][COMM]msg 02E1 loss. last_tick:123655. cur_tick:124670. period:100
[D][05:19:53][COMM]msg 02E2 loss. last_tick:123655. cur_tick:124671. period:100
[D][05:19:53][COMM]msg 0300 loss. last_tick:123655. cur_tick:124671. period:100
[D][05:19:53][COMM]msg 0301 loss. last_tick:1

2025-07-31 20:33:57:004 ==>> 23655. cur_tick:124671. period:100
[D][05:19:53][COMM]bat msg 0240 loss. last_tick:123655. cur_tick:124672. period:100. j,i:1 54
[D][05:19:53][COMM]bat msg 0241 loss. last_tick:123655. cur_tick:124672. period:100. j,i:2 55
[D][05:19:53][COMM]bat msg 0242 loss. last_tick:123655. cur_tick:124673. period:100. j,i:3 56
[D][05:19:53][COMM]bat msg 0244 loss. last_tick:123655. cur_tick:124673. period:100. j,i:5 58
[D][05:19:53][COMM]bat msg 024E loss. last_tick:123655. cur_tick:124673. period:100. j,i:15 68
[D][05:19:53][COMM]bat msg 024F loss. last_tick:123655. cur_tick:124674. period:100. j,i:16 69
[D][05:19:53][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 124674
[D][05:19:53][COMM]CAN message bat fault change: 0x00000000->0x0001802E 124675
[D][05:19:53][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 124675


2025-07-31 20:33:57:658 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:33:57:763 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:33:57:869 ==>>                                                                                                                                                                                                                                                                           io_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:54][COMM]Main Task receive event:66
[D][05:19:54][COMM]Try to Auto Lock Bat
[D][05:19:54][COMM]Main Task receive event:66 finished processing
[D][05:19:54][COMM]Receive Bat Lock cmd 0
[D][05:19:54][COMM]VBUS is 1
[D][05:19:54][COMM]Main Task receive event:60
[D][05:19:54][COMM]smart_helmet_vol=255,255
[D][05:19:54][COMM]BAT CAN get state1 Fail 204
[D][05:19:54][COMM]BAT CAN get soc Fail, 204
[D][05:19:54][COMM]BAT CAN get state2 fail 204
[D][05:19:54][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:54][COMM]get soh error
[E][05:19:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:54][COMM]report elecbike
[W][05:19:54][PROT]remove success[1629955194],s

2025-07-31 20:33:57:898 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:33:57:974 ==>> end_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:54][PROT]add success [1629955194],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:54][COMM]Main Task receive event:60 finished processing
[D][05:19:54][COMM]Main Task receive event:61
[D][05:19:54][COMM][D301]:type:3, trace id:280
[D][05:19:54][COMM]id[], hw[000
[D][05:19:54][COMM]get mcMaincircuitVolt error
[D][05:19:54][COMM]get mcSubcircuitVolt error
[D][05:19:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:54][COMM]BAT CAN get state1 Fail 204
[D][05:19:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:54][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:54][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:54][PROT]index:1
[D][05:19:54][PROT]is_send:1
[D][05:19:54][PROT]sequence_num:11
[D][05:19:54][PROT]retry_timeout:0
[D][05:19:54][PROT]retry_times:3
[D][05:19:54][PROT]send_path:0x3
[D][05:19:54][PROT]msg_type:0x5d03
[D][05:19:54][PROT]===========================================================
[W][05:19:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955194]
[D][05:19:54][PROT]===========================================================
[D][05:19:54][PROT]Sending

2025-07-31 20:33:58:079 ==>>  traceid[999999999990000C]
[D][05:19:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:54][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:54][COMM]BAT CAN get soc Fail, 204
[D][05:19:54][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:54][COMM]BAT CAN get state2 fail 204
[D][05:19:54][COMM]get bat work mode err
[W][05:19:54][PROT]remove success[1629955194],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:19:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:54][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:54][PROT]add success [1629955194],send_path[2],type[D302],priority[0],inde

2025-07-31 20:33:58:183 ==>> x[2],used[1]
[D][05:19:54][COMM]Main Task receive event:61 finished processing
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:54][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:54][COMM]arm_hub_enable: hub power: 0
[D][05:19:54][HSDK]hexlog index save 0 3584 187 @ 0 : 0
[D][05:19:54][HSDK]write save hexlog index [0]
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:54][COMM]read battery soc:255
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][C

2025-07-31 20:33:58:274 ==>> OMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:19:55][COMM]exit wheel stolen mode.
                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:33:58:378 ==>>                                                                                                                                                                                    55][HSDK]write save hexlog index [0]
[D][05:19:55][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:55][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:33:58:830 ==>> [W][05:19:56][COMM]Power Off


2025-07-31 20:33:58:950 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:33:58:960 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:33:58:996 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:33:59:055 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:33:59:224 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:33:59:233 ==>> 检测【检测小电池关机电流】
2025-07-31 20:33:59:242 ==>> 开始小电池电流采样
2025-07-31 20:33:59:272 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:33:59:282 ==>> [D][05:19:56][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 6,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 7,volt = 14
[D][05:19:56][FCTY]get_ext_48v_vol retry i = 8,volt = 14
[D][05:19:56][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 20:33:59:325 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:34:00:337 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:34:00:414 ==>> CurrentBattery:ƽ��:68.76

2025-07-31 20:34:00:839 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:34:00:849 ==>> 【检测小电池关机电流】通过,【68.76uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:34:01:173 ==>> MES过站成功
2025-07-31 20:34:01:183 ==>> #################### 【测试结束】 ####################
2025-07-31 20:34:01:203 ==>> 关闭5V供电
2025-07-31 20:34:01:218 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:34:01:252 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:34:02:210 ==>> 关闭5V供电成功
2025-07-31 20:34:02:224 ==>> 关闭33V供电
2025-07-31 20:34:02:249 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:34:02:350 ==>> 5A A5 02 5A A5 


2025-07-31 20:34:02:455 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:34:03:217 ==>> 关闭33V供电成功
2025-07-31 20:34:03:231 ==>> 关闭3.7V供电
2025-07-31 20:34:03:252 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:34:03:352 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:34:04:141 ==>>  

2025-07-31 20:34:04:231 ==>> 关闭3.7V供电成功
