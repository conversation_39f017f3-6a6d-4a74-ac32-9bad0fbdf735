2025-07-31 23:25:14:272 ==>> MES查站成功:
查站序号:P510001005313958验证通过
2025-07-31 23:25:14:276 ==>> 扫码结果:P510001005313958
2025-07-31 23:25:14:277 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:25:14:279 ==>> 测试参数版本:2024.10.11
2025-07-31 23:25:14:280 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:25:14:282 ==>> 检测【打开透传】
2025-07-31 23:25:14:283 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:25:14:333 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:25:14:550 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:25:14:560 ==>> 检测【检测接地电压】
2025-07-31 23:25:14:562 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:25:14:625 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:25:14:842 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:25:14:844 ==>> 检测【打开小电池】
2025-07-31 23:25:14:848 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:25:14:930 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:25:15:006 ==>>  

2025-07-31 23:25:15:115 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:25:15:119 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:25:15:122 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:25:15:232 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:25:15:384 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:25:15:388 ==>> 检测【等待设备启动】
2025-07-31 23:25:15:392 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:25:15:622 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:25:15:817 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:25:16:416 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:25:16:461 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:25:16:506 ==>>                                                    

2025-07-31 23:25:16:907 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:25:17:380 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:25:17:474 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:25:17:476 ==>> 检测【产品通信】
2025-07-31 23:25:17:479 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:25:17:605 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:25:17:755 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:25:17:757 ==>> 检测【初始化完成检测】
2025-07-31 23:25:17:760 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:25:17:957 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:25:18:031 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:25:18:035 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:25:18:037 ==>> 检测【关闭大灯控制1】
2025-07-31 23:25:18:038 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:25:18:185 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:25:18:313 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:25:18:315 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:25:18:316 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:25:18:428 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:25:18:626 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:25:18:843 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:25:18:847 ==>> 检测【关闭仪表供电】
2025-07-31 23:25:18:850 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:25:19:023 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:25:19:116 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:25:19:119 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:25:19:145 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:25:19:284 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:25:19:397 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:25:19:399 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:25:19:401 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:25:19:451 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:25:19:586 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:25:19:680 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:25:19:682 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:25:19:683 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:25:19:908 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:25:19:957 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:25:19:959 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:25:19:960 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:25:20:014 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:25:20:119 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:17:53][COMM]read battery soc:255


2025-07-31 23:25:20:227 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:25:20:230 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:25:20:232 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:25:20:331 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:25:20:481 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 23:25:20:558 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:25:20:560 ==>> 该项需要延时执行
2025-07-31 23:25:20:984 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5007. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5007. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5007. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5008. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5009. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00

2025-07-31 23:25:21:014 ==>> C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010


2025-07-31 23:25:21:474 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:25:21:717 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:25:22:225 ==>>                                                                       17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1-

2025-07-31 23:25:22:330 ==>> -----------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:t

2025-07-31 23:25:22:435 ==>> ype:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]

2025-07-31 23:25:22:525 ==>> BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 23:25:23:488 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 23:25:24:140 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:25:24:510 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:25:24:570 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:25:24:574 ==>> 检测【33V输入电压ADC】
2025-07-31 23:25:24:586 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:24:830 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:17:57][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:17:57][COMM]adc read left brake adc:2  volt:2 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:57][COMM]adc read battery ts volt:5 mv
[D][05:17:57][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:25:25:138 ==>> 【33V输入电压ADC】通过,【32754mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:25:25:142 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:25:25:144 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:25:25:232 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1670mV
Get AD_V4 0mV
Get AD_V5 2785mV
Get AD_V6 1989mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:25:25:446 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:25:449 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:25:25:510 ==>> [D][05:17:58][COMM]9702 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:25:25:521 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1670mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:25:523 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:25:25:526 ==>> 原始值:【2785】, 乘以分压基数【2】还原值:【5570】
2025-07-31 23:25:25:566 ==>> 【TP68_VCC5V5(ADV5)】通过,【5570mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:25:25:569 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:25:25:608 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:25:25:631 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:25:25:653 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:25:25:656 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:25:25:737 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1672mV
Get AD_V4 0mV
Get AD_V5 2785mV
Get AD_V6 1990mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:25:25:842 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN mess

2025-07-31 23:25:25:872 ==>> age fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10017


2025-07-31 23:25:25:977 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:25:979 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:25:26:034 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:26:037 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:25:26:040 ==>> 原始值:【2785】, 乘以分压基数【2】还原值:【5570】
2025-07-31 23:25:26:093 ==>> 【TP68_VCC5V5(ADV5)】通过,【5570mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:25:26:095 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:25:26:146 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:25:26:194 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:25:26:196 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:25:26:288 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:25:26:290 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:25:26:434 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1671mV
Get AD_V4 1mV
Get AD_V5 2782mV
Get AD_V6 1986mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:25:26:566 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:26:572 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:25:26:601 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:26:604 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:25:26:605 ==>> 原始值:【2782】, 乘以分压基数【2】还原值:【5564】
2025-07-31 23:25:26:619 ==>> 【TP68_VCC5V5(ADV5)】通过,【5564mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:25:26:622 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:25:26:640 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1986mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:25:26:643 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:25:26:675 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:25:26:679 ==>> 检测【打开WIFI(1)】
2025-07-31 23:25:26:682 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:25:26:769 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10713 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:5

2025-07-31 23:25:26:799 ==>> 9][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:25:27:227 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087631745

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:25:27:482 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:25:27:484 ==>> 检测【清空消息队列(1)】
2025-07-31 23:25:27:487 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:25:27:810 ==>>                   ]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 5, ret: 6
[D][05:18:00][CAT1]sub id: 5, ret: 6

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:00][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:00][GNSS]recv submsg id[1]
[D][05:18:00][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:00][GNSS]location recv gms init done evt
[D][05:18:00][M2M ]M2M_GSM_INIT OK
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:00][SAL ]open socket ind id[4], rst[0]
[D][05:18:00][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:00][SAL ]Cellular task submsg id[8]
[D][05:18:00][SAL ]cellular OPEN socket size[

2025-07-31 23:25:27:915 ==>> 144], msg->data[0x20052db0], socket[0]
[D][05:18:00][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:00][CAT1]gsm read msg sub id: 8
[D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:00][COMM]Main Task receive event:4
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:00][COMM]init key as 
[D][05:18:00][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:00][COMM]Main Task receive event:4 finished processing
[D][05:18:00][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:00][CAT1]<<< 
+QIACT: 1,1,1,"10.192.153.110"

OK

[D][05:18:00][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func

2025-07-31 23:25:27:945 ==>>  id: 8, ret: 6


2025-07-31 23:25:28:009 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:25:28:011 ==>> 检测【打开GPS(1)】
2025-07-31 23:25:28:013 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:25:28:450 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[D][05:18:01][GNSS]GPS start. ret=0
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:25:28:568 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:25:28:573 ==>> 检测【打开GSM联网】
2025-07-31 23:25:28:577 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:25:28:710 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:25:28:916 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:25:28:920 ==>> 检测【打开仪表供电1】
2025-07-31 23:25:28:924 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:25:29:156 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:25:29:217 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:25:29:220 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:25:29:223 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:25:29:415 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:25:29:493 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:25:29:496 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:25:29:500 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:25:29:520 ==>> [D][05:18:02][COMM]13729 imu init OK


2025-07-31 23:25:29:717 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33108]


2025-07-31 23:25:29:766 ==>> 【读取主控ADC采集的仪表电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:25:29:769 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:25:29:772 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:25:29:807 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:25:30:032 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,23,,,40,34,,,40,39,,,40,25,,,39,1*70

$GBGSV,3,2,10,7,,,42,59,,,39,4,,,38,5,,,37,1*4F

$GBGSV,3,3,10,11,,,37,16,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1647.839,1647.839,52.587,2097152,2097152,2097152*42

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 23:25:30:168 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 23:25:30:290 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:25:30:295 ==>> 检测【AD_V20电压】
2025-07-31 23:25:30:320 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:25:30:395 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:25:30:490 ==>> 本次取值间隔时间:87ms
2025-07-31 23:25:30:535 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:25:30:625 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:25:30:869 ==>> 本次取值间隔时间:371ms
2025-07-31 23:25:30:930 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,40,,,42,7,,,40,34,,,40,39,,,40,1*4D

$GBGSV,4,2,13,59,,,40,25,,,40,60,,,40,3,,,40,1*4C

$GBGSV,4,3,13,23,,,39,11,,,39,16,,,38,43,,,37,1*7D

$GBGSV,4,4,13,4,,,33,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1620.019,1620.019,51.780,2097152,2097152,2097152*44



2025-07-31 23:25:31:255 ==>> 本次取值间隔时间:371ms
2025-07-31 23:25:31:741 ==>> 本次取值间隔时间:475ms
2025-07-31 23:25:31:745 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:25:31:848 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:25:31:985 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,34,,,40,39,,,40,59,,,40,1*7C

$GBGSV,5,2,18,25,,,40,3,,,40,7,,,39,60,,,39,1*7D

$GBGSV,5,3,18,23,,,39,11,,,39,16,,,38,43,,,38,1*78

$GBGSV,5,4,18,2,,,34,1,,,34,41,,,34,5,,,32,1*4B

$GBGSV,5,5,18,4,,,31,32,,,30,1*4B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1540.900,1540.900,49.326,2097152,2097152,2097152*45

[D][05:18:05][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:05][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1661mV
OVER 150


2025-07-31 23:25:32:169 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 23:25:32:244 ==>> 本次取值间隔时间:384ms
2025-07-31 23:25:32:276 ==>> 【AD_V20电压】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:32:292 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:25:32:294 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:25:32:321 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:25:32:561 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:25:32:564 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:25:32:568 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:25:32:740 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:25:32:834 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:25:32:862 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:25:32:865 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:25:32:982 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150
$GBGGA,152536.832,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,39,,,41,34,,,40,59,,,40,1*76

$GBGSV,5,2,20,25,,,40,3,,,40,7,,,39,60,,,39,1*76

$GBGSV,5,3,20,23,,,39,11,,,39,43,,,39,16,,,38,1*72

$GBGSV,5,4,20,10,,,37,41,,,36,1,,,35,24,,,35,1*47

$GBGSV,5,5,20,32,,,34,2,,,33,5,,,32,4,,,31,1*42

$GBRMC,152536.832,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152536.832,0.000,1552.623,1552.623,49.678,2097152,2097152,2097152*5A



2025-07-31 23:25:33:131 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:25:33:134 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:25:33:136 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:25:33:318 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:25:33:467 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:25:33:471 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:25:33:474 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:25:33:702 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,152537.532,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,41,34,,,40,59,,,40,1*76

$GBGSV,6,2,23,25,,,40,3,,,40,60,,,40,11,,,40,1*41

$GBGSV,6,3,23,43,,,40,7,,,39,23,,,39,16,,,38,1*4B

$GBGSV,6,4,23,10,,,37,41,,,37,1,,,36,24,,,34,1*44

$GBGSV,6,5,23,32,,,34,2,,,34,9,,,34,6,,,32,1*4E

$GBGSV,6,6,23,5,,,32,4,,,31,44,,,45,1*74

$GBRMC,152537.532,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152537.532,0.000,1545.283,1545.283,49.453,2097152,2097152,2097152*5D



2025-07-31 23:25:33:785 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:25:33:788 ==>> 检测【AD_V21电压】
2025-07-31 23:25:33:792 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:25:33:931 ==>> 1A A1 20 00 00 
Get AD_V21 1657mV
OVER 150


2025-07-31 23:25:34:174 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 23:25:34:219 ==>> 本次取值间隔时间:423ms
2025-07-31 23:25:34:238 ==>> 【AD_V21电压】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:34:242 ==>> 检测【关闭仪表供电2】
2025-07-31 23:25:34:245 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:25:34:418 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:25:34:512 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:25:34:515 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:25:34:518 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:25:34:674 ==>> $GBGGA,152538.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,40,25,,,40,1*7F

$GBGSV,6,2,23,3,,,40,60,,,40,43,,,40,59,,,39,1*43

$GBGSV,6,3,23,11,,,39,7,,,39,23,,,39,16,,,39,1*43

$GBGSV,6,4,23,10,,,37,41,,,37,1,,,36,32,,,35,1*42

$GBGSV,6,5,23,24,,,34,9,,,34,6,,,34,2,,,33,1*48

$GBGSV,6,6,23,5,,,31,4,,,30,12,,,29,1*7F

$GBRMC,152538.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152538.512,0.000,1524.978,1524.978,48.818,2097152,2097152,2097152*52



2025-07-31 23:25:34:749 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:25:34:783 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:25:34:786 ==>> 检测【打开AccKey2供电】
2025-07-31 23:25:34:788 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:25:34:991 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:25:35:053 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:25:35:058 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:25:35:082 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:35:339 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:08][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:08][COMM]adc read battery ts volt:4 mv
[D][05:18:08][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 23:25:35:588 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33133mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:25:35:594 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:25:35:615 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:25:35:673 ==>> $GBGGA,152539.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,6,2,24,3,,,40,43,,,40,59,,,40,60,,,39,1*44

$GBGSV,6,3,24,11,,,39,7,,,39,23,,,39,16,,,38,1*45

$GBGSV,6,4,24,10,,,37,41,,,37,1,,,36,32,,,35,1*45

$GBGSV,6,5,24,6,,,35,24,,,34,9,,,34,2,,,33,1*4E

$GBGSV,6,6,24,5,,,31,12,,,31,4,,,30,44,,,29,1*7A

$GBRMC,152539.512,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152539.512,0.000,1514.993,1514.993,48.503,2097152,2097152,2097152*54



2025-07-31 23:25:35:748 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:25:35:878 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:25:35:882 ==>> 该项需要延时执行
2025-07-31 23:25:36:194 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 23:25:36:670 ==>> $GBGGA,152540.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,7,2,25,3,,,40,59,,,40,60,,,40,43,,,39,1*44

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,2,,,34,24,,,33,1*79

$GBGSV,7,6,25,12,,,32,5,,,31,4,,,31,44,,,30,1*70

$GBGSV,7,7,25,13,,,36,1*76

$GBRMC,152540.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152540.512,0.000,1520.162,1520.162,48.655,2097152,2097152,2097152*5A



2025-07-31 23:25:37:678 ==>> $GBGGA,152541.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,25,,,40,1*78

$GBGSV,7,2,25,3,,,40,59,,,40,60,,,39,43,,,39,1*4A

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,2,,,34,24,,,34,1*7E

$GBGSV,7,6,25,12,,,32,33,,,32,5,,,31,4,,,31,1*72

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152541.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152541.512,0.000,1512.423,1512.423,48.408,2097152,2097152,2097152*51



2025-07-31 23:25:38:190 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 23:25:38:697 ==>> $GBGGA,152542.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,3,,,40,1*4C

$GBGSV,7,2,25,59,,,40,34,,,39,60,,,39,43,,,39,1*70

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,34,2,,,33,1*79

$GBGSV,7,6,25,12,,,33,33,,,32,5,,,31,4,,,31,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152542.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152542.512,0.000,1512.417,1512.417,48.402,2097152,2097152,2097152*58



2025-07-31 23:25:38:879 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:25:38:884 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:25:38:911 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:39:233 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:12][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:12][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:12][COMM]adc read battery ts volt:2 mv
[D][05:18:12][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 23:25:39:418 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:25:39:423 ==>> 检测【打开AccKey1供电】
2025-07-31 23:25:39:427 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:25:39:709 ==>> $GBGGA,152543.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,3,,,40,1*4C

$GBGSV,7,2,25,59,,,40,34,,,40,60,,,39,43,,,39,1*7E

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,34,2,,,33,1*79

$GBGSV,7,6,25,12,,,33,33,,,32,5,,,31,4,,,31,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152543.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152543.512,0.000,1514.077,1514.077,48.456,2097152,2097152,2097152*58

[W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:25:39:957 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:25:39:961 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:25:39:963 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:25:40:031 ==>> 1A A1 00 40 00 
Get AD_V14 2684mV
OVER 150


2025-07-31 23:25:40:214 ==>> 原始值:【2684】, 乘以分压基数【2】还原值:【5368】
2025-07-31 23:25:40:221 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 23:25:40:302 ==>> 【读取AccKey1电压(ADV14)前】通过,【5368mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:25:40:308 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:25:40:313 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:40:704 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:7 mv
[D][05:18:13][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,152544.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,59,,,40,1*73

$GBGSV,7,2,25,34,,,40,3,,,39,60,,,39,43,,,39,1*4F

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,39,16,,,38,1*45

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,34,2,,,33,1*79

$GBGSV,7,6,25,1

2025-07-31 23:25:40:749 ==>> 2,,,33,33,,,32,4,,,32,5,,,31,1*70

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152544.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152544.512,0.000,1515.732,1515.732,48.506,2097152,2097152,2097152*5B



2025-07-31 23:25:40:850 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:25:40:855 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:25:40:858 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:25:40:993 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:25:41:142 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:25:41:145 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:25:41:148 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:25:41:222 ==>> 1A A1 00 40 00 
Get AD_V14 2682mV
OVER 150


2025-07-31 23:25:41:405 ==>> 原始值:【2682】, 乘以分压基数【2】还原值:【5364】
2025-07-31 23:25:41:425 ==>> 【读取AccKey1电压(ADV14)后】通过,【5364mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:25:41:430 ==>> 检测【打开WIFI(2)】
2025-07-31 23:25:41:452 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:25:41:741 ==>> $GBGGA,152545.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,59,,,40,1*73

$GBGSV,7,2,25,34,,,40,3,,,40,60,,,39,43,,,39,1*41

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,39,16,,,38,1*45

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,34,2,,,33,1*79

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,32,5,,,32,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152545.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152545.512,0.000,1519.047,1519.047,48.610,2097152,2097152,2097152*5E

[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:25:41:958 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:25:41:962 ==>> 检测【转刹把供电】
2025-07-31 23:25:41:965 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:25:42:110 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:25:42:200 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 23:25:42:233 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:25:42:237 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:25:42:242 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:25:42:336 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:25:42:428 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2430mV
OVER 150


2025-07-31 23:25:42:488 ==>> 原始值:【2430】, 乘以分压基数【2】还原值:【4860】
2025-07-31 23:25:42:506 ==>> 【读取AD_V15电压(前)】通过,【4860mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:25:42:511 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:25:42:516 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:25:42:610 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:25:42:793 ==>> +WIFISCAN:4,0,CC057790A621,-50
+WIFISCAN:4,1,F42A7D1297A3,-63
+WIFISCAN:4,2,F86FB0660A82,-83
+WIFISCAN:4,3,646E97BD0450,-83

[D][05:18:15][CAT1]wifi scan report total[4]
[D][05:18:15][GNSS]recv submsg id[3]
$GBGGA,152546.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,40,25,,,40,59,,,40,1*70

$GBGSV,7,2,25,34,,,40,3,,,40,60,,,39,43,,,39,1*41

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,39,16,,,38,1*45

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152546.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152546.512,0.000,1515.741,1515.741,48.515,2097152,2097152,2097152*5B

[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2459mV
OVER 150


2025-07-31 23:25:42:928 ==>> 原始值:【2459】, 乘以分压基数【2】还原值:【4918】
2025-07-31 23:25:42:956 ==>> 【读取AD_V16电压(前)】通过,【4918mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:25:42:962 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:25:42:967 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:43:236 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:16][COMM]adc read battery ts volt:8 mv
[D][05:18:16][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3119  volt:5482 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2387  volt:3846 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:25:43:498 ==>> 【转刹把供电电压(主控ADC)】通过,【5482mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:25:43:502 ==>> 检测【转刹把供电电压】
2025-07-31 23:25:43:505 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:43:680 ==>> $GBGGA,152547.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,34,,,40,1*78

$GBGSV,7,2,25,59,,,39,3,,,39,60,,,39,43,,,39,1*4A

$GBGSV,7,3,25,11,,,39,7,,,39,23,,,39,16,,,38,1*45

$GBGSV,7,4,25,10,,,37,41,,,37,1,,,36,6,,,36,1*71

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,32,5,,,31,1*70

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152547.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152547.512,0.000,1512.415,1512.415,48.400,2097152,2097152,2097152*5F



2025-07-31 23:25:43:906 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:16][COMM]adc read battery ts volt:10 mv
[D][05:18:16][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3115  volt:5475 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1429  volt:33131 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:25:44:031 ==>> 【转刹把供电电压】通过,【5475mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:25:44:035 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:25:44:039 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:25:44:238 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:17][COMM]read battery soc:255


2025-07-31 23:25:44:302 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:25:44:306 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:25:44:309 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:25:44:403 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:25:44:493 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:25:44:508 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:25:44:523 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:25:44:613 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:25:44:628 ==>> $GBGGA,152548.512,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,39,34,,,39,1*78

$GBGSV,7,2,25,59,,,39,3,,,39,60,,,39,43,,,39,1*4A

$GBGSV,7,3,25,7,,,39,23,,,39,11,,,38,16,,,38,1*44

$GBGSV,7,4,25,41,,,37,10,,,36,1,,,36,6,,,36,1*70

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2

2025-07-31 23:25:44:674 ==>> ,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152548.512,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152548.512,0.000,1502.467,1502.467,48.084,2097152,2097152,2097152*58



2025-07-31 23:25:44:737 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:25:44:741 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:25:44:746 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:25:44:767 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:25:44:838 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:25:44:883 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:25:44:928 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:25:44:961 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:25:44:966 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:25:44:970 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:25:45:018 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 23:25:45:231 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:25:45:238 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:25:45:249 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:25:45:321 ==>> 3A A3 04 01 A3 


2025-07-31 23:25:45:426 ==>> ON_OUT4
OVER 150


2025-07-31 23:25:45:512 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:25:45:515 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:25:45:518 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:25:45:698 ==>> 3A A3 05 01 A3 
$GBGGA,152549.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,25,,,39,1*76

$GBGSV,7,2,25,59,,,39,3,,,39,60,,,39,43,,,39,1*4A

$GBGSV,7,3,25,7,,,39,23,,,39,11,,,39,16,,,38,1*45

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,35,1*73

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152549.512,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152549.512,0.000,1505.784,1505.784,48.190,2097152,2097152,2097152*5D



2025-07-31 23:25:45:728 ==>> ON_OUT5
OVER 150


2025-07-31 23:25:45:791 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:25:45:797 ==>> 检测【左刹电压测试1】
2025-07-31 23:25:45:821 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:46:137 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1726  volt:2275 mv
[D][05:18:19][COMM]adc read right brake adc:1713  volt:2258 mv
[D][05:18:19][COMM]adc read throttle adc:1707  volt:2250 mv
[D][05:18:19][COMM]adc read battery ts volt:0 mv
[D][05:18:19][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 23:25:46:227 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 23:25:46:321 ==>> 【左刹电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 23:25:46:327 ==>> 检测【右刹电压测试1】
2025-07-31 23:25:46:355 ==>> 【右刹电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 23:25:46:359 ==>> 检测【转把电压测试1】
2025-07-31 23:25:46:374 ==>> 【转把电压测试1】通过,【2250】符合目标值【2250】至【2500】要求!
2025-07-31 23:25:46:379 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:25:46:389 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:25:46:423 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:25:46:655 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:25:46:659 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:25:46:665 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:25:46:670 ==>> $GBGGA,152550.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,3,,,40,1*4C

$GBGSV,7,2,25,34,,,39,59,,,39,60,,,39,43,,,39,1*7E

$GBGSV,7,3,25,7,,,39,23,,,39,11,,,39,16,,,38,1*45

$GBGSV,7,4,25,41,,,37,10,,,37,6,,,36,1,,,35,1*72

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,32,5,,,31,1*70

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152550.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152550.512,0.000,1510.757,1510.757,48.347,2097152,2097152,2097152*5D



2025-07-31 23:25:46:726 ==>> 3A A3 04 00 A3 


2025-07-31 23:25:46:831 ==>> OFF_OUT4
OVER 150


2025-07-31 23:25:46:924 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:25:46:931 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:25:46:954 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:25:47:028 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:25:47:213 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:25:47:217 ==>> 检测【左刹电压测试2】
2025-07-31 23:25:47:222 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:25:47:530 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:20][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:20][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:20][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:20][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:20][COMM]adc read battery ts volt:6 mv
[D][05:18:20][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:25:47:635 ==>>                                                                                                                       40,1*4C

$GBGSV,7,2,25,34,,,39,59,,,39,60,,,39,43,,,39,1*7E

$GBGSV,7,3,25,7,,,39,23,,,39,11,,,39,16,,,38,1*45

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,35,1*73

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,32,5,,,31,1*70

$GBGSV,7,7,2

2025-07-31 23:25:47:680 ==>> 5,44,,,31,1*73

$GBRMC,152551.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152551.512,0.000,1509.099,1509.099,48.295,2097152,2097152,2097152*52



2025-07-31 23:25:47:744 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 23:25:47:749 ==>> 检测【右刹电压测试2】
2025-07-31 23:25:47:764 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:25:47:769 ==>> 检测【转把电压测试2】
2025-07-31 23:25:47:784 ==>> 【转把电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 23:25:47:788 ==>> 检测【晶振检测】
2025-07-31 23:25:47:794 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:25:47:997 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 23:25:48:062 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:25:48:066 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:25:48:073 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:25:48:102 ==>> 1A A1 00 00 FC 
Get AD

2025-07-31 23:25:48:132 ==>> _V2 1655mV
Get AD_V3 1670mV
Get AD_V4 1651mV
Get AD_V5 2786mV
Get AD_V6 1989mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:25:48:222 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 23:25:48:346 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:25:48:350 ==>> 检测【检测BootVer】
2025-07-31 23:25:48:354 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:48:753 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = ***************
[D][05:18:21][FCTY]HardwareID  = 867222087631745
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:21][FCTY]Bat         = 3944 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1291
[D][05:18:21][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3793 mv
[D][05:18:

2025-07-31 23:25:48:843 ==>> 21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
$GBGGA,152552.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,39,3,,,39,1*4C

$GBGSV,7,2,25,34,,,39,59,,,39,43,,,39,7,,,39,1*4F

$GBGSV,7,3,25,11,,,39,60,,,38,23,,,38,16,,,38,1*74

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,35,1*73

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,31,5,,,31,1*73

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152552.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152552.512,0.000,1499.150,1499.150,47.977,2097152,2097152,2097152*59



2025-07-31 23:25:48:880 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:25:48:886 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:25:48:892 ==>> 检测【检测固件版本】
2025-07-31 23:25:48:903 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:25:48:907 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:25:48:911 ==>> 检测【检测蓝牙版本】
2025-07-31 23:25:48:922 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:25:48:927 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:25:48:931 ==>> 检测【检测MoBikeId】
2025-07-31 23:25:48:941 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:25:48:946 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:25:48:953 ==>> 检测【检测蓝牙地址】
2025-07-31 23:25:48:966 ==>> 取到目标值:C73F6BD61133
2025-07-31 23:25:48:970 ==>> 【检测蓝牙地址】通过,【C73F6BD61133】符合目标值【】要求!
2025-07-31 23:25:48:974 ==>> 提取到蓝牙地址:C73F6BD61133
2025-07-31 23:25:48:977 ==>> 检测【BOARD_ID】
2025-07-31 23:25:48:986 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:25:48:989 ==>> 检测【检测充电电压】
2025-07-31 23:25:49:004 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:25:49:008 ==>> 检测【检测VBUS电压1】
2025-07-31 23:25:49:023 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:25:49:027 ==>> 检测【检测充电电流】
2025-07-31 23:25:49:041 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:25:49:057 ==>> 检测【检测IMEI】
2025-07-31 23:25:49:061 ==>> 取到目标值:867222087631745
2025-07-31 23:25:49:065 ==>> 【检测IMEI】通过,【867222087631745】符合目标值【】要求!
2025-07-31 23:25:49:087 ==>> 提取到IMEI:867222087631745
2025-07-31 23:25:49:091 ==>> 检测【检测IMSI】
2025-07-31 23:25:49:095 ==>> 取到目标值:***************
2025-07-31 23:25:49:106 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 23:25:49:109 ==>> 提取到IMSI:***************
2025-07-31 23:25:49:113 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:25:49:120 ==>> 取到目标值:***************
2025-07-31 23:25:49:149 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 23:25:49:154 ==>> 检测【打开CAN通信】
2025-07-31 23:25:49:177 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:25:49:223 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:25:49:377 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:25:49:384 ==>> 检测【检测CAN通信】
2025-07-31 23:25:49:406 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:25:49:418 ==>> can send success


2025-07-31 23:25:49:448 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:25:49:508 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33690
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:25:49:568 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:25:49:667 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:25:49:671 ==>> 检测【关闭CAN通信】
2025-07-31 23:25:49:677 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:25:49:680 ==>> $GBGGA,152553.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,25,,,39,1*76

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,7,,,39,1*7B

$GBGSV,7,3,25,11,,,39,60,,,39,23,,,38,16,,,38,1*75

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,36,1*70

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,31,4,,,31,5,,,31,1*70

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152553.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152553.512,0.000,1502.472,1502.472,48.089,2097152,2097152,2097152*5F

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:25:49:733 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:25:49:972 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:25:49:976 ==>> 检测【打印IMU STATE】
2025-07-31 23:25:49:984 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:25:50:128 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:25:50:233 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 23:25:50:270 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:25:50:277 ==>> 检测【六轴自检】
2025-07-31 23:25:50:296 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:25:50:429 ==>> [D][05:18:23][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:25:50:886 ==>> $GBGGA,152554.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,39,25,,,39,1*78

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,11,,,39,1*4C

$GBGSV,7,3,25,60,,,39,23,,,39,7,,,38,16,,,38,1*42

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,36,1*70

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,2,,,33,1*7E

$GBGSV,7,6,25,12,,,33,33,,,32,4,,,32,5,,,31,1*70

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152554.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152554.512,0.000,1504.122,1504.122,48.134,2097152,2097152,2097152*5F



2025-07-31 23:25:51:681 ==>> $GBGGA,152555.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,34,,,39,1*76

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,11,,,39,1*4C

$GBGSV,7,3,25,60,,,39,23,,,38,7,,,38,16,,,38,1*43

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,36,1*70

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,12,,,33,1*4F

$GBGSV,7,6,25,2,,,32,33,,,32,4,,,32,5,,,31,1*40

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152555.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152555.512,0.000,1502.467,1502.467,48.084,2097152,2097152,2097152*54



2025-07-31 23:25:52:131 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:25:52:282 ==>> [D][05:18:25][COMM]read battery soc:255
[D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36453 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-11,-13,4038]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 23:25:52:346 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:25:52:350 ==>> 检测【打印IMU STATE2】
2025-07-31 23:25:52:357 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:25:52:526 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:25:52:621 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:25:52:632 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:25:52:651 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:25:52:656 ==>>                            ,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,40,34,,,39,1*76

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,11,,,39,1*4C

$GBGSV,7,3,25,60,,,39,23,,,39,7,,,39,16,,,38,1*43

$GBGSV,7,4,25,41,,,37,10,,,37,6,,,36,1,,,35,1*72

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,12,,,33,1*4F

$GBGSV,7,6

2025-07-31 23:25:52:676 ==>> ,25,2,,,32,33,,,31,4,,,31,5,,,31,1*40

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152556.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152556.512,0.000,1502.476,1502.476,48.093,2097152,2097152,2097152*51

5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:25:52:781 ==>> [

2025-07-31 23:25:52:841 ==>> D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:25:52:905 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:25:52:913 ==>> 检测【检测VBUS电压2】
2025-07-31 23:25:52:933 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:53:293 ==>> [D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087631745
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 9500 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 6, adc = 275
[D][05:18:26][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERS

2025-07-31 23:25:53:338 ==>> ION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3793 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:25:53:432 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:53:855 ==>> $GBGGA,152557.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,39,34,,,39,1*78

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,11,,,39,1*4C

$GBGSV,7,3,25,60,,,39,23,,,39,7,,,39,16,,,38,1*43

$GBGSV,7,4,25,41,,,37,10,,,36,6,,,36,1,,,35,1*73

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,12,,,33,1*4F

$GBGSV,7,6,25,2,,,33,33,,,32,4,,,31,5,,,31,1*42

$GBGSV,7,7,25,44,,,29,1*7A

$GBRMC,152557.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152557.512,0.000,1500.815,1500.815,48.037,2097152,2097152,2097152*5E

[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087631745
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 95

2025-07-31 23:25:53:930 ==>> 00 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 163
[D][05:18:26][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3793 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:25:53:964 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:54:292 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = ***************
[D][05:18:27][FCTY]HardwareID  = 867222087631745
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:27][FCTY]Bat         = 3844 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 9500 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 135
[D][05:18:27][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3793 mv
[D]

2025-07-31 23:25:54:322 ==>> [05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:25:54:492 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:54:503 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33688. cur_tick:38690. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38691


2025-07-31 23:25:55:189 ==>> $GBGGA,152558.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,25,,,39,34,,,39,1*78

$GBGSV,7,2,25,3,,,39,59,,,39,43,,,39,11,,,39,1*4C

$GBGSV,7,3,25,60,,,39,7,,,39,23,,,38,16,,,38,1*42

$GBGSV,7,4,25,41,,,37,10,,,37,6,,,36,1,,,35,1*72

$GBGSV,7,5,25,32,,,35,9,,,34,24,,,33,12,,,33,1*4F

$GBGSV,7,6,25,2,,,32,33,,,32,4,,,31,5,,,31,1*43

$GBGSV,7,7,25,44,,,29,1*7A

$GBRMC,152558.512,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152558.512,0.000,1499.158,1499.158,47.985,2097152,2097152,2097152*5E

[D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0

2025-07-31 23:25:55:295 ==>> 
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107


2025-07-31 23:25:55:399 ==>> [D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][CAT1]gsm read msg sub id: 24
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de8] format[0]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5

2025-07-31 23:25:55:504 ==>> 340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = ***************
[D][05:18:27][FCTY]HardwareID  = 867222087631745
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:27][FCTY]Bat         = 3844 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 119
[D][05:18:27][FCTY]Acckey1 vol = 5566 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3793 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==

2025-07-31 23:25:55:538 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:25:55:609 ==>> ========Modules-nRF5340 ==========
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 24, ret: 6
[D][05:18:28][CAT1]sub id: 24, ret: 6

[D][05:18:28][CAT1]gsm read msg sub id: 15
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B53864A82B8C97B994825E4528F1A47ED57095B41A78F35FBCA547B7B64F6515965EDB1FE50DEAFC15E986941FF9610FFDE4EC7C7AA48B9293595DE389095826FD6C59F9AA3E6AA1DF791562251EC193DC4B1E
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]


2025-07-31 23:25:55:654 ==>>                                                                                                                                            

2025-07-31 23:25:55:909 ==>>                                                                                                                  44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = ***************
[D][05:18:28][FCTY]HardwareID  = 867222087631745
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C73F6BD61133
[D][05:18:28][FCTY]Bat         = 3844 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 100
[D][05:18:28][FCTY]Acckey1 vol = 5565 mv, Acckey2 vol = 101 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:

2025-07-31 23:25:55:940 ==>> 18:28][FCTY]Bat1         = 3793 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:25:56:063 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:25:56:069 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:25:56:076 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:25:56:123 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:25:56:228 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18:29][COMM]re

2025-07-31 23:25:56:258 ==>> ad battery soc:255


2025-07-31 23:25:56:355 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:25:56:360 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:25:56:364 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:25:56:424 ==>> 5A A5 04 5A A5 


2025-07-31 23:25:56:529 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:25:56:638 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:25:56:645 ==>> 检测【打开WIFI(3)】
2025-07-31 23:25:56:651 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:25:56:844 ==>> [D][05:18:29][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:25:56:907 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:25:56:915 ==>> 检测【扩展芯片hw】
2025-07-31 23:25:56:924 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:25:57:115 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:25:57:180 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:25:57:185 ==>> 检测【扩展芯片boot】
2025-07-31 23:25:57:199 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:25:57:204 ==>> 检测【扩展芯片sw】
2025-07-31 23:25:57:218 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:25:57:224 ==>> 检测【检测音频FLASH】
2025-07-31 23:25:57:251 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:25:57:387 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:25:57:553 ==>> +WIFISCAN:4,0,CC057790A5C0,-71
+WIFISCAN:4,1,CC057790A4A1,-80
+WIFISCAN:4,2,CC057790A820,-82
+WIFISCAN:4,3,F86FB0660A82,-85

[D][05:18:30][CAT1]wifi scan report total[4]


2025-07-31 23:25:57:643 ==>> [D][05:18:30][GNSS]recv submsg id[3]


2025-07-31 23:25:57:808 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:25:58:521 ==>>                                                              
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. succes

2025-07-31 23:25:58:626 ==>> s, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, 

2025-07-31 23:25:58:731 ==>> trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]index:1
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub

2025-07-31 23:25:58:836 ==>> _vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:31][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:3

2025-07-31 23:25:58:941 ==>> 1][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].

2025-07-31 23:25:59:032 ==>> hexsend, index:4, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:31][COMM]read battery soc:255


2025-07-31 23:26:00:262 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052dd0] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]Send Data To Server[198][198] ... ->:
00

2025-07-31 23:26:00:337 ==>> 63B98D113311331133113311331B88B3BF1FDC0E1BDCC11296F2064E48F40F41CA6F391050642606EAB4C76A0E6B533521EDB167E2265324B39A426DB1B2DC13B6F63E3733AD841931D08E46B56C2151888E3D7C6051558AA2AE1B1963D34D473B12
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 23:26:00:367 ==>>                                          

2025-07-31 23:26:00:911 ==>> [D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:26:01:443 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:26:01:533 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 23:26:02:019 ==>> [D][05:18:35][COMM]46103 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:35][COMM]accel parse set 0
[D][05:18:35][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:26:02:268 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:26:02:275 ==>> 检测【打开喇叭声音】
2025-07-31 23:26:02:281 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:26:02:294 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 23:26:02:903 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:35][COMM]read file, len:15228, num:4
[D][05:18:35][COMM]--->crc16:0x419c
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_aud

2025-07-31 23:26:03:008 ==>> io_start].l:[691].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, l

2025-07-31 23:26:03:057 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:26:03:064 ==>> 检测【打开大灯控制】
2025-07-31 23:26:03:076 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:26:03:113 ==>> en:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
                                      

2025-07-31 23:26:03:188 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:26:03:327 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:26:03:333 ==>> 检测【关闭仪表供电3】
2025-07-31 23:26:03:355 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:26:03:512 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 23:26:03:606 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:26:03:611 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:26:03:617 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:26:03:788 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:26:03:886 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:26:03:891 ==>> 检测【读大灯电压】
2025-07-31 23:26:03:896 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:26:04:109 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[32923]


2025-07-31 23:26:04:157 ==>> 【读大灯电压】通过,【32923mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:26:04:163 ==>> 检测【关闭大灯控制2】
2025-07-31 23:26:04:172 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:26:04:307 ==>> [D][05:18:37][COMM]read battery soc:255
[W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:26:04:430 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:26:04:436 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:26:04:444 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:26:04:616 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[46]


2025-07-31 23:26:04:709 ==>> 【关大灯控制后读大灯电压】通过,【46mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:26:04:715 ==>> 检测【打开WIFI(4)】
2025-07-31 23:26:04:741 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:26:04:948 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:26:05:042 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:26:05:048 ==>> 检测【EC800M模组版本】
2025-07-31 23:26:05:056 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:26:05:614 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][PROT]CLEAN,SEND:1
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MT

2025-07-31 23:26:05:718 ==>> E_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3A56BF1C067F3827F9531D689AD1B48B3806720ABEDFE95D751F170EBACC23EB0CAC3AE9BFF024B1956E8B8FBCDF9E12BBB15E593E2C6B1BF9F52C1FE61B608B8011F8B645FEB42911EADCCBAF97ACD5718E0
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]


2025-07-31 23:26:05:827 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:26:05:834 ==>> 检测【配置蓝牙地址】
2025-07-31 23:26:05:857 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:26:05:993 ==>> [W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:26:06:038 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C73F6BD61133>】
2025-07-31 23:26:06:237 ==>> recv ble 1
recv ble 2
ble set mac ok :c7,3f,6b,d6,11,33
enable filters ret : 0

2025-07-31 23:26:06:281 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 23:26:06:315 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:26:06:320 ==>> 检测【BLETEST】
2025-07-31 23:26:06:330 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:26:06:387 ==>> [D][05:18:39][COMM]50582 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:26:06:417 ==>> 4A A4 01 A4 4A 


2025-07-31 23:26:06:522 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C73F6BD61133*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C73F6BD6113399999OVER 150


2025-07-31 23:26:07:344 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:26:07:350 ==>> 该项需要延时执行
2025-07-31 23:26:07:419 ==>> [D][05:18:40][COMM]51593 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:26:08:052 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:26:08:310 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 23:26:08:400 ==>> [D][05:18:41][COMM]52605 imu init OK


2025-07-31 23:26:09:945 ==>> +WIFISCAN:4,0,F62A7D2297A3,-65
+WIFISCAN:4,1,CC057790A5C0,-77
+WIFISCAN:4,2,F86FB0660A82,-82
+WIFISCAN:4,3,CC057790A821,-85

[D][05:18:43][CAT1]wifi scan report total[4]


2025-07-31 23:26:10:307 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 23:26:10:895 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ee8] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B346877884077687C7482B2BF4AFE12700127A0B52FED1101F9829CB2FCD34789CF0D6D0B2527CE7F0B51EB57E493CAF6AD98E8F7961B840C85329F

2025-07-31 23:26:10:970 ==>> AC07AEB2C9756B2FF79B6BD7C1356932578017D1B475F42
[D][05:18:43][GNSS]recv submsg id[3]
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 23:26:12:306 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 23:26:14:325 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 23:26:16:131 ==>> [D][05:18:49][PROT]CLEAN,SEND:1
[D][05:18:49][PROT]CLEAN:1
[D][05:18:49][PROT]index:0 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:4
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:2
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900005]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052dd0] format[0]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][CAT1]Send Data To Server[198][198] ... ->:
0063B982113311331133113311331B88B59851FDEB04C77774FC546FE3B6DD1D04607

2025-07-31 23:26:16:206 ==>> 27F9CDC3FF67230161A9C34978134FA54DF53B7422314CA2F0DA76D7D2937C8A84F4A9685C6BB73205E039248EB07FB70B81C2B03F336EF799A0F5079A213E7A0
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 23:26:16:311 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 23:26:17:354 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:26:17:361 ==>> 检测【检测WiFi结果】
2025-07-31 23:26:17:388 ==>> WiFi信号:【CC057790A621】,信号值:-50
2025-07-31 23:26:17:397 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 23:26:17:422 ==>> WiFi信号:【F86FB0660A82】,信号值:-83
2025-07-31 23:26:17:431 ==>> WiFi信号:【646E97BD0450】,信号值:-83
2025-07-31 23:26:17:453 ==>> WiFi信号:【CC057790A5C0】,信号值:-71
2025-07-31 23:26:17:470 ==>> WiFi信号:【CC057790A4A1】,信号值:-80
2025-07-31 23:26:17:480 ==>> WiFi信号:【CC057790A820】,信号值:-82
2025-07-31 23:26:17:499 ==>> WiFi信号:【F62A7D2297A3】,信号值:-65
2025-07-31 23:26:17:517 ==>> WiFi信号:【CC057790A821】,信号值:-85
2025-07-31 23:26:17:532 ==>> WiFi数量【9】, 最大信号值:-50
2025-07-31 23:26:17:544 ==>> 检测【检测GPS结果】
2025-07-31 23:26:17:552 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:26:17:561 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all continue location stop
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all sing location stop


2025-07-31 23:26:18:331 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 23:26:18:376 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:26:18:385 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:18:409 ==>> 定位已等待【1】秒.
2025-07-31 23:26:18:725 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:51][COMM]Open GPS Module...
[D][05:18:51][COMM]LOC_MODEL_CONT
[D][05:18:51][GNSS]start event:8
[D][05:18:51][GNSS]GPS start. ret=0
[W][05:18:51][GNSS]start cont locating
[D][05:18:51][CAT1]gsm read msg sub id: 23
[D][05:18:51][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:51][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:26:19:391 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:19:401 ==>> 定位已等待【2】秒.
2025-07-31 23:26:19:436 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:26:20:313 ==>> [D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,39,,,40,60,,,39,40,,,39,23,,,38,1*74

$GBGSV,2,2,07,25,,,37,34,,,36,11,,,37,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1582.246,1582.246,50.539,2097152,2097152,2097152*45

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:53][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:53][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 23, ret: 6
[D][05:18:53][CAT1]sub id: 23, ret: 6



2025-07-31 23:26:20:343 ==>>                                          

2025-07-31 23:26:20:403 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:20:412 ==>> 定位已等待【3】秒.
2025-07-31 23:26:20:749 ==>> [D][05:18:53][GNSS]recv submsg id[1]
[D][05:18:53][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:26:21:384 ==>> [D][05:18:54][PROT]CLEAN,SEND:0
[D][05:18:54][PROT]index:0 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:4
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:1
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900005]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052dd0] format[0]
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113

2025-07-31 23:26:21:414 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:21:426 ==>> 定位已等待【4】秒.
2025-07-31 23:26:21:489 ==>> 311331B88B5403F3EDB9AD351276A45987C763321C7641ED48F64770E4477A73ACF2714A63FF4C4BBCDFE5F5B68FDC95AF0B5EC2ED05AD25028742603242F94A4E6E884C52B76A1A7F7B1A2E6312F2A6C62A18E764F928A
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,3,,,41,39,,,40,40,,,40,60,,,39,1*42

$GBGSV,4,2,14,59,,,39,43,,,39,23,,,38,25,,,38,1*78

$GBGSV,4,3,14,11,,,37,34,,,37,7,,,37,10,,,36,1*44

$GBGSV,4,4,14,6,,,35,4,,,30,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1557.635,1557.635,49.817,2097152,2097152,2097152*4C

[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]


2025-07-31 23:26:22:260 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,41,3,,,40,39,,,40,60,,,39,1*4F

$GBGSV,5,2,18,59,,,39,43,,,39,25,,,39,23,,,38,1*74

$GBGSV,5,3,18,11,,,38,34,,,38,7,,,38,41,,,37,1*43

$GBGSV,5,4,18,10,,,36,6,,,36,1,,,33,2,,,33,1*4A

$GBGSV,5,5,18,4,,,29,5,,,27,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1520.182,1520.182,48.675,2097152,2097152,2097152*47



2025-07-31 23:26:22:350 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 23:26:22:425 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:22:436 ==>> 定位已等待【5】秒.
2025-07-31 23:26:23:286 ==>> $GBGGA,152627.130,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,39,,,40,3,,,39,60,,,39,1*48

$GBGSV,6,2,21,59,,,39,43,,,39,25,,,39,34,,,39,1*7A

$GBGSV,6,3,21,23,,,38,11,,,38,7,,,38,41,,,37,1*4C

$GBGSV,6,4,21,16,,,37,10,,,36,6,,,36,1,,,34,1*75

$GBGSV,6,5,21,32,,,34,2,,,33,4,,,30,5,,,28,1*4A

$GBGSV,6,6,21,9,,,38,1*47

$GBRMC,152627.130,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152627.130,0.000,1521.542,1521.542,48.697,2097152,2097152,2097152*52



2025-07-31 23:26:23:435 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:23:445 ==>> 定位已等待【6】秒.
2025-07-31 23:26:23:705 ==>> $GBGGA,152627.530,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,39,,,40,3,,,39,60,,,39,1*48

$GBGSV,6,2,21,59,,,39,43,,,39,25,,,39,34,,,39,1*7A

$GBGSV,6,3,21,23,,,38,11,,,38,7,,,38,41,,,37,1*4C

$GBGSV,6,4,21,16,,,37,10,,,36,6,,,36,1,,,35,1*74

$GBGSV,6,5,21,32,,,34,2,,,33,4,,,30,5,,,28,1*4A

$GBGSV,6,6,21,9,,,36,1*49

$GBRMC,152627.530,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152627.530,0.000,1523.613,1523.613,48.761,2097152,2097152,2097152*5E



2025-07-31 23:26:24:345 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 23:26:24:450 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:24:460 ==>> 定位已等待【7】秒.
2025-07-31 23:26:24:664 ==>> $GBGGA,152628.510,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,39,,,40,3,,,39,60,,,39,1*48

$GBGSV,6,2,21,59,,,39,43,,,39,25,,,39,34,,,39,1*7A

$GBGSV,6,3,21,23,,,39,11,,,39,7,,,38,16,,,38,1*41

$GBGSV,6,4,21,41,,,37,10,,,36,6,,,36,1,,,35,1*76

$GBGSV,6,5,21,32,,,34,2,,,32,4,,,30,5,,,29,1*4A

$GBGSV,6,6,21,44,,,26,1*71

$GBRMC,152628.510,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152628.510,0.000,1508.348,1508.348,48.307,2097152,2097152,2097152*57



2025-07-31 23:26:25:452 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:25:462 ==>> 定位已等待【8】秒.
2025-07-31 23:26:25:669 ==>> $GBGGA,152629.510,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,3,,,39,60,,,39,1*4A

$GBGSV,6,2,23,59,,,39,43,,,39,25,,,39,34,,,39,1*78

$GBGSV,6,3,23,23,,,39,11,,,39,7,,,38,16,,,37,1*4C

$GBGSV,6,4,23,41,,,37,10,,,36,6,,,36,1,,,35,1*74

$GBGSV,6,5,23,32,,,34,2,,,33,24,,,33,33,,,31,1*44

$GBGSV,6,6,23,4,,,31,5,,,30,44,,,29,1*7C

$GBRMC,152629.510,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152629.510,0.000,1501.544,1501.544,48.067,2097152,2097152,2097152*53



2025-07-31 23:26:26:467 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:26:478 ==>> 定位已等待【9】秒.
2025-07-31 23:26:26:572 ==>> [D][05:18:59][PROT]CLEAN,SEND:0
[D][05:18:59][PROT]CLEAN:0
[D][05:18:59][PROT]index:2 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:6
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:3
[D][05:18:59][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:18:59][PROT]sending traceid [9999999999900007]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[134]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:59][CAT1]gsm read msg sub id: 15
[D][05:18:59][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:59][COMM]read battery soc:255
[D]

2025-07-31 23:26:26:677 ==>> [05:18:59][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE6C7305FE90ACD529AA77FD9A8F3D572F7DCB77918459FD1FD3E9AEE1EE535100761E412BFFC0BF80A46565ECF7947475E7A5
[D][05:18:59][CAT1]<<< 
SEND OK

[D][05:18:59][CAT1]exec over: func id: 15, ret: 11
[D][05:18:59][CAT1]sub id: 15, ret: 11

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:59][M2M ]g_m2m_is_idle become true
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:59][PROT]M2M Send ok [1629955139]
                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 23:26:26:707 ==>>                                                                                                                                 

2025-07-31 23:26:27:468 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:27:478 ==>> 定位已等待【10】秒.
2025-07-31 23:26:27:698 ==>> $GBGGA,152631.510,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,25,,,40,3,,,39,1*42

$GBGSV,6,2,24,60,,,39,59,,,39,43,,,39,34,,,39,1*7E

$GBGSV,6,3,24,23,,,39,11,,,39,7,,,39,16,,,38,1*45

$GBGSV,6,4,24,41,,,37,10,,,37,6,,,36,1,,,35,1*72

$GBGSV,6,5,24,32,,,34,2,,,33,24,,,33,9,,,33,1*78

$GBGSV,6,6,24,33,,,32,4,,,31,5,,,31,44,,,30,1*73

$GBRMC,152631.510,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152631.510,0.000,752.638,752.638,688.307,2097152,2097152,2097152*65



2025-07-31 23:26:28:361 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 23:26:28:481 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:28:492 ==>> 定位已等待【11】秒.
2025-07-31 23:26:28:695 ==>> $GBGGA,152632.510,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,34,,,40,25,,,40,1*7B

$GBGSV,7,2,26,7,,,39,60,,,39,3,,,39,59,,,39,1*79

$GBGSV,7,3,26,11,,,39,43,,,39,23,,,39,16,,,38,1*76

$GBGSV,7,4,26,41,,,37,10,,,36,6,,,36,1,,,35,1*70

$GBGSV,7,5,26,32,,,34,2,,,33,24,,,33,9,,,33,1*7B

$GBGSV,7,6,26,33,,,32,5,,,31,4,,,31,44,,,30,1*70

$GBGSV,7,7,26,14,,,,13,,,,1*75

$GBRMC,152632.510,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152632.510,0.000,752.640,752.640,688.309,2097152,2097152,2097152*68



2025-07-31 23:26:29:493 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:29:503 ==>> 定位已等待【12】秒.
2025-07-31 23:26:29:692 ==>> $GBGGA,152633.510,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,7,,,39,60,,,39,1*4A

$GBGSV,7,2,26,3,,,39,59,,,39,34,,,39,25,,,39,1*48

$GBGSV,7,3,26,11,,,39,43,,,39,23,,,39,16,,,37,1*79

$GBGSV,7,4,26,41,,,37,10,,,36,6,,,36,1,,,35,1*70

$GBGSV,7,5,26,32,,,34,24,,,33,9,,,33,2,,,32,1*7A

$GBGSV,7,6,26,33,,,32,5,,,31,4,,,31,44,,,30,1*70

$GBGSV,7,7,26,13,,,,14,,,,1*75

$GBRMC,152633.510,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152633.510,0.000,749.190,749.190,685.154,2097152,2097152,2097152*6E



2025-07-31 23:26:30:373 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 23:26:30:508 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:30:520 ==>> 定位已等待【13】秒.
2025-07-31 23:26:30:688 ==>> $GBGGA,152634.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,60,,,39,3,,,39,1*4E

$GBGSV,7,2,26,59,,,39,34,,,39,25,,,39,11,,,39,1*7B

$GBGSV,7,3,26,43,,,39,23,,,39,7,,,38,16,,,37,1*4F

$GBGSV,7,4,26,41,,,37,10,,,36,6,,,36,1,,,35,1*70

$GBGSV,7,5,26,32,,,34,24,,,33,9,,,33,2,,,32,1*7A

$GBGSV,7,6,26,33,,,32,5,,,31,4,,,31,44,,,30,1*70

$GBGSV,7,7,26,13,,,,14,,,,1*75

$GBRMC,152634.510,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152634.510,0.000,748.326,748.326,684.364,2097152,2097152,2097152*69



2025-07-31 23:26:31:512 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:31:524 ==>> 定位已等待【14】秒.
2025-07-31 23:26:31:783 ==>> [D][05:19:04][PROT]CLEAN,SEND:2
[D][05:19:04][PROT]index:2 1629955144
[D][05:19:04][PROT]is_send:0
[D][05:19:04][PROT]sequence_num:6
[D][05:19:04][PROT]retry_timeout:0
[D][05:19:04][PROT]retry_times:2
[D][05:19:04][PROT]send_path:0x2
[D][05:19:04][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:04][PROT]===========================================================
[W][05:19:04][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955144]
[D][05:19:04][PROT]===========================================================
[D][05:19:04][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:19:04][PROT]sending traceid [9999999999900007]
[D][05:19:04][PROT]Send_TO_M2M [1629955144]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:04][SAL ]sock send credit cnt[6]
[D][05:19:04][SAL ]sock send ind credit cnt[6]
[D][05:19:04][M2M ]m2m send data len[134]
[D][05:19:04][SAL ]Cellular task submsg id[10]
[D][05:19:04][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:04][CAT1]gsm read msg sub id: 15
[D][05:19:04][CAT1]t

2025-07-31 23:26:31:858 ==>> x ret[17] >>> AT+QISEND=0,134

[D][05:19:04][CAT1]<<< 
ERROR

$GBGGA,152635.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,39,,,40,60,,,39,3,,,39,1*4E

$GBGSV,7,2,26,59,,,39,34,,,39,25,,,39,11,,,39,1*7B

$GBGSV,7,3,26,43,,,39,23,,,39,7,,,38,16,,,38,1*40

$GBGSV,7,4,26,41,,,37,10,,,36,6,,,36,1,,,34,1*71

$GBGSV,7,5,26,32,,,34,24,,,33,9,,,33,2,,,32,1*7A

$GBGSV,7,6,26,33,,,32,4,,,31,5,,,30,44,,,30,1*71

$GBGSV,7,7,26,13,,,,14,,,,1*75

$GBRMC,152635.510,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152635.510,0.000,747.469,747.470,683.581,2097152,2097152,2097152*6A



2025-07-31 23:26:32:360 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 23:26:32:525 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:26:32:536 ==>> 定位已等待【15】秒.
2025-07-31 23:26:33:347 ==>> $GBGGA,152632.517,2301.2561539,N,11421.9421299,E,1,11,1.13,83.739,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.13,2.08,4*0B

$GBGSV,7,1,25,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,7,2,25,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,7,3,25,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,7,4,25,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,7,5,25,23,33,316,39,4,32,111,31,41,31,240,37,24,22,279,34,1*42

$GBGSV,7,6,25,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,32,1*7B

$GBGSV,7,7,25,13,4,207,,1*72

$GBRMC,152632.517,A,2301.2561539,N,11421.9421299,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:19:05][GNSS]HD8040 GPS
[D][05:19:05][GNSS]GPS diff_sec 124020447, report 0x42 frame
$GBGST,152632.517,1.013,0.343,0.335,0.496,2.008,2.123,7.320*70

[D][05:19:05][COMM]Main Task receive event:131
[D][05:19:05][COMM]index:0,power_mode:0xFF
[D][05:19:05][COMM]index:1,sound_mode:0xFF
[D][05:19:05][COMM]index:2,gsensor_mode:0xFF
[D][05:19:05][COMM]index:3,report_freq_mode:0xFF
[D][05:19:05][COMM]index:4,report_period:0xFF
[D][05:19:05][COMM]index:5,normal_r

2025-07-31 23:26:33:452 ==>> eset_mode:0xFF
[D][05:19:05][COMM]index:6,normal_reset_period:0xFF
[D][05:19:05][COMM]index:7,spock_over_speed:0xFF
[D][05:19:05][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:05][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:05][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:05][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:05][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:05][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:05][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:05][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:05][COMM]index:16,imu_config_params:0xFF
[D][05:19:05][COMM]index:17,long_connect_params:0xFF
[D][05:19:05][COMM]index:18,detain_mark:0xFF
[D][05:19:05][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:05][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:05][COMM]index:21,mc_mode:0xFF
[D][05:19:05][COMM]index:22,S_mode:0xFF
[D][05:19:05][COMM]index:23,overweight:0xFF
[D][05:19:05][COMM]index:24,standstill_mode:0xFF
[D][05:19:05][COMM]index:25,night_mode:0xFF
[D][05:19:05][COMM]index:26,experiment1:0xFF
[D][05:19:05][COMM]index:27,experiment2:0xFF
[D][05:19:05][COMM]index:28,experiment3:0xFF
[D][05:19:05][COMM]index:29,e

2025-07-31 23:26:33:527 ==>> 符合定位需求的卫星数量:【16】
2025-07-31 23:26:33:534 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【38】
北斗星号:【39】,信号值:【40】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【38】
北斗星号:【3】,信号值:【39】
北斗星号:【11】,信号值:【39】
北斗星号:【59】,信号值:【39】
北斗星号:【10】,信号值:【36】
北斗星号:【25】,信号值:【39】
北斗星号:【43】,信号值:【39】
北斗星号:【1】,信号值:【35】
北斗星号:【34】,信号值:【39】
北斗星号:【60】,信号值:【39】
北斗星号:【23】,信号值:【39】
北斗星号:【41】,信号值:【37】

2025-07-31 23:26:33:543 ==>> 检测【CSQ强度】
2025-07-31 23:26:33:550 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:26:33:574 ==>> xperiment4:0xFF
[D][05:19:05][COMM]index:30,night_mode_start:0xFF
[D][05:19:05][COMM]index:31,night_mode_end:0xFF
[D][05:19:05][COMM]index:33,park_report_minutes:0xFF
[D][05:19:05][COMM]index:34,park_report_mode:0xFF
[D][05:19:05][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:05][COMM]index:38,charge_battery_para: FF
[D][05:19:05][COMM]index:39,multirider_mode:0xFF
[D][05:19:05][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:05][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:05][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:05][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:05][COMM]index:44,riding_duration_config:0xFF
[D][05:19:05][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:05][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:05][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:05][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:05][COMM]index:49,mc_load_startup:0xFF
[D][05:19:05][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:05][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:05][COMM]index:52,traffic_mode:0xFF
[D][05:19:05][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:05][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:

2025-07-31 23:26:33:662 ==>> 19:05][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:05][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:05][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:05][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:05][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:05][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:05][COMM]index:63,experiment5:0xFF
[D][05:19:05][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:05][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:05][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:05][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:05][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:05][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:05][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:05][COMM]index:72,experiment6:0xFF
[D][05:19:05][COMM]index:73,experiment7:0xFF
[D][05:19:05][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:05][COMM]index:75,zero_value_from_server:-1
[D][05:19:05][COMM]index:76,multirider_threshold:255
[D][05:19:05][COMM]index:77,experiment8:255
[D][05:19:05][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:05][COMM]index:79,temp_park_tail_light_twinkl

2025-07-31 23:26:33:767 ==>> e_duration:255
[D][05:19:05][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:05][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:05][COMM]index:83,loc_report_interval:255
[D][05:19:05][COMM]index:84,multirider_threshold_p2:255
[D][05:19:05][COMM]index:85,multirider_strategy:255
[D][05:19:05][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:05][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:05][COMM]index:90,weight_param:0xFF
[D][05:19:05][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:05][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:05][COMM]index:95,current_limit:0xFF
[D][05:19:05][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:05][COMM]index:100,location_mode:0xFF

[D][05:19:05][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:19:05][PROT]remove success[1629955145],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:05][PROT]add success [1629955145],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:05][COMM]Main Task receive event:131 finished processing
[D][05:19:05][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:05][M2M ]m2

2025-07-31 23:26:33:872 ==>> m_task: gpc:[0],gpo:[1]
$GBGGA,152633.017,2301.2566231,N,11421.9425436,E,1,11,1.14,82.329,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,240,37,24,22,279,34,1*42

$GBGSV,6,6,24,5,21,255,31,32,17,297,34,44,14,172,30,33,6,322,32,1*7C

$GBGSV,2,1,08,40,77,193,40,39,67,52,40,25,51,24,36,43,49,160,36,5*72

$GBGSV,2,2,08,34,46,73,40,23,33,316,39,41,31,240,38,32,17,297,35,5*47

$GBRMC,152633.017,A,2301.2566231,N,11421.9425436,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152633.017,2.193,1.015,0.985,1.473,2.241,2.223,5.521*7D



2025-07-31 23:26:33:947 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:26:34:308 ==>> $GBGGA,152634.000,2301.2569797,N,11421.9426769,E,1,11,1.14,83.587,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,240,37,24,22,279,33,1*45

$GBGSV,6,6,24,5,21,255,30,32,17,297,33,44,14,172,30,33,6,322,32,1*7A

$GBGSV,2,1,08,40,77,193,41,39,67,52,41,25,51,24,38,43,49,160,38,5*72

$GBGSV,2,2,08,34,46,73,40,23,33,316,37,41,31,240,38,32,17,297,35,5*49

$GBRMC,152634.000,A,2301.2569797,N,11421.9426769,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152634.000,1.934,0.209,0.206,0.302,1.857,1.872,4.497*7A



2025-07-31 23:26:34:383 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 23:26:35:304 ==>> $GBGGA,152635.000,2301.2572094,N,11421.9427551,E,1,11,1.14,84.244,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,240,37,24,22,279,33,1*45

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,32,1*7B

$GBGSV,2,1,08,40,77,193,41,39,67,52,41,25,51,24,40,43,49,160,39,5*7C

$GBGSV,2,2,08,34,46,73,41,23,33,316,38,41,31,240,38,32,17,297,35,5*47

$GBRMC,152635.000,A,2301.2572094,N,11421.9427551,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152635.000,1.784,0.223,0.220,0.322,1.633,1.657,3.901*70



2025-07-31 23:26:35:610 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:26:35:989 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:26:36:275 ==>> $GBGGA,152636.000,2301.2573694,N,11421.9428604,E,1,11,1.14,84.275,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,36,34,46,73,39,2,45,236,32,60,41,239,39,1*43

$GBGSV,6,5,24,23,33,316,38,4,32,111,31,41,31,240,37,24,22,279,33,1*44

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,32,1*7B

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,240,38,32,17,297,35,5*46

$GBRMC,152636.000,A,2301.2573694,N,11421.9428604,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152636.000,1.687,0.231,0.227,0.330,1.489,1.517,3.499*7E



2025-07-31 23:26:36:380 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 23:26:36:455 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:26:37:310 ==>> $GBGGA,152637.000,2301.2574843,N,11421.9428991,E,1,11,1.14,84.139,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,36,34,46,73,39,2,45,236,32,60,41,239,39,1*43

$GBGSV,6,5,24,23,33,316,38,4,32,111,31,41,31,240,36,24,22,279,33,1*45

$GBGSV,6,6,24,5,21,255,31,32,17,297,34,44,14,172,30,33,6,322,32,1*7C

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,240,38,32,17,297,36,5*45

$GBRMC,152637.000,A,2301.2574843,N,11421.9428991,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152637.000,1.791,0.242,0.238,0.348,1.512,1.538,3.293*7E



2025-07-31 23:26:37:665 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:26:38:046 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:26:38:288 ==>> $GBGGA,152638.000,2301.2575733,N,11421.9429235,E,1,11,1.14,84.084,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,37,3,60,190,39,11,53,112,39,59,52,129,39,1*7F

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,240,36,24,22,279,33,1*44

$GBGSV,6,6,24,5,21,255,30,32,17,297,33,44,14,172,30,33,6,322,31,1*79

$GBGSV,2,1,08,40,77,193,41,39,67,52,41,25,51,24,41,43,49,160,40,5*73

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,240,38,32,17,297,36,5*45

$GBRMC,152638.000,A,2301.2575733,N,11421.9429235,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152638.000,1.657,0.247,0.243,0.352,1.384,1.409,3.039*70



2025-07-31 23:26:38:393 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 23:26:38:954 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:26:39:304 ==>> $GBGGA,152639.000,2301.2576138,N,11421.9429270,E,1,11,1.14,84.105,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,38,1*71

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,38,4,32,111,31,41,31,239,37,24,22,279,33,1*4A

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,31,1*78

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,40,23,33,316,39,41,31,239,38,32,17,297,36,5*4A

$GBRMC,152639.000,A,2301.2576138,N,11421.9429270,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152639.000,1.633,0.256,0.251,0.367,1.339,1.363,2.871*7E



2025-07-31 23:26:39:736 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:26:40:091 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:26:40:301 ==>> $GBGGA,152640.000,2301.2576374,N,11421.9429410,E,1,11,1.14,84.069,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,37,3,60,190,39,11,53,112,39,59,52,129,38,1*7E

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,32,41,31,239,37,24,22,279,33,1*48

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,31,1*78

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,239,38,32,17,297,36,5*4B

$GBRMC,152640.000,A,2301.2576374,N,11421.9429410,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152640.000,1.651,0.244,0.240,0.351,1.331,1.353,2.759*7C



2025-07-31 23:26:40:406 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 23:26:41:305 ==>> $GBGGA,152641.000,2301.2576726,N,11421.9429485,E,1,11,1.14,84.036,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,38,1*71

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,36,24,22,279,33,1*4A

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,31,1*78

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,239,38,32,17,297,35,5*48

$GBRMC,152641.000,A,2301.2576726,N,11421.9429485,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152641.000,1.642,0.242,0.238,0.346,1.309,1.330,2.659*7F



2025-07-31 23:26:41:456 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:26:41:807 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:26:41:867 ==>> [D][05:19:14][CAT1]exec over: func id: 15, ret: -93
[D][05:19:14][CAT1]sub id: 15, ret: -93

[D][05:19:14][SAL ]Cellular task submsg id[68]
[D][05:19:14][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:14][SAL ]socket send fail. id[4]
[D][05:19:14][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:14][M2M ]m2m select fd[4]
[D][05:19:14][M2M ]socket[4] Link is disconnected
[D][05:19:14][M2M ]tcpclient close[4]
[D][05:19:14][SAL ]socket[4] has closed
[D][05:19:14][PROT]protocol read data ok
[E][05:19:14][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:14][PROT]M2M Send Fail [1629955154]
[D][05:19:14][PROT]CLEAN,SEND:2
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:14][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 21
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:14][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 21
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]S

2025-07-31 23:26:41:927 ==>> END RAW data >>> AT+CSQ

[D][05:19:14][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 21
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:14][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:14][CAT1]exec over: func id: 12, ret: 21
[D][05:19:14][CAT1]gsm read msg sub id: 10
[D][05:19:14][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:14][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:26:42:002 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:26:42:166 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 23:26:42:173 ==>> 检测【关闭GSM联网】
2025-07-31 23:26:42:180 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:26:42:384 ==>> [D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]exec over: func id: 10, ret: 6
[D][05:19:15][CAT1]sub id: 10, ret: 6

[D][05:19:15][SAL ]Cellular task submsg id[68]
[D][05:19:15][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:15][M2M ]m2m gsm shut done, ret[0]
[D][05:19:15][CAT1]gsm read msg sub id: 12
[D][05:19:15][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:15][SAL ]open socket ind id[4], rst[0]
[D][05:19:15][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:15][SAL ]Cellular task submsg id[8]
[D][05:19:15][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:19:15][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
$GBGGA,152642.000,2301.2577077,N,11421.9429724,E,1,11,1.14,83.987,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

[D][05:19:15][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:15][CAT1]exec 

2025-07-31 23:26:42:489 ==>> over: func id: 12, ret: 21
$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,24,22,279,33,1*4B

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,31,1*78

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,40,23,33,316,39,41,31,239,39,32,17,297,36,5*4B

$GBRMC,152642.000,A,2301.2577077,N,11421.9429724,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152642.000,1.660,0.239,0.235,0.342,1.309,1.329,2.586*70

[D][05:19:15][CAT1]gsm read msg sub id: 8
[D][05:19:15][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:15][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:15][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:15][CAT1]pdpdeact urc len[22]
[W][05:19:15][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:15][COMM]GSM test
[D][05:19:15][COMM]GSM test disable
                                         

2025-07-31 23:26:42:707 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:26:42:715 ==>> 检测【4G联网测试】
2025-07-31 23:26:42:740 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:26:42:891 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:26:43:805 ==>> [D][05:19:16][COMM]Main Task receive event:14
[D][05:19:16][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955156, allstateRepSeconds = 0
[D][05:19:16][COMM]index:0,power_mode:0xFF
[D][05:19:16][COMM]index:1,sound_mode:0xFF
[D][05:19:16][COMM]index:2,gsensor_mode:0xFF
[D][05:19:16][COMM]index:3,report_freq_mode:0xFF
[D][05:19:16][COMM]index:4,report_period:0xFF
[D][05:19:16][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:16][COMM]index:6,normal_reset_period:0xFF
[D][05:19:16][COMM]index:7,spock_over_speed:0xFF
[D][05:19:16][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:16][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:16][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:16][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:16][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:16][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:16][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:16][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:16][COMM]index:16,imu_config_params:0xFF
[D][05:19:16][COMM]index:17,long_connect_params:0xFF
[D][05:19:16][COMM]index:18,detain_mark:0xFF
[D][05:19:16][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:16][C

2025-07-31 23:26:43:910 ==>> OMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:16][COMM]index:21,mc_mode:0xFF
[D][05:19:16][COMM]index:22,S_mode:0xFF
[D][05:19:16][COMM]index:23,overweight:0xFF
[D][05:19:16][COMM]index:24,standstill_mode:0xFF
[D][05:19:16][COMM]index:25,night_mode:0xFF
[D][05:19:16][COMM]index:26,experiment1:0xFF
[D][05:19:16][COMM]index:27,experiment2:0xFF
[D][05:19:16][COMM]index:28,experiment3:0xFF
[D][05:19:16][COMM]index:29,experiment4:0xFF
[D][05:19:16][COMM]index:30,night_mode_start:0xFF
[D][05:19:16][COMM]index:31,night_mode_end:0xFF
[D][05:19:16][COMM]index:33,park_report_minutes:0xFF
[D][05:19:16][COMM]index:34,park_report_mode:0xFF
[D][05:19:16][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:16][COMM]index:38,charge_battery_para: FF
[D][05:19:16][COMM]index:39,multirider_mode:0xFF
[D][05:19:16][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:16][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:16][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:16][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:16][COMM]index:44,riding_duration_config:0xFF
[D][05:19:16][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:16][COMM]index:46,camera_park_type_cfg

2025-07-31 23:26:44:015 ==>> :0xFF
[D][05:19:16][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:16][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:16][COMM]index:49,mc_load_startup:0xFF
[D][05:19:16][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:16][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:16][COMM]index:52,traffic_mode:0xFF
[D][05:19:16][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:16][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:16][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:16][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:16][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:16][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:16][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:16][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:16][COMM]index:63,experiment5:0xFF
[D][05:19:16][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:16][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:16][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:16][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:16][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:16][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:16][COMM]index:71,camera_park_self_che

2025-07-31 23:26:44:120 ==>> ck_cfg:0xFF
[D][05:19:16][COMM]index:72,experiment6:0xFF
[D][05:19:16][COMM]index:73,experiment7:0xFF
[D][05:19:16][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:16][COMM]index:75,zero_value_from_server:-1
[D][05:19:16][COMM]index:76,multirider_threshold:255
[D][05:19:16][COMM]index:77,experiment8:255
[D][05:19:16][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:16][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:16][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:16][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:16][COMM]index:83,loc_report_interval:255
[D][05:19:16][COMM]index:84,multirider_threshold_p2:255
[D][05:19:16][COMM]index:85,multirider_strategy:255
[D][05:19:16][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:16][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:16][COMM]index:90,weight_param:0xFF
[D][05:19:16][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:16][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:16][COMM]index:95,current_limit:0xFF
[D][05:19:16][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:16][COMM]index:100,location_mode:0xFF

[W][

2025-07-31 23:26:44:225 ==>> 05:19:16][PROT]remove success[1629955156],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:16][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:16][PROT]add success [1629955156],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,152643.000,2301.2577257,N,11421.9429778,E,1,11,1.14,83.980,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,11,25,43,34,23,41,32,,2.37,1.14,2.08,4*0C

$GBGSV,6,1,24,40,77,193,41,7,70,220,38,39,67,52,40,6,65,105,36,1*4A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,11,53,112,39,59,52,129,39,1*70

$GBGSV,6,3,24,9,52,333,34,10,52,242,36,25,51,24,39,43,49,160,39,1*70

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,24,22,279,33,1*4B

$GBGSV,6,6,24,5,21,255,31,32,17,297,33,44,14,172,30,33,6,322,31,1*78

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,40,23,33,316,39,41,31,239,39,32,17,297,35,5*48

$GBRMC,152643.000,A,2301.2577257,N,11421.9429778,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.

2025-07-31 23:26:44:315 ==>> 00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152643.000,1.686,0.200,0.197,0.286,1.318,1.336,2.533*71

[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:16][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:16][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:16][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:16][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]exec over: func id: 8, ret: 6
[D][05:19:16][CAT1]gsm read msg sub id: 13
[D][05:19:16][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:16][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:16][CAT1]exec over: func id: 13, ret: 21
[D][05:19:16][M2M ]get csq[25]


2025-07-31 23:26:44:420 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:26:44:525 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:26:44:630 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:26:44:690 ==>>                                                                                                                                                                                                                                                                                                                                      

2025-07-31 23:26:45:332 ==>> $GBGGA,152645.000,2301.2577491,N,11421.9430135,E,1,15,0.78,83.933,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,1.34,0.78,1.09,4*03

$GBGSA,A,3,24,32,33,,,,,,,,,,1.34,0.78,1.09,4*0E

$GBGSV,6,1,24,40,77,193,41,7,70,220,39,39,67,52,40,6,65,14,36,1*7A

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,10,57,223,36,11,53,112,39,1*7E

$GBGSV,6,3,24,59,52,129,39,9,52,333,34,25,51,24,39,43,49,160,39,1*7C

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,5,21,255,31,1*77

$GBGSV,6,6,24,24,17,82,34,32,17,297,33,33,14,189,31,44,14,172,30,1*43

$GBGSV,2,1,08,40,77,193,41,39,67,52,42,25,51,24,41,43,49,160,40,5*70

$GBGSV,2,2,08,34,46,73,41,23,33,316,39,41,31,239,39,32,17,297,35,5*49

$GBRMC,152645.000,A,2301.2577491,N,11421.9430135,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152645.000,2.598,0.216,0.220,0.295,1.908,1.921,2.925*7E



2025-07-31 23:26:46:331 ==>> $GBGGA,152646.000,2301.2577708,N,11421.9430156,E,1,15,0.78,83.981,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,1.34,0.78,1.09,4*03

$GBGSA,A,3,24,32,33,,,,,,,,,,1.34,0.78,1.09,4*0E

$GBGSV,6,1,24,40,77,193,41,7,70,220,39,39,67,52,41,6,65,14,36,1*7B

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,10,57,223,36,11,53,112,39,1*7E

$GBGSV,6,3,24,59,52,129,39,9,52,333,34,25,51,25,40,43,49,160,39,1*73

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,5,21,255,31,1*77

$GBGSV,6,6,24,24,17,82,34,32,17,297,33,33,14,189,31,44,14,172,30,1*43

$GBGSV,3,1,10,40,77,193,41,39,67,52,42,25,51,25,41,43,49,160,40,5*79

$GBGSV,3,2,10,34,46,73,41,23,33,316,39,41,31,239,38,24,17,82,34,5*70

$GBGSV,3,3,10,32,17,297,35,33,14,189,32,5*7A

$GBRMC,152646.000,A,2301.2577708,N,11421.9430156,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152646.000,3.483,0.244,0.248,0.330,2.401,2.412,3.320*77



2025-07-31 23:26:46:421 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 23:26:47:312 ==>> $GBGGA,152647.000,2301.2577794,N,11421.9430194,E,1,15,0.78,83.988,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,1.34,0.78,1.09,4*03

$GBGSA,A,3,24,32,33,,,,,,,,,,1.34,0.78,1.09,4*0E

$GBGSV,6,1,24,40,77,193,42,7,70,220,39,39,67,52,40,6,65,14,36,1*79

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,10,57,223,36,11,53,112,39,1*7E

$GBGSV,6,3,24,59,52,129,39,9,52,333,34,25,51,25,39,43,49,160,39,1*7D

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,5,21,255,31,1*77

$GBGSV,6,6,24,24,17,82,34,32,17,297,33,33,14,189,31,44,14,172,30,1*43

$GBGSV,3,1,10,40,77,193,41,39,67,52,42,25,51,25,41,43,49,160,40,5*79

$GBGSV,3,2,10,34,46,73,41,23,33,316,39,41,31,239,38,24,17,82,34,5*70

$GBGSV,3,3,10,32,17,297,35,33,14,189,32,5*7A

$GBRMC,152647.000,A,2301.2577794,N,11421.9430194,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,152647.000,3.510,0.253,0.258,0.343,2.411,2.421,3.298*7D



2025-07-31 23:26:48:322 ==>> $GBGGA,152648.000,2301.2577882,N,11421.9430353,E,1,15,0.78,83.969,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,10,11,25,43,34,23,41,1.34,0.78,1.09,4*03

$GBGSA,A,3,24,32,33,,,,,,,,,,1.34,0.78,1.09,4*0E

$GBGSV,6,1,24,40,77,193,42,7,70,220,39,39,67,52,40,6,65,14,36,1*79

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,10,57,223,36,11,53,112,39,1*7E

$GBGSV,6,3,24,59,52,129,39,9,52,333,34,25,51,25,39,43,49,160,39,1*7D

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,33,60,41,239,39,1*41

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,5,21,255,31,1*77

$GBGSV,6,6,24,24,17,82,34,32,17,297,33,33,14,189,31,44,14,172,30,1*43

$GBGSV,3,1,10,40,77,193,41,39,67,52,42,25,51,25,41,43,49,160,40,5*79

$GBGSV,3,2,10,34,46,73,41,23,33,316,39,41,31,239,38,24,17,82,34,5*70

$GBGSV,3,3,10,32,17,297,36,33,14,189,32,5*79

$GBRMC,152648.000,A,2301.2577882,N,11421.9430353,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152648.000,3.605,0.210,0.214,0.287,2.457,2.466,3.313*70



2025-07-31 23:26:48:427 ==>> [D][05:19:21][COMM]read battery soc:255


2025-07-31 23:26:48:766 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:26:48:886 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:26:49:852 ==>> [D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:22][COMM]index:21,mc_mod

2025-07-31 23:26:49:959 ==>> e:0xFF
[D][05:19:22][COMM]index:22,S_mode:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:22][COMM]in

2025-07-31 23:26:50:063 ==>> dex:48,shlmt_sensor_en:0xFF
[D][05:19:22][COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:72,experiment6:0xFF
[D][05:19:22][COMM]ind

2025-07-31 23:26:50:168 ==>> ex:73,experiment7:0xFF
[D][05:19:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162],send_path[2],type[0000],priority[0],i

2025-07-31 23:26:50:273 ==>> ndex[0],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:22][CAT1]gsm read msg sub id: 13
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][PROT]index:0 1629955162
[D][05:19:22][PROT]is_send:0
[D][05:19:22][PROT]sequence_num:10
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:1
[D][05:19:22][PROT]send_path:0x2
[D][05:19:22][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]sending traceid [999999999990000B]
[D][05:19:22][PROT]Send_TO_M2M [1629955162]
[D][05:19:22][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:22][SAL ]sock send credit cnt[6]
[D][05:19:22][SAL ]sock send ind credit cnt[6]
[D][05:19:22][M2M ]m2m send data len[294]
[D][05:19:22][SAL ]Cellular task submsg id[10]
[D][05:19:22][SAL ]cellular SEND socket id[0] type[1], len[294],

2025-07-31 23:26:50:378 ==>>  data[0x20052e08] format[0]
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:22][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:22][CAT1]exec over: func id: 13, ret: 21
[D][05:19:22][M2M ]get csq[25]
[D][05:19:22][CAT1]gsm read msg sub id: 15
[D][05:19:22][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:22][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88BD321D87ACB69BA80F957784661E1B6FA3FA8821C9664CCBF305DA4CD107FC5C6E9A3DE3EFCEA21FA71F55B5D71E600651D6C792ED3877CFF7AFB039975A53C40A32CCFF39B34E44F68EA42E1A323C2CC69CC8349A38A2973AE11E6357A6CF9DF8AB0D5BF1387AD51CD48D0E46A766734203AB5089EE8922F21D6A52D5957A85F6DC29
[D][05:19:22][CAT1]<<< 
SEND OK

[D][05:19:22][CAT1]exec over: func id: 15, ret: 11
[D][05:19:22][CAT1]sub id: 15, ret: 11

[D][05:19:22][SAL ]Cellular task submsg id[68]
[D][05:19:22][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:22][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:22][M2M ]g_m2m_is_idle become true
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:22][PROT]M2M Send ok [1629955162]
$GBGGA,152649.000,2301.2578028,N,11421.9430396,E,1,15,0.78,83.952,M,-1.770,M,,*56

$GBGSA,A,3

2025-07-31 23:26:50:483 ==>> ,40,07,39,06,16,10,11,25,43,34,23,41,1.34,0.78,1.09,4*03

$GBGSA,A,3,24,32,33,,,,,,,,,,1.34,0.78,1.09,4*0E

$GBGSV,6,1,24,40,77,193,41,7,70,220,39,39,67,52,40,6,65,14,35,1*79

$GBGSV,6,2,24,16,64,19,38,3,60,190,39,10,57,223,36,11,53,112,39,1*7E

$GBGSV,6,3,24,59,52,129,39,9,52,333,34,25,51,25,39,43,49,160,39,1*7D

$GBGSV,6,4,24,1,48,125,35,34,46,73,39,2,45,236,32,60,41,239,39,1*40

$GBGSV,6,5,24,23,33,316,39,4,32,111,31,41,31,239,37,5,21,255,31,1*77

$GBGSV,6,6,24,24,17,82,33,32,17,297,33,33,14,189,31,44,14,172,30,1*44

$GBGSV,3,1,10,40,77,193,41,39,67,52,42,25,51,25,41,43,49,160,40,5*79

$GBGSV,3,2,10,34,46,73,41,23,33,316,39,41,31,239,38,24,17,82,34,5*70

$GBGSV,3,3,10,32,17,297,36,33,14,189,32,5*79

$GBRMC,152649.000,A,2301.2578028,N,11421.9430396,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152649.000                 

2025-07-31 23:26:50:588 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:26:50:805 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:26:50:816 ==>> 检测【关闭GPS】
2025-07-31 23:26:50:847 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:26:51:197 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:24][GNSS]stop locating
[D][05:19:24][GNSS]stop event:8
[D][05:19:24][GNSS]GPS stop. ret=0
[D][05:19:24][GNSS]all continue location stop
[W][05:19:24][GNSS]stop locating
[D][05:19:24][GNSS]all sing location stop
[D][05:19:24][CAT1]gsm read msg sub id: 24
[D][05:19:24][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]exec over: func id: 24, ret: 6
[D][05:19:24][CAT1]sub id: 24, ret: 6



2025-07-31 23:26:51:336 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:26:51:344 ==>> 检测【清空消息队列2】
2025-07-31 23:26:51:356 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:26:51:520 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:24][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:26:51:613 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:26:51:623 ==>> 检测【轮动检测】
2025-07-31 23:26:51:641 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:26:51:732 ==>> 3A A3 01 00 A3 


2025-07-31 23:26:51:822 ==>> OFF_OUT1
OVER 150


2025-07-31 23:26:51:927 ==>> [D][05:19:25][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:26:51:958 ==>> [D][05:19:25][GNSS]recv submsg id[1]
[D][05:19:25][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:25][GNSS]location stop evt done evt


2025-07-31 23:26:52:126 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:26:52:219 ==>> 3A A3 01 01 A3 


2025-07-31 23:26:52:324 ==>> ON_OUT1
OVER 150


2025-07-31 23:26:52:410 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:26:52:423 ==>> 检测【关闭小电池】
2025-07-31 23:26:52:438 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:26:52:460 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 23:26:52:534 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:26:52:687 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:26:52:703 ==>> 检测【进入休眠模式】
2025-07-31 23:26:52:720 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:26:52:973 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:26][COMM]Main Task receive event:28
[D][05:19:26][COMM]main task tmp_sleep_event = 8
[D][05:19:26][COMM]prepare to sleep
[D][05:19:26][CAT1]gsm read msg sub id: 12
[D][05:19:26][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:26:53:760 ==>> [D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 12, ret: 6
[D][05:19:26][M2M ]tcpclient close[4]
[D][05:19:26][SAL ]Cellular task submsg id[12]
[D][05:19:26][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:26][CAT1]gsm read msg sub id: 9
[D][05:19:26][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 9, ret: 6
[D][05:19:26][CAT1]sub id: 9, ret: 6

[D][05:19:26][SAL ]Cellular task submsg id[68]
[D][05:19:26][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:26][SAL ]socket close ind. id[4]
[D][05:19:26][COMM]1x1 tx_id:3,8, tx_len:2
[D]

2025-07-31 23:26:53:788 ==>> [05:19:26][COMM]1x1 frm_can_tp_send ok
[D][05:19:26][CAT1]pdpdeact urc len[22]


2025-07-31 23:26:54:093 ==>> [E][05:19:27][COMM]1x1 rx timeout
[D][05:19:27][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:26:54:607 ==>> [D][05:19:27][COMM]read battery soc:255
[E][05:19:27][COMM]1x1 rx timeout
[E][05:19:27][COMM]1x1 tp timeout
[E][05:19:27][COMM]1x1 error -3.
[W][05:19:27][COMM]CAN STOP!
[D][05:19:27][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:27][COMM]------------ready to Power off Acckey 1------------
[D][05:19:27][COMM]------------ready to Power off Acckey 2------------
[D][05:19:27][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:27][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1290
[D][05:19:27][COMM]bat sleep fail, reason:-1
[D][05:19:27][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:27][COMM]accel parse set 0
[D][05:19:27][COMM]imu rest ok. 98696
[D][05:19:27][COMM]imu sleep 0
[W][05:19:27][COMM]now sleep


2025-07-31 23:26:54:775 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:26:54:789 ==>> 检测【检测33V休眠电流】
2025-07-31 23:26:54:808 ==>> 开始33V电流采样
2025-07-31 23:26:54:816 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:26:54:878 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:26:55:887 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:26:55:948 ==>> Current33V:????:11.32

2025-07-31 23:26:56:396 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:26:56:405 ==>> 【检测33V休眠电流】通过,【11.32uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:26:56:429 ==>> 该项需要延时执行
2025-07-31 23:26:58:417 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:26:58:430 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:26:58:454 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:26:58:540 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 2021mV
Get AD_V7 1099mV
OVER 150


2025-07-31 23:26:59:451 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:26:59:464 ==>> 检测【打开小电池2】
2025-07-31 23:26:59:475 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:26:59:524 ==>> 6A A6 01 A6 6A 


2025-07-31 23:26:59:629 ==>> Battery ON
OVER 150


2025-07-31 23:26:59:729 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:26:59:740 ==>> 该项需要延时执行
2025-07-31 23:27:00:240 ==>> 此处延时了:【500】毫秒
2025-07-31 23:27:00:253 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:27:00:276 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:27:00:332 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:27:00:517 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:27:00:531 ==>> 该项需要延时执行
2025-07-31 23:27:01:018 ==>> 此处延时了:【500】毫秒
2025-07-31 23:27:01:049 ==>> 检测【进入休眠模式2】
2025-07-31 23:27:01:079 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:27:01:091 ==>> [D][05:19:33][COMM]------------ready to Power on Acckey 1------------
[D][05:19:33][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:33][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:33][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:33][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[W][05:19:34][COMM]CAN START!
[D][05:19:34][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:34][COMM]CAN message bat fault change: 0x01B987F

2025-07-31 23:27:01:118 ==>> E->0x00000000 105027
[D][05:19:34][COMM][Audio]exec status ready.
[D][05:19:34][CAT1]<<< 
OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 6
[D][05:19:34][COMM]imu wakeup ok. 105042
[D][05:19:34][COMM]imu wakeup 1
[W][05:19:34][COMM]wake up system, wakeupEvt=0x80
[D][05:19:34][COMM]frm_can_weigth_power_set 1
[D][05:19:34][COMM]Clear Sleep Block Evt
[D][05:19:34][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:34][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:27:01:168 ==>> [W][05:19:34][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:27:01:243 ==>> [E][05:19:34][COMM]1x1 rx timeout
[D][05:19:34][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:27:01:348 ==>> [D][05:19:34][COMM]msg 02A0 loss. last_tick:105012. cur_tick:105521. peri

2025-07-31 23:27:01:409 ==>> od:50
[D][05:19:34][COMM]msg 02A4 loss. last_tick:105012. cur_tick:105521. period:50
[D][05:19:34][COMM]msg 02A5 loss. last_tick:105012. cur_tick:105522. period:50
[D][05:19:34][COMM]msg 02A6 loss. last_tick:105012. cur_tick:105522. period:50
[D][05:19:34][COMM]msg 02A7 loss. last_tick:105012. cur_tick:105523. period:50
[D][05:19:34][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 105523
[D][05:19:34][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 105523


2025-07-31 23:27:01:757 ==>> [D][05:19:34][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[E][05:19:34][COMM]1x1 rx timeout
[E][05:19:34][COMM]1x1 tp timeout
[E][05:19:34][COMM]1x1 error -3.
[D][05:19:34][COMM]Main Task receive event:28 finished processing
[D][05:19:34][COMM]Main Task receive event:28
[D][05:19:34][COMM]prepare to sleep
[D][05:19:34][CAT1]gsm read msg sub id: 12
[D][05:19:34][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:34][CAT1]<<< 
OK

[D][05:19:34][CAT1]exec over: func id: 12, ret: 6
[D][05:19:34][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:34][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:27:02:046 ==>>                                                                            :100
[D][05:19:35][COMM]msg 0221 loss. last_tick:105012. cur_tick:106017. period:100
[D][05:19:35][COMM]msg 0224 loss. last_tick:105012. cur_tick:106018. period:100
[D][05:19:35][COMM]msg 0260 loss. last_tick:105012. cur_tick:106018. period:100
[D][05:19:35][COMM]msg 0280 loss. last_tick:105012. cur_tick:106018. period:100
[D][05:19:35][COMM]msg 02C0 loss. last_tick:105012. cur_tick:106019. period:100
[D][05:19:35][COMM]msg 02C1 loss. last_tick:105012. cur_tick:106019. period:100
[D][05:19:35][COMM]msg 02C2 loss. last_tick:105012. cur_tick:106019. period:100
[D][05:19:35][COMM]msg 02E0 loss. last_tick:105012. cur_tick:106020. period:100
[D][05:19:35][COMM]msg 02E1 loss. last_tick:105012. cur_tick:106020. period:100
[D][05:19:35][COMM]msg 02E2 loss. last_tick:105012. cur_tick:106021. period:100
[D][05:19:35][COMM]msg 0300 loss. last_tick:105012. cur_tick:106021. period:100
[D][05:19:35][COMM]msg 0301 loss. last_tick:105012. cur_tick:106021. period:100
[D][05:19:35][COMM]bat msg 0240 loss. last_tick:105012. cur_tick:106022. period:100. j,i:1 54
[D][05:19:35][COMM]bat msg 0241 loss. last_tick:105012. cu

2025-07-31 23:27:02:054 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 23:27:02:079 ==>> #################### 【测试结束】 ####################
2025-07-31 23:27:02:097 ==>> 关闭5V供电
2025-07-31 23:27:02:111 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:27:02:151 ==>> r_tick:106022. period:100. j,i:2 55
[D][05:19:35][COMM]bat msg 0242 loss. last_tick:105012. cur_tick:106022. period:100. j,i:3 56
[D][05:19:35][COMM]bat msg 0244 loss. last_tick:105012. cur_tick:106023. period:100. j,i:5 58
[D][05:19:35][COMM]bat msg 024E loss. last_tick:105012. cur_tick:106023. period:100. j,i:15 68
[D][05:19:35][COMM]bat msg 024F loss. last_tick:105012. cur_tick:106024. period:100. j,i:16 69
[D][05:19:35][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 106024
[D][05:19:35][COMM]CAN message bat fault change: 0x00000000->0x0001802E 106025
[D][05:19:35][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 106025
                                                                              

2025-07-31 23:27:02:226 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:27:02:391 ==>> [D][05:19:35][COMM]msg 0222 loss. last_tick:105012. cur_tick:106519. period:150
[D][05:19:35][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 106520
[D][05:19:35][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:35][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:35][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:27:02:602 ==>> [E][05:19:35][COMM]1x1 rx timeout
[E][05:19:35][COMM]1x1 tp timeout
[E][05:19:35][COMM]1x1 error -3.
[W][05:19:35][COMM]CAN STOP!
[D][05:19:35][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:35][COMM]------------ready to Power off Acckey 1------------
[D][05:19:35][COMM]------------ready to Power off Acckey 2------------
[D][05:19:35][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:35][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 104
[D][05:19:35][COMM]bat sleep fail, reason:-1
[D][05:19:35][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:35][COMM]accel parse set 0
[D][05:19:35][COMM]imu rest ok. 106710
[D][05:19:35][COMM]imu sleep 0
[W][05:19:35][COMM]now sleep


2025-07-31 23:27:03:097 ==>> 关闭5V供电成功
2025-07-31 23:27:03:110 ==>> 关闭33V供电
2025-07-31 23:27:03:133 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:27:03:221 ==>> 5A A5 02 5A A5 


2025-07-31 23:27:03:325 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:27:04:109 ==>> 关闭33V供电成功
2025-07-31 23:27:04:130 ==>> 关闭3.7V供电
2025-07-31 23:27:04:143 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:27:04:218 ==>> 6A A6 02 A6 6A 


2025-07-31 23:27:04:323 ==>> Battery OFF
OVER 150


2025-07-31 23:27:04:533 ==>> [D][05:19:37][COMM]------------ready to Power on Acckey 1------------
[D][05:19:37][COMM]############# RESUME SIGNAL [0x2] #############
[D][05:19:37][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:37][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:37][COMM]----- get Acckey 1 

2025-07-31 23:27:04:911 ==>>  

