2025-07-31 20:29:36:425 ==>> MES查站成功:
查站序号:P5100010053128F7验证通过
2025-07-31 20:29:36:435 ==>> 扫码结果:P5100010053128F7
2025-07-31 20:29:36:436 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:29:36:437 ==>> 测试参数版本:2024.10.11
2025-07-31 20:29:36:439 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:29:36:441 ==>> 检测【打开透传】
2025-07-31 20:29:36:444 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:29:36:549 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:29:36:798 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:29:36:804 ==>> 检测【检测接地电压】
2025-07-31 20:29:36:805 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:29:36:946 ==>> 1A A1 40 00 00 
Get AD_V22 233mV
OVER 150


2025-07-31 20:29:37:089 ==>> 【检测接地电压】通过,【233mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:29:37:091 ==>> 检测【打开小电池】
2025-07-31 20:29:37:093 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:29:37:143 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:29:37:360 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:29:37:363 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:29:37:366 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:29:37:447 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:29:37:642 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:29:37:645 ==>> 检测【等待设备启动】
2025-07-31 20:29:37:647 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:29:38:679 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:29:39:044 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:29:39:226 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 20:29:39:706 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:29:39:751 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:29:39:931 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:29:40:644 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:29:40:734 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:29:41:029 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:29:41:500 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:29:41:561 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:29:41:565 ==>> 检测【产品通信】
2025-07-31 20:29:41:566 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:29:41:742 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:29:41:867 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:29:41:870 ==>> 检测【初始化完成检测】
2025-07-31 20:29:41:872 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:29:42:078 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE44600] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:29:42:140 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:29:42:143 ==>> 检测【关闭大灯控制1】
2025-07-31 20:29:42:144 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:29:42:152 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:29:42:334 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:29:42:424 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:29:42:426 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:29:42:427 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:29:42:561 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:42:666 ==>> [W][05:17:51][COM

2025-07-31 20:29:42:741 ==>> M]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:29:42:957 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:29:42:960 ==>> 检测【关闭仪表供电】
2025-07-31 20:29:42:961 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:29:43:141 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:29:43:234 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:29:43:236 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:29:43:238 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:29:43:417 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:29:43:515 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:29:43:517 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:29:43:520 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:29:43:582 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:43:687 ==>> [W][05:17:52][COMM]>>>>>Input 

2025-07-31 20:29:43:717 ==>> command = AT+ACCKEY1=0<<<<<


2025-07-31 20:29:43:795 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:29:43:798 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:29:43:799 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:29:43:944 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE44700] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:29:44:069 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:29:44:072 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:29:44:076 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:29:44:154 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:29:44:260 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:29:44:352 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:29:44:355 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:29:44:357 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:29:44:455 ==>> 5A A5 03 5A A5 


2025-07-31 20:29:44:560 ==>> OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:44:648 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:29:44:650 ==>> 该项需要延时执行
2025-07-31 20:29:45:117 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017


2025-07-31 20:29:45:376 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]


2025-07-31 20:29:45:601 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:45:814 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:29:46:334 ==>>                           ipheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:6

2025-07-31 20:29:46:439 ==>> 5
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0

2025-07-31 20:29:46:544 ==>> ], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not

2025-07-31 20:29:46:619 ==>>  connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 20:29:47:607 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:48:275 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:29:48:380 ==>> [D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 20:29:48:637 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:48:652 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:29:48:655 ==>> 检测【33V输入电压ADC】
2025-07-31 20:29:48:658 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:29:48:957 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:17:57][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:57][COMM]adc read left brake adc:13  volt:17 mv
[D][05:17:57][COMM]adc read right brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:57][COMM]adc read battery ts volt:17 mv
[D][05:17:57][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:17:57][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:29:49:188 ==>> 【33V输入电压ADC】通过,【32754mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:29:49:191 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:29:49:193 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:29:49:263 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2757mV
Get AD_V6 1990mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:29:49:462 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:49:465 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:29:49:480 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:49:482 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:29:49:501 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 20:29:49:503 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:29:49:506 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:29:49:520 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:29:49:524 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:29:49:543 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:29:49:545 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:29:49:653 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2759mV
Get AD_V6 1988mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:29:49:938 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:49:941 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:29:49:975 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10004
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10004


2025-07-31 20:29:49:983 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:49:985 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:29:49:988 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 20:29:50:050 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:29:50:053 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:29:50:092 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:29:50:094 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:29:50:143 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:29:50:145 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:29:50:266 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 1990mV
Get AD_V7 1092mV
OVER 150
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:29:50:484 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:50:487 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:29:50:549 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:50:552 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:29:50:554 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 20:29:50:654 ==>> [D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:29:50:807 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:29:50:811 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:29:50:842 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:29:50:845 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:29:50:881 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:29:50:883 ==>> 检测【打开WIFI(1)】
2025-07-31 20:29:50:888 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:29:51:037 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE44800] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:29:51:202 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:29:51:205 ==>> 检测【清空消息队列(1)】
2025-07-31 20:29:51:207 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:29:51:461 ==>> [D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total



2025-07-31 20:29:51:656 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:29:51:735 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:29:51:739 ==>> 检测【打开GPS(1)】
2025-07-31 20:29:51:743 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:29:52:132 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:01][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:01][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:01][CAT1]exec over: func id: 1, ret: 18
[D][05:18:01][CAT1]sub id: 1, ret: 18

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:01][SAL ]gsm power on ind rst[18]
[D][05:18:01][M2M ]m2m gsm power on, ret[0]
[D][05:18:01][COMM]Main Task receive event:1
[D][05:18:01][COMM]Main Task receive event:1 finished processing
[D][05:18:01][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:01][COMM][Audio]exec status ready.
[D][05:18:01][M2M ]first set address
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:01][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:01][COMM]set time err 2021
[D][05:18:01][CAT1]gsm read msg sub id: 31
[D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,

2025-07-31 20:29:52:162 ==>> 0,0,16,2000

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 20:29:52:270 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:29:52:273 ==>> 检测【打开GSM联网】
2025-07-31 20:29:52:276 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:29:52:989 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 31, ret: 6
[D][05:18:01][CAT1]gsm read msg sub id: 32
[D][05:18:01][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 32, ret: 6
[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]gsm read msg sub id: 5
[D][05:18:01][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:01][CAT1]<<< 
867222087966562

OK

[D][05:18:01][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:01][CAT1]<<< 
460130071541421

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:29:53:061 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:29:53:067 ==>> 检测【打开仪表供电1】
2025-07-31 20:29:53:069 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:29:53:094 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[999

2025-07-31 20:29:53:199 ==>> 9] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6
                                                                                                                                                                           

2025-07-31 20:29:53:244 ==>>                                                                                                                                                                                                        

2025-07-31 20:29:53:304 ==>>                                                                                                                                                                                                      

2025-07-31 20:29:53:899 ==>> [D][05:18:02][COMM]13729 imu init OK
[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:29:54:097 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:29:54:307 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:03][COMM]set POWER 1
[D][05:18:03][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:29:54:386 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:29:54:389 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:29:54:391 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:29:54:549 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:03][COMM][oneline_display]: command mode, ON!
[D][05:18:03][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:29:54:594 ==>>                                                                                                                                                                  

2025-07-31 20:29:54:670 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:29:54:672 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:29:54:674 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:29:54:840 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub read adc[3],val[33224]


2025-07-31 20:29:54:982 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:29:54:985 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:29:54:988 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:29:55:155 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE44900] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:29:55:260 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:29:55:282 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:29:55:285 ==>> 检测【AD_V20电压】
2025-07-31 20:29:55:287 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:29:55:395 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:29:55:485 ==>> 本次取值间隔时间:87ms
2025-07-31 20:29:55:515 ==>> [D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,59,,,41,33,,,41,25,,,40,24,,,38,1*71

$GBGSV,2,2,06,14,,,44,3,,,41,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1658.242,1658.242,52.958,2097152,2097152,2097152*4C

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:29:55:590 ==>> 本次取值间隔时间:96ms
2025-07-31 20:29:55:632 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:29:55:696 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:29:55:741 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:29:55:847 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:29:56:182 ==>> 本次取值间隔时间:429ms
2025-07-31 20:29:56:227 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:29:56:334 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:29:56:487 ==>> 本次取值间隔时间:147ms
2025-07-31 20:29:56:491 ==>> [D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,41,14,,,40,59,,,40,25,,,40,1*72

$GBGSV,3,2,09,24,,,39,60,,,39,39,,,38,5,,,30,1*49

$GBGSV,3,3,09,40,,,37,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1590.979,1590.979,50.911,2097152,2097152,2097152*43

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:29:56:608 ==>> 本次取值间隔时间:116ms
2025-07-31 20:29:56:661 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:29:56:763 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:29:56:856 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 20:29:56:976 ==>> 本次取值间隔时间:201ms
2025-07-31 20:29:57:018 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:57:021 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:29:57:024 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:29:57:141 ==>> 3A A3 02 00 A3 


2025-07-31 20:29:57:246 ==>> OFF_OUT2
OVER 150


2025-07-31 20:29:57:333 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:29:57:336 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:29:57:339 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:29:57:427 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,14,,,40,25,,,40,24,,,40,1*72

$GBGSV,4,2,16,59,,,39,60,,,39,3,,,39,39,,,38,1*45

$GBGSV,4,3,16,42,,,38,40,,,36,38,,,36,1,,,35,1*43

$GBGSV,4,4,16,2,,,34,44,,,34,4,,,31,5,,,30,1*43

$GBRMC,,V,,,,,,,310725,0.1,E,N,V*53

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1531.388,1531.388,49.012,2097152,2097152,2097152*41



2025-07-31 20:29:57:532 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:29:57:638 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:29:57:641 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:29:57:645 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:29:57:744 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:29:57:932 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:29:57:937 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:29:57:942 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:29:58:173 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:29:58:225 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:29:58:228 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:29:58:230 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:29:58:279 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:29:58:489 ==>> $GBGGA,123002.252,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,41,60,,,40,24,,,40,14,,,40,1*7E

$GBGSV,5,2,19,25,,,40,3,,,39,59,,,39,39,,,38,1*44

$GBGSV,5,3,19,42,,,38,38,,,36,40,,,36,1,,,36,1*4E

$GBGSV,5,4,19,44,,,34,2,,,33,5,,,31,4,,,31,1*4B

$GBGSV,5,5,19,16,,,14,9,,,38,13,,,36,1*49

$GBRMC,123002.252,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123002.252,0.000,737.724,737.724,674.682,2097152,2097152,2097152*6F

[D][05:18:07][HSDK][0] flush to flash addr:[0xE44A00] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:29:58:747 ==>> $GBGGA,123002.552,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,41,33,,,41,60,,,40,14,,,40,1*75

$GBGSV,5,2,20,25,,,40,3,,,39,59,,,39,39,,,38,1*4E

$GBGSV,5,3,20,42,,,38,38,,,36,40,,,36,1,,,36,1*44

$GBGSV,5,4,20,16,,,36,2,,,34,44,,,34,13,,,31,1*45

$GBGSV,5,5,20,5,,,31,4,,,31,10,,,38,9,,,38,1*4D

$GBRMC,123002.552,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123002.552,0.000,759.821,759.821,694.876,2097152,2097152,2097152*63



2025-07-31 20:29:58:778 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:29:58:783 ==>> 检测【AD_V21电压】
2025-07-31 20:29:58:787 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:29:58:852 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 20:29:58:912 ==>> 本次取值间隔时间:127ms
2025-07-31 20:29:58:938 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:29:58:940 ==>> 检测【关闭仪表供电2】
2025-07-31 20:29:58:943 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:29:59:143 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:29:59:239 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:29:59:242 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:29:59:244 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:29:59:433 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:29:59:545 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:29:59:548 ==>> 检测【打开AccKey2供电】
2025-07-31 20:29:59:553 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:29:59:754 ==>> $GBGGA,123003.532,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,33,,,41,3,,,40,14,,,40,1*41

$GBGSV,6,2,22,25,,,40,60,,,39,59,,,39,39,,,38,1*7A

$GBGSV,6,3,22,42,,,38,38,,,36,40,,,36,1,,,36,1*45

$GBGSV,6,4,22,16,,,36,6,,,35,9,,,34,44,,,34,1*7F

$GBGSV,6,5,22,2,,,33,13,,,32,8,,,32,10,,,31,1*7E

$GBGSV,6,6,22,5,,,31,4,,,30,1*76

$GBRMC,123003.532,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123003.532,0.000,744.886,744.886,681.219,2097152,2097152,2097152*63

[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:29:59:835 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:29:59:840 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:29:59:843 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:00:167 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:09][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:18:09][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:09][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:09][COMM]adc read throttle adc:18  volt:23 mv
[D][05:18:09][COMM]adc read battery ts volt:16 mv
[D][05:18:09][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:09][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:30:00:317 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:30:00:375 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33260mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:30:00:378 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:30:00:383 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:30:00:514 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:30:00:658 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:30:00:662 ==>> 该项需要延时执行
2025-07-31 20:30:00:740 ==>> $GBGGA,123004.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,3,,,40,24,,,40,14,,,40,1*40

$GBGSV,6,2,22,25,,,40,60,,,39,59,,,39,39,,,38,1*7A

$GBGSV,6,3,22,42,,,38,38,,,36,40,,,36,1,,,36,1*45

$GBGSV,6,4,22,16,,,36,9,,,35,6,,,35,13,,,34,1*7C

$GBGSV,6,5,22,2,,,33,8,,,33,44,,,33,10,,,31,1*7C

$GBGSV,6,6,22,4,,,31,5,,,30,1*76

$GBRMC,123004.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123004.512,0.000,746.758,746.758,682.930,2097152,2097152,2097152*65



2025-07-31 20:30:01:732 ==>> $GBGGA,123005.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,41,33,,,41,60,,,40,3,,,40,1*43

$GBGSV,6,2,23,14,,,40,25,,,40,59,,,39,39,,,38,1*76

$GBGSV,6,3,23,42,,,38,38,,,36,40,,,36,1,,,36,1*44

$GBGSV,6,4,23,16,,,36,9,,,35,6,,,35,13,,,34,1*7D

$GBGSV,6,5,23,2,,,33,8,,,33,44,,,33,10,,,32,1*7E

$GBGSV,6,6,23,26,,,31,5,,,31,4,,,31,1*70

$GBRMC,123005.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123005.512,0.000,745.780,745.780,682.036,2097152,2097152,2097152*6B



2025-07-31 20:30:02:310 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:30:02:739 ==>> $GBGGA,123006.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,3,,,40,14,,,40,1*47

$GBGSV,6,2,24,25,,,40,60,,,39,59,,,39,39,,,38,1*7C

$GBGSV,6,3,24,42,,,38,40,,,36,38,,,36,16,,,36,1*75

$GBGSV,6,4,24,1,,,36,13,,,35,9,,,35,6,,,35,1*4D

$GBGSV,6,5,24,2,,,33,8,,,33,44,,,33,26,,,32,1*7C

$GBGSV,6,6,24,7,,,32,10,,,32,5,,,31,4,,,30,1*46

$GBRMC,123006.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123006.512,0.000,742.293,742.293,678.848,2097152,2097152,2097152*6C



2025-07-31 20:30:03:659 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:30:03:664 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:30:03:669 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:03:749 ==>> $GBGGA,123007.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,40,3,,,40,24,,,40,1*45

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,40,,,36,38,,,36,1,,,36,1*43

$GBGSV,6,4,24,16,,,36,13,,,35,9,,,35,6,,,35,1*7B

$GBGSV,6,5,24,8,,,34,2,,,33,7,,,33,44,,,33,1*49

$GBGSV,6,6,24,26,,,32,10,,,32,4,,,31,5,,,30,1*75

$GBRMC,123007.512,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123007.512,0.000,744.013,744.013,680.420,2097152,2097152,2097152*68



2025-07-31 20:30:03:959 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:12][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:12][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:12][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:12][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:12][COMM]adc read battery ts volt:18 mv
[D][05:18:12][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:12][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:30:04:194 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【303mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:30:04:199 ==>> 检测【打开AccKey1供电】
2025-07-31 20:30:04:203 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:30:04:336 ==>> [D][05:18:13][COMM]read battery soc:255
[W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:30:04:475 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:30:04:503 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:30:04:508 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:30:04:546 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 20:30:04:726 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 20:30:04:741 ==>> $GBGGA,123008.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,60,,,40,3,,,40,1*44

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,13,,,36,40,,,36,38,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,8,,,34,44,,,34,2,,,33,1*49

$GBGSV,6,6,24,26,,,33,10,,,32,5,,,31,4,,,31,1*75

$GBRMC,123008.512,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123008.512,0.000,749.178,749.178,685.142,2097152,2097152,2097152*63



2025-07-31 20:30:04:761 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:30:04:764 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:30:04:766 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:05:057 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:13][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:13][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:13][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:13][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:13][COMM]adc read battery ts volt:12 mv
[D][05:18:13][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:13][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:30:05:289 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:30:05:292 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:30:05:295 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:30:05:440 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:30:05:564 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:30:05:568 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:30:05:571 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:30:05:651 ==>> 1A A1 00 40 00 
Get AD_V14 2657mV
OVER 150


2025-07-31 20:30:05:741 ==>> $GBGGA,123009.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,41,60,,,40,14,,,40,1*71

$GBGSV,6,2,24,25,,,40,3,,,39,59,,,39,39,,,38,1*49

$GBGSV,6,3,24,42,,,38,13,,,36,40,,,36,38,,,36,1*70

$GBGSV,6,4,24,16,,,36,1,,,35,9,,,35,6,,,35,1*48

$GBGSV,6,5,24,7,,,34,8,,,34,44,,,34,2,,,33,1*49

$GBGSV,6,6,24,26,,,33,10,,,32,5,,,31,4,,,31,1*75

$GBRMC,123009.512,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123009.512,0.000,748.318,748.318,684.356,2097152,2097152,2097152*64



2025-07-31 20:30:05:816 ==>> 原始值:【2657】, 乘以分压基数【2】还原值:【5314】
2025-07-31 20:30:05:890 ==>> 【读取AccKey1电压(ADV14)后】通过,【5314mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:30:05:893 ==>> 检测【打开WIFI(2)】
2025-07-31 20:30:05:896 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:30:06:077 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE44B00] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:30:06:206 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:30:06:210 ==>> 检测【转刹把供电】
2025-07-31 20:30:06:213 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:30:06:306 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:30:06:411 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:30:06:514 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:30:06:521 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:30:06:526 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:30:06:621 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:30:06:805 ==>> $GBGGA,123010.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,60,,,40,3,,,40,1*44

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,8,,,34,2,,,33,26,,,33,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123010.512,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123010.512,0.000,748.318,748.318,684.356,2097152,2097152,2097152*6C

+WIFISCAN:4,0,44A1917CAD80,-79
+WIFISCAN:4,1,44A1917CAD81,-82
+WIFISCAN:4,2,CC057790A5C0,-85
+WIFISCAN:4,3,CC057790A5C1,-86

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 20:30:06:895 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:30:07:576 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:30:07:681 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:30:07:786 ==>> $GBGGA,123011.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,60,,,40,14,,,40,1*72

$GBGSV,6,2,24,25,,,40,3,,,39,59,,,39,39,,,38,1*49

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,8,,,34,2,,,33,26,,,33,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123011.512,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123011.512,0.000,747.453,747.453,683.565,2097152,2097152,2097152*6C

[W][05:18:16][COMM]>>>>>Input command = ?<<<<
[D][05:18:16][GNSS]recv submsg id[3]
1A A1 00 80 00 
Get AD_V15 2399mV
OVER 150


2025-07-31 20:30:07:847 ==>> 原始值:【2399】, 乘以分压基数【2】还原值:【4798】
2025-07-31 20:30:07:910 ==>> 【读取AD_V15电压(前)】通过,【4798mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:30:07:914 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:30:07:919 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:30:08:015 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:30:08:218 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:30:08:323 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:30:08:728 ==>> $GBGGA,123012.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,40,3,,,40,24,,,40,1*45

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,8,,,34,2,,,33,26,,,33,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123012.512,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123012.512,0.000,747.453,747.453,683.565,2097152,2097152,2097152*6F



2025-07-31 20:30:08:983 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:30:09:088 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:30:09:118 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:30:09:148 ==>> 1A A1 01 00 00 
Get AD_V16 2420mV
OVER 150


2025-07-31 20:30:09:253 ==>> 原始值:【2420】, 乘以分压基数【2】还原值:【4840】
2025-07-31 20:30:09:276 ==>> 【读取AD_V16电压(前)】通过,【4840mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:30:09:280 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:30:09:282 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:09:562 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:18][COMM]adc read out 24v adc:13  volt:328 mv
[D][05:18:18][COMM]adc read left brake adc:22  volt:29 mv
[D][05:18:18][COMM]adc read right brake adc:19  volt:25 mv
[D][05:18:18][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:18][COMM]adc read battery ts volt:18 mv
[D][05:18:18][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:30:09:668 ==>> $GBGGA,123013.512,,,,,0,00,,,M,,M,,*6

2025-07-31 20:30:09:727 ==>> E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,40,3,,,40,24,,,40,1*45

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,38,,,36,40,,,36,1,,,36,1*43

$GBGSV,6,4,24,16,,,36,7,,,35,13,,,35,9,,,35,1*7A

$GBGSV,6,5,24,6,,,35,8,,,34,2,,,33,26,,,33,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123013.512,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123013.512,0.000,747.452,747.452,683.564,2097152,2097152,2097152*6F



2025-07-31 20:30:09:806 ==>> 【转刹把供电电压(主控ADC)】通过,【5414mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:30:09:811 ==>> 检测【转刹把供电电压】
2025-07-31 20:30:09:815 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:10:162 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:19][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:19][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:19][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:19][COMM]adc read throttle adc:20  volt:26 mv
[D][05:18:19][COMM]adc read battery ts volt:21 mv
[D][05:18:19][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3075  volt:5405 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:30:10:327 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:30:10:334 ==>> 【转刹把供电电压】通过,【5405mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:30:10:339 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:30:10:347 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:30:10:539 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:30:10:618 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:30:10:623 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:30:10:627 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:30:10:720 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:30:10:735 ==>> $GBGGA,123014.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,40,3,,,40,24,,,40,1*45

$GBGSV,6,2,24,14,,,40,25,,,40,59,,,39,39,,,38,1*71

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,44,,,34,2,,,33,26,,,33,1*72

$GBGSV,6,6,24,8,,,33,10,,,32,5,,,31,4,,,31,1*49

$GBRMC,123014.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123014.512,0.000,747.453,747.453,683.565,2097152,2097152,2097152*69



2025-07-31 20:30:10:825 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:30:10:916 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:30:10:930 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:30:11:020 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE44C00] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:30:11:035 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:30:11:050 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:30:11:140 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:30:11:215 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:30:11:245 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:30:11:277 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:30:11:287 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:30:11:311 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:30:11:380 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:30:11:455 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:30:11:548 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:30:11:552 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:30:11:555 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:30:11:652 ==>> 3A A3 03 01 A3 


2025-07-31 20:30:11:742 ==>> $GBGGA,123015.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,40,24,,,40,14,,,40,1*46

$GBGSV,6,2,24,25,,,40,60,,,39,59,,,39,39,,,38,1*7C

$GBGSV,6,3,24,42,,,38,38,,,36,40,,,36,1,,,36,1*43

$GBGSV,6,4,24,16,,,36,13,,,35,9,,,35,6,,,35,1*7B

$GBGSV,6,5,24,7,,,34,2,,,33,26,,,33,8,,,33,1*4D

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123015.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123015.512,0.000,744.867,744.867,681.200,2097152,2097152,2097152*6E

ON_OUT3
OVER 150


2025-07-31 20:30:11:851 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:30:11:855 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:30:11:858 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:30:11:954 ==>> 3A A3 04 01 A3 


2025-07-31 20:30:12:044 ==>> ON_OUT4
OVER 150


2025-07-31 20:30:12:145 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:30:12:149 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:30:12:155 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:30:12:256 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:30:12:331 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:30:12:455 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:30:12:459 ==>> 检测【左刹电压测试1】
2025-07-31 20:30:12:465 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:12:814 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:21][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:21][COMM]adc read left brake adc:1738  volt:2291 mv
[D][05:18:21][COMM]adc read right brake adc:1729  volt:2279 mv
[D][05:18:21][COMM]adc read throttle adc:1730  volt:2280 mv
[D][05:18:21][COMM]adc read battery ts volt:17 mv
[D][05:18:21][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:21][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
$GBGGA,123016.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,14,,,40,25,,,40,1*73

$GBGSV,6,2,24,60,,,39,3,,,39,59,,,39,39,,,38,1*46

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,9,,,35,6,,,35,1*4B

$GBGSV,6,5,24,7,,,34,44,,,34,2,,,33,26,,,33,1*72

$GBGSV,6,6,24,8,,,33,10,,,32,5,,,31,4,,,31,1*49

$GBRMC,123016.512,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123016.512,0.000,746.589,746.589,682.775,2097152,2097152,2097152*69

[D][05:18:21][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:21][COMM]arm_hub adc read

2025-07-31 20:30:12:844 ==>>  led yb adc:1435  volt:33270 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:30:12:999 ==>> 【左刹电压测试1】通过,【2291】符合目标值【2250】至【2500】要求!
2025-07-31 20:30:13:003 ==>> 检测【右刹电压测试1】
2025-07-31 20:30:13:043 ==>> 【右刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 20:30:13:047 ==>> 检测【转把电压测试1】
2025-07-31 20:30:13:079 ==>> 【转把电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 20:30:13:085 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:30:13:092 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:30:13:145 ==>> 3A A3 03 00 A3 


2025-07-31 20:30:13:250 ==>> OFF_OUT3
OVER 150


2025-07-31 20:30:13:407 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:30:13:411 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:30:13:417 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:30:13:555 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:30:13:678 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:30:13:684 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:30:13:693 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:30:13:750 ==>> $GBGGA,123017.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,41,33,,,41,3,,,40,14,,,40,1*47

$GBGSV,6,2,24,25,,,40,60,,,39,59,,,39,39,,,38,1*7C

$GBGSV,6,3,24,42,,,38,13,,,36,38,,,36,40,,,36,1*70

$GBGSV,6,4,24,1,,,36,16,,,36,7,,,35,9,,,35,1*4A

$GBGSV,6,5,24,6,,,35,8,,,34,2,,,33,26,,,33,1*4A

$GBGSV,6,6,24,44,,,33,10,,,32,5,,,31,4,,,31,1*71

$GBRMC,123017.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123017.512,0.000,748.314,748.314,684.352,2097152,2097152,2097152*6F

3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:30:13:950 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:30:13:953 ==>> 检测【左刹电压测试2】
2025-07-31 20:30:13:959 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:30:14:256 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:23][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:23][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:23][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:23][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:23][COMM]adc read battery ts volt:19 mv
[D][05:18:23][COMM]adc read in 24v adc:1307  volt:33057 mv
[D][05:18:23][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:30:14:346 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:30:14:487 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 20:30:14:495 ==>> 检测【右刹电压测试2】
2025-07-31 20:30:14:529 ==>> 【右刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 20:30:14:532 ==>> 检测【转把电压测试2】
2025-07-31 20:30:14:551 ==>> 【转把电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 20:30:14:554 ==>> 检测【晶振检测】
2025-07-31 20:30:14:560 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:30:15:550 ==>> $GBGGA,123014.517,2301.2567744,N,11421.9414349,E,1,09,1.17,77.511,M,-1.770,M,,*5F

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,40,16,53,22,36,1*78

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,40,39,52,6,38,1*4A

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,38,25,41,278,40,1*72

$GBGSV,6,4,24,60,41,238,39,7,40,187,34,9,38,317,34,40,34,160,36,1*79

$GBGSV,6,5,24,4,32,112,31,10,30,195,32,38,29,192,36,13,26,210,36,1*47

$GBGSV,6,6,24,26,25,233,33,8,23,204,34,5,22,257,31,44,15,103,33,1*78

$GBRMC,123014.517,A,2301.2567744,N,11421.9414349,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:23][GNSS]HD8040 GPS
[D][05:18:23][GNSS]GPS diff_sec 124009911, report 0x42 frame
$GBGST,123014.517,1.152,0.434,0.390,0.611,1.936,2.780,7.503*7A

[W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]
[D][05:18:23][COMM]Main Task receive event:131
[D][05:18:23][COMM]index:0,power_mode:0xFF
[D][05:18:23][COMM]index:1,sound_mode:0xFF
[D][05:18:23][COMM]index:2,gsensor_mode:0xFF
[D][05:18:23][COMM]index:3,report_freq_mode:0xFF
[D][05:1

2025-07-31 20:30:15:601 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:30:15:605 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:30:15:612 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:30:15:656 ==>> 8:23][COMM]index:4,report_period:0xFF
[D][05:18:23][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:23][COMM]index:6,normal_reset_period:0xFF
[D][05:18:23][COMM]index:7,spock_over_speed:0xFF
[D][05:18:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:23][COMM]index:16,imu_config_params:0xFF
[D][05:18:23][COMM]index:17,long_connect_params:0xFF
[D][05:18:23][COMM]index:18,detain_mark:0xFF
[D][05:18:23][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:23][COMM]index:21,mc_mode:0xFF
[D][05:18:23][COMM]index:22,S_mode:0xFF
[D][05:18:23][COMM]index:23,overweight:0xFF
[D][05:18:23][COMM]index:24,standstill_mode:0xFF
[D][05:18:23][COMM]index:25,night_mode:0xFF
[D][05:18:23][COMM]index:26,experiment1:0xFF
[D][05:18:23][COMM]index:27,experiment2:0xFF
[D][05:18:23][COMM]i

2025-07-31 20:30:15:762 ==>> ndex:28,experiment3:0xFF
[D][05:18:23][COMM]index:29,experiment4:0xFF
[D][05:18:23][COMM]index:30,night_mode_start:0xFF
[D][05:18:23][COMM]index:31,night_mode_end:0xFF
[D][05:18:23][COMM]index:33,park_report_minutes:0xFF
[D][05:18:23][COMM]index:34,park_report_mode:0xFF
[D][05:18:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:23][COMM]index:38,charge_battery_para: FF
[D][05:18:23][COMM]index:39,multirider_mode:0xFF
[D][05:18:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:23][COMM]index:44,riding_duration_config:0xFF
[D][05:18:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:23][COMM]index:49,mc_load_startup:0xFF
[D][05:18:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:23][COMM]index:52,traffic_mode:0xFF
[D][05:18:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:23][COMM

2025-07-31 20:30:15:868 ==>> ]index:54,traffic_security_model_cycle:0xFF
[D][05:18:23][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:23][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:23][COMM]index:63,experiment5:0xFF
[D][05:18:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:23][COMM]index:72,experiment6:0xFF
[D][05:18:23][COMM]index:73,experiment7:0xFF
[D][05:18:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:23][COMM]index:75,zero_value_from_server:-1
[D][05:18:23][COMM]index:76,multirider_threshold:255
[D][05:18:23][COMM]index:77,experiment8:255
[D][05:18:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:23]

2025-07-31 20:30:15:974 ==>> [COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:23][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:23][COMM]index:83,loc_report_interval:255
[D][05:18:23][COMM]index:84,multirider_threshold_p2:255
[D][05:18:23][COMM]index:85,multirider_strategy:255
[D][05:18:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:23][COMM]index:90,weight_param:0xFF
[D][05:18:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:23][COMM]index:95,current_limit:0xFF
[D][05:18:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:23][COMM]index:100,location_mode:0xFF

[W][05:18:23][PROT]remove success[1629955103],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:23][PROT]index:0 1629955103
[D][05:18:23][PROT]is_send:0
[D][05:18:23][PROT]sequence_num:4
[D][05:18:23][PROT]retry_timeout:0
[D][05:18:23][PROT]retry_times:1

2025-07-31 20:30:16:079 ==>> 
[D][05:18:23][PROT]send_path:0x2
[D][05:18:23][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:23][PROT]===========================================================
[W][05:18:23][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955103]
[D][05:18:23][PROT]===========================================================
[D][05:18:23][PROT]sending traceid [9999999999900005]
[D][05:18:23][PROT]Send_TO_M2M [1629955103]
[W][05:18:23][PROT]add success [1629955103],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:23][COMM]Main Task receive event:131 finished processing
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:23][SAL ]sock send credit cnt[6]
[D][05:18:23][SAL ]sock send ind credit cnt[6]
[D][05:18:23][M2M ]m2m send data len[294]
[D][05:18:23][SAL ]Cellular task submsg id[10]
[D][05:18:23][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[0]
[D][05:18:23][CAT1]gsm read msg sub id: 15
[D][05:18:23][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:23][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B5B5A01035C0D5163B592CD272C2BCE1C245C87DD65CF9F49CE186B26F5403EC7F

2025-07-31 20:30:16:184 ==>> 2FF17EDE246A3092775175739881E88F3153ECAE091384A857D851F9369E25F9F158396936986E474C301804BD0F564A5B4042FB4FA3922866D7B05B3D3377D893D72B5BA62D45943E0B65484DCA0F6433156CAE9FD22FE16018D447B8564E1D7F10
[D][05:18:23][CAT1]<<< 
SEND OK

[D][05:18:23][CAT1]exec over: func id: 15, ret: 11
[D][05:18:23][CAT1]sub id: 15, ret: 11

[D][05:18:23][SAL ]Cellular task submsg id[68]
[D][05:18:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:23][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:23][M2M ]g_m2m_is_idle become true
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:23][PROT]M2M Send ok [1629955103]
$GBGGA,123015.017,2301.2572919,N,11421.9415976,E,1,09,1.17,77.614,M,-1.770,M,,*58

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,39,16,53,22,36,1*76

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,21A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1661mV
Get AD_V4 1653mV
Get AD_V5 2760mV
Get AD_V6 1991mV
Get AD_V7 

2025-07-31 20:30:16:214 ==>> 1092mV
OVER 150


2025-07-31 20:30:16:319 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          92,35,44,15,103,31,5*78

$GBRMC,123016.000,A,2301.2575941,N,11421.9413874,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,123016.000,

2025-07-31 20:30:16:364 ==>> 1.386,0.331,0.301,0.464,1.400,1.730,4.911*76

                                         

2025-07-31 20:30:16:403 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:30:16:411 ==>> 检测【检测BootVer】
2025-07-31 20:30:16:417 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:16:837 ==>> [D][05:18:25][HSDK][0] flush to flash addr:[0xE44D00] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541421
[D][05:18:25][FCTY]HardwareID  = 867222087966562
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1296
[D][05:18:25][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 126 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCT

2025-07-31 20:30:16:882 ==>> Y]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3699 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:30:16:971 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:30:16:976 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:30:16:982 ==>> 检测【检测固件版本】
2025-07-31 20:30:17:033 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:30:17:039 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:30:17:046 ==>> 检测【检测蓝牙版本】
2025-07-31 20:30:17:087 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:30:17:094 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:30:17:102 ==>> 检测【检测MoBikeId】
2025-07-31 20:30:17:207 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:30:17:211 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:30:17:217 ==>> 检测【检测蓝牙地址】
2025-07-31 20:30:17:224 ==>> 取到目标值:DE505F0B0851
2025-07-31 20:30:17:314 ==>> 【检测蓝牙地址】通过,【DE505F0B0851】符合目标值【】要求!
2025-07-31 20:30:17:322 ==>> 提取到蓝牙地址:DE505F0B0851
2025-07-31 20:30:17:329 ==>> 检测【BOARD_ID】
2025-07-31 20:30:17:341 ==>> $GBGGA,123017.000,2301.2577740,N,11421.9412808,E,1,09,1.17,76.861,M,-1.770,M,,*59

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,40,16,53,22,36,1*78

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,40,39,52,6,38,1*4A

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,38,25,41,279,40,1*73

$GBGSV,6,4,24,60,41,238,40,7,40,187,35,9,38,317,35,40,34,160,36,1*77

$GBGSV,6,5,24,4,32,112,31,10,30,195,32,38,29,192,36,13,26,210,36,1*47

$GBGSV,6,6,24,26,25,233,34,8,23,204,34,5,22,257,31,44,15,103,34,1*78

$GBGSV,2,1,08,33,68,289,42,24,52,10,42,39,52,6,40,42,45,163,39,5*42

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15,103,31,5*79

$GBRMC,123017.000,A,2301.2577740,N,11421.9412808,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,123017.000,1.476,0.213,0.199,0.301,1.356,1.613,4.366*77



2025-07-31 20:30:17:393 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:30:17:401 ==>> 检测【检测充电电压】
2025-07-31 20:30:17:446 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:30:17:451 ==>> 检测【检测VBUS电压1】
2025-07-31 20:30:17:501 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:30:17:508 ==>> 检测【检测充电电流】
2025-07-31 20:30:17:569 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:30:17:577 ==>> 检测【检测IMEI】
2025-07-31 20:30:17:590 ==>> 取到目标值:867222087966562
2025-07-31 20:30:17:616 ==>> 【检测IMEI】通过,【867222087966562】符合目标值【】要求!
2025-07-31 20:30:17:621 ==>> 提取到IMEI:867222087966562
2025-07-31 20:30:17:627 ==>> 检测【检测IMSI】
2025-07-31 20:30:17:633 ==>> 取到目标值:460130071541421
2025-07-31 20:30:17:665 ==>> 【检测IMSI】通过,【460130071541421】符合目标值【】要求!
2025-07-31 20:30:17:669 ==>> 提取到IMSI:460130071541421
2025-07-31 20:30:17:674 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:30:17:677 ==>> 取到目标值:460130071541421
2025-07-31 20:30:17:716 ==>> 【校验网络运营商(移动)】通过,【460130071541421】符合目标值【】要求!
2025-07-31 20:30:17:720 ==>> 检测【打开CAN通信】
2025-07-31 20:30:17:727 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:30:17:849 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:30:18:018 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:30:18:022 ==>> 检测【检测CAN通信】
2025-07-31 20:30:18:026 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:30:18:155 ==>> can send success


2025-07-31 20:30:18:201 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:30:18:306 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,123018.000,2301.2578514,N,11421.9412122,E,1,09,1.17,76.738,M,-1.770,M,,*58

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,40,16,53,22,36,1*78

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,40,39,52,6,38,1*4A

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,39,25,41,279,40,1*72

$GBGSV,6,4,24,60,41,238,40,7,40,187,34,9,38,317,35,40,34,160,36,1*76

$GBGSV,6,5,24,4,32,112,31,10,30,195,32,38,29,192,36,13,26,210,36,1*47

$GBGSV,6,6,24,26,25,233,33,8,23,204,34,5,22,257,31,44,15,103,34,1*7F

$GBGSV,2,1,08,33,68,289,43,24,52,10,42,39,52,6,41,42,45,163,40,5*4C

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15

2025-07-31 20:30:18:332 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:30:18:336 ==>> 检测【关闭CAN通信】
2025-07-31 20:30:18:342 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:30:18:381 ==>> ,103,31,5*79

$GBRMC,123018.000,A,2301.2578514,N,11421.9412122,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,123018.000,1.592,0.269,0.249,0.376,1.376,1.584,4.018*75

[D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38311
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
                                         标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:30:18:456 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:30:18:637 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:30:18:642 ==>> 检测【打印IMU STATE】
2025-07-31 20:30:18:646 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:30:18:839 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:30:18:955 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:30:18:961 ==>> 检测【六轴自检】
2025-07-31 20:30:18:965 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:30:19:348 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBGGA,123019.000,2301.2579090,N,11421.9411917,E,1,09,1.17,76.727,M,-1.770,M,,*52

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,42,3,61,191,40,16,53,22,36,1*7B

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,41,39,52,6,38,1*4B

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,38,25,41,279,40,1*73

$GBGSV,6,4,24,60,41,238,40,7,40,187,34,9,38,317,35,40,34,160,36,1*76

$GBGSV,6,5,24,4,32,112,31,10,30,195,32,38,29,192,36,13,26,210,36,1*47

$GBGSV,6,6,24,26,25,233,33,8,23,204,34,5,22,257,31,44,15,103,34,1*7F

$GBGSV,2,1,08,33,68,289,43,24,52,10,42,39,52,6,41,42,45,163,40,5*4C

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15,103,31,5*79

$GBRMC,123019.000,A,2301.2579090,N,11421.9411917,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,123019.000,1.397,0.291,0.269,0.404,1.186,1.374,3.638*77



2025-07-31 20:30:20:341 ==>> $GBGGA,123020.000,2301.2579530,N,11421.9411313,E,1,09,1.17,76.595,M,-1.770,M,,*52

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,40,16,53,22,36,1*78

$GBGSV,6,2,24,59,52,129,40,6,52,35,35,24,52,10,41,39,52,6,38,1*45

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,38,25,41,279,40,1*73

$GBGSV,6,4,24,60,41,238,40,7,40,187,35,9,38,317,35,40,34,160,36,1*77

$GBGSV,6,5,24,4,32,112,32,10,30,195,33,38,29,192,36,13,26,210,36,1*45

$GBGSV,6,6,24,26,25,233,33,8,23,204,34,5,22,257,32,44,15,103,34,1*7C

$GBGSV,2,1,08,33,68,289,43,24,52,10,42,39,52,6,41,42,45,163,41,5*4D

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15,103,31,5*79

$GBRMC,123020.000,A,2301.2579530,N,11421.9411313,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,123020.000,1.451,0.245,0.228,0.344,1.197,1.359,3.437*7D



2025-07-31 20:30:20:371 ==>>                                          

2025-07-31 20:30:20:882 ==>> [D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:30:21:064 ==>> [D][05:18:30][COMM]Main Task receive event:142
[D][05:18:30][COMM]###### 41111 imu self test OK ######
[D][05:18:30][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-14,-5,4033]
[D][05:18:30][COMM]Main Task receive event:142 finished processing


2025-07-31 20:30:21:296 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:30:21:305 ==>> 检测【打印IMU STATE2】
2025-07-31 20:30:21:314 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:30:21:352 ==>> $GBGGA,123021.000,2301.2579976,N,11421.9411136,E,1,09,1.17,76.569,M,-1.770,M,,*5B

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,289,41,3,61,191,40,16,53,22,36,1*78

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,41,39,52,6,38,1*4B

$GBGSV,6,3,24,1,48,126,36,2,46,238,34,42,45,163,38,25,41,279,41,1*75

$GBGSV,6,4,24,60,41,238,40,7,40,187,35,9,38,317,35,40,34,160,36,1*77

$GBGSV,6,5,24,4,32,112,32,10,30,195,33,38,29,192,36,13,26,210,36,1*45

$GBGSV,6,6,24,26,25,233,34,8,23,204,34,5,22,257,31,44,15,103,34,1*78

$GBGSV,2,1,08,33,68,289,43,24,52,10,43,39,52,6,40,42,45,163,40,5*4C

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15,103,31,5*79

$GBRMC,123021.000,A,2301.2579976,N,11421.9411136,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123021.000,1.261,0.206,0.194,0.290,1.022,1.173,3.163*73



2025-07-31 20:30:21:442 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:30][COMM]YAW data: 32763[32763]
[D][05:18:30][COMM]pitch:-66 roll:0
[D][05:18:30][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:30:21:654 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:30:21:659 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:30:21:663 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:30:21:750 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:30:21:930 ==>> [D][05:18:30][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:30:21:935 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:30:21:939 ==>> 检测【检测VBUS电压2】
2025-07-31 20:30:21:943 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:22:417 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071541421
[D][05:18:31][FCTY]HardwareID  = 867222087966562
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:31][FCTY]Bat         = 3924 mv
[D][05:18:31][FCTY]Current     = 50 ma
[D][05:18:31][FCTY]VBUS        = 9200 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 8, adc = 317
[D][05:18:31][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 151 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3699 mv
[D][0

2025-07-31 20:30:22:484 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:22:522 ==>> 5:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGGA,123022.000,2301.2580354,N,11421.9410617,E,1,09,1.17,76.481,M,-1.770,M,,*56

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.14,4*0D

$GBGSV,6,1,24,14,71,195,40,33,68,288,41,3,61,191,40,16,53,22,36,1*79

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,41,39,52,6,38,1*4B

$GBGSV,6,3,24,1,48,126,36,2,46,238,34,42,45,163,39,13,43,219,36,1*75

$GBGSV,6,4,24,25,41,279,41,60,41,238,40,7,40,187,35,9,38,317,35,1*7D

$GBGSV,6,5,24,40,34,160,36,4,32,112,32,10,30,195,33,38,29,192,36,1*44

$GBGSV,6,6,24,26,25,233,34,8,23,204,34,5,22,257,32,44,15,103,34,1*7B

$GBGSV,2,1,08,33,68,288,43,24,52,10,43,39,52,6,40,42,45,163,41,5*4C

$GBGSV,2,2,08,25,41,279,40,40,34,160,36,38,29,192,35,44,15,103,31,5*79

$GBRMC,123022.000,A,2301.2580354,N,11421.9410617,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123022.000,1.343,0.263,0.243,0.369,1.070,1.203,3.054*7A



2025-07-31 20:30:22:809 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071541421
[D][05:18:31][FCTY]HardwareID  = 867222087966562
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:31][FCTY]Bat         = 3924 mv
[D][05:18:31][FCTY]Current     = 50 ma
[D][05:18:31][FCTY]VBUS        = 9200 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 4, adc = 183
[D][05:18:31][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 202 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18

2025-07-31 20:30:22:854 ==>> :31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3699 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:30:23:025 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:23:472 ==>> $GBGGA,123023.000,2301.2580494,N,11421.9410439,E,1,09,1.17,76.510,M,-1.770,M,,*5B

$GBGSA,A,3,14,33,24,39,42,25,40,38,44,,,,2.44,1.17,2.15,4*0C

$GBGSV,6,1,24,14,71,195,40,33,68,288,42,3,61,191,40,16,53,22,36,1*7A

$GBGSV,6,2,24,59,52,129,39,6,52,35,35,24,52,10,40,39,52,6,38,1*4A

$GBGSV,6,3,24,1,48,126,36,2,46,238,33,42,45,163,39,13,43,219,36,1*72

$GBGSV,6,4,24,25,41,279,41,60,41,238,40,7,40,187,35,9,38,317,35,1*7D

$GBGSV,6,5,24,40,34,160,36,4,32,112,32,10,30,195,33,38,29,192,36,1*44

$GBGSV,6,6,24,26,25,233,34,8,23,204,34,5,22,257,32,44,15,103,34,1*7B

$GBGSV,2,1,08,33,68,288,43,24,52,10,42,39,52,6,41,42,45,163,40,5*4D

$GBGSV,2,2,08,25,41,279,41,40,34,160,36,38,29,192,35,44,15,103,31,5*78

$GBRMC,123023.000,A,2301.2580494,N,11421.9410439,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,123023.000,1.525,0.236,0.220,0.331,1.199,1.313,3.020*70

[W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][COMM]msg 0601 loss. last_tick:38294. cur_tick:43308. period:500
[D][05:

2025-07-31 20:30:23:577 ==>> 18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43309
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071541421
[D][05:18:32][FCTY]HardwareID  = 867222087966562
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:32][FCTY]Bat         = 3624 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 9200 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 3, adc = 158
[D][05:18:32][FCTY]Acckey1 vol = 5516 mv, Acckey2 vol = 151 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3699 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 =====

2025-07-31 20:30:23:607 ==>> =====
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:30:23:825 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:24:266 ==>> [D][05:18:32][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[W][05:18:32][GNSS]stop locating
[D][05:18:32][GNSS]stop event:8
[D][05:18:32][GNSS]GPS stop. ret=0
[D][05:18:32][GNSS]all continue location stop
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]index:0
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5


2025-07-31 20:30:24:371 ==>> 
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][PROT]index:0 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [999999999

2025-07-31 20:30:24:476 ==>> 9900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][CAT1]gsm read msg sub id: 24
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:32][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 24, ret: 6
[D][05:18:32][CAT1]sub id: 24, ret: 6

[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B387F06E624EC98B03CD443A4DE66A6CF02B4D761B7855D168333637757AD2C87D42AE04505EE7C9269F235AA21CB8A3E13BE3E61F370943D76019CA699D4BBCA22B9563E8AE87165969661BC4670A2DAF8996
[D][05:18:33][CAT1]<<< 
SEND OK

[

2025-07-31 20:30:24:581 ==>> D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]
[D][05:18:33][HSDK]need to erase for write: is[0x0] ie[0x3E00]
[D][05:18:33][HSDK][0] flush to flash addr:[0xE44E00] --- write len --- [256]
[W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130071541421
[D][05:18:33][FCTY]HardwareID  = 867222087966562
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:33][FCTY]Bat         = 3604 mv
[D

2025-07-31 20:30:24:671 ==>> ][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 4900 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 3, adc = 122
[D][05:18:33][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 177 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3699 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:30:24:776 ==>> [D][05:18:33][GNSS]recv submsg id[1]
[D][05:18:33][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:33][GNSS]location stop e

2025-07-31 20:30:24:806 ==>> vt done evt


2025-07-31 20:30:24:915 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:30:25:314 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:34][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========
[D][05:18:34][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:34][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:34][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:34][FCTY]DeviceID    = 460130071541421
[D][05:18:34][FCTY]HardwareID  = 867222087966562
[D][05:18:34][FCTY]MoBikeID    = 9999999999
[D][05:18:34][FCTY]LockID      = FFFFFFFFFF
[D][05:18:34][FCTY]BLEFWVersion= 105
[D][05:18:34][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:18:34][FCTY]Bat         = 3624 mv
[D][05:18:34][FCTY]Current     = 0 ma
[D][05:18:34][FCTY]VBUS        = 4900 mv
[D][05:18:34][FCTY]TEMP= 0,BATID= 0,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:34][FCTY]Ext battery vol = 2, adc = 107
[D][05:18:34][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 202 mv
[D][05:18:34][FCTY]Bike Type flag is invalied
[D][05:18:34][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:34][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:34][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:34][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:34][FCTY

2025-07-31 20:30:25:344 ==>> ]Bat1         = 3699 mv
[D][05:18:34][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:30:25:454 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:30:25:459 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:30:25:467 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:30:25:542 ==>> 5A A5 01 5A A5 


2025-07-31 20:30:25:647 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:30:25:707 ==>> [D][05:18:34][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 20:30:25:730 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:30:25:736 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:30:25:741 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:30:25:797 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 20:30:25:842 ==>> 5A A5 04 5A A5 


2025-07-31 20:30:25:948 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:30:26:012 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:30:26:018 ==>> 检测【打开WIFI(3)】
2025-07-31 20:30:26:027 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:30:26:269 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:35][CAT1]<<< 
OK

[D][05:18:35][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:30:26:548 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:30:26:553 ==>> 检测【扩展芯片hw】
2025-07-31 20:30:26:562 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:30:26:746 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:35][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:30:26:846 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:30:26:852 ==>> 检测【扩展芯片boot】
2025-07-31 20:30:26:867 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:30:26:887 ==>> 检测【扩展芯片sw】
2025-07-31 20:30:26:892 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:30:26:898 ==>> 检测【检测音频FLASH】
2025-07-31 20:30:26:918 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:30:27:035 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:30:27:322 ==>> [D][05:18:36][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:0------------
[D][05:18:36][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:30:28:059 ==>>                                                                                                                                 ][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]more than the number of battery plugs
[D][05:18:36][COMM]VBUS is 1
[D][05:18:36][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:36][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:36][COMM]Bat auth off fail, error:-1
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:36][COMM]read file, len:10800, num:3
[D][05

2025-07-31 20:30:28:164 ==>> :18:36][COMM]--->crc16:0xb8a
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]Main Task receive event:65
[D][05:18:36][COMM]main task tmp_sleep_event = 80
[D][05:18:36][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:36][COMM]Main Task receive event:65 finished processing
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]Main Task receive event:66
[D][05:18:36][COMM]Try to Auto Lock Bat
[D][05:18:36][COMM]Main Task receive event:66 finished proce

2025-07-31 20:30:28:269 ==>> ssing
[D][05:18:36][COMM]Main Task receive event:60
[D][05:18:36][COMM]smart_helmet_vol=255,255
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get soc error
[E][05:18:36][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:36][COMM]report elecbike
[W][05:18:36][PROT]remove success[1629955116],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]index:1
[D][05:18:36][PROT]is_send:1
[D][05:18:36][PROT]sequence_num:6
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x3
[D][05:18:36][PROT]msg_type:0x5d03
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [162995

2025-07-31 20:30:28:374 ==>> 5116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]Sending traceid[9999999999900007]
[D][05:18:36][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:36][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:36][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:36][PROT]add success [1629955116],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:36][COMM]Main Task receive event:60 finished processing
[D][05:18:36][COMM]Receive Bat Lock cmd 0
[D][05:18:36][COMM]VBUS is 1
[D][05:18:36][COMM]Main Task receive event:61
[D][05:18:36][COMM][D301]:type:3, trace id:280
[D][05:18:36][COMM]id[], hw[000
[D][05:18:36][COMM]get mcMaincircuitVolt error
[D][05:18:36][COMM]get mcSubcircuitVolt error
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get bat work state err
[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000

2025-07-31 20:30:28:479 ==>> ],priority[0],index[2],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]Main Task receive event:61 finished processing
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process]

2025-07-31 20:30:28:554 ==>> .l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:36][COMM]read battery soc:255
+WIFISCAN:4,0,CC057790A620,-58
+WIFISCAN:4,1,CC057790A621,-58
+WIFISCAN:4,2,CC057790A5C0,-84
+WIFISCAN:4,3,CC057790A5C1,-84

[D][05:18:37][CAT1]wifi scan report total[4]


2025-07-31 20:30:28:814 ==>> [D][05:18:37][GNSS]recv submsg id[3]


2025-07-31 20:30:29:337 ==>> [D][05:18:38][PROT]CLEAN,SEND:0
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:6
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:3
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900007]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE6D33B53B251C29209A4BBFB7488110C860F90385D3AC2FAF52AB12E69813CB6F354FE63C42AE6BD543F5D0

2025-07-31 20:30:29:412 ==>> 24BC4BD0072C0FE9FC7FD124110DB4550E91646E77204135303D24E835834CB68F02FAE8F954E1
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]


2025-07-31 20:30:29:798 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 20:30:30:387 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:30:30:599 ==>> [D][05:18:39][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:30:31:155 ==>> [D][05:18:40][COMM]crc 108B
[D][05:18:40][COMM]flash test ok


2025-07-31 20:30:31:507 ==>> [D][05:18:40][COMM]51472 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:30:31:816 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 20:30:31:940 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:30:31:946 ==>> 检测【打开喇叭声音】
2025-07-31 20:30:31:955 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:30:32:679 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:41][COMM]file:A20 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:41][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:41][COMM]file:A20 exist
[D][05:18:41][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:41][COMM]read file, len:15228, num:4
[D][05:18:41][COMM]--->crc16:0x419c
[D][05:18:41][COMM]read file success
[W][05:18:41][COMM][Audio].l:[936].close hexlog save
[D][05:18:41][COMM]accel parse set 1
[D][05:18:41][COMM][Audio]mon:9,05:18:41
[D][05:18:41][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:41][COMM]f:[ec800m_audi

2025-07-31 20:30:32:742 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:30:32:753 ==>> 检测【打开大灯控制】
2025-07-31 20:30:32:764 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:30:32:787 ==>> o_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:41][COMM]52483 imu init OK
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec80

2025-07-31 20:30:32:874 ==>> 0m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:30:32:949 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:30:33:013 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:30:33:019 ==>> 检测【关闭仪表供电3】
2025-07-31 20:30:33:029 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:30:33:240 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:42][COMM]set POWER 0


2025-07-31 20:30:33:289 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:30:33:295 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:30:33:303 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:30:33:407 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:30:33:579 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:30:33:586 ==>> 检测【读大灯电压】
2025-07-31 20:30:33:592 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:30:33:729 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:42][COMM]arm_hub read adc[5],val[33015]


2025-07-31 20:30:33:804 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 20:30:33:857 ==>> 【读大灯电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:30:33:863 ==>> 检测【关闭大灯控制2】
2025-07-31 20:30:33:869 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:30:34:018 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:30:34:127 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:30:34:137 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:30:34:158 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:30:34:572 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:43][COMM]arm_hub read adc[5],val[92]
[D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:6
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:2
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900007]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]S

2025-07-31 20:30:34:647 ==>> end Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BEA2ACD3FC7214056F1DECE8E8A1CB7B90C27DF105E32EA1C25B49EC8B9B873276CB8B2B68B248647DEE4561A55D0CEAECD667B686558622F2C42BE207BDA5F5627513C52A8039DC5E61F5D8E5CA58E740A8DA
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 20:30:34:666 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:30:34:673 ==>> 检测【打开WIFI(4)】
2025-07-31 20:30:34:683 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:30:34:874 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:43][CAT1]gsm read msg sub id: 12
[D][05:18:43][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:43][CAT1]<<< 
OK

[D][05:18:43][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:30:34:998 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:30:35:004 ==>> 检测【EC800M模组版本】
2025-07-31 20:30:35:014 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:30:35:209 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:30:35:389 ==>> [D][05:18:44][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:44][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:30:35:528 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:30:35:538 ==>> 检测【配置蓝牙地址】
2025-07-31 20:30:35:547 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:30:35:725 ==>> +WIFISCAN:4,0,CC057790A621,-57
+WIFISCAN:4,1,F62A7D2297A3,-63
+WIFISCAN:4,2,F42A7D1297A3,-65
+WIFISCAN:4,3,44A1917CAD81,-87

[D][05:18:44][CAT1]wifi scan report total[4]
[W][05:18:44][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:30:35:740 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:DE505F0B0851>】
2025-07-31 20:30:35:831 ==>> [D][05:18:44][COMM]read battery soc:255
[D][05:18:44][GNSS]recv subms

2025-07-31 20:30:35:861 ==>> g id[3]


2025-07-31 20:30:35:951 ==>> recv ble 1
recv ble 2
ble set mac ok :de,50,5f,b,8,51
enable filters ret : 0

2025-07-31 20:30:36:015 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:30:36:025 ==>> 检测【BLETEST】
2025-07-31 20:30:36:035 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:30:36:149 ==>> 4A A4 01 A4 4A 


2025-07-31 20:30:36:194 ==>> [D][05:18:45][COMM]56269 imu init OK
[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:30:36:360 ==>> recv ble 1
recv ble 2
<BSJ*MAC:DE505F0B0851*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9DE505F0B085199999

2025-07-31 20:30:36:450 ==>> OVER 150


2025-07-31 20:30:37:038 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:30:37:044 ==>> 该项需要延时执行
2025-07-31 20:30:37:222 ==>> [D][05:18:46][COMM]57281 imu init OK
[D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:30:37:809 ==>> [D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:46][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:46][COMM]accel parse set 0
[D][05:18:46][COMM][Audio].l:[1012].open hexlog save
                                         

2025-07-31 20:30:38:210 ==>> [D][05:18:47][COMM]58293 imu init OK


2025-07-31 20:30:39:810 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]index:1 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:6
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:1
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900007]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88BE59E697CC

2025-07-31 20:30:39:915 ==>> 55BF3E7F4748A8C39CB5FF3AEF8DAB7544E9A3FDF2FBB6CF10F246DC5062E9489470669101FD3657A8901EEE41AEBA572A3836BD2D9AF50C6D67372219F86F377832A3AE5551BE3B8D71C899C128
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
                                         

2025-07-31 20:30:41:829 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 20:30:43:827 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 20:30:45:051 ==>> [D][05:18:53][PROT]CLEAN,SEND:1
[D][05:18:53][PROT]CLEAN:1
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:5
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:2
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900006]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][201] ... ->:
0063B98E11331133113311

2025-07-31 20:30:45:126 ==>> 3311331B88B33D653ED08764F0D1DE1EFD3749EC08163623B8F3D2C84E05811AAC87A27546CDE5CB0FA5F42B27346C846C9C31E166E419F04E766CB285685138ED7B621711697B46B8978910E5AF04CE70C35277BA6A1D10
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 20:30:45:835 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 20:30:47:041 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:30:47:048 ==>> 检测【检测WiFi结果】
2025-07-31 20:30:47:054 ==>> WiFi信号:【44A1917CAD80】,信号值:-79
2025-07-31 20:30:47:061 ==>> WiFi信号:【44A1917CAD81】,信号值:-82
2025-07-31 20:30:47:076 ==>> WiFi信号:【CC057790A5C0】,信号值:-85
2025-07-31 20:30:47:082 ==>> WiFi信号:【CC057790A5C1】,信号值:-86
2025-07-31 20:30:47:108 ==>> WiFi信号:【CC057790A620】,信号值:-58
2025-07-31 20:30:47:117 ==>> WiFi信号:【CC057790A621】,信号值:-58
2025-07-31 20:30:47:129 ==>> WiFi信号:【F62A7D2297A3】,信号值:-63
2025-07-31 20:30:47:141 ==>> WiFi信号:【F42A7D1297A3】,信号值:-65
2025-07-31 20:30:47:152 ==>> WiFi数量【8】, 最大信号值:-58
2025-07-31 20:30:47:169 ==>> 检测【检测GPS结果】
2025-07-31 20:30:47:189 ==>> 符合定位需求的卫星数量:【15】
2025-07-31 20:30:47:200 ==>> 
北斗星号:【14】,信号值:【40】
北斗星号:【33】,信号值:【41】
北斗星号:【3】,信号值:【40】
北斗星号:【16】,信号值:【36】
北斗星号:【59】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【24】,信号值:【40】
北斗星号:【39】,信号值:【38】
北斗星号:【1】,信号值:【36】
北斗星号:【42】,信号值:【38】
北斗星号:【25】,信号值:【40】
北斗星号:【60】,信号值:【39】
北斗星号:【40】,信号值:【36】
北斗星号:【38】,信号值:【36】
北斗星号:【13】,信号值:【36】

2025-07-31 20:30:47:210 ==>> 检测【CSQ强度】
2025-07-31 20:30:47:232 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:30:47:273 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:56][CAT1]gsm read msg sub id: 12
[D][05:18:56][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:56][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:56][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:30:47:341 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 20:30:47:348 ==>> 检测【关闭GSM联网】
2025-07-31 20:30:47:379 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:30:47:535 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:56][COMM]GSM test
[D][05:18:56][COMM]GSM test disable


2025-07-31 20:30:47:617 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:30:47:624 ==>> 检测【4G联网测试】
2025-07-31 20:30:47:634 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:30:48:618 ==>> [D][05:18:56][HSDK][0] flush to flash addr:[0xE44F00] --- write len --- [256]
[W][05:18:56][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:56][COMM]Main Task receive event:14
[D][05:18:56][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955136, allstateRepSeconds = 0
[D][05:18:56][COMM]index:0,power_mode:0xFF
[D][05:18:56][COMM]index:1,sound_mode:0xFF
[D][05:18:56][COMM]index:2,gsensor_mode:0xFF
[D][05:18:56][COMM]index:3,report_freq_mode:0xFF
[D][05:18:56][COMM]index:4,report_period:0xFF
[D][05:18:56][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:56][COMM]index:6,normal_reset_period:0xFF
[D][05:18:56][COMM]index:7,spock_over_speed:0xFF
[D][05:18:56][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:56][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:56][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:56][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:56][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:56][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:56][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:56][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:56][COMM]index:16,imu_config_params:0xFF
[D][05:18:56][COMM]index:17,long_connect_params:0xFF
[D][05:18:56][COMM]index:18,detain_mark:0xFF
[D][05:18:56][COMM]index:19,lock_pos_re

2025-07-31 20:30:48:723 ==>> port_count:0xFF
[D][05:18:56][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:56][COMM]index:21,mc_mode:0xFF
[D][05:18:56][COMM]index:22,S_mode:0xFF
[D][05:18:56][COMM]index:23,overweight:0xFF
[D][05:18:56][COMM]index:24,standstill_mode:0xFF
[D][05:18:56][COMM]index:25,night_mode:0xFF
[D][05:18:56][COMM]index:26,experiment1:0xFF
[D][05:18:56][COMM]index:27,experiment2:0xFF
[D][05:18:56][COMM]index:28,experiment3:0xFF
[D][05:18:56][COMM]index:29,experiment4:0xFF
[D][05:18:56][COMM]index:30,night_mode_start:0xFF
[D][05:18:56][COMM]index:31,night_mode_end:0xFF
[D][05:18:56][COMM]index:33,park_report_minutes:0xFF
[D][05:18:56][COMM]index:34,park_report_mode:0xFF
[D][05:18:56][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:56][COMM]index:38,charge_battery_para: FF
[D][05:18:56][COMM]index:39,multirider_mode:0xFF
[D][05:18:56][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:56][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:56][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:56][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:56][COMM]index:44,riding_duration_config:0xFF
[D][05:18:56][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:56][C

2025-07-31 20:30:48:828 ==>> OMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:56][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:56][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:56][COMM]index:49,mc_load_startup:0xFF
[D][05:18:56][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:56][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:56][COMM]index:52,traffic_mode:0xFF
[D][05:18:56][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:56][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:56][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:56][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:56][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:56][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:56][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:56][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:56][COMM]index:63,experiment5:0xFF
[D][05:18:56][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:56][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:56][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:56][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:56][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:56][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:56

2025-07-31 20:30:48:933 ==>> ][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:56][COMM]index:72,experiment6:0xFF
[D][05:18:56][COMM]index:73,experiment7:0xFF
[D][05:18:56][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:56][COMM]index:75,zero_value_from_server:-1
[D][05:18:56][COMM]index:76,multirider_threshold:255
[D][05:18:56][COMM]index:77,experiment8:255
[D][05:18:56][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:56][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:56][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:56][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:56][COMM]index:83,loc_report_interval:255
[D][05:18:56][COMM]index:84,multirider_threshold_p2:255
[D][05:18:56][COMM]index:85,multirider_strategy:255
[D][05:18:56][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:56][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:56][COMM]index:90,weight_param:0xFF
[D][05:18:56][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:56][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:56][COMM]index:95,current_limit:0xFF
[D][05:18:56][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:56][COMM]ind

2025-07-31 20:30:49:038 ==>> ex:100,location_mode:0xFF

[W][05:18:56][PROT]remove success[1629955136],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:56][PROT]add success [1629955136],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:56][COMM]read battery soc:255
[D][05:18:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:56][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:8
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900009]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][CAT1]gsm read msg sub id: 13
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[294]


2025-07-31 20:30:49:143 ==>> 
[D][05:18:56][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:56][CAT1]exec over: func id: 13, ret: 21
[D][05:18:56][M2M ]get csq[24]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:57][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B1D7A82D9EEDD28C17A907E4D16900779C12F987564E864C56B5EDF484E67F04F012D471899DC132FD41366F63023D04D23542887FB9397CBC1F73EDC9C10A0FE4C5E80A61FDB4437F3F060E22B13CD8D56EBC20C86DF77314C89DEE62160FB744F29342FE5BEFCC6F01D42EF32B628963C4E4820BF55C6108D71504DE31A5D97A292E
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_G

2025-07-31 20:30:49:233 ==>> SM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]
>>>>>RESEND ALLSTATE<<<<<
[W][05:18:57][PROT]remove success[1629955137],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:18:57][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:57][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:57][PROT]add success [1629955137],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:18:57][COMM]------>period, report file manifest
[D][05:18:57][COMM]Main Task receive event:14 finished processing
[D][05:18:57][CAT1]gsm read msg sub id: 21
[D][05:18:57][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]cell info report total[0]
[D][05:18:57][CAT1]exec over: func id: 21, ret: 6


2025-07-31 20:30:49:638 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:30:49:645 ==>> 检测【关闭GPS】
2025-07-31 20:30:49:650 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:30:49:885 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]all continue location stop
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]all sing location stop
[D][05:18:58][COMM]read battery soc:255


2025-07-31 20:30:49:919 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:30:49:927 ==>> 检测【清空消息队列2】
2025-07-31 20:30:49:953 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:30:50:144 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:30:50:193 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:30:50:205 ==>> 检测【轮动检测】
2025-07-31 20:30:50:228 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:30:50:254 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:30:50:309 ==>> [D][05:18:59][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:30:50:694 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:30:50:755 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:30:50:972 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:30:50:985 ==>> 检测【关闭小电池】
2025-07-31 20:30:51:004 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:30:51:045 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:30:51:246 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:30:51:257 ==>> 检测【进入休眠模式】
2025-07-31 20:30:51:282 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:30:51:520 ==>> [D][05:19:00][HSDK][0] flush to flash addr:[0xE45000] --- write len --- [256]
[W][05:19:00][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:00][COMM]Main Task receive event:28
[D][05:19:00][COMM]main task tmp_sleep_event = 8
[D][05:19:00][COMM]prepare to sleep
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:30:51:891 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 20:30:52:117 ==>> [D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 12, ret: 6
[D][05:19:01][M2M ]tcpclient close[4]
[D][05:19:01][SAL ]Cellular task submsg id[12]
[D][05:19:01][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c98], socket[0]
[D][05:19:01][CAT1]gsm read msg sub id: 9
[D][05:19:01][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 9, ret: 6
[D][05:19:01][CAT1]sub id: 9, ret: 6

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:01][SAL ]socket close ind. id[4]
[D][05:19:01][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:01][COMM]1x1 frm_can_tp_send ok
[D][05:19:01][CAT1]pdpdeact urc len[22]


2025-07-31 20:30:52:395 ==>> [E][05:19:01][COMM]1x1 rx timeout
[D][05:19:01][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:30:52:903 ==>> [E][05:19:01][COMM]1x1 rx timeout
[E][05:19:01][COMM]1x1 tp timeout
[E][05:19:01][COMM]1x1 error -3.
[W][05:19:01][COMM]CAN STOP!
[D][05:19:01][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:01][COMM]------------ready to Power off Acckey 1------------
[D][05:19:01][COMM]------------ready to Power off Acckey 2------------
[D][05:19:01][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:01][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1304
[D][05:19:01][COMM]bat sleep fail, reason:-1
[D][05:19:01][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:01][COMM]accel parse set 0
[D][05:19:01][COMM]imu rest ok. 72891
[D][05:19:01][COMM]imu sleep 0
[W][05:19:01][COMM]now sleep


2025-07-31 20:30:53:083 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:30:53:089 ==>> 检测【检测33V休眠电流】
2025-07-31 20:30:53:100 ==>> 开始33V电流采样
2025-07-31 20:30:53:126 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:30:53:193 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:30:54:204 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:30:54:251 ==>> Current33V:????:17.05

2025-07-31 20:30:54:713 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:30:54:720 ==>> 【检测33V休眠电流】通过,【17.05uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:30:54:727 ==>> 该项需要延时执行
2025-07-31 20:30:56:728 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:30:56:739 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:30:56:763 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:30:56:867 ==>> 1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2743mV
Get AD_V6 1963mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:30:57:775 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:30:57:783 ==>> 检测【打开小电池2】
2025-07-31 20:30:57:807 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:30:57:846 ==>> 6A A6 01 A6 6A 


2025-07-31 20:30:57:951 ==>> Battery ON
OVER 150


2025-07-31 20:30:58:075 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:30:58:088 ==>> 该项需要延时执行
2025-07-31 20:30:58:589 ==>> 此处延时了:【500】毫秒
2025-07-31 20:30:58:599 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:30:58:611 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:30:58:651 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:30:58:871 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:30:58:877 ==>> 该项需要延时执行
2025-07-31 20:30:59:328 ==>> [D][05:19:08][COMM]------------ready to Power on Acckey 1------------
[D][05:19:08][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:08][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:08][COMM]----- get Acckey 1 and value:1------------
[W][05:19:08][COMM]CAN START!
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:08][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 79230
[D][05:19:08][COMM][Audio]exec status ready.
[D][05:19:08][CAT

2025-07-31 20:30:59:373 ==>> 此处延时了:【500】毫秒
2025-07-31 20:30:59:383 ==>> 检测【进入休眠模式2】
2025-07-31 20:30:59:407 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:30:59:418 ==>> 1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:19:08][COMM]imu wakeup ok. 79245
[D][05:19:08][COMM]imu wakeup 1
[W][05:19:08][COMM]wake up system, wakeupEvt=0x80
[D][05:19:08][COMM]frm_can_weigth_power_set 1
[D][05:19:08][COMM]Clear Sleep Block Evt
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:30:59:585 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:08][COMM]1x1 rx timeout
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:30:59:690 ==>>              [COMM]msg 02A0 loss. last_tick:79214. cur_tick:79724. period:50
[D][05:19:08][COMM]msg 02A4 loss. last_tick:79214. cur_tick:79725. period:50
[D][05:19:08][COMM]msg 02A5 loss. last_tick:79215. cur_tick:79725. period:50
[D][05:19:08][COMM]msg 02A6 loss. last_tick:79215. cur_tick:7

2025-07-31 20:30:59:735 ==>> 9726. period:50
[D][05:19:08][COMM]msg 02A7 loss. last_tick:79215. cur_tick:79726. period:50
[D][05:19:08][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 79726
[D][05:19:08][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 79727


2025-07-31 20:31:00:056 ==>> [E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[D][05:19:09][COMM]Main Task receive event:28 finished processing
[D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:31:00:376 ==>> [D][05:19:09][COMM]msg 0220 loss. last_tick:79214. cur_tick:80221. period:100
[D][05:19:09][COMM]msg 0221 loss. last_tick:79214. cur_tick:80221. period:100
[D][05:19:09][COMM]msg 0224 loss. last_tick:79214. cur_tick:80222. period:100
[D][05:19:09][COMM]msg 0260 loss. last_tick:79214. cur_tick:80222. period:100
[D][05:19:09][COMM]msg 0280 loss. last_tick:79214. cur_tick:80223. period:100
[D][05:19:09][COMM]msg 02C0 loss. last_tick:79214. cur_tick:80223. period:100
[D][05:19:09][COMM]msg 02C1 loss. last_tick:79214. cur_tick:80223. period:100
[D][05:19:09][COMM]msg 02C2 loss. last_tick:79214. cur_tick:80224. period:100
[D][05:19:09][COMM]msg 02E0 loss. last_tick:79215. cur_tick:80224. period:100
[D][05:19:09][COMM]msg 02E1 loss. last_tick:79215. cur_tick:80224. period:100
[D][05:19:09][COMM]msg 02E2 loss. last_tick:79215. cur_tick:80225. period:100
[D][05:19:09][COMM]msg 0300 loss. last_tick:79215. cur_tick:80225. period:100
[D][05:19:09][COMM]msg 0301 loss. last_tick:79215. cur_tick:80225. period:100
[D][05:19:09][COMM]bat msg 0240 loss. last_tick:79215. cur_tick:80226. period:100. j,i:1 54
[D][05:19:09][COMM]bat msg 0241 loss. last_tick:79215. cur_tick:80226. period:100. j,i:2 55
[D][05:19:09][COMM]bat msg 0242 loss. last_tick:79215. cur_tick:80227.

2025-07-31 20:31:00:466 ==>>  period:100. j,i:3 56
[D][05:19:09][COMM]bat msg 0244 loss. last_tick:79215. cur_tick:80227. period:100. j,i:5 58
[D][05:19:09][COMM]bat msg 024E loss. last_tick:79215. cur_tick:80228. period:100. j,i:15 68
[D][05:19:09][COMM]bat msg 024F loss. last_tick:79215. cur_tick:80228. period:100. j,i:16 69
[D][05:19:09][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 80228
[D][05:19:09][COMM]CAN message bat fault change: 0x00000000->0x0001802E 80229
[D][05:19:09][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 80229
                                                                              

2025-07-31 20:31:00:740 ==>> [D][05:19:09][COMM]msg 0222 loss. last_tick:79214. cur_tick:80724. period:150
[D][05:19:09][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 80725
[D][05:19:09][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:09][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:09][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:31:00:845 ==>> [

2025-07-31 20:31:00:935 ==>> E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[W][05:19:09][COMM]CAN STOP!
[D][05:19:09][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:09][COMM]------------ready to Power off Acckey 1------------
[D][05:19:09][COMM]------------ready to Power off Acckey 2------------
[D][05:19:09][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:09][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 112
[D][05:19:09][COMM]bat sleep fail, reason:-1
[D][05:19:09][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:09][COMM]accel parse set 0
[D][05:19:09][COMM]imu rest ok. 80904
[D][05:19:09][COMM]imu sleep 0
[W][05:19:09][COMM]now sleep


2025-07-31 20:31:01:224 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:31:01:231 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:31:01:242 ==>> 开始小电池电流采样
2025-07-31 20:31:01:263 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:31:01:333 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:31:02:345 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:31:02:392 ==>> CurrentBattery:ƽ��:68.09

2025-07-31 20:31:02:849 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:31:02:856 ==>> 【检测小电池休眠电流】通过,【68.09uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:31:02:862 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:31:02:876 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:31:02:954 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:31:03:143 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:31:03:152 ==>> 该项需要延时执行
2025-07-31 20:31:03:194 ==>> [D][05:19:12][COMM]------------ready to Power on Acckey 1------------
[D][05:19:12][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:12][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:12][COMM]----- get Acckey 1 and value:1------------
[W][05:19:12][COMM]CAN START!
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:12][COMM]CAN message bat fault change: 0x0001802E->0x00000000 83118
[D][05:19:12][COMM][Audio]exec status ready.
[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]imu wakeup ok. 83132
[D][05:19:12][COMM]imu wakeup 1
[D][05:19:12][HSDK][0] flush to flash addr:[0xE45100] --- write len --- [256]
[W][05:19:12][COMM]wake up system, wakeupEvt=0x80
[D][05:19:12][COMM]frm_can_weigth_power_set 1
[D][05:19:12][COMM]Clear Sleep Block Evt
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok
[D][05:19:12][COMM]read battery soc:0


2025-07-31 20:31:03:454 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:31:03:559 ==>> [D][05:19:12][COMM]msg 02A0 loss. last_tick:83100. cur_tick:83611. period:50
[D][05:19:12][COMM]msg 02A4 loss. last_tick:83100. cur_tick:83612. period:50
[D][05:19:

2025-07-31 20:31:03:619 ==>> 12][COMM]msg 02A5 loss. last_tick:83100. cur_tick:83612. period:50
[D][05:19:12][COMM]msg 02A6 loss. last_tick:83100. cur_tick:83613. period:50
[D][05:19:12][COMM]msg 02A7 loss. last_tick:83100. cur_tick:83613. period:50
[D][05:19:12][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 83613
[D][05:19:12][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 83614


2025-07-31 20:31:03:649 ==>> 此处延时了:【500】毫秒
2025-07-31 20:31:03:661 ==>> 检测【检测唤醒】
2025-07-31 20:31:03:684 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:31:04:414 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:12][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:12][FCTY]==========Modules-nRF5340 ==========
[D][05:19:12][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:12][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:12][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:12][FCTY]DeviceID    = 460130071541421
[D][05:19:12][FCTY]HardwareID  = 867222087966562
[D][05:19:12][FCTY]MoBikeID    = 9999999999
[D][05:19:12][FCTY]LockID      = FFFFFFFFFF
[D][05:19:12][FCTY]BLEFWVersion= 105
[D][05:19:12][FCTY]BLEMacAddr   = DE505F0B0851
[D][05:19:12][FCTY]Bat         = 3684 mv
[D][05:19:12][FCTY]Current     = 0 ma
[D][05:19:12][FCTY]VBUS        = 2600 mv
[D][05:19:12][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:12][FCTY]Ext battery vol = 32, adc = 1303
[D][05:19:12][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 177 mv
[D][05:19:12][FCTY]Bike Type flag is invalied
[D][05:19:12][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:12][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:12][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:12][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:12][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:12][FCTY]CAT1_GNSS_VERSION = V3465b5b1


2025-07-31 20:31:04:458 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:31:04:476 ==>> 检测【关机】
2025-07-31 20:31:04:500 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:31:04:520 ==>> [D][05:19:12][FCTY]Bat1         = 3699 mv
[D][05:19:12][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:12][FCTY]==========Modules-nRF5340 ==========
[E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[E][05:19:12][COMM]1x1 error -3.
[D][05:19:12][COMM]Main Task receive event:28 finished processing
[D][05:19:12][COMM]Main Task receive event:65
[D][05:19:12][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:12][COMM]Main Task receive event:65 finished processing
[D][05:19:12][COMM]Main Task receive event:60
[D][05:19:12][COMM]smart_helmet_vol=255,255
[D][05:19:12][COMM]report elecbike
[W][05:19:12][PROT]remove success[1629955152],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:12][PROT]add success [1629955152],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:12][COMM]Main Task receive event:60 finished processing
[D][05:19:12][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:12][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:12][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:12][PROT]index:0
[D][05:19:12][PROT]is_send:1
[D][05:19:12][PROT]sequence_num:10
[D][05:19:12][PRO

2025-07-31 20:31:04:624 ==>> T]retry_timeout:0
[D][05:19:12][PROT]retry_times:3
[D][05:19:12][PROT]send_path:0x3
[D][05:19:12][PROT]msg_type:0x5d03
[D][05:19:12][PROT]===========================================================
[W][05:19:12][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955152]
[D][05:19:12][PROT]===========================================================
[D][05:19:12][PROT]Sending traceid[999999999990000B]
[D][05:19:12][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:12][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:12][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:12][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:12][SAL ]open socket ind id[4], rst[0]
[D][05:19:12][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:12][SAL ]Cellular task submsg id[8]
[D][05:19:12][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:12][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:12][CAT1]gsm read msg sub id: 8
[D][05:19:12][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:12][M2M

2025-07-31 20:31:04:729 ==>>  ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:12][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:13][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:13][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:13][CAT1]<<< 
+CME ERROR: 100

[D][05:19:13][COMM]msg 0220 loss. last_tick:83100. cur_tick:84108. period:100
[D][05:19:13][COMM]msg 0221 loss. last_tick:83100. cur_tick:84109. period:100
[D][05:19:13][COMM]msg 0224 loss. last_tick:83100. cur_tick:84109. period:100
[D][05:19:13][COMM]msg 0260 loss. last_tick:83100. cur_tick:84110. period:100
[D][05:19:13][COMM]msg 0280 loss. last_tick:83100. cur_tick:84110. period:100
[D][05:19:13][COMM]msg 02C0 loss. last_tick:83100. cur_tick:84110. period:100
[D][05:19:13][COMM]msg 02C1 loss. last_tick:83100. cur_tick:84111. period:100
[D][05:19:13][COMM]msg 02C2 loss. last_tick:83100. cur_tick:84111. period:100
[D][05:19:13][COMM]msg 02E0 loss. last_tick:83100. cur_tick:84111. period:100
[D][05:19:13][COMM]msg 02E1 loss. last_tick:83100. cur_tick:84111. period:1

2025-07-31 20:31:04:834 ==>> 00
[D][05:19:13][COMM]msg 02E2 loss. last_tick:83100. cur_tick:84112. period:100
[D][05:19:13][COMM]msg 0300 loss. last_tick:83100. cur_tick:84112. period:100
[D][05:19:13][COMM]msg 0301 loss. last_tick:83100. cur_tick:84112. period:100
[D][05:19:13][COMM]bat msg 0240 loss. last_tick:83100. cur_tick:84113. period:100. j,i:1 54
[D][05:19:13][COMM]bat msg 0241 loss. last_tick:83100. cur_tick:84113. period:100. j,i:2 55
[D][05:19:13][COMM]bat msg 0242 loss. last_tick:83100. cur_tick:84114. period:100. j,i:3 56
[D][05:19:13][COMM]bat msg 0244 loss. last_tick:83100. cur_tick:84114. period:100. j,i:5 58
[D][05:19:13][COMM]bat msg 024E loss. last_tick:83100. cur_tick:84115. period:100. j,i:15 68
[D][05:19:13][COMM]bat msg 024F loss. last_tick:83100. cur_tick:84115. period:100. j,i:16 69
[D][05:19:13][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 84116
[D][05:19:13][COMM]CAN message bat fault change: 0x00000000->0x0001802E 84116
[D][05:19:13][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 84116


2025-07-31 20:31:05:431 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 20:31:05:491 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:31:05:536 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:31:05:641 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     UDIODATAHEX=0,10800,0

[D][05:19:13][COMM]Main Task receive event:60
[D][05:19:13][COMM]smart_helmet_vol=255,255
[D][05:19:13][COMM]BAT CAN get state1 Fail 204
[D][05:19:13][COMM]BAT CAN get soc Fail, 204
[D][05:19:13][COMM]BAT CAN get state2 fail 204
[D][05:19:13][COMM]get soh error
[E][05:19:13][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:13][COMM]report elecbike
[W][05:19:13][PROT]remove success[1629955153],send_pat

2025-07-31 20:31:05:746 ==>> h[3],type[0000],priority[0],index[1],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:13][COMM]Main Task receive event:60 finished processing
[D][05:19:13][COMM]Main Task receive event:61
[D][05:19:13][COMM][D301]:type:3, trace id:280
[D][05:19:13][COMM]id[], hw[000
[D][05:19:13][COMM]get mcMaincircuitVolt error
[D][05:19:13][COMM]get mcSubcircuitVolt error
[D][05:19:13][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:13][COMM]BAT CAN get state1 Fail 204
[D][05:19:13][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:13][COMM]BAT CAN get soc Fail, 204
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:13][PROT]index:1
[D][05:19:13][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:13][PROT]is_send:1
[D][05:19:13][PROT]sequence_num:11
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:3
[D][05:19:13][PROT]send_path:0x3
[D][05:19:13][PROT]msg_type:0x5d03
[D][05:19:13][PROT]====================================

2025-07-31 20:31:05:851 ==>> =======================
[W][05:19:13][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][PROT]Sending traceid[999999999990000C]
[D][05:19:13][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:13][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:13][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:13][COMM]Receive Bat Lock cmd 0
[D][05:19:13][COMM]VBUS is 1
[D][05:19:13][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:13][COMM]BAT CAN get state2 fail 204
[D][05:19:13][COMM]get bat work mode err
[W][05:19:13][PROT]remove success[1629955153],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:13][M2M ]m2m_task: con

2025-07-31 20:31:05:956 ==>> trol_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:13][COMM]Main Task receive event:61 finished processing
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:13][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:13][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:13][COMM]arm_hub_enable: hub power: 0
[D][05:19:13][HSDK]hexlog index save 0 16896 194 @ 0 : 0
[D][05:19:14][HSDK]write save hexlog index [0]
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:14][COMM]read battery soc:255
[D][05:19:14][CO

2025-07-31 20:31:06:031 ==>> MM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 20:31:06:136 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:15][COMM]arm_hub_enable: hub power: 0
[D][05:19:15][HSDK]hexlog index save 0 16896 194 @ 0 : 0
[D][05:19:15][HSDK]write save hexlog index [0]
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to wri

2025-07-31 20:31:06:166 ==>> te para flash


2025-07-31 20:31:06:515 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:31:06:591 ==>> [W][05:19:15][COMM]Power Off


2025-07-31 20:31:06:786 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:15][COMM]arm_hub_enable: hub power: 0
[D][05:19:15][HSDK]hexlog index save 0 16896 194 @ 0 : 0
[D][05:19:15][HSDK]write save hexlog index [0]
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:31:06:800 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:31:06:833 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:31:06:840 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:31:06:853 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:31:06:951 ==>> [D][05:19:16][FCTY]get_ext_48v_vol retry i = 0,volt = 16
[D][05:19:16][FCT

2025-07-31 20:31:07:011 ==>> Y]get_ext_48v_vol retry i = 1,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 2,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 3,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 4,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 5,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 6,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 7,volt = 16
[D][05:19:16][FCTY]get_ext_48v_vol retry i = 8,volt = 16


2025-07-31 20:31:07:075 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:31:07:082 ==>> 检测【检测小电池关机电流】
2025-07-31 20:31:07:093 ==>> 开始小电池电流采样
2025-07-31 20:31:07:107 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:31:07:176 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:31:08:181 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:31:08:256 ==>> CurrentBattery:ƽ��:68.08

2025-07-31 20:31:08:683 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:31:08:691 ==>> 【检测小电池关机电流】通过,【68.08uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:31:08:998 ==>> MES过站成功
2025-07-31 20:31:09:007 ==>> #################### 【测试结束】 ####################
2025-07-31 20:31:09:071 ==>> 关闭5V供电
2025-07-31 20:31:09:083 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:31:09:153 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:31:10:072 ==>> 关闭5V供电成功
2025-07-31 20:31:10:083 ==>> 关闭33V供电
2025-07-31 20:31:10:108 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:31:10:147 ==>> 5A A5 02 5A A5 


2025-07-31 20:31:10:252 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:31:11:082 ==>> 关闭33V供电成功
2025-07-31 20:31:11:094 ==>> 关闭3.7V供电
2025-07-31 20:31:11:101 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:31:11:144 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:31:11:945 ==>>  

2025-07-31 20:31:12:096 ==>> 关闭3.7V供电成功
