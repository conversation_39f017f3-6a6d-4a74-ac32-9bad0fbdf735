2025-07-31 20:36:09:674 ==>> MES查站成功:
查站序号:P51000100531360E验证通过
2025-07-31 20:36:09:682 ==>> 扫码结果:P51000100531360E
2025-07-31 20:36:09:684 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:36:09:686 ==>> 测试参数版本:2024.10.11
2025-07-31 20:36:09:688 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:36:09:689 ==>> 检测【打开透传】
2025-07-31 20:36:09:692 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:36:09:760 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:36:10:044 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:36:10:049 ==>> 检测【检测接地电压】
2025-07-31 20:36:10:052 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:36:10:155 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:36:10:329 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:36:10:331 ==>> 检测【打开小电池】
2025-07-31 20:36:10:334 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:36:10:460 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:36:10:601 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:36:10:604 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:36:10:607 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:36:10:656 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:36:10:871 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:36:10:874 ==>> 检测【等待设备启动】
2025-07-31 20:36:10:877 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:11:914 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:12:950 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:13:996 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:14:848 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:15:028 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:15:030 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:36:15:652 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:15:818 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:36:16:060 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:16:348 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:16:513 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 20:36:17:051 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:17:096 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:17:217 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:36:17:747 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:17:914 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 20:36:18:128 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:18:448 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:18:615 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 20:36:19:144 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:19:159 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:19:310 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti

2025-07-31 20:36:19:855 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:20:020 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 20:36:20:186 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:36:20:552 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:36:20:717 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Ti


2025-07-31 20:36:21:220 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:36:21:223 ==>> #################### 【测试结束】 ####################
2025-07-31 20:36:21:328 ==>> 关闭5V供电
2025-07-31 20:36:21:331 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:36:21:447 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:36:22:336 ==>> 关闭5V供电成功
2025-07-31 20:36:22:339 ==>> 关闭33V供电
2025-07-31 20:36:22:367 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:36:22:458 ==>> 5A A5 02 5A A5 


2025-07-31 20:36:22:548 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:36:23:337 ==>> 关闭33V供电成功
2025-07-31 20:36:23:340 ==>> 关闭3.7V供电
2025-07-31 20:36:23:342 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:36:23:459 ==>> 6A A6 02 A6 6A 


2025-07-31 20:36:23:549 ==>> Battery OFF
OVER 150


2025-07-31 20:36:23:654 ==>>  

