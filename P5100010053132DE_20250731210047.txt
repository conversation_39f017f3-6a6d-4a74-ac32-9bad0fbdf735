2025-07-31 21:00:47:316 ==>> MES查站成功:
查站序号:P5100010053132DE验证通过
2025-07-31 21:00:47:328 ==>> 扫码结果:P5100010053132DE
2025-07-31 21:00:47:330 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:00:47:332 ==>> 测试参数版本:2024.10.11
2025-07-31 21:00:47:333 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:00:47:334 ==>> 检测【打开透传】
2025-07-31 21:00:47:336 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:00:47:468 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:00:47:779 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:00:47:806 ==>> 检测【检测接地电压】
2025-07-31 21:00:47:809 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:00:47:866 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:00:48:105 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:00:48:107 ==>> 检测【打开小电池】
2025-07-31 21:00:48:110 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:00:48:170 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:00:48:402 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:00:48:404 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:00:48:408 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:00:48:460 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:00:48:690 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:00:48:693 ==>> 检测【等待设备启动】
2025-07-31 21:00:48:695 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:00:48:915 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:00:49:112 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:00:49:721 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:00:49:767 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:00:49:827 ==>>                                                    

2025-07-31 21:00:50:225 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:00:50:699 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:00:50:787 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:00:50:790 ==>> 检测【产品通信】
2025-07-31 21:00:50:791 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:00:50:926 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:00:51:084 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:00:51:086 ==>> 检测【初始化完成检测】
2025-07-31 21:00:51:088 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:00:51:288 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:00:51:348 ==>>                                                              

2025-07-31 21:00:51:398 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:00:51:401 ==>> 检测【关闭大灯控制1】
2025-07-31 21:00:51:403 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:00:51:543 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:00:51:695 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:00:51:698 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:00:51:700 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:00:51:754 ==>> [D][05:17:51][COMM]2646 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:51:859 ==>> [W

2025-07-31 21:00:51:934 ==>> ][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:00:51:986 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:00:51:988 ==>> 检测【关闭仪表供电】
2025-07-31 21:00:51:990 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:00:52:162 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:00:52:270 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:00:52:272 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:00:52:274 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:00:52:432 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:00:52:556 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:00:52:559 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:00:52:561 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:00:52:779 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3657 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:52:840 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:00:52:842 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:00:52:843 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:00:53:049 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:00:53:125 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:00:53:128 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:00:53:140 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:00:53:259 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:00:53:349 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:53][COMM]read battery soc:255


2025-07-31 21:00:53:410 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:00:53:412 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:00:53:413 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:00:53:454 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:00:53:700 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:00:53:703 ==>> 该项需要延时执行
2025-07-31 21:00:53:795 ==>> [D][05:17:53][COMM]4670 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:54:292 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5011. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 21:00:54:794 ==>> [D][05:17:54][COMM]5681 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:54:898 ==>> [

2025-07-31 21:00:54:943 ==>> D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:00:55:448 ==>>                                                  type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021]

2025-07-31 21:00:55:553 ==>> .play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:5

2025-07-31 21:00:55:658 ==>> 4][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]

2025-07-31 21:00:55:718 ==>> get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:00:55:808 ==>> [D][05:17:55][COMM]6692 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:56:006 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 21:00:56:808 ==>> [D][05:17:56][COMM]7703 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:57:358 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:00:57:712 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:00:57:715 ==>> 检测【33V输入电压ADC】
2025-07-31 21:00:57:718 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:00:57:988 ==>> [D][05:17:57][COMM]8714 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:17:57][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:57][COMM]adc read left brake adc:7  volt:9 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:17:57][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:00:58:269 ==>> 【33V输入电压ADC】通过,【32729mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:00:58:271 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:00:58:273 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:00:58:367 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1670mV
Get AD_V4 0mV
Get AD_V5 2756mV
Get AD_V6 1989mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:00:58:600 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:58:603 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:00:58:634 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1670mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:58:636 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:00:58:638 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:00:58:667 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:00:58:669 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:00:58:701 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:00:58:704 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:00:58:742 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:00:58:744 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:00:58:827 ==>> [D][05:17:58][COMM]9724 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:00:58:872 ==>> 1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1671mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1988mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:00:59:035 ==>> 【TP7_VCC3V3(ADV2)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:59:037 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:00:59:070 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:59:091 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:00:59:093 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:00:59:117 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:00:59:123 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:00:59:171 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:00:59:174 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:00:59:178 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018


2025-07-31 21:00:59:219 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:00:59:221 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:00:59:373 ==>> [D][05:17:59][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1671mV
Get AD_V4 2mV
Get AD_V5 2756mV
Get AD_V6 1988mV
Get AD_V7 1091mV
OVER 150


2025-07-31 21:00:59:517 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:59:520 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:00:59:554 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:00:59:556 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:00:59:558 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:00:59:591 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:00:59:593 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:00:59:628 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:00:59:633 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:00:59:669 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:00:59:671 ==>> 检测【打开WIFI(1)】
2025-07-31 21:00:59:690 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:01:00:206 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10736 imu init OK
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read ms

2025-07-31 21:01:00:311 ==>> g sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17] 
+MT ER

2025-07-31 21:01:00:356 ==>> ROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 21:01:00:478 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:01:00:509 ==>> 检测【清空消息队列(1)】
2025-07-31 21:01:00:511 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:01:00:656 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:01:00:767 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:01:00:770 ==>> 检测【打开GPS(1)】
2025-07-31 21:01:00:772 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:01:00:837 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:01:00:942 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:01:01:064 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:01:01:067 ==>> 检测【打开GSM联网】
2025-07-31 21:01:01:069 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:01:01:244 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:01:01:351 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:01:01:354 ==>> 检测【打开仪表供电1】
2025-07-31 21:01:01:356 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:01:01:380 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 21:01:01:561 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:01:01:641 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:01:01:644 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:01:01:648 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:01:01:863 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:01:01:929 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:01:01:932 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:01:01:934 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:01:01:954 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 21:01:02:541 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33015]
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087567105

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539069

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:01:02:733 ==>> 【读取主控ADC采集的仪表电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:01:02:736 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:01:02:738 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:02:956 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:01:03:022 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:01:03:027 ==>> 检测【AD_V20电压】
2025-07-31 21:01:03:031 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:03:123 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:03:403 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255


2025-07-31 21:01:03:617 ==>> 本次取值间隔时间:493ms
2025-07-31 21:01:03:709 ==>> 本次取值间隔时间:78ms
2025-07-31 21:01:03:818 ==>> 本次取值间隔时间:106ms
2025-07-31 21:01:03:848 ==>> [D][05:18:03][COMM]14747 imu init OK


2025-07-31 21:01:03:908 ==>> 本次取值间隔时间:81ms
2025-07-31 21:01:03:912 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:04:015 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:04:062 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:01:04:166 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = ?<<<<<
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:01:04:331 ==>> 本次取值间隔时间:303ms
2025-07-31 21:01:04:368 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:04:482 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:04:572 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:01:04:677 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[99

2025-07-31 21:01:04:737 ==>> 99]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK


2025-07-31 21:01:04:782 ==>> 本次取值间隔时间:290ms
2025-07-31 21:01:04:814 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:04:842 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 21:01:04:917 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:05:112 ==>>                                                                                                                                                                                                                                                                                                                                                      [D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:01:05:327 ==>> 本次取值间隔时间:405ms
2025-07-31 21:01:05:370 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:05:418 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 21:01:05:478 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:05:523 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY

2025-07-31 21:01:05:568 ==>> =TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:01:05:735 ==>> 本次取值间隔时间:250ms
2025-07-31 21:01:05:770 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:01:05:812 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:01:05:872 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:01:05:962 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1663mV
OVER 150


2025-07-31 21:01:06:052 ==>> 本次取值间隔时间:176ms
2025-07-31 21:01:06:088 ==>> 【AD_V20电压】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:01:06:091 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:01:06:094 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:01:06:157 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:01:06:392 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:01:06:396 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:01:06:400 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:01:06:474 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:01:06:729 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,33,,,40,24,,,39,41,,,36,14,,,40,1*79

$GBGSV,2,2,05,25,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1589.173,1589.173,50.777,2097152,2097152,2097152*4D

[D][05:18:06][CAT1]<<< 
OK

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:06][CAT1]opened : 0, 0
[D][05:18:06][SAL ]Cellular task submsg id[68]
[D][05:18:06][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:06][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:06][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:06][M2M ]g_m2m_is_idle become true
[D][05:18:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]su

2025-07-31 21:01:06:759 ==>> b id: 23, ret: 6



2025-07-31 21:01:06:864 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:01:06:970 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:01:06:973 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:01:06:976 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:01:07:063 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:01:07:287 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:01:07:291 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:01:07:296 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:01:07:470 ==>> [D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:01:07:575 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,40,24,,,39,60,,,39,41,,,37,1*71

$GBGSV,3,2,11,59,,,36,39,,,36,14,,,35,25,,,35,1*73

$GBGSV,3,3,11,40,,,35,1,,,34,38,,,33,1*49

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1503.771,1503.771,48.074,2097152,2097152,2097152*40



2025-07-31 21:01:07:599 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:01:07:604 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:01:07:608 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:01:07:772 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:01:07:920 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:01:07:925 ==>> 检测【AD_V21电压】
2025-07-31 21:01:07:929 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:01:08:058 ==>> 1A A1 20 00 00 
Get AD_V21 1026mV
OVER 150


2025-07-31 21:01:08:133 ==>> 本次取值间隔时间:210ms
2025-07-31 21:01:08:192 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:01:08:269 ==>> 本次取值间隔时间:64ms
2025-07-31 21:01:08:274 ==>> 1A A1 20 00 00 
Get AD_V21 1659mV
OVER 150


2025-07-31 21:01:08:436 ==>> 本次取值间隔时间:166ms
2025-07-31 21:01:08:490 ==>> 【AD_V21电压】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:01:08:493 ==>> 检测【关闭仪表供电2】
2025-07-31 21:01:08:497 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:01:08:696 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,41,3,,,40,24,,,39,60,,,39,1*46

$GBGSV,4,2,16,59,,,38,41,,,37,39,,,37,25,,,37,1*7C

$GBGSV,4,3,16,40,,,36,14,,,35,1,,,35,38,,,33,1*48

$GBGSV,4,4,16,2,,,33,34,,,33,4,,,32,5,,,31,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1492.504,1492.504,47.753,2097152,2097152,2097152*4D

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:01:08:785 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:01:08:788 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:01:08:792 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:01:08:956 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:01:09:077 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:01:09:081 ==>> 检测【打开AccKey2供电】
2025-07-31 21:01:09:093 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:01:09:229 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:01:09:367 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:01:09:373 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:01:09:377 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:09:427 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:01:09:732 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:09][COMM]adc read out 24v adc:1324  volt:33487 mv
[D][05:18:09][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:09][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:09][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:09][COMM]adc read battery ts volt:9 mv
[D][05:18:09][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:09][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
$GBGGA,130113.458,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,41,24,,,40,3,,,39,60,,,39,1*49

$GBGSV,5,2,18,59,,,38,39,,,38,25,,,38,41,,,37,1*73

$GBGSV,5,3,18,16,,,37,40,,,36,14,,,35,1,,,35,1*4F

$GBGSV,5,4,18,38,,,33,2,,,33,34,,,33,4,,,32,1*75

$GBGSV,5,5,18,5,,,31,12,,,38,1*40

$GBRMC,130113.458,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130113.458,0.000,1499.819,1499.819,47.985,2097152,2097152,2097152*5E

[D][05:18:09][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:09][COMM]arm_hub adc r

2025-07-31 21:01:09:762 ==>> ead front lamp adc:6  volt:139 mv


2025-07-31 21:01:09:914 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33487mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:01:09:918 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:01:09:922 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:01:10:021 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:01:10:215 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:01:10:218 ==>> 该项需要延时执行
2025-07-31 21:01:10:633 ==>> $GBGGA,130114.438,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,24,,,40,3,,,40,60,,,40,1*45

$GBGSV,6,2,24,59,,,39,25,,,39,39,,,38,41,,,38,1*70

$GBGSV,6,3,24,40,,,37,16,,,36,14,,,36,1,,,35,1*40

$GBGSV,6,4,24,13,,,35,38,,,34,9,,,34,7,,,34,1*74

$GBGSV,6,5,24,6,,,34,2,,,33,34,,,33,44,,,33,1*77

$GBGSV,6,6,24,4,,,32,10,,,32,23,,,32,5,,,31,1*72

$GBRMC,130114.438,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130114.438,0.000,1478.689,1478.689,47.316,2097152,2097152,2097152*5F



2025-07-31 21:01:11:443 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:01:11:623 ==>> $GBGGA,130115.418,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,24,,,40,3,,,40,60,,,40,1*45

$GBGSV,6,2,24,59,,,39,25,,,39,39,,,38,41,,,38,1*70

$GBGSV,6,3,24,40,,,37,16,,,36,14,,,36,1,,,35,1*40

$GBGSV,6,4,24,7,,,35,13,,,34,38,,,34,9,,,34,1*74

$GBGSV,6,5,24,6,,,34,2,,,33,34,,,33,44,,,33,1*77

$GBGSV,6,6,24,10,,,32,23,,,32,4,,,31,5,,,30,1*70

$GBRMC,130115.418,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130115.418,0.000,1475.241,1475.241,47.212,2097152,2097152,2097152*59



2025-07-31 21:01:12:612 ==>> $GBGGA,130116.398,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,3,,,40,60,,,40,1*46

$GBGSV,7,2,25,59,,,39,25,,,39,39,,,38,41,,,38,1*70

$GBGSV,7,3,25,40,,,37,16,,,36,14,,,36,42,,,36,1*74

$GBGSV,7,4,25,1,,,35,7,,,35,9,,,35,6,,,35,1*7B

$GBGSV,7,5,25,13,,,34,38,,,34,34,,,33,44,,,33,1*7D

$GBGSV,7,6,25,2,,,32,10,,,32,23,,,32,4,,,32,1*76

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,130116.398,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130116.398,0.000,1482.560,1482.560,47.441,2097152,2097152,2097152*55



2025-07-31 21:01:12:792 ==>> $GBGGA,130116.598,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,40,3,,,40,60,,,40,1*46

$GBGSV,7,2,25,59,,,39,25,,,39,39,,,38,41,,,38,1*70

$GBGSV,7,3,25,40,,,37,16,,,36,14,,,36,42,,,36,1*74

$GBGSV,7,4,25,1,,,35,7,,,35,9,,,35,6,,,35,1*7B

$GBGSV,7,5,25,13,,,34,38,,,34,34,,,33,44,,,33,1*7D

$GBGSV,7,6,25,2,,,33,10,,,32,23,,,32,4,,,32,1*77

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,130116.598,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130116.598,0.000,1484.216,1484.216,47.491,2097152,2097152,2097152*5E



2025-07-31 21:01:13:220 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:01:13:224 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:01:13:229 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:13:572 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:13][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:13][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:13][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:13][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:13][COMM]adc read battery ts volt:10 mv
[D][05:18:13][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:13][COMM]read battery soc:255
[D][05:18:13][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:01:13:773 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:01:13:777 ==>> 检测【打开AccKey1供电】
2025-07-31 21:01:13:781 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:01:13:797 ==>> $GBGGA,130117.578,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,60,,,40,1*45

$GBGSV,7,2,26,25,,,40,59,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,34,,,33,44,,,33,1*7F

$GBGSV,7,6,26,2,,,33,23,,,33,10,,,32,4,,,31,1*76

$GBGSV,7,7,26,5,,,31,19,,,41,1*48

$GBRMC,130117.578,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130117.578,0.000,1489.194,1489.194,47.654,2097152,2097152,2097152*5A



2025-07-31 21:01:13:948 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:01:14:065 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:01:14:085 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:01:14:089 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:01:14:160 ==>> 1A A1 00 40 00 
Get AD_V14 2564mV
OVER 150


2025-07-31 21:01:14:325 ==>> 原始值:【2564】, 乘以分压基数【2】还原值:【5128】
2025-07-31 21:01:14:357 ==>> 【读取AccKey1电压(ADV14)前】通过,【5128mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:01:14:361 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:01:14:363 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:14:673 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:14][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:14][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:14][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:14][COMM]adc read battery ts volt:6 mv
[D][05:18:14][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:14][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:01:14:778 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 21:01:14:903 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5501mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:01:14:907 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:01:14:910 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:01:15:050 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:01:15:191 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:01:15:194 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:01:15:199 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:01:15:262 ==>> 1A A1 00 40 00 
Get AD_V14 2568mV
OVER 150


2025-07-31 21:01:15:443 ==>> 原始值:【2568】, 乘以分压基数【2】还原值:【5136】
2025-07-31 21:01:15:448 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:01:15:479 ==>> 【读取AccKey1电压(ADV14)后】通过,【5136mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:01:15:484 ==>> 检测【打开WIFI(2)】
2025-07-31 21:01:15:489 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:01:15:793 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

$GBGGA,130119.538,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,40,1*44

$GBGSV,7,2,26,60,,,40,59,,,39,39,,,39,41,,,38,1*7D

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6
$GBGSV,7,4,26,1,,,35,7,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,34,,,33,44,,,33,1*7F

$GBGSV,7,6,26,23,,,33,2,,,33,10,,,33,4,,,32,1*74

$GBGSV,7,7,26,8,,,32,5,,,31,1*7C

$GBRMC,130119.538,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130119.538,0.000,1486.130,1486.130,47.553,2097152,2097152,2097152*54



2025-07-31 21:01:16:029 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:01:16:033 ==>> 检测【转刹把供电】
2025-07-31 21:01:16:036 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:01:16:223 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:01:16:317 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:01:16:325 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:01:16:345 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:01:16:420 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:01:16:745 ==>> $GBGGA,130120.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,40,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,34,,,34,44,,,33,1*78

$GBGSV,7,6,26,23,,,33,2,,,33,10,,,33,4,,,32,1*74

$GBGSV,7,7,26,8,,,32,5,,,32,1*7F

$GBRMC,130120.518,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130120.518,0.000,1487.718,1487.718,47.597,2097152,2097152,2097152*54

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:01:17:354 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:01:17:462 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:01:17:467 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:01:17:567 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2398mV
OVER 150


2025-07-31 21:01:17:627 ==>> 原始值:【2398】, 乘以分压基数【2】还原值:【4796】
2025-07-31 21:01:17:661 ==>> 【读取AD_V15电压(前)】通过,【4796mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:01:17:667 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:01:17:674 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:01:17:692 ==>> $GBGGA,130121.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,39,1*4A

$GBGSV,7,2,26,60,,,39,59,,,39

2025-07-31 21:01:17:732 ==>> ,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,34,,,34,44,,,33,1*78

$GBGSV,7,6,26,2,,,33,10,,,33,23,,,32,4,,,31,1*76

$GBGSV,7,7,26,8,,,31,5,,,31,1*7F

$GBRMC,130121.518,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130121.518,0.000,1479.754,1479.754,47.351,2097152,2097152,2097152*59



2025-07-31 21:01:17:762 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:01:17:837 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:01:17:867 ==>> 1A A1 01 00 00 
Get AD_V16 2430mV
OVER 150


2025-07-31 21:01:17:927 ==>> 原始值:【2430】, 乘以分压基数【2】还原值:【4860】
2025-07-31 21:01:17:970 ==>> 【读取AD_V16电压(前)】通过,【4860mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:01:17:976 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:01:17:981 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:18:274 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:18][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:18][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:18][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:18][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:18][COMM]adc read battery ts volt:6 mv
[D][05:18:18][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:01:18:515 ==>> +WIFISCAN:4,0,CC057790A620,-57
+WIFISCAN:4,1,CC057790A621,-57
+WIFISCAN:4,2,F42A7D1297A3,-71
+WIFISCAN:4,3,44A1917CAD80,-83

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 21:01:18:535 ==>> 【转刹把供电电压(主控ADC)】通过,【5417mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:01:18:541 ==>> 检测【转刹把供电电压】
2025-07-31 21:01:18:548 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:18:890 ==>> $GBGGA,130122.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,3,,,40,25,,,39,1*4B

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,38,41,,,37,1*7C

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,36,1*76

$GBGSV,7,4,27,1,,,35,7,,,35,6,,,35,9,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,34,,,33,44,,,33,1*7F

$GBGSV,7,6,27,2,,,33,10,,,33,23,,,32,4,,,31,1*77

$GBGSV,7,7,27,8,,,31,5,,,31,28,,,36,1*71

$GBRMC,130122.518,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130122.518,0.000,1471.781,1471.781,47.096,2097152,2097152,2097152*52

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:18][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:18][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:18][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:18][COMM]adc read battery ts volt:4 mv
[D][05:18:18][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3077  volt:5408 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:18][COMM]arm_hub adc re

2025-07-31 21:01:18:950 ==>> ad led yb adc:1426  volt:33062 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                      

2025-07-31 21:01:19:092 ==>> 【转刹把供电电压】通过,【5408mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:01:19:096 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:01:19:101 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:01:19:223 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:01:19:378 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:01:19:383 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:01:19:387 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:01:19:464 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:01:19:479 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:01:19:569 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:01:19:632 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:01:19:636 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:01:19:641 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:01:19:674 ==>> $GBGGA,130123.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,39,1*4A

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73


2025-07-31 21:01:19:735 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:01:19:740 ==>> 

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,6,,,35,9,,,35,1*78

$GBGSV,7,5,26,38,,,34,13,,,34,34,,,33,44,,,33,1*7E

$GBGSV,7,6,26,2,,,33,10,,,33,23,,,32,4,,,32,1*75

$GBGSV,7,7,26,8,,,31,5,,,31,1*7F

$GBRMC,130123.518,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130123.518,0.000,1478.159,1478.159,47.300,2097152,2097152,2097152*5F



2025-07-31 21:01:19:839 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:01:19:945 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:01:19:960 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:01:20:020 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:01:20:050 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:01:20:066 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:01:20:155 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:01:20:260 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:01:20:296 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:01:20:299 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:01:20:303 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:01:20:365 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:01:20:595 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:01:20:598 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:01:20:602 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:01:20:729 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150
$GBGGA,130124.518,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,39,25,,,39,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,6,,,35,9,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,34,,,33,44,,,33,1*7F

$GBGSV,7,6,26,2,,,33,10,,,33,23,,,32,4,,,32,1*75

$GBGSV,7,7,26,8,,,32,5,,,31,1*7C

$GBRMC,130124.518,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130124.518,0.000,1479.747,1479.747,47.345,2097152,2097152,2097152*59



2025-07-31 21:01:20:885 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:01:20:890 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:01:20:894 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:01:20:969 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:01:21:168 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:01:21:174 ==>> 检测【左刹电压测试1】
2025-07-31 21:01:21:181 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:21:491 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:21][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:21][COMM]adc read left brake adc:1729  volt:2279 mv
[D][05:18:21][COMM]adc read right brake adc:1722  volt:2270 mv
[D][05:18:21][COMM]adc read throttle adc:1729  volt:2279 mv
[D][05:18:21][COMM]adc read battery ts volt:8 mv
[D][05:18:21][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:21][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
                                         

2025-07-31 21:01:21:720 ==>> 【左刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 21:01:21:725 ==>> 检测【右刹电压测试1】
2025-07-31 21:01:21:731 ==>> $GBGGA,130125.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,24,,,40,3,,,40,60,,,40,1*46

$GBGSV,7,2,26,25,,,39,59,,,39,39,,,39,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,42,,,36,1*77

$GBGSV,7,4,26,1,,,35,7,,,35,6,,,35,9,,,35,1*78

$GBGSV,7,5,26,38,,,34,13,,,34,2,,,34,34,,,33,1*4B

$GBGSV,7,6,26,44,,,33,10,,,33,23,,,32,4,,,32,1*47

$GBGSV,7,7,26,8,,,32,5,,,32,1*7F

$GBRMC,130125.518,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130125.518,0.000,1482.934,1482.934,47.444,2097152,2097152,2097152*5E



2025-07-31 21:01:21:758 ==>> 【右刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 21:01:21:761 ==>> 检测【转把电压测试1】
2025-07-31 21:01:21:792 ==>> 【转把电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 21:01:21:796 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:01:21:801 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:01:21:867 ==>> 3A A3 03 00 A3 


2025-07-31 21:01:21:957 ==>> OFF_OUT3
OVER 150


2025-07-31 21:01:22:146 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:01:22:150 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:01:22:153 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:01:22:257 ==>> 3A A3 04 00 A3 


2025-07-31 21:01:22:362 ==>> OFF_OUT4
OVER 150


2025-07-31 21:01:22:463 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:01:22:469 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:01:22:475 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:01:22:560 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:01:22:665 ==>> $GBGGA,130126.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,

2025-07-31 21:01:22:740 ==>> ,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,24,,,39,60,,,39,1*47

$GBGSV,7,2,27,25,,,39,59,,,39,39,,,39,41,,,37,1*7C

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,36,1*76

$GBGSV,7,4,27,1,,,35,7,,,35,6,,,34,9,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,34,,,33,1*4D

$GBGSV,7,6,27,44,,,33,10,,,33,23,,,32,8,,,32,1*4A

$GBGSV,7,7,27,4,,,31,5,,,31,12,,,29,1*7A

$GBRMC,130126.518,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130126.518,0.000,1460.269,1460.269,46.732,2097152,2097152,2097152*5E



2025-07-31 21:01:23:042 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:01:23:049 ==>> 检测【左刹电压测试2】
2025-07-31 21:01:23:055 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:01:23:369 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:23][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:23][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:23][COMM]adc read battery ts volt:8 mv
[D][05:18:23][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:01:23:474 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:01:23:600 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:01:23:604 ==>> 检测【右刹电压测试2】
2025-07-31 21:01:23:650 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:01:23:654 ==>> 检测【转把电压测试2】
2025-07-31 21:01:23:704 ==>> 【转把电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 21:01:23:708 ==>> 检测【晶振检测】
2025-07-31 21:01:23:715 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:01:23:729 ==>> $GBGGA,130127.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,24,,,39,60,,,39,1*47

$GBGSV,7,2,27,25,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,36,1*76

$GBGSV,7,4,27,1,,,35,7,,,35,6,,,34,9,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,34,34,,,33,1*4A

$GBGSV,7,6,27,44,,,33,10,,,33,23,,,32,8,,,32,1*4A

$GBGSV,7,7,27,4,,,31,5,,,31,12,,,30,1*72

$GBRMC,130127.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130127.518,0.000,1461.798,1461.798,46.775,2097152,2097152,2097152*5C



2025-07-31 21:01:23:834 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 21:01:23:990 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:01:23:994 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:01:23:999 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:01:24:079 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1671mV
Get AD_V4 1650mV
Get AD_V5 2756mV
Get AD_V6 1989mV
Get AD_V7 1091mV
OVER 150


2025-07-31 21:01:24:287 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:01:24:291 ==>> 检测【检测BootVer】
2025-07-31 21:01:24:296 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:24:627 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071539069
[D][05:18:24][FCTY]HardwareID  = 867222087567105
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = C25702076663
[D][05:18:24][FCTY]Bat         = 3944 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11700 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 32, adc = 1297
[D][05:18:24][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 25 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][0

2025-07-31 21:01:24:732 ==>> 5:18:24][FCTY]Bat1         = 3781 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 21:01:24:855 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:01:24:859 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:01:24:865 ==>> 检测【检测固件版本】
2025-07-31 21:01:24:888 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:01:24:892 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:01:24:897 ==>> 检测【检测蓝牙版本】
2025-07-31 21:01:24:933 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:01:24:938 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:01:24:944 ==>> 检测【检测MoBikeId】
2025-07-31 21:01:24:968 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:01:24:972 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:01:24:979 ==>> 检测【检测蓝牙地址】
2025-07-31 21:01:24:997 ==>> 取到目标值:C25702076663
2025-07-31 21:01:25:003 ==>> 【检测蓝牙地址】通过,【C25702076663】符合目标值【】要求!
2025-07-31 21:01:25:006 ==>> 提取到蓝牙地址:C25702076663
2025-07-31 21:01:25:027 ==>> 检测【BOARD_ID】
2025-07-31 21:01:25:036 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:01:25:040 ==>> 检测【检测充电电压】
2025-07-31 21:01:25:079 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:01:25:083 ==>> 检测【检测VBUS电压1】
2025-07-31 21:01:25:112 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:01:25:117 ==>> 检测【检测充电电流】
2025-07-31 21:01:25:145 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:01:25:150 ==>> 检测【检测IMEI】
2025-07-31 21:01:25:153 ==>> 取到目标值:867222087567105
2025-07-31 21:01:25:177 ==>> 【检测IMEI】通过,【867222087567105】符合目标值【】要求!
2025-07-31 21:01:25:181 ==>> 提取到IMEI:867222087567105
2025-07-31 21:01:25:188 ==>> 检测【检测IMSI】
2025-07-31 21:01:25:201 ==>> 取到目标值:460130071539069
2025-07-31 21:01:25:215 ==>> 【检测IMSI】通过,【460130071539069】符合目标值【】要求!
2025-07-31 21:01:25:219 ==>> 提取到IMSI:460130071539069
2025-07-31 21:01:25:224 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:01:25:230 ==>> 取到目标值:460130071539069
2025-07-31 21:01:25:248 ==>> 【校验网络运营商(移动)】通过,【460130071539069】符合目标值【】要求!
2025-07-31 21:01:25:251 ==>> 检测【打开CAN通信】
2025-07-31 21:01:25:255 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:01:25:367 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:01:25:472 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:01:25:535 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:01:25:539 ==>> 检测【检测CAN通信】
2025-07-31 21:01:25:543 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:01:25:669 ==>> can send success


2025-07-31 21:01:25:699 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:01:25:804 ==>> $GBGGA,130129.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,24,,,39,60,,,39,1*47

$GBGSV,7,2,27,25,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,35,1*75

$GBGSV,7,4,27,1,,,35,7,,,35,6,,,34,9,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,34,,,33,1*4D

$GBGSV,7,6,27,44,,,33,10,,,33,23,,,32,8,,,32,1*4A

$GBGSV,7,7,27,4,,,32,5,,,31,12,,,30,1*71

$GBRMC,130129.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130129.518,0.000,1460.261,1460.261,46.724,2097152,2097152,2097152*56

[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36645
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:01:25:826 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:01:25:832 ==>> 检测【关闭CAN通信】
2025-07-31 21:01:25:838 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:01:25:879 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:01:25:970 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:01:26:124 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:01:26:131 ==>> 检测【打印IMU STATE】
2025-07-31 21:01:26:137 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:01:26:353 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:01:26:419 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:01:26:426 ==>> 检测【六轴自检】
2025-07-31 21:01:26:432 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:01:26:764 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
$GBGGA,130130.518,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,39,24,,,39,60,,,39,1*49

$GBGSV,7,2,27,25,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,36,1*76

$GBGSV,7,4,27,1,,,35,7,,,35,6,,,34,9,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,34,,,33,1*4D

$GBGSV,7,6,27,44,,,33,10,,,32,23,,,32,8,,,32,1*4B

$GBGSV,7,7,27,4,,,32,5,,,31,12,,,30,1*71

[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBRMC,130130.518,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130130.518,0.000,1458.725,1458.725,46.675,2097152,2097152,2097152*5B



2025-07-31 21:01:27:480 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:01:27:752 ==>> $GBGGA,130131.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,39,24,,,39,60,,,39,1*49

$GBGSV,7,2,27,25,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,36,16,,,36,14,,,35,42,,,35,1*77

$GBGSV,7,4,27,1,,,35,7,,,34,6,,,34,9,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,34,,,33,1*4D

$GBGSV,7,6,27,44,,,33,10,,,32,4,,,32,23,,,31,1*44

$GBGSV,7,7,27,8,,,31,5,,,30,12,,,30,1*7F

$GBRMC,130131.518,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130131.518,0.000,722.645,722.645,660.879,2097152,2097152,2097152*6A



2025-07-31 21:01:28:374 ==>> [D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:01:28:479 ==>> [D][05:18:28]

2025-07-31 21:01:28:524 ==>> [COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39399 imu self test OK ######
[D][05:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-12,-4,4092]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 21:01:28:734 ==>> $GBGGA,130132.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,39,3,,,39,59,,,39,1*43

$GBGSV,7,2,27,24,,,39,25,,,39,39,,,38,41,,,37,1*77

$GBGSV,7,3,27,40,,,36,1,,,35,16,,,35,42,,,35,1*40

$GBGSV,7,4,27,14,,,35,7,,,34,13,,,34,38,,,34,1*4A

$GBGSV,7,5,27,9,,,34,6,,,34,2,,,33,44,,,33,1*4C

$GBGSV,7,6,27,34,,,33,10,,,32,4,,,32,8,,,31,1*7A

$GBGSV,7,7,27,5,,,31,23,,,31,12,,,30,1*47

$GBRMC,130132.518,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130132.518,0.000,722.642,722.642,660.876,2097152,2097152,2097152*66



2025-07-31 21:01:28:767 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:01:28:776 ==>> 检测【打印IMU STATE2】
2025-07-31 21:01:28:783 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:01:28:962 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:01:29:052 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:01:29:056 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:01:29:061 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:01:29:157 ==>> 5A A5 02 5A A5 


2025-07-31 21:01:29:262 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:01:29:336 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:01:29:341 ==>> 检测【检测VBUS电压2】
2025-07-31 21:01:29:347 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:29:367 ==>> [D][05:18:29][FCTY]get_ext_48v_vol retry i = 0,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i

2025-07-31 21:01:29:427 ==>>  = 1,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 2,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 3,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 4,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 5,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 6,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 7,volt = 17
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 8,volt = 17


2025-07-31 21:01:29:793 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539069
[D][05:18:29][FCTY]HardwareID  = 867222087567105
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C25702076663
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 8, adc = 342
[D][05:18:29][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 50 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3781 mv
[D][05:18:29][FCTY]==========

2025-07-31 21:01:29:882 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:29:891 ==>> ========== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGGA,130133.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,39,3,,,39,59,,,39,1*43

$GBGSV,7,2,27,24,,,39,25,,,39,39,,,38,40,,,37,1*76

$GBGSV,7,3,27,41,,,37,1,,,35,16,,,35,42,,,35,1*40

$GBGSV,7,4,27,14,,,35,7,,,34,13,,,34,38,,,34,1*4A

$GBGSV,7,5,27,9,,,34,6,,,34,2,,,33,10,,,32,1*4C

$GBGSV,7,6,27,44,,,32,4,,,32,34,,,32,8,,,31,1*7A

$GBGSV,7,7,27,12,,,31,23,,,31,5,,,30,1*47

$GBRMC,130133.518,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130133.518,0.000,721.880,721.880,660.179,2097152,2097152,2097152*61



2025-07-31 21:01:30:228 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539069
[D][05:18:29][FCTY]HardwareID  = 867222087567105
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C25702076663
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 100 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 182
[D][05:18:29][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29

2025-07-31 21:01:30:258 ==>> ][FCTY]Bat1         = 3781 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:01:30:427 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:30:910 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539069
[D][05:18:30][FCTY]HardwareID  = 867222087567105
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = C25702076663
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 100 ma
[D][05:18:30][FCTY]VBUS        = 11800 mv
$GBGGA,130134.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,60,,,39,3,,,39,59,,,39,1*43

$GBGSV,7,2,27,24,,,39,25,,,39,39,,,38,41,,,38,1*78

$GBGSV,7,3,27,40,,,37,14,,,36,7,,,35,1,,,35,1*71

$GBGSV,7,4,27,16,,,35,42,,,35,13,,,34,38,,,34,1*78

$GBGSV,7,5,27,9,,,34,6,,,34,2,,,33,44,,,33,1*4C

$GBGSV,7,6,27,34,,,33,10,,,32,8,,,32,4,,,32,1*79

$GBGSV,7,7,27,23,,,32,5,,,31,12,,,31,1*45

$GBRMC,130134.518,V

2025-07-31 21:01:31:015 ==>> ,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130134.518,0.000,728.000,728.000,665.776,2097152,2097152,2097152*6A

[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 146
[D][05:18:30][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 75 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3781 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]msg 0601 loss. last_tick:36627. cur_tick:41643. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41643


2025-07-31 21:01:31:226 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:31:743 ==>> [D][05:18:31][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[W][05:18:31][GNSS]stop locating
[D][05:18:31][GNSS]stop event:8
[D][05:18:31][GNSS]GPS stop. ret=0
[D][05:18:31][GNSS]all continue location stop
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][CAT1]gsm read msg sub id: 24
[D][05:18:31][PROT]index:0
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retr

2025-07-31 21:01:31:848 ==>> y_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900005]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][PROT]index:0 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:4
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900005]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05

2025-07-31 21:01:31:953 ==>> :18:31][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 24, ret: 6
[D][05:18:31][CAT1]sub id: 24, ret: 6

[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B52F578F1B5F0950B90294964F05382B01C2E306EF0221F3FF166A5C2A7C97CD23CC31B913BF4FCE646AB751B7C37E34274CD0EB606E76C17CFFFCBC5C580C61652EE1808F80F9239C536115A564AE9D9D1BD2
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15

2025-07-31 21:01:32:059 ==>> , ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]
[W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539069
[D][05:18:31][FCTY]HardwareID  = 867222087567105
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = C25702076663
[D][05:18:31][FCTY]Bat         = 3844 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 5000 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 29,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol 

2025-07-31 21:01:32:148 ==>> = 2, adc = 115
[D][05:18:31][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3781 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                           

2025-07-31 21:01:32:287 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:01:32:622 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071539069
[D][05:18:32][FCTY]HardwareID  = 867222087567105
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = C25702076663
[D][05:18:32][FCTY]Bat         = 3884 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 5000 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:32][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][

2025-07-31 21:01:32:667 ==>> 05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3781 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:01:32:845 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:01:32:853 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:01:32:881 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:01:32:956 ==>> 5A A5 01 5A A5 


2025-07-31 21:01:33:061 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:01:33:121 ==>> [D][05:18:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 24


2025-07-31 21:01:33:158 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:01:33:164 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:01:33:171 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:01:33:211 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 21:01:33:271 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:01:33:462 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:01:33:468 ==>> 检测【打开WIFI(3)】
2025-07-31 21:01:33:477 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:01:33:676 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:01:33:756 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:01:33:765 ==>> 检测【扩展芯片hw】
2025-07-31 21:01:33:773 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:01:33:962 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:01:34:044 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:01:34:049 ==>> 检测【扩展芯片boot】
2025-07-31 21:01:34:081 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:01:34:089 ==>> 检测【扩展芯片sw】
2025-07-31 21:01:34:137 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:01:34:142 ==>> 检测【检测音频FLASH】
2025-07-31 21:01:34:149 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:01:34:423 ==>> [D][05:18:34][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
+WIFISCAN:4,0,CC057790A620,-57
+WIFISCAN:4,1,CC057790A621,-59
+WIFISCAN:4,2,44A1917CAD81,-84
+WIFISCAN:4,3,44A1917CAD80,-84

[D][05:18:34][CAT1]wifi scan report total[4]


2025-07-31 21:01:34:729 ==>> [D][05:18:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:0------------
[D][05:18:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:01:35:428 ==>>                                                                                       - get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B

2025-07-31 21:01:35:533 ==>> 50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN 

2025-07-31 21:01:35:638 ==>> get soc Fail, 204
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][

2025-07-31 21:01:35:743 ==>> 05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]

2025-07-31 21:01:35:848 ==>> 
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][GNSS]recv submsg id[3]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio

2025-07-31 21:01:35:923 ==>> _play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:35][COMM]read battery soc:255


2025-07-31 21:01:36:779 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][20

2025-07-31 21:01:36:854 ==>> 1] ... ->:
0063B98D113311331133113311331B88B38BF956E6075C1690C676759A054BC66EF1AF37DD230912B65DF5F086B233CF0C02ABA6067517E80118FE4920AD921B81C21933F78A9ABB5D309C08BEB60920F0100B74C6A15B7586FF8D86E92D0B47DAB397
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 21:01:37:218 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:01:37:742 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:01:38:469 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 21:01:38:744 ==>> [D][05:18:38][COMM]49642 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:01:38:849 ==>> [D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18

2025-07-31 21:01:38:909 ==>> :38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:01:39:200 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:01:39:205 ==>> 检测【打开喇叭声音】
2025-07-31 21:01:39:213 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:01:39:241 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 21:01:39:865 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:39][COMM]f:[

2025-07-31 21:01:39:970 ==>> ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_

2025-07-31 21:01:40:003 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:01:40:013 ==>> 检测【打开大灯控制】
2025-07-31 21:01:40:022 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:01:40:075 ==>> process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]50653 imu init OK
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 21:01:40:164 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:01:40:288 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:01:40:297 ==>> 检测【关闭仪表供电3】
2025-07-31 21:01:40:318 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:01:40:451 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 21:01:40:577 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:01:40:586 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:01:40:597 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:01:40:722 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:01:40:867 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:01:40:872 ==>> 检测【读大灯电压】
2025-07-31 21:01:40:881 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:01:41:046 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33038]


2025-07-31 21:01:41:174 ==>> 【读大灯电压】通过,【33038mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:01:41:185 ==>> 检测【关闭大灯控制2】
2025-07-31 21:01:41:202 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:01:41:243 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:01:41:347 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:01:41:474 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:01:41:480 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:01:41:490 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:01:41:649 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[115]


2025-07-31 21:01:41:818 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:01:41:827 ==>> 检测【打开WIFI(4)】
2025-07-31 21:01:41:851 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:01:42:001 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C1

2025-07-31 21:01:42:076 ==>> 13311331133113311331B88B371BAB35B592BC583A68D01F4AFD33F04059611BDE5A8986559BB202FAB03019DCB7CBC515FDF5EF7F84248BF6CBB2A18C4B73BD7FDE2B5D1B4133957444A998001044FC456DF66AAF4DE17C63D8B2B07CBF5
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 21:01:42:181 ==>> [W][05:18:42][COMM]>>>>>Inp

2025-07-31 21:01:42:227 ==>> ut command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:01:42:362 ==>> [D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:01:42:484 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:01:42:490 ==>> 检测【EC800M模组版本】
2025-07-31 21:01:42:495 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:01:42:654 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:01:42:744 ==>>                                                                                                                                                   "

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 21:01:42:911 ==>> +WIFISCAN:4,0,CC057790A621,-54
+WIFISCAN:4,1,CC057790A620,-54
+WIFISCAN:4,2,44A1917CAD80,-83
+WIFISCAN:4,3,44A1917CAD81,-84

[D][05:18:42][CAT1]wifi scan report total[4]


2025-07-31 21:01:43:046 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 21:01:43:243 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 21:01:43:348 ==>> [D][05:18:43][COMM]54266 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:01:43:515 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:01:43:810 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:43][CAT1]gsm read msg sub id: 12
[D][05:18:43][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:43][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:43][CAT1]exec over: func id: 12, ret: 132


2025-07-31 21:01:44:074 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 21:01:44:080 ==>> 检测【配置蓝牙地址】
2025-07-31 21:01:44:088 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 21:01:44:227 ==>> [W][05:18:44][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 21:01:44:287 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C25702076663>】
2025-07-31 21:01:44:393 ==>> [D][05:18:44][COMM]55277 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:01:44:468 ==>> recv ble 1
recv ble 2
ble set mac ok :c2,57,2,7,66,63
enable filters ret : 0

2025-07-31 21:01:44:575 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 21:01:44:584 ==>> 检测【BLETEST】
2025-07-31 21:01:44:595 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 21:01:44:667 ==>> 4A A4 01 A4 4A 


2025-07-31 21:01:44:772 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C25702076663*RSSI:-27*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C2570207666399999OVER 150


2025-07-31 21:01:44:984 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:01:45:271 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 21:01:45:376 ==>> [D][05:18:45][COMM]56288 imu init OK


2025-07-31 21:01:45:619 ==>> 【BLETEST】通过,【-27dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 21:01:45:646 ==>> 该项需要延时执行
2025-07-31 21:01:47:251 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B391705CBC6C42209E29CF240F5878AA4BB11EC4980EBCE5C68E3D51E4D044B788CB848BDAE30B669CD9258E5

2025-07-31 21:01:47:341 ==>> 2EF145E27225CDA733BEE2B2D0A9D7272CAA33C2D94FF7FE075698300B8DD629BB9DDA7A57E52
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
                                         

2025-07-31 21:01:49:268 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 21:01:51:288 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:01:52:464 ==>> [D][05:18:52][PROT]CLEAN,SEND:1
[D][05:18:52][PROT]CLEAN:1
[D][05:18:52][PROT]index:0 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:4
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900005]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][CAT1]Send Data To Server[198][201] ... ->:
0063B9821133113

2025-07-31 21:01:52:539 ==>> 31133113311331B88B5ACF4E5031BFFC13D729F9FA1D506EFE1E9E011C92ED6A6AA146240E867BEE385A2F9E9734A418601CE0E4C7C11DCFF9EC4B88C56DCB2B71012941FE48563B83665188AA98F8E3CFB0C911C19137395CC268A
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 21:01:53:290 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:01:55:279 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:01:55:626 ==>> 此处延时了:【10000】毫秒
2025-07-31 21:01:55:632 ==>> 检测【检测WiFi结果】
2025-07-31 21:01:55:636 ==>> WiFi信号:【CC057790A620】,信号值:-57
2025-07-31 21:01:55:645 ==>> WiFi信号:【CC057790A621】,信号值:-57
2025-07-31 21:01:55:675 ==>> WiFi信号:【F42A7D1297A3】,信号值:-71
2025-07-31 21:01:55:680 ==>> WiFi信号:【44A1917CAD80】,信号值:-83
2025-07-31 21:01:55:688 ==>> WiFi信号:【44A1917CAD81】,信号值:-84
2025-07-31 21:01:55:696 ==>> WiFi数量【5】, 最大信号值:-57
2025-07-31 21:01:55:722 ==>> 检测【检测GPS结果】
2025-07-31 21:01:55:731 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:01:55:859 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all continue location stop
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all sing location stop


2025-07-31 21:01:56:639 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:01:56:648 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:01:56:674 ==>> 定位已等待【1】秒.
2025-07-31 21:01:57:073 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:56][COMM]Open GPS Module...
[D][05:18:56][COMM]LOC_MODEL_CONT
[D][05:18:56][GNSS]start event:8
[D][05:18:56][GNSS]GPS start. ret=0
[W][05:18:56][GNSS]start cont locating
[D][05:18:56][CAT1]gsm read msg sub id: 23
[D][05:18:56][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:56][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:01:57:286 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 21:01:57:633 ==>> [D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:4
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900005]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[198]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 21:01:57:648 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:01:57:670 ==>> 定位已等待【2】秒.
2025-07-31 21:01:57:800 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:01:58:445 ==>> [D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:01:58:658 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:01:58:668 ==>> 定位已等待【3】秒.
2025-07-31 21:01:58:763 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,04,33,,,42,39,,,39,41,,,37,24,,,16,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1389.232,1389.232,44.814,2097152,2097152,2097152*42

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:58][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:58][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 23, ret: 6
[D][05:18:58][CAT1]sub id: 23, ret: 6

[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:58][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B5659986887DA65DF8328827527015E7B0872D83E639AB61D47AB4D1F3333CCF871E164254EF62EF01188D3E3EBB6AD2A57E17BAD3342E84CF01ECD4B83A605DEC6BCE3AA1502F98D9A77A767746ADAFAF82F6
[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task

2025-07-31 21:01:58:808 ==>>  submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]


2025-07-31 21:01:59:145 ==>> [D][05:18:59][GNSS]recv submsg id[1]
[D][05:18:59][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:01:59:310 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 21:01:59:585 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,33,,,42,60,,,40,39,,,39,24,,,39,1*7D

$GBGSV,3,2,10,59,,,38,41,,,37,14,,,35,7,,,35,1*42

$GBGSV,3,3,10,2,,,38,3,,,36,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1580.565,1580.565,50.530,2097152,2097152,2097152*4C



2025-07-31 21:01:59:659 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:01:59:668 ==>> 定位已等待【4】秒.
2025-07-31 21:02:00:600 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,41,60,,,40,3,,,40,24,,,39,1*48

$GBGSV,4,2,16,39,,,38,59,,,38,41,,,37,40,,,36,1*71

$GBGSV,4,3,16,14,,,35,7,,,35,5,,,35,1,,,33,1*46

$GBGSV,4,4,16,2,,,32,4,,,32,44,,,30,38,,,36,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1495.282,1495.282,47.856,2097152,2097152,2097152*47



2025-07-31 21:02:00:661 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:00:670 ==>> 定位已等待【5】秒.
2025-07-31 21:02:01:303 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 21:02:01:620 ==>> $GBGGA,130205.440,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,41,3,,,40,60,,,39,24,,,39,1*48

$GBGSV,5,2,19,39,,,38,59,,,38,41,,,37,25,,,37,1*7D

$GBGSV,5,3,19,40,,,36,14,,,36,7,,,35,38,,,34,1*44

$GBGSV,5,4,19,1,,,34,5,,,33,2,,,32,4,,,32,1*7A

$GBGSV,5,5,19,34,,,32,23,,,32,44,,,31,1*7A

$GBRMC,130205.440,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130205.440,0.000,1475.054,1475.054,47.201,2097152,2097152,2097152*54



2025-07-31 21:02:01:665 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:01:674 ==>> 定位已等待【6】秒.
2025-07-31 21:02:02:633 ==>> $GBGGA,130206.420,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,3,,,39,60,,,39,24,,,39,1*4D

$GBGSV,6,2,22,39,,,38,59,,,38,25,,,38,41,,,37,1*79

$GBGSV,6,3,22,40,,,36,14,,,36,16,,,36,7,,,35,1*41

$GBGSV,6,4,22,38,,,34,1,,,34,13,,,34,2,,,32,1*78

$GBGSV,6,5,22,34,,,32,23,,,32,5,,,31,4,,,31,1*72

$GBGSV,6,6,22,44,,,31,9,,,31,1*4F

$GBRMC,130206.420,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130206.420,0.000,1458.595,1458.595,46.680,2097152,2097152,2097152*5D



2025-07-31 21:02:02:679 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:02:688 ==>> 定位已等待【7】秒.
2025-07-31 21:02:03:306 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 21:02:03:596 ==>> $GBGGA,130207.400,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,24,,,40,3,,,39,60,,,39,1*42

$GBGSV,6,2,23,39,,,38,59,,,38,25,,,38,41,,,37,1*78

$GBGSV,6,3,23,40,,,36,14,,,36,16,,,36,7,,,35,1*40

$GBGSV,6,4,23,38,,,34,1,,,34,13,,,34,9,,,33,1*73

$GBGSV,6,5,23,2,,,32,34,,,32,23,,,32,44,,,32,1*40

$GBGSV,6,6,23,6,,,31,5,,,31,4,,,31,1*42

$GBRMC,130207.400,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130207.400,0.000,1458.266,1458.266,46.668,2097152,2097152,2097152*58



2025-07-31 21:02:03:686 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:03:697 ==>> 定位已等待【8】秒.
2025-07-31 21:02:03:791 ==>> $GBGGA,130207.600,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,24,,,40,3,,,39,60,,,39,1*45

$GBGSV,6,2,24,25,,,39,39,,,38,59,,,38,41,,,37,1*7E

$GBGSV,6,3,24,40,,,37,14,,,36,16,,,36,7,,,35,1*46

$GBGSV,6,4,24,1,,,35,38,,,34,13,,,34,9,,,33,1*75

$GBGSV,6,5,24,10,,,33,2,,,32,34,,,32,23,,,32,1*47

$GBGSV,6,6,24,44,,,32,6,,,32,5,,,31,4,,,31,1*47

$GBRMC,130207.600,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130207.600,0.000,1461.419,1461.419,46.767,2097152,2097152,2097152*54



2025-07-31 21:02:04:064 ==>>                                                                                                                                                                                                                                                                                                                                      
[D][05:19:03][PROT]===========================================================
[W][05:19:03][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955143]
[D][05:19:03][PROT]===========================================================
[D][05:19:03][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:03][PROT]sending traceid [9999999999900007]
[D][05:19:03][PROT]Send_TO_M2M [1629955143]
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:03][SAL ]sock send credit cnt[6]
[D][05:19:03][SAL ]sock send ind credit cnt[6]
[D][05:19:03][M2M ]m2m send data len[134]
[D][05:19:03][SAL ]Cellular task submsg id[10]
[D][05:19:03][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:03][CAT1]gsm read msg sub id: 15
[D][05:19:03][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:03][M2M ]m2m sw

2025-07-31 21:02:04:139 ==>> itch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:03][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE4C5F5871FE014F91C65204D12FE5F3BADFE1220979B5A4E5DFB73F5FD77EEFAAD394BB8B7A36329628E75275C4C85880A8E4
[D][05:19:03][CAT1]<<< 
SEND OK

[D][05:19:03][CAT1]exec over: func id: 15, ret: 11
[D][05:19:03][CAT1]sub id: 15, ret: 11

[D][05:19:03][SAL ]Cellular task submsg id[68]
[D][05:19:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:03][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:03][M2M ]g_m2m_is_idle become true
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:03][PROT]M2M Send ok [1629955143]


2025-07-31 21:02:04:691 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:04:704 ==>> 定位已等待【9】秒.
2025-07-31 21:02:04:797 ==>> $GBGGA,130208.580,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,40,3,,,39,60,,,39,1*45

$GBGSV,7,2,25,25,,,39,59,,,39,39,,,38,41,,,37,1*7F

$GBGSV,7,3,25,40,,,37,14,,,36,16,,,36,7,,,35,1*46

$GBGSV,7,4,25,1,,,35,38,,,34,13,,,34,9,,,34,1*72

$GBGSV,7,5,25,42,,,33,6,,,33,10,,,32,2,,,32,1*70

$GBGSV,7,6,25,34,,,32,23,,,32,44,,,32,4,,,31,1*41

$GBGSV,7,7,25,5,,,30,1*47

$GBRMC,130208.580,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130208.580,0.000,1459.349,1459.349,46.704,2097152,2097152,2097152*55



2025-07-31 21:02:05:318 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 21:02:05:701 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:05:713 ==>> 定位已等待【10】秒.
2025-07-31 21:02:05:776 ==>> $GBGGA,130209.560,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,24,,,40,3,,,39,60,,,39,1*45

$GBGSV,7,2,25,25,,,39,59,,,39,39,,,38,41,,,37,1*7F

$GBGSV,7,3,25,40,,,37,14,,,36,16,,,36,7,,,35,1*46

$GBGSV,7,4,25,1,,,35,38,,,34,13,,,34,9,,,34,1*72

$GBGSV,7,5,25,42,,,34,6,,,34,10,,,33,2,,,33,1*70

$GBGSV,7,6,25,23,,,33,44,,,33,34,,,32,4,,,31,1*41

$GBGSV,7,7,25,5,,,31,1*46

$GBRMC,130209.560,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130209.560,0.000,1470.943,1470.943,47.061,2097152,2097152,2097152*5F



2025-07-31 21:02:06:715 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:06:730 ==>> 定位已等待【11】秒.
2025-07-31 21:02:06:749 ==>> $GBGGA,130210.540,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,39,60,,,39,1*45

$GBGSV,7,2,26,25,,,39,59,,,39,39,,,38,41,,,37,1*7C

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,38,,,35,9,,,35,42,,,35,1*74

$GBGSV,7,5,26,13,,,34,6,,,34,10,,,33,2,,,33,1*77

$GBGSV,7,6,26,23,,,33,44,,,33,34,,,33,4,,,31,1*43

$GBGSV,7,7,26,5,,,31,8,,,28,1*77

$GBRMC,130210.540,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130210.540,0.000,1467.004,1467.004,46.950,2097152,2097152,2097152*5F



2025-07-31 21:02:07:324 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 21:02:07:725 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:07:737 ==>> 定位已等待【12】秒.
2025-07-31 21:02:07:759 ==>> $GBGGA,130211.520,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,25,,,40,3,,,39,1*4A

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,38,,,35,9,,,35,42,,,35,1*74

$GBGSV,7,5,26,13,,,34,6,,,34,2,,,33,23,,,33,1*77

$GBGSV,7,6,26,44,,,33,34,,,33,10,,,32,4,,,31,1*42

$GBGSV,7,7,26,5,,,31,8,,,29,1*76

$GBRMC,130211.520,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130211.520,0.000,1468.598,1468.598,47.001,2097152,2097152,2097152*54



2025-07-31 21:02:08:730 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:08:743 ==>> 定位已等待【13】秒.
2025-07-31 21:02:08:764 ==>> $GBGGA,130212.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,25,,,40,3,,,39,1*4A

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,38,,,34,1*75

$GBGSV,7,5,26,13,,,34,6,,,34,2,,,33,23,,,33,1*77

$GBGSV,7,6,26,44,,,33,34,,,33,10,,,32,4,,,31,1*42

$GBGSV,7,7,26,5,,,31,8,,,30,1*7E

$GBRMC,130212.520,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130212.520,0.000,1468.595,1468.595,46.998,2097152,2097152,2097152*5F



2025-07-31 21:02:09:251 ==>> [D][05:19:08][PROT]CLEAN,SEND:2
[D][05:19:08][PROT]index:2 1629955148
[D][05:19:08][PROT]is_send:0
[D][05:19:08][PROT]sequence_num:6
[D][05:19:08][PROT]retry_timeout:0
[D][05:19:08][PROT]retry_times:2
[D][05:19:08][PROT]send_path:0x2
[D][05:19:08][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:08][PROT]===========================================================
[W][05:19:08][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955148]
[D][05:19:08][PROT]===========================================================
[D][05:19:08][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:08][PROT]sending traceid [9999999999900007]
[D][05:19:08][PROT]Send_TO_M2M [1629955148]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:08][SAL ]sock send credit cnt[6]
[D][05:19:08][SAL ]sock send ind credit cnt[6]
[D][05:19:08][M2M ]m2m send data len[134]
[D][05:19:08][SAL ]Cellular task submsg id[10]
[D][05:19:08][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:08][CAT1]gsm read msg sub id: 15
[D][05:19:08][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:08][CAT1]<<< 
ERROR



2025-07-31 21:02:09:326 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 21:02:09:725 ==>> $GBGGA,130213.520,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,39,1*4A

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,38,,,34,1*75

$GBGSV,7,5,26,13,,,34,6,,,34,2,,,33,23,,,33,1*77

$GBGSV,7,6,26,44,,,33,34,,,33,10,,,33,4,,,32,1*40

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,130213.520,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130213.520,0.000,1473.371,1473.371,47.143,2097152,2097152,2097152*51



2025-07-31 21:02:09:740 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:09:749 ==>> 定位已等待【14】秒.
2025-07-31 21:02:10:736 ==>> $GBGGA,130214.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,3,,,40,25,,,40,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,37,1*7C

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,42,,,36,1*77

$GBGSV,7,4,26,7,,,35,1,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,34,13,,,34,2,,,33,23,,,33,1*4A

$GBGSV,7,6,26,44,,,33,34,,,33,10,,,33,4,,,32,1*40

$GBGSV,7,7,26,5,,,31,8,,,31,1*7F

$GBRMC,130214.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130214.520,0.000,1479.752,1479.752,47.350,2097152,2097152,2097152*56



2025-07-31 21:02:10:751 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:10:764 ==>> 定位已等待【15】秒.
2025-07-31 21:02:11:347 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 21:02:11:731 ==>> $GBGGA,130215.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,24,,,40,3,,,39,25,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,14,,,36,16,,,36,42,,,35,1*74

$GBGSV,7,4,26,7,,,35,1,,,35,9,,,35,6,,,35,1*78

$GBGSV,7,5,26,38,,,35,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,26,34,,,33,10,,,33,23,,,32,4,,,32,1*40

$GBGSV,7,7,26,8,,,32,5,,,31,1*7C

$GBRMC,130215.520,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130215.520,0.000,1473.362,1473.362,47.134,2097152,2097152,2097152*57



2025-07-31 21:02:11:761 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:11:770 ==>> 定位已等待【16】秒.
2025-07-31 21:02:12:737 ==>> $GBGGA,130216.520,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,38,41,,,37,1*7C

$GBGSV,7,3,27,40,,,37,14,,,36,16,,,36,42,,,35,1*75

$GBGSV,7,4,27,7,,,35,1,,,35,9,,,34,6,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,33,23,,,32,4,,,32,1*41

$GBGSV,7,7,27,8,,,32,5,,,31,26,,,29,1*72

$GBRMC,130216.520,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130216.520,0.000,1458.729,1458.729,46.679,2097152,2097152,2097152*5B



2025-07-31 21:02:12:767 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:12:777 ==>> 定位已等待【17】秒.
2025-07-31 21:02:13:338 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 21:02:13:736 ==>> $GBGGA,130217.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,14,,,36,16,,,36,42,,,35,1*75

$GBGSV,7,4,27,7,,,35,1,,,35,9,,,34,6,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,4,,,32,1*40

$GBGSV,7,7,27,8,,,32,5,,,31,26,,,29,1*72

$GBRMC,130217.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130217.520,0.000,1455.658,1455.658,46.581,2097152,2097152,2097152*5E



2025-07-31 21:02:13:781 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:13:790 ==>> 定位已等待【18】秒.
2025-07-31 21:02:14:741 ==>> $GBGGA,130218.520,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,35,42,,,35,1*76

$GBGSV,7,4,27,7,,,35,1,,,35,9,,,34,6,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,8,,,32,1*4C

$GBGSV,7,7,27,4,,,31,5,,,31,26,,,29,1*7D

$GBRMC,130218.520,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130218.520,0.000,1452.589,1452.589,46.485,2097152,2097152,2097152*54



2025-07-31 21:02:14:786 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:14:798 ==>> 定位已等待【19】秒.
2025-07-31 21:02:15:343 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 21:02:15:733 ==>> $GBGGA,130219.520,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,35,42,,,35,1*76

$GBGSV,7,4,27,7,,,35,1,,,35,9,,,34,6,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,8,,,32,1*4C

$GBGSV,7,7,27,4,,,31,5,,,31,26,,,28,1*7C

$GBRMC,130219.520,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130219.520,0.000,1451.057,1451.057,46.440,2097152,2097152,2097152*5C



2025-07-31 21:02:15:793 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:15:807 ==>> 定位已等待【20】秒.
2025-07-31 21:02:16:735 ==>> $GBGGA,130220.520,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,35,42,,,35,1*76

$GBGSV,7,4,27,7,,,35,1,,,35,9,,,35,6,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,8,,,32,1*4C

$GBGSV,7,7,27,4,,,32,5,,,31,26,,,28,1*7F

$GBRMC,130220.520,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130220.520,0.000,1454.126,1454.126,46.535,2097152,2097152,2097152*55



2025-07-31 21:02:16:795 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:16:808 ==>> 定位已等待【21】秒.
2025-07-31 21:02:17:370 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 21:02:17:733 ==>> $GBGGA,130221.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,39,39,,,38,41,,,37,1*7C

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,35,42,,,35,1*76

$GBGSV,7,4,27,7,,,35,9,,,35,1,,,34,6,,,34,1*79

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,33,23,,,33,8,,,32,1*4C

$GBGSV,7,7,27,4,,,32,5,,,31,26,,,29,1*7E

$GBRMC,130221.520,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130221.520,0.000,1458.727,1458.727,46.677,2097152,2097152,2097152*51



2025-07-31 21:02:17:808 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:17:818 ==>> 定位已等待【22】秒.
2025-07-31 21:02:18:729 ==>> $GBGGA,130222.520,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,35,1*75

$GBGSV,7,4,27,7,,,35,9,,,34,1,,,34,6,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,8,,,32,1*4C

$GBGSV,7,7,27,4,,,31,5,,,31,26,,,29,1*7D

$GBRMC,130222.520,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130222.520,0.000,1452.590,1452.590,46.485,2097152,2097152,2097152*5D



2025-07-31 21:02:18:819 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:18:829 ==>> 定位已等待【23】秒.
2025-07-31 21:02:19:269 ==>> [D][05:19:18][CAT1]exec over: func id: 15, ret: -93
[D][05:19:18][CAT1]sub id: 15, ret: -93

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:18][SAL ]socket send fail. id[4]
[D][05:19:18][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:19][M2M ]m2m select fd[4]
[D][05:19:19][M2M ]socket[4] Link is disconnected
[D][05:19:19][M2M ]tcpclient close[4]
[D][05:19:19][SAL ]socket[4] has closed
[D][05:19:19][PROT]protocol read data ok
[D][05:19:19][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[E][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:19][PROT]M2M Send Fail [1629955159]
[D][05:19:19][PROT]CLEAN,SEND:2
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:19][CAT1]gsm read msg sub id: 10
[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 21:02:19:359 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 21:02:19:663 ==>> [D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 10, ret: 6
[D][05:19:19][CAT1]sub id: 10, ret: 6

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:19][M2M ]m2m gsm shut done, ret[0]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:19][SAL ]open socket ind id[4], rst[0]
[D][05:19:19][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:19][SAL ]Cellular task submsg id[8]
[D][05:19:19][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:19][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:19][CAT1]gsm read msg sub id: 8
[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:19][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:02:19:830 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:19:841 ==>> 定位已等待【24】秒.
2025-07-31 21:02:19:983 ==>> [D][05:19:19][CAT1]pdpdeact urc len[22]
$GBGGA,130223.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,39,3,,,39,25,,,39,1*48

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,36,16,,,36,14,,,36,42,,,35,1*74

$GBGSV,7,4,27,7,,,35,9,,,34,1,,,34,6,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,10,,,32,23,,,32,8,,,32,1*4C

$GBGSV,7,7,27,4,,,31,5,,,31,26,,,29,1*7D

$GBRMC,130223.520,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130223.520,0.000,1449.515,1449.515,46.384,2097152,2097152,2097152*5A



2025-07-31 21:02:20:734 ==>> $GBGGA,130224.520,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,24,,,40,3,,,39,25,,,39,1*46

$GBGSV,7,2,27,60,,,39,59,,,38,39,,,38,41,,,37,1*7D

$GBGSV,7,3,27,40,,,37,16,,,36,14,,,36,42,,,35,1*75

$GBGSV,7,4,27,7,,,35,9,,,34,1,,,34,6,,,34,1*78

$GBGSV,7,5,27,38,,,34,13,,,34,2,,,33,44,,,33,1*4A

$GBGSV,7,6,27,34,,,33,23,,,33,10,,,32,8,,,32,1*4D

$GBGSV,7,7,27,4,,,31,5,,,31,26,,,29,1*7D

$GBRMC,130224.520,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130224.520,0.000,1454.123,1454.123,46.533,2097152,2097152,2097152*57



2025-07-31 21:02:20:839 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:20:850 ==>> 定位已等待【25】秒.
2025-07-31 21:02:21:072 ==>> [D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:20][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:20][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:20][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:20][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:02:21:509 ==>> [D][05:19:21][CAT1]opened : 0, 0
[D][05:19:21][SAL ]Cellular task submsg id[68]
[D][05:19:21][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:21][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:21][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:21][M2M ]g_m2m_is_idle become true
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:21][PROT]index:2 1629955161
[D][05:19:21][PROT]is_send:0
[D][05:19:21][PROT]sequence_num:6
[D][05:19:21][PROT]retry_timeout:0
[D][05:19:21][PROT]retry_times:1
[D][05:19:21][PROT]send_path:0x2
[D][05:19:21][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:21][PROT]===========================================================
[W][05:19:21][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955161]
[D][05:19:21][PROT]===========================================================
[D][05:19:21][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:21][PROT]sending traceid [9999999999900007]
[D][05:19:21][PROT]Send_TO_M2M [1629955161]
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:21][SAL ]sock send credit cnt[6]
[D][05:19:21][SAL ]sock send ind credit cnt[6]
[D][05:19:21][M2M ]m2m send data len[134]
[D][05:19:21][SA

2025-07-31 21:02:21:614 ==>> L ]Cellular task submsg id[10]
[D][05:19:21][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd0] format[0]
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:21][CAT1]gsm read msg sub id: 15
[D][05:19:21][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:21][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BE2B7FCFA60745BF0748881DCBCF380D94902BD2F2F28CA9CB94A7C761688DC257A9317913FAFE097D42622EE998A8418B673C
[D][05:19:21][CAT1]<<< 
SEND OK

[D][05:19:21][CAT1]exec over: func id: 15, ret: 11
[D][05:19:21][CAT1]sub id: 15, ret: 11

[D][05:19:21][SAL ]Cellular task submsg id[68]
[D][05:19:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:21][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:21][M2M ]g_m2m_is_idle become true
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:21][PROT]M2M Send ok [1629955161]
[D][05:19:21][COMM]read battery soc:255


2025-07-31 21:02:21:719 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                      26,,,30,1*75

$GBRMC,130225.520,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130225.520,0.000,1451.048,1451.048,46.430,2097152,2097152,2097152*54



2025-07-31 21:02:21:854 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:21:864 ==>> 定位已等待【26】秒.
2025-07-31 21:02:22:730 ==>> $GBGGA,130226.520,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,41,24,,,39,3,,,39,25,,,39,1*47

$GBGSV,7,2,28,60,,,39,59,,,38,39,,,38,41,,,37,1*72

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,35,42,,,35,1*79

$GBGSV,7,4,28,7,,,35,1,,,35,9,,,34,6,,,34,1*76

$GBGSV,7,5,28,38,,,34,13,,,34,2,,,33,44,,,33,1*45

$GBGSV,7,6,28,34,,,33,23,,,32,10,,,32,8,,,32,1*43

$GBGSV,7,7,28,4,,,32,5,,,31,26,,,30,12,,,38,1*71

$GBRMC,130226.520,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130226.520,0.000,1454.116,1454.116,46.525,2097152,2097152,2097152*52



2025-07-31 21:02:22:867 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:22:878 ==>> 定位已等待【27】秒.
2025-07-31 21:02:23:380 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 21:02:23:732 ==>> $GBGGA,130227.520,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,41,24,,,40,3,,,39,25,,,39,1*49

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,38,41,,,37,1*73

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,36,42,,,35,1*7A

$GBGSV,7,4,28,7,,,35,1,,,35,9,,,34,6,,,34,1*76

$GBGSV,7,5,28,38,,,34,13,,,34,2,,,33,44,,,33,1*45

$GBGSV,7,6,28,34,,,33,23,,,32,10,,,32,8,,,32,1*43

$GBGSV,7,7,28,4,,,32,5,,,31,26,,,30,12,,,29,1*71

$GBRMC,130227.520,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130227.520,0.000,1449.578,1449.578,46.393,2097152,2097152,2097152*58



2025-07-31 21:02:23:868 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:23:879 ==>> 定位已等待【28】秒.
2025-07-31 21:02:24:738 ==>> $GBGGA,130228.520,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,41,24,,,40,3,,,40,25,,,39,1*47

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,38,41,,,37,1*73

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,36,42,,,35,1*7A

$GBGSV,7,4,28,7,,,35,1,,,35,9,,,35,6,,,34,1*77

$GBGSV,7,5,28,38,,,34,13,,,34,2,,,33,44,,,33,1*45

$GBGSV,7,6,28,34,,,33,23,,,33,10,,,33,8,,,31,1*40

$GBGSV,7,7,28,4,,,31,5,,,31,12,,,30,26,,,29,1*72

$GBRMC,130228.520,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130228.520,0.000,1452.542,1452.542,46.491,2097152,2097152,2097152*52



2025-07-31 21:02:24:873 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:24:886 ==>> 定位已等待【29】秒.
2025-07-31 21:02:25:383 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 21:02:25:730 ==>> $GBGGA,130229.520,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,41,24,,,40,3,,,40,25,,,39,1*47

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,38,41,,,37,1*73

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,36,42,,,35,1*7A

$GBGSV,7,4,28,7,,,35,9,,,35,1,,,34,6,,,34,1*76

$GBGSV,7,5,28,38,,,34,13,,,34,2,,,33,44,,,33,1*45

$GBGSV,7,6,28,34,,,33,23,,,33,10,,,33,8,,,32,1*43

$GBGSV,7,7,28,4,,,32,5,,,30,12,,,30,26,,,29,1*70

$GBRMC,130229.520,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130229.520,0.000,1452.541,1452.541,46.490,2097152,2097152,2097152*52



2025-07-31 21:02:25:883 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:25:894 ==>> 定位已等待【30】秒.
2025-07-31 21:02:26:483 ==>> [D][05:19:26][PROT]CLEAN,SEND:2
[D][05:19:26][PROT]CLEAN:2


2025-07-31 21:02:26:738 ==>> $GBGGA,130230.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,41,24,,,40,3,,,40,25,,,39,1*47

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,38,41,,,37,1*73

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,36,42,,,35,1*7A

$GBGSV,7,4,28,7,,,35,9,,,35,1,,,35,13,,,35,1*42

$GBGSV,7,5,28,6,,,34,38,,,34,2,,,33,44,,,33,1*71

$GBGSV,7,6,28,34,,,33,23,,,33,10,,,33,8,,,32,1*43

$GBGSV,7,7,28,4,,,31,5,,,31,12,,,31,26,,,29,1*73

$GBRMC,130230.520,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130230.520,0.000,1456.979,1456.979,46.628,2097152,2097152,2097152*5B



2025-07-31 21:02:26:888 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:26:900 ==>> 定位已等待【31】秒.
2025-07-31 21:02:27:381 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 21:02:27:759 ==>> $GBGGA,130231.520,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,3,,,40,25,,,39,1*44

$GBGSV,7,2,28,60,,,39,39,,,39,59,,,38,41,,,37,1*73

$GBGSV,7,3,28,40,,,37,16,,,36,14,,,36,42,,,35,1*7A

$GBGSV,7,4,28,7,,,35,9,,,35,1,,,35,6,,,35,1*76

$GBGSV,7,5,28,13,,,34,38,,,34,2,,,34,44,,,33,1*42

$GBGSV,7,6,28,34,,,33,23,,,33,10,,,33,8,,,32,1*43

$GBGSV,7,7,28,4,,,32,5,,,31,12,,,31,26,,,29,1*70

$GBRMC,130231.520,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130231.520,0.000,729.353,729.353,667.013,2097152,2097152,2097152*61



2025-07-31 21:02:27:895 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:27:906 ==>> 定位已等待【32】秒.
2025-07-31 21:02:28:757 ==>> $GBGGA,130232.520,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,40,24,,,40,60,,,39,1*4B

$GBGSV,8,2,29,59,,,39,39,,,39,25,,,39,40,,,37,1*7C

$GBGSV,8,3,29,41,,,37,14,,,36,16,,,36,42,,,36,1*76

$GBGSV,8,4,29,7,,,35,1,,,35,9,,,35,6,,,35,1*78

$GBGSV,8,5,29,13,,,34,38,,,34,34,,,34,2,,,33,1*4B

$GBGSV,8,6,29,10,,,33,44,,,33,8,,,32,4,,,32,1*7E

$GBGSV,8,7,29,23,,,32,5,,,31,12,,,31,26,,,29,1*4B

$GBGSV,8,8,29,11,,,,1*7D

$GBRMC,130232.520,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130232.520,0.000,730.095,730.095,667.693,2097152,2097152,2097152*6C



2025-07-31 21:02:28:907 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:28:918 ==>> 定位已等待【33】秒.
2025-07-31 21:02:29:485 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02A3 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02C3 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02C4 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02C5 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02E3 loss. last_tick:0. cur_

2025-07-31 21:02:29:590 ==>> tick:100008. period:10000
[D][05:19:28][COMM]msg 02E4 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02E5 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0302 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0303 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0304 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02E6 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02E7 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 0305 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 0306 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100014. period:10000. j,i:0 53


2025-07-31 21:02:29:696 ==>> 
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100014. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100014. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100015. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100015. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100015. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100016. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100016. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100016
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100017
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100017
[D][05:19:29][COMM]read battery soc:255


2025-07-31 21:02:29:800 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 21:02:29:920 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:29:933 ==>> 定位已等待【34】秒.
2025-07-31 21:02:30:793 ==>> $GBGGA,130230.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,67,247,42,3,61,190,40,16,56,30,36,14,55,188,36,1*77

$GBGSV,7,2,28,39,52,13,39,59,52,129,39,6,52,349,35,40,50,175,37,1*78

$GBGSV,7,3,28,1,48,126,35,9,47,327,35,2,45,238,34,24,44,24,40,1*75

$GBGSV,7,4,28,7,44,178,35,60,43,240,39,10,35,190,33,13,34,218,34,1*4B

$GBGSV,7,5,28,26,34,246,29,4,32,112,32,41,31,314,38,8,30,207,32,1*75

$GBGSV,7,6,28,5,24,258,31,12,19,116,31,38,17,199,34,42,5,322,36,1*72

$GBGSV,7,7,28,25,,,39,34,,,34,44,,,33,23,,,32,1*71

$GBGSV,1,1,04,33,67,247,42,39,52,13,40,24,44,24,40,41,31,314,35,5*78

$GBRMC,130230.507,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,164.49,T,,M,0.000,N,0.001,K,N*2F

$GBGST,130230.507,0.683,0.262,0.233,0.395,2.694,3.125,11*50



2025-07-31 21:02:30:929 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:30:940 ==>> 定位已等待【35】秒.
2025-07-31 21:02:31:427 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 21:02:31:939 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 21:02:31:951 ==>> 定位已等待【36】秒.
2025-07-31 21:02:32:467 ==>> $GBGGA,130231.507,2301.2574060,N,11421.9421676,E,1,08,1.23,70.834,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,39,24,07,60,40,41,,,,,3.47,1.23,3.24,4*04

$GBGSV,7,1,28,33,67,247,42,3,61,190,40,16,56,30,36,14,55,188,36,1*77

$GBGSV,7,2,28,39,52,13,39,59,52,129,39,6,52,349,35,1,48,126,35,1*40

$GBGSV,7,3,28,9,47,327,35,2,45,238,33,24,44,24,40,7,44,178,35,1*73

$GBGSV,7,4,28,60,43,240,39,40,41,161,37,10,35,190,33,13,34,218,35,1*76

$GBGSV,7,5,28,26,34,246,30,4,32,112,32,41,31,314,38,8,30,207,32,1*7D

$GBGSV,7,6,28,5,24,258,32,34,20,145,34,12,19,116,31,44,19,90,33,1*7C

$GBGSV,7,7,28,38,17,199,34,42,5,322,36,25,,,40,23,,,32,1*41

$GBGSV,1,1,04,33,67,247,43,39,52,13,40,24,44,24,40,41,31,314,37,5*7B

$GBRMC,130231.507,A,2301.2574060,N,11421.9421676,E,0.001,164.49,310725,,,A,S*32

$GBVTG,164.49,T,,M,0.001,N,0.001,K,A*21

[D][05:19:31][GNSS]HD8040 GPS
[D][05:19:31][GNSS]GPS diff_sec 124011780, report 0x42 frame
$GBGST,130231.507,0.971,0.213,0.193,0.320,1.672,2.101,7.271*73

[D][05:19:31][COMM]Main Task receive event:131
[D][05:19:31][COMM]index:0,power_mode:0xFF
[D][05:19:31][COMM]index:1,sound_mode:0xFF
[D][05:19:31][COMM]index:2,gsensor_mode:0xFF
[D][05:19:31][COMM]index:3,report_freq_mode:0xFF
[D][

2025-07-31 21:02:32:572 ==>> 05:19:31][COMM]index:4,report_period:0xFF
[D][05:19:31][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:31][COMM]index:6,normal_reset_period:0xFF
[D][05:19:31][COMM]index:7,spock_over_speed:0xFF
[D][05:19:31][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:31][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:31][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:31][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:31][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:31][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:31][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:31][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:31][COMM]index:16,imu_config_params:0xFF
[D][05:19:31][COMM]index:17,long_connect_params:0xFF
[D][05:19:31][COMM]index:18,detain_mark:0xFF
[D][05:19:31][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:31][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:31][COMM]index:21,mc_mode:0xFF
[D][05:19:31][COMM]index:22,S_mode:0xFF
[D][05:19:31][COMM]index:23,overweight:0xFF
[D][05:19:31][COMM]index:24,standstill_mode:0xFF
[D][05:19:31][COMM]index:25,night_mode:0xFF
[D][05:19:31][COMM]index:26,experiment1:0xFF
[D][05:19:31][COMM]index:27,experiment2:0xFF

2025-07-31 21:02:32:677 ==>> 
[D][05:19:31][COMM]index:28,experiment3:0xFF
[D][05:19:31][COMM]index:29,experiment4:0xFF
[D][05:19:31][COMM]index:30,night_mode_start:0xFF
[D][05:19:31][COMM]index:31,night_mode_end:0xFF
[D][05:19:31][COMM]index:33,park_report_minutes:0xFF
[D][05:19:31][COMM]index:34,park_report_mode:0xFF
[D][05:19:31][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:31][COMM]index:38,charge_battery_para: FF
[D][05:19:31][COMM]index:39,multirider_mode:0xFF
[D][05:19:31][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:31][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:31][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:31][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:31][COMM]index:44,riding_duration_config:0xFF
[D][05:19:31][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:31][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:31][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:31][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:31][COMM]index:49,mc_load_startup:0xFF
[D][05:19:31][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:31][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:31][COMM]index:52,traffic_mode:0xFF
[D][05:19:31][COMM]index:53,traffic_info_collect_freq

2025-07-31 21:02:32:782 ==>> :0xFF
[D][05:19:31][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:31][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:31][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:31][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:31][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:31][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:31][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:31][COMM]index:63,experiment5:0xFF
[D][05:19:31][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:31][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:31][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:31][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:31][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:31][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:31][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:31][COMM]index:72,experiment6:0xFF
[D][05:19:31][COMM]index:73,experiment7:0xFF
[D][05:19:31][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:31][COMM]index:75,zero_value_from_server:-1
[D][05:19:31][COMM]index:76,multirider_threshold:255
[D][05:19:31][COMM]index:77,experiment8:255
[D][05:19:31][COMM]index:78,temp_p

2025-07-31 21:02:32:887 ==>> ark_audio_play_duration:255
[D][05:19:31][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:31][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:31][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:31][COMM]index:83,loc_report_interval:255
[D][05:19:31][COMM]index:84,multirider_threshold_p2:255
[D][05:19:31][COMM]index:85,multirider_strategy:255
[D][05:19:31][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:31][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:31][COMM]index:90,weight_param:0xFF
[D][05:19:31][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:31][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:31][COMM]index:95,current_limit:0xFF
[D][05:19:31][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:31][COMM]index:100,location_mode:0xFF

[W][05:19:31][PROT]remove success[1629955171],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:31][PROT]add success [1629955171],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:31][COMM]Main Task receive event:131 finished processing
[D][05:19:31][PROT]index:0 1629955171
[D][05:19:31][PROT]is_send:0
[D][05:19:31][PROT]se

2025-07-31 21:02:32:947 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 21:02:32:957 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【40】
北斗星号:【16】,信号值:【36】
北斗星号:【14】,信号值:【36】
北斗星号:【39】,信号值:【40】
北斗星号:【59】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【1】,信号值:【35】
北斗星号:【9】,信号值:【35】
北斗星号:【24】,信号值:【40】
北斗星号:【7】,信号值:【35】
北斗星号:【60】,信号值:【39】
北斗星号:【40】,信号值:【37】
北斗星号:【13】,信号值:【35】
北斗星号:【41】,信号值:【37】
北斗星号:【42】,信号值:【36】
北斗星号:【25】,信号值:【40】

2025-07-31 21:02:32:972 ==>> 检测【CSQ强度】
2025-07-31 21:02:33:000 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:02:33:009 ==>> quence_num:7
[D][05:19:31][PROT]retry_timeout:0
[D][05:19:31][PROT]retry_times:1
[D][05:19:31][PROT]send_path:0x2
[D][05:19:31][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:31][PROT]===========================================================
[W][05:19:31][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955171]
[D][05:19:31][PROT]===========================================================
[D][05:19:31][PROT]sending traceid [9999999999900008]
[D][05:19:31][PROT]Send_TO_M2M [1629955171]
[D][05:19:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:31][SAL ]sock send credit cnt[6]
[D][05:19:31][SAL ]sock send ind credit cnt[6]
[D][05:19:31][M2M ]m2m send data len[294]
[D][05:19:31][SAL ]Cellular task submsg id[10]
[D][05:19:31][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd0] format[0]
[D][05:19:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:31][CAT1]gsm read msg sub id: 15
[D][05:19:31][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:31][CAT1]<<< 
ERROR

$GBGGA,130232.007,2301.2577531,N,11421.9418138,E,1,11,0.92,72.516,M,-1.

2025-07-31 21:02:33:097 ==>> 770,M,,*51

$GBGSA,A,3,33,14,39,07,24,60,40,41,38,34,44,,1.99,0.92,1.77,4*06

$GBGSV,7,1,28,33,67,247,42,3,61,190,39,16,56,30,36,14,55,188,36,1*79

$GBGSV,7,2,28,39,52,13,39,59,52,129,39,6,52,349,35,1,48,126,35,1*40

$GBGSV,7,3,28,9,47,327,35,2,45,238,33,7,44,178,35,24,44,24,40,1*73

$GBGSV,7,4,28,60,43,240,39,40,41,161,37,10,35,190,33,13,34,218,35,1*76

$GBGSV,7,5,28,26,34,246,30,4,32,112,32,41,31,314,38,8,30,207,32,1*7D

$GBGSV,7,6,28,5,24,258,32,38,22,192,34,34,20,145,33,12,19,116,31,1*4C

$GBGSV,7,7,28,44,19,90,33,42,5,322,36,25,,,40,23,,,32,1*7B

$GBGSV,2,1,05,33,67,247,43,39,52,13,40,24,44,24,41,40,41,161,37,5*7E

$GBGSV,2,2,05,41,31,314,37,5*42

$GBRMC,130232.007,A,2301.2577531,N,11421.9418138,E,0.001,164.49,310725,,,A,S*31

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130232.007,1.491,0.219,0.205,0.311,1.496,1.869,4.993*7B



2025-07-31 21:02:33:385 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+CSQ<<<<<
$GBGGA,130233.000,2301.2578603,N,11421.9417087,E,1,18,0.72,73.226,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,07,24,60,1.63,0.72,1.46,4*04

$GBGSA,A,3,40,13,41,38,34,44,,,,,,,1.63,0.72,1.46,4*05

$GBGSV,7,1,28,33,67,247,42,3,62,190,40,14,55,188,36,39,52,13,39,1*73

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,40,7,44,178,35,1*47

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,41,31,314,38,1*42

$GBGSV,7,6,28,8,30,207,32,5,24,258,31,38,22,192,35,34,20,145,33,1*7E

$GBGSV,7,7,28,12,19,116,31,44,19,90,33,23,8,260,32,42,5,322,36,1*4B

$GBGSV,2,1,07,33,67,247,43,39,52,13,40,24,44,24,41,40,41,161,38,5*73

$GBGSV,2,2,07,41,31,314,38,34,20,145,32,44,19,90,35,5*7C

$GBRMC,130233.000,A,2301.2578603,N,11421.9417087,E,0.004,164.49,310725,,,A,S*35

$GBVTG,164.49,T,,M,0.004,N,0.007,K,A*22

$GBGST,130233.000,1.759,0.436,0.396,0.630,1.548,1.842,4.305*72



2025-07-31 21:02:33:430 ==>>                                          

2025-07-31 21:02:34:387 ==>> $GBGGA,130234.000,2301.2579229,N,11421.9416277,E,1,20,0.64,73.497,M,-1.770,M,,*54

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.32,0.64,1.15,4*00

$GBGSA,A,3,60,40,13,41,38,34,44,23,,,,,1.32,0.64,1.15,4*07

$GBGSV,7,1,28,33,67,247,42,3,62,190,40,14,55,188,36,39,52,13,38,1*72

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,33,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,34,26,34,246,29,4,32,112,32,41,31,314,38,1*43

$GBGSV,7,6,28,8,30,207,32,5,24,258,31,38,22,192,35,34,20,145,33,1*7E

$GBGSV,7,7,28,12,19,116,31,44,19,90,33,23,8,260,32,42,5,322,35,1*48

$GBGSV,3,1,10,33,67,247,43,39,52,13,40,25,45,297,37,24,44,24,41,5*76

$GBGSV,3,2,10,40,41,161,38,41,31,314,38,38,22,192,32,34,20,145,33,5*71

$GBGSV,3,3,10,44,19,90,34,23,8,260,31,5*7A

$GBRMC,130234.000,A,2301.2579229,N,11421.9416277,E,0.001,164.49,310725,,,A,S*36

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130234.000,3.206,0.279,0.271,0.395,2.343,2.514,4.359*7D



2025-07-31 21:02:35:009 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:02:35:381 ==>> $GBGGA,130235.000,2301.2579331,N,11421.9416087,E,1,21,0.63,73.771,M,-1.770,M,,*5D

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.31,0.63,1.15,4*04

$GBGSA,A,3,60,40,10,13,41,38,34,44,23,,,,1.31,0.63,1.15,4*02

$GBGSV,7,1,28,33,67,247,41,3,62,190,39,14,55,188,36,39,52,13,38,1*7F

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,33,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,41,31,314,38,1*42

$GBGSV,7,6,28,8,30,207,32,5,24,258,31,38,22,192,34,34,20,145,34,1*78

$GBGSV,7,7,28,12,19,116,31,44,19,90,33,23,8,260,32,42,5,322,36,1*4B

$GBGSV,3,1,10,33,67,247,43,39,52,13,40,25,45,297,39,24,44,24,40,5*79

$GBGSV,3,2,10,40,41,161,38,41,31,314,38,38,22,192,31,34,20,145,33,5*72

$GBGSV,3,3,10,44,19,90,34,23,8,260,31,5*7A

$GBRMC,130235.000,A,2301.2579331,N,11421.9416087,E,0.001,164.49,310725,,,A,S*32

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130235.000,3.058,0.173,0.169,0.246,2.228,2.366,4.011*79

[W][05:19:35][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 21:02:35:426 ==>>                                          

2025-07-31 21:02:36:384 ==>> $GBGGA,130236.000,2301.2579528,N,11421.9415963,E,1,22,0.62,74.059,M,-1.770,M,,*58

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.31,0.62,1.15,4*05

$GBGSA,A,3,60,40,10,13,42,41,38,34,44,23,,,1.31,0.62,1.15,4*05

$GBGSV,7,1,28,33,67,247,42,3,62,190,40,14,55,188,36,39,52,13,38,1*72

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,33,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,40,60,43,240,40,40,41,161,37,10,35,190,33,1*44

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,31,38,22,192,34,1*70

$GBGSV,7,7,28,34,20,145,34,12,19,116,31,44,19,90,33,23,8,260,32,1*7C

$GBGSV,3,1,10,33,67,247,43,39,52,13,40,25,45,297,40,24,44,24,40,5*77

$GBGSV,3,2,10,40,41,161,38,41,31,314,38,38,22,192,31,34,20,145,33,5*72

$GBGSV,3,3,10,44,19,90,34,23,8,260,31,5*7A

$GBRMC,130236.000,A,2301.2579528,N,11421.9415963,E,0.001,164.49,310725,,,A,S*3F

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130236.000,2.915,0.201,0.196,0.283,2.122,2.239,3.723*77



2025-07-31 21:02:37:055 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:02:37:410 ==>> $GBGGA,130237.000,2301.2579852,N,11421.9415758,E,1,22,0.62,74.298,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.31,0.62,1.15,4*05

$GBGSA,A,3,60,40,10,13,42,41,38,34,44,23,,,1.31,0.62,1.15,4*05

$GBGSV,7,1,28,33,67,247,41,3,62,190,40,14,55,188,36,39,52,13,39,1*70

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,33,25,45,297,40,7,44,178,35,1*40

$GBGSV,7,4,28,24,44,24,40,60,43,240,40,40,41,161,37,10,35,190,33,1*44

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,31,38,22,192,34,1*70

$GBGSV,7,7,28,34,20,145,34,12,19,116,32,44,19,90,33,23,8,260,32,1*7F

$GBGSV,3,1,11,33,67,247,43,39,52,13,40,25,45,297,40,24,44,24,40,5*76

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,35,23,8,260,31,5*4E

$GBRMC,130237.000,A,2301.2579852,N,11421.9415758,E,0.003,164.49,310725,,,A,S*3A

$GBVTG,164.49,T,,M,0.003,N,0.005,K,A*27

$GBGST,130237.000,2.979,0.253,0.243,0.357,2.142,2.240,3.591*7B

[D][05:19:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:19:37][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 21:02:37:440 ==>>                                          

2025-07-31 21:02:38:385 ==>> $GBGGA,130238.000,2301.2580146,N,11421.9415397,E,1,22,0.62,74.489,M,-1.770,M,,*54

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.31,0.62,1.15,4*05

$GBGSA,A,3,60,40,10,13,42,41,38,34,44,23,,,1.31,0.62,1.15,4*05

$GBGSV,7,1,28,33,67,247,42,3,62,190,40,14,55,188,36,39,52,13,39,1*73

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,40,7,44,178,35,1*47

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,31,38,22,192,35,1*71

$GBGSV,7,7,28,34,20,145,34,12,19,116,32,44,19,90,33,23,8,260,32,1*7F

$GBGSV,3,1,11,33,67,247,43,39,52,13,40,25,45,297,41,24,44,24,41,5*76

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,35,23,8,260,31,5*4E

$GBRMC,130238.000,A,2301.2580146,N,11421.9415397,E,0.001,164.49,310725,,,A,S*3A

$GBVTG,164.49,T,,M,0.001,N,0.001,K,A*21

$GBGST,130238.000,2.921,0.214,0.208,0.302,2.095,2.182,3.430*79



2025-07-31 21:02:39:117 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:02:39:411 ==>> $GBGGA,130239.000,2301.2580298,N,11421.9415309,E,1,22,0.62,74.629,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.31,0.62,1.15,4*05

$GBGSA,A,3,60,40,10,13,42,41,38,34,44,23,,,1.31,0.62,1.15,4*05

$GBGSV,7,1,28,33,67,247,42,3,62,190,40,14,55,188,36,39,52,13,38,1*72

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,39,7,44,178,35,1*49

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,35,1*4B

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,31,38,22,192,35,1*71

$GBGSV,7,7,28,34,20,145,34,12,19,116,31,44,19,90,33,23,8,260,32,1*7C

$GBGSV,3,1,11,33,67,247,43,39,52,13,41,25,45,297,41,24,44,24,41,5*77

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,34,23,8,260,31,5*4F

$GBRMC,130239.000,A,2301.2580298,N,11421.9415309,E,0.002,164.49,310725,,,A,S*3F

$GBVTG,164.49,T,,M,0.002,N,0.003,K,A*20

$GBGST,130239.000,2.956,0.228,0.220,0.320,2.107,2.183,3.346*70

[W][05:19:39][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 21:02:39:441 ==>>                                          

2025-07-31 21:02:40:402 ==>> $GBGGA,130240.000,2301.2580316,N,11421.9415338,E,1,23,0.61,74.731,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.30,0.61,1.14,4*06

$GBGSA,A,3,60,40,10,13,42,41,08,38,34,44,23,,1.30,0.61,1.14,4*0E

$GBGSV,7,1,28,33,67,246,41,3,62,190,39,14,55,188,36,39,52,13,38,1*7E

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,39,7,44,178,35,1*49

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,31,38,22,192,34,1*70

$GBGSV,7,7,28,34,20,145,33,12,19,116,31,44,19,90,33,23,8,260,32,1*7B

$GBGSV,3,1,11,33,67,246,43,39,52,13,40,25,45,297,41,24,44,24,41,5*77

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,34,23,8,260,31,5*4F

$GBRMC,130240.000,A,2301.2580316,N,11421.9415338,E,0.001,164.49,310725,,,A,S*37

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130240.000,2.865,0.176,0.172,0.249,2.047,2.117,3.226*72



2025-07-31 21:02:41:199 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:02:41:383 ==>> $GBGGA,130241.000,2301.2580232,N,11421.9415183,E,1,23,0.61,74.808,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.30,0.61,1.14,4*06

$GBGSA,A,3,60,40,10,13,42,41,08,38,34,44,23,,1.30,0.61,1.14,4*0E

$GBGSV,7,1,28,33,67,246,41,3,62,190,40,14,55,188,36,39,52,13,39,1*71

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,39,7,44,178,35,1*49

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,32,38,22,192,34,1*73

$GBGSV,7,7,28,34,20,145,33,12,19,116,31,44,19,90,33,23,8,260,32,1*7B

$GBGSV,3,1,11,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,41,5*76

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,35,23,8,260,32,5*4D

$GBRMC,130241.000,A,2301.2580232,N,11421.9415183,E,0.001,164.49,310725,,,A,S*33

$GBVTG,164.49,T,,M,0.001,N,0.003,K,A*23

$GBGST,130241.000,2.903,0.219,0.211,0.312,2.063,2.126,3.178*7D



2025-07-31 21:02:41:443 ==>>                                                                                               

2025-07-31 21:02:42:108 ==>> [D][05:19:41][CAT1]exec over: func id: 15, ret: -93
[D][05:19:41][CAT1]sub id: 15, ret: -93

[D][05:19:41][SAL ]Cellular task submsg id[68]
[D][05:19:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:41][SAL ]socket send fail. id[4]
[D][05:19:41][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:41][M2M ]m2m select fd[4]
[D][05:19:41][M2M ]socket[4] Link is disconnected
[D][05:19:41][M2M ]tcpclient close[4]
[D][05:19:41][SAL ]socket[4] has closed
[D][05:19:41][PROT]protocol read data ok
[E][05:19:41][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:41][PROT]M2M Send Fail [1629955181]
[D][05:19:41][PROT]CLEAN,SEND:0
[D][05:19:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:41][PROT]CLEAN:0
[D][05:19:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:41][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 21
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:41][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 21
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1

2025-07-31 21:02:42:183 ==>> ]SEND RAW data >>> AT+CSQ

[D][05:19:41][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 21
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:41][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 21
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:41][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:41][CAT1]exec over: func id: 12, ret: 21
[D][05:19:41][CAT1]gsm read msg sub id: 10
[D][05:19:41][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:41][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:41][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 21:02:42:410 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 21:02:42:421 ==>> 检测【关闭GSM联网】
2025-07-31 21:02:42:452 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 21:02:42:486 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                             97,39,7,44,178,35,1*49

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,,38,22,192,35,1*73

$GBGSV,7,7,28,34,20,145,33,12,19,116,31,44,19,90,33,23,8,260,32,1*7B

$GBGSV,3,1,11,33,67,246,43,39,52,13,41,25,45,297,40,24,44,24,40,5*76

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,32,44,19,90,35,23,8,260,32,5*4D

$GBRMC,130242.000,A,2301.2580215,N,11421.9414998,E,0.000,164.49,310725,,,A,S*37

$GBVTG,164.49,T,,M,0.000,N,0.000,K,A*21

$GBGST,130242.000,3.018,0.215,0.207,0.306,2.124,2.181,3.178*7D

[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 10, ret: 6
[D][05:19:42][CAT1]sub i

2025-07-31 21:02:42:576 ==>> d: 10, ret: 6

[D][05:19:42][SAL ]Cellular task submsg id[68]
[D][05:19:42][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:42][M2M ]m2m gsm shut done, ret[0]
[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:42][SAL ]open socket ind id[4], rst[0]
[D][05:19:42][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:42][SAL ]Cellular task submsg id[8]
[D][05:19:42][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:42][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:42][CAT1]gsm read msg sub id: 8
[D][05:19:42][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:42][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:42][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:42][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:02:42:681 ==>> [D][05:19:42][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:42][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:42][COMM]GSM test
[D][05:19:42][COMM]GSM test disable
[D][05:19:42][CAT1]pdpdeact urc len[22]


2025-07-31 21:02:42:711 ==>> 


2025-07-31 21:02:42:961 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 21:02:42:974 ==>> 检测【4G联网测试】
2025-07-31 21:02:43:000 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 21:02:43:130 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 21:02:44:048 ==>>    [05:19:43][COMM]Main Task receive event:14
[D][05:19:43][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955183, allstateRepSeconds = 0
[D][05:19:43][COMM]index:0,power_mode:0xFF
[D][05:19:43][COMM]index:1,sound_mode:0xFF
[D][05:19:43][COMM]index:2,gsensor_mode:0xFF
[D][05:19:43][COMM]index:3,report_freq_mode:0xFF
[D][05:19:43][COMM]index:4,report_period:0xFF
[D][05:19:43][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:43][COMM]index:6,normal_reset_period:0xFF
[D][05:19:43][COMM]index:7,spock_over_speed:0xFF
[D][05:19:43][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:43][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:43][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:43][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:43][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:43][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:43][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:43][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:43][COMM]index:16,imu_config_params:0xFF
[D][05:19:43][COMM]index:17,long_connect_params:0xFF
[D][05:19:43][COMM]index:18,detain_mark:0xFF
[D][05:19:43][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:43][COMM]index:20,lock_pos_report_interval:0xFF
[D][05

2025-07-31 21:02:44:153 ==>> :19:43][COMM]index:21,mc_mode:0xFF
[D][05:19:43][COMM]index:22,S_mode:0xFF
[D][05:19:43][COMM]index:23,overweight:0xFF
[D][05:19:43][COMM]index:24,standstill_mode:0xFF
[D][05:19:43][COMM]index:25,night_mode:0xFF
[D][05:19:43][COMM]index:26,experiment1:0xFF
[D][05:19:43][COMM]index:27,experiment2:0xFF
[D][05:19:43][COMM]index:28,experiment3:0xFF
[D][05:19:43][COMM]index:29,experiment4:0xFF
[D][05:19:43][COMM]index:30,night_mode_start:0xFF
[D][05:19:43][COMM]index:31,night_mode_end:0xFF
[D][05:19:43][COMM]index:33,park_report_minutes:0xFF
[D][05:19:43][COMM]index:34,park_report_mode:0xFF
[D][05:19:43][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:43][COMM]index:38,charge_battery_para: FF
[D][05:19:43][COMM]index:39,multirider_mode:0xFF
[D][05:19:43][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:43][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:43][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:43][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:43][COMM]index:44,riding_duration_config:0xFF
[D][05:19:43][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:43][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:43][COMM]index:47,bat_info_rep_cf

2025-07-31 21:02:44:258 ==>> g:0xFF
[D][05:19:43][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:43][COMM]index:49,mc_load_startup:0xFF
[D][05:19:43][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:43][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:43][COMM]index:52,traffic_mode:0xFF
[D][05:19:43][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:43][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:43][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:43][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:43][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:43][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:43][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:43][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:43][COMM]index:63,experiment5:0xFF
[D][05:19:43][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:43][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:43][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:43][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:43][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:43][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:43][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:43][COMM]index:72

2025-07-31 21:02:44:362 ==>> ,experiment6:0xFF
[D][05:19:43][COMM]index:73,experiment7:0xFF
[D][05:19:43][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:43][COMM]index:75,zero_value_from_server:-1
[D][05:19:43][COMM]index:76,multirider_threshold:255
[D][05:19:43][COMM]index:77,experiment8:255
[D][05:19:43][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:43][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:43][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:43][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:43][COMM]index:83,loc_report_interval:255
[D][05:19:43][COMM]index:84,multirider_threshold_p2:255
[D][05:19:43][COMM]index:85,multirider_strategy:255
[D][05:19:43][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:43][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:43][COMM]index:90,weight_param:0xFF
[D][05:19:43][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:43][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:43][COMM]index:95,current_limit:0xFF
[D][05:19:43][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:43][COMM]index:100,location_mode:0xFF

[W][05:19:43][PROT]remove success[1629955

2025-07-31 21:02:44:467 ==>> 183],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:43][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:43][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:43][PROT]add success [1629955183],send_path[2],type[4205],priority[0],index[0],used[1]
$GBGGA,130243.000,2301.2580240,N,11421.9415104,E,1,23,0.61,74.891,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,39,06,16,59,09,01,25,07,24,1.30,0.61,1.14,4*06

$GBGSA,A,3,60,40,10,13,42,41,08,38,34,44,23,,1.30,0.61,1.14,4*0E

$GBGSV,7,1,28,33,67,246,42,3,62,190,40,14,55,188,36,39,52,13,38,1*73

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,9,47,327,35,1*73

$GBGSV,7,3,28,1,46,125,35,2,45,238,34,25,45,297,40,7,44,178,35,1*47

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,26,34,246,29,4,32,112,32,42,31,166,36,1*48

$GBGSV,7,6,28,41,31,314,38,8,30,207,32,5,24,258,,38,22,192,34,1*72

$GBGSV,7,7,28,34,20,145,33,12,19,116,31,44,19,90,33,23,8,260,32,1*7B

$GBGSV,3,1,11,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,40,5*77

$GBGSV,3,2,11,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*76

$GBGSV,3,3,11,34,20,145,33,44,19,90,35,23,8,260,32,5*4C

$GB

2025-07-31 21:02:44:571 ==>> RMC,130243.000,A,2301.2580240,N,11421.9415104,E,0.001,164.49,310725,,,A,S*3B

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130243.000,3.054,0.208,0.201,0.296,2.141,2.194,3.150*7B

[D][05:19:43][COMM]read battery soc:255
[D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:43][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:43][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:43][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:43][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]exec over: func id: 8, ret: 6
[D][05:19:43][CAT1]gsm read msg sub id: 13
[D][05:19:43][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:43][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:43][CAT1]exec over: func id: 13, ret: 21
[D][05:19:43][M2M ]get csq[25]


2025-07-31 21:02:44:678 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:02:44:784 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:02:44:890 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:02:44:950 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 21:02:45:423 ==>> $GBGGA,130245.000,2301.2580467,N,11421.9414908,E,1,26,0.54,75.039,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,42,41,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,27,33,67,246,42,3,62,190,40,14,55,188,36,39,52,13,38,1*7C

$GBGSV,7,2,27,6,52,349,35,16,52,353,36,59,50,129,39,2,47,239,33,1*7F

$GBGSV,7,3,27,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*41

$GBGSV,7,4,27,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*45

$GBGSV,7,5,27,13,34,218,35,4,32,112,32,42,31,166,35,41,31,314,38,1*46

$GBGSV,7,6,27,8,30,207,32,38,22,192,34,34,20,145,33,12,19,116,31,1*41

$GBGSV,7,7,27,44,19,90,33,23,8,260,32,26,7,58,29,1*4B

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,41,5*75

$GBGSV,3,2,12,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,32,44,19,90,35,23,8,260,32,26,7,58,28,5*7A

$GBRMC,130245.000,A,2301.2580467,N,11421.9414908,E,0.001,164.49,310725,,,A,S*3B

$GBVTG,164.49,T,,M,0.001,N,0.002,K,A*22

$GBGST,130245.000,2.963,0.225,0.220,0.313,2.083,2.130,3.032*75



2025-07-31 21:02:45:468 ==>>                                          

2025-07-31 21:02:46:411 ==>> $GBGGA,130246.000,2301.2580502,N,11421.9414829,E,1,26,0.54,75.004,M,-1.770,M,,*57

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,42,41,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,28,33,67,246,42,3,62,190,40,14,55,188,36,39,52,13,39,1*72

$GBGSV,7,2,28,6,52,349,35,16,52,353,36,59,50,129,39,2,47,239,33,1*70

$GBGSV,7,3,28,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,40,60,43,240,39,40,41,161,37,10,35,190,33,1*4A

$GBGSV,7,5,28,13,34,218,35,4,32,112,32,42,31,166,36,41,31,314,38,1*4A

$GBGSV,7,6,28,8,30,207,32,38,22,192,34,34,20,145,34,12,19,116,31,1*49

$GBGSV,7,7,28,44,19,90,33,19,14,139,,23,8,260,32,26,7,58,28,1*73

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,40,5*74

$GBGSV,3,2,12,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,33,44,19,90,34,23,8,260,32,26,7,58,28,5*7A

$GBRMC,130246.000,A,2301.2580502,N,11421.9414829,E,0.000,164.49,310725,,,A,S*39

$GBVTG,164.49,T,,M,0.000,N,0.001,K,A*20

$GBGST,130246.000,3.045,0.206,0.202,0.286,2.128,2.171,3.043*75



2025-07-31 21:02:47:402 ==>> $GBGGA,130247.000,2301.2580667,N,11421.9414794,E,1,26,0.54,74.993,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,42,41,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,27,33,67,246,41,3,62,190,40,14,55,188,36,39,52,13,38,1*7F

$GBGSV,7,2,27,6,52,349,35,16,52,353,36,59,50,129,39,2,47,239,33,1*7F

$GBGSV,7,3,27,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*41

$GBGSV,7,4,27,24,44,24,39,60,43,240,39,40,41,161,37,10,35,190,33,1*4B

$GBGSV,7,5,27,13,34,218,35,4,32,112,32,42,31,166,35,41,31,314,38,1*46

$GBGSV,7,6,27,8,30,207,32,38,22,192,34,34,20,145,33,12,19,116,31,1*41

$GBGSV,7,7,27,44,19,90,33,23,8,260,32,26,7,58,28,1*4A

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,40,5*74

$GBGSV,3,2,12,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,33,44,19,90,35,23,8,260,31,26,7,58,28,5*78

$GBRMC,130247.000,A,2301.2580667,N,11421.9414794,E,0.002,164.49,310725,,,A,S*33

$GBVTG,164.49,T,,M,0.002,N,0.003,K,A*20

$GBGST,130247.000,3.067,0.246,0.240,0.344,2.139,2.179,3.028*7C



2025-07-31 21:02:47:462 ==>>                                          

2025-07-31 21:02:48:405 ==>> $GBGGA,130248.000,2301.2580809,N,11421.9414807,E,1,26,0.54,75.027,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,42,41,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,28,33,67,246,41,3,62,190,39,14,55,188,36,39,52,13,38,1*7E

$GBGSV,7,2,28,6,52,349,34,16,52,353,36,59,50,129,39,2,47,239,33,1*71

$GBGSV,7,3,28,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,39,60,43,240,39,40,41,161,37,10,35,190,33,1*44

$GBGSV,7,5,28,13,34,218,34,4,32,112,32,42,31,166,35,41,31,314,38,1*48

$GBGSV,7,6,28,8,30,207,32,5,24,258,,38,22,192,34,34,20,145,33,1*7D

$GBGSV,7,7,28,12,19,116,31,44,19,90,32,23,8,260,32,26,7,58,28,1*7B

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,40,5*74

$GBGSV,3,2,12,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,32,44,19,90,34,23,8,260,31,26,7,58,28,5*78

$GBRMC,130248.000,A,2301.2580809,N,11421.9414807,E,0.000,164.49,310725,,,A,S*3D

$GBVTG,164.49,T,,M,0.000,N,0.001,K,A*20

$GBGST,130248.000,3.092,0.201,0.198,0.280,2.151,2.190,3.019*7E



2025-07-31 21:02:49:010 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 21:02:50:005 ==>> [W][05:19:49][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:49][COMM]Main Task receive event:14
[D][05:19:49][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955189, allstateRepSeconds = 0
[D][05:19:49][COMM]index:0,power_mode:0xFF
[D][05:19:49][COMM]index:1,sound_mode:0xFF
[D][05:19:49][COMM]index:2,gsensor_mode:0xFF
[D][05:19:49][COMM]index:3,report_freq_mode:0xFF
[D][05:19:49][COMM]index:4,report_period:0xFF
[D][05:19:49][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:49][COMM]index:6,normal_reset_period:0xFF
[D][05:19:49][COMM]index:7,spock_over_speed:0xFF
[D][05:19:49][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:49][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:49][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:49][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:49][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:49][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:49][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:49][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:49][COMM]index:16,imu_config_params:0xFF
[D][05:19:49][COMM]index:17,long_connect_params:0xFF
[D][05:19:49][COMM]index:18,detain_mark:0xFF
[D][05:19:49][COMM]index:19,l

2025-07-31 21:02:50:110 ==>> ock_pos_report_count:0xFF
[D][05:19:49][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:49][COMM]index:21,mc_mode:0xFF
[D][05:19:49][COMM]index:22,S_mode:0xFF
[D][05:19:49][COMM]index:23,overweight:0xFF
[D][05:19:49][COMM]index:24,standstill_mode:0xFF
[D][05:19:49][COMM]index:25,night_mode:0xFF
[D][05:19:49][COMM]index:26,experiment1:0xFF
[D][05:19:49][COMM]index:27,experiment2:0xFF
[D][05:19:49][COMM]index:28,experiment3:0xFF
[D][05:19:49][COMM]index:29,experiment4:0xFF
[D][05:19:49][COMM]index:30,night_mode_start:0xFF
[D][05:19:49][COMM]index:31,night_mode_end:0xFF
[D][05:19:49][COMM]index:33,park_report_minutes:0xFF
[D][05:19:49][COMM]index:34,park_report_mode:0xFF
[D][05:19:49][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:49][COMM]index:38,charge_battery_para: FF
[D][05:19:49][COMM]index:39,multirider_mode:0xFF
[D][05:19:49][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:49][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:49][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:49][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:49][COMM]index:44,riding_duration_config:0xFF
[D][05:19:49][COMM]index:45,camera_park_angle_cfg:0xFF
[D][0

2025-07-31 21:02:50:216 ==>> 5:19:49][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:49][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:49][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:49][COMM]index:49,mc_load_startup:0xFF
[D][05:19:49][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:49][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:49][COMM]index:52,traffic_mode:0xFF
[D][05:19:49][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:49][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:49][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:49][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:49][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:49][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:49][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:49][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:49][COMM]index:63,experiment5:0xFF
[D][05:19:49][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:49][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:49][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:49][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:49][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:49][COMM]index:70,camera_park_light_cfg:0xFF


2025-07-31 21:02:50:320 ==>> 
[D][05:19:49][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:49][COMM]index:72,experiment6:0xFF
[D][05:19:49][COMM]index:73,experiment7:0xFF
[D][05:19:49][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:49][COMM]index:75,zero_value_from_server:-1
[D][05:19:49][COMM]index:76,multirider_threshold:255
[D][05:19:49][COMM]index:77,experiment8:255
[D][05:19:49][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:49][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:49][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:49][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:49][COMM]index:83,loc_report_interval:255
[D][05:19:49][COMM]index:84,multirider_threshold_p2:255
[D][05:19:49][COMM]index:85,multirider_strategy:255
[D][05:19:49][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:49][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:49][COMM]index:90,weight_param:0xFF
[D][05:19:49][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:49][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:49][COMM]index:95,current_limit:0xFF
[D][05:19:49][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19

2025-07-31 21:02:50:425 ==>> :49][COMM]index:100,location_mode:0xFF

[W][05:19:49][PROT]remove success[1629955189],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:49][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:49][PROT]add success [1629955189],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:49][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:49][CAT1]gsm read msg sub id: 13
[D][05:19:49][PROT]index:0 1629955189
[D][05:19:49][PROT]is_send:0
[D][05:19:49][PROT]sequence_num:10
[D][05:19:49][PROT]retry_timeout:0
[D][05:19:49][PROT]retry_times:1
[D][05:19:49][PROT]send_path:0x2
[D][05:19:49][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:49][PROT]===========================================================
[W][05:19:49][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955189]
[D][05:19:49][PROT]===========================================================
[D][05:19:49][PROT]sending traceid [999999999990000B]
[D][05:19:49][PROT]Send_TO_M2M [1629955189]
[D][05:19:49][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:49][SAL ]sock send credit cnt[6

2025-07-31 21:02:50:530 ==>> ]
[D][05:19:49][SAL ]sock send ind credit cnt[6]
[D][05:19:49][M2M ]m2m send data len[294]
[D][05:19:49][SAL ]Cellular task submsg id[10]
[D][05:19:49][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:49][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:49][CAT1]exec over: func id: 13, ret: 21
[D][05:19:49][M2M ]get csq[26]
[D][05:19:49][CAT1]gsm read msg sub id: 15
[D][05:19:49][CAT1]tx ret[17] >>> AT+QISEND=0,294

$GBGGA,130249.000,2301.2580757,N,11421.9414722,E,1,26,0.54,75.082,M,-1.770,M,,*50

[D][05:19:49][CAT1]Send Data To Server[294][294] ... ->:
0093B984113311331133113311331B88BDCF9E8721D0A2D25CA5DD22C05D5FFF9C6CD63C69DC0A0F3A6B031392C370CC7843E0AEDDCBACF44333AB78950288A0422C5CA38C90E99435DC1DCC13010370E7191ED6FF43B3D91DDFC738C429D71CDEC8DF6E57B3B4968FD4381FA90D71C350998A42218057D30058AAC6633A645D6E1CD13BE6CF1D5C6431D0AFDE96CD68DE039A
$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,42,41,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,28,33,67,246,41,3,62,190,40,14,55,188,36,39,52,1

2025-07-31 21:02:50:635 ==>> 3,38,1*70

$GBGSV,7,2,28,6,52,349,34,16,52,353,36,59,50,129,39,2,47,239,33,1*71

$GBGSV,7,3,28,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,39,60,43,240,39,40,41,161,37,10,35,190,33,1*44

$GBGSV,7,5,28,13,34,218,34,4,32,112,31,42,31,166,35,41,31,314,37,1*44

$GBGSV,7,6,28,8,30,207,32,5,24,258,,38,22,192,34,34,20,145,33,1*7D

$GBGSV,7,7,28,12,19,116,31,44,19,90,33,23,8,260,32,26,7,58,28,1*7A

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,41,24,44,24,40,5*75

$GBGSV,3,2,12,40,41,161,38,42,31,166,36,41,31,314,38,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,32,44,19,90,35,23,8,260,31,26,7,58,27,5*76

$GBRMC,130249.000,A,2301.2580757,N,11421.9414722,E,0.001,164.49,310725,,,A,S*31

$GBVTG,164.49,T,,M,0.001,N,0.001,K,A*21

$GBGST,130249.000,3.210,0.224,0.220,0.311,2.215,2.251,3.057*7E

[D][05:19:49][CAT1]<<< 
SEND OK

[D][05:19:49][CAT1]exec over: func id: 15, ret: 11
[

2025-07-31 21:02:50:740 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 21:02:50:770 ==>>                      

2025-07-31 21:02:51:062 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 21:02:51:073 ==>> 检测【关闭GPS】
2025-07-31 21:02:51:088 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:02:51:480 ==>> $GBGGA,130251.000,2301.2580711,N,11421.9414759,E,1,26,0.54,75.172,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,39,06,16,59,02,09,01,25,07,1.10,0.54,0.95,4*0E

$GBGSA,A,3,24,60,40,10,13,41,42,08,38,34,12,44,1.10,0.54,0.95,4*06

$GBGSA,A,3,23,26,,,,,,,,,,,1.10,0.54,0.95,4*00

$GBGSV,7,1,28,33,67,246,41,3,62,190,40,14,55,188,36,39,52,13,38,1*70

$GBGSV,7,2,28,6,52,349,34,16,52,353,36,59,50,129,39,2,47,239,33,1*71

$GBGSV,7,3,28,9,47,327,35,1,46,125,35,25,45,297,39,7,44,178,35,1*4E

$GBGSV,7,4,28,24,44,24,39,60,43,240,39,40,41,161,37,10,35,190,33,1*44

$GBGSV,7,5,28,13,34,218,34,4,32,112,31,41,31,314,38,42,31,166,35,1*4B

$GBGSV,7,6,28,8,30,207,32,5,24,258,32,38,22,192,34,34,20,145,33,1*7C

$GBGSV,7,7,28,12,19,116,31,44,19,90,32,23,8,260,32,26,7,58,29,1*7A

$GBGSV,3,1,12,33,67,246,43,39,52,13,40,25,45,297,40,24,44,24,40,5*74

$GBGSV,3,2,12,40,41,161,38,41,31,314,38,42,31,166,36,38,22,192,31,5*75

$GBGSV,3,3,12,34,20,145,33,44,19,90,35,23,8,260,32,26,7,58,27,5*74

$GBRMC,130251.000,A,2301.2580711,N,11421.9414759,E,0.002,164.49,310725,,,A,S*35

$GBVTG,164.49,T,,M,0.002,N,0.004,K,A*27

$GBGST,130251.000,3.298,0.198,0.195,0.276,2

2025-07-31 21:02:51:585 ==>> .261,2.294,3.067*77

[W][05:19:51][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:51][GNSS]stop locating
[D][05:19:51][GNSS]stop event:8
[D][05:19:51][GNSS]GPS stop. ret=0
[D][05:19:51][GNSS]all continue location stop
[W][05:19:51][GNSS]stop locating
[D][05:19:51][GNSS]all sing location stop
[D][05:19:51][CAT1]gsm read msg sub id: 24
[D][05:19:51][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:51][CAT1]<<< 
OK

[D][05:19:51][CAT1]exec over: func id: 24, ret: 6
[D][05:19:51][CAT1]sub id: 24, ret: 6

[D][05:19:51][GNSS]recv submsg id[1]
[D][05:19:51][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:51][GNSS]location stop evt done evt
                                         

2025-07-31 21:02:51:620 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 21:02:51:629 ==>> 检测【清空消息队列2】
2025-07-31 21:02:51:638 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:02:51:856 ==>> [W][05:19:51][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:51][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:02:51:910 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:02:51:920 ==>> 检测【轮动检测】
2025-07-31 21:02:51:930 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 21:02:51:961 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 21:02:52:023 ==>> [D][05:19:51][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 21:02:52:424 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 21:02:52:564 ==>> 3A A3 01 01 A3 


2025-07-31 21:02:52:669 ==>> ON_OUT1
OVER 150


2025-07-31 21:02:52:720 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 21:02:52:730 ==>> 检测【关闭小电池】
2025-07-31 21:02:52:744 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:02:52:868 ==>> 6A A6 02 A6 6A 


2025-07-31 21:02:52:959 ==>> Battery OFF
OVER 150


2025-07-31 21:02:53:005 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 21:02:53:018 ==>> 检测【进入休眠模式】
2025-07-31 21:02:53:029 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:02:53:204 ==>> [W][05:19:53][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:53][COMM]Main Task receive event:28
[D][05:19:53][COMM]main task tmp_sleep_event = 8
[D][05:19:53][COMM]prepare to sleep
[D][05:19:53][CAT1]gsm read msg sub id: 12
[D][05:19:53][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 21:02:53:481 ==>> [D][05:19:53][COMM]read battery soc:255


2025-07-31 21:02:54:033 ==>> [D][05:19:53][CAT1]<<< 
OK

[D][05:19:53][CAT1]exec over: func id: 12, ret: 6
[D][05:19:53][M2M ]tcpclient close[4]
[D][05:19:53][SAL ]Cellular task submsg id[12]
[D][05:19:53][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:53][CAT1]gsm read msg sub id: 9
[D][05:19:53][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:53][CAT1]<<< 
OK

[D][05:19:53][CAT1]exec over: func id: 9, ret: 6
[D][05:19:53][CAT1]sub id: 9, ret: 6

[D][05:19:53][SAL ]Cellular task submsg id[68]
[D][05:19:53][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:53][SAL ]socket close ind. id[4]
[D][05:19:53][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:53][COMM]1x1 frm_can_tp_send ok
[D][05:19:53][CAT1]pdpdeact urc len[22]


2025-07-31 21:02:54:309 ==>> [E][05:19:54][COMM]1x1 rx timeout
[D][05:19:54][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:02:54:845 ==>> [E][05:19:54][COMM]1x1 rx timeout
[E][05:19:54][COMM]1x1 tp timeout
[E][05:19:54][COMM]1x1 error -3.
[D][05:19:54][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:54][COMM]CAN STOP!
[D][05:19:54][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:54][COMM]------------ready to Power off Acckey 1------------
[D][05:19:54][COMM]------------ready to Power off Acckey 2------------
[D][05:19:54][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:54][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1296
[D][05:19:54][COMM]bat sleep fail, reason:-1
[D][05:19:54][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:54][COMM]accel parse set 0
[D][05:19:54][COMM]imu rest ok. 125630
[D][05:19:54][COMM]imu sleep 0
[W][05:19:54][COMM]now sleep


2025-07-31 21:02:55:126 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:02:55:135 ==>> 检测【检测33V休眠电流】
2025-07-31 21:02:55:149 ==>> 开始33V电流采样
2025-07-31 21:02:55:174 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:02:55:228 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 21:02:56:232 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 21:02:56:310 ==>> Current33V:????:15.91

2025-07-31 21:02:56:745 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:02:56:754 ==>> 【检测33V休眠电流】通过,【15.91uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:02:56:767 ==>> 该项需要延时执行
2025-07-31 21:02:58:752 ==>> 此处延时了:【2000】毫秒
2025-07-31 21:02:58:766 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 21:02:58:789 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:02:58:875 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1672mV
Get AD_V4 0mV
Get AD_V5 2741mV
Get AD_V6 1920mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:02:59:790 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:02:59:799 ==>> 检测【打开小电池2】
2025-07-31 21:02:59:808 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:02:59:867 ==>> 6A A6 01 A6 6A 


2025-07-31 21:02:59:957 ==>> Battery ON
OVER 150


2025-07-31 21:03:00:086 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:03:00:096 ==>> 该项需要延时执行
2025-07-31 21:03:00:592 ==>> 此处延时了:【500】毫秒
2025-07-31 21:03:00:606 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 21:03:00:620 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:03:00:669 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:03:00:915 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:03:00:930 ==>> 该项需要延时执行
2025-07-31 21:03:01:366 ==>> [D][05:20:01][COMM]------------ready to Power on Acckey 1------------
[D][05:20:01][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:20:01][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:20:01][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:20:01][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:20:01][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:20:01][COMM]----- get Acckey 1 and value:1------------
[W][05:20:01][COMM]CAN START!
[D][05:20:01][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:20:01][CAT1]gsm read msg sub id: 12
[D][05:20:01][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D]

2025-07-31 21:03:01:426 ==>> 此处延时了:【500】毫秒
2025-07-31 21:03:01:440 ==>> 检测【进入休眠模式2】
2025-07-31 21:03:01:472 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:03:01:485 ==>> [05:20:01][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 132073
[D][05:20:01][COMM][Audio]exec status ready.
[D][05:20:01][CAT1]<<< 
OK

[D][05:20:01][CAT1]exec over: func id: 12, ret: 6
[D][05:20:01][COMM]imu wakeup ok. 132087
[D][05:20:01][COMM]imu wakeup 1
[W][05:20:01][COMM]wake up system, wakeupEvt=0x80
[D][05:20:01][COMM]frm_can_weigth_power_set 1
[D][05:20:01][COMM]Clear Sleep Block Evt
[D][05:20:01][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:20:01][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:03:01:763 ==>> [E][05:20:01][COMM]1x1 rx timeout
[D][05:20:01][COMM]1x1 frm_can_tp_send ok
[W][05:20:01][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:20:01][COMM]msg 02A0 loss. last_tick:132058. cur_tick:132567. period:50
[D][05:20:01][COMM]msg 02A4 loss. last_tick:132058. cur_tick:132567. period:50
[D][05:20:01][COMM]msg 02A5 loss. last_tick:132058. cur_tick:132568. period:50
[D][05:20:01][COMM]msg 02A6 loss. last_tick:132058. cur_tick:132568. period:50
[D][05:20:01][COMM]msg 02A7 loss. last_tick:132058. cur_tick:132568. period:50
[D][05:20:01][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 132569
[D][05:20:01][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 132569


2025-07-31 21:03:02:084 ==>> [E][05:20:01][COMM]1x1 rx timeout
[E][05:20:01][COMM]1x1 tp timeout
[E][05:20:01][COMM]1x1 error -3.
[D][05:20:01][COMM]Main Task receive event:28 finished processing
[D][05:20:01][COMM]Main Task receive event:28
[D][05:20:01][COMM]prepare to sleep
[D][05:20:01][CAT1]gsm read msg sub id: 12
[D][05:20:01][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:20:01][CAT1]<<< 
OK

[D][05:20:01][CAT1]exec over: func id: 12, ret: 6
[D][05:20:01][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:20:01][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:03:02:385 ==>> [D][05:20:02][COMM]msg 0220 loss. last_tick:132058. cur_tick:133063. period:100
[D][05:20:02][COMM]msg 0221 loss. last_tick:132058. cur_tick:133063. period:100
[D][05:20:02][COMM]msg 0224 loss. last_tick:132058. cur_tick:133063. period:100
[D][05:20:02][COMM]msg 0260 loss. last_tick:132058. cur_tick:133064. period:100
[D][05:20:02][COMM]msg 0280 loss. last_tick:132058. cur_tick:133064. period:100
[D][05:20:02][COMM]msg 02C0 loss. last_tick:132058. cur_tick:133064. period:100
[D][05:20:02][COMM]msg 02C1 loss. last_tick:132058. cur_tick:133065. period:100
[D][05:20:02][COMM]msg 02C2 loss. last_tick:132058. cur_tick:133065. period:100
[D][05:20:02][COMM]msg 02E0 loss. last_tick:132058. cur_tick:133065. period:100
[D][05:20:02][COMM]msg 02E1 loss. last_tick:132058. cur_tick:133066. period:100
[D][05:20:02][COMM]msg 02E2 loss. last_tick:132058. cur_tick:133066. period:100
[D][05:20:02][COMM]msg 0300 loss. last_tick:132058. cur_tick:133067. period:100
[D][05:20:02][COMM]msg 0301 loss. last_tick:132058. cur_tick:133067. period:100
[D][05:20:02][COMM]bat msg 0240 loss. last_tick:132058. cur_tick:133067. period:100. j,i:1 54
[D][05:20:02][COMM]bat msg 0241 loss. last_tick:132058. cur_tick:13306

2025-07-31 21:03:02:490 ==>> 8. period:100. j,i:2 55
[D][05:20:02][COMM]bat msg 0242 loss. last_tick:132058. cur_tick:133068. period:100. j,i:3 56
[D][05:20:02][COMM]bat msg 0244 loss. last_tick:132058. cur_tick:133069. period:100. j,i:5 58
[D][05:20:02][COMM]bat msg 024E loss. last_tick:132058. cur_tick:133069. period:100. j,i:15 68
[D][05:20:02][COMM]bat msg 024F loss. last_tick:132058. cur_tick:133069. period:100. j,i:16 69
[D][05:20:02][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 133070
[D][05:20:02][COMM]CAN message bat fault change: 0x00000000->0x0001802E 133070
[D][05:20:02][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 133071
                                                                              

2025-07-31 21:03:02:672 ==>> [D][05:20:02][COMM]msg 0222 loss. last_tick:132058. cur_tick:133565. period:150
[D][05:20:02][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 133567


2025-07-31 21:03:02:762 ==>>                                                                                       MM]frm_peripheral_device_poweroff type 16.... 
[D][05:20:02][COMM]------------ready to Power off Acckey 2------------


2025-07-31 21:03:02:958 ==>> [E][05:20:02][COMM]1x1 rx timeout
[E][05:20:02][COMM]1x1 tp timeout
[E][05:20:02][COMM]1x1 error -3.
[W][05:20:02][COMM]CAN STOP!
[D][05:20:02][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:20:02][COMM]------------ready to Power off Acckey 1------------
[D][05:20:02][COMM]------------ready to Power off Acckey 2------------
[D][05:20:02][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:20:02][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 103
[D][05:20:02][COMM]bat sleep fail, reason:-1
[D][05:20:02][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:20:02][COMM]accel parse set 0
[D][05:20:02][COMM]imu rest ok. 133757
[D][05:20:02][COMM]imu sleep 0
[W][05:20:02][COMM]now sleep


2025-07-31 21:03:03:015 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:03:03:024 ==>> 检测【检测小电池休眠电流】
2025-07-31 21:03:03:039 ==>> 开始小电池电流采样
2025-07-31 21:03:03:065 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:03:03:123 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:03:04:129 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:03:04:176 ==>> CurrentBattery:ƽ��:69.47

2025-07-31 21:03:04:639 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:03:04:653 ==>> 【检测小电池休眠电流】通过,【69.47uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:03:04:676 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 21:03:04:689 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:03:04:759 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:03:04:938 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:03:04:957 ==>> 该项需要延时执行
2025-07-31 21:03:05:001 ==>> [D][05:20:04][COMM]------------ready to Power on Acckey 1------------
[D][05:20:04][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:20:04][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:20:04][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 10
[D][05:20:04][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:20:04][COMM]----- get Acckey 1 and value:1------------
[W][05:20:04][COMM]CAN START!
[D][05:20:04][GNSS]handler GSMGet Base timeout
[D][05:20:04][CAT1]gsm read msg sub id: 12
[D][05:20:04][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:20:04][COMM]CAN message bat fault change: 0x0001802E->0x00000000 135755
[D][05:20:04][COMM][Audio]exec status ready.
[D][05:20:04][CAT1]<<< 
OK

[D][05:20:04][CAT1]exec over: func id: 12, ret: 6
[D][05:20:04][COMM]imu wakeup ok. 135770
[D][05:20:04][COMM]imu wakeup 1
[W][05:20:04][COMM]wake up system, wakeupEvt=0x80
[D][05:20:04][COMM]frm_can_weigth_power_set 1
[D][05:20:04][COMM]Clear Sleep Block Evt
[D][05:20:04][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:20:04][COMM]1x1 frm_can_tp_send ok
[D][05:20:04][COMM]read battery soc:0


2025-07-31 21:03:05:275 ==>> [E][05:20:05][COMM]1x1 rx timeout
[D][05:20:05][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:03:05:380 ==>> [D][05:20:05][COMM]msg 02A0 loss. last_tick:135737. cur_tick:136250. period:50
[D][05:20:05][COMM]msg 02A4 loss. last_tick:135737. cur_tick:136250. period:50
[D][05:20:05][COMM]msg 02A5 loss. last_tick:135737. cur_tick:136251. period:50
[D][05

2025-07-31 21:03:05:426 ==>> :20:05][COMM]msg 02A6 loss. last_tick:135737. cur_tick:136251. period:50
[D][05:20:05][COMM]msg 02A7 loss. last_tick:135737. cur_tick:136251. period:50
[D][05:20:05][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 136252
[D][05:20:05][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 136252


2025-07-31 21:03:05:442 ==>> 此处延时了:【500】毫秒
2025-07-31 21:03:05:450 ==>> 检测【检测唤醒】
2025-07-31 21:03:05:465 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:03:06:229 ==>> [W][05:20:05][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:20:05][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:05][FCTY]==========Modules-nRF5340 ==========
[D][05:20:05][FCTY]BootVersion = SA_BOOT_V109
[D][05:20:05][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:20:05][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:20:05][FCTY]DeviceID    = 460130071539069
[D][05:20:05][FCTY]HardwareID  = 867222087567105
[D][05:20:05][FCTY]MoBikeID    = 9999999999
[D][05:20:05][FCTY]LockID      = FFFFFFFFFF
[D][05:20:05][FCTY]BLEFWVersion= 105
[D][05:20:05][FCTY]BLEMacAddr   = C25702076663
[D][05:20:05][FCTY]Bat         = 3844 mv
[D][05:20:05][FCTY]Current     = 0 ma
[D][05:20:05][FCTY]VBUS        = 2600 mv
[D][05:20:05][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:20:05][FCTY]Ext battery vol = 32, adc = 1296
[D][05:20:05][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 0 mv
[D][05:20:05][FCTY]Bike Type flag is invalied
[D][05:20:05][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:20:05][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:20:05][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:20:05][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:20:05][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:20:05][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:2

2025-07-31 21:03:06:334 ==>> 0:05][FCTY]Bat1         = 3781 mv
[D][05:20:05][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:20:05][FCTY]==========Modules-nRF5340 ==========
[E][05:20:05][COMM]1x1 rx timeout
[E][05:20:05][COMM]1x1 tp timeout
[D][05:20:05][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:20:05][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[E][05:20:05][COMM]1x1 error -3.
[D][05:20:05][COMM]Main Task receive event:28 finished processing
[D][05:20:05][COMM]Main Task receive event:65
[D][05:20:05][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:20:05][COMM]Main Task receive event:65 finished processing
[D][05:20:05][COMM]Main Task receive event:60
[D][05:20:05][COMM]smart_helmet_vol=255,255
[D][05:20:05][COMM]report elecbike
[W][05:20:05][PROT]remove success[1629955205],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:20:05][PROT]add success [1629955205],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:20:05][COMM]Main Task receive event:60 finished processing
[D][05:20:05][PROT]min_index:0, type:0x5D03, priority:3
[D][05:20:05][PROT]index:0
[D][05:20:05][PROT]is_send:1
[D][05:20:05][PROT]sequence_num:12
[D

2025-07-31 21:03:06:439 ==>> ][05:20:05][PROT]retry_timeout:0
[D][05:20:05][PROT]retry_times:3
[D][05:20:05][PROT]send_path:0x3
[D][05:20:05][PROT]msg_type:0x5d03
[D][05:20:05][PROT]===========================================================
[W][05:20:05][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955205]
[D][05:20:05][PROT]===========================================================
[D][05:20:05][PROT]Sending traceid[999999999990000D]
[D][05:20:05][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:20:05][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:20:05][PROT]ble is not inited or not connected or cccd not enabled
[D][05:20:05][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:05][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:20:05][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:20:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:20:05][SAL ]open socket ind id[4], rst[0]
[D][05:20:05][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:20:05][SAL ]Cellular task submsg id[8]
[D][05:20:05][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:20:05][SAL ]domain[bikeapi.mobik

2025-07-31 21:03:06:521 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 21:03:06:531 ==>> 检测【关机】
2025-07-31 21:03:06:541 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:03:06:567 ==>> e.com] port[9999] type[1]
[D][05:20:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:20:05][CAT1]gsm read msg sub id: 8
[D][05:20:05][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:20:05][CAT1]<<< 
+CGATT: 0

OK

[D][05:20:05][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:20:05][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:20:05][CAT1]<<< 
+CME ERROR: 100

[D][05:20:05][COMM]msg 0220 loss. last_tick:135737. cur_tick:136748. period:100
[D][05:20:05][COMM]msg 0221 loss. last_tick:135737. cur_tick:136748. period:100
[D][05:20:05][COMM]msg 0224 loss. last_tick:135737. cur_tick:136749. period:100
[D][05:20:05][COMM]msg 0260 loss. last_tick:135737. cur_tick:136749. period:100
[D][05:20:05][COMM]msg 0280 loss. last_tick:135737. cur_tick:136750. period:100
[D][05:20:05][COMM]msg 02C0 loss. last_tick:135737. cur_tick:136750. period:100
[D][05:20:05][COMM]msg 02C1 loss. last_tick:135737. cur_tick:136750. period:100
[D][05:20:05][COMM]msg 02C2 loss. last_tick:135737. cur_tick:136751. period:100
[D][05:20:05][COMM]msg 02E0 loss. last_tick:135737. cur_tick:136751. period:100
[D][05:20:05][COMM]msg 02E1 loss. last_tick:135737. cur_tick:136751. period:100
[D][05:20:05][COMM]msg 02E2 los

2025-07-31 21:03:06:649 ==>> s. last_tick:135737. cur_tick:136752. period:100
[D][05:20:05][COMM]msg 0300 loss. last_tick:135737. cur_tick:136752. period:100
[D][05:20:05][COMM]msg 0301 loss. last_tick:135737. cur_tick:136752. period:100
[D][05:20:05][COMM]bat msg 0240 loss. last_tick:135737. cur_tick:136753. period:100. j,i:1 54
[D][05:20:05][COMM]bat msg 0241 loss. last_tick:135737. cur_tick:136753. period:100. j,i:2 55
[D][05:20:05][COMM]bat msg 0242 loss. last_tick:135737. cur_tick:136754. period:100. j,i:3 56
[D][05:20:05][COMM]bat msg 0244 loss. last_tick:135737. cur_tick:136754. period:100. j,i:5 58
[D][05:20:05][COMM]bat msg 024E loss. last_tick:135737. cur_tick:136754. period:100. j,i:15 68
[D][05:20:05][COMM]bat msg 024F loss. last_tick:135737. cur_tick:136755. period:100. j,i:16 69
[D][05:20:05][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 136755
[D][05:20:05][COMM]CAN message bat fault change: 0x00000000->0x0001802E 136755
[D][05:20:05][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 136756


2025-07-31 21:03:07:267 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 21:03:07:372 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 21:03:07:477 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             PROT]msg_type:0x5d03
[D][05:20:06][PROT]===========================================================
[W][05:20:06][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955206]
[D][05:20:06][PROT]===========================================================
[D][05:20:06][PROT]Sending traceid[999999999990000E]
[D][05:20:06][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:20:06][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:20:06

2025-07-31 21:03:07:567 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:03:07:584 ==>> ][PROT]ble is not inited or not connected or cccd not enabled
[D][05:20:06][COMM]Receive Bat Lock cmd 0
[D][05:20:06][COMM]VBUS is 1
[D][05:20:06][COMM]Main Task receive event:61
[D][05:20:06][COMM][D301]:type:3, trace id:280
[D][05:20:06][COMM]id[], hw[000
[D][05:20:06][COMM]get mcMaincircuitVolt error
[D][05:20:06][COMM]get mcSubcircuitVolt error
[D][05:20:06][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:20:06][COMM]BAT CAN get state1 Fail 204
[D][05:20:06][COMM]BAT CAN get soc Fail, 204
[D][05:20:06][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:20:06][COMM]BAT CAN get state2 fail 204
[D][05:20:06][COMM]get bat work mode err
[D][05:20:06][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[D][05:20:06][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:20:06][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:20:06][PROT]remove success[1629955206],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:20:06][COMM]--->crc16:0xb8a
[D][05:20:06][COMM]read file success
[W][05:20:06][COMM][Audio].l:[936].clos

2025-07-31 21:03:07:687 ==>> e hexlog save
[D][05:20:06][COMM]accel parse set 1
[D][05:20:06][COMM][Audio]mon:9,05:20:06
[D][05:20:06][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:20:06][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:20:06][PROT]add success [1629955206],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:20:06][COMM]Main Task receive event:61 finished processing
[D][05:20:06][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:20:06][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:20:06][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:20:06][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:06][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:20:06][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:20:0

2025-07-31 21:03:07:792 ==>> 6][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:20:06][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:06][COMM]arm_hub_enable: hub power: 0
[D][05:20:06][HSDK]hexlog index save 0 4096 18 @ 0 : 0
[D][05:20:06][HSDK]write save hexlog index [0]
[D][05:20:06][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:06][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, le

2025-07-31 21:03:07:852 ==>> n:2048
[D][05:20:06][COMM]read battery soc:255
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:20:06][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:20:06][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:20:06][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:20:06][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 21:03:07:959 ==>>                               [W][05:20:07][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:20:07][COMM]arm_hub_enable: hub power: 0
[D][05:20:07][HSDK]hexlog index save 0 4096 18 @ 0 : 0
[D][05:20:07][HSDK]write sav

2025-07-31 21:03:08:004 ==>> e hexlog index [0]
[D][05:20:07][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:20:07][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 21:03:08:446 ==>> [W][05:20:08][COMM]Power Off


2025-07-31 21:03:08:633 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 21:03:08:642 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 21:03:08:650 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:03:08:768 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:03:08:843 ==>> [D][05:20:08][FCT

2025-07-31 21:03:08:920 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:03:08:929 ==>> 检测【检测小电池关机电流】
2025-07-31 21:03:08:944 ==>> 开始小电池电流采样
2025-07-31 21:03:08:969 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:03:09:023 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:03:10:032 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:03:10:078 ==>> CurrentBattery:ƽ��:68.71

2025-07-31 21:03:10:540 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:03:10:550 ==>> 【检测小电池关机电流】通过,【68.71uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 21:03:10:995 ==>> MES过站成功
2025-07-31 21:03:11:013 ==>> #################### 【测试结束】 ####################
2025-07-31 21:03:11:048 ==>> 关闭5V供电
2025-07-31 21:03:11:068 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:03:11:173 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:03:12:056 ==>> 关闭5V供电成功
2025-07-31 21:03:12:072 ==>> 关闭33V供电
2025-07-31 21:03:12:099 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:03:12:164 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:03:13:068 ==>> 关闭33V供电成功
2025-07-31 21:03:13:084 ==>> 关闭3.7V供电
2025-07-31 21:03:13:110 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:03:13:162 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:03:13:955 ==>>  

2025-07-31 21:03:14:075 ==>> 关闭3.7V供电成功
