2025-07-31 21:15:13:677 ==>> MES查站成功:
查站序号:P510001005313282验证通过
2025-07-31 21:15:13:692 ==>> 扫码结果:P510001005313282
2025-07-31 21:15:13:694 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:15:13:696 ==>> 测试参数版本:2024.10.11
2025-07-31 21:15:13:697 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:15:13:699 ==>> 检测【打开透传】
2025-07-31 21:15:13:703 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:15:13:764 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:15:14:237 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:15:14:258 ==>> 检测【检测接地电压】
2025-07-31 21:15:14:261 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:15:14:376 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:15:14:563 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:15:14:566 ==>> 检测【打开小电池】
2025-07-31 21:15:14:569 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:15:14:667 ==>> 6A A6 01 A6 6A 


2025-07-31 21:15:14:772 ==>> Battery ON
OVER 150


2025-07-31 21:15:14:857 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:15:14:859 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:15:14:860 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:15:14:969 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:15:15:169 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:15:15:171 ==>> 检测【等待设备启动】
2025-07-31 21:15:15:174 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:15:15:613 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:15:15:795 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:15:16:209 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:15:16:424 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:15:16:499 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:15:16:894 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:15:17:243 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:15:17:364 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:15:17:566 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:15:17:569 ==>> 检测【产品通信】
2025-07-31 21:15:17:570 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:15:18:046 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:15:18:229 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:15:18:593 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:15:18:888 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<


2025-07-31 21:15:18:948 ==>> [W][05:17:49][GNSS]start sing locating


2025-07-31 21:15:19:346 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:15:19:638 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:15:19:896 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]1614 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:20:186 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:15:20:188 ==>> 检测【初始化完成检测】
2025-07-31 21:15:20:190 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:15:20:383 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:15:20:458 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:15:20:475 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:15:20:477 ==>> 检测【关闭大灯控制1】
2025-07-31 21:15:20:478 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:15:20:638 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:15:20:763 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:15:20:766 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:15:20:768 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:15:20:878 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:21:073 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:15:21:322 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:15:21:333 ==>> 检测【关闭仪表供电】
2025-07-31 21:15:21:335 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:15:21:562 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:15:21:636 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:15:21:638 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:15:21:640 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:15:21:884 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3636 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:21:939 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:15:21:941 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:15:21:943 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:15:22:129 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:15:22:239 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:15:22:242 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:15:22:243 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:22:465 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:15:22:527 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:15:22:529 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:15:22:530 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:15:22:663 ==>> 5A A5 01 5A A5 


2025-07-31 21:15:22:768 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:15:22:814 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:15:22:816 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:15:22:818 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:15:22:873 ==>> 5A A5 03 5A A5 
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26
[D][05:17:53][COMM]read ba

2025-07-31 21:15:22:903 ==>> ttery soc:255
[D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:22:962 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 21:15:23:109 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:15:23:111 ==>> 该项需要延时执行
2025-07-31 21:15:23:433 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5014. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5016. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5017. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5018
[D][05:17:54][COMM]CAN message bat fault

2025-07-31 21:15:23:463 ==>>  change: 0x0001802E->0x01B987FE 5018


2025-07-31 21:15:23:895 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:24:431 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:15:24:997 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main ta

2025-07-31 21:15:25:102 ==>> sk tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2

2025-07-31 21:15:25:206 ==>> M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],

2025-07-31 21:15:25:296 ==>> priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 21:15:25:939 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:26:946 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:27:113 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:15:27:116 ==>> 检测【33V输入电压ADC】
2025-07-31 21:15:27:119 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:27:388 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:17:57][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:17:57][COMM]adc read left brake adc:1  volt:1 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:2 mv
[D][05:17:57][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2492  volt:4015 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:15:27:665 ==>> 【33V输入电压ADC】通过,【32273mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:15:27:668 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:15:27:671 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:15:27:780 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 2025mV
Get AD_V7 1093mV
OVER 150


2025-07-31 21:15:27:946 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:27:950 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:15:27:963 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:15:27:977 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:27:979 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:15:27:981 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 21:15:28:008 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:15:28:011 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:15:28:041 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2025mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:15:28:044 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:15:28:075 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:15:28:078 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:15:28:174 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1650mV
Get AD_V4 1mV
Get AD_V5 2768mV
Get AD_V6 1986mV
Get AD_V7 1094mV
OVER 150


2025-07-31 21:15:28:279 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10008
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01

2025-07-31 21:15:28:309 ==>>  10008


2025-07-31 21:15:28:387 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:28:389 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:15:28:430 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:28:433 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:15:28:435 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 21:15:28:464 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:15:28:468 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:15:28:499 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1986mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:15:28:501 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:15:28:542 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:15:28:544 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:15:28:673 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1647mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 2024mV
Get AD_V7 1093mV
OVER 150


2025-07-31 21:15:28:778 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:15:28:834 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:28:837 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:15:28:868 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:28:885 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:15:28:888 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 21:15:28:911 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:15:28:915 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:15:28:950 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:15:28:962 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:15:28:984 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:15:28:986 ==>> 检测【打开WIFI(1)】
2025-07-31 21:15:28:988 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:15:29:235 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][COMM][Audio]exec st

2025-07-31 21:15:29:280 ==>> atus ready.
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:15:29:525 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:15:29:529 ==>> 检测【清空消息队列(1)】
2025-07-31 21:15:29:532 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:15:29:626 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                    [D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539012

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:15:29:716 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:15:29:806 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:15:29:809 ==>> 检测【打开GPS(1)】
2025-07-31 21:15:29:811 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:15:29:972 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:15:30:094 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:15:30:096 ==>> 检测【打开GSM联网】
2025-07-31 21:15:30:099 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:15:30:356 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:15:30:657 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:15:30:660 ==>> 检测【打开仪表供电1】
2025-07-31 21:15:30:662 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:15:30:913 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 21:15:31:202 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:15:31:205 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:15:31:208 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:15:31:371 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:15:31:501 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:15:31:504 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:15:31:506 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:15:31:659 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33479]


2025-07-31 21:15:31:793 ==>> 【读取主控ADC采集的仪表电压】通过,【33479mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:15:31:796 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:15:31:798 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:15:31:964 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13726 imu init OK


2025-07-31 21:15:32:083 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:15:32:086 ==>> 检测【AD_V20电压】
2025-07-31 21:15:32:088 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:15:32:192 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:15:32:269 ==>> 本次取值间隔时间:64ms
2025-07-31 21:15:32:314 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1639mV
OVER 150


2025-07-31 21:15:32:449 ==>> 本次取值间隔时间:166ms
2025-07-31 21:15:32:480 ==>> 【AD_V20电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:32:483 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:15:32:485 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:15:32:569 ==>> 3A A3 02 00 A3 


2025-07-31 21:15:32:674 ==>> OFF_OUT2
OVER 150


2025-07-31 21:15:32:769 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:15:32:772 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:15:32:774 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:15:33:010 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 21:15:33:355 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GP

2025-07-31 21:15:33:460 ==>> SCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 21:15:33:795 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:15:33:990 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:15:34:035 ==>>                                                                                                                                                                  

2025-07-31 21:15:34:083 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:15:34:088 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:15:34:092 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:15:34:171 ==>> 3A A3 02 01 A3 


2025-07-31 21:15:34:276 ==>> ON_OUT2
OVER 150


2025-07-31 21:15:34:372 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:15:34:377 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:15:34:380 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:15:34:585 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:15:34:662 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:15:34:665 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:15:34:667 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:15:34:690 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>>

2025-07-31 21:15:34:720 ==>>  AT+GPSNAVSAT=1F



2025-07-31 21:15:34:945 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,43,25,,,41,39,,,39,40,,,38,1*78

$GBGSV,2,2,07,41,,,38,34,,,35,24,,,32,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1575.439,1575.439,50.421,2097152,2097152,2097152*4D

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
 

2025-07-31 21:15:34:975 ==>>                                         

2025-07-31 21:15:35:080 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:15:35:271 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:15:35:274 ==>> 检测【AD_V21电压】
2025-07-31 21:15:35:277 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:15:35:365 ==>> 1A A1 20 00 00 
Get AD_V21 1636mV
OVER 150


2025-07-31 21:15:35:470 ==>> 本次取值间隔时间:186ms
2025-07-31 21:15:35:576 ==>> 【AD_V21电压】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:35:580 ==>> 检测【关闭仪表供电2】
2025-07-31 21:15:35:584 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:15:35:865 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,43,25,,,41,60,,,41,39,,,39,1*77

$GBGSV,4,2,13,41,,,39,40,,,38,24,,,37,34,,,36,1*72

$GBGSV,4,3,13,14,,,36,1,,,36,44,,,35,2,,,35,1*75

$GBGSV,4,4,13,3,,,41,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1575.396,1575.396,50.377,2097152,2097152,2097152*49



2025-07-31 21:15:36:140 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:15:36:146 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:15:36:168 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:15:36:363 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:15:36:422 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:15:36:425 ==>> 检测【打开AccKey2供电】
2025-07-31 21:15:36:428 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:15:36:649 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:15:36:713 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:15:36:727 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:15:36:732 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:37:026 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,25,,,41,60,,,40,3,,,40,1*45

$GBGSV,4,2,16,39,,,39,41,,,39,40,,,38,24,,,38,1*7A

$GBGSV,4,3,16,59,,,38,14,,,37,1,,,37,34,,,36,1*47

$GBGSV,4,4,16,44,,,35,2,,,35,5,,,34,4,,,32,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1557.263,1557.263,49.803,2097152,2097152,2097152*49

[D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:07][COMM]adc read out 24v adc:1307  volt:33057 mv
[D][05:18:07][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:07][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:07][COMM]adc read battery ts volt:9 mv
[D][05:18:07][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2438  volt:3928 mv
[D][05:18:07][

2025-07-31 21:15:37:070 ==>> COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:15:37:257 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33057mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:15:37:260 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:15:37:265 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:15:37:437 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:15:37:561 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:15:37:564 ==>> 该项需要延时执行
2025-07-31 21:15:37:893 ==>> $GBGGA,131541.695,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,25,,,41,3,,,41,60,,,40,1*4A

$GBGSV,5,2,19,24,,,40,39,,,39,41,,,39,59,,,39,1*72

$GBGSV,5,3,19,40,,,38,14,,,37,1,,,37,34,,,36,1*41

$GBGSV,5,4,19,44,,,35,2,,,35,5,,,33,4,,,32,1*4D

$GBGSV,5,5,19,13,,,38,7,,,36,10,,,36,1*41

$GBRMC,131541.695,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131541.695,0.000,1565.048,1565.048,50.063,2097152,2097152,2097152*58



2025-07-31 21:15:38:797 ==>> $GBGGA,131542.595,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,25,,,41,3,,,41,60,,,40,1*40

$GBGSV,6,2,23,24,,,40,59,,,40,39,,,39,41,,,39,1*76

$GBGSV,6,3,23,40,,,38,14,,,38,1,,,37,7,,,37,1*75

$GBGSV,6,4,23,16,,,37,34,,,36,6,,,36,44,,,35,1*41

$GBGSV,6,5,23,2,,,35,9,,,35,10,,,34,12,,,34,1*7D

$GBGSV,6,6,23,5,,,33,4,,,32,23,,,31,1*74

$GBRMC,131542.595,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131542.595,0.000,1532.160,1532.160,49.020,2097152,2097152,2097152*57



2025-07-31 21:15:38:947 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:15:39:776 ==>> $GBGGA,131543.575,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,25,,,41,3,,,41,60,,,41,1*47

$GBGSV,6,2,24,24,,,41,59,,,40,39,,,39,41,,,39,1*70

$GBGSV,6,3,24,40,,,39,14,,,38,1,,,37,7,,,37,1*73

$GBGSV,6,4,24,16,,,37,42,,,37,34,,,36,6,,,36,1*42

$GBGSV,6,5,24,44,,,35,2,,,35,9,,,35,10,,,34,1*78

$GBGSV,6,6,24,12,,,33,23,,,33,5,,,32,4,,,32,1*73

$GBRMC,131543.575,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131543.575,0.000,1539.149,1539.149,49.248,2097152,2097152,2097152*54



2025-07-31 21:15:40:569 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:15:40:574 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:15:40:578 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:40:888 ==>> $GBGGA,131544.555,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,42,,,36,34,,,36,6,,,36,1*40

$GBGSV,7,5,26,9,,,36,44,,,35,2,,,35,38,,,35,1*73

$GBGSV,7,6,26,10,,,34,13,,,34,23,,,34,12,,,33,1*75

$GBGSV,7,7,26,5,,,33,4,,,32,1*72

$GBRMC,131544.555,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131544.555,0.000,1530.768,1530.768,48.970,2097152,2097152,2097152*50

[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:6 mv
[D][05:18:11][COMM]adc read in 24v adc:1271  volt:32147 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11]

2025-07-31 21:15:40:933 ==>> [COMM]arm_hub adc read vbat adc:2471  volt:3981 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:15:40:963 ==>>                                          

2025-07-31 21:15:41:118 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:15:41:121 ==>> 检测【打开AccKey1供电】
2025-07-31 21:15:41:126 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:15:41:253 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:15:41:408 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:15:41:411 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:15:41:414 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:15:41:467 ==>> 1A A1 00 40 00 
Get AD_V14 2557mV
OVER 150


2025-07-31 21:15:41:665 ==>> 原始值:【2557】, 乘以分压基数【2】还原值:【5114】
2025-07-31 21:15:41:702 ==>> 【读取AccKey1电压(ADV14)前】通过,【5114mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:15:41:708 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:15:41:711 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:41:740 ==>> $GBGGA,131545.535,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,39,14,,,38,1,,,37,7,,,37,1*70

$GBGSV,7,4,26,16,,,37,42,,,36,34,,,36,6,,,36,1*40

$GBGSV,7,5,26,9,,,36,44,,,35,2,,,35,38,,,35,1*73

$GBGSV,7,6,26,13,,,35,10,,,34,23,,,34,12,,,33,1*74

$GBGSV,7,7,26,5,,,32,4,,,32,1*73

$GBRMC,131545.535,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131545.535,0.000,1532.365,1532.365,49.023,2097152,2097152,2097152*59



2025-07-31 21:15:41:982 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:12][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:7 mv
[D][05:18:12][COMM]adc read in 24v adc:1270  volt:32122 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2478  volt:3992 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:15:42:267 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5519mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:15:42:271 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:15:42:273 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:15:42:459 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:15:42:552 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:15:42:558 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:15:42:563 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:15:42:744 ==>> 1A A1 00 40 00 
Get AD_V14 2561mV
OVER 150
$GBGGA,131546.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,39,14,,,38,1,,,37,7,,,37,1*70

$GBGSV,7,4,26,16,,,37,42,,,36,34,,,36,6,,,36,1*40

$GBGSV,7,5,26,9,,,36,44,,,35,2,,,35,38,,,35,1*73

$GBGSV,7,6,26,13,,,35,23,,,35,10,,,34,12,,,33,1*75

$GBGSV,7,7,26,5,,,32,4,,,32,1*73

$GBRMC,131546.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131546.515,0.000,1533.957,1533.957,49.073,2097152,2097152,2097152*5D



2025-07-31 21:15:42:804 ==>> 原始值:【2561】, 乘以分压基数【2】还原值:【5122】
2025-07-31 21:15:42:840 ==>> 【读取AccKey1电压(ADV14)后】通过,【5122mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:15:42:844 ==>> 检测【打开WIFI(2)】
2025-07-31 21:15:42:849 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:15:42:971 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:15:43:076 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:15:43:123 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:15:43:128 ==>> 检测【转刹把供电】
2025-07-31 21:15:43:132 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:15:43:349 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:15:43:411 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:15:43:414 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:15:43:417 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:15:43:517 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:15:43:577 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2406mV
OVER 150


2025-07-31 21:15:43:682 ==>> 原始值:【2406】, 乘以分压基数【2】还原值:【4812】
2025-07-31 21:15:43:687 ==>> $GBGGA,131547.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,59,,,41,60,,,40,39,,,39,41,,,39,1*73

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,2,,,35,38,,,35,1*7F

$GBGSV,7,6,26,13,,,35,23,,,35,10,,,34,12,,,33,1*75

$GBGSV,7,7,26,4,,,33,5,,,32,1*72

2025-07-31 21:15:43:712 ==>> 【读取AD_V15电压(前)】通过,【4812mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:15:43:716 ==>> 

$GBRMC,131547.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131547.515,0.000,1532.363,1532.363,49.022,2097152,2097152,2097152*58



2025-07-31 21:15:43:721 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:15:43:745 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:15:43:817 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:15:43:877 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2438mV
OVER 150


2025-07-31 21:15:43:967 ==>> 原始值:【2438】, 乘以分压基数【2】还原值:【4876】
2025-07-31 21:15:44:003 ==>> 【读取AD_V16电压(前)】通过,【4876mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:15:44:057 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:15:44:066 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:44:348 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:14][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:14][COMM]adc read battery ts volt:14 mv
[D][05:18:14][COMM]adc read in 24v adc:1271  volt:32147 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2491  volt:4013 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-63
+WIFISCAN:4,2,CC057790A5C1,-71
+WIFISCAN:4,3,74C330CCAB10,-73

[D][05:18:15][CAT1]wifi scan report total[4]


2025-07-31 21:15:44:543 ==>> 【转刹把供电电压(主控ADC)】通过,【5407mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:15:44:547 ==>> 检测【转刹把供电电压】
2025-07-31 21:15:44:549 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:44:714 ==>> $GBGGA,131548.515,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,39,14,,,38,1,,,38,7,,,37,1*7F

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,36,1*4F

$GBGSV,7,5,26,6,,,35,44,,,35,2,,,35,38,,,35,1*7F

$GBGSV,7,6,26,13,,,35,23,,,35,10,,,34,12,,,33,1*75

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131548.515,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131548.515,0.000,1537.141,1537.141,49.170,2097152,2097152,2097152*51



2025-07-31 21:15:44:939 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:7 mv
[D][05:18:15][COMM]adc read in 24v adc:1274  volt:32223 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:15:44:984 ==>>                                          

2025-07-31 21:15:45:089 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 21:15:45:092 ==>> 【转刹把供电电压】通过,【5407mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:15:45:097 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:15:45:100 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:45:254 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:15:45:373 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:15:45:380 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:15:45:385 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:45:486 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:15:45:576 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:15:45:620 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:15:45:624 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:15:45:629 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:45:681 ==>> $GBGGA,131549.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,24,,,41,3,,,40,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,2,,,35,38,,,35,1*7F

$GBGSV,7,6,26,13,,,35,23,,,35,10,,,34,12,,,33,1*75

$

2025-07-31 21:15:45:726 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:15:45:756 ==>> GBGSV,7,7,26,4,,,33,5,,,32,1*72

$GBRMC,131549.515,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131549.515,0.000,1527.571,1527.571,48.861,2097152,2097152,2097152*58

                                                                        

2025-07-31 21:15:45:831 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:45:938 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:15:46:047 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:46:139 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:15:46:154 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:15:46:244 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:15:46:259 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:15:46:274 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:15:46:364 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:15:46:469 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:15:46:501 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:15:46:505 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:15:46:508 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:15:46:574 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:15:46:679 ==>> $GBGGA,131550.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,24,,,41,3,,,40,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,2,,,35,38,,,35,1*7F

$GBGSV,7,6,26,13,,,35,23,,,35,10,,,34,12,,,33,1*75

$G

2025-07-31 21:15:46:724 ==>> BGSV,7,7,26,4,,,33,5,,,32,1*72

$GBRMC,131550.515,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131550.515,0.000,1527.571,1527.571,48.861,2097152,2097152,2097152*50



2025-07-31 21:15:46:787 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:15:46:792 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:15:46:795 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:15:46:874 ==>> 3A A3 04 01 A3 


2025-07-31 21:15:46:980 ==>> ON_OUT4
OVER 150
[D][05:18:17][COMM]read battery soc:255


2025-07-31 21:15:47:085 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:15:47:091 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:15:47:096 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:15:47:178 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:15:47:393 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:15:47:397 ==>> 检测【左刹电压测试1】
2025-07-31 21:15:47:403 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:47:754 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:18][COMM]adc read right brake adc:1717  volt:2263 mv
[D][05:18:18][COMM]adc read throttle adc:1722  volt:2270 mv
[D][05:18:18][COMM]adc read battery ts volt:7 mv
[D][05:18:18][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2491  volt:4013 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:2  volt:46 mv
$GBGGA,131551.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,40,3,,,40,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,2,,,35,38,,,35,1*7F

$GBGSV,7,6,26,13,,,35,23,,,34,10,,,34,12,,,33,1*74


2025-07-31 21:15:47:799 ==>> 
$GBGSV,7,7,26,4,,,33,5,,,32,1*72

$GBRMC,131551.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131551.515,0.000,1525.980,1525.980,48.813,2097152,2097152,2097152*54



2025-07-31 21:15:47:940 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 21:15:47:944 ==>> 检测【右刹电压测试1】
2025-07-31 21:15:47:979 ==>> 【右刹电压测试1】通过,【2263】符合目标值【2250】至【2500】要求!
2025-07-31 21:15:47:983 ==>> 检测【转把电压测试1】
2025-07-31 21:15:48:026 ==>> 【转把电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 21:15:48:029 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:15:48:034 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:15:48:166 ==>> 3A A3 03 00 A3 


2025-07-31 21:15:48:271 ==>> OFF_OUT3
OVER 150


2025-07-31 21:15:48:319 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:15:48:323 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:15:48:326 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:15:48:376 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:15:48:606 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:15:48:612 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:15:48:618 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:15:48:726 ==>> $GBGGA,131552.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,34,23,,,34,10,,,34,12,,,33,1*45

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131552.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131552.515,0.000,1527.575,1527.575,48.865,2097152,2097152,2097152*56

3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:15:48:895 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:15:48:900 ==>> 检测【左刹电压测试2】
2025-07-31 21:15:48:905 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:15:49:194 ==>> [D][05:18:19][COMM]read battery soc:255
[D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:3 mv
[D][05:18:19][COMM]adc read in 24v adc:1269  volt:32096 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2494  volt:4018 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 21:15:49:445 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 21:15:49:449 ==>> 检测【右刹电压测试2】
2025-07-31 21:15:49:482 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:15:49:485 ==>> 检测【转把电压测试2】
2025-07-31 21:15:49:519 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 21:15:49:526 ==>> 检测【晶振检测】
2025-07-31 21:15:49:532 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:15:49:752 ==>> $GBGGA,131553.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,34,23,,,34,10,,,34,12,,,33,1*45

$GBGSV,7,7,26,4,,,32,5,,,32,1*73

$GBRMC,131553.515,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131553.515,0.000,1524.392,1524.392,48.769,2097152,2097152,2097152*54

[W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 21:15:49:817 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:15:49:821 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:15:49:826 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:15:49:857 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1650mV
Get AD_V4 1651mV
Get AD_V5 2767mV
Get AD_V6 1988mV
Get AD_V7 

2025-07-31 21:15:49:889 ==>> 1094mV
OVER 150


2025-07-31 21:15:50:178 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:15:50:182 ==>> 检测【检测BootVer】
2025-07-31 21:15:50:185 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:15:50:527 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539012
[D][05:18:21][FCTY]HardwareID  = 867222087567642
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = EEE4C55A18A3
[D][05:18:21][FCTY]Bat         = 3944 mv
[D][05:18:21][FCTY]Current     = 50 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1280
[D][05:18:21][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS

2025-07-31 21:15:50:572 ==>> _PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3736 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:15:50:677 ==>>                                       

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,1,,,37,7,,,37,1*71

$GBGSV,7,4,26,16,,,37,34,,,36,9,,,36,42,,,35,1*4C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,

2025-07-31 21:15:50:722 ==>> 34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131554.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131554.515,0.000,1529.168,1529.168,48.914,2097152,2097152,2097152*57



2025-07-31 21:15:50:745 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:15:50:749 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:15:50:754 ==>> 检测【检测固件版本】
2025-07-31 21:15:50:806 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:15:50:813 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:15:50:818 ==>> 检测【检测蓝牙版本】
2025-07-31 21:15:50:850 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:15:50:854 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:15:50:866 ==>> 检测【检测MoBikeId】
2025-07-31 21:15:50:896 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:15:50:900 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:15:50:905 ==>> 检测【检测蓝牙地址】
2025-07-31 21:15:50:912 ==>> 取到目标值:EEE4C55A18A3
2025-07-31 21:15:50:948 ==>> 【检测蓝牙地址】通过,【EEE4C55A18A3】符合目标值【】要求!
2025-07-31 21:15:50:954 ==>> 提取到蓝牙地址:EEE4C55A18A3
2025-07-31 21:15:50:959 ==>> 检测【BOARD_ID】
2025-07-31 21:15:50:992 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:15:50:996 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:15:50:999 ==>> 检测【检测充电电压】
2025-07-31 21:15:51:045 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:15:51:048 ==>> 检测【检测VBUS电压1】
2025-07-31 21:15:51:096 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:15:51:100 ==>> 检测【检测充电电流】
2025-07-31 21:15:51:175 ==>> 【检测充电电流】通过,【50ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:15:51:188 ==>> 检测【检测IMEI】
2025-07-31 21:15:51:212 ==>> 取到目标值:867222087567642
2025-07-31 21:15:51:226 ==>> 【检测IMEI】通过,【867222087567642】符合目标值【】要求!
2025-07-31 21:15:51:230 ==>> 提取到IMEI:867222087567642
2025-07-31 21:15:51:233 ==>> 检测【检测IMSI】
2025-07-31 21:15:51:241 ==>> 取到目标值:460130071539012
2025-07-31 21:15:51:308 ==>> 【检测IMSI】通过,【460130071539012】符合目标值【】要求!
2025-07-31 21:15:51:312 ==>> 提取到IMSI:460130071539012
2025-07-31 21:15:51:318 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:15:51:324 ==>> 取到目标值:460130071539012
2025-07-31 21:15:51:345 ==>> 【校验网络运营商(移动)】通过,【460130071539012】符合目标值【】要求!
2025-07-31 21:15:51:349 ==>> 检测【打开CAN通信】
2025-07-31 21:15:51:354 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:15:51:470 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:15:51:652 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:15:51:658 ==>> 检测【检测CAN通信】
2025-07-31 21:15:51:664 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:15:51:757 ==>> $GBGGA,131555.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,16,,,38,1,,,37,1*4E

$GBGSV,7,4,26,7,,,37,34,,,36,9,,,36,42,,,35,1*7C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131555.515,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131555.515,0.000,1530.763,1530.763,48.965,2097152,2097152,2097152*50

can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:15:51:817 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33564
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:15:51:877 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:15:51:937 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:15:51:941 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:15:51:945 ==>> 检测【关闭CAN通信】
2025-07-31 21:15:51:948 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:15:51:997 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:15:52:072 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:15:52:227 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:15:52:231 ==>> 检测【打印IMU STATE】
2025-07-31 21:15:52:237 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:15:52:468 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:15:52:516 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:15:52:520 ==>> 检测【六轴自检】
2025-07-31 21:15:52:523 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:15:52:753 ==>> $GBGGA,131556.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,3,,,41,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,16,,,37,1,,,37,1*41

$GBGSV,7,4,26,7,,,37,34,,,36,9,,,36,42,,,35,1*7C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131556.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131556.515,0.000,1530.765,1530.765,48.967,2097152,2097152,2097152*51

[W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:15:53:011 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:15:53:720 ==>> $GBGGA,131557.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,24,,,40,3,,,40,1*45

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,16,,,37,1,,,37,1*41

$GBGSV,7,4,26,7,,,37,34,,,36,9,,,36,42,,,35,1*7C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131557.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131557.515,0.000,1525.973,1525.973,48.806,2097152,2097152,2097152*56



2025-07-31 21:15:54:373 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:15:54:553 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36281 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-8,-8,4060]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 21:15:54:611 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:15:54:615 ==>> 检测【打印IMU STATE2】
2025-07-31 21:15:54:621 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:15:54:658 ==>> $GBGGA,131558.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,3,,,41,24,,,40,1*44

$GBGSV,7,2,26,59,,,40,60,,,

2025-07-31 21:15:54:718 ==>> 40,39,,,39,41,,,39,1*72

$GBGSV,7,3,26,40,,,38,14,,,38,16,,,37,1,,,37,1*41

$GBGSV,7,4,26,7,,,37,34,,,36,9,,,36,42,,,35,1*7C

$GBGSV,7,5,26,6,,,35,44,,,35,38,,,35,13,,,35,1*4F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131558.515,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131558.515,0.000,1527.569,1527.569,48.859,2097152,2097152,2097152*53



2025-07-31 21:15:54:823 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:15:54:901 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:15:54:906 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:15:54:912 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:15:54:973 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:15:55:154 ==>> [D][05:18:25][COMM]read battery soc:255
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 10
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 10


2025-07-31 21:15:55:187 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:15:55:195 ==>> 检测【检测VBUS电压2】
2025-07-31 21:15:55:201 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:15:55:549 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539012
[D][05:18:26][FCTY]HardwareID  = 867222087567642
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = EEE4C55A18A3
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 294
[D][05:18:26][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT

2025-07-31 21:15:55:594 ==>> 1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3736 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:15:55:699 ==>>                                                                                                                                                                                                                                                                                                                                                  

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,131559.515,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131559.515,0.000,1530.760,1530.760,48.963,2097152,2097152,2097152*5A



2025-07-31 21:15:55:731 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:15:56:126 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539012
[D][05:18:26][FCTY]HardwareID  = 867222087567642
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = EEE4C55A18A3
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 5300 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 157
[D][05:18:26][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY

2025-07-31 21:15:56:171 ==>> ]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3736 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:15:56:277 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:15:56:630 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539012
[D][05:18:27][FCTY]HardwareID  = 867222087567642
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = EEE4C55A18A3
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 50 ma
[D][05:18:27][FCTY]VBUS        = 5300 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 138
[D][05:18:27][FCTY]Acckey1 vol = 5514 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]C

2025-07-31 21:15:56:735 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3736 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 21:15:56:795 ==>>                                                                                                                     change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38559


2025-07-31 21:15:56:799 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 21:15:56:805 ==>> #################### 【测试结束】 ####################
2025-07-31 21:15:56:843 ==>> 关闭5V供电
2025-07-31 21:15:56:849 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:15:56:975 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:15:57:306 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]all continue location stop
[W][05:18:27][GNSS]sing locating running
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_nu

2025-07-31 21:15:57:411 ==>> m:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]=====================

2025-07-31 21:15:57:516 ==>> ======================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5E850772027EEB8725C36E1167037C246CD225D7DDA769FAECF3ECEABD4AB2E8ED71F0F92F67DD450F4C0E31C42304C9B2DD4BC108B37188593C0452861B3182ADF8F6FAF413BF89B129C7F98BB7BF6A89FED
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_id

2025-07-31 21:15:57:546 ==>> le become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]


2025-07-31 21:15:57:741 ==>> $GBGGA,131601.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,3,,,41,24,,,41,1*44

$GBGSV,7,2,26,59,,,40,60,,,40,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,14,,,37,16,,,37,1,,,37,1*4E

$GBGSV,7,4,26,7,,,37,34,,,36,9,,,35,6,,,35,1*4F

$GBGSV,7,5,26,42,,,35,44,,,35,38,,,35,13,,,35,1*7F

$GBGSV,7,6,26,2,,,35,23,,,34,10,,,34,12,,,33,1*44

$GBGSV,7,7,26,4,,,33,5,,,32,1*72

$GBRMC,131601.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131601.515,0.000,760.770,760.770,695.742,2097152,2097152,2097152*6F



2025-07-31 21:15:57:846 ==>> 关闭5V供电成功
2025-07-31 21:15:57:853 ==>> 关闭33V供电
2025-07-31 21:15:57:860 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:15:57:966 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:15:58:743 ==>> $GBGGA,131602.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,59,,,40,24,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,7,,,37,1,,,37,16,,,37,1*7C

$GBGSV,7,4,27,14,,,37,9,,,36,34,,,36,2,,,35,1*7B

$GBGSV,7,5,27,38,,,35,44,,,35,6,,,35,42,,,35,1*4A

$GBGSV,7,6,27,10,,,34,13,,,34,23,,,34,12,,,33,1*74

$GBGSV,7,7,27,4,,,33,5,,,32,8,,,,1*4B

$GBRMC,131602.515,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131602.515,0.000,758.374,758.374,693.550,2097152,2097152,2097152*6B



2025-07-31 21:15:58:848 ==>> 关闭33V供电成功
2025-07-31 21:15:58:855 ==>> 关闭3.7V供电
2025-07-31 21:15:58:862 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:15:58:968 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:15:59:260 ==>>  

