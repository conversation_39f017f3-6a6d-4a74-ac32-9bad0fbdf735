2025-07-31 18:26:49:700 ==>> MES查站成功:
查站序号:P510001005312EA4验证通过
2025-07-31 18:26:49:704 ==>> 扫码结果:P510001005312EA4
2025-07-31 18:26:49:706 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:26:49:707 ==>> 测试参数版本:2024.10.11
2025-07-31 18:26:49:710 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:26:49:711 ==>> 检测【打开透传】
2025-07-31 18:26:49:713 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:26:49:772 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:26:50:280 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:26:50:314 ==>> 检测【检测接地电压】
2025-07-31 18:26:50:317 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:26:50:374 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:26:50:592 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:26:50:594 ==>> 检测【打开小电池】
2025-07-31 18:26:50:597 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:26:50:679 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:26:50:865 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:26:50:867 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:26:50:869 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:26:50:971 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:26:51:141 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:26:51:143 ==>> 检测【等待设备启动】
2025-07-31 18:26:51:146 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:26:51:476 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:26:51:643 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:26:52:179 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:26:52:379 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:26:52:758 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:26:53:216 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:26:53:231 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:26:53:527 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:26:53:529 ==>> 检测【产品通信】
2025-07-31 18:26:53:531 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:26:53:656 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:26:53:869 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:26:53:871 ==>> 检测【初始化完成检测】
2025-07-31 18:26:53:875 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:26:53:884 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:26:54:111 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:26:54:160 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:26:54:162 ==>> 检测【关闭大灯控制1】
2025-07-31 18:26:54:164 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:26:54:443 ==>> [D][05:17:51][COMM]2635 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:26:54:706 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:26:54:709 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:26:54:710 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:26:54:870 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:26:54:979 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:26:54:981 ==>> 检测【关闭仪表供电】
2025-07-31 18:26:54:983 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:26:55:176 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:26:55:258 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:26:55:260 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:26:55:262 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:26:55:281 ==>> [D][05:17:52][COMM]3648 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:26:55:446 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:26:55:563 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:26:55:565 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:26:55:566 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:26:55:737 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:26:55:860 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:26:55:863 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:26:55:864 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:26:56:038 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:26:56:137 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:26:56:140 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:26:56:160 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:26:56:283 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:26:56:387 ==>> [D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]4659 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 18:26:56:425 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:26:56:428 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:26:56:431 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:26:56:477 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:26:56:739 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:26:56:742 ==>> 该项需要延时执行
2025-07-31 18:26:56:841 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat

2025-07-31 18:26:56:871 ==>>  fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 18:26:57:327 ==>> [D][05:17:54][COMM]5670 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:26:57:949 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:26:58:463 ==>>               COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021

2025-07-31 18:26:58:568 ==>> ].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]

2025-07-31 18:26:58:673 ==>> get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][CO

2025-07-31 18:26:58:748 ==>> MM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6682 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255


2025-07-31 18:26:59:347 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7694 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:27:00:401 ==>> [D][05:17:57][COMM]8705 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 18:27:00:752 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:27:00:755 ==>> 检测【33V输入电压ADC】
2025-07-31 18:27:00:769 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:27:01:090 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:17:58][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:58][COMM]adc read left brake adc:1  volt:1 mv
[D][05:17:58][COMM]adc read right brake adc:7  volt:9 mv
[D][05:17:58][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:58][COMM]adc read battery ts volt:10 mv
[D][05:17:58][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2416  volt:3892 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:27:01:303 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:27:01:307 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:27:01:310 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:27:01:382 ==>> [D][05:17:58][COMM]9716 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1654mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 2021mV
Get AD_V7 1093mV
OVER 150


2025-07-31 18:27:01:584 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:01:598 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:27:01:607 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:01:610 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:27:01:612 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 18:27:01:626 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:27:01:629 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:27:01:645 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:27:01:647 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:27:01:669 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:27:01:671 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:27:01:705 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018


2025-07-31 18:27:01:795 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1653mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 2005mV
Get AD_V7 1094mV
OVER 150


2025-07-31 18:27:01:954 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:01:956 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:27:01:973 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:01:975 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:27:01:994 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 18:27:01:995 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:27:01:997 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:27:02:011 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2005mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:27:02:013 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:27:02:035 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:27:02:037 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:27:02:192 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1652mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 2020mV
Get AD_V7 1093mV
OVER 150


2025-07-31 18:27:02:320 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:02:323 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:27:02:341 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:02:344 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:27:02:346 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 18:27:02:366 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:27:02:389 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:27:02:391 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2020mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:27:02:393 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:27:02:417 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:27:02:419 ==>> 检测【打开WIFI(1)】
2025-07-31 18:27:02:420 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:27:02:646 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10727 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2

2025-07-31 18:27:02:691 ==>> m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:27:03:047 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:27:03:053 ==>> 检测【清空消息队列(1)】
2025-07-31 18:27:03:057 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:27:03:098 ==>>                                                                                                                                                                                                                                                                                        ][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087646784

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071544493

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1



2025-07-31 18:27:03:203 ==>>                                                                                                                                                                                                          [D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Inp

2025-07-31 18:27:03:233 ==>> ut command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:27:03:347 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:27:03:349 ==>> 检测【打开GPS(1)】
2025-07-31 18:27:03:351 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:27:03:384 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:27:03:565 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 18:27:03:654 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:27:03:658 ==>> 检测【打开GSM联网】
2025-07-31 18:27:03:662 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:27:04:021 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][COMM]Main Task receive event:4 finished processing

2025-07-31 18:27:04:096 ==>> 
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.170.99.69"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:27:04:201 ==>>              [CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 18:27:04:203 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:27:04:205 ==>> 检测【打开仪表供电1】
2025-07-31 18:27:04:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:27:04:599 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:27:04:754 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:27:04:757 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:27:04:760 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:27:04:978 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:27:05:038 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:27:05:043 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:27:05:045 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:27:05:296 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33409]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:27:05:385 ==>> [D][05:18:02][COMM]13739 imu init OK


2025-07-31 18:27:05:571 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:27:05:574 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:27:05:576 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:05:767 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:05:845 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:27:05:874 ==>> 检测【AD_V20电压】
2025-07-31 18:27:05:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:05:950 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:05:965 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:27:06:025 ==>> 本次取值间隔时间:63ms
2025-07-31 18:27:06:235 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,24,,,43,42,,,40,38,,,39,39,,,38,1*71

$GBGSV,2,2,07,26,,,42,13,,,40,59,,,39,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1658.275,1658.275,52.990,2097152,2097152,2097152*48

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:06:430 ==>> 本次取值间隔时间:404ms
2025-07-31 18:27:06:447 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:03][COMM]read battery soc:255


2025-07-31 18:27:06:655 ==>> 本次取值间隔时间:216ms
2025-07-31 18:27:07:083 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,42,3,,,41,13,,,40,42,,,40,1*46

$GBGSV,3,2,11,26,,,39,38,,,39,39,,,39,8,,,38,1*4B

$GBGSV,3,3,11,21,,,34,4,,,24,59,,,39,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1558.936,1558.936,49.971,2097152,2097152,2097152*4D



2025-07-31 18:27:07:128 ==>> 本次取值间隔时间:461ms
2025-07-31 18:27:07:133 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:07:234 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:07:461 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:27:07:731 ==>> 本次取值间隔时间:488ms
2025-07-31 18:27:08:063 ==>> 本次取值间隔时间:328ms
2025-07-31 18:27:08:108 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,42,3,,,40,13,,,40,42,,,40,1*47

$GBGSV,4,2,16,38,,,40,59,,,40,60,,,40,26,,,39,1*7C

$GBGSV,4,3,16,39,,,39,8,,,39,1,,,37,21,,,36,1*77

$GBGSV,4,4,16,2,,,35,5,,,32,4,,,27,29,,,39,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1564.403,1564.403,50.087,2097152,2097152,2097152*45



2025-07-31 18:27:08:411 ==>> 本次取值间隔时间:337ms
2025-07-31 18:27:08:426 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 18:27:08:669 ==>> 本次取值间隔时间:249ms
2025-07-31 18:27:08:675 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:08:780 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:08:886 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 18:27:08:931 ==>> 本次取值间隔时间:148ms
2025-07-31 18:27:08:975 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:09:081 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:09:111 ==>> $GBGGA,102712.924,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,42,3,,,40,13,,,40,42,,,40,1*47

$GBGSV,4,2,16,38,,,40,60,,,40,59,,,39,26,,,39,1*72

$GBGSV,4,3,16,8,,,39,39,,,38,33,,,37,1,,,36,1*75

$GBGSV,4,4,16,21,,,36,2,,,35,5,,,31,4,,,29,1*4B

$GBRMC,102712.924,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102712.924,0.000,1557.302,1557.302,49.843,2097152,2097152,2097152*5B



2025-07-31 18:27:09:156 ==>> 本次取值间隔时间:69ms
2025-07-31 18:27:09:186 ==>>                                                                                                                                                                                                                     1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:27:09:321 ==>> 本次取值间隔时间:151ms
2025-07-31 18:27:09:364 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:09:473 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:09:578 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:27:09:683 ==>> $GBGGA,102713.524,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,3,,,41,13,,,40,42,,,40,1*48

$GBGSV,5,2,19,60,,,40,38,,,39,59,,,39,26,,,39,1*72

$GBGSV,5,3,19,8,,,39,39,,,39,33,,,37,21,,,37,1*49

$GBGSV,5,4,19,16,,,37,1,,,36,2,,,35,5,,,31,1*4B

$GBGSV,5,5,19,4,,,29,7,,,37,14,,,36,1*72

$GBRMC,102713.524,V,,,,

2025-07-31 18:27:09:713 ==>> ,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102713.524,0.000,1560.804,1560.804,49.950,2097152,2097152,2097152*55



2025-07-31 18:27:09:911 ==>> 本次取值间隔时间:425ms
2025-07-31 18:27:09:929 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:10:034 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:10:274 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:10:304 ==>> 本次取值间隔时间:269ms
2025-07-31 18:27:10:424 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:27:10:607 ==>> 本次取值间隔时间:294ms
2025-07-31 18:27:10:715 ==>> $GBGGA,102714.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,3,,,40,13,,,40,42,,,40,1*43

$GBGSV,5,2,20,60,,,40,38,,,40,59,,,39,26,,,39,1*76

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,33,,,37,1*48

$GBGSV,5,4,20,21,,,37,1,,,36,6,,,36,14,,,35,1*76

$GBGSV,5,5,20,2,,,35,9,,,31,5,,,30,4,,,30,1*7A

$GBRMC,102714.504,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102714.504,0.000,1540.202,1540.202,49.297,2097152,2097152,2097152*50



2025-07-31 18:27:11:049 ==>> 本次取值间隔时间:427ms
2025-07-31 18:27:11:252 ==>> 本次取值间隔时间:196ms
2025-07-31 18:27:11:257 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:27:11:361 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:27:11:437 ==>> [W][05:18:08][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:27:11:482 ==>> 1A A1 10 00 00 
Get AD_V20 1641mV
OVER 150


2025-07-31 18:27:11:557 ==>> 本次取值间隔时间:194ms
2025-07-31 18:27:11:576 ==>> 【AD_V20电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:27:11:580 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:27:11:603 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:27:11:708 ==>> $GBGGA,102715.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,41,3,,,40,13,,,40,1*42

$GBGSV,5,2,20,60,,,40,38,,,40,59,,,39,26,,,39,1*76

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,33,,,37,1*48

$GBGSV,5,4,20,21,,,37,1,,,36,6,,,36,14,,,35,1*76

$GBGSV,5,5,20,2,,,34,9,,,34,4,,,31,5,,,30,1*7F

$GBRMC,102715.504,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102715.504,0.000,1548.482,1548.482,49.550,2097152,2097152,2097152*5D

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:27:11:882 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:27:11:885 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:27:11:889 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:27:12:075 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:12:439 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:27:12:724 ==>> $GBGGA,102716.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,3,,,41,42,,,40,13,,,40,1*42

$GBGSV,5,2,20,60,,,40,38,,,40,26,,,40,59,,,39,1*78

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,33,,,37,1*48

$GBGSV,5,4,20,21,,,37,1,,,36,6,,,36,14,,,35,1*76

$GBGSV,5,5,20,2,,,35,9,,,35,4,,,31,5,,,31,1*7E

$GBRMC,102716.504,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102716.504,0.000,1556.764,1556.764,49.806,2097152,2097152,2097152*50



2025-07-31 18:27:12:908 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:27:13:075 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:13:722 ==>> $GBGGA,102717.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,3,,,40,42,,,40,13,,,40,1*43

$GBGSV,5,2,20,60,,,40,38,,,40,26,,,39,59,,,39,1*76

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,33,,,37,1*48

$GBGSV,5,4,20,21,,,37,1,,,36,6,,,36,9,,,36,1*49

$GBGSV,5,5,20,14,,,35,2,,,35,4,,,32,5,,,31,1*41

$GBRMC,102717.504,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102717.504,0.000,1556.753,1556.753,49.795,2097152,2097152,2097152*54



2025-07-31 18:27:13:952 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:27:14:197 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:11][COMM]oneline display read state:1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:27:14:440 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:27:14:714 ==>> $GBGGA,102718.504,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,41,3,,,40,13,,,40,1*42

$GBGSV,5,2,20,60,,,40,38,,,40,26,,,40,59,,,39,1*78

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,21,,,38,1*44

$GBGSV,5,4,20,33,,,37,1,,,36,6,,,36,9,,,36,1*4A

$GBGSV,5,5,20,14,,,35,2,,,35,4,,,32,5,,,31,1*41

$GBRMC,102718.504,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102718.504,0.000,1562.975,1562.975,49.997,2097152,2097152,2097152*57



2025-07-31 18:27:14:990 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:27:14:995 ==>> #################### 【测试结束】 ####################
2025-07-31 18:27:15:222 ==>> 关闭5V供电
2025-07-31 18:27:15:227 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:27:15:286 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:27:15:713 ==>> $GBGGA,102719.504,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,41,3,,,40,13,,,40,1*42

$GBGSV,5,2,20,60,,,40,38,,,40,26,,,40,59,,,39,1*78

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,21,,,38,1*44

$GBGSV,5,4,20,33,,,37,1,,,36,6,,,36,9,,,36,1*4A

$GBGSV,5,5,20,14,,,35,2,,,34,5,,,32,4,,,31,1*40

$GBRMC,102719.504,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102719.504,0.000,1560.905,1560.905,49.934,2097152,2097152,2097152*5F



2025-07-31 18:27:16:234 ==>> 关闭5V供电成功
2025-07-31 18:27:16:239 ==>> 关闭33V供电
2025-07-31 18:27:16:243 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:27:16:386 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:27:16:491 ==>> [D][05:18:13][COMM]read battery soc:255
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D

2025-07-31 18:27:16:551 ==>> ][05:18:13][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 6,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 7,volt = 14
[D][05:18:13][FCTY]get_ext_48v_vol retry i = 8,volt = 14


2025-07-31 18:27:16:656 ==>> $GBGGA,102720.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,40,3,,,40,13,,,40,1*43

$GBGSV,5,2,20,60,,,40,38,,,40,26,,,39,59,,,39,1*76

$GBGSV,5,3,20,8,,,39,39,,,39,16,,,38,21,,,38,1*44

$GBGSV,5,4,20,33,,,37,1,,,36,6,,,36,9,

2025-07-31 18:27:16:701 ==>> ,,36,1*4A

$GBGSV,5,5,20,14,,,35,2,,,34,5,,,32,4,,,31,1*40

$GBRMC,102720.504,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102720.504,0.000,1556.755,1556.755,49.798,2097152,2097152,2097152*5D



2025-07-31 18:27:16:761 ==>> [D][05:18:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:27:17:250 ==>> 关闭33V供电成功
2025-07-31 18:27:17:255 ==>> 关闭3.7V供电
2025-07-31 18:27:17:259 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:27:17:375 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


