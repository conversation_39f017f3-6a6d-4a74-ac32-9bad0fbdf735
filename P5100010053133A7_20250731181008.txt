2025-07-31 18:10:08:432 ==>> MES查站成功:
查站序号:P5100010053133A7验证通过
2025-07-31 18:10:08:438 ==>> 扫码结果:P5100010053133A7
2025-07-31 18:10:08:440 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:10:08:441 ==>> 测试参数版本:2024.10.11
2025-07-31 18:10:08:442 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:10:08:444 ==>> 检测【打开透传】
2025-07-31 18:10:08:445 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:10:08:563 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:10:08:805 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:10:08:810 ==>> 检测【检测接地电压】
2025-07-31 18:10:08:812 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:10:08:865 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 18:10:09:095 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:10:09:097 ==>> 检测【打开小电池】
2025-07-31 18:10:09:099 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:10:09:167 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:10:09:368 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:10:09:370 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:10:09:372 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:10:09:467 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:10:09:650 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:10:09:654 ==>> 检测【等待设备启动】
2025-07-31 18:10:09:680 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:10:09:938 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:10:10:133 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:10:10:679 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:10:10:784 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:10:10:843 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:10:11:239 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:10:11:708 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:10:11:723 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:10:11:991 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:10:11:994 ==>> 检测【产品通信】
2025-07-31 18:10:11:995 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:10:12:121 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:10:12:277 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:10:12:279 ==>> 检测【初始化完成检测】
2025-07-31 18:10:12:281 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:10:12:361 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:10:12:466 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init succe

2025-07-31 18:10:12:496 ==>> ss!


2025-07-31 18:10:12:613 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:10:12:616 ==>> 检测【关闭大灯控制1】
2025-07-31 18:10:12:619 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:10:12:945 ==>> [D][05:17:51][COMM]2647 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:10:13:176 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:10:13:179 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:10:13:181 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:10:13:360 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:10:13:480 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:10:13:483 ==>> 检测【关闭仪表供电】
2025-07-31 18:10:13:512 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:10:13:650 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:10:13:802 ==>> [D][05:17:52][COMM]3661 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:13:819 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:10:13:821 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:10:13:824 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:10:14:017 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:10:14:126 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:10:14:128 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:10:14:129 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:10:14:318 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:10:14:396 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:10:14:400 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:10:14:402 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:10:14:543 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:10:14:669 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:10:14:671 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:10:14:694 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:10:14:756 ==>> 5A A5 01 5A A5 


2025-07-31 18:10:14:801 ==>> [D][05:17:53][COMM]4672 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:14:860 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:10:14:920 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 24


2025-07-31 18:10:14:951 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:10:14:953 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:10:14:955 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:10:14:980 ==>>                                          

2025-07-31 18:10:15:056 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:10:15:226 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:10:15:231 ==>> 该项需要延时执行
2025-07-31 18:10:15:328 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 18:10:15:822 ==>> [D][05:17:54][COMM]5683 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:16:521 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:10:17:057 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][

2025-07-31 18:10:17:162 ==>> COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][CO

2025-07-31 18:10:17:267 ==>> MM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0


2025-07-31 18:10:17:342 ==>> 
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6694 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 18:10:17:845 ==>> [D][05:17:56][COMM]7705 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:18:841 ==>> [D][05:17:57][COMM]8717 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:18:977 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:10:19:232 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:10:19:235 ==>> 检测【33V输入电压ADC】
2025-07-31 18:10:19:238 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:19:577 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:17:58][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:58][COMM]adc read battery ts volt:11 mv
[D][05:17:58][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:10:19:759 ==>> 【33V输入电压ADC】通过,【32729mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:10:19:762 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:10:19:764 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:10:19:877 ==>> [D][05:17:58][COMM]9729 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1657mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2769mV
Get AD_V6 1992mV
Get AD_V7 1098mV
OVER 150


2025-07-31 18:10:20:032 ==>> 【TP7_VCC3V3(ADV2)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:035 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:10:20:050 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:053 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:10:20:055 ==>> 原始值:【2769】, 乘以分压基数【2】还原值:【5538】
2025-07-31 18:10:20:069 ==>> 【TP68_VCC5V5(ADV5)】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:10:20:071 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:10:20:094 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:10:20:097 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:10:20:117 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:10:20:119 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:10:20:211 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021
1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1671mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1990mV
Get AD_V7 1097mV
OVER 150


2025-07-31 18:10:20:400 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:402 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:10:20:418 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:423 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:10:20:426 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:10:20:437 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:10:20:452 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:10:20:456 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:10:20:458 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:10:20:479 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:10:20:483 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:10:20:571 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1670mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 1990mV
Get AD_V7 1097mV
OVER 150


2025-07-31 18:10:20:676 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 18:10:20:769 ==>> 【TP7_VCC3V3(ADV2)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:772 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:10:20:791 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1670mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:20:794 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:10:20:798 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 18:10:20:820 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:10:20:823 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:10:20:844 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:10:20:846 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:10:20:878 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:10:20:880 ==>> 检测【打开WIFI(1)】
2025-07-31 18:10:20:901 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:10:21:129 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10739 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]read 

2025-07-31 18:10:21:174 ==>> battery soc:255
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:10:21:403 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:10:21:406 ==>> 检测【清空消息队列(1)】
2025-07-31 18:10:21:408 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:10:21:628 ==>>                                                                                                                                                                                                         AT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087871747

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290639

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:10:21:676 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:10:21:680 ==>> 检测【打开GPS(1)】
2025-07-31 18:10:21:684 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:10:21:883 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:10:21:946 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:10:21:949 ==>> 检测【打开GSM联网】
2025-07-31 18:10:21:952 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:10:22:144 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:10:22:223 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:10:22:225 ==>> 检测【打开仪表供电1】
2025-07-31 18:10:22:227 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:10:22:552 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:10:22:776 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:10:22:779 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:10:22:782 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:10:22:959 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:10:23:004 ==>>                                          

2025-07-31 18:10:23:069 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:10:23:071 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:10:23:075 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:10:23:245 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[32992]


2025-07-31 18:10:23:357 ==>> 【读取主控ADC采集的仪表电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:10:23:359 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:10:23:361 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:10:23:560 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:10:23:644 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:10:23:648 ==>> 检测【AD_V20电压】
2025-07-31 18:10:23:651 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:10:23:725 ==>> [D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:10:23:755 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:10:23:876 ==>> 本次取值间隔时间:120ms
2025-07-31 18:10:23:906 ==>> 1A A1 10 00 00 
Get AD_V20 1661mV
OVER 150
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13752 imu init OK


2025-07-31 18:10:24:106 ==>> 本次取值间隔时间:216ms
2025-07-31 18:10:24:128 ==>> 【AD_V20电压】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:24:131 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:10:24:133 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:10:24:257 ==>> 3A A3 02 00 A3 


2025-07-31 18:10:24:362 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: 

2025-07-31 18:10:24:437 ==>> M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

OFF_OUT2
OVER 150


2025-07-31 18:10:24:542 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 18:10:24:602 ==>>                                                                                                                                                                               18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.75.7.191"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:10:24:672 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:10:24:675 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:10:24:678 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:10:25:116 ==>> [D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<<

2025-07-31 18:10:25:161 ==>>  
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][COMM]read battery soc:255


2025-07-31 18:10:25:228 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:10:25:232 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:10:25:235 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:10:25:358 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:10:25:531 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:10:25:534 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:10:25:538 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:10:25:788 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:10:26:445 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:10:26:640 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

[D][05:18:05][CAT1]<<< 
OK

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,24,,,43,38,,,42,59,,,41,60,,,41,1*7B

$GBGSV,3,2,09,26,,,40,42,,,40,39,,,31,13,,,42,1*70

$GBGSV,3,3,09,16,,,36,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1646.521,1646.521,52.703,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 18:10:26:850 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:10:26:905 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:10:26:908 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:10:26:927 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:10:27:079 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][COMM]read battery soc:255


2025-07-31 18:10:27:202 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:10:27:206 ==>> 检测【AD_V21电压】
2025-07-31 18:10:27:210 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:10:27:259 ==>> 1A A1 20 00 00 
Get AD_V21 1658mV
OVER 150


2025-07-31 18:10:27:380 ==>> 本次取值间隔时间:165ms
2025-07-31 18:10:27:410 ==>> 【AD_V21电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:27:417 ==>> 检测【关闭仪表供电2】
2025-07-31 18:10:27:421 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:10:27:606 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,42,13,,,41,38,,,41,59,,,41,1*74

$GBGSV,4,2,16,60,,,41,3,,,41,26,,,40,42,,,40,1*40

$GBGSV,4,3,16,21,,,39,8,,,39,1,,,38,16,,,37,1*74

$GBGSV,4,4,16,6,,,37,14,,,35,39,,,35,2,,,36,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1622.362,1622.362,51.860,2097152,2097152,2097152*45

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:10:27:694 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:10:27:698 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:10:27:703 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:10:27:846 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:10:27:974 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:10:27:980 ==>> 检测【打开AccKey2供电】
2025-07-31 18:10:27:985 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:10:28:116 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:10:28:261 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:10:28:267 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:10:28:289 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:28:641 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:07][COMM]adc read out 24v adc:1309  volt:33108 mv
[D][05:18:07][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:07][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:07][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:07][COMM]adc read battery ts volt:12 mv
[D][05:18:07][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,42,13,,,41,38,,,41,60,,,41,1*71

$GBGSV,5,2,18,26,,,41,59,,,40,3,,,40,42,,,40,1*44

$GBGSV,5,3,18,8,,,40,21,,,39,16,,,38,1,,,37,1*75

$GBGSV,5,4,18,39,,,37,6,,,36,14,,,36,2,,,35,1*77

$GBGSV,5,5,18,4,,,34,5,,,34,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T

2025-07-31 18:10:28:671 ==>> ,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1593.822,1593.822,50.967,2097152,2097152,2097152*42



2025-07-31 18:10:28:794 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:10:28:798 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:10:28:802 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:10:28:927 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:10:29:062 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:10:29:071 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:10:29:074 ==>> 该项需要延时执行
2025-07-31 18:10:29:603 ==>> $GBGGA,101033.409,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,13,,,41,38,,,41,26,,,41,1*72

$GBGSV,5,2,19,60,,,40,59,,,40,3,,,40,42,,,40,1*46

$GBGSV,5,3,19,8,,,40,21,,,40,16,,,38,1,,,38,1*75

$GBGSV,5,4,19,39,,,38,6,,,36,14,,,36,2,,,36,1*7A

$GBGSV,5,5,19,5,,,34,4,,,33,33,,,33,1*78

$GBRMC,101033.409,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101033.409,0.000,1586.317,1586.317,50.737,2097152,2097152,2097152*5A



2025-07-31 18:10:30:595 ==>> $GBGGA,101034.389,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,13,,,41,38,,,41,26,,,41,1*78

$GBGSV,5,2,20,60,,,41,59,,,40,3,,,40,42,,,40,1*4D

$GBGSV,5,3,20,8,,,40,21,,,40,16,,,39,39,,,39,1*44

$GBGSV,5,4,20,1,,,38,9,,,38,14,,,37,6,,,36,1*4F

$GBGSV,5,5,20,2,,,35,5,,,34,33,,,34,4,,,33,1*41

$GBRMC,101034.389,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101034.389,0.000,1594.058,1594.058,50.979,2097152,2097152,2097152*56



2025-07-31 18:10:30:775 ==>> $GBGGA,101034.589,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,13,,,41,38,,,41,26,,,41,1*79

$GBGSV,5,2,20,60,,,40,59,,,40,3,,,40,42,,,40,1*4C

$GBGSV,5,3,20,8,,,40,21,,,40,16,,,39,39,,,39,1*44

$GBGSV,5,4,20,1,,,38,9,,,37,14,,,37,6,,,36,1*40

$GBGSV,5,5,20,2,,,36,5,,,34,33,,,34,4,,,33,1*42

$GBRMC,101034.589,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101034.589,0.000,1594.058,1594.058,50.979,2097152,2097152,2097152*50



2025-07-31 18:10:31:066 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:10:31:780 ==>> $GBGGA,101035.569,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,13,,,42,38,,,41,26,,,41,1*79

$GBGSV,6,2,21,60,,,40,59,,,40,3,,,40,42,,,40,1*4E

$GBGSV,6,3,21,8,,,40,21,,,40,16,,,39,39,,,39,1*46

$GBGSV,6,4,21,1,,,38,9,,,37,14,,,37,6,,,36,1*42

$GBGSV,6,5,21,2,,,36,33,,,35,5,,,34,4,,,33,1*41

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,101035.569,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101035.569,0.000,1579.372,1579.372,50.532,2097152,2097152,2097152*5C



2025-07-31 18:10:32:081 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:10:32:086 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:10:32:090 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:32:381 ==>> [D][05:18:11][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:11][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:11][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:11][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:11][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:11][COMM]adc read battery ts volt:11 mv
[D][05:18:11][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:11][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:10:32:634 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【177mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:10:32:637 ==>> 检测【打开AccKey1供电】
2025-07-31 18:10:32:644 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:10:32:771 ==>> $GBGGA,101036.549,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,13,,,41,38,,,41,26,,,41,1*7A

$GBGSV,6,2,21,60,,,40,59,,,40,3,,,40,42,,,40,1*4E

$GBGSV,6,3,21,8,,,40,21,,,40,16,,,39,39,,,39,1*46

$GBGSV,6,4,21,1,,,38,9,,,37,14,,,37,6,,,36,1*42

$GBGSV,6,5,21,2,,,35,33,,,35,5,,,34,4,,,33,1*42

$GBGSV,6,6,21,7,,,30,1*41

$GBRMC,101036.549,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101036.549,0.000,1575.423,1575.423,50.405,2097152,2097152,2097152*58



2025-07-31 18:10:32:846 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:10:32:923 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:10:32:926 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:10:32:929 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:10:33:087 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150
[D][05:18:11][COMM]read battery soc:255


2025-07-31 18:10:33:177 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 18:10:33:234 ==>> 【读取AccKey1电压(ADV14)前】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:10:33:238 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:10:33:241 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:33:567 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:12][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:12][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:12][COMM]adc read battery ts volt:14 mv
[D][05:18:12][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:12][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:10:33:762 ==>> $GBGGA,101037.529,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,38,,,42,26,,,41,1*78

$GBGSV,6,2,22,60,,,41,3,,,41,59,,,40,42,,,40,1*4D

$GBGSV,6,3,22,8,,,40,21,,,40,39,,,40,16,,,39,1*4B

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,6,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,36,5,,,34,4,,,33,1*41

$GBGSV,6,6,22,7,,,31,45,,,27,1*47

$GBRMC,101037.529,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101037.529,0.000,785.342,785.342,718.216,2097152,2097152,2097152*60



2025-07-31 18:10:33:831 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5501mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:10:33:834 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:10:33:837 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:10:34:047 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:10:34:125 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:10:34:130 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:10:34:153 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:10:34:258 ==>> 1A A1 00 40 00 
Get AD_V14 2662mV
OVER 150


2025-07-31 18:10:34:379 ==>> 原始值:【2662】, 乘以分压基数【2】还原值:【5324】
2025-07-31 18:10:34:436 ==>> 【读取AccKey1电压(ADV14)后】通过,【5324mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:10:34:443 ==>> 检测【打开WIFI(2)】
2025-07-31 18:10:34:459 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:10:34:771 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

$GBGGA,101038.509,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,42,38,,,42,3,,,41,1*4F

$GBGSV,6,2,22,26,,,41,60,,,40,8,,,40,39,,,40,1*43

$GBGSV,6,3,22,59,,,40,21,,,40,42,,,40,16,,,39,1*73

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,31,45,,,27,1*47

$GBRMC,101038.509,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101038.509,0.000,785.338,785.338,718.212,2097152,2097152,2097152*69

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:10:34:970 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:10:34:974 ==>> 检测【转刹把供电】
2025-07-31 18:10:34:978 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:10:35:154 ==>> [D][05:18:13][COMM]read battery soc:255
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:10:35:247 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:10:35:251 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:10:35:253 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:10:35:355 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:10:35:462 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2405mV
OVER 150


2025-07-31 18:10:35:507 ==>> 原始值:【2405】, 乘以分压基数【2】还原值:【4810】
2025-07-31 18:10:35:527 ==>> 【读取AD_V15电压(前)】通过,【4810mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:10:35:530 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:10:35:535 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:10:35:630 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:10:35:722 ==>> $GBGGA,101039.509,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,38,,,42,24,,,42,13,,,41,26,,,41,1*7A

$GBGSV,6,2,22,60,,,40,8,,,40,3,,,40,59,,,40,1*73

$GBGSV,6,3,22,21,,,40,42,,,40,39,,,39,16,,,39,1*7B

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,36,5,,,34,4,,,33,1*41

$GBGSV,6,6,22,7,,,30,45,,,28,1*49

$GBRMC,101039.509,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101039.509,0.000,780.629,780.629,713.905,2097152,2097152,2097152*6E



2025-07-31 18:10:35:827 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:10:36:588 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:10:36:693 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:10:36:783 ==>> $GBGGA,101040.509,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,38,,,41,26,,,41,1*79

$GBGSV,6,2,22,60,,,40,8,,,40,3,,,40,39,,,40,1*75

$GBGSV,6,3,22,59,,,40,21,,,40,42,,,40,16,,,39,1*73

$GBGSV,6,4,22,1,,,38,9,,,37,14,,,37,2,,,36,1*45

$GBGSV,6,5,22,6,,,36,33,,,36,5,,,34,4,,,33,1*45

$GBGSV,6,6,22,7,,,31,45,,,29,1*49

$GBRMC,101040.509,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101040.509,0.000,781.558,781.558,714.754,2097152,2097152,2097152*6D

[W][05:18:15][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 18:10:36:858 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 18:10:36:890 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:10:36:896 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:10:36:900 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:37:218 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:15][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:15][COMM]adc read battery ts volt:6 mv
[D][05:18:15][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3087  volt:5426 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:15][COMM]read battery soc:255
+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-65
+WIFISCAN:4,2,F62A7D2297A3,-67
+WIFISCAN:4,3,44A1917CA62B,-70

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 18:10:37:417 ==>> 【转刹把供电电压(主控ADC)】通过,【5426mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:10:37:421 ==>> 检测【转刹把供电电压】
2025-07-31 18:10:37:423 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:37:841 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:16][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:16][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:16][COMM]adc read battery ts volt:10 mv
[D][05:18:16][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3081  volt:5415 mv
$GBGGA,101041.509,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,38,,,41,26,,,41,1*7A

$GBGSV,6,2,23,60,,,40,8,,,40,3,,,40,39,,,40,1*74

$GBGSV,6,3,23,59,,,40,21,,,40,42,,,40,16,,,39,1*72

$GBGSV,6,4,23,1,,,38,9,,,37,6,,,37,14,,,37,1*41

$GBGSV,6,5,23,2,,,36,33,,,36,5,,,34,4,,,34,1*47

$GBGSV,6,6,23,7,,,31,45,,,29,36,,,36,1*48

$GBRMC,101041.509,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101041.509,0.000,785.321,785.321,718.195,2097152,2097152,2097152*6B

[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D]

2025-07-31 18:10:37:901 ==>> [05:18:16][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
                                      

2025-07-31 18:10:37:950 ==>> 【转刹把供电电压】通过,【5415mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:10:37:954 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:10:37:977 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:10:38:117 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:10:38:265 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:10:38:269 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:10:38:273 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:10:38:381 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:10:38:459 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 18:10:38:508 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:10:38:511 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:10:38:516 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:10:38:611 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:10:38:718 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:10:38:733 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,101042.509,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,38,,,41,3,,,41,1*4E

$GBGSV,6,2,22,59,,,41,26,,,41,42,,,41,60,,,40,1*7B

$GBGSV,6,3,22,8,,,40,39,,,40,21,,,40,16,,,39,1*4B

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,31,45,,,29,1*49

$GBRMC,101042.509,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101042.509,0.000,786.261,786.261,719.055,2097152,2097152,2097152*64



2025-07-31 18:10:38:823 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:10:38:975 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:10:38:978 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:10:38:981 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:10:39:057 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:10:39:102 ==>>        18:17][COMM]read battery soc:255


2025-07-31 18:10:39:258 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:10:39:264 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:10:39:289 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:10:39:365 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:10:39:534 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:10:39:539 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:10:39:546 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:10:39:726 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150
$GBGGA,101043.509,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,38,,,41,3,,,41,1*4E

$GBGSV,6,2,22,26,,,41,60,,,40,8,,,40,39,,,40,1*43

$GBGSV,6,3,22,59,,,40,21,,,40,42,,,40,16,,,39,1*73

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,2,,,36,33,,,36,5,,,34,4,,,34,1*46

$GBGSV,6,6,22,7,,,31,45,,,29,1*49

$GBRMC,101043.509,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101043.509,0.000,784.377,784.377,717.332,2097152,2097152,2097152*69



2025-07-31 18:10:39:805 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:10:39:812 ==>> 检测【左刹电压测试1】
2025-07-31 18:10:39:818 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:39:921 ==>> [W][05:18:18][COMM]>>>>>Input command = ?<<<<<


2025-07-31 18:10:40:727 ==>> $GBGGA,101044.509,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,13,,,41,60,,,41,38,,,41,1*7A

$GBGSV,6,2,22,3,,,41,59,,,41,26,,,41,42,,,41,1*4F

$GBGSV,6,3,22,8,,,40,39,,,40,21,,,40,16,,,39,1*4B

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,33,,,37,2,,,36,5,,,34,4,,,34,1*47

$GBGSV,6,6,22,7,,,31,45,,,29,1*49

$GBRMC,101044.509,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101044.509,0.000,789.086,789.086,721.638,2097152,2097152,2097152*64



2025-07-31 18:10:40:832 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:41:174 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:19][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:19][COMM]adc read left brake adc:1724  volt:2272 mv
[D][05:18:19][COMM]adc read right brake adc:1725  volt:2274 mv
[D][05:18:19][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:19][COMM]adc read battery ts volt:6 mv
[D][05:18:19][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
[D][05:18:19][COMM]read battery soc:255


2025-07-31 18:10:41:362 ==>> 【左刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 18:10:41:365 ==>> 检测【右刹电压测试1】
2025-07-31 18:10:41:386 ==>> 【右刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 18:10:41:390 ==>> 检测【转把电压测试1】
2025-07-31 18:10:41:410 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 18:10:41:413 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:10:41:417 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:10:41:462 ==>> 3A A3 03 00 A3 


2025-07-31 18:10:41:567 ==>> OFF_OUT3
OVER 150


2025-07-31 18:10:41:672 ==>> $GBGGA,101045.509,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,38,,,41,26,,,41,1*79

$GBGSV,6,2,22,60,,,40,8,,,40,3,,,40,39,,,40,1*75

$GBGSV,6,3,22,59

2025-07-31 18:10:41:691 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:10:41:697 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:10:41:706 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:10:41:720 ==>> ,,,40,21,,,40,42,,,40,16,,,39,1*73

$GBGSV,6,4,22,1,,,38,9,,,37,6,,,37,14,,,37,1*40

$GBGSV,6,5,22,33,,,37,2,,,36,5,,,34,4,,,33,1*40

$GBGSV,6,6,22,7,,,31,45,,,30,1*41

$GBRMC,101045.509,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101045.509,0.000,784.372,784.372,717.326,2097152,2097152,2097152*6A



2025-07-31 18:10:41:762 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:10:41:966 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:10:41:970 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:10:41:975 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:10:42:056 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:10:42:240 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:10:42:243 ==>> 检测【左刹电压测试2】
2025-07-31 18:10:42:250 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:10:42:565 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:21][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:21][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:21][COMM]adc read battery ts volt:7 mv
[D][05:18:21][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:21][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2382  volt:3838 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:10:42:670 ==>> $GBGGA,101046.509,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,41,60,,,41,38,,,41,1*7B

$GBGSV,6,2,22,26,,,41,42,,,41,8,,,40,3,,,40

2025-07-31 18:10:42:730 ==>> ,1*7B

$GBGSV,6,3,22,39,,,40,59,,,40,21,,,40,16,,,39,1*7F

$GBGSV,6,4,22,1,,,38,2,,,37,9,,,37,6,,,37,1*77

$GBGSV,6,5,22,14,,,37,33,,,37,5,,,34,4,,,34,1*71

$GBGSV,6,6,22,7,,,31,45,,,30,1*41

$GBRMC,101046.509,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101046.509,0.000,788.133,788.133,720.765,2097152,2097152,2097152*6E



2025-07-31 18:10:42:787 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 18:10:42:792 ==>> 检测【右刹电压测试2】
2025-07-31 18:10:42:817 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 18:10:42:824 ==>> 检测【转把电压测试2】
2025-07-31 18:10:42:843 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 18:10:42:846 ==>> 检测【晶振检测】
2025-07-31 18:10:42:849 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:10:43:041 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 18:10:43:116 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 18:10:43:124 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:10:43:149 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:10:43:153 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:10:43:266 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1672mV
Get AD_V4 1653mV
Get AD_V5 2768mV
Get AD_V6 1988mV
Get AD_V7 1096mV
OVER 150


2025-07-31 18:10:43:437 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:10:43:443 ==>> 检测【检测BootVer】
2025-07-31 18:10:43:447 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:10:43:887 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130020290639
[D][05:18:22][FCTY]HardwareID  = 867222087871747
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = D7F2CB10B528
[D][05:18:22][FCTY]Bat         = 3944 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11900 mv
$GBGGA,101047.509,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,42,13,,,41,38,,,41,26,,,41,1*7F

$GBGSV,6,2,24,60,,,40,8,,,40,3,,,40,59,,,40,1*75

$GBGSV,6,3,24,21,,,40,42,,,40,39,,,39,16,,,39,1*7D

$GBGSV,6,4,24,1,,,38,9,,,37,14,,,37,2,,,36,1*43

$GBGSV,6,5,24,6,,,36,33,,,36,5,,,34,4,,,33,1*43

$GBGSV,6,6,24,7,,,30,45,,,29,40,,,29,25,,,,1*46

$GBRMC,101047.509,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101047.509,0.000,771.890,771.890,705.914,2097152,2097152,2

2025-07-31 18:10:43:977 ==>> 097152*60

[D][05:18:22][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1295
[D][05:18:22][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 101 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3775 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:10:44:256 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:10:44:260 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:10:44:265 ==>> 检测【检测固件版本】
2025-07-31 18:10:44:297 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:10:44:305 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:10:44:311 ==>> 检测【检测蓝牙版本】
2025-07-31 18:10:44:331 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:10:44:336 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:10:44:343 ==>> 检测【检测MoBikeId】
2025-07-31 18:10:44:363 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:10:44:369 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:10:44:375 ==>> 检测【检测蓝牙地址】
2025-07-31 18:10:44:380 ==>> 取到目标值:D7F2CB10B528
2025-07-31 18:10:44:394 ==>> 【检测蓝牙地址】通过,【D7F2CB10B528】符合目标值【】要求!
2025-07-31 18:10:44:405 ==>> 提取到蓝牙地址:D7F2CB10B528
2025-07-31 18:10:44:424 ==>> 检测【BOARD_ID】
2025-07-31 18:10:44:432 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:10:44:438 ==>> 检测【检测充电电压】
2025-07-31 18:10:44:450 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:10:44:455 ==>> 检测【检测VBUS电压1】
2025-07-31 18:10:44:472 ==>> 【检测VBUS电压1】通过,【11900mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:10:44:477 ==>> 检测【检测充电电流】
2025-07-31 18:10:44:492 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:10:44:496 ==>> 检测【检测IMEI】
2025-07-31 18:10:44:505 ==>> 取到目标值:867222087871747
2025-07-31 18:10:44:521 ==>> 【检测IMEI】通过,【867222087871747】符合目标值【】要求!
2025-07-31 18:10:44:526 ==>> 提取到IMEI:867222087871747
2025-07-31 18:10:44:533 ==>> 检测【检测IMSI】
2025-07-31 18:10:44:562 ==>> 取到目标值:460130020290639
2025-07-31 18:10:44:566 ==>> 【检测IMSI】通过,【460130020290639】符合目标值【】要求!
2025-07-31 18:10:44:570 ==>> 提取到IMSI:460130020290639
2025-07-31 18:10:44:575 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:10:44:604 ==>> 取到目标值:460130020290639
2025-07-31 18:10:44:609 ==>> 【校验网络运营商(移动)】通过,【460130020290639】符合目标值【】要求!
2025-07-31 18:10:44:616 ==>> 检测【打开CAN通信】
2025-07-31 18:10:44:636 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:10:44:662 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:10:44:886 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:10:44:894 ==>> 检测【检测CAN通信】
2025-07-31 18:10:44:898 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:10:44:977 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:10:45:037 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:10:45:127 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:10:45:173 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:10:45:181 ==>> 检测【关闭CAN通信】
2025-07-31 18:10:45:190 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:10:45:195 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:10:45:262 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:10:45:453 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:10:45:460 ==>> 检测【打印IMU STATE】
2025-07-31 18:10:45:467 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:10:45:562 ==>> $GBGGA,101044.515,2301.2558083,N,11421.9413105,E,1,09,1.59,69.046,M,-1.770,M,,*57

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,24,13,81,254,41,8,79,185,40,42,67,4,41,16,64,311,39,1*4E

$GBGSV,6,2,24,38,63,168,41,26,62,30,41,3,62,191,40,24,62,232,42,1*7B

$GBGSV,6,3,24,39,54,331,40,6,54,8,36,59,52,130,40,1,48,126,38,1*7F

$GBGSV,6,4,24,2,46,239,36,21,46,111,40,14,43,333,37,9,42,283,37,1*7C

$GBGSV,6,5,24,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,34,1*74

$GBGSV,6,6,24,7,15,181,30,40,12,169,29,25,7,224,,33,,,36,1*4B

$GBRMC,101044.515,A,2301.2558083,N,11421.9413105,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:18:23][GNSS]HD8040 GPS
[D][05:18:23][GNSS]GPS diff_sec 124001541, report 0x42 frame
$GBGST,101044.515,0.568,0.255,0.247,0.376,3.398,4.279,17*5F

[D][05:18:23][COMM]Main Task receive event:131
[D][05:18:23][COMM]index:0,power_mode:0xFF
[D][05:18:23][COMM]index:1,sound_mode:0xFF
[D][05:18:23][COMM]index:2,gsensor_mode:0xFF
[D][05:18:23][COMM]index:3,report_freq_mode:0xFF
[D][05:18:23][COMM]index:4,report_period:0xFF
[D][05:18:23][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:23][COMM]index:6,normal_re

2025-07-31 18:10:45:667 ==>> set_period:0xFF
[D][05:18:23][COMM]index:7,spock_over_speed:0xFF
[D][05:18:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:23][COMM]index:16,imu_config_params:0xFF
[D][05:18:23][COMM]index:17,long_connect_params:0xFF
[D][05:18:23][COMM]index:18,detain_mark:0xFF
[D][05:18:23][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:23][COMM]index:21,mc_mode:0xFF
[D][05:18:23][COMM]index:22,S_mode:0xFF
[D][05:18:23][COMM]index:23,overweight:0xFF
[D][05:18:23][COMM]index:24,standstill_mode:0xFF
[D][05:18:23][COMM]index:25,night_mode:0xFF
[D][05:18:23][COMM]index:26,experiment1:0xFF
[D][05:18:23][COMM]index:27,experiment2:0xFF
[D][05:18:23][COMM]index:28,experiment3:0xFF
[D][05:18:23][COMM]index:29,experiment4:0xFF
[D][05:18:23][COMM]index:30,night_mode_

2025-07-31 18:10:45:772 ==>> start:0xFF
[D][05:18:23][COMM]index:31,night_mode_end:0xFF
[D][05:18:23][COMM]index:33,park_report_minutes:0xFF
[D][05:18:23][COMM]index:34,park_report_mode:0xFF
[D][05:18:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:23][COMM]index:38,charge_battery_para: FF
[D][05:18:23][COMM]index:39,multirider_mode:0xFF
[D][05:18:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:23][COMM]index:44,riding_duration_config:0xFF
[D][05:18:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:23][COMM]index:49,mc_load_startup:0xFF
[D][05:18:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:23][COMM]index:52,traffic_mode:0xFF
[D][05:18:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:23][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:23][COMM]index:55,wheel_alarm_play_switch:255
[D][0

2025-07-31 18:10:45:877 ==>> 5:18:23][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:23][COMM]index:63,experiment5:0xFF
[D][05:18:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:23][COMM]index:72,experiment6:0xFF
[D][05:18:23][COMM]index:73,experiment7:0xFF
[D][05:18:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:23][COMM]index:75,zero_value_from_server:-1
[D][05:18:23][COMM]index:76,multirider_threshold:255
[D][05:18:23][COMM]index:77,experiment8:255
[D][05:18:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:23][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:23][COMM]index:80,temp_par

2025-07-31 18:10:45:982 ==>> k_reminder_timeout_duration:255
[D][05:18:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:23][COMM]index:83,loc_report_interval:255
[D][05:18:23][COMM]index:84,multirider_threshold_p2:255
[D][05:18:23][COMM]index:85,multirider_strategy:255
[D][05:18:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:23][COMM]index:90,weight_param:0xFF
[D][05:18:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:23][COMM]index:95,current_limit:0xFF
[D][05:18:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:23][COMM]index:100,location_mode:0xFF

[D][05:18:23][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:23][PROT]remove success[1629955103],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:23][PROT]add success [1629955103],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:23][PROT]index:0 1629955103
[D][05:18:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:23][PROT]is_send:0
[D][05:18:23][PROT]sequence

2025-07-31 18:10:46:087 ==>> _num:4
[D][05:18:23][PROT]retry_timeout:0
[D][05:18:23][PROT]retry_times:1
[D][05:18:23][PROT]send_path:0x2
[D][05:18:23][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:23][PROT]===========================================================
[W][05:18:23][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955103]
[D][05:18:23][PROT]===========================================================
[D][05:18:23][PROT]sending traceid [9999999999900005]
[D][05:18:23][PROT]Send_TO_M2M [1629955103]
[D][05:18:23][COMM]Main Task receive event:131 finished processing
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:23][SAL ]sock send credit cnt[6]
[D][05:18:23][SAL ]sock send ind credit cnt[6]
[D][05:18:23][M2M ]m2m send data len[294]
[D][05:18:23][SAL ]Cellular task submsg id[10]
[D][05:18:23][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:23][CAT1]gsm read msg sub id: 15
[D][05:18:23][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:23][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B5065973F4589B04F5EC13EFD9DA45A8B2E3CC80A225D5430C47FD61274270A3B6523D2

2025-07-31 18:10:46:192 ==>> E0A1721A8F0E1DD3A861B9C5E2C9359B067F89C6F764E4F6AE7385357C52D22D4A93E6724B2CAC344066475132C4945885C3B0302FE516595DB332938D1F52881E5C34F8CA19E2B40748C6614956C1D6AC83842863B656D2A7C4C97D724964D
[D][05:18:23][CAT1]<<< 
SEND OK

[D][05:18:23][CAT1]exec over: func id: 15, ret: 11
[D][05:18:23][CAT1]sub id: 15, ret: 11

[D][05:18:23][SAL ]Cellular task submsg id[68]
[D][05:18:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:23][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:23][M2M ]g_m2m_is_idle become true
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:23][PROT]M2M Send ok [1629955103]
[D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34912
$GBGGA,101045.015,2301.2566372,N,11421.9414934,E,1,09,1.59,73.480,M,-1.770,M,,*5B

[D][05:18:23][COMM]read battery soc:255
$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,23,

2025-07-31 18:10:46:298 ==>>                                                                                                                                                                                                                                                           

2025-07-31 18:10:46:387 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 .451,1.293,1.861,8.856*7D

[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:10:46:496 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:10:46:501 ==>> 检测【六轴自检】
2025-07-31 18:10:46:508 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:10:46:650 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:10:47:128 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 18:10:47:233 ==>> $GBGGA,101047.000,2301.2570924,N,11421.9418347,E,1,09,1.59,74.664,M,-1.770,M,,*5E

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,4,40,16,

2025-07-31 18:10:47:308 ==>> 64,311,39,1*48

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,40,24,62,232,42,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,36,59,52,130,40,1,48,126,38,1*78

$GBGSV,6,4,23,2,46,239,37,21,46,111,40,14,43,333,36,9,42,283,37,1*7B

$GBGSV,6,5,23,60,41,238,40,4,32,112,33,45,25,199,30,5,22,258,34,1*74

$GBGSV,6,6,23,7,15,181,30,40,12,169,30,33,,,37,1*41

$GBGSV,2,1,05,42,67,4,43,38,63,168,40,26,62,30,41,24,62,232,42,5*44

$GBGSV,2,2,05,21,46,111,41,5*42

$GBRMC,101047.000,A,2301.2570924,N,11421.9418347,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101047.000,0.984,0.299,0.287,0.448,1.191,1.626,7.827*75



2025-07-31 18:10:48:339 ==>> $GBGGA,101048.000,2301.2571514,N,11421.9420063,E,1,09,1.59,74.767,M,-1.770,M,,*53

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,4,40,16,64,311,39,1*48

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,41,24,62,232,42,1*7D

$GBGSV,6,3,23,39,54,331,40,6,54,8,36,59,52,130,40,1,48,126,38,1*78

$GBGSV,6,4,23,2,46,239,37,21,46,111,40,14,43,333,36,9,42,283,37,1*7B

$GBGSV,6,5,23,60,41,238,40,4,32,112,33,45,25,199,30,5,22,258,34,1*74

$GBGSV,6,6,23,7,15,181,30,40,12,169,30,33,,,37,1*41

$GBGSV,2,1,05,42,67,4,43,38,63,168,41,26,62,30,41,24,62,232,42,5*45

$GBGSV,2,2,05,21,46,111,41,5*42

$GBRMC,101048.000,A,2301.2571514,N,11421.9420063,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,101048.000,0.742,0.300,0.289,0.449,0.866,1.260,7.021*78

                                            

2025-07-31 18:10:48:369 ==>>                                       

2025-07-31 18:10:48:474 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38354 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-24,-13,4032]
[D][05:18:27][COMM]Main Task receive event:14

2025-07-31 18:10:48:504 ==>> 2 finished processing


2025-07-31 18:10:48:583 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:10:48:591 ==>> 检测【打印IMU STATE2】
2025-07-31 18:10:48:612 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:10:48:764 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:10:48:860 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:10:48:867 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:10:48:887 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:10:48:963 ==>> 5A A5 02 5A A5 


2025-07-31 18:10:49:068 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:10:49:131 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:10:49:139 ==>> 检测【检测VBUS电压2】
2025-07-31 18:10:49:145 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:10:49:342 ==>> [D][05:18:28][COMM]read battery soc:255
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 12
$GBGGA,101049.000,2301.2572125,N,11421.9420330,E,1,09,1.59,74.109,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,4,41,16,64,311,39,1*49

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,40,24,62,232,43,1*7D

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,37,21,46,111,40,14,43,333,36,9,42,283,37,1*7B

$GBGSV,6,5,23,60,41,238,40,4,32,112,33,45,25,199,30,5,22,258,34,1*74

$GBGSV,6,6,23,7,15,181,30,40,12,169,30,33,,,37,1*41

$GBGSV,2,1,05,42,67,4,43,38,63,168,41,26,62,30,41,24,62,232,43,5*44

$GBGSV,2,2,05,21,46,111,41,5*42

$G

2025-07-31 18:10:49:388 ==>> BRMC,101049.000,A,2301.2572125,N,11421.9420330,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101049.000,0.787,0.242,0.234,0.353,0.819,1.155,6.453*70



2025-07-31 18:10:49:660 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130020290639
[D][05:18:28][FCTY]HardwareID  = 867222087871747
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D7F2CB10B528
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 7, adc = 308
[D][05:18:28][FCTY]Acckey1 vol = 5498 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL

2025-07-31 18:10:49:705 ==>> _RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3775 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:10:49:923 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:10:50:058 ==>> [D][05:18:28][COMM]msg 0601 loss. last_tick:34899. cur_tick:39905. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39906


2025-07-31 18:10:50:407 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130020290639
[D][05:18:28][FCTY]HardwareID  = 867222087871747
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D7F2CB10B528
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 170
[D][05:18:29][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 37

2025-07-31 18:10:50:456 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:10:50:462 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:10:50:471 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:10:50:512 ==>> 75 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
$GBGGA,101050.000,2301.2572887,N,11421.9420039,E,1,09,1.59,73.927,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.04,1.59,3.72,4*07

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,5,41,16,64,311,39,1*48

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,40,24,62,232,42,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,36,21,46,111,40,14,43,333,37,9,42,283,37,1*7B

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,34,1*73

$GBGSV,6,6,23,7,16,181,30,40,12,169,30,33,,,37,1*42

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,46,111,41,5*42

$GBRMC,101050.000,A,2301.2572887,N,11421.9420039,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,101050.000,0.782,0.263,0.254,0.390,0.753,1.048,5.958*7E

5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:10:50:587 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 3


2025-07-31 18:10:50:730 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:10:50:735 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:10:50:743 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:10:50:865 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:10:51:004 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:10:51:012 ==>> 检测【打开WIFI(3)】
2025-07-31 18:10:51:021 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:10:51:343 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:30][COMM]read battery soc:255
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6
$GBGGA,101051.000,2301.2573003,N,11421.9419848,E,1,09,1.59,74.107,M,-1.770,M,,*51

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,5,41,16,64,311,39,1*48

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,41,24,62,232,43,1*7C

$GBGSV,6,3,23,39,54,331,39,6,54,8,37,59,52,130,40,1,48,126,38,1*77

$GBGSV,6,4,23,2,46,239,37,21,46,111,40,14,43,333,36,9,42,283,37,1*7B

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,34,1*73

$GBGSV,6,6,23,7,16,181,30,40,12,169,30,33,,,37,1*42

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,46,111,41,5*42

$GBRMC,101051.000,A,2301.2573003,N,11421.9419848,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101051.000,0.849,0.243,0.236,0.357,0.770,1.028,5.606*79



2025-07-31 18:10:51:544 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:10:51:549 ==>> 检测【扩展芯片hw】
2025-07-31 18:10:51:557 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:10:51:761 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:10:51:825 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:10:51:834 ==>> 检测【扩展芯片boot】
2025-07-31 18:10:51:848 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:10:51:856 ==>> 检测【扩展芯片sw】
2025-07-31 18:10:51:883 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:10:51:888 ==>> 检测【检测音频FLASH】
2025-07-31 18:10:51:917 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:10:52:069 ==>> +WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,44A1917CA62B,-70
+WIFISCAN:4,2,CC057790A741,-71
+WIFISCAN:4,3,603A7CF67DD4,-72

[D][05:18:30][CAT1]wifi scan report total[4]
[D][05:18:30][GNSS]recv submsg id[3]
[D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:10:53:062 ==>> [D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------
$GBGGA,101052.000,2301.2572919,N,11421.9419759,E,1,09,1.59,74.391,M,-1.770,M,,*53

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,254,41,8,79,185,40,42,67,5,41,16,64,311,39,1*48

$GBGSV,6,2,23,38,63,168,41,26,62,30,41,3,62,191,41,24,62,232,43,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,37,21,46,111,40,14,43,333,37,9,42,283,37,1*7A

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,35,1*72

$GBGSV,6,6,23,7,16,181,30,40,12,169,31,33,,,37,1*43

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,46,111,41,5*42

$GBRMC,101052.000,A,2301.2572919,N,11421.9419759,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101052.000,1.142,0.225,0.218,0.331,0.994,1.205,5.394*72

[D][05:18:31][COMM]frm_periphera

2025-07-31 18:10:53:167 ==>> l_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].

2025-07-31 18:10:53:272 ==>> cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495

2025-07-31 18:10:53:377 ==>> A453A323034380D0A0D0A4F4B0D0A
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][PROT]index:0
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble 

2025-07-31 18:10:53:482 ==>> is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][PROT]index:0 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900006]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[704].audio cmd se

2025-07-31 18:10:53:587 ==>> nd:AT+AUDIOSEND=1

[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B397E554B14758B8C9C1D4043D0B0666DBBDA46DE80E131CC7A0C13E5D31EC6CE010904049EDD9A1E216055CD5BD56

2025-07-31 18:10:53:692 ==>> 1781DB0A39609F442C3491C55C77718F5F77F80B79C3872BAB626A8B1AA4BBA99CE45EF5
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle 

2025-07-31 18:10:53:797 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:10:54:337 ==>> $GBGGA,101054.000,2301.2573151,N,11421.9419610,E,1,09,1.59,74.219,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,38,63,168,42,26,62,30,41,3,62,191,41,24,62,232,43,1*7F

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,37,21,45,111,40,14,43,333,37,9,42,283,37,1*79

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,35,1*72

$GBGSV,6,6,23,7,16,181,30,40,12,169,31,33,,,37,1*43

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,45,111,41,5*41

$GBRMC,101054.000,A,2301.2573151,N,11421.9419610,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,101054.000,0.718,0.232,0.225,0.342,0.554,0.761,4.723*7F



2025-07-31 18:10:55:335 ==>> [D][05:18:34][COMM]read battery soc:255
$GBGGA,101055.000,2301.2573297,N,11421.9419537,E,1,09,1.59,74.175,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,38,63,168,41,3,62,191,41,26,62,30,41,24,62,232,43,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,41,1,48,126,38,1*78

$GBGSV,6,4,23,2,46,239,37,21,45,111,40,14,43,333,37,9,42,283,37,1*79

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,34,1*73

$GBGSV,6,6,23,7,16,181,31,40,12,169,31,33,,,37,1*42

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,45,111,41,5*41

$GBRMC,101055.000,A,2301.2573297,N,11421.9419537,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,101055.000,0.730,0.277,0.267,0.416,0.545,0.736,4.503*77

[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:10:55:790 ==>> [D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 18:10:56:422 ==>> $GBGGA,101056.000,2301.2573420,N,11421.9419612,E,1,09,1.59,74.207,M,-1.770,M,,*51

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,38,63,168,41,3,62,191,41,26,62,30,41,24,62,232,43,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,37,21,45,111,40,14,43,333,37,9,42,284,37,1*7E

$GBGSV,6,5,23,60,41,238,41,4,32,112,34,45,25,199,30,5,22,258,35,1*73

$GBGSV,6,6,23,7,16,181,31,40,12,169,31,33,,,37,1*42

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,45,111,41,5*41

$GBRMC,101056.000,A,2301.2573420,N,11421.9419612,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101056.000,0.966,0.225,0.218,0.334,0.760,0.921,4.430*7D

[D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok
[D][05:18:35][COMM]46108 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:

2025-07-31 18:10:56:467 ==>> 35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:35][COMM]accel parse set 0
[D][05:18:35][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:10:56:951 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:10:56:957 ==>> 检测【打开喇叭声音】
2025-07-31 18:10:56:981 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:10:57:800 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:36][COMM]read file, len:15228, num:4
[D][05:18:36][COMM]read battery soc:255
$GBGGA,101057.000,2301.2573439,N,11421.9419592,E,1,09,1.59,74.149,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,16,38,26,24,21,14,,,,4.05,1.59,3.72,4*06

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,38,63,168,41,3,62,191,41,26,62,30,41,24,62,232,43,1*7C

$GBGSV,6,3,23,39,54,331,40,6,54,8,37,59,52,130,40,1,48,126,38,1*79

$GBGSV,6,4,23,2,46,239,37,21,45,111,40,14,43,333,37,9,42,284,37,1*7E

$GBGSV,6,5,23,60,41,238,41,4,32,112,34,45,25,199,30,5,22,258,35,1*73

$GBGSV,6,6,23,7,16,181,31,40,12,169,31,33,,,37,1*42

$GBGSV,2,1,05,42,67,5,43,38,63,168,41,2

2025-07-31 18:10:57:904 ==>> 6,62,30,41,24,62,232,43,5*45

$GBGSV,2,2,05,21,45,111,41,5*41

$GBRMC,101057.000,A,2301.2573439,N,11421.9419592,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101057.000,1.055,0.208,0.202,0.306,0.830,0.976,4.320*7F

[D][05:18:36][COMM]--->crc16:0x419c
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]47121 imu init OK
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:

2025-07-31 18:10:58:009 ==>> [ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[9

2025-07-31 18:10:58:015 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:10:58:021 ==>> 检测【打开大灯控制】
2025-07-31 18:10:58:032 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:10:58:114 ==>> 75].hexsend, index:6, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:0 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:2
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D]

2025-07-31 18:10:58:219 ==>> [05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3AE35CA9CEB49A3B7513EB306C99074CDD0AED3CF57385336E0F411CF9AB998B5657D452A8205E33A844A8E4E6EEB609A1F4E613506390758403E1B991AA4E1E55D38E8891972262CEB4844F5AF380B718751
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM

2025-07-31 18:10:58:325 ==>> _SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:10:59:045 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:10:59:341 ==>> [D][05:18:38][COMM]read battery soc:255
$GBGGA,101059.000,2301.2573206,N,11421.9419343,E,1,11,1.53,74.157,M,-1.770,M,,*58

$GBGSA,A,3,13,08,42,16,06,38,26,24,39,21,14,,4.00,1.53,3.70,4*07

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,6,64,305,37,38,63,168,41,3,62,191,40,26,62,30,41,1*4D

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,59,52,130,40,1,48,126,38,1*48

$GBGSV,6,4,23,2,46,239,37,21,45,111,40,14,43,333,36,9,42,284,37,1*7F

$GBGSV,6,5,23,60,41,238,40,4,32,112,34,45,25,199,30,5,22,258,34,1*73

$GBGSV,6,6,23,7,16,181,31,40,12,169,31,33,,,37,1*42

$GBGSV,2,1,06,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*46

$GBGSV,2,2,06,39,62,339,38,21,45,111,41,5*7E

$GBRMC,101059.000,A,2301.2573206,N,11421.9419343,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,101059.000,1.026,0.247,0.238,0.374,0.780,0.912,4.042*73

[W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:10:59:610 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:10:59:616 ==>> 检测【关闭仪表供电3】
2025-07-31 18:10:59:622 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:10:59:753 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 18:10:59:902 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:10:59:907 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:10:59:913 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:11:00:031 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:11:00:135 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:11:00:188 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:11:00:198 ==>> 检测【读大灯电压】
2025-07-31 18:11:00:206 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:11:00:426 ==>> $GBGGA,101100.000,2301.2573520,N,11421.9419254,E,1,17,0.95,74.101,M,-1.770,M,,*5F

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,59,02,2.12,0.95,1.89,4*06

$GBGSA,A,3,01,21,60,14,05,,,,,,,,2.12,0.95,1.89,4*01

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,6,64,305,37,3,64,190,40,38,63,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,59,52,126,40,2,49,241,37,1*40

$GBGSV,6,4,23,1,47,123,38,21,45,111,40,60,44,243,41,14,43,333,36,1*49

$GBGSV,6,5,23,9,42,284,37,4,32,112,33,45,25,199,30,5,24,258,34,1*49

$GBGSV,6,6,23,33,18,323,37,7,16,181,30,40,12,169,31,1*78

$GBGSV,2,1,06,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*46

$GBGSV,2,2,06,39,62,339,39,21,45,111,41,5*7F

$GBRMC,101100.000,A,2301.2573520,N,11421.9419254,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,101100.000,1.145,0.242,0.249,0.356,0.876,0.994,3.954*78

[W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[32992]


2025-07-31 18:11:00:459 ==>> 【读大灯电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:11:00:473 ==>> 检测【关闭大灯控制2】
2025-07-31 18:11:00:482 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:11:00:622 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:11:00:732 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:11:00:739 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:11:00:747 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:11:00:940 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:11:01:005 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:11:01:014 ==>> 检测【打开WIFI(4)】
2025-07-31 18:11:01:036 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:11:01:359 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][COMM]51016 imu init OK
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6
[D][05:18:40][COMM]read battery soc:255
$GBGGA,101101.000,2301.2573868,N,11421.9418724,E,1,19,0.87,73.772,M,-1.770,M,,*54

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.69,0.87,1.45,4*01

$GBGSA,A,3,02,01,21,60,14,05,33,,,,,,1.69,0.87,1.45,4*0F

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,40,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,33,45,25,199,30,5,24,258,34,1*78

$GBGSV,6,6,23,33,18,323,37,7,16,181,30,40,12,169,31,1*78

$GBGSV,2,1,07,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*47

$GBGSV,2,2,07,39,62,339,40,21,45,111,41,33,18,323,30,5*48

$GBRMC,101101.000,A,2301.2573868,N,114

2025-07-31 18:11:01:389 ==>> 21.9418724,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,101101.000,1.702,0.269,0.271,0.369,1.314,1.399,3.913*79



2025-07-31 18:11:01:633 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:11:01:643 ==>> 检测【EC800M模组版本】
2025-07-31 18:11:01:667 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:11:01:908 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:40][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:11:02:058 ==>> +WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,CC057790A740,-68
+WIFISCAN:4,2,CC057790A741,-70
+WIFISCAN:4,3,CC057790A7C1,-71

[D][05:18:40][CAT1]wifi scan report total[4]
[D][05:18:40][GNSS]recv submsg id[3]


2025-07-31 18:11:02:166 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:11:02:173 ==>> 检测【配置蓝牙地址】
2025-07-31 18:11:02:183 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:11:02:382 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D7F2CB10B528>】
2025-07-31 18:11:02:397 ==>> [D][05:18:41][COMM]52027 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init
$GBGGA,101102.000,2301.2573885,N,11421.9418519,E,1,20,0.82,73.670,M,-1.770,M,,*54

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.43,0.82,1.17,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,,,,,1.43,0.82,1.17,4*02

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,40,16,64,311,39,1*4E

$GBGSV,6,2,23,6,64,305,37,3,64,190,40,38,63,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,41,1*4F

$GBGSV,6,4,23,2,49,241,36,1,47,123,38,21,45,111,40,60,44,243,41,1*70

$GBGSV,6,5,23,14,43,333,36,4,32,112,33,45,25,199,31,5,24,258,34,1*79

$GBGSV,6,6,23,33,18,323,37,7,15,176,30,40,12,169,31,1*73

$GBGSV,2,1,07,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*47

$GBGSV,2,2,07,39,62,339,41,21,45,111,41,33,18,323,30,5*49

$GBRMC,101102.000,A,2301.2573885,N,11421.9418519,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101102.000,1.865,0.249,0.246,0.329,1.425,1.496,3.776*76

[W][05:18:41][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:11:02:563 ==>> recv ble 1
recv ble 2
ble set mac ok :d7,f2,cb,10,b5,28
enable filters ret : 0

2025-07-31 18:11:02:690 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:11:02:700 ==>> 检测【BLETEST】
2025-07-31 18:11:02:720 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:11:02:761 ==>> 4A A4 01 A4 4A 


2025-07-31 18:11:03:003 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save
[D][05:18:41][PROT]CLEAN,SEND:0
[D][05:18:41][PROT]index:0 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:1
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit

2025-07-31 18:11:03:109 ==>>  cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3AB50525980421DFBCB07B5BB793868403C78C1C413F9203F9C564275CDF3E262548AE1D8481B84315728E5A01B0B4B0A81CAA5B7B71C05770A79E76FE6D9F0733B907665FF839D08C6190AAAFFFD01DD2E15
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]
recv ble 1
recv ble 2
<BSJ*MAC:D7F2CB10B528*RSSI:-23*ADD:110765

2025-07-31 18:11:03:139 ==>> 6B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D7F2CB10B52899999OVER 150


2025-07-31 18:11:03:379 ==>>                                       [D][05:18:42][COMM]read battery soc:255
$GBGGA,101103.000,2301.2573958,N,11421.9418151,E,1,20,0.82,73.422,M,-1.770,M,,*59

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.43,0.82,1.17,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,,,,,1.43,0.82,1.17,4*02

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,6,64,305,37,3,64,190,40,38,63,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,43,39,62,339,40,9,58,277,37,59,52,126,41,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,33,45,26,199,31,5,24,258,34,1*7A

$GBGSV,6,6,23,33,18,323,37,7,15,176,30,40,12,169,31,1*73

$GBGSV,2,1,07,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*47

$GBGSV,2,2,07,39,62,339,41,21,45,111,41,33,18,323,30,5*49

$GBRMC,101103.000,A,2301.2573958,N,11421.9418151,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,101103.000,1.629,0.243,0.240,0.320,1.247,1.314,3.503*7B



2025-07-31 18:11:03:715 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:11:03:722 ==>> 该项需要延时执行
2025-07-31 18:11:04:360 ==>> $GBGGA,101104.000,2301.2574088,N,11421.9418045,E,1,20,0.82,73.364,M,-1.770,M,,*5C

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.43,0.82,1.17,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,,,,,1.43,0.82,1.17,4*02

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,39,1*4F

$GBGSV,6,2,23,6,64,305,37,3,64,190,40,38,63,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,43,39,62,339,40,9,58,277,37,59,52,126,41,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,45,26,199,31,5,24,258,34,1*7D

$GBGSV,6,6,23,33,18,323,37,7,15,176,30,40,12,169,31,1*73

$GBGSV,2,1,07,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*47

$GBGSV,2,2,07,39,62,339,41,21,45,111,41,33,18,323,30,5*49

$GBRMC,101104.000,A,2301.2574088,N,11421.9418045,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,101104.000,1.601,0.268,0.265,0.353,1.221,1.282,3.348*7B



2025-07-31 18:11:05:356 ==>> $GBGGA,101105.000,2301.2574402,N,11421.9417714,E,1,21,0.71,73.231,M,-1.770,M,,*5B

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,185,40,42,67,5,41,16,64,311,40,1*41

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,42,26,62,30,41,1*48

$GBGSV,6,3,23,24,62,232,43,39,62,339,40,9,58,277,37,59,52,126,41,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,39,21,45,111,40,60,44,243,41,1*70

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,18,323,38,1*7B

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,07,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*47

$GBGSV,2,2,07,39,62,339,40,21,45,111,41,33,18,323,30,5*48

$GBRMC,101105.000,A,2301.2574402,N,11421.9417714,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101105.000,1.609,0.250,0.248,0.325,1.222,1.277,3.205*76

[D][05:18:44][COMM]read battery soc:255


2025-07-31 18:11:06:366 ==>> $GBGGA,101106.000,2301.2574545,N,11421.9417618,E,1,21,0.71,73.202,M,-1.770,M,,*57

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,43,39,62,339,40,9,58,277,37,59,52,126,41,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,18,323,38,1*7B

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,18,323,31,45,7,40,32,5*74

$GBRMC,101106.000,A,2301.2574545,N,11421.9417618,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101106.000,1.708,0.222,0.221,0.290,1.293,1.340,3.119*71



2025-07-31 18:11:07:386 ==>> $GBGGA,101107.000,2301.2574609,N,11421.9417777,E,1,21,0.71,73.186,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,40,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,18,323,37,1*74

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,18,323,31,45,7,40,33,5*75

$GBRMC,101107.000,A,2301.2574609,N,11421.9417777,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101107.000,1.781,0.202,0.201,0.267,1.343,1.386,3.065*75

[D][05:18:46][COMM]read battery soc:255


2025-07-31 18:11:08:237 ==>> [D][05:18:46][PROT]CLEAN,SEND:0
[D][05:18:46][PROT]CLEAN:0
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:6
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:3
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:18:46][PROT]sending traceid [9999999999900007]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[134]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm rea

2025-07-31 18:11:08:342 ==>> d msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:46][CAT1]Send Data To Server[134][137] ... ->:
0043B681113311331133113311331B88BEFDE5FCD183D92B7706948C58BEBAEA975A50D9F2085F851DD705A12DC080CBE7FA58E3003CBCBDAC4254F76CE09C1BEA9ECC
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
                                                                                                                                                                                                                                                                                                                

2025-07-31 18:11:08:417 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:11:09:385 ==>> $GBGGA,101109.000,2301.2574706,N,11421.9417629,E,1,21,0.71,73.193,M,-1.770,M,,*54

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,40,16,64,311,39,1*4D

$GBGSV,6,2,23,6,64,305,37,3,64,190,40,38,63,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,40,1*4E

$GBGSV,6,4,23,2,49,241,36,1,47,123,38,21,45,111,40,60,44,243,41,1*70

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,18,323,37,1*74

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,18,323,31,45,7,40,33,5*75

$GBRMC,101109.000,A,2301.2574706,N,11421.9417629,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101109.000,1.865,0.198,0.197,0.262,1.398,1.434,2.961*73

[D][05:18:48][COMM]read battery soc:255


2025-07-31 18:11:10:384 ==>> $GBGGA,101110.000,2301.2574751,N,11421.9417568,E,1,21,0.71,73.155,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,40,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,37,4,32,112,34,5,24,258,34,33,19,323,37,1*74

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,19,323,31,45,7,40,33,5*74

$GBRMC,101110.000,A,2301.2574751,N,11421.9417568,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,101110.000,1.926,0.230,0.229,0.301,1.439,1.473,2.932*77



2025-07-31 18:11:11:356 ==>> $GBGGA,101111.000,2301.2574718,N,11421.9417487,E,1,21,0.71,73.250,M,-1.770,M,,*58

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,40,16,64,311,39,1*4D

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,42,39,62,339,40,9,58,277,37,59,52,126,40,1*4E

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,19,323,37,1*75

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,19,323,31,45,7,40,33,5*74

$GBRMC,101111.000,A,2301.2574718,N,11421.9417487,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101111.000,1.839,0.225,0.223,0.294,1.375,1.407,2.833*76

[D][05:18:50][COMM]read battery soc:255


2025-07-31 18:11:12:367 ==>> $GBGGA,101112.000,2301.2574770,N,11421.9417398,E,1,21,0.71,73.225,M,-1.770,M,,*5E

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,63,168,41,26,62,30,41,1*4B

$GBGSV,6,3,23,24,62,232,42,39,62,339,39,9,58,277,37,59,52,126,40,1*40

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,19,323,37,1*75

$GBGSV,6,6,23,7,15,176,30,40,12,169,31,45,7,40,31,1*7C

$GBGSV,2,1,08,42,67,5,43,38,63,168,41,26,62,30,41,24,62,232,43,5*48

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,19,323,31,45,7,40,33,5*74

$GBRMC,101112.000,A,2301.2574770,N,11421.9417398,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,101112.000,1.579,0.266,0.264,0.344,1.179,1.212,2.642*70



2025-07-31 18:11:13:536 ==>> $GBGGA,101113.000,2301.2574758,N,11421.9417336,E,1,21,0.71,73.254,M,-1.770,M,,*57

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,62,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,42,39,62,339,39,9,58,277,37,59,52,126,40,1*40

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,40,1*70

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,19,323,37,1*75

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,31,1*7D

$GBGSV,2,1,08,42,67,5,43,38,62,168,41,26,62,30,41,24,62,232,43,5*49

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,19,323,31,45,7,40,33,5*74

$GBRMC,101113.000,A,2301.2574758,N,11421.9417336,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:18:52][PROT]CLEAN,SEND:1
$GBGST,101113.000,1.578,0.248,0.246,0.323,1.176,1.208,2.597*72

[D][05:18:52][PROT]index:1 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:6
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:5

2025-07-31 18:11:13:641 ==>> 2][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:18:52][PROT]sending traceid [9999999999900007]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[134]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:52][COMM]read battery soc:255
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:52][CAT1]Send Data To Server[134][137] ... ->:
0043B682113311331133113311331B88BEA04C8041C2D0AB92352F3AE3B193365F2272376552952AE5D6EF

2025-07-31 18:11:13:701 ==>> F39341799FA54D9DBEB14E05AA34D422201134C8B3049304
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 18:11:13:731 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:11:13:738 ==>> 检测【检测WiFi结果】
2025-07-31 18:11:13:751 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 18:11:13:757 ==>> WiFi信号:【F42A7D1297A3】,信号值:-65
2025-07-31 18:11:13:777 ==>> WiFi信号:【F62A7D2297A3】,信号值:-67
2025-07-31 18:11:13:783 ==>> WiFi信号:【44A1917CA62B】,信号值:-70
2025-07-31 18:11:13:790 ==>> WiFi信号:【CC057790A741】,信号值:-71
2025-07-31 18:11:13:823 ==>> WiFi信号:【603A7CF67DD4】,信号值:-72
2025-07-31 18:11:13:829 ==>> WiFi信号:【CC057790A740】,信号值:-68
2025-07-31 18:11:13:856 ==>> WiFi信号:【CC057790A7C1】,信号值:-71
2025-07-31 18:11:13:867 ==>> WiFi数量【8】, 最大信号值:-34
2025-07-31 18:11:13:877 ==>> 检测【检测GPS结果】
2025-07-31 18:11:13:893 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 18:11:13:899 ==>> 
北斗星号:【24】,信号值:【42】
北斗星号:【13】,信号值:【41】
北斗星号:【38】,信号值:【41】
北斗星号:【26】,信号值:【41】
北斗星号:【60】,信号值:【40】
北斗星号:【8】,信号值:【40】
北斗星号:【3】,信号值:【40】
北斗星号:【39】,信号值:【40】
北斗星号:【42】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【59】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【36】
北斗星号:【21】,信号值:【40】
北斗星号:【14】,信号值:【37】
北斗星号:【9】,信号值:【37】
北斗星号:【33】,信号值:【36】

2025-07-31 18:11:13:911 ==>> 检测【CSQ强度】
2025-07-31 18:11:13:923 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:11:13:979 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 12
[D][05:18:52][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:52][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:52][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:11:14:033 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 18:11:14:040 ==>> 检测【关闭GSM联网】
2025-07-31 18:11:14:069 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:11:14:385 ==>> $GBGGA,101114.000,2301.2574837,N,11421.9417212,E,1,21,0.71,73.231,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.16,0.71,0.92,4*0B

$GBGSA,A,3,02,01,21,60,14,05,33,07,45,,,,1.16,0.71,0.92,4*03

$GBGSV,6,1,23,13,81,253,41,8,79,186,40,42,67,5,41,16,64,311,39,1*4C

$GBGSV,6,2,23,6,64,305,37,3,64,190,41,38,62,168,41,26,62,30,41,1*4A

$GBGSV,6,3,23,24,62,232,42,39,62,339,39,9,58,277,37,59,52,126,41,1*41

$GBGSV,6,4,23,2,49,241,37,1,47,123,38,21,45,111,40,60,44,243,41,1*71

$GBGSV,6,5,23,14,43,333,36,4,32,112,34,5,24,258,34,33,19,323,37,1*75

$GBGSV,6,6,23,7,15,176,31,40,12,169,31,45,7,40,30,1*7C

$GBGSV,2,1,08,42,67,5,43,38,62,168,41,26,62,30,41,24,62,232,43,5*49

$GBGSV,2,2,08,39,62,339,41,21,45,111,41,33,19,323,31,45,7,40,33,5*74

$GBRMC,101114.000,A,2301.2574837,N,11421.9417212,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,101114.000,1.597,0.220,0.218,0.287,1.189,1.219,2.568*7E

[W][05:18:53][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:53][COMM]GSM test
[D][05:18:53][COMM]GSM test disable


2025-07-31 18:11:14:570 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:11:14:576 ==>> 检测【4G联网测试】
2025-07-31 18:11:14:582 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:11:15:648 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:53][COMM]Main Task receive event:14
[D][05:18:53][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955133, allstateRepSeconds = 0
[D][05:18:53][COMM]index:0,power_mode:0xFF
[D][05:18:53][COMM]index:1,sound_mode:0xFF
[D][05:18:53][COMM]index:2,gsensor_mode:0xFF
[D][05:18:53][COMM]index:3,report_freq_mode:0xFF
[D][05:18:53][COMM]index:4,report_period:0xFF
[D][05:18:53][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:53][COMM]index:6,normal_reset_period:0xFF
[D][05:18:53][COMM]index:7,spock_over_speed:0xFF
[D][05:18:53][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:53][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:53][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:53][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:53][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:53][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:53][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:53][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:53][COMM]index:16,imu_config_params:0xFF
[D][05:18:53][COMM]index:17,long_connect_params:0xFF
[D][05:18:53][COMM]index:18,detain_mark:0xFF
[D][05:18:53][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:53][COMM]index:20,lock_pos_repo

2025-07-31 18:11:15:753 ==>> rt_interval:0xFF
[D][05:18:53][COMM]index:21,mc_mode:0xFF
[D][05:18:53][COMM]index:22,S_mode:0xFF
[D][05:18:53][COMM]index:23,overweight:0xFF
[D][05:18:53][COMM]index:24,standstill_mode:0xFF
[D][05:18:53][COMM]index:25,night_mode:0xFF
[D][05:18:53][COMM]index:26,experiment1:0xFF
[D][05:18:53][COMM]index:27,experiment2:0xFF
[D][05:18:53][COMM]index:28,experiment3:0xFF
[D][05:18:53][COMM]index:29,experiment4:0xFF
[D][05:18:53][COMM]index:30,night_mode_start:0xFF
[D][05:18:53][COMM]index:31,night_mode_end:0xFF
[D][05:18:53][COMM]index:33,park_report_minutes:0xFF
[D][05:18:53][COMM]index:34,park_report_mode:0xFF
[D][05:18:53][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:53][COMM]index:38,charge_battery_para: FF
[D][05:18:53][COMM]index:39,multirider_mode:0xFF
[D][05:18:53][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:53][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:53][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:53][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:53][COMM]index:44,riding_duration_config:0xFF
[D][05:18:53][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:53][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:53][COMM]inde

2025-07-31 18:11:15:858 ==>> x:47,bat_info_rep_cfg:0xFF
[D][05:18:53][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:53][COMM]index:49,mc_load_startup:0xFF
[D][05:18:53][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:53][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:53][COMM]index:52,traffic_mode:0xFF
[D][05:18:53][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:53][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:53][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:53][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:53][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:53][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:53][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:53][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:53][COMM]index:63,experiment5:0xFF
[D][05:18:53][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:53][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:53][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:53][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:53][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:53][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:53][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:

2025-07-31 18:11:15:963 ==>> 18:53][COMM]index:72,experiment6:0xFF
[D][05:18:53][COMM]index:73,experiment7:0xFF
[D][05:18:53][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:53][COMM]index:75,zero_value_from_server:-1
[D][05:18:53][COMM]index:76,multirider_threshold:255
[D][05:18:53][COMM]index:77,experiment8:255
[D][05:18:53][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:53][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:53][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:53][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:53][COMM]index:83,loc_report_interval:255
[D][05:18:53][COMM]index:84,multirider_threshold_p2:255
[D][05:18:53][COMM]index:85,multirider_strategy:255
[D][05:18:53][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:53][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:53][COMM]index:90,weight_param:0xFF
[D][05:18:53][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:53][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:53][COMM]index:95,current_limit:0xFF
[D][05:18:53][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:53][COMM]index:100,location_mode:0xFF

[W][05:18:53][PROT]remove success

2025-07-31 18:11:16:069 ==>> [1629955133],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:53][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:7
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:1
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:53][PROT]===========================================================
[W][05:18:53][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900008]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[W][05:18:53][PROT]add success [1629955133],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[294]
[D][05:18:53][CAT1]gsm read msg sub id: 13
[D][05:18:53][SAL ]Cellular t

2025-07-31 18:11:16:174 ==>> ask submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:53][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:53][CAT1]exec over: func id: 13, ret: 21
[D][05:18:53][M2M ]get csq[23]
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:53][CAT1]Send Data To Server[294][297] ... ->:
0093B98E113311331133113311331B88B243F1CFBA93E08BA90C3933C6D781184B955AB04EA4259FC4670065361BD56A07369D06E96BE8B28D2B9BEB0B0CE425C5AFA55A356EC1CDA3D156324CC154C6AD12D05314EE861D62F2555C5257E9271D5BB80FB6B1BA396C9321D30E530080199BF64365B88C570477B3AE0C7AC7B42A36B63E03A71AA7969F9AFC15EF3F073E0EA0
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M 

2025-07-31 18:11:16:265 ==>> Send ok [1629955133]
>>>>>RESEND ALLSTATE<<<<<
[W][05:18:53][PROT]remove success[1629955133],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:53][PROT]add success [1629955133],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:18:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:53][COMM]------>period, report file manifest
[D][05:18:53][COMM]Main Task receive event:14 finished processing
[D][05:18:53][CAT1]gsm read msg sub id: 21
[D][05:18:53][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]cell info report total[0]
[D][05:18:54][CAT1]exec over: func id: 21, ret: 6
$GBGGA,101115.000,2301.2574958,N,11421.9417077,E,1,21,0.80,73.188,M,-1.770,M,,*55

$GBGSA,A,3,13,08,42,16,06,03,38,26,24,39,09,59,1.29,0.80,1.01,

2025-07-31 18:11:16:369 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 18:11:16:603 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:11:16:610 ==>> 检测【关闭GPS】
2025-07-31 18:11:16:617 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:11:16:910 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]stop event:8
[D][05:18:55][GNSS]GPS stop. ret=0
[D][05:18:55][GNSS]all continue location stop
[W][05:18:55][GNSS]stop locating
[D][05:18:55][GNSS]all sing location stop
[D][05:18:55][CAT1]gsm read msg sub id: 24
[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 24, ret: 6
[D][05:18:55][CAT1]sub id: 24, ret: 6



2025-07-31 18:11:17:125 ==>> [D][05:18:55][GNSS]recv submsg id[1]
[D][05:18:55][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:55][GNSS]location stop evt done evt


2025-07-31 18:11:17:167 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:11:17:174 ==>> 检测【清空消息队列2】
2025-07-31 18:11:17:186 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:11:17:230 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 18:11:17:334 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:56][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:11:17:444 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:11:17:451 ==>> 检测【轮动检测】
2025-07-31 18:11:17:459 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:11:17:561 ==>> 3A A3 01 00 A3 


2025-07-31 18:11:17:667 ==>> OFF_OUT1
OVER 150


2025-07-31 18:11:17:726 ==>> [D][05:18:56][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:11:17:953 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:11:18:059 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:11:18:229 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:11:18:236 ==>> 检测【关闭小电池】
2025-07-31 18:11:18:247 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:11:18:366 ==>> 6A A6 02 A6 6A 


2025-07-31 18:11:18:456 ==>> Battery OFF
OVER 150


2025-07-31 18:11:18:512 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:11:18:520 ==>> 检测【进入休眠模式】
2025-07-31 18:11:18:532 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:11:18:670 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:18:57][COMM]Main Task receive event:28
[D][05:18:57][COMM]main task tmp_sleep_event = 8
[D][05:18:57][COMM]prepare to sleep
[D][05:18:57][CAT1]gsm read msg sub id: 12
[D][05:18:57][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:11:19:240 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 18:11:20:688 ==>> [D][05:18:59][M2M ]tcpclient close[4]
[D][05:18:59][SAL ]Cellular task submsg id[12]
[D][05:18:59][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c68], socket[0]


2025-07-31 18:11:21:265 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 18:11:21:745 ==>> [D][05:19:00][CAT1]SEND RAW data timeout
[D][05:19:00][CAT1]exec over: func id: 12, ret: -52
[D][05:19:00][CAT1]gsm read msg sub id: 9
[D][05:19:00][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]exec over: func id: 9, ret: 6
[D][05:19:00][CAT1]sub id: 9, ret: 6

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:00][SAL ]socket close ind. id[4]
[D][05:19:00][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:00][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:22:086 ==>> [E][05:19:00][COMM]1x1 rx timeout
[D][05:19:00][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:22:613 ==>> [E][05:19:01][COMM]1x1 rx timeout
[E][05:19:01][COMM]1x1 tp timeout
[E][05:19:01][COMM]1x1 error -3.
[W][05:19:01][COMM]CAN STOP!
[D][05:19:01][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:01][COMM]------------ready to Power off Acckey 1------------
[D][05:19:01][COMM]------------ready to Power off Acckey 2------------
[D][05:19:01][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:01][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1297
[D][05:19:01][COMM]bat sleep fail, reason:-1
[D][05:19:01][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:01][COMM]accel parse set 0
[D][05:19:01][COMM]imu rest ok. 72367
[D][05:19:01][COMM]imu sleep 0
[D][05:19:01][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:01][COMM]now sleep


2025-07-31 18:11:22:647 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:11:22:660 ==>> 检测【检测33V休眠电流】
2025-07-31 18:11:22:681 ==>> 开始33V电流采样
2025-07-31 18:11:22:696 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:22:748 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:11:23:756 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:11:23:835 ==>> Current33V:????:19.67

2025-07-31 18:11:24:257 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:24:264 ==>> 【检测33V休眠电流】通过,【19.67uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:11:24:279 ==>> 该项需要延时执行
2025-07-31 18:11:26:269 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:11:26:280 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:11:26:303 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:11:26:376 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1672mV
Get AD_V4 1mV
Get AD_V5 2752mV
Get AD_V6 1949mV
Get AD_V7 1097mV
OVER 150


2025-07-31 18:11:27:307 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:11:27:319 ==>> 检测【打开小电池2】
2025-07-31 18:11:27:347 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:11:27:374 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:11:27:600 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:11:27:607 ==>> 该项需要延时执行
2025-07-31 18:11:28:105 ==>> 此处延时了:【500】毫秒
2025-07-31 18:11:28:115 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:11:28:142 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:11:28:168 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:11:28:386 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:11:28:399 ==>> 该项需要延时执行
2025-07-31 18:11:28:866 ==>> [D][05:19:07][COMM]------------ready to Power on Acckey 1------------
[D][05:19:07][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:07][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:07][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:07][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:07][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:07][COMM]----- get Acckey 1 and value:1------------
[W][05:19:07][COMM]CAN START!
[D][05:19:07][CAT1]gsm read msg sub id: 12
[D][05:19:07][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:07][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 78559
[D][05:19:07][COMM][Audio]exec status ready.
[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]exec over: func id: 12, ret: 6
[D][0

2025-07-31 18:11:28:896 ==>> 此处延时了:【500】毫秒
2025-07-31 18:11:28:903 ==>> 检测【进入休眠模式2】
2025-07-31 18:11:28:915 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:11:28:935 ==>> 5:19:07][COMM]imu wakeup ok. 78574
[D][05:19:07][COMM]imu wakeup 1
[W][05:19:07][COMM]wake up system, wakeupEvt=0x80
[D][05:19:07][COMM]frm_can_weigth_power_set 1
[D][05:19:07][COMM]Clear Sleep Block Evt
[D][05:19:07][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:29:046 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:11:29:106 ==>> [E][05:19:07][COMM]1x1 rx timeout
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:29:211 ==>> [D][05:19:08][COMM]msg 02A0 loss. last_tick:78544. cur_tick:79053. period:50
[D][05:19:08][COMM]msg 02A4 loss. last_tick:78544. cur_tick:79053. period:50
[D][05:19:08][C

2025-07-31 18:11:29:271 ==>> OMM]msg 02A5 loss. last_tick:78545. cur_tick:79053. period:50
[D][05:19:08][COMM]msg 02A6 loss. last_tick:78545. cur_tick:79054. period:50
[D][05:19:08][COMM]msg 02A7 loss. last_tick:78545. cur_tick:79054. period:50
[D][05:19:08][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 79055
[D][05:19:08][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 79055


2025-07-31 18:11:29:611 ==>> [E][05:19:08][COMM]1x1 rx timeout
[E][05:19:08][COMM]1x1 tp timeout
[E][05:19:08][COMM]1x1 error -3.
[D][05:19:08][COMM]Main Task receive event:28 finished processing
[D][05:19:08][COMM]Main Task receive event:28
[D][05:19:08][COMM]prepare to sleep
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:29:901 ==>> [D][05:19:08][COMM]msg 0220 loss. last_tick:78544. cur_tick:79549. period:100
[D][05:19:08][COMM]msg 0221 loss. last_tick:78544. cur_tick:79550. period:100
[D][05:19:08][COMM]msg 0224 loss. last_tick:78544. cur_tick:79550. period:100
[D][05:19:08][COMM]msg 0260 loss. last_tick:78544. cur_tick:79551. period:100
[D][05:19:08][COMM]msg 0280 loss. last_tick:78544. cur_tick:79551. period:100
[D][05:19:08][COMM]msg 02C0 loss. last_tick:78544. cur_tick:79551. period:100
[D][05:19:08][COMM]msg 02C1 loss. last_tick:78544. cur_tick:79552. period:100
[D][05:19:08][COMM]msg 02C2 loss. last_tick:78544. cur_tick:79552. period:100
[D][05:19:08][COMM]msg 02E0 loss. last_tick:78544. cur_tick:79552. period:100
[D][05:19:08][COMM]msg 02E1 loss. last_tick:78544. cur_tick:79553. period:100
[D][05:19:08][COMM]msg 02E2 loss. last_tick:78544. cur_tick:79553. period:100
[D][05:19:08][COMM]msg 0300 loss. last_tick:78544. cur_tick:79553. period:100
[D][05:19:08][COMM]msg 0301 loss. last_tick:78544. cur_tick:79554. period:100
[D][05:19:08][COMM]bat msg 0240 loss. last_tick:78545. cur_tick:79554. period:100. j,i:1 54
[D][05:19:08][COMM]bat msg 0241 loss. last_tick:78545. cur_tick:79555. period:100. j,

2025-07-31 18:11:29:976 ==>> i:2 55
[D][05:19:08][COMM]bat msg 0242 loss. last_tick:78545. cur_tick:79555. period:100. j,i:3 56
[D][05:19:08][COMM]bat msg 0244 loss. last_tick:78545. cur_tick:79555. period:100. j,i:5 58
[D][05:19:08][COMM]bat msg 024E loss. last_tick:78545. cur_tick:79556. period:100. j,i:15 68
[D][05:19:08][COMM]bat msg 024F loss. last_tick:78545. cur_tick:79556. period:100. j,i:16 69
[D][05:19:08][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 79556
[D][05:19:08][COMM]CAN message bat fault change: 0x00000000->0x0001802E 79557
[D][05:19:08][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 79557


2025-07-31 18:11:30:006 ==>>                                                                               

2025-07-31 18:11:30:268 ==>> [D][05:19:09][COMM]msg 0222 loss. last_tick:78544. cur_tick:80052. period:150
[D][05:19:09][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 80053
[D][05:19:09][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:09][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:09][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:11:30:463 ==>> [E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[W][05:19:09][COMM]CAN STOP!
[D][05:19:09][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:09][COMM]------------ready to Power off Acckey 1------------
[D][05:19:09][COMM]------------ready to Power off Acckey 2------------
[D][05:19:09][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:09][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 98
[D][05:19:09][COMM]bat sleep fail, reason:-1
[D][05:19:09][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:09][COMM]accel parse set 0
[D][05:19:09][COMM]imu rest ok. 80242
[D][05:19:09][COMM]imu sleep 0
[W][05:19:09][COMM]now sleep


2025-07-31 18:11:30:767 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:11:30:774 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:11:30:785 ==>> 开始小电池电流采样
2025-07-31 18:11:30:809 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:30:879 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:11:31:882 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:11:31:929 ==>> CurrentBattery:ƽ��:3148.77

2025-07-31 18:11:32:384 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:32:491 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:11:33:501 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:11:33:548 ==>> CurrentBattery:ƽ��:69.47

2025-07-31 18:11:34:016 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:34:025 ==>> 【检测小电池休眠电流】通过,【69.47uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:11:34:049 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:11:34:065 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:11:34:170 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:11:34:326 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:11:34:336 ==>> 该项需要延时执行
2025-07-31 18:11:34:411 ==>> [D][05:19:13][COMM]------------ready to Power on Acckey 1------------
[D][05:19:13][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:13][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:13][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 1
[D][05:19:13][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:13][COMM]----- get Acckey 1 and value:1------------
[W][05:19:13][COMM]CAN START!
[D][05:19:13][CAT1]gsm read msg sub id: 12
[D][05:19:13][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:13][COMM]CAN message bat fault change: 0x0001802E->0x00000000 84124
[D][05:19:13][COMM][Audio]exec status ready.
[D][05:19:13][CAT1]<<< 
OK

[D][05:19:13][CAT1]exec over: func id: 12, ret: 6
[D][05:19:13][COMM]imu wakeup ok. 84138
[D][05:19:13][COMM]imu wakeup 1
[W][05:19:13][COMM]wake up system, wakeupEvt=0x80
[D][05:19:13][COMM]frm_can_weigth_power_set 1
[D][05:19:13][COMM]Clear Sleep Block Evt
[D][05:19:13][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:13][COMM]1x1 frm_can_tp_send ok
[D][05:19:13][COMM]read battery soc:0


2025-07-31 18:11:34:671 ==>> [E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:34:776 ==>> [D][05:19:13][COMM]msg 02A0 loss. last_tick:84106. cur_tick:84618. period:50
[D][05:19:13][COMM]msg 02A4 loss. last_tick:84106. cur_tick:84619. period:50
[D][05:19:13][COMM]msg 02A5 loss. last_tick:84107. cur_tick:84619. pe

2025-07-31 18:11:34:821 ==>> riod:50
[D][05:19:13][COMM]msg 02A6 loss. last_tick:84107. cur_tick:84620. period:50
[D][05:19:13][COMM]msg 02A7 loss. last_tick:84107. cur_tick:84620. period:50
[D][05:19:13][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 84620
[D][05:19:13][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 84621


2025-07-31 18:11:34:836 ==>> 此处延时了:【500】毫秒
2025-07-31 18:11:34:860 ==>> 检测【检测唤醒】
2025-07-31 18:11:34:885 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:11:35:677 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:13][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[D][05:19:13][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:13][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:13][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:13][FCTY]DeviceID    = 460130020290639
[D][05:19:13][FCTY]HardwareID  = 867222087871747
[D][05:19:13][FCTY]MoBikeID    = 9999999999
[D][05:19:13][FCTY]LockID      = FFFFFFFFFF
[D][05:19:13][FCTY]BLEFWVersion= 105
[D][05:19:13][FCTY]BLEMacAddr   = D7F2CB10B528
[D][05:19:13][FCTY]Bat         = 3804 mv
[D][05:19:13][FCTY]Current     = 0 ma
[D][05:19:13][FCTY]VBUS        = 2600 mv
[D][05:19:13][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:13][FCTY]Ext battery vol = 32, adc = 1296
[D][05:19:13][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:19:13][FCTY]Bike Type flag is invalied
[D][05:19:13][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:13][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D

2025-07-31 18:11:35:782 ==>> ][05:19:13][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:13][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:13][FCTY]Bat1         = 3775 mv
[D][05:19:13][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[D][05:19:13][COMM]Main Task receive event:28 finished processing
[D][05:19:13][COMM]Main Task receive event:65
[D][05:19:13][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:13][COMM]Main Task receive event:65 finished processing
[D][05:19:13][COMM]Main Task receive event:60
[D][05:19:13][COMM]smart_helmet_vol=255,255
[D][05:19:13][COMM]report elecbike
[D][05:19:13][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:13][PROT]remove success[1629955153],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:13][COMM]Main Task receive event:60 finished processing
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo

2025-07-31 18:11:35:887 ==>> :[1]
[D][05:19:13][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:13][PROT]index:0
[D][05:19:13][PROT]is_send:1
[D][05:19:13][PROT]sequence_num:9
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:3
[D][05:19:13][PROT]send_path:0x3
[D][05:19:13][PROT]msg_type:0x5d03
[D][05:19:13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][PROT]Sending traceid[999999999990000A]
[D][05:19:13][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:13][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:13][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:13][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:13][SAL ]open socket ind id[4], rst[0]
[D][05:19:13][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:13][SAL ]Cellular task submsg id[8]
[D][05:19:13][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[

2025-07-31 18:11:35:933 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:11:35:942 ==>> 检测【关机】
2025-07-31 18:11:35:956 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:11:35:992 ==>> D][05:19:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:13][CAT1]gsm read msg sub id: 8
[D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:13][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:14][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:14][COMM]exit wheel stolen mode.
[D][05:19:14][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:14][CAT1]<<< 
+CME ERROR: 100

[D][05:19:14][COMM]Main Task receive event:68
[D][05:19:14][COMM]handlerWheelStolen evt type = 2.
[D][05:19:14][COMM]frm CAN read mc work mode invalid,val:254
[E][05:19:14][COMM][MC]exit stolen,get work mode err,rt:-4
[W][05:19:14][GNSS]stop locating
[D][05:19:14][GNSS]all continue location stop
[D][05:19:14][COMM]1x1 tx_id:3,1, tx_len:2
[D][05:19:14][COMM]1x1 frm_can_tp_send ok
[D][05:19:14][GNSS]handler GSMGet Base timeout
[D][05:19:14][COMM]msg 0220 loss. last_tick:84106. cur_tick:85123. period:100
[D][05:19:14][COMM]msg 0221 loss. last_tick:84106. cur_tick:85123. period:100
[D][05:19:14][COMM]msg 0224 loss. last_tick:84106. cur_tick:85123. period:100
[D][05:19:14][COMM]msg 0260 loss. last_tick:84106. cur_tick:85124. period:100

2025-07-31 18:11:36:097 ==>> 
[D][05:19:14][COMM]msg 0280 loss. last_tick:84106. cur_tick:85124. period:100
[D][05:19:14][COMM]msg 02C0 loss. last_tick:84106. cur_tick:85125. period:100
[D][05:19:14][COMM]msg 02C1 loss. last_tick:84106. cur_tick:85125. period:100
[D][05:19:14][COMM]msg 02C2 loss. last_tick:84106. cur_tick:85125. period:100
[D][05:19:14][COMM]msg 02E0 loss. last_tick:84106. cur_tick:85126. period:100
[D][05:19:14][COMM]msg 02E1 loss. last_tick:84106. cur_tick:85126. period:100
[D][05:19:14][COMM]msg 02E2 loss. last_tick:84106. cur_tick:85126. period:100
[D][05:19:14][COMM]msg 0300 loss. last_tick:84107. cur_tick:85127. period:100
[D][05:19:14][COMM]msg 0301 loss. last_tick:84107. cur_tick:85127. period:100
[D][05:19:14][COMM]bat msg 0240 loss. last_tick:84107. cur_tick:85127. period:100. j,i:1 54
[D][05:19:14][COMM]bat msg 0241 loss. last_tick:84107. cur_tick:85128. period:100. j,i:2 55
[D][05:19:14][COMM]bat msg 0242 loss. last_tick:84107. cur_tick:85128. period:100. j,i:3 56
[D][05:19:14][COMM]bat msg 0244 loss. last_tick:84107. cur_tick:85129. period:100. j,i:5 58
[D][05:19:14][COMM]bat msg 024E loss. last_tick:84107. cur_tick:85129. period:100. j,i:15 68
[D][05:19:14][COMM]bat msg 02

2025-07-31 18:11:36:172 ==>> 4F loss. last_tick:84107. cur_tick:85130. period:100. j,i:16 69
[D][05:19:14][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 85130
[D][05:19:14][COMM]CAN message bat fault change: 0x00000000->0x0001802E 85130
[D][05:19:14][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 85131
[D][05:19:14][COMM]IMU: [70,-36,-896] ret=30 AWAKE!
[D][05:19:14][COMM]S->M yaw:INVALID
[D][05:19:14][COMM]IMU: [12,2,-970] ret=34 AWAKE!
[E][05:19:14][COMM]1x1 rx timeout
[D][05:19:14][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:11:36:277 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 18:11:36:382 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 18:11:36:487 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 18:11:36:593 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 18:11:36:698 ==>>                                                                                                                                                            t: 0
[D][05:19:15][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:15][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:15][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 18:11:36:962 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:11:37:224 ==>> [E][05:19:15][COMM]1x1 rx timeout
[E][05:19:15][COMM]1x1 tp timeout
[E][05:19:15][COMM]1x1 error -3.
[D][05:19:15][COMM]Main Task receive event:68 finished processing
[D][05:19:15][COMM]Main Task receive event:65
[D][05:19:15][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:15][COMM]Main Task receive event:65 finished processing
[D][05:19:15][COMM]Main Task receive event:66
[D][05:19:15][COMM]Try to Auto Lock Bat
[D][05:19:15][COMM]Main Task receive event:66 finished processing
[D][05:19:15][COMM]Main Task receive event:60
[D][05:19:15][COMM]smart_helmet_vol=255,255
[D][05:19:15][COMM]BAT CAN get state1 Fail 204
[D][05:19:15][COMM]BAT CAN get soc Fail, 204
[D][05:19:15][COMM]BAT CAN get state2 fail 204
[D][05:19:15][COMM]get soh error
[E][05:19:15][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:15][COMM]report elecbike
[D][05:19:15][COMM]Receive Bat Lock cmd 0
[D][05:19:15][COMM]VBUS is 1
[W][05:19:15][PROT]remove success[1629955155],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:15][PROT]add success [1629955155],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:15][COMM]Main Task receive event:60 finished processing
[D][05:19:15][C

2025-07-31 18:11:37:329 ==>> OMM]Main Task receive event:61
[D][05:19:15][COMM][D301]:type:3, trace id:280
[D][05:19:15][COMM]id[], hw[000
[D][05:19:15][COMM]get mcMaincircuitVolt error
[D][05:19:15][COMM]get mcSubcircuitVolt error
[D][05:19:15][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:15][COMM]BAT CAN get state1 Fail 204
[D][05:19:15][COMM]BAT CAN get soc Fail, 204
[D][05:19:15][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:15][COMM]BAT CAN get state2 fail 204
[D][05:19:15][COMM]get bat work mode err
[W][05:19:15][PROT]remove success[1629955155],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:15][PROT]add success [1629955155],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:15][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:15][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:15][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:15][PROT]index:1
[D][05:19:15][PROT]is_send:1
[D][05:19:15][PROT]sequence_num:10
[D][05:19:15][PROT]retry_timeout:0
[D][05:19:15][PROT]retry_times:3
[D][05:19:15][PROT]send_path:0x3

2025-07-31 18:11:37:434 ==>> 
[D][05:19:15][PROT]msg_type:0x5d03
[D][05:19:15][PROT]===========================================================
[W][05:19:15][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955155]
[D][05:19:15][PROT]===========================================================
[D][05:19:15][PROT]Sending traceid[999999999990000B]
[D][05:19:15][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:15][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:15][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:15][COMM]Main Task receive event:61 finished processing
[D][05:19:15][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:15][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:15][COMM]Power Off
                                                                                                                                                                                                              

2025-07-31 18:11:37:464 ==>>                                                                                                                                                                             

2025-07-31 18:11:37:529 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:11:37:537 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:11:37:559 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:11:37:662 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:11:37:737 ==>> [D][0

2025-07-31 18:11:37:826 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:11:37:835 ==>> 检测【检测小电池关机电流】
2025-07-31 18:11:37:843 ==>> 开始小电池电流采样
2025-07-31 18:11:37:855 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:37:933 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:11:38:935 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:11:38:998 ==>> CurrentBattery:ƽ��:67.26

2025-07-31 18:11:39:438 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:11:39:446 ==>> 【检测小电池关机电流】通过,【67.26uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:11:39:709 ==>> MES过站成功
2025-07-31 18:11:39:722 ==>> #################### 【测试结束】 ####################
2025-07-31 18:11:39:762 ==>> 关闭5V供电
2025-07-31 18:11:39:776 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:11:39:861 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:11:40:772 ==>> 关闭5V供电成功
2025-07-31 18:11:40:785 ==>> 关闭33V供电
2025-07-31 18:11:40:810 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:11:40:866 ==>> 5A A5 02 5A A5 


2025-07-31 18:11:40:956 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:11:41:776 ==>> 关闭33V供电成功
2025-07-31 18:11:41:788 ==>> 关闭3.7V供电
2025-07-31 18:11:41:811 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:11:41:854 ==>> 6A A6 02 A6 6A 


2025-07-31 18:11:41:959 ==>> Battery OFF
OVER 150


