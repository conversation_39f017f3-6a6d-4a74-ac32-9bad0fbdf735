2025-07-31 21:44:01:206 ==>> MES查站成功:
查站序号:P5100010053128B1验证通过
2025-07-31 21:44:01:219 ==>> 扫码结果:P5100010053128B1
2025-07-31 21:44:01:220 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:44:01:222 ==>> 测试参数版本:2024.10.11
2025-07-31 21:44:01:223 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:44:01:224 ==>> 检测【打开透传】
2025-07-31 21:44:01:226 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:44:01:277 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:44:01:502 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:44:01:560 ==>> 检测【检测接地电压】
2025-07-31 21:44:01:561 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:44:01:690 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:44:01:849 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:44:01:851 ==>> 检测【打开小电池】
2025-07-31 21:44:01:853 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:44:01:981 ==>> 6A A6 01 A6 6A 


2025-07-31 21:44:02:086 ==>> Battery ON
OVER 150


2025-07-31 21:44:02:132 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:44:02:135 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:44:02:137 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:44:02:146 ==>>  

2025-07-31 21:44:02:191 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:44:02:420 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:44:02:423 ==>> 检测【等待设备启动】
2025-07-31 21:44:02:425 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:03:448 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:04:502 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:05:550 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:06:596 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:07:650 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:08:706 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:09:749 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:10:800 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:11:821 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:44:12:868 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:44:12:871 ==>> #################### 【测试结束】 ####################
2025-07-31 21:44:12:892 ==>> 关闭5V供电
2025-07-31 21:44:12:895 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:44:12:979 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:44:13:893 ==>> 关闭5V供电成功
2025-07-31 21:44:13:896 ==>> 关闭33V供电
2025-07-31 21:44:13:899 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:44:13:984 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:44:14:902 ==>> 关闭33V供电成功
2025-07-31 21:44:14:905 ==>> 关闭3.7V供电
2025-07-31 21:44:14:907 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:44:14:979 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:44:15:131 ==>>  

