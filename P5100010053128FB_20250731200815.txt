2025-07-31 20:08:15:138 ==>> MES查站成功:
查站序号:P5100010053128FB验证通过
2025-07-31 20:08:15:157 ==>> 扫码结果:P5100010053128FB
2025-07-31 20:08:15:159 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:08:15:180 ==>> 测试参数版本:2024.10.11
2025-07-31 20:08:15:181 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:08:15:183 ==>> 检测【打开透传】
2025-07-31 20:08:15:184 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:08:15:239 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:08:15:428 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:08:15:433 ==>> 检测【检测接地电压】
2025-07-31 20:08:15:435 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:08:15:541 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:08:15:710 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:08:15:713 ==>> 检测【打开小电池】
2025-07-31 20:08:15:716 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:08:15:842 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:08:15:982 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:08:15:984 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:08:15:987 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:08:16:038 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:08:16:255 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:08:16:257 ==>> 检测【等待设备启动】
2025-07-31 20:08:16:260 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:16:782 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:16:963 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 20:08:17:296 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:17:558 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:17:724 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 20:08:18:316 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:18:319 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:18:499 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 20:08:19:102 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:19:284 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 20:08:19:359 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:19:871 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:20:068 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 20:08:20:405 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:20:653 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:20:848 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 20:08:21:436 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:21:438 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:21:618 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 20:08:22:222 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:22:404 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]

2025-07-31 20:08:22:479 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:23:519 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:24:555 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:25:572 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:26:604 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:08:26:606 ==>> #################### 【测试结束】 ####################
2025-07-31 20:08:26:640 ==>> 关闭5V供电
2025-07-31 20:08:26:642 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:08:26:742 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:08:27:653 ==>> 关闭5V供电成功
2025-07-31 20:08:27:655 ==>> 关闭33V供电
2025-07-31 20:08:27:658 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:08:27:746 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:08:28:656 ==>> 关闭33V供电成功
2025-07-31 20:08:28:659 ==>> 关闭3.7V供电
2025-07-31 20:08:28:661 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:08:28:732 ==>> 6A A6 02 A6 6A 


2025-07-31 20:08:28:837 ==>> Battery OFF
OVER 150


2025-07-31 20:08:28:942 ==>>  

