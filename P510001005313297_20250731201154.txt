2025-07-31 20:11:54:619 ==>> MES查站成功:
查站序号:P510001005313297验证通过
2025-07-31 20:11:54:628 ==>> 扫码结果:P510001005313297
2025-07-31 20:11:54:629 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:11:54:631 ==>> 测试参数版本:2024.10.11
2025-07-31 20:11:54:632 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:11:54:633 ==>> 检测【打开透传】
2025-07-31 20:11:54:635 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:11:54:738 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:11:55:002 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:11:55:008 ==>> 检测【检测接地电压】
2025-07-31 20:11:55:012 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:11:55:145 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:11:55:285 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:11:55:287 ==>> 检测【打开小电池】
2025-07-31 20:11:55:289 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:11:55:340 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:11:55:556 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:11:55:557 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:11:55:560 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:11:55:645 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:11:55:830 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:11:55:832 ==>> 检测【等待设备启动】
2025-07-31 20:11:55:836 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:56:177 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:56:359 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 20:11:56:864 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:56:954 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:57:152 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:11:57:811 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:11:57:856 ==>>                                                    

2025-07-31 20:11:57:901 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:11:58:262 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:11:58:732 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:11:58:946 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:11:58:948 ==>> 检测【产品通信】
2025-07-31 20:11:58:951 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:11:59:436 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:11:59:634 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:11:59:981 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:12:00:303 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 20:12:00:682 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:12:01:014 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:12:01:164 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:12:01:254 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:01:287 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:12:01:289 ==>> 检测【初始化完成检测】
2025-07-31 20:12:01:292 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:12:01:465 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 20:12:01:561 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:12:01:563 ==>> 检测【关闭大灯控制1】
2025-07-31 20:12:01:564 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:12:01:707 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:12:01:812 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:12:01:830 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:12:01:832 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:12:01:835 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:12:02:043 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:12:02:107 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:12:02:109 ==>> 检测【关闭仪表供电】
2025-07-31 20:12:02:111 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:12:02:242 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:02:347 ==>>                OMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCT

2025-07-31 20:12:02:422 ==>> Y]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:12:02:637 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:12:02:642 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:12:02:667 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:12:02:803 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:12:02:918 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:12:02:921 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:12:02:922 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:12:03:122 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:12:03:207 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:12:03:210 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:12:03:212 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:03:273 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:03:439 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:12:03:487 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:12:03:490 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:12:03:493 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:12:03:544 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:12:03:649 ==>> [D][05:17:52][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:12:03:758 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:12:03:761 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:12:03:763 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:12:03:844 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:12:04:035 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:12:04:038 ==>> 该项需要延时执行
2025-07-31 20:12:04:276 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:04:797 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5009. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 

2025-07-31 20:12:04:827 ==>> 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 20:12:05:205 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:12:05:726 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:1

2025-07-31 20:12:05:831 ==>> 7:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:5

2025-07-31 20:12:05:936 ==>> 4][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204

2025-07-31 20:12:05:996 ==>> 
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 20:12:06:313 ==>> [D][05:17:55][COMM]6680 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:06:418 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:12:07:305 ==>> [D][05:17:56][COMM]7691 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:07:639 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:12:08:051 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:12:08:054 ==>> 检测【33V输入电压ADC】
2025-07-31 20:12:08:057 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:08:401 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:17:57][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read left brake adc:6  volt:7 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:8  volt:10 mv
[D][05:17:57][COMM]adc read battery ts volt:21 mv
[D][05:17:57][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:17:57][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:13  volt:10 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2478  volt:3992 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv
[D][05:17:57][COMM]8703 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:08:587 ==>> 【33V输入电压ADC】通过,【32450mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:12:08:590 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:12:08:593 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:12:08:656 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1653mV
Get AD_V4 0mV
Get AD_V5 2766mV
Get AD_V6 1988mV
Get AD_V7 1097mV
OVER 150


2025-07-31 20:12:08:870 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:08:874 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:12:08:889 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:08:891 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:12:08:893 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:12:08:908 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:12:08:910 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:12:08:926 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:12:08:949 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:12:08:951 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:12:08:953 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:12:09:048 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1652mV
Get AD_V4 0mV
Get AD_V5 2767mV
Get AD_V6 2021mV
Get AD_V7 1096mV
OVER 150


2025-07-31 20:12:09:224 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:09:226 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:12:09:244 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:09:264 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:12:09:266 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 20:12:09:268 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:12:09:270 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:12:09:285 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:12:09:287 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:12:09:307 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:12:09:312 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:12:09:341 ==>> [D][05:17:58][COMM]9714 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:09:445 ==>> 1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1653mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1988mV
Get AD_V7 1096mV
OVER 150


2025-07-31 20:12:09:590 ==>> 【TP7_VCC3V3(ADV2)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:09:592 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:12:09:608 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:09:610 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:12:09:612 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:12:09:626 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:12:09:628 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:12:09:644 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:12:09:647 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:12:09:669 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:12:09:672 ==>> 检测【打开WIFI(1)】
2025-07-31 20:12:09:676 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:12:09:704 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10003
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10003
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:12:09:809 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<

2025-07-31 20:12:09:840 ==>> 


2025-07-31 20:12:09:947 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:12:09:950 ==>> 检测【清空消息队列(1)】
2025-07-31 20:12:09:952 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:12:10:127 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:12:10:221 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:12:10:224 ==>> 检测【打开GPS(1)】
2025-07-31 20:12:10:228 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:12:10:565 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10726 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[W][05:17:59][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:17:59][COMM]Open GPS Module...
[D][05:17:59][COMM]LOC_MODEL_CONT
[D][05:17:59][GNSS]start event:8
[W][05:17:59][GNSS]start cont 

2025-07-31 20:12:10:626 ==>> locating
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:12:10:750 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:12:10:755 ==>> 检测【打开GSM联网】
2025-07-31 20:12:10:780 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:12:11:003 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087745289

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539159

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:12:11:278 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:12:11:281 ==>> 检测【打开仪表供电1】
2025-07-31 20:12:11:284 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:12:11:337 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:12:11:441 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:12:11:551 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:12:11:555 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:12:11:559 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:12:11:746 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:12:11:824 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:12:11:827 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:12:11:829 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:12:12:034 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33479]


2025-07-31 20:12:12:098 ==>> 【读取主控ADC采集的仪表电压】通过,【33479mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:12:12:111 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:12:12:113 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:12:337 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:12:12:371 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:12:12:374 ==>> 检测【AD_V20电压】
2025-07-31 20:12:12:377 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:12:472 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:12:578 ==>> [D][05:18:01][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:12:12:669 ==>> 本次取值间隔时间:189ms
2025-07-31 20:12:12:687 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:12:794 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:12:840 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:12:13:097 ==>> 本次取值间隔时间:289ms
2025-07-31 20:12:13:120 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:13:223 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:13:360 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13737 imu init OK
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:12:13:375 ==>> 本次取值间隔时间:152ms
2025-07-31 20:12:13:392 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:13:496 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:13:741 ==>> [D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:12:13:937 ==>> 本次取值间隔时间:432ms
2025-07-31 20:12:14:105 ==>> 本次取值间隔时间:157ms
2025-07-31 20:12:14:424 ==>> 本次取值间隔时间:311ms
2025-07-31 20:12:14:581 ==>> 本次取值间隔时间:155ms
2025-07-31 20:12:14:585 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:14:689 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:14:704 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:12:14:749 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:12:15:055 ==>> 本次取值间隔时间:354ms
2025-07-31 20:12:15:074 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:12:15:179 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:12:15:240 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1640mV
OVER 150


2025-07-31 20:12:15:345 ==>> 本次取值间隔时间:158ms
2025-07-31 20:12:15:363 ==>> 【AD_V20电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:15:366 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:12:15:369 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:12:15:437 ==>> 3A A3 02 00 A3 


2025-07-31 20:12:15:542 ==>> OFF_OUT2
OVER 150


2025-07-31 20:12:15:642 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:12:15:647 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:12:15:650 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:12:15:677 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:12:15:858 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:12:15:921 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:12:15:925 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:12:15:930 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:12:16:038 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:12:16:193 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:12:16:196 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:12:16:199 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:12:16:459 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:12:16:723 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:12:16:728 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:12:16:733 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:12:16:937 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:12:17:001 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:12:17:013 ==>> 检测【AD_V21电压】
2025-07-31 20:12:17:015 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:12:17:149 ==>> 1A A1 20 00 00 
Get AD_V21 1076mV
OVER 150


2025-07-31 20:12:17:165 ==>> 本次取值间隔时间:161ms
2025-07-31 20:12:17:182 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:12:17:194 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:06][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:06][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:12:17:239 ==>> 1A A1 20 00 00 
Get AD_V21 1638mV
OVER 150


2025-07-31 20:12:17:602 ==>> 本次取值间隔时间:413ms
2025-07-31 20:12:17:621 ==>> 【AD_V21电压】通过,【1638mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:17:625 ==>> 检测【关闭仪表供电2】
2025-07-31 20:12:17:628 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:12:17:677 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:12:18:070 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 5, ret: 6
[D][05:18:07][CAT1]sub id: 5, ret: 6

[D][05:18:07][SAL ]Cellular task submsg id[68]
[D][05:18:07][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:07][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:07][M2M ]M2M_GSM_INIT OK
[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:07][SAL ]open socket ind id[4], rst[0]
[D][05:18:07][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:07][SAL ]Cellular task submsg id[8]
[D][05:18:07][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:07][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:07][CAT1]gsm read msg sub id: 8
[D][05:18:07][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:07][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:07][CAT1]tx ret[8]

2025-07-31 20:12:18:100 ==>>  >>> AT+CSQ

[D][05:18:07][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:07][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:07][COMM]Main Task receive event:4


2025-07-31 20:12:18:148 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:12:18:152 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:12:18:155 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:12:18:190 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:12:18:625 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!
[D][05:18:07][GNSS]recv submsg id[1]
[D][05:18:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:07][GNSS]location recv gms init done evt
[D][05:18:07][GNSS]GPS start. ret=0
[D][05:18:07][CAT1]gsm read msg sub id: 23
[D][05:18:07][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:07][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:07][CAT1]opened : 0, 0
[D][05:18:07][SAL ]Cellular task submsg id[68]
[D][05:18:07][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:07][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:07][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:07][M2M ]g_m2m_is_idle become true
[D][05:18:07][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:12:18:674 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:12:18:677 ==>> 检测【打开AccKey2供电】
2025-07-31 20:12:18:679 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:12:18:807 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:12:18:946 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:12:18:951 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:12:18:954 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:19:248 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:08][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:18:08][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:08][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:08][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:08][COMM]adc read battery ts volt:11 mv
[D][05:18:08][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:08][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2475  volt:3988 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:12:19:308 ==>>                                                                                                                                    2097152,2097152,2097152*7A



2025-07-31 20:12:19:476 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33361mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:12:19:483 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:12:19:487 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:12:19:626 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:12:19:717 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:12:19:757 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:12:19:761 ==>> 该项需要延时执行
2025-07-31 20:12:19:960 ==>> [D][05:18:09][CAT1]<<< 
OK

[D][05:18:09][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:12:20:187 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,42,59,,,41,25,,,40,42,,,40,1*7C

$GBGSV,2,2,07,39,,,34,40,,,34,24,,,33,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1563.604,1563.604,50.053,2097152,2097152,2097152*4C

[D][05:18:09][CAT1]<<< 
OK

[D][05:18:09][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:09][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:09][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:09][CAT1]<<< 
OK

[D][05:18:09][CAT1]exec over: func id: 23, ret: 6
[D][05:18:09][CAT1]sub id: 23, ret: 6



2025-07-31 20:12:20:352 ==>> [D][05:18:09][GNSS]recv submsg id[1]
[D][05:18:09][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:12:21:109 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,3,,,43,33,,,42,60,,,42,59,,,41,1*4A

$GBGSV,4,2,13,25,,,41,14,,,41,42,,,40,16,,,37,1*71

$GBGSV,4,3,13,39,,,36,40,,,36,24,,,36,13,,,35,1*7A

$GBGSV,4,4,13,1,,,35,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1610.493,1610.493,51.516,2097152,2097152,2097152*49



2025-07-31 20:12:21:721 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:12:22:137 ==>> $GBGGA,121225.952,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,3,,,41,60,,,41,59,,,41,1*4E

$GBGSV,5,2,17,25,,,41,14,,,41,42,,,40,39,,,38,1*76

$GBGSV,5,3,17,24,,,38,16,,,37,41,,,37,2,,,37,1*4F

$GBGSV,5,4,17,38,,,37,40,,,36,13,,,36,1,,,36,1*4C

$GBGSV,5,5,17,5,,,32,1*44

$GBRMC,121225.952,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121225.952,0.000,1587.593,1587.593,50.771,2097152,2097152,2097152*5C



2025-07-31 20:12:22:734 ==>> $GBGGA,121226.552,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,3,,,41,60,,,41,14,,,41,1*47

$GBGSV,5,2,17,59,,,40,25,,,40,42,,,40,24,,,39,1*72

$GBGSV,5,3,17,39,,,38,16,,,37,41,,,37,38,,,37,1*7A

$GBGSV,5,4,17,40,,,37,13,,,37,1,,,37,2,,,36,1*75

$GBGSV,5,5,17,5,,,33,1*45

$GBRMC,121226.552,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121226.552,0.000,1592.453,1592.453,50.910,2097152,2097152,2097152*5A



2025-07-31 20:12:22:764 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:12:22:781 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:12:22:786 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:23:053 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:12][COMM]adc read battery ts volt:11 mv
[D][05:18:12][COMM]adc read in 24v adc:1280  volt:32375 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2474  volt:3986 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:12:23:300 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:12:23:304 ==>> 检测【打开AccKey1供电】
2025-07-31 20:12:23:307 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:12:23:530 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:12:23:577 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:12:23:581 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:12:23:586 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:12:23:635 ==>> 1A A1 00 40 00 
Get AD_V14 2592mV
OVER 150


2025-07-31 20:12:23:740 ==>> $GBGGA,121227.532,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,42,3,,,41,60,,,41,14,,,41,1*41

$GBGSV,6,2,21,59,,,41,25,,,41,42,,,40,24,,,40,1*7A

$GBGSV,6,3,21,39,,,39,13,,,38,1,,,38,16,,,37,1*40

$GBGSV,6,4,21,41,,,37,38,,,37,40,,,37,6,,,37,1*4B

$GBGSV,6,5,21,2,,,36,5,,,33,34,,,30,9,,,39,1*43

$GBGSV,6,6,21,8,,,37,1*49

$GBRMC,121227.532,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121227.532,0.000,1584.145,1584.145,50.678,20971

2025-07-31 20:12:23:770 ==>> 52,2097152,2097152*5C

[D][05:18:13][COMM]read battery soc:255


2025-07-31 20:12:23:830 ==>> 原始值:【2592】, 乘以分压基数【2】还原值:【5184】
2025-07-31 20:12:23:849 ==>> 【读取AccKey1电压(ADV14)前】通过,【5184mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:12:23:853 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:12:23:856 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:24:150 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:13][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:13][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:13][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:13][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:13][COMM]adc read battery ts volt:14 mv
[D][05:18:13][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:13][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:12:24:382 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5503mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:12:24:399 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:12:24:403 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:12:24:536 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:12:24:659 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:12:24:664 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:12:24:668 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:12:24:746 ==>> $GBGGA,121228.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,41,14,,,41,59,,,41,1*7C

$GBGSV,6,2,23,25,,,41,24,,,41,3,,,40,42,,,40,1*47

$GBGSV,6,3,23,39,,,39,13,,,38,1,,,38,16,,,37,1*42

$GBGSV,6,4,23,41,,,37,38,,,37,40,,,37,9,,,36,1*47

$GBGSV,6,5,23,6,,,36,2,,,36,7,,,35,8,,,34,1*7E

$GBGSV,6,6,23,5,,,33,4,,,33,34,,,31,1*73

$GBRMC,121228.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121228.512,0.000,1557.395,1557.395,49.826,2097152,2097152,2097152*5C

1A A1 00 40 00 
Get AD_V14 2591mV
OVER 150


2025-07-31 20:12:24:913 ==>> 原始值:【2591】, 乘以分压基数【2】还原值:【5182】
2025-07-31 20:12:24:932 ==>> 【读取AccKey1电压(ADV14)后】通过,【5182mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:12:24:935 ==>> 检测【打开WIFI(2)】
2025-07-31 20:12:24:939 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:12:25:172 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:12:25:205 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:12:25:209 ==>> 检测【转刹把供电】
2025-07-31 20:12:25:213 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:12:25:432 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:12:25:480 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:12:25:484 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:12:25:488 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:12:25:584 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:12:25:645 ==>> 1A A1 00 80 00 
Get AD_V15 2386mV
OVER 150


2025-07-31 20:12:25:735 ==>> 原始值:【2386】, 乘以分压基数【2】还原值:【4772】
2025-07-31 20:12:25:750 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,121229.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,60,,,41,14,,,41,59,,,41,1*7C

$GBGSV,6,2,23,24,,,41,25,,,40,3,,,40,42,,,40,1*46

$GBGSV,6,3,23,39,,,39,13,,,38,1,,,38,16,,,37,1*42

$GBGSV,6,4,23,41,,,37,38,,,37,40,,,37,9,,,36,1*47

$GBGSV,6,5,23,6,,,36,2,,,36,8,,,35,7,,,34,1*7E

$GBGSV,6,6,23,5,,,33,4,,,33,34,,,31,1*73

$GBRMC,121229.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121229.512,0.000,1555.590,1555.590,49.767,2097152,2097152,2097152*57

                                         

2025-07-31 20:12:25:754 ==>> 【读取AD_V15电压(前)】通过,【4772mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:12:25:757 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:12:25:760 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:12:25:855 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:12:25:947 ==>> +WIFISCAN:4,0,CC057790A741,-63
+WIFISCAN:4,1,CC057790A6E1,-74
+WIFISCAN:4,2,CC057790A7C1,-78
+WIFISCAN:4,3,F86FB0660A82,-81

[D][05:18:15][CAT1]wifi scan report total[4]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 20:12:26:007 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 20:12:26:030 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:12:26:033 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:12:26:038 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:26:358 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:15][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:15][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:15][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:15][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:15][COMM]adc read battery ts volt:11 mv
[D][05:18:15][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3069  volt:5394 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                      

2025-07-31 20:12:26:559 ==>> 【转刹把供电电压(主控ADC)】通过,【5394mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:12:26:563 ==>> 检测【转刹把供电电压】
2025-07-31 20:12:26:568 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:26:862 ==>> $GBGGA,121230.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,14,,,41,59,,,41,24,,,41,1*7B

$GBGSV,6,2,24,25,,,41,3,,,41,60,,,40,42,,,40,1*40

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,16,,,37,1*45

$GBGSV,6,4,24,41,,,37,38,,,37,40,,,37,9,,,36,1*40

$GBGSV,6,5,24,6,,,36,2,,,36,8,,,35,26,,,35,1*4B

$GBGSV,6,6,24,7,,,34,5,,,33,4,,,33,34,,,31,1*44

$GBRMC,121230.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121230.512,0.000,1552.963,1552.963,49.684,2097152,2097152,2097152*53

[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:16][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:16][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:16][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:16][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:16][COMM]adc read battery ts volt:14 mv
[D][05:18:16][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3072  volt:5400 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2473  volt:3984 mv
[D][05:18:16][COM

2025-07-31 20:12:26:907 ==>> M]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:12:27:093 ==>> 【转刹把供电电压】通过,【5400mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:12:27:097 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:12:27:101 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:27:322 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:12:27:370 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:12:27:375 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:12:27:378 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:27:475 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:12:27:537 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:12:27:600 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:12:27:604 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:12:27:607 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:27:703 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:12:27:794 ==>> $GBGGA,121231.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,14,,,41,59,,,41,24,,,41,1*7B

$GBGSV,6,2,24,25,,,41,3,,,41,60,,,41,42,,,41,1*40

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,16,,,38,1*4A

$GBGSV,6,4,24,41,,,37,38,,,37,40,,,37,9,,,36,1*40

$GBGSV,6,5,24,6,,,36,2,,,36,26,,,36,8,,,35,1*48

$GBGSV,6,6,24,7,,,35,5,,,34,4,,,33,34,,,31,1*42

$GBRMC,121231.512,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121231.512,0.000,780.202,780.202,713.511,2097152,2097152,2097152*65

[D][05:18:17][COMM]read battery soc:255


2025-07-31 20:12:27:809 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:27:869 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:12:27:914 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:12:28:021 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:28:098 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:12:28:128 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:12:28:203 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:12:28:233 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:12:28:248 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:12:28:338 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:12:28:398 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:12:28:443 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:12:28:499 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:12:28:502 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:12:28:508 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:12:28:640 ==>> 3A A3 03 01 A3 


2025-07-31 20:12:28:745 ==>> $GBGGA,121232.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,60,,,41,3,,,41,59,,,41,1*4D

$GBGSV,6,2,24,24,,,41,14,,,41,25,,,41,42,,,40,1*77

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,6,4,24,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,6,5,24,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,6,6,24,7,,,35,5,,,34,4,,,33,34,,,31,1*42

$GBRMC,121232.512,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121232.512,0.000,778.476,778.476,711.933,2097152,2097152,2097152*68

ON_OUT3
OVER 150


2025-07-31 20:12:28:786 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:12:28:790 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:12:28:795 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:12:28:850 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:12:29:068 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:12:29:075 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:12:29:082 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:12:29:142 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:12:29:341 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:12:29:346 ==>> 检测【左刹电压测试1】
2025-07-31 20:12:29:350 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:29:657 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3134  volt:5508 mv
[D][05:18:18][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:18][COMM]adc read left brake adc:1718  volt:2264 mv
[D][05:18:18][COMM]adc read right brake adc:1726  volt:2275 mv
[D][05:18:18][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:18][COMM]adc read battery ts volt:13 mv
[D][05:18:18][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2431  volt:3917 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
 

2025-07-31 20:12:29:732 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 20:12:29:762 ==>>                                          

2025-07-31 20:12:29:870 ==>> 【左刹电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 20:12:29:874 ==>> 检测【右刹电压测试1】
2025-07-31 20:12:29:893 ==>> 【右刹电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 20:12:29:896 ==>> 检测【转把电压测试1】
2025-07-31 20:12:29:911 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 20:12:29:917 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:12:29:921 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:12:30:049 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:12:30:187 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:12:30:190 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:12:30:196 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:12:30:246 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:12:30:462 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:12:30:466 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:12:30:472 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:12:30:533 ==>> 3A A3 05 00 A3 


2025-07-31 20:12:30:639 ==>> OFF_OUT5
OVER 150


2025-07-31 20:12:30:745 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:12:30:750 ==>> 检测【左刹电压测试2】
2025-07-31 20:12:30:754 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:12:30:759 ==>> $GBGGA,121234.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,60,,,41,59,,,41,24,,,41,1*78

$GBGSV,6,2,24,42,,,41,14,,,41,25,,,41,3,,,40,1*42

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,6,4,24,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,6,5,24,9,,,36,6,,,36,8,,,35,26,,,35,1*40

$GBGSV,6,6,24,7,,,35,5,,,34,4,,,33,34,,,31,1*42

$GBRMC,121234.512,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121234.512,0.000,777.615,777.615,711.146,2097152,2097152,2097152*64



2025-07-31 20:12:31:050 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:20][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:20][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:20][COMM]adc read battery ts volt:10 mv
[D][05:18:20][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:12:31:282 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 20:12:31:285 ==>> 检测【右刹电压测试2】
2025-07-31 20:12:31:300 ==>> 【右刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:12:31:304 ==>> 检测【转把电压测试2】
2025-07-31 20:12:31:319 ==>> 【转把电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 20:12:31:323 ==>> 检测【晶振检测】
2025-07-31 20:12:31:328 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:12:31:521 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 20:12:31:600 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:12:31:605 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:12:31:611 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:12:31:763 ==>> $GBGGA,121235.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,60,,,41,59,,,41,24,,,41,1*78

$GBGSV,6,2,24,42,,,41,14,,,41,25,,,41,3,,,40,1*42

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,6,4,24,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,6,5,24,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,6,6,24,7,,,35,5,,,34,4,,,33,34,,,31,1*42

$GBRMC,121235.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121235.512,0.000,778.476,778.476,711.933,2097152,2097152,2097152*6F

1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1652mV
Get AD_V4 1652mV
Get AD_V5 2766mV
Get AD_V6 2022mV
Get AD_V7 1097mV
OVER 150
                                         

2025-07-31 20:12:31:872 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:12:31:876 ==>> 检测【检测BootVer】
2025-07-31 20:12:31:881 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:32:223 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539159
[D][05:18:21][FCTY]HardwareID  = 867222087745289
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:21][FCTY]Bat         = 3944 mv
[D][05:18:21][FCTY]Current     = 50 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1289
[D][05:18:21][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_

2025-07-31 20:12:32:269 ==>> VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3701 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:12:32:343 ==>> [D][05:18:21][COMM]S->M yaw:INVALID


2025-07-31 20:12:32:412 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:12:32:415 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:12:32:421 ==>> 检测【检测固件版本】
2025-07-31 20:12:32:430 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:12:32:451 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:12:32:455 ==>> 检测【检测蓝牙版本】
2025-07-31 20:12:32:458 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:12:32:461 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:12:32:465 ==>> 检测【检测MoBikeId】
2025-07-31 20:12:32:471 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:12:32:474 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:12:32:480 ==>> 检测【检测蓝牙地址】
2025-07-31 20:12:32:487 ==>> 取到目标值:CF7E30579718
2025-07-31 20:12:32:510 ==>> 【检测蓝牙地址】通过,【CF7E30579718】符合目标值【】要求!
2025-07-31 20:12:32:514 ==>> 提取到蓝牙地址:CF7E30579718
2025-07-31 20:12:32:517 ==>> 检测【BOARD_ID】
2025-07-31 20:12:32:530 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:12:32:534 ==>> 检测【检测充电电压】
2025-07-31 20:12:32:549 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:12:32:554 ==>> 检测【检测VBUS电压1】
2025-07-31 20:12:32:568 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:12:32:573 ==>> 检测【检测充电电流】
2025-07-31 20:12:32:587 ==>> 【检测充电电流】通过,【50ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:12:32:591 ==>> 检测【检测IMEI】
2025-07-31 20:12:32:596 ==>> 取到目标值:867222087745289
2025-07-31 20:12:32:617 ==>> 【检测IMEI】通过,【867222087745289】符合目标值【】要求!
2025-07-31 20:12:32:621 ==>> 提取到IMEI:867222087745289
2025-07-31 20:12:32:624 ==>> 检测【检测IMSI】
2025-07-31 20:12:32:629 ==>> 取到目标值:460130071539159
2025-07-31 20:12:32:650 ==>> 【检测IMSI】通过,【460130071539159】符合目标值【】要求!
2025-07-31 20:12:32:654 ==>> 提取到IMSI:460130071539159
2025-07-31 20:12:32:657 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:12:32:661 ==>> 取到目标值:460130071539159
2025-07-31 20:12:32:669 ==>> 【校验网络运营商(移动)】通过,【460130071539159】符合目标值【】要求!
2025-07-31 20:12:32:673 ==>> 检测【打开CAN通信】
2025-07-31 20:12:32:679 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:12:32:752 ==>> $GBGGA,121236.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,41,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,24,,,41,42,,,41,14,,,41,25,,,41,1*76

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,4,,,33,34,,,31,1*42

$GBGSV,7,7,25,10,,,36,1*75

$GBRMC,121236.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121236.512,0.000,779.340,779.340,712.723,2097152,2097152,2097152*60

[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:12:32:951 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:12:32:955 ==>> 检测【检测CAN通信】
2025-07-31 20:12:32:961 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:12:33:041 ==>> can send success


2025-07-31 20:12:33:086 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:12:33:131 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33509
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:12:33:206 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:12:33:225 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:12:33:230 ==>> 检测【关闭CAN通信】
2025-07-31 20:12:33:238 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:12:33:266 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:12:33:342 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:12:33:387 ==>> [D][05:18:22][COMM]M->S yaw:INVALID


2025-07-31 20:12:33:497 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:12:33:503 ==>> 检测【打印IMU STATE】
2025-07-31 20:12:33:507 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:12:33:796 ==>> $GBGGA,121237.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,41,59,,,41,24,,,41,1*78

$GBGSV,7,2,25,42,,,41,14,,,41,25,,,41,3,,,40,1*42

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,34,4,,,33,1*41

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121237.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121237.512,0.000,775.478,775.478,709.191,2097152,2097152,2097152*64

[W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:1
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
                                         

2025-07-31 20:12:34:030 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:12:34:035 ==>> 检测【六轴自检】
2025-07-31 20:12:34:038 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:12:34:239 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:12:34:752 ==>> $GBGGA,121238.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,41,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,24,,,41,14,,,41,25,,,41,42,,,40,1*77

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,34,4,,,33,1*41

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121238.512,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121238.512,0.000,775.478,775.478,709.191,2097152,2097152,2097152*6B



2025-07-31 20:12:35:786 ==>> $GBGGA,121239.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,41,59,,,41,24,,,41,1*4D

$GBGSV,7,2,25,14,,,41,25,,,41,60,,,40,42,,,40,1*76

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,33,4,,,33,1*46

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121239.512,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121239.512,0.000,773.823,773.823,707.678,2097152,2097152,2097152*64

                                         

2025-07-31 20:12:35:922 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:12:36:119 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36487 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-15,-14,4068]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 20:12:36:362 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:12:36:367 ==>> 检测【打印IMU STATE2】
2025-07-31 20:12:36:371 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:12:36:541 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:12:36:634 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:12:36:640 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:12:36:646 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:12:36:752 ==>> $GBGGA,121240.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,41,59,,,41,24,,,41,1*4D

$GBGSV,7,2,25,14,,,41,25,,,41,60,,,40,42,,,40,1*76

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,33,4,,,33,1*46

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121240.512,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121240.512,0.000,773.823,773.823,707.678,2097152,2097152,2097152*6A

5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:12:36:857 ==>> [

2025-07-31 20:12:36:905 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:12:36:933 ==>> 检测【检测VBUS电压2】
2025-07-31 20:12:36:952 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:36:981 ==>> D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:12:37:322 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539159
[D][05:18:26][FCTY]HardwareID  = 867222087745289
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:26][FCTY]Bat         = 4064 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 315
[D][05:18:26][FCTY]Acckey1 vol = 5503 mv, Acckey2 vol = 25 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]

2025-07-31 20:12:37:367 ==>> CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3701 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 20:12:37:431 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:37:870 ==>> [D][05:18:26][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539159
[D][05:18:26][FCTY]HardwareID  = 867222087745289
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:27][FCTY]Bat         = 4064 mv
[D][05:18:27][FCTY]Current     = 50 ma
[D][05:18:27][FCTY]VBUS        = 11900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 175
[D][05:18:27][FCTY]Acckey1 vol = 5501 mv, Acckey2 vol = 75 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION 

2025-07-31 20:12:37:961 ==>> = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3701 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121241.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,3,,,41,24,,,41,42,,,41,1*46

$GBGSV,7,2,25,14,,,41,25,,,41,60,,,40,59,,,40,1*7C

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,33,4,,,33,1*46

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121241.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121241.512,0.000,774.654,774.654,708.438,2097152,2097152,2097152*62



2025-07-31 20:12:37:968 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:38:324 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33493. cur_tick:38505. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38505
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539159
[D][05:18:27][FCTY]HardwareID  = 867222087745289
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:27][FCTY]Bat         = 4064 mv
[D][05:18:27][FCTY]Current     = 50 ma
[D][05:18:27][FCTY]VBUS        = 11900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 151
[D][05:18:27][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 25 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05

2025-07-31 20:12:38:384 ==>> :18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3701 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:12:38:493 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:39:180 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539159
[D][05:18:27][FCTY]HardwareID  = 867222087745289
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:27][FCTY]Bat         = 3644 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 7900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 129
[D][05:18:28][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM 

2025-07-31 20:12:39:285 ==>> = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3701 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121242.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,41,24,,,41,42,,,41,1*47

$GBGSV,7,2,25,14,,,41,25,,,41,60,,,40,59,,,40,1*7C

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,41,,,37,2,,,36,1*47

$GBGSV,7,5,25,26,,,36,9,,,36,6,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,35,5,,,34,10,,,33,4,,,33,1*46

$GBGSV,7,7,25,34,,,31,1*74

$GBRMC,121242.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121242.512,0.000,773.823,773.823,707.678,2097152,2097152,2097152*68

[D][05:18:28][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:28][COMM]Main Task receive event:65
[D][05:18:28][COMM]main task tmp_sleep_event = 80
[D][05:18:28][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:28][COMM]Main Task receive event:65 finished processing
[D][05:18:28][C

2025-07-31 20:12:39:294 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:12:39:390 ==>> OMM]Main Task receive event:60
[D][05:18:28][COMM]smart_helmet_vol=255,255
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[W][05:18:28][GNSS]stop locating
[D][05:18:28][GNSS]stop event:8
[D][05:18:28][GNSS]all continue location stop
[W][05:18:28][GNSS]sing locating running
[D][05:18:28][COMM]report elecbike
[W][05:18:28][PROT]remove success[1629955108],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]index:0
[D][05:18:28][PROT]is_send:1
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg_type:0x5d03
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900005]
[D][05

2025-07-31 20:12:39:495 ==>> :18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[198]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:28][CAT1]gsm read msg sub id: 15
[

2025-07-31 20:12:39:585 ==>> D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B56CD67C1249DB337424EEC7A7DC320AF7FFF9AA4B9821BC4E41D4E18F853CE1B7513BD52DEFF6060F465987536B9BD532B93264D0DE82FEBFCDC7AFB155D93F51092BF83D59DD3DDDEE680E41A93395BBED25
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]


2025-07-31 20:12:39:857 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539159
[D][05:18:29][FCTY]HardwareID  = 867222087745289
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = CF7E30579718
[D][05:18:29][FCTY]Bat         = 3644 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 5000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 112
[D][05:18:29][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:1

2025-07-31 20:12:39:902 ==>> 8:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3701 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:12:40:073 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:12:40:080 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:12:40:086 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:12:40:146 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:12:40:206 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 21
[D][05:18:29][COMM]read battery soc:255


2025-07-31 20:12:40:349 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:12:40:354 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:12:40:359 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:12:40:446 ==>> 5A A5 04 5A A5 


2025-07-31 20:12:40:536 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:12:40:629 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:12:40:640 ==>> 检测【打开WIFI(3)】
2025-07-31 20:12:40:660 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:12:40:837 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 20:12:40:901 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:12:40:909 ==>> 检测【扩展芯片hw】
2025-07-31 20:12:40:931 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:12:41:921 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:12:42:343 ==>> [D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_

2025-07-31 20:12:42:448 ==>> device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[D][05:18:31][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive

2025-07-31 20:12:42:553 ==>>  event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][PROT]index:1
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_p

2025-07-31 20:12:42:658 ==>> ath:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],g

2025-07-31 20:12:42:703 ==>> po:[1]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]read battery soc:255


2025-07-31 20:12:42:915 ==>> [D][05:18:32][COMM]43187 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:12:42:964 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:12:42:969 ==>> 检测【扩展芯片boot】
2025-07-31 20:12:42:977 ==>>                                                                                                                                                                                                                                                                

2025-07-31 20:12:42:983 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:12:42:988 ==>> 检测【扩展芯片sw】
2025-07-31 20:12:43:002 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:12:43:007 ==>> 检测【检测音频FLASH】
2025-07-31 20:12:43:012 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:12:43:217 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:12:43:838 ==>> [D][05:18:33][COMM]44198 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][CAT1]SEND RAW data timeout
[D][05:18:33][CAT1]exec over: func id: 12, ret: -52


2025-07-31 20:12:44:141 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44369]
[D][05:18:33][COMM

2025-07-31 20:12:44:171 ==>> ]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 20:12:44:217 ==>>                                          

2025-07-31 20:12:44:826 ==>> [D][05:18:34][COMM]45210 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:45:036 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45397]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 20:12:45:843 ==>> [D][05:18:35][COMM]46222 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:46:238 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:12:46:875 ==>> [D][05:18:36][COMM]47234 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:47:118 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 20:12:47:863 ==>> [D][05:18:37][COMM]48246 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:48:034 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:12:48:040 ==>> 检测【打开喇叭声音】
2025-07-31 20:12:48:044 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:12:48:261 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]read battery soc:255


2025-07-31 20:12:48:310 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:12:48:319 ==>> 检测【打开大灯控制】
2025-07-31 20:12:48:340 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:12:48:504 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:12:48:583 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:12:48:591 ==>> 检测【关闭仪表供电3】
2025-07-31 20:12:48:600 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:12:48:729 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 20:12:48:853 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:12:48:859 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:12:48:883 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:12:48:890 ==>> [D][05:18:38][COMM]49256 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:48:986 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:12:49:129 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:12:49:135 ==>> 检测【读大灯电压】
2025-07-31 20:12:49:140 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:12:49:325 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[33224]


2025-07-31 20:12:49:404 ==>> 【读大灯电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:12:49:409 ==>> 检测【关闭大灯控制2】
2025-07-31 20:12:49:418 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:12:49:492 ==>> [E][05:18:38][GNSS]GPS module no nmea data!
[D][05:18:38][GNSS]GPS reload stop. ret=0
[D][05:18:38][GNSS]GPS reload start. ret=0


2025-07-31 20:12:49:597 ==>> [

2025-07-31 20:12:49:627 ==>> W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:12:49:676 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:12:49:687 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:12:49:707 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:12:49:898 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[92]
[D][05:18:39][COMM]50268 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:49:947 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:12:49:955 ==>> 检测【打开WIFI(4)】
2025-07-31 20:12:49:976 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:12:50:097 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:12:50:248 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:12:50:267 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:12:50:272 ==>> 检测【EC800M模组版本】
2025-07-31 20:12:50:277 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:50:905 ==>> [D][05:18:40][COMM]51279 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:51:305 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:51:905 ==>> [D][05:18:41][COMM]52290 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:12:52:162 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:12:52:237 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:12:52:342 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:52:764 ==>> [D][05:18:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:12:52:899 ==>> [D][05:18:42][COMM]imu error,enter wait


2025-07-31 20:12:53:372 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:54:136 ==>> [D][05:18:43][CAT1]exec over: func id: 15, ret: -93
[D][05:18:43][CAT1]sub id: 15, ret: -93

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:43][SAL ]socket send fail. id[4]
[D][05:18:43][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:43][CAT1]gsm read msg sub id: 24
[D][05:18:43][M2M ]m2m select fd[4]
[D][05:18:43][M2M ]socket[4] Link is disconnected
[D][05:18:43][M2M ]tcpclient close[4]
[D][05:18:43][SAL ]socket[4] has closed
[D][05:18:43][PROT]protocol read data ok
[E][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:43][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:43][PROT]M2M Send Fail [1629955123]
[D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 20:12:54:407 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:54:437 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54648]
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1032].open hexlog save
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]file:A20 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:43][COMM]read file, len:15228, num:4
[D][05:18:43][COMM]--->crc16:0x419c
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel parse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 20:12:55:384 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55746]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 20:12:55:429 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:56:010 ==>> [D][05:18:45][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 20:12:56:270 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:45][COMM]read battery soc:255


2025-07-31 20:12:56:419 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56775]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 20:12:56:464 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:57:446 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57803]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 20:12:57:490 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:57:764 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:12:58:051 ==>> [D][05:18:47][CAT1]exec over: func id: 24, ret: -181
[D][05:18:47][CAT1]sub id: 24, ret: -181

[D][05:18:47][CAT1]gsm read msg sub id: 23
[D][05:18:47][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 20:12:58:309 ==>> [D][05:18:47][COMM]read battery soc:255
[W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:12:58:522 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:58:552 ==>> [D][05:18:47][GNSS]recv submsg id[1]
[D][05:18:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:47][GNSS]stop gps fail
[D][05:18:47][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 20:12:59:092 ==>> [D][05:18:48][CAT1]exec over: func id: 23, ret: -151
[D][05:18:48][CAT1]sub id: 23, ret: -151

[D][05:18:48][CAT1]gsm read msg sub id: 12
[D][05:18:48][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 20:12:59:545 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:12:59:560 ==>> [D][05:18:48][GNSS]recv submsg id[1]
[D][05:18:48][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:48][GNSS]start gps fail
[E][05:18:48][GNSS]GPS module no nmea data!
[D][05:18:48][GNSS]GPS reload stop. ret=0
[D][05:18:48][GNSS]GPS reload start. ret=0


2025-07-31 20:13:00:272 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:49][COMM]read battery soc:255


2025-07-31 20:13:00:362 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:00:575 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:01:602 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:02:119 ==>> [D][05:18:51][CAT1]SEND RAW data timeout
[D][05:18:51][CAT1]exec over: func id: 12, ret: -52
[W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:13:02:284 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:13:02:636 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:02:771 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:03:673 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:04:120 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:04:286 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:13:04:705 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:05:128 ==>> [D][05:18:54][CAT1]SEND RAW data timeout
[D][05:18:54][CAT1]exec over: func id: 12, ret: -52
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 10
[D][05:18:54][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:13:05:263 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:05:735 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:06:279 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:13:06:761 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:07:139 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:07:770 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:07:800 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:08:297 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:13:08:825 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:09:179 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:09:616 ==>> [E][05:18:58][GNSS]GPS module no nmea data!
[D][05:18:58][GNSS]GPS reload stop. ret=0
[D][05:18:58][GNSS]GPS reload start. ret=0


2025-07-31 20:13:09:781 ==>> [D][05:18:59][COMM]f:[drv_audio_ack_receive].wait ack timeout!![70147]
[D][05:18:59][COMM]accel parse set 0
[D][05:18:59][COMM][Audio].l:[1032].open hexlog save
[D][05:18:59][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 20:13:09:856 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:10:314 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:59][COMM]read battery soc:255


2025-07-31 20:13:10:891 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:11:241 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:11:930 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:12:305 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 20:13:12:769 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:12:968 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:13:121 ==>> [D][05:19:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:13:13:286 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:14:010 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:14:317 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 20:13:15:057 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:15:271 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:15:346 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:16:099 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:16:327 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 20:13:17:130 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:17:393 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:17:769 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:18:166 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:18:320 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 20:13:19:199 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:19:444 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:19:684 ==>> [E][05:19:09][GNSS]GPS module no nmea data!
[D][05:19:09][GNSS]GPS reload stop. ret=0
[D][05:19:09][GNSS]GPS reload start. ret=0


2025-07-31 20:13:20:237 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:13:20:267 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:13:20:342 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 20:13:21:124 ==>> [D][05:19:10][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:13:21:275 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 20:13:21:285 ==>> #################### 【测试结束】 ####################
2025-07-31 20:13:21:378 ==>> 关闭5V供电
2025-07-31 20:13:21:391 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:13:21:445 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:13:21:670 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 20:13:22:343 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 20:13:22:388 ==>> 关闭5V供电成功
2025-07-31 20:13:22:396 ==>> 关闭33V供电
2025-07-31 20:13:22:405 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:13:22:448 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:13:22:659 ==>> [D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 9
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 9
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 9
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 20:13:23:397 ==>> 关闭33V供电成功
2025-07-31 20:13:23:407 ==>> 关闭3.7V供电
2025-07-31 20:13:23:430 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:13:23:537 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:13:23:993 ==>>  

