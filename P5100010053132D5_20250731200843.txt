2025-07-31 20:08:43:631 ==>> MES查站成功:
查站序号:P5100010053132D5验证通过
2025-07-31 20:08:43:634 ==>> 扫码结果:P5100010053132D5
2025-07-31 20:08:43:636 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:08:43:638 ==>> 测试参数版本:2024.10.11
2025-07-31 20:08:43:640 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:08:43:642 ==>> 检测【打开透传】
2025-07-31 20:08:43:643 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:08:43:734 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:08:43:917 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:08:43:921 ==>> 检测【检测接地电压】
2025-07-31 20:08:43:923 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:08:44:039 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:08:44:194 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:08:44:198 ==>> 检测【打开小电池】
2025-07-31 20:08:44:200 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:08:44:345 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:08:44:470 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:08:44:472 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:08:44:474 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:08:44:543 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:08:44:745 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:08:44:747 ==>> 检测【等待设备启动】
2025-07-31 20:08:44:750 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:45:130 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:08:45:312 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:08:45:778 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:45:989 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:08:46:387 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:08:46:814 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:08:46:859 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:08:47:090 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:08:47:094 ==>> 检测【产品通信】
2025-07-31 20:08:47:097 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:08:47:222 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:08:47:362 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:08:47:364 ==>> 检测【初始化完成检测】
2025-07-31 20:08:47:366 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:08:47:572 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:08:47:636 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:08:47:641 ==>> 检测【关闭大灯控制1】
2025-07-31 20:08:47:643 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:08:47:829 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:08:47:922 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:08:47:924 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:08:47:926 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:08:48:088 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:08:48:193 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:08:48:459 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:08:48:461 ==>> 检测【关闭仪表供电】
2025-07-31 20:08:48:462 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:08:48:639 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:08:48:732 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:08:48:735 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:08:48:737 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:08:48:969 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3636 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:49:009 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:08:49:012 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:08:49:014 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:08:49:194 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:08:49:288 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:08:49:291 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:08:49:293 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:08:49:405 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:08:49:559 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:08:49:562 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:08:49:564 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:08:49:631 ==>> 5A A5 01 5A A5 


2025-07-31 20:08:49:736 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:08:49:829 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:08:49:831 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:08:49:833 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:08:49:841 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 4
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:08:49:948 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:50:114 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:08:50:116 ==>> 该项需要延时执行
2025-07-31 20:08:50:499 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5018. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22

2025-07-31 20:08:50:529 ==>> 217 5020
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020


2025-07-31 20:08:50:992 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:51:404 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:08:51:926 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio 

2025-07-31 20:08:52:031 ==>> file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_ty

2025-07-31 20:08:52:137 ==>> pe:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat wo

2025-07-31 20:08:52:227 ==>> rk state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                                                         

2025-07-31 20:08:52:998 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:53:842 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:08:54:007 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:54:127 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:08:54:130 ==>> 检测【33V输入电压ADC】
2025-07-31 20:08:54:144 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:08:54:450 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:2 mv
[D][05:17:57][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 20:08:54:665 ==>> 【33V输入电压ADC】通过,【32804mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:08:54:668 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:08:54:670 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:08:54:751 ==>> 1A A1 00 00 FC 
Get AD_V2 1672mV
Get AD_V3 1667mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1996mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:08:54:934 ==>> 【TP7_VCC3V3(ADV2)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:54:954 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:08:54:956 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:54:959 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:08:54:961 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:08:54:973 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:08:54:976 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:08:54:991 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:08:54:993 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:08:55:015 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:08:55:019 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:08:55:022 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:08:55:126 ==>> 1A A1 00 00 FC 
Get AD_V2 1670mV
Get AD_V3 1668mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 1996mV
Get AD_V7 1091mV
OVER

2025-07-31 20:08:55:156 ==>>  150


2025-07-31 20:08:55:299 ==>> 【TP7_VCC3V3(ADV2)】通过,【1670mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:55:302 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:08:55:318 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:55:321 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:08:55:324 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:08:55:337 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:08:55:339 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:08:55:355 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:08:55:380 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:08:55:383 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10006


2025-07-31 20:08:55:386 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:08:55:388 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:08:55:457 ==>> 1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1995mV
Get AD_V7 1090mV
OVER 150


2025-07-31 20:08:55:661 ==>> 【TP7_VCC3V3(ADV2)】通过,【1671mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:55:663 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:08:55:681 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:08:55:684 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:08:55:687 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:08:55:690 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:08:55:700 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:08:55:704 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:08:55:719 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:08:55:721 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:08:55:742 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:08:55:745 ==>> 检测【打开WIFI(1)】
2025-07-31 20:08:55:747 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:08:55:834 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:08:56:226 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1

2025-07-31 20:08:56:271 ==>> ,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:08:56:274 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:08:56:277 ==>> 检测【清空消息队列(1)】
2025-07-31 20:08:56:301 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:08:56:695 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
867222087783504

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539078

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<

2025-07-31 20:08:56:725 ==>> < 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:08:56:804 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:08:56:807 ==>> 检测【打开GPS(1)】
2025-07-31 20:08:56:810 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:08:57:044 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:08:57:077 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:08:57:080 ==>> 检测【打开GSM联网】
2025-07-31 20:08:57:082 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:08:57:209 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 20:08:57:349 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:08:57:351 ==>> 检测【打开仪表供电1】
2025-07-31 20:08:57:355 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:08:57:376 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:08:57:541 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:08:57:620 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:08:57:623 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:08:57:626 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:08:57:886 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:08:58:151 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:08:58:154 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:08:58:157 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:08:58:332 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33270]


2025-07-31 20:08:58:426 ==>> 【读取主控ADC采集的仪表电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:08:58:431 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:08:58:436 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:08:58:636 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:08:58:699 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:08:58:702 ==>> 检测【AD_V20电压】
2025-07-31 20:08:58:706 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:08:58:801 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:08:58:940 ==>> 本次取值间隔时间:135ms
2025-07-31 20:08:59:065 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13729 imu init OK


2025-07-31 20:08:59:354 ==>> 本次取值间隔时间:412ms
2025-07-31 20:08:59:476 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:08:59:704 ==>> 本次取值间隔时间:349ms
2025-07-31 20:08:59:859 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:08:59:994 ==>> 本次取值间隔时间:281ms
2025-07-31 20:08:59:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:09:00:103 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:09:00:269 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:09:00:374 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 20:09:00:404 ==>> 本次取值间隔时间:291ms
2025-07-31 20:09:00:449 ==>>                                                                                                                                                                                                                                                                                                                        [D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:09:00:674 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:09:00:810 ==>> 本次取值间隔时间:403ms
2025-07-31 20:09:01:266 ==>> 本次取值间隔时间:450ms
2025-07-31 20:09:01:270 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:09:01:638 ==>> 本次取值间隔时间:361ms
2025-07-31 20:09:01:642 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:09:01:749 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:09:01:809 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:09:01:824 ==>> 本次取值间隔时间:60ms
2025-07-31 20:09:01:839 ==>> 1A A1 10 00 00 
Get AD_V20 1659mV
OVER 150


2025-07-31 20:09:01:884 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:09:01:975 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:09:01:979 ==>> 本次取值间隔时间:142ms
2025-07-31 20:09:01:994 ==>> 【AD_V20电压】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:09:01:997 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:09:02:000 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:09:02:142 ==>> 3A A3 02 00 A3 


2025-07-31 20:09:02:232 ==>> OFF_OUT2
OVER 150


2025-07-31 20:09:02:270 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:09:02:273 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:09:02:276 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:09:02:429 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:09:02:552 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:09:02:555 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:09:02:557 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:09:02:641 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:09:02:824 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:09:02:827 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:09:02:831 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:09:02:836 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,42,,,41,25,,,40,33,,,40,39,,,39,1*77

$GBGSV,2,2,06,24,,,18,14,,,51,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1476.218,1476.218,47.519,2097152,2097152,2097152*41

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 20:09:03:064 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:09:03:117 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:09:03:120 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:09:03:124 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:09:03:336 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:09:03:407 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:09:03:410 ==>> 检测【AD_V21电压】
2025-07-31 20:09:03:412 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:09:03:548 ==>> 1A A1 20 00 00 
Get AD_V21 1076mV
OVER 150


2025-07-31 20:09:03:623 ==>> 本次取值间隔时间:209ms
2025-07-31 20:09:03:641 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:09:03:776 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,25,,,41,33,,,41,14,,,40,42,,,40,1*75

$GBGSV,4,2,13,60,,,39,3,,,39,39,,,38,13,,,38,1*4F

$GBGSV,4,3,13,24,,,37,1,,,37,16,,,35,40,,,37,1*45

$GBGSV,4,4,13,5,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1601.739,1601.739,51.182,2097152,2097152,2097152*40

1A A1 20 00 00 
Get AD_V21 1654mV
OVER 150


2025-07-31 20:09:03:881 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:09:03:971 ==>> 本次取值间隔时间:320ms
2025-07-31 20:09:03:990 ==>> 【AD_V21电压】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:09:03:993 ==>> 检测【关闭仪表供电2】
2025-07-31 20:09:03:996 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:09:04:139 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:09:04:262 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:09:04:265 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:09:04:269 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:09:04:427 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:09:04:538 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:09:04:541 ==>> 检测【打开AccKey2供电】
2025-07-31 20:09:04:545 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:09:04:795 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,25,,,41,33,,,41,42,,,41,14,,,40,1*71

$GBGSV,4,2,16,60,,,40,59,,,40,3,,,39,24,,,39,1*48

$GBGSV,4,3,16,39,,,38,13,,,38,2,,,38,40,,,37,1*47

$GBGSV,4,4,16,1,,,37,38,,,37,16,,,36,5,,,32,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1590.929,1590.929,50.861,2097152,2097152,2097152*45

[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:09:05:071 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:09:05:075 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:09:05:079 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:05:347 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:08][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:08][COMM]adc read battery ts volt:3 mv
[D][05:18:08][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2393  volt:3855 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:09:05:606 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33184mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:09:05:609 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:09:05:612 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:09:05:872 ==>> $GBGGA,120909.612,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,25,,,41,33,,,41,42,,,41,59,,,41,1*76

$GBGSV,5,2,18,14,,,40,60,,,40,3,,,40,24,,,40,1*4E

$GBGSV,5,3,18,39,,,38,13,,,38,2,,,37,40,,,37,1*47

$GBGSV,5,4,18,38,,,37,16,,,37,1,,,36,5,,,33,1*73

$GBGSV,5,5,18,4,,,33,19,,,36,1*46

$GBRMC,120909.612,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120909.612,0.000,1585.151,1585.151,50.689,2097152,2097152,2097152*55

[D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:09:05:902 ==>>                                          

2025-07-31 20:09:06:147 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:09:06:151 ==>> 该项需要延时执行
2025-07-31 20:09:06:721 ==>> $GBGGA,120910.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,42,25,,,41,42,,,41,59,,,41,1*7C

$GBGSV,6,2,21,24,,,41,14,,,40,60,,,40,3,,,40,1*46

$GBGSV,6,3,21,39,,,38,13,,,38,2,,,37,40,,,37,1*4E

$GBGSV,6,4,21,38,,,37,16,,,37,1,,,36,41,,,36,1*4F

$GBGSV,6,5,21,6,,,35,7,,,35,5,,,33,4,,,33,1*76

$GBGSV,6,6,21,9,,,32,1*4D

$GBRMC,120910.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120910.512,0.000,1559.622,1559.622,49.893,2097152,2097152,2097152*53



2025-07-31 20:09:07:721 ==>> $GBGGA,120911.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,59,,,41,24,,,41,25,,,40,1*7D

$GBGSV,6,2,22,42,,,40,14,,,40,60,,,40,3,,,40,1*44

$GBGSV,6,3,22,39,,,38,13,,,38,40,,,37,38,,,37,1*74

$GBGSV,6,4,22,16,,,37,1,,,37,2,,,36,41,,,36,1*75

$GBGSV,6,5,22,6,,,35,8,,,35,7,,,34,9,,,34,1*75

$GBGSV,6,6,22,5,,,33,4,,,33,1*77

$GBRMC,120911.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120911.512,0.000,1550.904,1550.904,49.601,2097152,2097152,2097152*57



2025-07-31 20:09:07:901 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:09:08:718 ==>> $GBGGA,120912.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,59,,,41,24,,,41,25,,,41,1*7E

$GBGSV,6,2,23,42,,,41,60,,,41,14,,,40,3,,,40,1*45

$GBGSV,6,3,23,39,,,38,13,,,38,40,,,37,38,,,37,1*75

$GBGSV,6,4,23,16,,,37,1,,,37,2,,,36,41,,,36,1*74

$GBGSV,6,5,23,6,,,35,8,,,35,7,,,34,9,,,34,1*74

$GBGSV,6,6,23,5,,,33,4,,,33,10,,,30,1*74

$GBRMC,120912.512,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120912.512,0.000,1544.787,1544.787,49.433,2097152,2097152,2097152*57



2025-07-31 20:09:09:162 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:09:09:166 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:09:09:171 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:09:448 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:12][COMM]adc read battery ts volt:7 mv
[D][05:18:12][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2394  volt:3857 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:09:09:692 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:09:09:698 ==>> 检测【打开AccKey1供电】
2025-07-31 20:09:09:710 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:09:09:718 ==>> $GBGGA,120913.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,59,,,41,24,,,41,25,,,41,1*7A

$GBGSV,6,2,24,42,,,41,60,,,40,14,,,40,3,,,40,1*43

$GBGSV,6,3,24,39,,,38,13,,,38,40,,,37,38,,,37,1*72

$GBGSV,6,4,24,16,,,37,1,,,37,26,,,37,2,,,36,1*73

$GBGSV,6,5,24,41,,,36,6,,,36,8,,,35,9,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,33,4,,,33,10,,,31,1*42

$GBRMC,120913.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120913.512,0.000,1546.048,1546.048,49.457,2097152,2097152,2097152*54



2025-07-31 20:09:09:823 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:09:09:898 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:09:09:971 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:09:09:976 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:09:09:981 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:09:10:048 ==>> 1A A1 00 40 00 
Get AD_V14 2651mV
OVER 150


2025-07-31 20:09:10:232 ==>> 原始值:【2651】, 乘以分压基数【2】还原值:【5302】
2025-07-31 20:09:10:251 ==>> 【读取AccKey1电压(ADV14)前】通过,【5302mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:09:10:264 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:09:10:268 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:10:551 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:4 mv
[D][05:18:14][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2393  volt:3855 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:09:10:656 ==>> $GBGGA,120914.

2025-07-31 20:09:10:731 ==>> 512,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,25,,,41,1*79

$GBGSV,6,2,24,42,,,41,60,,,40,14,,,40,3,,,40,1*43

$GBGSV,6,3,24,39,,,38,13,,,38,40,,,37,38,,,37,1*72

$GBGSV,6,4,24,16,,,37,1,,,37,26,,,36,2,,,36,1*72

$GBGSV,6,5,24,41,,,36,6,,,36,8,,,35,9,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,33,4,,,33,10,,,32,1*41

$GBRMC,120914.512,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120914.512,0.000,1547.774,1547.774,49.512,2097152,2097152,2097152*53



2025-07-31 20:09:10:780 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5549mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:09:10:787 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:09:10:793 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:09:10:926 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:09:11:050 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:09:11:055 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:09:11:058 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:09:11:139 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 20:09:11:306 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 20:09:11:325 ==>> 【读取AccKey1电压(ADV14)后】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:09:11:328 ==>> 检测【打开WIFI(2)】
2025-07-31 20:09:11:333 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:09:11:551 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:09:11:600 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:09:11:605 ==>> 检测【转刹把供电】
2025-07-31 20:09:11:608 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:09:11:657 ==>> $GBGGA,120915.512,

2025-07-31 20:09:11:731 ==>> ,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,25,,,41,1*79

$GBGSV,6,2,24,42,,,41,60,,,40,14,,,40,3,,,40,1*43

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,26,,,36,2,,,36,1*48

$GBGSV,6,5,24,41,,,36,6,,,36,8,,,35,9,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,33,4,,,33,10,,,32,1*41

$GBRMC,120915.512,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120915.512,0.000,1549.502,1549.502,49.567,2097152,2097152,2097152*50



2025-07-31 20:09:11:806 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:09:11:872 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:09:11:900 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:09:11:905 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:09:11:912 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:09:11:986 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:09:12:016 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get 

2025-07-31 20:09:12:046 ==>> AD_V15 2400mV
OVER 150


2025-07-31 20:09:12:151 ==>> 原始值:【2400】, 乘以分压基数【2】还原值:【4800】
2025-07-31 20:09:12:171 ==>> 【读取AD_V15电压(前)】通过,【4800mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:09:12:174 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:09:12:179 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:09:12:286 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:09:12:301 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:09:12:346 ==>> 1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 20:09:12:451 ==>> +WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,F42A7D1297A3,-72
+WIFISCAN:4,2,CC057790A740,-78
+WIFISCAN:4,3,44A1917CAD80,-84

[D][05:18:16][CAT1]wifi scan report total[4]


2025-07-31 20:09:12:456 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 20:09:12:474 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:09:12:478 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:09:12:480 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:12:826 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:3 mv
[D][05:18:16][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3103  volt:5454 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
$GBGGA,120916.512,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,59,,,41,24,,,41,25,,,41,1*7A

$GBGSV,6,2,24,42,,,40,60,,,40,14,,,40,3,,,40,1*42

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,26,,,36,2,,,36,1*48

$GBGSV,6,5,24,41,,,36,6,,,36,8,,,35,9,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,33,4,,,33,10,,,32,1*41

$GBRMC,120916.512,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120916.5

2025-07-31 20:09:12:871 ==>> 12,0.000,1546.042,1546.042,49.451,2097152,2097152,2097152*57

[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:09:13:010 ==>> 【转刹把供电电压(主控ADC)】通过,【5454mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:09:13:015 ==>> 检测【转刹把供电电压】
2025-07-31 20:09:13:020 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:13:066 ==>> [D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 20:09:13:353 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:7 mv
[D][05:18:16][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3099  volt:5447 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:09:13:718 ==>> $GBGGA,120917.512,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,59,,,41,24,,,41,25,,,41,1*7A

$GBGSV,6,2,24,42,,,40,60,,,40,14,,,40,3,,,40,1*42

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,26,,,36,2,,,36,1*48

$GBGSV,6,5,24,41,,,36,6,,,36,9,,,36,8,,,35,1*42

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBRMC,120917.512,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120917.512,0.000,1549.492,1549.492,49.557,2097152,2097152,2097152*51



2025-07-31 20:09:13:722 ==>> 【转刹把供电电压】通过,【5447mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:09:13:726 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:09:13:735 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:13:932 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:17][COMM]read battery soc:255


2025-07-31 20:09:13:995 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:09:14:000 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:09:14:007 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:14:099 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:09:14:209 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:14:317 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:09:14:425 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:14:440 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:18][COMM]>>>>>Input command = ?<<<<
00 00 00 00 00 
head err!


2025-07-31 20:09:14:530 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:09:14:637 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:14:642 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:09:14:727 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,120918.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,59,,,41,24,,,41,25,,,41,1*7A

$GBGSV,6,2,24,42,,,40,60,,,40,14,,,40,3,,,40,1*42

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,26,,,36,2,,,36,1*48

$GBGSV,6,5,24,41,,,36,6,,,36,9,,,35,8,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,34,10,,,33,1*40

$GBRMC,120918.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120918.512,0.000,1551.214,1551.214,49.607,2097152,2097152,2097152*58



2025-07-31 20:09:14:742 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:09:14:802 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:09:14:848 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:09:14:867 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:09:14:871 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:09:14:873 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:09:14:982 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:09:15:043 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:09:15:107 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:09:15:111 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:09:15:114 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:09:15:242 ==>> 3A A3 03 01 A3 


2025-07-31 20:09:15:332 ==>> ON_OUT3
OVER 150


2025-07-31 20:09:15:380 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:09:15:386 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:09:15:410 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:09:15:437 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:09:15:650 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:09:15:658 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:09:15:683 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:09:15:739 ==>> $GBGGA,120919.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,25,,,41,1*79

$GBGSV,6,2,24,60,,,41,42,,,40,14,,,40,3,,,40,1*43

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,2,,,36,26,,,35,1*4B

$GBGSV,6,5,24,41,,,35,6,,,35,9,,,35,8,,,35,1*41

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,33,10,,,33,1*47

$GBRMC,120919.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120919.512,0.000,1547.772,1547.772,49.510,2097152,2097152,2097152*5C

3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:09:15:921 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:09:15:925 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:09:15:929 ==>> 检测【左刹电压测试1】
2025-07-31 20:09:15:931 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:16:255 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3149  volt:5535 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:19][COMM]adc read right brake adc:1729  volt:2279 mv
[D][05:18:19][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:19][COMM]adc read battery ts volt:0 mv
[D][05:18:19][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1436  volt:33293 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:09:16:456 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:09:16:461 ==>> 检测【右刹电压测试1】
2025-07-31 20:09:16:478 ==>> 【右刹电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 20:09:16:481 ==>> 检测【转把电压测试1】
2025-07-31 20:09:16:497 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 20:09:16:504 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:09:16:515 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:09:16:633 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:09:16:723 ==>> $GBGGA,120920.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,59,,,41,24,,,41,25,,,41,1*79

$GBGSV,6,2,24,60,,,40,42,,,40,14,,,40,3,,,40,1*42

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,40,,,37,1*47

$GBGSV,6,4,24,38,,,37,16,,,37,2,,,36,41,,,36,1*49

$GBGSV,6,5,24,26,,,35,6,,,35,9,,,35,8,,,35,1*40

$GBGSV,6,6,24,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBRMC,120920.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120920.512,0.000,1546.045,1546.045,49.454,2097152,2097152,2097152*57



2025-07-31 20:09:16:773 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:09:16:777 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:09:16:780 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:09:16:828 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:09:17:293 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:09:17:298 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:09:17:304 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:09:17:447 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:09:17:570 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:09:17:574 ==>> 检测【左刹电压测试2】
2025-07-31 20:09:17:577 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:09:17:858 ==>> $GBGGA,120921.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,24,,,41,25,,,41,60,,,41,1*73

$GBGSV,6,2,24,59,,,40,42,,,40,14,,,40,3,,,40,1*48

$GBGSV,6,3,24,39,,,38,13,,,38,1,,,38,38,,,38,1*47

$GBGSV,6,4,24,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,6,5,24,6,,,36,26,,,35,9,,,35,8,,,35,1*43

$GBGSV,6,6,24,7,,,34,4,,,34,5,,,33,10,,,32,1*46

$GBRMC,120921.512,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120921.512,0.000,1549.498,1549.498,49.563,2097152,2097152,2097152*53

[D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:21][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:21][COMM]adc read battery ts volt:0 mv
[D][05:18:21][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_

2025-07-31 20:09:17:903 ==>> id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:09:17:948 ==>>                                          

2025-07-31 20:09:18:103 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:09:18:107 ==>> 检测【右刹电压测试2】
2025-07-31 20:09:18:121 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:09:18:125 ==>> 检测【转把电压测试2】
2025-07-31 20:09:18:140 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:09:18:144 ==>> 检测【晶振检测】
2025-07-31 20:09:18:148 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:09:18:331 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 20:09:18:410 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:09:18:417 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:09:18:424 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:09:18:558 ==>> 1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1667mV
Get AD_V4 1645mV
Get AD_V5 2762mV
Get AD_V6 1996mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:09:18:663 ==>> $GBGGA,120922.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV

2025-07-31 20:09:18:682 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:09:18:690 ==>> 检测【检测BootVer】
2025-07-31 20:09:18:711 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:09:18:723 ==>> ,7,1,25,33,,,42,24,,,41,25,,,41,60,,,40,1*72

$GBGSV,7,2,25,59,,,40,42,,,40,14,,,40,3,,,40,1*48

$GBGSV,7,3,25,39,,,38,13,,,38,1,,,38,38,,,37,1*48

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,7,5,25,6,,,36,26,,,35,9,,,35,8,,,35,1*43

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,33,10,,,33,1*47

$GBGSV,7,7,25,21,,,29,1*79

$GBRMC,120922.512,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120922.512,0.000,1533.967,1533.967,49.082,2097152,2097152,2097152*5A



2025-07-31 20:09:18:997 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071539078
[D][05:18:22][FCTY]HardwareID  = 867222087783504
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:18:22][FCTY]Bat         = 3944 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1295
[D][05:18:22][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_

2025-07-31 20:09:19:042 ==>> GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3791 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:09:19:216 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:09:19:236 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:09:19:240 ==>> 检测【检测固件版本】
2025-07-31 20:09:19:243 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:09:19:247 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:09:19:250 ==>> 检测【检测蓝牙版本】
2025-07-31 20:09:19:273 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:09:19:277 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:09:19:280 ==>> 检测【检测MoBikeId】
2025-07-31 20:09:19:292 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:09:19:297 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:09:19:303 ==>> 检测【检测蓝牙地址】
2025-07-31 20:09:19:315 ==>> 取到目标值:D308A0C65ACC
2025-07-31 20:09:19:318 ==>> 【检测蓝牙地址】通过,【D308A0C65ACC】符合目标值【】要求!
2025-07-31 20:09:19:323 ==>> 提取到蓝牙地址:D308A0C65ACC
2025-07-31 20:09:19:326 ==>> 检测【BOARD_ID】
2025-07-31 20:09:19:352 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:09:19:356 ==>> 检测【检测充电电压】
2025-07-31 20:09:19:371 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:09:19:375 ==>> 检测【检测VBUS电压1】
2025-07-31 20:09:19:389 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:09:19:393 ==>> 检测【检测充电电流】
2025-07-31 20:09:19:407 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:09:19:412 ==>> 检测【检测IMEI】
2025-07-31 20:09:19:417 ==>> 取到目标值:867222087783504
2025-07-31 20:09:19:430 ==>> 【检测IMEI】通过,【867222087783504】符合目标值【】要求!
2025-07-31 20:09:19:434 ==>> 提取到IMEI:867222087783504
2025-07-31 20:09:19:439 ==>> 检测【检测IMSI】
2025-07-31 20:09:19:463 ==>> 取到目标值:460130071539078
2025-07-31 20:09:19:470 ==>> 【检测IMSI】通过,【460130071539078】符合目标值【】要求!
2025-07-31 20:09:19:473 ==>> 提取到IMSI:460130071539078
2025-07-31 20:09:19:478 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:09:19:482 ==>> 取到目标值:460130071539078
2025-07-31 20:09:19:508 ==>> 【校验网络运营商(移动)】通过,【460130071539078】符合目标值【】要求!
2025-07-31 20:09:19:512 ==>> 检测【打开CAN通信】
2025-07-31 20:09:19:515 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:09:19:646 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:09:19:722 ==>> $GBGGA,120923.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,25,,,41,24,,,40,60,,,40,1*70

$GBGSV,7,2,25,59,,,40,42,,,40,14,,,40,3,,,40,1*48

$GBGSV,7,3,25,39,,,38,13,,,38,1,,,38,38,,,37,1*48

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,7,5,25,6,,,36,26,,,35,9,,,35,8,,,35,1*43

$GBGSV,7,6,25,7,,,34,4,,,34,5,,,33,10,,,33,1*47

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120923.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120923.512,0.000,1532.298,1532.298,49.019,2097152,2097152,2097152*59



2025-07-31 20:09:19:785 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:09:19:793 ==>> 检测【检测CAN通信】
2025-07-31 20:09:19:814 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:09:19:826 ==>> [D][05:18:23][COMM]IMU: [22,9,-1005] ret=30 AWAKE!
can send success


2025-07-31 20:09:19:856 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:09:19:947 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34609
[D][05:18:23][COMM]read battery soc:255


2025-07-31 20:09:19:977 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:09:20:056 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:09:20:063 ==>> 检测【关闭CAN通信】
2025-07-31 20:09:20:085 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:09:20:092 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:09:20:143 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:09:20:329 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:09:20:336 ==>> 检测【打印IMU STATE】
2025-07-31 20:09:20:341 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:09:20:539 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:09:20:604 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:09:20:609 ==>> 检测【六轴自检】
2025-07-31 20:09:20:615 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:09:20:721 ==>> $GBGGA,120924.512,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,25,,,41,60,,,41,24,,,40,1*71

$GBGSV,7,2,25,59,,,40,42,,,40,14,,,40,3,,,40,1*48

$GBGSV,7,3,25,39,,,38,13,,,38,1,,,38,38,,,37,1*48

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,7,5,25,6,,,36,9,,,36,26,,,35,8,,,35,1*40

$GBGSV,7,6,25,7,,,34,4,,,34,5,,,33,10,,,33,1*47

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120924.512,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120924.512,0.000,1537.270,1537.270,49.174,2097152,2097152,2097152*54



2025-07-31 20:09:20:825 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:09:21:735 ==>> $GBGGA,120925.512,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,41,59,,,41,1*79

$GBGSV,7,2,25,42,,,41,60,,,40,14,,,40,3,,,40,1*43

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,7,5,25,6,,,36,9,,,36,26,,,35,8,,,35,1*40

$GBGSV,7,6,25,7,,,34,4,,,34,5,,,34,10,,,33,1*40

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120925.512,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120925.512,0.000,1545.567,1545.567,49.445,2097152,2097152,2097152*52



2025-07-31 20:09:21:963 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:09:22:551 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:09:22:777 ==>> $GBGGA,120926.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,41,59,,,41,1*79

$GBGSV,7,2,25,42,,,41,60,,,41,14,,,41,3,,,41,1*42

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,41,,,36,1*46

$GBGSV,7,5,25,6,,,36,9,,,36,26,,,36,8,,,35,1*43

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,33,10,,,33,1*47

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120926.512,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120926.512,0.000,1550.549,1550.549,49.611,2097152,2097152,2097152*52

[D][05:18:26][COMM]Main Task receive event:142
[D][05:18:26][COMM]###### 37356 imu self test OK ######
[D][05:18:26][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-28,0,4065]
[D][05:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 20:09:22:966 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:09:22:973 ==>> 检测【打印IMU STATE2】
2025-07-31 20:09:22:981 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:09:23:130 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:09:23:252 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:09:23:260 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:09:23:284 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:09:23:344 ==>> 5A A5 02 5A A5 


2025-07-31 20:09:23:434 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:09:23:529 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:09:23:536 ==>> 检测【检测VBUS电压2】
2025-07-31 20:09:23:543 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:09:23:616 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:09:23:903 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             GST,120927.512,0.000,1545.573,1545.573,49.451,2097152,2097152,2097152*55

[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539078
[D][05:18:27][FCTY]HardwareID  = 867222087783504
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:18:27][FCTY]Bat         = 3924 mv
[D][05:

2025-07-31 20:09:23:993 ==>> 18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11700 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 332
[D][05:18:27][FCTY]Acckey1 vol = 5535 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3791 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:09:24:056 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:09:24:405 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539078
[D][05:18:27][FCTY]HardwareID  = 867222087783504
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:18:27][FCTY]Bat         = 3924 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 8500 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 178
[D][05:18:27][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1      

2025-07-31 20:09:24:435 ==>>    = 3791 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:09:24:613 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:09:24:726 ==>> $GBGGA,120928.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,24,,,41,59,,,41,1*79

$GBGSV,7,2,25,42,,,41,3,,,41,14,,,41,60,,,40,1*43

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,6,,,36,1*75

$GBGSV,7,5,25,9,,,36,41,,,35,26,,,35,8,,,35,1*70

$GBGSV,7,6,25,7,,,34,5,,,33,4,,,33,10,,,33,1*40

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120928.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120928.512,0.000,1545.573,1545.573,49.451,2097152,2097152,2097152*5A



2025-07-31 20:09:25:030 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539078
[D][05:18:28][FCTY]HardwareID  = 867222087783504
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:18:28][FCTY]Bat         = 3924 mv
[D][05:18:28][FCTY]Current     = 150 ma
[D][05:18:28][FCTY]VBUS        = 8500 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 152
[D][05:18:28][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3791 mv
[D][05:18:28][FCTY]==================== E4

2025-07-31 20:09:25:075 ==>> _X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]msg 0601 loss. last_tick:34602. cur_tick:39606. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39606


2025-07-31 20:09:25:153 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:09:25:878 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539078
[D][05:18:29][FCTY]HardwareID  = 867222087783504
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:18:29][FCTY]Bat         = 3844 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 4900 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 118
[D][05:18:29][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM =

2025-07-31 20:09:25:957 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:09:25:967 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:09:25:975 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:09:25:985 ==>>  C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3791 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][PROT]add success [1629955109],send_path[3],typ

2025-07-31 20:09:26:089 ==>> e[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PR

2025-07-31 20:09:26:194 ==>> OT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6

[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Serv

2025-07-31 20:09:26:284 ==>> er[198][201] ... ->:
0063B98F113311331133113311331B88B54E7DD7F4953E03A4DEEEC9157E47C36AF783EC98A239C9DD7BCAE3F73D50DF5A4511DB70FAE95F5C2365E06284A2D76EEB76442ADD28E452E2996A4F559E5DF00365279B4F753B7F2C8AC8E45DE2D74AAD0B
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:09:26:389 ==>>                                                                                                                                            [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 22
[D][05:18:30][COMM]read battery soc:255


2025-07-31 20:09:26:487 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:09:26:492 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:09:26:498 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:09:26:540 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:09:26:758 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:09:26:763 ==>> 检测【打开WIFI(3)】
2025-07-31 20:09:26:769 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:09:26:972 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:09:27:036 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:09:27:040 ==>> 检测【扩展芯片hw】
2025-07-31 20:09:27:044 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:09:27:230 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:09:27:312 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:09:27:319 ==>> 检测【扩展芯片boot】
2025-07-31 20:09:27:341 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:09:27:345 ==>> 检测【扩展芯片sw】
2025-07-31 20:09:27:370 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:09:27:378 ==>> 检测【检测音频FLASH】
2025-07-31 20:09:27:385 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:09:27:521 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:09:27:671 ==>> +WIFISCAN:4,0,CC057790A740,-78
+WIFISCAN:4,1,CC057790A741,-80
+WIFISCAN:4,2,CC057790A5C0,-85
+WIFISCAN:4,3,CC057790A5C1,-86

[D][05:18:31][CAT1]wifi scan report total[4]


2025-07-31 20:09:27:960 ==>> [D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:09:28:667 ==>>                                                                                                                               [D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file

2025-07-31 20:09:28:772 ==>> , len:10800, num:3
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task rece

2025-07-31 20:09:28:877 ==>> ive event:66 finished processing
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]index:1
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[

2025-07-31 20:09:28:983 ==>> D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:31][PROT]add success [

2025-07-31 20:09:29:088 ==>> 1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:31][GNSS]recv submsg id[3]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, l

2025-07-31 20:09:29:163 ==>> en:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]read battery soc:255
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:09:30:400 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 20:09:31:080 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:34][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3D836A

2025-07-31 20:09:31:185 ==>> F987090F75DEF18B056DB506DFE93743F22662DCEA74C1AD74F7D31F43B63872477309B815A4F4CEBA97FFD86A06D896FB243EED4F2002DA5BE02AE36D0EFA8713FD2C3729F00454185754232443B53
[D][05:18:34][CAT1]<<< 
SEND OK

[D][05:18:34][CAT1]exec over: func id: 15, ret: 11
[D][05:18:34][CAT1]sub id: 15, ret: 11

[D][05:18:34][SAL ]Cellular task submsg id[68]
[D][05:18:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:34][M2M ]g_m2m_is_idle become true
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:34][PROT]M2M Send ok [1629955114]
[D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
                                                            

2025-07-31 20:09:31:645 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 20:09:32:146 ==>> [D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:35][COMM]46756 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:35][COMM]accel parse set 0
[D][05:18:35][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:09:32:404 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 20:09:32:438 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:09:32:443 ==>> 检测【打开喇叭声音】
2025-07-31 20:09:32:450 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:09:33:140 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:36][COMM]read file, len:15228, num:4
[D][05:18:36][COMM]--->crc16:0x419c
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:

2025-07-31 20:09:33:234 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:09:33:244 ==>> 检测【打开大灯控制】
2025-07-31 20:09:33:252 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:09:33:265 ==>> 18:36][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m

2025-07-31 20:09:33:350 ==>> _audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:36][COMM]47767 imu init OK


2025-07-31 20:09:33:425 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:09:33:504 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:09:33:512 ==>> 检测【关闭仪表供电3】
2025-07-31 20:09:33:533 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:09:33:728 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:37][COMM]set POWER 0


2025-07-31 20:09:33:777 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:09:33:782 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:09:33:791 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:09:33:909 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:09:34:180 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:09:34:185 ==>> 检测【读大灯电压】
2025-07-31 20:09:34:205 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:09:34:321 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[33038]


2025-07-31 20:09:34:396 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 20:09:34:459 ==>> 【读大灯电压】通过,【33038mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:09:34:465 ==>> 检测【关闭大灯控制2】
2025-07-31 20:09:34:469 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:09:34:609 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:09:34:733 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:09:34:738 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:09:34:746 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:09:34:927 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[115]


2025-07-31 20:09:35:006 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:09:35:012 ==>> 检测【打开WIFI(4)】
2025-07-31 20:09:35:020 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:09:35:261 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:09:35:338 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:09:35:347 ==>> 检测【EC800M模组版本】
2025-07-31 20:09:35:369 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:09:35:530 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:39][CAT1]gsm read msg sub id: 12
[D][05:18:39][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:09:35:635 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:09:35:740 ==>> [D][05:18:39][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP

2025-07-31 20:09:35:770 ==>> _MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:39][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:09:35:865 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:09:35:871 ==>> 检测【配置蓝牙地址】
2025-07-31 20:09:35:879 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:09:36:074 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D308A0C65ACC>】
2025-07-31 20:09:36:319 ==>> [W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<
+WIFISCAN:4,0,F88C21BCF57D,-33
+WIFISCAN:4,1,CC057790A741,-78
+WIFISCAN:4,2,603A7CF67DD4,-80
+WIFISCAN:4,3,CC057790A5C0,-86

[D][05:18:39][CAT1]wifi scan report total[4]
[D][05:18:39][PROT]CLEAN,SEND:1
[D][05:18:39][PROT]index:1 1629955119
[D][05:18:39][PROT]is_send:0
[D][05:18:39][PROT]sequence_num:5
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:2
[D][05:18:39][PROT]send_path:0x2
[D][05:18:39][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]sending traceid [9999999999900006]
[D][05:18:39][PROT]Send_TO_M2M [1629955119]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:39][SAL ]sock send credit cnt[6]
[D][05:18:39][SAL ]sock send ind credit cnt[6]
[D][05:18:39][M2M ]m2m send data len[198]
[D][05:18:39][SAL ]Cellular task submsg id[10]
[D][05:18:39][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:39]

2025-07-31 20:09:36:424 ==>> [CAT1]gsm read msg sub id: 15
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:39][CAT1]Send Data To Server[198][198] ... ->:
0063B98C113311331133113311331B88B3BE6F2939F4ECF38FB24A726CD9FC7A4592FC89293069CC588C1BBB683228715B1B65A26F0A589186F455539CFE938D3C5E0E091C741372A27E277242C83F5A3DBEDE834873930E24D2EFAF7482DDBAEA3BA5
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]
[D][05:18:39][GNSS]recv submsg id[3]
recv ble 1
recv ble 2
ble set mac ok :d3,8,a0,c6,5a,cc
enable filters ret : 0                                         

2025-07-31 20:09:36:613 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:09:36:624 ==>> 检测【BLETEST】
2025-07-31 20:09:36:640 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:09:36:651 ==>> [D][05:18:40][COMM]51335 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:09:36:741 ==>> 4A A4 01 A4 4A 


2025-07-31 20:09:36:846 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D308A0C65ACC*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D308A0C65ACC99999OVER 150


2025-07-31 20:09:37:642 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:09:37:651 ==>> 该项需要延时执行
2025-07-31 20:09:37:668 ==>> [D][05:18:41][COMM]52345 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:09:38:265 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:09:38:415 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 20:09:38:660 ==>> [D][05:18:42][COMM]53356 imu init OK


2025-07-31 20:09:40:423 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 20:09:41:547 ==>> [D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][PROT]index:1 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:5
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:1
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900006]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3E7CCB01F15535B62CCB8E56B252E6040B694D793FC952AF4AE4CE83FE0D6E164AF2C94F6EB6E164F7285D329

2025-07-31 20:09:41:622 ==>> D32FA1B1AF09354265902180B0088AC4CB17DF562D96573CC2D06A964ED544C6163A63879464
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]


2025-07-31 20:09:42:415 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 20:09:44:421 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 20:09:46:448 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 20:09:46:765 ==>> [D][05:18:50][PROT]CLEAN,SEND:1
[D][05:18:50][PROT]CLEAN:1
[D][05:18:50][PROT]index:0 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:4
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900005]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[198]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:50][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B59F5EC97CD9A22C6841869707C2EA52200696D444

2025-07-31 20:09:46:840 ==>> 556CB9AA93F51CDC3B17138FD197D048D8C2965779709C1208B2785B3A069A0F6EFD3683F4C345EE1017FC1DE38C744B3DE0ED1DFFD2A7E1EE430C90994E
[D][05:18:50][CAT1]<<< 
SEND OK

[D][05:18:50][CAT1]exec over: func id: 15, ret: 11
[D][05:18:50][CAT1]sub id: 15, ret: 11

[D][05:18:50][SAL ]Cellular task submsg id[68]
[D][05:18:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:50][M2M ]g_m2m_is_idle become true
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:50][PROT]M2M Send ok [1629955130]


2025-07-31 20:09:47:650 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:09:47:655 ==>> 检测【检测WiFi结果】
2025-07-31 20:09:47:663 ==>> WiFi信号:【F88C21BCF57D】,信号值:-32
2025-07-31 20:09:47:684 ==>> WiFi信号:【F42A7D1297A3】,信号值:-72
2025-07-31 20:09:47:693 ==>> WiFi信号:【CC057790A740】,信号值:-78
2025-07-31 20:09:47:716 ==>> WiFi信号:【44A1917CAD80】,信号值:-84
2025-07-31 20:09:47:724 ==>> WiFi信号:【CC057790A741】,信号值:-80
2025-07-31 20:09:47:748 ==>> WiFi信号:【CC057790A5C0】,信号值:-85
2025-07-31 20:09:47:756 ==>> WiFi信号:【CC057790A5C1】,信号值:-86
2025-07-31 20:09:47:781 ==>> WiFi信号:【603A7CF67DD4】,信号值:-80
2025-07-31 20:09:47:794 ==>> WiFi数量【8】, 最大信号值:-32
2025-07-31 20:09:47:812 ==>> 检测【检测GPS结果】
2025-07-31 20:09:47:822 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:09:47:868 ==>> [D][05:18:51][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all continue location stop
[W][05:18:51][GNSS]stop locating
[D][05:18:51][GNSS]all sing location stop


2025-07-31 20:09:48:444 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 20:09:48:657 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:09:48:667 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:48:691 ==>> 定位已等待【1】秒.
2025-07-31 20:09:49:051 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:52][COMM]Open GPS Module...
[D][05:18:52][COMM]LOC_MODEL_CONT
[D][05:18:52][GNSS]start event:8
[D][05:18:52][GNSS]GPS start. ret=0
[W][05:18:52][GNSS]start cont locating
[D][05:18:52][CAT1]gsm read msg sub id: 23
[D][05:18:52][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:52][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:09:49:672 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:49:682 ==>> 定位已等待【2】秒.
2025-07-31 20:09:49:748 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:09:50:629 ==>> [D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:54][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,25,,,42,24,,,41,33,,,41,42,,,40,1*78

$GBGSV,3,2,09,39,,,39,40,,,35,59,,,41,13,,,37,1*73

$GBGSV,3,3,09,14,,,36,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1644.477,1644.477,52.570,2097152,2097152,2097152*4A

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:54][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]exec over: func id: 23, ret: 6
[D][05:18:54][CAT1]sub id: 23, ret: 6



2025-07-31 20:09:50:674 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:50:684 ==>> 定位已等待【3】秒.
2025-07-31 20:09:51:280 ==>> [D][05:18:54][GNSS]recv submsg id[1]
[D][05:18:54][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:09:51:554 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,42,25,,,41,24,,,41,14,,,41,1*73

$GBGSV,3,2,11,60,,,41,42,,,40,39,,,39,13,,,38,1*7F

$GBGSV,3,3,11,40,,,36,59,,,41,1,,,41,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1653.665,1653.665,52.839,2097152,2097152,2097152*4A



2025-07-31 20:09:51:675 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:51:685 ==>> 定位已等待【4】秒.
2025-07-31 20:09:52:010 ==>> [D][05:18:55][PROT]CLEAN,SEND:0
[D][05:18:55][PROT]index:0 1629955135
[D][05:18:55][PROT]is_send:0
[D][05:18:55][PROT]sequence_num:4
[D][05:18:55][PROT]retry_timeout:0
[D][05:18:55][PROT]retry_times:1
[D][05:18:55][PROT]send_path:0x2
[D][05:18:55][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:55][PROT]===========================================================
[W][05:18:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955135]
[D][05:18:55][PROT]===========================================================
[D][05:18:55][PROT]sending traceid [9999999999900005]
[D][05:18:55][PROT]Send_TO_M2M [1629955135]
[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:55][SAL ]sock send credit cnt[6]
[D][05:18:55][SAL ]sock send ind credit cnt[6]
[D][05:18:55][M2M ]m2m send data len[198]
[D][05:18:55][SAL ]Cellular task submsg id[10]
[D][05:18:55][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:55][CAT1]gsm read msg sub id: 15
[D][05:18:55][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:55][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5BB4AC5D52E4B2002EA7AB754E6F3FAB32B60D5E012E775EB8C89DC6150A874859A18D7BA91025A682C92BCF6CA1679715D65954D2F023BFDA4FD21

2025-07-31 20:09:52:069 ==>> 8FACF05457E699226216444698CDC8C675D4661E3C7064
[D][05:18:55][CAT1]<<< 
SEND OK

[D][05:18:55][CAT1]exec over: func id: 15, ret: 11
[D][05:18:55][CAT1]sub id: 15, ret: 11

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:55][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:55][M2M ]g_m2m_is_idle become true
[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:55][PROT]M2M Send ok [1629955135]


2025-07-31 20:09:52:561 ==>> [D][05:18:56][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,25,,,41,59,,,41,14,,,41,1*79

$GBGSV,4,2,16,60,,,41,42,,,41,24,,,40,3,,,40,1*42

$GBGSV,4,3,16,39,,,39,13,,,38,1,,,37,40,,,37,1*4A

$GBGSV,4,4,16,38,,,37,2,,,36,41,,,35,4,,,34,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1606.482,1606.482,51.364,2097152,2097152,2097152*4A



2025-07-31 20:09:52:681 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:52:691 ==>> 定位已等待【5】秒.
2025-07-31 20:09:53:576 ==>> $GBGGA,120957.389,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,41,25,,,41,59,,,41,60,,,41,1*76

$GBGSV,5,2,18,14,,,40,42,,,40,24,,,40,3,,,40,1*4E

$GBGSV,5,3,18,39,,,39,13,,,38,1,,,37,40,,,37,1*45

$GBGSV,5,4,18,38,,,37,2,,,35,41,,,35,4,,,33,1*72

$GBGSV,5,5,18,5,,,39,10,,,38,1*4A

$GBRMC,120957.389,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120957.389,0.000,1593.529,1593.529,50.953,2097152,2097152,2097152*51



2025-07-31 20:09:53:682 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:53:692 ==>> 定位已等待【6】秒.
2025-07-31 20:09:53:787 ==>> $GBGGA,120957.589,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,25,,,41,59,,,41,60,,,41,1*74

$GBGSV,5,2,19,14,,,40,42,,,40,24,,,40,3,,,40,1*4F

$GBGSV,5,3,19,39,,,39,13,,,37,1,,,37,40,,,37,1*4B

$GBGSV,5,4,19,38,,,37,16,,,37,2,,,35,41,,,35,1*44

$GBGSV,5,5,19,4,,,33,5,,,39,6,,,37,1*47

$GBRMC,120957.589,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120957.589,0.000,1590.026,1590.026,50.843,2097152,2097152,2097152*57



2025-07-31 20:09:54:466 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 20:09:54:696 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:54:705 ==>> 定位已等待【7】秒.
2025-07-31 20:09:54:785 ==>> $GBGGA,120958.569,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,41,60,,,41,25,,,40,59,,,40,1*7F

$GBGSV,6,2,21,14,,,40,42,,,40,24,,,40,3,,,40,1*47

$GBGSV,6,3,21,39,,,38,1,,,38,13,,,37,40,,,37,1*4D

$GBGSV,6,4,21,38,,,37,16,,,37,2,,,36,6,,,35,1*7C

$GBGSV,6,5,21,41,,,35,9,,,35,4,,,33,8,,,33,1*46

$GBGSV,6,6,21,7,,,38,1*49

$GBRMC,120958.569,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120958.569,0.000,1560.885,1560.885,49.914,2097152,2097152,2097152*5D



2025-07-31 20:09:55:699 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:55:710 ==>> 定位已等待【8】秒.
2025-07-31 20:09:55:864 ==>> $GBGGA,120959.549,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,60,,,41,25,,,41,24,,,41,1*70

$GBGSV,6,2,24,59,,,40,14,,,40,42,,,40,3,,,40,1*48

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,37,40,,,37,1*49

$GBGSV,6,4,24,38,,,37,16,,,37,2,,,36,6,,,36,1*7A

$GBGSV,6,5,24,9,,,36,41,,,35,7,,,34,8,,,34,1*43

$GBGSV,6,6,24,4,,,33,10,,,31,5,,,30,12,,,27,1*77

$GBRMC,120959.549,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120959.549,0.000,1521.908,1521.908,48.729,2097152,2097152,2097152*5F



2025-07-31 20:09:56:461 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 20:09:56:704 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:56:714 ==>> 定位已等待【9】秒.
2025-07-31 20:09:56:749 ==>> $GBGGA,121000.529,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,60,,,41,25,,,41,24,,,41,1*73

$GBGSV,6,2,24,59,,,41,14,,,41,42,,,41,3,,,41,1*48

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,40,,,37,1*46

$GBGSV,6,4,24,38,,,37,16,,,37,2,,,36,6,,,36,1*7A

$GBGSV,6,5,24,9,,,36,41,,,35,8,,,35,7,,,34,1*42

$GBGSV,6,6,24,4,,,33,10,,,32,5,,,31,12,,,28,1*7A

$GBRMC,121000.529,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121000.529,0.000,1539.179,1539.179,49.277,2097152,2097152,2097152*52



2025-07-31 20:09:57:223 ==>> [D][05:19:00][PROT]CLEAN,SEND:0
[D][05:19:00][PROT]CLEAN:0
[D][05:19:00][PROT]index:2 1629955140
[D][05:19:00][PROT]is_send:0
[D][05:19:00][PROT]sequence_num:6
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:3
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:00][PROT]===========================================================
[W][05:19:00][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:19:00][PROT]sending traceid [9999999999900007]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[134]
[D][05:19:00][SAL ]Cellular task submsg id[10]
[D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][0

2025-07-31 20:09:57:298 ==>> 5:19:00][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE89F9FAC485CA7AB26C8016A8146A788BBF9AB515C6C471827906323AD67CDE067881A1F03632DB5403C1B3442E873FF000F0
[D][05:19:00][CAT1]<<< 
SEND OK

[D][05:19:00][CAT1]exec over: func id: 15, ret: 11
[D][05:19:00][CAT1]sub id: 15, ret: 11

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:00][M2M ]g_m2m_is_idle become true
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:00][PROT]M2M Send ok [1629955140]


2025-07-31 20:09:57:707 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:57:717 ==>> 定位已等待【10】秒.
2025-07-31 20:09:57:752 ==>> $GBGGA,121001.509,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,60,,,41,25,,,41,24,,,41,1*73

$GBGSV,6,2,24,14,,,41,42,,,41,3,,,41,59,,,40,1*49

$GBGSV,6,3,24,39,,,39,13,,,38,1,,,38,38,,,38,1*46

$GBGSV,6,4,24,40,,,37,16,,,37,2,,,36,6,,,36,1*75

$GBGSV,6,5,24,9,,,36,41,,,35,8,,,35,7,,,34,1*42

$GBGSV,6,6,24,4,,,33,10,,,32,5,,,32,12,,,29,1*78

$GBRMC,121001.509,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121001.509,0.000,769.884,769.884,704.079,2097152,2097152,2097152*63



2025-07-31 20:09:58:455 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 20:09:58:710 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:58:721 ==>> 定位已等待【11】秒.
2025-07-31 20:09:58:755 ==>> $GBGGA,121002.509,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,60,,,41,3,,,41,24,,,41,1*47

$GBGSV,7,2,25,42,,,41,14,,,41,25,,,41,59,,,40,1*7D

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,9,,,36,1*7A

$GBGSV,7,5,25,6,,,36,8,,,35,7,,,35,41,,,35,1*4C

$GBGSV,7,6,25,5,,,33,10,,,33,4,,,33,26,,,32,1*75

$GBGSV,7,7,25,12,,,29,1*79

$GBRMC,121002.509,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121002.509,0.000,767.226,767.226,701.648,2097152,2097152,2097152*61



2025-07-31 20:09:59:717 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:09:59:727 ==>> 定位已等待【12】秒.
2025-07-31 20:09:59:747 ==>> $GBGGA,121003.509,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,41,59,,,41,24,,,41,1*4D

$GBGSV,7,2,25,25,,,41,60,,,40,42,,,40,14,,,40,1*77

$GBGSV,7,3,25,39,,,39,13,,,38,1,,,38,38,,,37,1*49

$GBGSV,7,4,25,40,,,37,16,,,37,9,,,36,6,,,36,1*7E

$GBGSV,7,5,25,2,,,35,8,,,35,41,,,35,7,,,34,1*4A

$GBGSV,7,6,25,26,,,33,5,,,33,10,,,33,4,,,33,1*74

$GBGSV,7,7,25,12,,,29,1*79

$GBRMC,121003.509,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121003.509,0.000,764.739,764.739,699.373,2097152,2097152,2097152*6D



2025-07-31 20:10:00:203 ==>> [D][05:19:03][COMM]IMU: [14,0,-1008] ret=21 AWAKE!


2025-07-31 20:10:00:476 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 20:10:00:719 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:00:729 ==>> 定位已等待【13】秒.
2025-07-31 20:10:00:749 ==>> $GBGGA,121004.509,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,60,,,41,33,,,41,3,,,40,59,,,40,1*4E

$GBGSV,7,2,25,24,,,40,42,,,40,14,,,40,25,,,40,1*76

$GBGSV,7,3,25,39,,,39,1,,,38,13,,,37,38,,,37,1*46

$GBGSV,7,4,25,40,,,37,16,,,37,6,,,36,2,,,35,1*76

$GBGSV,7,5,25,8,,,35,9,,,35,41,,,35,7,,,34,1*41

$GBGSV,7,6,25,26,,,34,10,,,33,5,,,33,4,,,33,1*73

$GBGSV,7,7,25,12,,,29,1*79

$GBRMC,121004.509,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121004.509,0.000,760.590,760.590,695.578,2097152,2097152,2097152*6B



2025-07-31 20:10:01:725 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:01:734 ==>> 定位已等待【14】秒.
2025-07-31 20:10:01:755 ==>> $GBGGA,121005.509,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,3,,,41,14,,,41,33,,,41,60,,,40,1*46

$GBGSV,7,2,25,59,,,40,24,,,40,42,,,40,25,,,40,1*7F

$GBGSV,7,3,25,39,,,39,13,,,38,38,,,37,40,,,37,1*73

$GBGSV,7,4,25,1,,,37,16,,,37,6,,,36,2,,,35,1*43

$GBGSV,7,5,25,8,,,35,9,,,35,41,,,35,7,,,34,1*41

$GBGSV,7,6,25,26,,,34,10,,,33,5,,,33,4,,,33,1*73

$GBGSV,7,7,25,12,,,30,1*71

$GBRMC,121005.509,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121005.509,0.000,762.243,762.243,697.089,2097152,2097152,2097152*63



2025-07-31 20:10:02:469 ==>> [D][05:19:05][PROT]CLEAN,SEND:2
[D][05:19:05][PROT]index:2 1629955145
[D][05:19:05][PROT]is_send:0
[D][05:19:05][PROT]sequence_num:6
[D][05:19:05][PROT]retry_timeout:0
[D][05:19:05][PROT]retry_times:2
[D][05:19:05][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:05][PROT]===========================================================
[W][05:19:05][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:19:05][PROT]sending traceid [9999999999900007]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[134]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][05:19:05][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:05][CAT1]Send Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BEBFE9249314D6

2025-07-31 20:10:02:559 ==>> 640D28E6FB32AC0DBFD626BE205B5BAB153744A3378CC8B148577B2DACDBADA549362E981EFFE0939EDFE678
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:05][PROT]M2M Send ok [1629955145]
                                         

2025-07-31 20:10:02:725 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:02:735 ==>> 定位已等待【15】秒.
2025-07-31 20:10:02:770 ==>> $GBGGA,121002.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,60,,,41,14,,,41,25,,,41,33,,,41,1*73

$GBGSV,7,2,25,3,,,40,59,,,40,24,,,40,42,,,40,1*4B

$GBGSV,7,3,25,13,,,38,1,,,38,39,,,38,38,,,37,1*48

$GBGSV,7,4,25,40,,,37,16,,,37,2,,,36,8,,,35,1*78

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,41,,,35,1*7D

$GBGSV,7,6,25,7,,,34,10,,,33,5,,,33,4,,,33,1*40

$GBGSV,7,7,25,12,,,30,1*71

$GBRMC,121002.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121002.516,4.645,763.898,763.898,698.603,2097152,2097152,2097152*62



2025-07-31 20:10:03:736 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:03:749 ==>> 定位已等待【16】秒.
2025-07-31 20:10:03:796 ==>> $GBGGA,121003.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,40,42,54,159,40,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,40,39,52,2,38,6,51,30,35,1,48,126,38,1*41

$GBGSV,7,3,25,2,46,238,36,60,41,238,40,25,38,268,41,9,38,313,35,1*77

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,40,41,,,35,1*4D

$GBGSV,7,7,25,12,,,29,1*79

$GBRMC,121003.516,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,121003.516,0.000,0.229,0.210,0.375,4.550,6.644,29*5C



2025-07-31 20:10:04:482 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 20:10:04:744 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:04:764 ==>> 定位已等待【17】秒.
2025-07-31 20:10:04:819 ==>> $GBGGA,121004.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,41,3,61,191,40,42,54,159,40,16,52,18,37,1*76

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,35,1,48,126,38,1*40

$GBGSV,7,3,25,2,46,238,35,60,41,238,40,25,38,268,41,9,38,313,35,1*74

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,40,41,,,35,1*4D

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,42,42,54,159,39,39,52,2,40,25,38,268,39,5*7F

$GBRMC,121004.516,V,,,,,,,310725,1.0,E,N,V*49

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,121004.516,1.192,0.274,0.247,0.455,2.377,3.311,15*55



2025-07-31 20:10:05:751 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:05:782 ==>> 定位已等待【18】秒.
2025-07-31 20:10:05:796 ==>> $GBGGA,121005.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,40,42,54,159,40,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,35,1,48,126,38,1*40

$GBGSV,7,3,25,2,46,238,35,60,41,238,40,25,38,268,41,9,38,313,35,1*74

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,37,1*70

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,40,41,,,35,1*4D

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,43,42,54,159,41,39,52,2,41,25,38,268,40,5*7E

$GBRMC,121005.516,V,,,,,,,310725,1.0,E,N,V*48

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,121005.516,0.993,0.417,0.370,0.704,1.649,2.314,11*56



2025-07-31 20:10:06:498 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 20:10:06:755 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:06:765 ==>> 定位已等待【19】秒.
2025-07-31 20:10:06:800 ==>> $GBGGA,121006.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,41,42,54,159,41,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,41,39,52,2,39,6,51,30,35,1,48,126,37,1*4E

$GBGSV,7,3,25,2,46,238,35,60,41,238,40,25,38,268,41,9,38,313,35,1*74

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,40,41,,,35,1*4D

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,43,42,54,159,42,39,52,2,41,25,38,268,40,5*7D

$GBRMC,121006.516,V,,,,,,,310725,1.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,121006.516,0.618,0.291,0.264,0.472,1.079,1.649,9.001*7A



2025-07-31 20:10:07:640 ==>> [D][05:19:11][PROT]CLEAN,SEND:2
[D][05:19:11][PROT]index:2 1629955151
[D][05:19:11][PROT]is_send:0
[D][05:19:11][PROT]sequence_num:6
[D][05:19:11][PROT]retry_timeout:0
[D][05:19:11][PROT]retry_times:1
[D][05:19:11][PROT]send_path:0x2
[D][05:19:11][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:11][PROT]===========================================================
[W][05:19:11][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955151]
[D][05:19:11][PROT]===========================================================
[D][05:19:11][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A7C89C8906980220
[D][05:19:11][PROT]sending traceid [9999999999900007]
[D][05:19:11][PROT]Send_TO_M2M [1629955151]
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:11][SAL ]sock send credit cnt[6]
[D][05:19:11][SAL ]sock send ind credit cnt[6]
[D][05:19:11][M2M ]m2m send data len[134]
[D][05:19:11][SAL ]Cellular task submsg id[10]
[D][05:19:11][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:11][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:11][CAT1]gsm read msg sub id: 15
[D][05:19:11][CAT1]tx ret[17] >>> AT+QISEND=0,134

2025-07-31 20:10:07:670 ==>> 

[D][05:19:11][CAT1]<<< 
ERROR



2025-07-31 20:10:07:760 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:07:769 ==>> 定位已等待【20】秒.
2025-07-31 20:10:07:780 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                              5,22,257,33,1*72

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,41,41,,,35,1*4C

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,43,42,54,159,42,39,52,2,41,25,38,268,40,5*7D

$GBRMC,121007.516,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGS

2025-07-31 20:10:07:806 ==>> T,121007.516,0.741,0.262,0.240,0.423,1.002,1.462,7.831*7A



2025-07-31 20:10:08:503 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 20:10:08:773 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:08:783 ==>> 定位已等待【21】秒.
2025-07-31 20:10:08:807 ==>> $GBGGA,121008.516,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,40,42,54,159,41,16,52,18,37,1*74

$GBGSV,7,2,25,59,52,129,41,39,52,2,39,6,51,30,36,1,48,126,38,1*42

$GBGSV,7,3,25,2,46,238,36,60,41,238,40,25,38,268,41,9,38,313,36,1*74

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,41,41,,,35,1*4C

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,44,42,54,159,43,39,52,2,41,25,38,268,40,5*7B

$GBRMC,121008.516,V,,,,,,,310725,1.0,E,N,V*45

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,121008.516,0.707,0.202,0.190,0.312,0.856,1.255,6.973*76



2025-07-31 20:10:09:778 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:09:788 ==>> 定位已等待【22】秒.
2025-07-31 20:10:09:823 ==>> $GBGGA,121009.516,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,40,42,54,159,41,16,52,18,37,1*74

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,36,1,48,126,38,1*43

$GBGSV,7,3,25,2,46,238,36,60,41,238,40,25,38,268,41,9,38,313,36,1*74

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,41,41,,,35,1*4C

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,43,42,54,159,43,39,52,2,41,25,38,268,40,5*7C

$GBRMC,121009.516,V,,,,,,,310725,1.0,E,N,V*44

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,121009.516,0.764,0.235,0.217,0.374,0.827,1.171,6.368*79



2025-07-31 20:10:10:507 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 20:10:10:792 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:10:801 ==>> 定位已等待【23】秒.
2025-07-31 20:10:10:813 ==>> $GBGGA,121010.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,41,42,54,159,41,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,41,39,52,2,39,6,51,30,36,1,48,126,38,1*42

$GBGSV,7,3,25,2,46,238,36,60,41,238,41,25,38,268,41,9,38,313,36,1*75

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,36,38,28,201,37,10,26,193,33,5,22,257,33,1*7E

$GBGSV,7,6,25,26,18,227,35,24,,,41,14,,,41,41,,,35,1*4C

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,44,42,54,159,43,39,52,2,41,25,38,268,40,5*7B

$GBRMC,121010.516,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,N*21

$GBGST,121010.516,0.675,0.295,0.269,0.477,0.683,1.000,5.847*75



2025-07-31 20:10:11:804 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:11:815 ==>> 定位已等待【24】秒.
2025-07-31 20:10:11:837 ==>> $GBGGA,121011.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,41,42,54,159,41,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,36,1,48,126,38,1*43

$GBGSV,7,3,25,2,46,238,36,60,41,238,41,25,38,268,41,9,38,313,36,1*75

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,37,10,26,193,33,5,22,257,33,1*7D

$GBGSV,7,6,25,26,19,227,35,24,,,41,14,,,41,41,,,35,1*4D

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,1,1,04,33,63,308,44,42,54,159,43,39,52,2,41,25,38,268,40,5*7B

$GBRMC,121011.516,V,,,,,,,310725,1.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.003,N,0.005,K,N*26

$GBGST,121011.516,0.855,0.329,0.298,0.539,0.805,1.072,5.502*72



2025-07-31 20:10:12:521 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 20:10:12:810 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:12:821 ==>> 定位已等待【25】秒.
2025-07-31 20:10:12:844 ==>> $GBGGA,121012.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,41,42,54,159,41,16,52,18,37,1*75

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,36,1,48,126,38,1*43

$GBGSV,7,3,25,2,46,238,36,60,41,238,41,25,38,268,41,9,38,313,36,1*75

$GBGSV,7,4,25,40,37,169,37,7,36,185,35,4,32,112,33,13,31,211,38,1*7E

$GBGSV,7,5,25,8,28,205,35,38,28,201,38,10,26,193,33,5,22,257,33,1*72

$GBGSV,7,6,25,26,19,227,35,24,,,41,14,,,41,41,,,35,1*4D

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,1,1,04,33,63,308,43,42,54,159,43,39,52,2,41,25,38,268,40,5*7C

$GBRMC,121012.516,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,121012.516,0.883,0.222,0.206,0.354,0.796,1.037,5.190*71



2025-07-31 20:10:13:803 ==>> $GBGGA,121013.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,63,308,42,3,61,191,41,42,54,159,40,16,52,18,37,1*74

$GBGSV,7,2,25,59,52,129,40,39,52,2,39,6,51,30,36,1,48,126,38,1*43

$GBGSV,7,3,25,2,46,238,36,60,41,238,41,25,38,268,41,9,38,313,35,1*76

$GBGSV,7,4,25,40,37,169,37,7,36,185,34,4,32,112,33,13,31,211,38,1*7F

$GBGSV,7,5,25,8,28,205,35,38,28,201,38,10,26,193,33,5,22,257,33,1*72

$GBGSV,7,6,25,26,19,227,35,24,,,41,14,,,40,41,,,34,1*4D

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,1,1,04,33,63,308,43,42,54,159,43,39,52,2,41,25,38,268,40,5*7C

$GBRMC,121013.516,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121013.516,1.005,0.189,0.178,0.293,0.877,1.089,4.959*7C



2025-07-31 20:10:13:826 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:13:837 ==>> 定位已等待【26】秒.
2025-07-31 20:10:14:529 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 20:10:14:819 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:10:14:830 ==>> 定位已等待【27】秒.
2025-07-31 20:10:15:422 ==>> $GBGGA,121014.516,2301.2583960,N,11421.9416826,E,1,13,1.27,69.971,M,-1.770,M,,*53

$GBGSA,A,3,33,42,06,16,39,09,13,08,25,38,07,40,3.29,1.27,3.04,4*02

$GBGSA,A,3,10,,,,,,,,,,,,3.29,1.27,3.04,4*02

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,61,191,41,24,58,0,41,1*40

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,41,1*42

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,36,60,41,238,41,25,38,268,41,1*7D

$GBGSV,7,5,25,38,34,191,38,7,34,175,34,4,32,112,33,40,29,160,37,1*72

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,227,35,41,14,323,35,1*41

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,1,1,04,33,63,308,43,42,54,159,43,39,52,2,41,25,38,268,40,5*7C

$GBRMC,121014.516,A,2301.2583960,N,11421.9416826,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:19:18][GNSS]HD8040 GPS
[D][05:19:18][GNSS]GPS diff_sec 124008656, report 0x42 frame
$GBGST,121014.516,1.206,0.265,0.239,0.396,1.015,1.188,4.522*74

[D][05:19:18][COMM]Main Task receive event:131
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:1

2025-07-31 20:10:15:526 ==>> 8][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xF

2025-07-31 20:10:15:630 ==>> F
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF


2025-07-31 20:10:15:736 ==>> 
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experimen

2025-07-31 20:10:15:826 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 20:10:15:839 ==>> 
北斗星号:【14】,信号值:【41】
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【41】
北斗星号:【24】,信号值:【41】
北斗星号:【42】,信号值:【43】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【37】
北斗星号:【59】,信号值:【41】
北斗星号:【39】,信号值:【41】
北斗星号:【9】,信号值:【36】
北斗星号:【13】,信号值:【38】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【36】
北斗星号:【8】,信号值:【36】
北斗星号:【60】,信号值:【41】
北斗星号:【25】,信号值:【40】
北斗星号:【38】,信号值:【38】
北斗星号:【40】,信号值:【37】
北斗星号:【26】,信号值:【35】
北斗星号:【41】,信号值:【35】

2025-07-31 20:10:15:851 ==>> 检测【CSQ强度】
2025-07-31 20:10:15:873 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:10:15:888 ==>> t8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][COMM]Main Task receive event:131 finished processing
[D][05:19:18][M2M ]m2m_task: control_queue type:[

2025-07-31 20:10:15:947 ==>> M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,121015.016,2301.2583233,N,11421.9415855,E,1,16,1.11,71.060,M,-1.770,M,,*5D

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,61,191,41,24,58,0,41,1*40

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,41,1*42

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,35,60,41,238,41,25,38,268,41,1*7E

$GBGSV,7,5,25,38,34,191,38,7,34,175,34,4,32,112,33,40,29,160,37,1*72

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,227,35,41,14,323,35,1*41

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,2,1,07,33,63,308,44,24,58,0,41,42,54,159,43,39,52,2,41,5*71

$GBGSV,2,2,07,25,38,268,40,38,34,191,36,40,29,160,34,5*4E

$GBRMC,121015.016,A,2301.2583233,N,11421.9415855,E,0.003,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,121015.016,1.362,0.409,0.319,0.488,1.108,1.252,3.941*73



2025-07-31 20:10:16:036 ==>> [W][05:19:19][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:10:16:387 ==>> $GBGGA,121016.000,2301.2582784,N,11421.9415017,E,1,16,1.11,71.480,M,-1.770,M,,*55

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,40,33,63,308,42,3,61,191,41,24,58,0,41,1*41

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,41,1*42

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,36,60,41,238,41,25,38,268,41,1*7D

$GBGSV,7,5,25,38,34,191,38,7,34,175,35,4,32,112,33,40,29,160,38,1*7C

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,227,35,41,14,323,35,1*41

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,2,1,08,33,63,308,43,24,58,0,42,42,54,159,43,39,52,2,41,5*7A

$GBGSV,2,2,08,25,38,268,40,38,34,191,36,40,29,160,34,41,14,323,32,5*72

$GBRMC,121016.000,A,2301.2582784,N,11421.9415017,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,121016.000,1.225,0.495,0.378,0.582,0.974,1.112,3.567*71



2025-07-31 20:10:16:523 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 20:10:17:368 ==>> $GBGGA,121017.000,2301.2582396,N,11421.9414526,E,1,16,1.11,71.941,M,-1.770,M,,*55

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,40,33,63,308,42,3,61,191,40,24,58,0,40,1*41

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,40,1*43

$GBGSV,7,3,25,39,52,2,38,9,49,315,36,13,48,220,38,1,48,126,38,1*71

$GBGSV,7,4,25,2,46,238,35,8,44,206,35,60,41,238,41,25,38,268,41,1*7D

$GBGSV,7,5,25,38,34,191,37,7,34,175,34,4,32,112,33,40,29,160,37,1*7D

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,227,35,41,14,323,34,1*40

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,2,1,08,33,63,308,44,24,58,0,43,42,54,159,43,39,52,2,41,5*7C

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121017.000,A,2301.2582396,N,11421.9414526,E,0.000,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,121017.000,1.338,0.227,0.195,0.286,1.052,1.176,3.404*7A



2025-07-31 20:10:17:684 ==>> [D][05:19:21][CAT1]exec over: func id: 15, ret: -93
[D][05:19:21][CAT1]sub id: 15, ret: -93

[D][05:19:21][SAL ]Cellular task submsg id[68]
[D][05:19:21][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:21][SAL ]socket send fail. id[4]
[D][05:19:21][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:21][M2M ]m2m select fd[4]
[D][05:19:21][M2M ]socket[4] Link is disconnected
[D][05:19:21][M2M ]tcpclient close[4]
[D][05:19:21][SAL ]socket[4] has closed
[D][05:19:21][PROT]protocol read data ok
[E][05:19:21][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:21][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[E][05:19:21][PROT]M2M Send Fail [1629955161]
[D][05:19:21][PROT]CLEAN,SEND:2
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:21][PROT]CLEAN:2
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:21][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 21
[D][05:19:21][CAT1]gsm read msg sub id: 10
[D][05:19:21][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:21][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:21][CAT1]tx ret[12] >>>

2025-07-31 20:10:17:714 ==>>  AT+CGATT=0



2025-07-31 20:10:18:000 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 20:10:18:007 ==>> [D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 10, ret: 6
[D][05:19:21][CAT1]sub id: 10, ret: 6

[D][05:19:21][SAL ]Cellular task submsg id[68]
[D][05:19:21][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:21][M2M ]m2m gsm shut done, ret[0]
[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:21][SAL ]open socket ind id[4], rst[0]
[D][05:19:21][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:21][SAL ]Cellular task submsg id[8]
[D][05:19:21][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:21][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:21][CAT1]gsm read msg sub id: 8
[D][05:19:21][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:21][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:21][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:21][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:10:18:019 ==>> 检测【关闭GSM联网】
2025-07-31 20:10:18:032 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:10:18:380 ==>> [D][05:19:21][CAT1]pdpdeact urc len[22]
$GBGGA,121018.000,2301.2582179,N,11421.9414037,E,1,16,1.11,72.262,M,-1.770,M,,*55

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,61,191,40,24,58,0,41,1*41

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,40,1*43

$GBGSV,7,3,25,39,52,2,38,9,49,315,36,13,48,220,38,1,48,126,38,1*71

$GBGSV,7,4,25,2,46,238,36,8,44,206,35,60,41,238,41,25,38,268,41,1*7E

$GBGSV,7,5,25,38,34,191,37,7,34,175,34,4,32,112,34,40,29,160,37,1*7A

$GBGSV,7,6,25,10,26,186,33,5,22,257,33,26,19,227,35,41,14,323,34,1*47

$GBGSV,7,7,25,12,,,30,1*71

$GBGSV,2,1,08,33,63,308,43,24,58,0,43,42,54,159,43,39,52,2,41,5*7B

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121018.000,A,2301.2582179,N,11421.9414037,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,121018.000,1.399,0.215,0.187,0.274,1.089,1.202,3.255*75

[W][05:19:21][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:21][COMM]GSM test
[D][05:19:21][COMM]GSM test disable


2025-07-31 20:10:18:515 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 20:10:18:534 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:10:18:546 ==>> 检测【4G联网测试】
2025-07-31 20:10:18:569 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:10:19:548 ==>> [W][05:19:22][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:22][COMM]Main Task receive event:14
[D][05:19:22][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955162, allstateRepSeconds = 0
[D][05:19:22][COMM]index:0,power_mode:0xFF
[D][05:19:22][COMM]index:1,sound_mode:0xFF
[D][05:19:22][COMM]index:2,gsensor_mode:0xFF
[D][05:19:22][COMM]index:3,report_freq_mode:0xFF
[D][05:19:22][COMM]index:4,report_period:0xFF
[D][05:19:22][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:22][COMM]index:6,normal_reset_period:0xFF
[D][05:19:22][COMM]index:7,spock_over_speed:0xFF
[D][05:19:22][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:22][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:22][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:22][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:22][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:22][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:22][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:22][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:22][COMM]index:16,imu_config_params:0xFF
[D][05:19:22][COMM]index:17,long_connect_params:0xFF
[D][05:19:22][COMM]index:18,detain_mark:0xFF
[D][05:19:22][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:22][COMM]index:20,lock_pos_report_interval:0xFF
[

2025-07-31 20:10:19:653 ==>> D][05:19:22][COMM]index:21,mc_mode:0xFF
[D][05:19:22][COMM]index:22,S_mode:0xFF
[D][05:19:22][COMM]index:23,overweight:0xFF
[D][05:19:22][COMM]index:24,standstill_mode:0xFF
[D][05:19:22][COMM]index:25,night_mode:0xFF
[D][05:19:22][COMM]index:26,experiment1:0xFF
[D][05:19:22][COMM]index:27,experiment2:0xFF
[D][05:19:22][COMM]index:28,experiment3:0xFF
[D][05:19:22][COMM]index:29,experiment4:0xFF
[D][05:19:22][COMM]index:30,night_mode_start:0xFF
[D][05:19:22][COMM]index:31,night_mode_end:0xFF
[D][05:19:22][COMM]index:33,park_report_minutes:0xFF
[D][05:19:22][COMM]index:34,park_report_mode:0xFF
[D][05:19:22][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:22][COMM]index:38,charge_battery_para: FF
[D][05:19:22][COMM]index:39,multirider_mode:0xFF
[D][05:19:22][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:22][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:22][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:22][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:22][COMM]index:44,riding_duration_config:0xFF
[D][05:19:22][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:22][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:22][COMM]index:47,bat_inf

2025-07-31 20:10:19:758 ==>> o_rep_cfg:0xFF
[D][05:19:22][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:22][COMM]index:49,mc_load_startup:0xFF
[D][05:19:22][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:22][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:22][COMM]index:52,traffic_mode:0xFF
[D][05:19:22][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:22][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:22][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:22][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:22][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:22][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:22][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:22][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:22][COMM]index:63,experiment5:0xFF
[D][05:19:22][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:22][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:22][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:22][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:22][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:22][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:22][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:22][COMM]index:

2025-07-31 20:10:19:863 ==>> 72,experiment6:0xFF
[D][05:19:22][COMM]index:73,experiment7:0xFF
[D][05:19:22][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:22][COMM]index:75,zero_value_from_server:-1
[D][05:19:22][COMM]index:76,multirider_threshold:255
[D][05:19:22][COMM]index:77,experiment8:255
[D][05:19:22][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:22][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:22][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:22][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:22][COMM]index:83,loc_report_interval:255
[D][05:19:22][COMM]index:84,multirider_threshold_p2:255
[D][05:19:22][COMM]index:85,multirider_strategy:255
[D][05:19:22][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:22][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:22][COMM]index:90,weight_param:0xFF
[D][05:19:22][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:22][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:22][COMM]index:95,current_limit:0xFF
[D][05:19:22][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:22][COMM]index:100,location_mode:0xFF

[W][05:19:22][PROT]remove success[1629955162

2025-07-31 20:10:19:969 ==>> ],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:22][PROT]add success [1629955162],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]tx ret[11] >>> AT+CGATT?

$GBGGA,121019.000,2301.2581884,N,11421.9413750,E,1,16,1.11,72.496,M,-1.770,M,,*50

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,40,33,63,308,42,3,61,191,40,24,58,0,40,1*41

$GBGSV,7,2,25,42,54,159,40,6,53,338,35,16,53,342,37,59,52,129,40,1*41

$GBGSV,7,3,25,39,52,2,38,9,49,315,35,13,48,220,37,1,48,126,38,1*7D

$GBGSV,7,4,25,2,46,238,35,8,44,206,35,60,41,238,41,25,38,268,40,1*7C

$GBGSV,7,5,25,38,34,191,37,7,34,175,34,4,32,112,33,40,29,160,37,1*7D

$GBGSV,7,6,25,10,26,186,33,5,22,257,33,26,19,227,35,41,14,323,34,1*47

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,2,1,08,33,63,308,43,24,58,0,43,42,54,159,43,39,52,2,41,5*7B

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121019.000,A,2301.2581884,N,11421.9413750,E,0.0

2025-07-31 20:10:20:074 ==>> 01,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,121019.000,1.222,0.215,0.188,0.274,0.931,1.044,3.014*76

[D][05:19:22][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:22][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:22][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:22][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:23][CAT1]<<< 
OK

[D][05:19:23][CAT1]exec over: func id: 8, ret: 6
[D][05:19:23][CAT1]gsm read msg sub id: 13
[D][05:19:23][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:23][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:23][CAT1]exec over: func id: 13, ret: 21
[D][05:19:23][M2M ]get csq[23]
                                                                                                                                                                                                                       

2025-07-31 20:10:20:179 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:10:20:284 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:10:20:389 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:10:20:419 ==>>                                                                                                                                                         

2025-07-31 20:10:20:569 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 20:10:21:387 ==>> $GBGGA,121021.000,2301.2581684,N,11421.9413517,E,1,16,1.11,72.956,M,-1.770,M,,*55

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,40,33,63,308,42,3,61,191,40,24,58,0,41,1*40

$GBGSV,7,2,25,42,54,159,40,6,53,338,36,16,53,342,37,59,52,129,40,1*42

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,35,60,41,238,41,25,38,268,41,1*7E

$GBGSV,7,5,25,38,34,191,37,7,34,175,35,4,32,112,34,40,29,160,37,1*7B

$GBGSV,7,6,25,10,26,186,33,5,22,257,33,26,19,228,35,41,14,323,34,1*48

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,2,1,08,33,63,308,43,24,58,0,44,42,54,159,43,39,52,2,41,5*7C

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121021.000,A,2301.2581684,N,11421.9413517,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,121021.000,1.164,0.247,0.209,0.308,0.864,0.967,2.754*71



2025-07-31 20:10:22:385 ==>> $GBGGA,121022.000,2301.2581668,N,11421.9413452,E,1,16,1.11,73.050,M,-1.770,M,,*5A

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,61,191,41,24,58,0,41,1*40

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,59,52,129,41,1*42

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,36,60,41,238,41,25,38,268,41,1*7D

$GBGSV,7,5,25,38,34,191,37,7,34,175,34,4,32,112,34,40,29,160,37,1*7A

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,228,35,41,14,323,34,1*4F

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,2,1,08,33,63,308,44,24,58,0,44,42,54,159,43,39,52,2,41,5*7B

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121022.000,A,2301.2581668,N,11421.9413452,E,0.004,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,121022.000,1.228,0.290,0.241,0.355,0.913,1.009,2.695*7A



2025-07-31 20:10:22:565 ==>> [D][05:19:26][COMM]read battery soc:255


2025-07-31 20:10:23:384 ==>> $GBGGA,121023.000,2301.2581616,N,11421.9413315,E,1,16,1.11,73.052,M,-1.770,M,,*54

$GBGSA,A,3,14,33,24,42,06,16,39,09,13,08,25,38,1.83,1.11,1.46,4*01

$GBGSA,A,3,07,40,10,41,,,,,,,,,1.83,1.11,1.46,4*07

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,61,191,41,24,58,0,41,1*40

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,38,59,52,129,41,1*4D

$GBGSV,7,3,25,39,52,2,39,9,49,315,36,13,48,220,38,1,48,126,38,1*70

$GBGSV,7,4,25,2,46,238,36,8,44,206,36,60,41,238,41,25,38,268,41,1*7D

$GBGSV,7,5,25,38,34,191,38,7,34,175,35,4,32,112,34,40,29,160,38,1*7B

$GBGSV,7,6,25,10,26,186,33,5,22,257,33,26,19,228,35,41,14,323,35,1*49

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,2,1,08,33,63,308,43,24,58,0,44,42,54,159,43,39,52,2,41,5*7C

$GBGSV,2,2,08,25,38,268,40,38,34,191,37,40,29,160,34,41,14,323,32,5*73

$GBRMC,121023.000,A,2301.2581616,N,11421.9413315,E,0.003,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,121023.000,1.244,0.244,0.208,0.305,0.920,1.011,2.611*75



2025-07-31 20:10:24:391 ==>> $GBGGA,121024.000,2301.2581557,N,11421.9413204,E,1,21,0.80,73.190,M,-1.770,M,,*56

$GBGSA,A,3,14,33,03,24,42,06,16,39,59,09,13,02,1.58,0.80,1.37,4*01

$GBGSA,A,3,01,08,60,25,38,07,40,10,41,,,,1.58,0.80,1.37,4*0D

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,63,190,41,24,58,0,41,1*43

$GBGSV,7,2,25,42,54,159,41,6,53,338,36,16,53,342,37,39,52,2,39,1*43

$GBGSV,7,3,25,59,51,128,41,9,49,315,36,13,48,220,38,2,48,240,36,1*7D

$GBGSV,7,4,25,1,46,124,38,8,44,206,36,60,43,241,41,25,38,268,41,1*72

$GBGSV,7,5,25,38,34,191,38,7,34,175,35,4,32,112,33,40,29,160,38,1*7C

$GBGSV,7,6,25,10,26,186,33,5,22,257,34,26,19,228,35,41,14,323,34,1*4F

$GBGSV,7,7,25,12,,,29,1*79

$GBGSV,2,1,08,33,63,308,43,24,58,0,44,42,54,159,43,39,52,2,41,5*7C

$GBGSV,2,2,08,25,38,268,40,38,34,191,36,40,29,160,34,41,14,323,33,5*73

$GBRMC,121024.000,A,2301.2581557,N,11421.9413204,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,121024.000,1.709,0.244,0.222,0.329,1.287,1.362,2.793*73



2025-07-31 20:10:24:588 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:10:24:600 ==>> [D][05:19:28][COMM]read battery soc:255


2025-07-31 20:10:25:882 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:28][COMM]Main Task receive event:14
[D][05:19:28][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955168, allstateRepSeconds = 0
[D][05:19:28][COMM]index:0,power_mode:0xFF
[D][05:19:28][COMM]index:1,sound_mode:0xFF
[D][05:19:28][COMM]index:2,gsensor_mode:0xFF
[D][05:19:28][COMM]index:3,report_freq_mode:0xFF
[D][05:19:28][COMM]index:4,report_period:0xFF
[D][05:19:28][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:28][COMM]index:6,normal_reset_period:0xFF
[D][05:19:28][COMM]index:7,spock_over_speed:0xFF
[D][05:19:28][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:28][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:28][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:28][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:28][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:28][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:28][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:28][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:28][COMM]index:16,imu_config_params:0xFF
[D][05:19:28][COMM]index:17,long_connect_params:0xFF
[D][05:19:28][COMM]index:18,detain_mark:0xFF
[D][05:19:28][COMM]index:19,lock_pos_

2025-07-31 20:10:25:987 ==>> report_count:0xFF
[D][05:19:28][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:28][COMM]index:21,mc_mode:0xFF
[D][05:19:28][COMM]index:22,S_mode:0xFF
[D][05:19:28][COMM]index:23,overweight:0xFF
[D][05:19:28][COMM]index:24,standstill_mode:0xFF
[D][05:19:28][COMM]index:25,night_mode:0xFF
[D][05:19:28][COMM]index:26,experiment1:0xFF
[D][05:19:28][COMM]index:27,experiment2:0xFF
[D][05:19:28][COMM]index:28,experiment3:0xFF
[D][05:19:28][COMM]index:29,experiment4:0xFF
[D][05:19:28][COMM]index:30,night_mode_start:0xFF
[D][05:19:28][COMM]index:31,night_mode_end:0xFF
[D][05:19:28][COMM]index:33,park_report_minutes:0xFF
[D][05:19:28][COMM]index:34,park_report_mode:0xFF
[D][05:19:28][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:28][COMM]index:38,charge_battery_para: FF
[D][05:19:28][COMM]index:39,multirider_mode:0xFF
[D][05:19:28][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:28][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:28][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:28][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:28][COMM]index:44,riding_duration_config:0xFF
[D][05:19:28][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:28][COMM]

2025-07-31 20:10:26:092 ==>> index:46,camera_park_type_cfg:0xFF
[D][05:19:28][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:28][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:28][COMM]index:49,mc_load_startup:0xFF
[D][05:19:28][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:28][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:28][COMM]index:52,traffic_mode:0xFF
[D][05:19:28][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:28][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:28][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:28][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:28][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:28][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:28][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:28][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:28][COMM]index:63,experiment5:0xFF
[D][05:19:28][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:28][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:28][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:28][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:28][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:28][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:28][CO

2025-07-31 20:10:26:197 ==>> MM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:28][COMM]index:72,experiment6:0xFF
[D][05:19:28][COMM]index:73,experiment7:0xFF
[D][05:19:28][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:28][COMM]index:75,zero_value_from_server:-1
[D][05:19:28][COMM]index:76,multirider_threshold:255
[D][05:19:28][COMM]index:77,experiment8:255
[D][05:19:28][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:28][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:28][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:28][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:28][COMM]index:83,loc_report_interval:255
[D][05:19:28][COMM]index:84,multirider_threshold_p2:255
[D][05:19:28][COMM]index:85,multirider_strategy:255
[D][05:19:28][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:28][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:28][COMM]index:90,weight_param:0xFF
[D][05:19:28][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:28][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:28][COMM]index:95,current_limit:0xFF
[D][05:19:28][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:28][COMM]in

2025-07-31 20:10:26:302 ==>> dex:100,location_mode:0xFF

[D][05:19:28][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:19:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:28][PROT]remove success[1629955168],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:28][PROT]index:0 1629955168
[D][05:19:28][PROT]is_send:0
[D][05:19:28][PROT]sequence_num:10
[D][05:19:28][PROT]retry_timeout:0
[D][05:19:28][PROT]retry_times:1
[D][05:19:28][PROT]send_path:0x2
[D][05:19:28][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:28][PROT]===========================================================
[W][05:19:28][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955168]
[D][05:19:28][PROT]===========================================================
[D][05:19:28][PROT]sending traceid [999999999990000B]
[D][05:19:28][PROT]Send_TO_M2M [1629955168]
[W][05:19:28][PROT]add success [1629955168],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:28][CAT1]gsm read msg sub id: 13
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:28][SAL ]sock send credit cnt[6]
[D][05:19:28][SAL ]sock send ind credit cnt[6]
[D][

2025-07-31 20:10:26:407 ==>> 05:19:28][M2M ]m2m send data len[294]
[D][05:19:28][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:28][SAL ]Cellular task submsg id[10]
[D][05:19:28][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de8] format[0]
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:28][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:28][CAT1]exec over: func id: 13, ret: 21
[D][05:19:28][M2M ]get csq[23]
[D][05:19:28][CAT1]gsm read msg sub id: 15
[D][05:19:28][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:28][CAT1]Send Data To Server[294][294] ... ->:
0093B987113311331133113311331B88BD9253D269BFDC753ACDA0C9E095BB77644C2B081B195077849B5B5F47EE0A07656998E4EA28317F8AD8042AD8B46F954C89F68969A14F622B1A8521FD3F4B3C0C584C1F3ACCEE249E8DE9113A2378B99F2C21AF057971633590D9E3B7B579258E14B4EDE568F336C832D4EC52309B79776B5D1A70F32E25AA00C669824371AE097B39
[D][05:19:28][CAT1]<<< 
SEND OK

[D][05:19:28][CAT1]exec over: func id: 15, ret: 11
[D][05:19:28][CAT1]sub id: 15, ret: 11

[D][05:19:28][SAL ]Cellular task submsg id[68]
[D][05:19:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:28][M2M ]g_m2m_is_idle become true
[D

2025-07-31 20:10:26:512 ==>> ][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:28][PROT]M2M Send ok [1629955168]
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:28][PROT]remove success[1629955168],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:28][PROT]add success [1629955168],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:28][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:28][COMM][LOC]wifi scan is already running, error
[D][05:19:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:28][COMM]Main Task receive event:14 finished processing
$GBGGA,121025.000,2301.2581403,N,11421.9413193,E,1,21,0.80,73.298,M,-1.770,M,,*51

$GBGSA,A,3,14,33,03,24,42,06,16,39,59,09,13,02,1.58,0.80,1.37,4*01

$GBGSA,A,3,01,08,60,25,38,07,40,10,41,,,,1.58,0.80,1.37,4*0D

$GBGSV,7,1,25,14,80,212,41,33,63,308,42,3,63,190,41,2

2025-07-31 20:10:26:617 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 20:10:26:637 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:10:26:647 ==>> 检测【关闭GPS】
2025-07-31 20:10:26:655 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:10:26:682 ==>>                                                                                                                                                                                                                                                                                                       [D][05:19:30][COMM]read battery soc:255


2025-07-31 20:10:26:999 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:30][GNSS]stop locating
[D][05:19:30][GNSS]stop event:8
[D][05:19:30][GNSS]GPS stop. ret=0
[D][05:19:30][GNSS]all continue location stop
[W][05:19:30][GNSS]stop locating
[D][05:19:30][GNSS]all sing location stop
[D][05:19:30][CAT1]gsm read msg sub id: 24
[D][05:19:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 24, ret: 6
[D][05:19:30][CAT1]sub id: 24, ret: 6



2025-07-31 20:10:27:173 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:10:27:180 ==>> 检测【清空消息队列2】
2025-07-31 20:10:27:194 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:10:27:305 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:30][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:10:27:444 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:10:27:452 ==>> 检测【轮动检测】
2025-07-31 20:10:27:462 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:10:27:485 ==>> [D][05:19:31][GNSS]recv submsg id[1]
[D][05:19:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:31][GNSS]location stop evt done evt


2025-07-31 20:10:27:545 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:10:27:620 ==>> [D][05:19:31][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:10:27:959 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:10:28:037 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:10:28:236 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:10:28:246 ==>> 检测【关闭小电池】
2025-07-31 20:10:28:260 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:10:28:337 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:10:28:506 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:10:28:513 ==>> 检测【进入休眠模式】
2025-07-31 20:10:28:526 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:10:28:609 ==>> [D][05:19:32][COMM]read battery soc:255


2025-07-31 20:10:28:698 ==>> [W][05:19:32][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:10:28:803 ==>> [D][05:19:32][COMM]Main Task receive event:28
[D][05:19:32][COMM]main task tmp_sleep_event = 8
[D][05:19:32][COMM]prepare to sleep
[D][05:19:32][CAT1]gsm read msg sub id: 12
[D][05:19:32][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:10:29:664 ==>> [D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 12, ret: 6
[D][05:19:33][M2M ]tcpclient close[4]
[D][05:19:33][SAL ]Cellular task submsg id[12]
[D][05:19:33][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:33][CAT1]gsm read msg sub id: 9
[D][05:19:33][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 9, ret: 6
[D][05:19:33][CAT1]sub id: 9, ret: 6

[D][05:19:33][SAL ]Cellular task submsg id[68]
[D][05:19:33][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:33][SAL ]socket close ind. id[4]
[D][05:19:33][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:33][COMM]1x1 frm_can_tp_send ok
[D][05:19:33][CAT1]pdpdeact urc len[22]


2025-07-31 20:10:29:939 ==>> [E][05:19:33][COMM]1x1 rx timeout
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:10:30:462 ==>> [E][05:19:34][COMM]1x1 rx timeout
[E][05:19:34][COMM]1x1 tp timeout
[D][05:19:34][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:34][COMM]1x1 error -3.
[W][05:19:34][COMM]CAN STOP!
[D][05:19:34][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:34][COMM]------------ready to Power off Acckey 1------------
[D][05:19:34][COMM]------------ready to Power off Acckey 2------------
[D][05:19:34][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:34][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1297
[D][05:19:34][COMM]bat sleep fail, reason:-1
[D][05:19:34][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:34][COMM]accel parse set 0
[D][05:19:34][COMM]imu rest ok. 105043
[D][05:19:34][COMM]imu sleep 0
[W][05:19:34][COMM]now sleep


2025-07-31 20:10:30:582 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:10:30:595 ==>> 检测【检测33V休眠电流】
2025-07-31 20:10:30:620 ==>> 开始33V电流采样
2025-07-31 20:10:30:635 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:30:688 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:10:31:701 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:10:31:762 ==>> Current33V:????:17.75

2025-07-31 20:10:32:209 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:32:217 ==>> 【检测33V休眠电流】通过,【17.75uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:10:32:230 ==>> 该项需要延时执行
2025-07-31 20:10:34:224 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:10:34:237 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:10:34:266 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:10:34:348 ==>> 1A A1 00 00 FC 
Get AD_V2 1671mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2754mV
Get AD_V6 2022mV
Get AD_V7 1090mV
OVER 150


2025-07-31 20:10:35:258 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:10:35:271 ==>> 检测【打开小电池2】
2025-07-31 20:10:35:295 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:10:35:347 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:10:35:539 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:10:35:548 ==>> 该项需要延时执行
2025-07-31 20:10:36:052 ==>> 此处延时了:【500】毫秒
2025-07-31 20:10:36:066 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:10:36:094 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:10:36:145 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:10:36:331 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:10:36:342 ==>> 该项需要延时执行
2025-07-31 20:10:36:842 ==>> 此处延时了:【500】毫秒
2025-07-31 20:10:36:856 ==>> 检测【进入休眠模式2】
2025-07-31 20:10:36:879 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:10:36:897 ==>> [D][05:19:40][COMM]------------ready to Power on Acckey 1------------
[D][05:19:40][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:40][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:40][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:40][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:40][COMM]----- get Acckey 1 and value:1------------
[W][05:19:40][COMM]CAN START!
[D][05:19:40][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:40][COMM]1x1 frm_can_tp_send ok
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:40][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 111344
[D][05:19:40][C

2025-07-31 20:10:36:912 ==>> OMM][Audio]exec status ready.
[D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 12, ret: 6
[D][05:19:40][COMM]imu wakeup ok. 111358
[D][05:19:40][COMM]imu wakeup 1
[W][05:19:40][COMM]wake up system, wakeupEvt=0x80
[D][05:19:40][COMM]frm_can_weigth_power_set 1
[D][05:19:40][COMM]Clear Sleep Block Evt
[D][05:19:40][COMM]Main Task receive event:28 finished processing


2025-07-31 20:10:37:238 ==>> [W][05:19:40][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:40][COMM]1x1 rx timeout
[D][05:19:40][COMM]1x1 frm_can_tp_send ok
[D][05:19:40][COMM]Main Task receive event:28
[D][05:19:40][COMM]prepare to sleep
[D][05:19:40][CAT1]gsm read msg sub id: 12
[D][05:19:40][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:40][CAT1]<<< 
OK

[D][05:19:40][CAT1]exec over: func id: 12, ret: 6
[W][05:19:40][COMM]CAN STOP!
[D][05:19:40][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:40][COMM]------------ready to Power off Acckey 1------------
[D][05:19:40][COMM]------------ready to Power off Acckey 2------------
[D][05:19:40][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:40][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 127
[D][05:19:40][COMM]bat sleep fail, reason:-1
[D][05:19:40][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:40][COMM]accel parse set 0
[D][05:19:40][COMM]imu rest ok. 111804
[D][05:19:40][COMM]imu sleep 0
[W][05:19:40][COMM]now sleep


2025-07-31 20:10:37:377 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:10:37:388 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:10:37:396 ==>> 开始小电池电流采样
2025-07-31 20:10:37:413 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:37:478 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:10:38:492 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:10:38:569 ==>> CurrentBattery:ƽ��:69.57

2025-07-31 20:10:38:996 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:39:006 ==>> 【检测小电池休眠电流】通过,【69.57uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:10:39:016 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:10:39:033 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:10:39:135 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:10:39:289 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:10:39:297 ==>> 该项需要延时执行
2025-07-31 20:10:39:423 ==>> [D][05:19:42][COMM]------------ready to Power on Acckey 1------------
[D][05:19:42][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:42][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 14
[D][05:19:42][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:42][COMM]----- get Acckey 1 and value:1------------
[W][05:19:42][COMM]CAN START!
[E][05:19:42][COMM]1x1 rx timeout
[E][05:19:42][COMM]1x1 tp timeout
[E][05:19:42][COMM]1x1 error -3.
[D][05:19:42][CAT1]gsm read msg sub id: 12
[D][05:19:42][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:42][COMM][Audio]exec status ready.
[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 12, ret: 6
[D][05:19:42][COMM]imu wakeup ok. 113936
[D][05:19:42][COMM]imu wakeup 1
[W][05:19:42][COMM]wake up system, wakeupEvt=0x80
[D][05:19:42][COMM]frm_can_weigth_power_set 1
[D][05:19:42][COMM]Clear Sleep Block Evt
[D][05:19:42][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:42][COMM]1x1 frm_can_tp_send ok
[D][05:19:42][COMM]read battery soc:0
[D][05:19:43][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0

2025-07-31 20:10:39:452 ==>> ,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 20:10:39:647 ==>> [E][05:19:43][COMM]1x1 rx timeout
[D][05:19:43][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:10:39:752 ==>> [D][05:19:43][COMM]msg 02A0 loss. last_tick:113904. cur_tick:114415. period:50
[D][05:19:43][COMM]msg 02A4 loss. last_tick:113904. cur_tick:114416. period:50
[D][05:19:43][COMM]msg 02A5 loss. last

2025-07-31 20:10:39:797 ==>> 此处延时了:【500】毫秒
2025-07-31 20:10:39:810 ==>> 检测【检测唤醒】
2025-07-31 20:10:39:832 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:10:39:846 ==>> _tick:113905. cur_tick:114416. period:50
[D][05:19:43][COMM]msg 02A6 loss. last_tick:113905. cur_tick:114417. period:50
[D][05:19:43][COMM]msg 02A7 loss. last_tick:113905. cur_tick:114417. period:50
[D][05:19:43][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 114417
[D][05:19:43][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 114418


2025-07-31 20:10:40:225 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:43][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:43][FCTY]==========Modules-nRF5340 ==========
[D][05:19:43][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:43][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:43][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:43][FCTY]DeviceID    = 460130071539078
[D][05:19:43][FCTY]HardwareID  = 867222087783504
[D][05:19:43][FCTY]MoBikeID    = 9999999999
[D][05:19:43][FCTY]LockID      = FFFFFFFFFF
[D][05:19:43][FCTY]BLEFWVersion= 105
[D][05:19:43][FCTY]BLEMacAddr   = D308A0C65ACC
[D][05:19:43][FCTY]Bat         = 3884 mv
[D][05:19:43][FCTY]Current     = 0 ma
[D][05:19:43][FCTY]VBUS        = 2600 mv
[D][05:19:43][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:43][FCTY]Ext battery vol = 32, adc = 1300
[D][05:19:43][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 0 mv
[D][05:19:43][FCTY]Bike Type flag is invalied
[D][05:19:43][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:43][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:43][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:43][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:43][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:43][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:43][FCTY]Bat

2025-07-31 20:10:40:270 ==>> 1         = 3791 mv
[D][05:19:43][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:43][FCTY]==========Modules-nRF5340 ==========
[E][05:19:43][COMM]1x1 rx timeout
[E][05:19:43][COMM]1x1 tp timeout
[E][05:19:43][COMM]1x1 error -3.
[D][05:19:43][COMM]Main Task receive event:28 finished processing


2025-07-31 20:10:40:334 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:10:40:344 ==>> 检测【关机】
2025-07-31 20:10:40:353 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:10:40:375 ==>>                                                                                                                                                                                                                                         

2025-07-31 20:10:40:480 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:10:40:540 ==>>                      period:100. j,i:5 58
[D][05:19:43][COMM]bat msg 024E loss. last_tick:113905. cur_tick:114919. period:100. j,i:15 68
[D][05:19:43][COMM]bat msg 024F loss. last_tick:113905. cur_tick:114920. period:100. j,i:16 69
[D][05:19:43][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 114921
[D][05:19:43][COMM]CAN message bat fault change: 0x00000000->0x0001802E 114921
[D][05:19:43][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 114921


2025-07-31 20:10:40:630 ==>> [D][05:19:44][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]


2025-07-31 20:10:40:735 ==>> [W][05:19:44][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:44][COMM]arm_hub_enable: hub power: 0
[D][05:19:44][HSDK]hexlog index save 0 3584 1 @ 0 : 0
[D][05:19:44][HSDK]write save hexlog index [0]
[D][05:19:44][FCT

2025-07-31 20:10:40:765 ==>> Y]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:44][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:10:40:825 ==>>                                                                                                                                                 ->0x0000E00C71E22217 115426


2025-07-31 20:10:40:990 ==>> [D][05:19:44][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:44][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:0------------
[D][05:19:44][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:10:41:359 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:10:41:828 ==>>                                     vice_poweron type 16.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1------------
[D][05:19:44][COMM]more than the number of battery plugs
[D][05:19:44][COMM]VBUS is 1
[D][05:19:44][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:44][COMM]Main Task receive event:65
[D][05:19:44][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:44][COMM]Main Task receive event:65 finished processing
[D][05:19:44][COMM]file:B50 exist
[D][05:19:44][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:44][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:44][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:44][COMM]Bat auth off fail, error:-1
[D][05:19:44][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1------------
[D][05:19:44][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:44][COMM]----- get Acckey 1 and value:1------------
[D][05:19:44][COMM]----- get Acckey 2 and value:1------------
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:44][COMM]file:B50 e

2025-07-31 20:10:41:933 ==>> xist
[D][05:19:44][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:44][COMM]read file, len:10800, num:3
[D][05:19:44][COMM]Main Task receive event:66
[D][05:19:44][COMM]Try to Auto Lock Bat
[D][05:19:44][COMM]Main Task receive event:66 finished processing
[D][05:19:44][COMM]Main Task receive event:60
[D][05:19:44][COMM]smart_helmet_vol=255,255
[D][05:19:44][COMM]BAT CAN get state1 Fail 204
[D][05:19:44][COMM]BAT CAN get soc Fail, 204
[D][05:19:44][COMM]BAT CAN get state2 fail 204
[D][05:19:44][COMM]get soh error
[E][05:19:44][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:44][COMM]report elecbike
[W][05:19:44][PROT]remove success[1629955184],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:44][PROT]add success [1629955184],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:44][COMM]Main Task receive event:60 finished processing
[D][05:19:44][COMM]Main Task receive event:61
[D][05:19:44][COMM][D301]:type:3, trace id:280
[D][05:19:44][COMM]id[], hw[000
[D][05:19:44][COMM]get mcMaincircuitVolt error
[D][05:19:44][PROT]min_index:0, type:0x5

2025-07-31 20:10:42:038 ==>> D03, priority:4
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][PROT]index:0
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][PROT]is_send:1
[D][05:19:44][PROT]sequence_num:12
[D][05:19:44][PROT]retry_timeout:0
[D][05:19:44][PROT]retry_times:3
[D][05:19:44][PROT]send_path:0x3
[D][05:19:44][PROT]msg_type:0x5d03
[D][05:19:44][PROT]===========================================================
[W][05:19:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955184]
[D][05:19:44][PROT]===========================================================
[D][05:19:44][PROT]Sending traceid[999999999990000D]
[D][05:19:44][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:44][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:44][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:44][COMM]Receive Bat Lock cmd 0
[D][05:19:44][COMM]VBUS is 1
[D][05:19:44][COMM]get mcSubcircuitVolt error
[D][05:19:44][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:44][COMM]BAT CAN get state1 Fail 204
[D][05:19:44][COMM]BAT CAN get soc Fail, 204
[D][05:19:44][COMM]BatVolt[0], Curren

2025-07-31 20:10:42:143 ==>> t[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:44][COMM]BAT CAN get state2 fail 204
[D][05:19:44][COMM]get bat work mode err
[W][05:19:44][PROT]remove success[1629955184],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:19:44][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:44][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:44][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:44][SAL ]open socket ind id[4], rst[0]
[D][05:19:44][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:44][SAL ]Cellular task submsg id[8]
[D][05:19:44][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:44][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:19:44][PROT]add success [1629955184],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:44][COMM]Main Task receive event:61 finished processing
[D][05:19:44][CAT1]gsm read msg sub id: 8
[D][05:19:44][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:44][COMM]--->crc16:0xb8a
[D][05:19:44][COMM]

2025-07-31 20:10:42:248 ==>> read file success
[D][05:19:44][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:44][COMM]accel parse set 1
[D][05:19:44][COMM][Audio]mon:9,05:19:44
[D][05:19:44][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:44][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:44][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:44][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:44][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:44][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:44][CAT1]<<< 
+CME ERROR

2025-07-31 20:10:42:353 ==>> : 100

[D][05:19:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:44][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:44][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:45][COMM]Power Off
[D][05:19:45][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:45][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:45][COMM]read battery soc:255
[D][05:19:45][COMM]f:[ec800m_audio_play_

2025-07-31 20:10:42:402 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:10:42:420 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:10:42:449 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:10:42:458 ==>> process].l:[991]. send ret: 0
[D][05:19:45][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:45][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:45][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:45][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:45][COMM]arm_hub_enable: hub power: 0
[D][05:19:45][HSDK]hexlog index save 0 3584 1 @ 0 : 0
[D][05:19:45][HSDK]write save hexlog index [0]
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:45][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
                              

2025-07-31 20:10:42:548 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:10:42:672 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:10:42:683 ==>> 检测【检测小电池关机电流】
2025-07-31 20:10:42:705 ==>> 开始小电池电流采样
2025-07-31 20:10:42:715 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:42:730 ==>> [D][05:19:46][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:46][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:10:42:773 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:10:43:784 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:10:43:829 ==>> CurrentBattery:ƽ��:67.43

2025-07-31 20:10:44:298 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:10:44:307 ==>> 【检测小电池关机电流】通过,【67.43uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:10:44:658 ==>> MES过站成功
2025-07-31 20:10:44:668 ==>> #################### 【测试结束】 ####################
2025-07-31 20:10:44:707 ==>> 关闭5V供电
2025-07-31 20:10:44:722 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:10:44:844 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:10:45:717 ==>> 关闭5V供电成功
2025-07-31 20:10:45:737 ==>> 关闭33V供电
2025-07-31 20:10:45:758 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:10:45:837 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:10:46:732 ==>> 关闭33V供电成功
2025-07-31 20:10:46:747 ==>> 关闭3.7V供电
2025-07-31 20:10:46:770 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:10:46:841 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:10:47:608 ==>>  

2025-07-31 20:10:47:743 ==>> 关闭3.7V供电成功
