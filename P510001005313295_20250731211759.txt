2025-07-31 21:17:59:329 ==>> MES查站成功:
查站序号:P510001005313295验证通过
2025-07-31 21:17:59:337 ==>> 扫码结果:P510001005313295
2025-07-31 21:17:59:339 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:17:59:340 ==>> 测试参数版本:2024.10.11
2025-07-31 21:17:59:342 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:17:59:359 ==>> 检测【打开透传】
2025-07-31 21:17:59:361 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:17:59:467 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:17:59:637 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:17:59:703 ==>> 检测【检测接地电压】
2025-07-31 21:17:59:706 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:17:59:774 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:17:59:987 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:17:59:989 ==>> 检测【打开小电池】
2025-07-31 21:17:59:992 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:18:00:075 ==>> 6A A6 01 A6 6A 


2025-07-31 21:18:00:165 ==>> Battery ON
OVER 150


2025-07-31 21:18:00:275 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:18:00:286 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:18:00:288 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:18:00:375 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:18:00:582 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:18:00:584 ==>> 检测【等待设备启动】
2025-07-31 21:18:00:586 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:18:00:952 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:18:01:133 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim

2025-07-31 21:18:01:613 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:18:01:628 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:18:01:810 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:18:02:532 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:18:02:653 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:18:02:910 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:18:03:381 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:18:03:460 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:18:03:463 ==>> 检测【产品通信】
2025-07-31 21:18:03:465 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:18:03:637 ==>> AT+PWD=6789


2025-07-31 21:18:04:104 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:18:04:299 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:18:04:480 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:18:05:522 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:18:05:597 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]frm CAN read mc pwr mode invalid,val:254
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]app

2025-07-31 21:18:05:702 ==>> BledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]netcore_ver 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917V1.1_b1e

2025-07-31 21:18:05:807 ==>> 44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA_BOOT_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:17:49][FCTY]Bat         = 3764 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][COMM][CHG]ext_48v_vol:0, disable charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 0
[D][05:17:49][FCTY]Acckey1 vol = 5588 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied


2025-07-31 21:18:05:813 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:18:05:819 ==>> 检测【初始化完成检测】
2025-07-31 21:18:05:825 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:18:05:912 ==>> 
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]CAT1_GNSS_VERSION =
[D][05:17:49][FCTY]Bat1         = 3717 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi scan.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][CAT1]gsm read msg sub 

2025-07-31 21:18:06:017 ==>> id: 1
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:50][COMM]msg 0220 loss. last_tick:0. cur_tick:1019. period:100
[D][05:17:50][COMM]msg 0221 loss. last_tick:0. cur_tick:1019. period:100
[D][05:17:50][COMM]msg 0224 loss. last_tick:0. cur_tick:1019. period:100
[D][05:17:50][COMM]msg 0260 loss. last_tick:0. cur_tick:1020. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1020. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1020. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1021. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1021. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1022. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1022. period:100
[D][05:17:50][COMM]msg 02E2 loss. last_tick:0. cur_tick:1022. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1023. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1023. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick:0. cur_tick:1023. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:

2025-07-31 21:18:06:107 ==>> 1024. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1024. period:100. j,i:3 56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1024. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1025. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1025. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1026
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1026
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1026
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:18:06:437 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 21:18:06:542 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 21:18:06:647 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:18:06:752 ==>>                                                                                                                                                                                                                                                                                                                                                                                           ][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg

2025-07-31 21:18:06:842 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:18:06:857 ==>> :0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Ac

2025-07-31 21:18:06:899 ==>> ckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 21:18:06:992 ==>>                                                                      

2025-07-31 21:18:07:082 ==>>                                                                                          [W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:18:07:130 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:18:07:132 ==>> 检测【关闭大灯控制1】
2025-07-31 21:18:07:135 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:18:07:340 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:18:07:415 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:18:07:419 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:18:07:422 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:18:07:567 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:18:07:708 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:18:07:711 ==>> 检测【关闭仪表供电】
2025-07-31 21:18:07:715 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:18:07:867 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:18:07:942 ==>>                                      [D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:08:008 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:18:08:011 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:18:08:014 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:18:08:136 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:18:08:309 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:18:08:311 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:18:08:314 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:18:08:458 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:18:08:609 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:18:08:612 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:18:08:614 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:18:08:732 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:18:08:902 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:18:08:905 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:18:08:907 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:18:08:972 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:18:09:032 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 22


2025-07-31 21:18:09:107 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 21:18:09:206 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:18:09:209 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:18:09:211 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:18:09:273 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:18:09:499 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5020. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5021. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5021. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5021. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5022. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5022. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5023. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5023. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5023. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5024. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5024. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5025. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5025. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5025
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5026


2025-07-31 21:18:09:514 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:18:09:516 ==>> 该项需要延时执行
2025-07-31 21:18:09:952 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:10:639 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:18:11:189 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[10

2025-07-31 21:18:11:294 ==>> 21].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:

2025-07-31 21:18:11:399 ==>> 17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][

2025-07-31 21:18:11:474 ==>> COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][CAT1]power_urc_cb ret[5]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:18:11:975 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:12:992 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:13:127 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:18:13:527 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:18:13:531 ==>> 检测【33V输入电压ADC】
2025-07-31 21:18:13:534 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:13:887 ==>> [D][05:17:58][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3169  volt:5570 mv
[D][05:17:58][COMM]adc read out 24v adc:1318  volt:33336 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:8  volt:10 mv
[D][05:17:58][COMM]adc read throttle adc:5  volt:6 mv
[D][05:17:58][COMM]adc read battery ts volt:7 mv
[D][05:17:58][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:18:13:993 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:14:070 ==>> 【33V输入电压ADC】通过,【32552mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:18:14:074 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:18:14:077 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:18:14:187 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2789mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:18:14:357 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:14:364 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:18:14:370 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10010. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10011. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10011. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10012
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10012


2025-07-31 21:18:14:404 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:14:407 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:18:14:409 ==>> 原始值:【2789】, 乘以分压基数【2】还原值:【5578】
2025-07-31 21:18:14:466 ==>> 【TP68_VCC5V5(ADV5)】通过,【5578mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:18:14:469 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:18:14:509 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:18:14:511 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:18:14:557 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:18:14:559 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:18:14:683 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2789mV
Get AD_V6 1990mV
Get AD_V7 1096mV
OVER 150


2025-07-31 21:18:14:773 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:18:14:856 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:14:858 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:18:14:889 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:14:894 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:18:14:898 ==>> 原始值:【2789】, 乘以分压基数【2】还原值:【5578】
2025-07-31 21:18:14:923 ==>> 【TP68_VCC5V5(ADV5)】通过,【5578mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:18:14:927 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:18:14:969 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:18:14:972 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:18:15:078 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:18:15:081 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:18:15:208 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> A

2025-07-31 21:18:15:268 ==>> T+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2789mV
Get AD_V6 1992mV
Get AD_V7 1098mV
OVER 150


2025-07-31 21:18:15:383 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:15:386 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:18:15:418 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:15:424 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:18:15:429 ==>> 原始值:【2789】, 乘以分压基数【2】还原值:【5578】
2025-07-31 21:18:15:452 ==>> 【TP68_VCC5V5(ADV5)】通过,【5578mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:18:15:456 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:18:15:486 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:18:15:489 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:18:15:525 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:18:15:528 ==>> 检测【打开WIFI(1)】
2025-07-31 21:18:15:532 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:18:15:688 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087744068

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539160

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:18:15:778 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:18:16:019 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:18:16:093 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:18:16:097 ==>> 检测【清空消息队列(1)】
2025-07-31 21:18:16:101 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:18:16:259 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:18:16:383 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:18:16:388 ==>> 检测【打开GPS(1)】
2025-07-31 21:18:16:392 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:18:16:642 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:18:16:935 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:18:16:938 ==>> 检测【打开GSM联网】
2025-07-31 21:18:16:940 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:18:17:166 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:18:17:234 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:18:17:237 ==>> 检测【打开仪表供电1】
2025-07-31 21:18:17:249 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:18:17:472 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:18:17:518 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:18:17:522 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:18:17:527 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:18:17:667 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:18:17:818 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:18:17:822 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:18:17:825 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:18:18:030 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33340]
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][COMM]13729 imu init OK
[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:18:18:112 ==>> 【读取主控ADC采集的仪表电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:18:18:115 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:18:18:118 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:18:18:289 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:18:18:411 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:18:18:416 ==>> 检测【AD_V20电压】
2025-07-31 21:18:18:420 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:18:18:518 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:18:18:578 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:18:18:729 ==>> 本次取值间隔时间:204ms
2025-07-31 21:18:18:764 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:18:18:833 ==>>                            

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:18:18:878 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:18:18:938 ==>>                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:18:19:028 ==>>                                                                                                                                                                                                                                                                                                                                                                                   .70.34.117"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 21:18:19:118 ==>> 本次取值间隔时间:237ms
2025-07-31 21:18:19:156 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:19:159 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:18:19:163 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:18:19:299 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:18:19:456 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:18:19:460 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:18:19:464 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:18:19:681 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:18:19:746 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:18:19:749 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:18:19:754 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:18:19:876 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:18:19:937 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:18:20:032 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:18:20:036 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:18:20:038 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:18:20:285 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:18:20:574 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:18:20:577 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:18:20:579 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:18:20:618 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:18:20:858 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,43,25,,,42,59,,,42,39,,,41,1*77

$GBGSV,3,2,11,40,,,40,60,,,40,41,,,40,34,,,33,1*73

[D][05:18:05][CAT1]<<< 
OK

$GBGSV,3,3,11,38,,,33,24,,,20,16,,,46,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1550.731,1550.731,49.793,2097152,2097152,2097152*4F

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:18:21:009 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:18:21:121 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:18:21:124 ==>> 检测【AD_V21电压】
2025-07-31 21:18:21:127 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:18:21:174 ==>> [D][05:18:05][COMM]read battery soc:255
1A A1 20 00 00 
Get AD_V21 1026mV
OVER 150


2025-07-31 21:18:21:598 ==>> 本次取值间隔时间:472ms
2025-07-31 21:18:21:741 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:18:21:748 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,44,25,,,42,59,,,42,60,,,41,1*7C

$GBGSV,5,2,17,24,,,41,39,,,40,40,,,40,41,,,40,1*7B

$GBGSV,5,3,17,14,,,39,2,,,39,16,,,38,7,,,38,1*71

$GBGSV,5,4,17,1,,,37,9,,,37,44,,,36,34,,,35,1*7D

$GBGSV,5,5,17,38,,,34,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.855,1616.855,51.704,2097152,2097152,2097152*48



2025-07-31 21:18:21:853 ==>> 1A A1 20 00 00 
Get AD_V21 1644mV
OVER 150


2025-07-31 21:18:21:943 ==>> 本次取值间隔时间:194ms
2025-07-31 21:18:22:052 ==>> 【AD_V21电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:22:055 ==>> 检测【关闭仪表供电2】
2025-07-31 21:18:22:058 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:18:22:265 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:18:22:421 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:18:22:425 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:18:22:427 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:18:22:789 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!
$GBGGA,131826.596,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,44,25,,,42,59,,,42,60,,,41,1*7A

$GBGSV,6,2,21,24,,,41,3,,,41,39,,,40,40,,,40,1*4A

$GBGSV,6,3,21,41,,,40,14,,,39,16,,,38,7,,,38,1*4E

$GBGSV,6,4,21,1,,,38,2,,,37,9,,,36,44,,,36,1*42

$GBGSV,6,5,21,34,,,36,38,,,35,5,,,35,23,,,35,1*4D

$GBGSV,6,6,21,4,,,33,1*41

$GBRMC,131826.596,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131826.596,0.000,1593.178,1593.178,50.960,2097152,2097152,2097152*5E



2025-07-31 21:18:22:989 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:18:22:992 ==>> 检测【打开AccKey2供电】
2025-07-31 21:18:22:996 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:18:23:182 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:18:23:291 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:18:23:295 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:18:23:297 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:23:576 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:08][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:18:08][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:08][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:08][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:08][COMM]adc read battery ts volt:7 mv
[D][05:18:08][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:08][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:18:23:772 ==>> $GBGGA,131827.576,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,44,25,,,42,59,,,41,60,,,41,1*7C

$GBGSV,7,2,25,24,,,41,3,,,41,39,,,40,40,,,40,1*4F

$GBGSV,7,3,25,41,,,40,14,,,39,16,,,38,7,,,38,1*4B

$GBGSV,7,4,25,1,,,38,44,,,37,34,,,37,2,,,36,1*78

$GBGSV,7,5,25,9,,,36,6,,,36,38,,,35,23,,,35,1*76

$GBGSV,7,6,25,5,,,34,4,,,34,10,,,34,8,,,39,1*45

$GBGSV,7,7,25,13,,,37,1*77

$GBRMC,131827.576,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131827.576,0.000,1580.817,1580.817,50.564,2097152,2097152,2097152*59



2025-07-31 21:18:23:838 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33285mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:18:23:841 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:18:23:844 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:18:24:047 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:18:24:132 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:18:24:135 ==>> 该项需要延时执行
2025-07-31 21:18:24:758 ==>> $GBGGA,131828.556,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,44,25,,,42,59,,,41,60,,,41,1*7F

$GBGSV,7,2,26,24,,,41,3,,,41,39,,,40,41,,,40,1*4D

$GBGSV,7,3,26,40,,,39,14,,,39,16,,,38,7,,,38,1*47

$GBGSV,7,4,26,1,,,38,34,,,38,44,,,37,9,,,37,1*7E

$GBGSV,7,5,26,2,,,36,6,,,36,23,,,36,13,,,35,1*74

$GBGSV,7,6,26,38,,,35,5,,,34,4,,,34,10,,,34,1*79

$GBGSV,7,7,26,12,,,34,11,,,31,1*74

$GBRMC,131828.556,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131828.556,0.000,1561.072,1561.072,49.946,2097152,2097152,2097152*50



2025-07-31 21:18:25:172 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:18:25:743 ==>> $GBGGA,131829.536,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,59,,,41,60,,,41,1*7E

$GBGSV,7,2,27,24,,,41,3,,,41,39,,,40,41,,,40,1*4C

$GBGSV,7,3,27,40,,,40,14,,,39,16,,,38,7,,,38,1*48

$GBGSV,7,4,27,1,,,38,34,,,38,44,,,37,9,,,37,1*7F

$GBGSV,7,5,27,6,,,37,2,,,36,23,,,36,13,,,36,1*77

$GBGSV,7,6,27,38,,,36,10,,,35,5,,,34,4,,,34,1*7A

$GBGSV,7,7,27,12,,,34,8,,,33,11,,,31,1*4D

$GBRMC,131829.536,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131829.536,0.000,1561.603,1561.603,49.962,2097152,2097152,2097152*51



2025-07-31 21:18:26:741 ==>> $GBGGA,131830.516,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,59,,,41,60,,,41,1*7E

$GBGSV,7,2,27,24,,,41,3,,,41,39,,,40,41,,,40,1*4C

$GBGSV,7,3,27,40,,,40,14,,,39,1,,,39,16,,,38,1*4F

$GBGSV,7,4,27,7,,,38,34,,,38,9,,,37,6,,,37,1*4F

$GBGSV,7,5,27,44,,,36,2,,,36,23,,,36,13,,,36,1*40

$GBGSV,7,6,27,38,,,36,10,,,35,5,,,34,4,,,34,1*7A

$GBGSV,7,7,27,12,,,34,8,,,33,11,,,31,1*4D

$GBRMC,131830.516,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131830.516,0.000,1561.604,1561.604,49.964,2097152,2097152,2097152*5D



2025-07-31 21:18:27:143 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:18:27:148 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:18:27:153 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:27:188 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:18:27:474 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3169  volt:5570 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:12][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:12][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:12][COMM]adc read battery ts volt:6 mv
[D][05:18:12][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:18:27:688 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:18:27:693 ==>> 检测【打开AccKey1供电】
2025-07-31 21:18:27:698 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:18:27:748 ==>> $GBGGA,131831.516,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,59,,,41,60,,,41,1*7E

$GBGSV,7,2,27,24,,,41,3,,,41,39,,,40,41,,,40,1*4C

$GBGSV,7,3,27,40,,,40,14,,,39,1,,,39,16,,,39,1*4E

$GBGSV,7,4,27,7,,,38,34,,,38,9,,,37,6,,,37,1*4F

$GBGSV,7,5,27,44,,,37,2,,,37,23,,,36,13,,,36,1*40

$GBGSV,7,6,27,38,,,36,10,,,35,5,,,34,4,,,34,1*7A

$GBGSV,7,7,27,12,,,34,8,,,33,11,,,31,1*4D

$GBRMC,131831.516,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131831.516,0.000,781.644,781.644,714.831,2097152,2097152,2097152*62



2025-07-31 21:18:27:854 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:18:27:980 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:18:27:986 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:18:27:991 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:18:28:078 ==>> 1A A1 00 40 00 
Get AD_V14 2568mV
OVER 150


2025-07-31 21:18:28:244 ==>> 原始值:【2568】, 乘以分压基数【2】还原值:【5136】
2025-07-31 21:18:28:275 ==>> 【读取AccKey1电压(ADV14)前】通过,【5136mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:18:28:279 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:18:28:284 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:28:592 ==>> [D][05:18:13][COMM]IMU: [10,1,-930] ret=22 AWAKE!
[W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:13][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:13][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:13][COMM]adc read battery ts volt:7 mv
[D][05:18:13][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:13][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:18:28:697 ==>>                                                                                              ,,44,25,,,42,60,,,41,3,,,41,1*41

$GBGSV,7,2,27,59,,,41,24,,,41,40,,,40,39,,,40,1*72

$GBGSV,7,3,27,41,,,40,1,,,39,16,,,39,14,,,39,1*4F

$GBGSV,7,4,27,7,,,38,34,,,38,2,,,37,44,,,37,1*72

$GBGSV,7,5,27,9,,,37,6,,,37,13,,,36,38,,,36,1*77

$GBGSV,7,6,27,23,,,36,

2025-07-31 21:18:28:742 ==>> 10,,,35,5,,,34,12,,,34,1*47

$GBGSV,7,7,27,4,,,34,8,,,33,11,,,31,1*7A

$GBRMC,131832.516,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131832.516,0.000,781.644,781.644,714.831,2097152,2097152,2097152*61



2025-07-31 21:18:28:819 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5566mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:18:28:823 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:18:28:828 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:18:28:955 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:18:29:118 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:18:29:124 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:18:29:129 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:18:29:197 ==>> 1A A1 00 40 00 
Get AD_V14 2565mV
OVER 150
[D][05:18:13][COMM]read battery soc:255


2025-07-31 21:18:29:379 ==>> 原始值:【2565】, 乘以分压基数【2】还原值:【5130】
2025-07-31 21:18:29:446 ==>> 【读取AccKey1电压(ADV14)后】通过,【5130mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:18:29:452 ==>> 检测【打开WIFI(2)】
2025-07-31 21:18:29:457 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:18:29:787 ==>> $GBGGA,131833.516,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,3,,,41,24,,,41,59,,,41,1*49

$GBGSV,7,2,27,25,,,41,60,,,40,40,,,40,39,,,40,1*78

$GBGSV,7,3,27,41,,,40,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,27,16,,,38,34,,,38,44,,,37,9,,,37,1*49

$GBGSV,7,5,27,6,,,37,2,,,36,13,,,36,38,,,36,1*7D

$GBGSV,7,6,27,23,,,36,10,,,35,4,,,35,5,,,34,1*71

$GBGSV,7,7,27,12,,,34,8,,,33,11,,,31,1*4D

$GBRMC,131833.516,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131833.516,0.000,779.340,779.340,712.723,2097152,2097152,2097152*6A

[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:18:29:986 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:18:29:990 ==>> 检测【转刹把供电】
2025-07-31 21:18:29:995 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:18:30:151 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:18:30:284 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:18:30:290 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:18:30:295 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:18:30:397 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:18:30:488 ==>> +WIFISCAN:4,0,CC057790A741,-79
+WIFISCAN:4,1,CC057790A5C0,-79
+WIFISCAN:4,2,CC057790A740,-79
+WIFISCAN:4,3,CC057790A5C1,-80

[D][05:18:15][CAT1]wifi scan report total[4]
[D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2428mV
OVER 150


2025-07-31 21:18:30:548 ==>> 原始值:【2428】, 乘以分压基数【2】还原值:【4856】
2025-07-31 21:18:30:582 ==>> 【读取AD_V15电压(前)】通过,【4856mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:18:30:588 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:18:30:592 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:18:30:685 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:18:30:790 ==>> $GBGGA,131834.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,60,,,41,3,,,41,1*41

$GBGSV,7,2,27,24,,,41,59,,,41,40,,,40,39,,,40,1*72

$GBGSV,7,3,27,41,,,40,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,27,16,,,38,34,,,38,44,,,37,9,,,37,1*49

$GBGSV,7,5,27,6,,,37,2,,,36,13,,,36,38,,,36,1*7D

$GBGSV,7,6,27,23,,,36,10,,,35,4,,,35,5,,,34,1*71

$GBGSV,7,7,27,12,,,34,8,,,33,11,,,31,1*4D

$GBRMC,131834.516,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131834.516,0.000,780.876,780.876,714.128,2097152,2097152,2097152*66

[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2454mV
OVER 150


2025-07-31 21:18:30:850 ==>> 原始值:【2454】, 乘以分压基数【2】还原值:【4908】
2025-07-31 21:18:30:936 ==>> 【读取AD_V16电压(前)】通过,【4908mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:18:30:939 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:18:30:945 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:31:075 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 21:18:31:285 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:15][COMM]adc read battery ts volt:12 mv
[D][05:18:15][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3120  volt:5484 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:15][COMM]read battery soc:255


2025-07-31 21:18:31:485 ==>> 【转刹把供电电压(主控ADC)】通过,【5484mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:18:31:489 ==>> 检测【转刹把供电电压】
2025-07-31 21:18:31:495 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:31:831 ==>> $GBGGA,131835.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,60,,,41,3,,,41,1*41

$GBGSV,7,2,27,24,,,41,59,,,41,40,,,40,39,,,40,1*72

$GBGSV,7,3,27,41,,,40,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,27,16,,,38,34,,,38,6,,,37,2,,,36,1*75

$GBGSV,7,5,27,13,,,36,44,,,36,9,,,36,23,,,36,1*4B

$GBGSV,7,6,27,10,,,35,38,,,35,5,,,34,12,,,34,1*4E

$GBGSV,7,7,27,4,,,34,8,,,33,11,,,31,1*7A

$GBRMC,131835.516,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131835.516,0.000,777.816,777.816,711.330,2097152,2097152,2097152*69

[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3114  volt:5473 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_h

2025-07-31 21:18:31:876 ==>> ub adc read vbat adc:2412  volt:3886 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:18:32:032 ==>> 【转刹把供电电压】通过,【5473mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:18:32:036 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:18:32:042 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:18:32:238 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:18:32:316 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:18:32:322 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:18:32:328 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:18:32:421 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:18:32:436 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:18:32:466 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:18:32:581 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:18:32:585 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:18:32:591 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:18:32:693 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:18:32:799 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:18:32:809 ==>> $GBGGA,131836.516,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,44,25,,,42,60,,,41,3,,,41,1*4E

$GBGSV,7,2,28,24,,,41,59,,,41,40,,,40,39,,,40,1*7D

$GBGSV,7,3,28,41,,,40,14,,,39,7,,,38,1,,,38,1*70

$GBGSV,7,4,28,16,,,38,34,,,38,44,,,37,9,,,37,1*46

$GBGSV,7,5,28,2,,,36,13,,,36,6,,,36,23,,,36,1*79

$GBGSV,7,6,28,10,,,35,38,,,35,12,,,34,4,,,34,1*40

$GBGSV,7,7,28,5,,,33,8,,,33,11,,,31,42,,,37,1*71

$GBRMC,131836.516,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131836.516,0.000,777.051,777.051,710.631,2097152,2097152,2097152*6F

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:18:32:903 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:18:32:965 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:18:33:041 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:18:33:048 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:18:33:053 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:18:33:176 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:18:33:206 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:18:33:338 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:18:33:342 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:18:33:347 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:18:33:479 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:18:33:684 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:18:33:691 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:18:33:702 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:18:33:735 ==>> $GBGGA,131837.516,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,41,24,,,41,59,,,41,1*41

$GBGSV,7,2,28,25,,,41,60,,,40,39,,,40,40,,,39,1*79

$GBGSV,7,3,28,14,,,39,41,,,39,7,,,38,1,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,2,,,37,44,,,37,1*4D

$GBGSV,7,5,28,6,,,37,13,,,36,9,,,36,23,,,36,1*73

$GBGSV,7,6,28,10,,,35,38,,,35,42,,,35,5,,,34,1*45

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131837.516,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131837.516,0.000,772.932,772.932,706.863,2097152,2097152,2097152*60



2025-07-31 21:18:33:765 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:18:33:976 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:18:33:982 ==>> 检测【左刹电压测试1】
2025-07-31 21:18:33:987 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:34:276 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3170  volt:5572 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:1724  volt:2272 mv
[D][05:18:18][COMM]adc read right brake adc:1727  volt:2276 mv
[D][05:18:18][COMM]adc read throttle adc:1720  volt:2267 mv
[D][05:18:18][COMM]adc read battery ts volt:1 mv
[D][05:18:18][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:18:34:534 ==>> 【左刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 21:18:34:540 ==>> 检测【右刹电压测试1】
2025-07-31 21:18:34:572 ==>> 【右刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 21:18:34:575 ==>> 检测【转把电压测试1】
2025-07-31 21:18:34:608 ==>> 【转把电压测试1】通过,【2267】符合目标值【2250】至【2500】要求!
2025-07-31 21:18:34:612 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:18:34:617 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:18:34:747 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150
$GBGGA,131838.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,41,24,,,41,59,,,41,1*41

$GBGSV,7,2,28,25,,,41,60,,,40,39,,,40,41,,,40,1*76

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,44,,,37,9,,,37,1*46

$GBGSV,7,5,28,6,,,37,2,,,36,13,,,36,42,,,36,1*7F

$GBGSV,7,6,28,23,,,36,10,,,35,38,,,35,5,,,34,1*41

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131838.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131838.516,0.000,775.149,775.149,708.891,2097152,2097152,2097152*6C



2025-07-31 21:18:34:906 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:18:34:913 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:18:34:919 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:18:34:976 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:18:35:203 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:18:35:210 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:18:35:237 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:18:35:241 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:18:35:278 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:18:35:542 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:18:35:546 ==>> 检测【左刹电压测试2】
2025-07-31 21:18:35:552 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:18:35:897 ==>> $GBGGA,131839.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,41,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,44,,,37,9,,,37,1*46

$GBGSV,7,5,28,42,,,37,2,,,36,13,,,36,38,,,36,1*42

$GBGSV,7,6,28,6,,,36,23,,,36,10,,,35,5,,,34,1*7F

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131839.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131839.516,0.000,777.369,777.369,710.921,2097152,2097152,2097152*6E

[D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:20][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:20][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:20][COMM]adc read battery ts volt:5 mv
[D][05:18:20][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv


2025-07-31 21:18:35:941 ==>> [D][05:18:20][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:18:36:183 ==>> 【左刹电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 21:18:36:188 ==>> 检测【右刹电压测试2】
2025-07-31 21:18:36:284 ==>> 【右刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 21:18:36:289 ==>> 检测【转把电压测试2】
2025-07-31 21:18:36:356 ==>> 【转把电压测试2】通过,【2】符合目标值【0】至【50】要求!
2025-07-31 21:18:36:362 ==>> 检测【晶振检测】
2025-07-31 21:18:36:366 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:18:36:555 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 21:18:36:657 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:18:36:662 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:18:36:668 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:18:36:735 ==>> $GBGGA,131840.516,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,24,,,41,59,,,41,1*76

$GBGSV,7,2,28,60,,,40,3,,,40,39,,,40,40,,,39,1*4C

$GBGSV,7,3,28,1,,,39,14,,,39,41,,,39,7,,,38,1*7F

$GBGSV,7,4,28,16,,,38,34,,,38,2,,,36,13,,,36,1*4F

$GBGSV,7,5,28,44,,,36,9,,,36,6,,,36,42,,,36,1*77

$GBGSV,7,6,28,23,,,36,10,,,35,38,,,35,12,,,34,1*77

$GBGSV,7,7,28,4,,,34,5,,,33,8,,,33,11,,,31,1*40

$GBRMC,131840.516,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131840.516,0.000,771.458,771.458,705.516,2097152,2097152,2097152*6C



2025-07-31 21:18:36:780 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1657mV
Get AD_V4 1652mV
Get AD_V5 2789mV
Get AD_V6 1992mV
Get AD_V7 1096mV
OVER 150


2025-07-31 21:18:36:969 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:18:36:977 ==>> 检测【检测BootVer】
2025-07-31 21:18:36:985 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:37:332 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539160
[D][05:18:21][FCTY]HardwareID  = 867222087744068
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:21][FCTY]Bat         = 3964 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11900 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1286
[D][05:18:21][FCTY]Acckey1 vol = 5570 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNS

2025-07-31 21:18:37:377 ==>> S_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3717 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][COMM]read battery soc:255


2025-07-31 21:18:37:522 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:18:37:526 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:18:37:531 ==>> 检测【检测固件版本】
2025-07-31 21:18:37:572 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:18:37:577 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:18:37:583 ==>> 检测【检测蓝牙版本】
2025-07-31 21:18:37:611 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:18:37:618 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:18:37:626 ==>> 检测【检测MoBikeId】
2025-07-31 21:18:37:655 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:18:37:662 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:18:37:676 ==>> 检测【检测蓝牙地址】
2025-07-31 21:18:37:700 ==>> 取到目标值:D90A9D65FCE4
2025-07-31 21:18:37:706 ==>> 【检测蓝牙地址】通过,【D90A9D65FCE4】符合目标值【】要求!
2025-07-31 21:18:37:712 ==>> 提取到蓝牙地址:D90A9D65FCE4
2025-07-31 21:18:37:719 ==>> 检测【BOARD_ID】
2025-07-31 21:18:37:741 ==>> $GBGGA,131841.516,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,41,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,44,,,37,9,,,37,1*46

$GBGSV,7,5,28,6,,,37,42,,,37,2,,,36,13,,,36,1*7E

$GBGSV,7,6,28,38,,,36,23,,,36,10,,,35,5,,,34,1*42

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131841.516,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131841.516,0.000,778.107,778.107,711.596,2097152,2097152,2097152*60



2025-07-31 21:18:37:746 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:18:37:750 ==>> 检测【检测充电电压】
2025-07-31 21:18:37:788 ==>> 【检测充电电压】通过,【3964mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:18:37:794 ==>> 检测【检测VBUS电压1】
2025-07-31 21:18:37:829 ==>> 【检测VBUS电压1】通过,【11900mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:18:37:835 ==>> 检测【检测充电电流】
2025-07-31 21:18:37:867 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:18:37:870 ==>> 检测【检测IMEI】
2025-07-31 21:18:37:876 ==>> 取到目标值:867222087744068
2025-07-31 21:18:37:906 ==>> 【检测IMEI】通过,【867222087744068】符合目标值【】要求!
2025-07-31 21:18:37:911 ==>> 提取到IMEI:867222087744068
2025-07-31 21:18:37:919 ==>> 检测【检测IMSI】
2025-07-31 21:18:37:927 ==>> 取到目标值:460130071539160
2025-07-31 21:18:37:965 ==>> 【检测IMSI】通过,【460130071539160】符合目标值【】要求!
2025-07-31 21:18:37:970 ==>> 提取到IMSI:460130071539160
2025-07-31 21:18:37:977 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:18:37:984 ==>> 取到目标值:460130071539160
2025-07-31 21:18:38:000 ==>> 【校验网络运营商(移动)】通过,【460130071539160】符合目标值【】要求!
2025-07-31 21:18:38:009 ==>> 检测【打开CAN通信】
2025-07-31 21:18:38:017 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:18:38:073 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:18:38:289 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:18:38:296 ==>> 检测【检测CAN通信】
2025-07-31 21:18:38:306 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:18:38:392 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:18:38:452 ==>> [D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34151
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:18:38:512 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:18:38:573 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:18:38:577 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:18:38:581 ==>> 检测【关闭CAN通信】
2025-07-31 21:18:38:584 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:18:38:632 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:18:38:737 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
$GBGGA,131842.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,41,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,44,,,37,9,,,37,1*46

$GBGSV,7,5,28,6,,,37,42,,,37,2,,,36,13,,,36,1*7E

$GBGSV,7,6,28,38,,,36,23,,,36,10,,,35,5,,,34,1*42

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131842.516,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131842.516,0.000,778.107,778.107,711.596,2097152,2097152,2097152*63



2025-07-31 21:18:38:874 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:18:38:881 ==>> 检测【打印IMU STATE】
2025-07-31 21:18:38:886 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:18:39:073 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:18:39:165 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:18:39:172 ==>> 检测【六轴自检】
2025-07-31 21:18:39:178 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:18:39:239 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:18:39:343 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:18:39:817 ==>> $GBGGA,131843.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,41,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,2,,,37,9,,,37,1*74

$GBGSV,7,5,28,6,,,37,42,,,37,38,,,36,44,,,36,1*45

$GBGSV,7,6,28,23,,,36,10,,,35,13,,,35,5,,,34,1*48

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131843.516,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131843.516,0.000,777.369,777.369,710.921,2097152,2097152,2097152*63



2025-07-31 21:18:40:736 ==>> $GBGGA,131844.516,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,44,25,,,42,60,,,41,3,,,41,1*4E

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,41,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,14,,,39,7,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,38,2,,,37,44,,,37,1*4D

$GBGSV,7,5,28,9,,,37,42,,,37,13,,,36,38,,,36,1*48

$GBGSV,7,6,28,6,,,36,23,,,36,10,,,35,5,,,34,1*7F

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131844.516,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131844.516,0.000,778.849,778.849,712.275,2097152,2097152,2097152*6C



2025-07-31 21:18:41:057 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:18:41:300 ==>> [D][05:18:25][COMM]read battery soc:255
[D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36964 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-15,-2,4068]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 21:18:41:563 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:18:41:570 ==>> 检测【打印IMU STATE2】
2025-07-31 21:18:41:576 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:18:41:820 ==>> $GBGGA,131845.516,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,24,,,41,59,,,41,39,,,40,40,,,39,1*73

$GBGSV,7,3,28,1,,,39,14,,,39,41,,,39,7,,,38,1*7F

$GBGSV,7,4,28,16,,,38,34,,,38,2,,,37,9,,,37,1*74

$GBGSV,7,5,28,42,,,37,13,,,36,38,,,36,44,,,36,1*70

$GBGSV,7,6,28,6,,,36,23,,,36,10,,,35,5,,,34,1*7F

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131845.516,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131845.516,0.000,776.629,776.629,710.244,2097152,2097152,2097152*6D

[W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:18:42:116 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:18:42:121 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:18:42:128 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:18:42:165 ==>> 5A A5 02 5A A5 


2025-07-31 21:18:42:270 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:18:42:375 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i 

2025-07-31 21:18:42:410 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:18:42:416 ==>> 检测【检测VBUS电压2】
2025-07-31 21:18:42:424 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:42:435 ==>> = 1,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:18:42:798 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539160
[D][05:18:27][FCTY]HardwareID  = 867222087744068
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:27][FCTY]Bat         = 3964 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 338
[D][05:18:27][FCTY]Acckey1 vol = 5570 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 

2025-07-31 21:18:42:887 ==>> 3717 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,131846.516,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,25,,,42,60,,,41,3,,,41,1*49

$GBGSV,7,2,28,59,,,41,24,,,40,40,,,39,1,,,39,1*47

$GBGSV,7,3,28,39,,,39,14,,,39,41,,,39,7,,,38,1*44

$GBGSV,7,4,28,16,,,38,34,,,37,42,,,37,2,,,36,1*45

$GBGSV,7,5,28,13,,,36,44,,,36,9,,,36,6,,,36,1*73

$GBGSV,7,6,28,23,,,36,10,,,35,38,,,35,5,,,34,1*41

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131846.516,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131846.516,0.000,772.195,772.195,706.189,2097152,2097152,2097152*6B

[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:18:42:952 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:43:327 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539160
[D][05:18:27][FCTY]HardwareID  = 867222087744068
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:27][FCTY]Bat         = 3964 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 172
[D][05:18:27][FCTY]Acckey1 vol = 5565 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FC

2025-07-31 21:18:43:372 ==>> TY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3717 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:18:43:462 ==>> [D][05:18:28][COMM]msg 0601 loss. last_tick:34148. cur_tick:39168. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39169


2025-07-31 21:18:43:497 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:43:901 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539160
[D][05:18:28][FCTY]HardwareID  = 867222087744068
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:28][FCTY]Bat         = 3964 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11900 mv
$GBGGA,131847.516,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,59,,,41,25,,,41,1*75

$GBGSV,7,2,28,3,,,40,24,,,40,40,,,39,39,,,39,1*42

$GBGSV,7,3,28,14,,,39,41,,,39,7,,,38,1,,,38,1*7E

$GBGSV,7,4,28,16,,,38,34,,,37,42,,,37,2,,,36,1*45

$GBGSV,7,5,28,44,,,36,9,,,36,6,,,36,23,,,36,1*70

$GBGSV,7,6,28,10,,,35,13,,,35,38,,,35,5,,,34,1*41

$GBGSV,7,7,28,12,,,34,4,,,34,8,,,33,11,,,31,1*71

$GBRMC,131847.516,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131847.516,0.000,769.235,769.23

2025-07-31 21:18:44:006 ==>> 5,703.482,2097152,2097152,2097152*61

[D][05:18:28][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 156
[D][05:18:28][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3717 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
                                                   

2025-07-31 21:18:44:307 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:44:779 ==>> [D][05:18:28][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:28][COMM]Main Task receive event:65
[D][05:18:28][COMM]main task tmp_sleep_event = 80
[D][05:18:28][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:28][COMM]Main Task receive event:65 finished processing
[D][05:18:28][COMM]Main Task receive event:60
[D][05:18:28][COMM]smart_helmet_vol=255,255
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[W][05:18:28][GNSS]stop locating
[D][05:18:28][GNSS]stop event:8
[D][05:18:28][GNSS]all continue location stop
[W][05:18:28][GNSS]sing locating running
[D][05:18:28][COMM]report elecbike
[W][05:18:28][PROT]remove success[1629955108],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]index:0
[D][05:18:28][PROT]is_send:1
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg_type

2025-07-31 21:18:44:884 ==>> :0x5d03
[D][05:18:28][PROT]===========================================================
[D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900005]
[D][05:18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][PROT]index:0 1629955108
[D][05:18:28][PROT]is_send:0
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]


2025-07-31 21:18:44:989 ==>> 
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send data len[198]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5C4F476F7882763A3A421AC1BB7EADB4BEB6865ED8151BD87F29F5501DB52A7EDB985C2F45DF61FD056DA43C72459C973F69F44107EBF3819DD9DA88DE07E6BDEACEB2AE386088A93728E940298844A6096A9
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]
[W][05:18:29][COMM]>>>>

2025-07-31 21:18:45:095 ==>> >Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539160
[D][05:18:29][FCTY]HardwareID  = 867222087744068
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:29][FCTY]Bat         = 3624 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 5000 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 2, adc = 114
[D][05:18:29][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = 

2025-07-31 21:18:45:139 ==>> V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3717 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:18:45:368 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:18:45:739 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539160
[D][05:18:30][FCTY]HardwareID  = 867222087744068
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D90A9D65FCE4
[D][05:18:30][FCTY]Bat         = 3604 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 95
[D][05:18:30][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 25 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3717 mv
[D][05:18:30][FCT

2025-07-31 21:18:45:769 ==>> Y]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:18:45:953 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:18:45:958 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:18:45:966 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:18:46:069 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:18:46:174 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18:30][COMM]read battery soc:255


2025-07-31 21:18:46:276 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:18:46:281 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:18:46:288 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:18:46:369 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:18:46:579 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:18:46:584 ==>> 检测【打开WIFI(3)】
2025-07-31 21:18:46:592 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:18:46:763 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:18:46:872 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:18:46:880 ==>> 检测【扩展芯片hw】
2025-07-31 21:18:46:892 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:18:47:356 ==>> [D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:47:750 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:18:47:901 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:18:48:267 ==>>                                                                                                                         ----
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file

2025-07-31 21:18:48:372 ==>> _name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get s

2025-07-31 21:18:48:477 ==>> oc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_

2025-07-31 21:18:48:582 ==>> current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]read battery soc:255


2025-07-31 21:18:48:612 ==>>                                                                                                   

2025-07-31 21:18:48:927 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:18:48:942 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44604]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:18:49:167 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:18:49:378 ==>> [D][05:18:34][COMM]45074 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:49:666 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 21:18:49:772 ==>> [D][05:18:34][CAT1]SEND RAW data timeout
[D][05:18:34][CAT1]exec over: func id: 12, ret: -52
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 21:18:49:921 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:18:49:929 ==>> 检测【扩展芯片boot】
2025-07-31 21:18:49:953 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45630]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:18:49:959 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:18:49:971 ==>> 检测【扩展芯片sw】
2025-07-31 21:18:49:993 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:18:49:999 ==>> 检测【检测音频FLASH】
2025-07-31 21:18:50:007 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:18:50:194 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:34][COMM]read battery soc:255


2025-07-31 21:18:50:375 ==>> [D][05:18:35][COMM]46086 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:50:972 ==>> [D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46658]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:18:51:400 ==>> [D][05:18:36][COMM]47097 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:52:198 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 21:18:52:396 ==>> [D][05:18:37][COMM]48110 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:53:421 ==>> [D][05:18:38][COMM]49122 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:54:031 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok


2025-07-31 21:18:54:228 ==>> [E][05:18:38][GNSS]GPS module no nmea data!
[D][05:18:38][GNSS]GPS reload stop. ret=0
[D][05:18:38][GNSS]GPS reload start. ret=0
[D][05:18:38][COMM]read battery soc:255


2025-07-31 21:18:54:441 ==>> [D][05:18:39][COMM]50133 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:55:071 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:18:55:080 ==>> 检测【打开喇叭声音】
2025-07-31 21:18:55:104 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:18:55:270 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:18:55:364 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:18:55:371 ==>> 检测【打开大灯控制】
2025-07-31 21:18:55:377 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:18:55:435 ==>> [D][05:18:40][COMM]51144 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:55:539 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:18:55:653 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:18:55:661 ==>> 检测【关闭仪表供电3】
2025-07-31 21:18:55:679 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:18:55:841 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0


2025-07-31 21:18:55:956 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:18:55:964 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:18:55:973 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:18:56:156 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:18:56:217 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 21:18:56:254 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:18:56:260 ==>> 检测【读大灯电压】
2025-07-31 21:18:56:268 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:18:56:460 ==>> [D][05:18:41][COMM]52155 imu init OK
[W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:41][COMM]arm_hub read adc[5],val[33247]


2025-07-31 21:18:56:554 ==>> 【读大灯电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:18:56:561 ==>> 检测【关闭大灯控制2】
2025-07-31 21:18:56:567 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:18:56:730 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:18:56:842 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:18:56:852 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:18:56:867 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:18:57:062 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[92]


2025-07-31 21:18:57:143 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:18:57:148 ==>> 检测【打开WIFI(4)】
2025-07-31 21:18:57:155 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:18:57:337 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:18:57:443 ==>> [D][05:18:42][COMM]53166 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:18:57:508 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:18:57:517 ==>> 检测【EC800M模组版本】
2025-07-31 21:18:57:540 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:18:58:214 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 21:18:58:456 ==>> [D][05:18:43][COMM]imu error,enter wait


2025-07-31 21:18:58:546 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:18:59:396 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:18:59:594 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:18:59:932 ==>> [D][05:18:44][CAT1]exec over: func id: 15, ret: -93
[D][05:18:44][CAT1]sub id: 15, ret: -93

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:44][SAL ]socket send fail. id[4]
[D][05:18:44][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:44][CAT1]gsm read msg sub id: 24
[D][05:18:44][M2M ]m2m select fd[4]
[D][05:18:44][M2M ]socket[4] Link is disconnected
[D][05:18:44][M2M ]tcpclient close[4]
[D][05:18:44][SAL ]socket[4] has closed
[D][05:18:44][PROT]protocol read data ok
[E][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:44][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:44][PROT]M2M Send Fail [1629955124]
[D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
                                                                   

2025-07-31 21:19:00:375 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55905]
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1032].open hexlog save
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:44][COMM]file:A20 exist
[D][05:18:44][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:44][COMM]read file, len:15228, num:4
[D][05:18:44][COMM]read battery soc:255
[D][05:18:44][COMM]--->crc16:0x419c
[D][05:18:44][COMM]read file success
[W][05:18:44][COMM][Audio].l:[936].close hexlog save
[D][05:18:44][COMM]accel parse set 1
[D][05:18:44][COMM][Audio]mon:9,05:18:44
[D][05:18:44][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:19:00:632 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:01:313 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57004]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:19:01:418 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:01:662 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:01:818 ==>> [D][05:18:46][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 21:19:02:234 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 21:19:02:339 ==>> [D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58033]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:19:02:704 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:03:374 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59061]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:19:03:479 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:03:740 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:03:848 ==>> [D][05:18:48][CAT1]exec over: func id: 24, ret: -181
[D][05:18:48][CAT1]sub id: 24, ret: -181

[D][05:18:48][CAT1]gsm read msg sub id: 23
[D][05:18:48][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 21:19:04:310 ==>> [D][05:18:48][COMM]read battery soc:255
[D][05:18:48][GNSS]recv submsg id[1]
[D][05:18:48][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:48][GNSS]stop gps fail
[E][05:18:48][GNSS]GPS module no nmea data!
[D][05:18:48][GNSS]GPS reload stop. ret=0
[D][05:18:48][GNSS]GPS reload start. ret=0


2025-07-31 21:19:04:355 ==>>                                                 

2025-07-31 21:19:04:784 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:04:955 ==>> [D][05:18:49][CAT1]exec over: func id: 23, ret: -151
[D][05:18:49][CAT1]sub id: 23, ret: -151

[D][05:18:49][CAT1]gsm read msg sub id: 12
[D][05:18:49][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:05:278 ==>> [D][05:18:49][GNSS]recv submsg id[1]
[D][05:18:49][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:49][GNSS]start gps fail


2025-07-31 21:19:05:538 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:05:828 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:06:255 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 21:19:06:868 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:07:440 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:07:591 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:07:901 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:07:932 ==>> [D][05:18:52][CAT1]SEND RAW data timeout
[D][05:18:52][CAT1]exec over: func id: 12, ret: -52
[W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:52][CAT1]gsm read msg sub id: 12
[D][05:18:52][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:19:08:248 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 21:19:08:938 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:09:943 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:09:973 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:10:278 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 21:19:10:953 ==>> [D][05:18:55][CAT1]SEND RAW data timeout
[D][05:18:55][CAT1]exec over: func id: 12, ret: -52
[W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:55][CAT1]gsm read msg sub id: 10
[D][05:18:55][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:19:11:013 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:12:038 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:12:273 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 21:19:12:440 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:12:954 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:13:076 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:14:099 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:14:271 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 21:19:14:361 ==>> [E][05:18:59][GNSS]GPS module no nmea data!
[D][05:18:59][GNSS]GPS reload stop. ret=0
[D][05:18:59][GNSS]GPS reload start. ret=0


2025-07-31 21:19:14:935 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:15:010 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:15:130 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:15:704 ==>> [D][05:19:00][COMM]f:[drv_audio_ack_receive].wait ack timeout!![71401]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM][Audio].l:[1032].open hexlog save
[D][05:19:00][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:19:16:180 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:16:272 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 21:19:17:062 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:17:214 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:17:430 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:18:260 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:18:290 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 21:19:18:930 ==>> [D][05:19:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:19:19:113 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:19:296 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:19:930 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:20:299 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 21:19:20:328 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:21:157 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:21:370 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:22:295 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 21:19:22:400 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:22:429 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:23:226 ==>> [D][05:19:07][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:23:440 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:24:319 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 21:19:24:424 ==>> [E][05:19:09][GNSS]GPS module no nmea data!
[D][05:19:09][GNSS]GPS reload stop. ret=0
[D][05:19:09][GNSS]GPS reload start. ret=0


2025-07-31 21:19:24:484 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:24:939 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:19:25:259 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:25:521 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:26:309 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 21:19:26:570 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:26:954 ==>> [D][05:19:11][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:19:27:480 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:12][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:27:599 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:19:27:755 ==>> [D][05:19:12][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:12][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:19:28:338 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 21:19:28:638 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:19:28:652 ==>> #################### 【测试结束】 ####################
2025-07-31 21:19:30:071 ==>> 关闭5V供电
2025-07-31 21:19:30:080 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:19:30:171 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:19:30:337 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 21:19:31:076 ==>> 关闭5V供电成功
2025-07-31 21:19:31:086 ==>> 关闭33V供电
2025-07-31 21:19:31:111 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:19:31:170 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:19:31:366 ==>> [D][05:19:15][FCTY]get_ext_48v_vol retry i = 0,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 1,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 2,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 3,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 4,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 5,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 6,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 7,volt = 15
[D][05:19:15][FCTY]get_ext_48v_vol retry i = 8,volt = 15
[D][05:19:16][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 21:19:32:089 ==>> 关闭33V供电成功
2025-07-31 21:19:32:098 ==>> 关闭3.7V供电
2025-07-31 21:19:32:107 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:19:32:179 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:19:32:641 ==>>  

