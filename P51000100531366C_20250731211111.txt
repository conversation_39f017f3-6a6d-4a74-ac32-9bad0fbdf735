2025-07-31 21:11:11:409 ==>> MES查站成功:
查站序号:P51000100531366C验证通过
2025-07-31 21:11:11:423 ==>> 扫码结果:P51000100531366C
2025-07-31 21:11:11:425 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:11:11:426 ==>> 测试参数版本:2024.10.11
2025-07-31 21:11:11:427 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:11:11:429 ==>> 检测【打开透传】
2025-07-31 21:11:11:430 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:11:11:572 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:11:11:721 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:11:11:732 ==>> 检测【检测接地电压】
2025-07-31 21:11:11:735 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:11:11:863 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:11:12:090 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:11:12:092 ==>> 检测【打开小电池】
2025-07-31 21:11:12:096 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:11:12:172 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:11:12:462 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:11:12:464 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:11:12:466 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:11:12:573 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:11:12:760 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:11:12:762 ==>> 检测【等待设备启动】
2025-07-31 21:11:12:764 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:11:12:940 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:11:13:137 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:11:13:642 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:11:13:794 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:11:13:840 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:11:14:476 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:11:14:551 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:11:14:827 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:11:14:947 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:11:15:423 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:11:15:652 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:11:15:655 ==>> 检测【产品通信】
2025-07-31 21:11:15:656 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:11:16:122 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:11:16:319 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:11:16:689 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:11:17:029 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 21:11:17:414 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:11:17:734 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:11:17:872 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:11:17:947 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:11:18:027 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:11:18:029 ==>> 检测【初始化完成检测】
2025-07-31 21:11:18:031 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:11:18:300 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 21:11:18:530 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:11:18:589 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:11:18:591 ==>> 检测【关闭大灯控制1】
2025-07-31 21:11:18:593 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:11:18:726 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:11:18:884 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:11:18:888 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:11:18:889 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:11:18:938 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:19:118 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:11:19:180 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:11:19:183 ==>> 检测【关闭仪表供电】
2025-07-31 21:11:19:185 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:11:19:360 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:11:19:768 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:11:19:771 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:11:19:774 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:11:19:957 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:20:150 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:11:20:152 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:11:20:154 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:11:20:336 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:11:20:473 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:11:20:475 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:11:20:478 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:20:653 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:11:20:784 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:11:20:786 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:11:20:788 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:11:20:866 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:11:20:956 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 4
[D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:21:031 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 21:11:21:092 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:11:21:095 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:11:21:097 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:11:21:166 ==>> 5A A5 03 5A A5 


2025-07-31 21:11:21:271 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 21:11:21:497 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5007. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5008. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5008. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5009. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010


2025-07-31 21:11:21:543 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:11:21:546 ==>> 该项需要延时执行
2025-07-31 21:11:21:970 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:22:532 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:11:23:052 ==>>                     rm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive e

2025-07-31 21:11:23:157 ==>> vent:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main

2025-07-31 21:11:23:262 ==>> _vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[16299

2025-07-31 21:11:23:322 ==>> 55075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:23:352 ==>>                                                                                  

2025-07-31 21:11:23:992 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:25:062 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 21:11:25:556 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:11:25:559 ==>> 检测【33V输入电压ADC】
2025-07-31 21:11:25:562 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:25:876 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:17:58][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:58][COMM]adc read left brake adc:8  volt:10 mv
[D][05:17:58][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:58][COMM]adc read throttle adc:12  volt:15 mv
[D][05:17:58][COMM]adc read battery ts volt:18 mv
[D][05:17:58][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:58][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 21:11:26:012 ==>> [D][05:17:58][COMM]9703 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:11:26:111 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:11:26:114 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:11:26:117 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:11:26:178 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2770mV
Get AD_V6 1989mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:11:26:374 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10019


2025-07-31 21:11:26:406 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:26:411 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:11:26:439 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:26:442 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:11:26:445 ==>> 原始值:【2770】, 乘以分压基数【2】还原值:【5540】
2025-07-31 21:11:26:475 ==>> 【TP68_VCC5V5(ADV5)】通过,【5540mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:11:26:478 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:11:26:525 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:11:26:527 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:11:26:585 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:11:26:588 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:11:26:680 ==>> 1A A1 00 00 FC 
Get AD_V2 1654mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2772mV
Get AD_V6 1988mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:11:26:830 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:11:26:895 ==>> 【TP7_VCC3V3(ADV2)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:26:898 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:11:26:936 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:26:938 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:11:26:941 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 21:11:26:989 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:11:26:992 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:11:27:035 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:11:27:060 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:11:27:077 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:11:27:079 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:11:27:256 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m 

2025-07-31 21:11:27:301 ==>> switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2773mV
Get AD_V6 1989mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:11:27:370 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:27:373 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:11:27:410 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:27:414 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:11:27:418 ==>> 原始值:【2773】, 乘以分压基数【2】还原值:【5546】
2025-07-31 21:11:27:444 ==>> 【TP68_VCC5V5(ADV5)】通过,【5546mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:11:27:447 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:11:27:483 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:11:27:485 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:11:27:520 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:11:27:522 ==>> 检测【打开WIFI(1)】
2025-07-31 21:11:27:525 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:11:27:691 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087912343

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290491

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:11:27:781 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:11:28:023 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:11:28:214 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:11:28:224 ==>> 检测【清空消息队列(1)】
2025-07-31 21:11:28:227 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:11:28:391 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:01][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:11:28:544 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:11:28:547 ==>> 检测【打开GPS(1)】
2025-07-31 21:11:28:549 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:11:28:756 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:11:28:841 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:11:28:844 ==>> 检测【打开GSM联网】
2025-07-31 21:11:28:847 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:11:29:062 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][COMM]read battery soc:255


2025-07-31 21:11:29:138 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:11:29:141 ==>> 检测【打开仪表供电1】
2025-07-31 21:11:29:145 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:11:29:369 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:11:29:436 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:11:29:439 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:11:29:444 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:11:29:670 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:11:29:746 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:11:29:752 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:11:29:756 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:11:29:957 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[32992]


2025-07-31 21:11:30:029 ==>> 【读取主控ADC采集的仪表电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:11:30:032 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:11:30:034 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:30:036 ==>> [D][05:18:02][COMM]13728 imu init OK


2025-07-31 21:11:30:259 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:30:320 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:11:30:323 ==>> 检测【AD_V20电压】
2025-07-31 21:11:30:325 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:30:426 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:30:612 ==>> 本次取值间隔时间:175ms
2025-07-31 21:11:30:765 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:11:30:870 ==>> 本次取值间隔时间:252ms
2025-07-31 21:11:31:035 ==>> 本次取值间隔时间:156ms
2025-07-31 21:11:31:050 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 21:11:31:095 ==>> 本次取值间隔时间:55ms
2025-07-31 21:11:31:098 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:31:201 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:31:276 ==>> [W][05:18:03][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:31:595 ==>> 本次取值间隔时间:380ms
2025-07-31 21:11:31:600 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][COMM]Main Task receive event:4
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:04][GNSS]rtk_id: 303E383D3

2025-07-31 21:11:31:631 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:31:700 ==>> 535373F3836363534333407

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:11:31:745 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:31:805 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:31:865 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:11:31:955 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:11:32:215 ==>> 本次取值间隔时间:469ms
2025-07-31 21:11:32:230 ==>>                                                                                                                              ms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:11:32:257 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:32:365 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:32:472 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:32:607 ==>> 本次取值间隔时间:235ms
2025-07-31 21:11:32:650 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:32:763 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:32:870 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:32:960 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:11:32:975 ==>> 本次取值间隔时间:201ms
2025-07-31 21:11:33:019 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:33:110 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 21:11:33:125 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:33:201 ==>> 本次取值间隔时间:63ms
2025-07-31 21:11:33:369 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:33:594 ==>> 本次取值间隔时间:380ms
2025-07-31 21:11:33:609 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:11:33:669 ==>> 本次取值间隔时间:71ms
2025-07-31 21:11:33:820 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,33,,,40,25,,,39,39,,,39,60,,,38,1*74

$GBGSV,2,2,05,16,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.760,1616.760,51.609,2097152,2097152,2097152*44

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 21:11:33:837 ==>> 本次取值间隔时间:152ms
2025-07-31 21:11:33:843 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:33:939 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:34:030 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:11:34:119 ==>> 本次取值间隔时间:177ms
2025-07-31 21:11:34:134 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:11:34:574 ==>> 本次取值间隔时间:452ms
2025-07-31 21:11:34:729 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,33,,,41,25,,,39,60,,,39,39,,,38,1*70

$GBGSV,3,2,10,24,,,38,59,,,38,16,,,36,13,,,33,1*7C

$GBGSV,3,3,10,14,,,32,3,,,40,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1538.558,1538.558,49.214,2097152,2097152,2097152*45



2025-07-31 21:11:34:896 ==>> 本次取值间隔时间:319ms
2025-07-31 21:11:35:113 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 21:11:35:325 ==>> 本次取值间隔时间:418ms
2025-07-31 21:11:35:329 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:35:430 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:35:626 ==>> [W][05:18:08][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:11:35:656 ==>> 本次取值间隔时间:216ms
2025-07-31 21:11:35:731 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,41,3,,,40,25,,,39,60,,,39,1*47

$GBGSV,5,2,17,24,,,39,39,,,38,59,,,38,16,,,36,1*7F

$GBGSV,5,3,17,40,,,36,1,,,35,13,,,33,14,,,33,1*47

$GBGSV,5,4,17,5,,,32,44,,,32,38,,,32,2,,,29,1*77

$GBGSV,5,5,17,4,,,26,1*40

$GBRMC,,V,,,,,,,,0.0

2025-07-31 21:11:35:761 ==>> ,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1458.421,1458.421,46.721,2097152,2097152,2097152*49



2025-07-31 21:11:35:914 ==>> 本次取值间隔时间:246ms
2025-07-31 21:11:36:067 ==>> 本次取值间隔时间:150ms
2025-07-31 21:11:36:448 ==>> 本次取值间隔时间:377ms
2025-07-31 21:11:36:452 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:36:555 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:36:664 ==>> [W][05:18:09][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:36:769 ==>> $GBGGA,131140.600,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,41,3,,,40,25,,,39,60,,,39,1*49

$GBGSV,5,2,19,24,,,39,39,,,38,59,,,38,40,,,37,1*73

$GBGSV,5,3,19,16,,,36,1,,,35,14,,,34,13,,,33,1*4D

$GBGSV,5,4,19,44,,,32,38,,,32,5,,,31,2,,,31,1*73

$GBGSV,5,5

2025-07-31 21:11:36:815 ==>> ,19,23,,,29,4,,,28,6,,,36,1*79

$GBRMC,131140.600,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131140.600,0.000,1455.703,1455.703,46.626,2097152,2097152,2097152*51



2025-07-31 21:11:36:919 ==>> 本次取值间隔时间:352ms
2025-07-31 21:11:36:956 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:37:059 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:37:167 ==>> [D][05:18:09][COMM]read battery soc:255
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:09][COMM]oneline display ALL on 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:37:362 ==>> 本次取值间隔时间:297ms
2025-07-31 21:11:37:395 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:37:497 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:37:572 ==>> 1A A1 10 00 00 
Get AD_V20 159mV
OVER 150


2025-07-31 21:11:37:677 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:10][COMM]oneline display ALL on 1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,131141.500,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,3,,,40,

2025-07-31 21:11:37:722 ==>> 本次取值间隔时间:217ms
2025-07-31 21:11:37:737 ==>> 25,,,40,60,,,39,1*4A

$GBGSV,6,2,24,24,,,39,39,,,38,59,,,38,40,,,37,1*7E

$GBGSV,6,3,24,16,,,36,7,,,36,1,,,35,14,,,35,1*71

$GBGSV,6,4,24,9,,,35,6,,,34,13,,,33,44,,,33,1*7E

$GBGSV,6,5,24,38,,,33,10,,,32,12,,,32,2,,,32,1*49

$GBGSV,6,6,24,23,,,32,5,,,30,4,,,30,42,,,36,1*72

$GBRMC,131141.500,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131141.500,0.000,1460.076,1460.076,46.733,2097152,2097152,2097152*56



2025-07-31 21:11:37:764 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:37:873 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:37:965 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:10][COMM]oneline display ALL on 1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:11:38:290 ==>> 本次取值间隔时间:411ms
2025-07-31 21:11:38:322 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:38:425 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:38:757 ==>> $GBGGA,131142.500,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,39,25,,,39,60,,,39,1*4A

$GBGSV,7,2,25,24,,,39,39,,,38,59,,,38,40,,,37,1*7E

$GBGSV,7,3,25,16,,,36,7,,,36,1,,,35,14,,,35,1*71

$GBGSV,7,4,25,9,,,35,6,,,34,34,,,34,13,,,34,1*79

$GBGSV,7,5,25,42,,,33,44,,,33,38,,,33,23,,,33,1*7F

$GBGSV,7,6,25,10,,,32,12,,,32,2,,,32,5,,,30,1*77

$GBGSV,7,7,25,4,,,30,1*46

$GBRMC,131142.500,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131142.500,0.000,1454.367,1454.367,46.538,2097152,2097152,2097152*5C

[W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:11][COMM]oneline display ALL on 1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:38:817 ==>> 本次取值间隔时间:390ms
2025-07-31 21:11:39:125 ==>> 本次取值间隔时间:296ms
2025-07-31 21:11:39:129 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:11:39:200 ==>> 本次取值间隔时间:63ms
2025-07-31 21:11:39:352 ==>> 本次取值间隔时间:145ms
2025-07-31 21:11:39:356 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:39:457 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:39:532 ==>> [W][05:18:12][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:11:39:562 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:11:39:667 ==>> $GBGGA,131143.500,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,3,,,40,25,,,40,60,,,39,1*4A

$GBGSV,7,2,25,24,,,39,59,,,39,39,,,38,40,,,37,1*7F

$GBGSV,7,3,25,16,,,36,7,,,36,1,,,35,14,,,35,1*71

$GBGSV,7,4,25,9,,,34,6,,,34,34,,,34,42,,,34,1*7C

$GBGSV,7,5,25,38,,,34,13,,,33,44,,,33,23,,,33,1*7C

$GBGSV,7,6,25,10,,,33,12,,,32,2,,,32,5,,,30,1*76

$GBGSV,7,7,25,4,,,30,1*46

$GBRMC,131143.500,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20


2025-07-31 21:11:39:697 ==>> 
$GBGST,131143.500,0.000,1461.006,1461.006,46.756,2097152,2097152,2097152*57



2025-07-31 21:11:39:727 ==>> 本次取值间隔时间:263ms
2025-07-31 21:11:39:758 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:39:863 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:39:971 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:12][COMM]oneline display ALL on 1
[D][05:18:12][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:11:40:184 ==>> 本次取值间隔时间:310ms
2025-07-31 21:11:40:218 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:11:40:324 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:11:40:369 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:13][COMM]oneline display ALL on 1
[D][05:18:13][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:40:474 ==>> 1A A1 10 00 00 
Get AD_V20 1655mV
OVER 150


2025-07-31 21:11:40:716 ==>> $GBGGA,131144.500,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,39,60,,,39,1*47

$GBGSV,7,2,26,24,,,39,59,,,39,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,16,,,36,41,,,36,7,,,35,1,,,35,1*72

$GBGSV,7,4,26,14,,,35,9,,,35,6,,,34,34,,,34,1*7C

$GBGSV,7,5,26,42,,,34,38,,,34,13,,,33,44,,,33,1*7F

$GBGSV,7,6,26,23,,,33,10,,,33,2,,,33,12,,,32,1*43

$GBGSV,7,7,26,4,,,31,5,,,30,1*72

$GBRMC,131144.500,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131144.500,0.000,1463.801,1463.801,46.834,2097152,2097152,2097152*5B



2025-07-31 21:11:40:821 ==>> 本次取值间隔时间:496ms
2025-07-31 21:11:40:854 ==>> 【AD_V20电压】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:40:859 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:11:40:863 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:11:40:975 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:11:41:125 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:11:41:149 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:11:41:152 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:11:41:157 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:11:41:368 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:14][COMM]oneline display read state:0
[D][05:18:14][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:41:446 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:11:41:449 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:11:41:455 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:11:41:563 ==>> 3A A3 02 01 A3 


2025-07-31 21:11:41:668 ==>> $GBGGA,131145.500,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,60,,,39,1*49

$GBGSV,7,2,26,24,,,39,59,,,39,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,16,,,36,7,,,36,1,,,35,1*70

$GBGSV,7,4,26,14,,,35,9,,,35,6,,,34,34,,,34,1*7C

$GBGSV,7,5,26,42,,,34,38,,,34,13,,,33,44,,,33,1*7F

$GBGSV,7,6,26,23,,,33,10,,,33,2,,,33,12,,,32,1*43

$GBGSV,7,7,26,4,,,31,5,,,30,1*72

$GBRMC,131145.500,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,1311

2025-07-31 21:11:41:699 ==>> 45.500,0.000,1468.588,1468.588,46.990,2097152,2097152,2097152*55

ON_OUT2
OVER 150


2025-07-31 21:11:41:994 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:11:41:997 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:11:42:001 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:11:42:159 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:14][COMM]oneline display read state:1
[D][05:18:14][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:42:283 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:11:42:287 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:11:42:291 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:11:42:497 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:15][COMM]oneline display set 1
[D][05:18:15][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:11:42:577 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:11:42:581 ==>> 检测【AD_V21电压】
2025-07-31 21:11:42:583 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:11:42:707 ==>> 本次取值间隔时间:124ms
2025-07-31 21:11:42:722 ==>> $GBGGA,131146.500,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,60,,,39,1*49

$GBGSV,7,2,26,24,,,39,59,,,39,39,,,38,40,,,37,1*7C

$GBGSV,7,3,26,41,,,37,16,,,36,7,,,36,14,,,36,1*47

$GBGSV,7,4,26,1,,,35,9,,,34,6,,,34,34,,,34,1*49

$GBGSV,7,5,26,42,,,34,38,,,34,13,,,33,44,,,33,1*7F

$GBGSV,7,6,26,23,,,33,10,,,33,2,,,33,12,,,32,1*43

$GBGSV,7,7,26,4,,,31,5,,,30,1*72

$GBRMC,131146.500,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131146.500,0.000,1468.588,1468.588,46.991,2097152,2097152,2097152*57

1A A1 20 00 00 
Get AD_V21 1062mV
OVER 150


2025-07-31 21:11:42:798 ==>> 本次取值间隔时间:86ms
2025-07-31 21:11:42:844 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:11:42:962 ==>> 本次取值间隔时间:108ms
2025-07-31 21:11:42:967 ==>> 1A A1 20 00 00 
Get AD_V21 1650mV
OVER 150


2025-07-31 21:11:43:127 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:11:43:157 ==>> 本次取值间隔时间:184ms
2025-07-31 21:11:43:261 ==>> 【AD_V21电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:43:269 ==>> 检测【关闭仪表供电2】
2025-07-31 21:11:43:273 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:11:43:457 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:16][COMM]set POWER 0
[D][05:18:16][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:11:43:552 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:11:43:555 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:11:43:562 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:11:43:713 ==>> $GBGGA,131147.500,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,7,,,35,14,,,35,1*46

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,6,,,34,1*48

$GBGSV,7,5,26,34,,,34,38,,,34,13,,,34,44,,,33,1*79

$GBGSV,7,6,26,23,,,33,10,,,33,2,,,33,12,,,33,1*42

$GBGSV,7,7,26,4,,,31,5,,,30,1*72

$GBRMC,131147.500,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131147.500,0.000,1474.965,1474.965,47.194,2097152,2097152,2097152*5A



2025-07-31 21:11:43:758 ==>>                                                                                                                                       

2025-07-31 21:11:44:583 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:11:44:721 ==>> $GBGGA,131148.500,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,7,,,36,14,,,36,1*46

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,13,,,34,44,,,33,1*79

$GBGSV,7,6,26,23,,,33,10,,,33,2,,,33,12,,,33,1*42

$GBGSV,7,7,26,4,,,31,5,,,31,1*73

$GBRMC,131148.500,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131148.500,0.000,1481.339,1481.339,47.393,2097152,2097152,2097152*50



2025-07-31 21:11:44:796 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:17][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:11:44:874 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:11:44:877 ==>> 检测【打开AccKey2供电】
2025-07-31 21:11:44:880 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:11:45:026 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:11:45:131 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:11:45:161 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:11:45:165 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:11:45:170 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:45:478 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:18][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:18:18][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:18][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:18][COMM]adc read battery ts volt:10 mv
[D][05:18:18][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:18][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:11:45:713 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:11:45:717 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:11:45:720 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:11:45:723 ==>> $GBGGA,131149.500,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,40,25,,,40,24,,,40,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,38,16,,,36,7,,,36,14,,,36,1*49

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,13,,,34,23,,,34,1*7F

$GBGSV,7,6,26,44,,,33,10,,,33,2,,,33,12,,,33,1*43

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131149.500,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131149.500,0.000,1487.717,1487.717,47.597,2097152,2097152,2097152*53



2025-07-31 21:11:45:824 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:11:46:013 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:11:46:017 ==>> 该项需要延时执行
2025-07-31 21:11:46:715 ==>> $GBGGA,131150.500,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,13,,,33,23,,,33,1*7F

$GBGSV,7,6,26,44,,,33,10,,,33,2,,,33,12,,,33,1*43

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131150.500,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131150.500,0.000,1476.550,1476.550,47.235,2097152,2097152,2097152*54



2025-07-31 21:11:47:146 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:11:47:718 ==>> $GBGGA,131151.500,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,39,1*49

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,35,7,,,35,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,9,,,34,6,,,34,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,23,,,34,13,,,33,1*78

$GBGSV,7,6,26,44,,,33,10,,,33,2,,,33,12,,,32,1*42

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131151.500,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131151.500,0.000,1471.769,1471.769,47.084,2097152,2097152,2097152*5D



2025-07-31 21:11:48:722 ==>> $GBGGA,131152.500,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,1,,,35,42,,,35,9,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,23,,,34,13,,,34,1*7F

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131152.500,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131152.500,0.000,1486.116,1486.116,47.539,2097152,2097152,2097152*5D



2025-07-31 21:11:49:027 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:11:49:033 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:11:49:039 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:49:147 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:11:49:372 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:21][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:21][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:21][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:21][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:21][COMM]adc read battery ts volt:21 mv
[D][05:18:21][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:21][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:11:49:569 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【25mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:11:49:573 ==>> 检测【打开AccKey1供电】
2025-07-31 21:11:49:577 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:11:49:717 ==>> $GBGGA,131153.500,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,1,,,36,42,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131153.500,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131153.500,0.000,1492.495,1492.495,47.744,2097152,2097152,2097152*54



2025-07-31 21:11:49:822 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:22][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:11:50:127 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:11:50:131 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:11:50:134 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:11:50:270 ==>> 1A A1 00 40 00 
Get AD_V14 2661mV
OVER 150


2025-07-31 21:11:50:390 ==>> 原始值:【2661】, 乘以分压基数【2】还原值:【5322】
2025-07-31 21:11:50:427 ==>> 【读取AccKey1电压(ADV14)前】通过,【5322mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:11:50:430 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:11:50:433 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:50:804 ==>> $GBGGA,131154.500,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,1,,,35,42,,,35,9,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131154.500,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131154.500,0.000,1490.901,1490.901,47.694,2097152,2097152,2097152*5F

[W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:23][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:23][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:23][COMM]adc read battery ts volt:14 mv
[D][05:18:23][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:23][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  

2025-07-31 21:11:50:849 ==>> volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:6  volt:139 mv


2025-07-31 21:11:50:973 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5519mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:11:50:977 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:11:50:982 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:11:51:180 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:23][COMM]frm_peripheral_device_poweroff type 5.... 
[D][05:18:23][COMM]read battery soc:255


2025-07-31 21:11:51:328 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:11:51:332 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:11:51:337 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:11:51:471 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 21:11:51:591 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 21:11:51:711 ==>> $GBGGA,131155.500,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,1,,,36,42,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,32,1*73

$GBRMC,131155.500,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131155.500,0.000,1494.087,1494.087,47.792,2097152,2097152,2097152*59



2025-07-31 21:11:51:719 ==>> 【读取AccKey1电压(ADV14)后】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:11:51:725 ==>> 检测【打开WIFI(2)】
2025-07-31 21:11:51:730 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:11:51:891 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:11:52:065 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:11:52:071 ==>> 检测【转刹把供电】
2025-07-31 21:11:52:078 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:11:52:257 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:11:52:386 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:11:52:392 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:11:52:397 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:11:52:501 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:11:52:563 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 21:11:52:653 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 21:11:52:668 ==>>                                   ,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,40,59,,,39,39,,,39,41,,,38,1*7D

$GBGSV,7,3,26,40,,,38,16,,,37,14,,,36,7,,,36,1*48

$GBGSV,7,4,26,1,,,35,42,,,35,9,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131156.500,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGS

2025-07-31 21:11:52:698 ==>> T,131156.500,0.000,1494.093,1494.093,47.798,2097152,2097152,2097152*50



2025-07-31 21:11:52:707 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:11:52:710 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:11:52:716 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:11:52:788 ==>> +WIFISCAN:4,0,F62A7D2297A3,-68
+WIFISCAN:4,1,44A1917CAD81,-77
+WIFISCAN:4,2,CC057790A7C1,-79
+WIFISCAN:4,3,CC057790A7C0,-79

[D][05:18:25][CAT1]wifi scan report total[4]


2025-07-31 21:11:52:818 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:11:52:864 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2440mV
OVER 150


2025-07-31 21:11:52:983 ==>> 原始值:【2440】, 乘以分压基数【2】还原值:【4880】
2025-07-31 21:11:53:039 ==>> 【读取AD_V16电压(前)】通过,【4880mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:11:53:044 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:11:53:048 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:53:108 ==>> [D][05:18:25][GNSS]recv submsg id[3]


2025-07-31 21:11:53:168 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:11:53:379 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:25][COMM]adc read vcc5v mc adc:3147  volt:5531 mv
[D][05:18:25][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:25][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:25][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:25][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:25][COMM]adc read battery ts volt:15 mv
[D][05:18:25][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:25][COMM]adc read throttle brake in adc:3090  volt:5431 mv
[D][05:18:25][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:25][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:25][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:25][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:25][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:11:53:605 ==>> 【转刹把供电电压(主控ADC)】通过,【5431mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:11:53:610 ==>> 检测【转刹把供电电压】
2025-07-31 21:11:53:614 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:53:694 ==>> $GBGGA,131157.500,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,25,,,40,24,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,16,,,37,14,,,36,7,,,36,1*48

$GBGSV,7,4,26,1,,,35,42,,,35,9,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131157.500,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131157.500,0.000,1492.496,1492.496,47.745,2097152,2097152,2097152*51



2025-07-31 21:11:53:904 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:26][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:26][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:26][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:26][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:26][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:26][COMM]adc read battery ts volt:16 mv
[D][05:18:26][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:26][COMM]adc read throttle brake in adc:3090  volt:5431 mv
[D][05:18:26][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:26][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:26][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:26][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:26][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:11:54:147 ==>> 【转刹把供电电压】通过,【5431mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:11:54:152 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:11:54:157 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:54:359 ==>> [D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:27][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:11:54:435 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:11:54:439 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:11:54:445 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:54:541 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:11:54:649 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:54:710 ==>> $GBGGA,131158.500,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,40,25,,,40,24,,,40,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,38,16,,,36,14,,,36,7,,,36,1*49

$GBGSV,7,4,26,42,,,36,1,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,5,26,34,,,35,38,,,34,23,,,34,13,,,34,1*7E

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,33,1*44

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131158.500,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131158.500,0.000,1492.498,1492.498,47.746,2097152,2097152,2097152*5D



2025-07-31 21:11:54:755 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:11:54:800 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:27][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:11:54:860 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:54:875 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:11:54:965 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:11:55:072 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:11:55:102 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:11:55:107 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:11:55:111 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:11:55:176 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:11:55:207 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:11:55:267 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:11:55:342 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:11:55:348 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:11:55:352 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:11:55:464 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:11:55:640 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:11:55:644 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:11:55:652 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:11:55:722 ==>> $GBGGA,131159.500,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,39,24,,,39,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,37,1*7D

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,36,1*46

$GBGSV,7,4,26,42,,,35,1,,,35,9,,,35,6,,,35,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,23,,,34,13,,,34,1*7F

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,32,1*45

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131159.500,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131159.500,0.000,1479.734,1479.734,47.331,2097152,2097152,2097152*58

3A A3 04 01 A3 


2025-07-31 21:11:55:767 ==>> ON_OUT4
OVER 150


2025-07-31 21:11:55:932 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:11:55:939 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:11:55:945 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:11:56:074 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:11:56:231 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:11:56:237 ==>> 检测【左刹电压测试1】
2025-07-31 21:11:56:243 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:56:577 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:29][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:29][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:29][COMM]adc read left brake adc:1732  volt:2283 mv
[D][05:18:29][COMM]adc read right brake adc:1732  volt:2283 mv
[D][05:18:29][COMM]adc read throttle adc:1726  volt:2275 mv
[D][05:18:29][COMM]adc read battery ts volt:20 mv
[D][05:18:29][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:29][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:29][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:29][COMM]arm_hub adc read vbat adc:2398  volt:3863 mv
[D][05:18:29][COMM]arm_hub adc read led yb adc:1424  volt:33015 mv
[D][05:18:29][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:29][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:11:56:683 ==>>                                                                                                                                                                                                                                                                                                                                   34,13,,,34,1*7F

$GBGSV,7,6,26,2,,,34,44,,,33,10,,,33,12,,,32,1*45

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131200.500,V,,,,,,,,0.0,

2025-07-31 21:11:56:713 ==>> E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131200.500,0.000,1476.546,1476.546,47.231,2097152,2097152,2097152*56



2025-07-31 21:11:56:774 ==>> 【左刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 21:11:56:780 ==>> 检测【右刹电压测试1】
2025-07-31 21:11:56:819 ==>> 【右刹电压测试1】通过,【2283】符合目标值【2250】至【2500】要求!
2025-07-31 21:11:56:823 ==>> 检测【转把电压测试1】
2025-07-31 21:11:56:847 ==>> 【转把电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 21:11:56:851 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:11:56:857 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:11:56:972 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 21:11:57:141 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:11:57:145 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:11:57:152 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:11:57:185 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 21:11:57:274 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:11:57:427 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:11:57:432 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:11:57:438 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:11:57:566 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:11:57:671 ==>> $GBGGA,131201.500,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33

2025-07-31 21:11:57:715 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:11:57:719 ==>> 检测【左刹电压测试2】
2025-07-31 21:11:57:726 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:11:57:733 ==>> ,,,41,25,,,40,3,,,39,24,,,39,1*47

$GBGSV,7,2,26,59,,,39,60,,,38,39,,,38,41,,,37,1*7C

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26,42,,,35,1,,,35,9,,,34,6,,,34,1*49

$GBGSV,7,5,26,34,,,34,38,,,34,23,,,34,2,,,34,1*4F

$GBGSV,7,6,26,13,,,33,44,,,33,10,,,33,12,,,32,1*72

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131201.500,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131201.500,0.000,734.502,734.502,671.720,2097152,2097152,2097152*61



2025-07-31 21:11:57:986 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:30][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:30][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:30][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:30][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:30][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:30][COMM]adc read battery ts volt:14 mv
[D][05:18:30][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:30][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:30][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:30][COMM]arm_hub adc read vbat adc:2397  volt:3862 mv
[D][05:18:30][COMM]arm_hub adc read led yb adc:1423  volt:32992 mv
[D][05:18:30][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:30][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:11:58:267 ==>> 【左刹电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 21:11:58:271 ==>> 检测【右刹电压测试2】
2025-07-31 21:11:58:297 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:11:58:301 ==>> 检测【转把电压测试2】
2025-07-31 21:11:58:340 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:11:58:345 ==>> 检测【晶振检测】
2025-07-31 21:11:58:348 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:11:58:560 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:31][COMM][lf state:1][hf state:1]


2025-07-31 21:11:58:635 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:11:58:640 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:11:58:647 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:11:58:665 ==>> $GBGGA,131202.500,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,25,,,40,3,,,39,60,,,39,1*47

$GBGSV,7,2,26,59,,,39,24,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,16,,,36,14,,,36,7,,,35,1*45

$GBGSV,7,4,26

2025-07-31 21:11:58:740 ==>> ,1,,,35,9,,,35,6,,,35,42,,,35,1*49

$GBGSV,7,5,26,38,,,34,34,,,34,23,,,34,2,,,33,1*48

$GBGSV,7,6,26,10,,,33,13,,,33,44,,,33,12,,,32,1*72

$GBGSV,7,7,26,5,,,31,4,,,31,1*73

$GBRMC,131202.500,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131202.500,0.000,736.100,736.100,673.182,2097152,2097152,2097152*6E

1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1664mV
Get AD_V4 1652mV
Get AD_V5 2772mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 21:11:58:920 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:11:58:932 ==>> 检测【检测BootVer】
2025-07-31 21:11:58:942 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:11:59:273 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130020290491
[D][05:18:31][FCTY]HardwareID  = 867222087912343
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:31][FCTY]Bat         = 3944 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 11700 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 32, adc = 1284
[D][05:18:31][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 75 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCT

2025-07-31 21:11:59:318 ==>> Y]Bat1         = 3699 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][COMM]read battery soc:255


2025-07-31 21:11:59:545 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:11:59:549 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:11:59:555 ==>> 检测【检测固件版本】
2025-07-31 21:11:59:666 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:11:59:674 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:11:59:683 ==>> 检测【检测蓝牙版本】
2025-07-31 21:11:59:719 ==>> $GBGGA,131203.500,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,7,,,36,16,,,36,1,,,35,1*71

$GBGSV,7,4,26,9,,,35,6,,,35,42,,,35,14,,,35,1*7D

$GBGSV,7,5,26,13,,,34,38,,,34,34,,,34,23,,,34,1*7F

$GBGSV,7,6,26,2,,,33,10,,,33,44,,,33,12,,,32,1*42

$GBGSV,7,7,26,5,,,31,4,,,31,1*73

$GBRMC,131203.500,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131203.500,0.000,738.491,738.491,675.369,2097152,2097152,2097152*6E



2025-07-31 21:11:59:742 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:11:59:746 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:11:59:750 ==>> 检测【检测MoBikeId】
2025-07-31 21:11:59:827 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:11:59:832 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:11:59:836 ==>> 检测【检测蓝牙地址】
2025-07-31 21:11:59:840 ==>> 取到目标值:C897881DBBB8
2025-07-31 21:11:59:884 ==>> 【检测蓝牙地址】通过,【C897881DBBB8】符合目标值【】要求!
2025-07-31 21:11:59:890 ==>> 提取到蓝牙地址:C897881DBBB8
2025-07-31 21:11:59:894 ==>> 检测【BOARD_ID】
2025-07-31 21:11:59:944 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:11:59:949 ==>> 检测【检测充电电压】
2025-07-31 21:12:00:002 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:12:00:007 ==>> 检测【检测VBUS电压1】
2025-07-31 21:12:00:064 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:12:00:072 ==>> 检测【检测充电电流】
2025-07-31 21:12:00:187 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:12:00:191 ==>> 检测【检测IMEI】
2025-07-31 21:12:00:195 ==>> 取到目标值:867222087912343
2025-07-31 21:12:00:286 ==>> 【检测IMEI】通过,【867222087912343】符合目标值【】要求!
2025-07-31 21:12:00:294 ==>> 提取到IMEI:867222087912343
2025-07-31 21:12:00:315 ==>> 检测【检测IMSI】
2025-07-31 21:12:00:321 ==>> 取到目标值:460130020290491
2025-07-31 21:12:00:387 ==>> 【检测IMSI】通过,【460130020290491】符合目标值【】要求!
2025-07-31 21:12:00:391 ==>> 提取到IMSI:460130020290491
2025-07-31 21:12:00:398 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:12:00:419 ==>> 取到目标值:460130020290491
2025-07-31 21:12:00:431 ==>> 【校验网络运营商(移动)】通过,【460130020290491】符合目标值【】要求!
2025-07-31 21:12:00:439 ==>> 检测【打开CAN通信】
2025-07-31 21:12:00:463 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:12:00:565 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:12:00:670 ==>> $GBGGA,131204.500,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,7,,,36,1,,,36,16,,,36,1*72

$GBGSV,7,4,26,14,,,36,9,,,35,34,,,35,42,,,35,1*4F

$GBGSV,7,5,26,23,,,35,2,,

2025-07-31 21:12:00:715 ==>> ,34,13,,,34,38,,,34,1*4B

$GBGSV,7,6,26,6,,,34,10,,,33,44,,,33,12,,,32,1*41

$GBGSV,7,7,26,5,,,31,4,,,31,1*73

$GBRMC,131204.500,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131204.500,0.000,741.671,741.671,678.276,2097152,2097152,2097152*6B



2025-07-31 21:12:00:722 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:12:00:726 ==>> 检测【检测CAN通信】
2025-07-31 21:12:00:729 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:12:00:895 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:12:00:955 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:33][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 44645


2025-07-31 21:12:01:015 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:12:01:024 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:12:01:046 ==>> 检测【关闭CAN通信】
2025-07-31 21:12:01:052 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:12:01:090 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:12:01:195 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
[D][05:18:33][COMM]read battery soc:255


2025-07-31 21:12:01:322 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:12:01:326 ==>> 检测【打印IMU STATE】
2025-07-31 21:12:01:333 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:12:01:571 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:34][COMM]YAW data: 32763[32763]
[D][05:18:34][COMM]pitch:-66 roll:0
[D][05:18:34][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:12:01:623 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:12:01:628 ==>> 检测【六轴自检】
2025-07-31 21:12:01:632 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:12:01:676 ==>> $GBGGA,131205.500,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,5

2025-07-31 21:12:01:736 ==>> 9,,,39,40,,,38,39,,,38,1*73

$GBGSV,7,3,26,41,,,38,7,,,36,1,,,36,16,,,36,1*7C

$GBGSV,7,4,26,14,,,36,9,,,35,6,,,35,34,,,35,1*7F

$GBGSV,7,5,26,42,,,35,2,,,34,13,,,34,38,,,34,1*4C

$GBGSV,7,6,26,23,,,34,10,,,33,44,,,33,12,,,32,1*76

$GBGSV,7,7,26,4,,,32,5,,,31,1*70

$GBRMC,131205.500,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131205.500,0.000,743.260,743.260,679.730,2097152,2097152,2097152*6C



2025-07-31 21:12:01:841 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:34][CAT1]gsm read msg sub id: 12
[D][05:18:34][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:12:02:738 ==>> $GBGGA,131206.500,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,38,41,,,38,1*72

$GBGSV,7,3,26,40,,,37,7,,,36,16,,,36,14,,,36,1*46

$GBGSV,7,4,26,1,,,35,9,,,35,42,,,35,23,,,35,1*7E

$GBGSV,7,5,26,2,,,34,13,,,34,38,,,34,6,,,34,1*7D

$GBGSV,7,6,26,34,,,34,10,,,33,44,,,33,4,,,32,1*47

$GBGSV,7,7,26,12,,,32,5,,,31,1*47

$GBRMC,131206.500,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131206.500,0.000,740.873,740.873,677.547,2097152,2097152,2097152*63



2025-07-31 21:12:03:216 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 21:12:03:548 ==>> [D][05:18:36][CAT1]<<< 
OK

[D][05:18:36][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:12:03:805 ==>> $GBGGA,131207.500,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,41,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,26,60,,,39,59,,,39,40,,,38,39,,,38,1*73

$GBGSV,7,3,26,41,,,38,7,,,36,16,,,36,14,,,36,1*48

$GBGSV,7,4,26,1,,,35,9,,,35,6,,,35,42,,,35,1*49

$GBGSV,7,5,26,23,,,35,13,,,34,38,,,34,34,,,34,1*7E

$GBGSV,7,6,26,2,,,33,10,,,33,44,,,33,4,,,32,1*75

$GBGSV,7,7,26,12,,,32,5,,,31,1*47

$GBRMC,131207.500,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131207.500,0.000,741.671,741.671,678.276,2097152,2097152,2097152*68

[D][05:18:36][COMM]Main Task receive event:142
[D][05:18:36][COMM]###### 47415 imu self test OK ######
[D][05:18:36][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-13,-4,4034]
[D][05:18:36][COMM]Main Task receive event:142 finished processing


2025-07-31 21:12:04:000 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:12:04:004 ==>> 检测【打印IMU STATE2】
2025-07-31 21:12:04:011 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:12:04:165 ==>> [D][05:18:36][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:36][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:36][COMM]YAW data: 32763[32763]
[D][05:18:36][COMM]pitch:-66 roll:0
[D][05:18:36][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:12:04:336 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:12:04:341 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:12:04:348 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:12:04:469 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:12:04:623 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:12:04:628 ==>> 检测【检测VBUS电压2】
2025-07-31 21:12:04:632 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:12:04:769 ==>> [D][05:18:37][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:37][FCTY]get_ext_48v_vol retry i = 8,volt = 11
$GBGGA,131204.506,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,24,,,40,25,,,40,1*46

$GBGSV,7,2,27,60,,,39,59,,,39,40,,,38,39,,,38,1*72

$GBGSV,7,3,27,41,,,38,7,,,36,16,,,36,14,,,36,1*49

$GBGSV,7,4,27,1,,,35,9,,,35,6,,,35,42,,,35,1*48

$GBGSV,7,5,27,34,,,35,23,,,35,13,,,34,38,,,34,1*7E

$GBGSV,7,6,27,2,,,33,10,,,33,44,,,33,4,,,32,1*74

$GBGSV,7,7,27,12,,,32,5,,,31,11,,,37,1*42

$GBRMC,131204.506,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131204.506,2.712,742.466,742.466,679.003,2097152,2097152,2097152*6A



2025-07-31 21:12:05:057 ==>> [D][05:18:37][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[W][05:18:37][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:37][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========
[D][05:18:37][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:37][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:37][FCTY]DeviceID    = 460130020290491
[D][05:18:37][FCTY]HardwareID  = 867222087912343
[D][05:18:37][FCTY]MoBikeID    = 9999999999
[D][05:18:37][FCTY]LockID      = FFFFFFFFFF
[D][05:18:37][FCTY]BLEFWVersion= 105
[D][05:18:37][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:37][FCTY]Bat         = 3944 mv
[D][05:18:37][FCTY]Current     = 50 ma
[D][05:18:37][FCTY]VBUS        = 9000 mv
[D][05:18:37][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:37][FCTY]Ext battery vol = 7, adc = 299
[D][05:18:37][FCTY]Acckey1 vol = 5533 mv, Acckey2 vol = 25 mv
[D][05:18:37][FCTY]Bike Type flag is invalied
[D][05:18:37][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:37][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:37][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:37][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:37][FCTY]CAT1_GNSS_VERSION = V3

2025-07-31 21:12:05:102 ==>> 465b5b1
[D][05:18:37][FCTY]Bat1         = 3699 mv
[D][05:18:37][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:37][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:12:05:162 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:12:05:524 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][05:18:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:38][FCTY]DeviceID    = 460130020290491
[D][05:18:38][FCTY]HardwareID  = 867222087912343
[D][05:18:38][FCTY]MoBikeID    = 9999999999
[D][05:18:38][FCTY]LockID      = FFFFFFFFFF
[D][05:18:38][FCTY]BLEFWVersion= 105
[D][05:18:38][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:38][FCTY]Bat         = 3944 mv
[D][05:18:38][FCTY]Current     = 50 ma
[D][05:18:38][FCTY]VBUS        = 9000 mv
[D][05:18:38][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:38][FCTY]Ext battery vol = 4, adc = 182
[D][05:18:38][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 202 mv
[D][05:18:38][FCTY]Bike Type flag is invalied
[D][05:18:38][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:38][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][

2025-07-31 21:12:05:569 ==>> 05:18:38][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:38][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:38][FCTY]Bat1         = 3699 mv
[D][05:18:38][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:12:05:732 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:12:05:779 ==>> $GBGGA,131205.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,65,237,41,3,61,190,40,6,55,45,35,39,53,15,38,1*7E

$GBGSV,7,2,26,59,52,129,39,40,52,177,38,16,52,355,36,14,51,187,36,1*79

$GBGSV,7,3,26,7,48,194,36,1,48,126,35,25,46,303,40,2,45,238,33,1*41

$GBGSV,7,4,26,24,42,28,40,60,41,238,39,9,39,324,35,10,37,202,33,1*7A

$GBGSV,7,5,26,4,32,112,32,13,32,217,34,5,22,257,31,38,16,198,34,1*7A

$GBGSV,7,6,26,42,8,322,35,41,,,38,23,,,35,34,,,34,1*71

$GBGSV,7,7,26,44,,,33,12,,,32,1*70

$GBRMC,131205.506,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,131205.506,0.513,0.267,0.226,0.398,1.931,3.576,12*52



2025-07-31 21:12:06:068 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:38][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][05:18:38][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:38][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:38][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:38][FCTY]DeviceID    = 460130020290491
[D][05:18:38][FCTY]HardwareID  = 867222087912343
[D][05:18:38][FCTY]MoBikeID    = 9999999999
[D][05:18:38][FCTY]LockID      = FFFFFFFFFF
[D][05:18:38][FCTY]BLEFWVersion= 105
[D][05:18:38][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:38][FCTY]Bat         = 3704 mv
[D][05:18:38][FCTY]Current     = 0 ma
[D][05:18:38][FCTY]VBUS        = 9000 mv
[D][05:18:38][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:38][FCTY]Ext battery vol = 3, adc = 149
[D][05:18:38][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 0 mv
[D][05:18:38][FCTY]Bike Type flag is invalied
[D][05:18:38][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:38][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:38][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:38][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18

2025-07-31 21:12:06:128 ==>> :38][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:38][FCTY]Bat1         = 3699 mv
[D][05:18:38][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:38][FCTY]==========Modules-nRF5340 ==========
[D][05:18:38][COMM]msg 0601 loss. last_tick:44634. cur_tick:49651. period:500
[D][05:18:38][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 49652


2025-07-31 21:12:06:333 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:12:07:533 ==>> [D][05:18:39][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:39][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:39][COMM]Main Task receive event:65
[D][05:18:39][COMM]main task tmp_sleep_event = 80
[D][05:18:39][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:39][COMM]Main Task receive event:65 finished processing
[D][05:18:39][COMM]Main Task receive event:60
[D][05:18:39][COMM]smart_helmet_vol=255,255
[D][05:18:39][COMM]BAT CAN get state1 Fail 204
[D][05:18:39][COMM]BAT CAN get soc Fail, 204
[W][05:18:39][GNSS]stop locating
[D][05:18:39][GNSS]stop event:8
[D][05:18:39][GNSS]all continue location stop
[W][05:18:39][GNSS]sing locating running
[D][05:18:39][COMM]report elecbike
[W][05:18:39][PROT]remove success[1629955119],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:39][PROT]add success [1629955119],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:39][COMM]Main Task receive event:60 finished processing
[D][05:18:39][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:39][PROT]index:0
[D][05:18:39][PROT]is_send:1
[D][05:18:39][PROT]sequence_num:4
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:3
[D][05:18:39]

2025-07-31 21:12:07:638 ==>> [PROT]send_path:0x3
[D][05:18:39][PROT]msg_type:0x5d03
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]Sending traceid[9999999999900005]
[D][05:18:39][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:39][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:39][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:39][PROT]index:0 1629955119
[D][05:18:39][PROT]is_send:0
[D][05:18:39][PROT]sequence_num:4
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:3
[D][05:18:39][PROT]send_path:0x2
[D][05:18:39][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]sending traceid [9999999999900005]
[D][05:18:39][PROT]Send_TO_M2M [1629955119]
[D][05:18:39][M2M ]m2m switch to

2025-07-31 21:12:07:743 ==>> : M2M_GSM_SOCKET_SEND
[D][05:18:39][SAL ]sock send credit cnt[6]
[D][05:18:39][SAL ]sock send ind credit cnt[6]
[D][05:18:39][M2M ]m2m send data len[198]
[D][05:18:39][SAL ]Cellular task submsg id[10]
[D][05:18:39][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:39][CAT1]gsm read msg sub id: 15
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:39][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B560682EAB0000ABE27EE27E9EF06D089CCBF402C3B9582C6CA4375AEE8A6BF2E1023B7BF8172DF397C182DF29EA9073B72CD77ADD890767F63AE8B8DD334A7902AEEBBBA078984C4EC4A14462FC073FCC6943
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]
[W][05:18:39][COMM]>>>>>Input command = AT+INFO<<<<<


2025-07-31 21:12:07:848 ==>> 
[D][05:18:39][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========
[D][05:18:39][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:39][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:39][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:39][FCTY]DeviceID    = 460130020290491
[D][05:18:39][FCTY]HardwareID  = 867222087912343
[D][05:18:39][FCTY]MoBikeID    = 9999999999
[D][05:18:39][FCTY]LockID      = FFFFFFFFFF
[D][05:18:39][FCTY]BLEFWVersion= 105
[D][05:18:39][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:39][FCTY]Bat         = 3704 mv
[D][05:18:39][FCTY]Current     = 0 ma
[D][05:18:39][FCTY]VBUS        = 5000 mv
[D][05:18:39][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:39][FCTY]Ext battery vol = 2, adc = 118
[D][05:18:39][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 126 mv
[D][05:18:39][FCTY]Bike Type flag is invalied
[D][05:18:39][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:39][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:39][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:39][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:39][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:39][FCT

2025-07-31 21:12:07:908 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:12:07:953 ==>> Y]Bat1         = 3699 mv
[D][05:18:39][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:39][FCTY]==========Modules-nRF5340 ==========
$GBGGA,131206.506,2301.2577360,N,11421.9421871,E,1,11,1.03,75.385,M,-1.770,M,,*5D

$GBGSA,A,3,33,39,16,14,59,25,40,60,24,13,38,,2.61,1.03,2.40,4*07

$GBGSV,7,1,26,33,65,237,41,3,61,190,40,6,55,45,35,39,53,15,38,1*7E

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,7,48,194,36,1*40

$GBGSV,7,3,26,1,48,126,36,25,46,303,40,2,45,238,34,40,44,161,38,1*7E

$GBGSV,7,4,26,60,42,240,39,24,42,28,40,9,39,324,35,10,37,202,33,1*76

$GBGSV,7,5,26,4,32,112,32,13,32,217,34,5,22,257,31,38,20,192,34,1*75

$GBGSV,7,6,26,44,20,86,33,42,8,322,35,41,,,38,23,,,35,1*7D

$GBGSV,7,7,26,34,,,34,12,,,32,1*70

$GBGSV,1,1,04,33,65,237,42,39,53,15,41,25,46,303,41,24,42,28,39,5*78

$GBRMC,131206.506,A,2301.2577360,N,11421.9421871,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:39][GNSS]HD8040 GPS
[D][05:18:39][GNSS]GPS diff_sec 124012407, report 0x42 frame
$GBGST,131206.506,1.514,0.247,0.225,0.374,1.738,2.338,7.766*73

[D][05:18:39][COMM]Main Task receive event:131
[D][05:18:39][COMM]index:0,power_mod

2025-07-31 21:12:08:058 ==>> e:0xFF
[D][05:18:39][COMM]index:1,sound_mode:0xFF
[D][05:18:39][COMM]index:2,gsensor_mode:0xFF
[D][05:18:39][COMM]index:3,report_freq_mode:0xFF
[D][05:18:39][COMM]index:4,report_period:0xFF
[D][05:18:39][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:39][COMM]index:6,normal_reset_period:0xFF
[D][05:18:39][COMM]index:7,spock_over_speed:0xFF
[D][05:18:39][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:39][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:39][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:39][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:39][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:39][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:39][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:39][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:39][COMM]index:16,imu_config_params:0xFF
[D][05:18:39][COMM]index:17,long_connect_params:0xFF
[D][05:18:39][COMM]index:18,detain_mark:0xFF
[D][05:18:39][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:39][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:39][COMM]index:21,mc_mode:0xFF
[D][05:18:39][COMM]index:22,S_mode:0xFF
[D][05:18:39][COMM]index:23,overweight:0xFF
[D][05:18:39][COMM]index:24,stands

2025-07-31 21:12:08:163 ==>> till_mode:0xFF
[D][05:18:39][COMM]index:25,night_mode:0xFF
[D][05:18:39][COMM]index:26,experiment1:0xFF
[D][05:18:39][COMM]index:27,experiment2:0xFF
[D][05:18:39][COMM]index:28,experiment3:0xFF
[D][05:18:39][COMM]index:29,experiment4:0xFF
[D][05:18:39][COMM]index:30,night_mode_start:0xFF
[D][05:18:39][COMM]index:31,night_mode_end:0xFF
[D][05:18:39][COMM]index:33,park_report_minutes:0xFF
[D][05:18:39][COMM]index:34,park_report_mode:0xFF
[D][05:18:39][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:39][COMM]index:38,charge_battery_para: FF
[D][05:18:39][COMM]index:39,multirider_mode:0xFF
[D][05:18:39][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:39][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:39][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:39][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:39][COMM]index:44,riding_duration_config:0xFF


2025-07-31 21:12:08:469 ==>>                                                                                                                                                                                                                                                                                                                                                                                                        ,179,36,2,45,238,34,1*40

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,39,10,37,202,33,1*41

$GBGSV,7,5,26,4,32,112,32,13,32,217,34,5,22,257,31,38,20,192,34,1*75

$GBGSV,7,6,26,44,20,86,34,42,8,322,35,41,,,38,34,,,35,1*7C

$GBGSV,7,7,26,23,,,35,12,,,32,1*77

$GBGSV,2,1,07,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,2,2,07,24,42,28,39,38,20,192,31,44,20,86,37,5*4C

$GBRMC,131208.000,A,2301.2581225,N,11421.9415869,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,131208.000,1.668,0.243,0.227,0.368,1.465,1.721,5.051*78

[W][05:18:40][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:40][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========
[D][05:18:40][FCTY]BootVersion = SA_BOOT_V1

2025-07-31 21:12:08:574 ==>> 09
[D][05:18:40][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:40][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:40][FCTY]DeviceID    = 460130020290491
[D][05:18:40][FCTY]HardwareID  = 867222087912343
[D][05:18:40][FCTY]MoBikeID    = 9999999999
[D][05:18:40][FCTY]LockID      = FFFFFFFFFF
[D][05:18:40][FCTY]BLEFWVersion= 105
[D][05:18:40][FCTY]BLEMacAddr   = C897881DBBB8
[D][05:18:40][FCTY]Bat         = 3644 mv
[D][05:18:40][FCTY]Current     = 0 ma
[D][05:18:40][FCTY]VBUS        = 5000 mv
[D][05:18:40][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:40][FCTY]Ext battery vol = 2, adc = 95
[D][05:18:40][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 75 mv
[D][05:18:40][FCTY]Bike Type flag is invalied
[D][05:18:40][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:40][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:40][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:40][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:40][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:40][FCTY]Bat1         = 3699 mv
[D][05:18:40][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:40][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:12:08:724 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:12:08:729 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:12:08:736 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:12:08:864 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:12:08:954 ==>> [D][05:18:41][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28


2025-07-31 21:12:09:029 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:12:09:095 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:12:09:100 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:12:09:107 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:12:09:164 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:12:09:269 ==>> $GBGGA,131209.000,2301.2581451,N,11421.9415382,E,1,18,0.74,77.1

2025-07-31 21:12:09:374 ==>> 56,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,39,16,14,59,02,09,25,07,01,40,1.85,0.74,1.69,4*00

$GBGSA,A,3,60,24,10,13,38,44,,,,,,,1.85,0.74,1.69,4*01

$GBGSV,7,1,26,33,65,237,42,3,62,190,40,6,55,45,35,39,53,15,38,1*7E

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,40,10,37,191,33,1*46

$GBGSV,7,5,26,4,32,112,32,13,32,217,34,5,22,257,31,12,21,112,32,1*72

$GBGSV,7,6,26,38,20,192,34,44,20,86,34,42,8,322,35,41,,,38,1*49

$GBGSV,7,7,26,34,,,35,23,,,35,1*74

$GBGSV,2,1,07,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,2,2,07,24,42,28,39,38,20,192,31,44,20,86,37,5*4C

$GBRMC,131209.000,A,2301.2581451,N,11421.9415382,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131209.000,1.801,0.253,0.239,0.385,1.497,1.706,4.603*7D



2025-07-31 21:12:09:455 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:12:09:460 ==>> 检测【打开WIFI(3)】
2025-07-31 21:12:09:466 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:12:09:691 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:12:09:737 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:12:09:747 ==>> 检测【扩展芯片hw】
2025-07-31 21:12:09:755 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:12:09:965 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:42][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:12:10:036 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:12:10:046 ==>> 检测【扩展芯片boot】
2025-07-31 21:12:10:075 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:12:10:083 ==>> 检测【扩展芯片sw】
2025-07-31 21:12:10:115 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:12:10:148 ==>> 检测【检测音频FLASH】
2025-07-31 21:12:10:155 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:12:10:373 ==>> $GBGGA,131210.000,2301.2581846,N,11421.9415062,E,1,21,0.70,77.155,M,-1.770,M,,*59

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.72,0.70,1.57,4*03

$GBGSA,A,3,40,60,24,10,13,42,12,38,44,,,,1.72,0.70,1.57,4*01

$GBGSV,7,1,26,33,65,237,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,39,10,37,191,33,1*48

$GBGSV,7,5,26,4,32,112,31,13,32,217,34,42,27,167,35,34,22,142,34,1*47

$GBGSV,7,6,26,5,22,257,32,12,21,112,32,38,20,192,33,44,20,86,34,1*7C

$GBGSV,7,7,26,23,10,264,35,41,,,38,1*4A

$GBGSV,2,1,07,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,39,5*41

$GBGSV,2,2,07,24,42,28,40,38,20,192,31,44,20,86,37,5*42

$GBRMC,131210.000,A,2301.2581846,N,11421.9415062,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,131210.000,1.910,0.282,0.265,0.428,1.533,1.711,4.287*70

[W][05:18:42][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:12:10:553 ==>> [D][05:18:43][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:43][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:0------------
[D][05:18:43][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:12:11:395 ==>>                                                                                                                                                                       d value:1------------
[D][05:18:43][COMM]more than the number of battery plugs
[D][05:18:43][COMM]VBUS is 1
[D][05:18:43][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:43][COMM]file:B50 exist
+WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,F42A7D1297A3,-70
+WIFISCAN:4,2,44A1917CA62F,-75
+WIFISCAN:4,3,44A1917CAD80,-77

[D][05:18:43][CAT1]wifi scan report total[4]
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:43][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:43][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:43][COMM]Bat auth off fail, error:-1
[D][05:18:43][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:1------------
[D][05:18:43][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:43][COMM]----- get Acckey 1 and value:1------------
[D][05:18:43][COMM]----- get Acckey 2 and value:1------------
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]fi

2025-07-31 21:12:11:500 ==>> le:B50 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:43][COMM]read file, len:10800, num:3
[D][05:18:43][COMM]--->crc16:0xb8a
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel parse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:43][COMM]Main Task receive event:65
[D][05:18:43][COMM]main task tmp_sleep_event = 80
[D][05:18:43][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:43][COMM]Main Task receive event:65 finished processing
[D][05:18:43][COMM]Main Task receive event:66
[D][05:18:43][COMM]Try to Auto Lock Bat
[D][05:18:43][COMM]Main Task receive event:66 finished processing
[D][05:18:43]

2025-07-31 21:12:11:605 ==>> [COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:43][COMM]Main Task receive event:60
[D][05:18:43][COMM]smart_helmet_vol=255,255
[D][05:18:43][COMM]BAT CAN get state1 Fail 204
[D][05:18:43][COMM]BAT CAN get soc Fail, 204
[D][05:18:43][COMM]get soc error
[E][05:18:43][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:43][COMM]report elecbike
[W][05:18:43][PROT]remove success[1629955123],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:18:43][PROT]add success [1629955123],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:18:43][COMM]Main Task receive event:60 finished processing
[D][05:18:43][COMM]Main Task receive event:61
[D][05:18:43][COMM][D301]:type:3, trace id:280
[D][05:18:43][COMM]id[], hw[000
[D][05:18:43][COMM]get mcMaincircuitVolt error
[D][05:18:43][COMM]get mcSubcircuitVolt error
[D][05:18:43][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:43][COMM]BAT CAN get state1 Fail 204
[D][05:18:43][COMM]BAT CAN get soc Fail, 204
[D][05:18:43][COMM]get bat work state err
[W][05:18:43][PROT]remove success[1629955123],send_path[2],type[00

2025-07-31 21:12:11:710 ==>> 00],priority[0],index[3],used[0]
[W][05:18:43][PROT]add success [1629955123],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:18:43][COMM]Main Task receive event:61 finished processing
[D][05:18:43][COMM]Receive Bat Lock cmd 0
[D][05:18:43][COMM]VBUS is 1
[D][05:18:43][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:43][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:43][PROT]index:2
[D][05:18:43][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:43][PROT]is_send:1
[D][05:18:43][PROT]sequence_num:6
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:3
[D][05:18:43][PROT]send_path:0x3
[D][05:18:43][PROT]msg_type:0x5d03
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]Sending traceid[9999999999900007]
[D][05:18:43][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:43][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:43][PROT]ble is not inited or not connected or cccd not enabled
[D]

2025-07-31 21:12:11:815 ==>> [05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:43][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:43][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4

2025-07-31 21:12:11:920 ==>> , len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][GNSS]recv submsg id[3]
$GBGGA,131211.000,2301.2581903,N,11421.9415466,E,1,23,0.60,77.179,M,-1.770,M,,*55

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.29,0.60,1.14,4*0B

$GBGSA,A,3,40,60,24,10,13,42,34,12,38,44,23,,1.29,0.60,1.14,4*0F

$GBGSV,7,1,26,33,65,237,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,39,1

2025-07-31 21:12:12:025 ==>> 0,37,191,33,1*48

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,26,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,33,1*42

$GBGSV,7,7,26,44,20,86,33,23,10,264,34,1*49

$GBGSV,3,1,10,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,10,24,42,28,39,42,27,167,35,34,22,142,33,38,20,192,31,5*46

$GBGSV,3,3,10,44,20,86,37,23,10,264,33,5*4B

$GBRMC,131211.000,A,2301.2581903,N,11421.9415466,E,0.000,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,131211.000,3.713,0.272,0.268,0.400,2.555,2.674,4.640*7B

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 21:12:12:130 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:12:12:160 ==>>                                                                                                                            

2025-07-31 21:12:12:386 ==>> $GBGGA,131212.000,2301.2581680,N,11421.9415840,E,1,24,0.58,77.283,M,-1.770,M,,*50

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,237,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,33,1*4B

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,40,10,37,191,33,1*46

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,26,34,22,142,34,5,22,257,31,12,21,112,32,38,20,192,33,1*43

$GBGSV,7,7,26,44,20,86,33,23,10,264,34,1*49

$GBGSV,3,1,11,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*46

$GBGSV,3,2,11,24,42,28,40,41,34,311,38,42,27,167,36,34,22,142,33,5*41

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,32,5*7A

$GBRMC,131212.000,A,2301.2581680,N,11421.9415840,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,131212.000,3.360,0.258,0.254,0.379,2.356,2.460,4.231*76



2025-07-31 21:12:13:048 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 21:12:13:398 ==>> $GBGGA,131213.000,2301.2581495,N,11421.9415729,E,1,24,0.58,77.146,M,-1.770,M,,*5D

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,237,42,3,62,190,40,39,53,15,38,6,52,351,34,1*4E

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,42,28,39,10,37,191,33,1*48

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,33,42,27,167,35,1*4E

$GBGSV,7,6,26,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*45

$GBGSV,7,7,26,44,20,86,33,23,10,264,35,1*48

$GBGSV,3,1,11,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*46

$GBGSV,3,2,11,24,42,28,40,41,34,311,38,42,27,167,37,34,22,142,33,5*40

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,32,5*79

$GBRMC,131213.000,A,2301.2581495,N,11421.9415729,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:45][GNSS][RTK]first enter, init gPos info.
$GBGST,131213.000,3.445,0.244,0.240,0.360,2.388,2.473,4.023*77



2025-07-31 21:12:13:657 ==>> [D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:12:14:396 ==>> $GBGGA,131214.000,2301.2581451,N,11421.9415659,E,1,24,0.58,77.017,M,-1.770,M,,*51

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,237,42,3,62,190,40,39,53,15,38,6,52,351,34,1*4E

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,41,28,40,10,37,191,33,1*45

$GBGSV,7,5,26,41,34,311,38,4,32,112,31,13,32,217,33,42,27,167,35,1*4D

$GBGSV,7,6,26,34,22,142,34,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,26,44,20,86,34,23,10,264,35,1*4F

$GBGSV,3,1,11,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,38,42,27,167,37,34,22,142,33,5*43

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,32,5*7A

$GBRMC,131214.000,A,2301.2581451,N,11421.9415659,E,0.000,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,131214.000,3.695,0.224,0.221,0.328,2.509,2.579,3.960*7F



2025-07-31 21:12:14:426 ==>>                                                                

2025-07-31 21:12:14:745 ==>> [D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:47][COMM]58357 imu init OK
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:47][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:47][COMM]accel parse set 0
[D][05:18:47][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:12:15:053 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 21:12:15:390 ==>> $GBGGA,131215.000,2301.2581526,N,11421.9415548,E,1,24,0.58,76.818,M,-1.770,M,,*54

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,237,42,3,62,190,40,39,53,15,38,6,52,351,35,1*4F

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4E

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4B

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,26,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*45

$GBGSV,7,7,26,44,20,86,34,23,10,264,35,1*4F

$GBGSV,3,1,11,33,65,237,43,39,53,15,41,25,46,303,41,40,44,161,38,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,38,42,27,167,37,34,22,142,33,5*43

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,32,5*79

$GBRMC,131215.000,A,2301.2581526,N,11421.9415548,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,131215.000,3.643,0.214,0.211,0.317,2.476,2.537,3.812*7E



2025-07-31 21:12:15:649 ==>> [D][05:18:48][COMM]59368 imu init OK


2025-07-31 21:12:16:393 ==>> $GBGGA,131216.000,2301.2581627,N,11421.9415514,E,1,24,0.58,76.749,M,-1.770,M,,*57

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,07,25,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4D

$GBGSV,7,2,26,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4C

$GBGSV,7,3,26,9,47,328,35,7,46,179,36,25,46,303,40,1,46,125,36,1*4D

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,41,28,40,10,37,191,33,1*45

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,26,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*46

$GBGSV,7,7,26,44,20,86,34,23,10,264,35,1*4F

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,32,5*7A

$GBRMC,131216.000,A,2301.2581627,N,11421.9415514,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131216.000,3.598,0.206,0.203,0.305,2.447,2.501,3.693*7B



2025-07-31 21:12:17:130 ==>> [D][05:18:49][PROT]CLEAN,SEND:2
[D][05:18:49][PROT]index:2 1629955129
[D][05:18:49][PROT]is_send:0
[D][05:18:49][PROT]sequence_num:6
[D][05:18:49][PROT]retry_timeout:0
[D][05:18:49][PROT]retry_times:2
[D][05:18:49][PROT]send_path:0x2
[D][05:18:49][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:49][PROT]===========================================================
[W][05:18:49][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955129]
[D][05:18:49][PROT]===========================================================
[D][05:18:49][PROT]sending traceid [9999999999900007]
[D][05:18:49][PROT]Send_TO_M2M [1629955129]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:49][SAL ]sock send credit cnt[6]
[D][05:18:49][SAL ]sock send ind credit cnt[6]
[D][05:18:49][M2M ]m2m send data len[198]
[D][05:18:49][SAL ]Cellular task submsg id[10]
[D][05:18:49][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]gsm read msg sub id: 15
[D][05:18:49][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE987879F459C6174FC35F2C

2025-07-31 21:12:17:205 ==>> 8A4EDAA40B4BE92D864BB49CCF20EFE6C404043BAFC739987FD89D5754956C44C74CE0D5E7A532D757183073D45BA2FA29CA10CAEA5092868C46DBCAE1F648FE017F2E7867AB3D
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]
[D][05:18:49][COMM]read battery soc:255


2025-07-31 21:12:17:310 ==>>                                                                                                                                                                                                                                                                                                                 

2025-07-31 21:12:17:400 ==>>                                                                                                                                                                                                                                                                                            GBGSV,7,6,26,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*45

$GBGSV,7,7,26,44,20,86,34,23,10,264,34,1*4E

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,3,2,11,24,41,28,39,41,34,311,39,42,27,167,37,34,22,142,33,5*4C

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,32,5*7A

$GBRMC,131217.000,A,2301.2581718,N,11421.9415324,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131217.000,3.498,0.191,0.188,0.280,2.390,2.440,3.568*74



2025-07-31 21:12:18:388 ==>> $GBGGA,131218.000,2301.2581720,N,11421.9415247,E,1,24,0.58,76.563,M,-1.770,M,,*54

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,26,33,65,236,42,3,62,190,40,39,53,15,38,6,52,351,35,1*4E

$GBGSV,7,2,26,16,52,355,37,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,26,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4D

$GBGSV,7,4,26,40,44,161,38,60,42,240,39,24,41,28,40,10,37,191,33,1*45

$GBGSV,7,5,26,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,26,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*45

$GBGSV,7,7,26,44,20,86,34,23,10,264,34,1*4E

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,39,41,34,311,39,42,27,167,37,34,22,142,33,5*4C

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,32,5*7A

$GBRMC,131218.000,A,2301.2581720,N,11421.9415247,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131218.000,3.546,0.177,0.175,0.261,2.411,2.457,3.524*7C



2025-07-31 21:12:19:052 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:12:19:387 ==>> $GBGGA,131219.000,2301.2581756,N,11421.9415336,E,1,24,0.58,76.541,M,-1.770,M,,*53

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,40,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,311,38,4,32,112,31,13,32,217,34,42,27,167,35,1*4B

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,15,202,32,23,10,264,34,1*42

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,32,5*79

$GBRMC,131219.000,A,2301.2581756,N,11421.9415336,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,131219.000,2.449,0.215,0.212,0.316,1.781,1.830,2.941*7F



2025-07-31 21:12:20:219 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:12:20:404 ==>> $GBGGA,131220.000,2301.2581814,N,11421.9415399,E,1,24,0.58,76.447,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,31,13,32,217,34,42,27,167,35,1*4B

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,32,5*79

$GBRMC,131220.000,A,2301.2581814,N,11421.9415399,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131220.000,2.517,0.222,0.219,0.327,1.820,1.865,2.923*72

[W][05:18:53][COMM]>>>>>Input command = AT+FLASH

2025-07-31 21:12:20:434 ==>> _TEST=4235,2<<<<<


2025-07-31 21:12:21:076 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:12:21:395 ==>> [D][05:18:53][COMM]IMU: [-11,-3,-997] ret=32 AWAKE!
$GBGGA,131221.000,2301.2581845,N,11421.9415296,E,1,24,0.58,76.360,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,32,5*79

$GBRMC,131221.000,A,2301.2581845,N,11421.9415296,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131221.000,2.574,0.211,0.208,0.312,1.852,1.894,2.908*72



2025-07-31 21:12:22:448 ==>> [D][05:18:54][PROT]CLEAN,SEND:2
[D][05:18:54][PROT]index:2 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:6
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:1
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900007]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BE0

2025-07-31 21:12:22:553 ==>> 8C6396143EBA7BC629B6B750B423C927D93BCCFA0261972E3A12CB897CF5B5A20B231A89BA832E8B8CB2F386B14B724C76041867314240332BBD6684B0DC37BF220B38810FAD156E7EEDFFFAA9552915C31
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]
$GBGGA,131222.000,2301.2581891,N,11421.9415296,E,1,24,0.58,76.330,M,-1.770,M,,*54

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,39,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,

2025-07-31 21:12:22:613 ==>> 34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,39,41,34,311,39,42,27,167,37,34,22,142,33,5*4C

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131222.000,A,2301.2581891,N,11421.9415296,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131222.000,2.525,0.197,0.195,0.291,1.820,1.859,2.843*7F



2025-07-31 21:12:23:075 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:12:23:395 ==>> $GBGGA,131223.000,2301.2581945,N,11421.9415272,E,1,24,0.58,76.309,M,-1.770,M,,*5D

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,39,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4C

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,33,5*78

$GBRMC,131223.000,A,2301.2581945,N,11421.9415272,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131223.000,2.782,0.191,0.188,0.282,1.974,2.009,2.943*76



2025-07-31 21:12:24:397 ==>> $GBGGA,131224.000,2301.2581997,N,11421.9415193,E,1,24,0.58,76.263,M,-1.770,M,,*54

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,34,1*4D

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131224.000,A,2301.2581997,N,11421.9415193,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,131224.000,3.731,0.199,0.196,0.293,2.495,2.524,3.379*76



2025-07-31 21:12:24:427 ==>>                                                                

2025-07-31 21:12:25:089 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 21:12:25:392 ==>> $GBGGA,131225.000,2301.2582026,N,11421.9415113,E,1,24,0.58,76.252,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,38,6,52,351,35,1*4F

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,40,2,47,239,34,1*43

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131225.000,A,2301.2582026,N,11421.9415113,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131225.000,3.687,0.189,0.186,0.276,2.471,2.499,3.335*75



2025-07-31 21:12:26:396 ==>> $GBGGA,131226.000,2301.2582004,N,11421.9415016,E,1,24,0.58,76.190,M,-1.770,M,,*55

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,40,24,41,28,39,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131226.000,A,2301.2582004,N,11421.9415016,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131226.000,3.710,0.221,0.217,0.325,2.481,2.508,3.325*73



2025-07-31 21:12:27:094 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 21:12:27:587 ==>> $GBGGA,131227.000,2301.2582048,N,11421.9414885,E,1,24,0.58,76.115,M,-1.770,M,,*52

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,35,1*4F

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,31,13,32,217,34,42,27,167,35,1*4B

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,38,5*47

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,33,5*78

$GBRMC,131227.000,A,2301.2582048,N,11421.9414885,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,131227.000,2.527,0.214,0.211,0.315,1.814,1.844,2.707*76

[D][05:19:00][PROT]CLEAN,SEND:2
[D][05:19:00][PROT]CLEAN:2
[D][05:19:00][PROT]

2025-07-31 21:12:27:692 ==>> index:0 1629955140
[D][05:19:00][PROT]is_send:0
[D][05:19:00][PROT]sequence_num:4
[D][05:19:00][PROT]retry_timeout:0
[D][05:19:00][PROT]retry_times:2
[D][05:19:00][PROT]send_path:0x2
[D][05:19:00][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:00][PROT]===========================================================
[D][05:19:00][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:00][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955140]
[D][05:19:00][PROT]===========================================================
[D][05:19:00][PROT]sending traceid [9999999999900005]
[D][05:19:00][PROT]Send_TO_M2M [1629955140]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:00][SAL ]sock send credit cnt[6]
[D][05:19:00][SAL ]sock send ind credit cnt[6]
[D][05:19:00][M2M ]m2m send data len[198]
[D][05:19:00][SAL ]Cellular task submsg id[10]
[D][05:19:00][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:00][CAT1]gsm read msg sub id: 15
[D][05:19:00][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:00][CAT1]Send Data To Server[198][201] ... ->:
0063B9821

2025-07-31 21:12:27:767 ==>> 13311331133113311331B88B5895C0F799B28C33107B3875694F15084BB5B8B17F8F56E33B6752EB0887E75102E0BF6C782DAEA8AEAEE0FD7D3911FCC2AA28C7DA5F25B0115B1F7927BEAA34BD54E9A643D5BC17A9ED3E79C24735F206195
[D][05:19:00][CAT1]<<< 
SEND OK

[D][05:19:00][CAT1]exec over: func id: 15, ret: 11
[D][05:19:00][CAT1]sub id: 15, ret: 11

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:00][M2M ]g_m2m_is_idle become true
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:00][PROT]M2M Send ok [1629955140]


2025-07-31 21:12:28:384 ==>> $GBGGA,131228.000,2301.2582067,N,11421.9414779,E,1,24,0.58,76.073,M,-1.770,M,,*5D

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,39,6,52,351,34,1*4F

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,34,1*4D

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4C

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,38,5*40

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,33,5*78

$GBRMC,131228.000,A,2301.2582067,N,11421.9414779,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,131228.000,2.522,0.200,0.198,0.293,1.809,1.838,2.685*78



2025-07-31 21:12:29:095 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 21:12:29:384 ==>> $GBGGA,131229.000,2301.2582116,N,11421.9414761,E,1,24,0.58,76.065,M,-1.770,M,,*55

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,39,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,35,1*4C

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4C

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,31,12,21,112,32,38,20,192,34,1*44

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,33,5*78

$GBRMC,131229.000,A,2301.2582116,N,11421.9414761,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131229.000,2.368,0.225,0.221,0.332,1.711,1.739,2.580*73



2025-07-31 21:12:30:285 ==>> 未匹配到【检测音频FLASH】数据,请核对检查!
2025-07-31 21:12:30:297 ==>> #################### 【测试结束】 ####################
2025-07-31 21:12:30:390 ==>> $GBGGA,131230.000,2301.2582158,N,11421.9414777,E,1,24,0.58,76.044,M,-1.770,M,,*53

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,39,2,47,239,35,1*4C

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4C

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,32,44,20,86,37,23,10,264,33,5*78

$GBRMC,131230.000,A,2301.2582158,N,11421.9414777,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,131230.000,2.350,0.219,0.216,0.326,1.698,1.726,2.553*7E



2025-07-31 21:12:30:432 ==>> 关闭5V供电
2025-07-31 21:12:30:442 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:12:30:570 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:12:31:094 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 21:12:31:397 ==>> $GBGGA,131231.000,2301.2582173,N,11421.9414724,E,1,24,0.58,76.036,M,-1.770,M,,*58

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,41,3,62,190,40,39,53,15,38,6,52,351,35,1*4C

$GBGSV,7,2,27,16,52,355,36,14,51,187,36,59,50,129,40,2,47,239,34,1*43

$GBGSV,7,3,27,9,47,328,35,25,46,303,40,7,46,179,36,1,46,125,36,1*4C

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,39,10,37,191,33,1*4A

$GBGSV,7,5,27,41,34,311,38,4,32,112,32,13,32,217,34,42,27,167,35,1*48

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,34,8,14,202,32,23,10,264,34,1*43

$GBGSV,3,1,11,33,65,236,43,39,53,15,41,25,46,303,41,40,44,161,39,5*46

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131231.000,A,2301.2582173,N,11421.9414724,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131231.000,2.326,0.174,0.172,0.256,1.682,1.709,2.525*76



2025-07-31 21:12:31:442 ==>> 关闭5V供电成功
2025-07-31 21:12:31:454 ==>> 关闭33V供电
2025-07-31 21:12:31:476 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:12:31:564 ==>> 5A A5 02 5A A5 


2025-07-31 21:12:31:669 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:12:31:911 ==>> [D][05:19:04][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:04][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:04][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:04][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:04][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 21:12:32:389 ==>> $GBGGA,131232.000,2301.2582156,N,11421.9414749,E,1,24,0.58,76.056,M,-1.770,M,,*51

$GBGSA,A,3,33,03,39,06,16,14,59,02,09,25,07,01,1.26,0.58,1.12,4*09

$GBGSA,A,3,40,60,24,10,41,13,42,34,12,38,44,23,1.26,0.58,1.12,4*08

$GBGSV,7,1,27,33,65,236,42,3,62,190,40,39,53,15,39,6,52,351,35,1*4E

$GBGSV,7,2,27,16,52,355,37,14,51,187,36,59,50,129,40,2,47,239,35,1*43

$GBGSV,7,3,27,9,47,328,35,25,46,303,41,7,46,179,36,1,46,125,36,1*4D

$GBGSV,7,4,27,40,44,161,38,60,42,240,39,24,41,28,40,10,37,191,33,1*44

$GBGSV,7,5,27,41,34,311,38,4,32,112,33,13,32,217,34,42,27,167,35,1*49

$GBGSV,7,6,27,34,22,142,35,5,22,257,32,12,21,112,32,38,20,192,34,1*47

$GBGSV,7,7,27,44,20,86,35,8,14,202,32,23,10,264,34,1*42

$GBGSV,3,1,11,33,65,236,44,39,53,15,41,25,46,303,41,40,44,161,39,5*41

$GBGSV,3,2,11,24,41,28,40,41,34,311,39,42,27,167,37,34,22,142,33,5*42

$GBGSV,3,3,11,38,20,192,31,44,20,86,37,23,10,264,33,5*7B

$GBRMC,131232.000,A,2301.2582156,N,11421.9414749,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,131232.000,2.443,0.161,0.160,0.235,1.756,1.781,2.578*73



2025-07-31 21:12:32:448 ==>> 关闭33V供电成功
2025-07-31 21:12:32:459 ==>> 关闭3.7V供电
2025-07-31 21:12:32:470 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:12:32:569 ==>> 6A A6 02 A6 6A 


2025-07-31 21:12:32:674 ==>> Battery OFF
OVER 150


2025-07-31 21:12:32:779 ==>> [D][05:19:05][PROT]CLEAN,SEND:0
[D][05:19:05][PROT]index:0 1629955145
[D][05:19:05][PROT]is_send:0
[D][05:19:05][PROT]sequence_num:4
[D][05:19:05][PROT]retry_timeout:0
[D][05:19:05][PROT]retry_times:1
[D][05:19:05][PROT]send_path:0x2
[D][05:19:05][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:05][PROT]===========================================================
[W][05:19:05][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955145]
[D][05:19:05][PROT]===========================================================
[D][05:19:05][PROT]sending traceid [9999999999900005]
[D][05:19:05][PROT]Send_TO_M2M [1629955145]
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:05][SAL ]sock send credit cnt[6]
[D][05:19:05][SAL ]sock send ind credit cnt[6]
[D][05:19:05][M2M ]m2m send data len[198]
[D][05:19:05][SAL ]Cellular task submsg id[10]
[D][05:19:05

2025-07-31 21:12:32:884 ==>> ][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:19:05][CAT1]gsm read msg sub id: 15
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:05][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:19:05][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B5881C9D0DFC7D5F924B563A7B4464A828C48DD57AC19ADB53D97F0860DB679FA8FF1E7C6F9CBB58C67AF5A9F74A086C94B913099892B9F11B6465DA58E29787E516EFDE72933DDA1F58F5B20F62468E025F1C
[D][05:19:05][CAT1]<<< 
SEND OK

[D][05:19:05][CAT1]exec over: func id: 15, ret: 11
[D][05:19:05][CAT1]sub id: 15, ret: 11

[D][05:19:05][SAL ]Cellular task submsg id[68]
[D][05:19:05][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:05][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:05][M2M ]g_m2m_is_idle become true
[D][05:19:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:05][PROT]M2M Send ok [1629955145]


2025-07-31 21:12:33:147 ==>>  

