2025-07-31 20:03:22:162 ==>> MES查站成功:
查站序号:P5100010053132DA验证通过
2025-07-31 20:03:22:176 ==>> 扫码结果:P5100010053132DA
2025-07-31 20:03:22:177 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:03:22:178 ==>> 测试参数版本:2024.10.11
2025-07-31 20:03:22:180 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:03:22:182 ==>> 检测【打开透传】
2025-07-31 20:03:22:183 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:03:22:244 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:03:22:468 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:03:22:473 ==>> 检测【检测接地电压】
2025-07-31 20:03:22:475 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:03:22:544 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:03:22:743 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:03:22:745 ==>> 检测【打开小电池】
2025-07-31 20:03:22:749 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:03:22:844 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:03:22:919 ==>>  

2025-07-31 20:03:23:012 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:03:23:016 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:03:23:020 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:03:23:144 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:03:23:282 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:03:23:286 ==>> 检测【等待设备启动】
2025-07-31 20:03:23:291 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:03:23:631 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:03:23:826 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:03:24:309 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:03:24:465 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:03:24:540 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:03:24:935 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:03:25:345 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:03:25:406 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:03:25:621 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:03:25:624 ==>> 检测【产品通信】
2025-07-31 20:03:25:628 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:03:25:802 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:03:25:897 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:03:25:900 ==>> 检测【初始化完成检测】
2025-07-31 20:03:25:903 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:03:26:170 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:03:26:433 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:03:26:436 ==>> 检测【关闭大灯控制1】
2025-07-31 20:03:26:437 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:03:26:461 ==>> [D][05:17:51][COMM]2638 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:26:658 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:03:26:705 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:03:26:718 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:03:26:719 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:03:26:927 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:03:26:975 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:03:26:978 ==>> 检测【关闭仪表供电】
2025-07-31 20:03:26:980 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:03:27:138 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:03:27:246 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:03:27:249 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:03:27:250 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:03:27:492 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3650 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:27:530 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:03:27:533 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:03:27:535 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:03:27:721 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:03:27:799 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:03:27:802 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:03:27:803 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:03:28:023 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:03:28:072 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:03:28:074 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:03:28:076 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:03:28:128 ==>> 5A A5 01 5A A5 


2025-07-31 20:03:28:233 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:03:28:308 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29


2025-07-31 20:03:28:342 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:03:28:344 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:03:28:347 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:03:28:354 ==>>                    read battery soc:255


2025-07-31 20:03:28:443 ==>> 5A A5 03 5A A5 


2025-07-31 20:03:28:488 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:28:533 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:03:28:612 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:03:28:616 ==>> 该项需要延时执行
2025-07-31 20:03:29:004 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5010. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5010. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5011. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5011. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5012. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5012. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5012. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5013. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5013. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5013. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5014. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5014. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5015. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5015
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5015


2025-07-31 20:03:29:509 ==>> [D][05:17:54][COMM]5672 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:29:929 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:03:30:446 ==>>                                                                                                                                                                     and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM

2025-07-31 20:03:30:551 ==>> ]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/4

2025-07-31 20:03:30:657 ==>> 8v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove s

2025-07-31 20:03:30:746 ==>> uccess[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 20:03:30:821 ==>> [D][05:17:56][CAT1]power_urc_cb ret[5]


2025-07-31 20:03:31:518 ==>> [D][05:17:56][COMM]7696 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:32:372 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:03:32:522 ==>> [D][05:17:57][COMM]8707 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:32:627 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:03:32:630 ==>> 检测【33V输入电压ADC】
2025-07-31 20:03:32:633 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:32:942 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:17:57][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:57][COMM]adc read left brake adc:14  volt:18 mv
[D][05:17:57][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:03:33:160 ==>> 【33V输入电压ADC】通过,【32804mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:03:33:174 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:03:33:177 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:03:33:249 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2775mV
Get AD_V6 1985mV
Get AD_V7 1090mV
OVER 150


2025-07-31 20:03:33:432 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:33:435 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:03:33:451 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:33:455 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:03:33:457 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 20:03:33:490 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:03:33:493 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:03:33:509 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1985mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:03:33:512 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:03:33:535 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:03:33:537 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:03:33:540 ==>> [D][05:17:58][COMM]9718 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:03:33:644 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2773mV
Get AD_V6 1988mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:03:33:821 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:33:825 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:03:33:839 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:33:842 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:03:33:844 ==>> 原始值:【2773】, 乘以分压基数【2】还原值:【5546】
2025-07-31 20:03:33:858 ==>> 【TP68_VCC5V5(ADV5)】通过,【5546mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:03:33:861 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:03:33:869 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10004
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10004


2025-07-31 20:03:33:876 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:03:33:880 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:03:33:899 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:03:33:902 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:03:34:050 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1988mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:03:34:177 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:34:185 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:03:34:196 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1659mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:34:199 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:03:34:203 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:03:34:214 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:03:34:217 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:03:34:231 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:03:34:234 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:03:34:255 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:03:34:257 ==>> 检测【打开WIFI(1)】
2025-07-31 20:03:34:258 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:03:34:398 ==>> [D][05:17:59][COMM]read battery soc:255
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:03:34:537 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:03:34:541 ==>> 检测【清空消息队列(1)】
2025-07-31 20:03:34:544 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:03:34:941 ==>>                                        [D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10729 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,

2025-07-31 20:03:35:046 ==>> -1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][0

2025-07-31 20:03:35:065 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:03:35:069 ==>> 检测【打开GPS(1)】
2025-07-31 20:03:35:073 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:03:35:092 ==>> 5:17:59][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 20:03:35:197 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][0

2025-07-31 20:03:35:227 ==>> 5:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:03:35:336 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:03:35:340 ==>> 检测【打开GSM联网】
2025-07-31 20:03:35:343 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:03:35:562 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:03:35:610 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:03:35:614 ==>> 检测【打开仪表供电1】
2025-07-31 20:03:35:616 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:03:35:833 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:03:35:882 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:03:35:884 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:03:35:888 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:03:36:031 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:03:36:165 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:03:36:168 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:03:36:170 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:03:36:318 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33224]


2025-07-31 20:03:36:378 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:03:36:441 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:03:36:444 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:03:36:446 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:03:36:695 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 20:03:36:973 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:03:36:975 ==>> 检测【AD_V20电压】
2025-07-31 20:03:36:979 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:03:37:075 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:03:37:136 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:03:37:188 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087783389

OK

[D][05:18:02

2025-07-31 20:03:37:287 ==>> ][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539080

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:03:37:548 ==>> 本次取值间隔时间:458ms
2025-07-31 20:03:37:567 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:03:37:670 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:03:37:760 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:03:38:066 ==>> 本次取值间隔时间:388ms
2025-07-31 20:03:38:092 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:03:38:201 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:03:38:432 ==>> [D][05:18:03][COMM]read battery soc:255
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:03:38:538 ==>> [D][05:18:03][COMM]14743 imu init OK


2025-07-31 20:03:38:583 ==>> 本次取值间隔时间:380ms
2025-07-31 20:03:38:778 ==>> 本次取值间隔时间:185ms
2025-07-31 20:03:38:898 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:03:38:913 ==>> 本次取值间隔时间:127ms
2025-07-31 20:03:39:293 ==>> 本次取值间隔时间:365ms
2025-07-31 20:03:39:298 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:03:39:354 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 20:03:39:398 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:03:39:534 ==>> 1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 20:03:39:747 ==>> 本次取值间隔时间:343ms
2025-07-31 20:03:39:766 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:39:770 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:03:39:776 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:03:39:795 ==>>                                                                        2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][COMM]Main Task receive event:4
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:04][GNSS]rtk_id: 303E383D3535373F38383F34343F3E07

[D][05:18:04][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][COMM]init key as 
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:04][COMM]Main Task receive event:4 finished processing
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
[D][05:18:04][CAT1]t

2025-07-31 20:03:39:897 ==>> x ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:04][CAT1]<<< 
+QIACT: 1,1,1,"10.123.34.44","2409:8D5A:F4A:13D8:1857:546F:4730:4D2C"

OK

[D][05:18:04][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 8, ret: 6
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:03:40:041 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:03:40:044 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:03:40:046 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:03:40:229 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:03:40:323 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:03:40:326 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:03:40:337 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:03:40:441 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:03:40:486 ==>> [D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:03:40:594 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:03:40:597 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:03:40:599 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:03:40:834 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:03:40:868 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:03:40:895 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:03:40:898 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:03:41:031 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:03:41:136 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret

2025-07-31 20:03:41:164 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:03:41:167 ==>> 检测【AD_V21电压】
2025-07-31 20:03:41:169 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:41:173 ==>> [17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:03:41:241 ==>> 1A A1 20 00 00 
Get AD_V21 1244mV
OVER 150


2025-07-31 20:03:41:407 ==>> 本次取值间隔时间:231ms
2025-07-31 20:03:41:440 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:41:482 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,25,,,41,33,,,40,42,,,40,38,,,38,1*71

$GBGSV,3,2,10,39,,,37,40,,,36,13,,,42,59,,,42,1*77

$GBGSV,3,3,10,14,,,41,41,,,36,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1602.997,1602.997,51.223,2097152,2097152,2097152*48

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]opened : 0, 0
[D][05:18:06][SAL ]Cellular task submsg id[68]
[D][05:18:06][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:06][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:06][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:06][M2M ]g_m2m_is_idle become true
[D][05:18:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

1A A1 20 00 00 
Get AD_V21 1244mV
OVER 150


2025-07-31 20:03:41:542 ==>>                                                                                             

2025-07-31 20:03:41:768 ==>> 本次取值间隔时间:317ms
2025-07-31 20:03:41:787 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:41:843 ==>> 1A A1 20 00 00 
Get AD_V21 1329mV
OVER 150


2025-07-31 20:03:42:010 ==>> 本次取值间隔时间:208ms
2025-07-31 20:03:42:028 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:42:134 ==>> 1A A1 20 00 00 
Get AD_V21 1329mV
OVER 150


2025-07-31 20:03:42:239 ==>> 本次取值间隔时间:209ms
2025-07-31 20:03:42:244 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,

2025-07-31 20:03:42:270 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:42:284 ==>> 60,,,42,14,,,41,33,,,41,42,,,41,1*72

$GBGSV,4,2,16,24,,,41,25,,,40,3,,,40,13,,,39,1*48

$GBGSV,4,3,16,59,,,38,38,,,38,39,,,38,16,,,38,1*7C

$GBGSV,4,4,16,40,,,37,6,,,35,41,,,36,2,,,36,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1625.704,1625.704,51.952,2097152,2097152,2097152*45



2025-07-31 20:03:42:344 ==>> 1A A1 20 00 00 
Get AD_V21 1290mV
OVER 150


2025-07-31 20:03:42:449 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:03:42:616 ==>> 本次取值间隔时间:340ms
2025-07-31 20:03:42:634 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:42:737 ==>> 1A A1 20 00 00 
Get AD_V21 1409mV
OVER 150


2025-07-31 20:03:43:113 ==>> 本次取值间隔时间:474ms
2025-07-31 20:03:43:132 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:43:236 ==>> 1A A1 20 00 00 
Get AD_V21 1411mV
OVER 150


2025-07-31 20:03:43:311 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,14,,,41,42,,,41,24,,,41,1*7C

$GBGSV,5,2,19,60,,,40,25,,,40,3,,,40,13,,,39,1*47

$GBGSV,5,3,19,59,,,39,1,,,39,38,,,38,39,,,38,1*44

$GBGSV,5,4,19,16,,,38,40,,,37,41,,,36,6,,,35,1*43

$GBGSV,5,5,19,5,,,35,2,,,34,4,,,32,1*4D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1581.947,1581.947,50.592,2097152,2097152,2097152*44



2025-07-31 20:03:43:492 ==>> 本次取值间隔时间:358ms
2025-07-31 20:03:43:510 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:43:642 ==>> 本次取值间隔时间:123ms
2025-07-31 20:03:43:646 ==>> 1A A1 20 00 00 
Get AD_V21 1411mV
OVER 150


2025-07-31 20:03:43:763 ==>> 本次取值间隔时间:105ms
2025-07-31 20:03:43:781 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:43:837 ==>> 1A A1 20 00 00 
Get AD_V21 1307mV
OVER 150


2025-07-31 20:03:44:169 ==>> 本次取值间隔时间:383ms
2025-07-31 20:03:44:187 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:44:245 ==>> 1A A1 20 00 00 
Get AD_V21 1450mV
OVER 150


2025-07-31 20:03:44:350 ==>> $GBGGA,120348.128,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,14,,,41,42,,,41,24,,,41,1*76

$GBGSV,5,2,20,60,,,40,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,5,3,20,13,,,39,1,,,39

2025-07-31 20:03:44:396 ==>> ,38,,,38,39,,,38,1*40

$GBGSV,5,4,20,16,,,38,40,,,37,41,,,36,6,,,35,1*49

$GBGSV,5,5,20,5,,,34,2,,,34,4,,,32,9,,,41,1*7A

$GBRMC,120348.128,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120348.128,0.000,1581.952,1581.952,50.597,2097152,2097152,2097152*58



2025-07-31 20:03:44:440 ==>> 本次取值间隔时间:238ms
2025-07-31 20:03:44:456 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:03:44:460 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:44:545 ==>> 1A A1 20 00 00 
Get AD_V21 1450mV
OVER 150


2025-07-31 20:03:44:740 ==>> $GBGGA,120348.528,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,42,14,,,41,42,,,41,24,,,41,1*76

$GBGSV,5,2,20,60,,,40,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,5,3,20,13,,,39,1,,,38,38,,,38,39,,,38,1*41

$GBGSV,5,4,20,16,,,38,40,,,37,41,,,36,9,,,35,1*46

$GBGSV,5,5,20,6,,,35,5,,,34,2,,,33,4,,,31,1*72

$GBRMC,120348.528,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120348.528,0.000,1569.198,1569.198,50.200,2097152,2097152,2097152*55



2025-07-31 20:03:44:970 ==>> 本次取值间隔时间:498ms
2025-07-31 20:03:45:012 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:45:140 ==>> 1A A1 20 00 00 
Get AD_V21 1454mV
OVER 150


2025-07-31 20:03:45:511 ==>> 本次取值间隔时间:497ms
2025-07-31 20:03:45:529 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:45:620 ==>> 本次取值间隔时间:90ms
2025-07-31 20:03:45:635 ==>> 1A A1 20 00 00 
Get AD_V21 1483mV
OVER 150


2025-07-31 20:03:45:726 ==>> $GBGGA,120349.508,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,14,,,41,42,,,41,24,,,41,1*76

$GBGSV,6,2,23,60,,,40,25,,,40,3,,,40,59,,,40,1*4D

$GBGSV,6,3,23,13,,,39,1,,,38,38,,,38,39,,,38,1*41

$GBGSV,6,4,23,16,,,38,40,,,37,41,,,36,8,,,36,1*44

$GBGSV,6,5,23,9,,,35,6,,,35,5,,,34,2,,,33,1*7B

$GBGSV,6,6,23,4,,,31,7,,,31,10,,,29,1*7E

$GBRMC,120349.508,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120349.508,0.000,1537.596,1537.596,49.221,2097152,2097152,2097152*5D



2025-07-31 20:03:45:966 ==>> 本次取值间隔时间:333ms
2025-07-31 20:03:45:984 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:46:041 ==>> 1A A1 20 00 00 
Get AD_V21 1483mV
OVER 150


2025-07-31 20:03:46:402 ==>> 本次取值间隔时间:406ms
2025-07-31 20:03:46:420 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:46:477 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:03:46:537 ==>> 本次取值间隔时间:115ms
2025-07-31 20:03:46:541 ==>> 1A A1 20 00 00 
Get AD_V21 1486mV
OVER 150


2025-07-31 20:03:46:717 ==>> 本次取值间隔时间:170ms
2025-07-31 20:03:46:722 ==>> $GBGGA,120350.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,14,,,41,42,,,41,24,,,41,1*76

$GBGSV,6,2,23,3,,,41,60,,,40,25,,,40,59,,,40,1*4C

$GBGSV,6,3,23,13,,,39,38,,,38,39,,,38,16,,,38,1*77

$GBGSV,6,4,23,1,,,37,40,,,37,41,,,36,8,,,36,1*7D

$GBGSV,6,5,23,9,,,35,6,,,35,5,,,34,2,,,33,1*7B

$GBGSV,6,6,23,4,,,32,7,,,32,10,,,30,1*76

$GBRMC,120350.508,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120350.508,0.000,1542.993,1542.993,49.383,2097152,2097152,2097152*5C



2025-07-31 20:03:46:735 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:46:822 ==>> 1A A1 20 00 00 
Get AD_V21 1489mV
OVER 150


2025-07-31 20:03:47:115 ==>> 本次取值间隔时间:368ms
2025-07-31 20:03:47:133 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:47:239 ==>> 1A A1 20 00 00 
Get AD_V21 1490mV
OVER 150


2025-07-31 20:03:47:284 ==>> 本次取值间隔时间:143ms
2025-07-31 20:03:47:302 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:47:438 ==>> 1A A1 20 00 00 
Get AD_V21 1490mV
OVER 150


2025-07-31 20:03:47:589 ==>> 本次取值间隔时间:274ms
2025-07-31 20:03:47:609 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:47:740 ==>> $GBGGA,120351.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,14,,,41,42,,,41,24,,,41,1*71

$GBGSV,6,2,24,3,,,40,60,,,40,25,,,40,59,,,40,1*4A

$GBGSV,6,3,24,13,,,39,38,,,38,39,,,38,16,,,38,1*70

$GBGSV,6,4,24,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,6,5,24,9,,,35,6,,,35,5,,,34,2,,,33,1*7C

$GBGSV,6,6,24,7,,,33,4,,,32,10,,,31,26,,,40,1*71

$GBRMC,120351.508,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120351.508,0.000,1546.588,1546.588,49.489,2097152,2097152,2097152*50

1A A1 20 00 00 
Get AD_V21 1472mV
OVER 150


2025-07-31 20:03:47:831 ==>> 本次取值间隔时间:212ms
2025-07-31 20:03:47:856 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:47:938 ==>> 1A A1 20 00 00 
Get AD_V21 1472mV
OVER 150


2025-07-31 20:03:48:089 ==>> 本次取值间隔时间:229ms
2025-07-31 20:03:48:108 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:48:224 ==>> 本次取值间隔时间:116ms
2025-07-31 20:03:48:239 ==>> 1A A1 20 00 00 
Get AD_V21 1473mV
OVER 150


2025-07-31 20:03:48:483 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:03:48:618 ==>> 本次取值间隔时间:387ms
2025-07-31 20:03:48:636 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:48:738 ==>> $GBGGA,120352.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,42,,,41,24,,,41,1*71

$GBGSV,7,2,25,3,,,40,60,,,40,25,,,40,59,,,40,1*4A

$GBGSV,7,3,25,13,,,39,38,,,38,39,,,38,16,,,38,1*70

$GBGSV,7,4,25,1,,,37,40,,,37,41,,,36,8,,,36,1*7A

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,33,4,,,32,10,,,31,1*44

$GBGSV,7,7,25,21,,,28,1*78

$GBRMC,120352.508,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120352.508,0.000,1527.356,1527.356,48.892,2097152,2097152,2097152*54

1A A1 20 00 00 
Get AD_V21 1475mV
OVER 150


2025-07-31 20:03:49:134 ==>> 本次取值间隔时间:494ms
2025-07-31 20:03:49:152 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:49:240 ==>> 1A A1 20 00 00 
Get AD_V21 1475mV
OVER 150


2025-07-31 20:03:49:269 ==>> 本次取值间隔时间:110ms
2025-07-31 20:03:49:288 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:49:344 ==>> 1A A1 20 00 00 
Get AD_V21 1475mV
OVER 150


2025-07-31 20:03:49:434 ==>> 本次取值间隔时间:133ms
2025-07-31 20:03:49:453 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:49:541 ==>> 1A A1 20 00 00 
Get AD_V21 1475mV
OVER 150


2025-07-31 20:03:49:691 ==>> 本次取值间隔时间:223ms
2025-07-31 20:03:49:710 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:03:49:721 ==>> $GBGGA,120353.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,25,,,41,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,60,,,40,59,,,40,1*48

$GBGSV,7,3,25,13,,,39,38,,,38,39,,,38,16,,,38,1*70

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,33,4,,,32,10,,,31,1*44

$GBGSV,7,7,25,21,,,29,1*79

$GBRMC,120353.508,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120353.508,0.000,1530.668,1530.668,48.994,2097152,2097152,2097152*52



2025-07-31 20:03:49:826 ==>> 1A A1 20 00 00 
Get AD_V21 1505mV
OVER 150


2025-07-31 20:03:50:055 ==>> 本次取值间隔时间:341ms
2025-07-31 20:03:50:073 ==>> 【AD_V21电压】通过,【1505mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:03:50:076 ==>> 检测【关闭仪表供电2】
2025-07-31 20:03:50:080 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:03:50:227 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:15][COMM]set POWER 0
[D][05:18:15][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:03:50:350 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:03:50:355 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:03:50:358 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:03:50:531 ==>> [D][05:18:15][COMM]read battery soc:255
[D][05:18:15][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:15][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:03:50:625 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:03:50:630 ==>> 检测【打开AccKey2供电】
2025-07-31 20:03:50:635 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:03:50:726 ==>> $GBGGA,120354.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,25,,,41,1*73

$GBGSV,7,2,25,14,,,40,3,,,40,60,,,40,59,,,40,1*48

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,37,40,,,37,41,,,36,8,,,36,1*7A

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,33,4,,,32,10,,,32,1*47

$GBGSV,7,7,25,21,,,29,1*79

$GBRMC,120354.508,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120354.508,0.000,1529.005,1529.005,48.936,2097152,2097152,2097152*5D



2025-07-31 20:03:50:816 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:03:50:899 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:03:50:905 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:03:50:911 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:51:252 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:16][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:16][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:16][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:16][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:16][COMM]adc read battery ts volt:17 mv
[D][05:18:16][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:03:51:438 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:03:51:441 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:03:51:444 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:03:51:727 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,120355.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,41,24,,,41,25,,,40,1*71

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,60,,,39,1*46

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,33,4,,,32,10,,,31,1*44

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120355.508,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120355.508,0.000,1525.681,1525.681,48.823,2097152,2097152,2097152*59



2025-07-31 20:03:51:996 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:03:51:999 ==>> 该项需要延时执行
2025-07-31 20:03:52:499 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:03:52:726 ==>> $GBGGA,120356.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,25,,,40,1*72

$GBGSV,7,2,25,14,,,40,3,,,40,59,,,40,60,,,40,1*48

$GBGSV,7,3,25,13,,,39,38,,,38,39,,,38,16,,,38,1*70

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,30,1*71

$GBRMC,120356.508,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120356.508,0.000,1533.972,1533.972,49.087,2097152,2097152,2097152*5D



2025-07-31 20:03:53:725 ==>> $GBGGA,120357.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,14,,,41,1*71

$GBGSV,7,2,25,25,,,40,3,,,40,59,,,40,60,,,39,1*44

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120357.508,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120357.508,0.000,1533.968,1533.968,49.083,2097152,2097152,2097152*58



2025-07-31 20:03:54:505 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:03:54:732 ==>> $GBGGA,120358.508,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,14,,,40,1*70

$GBGSV,7,2,25,25,,,40,3,,,40,59,,,40,60,,,40,1*4A

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120358.508,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120358.508,0.000,1533.967,1533.967,49.082,2097152,2097152,2097152*56



2025-07-31 20:03:55:002 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:03:55:007 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:03:55:012 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:55:349 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:20][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:20][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:20][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:20][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:20][COMM]adc read battery ts volt:17 mv
[D][05:18:20][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:20][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:03:55:533 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:03:55:539 ==>> 检测【打开AccKey1供电】
2025-07-31 20:03:55:545 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:03:55:769 ==>> $GBGGA,120359.508,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,42,,,41,24,,,41,14,,,41,1*71

$GBGSV,7,2,25,25,,,40,3,,,40,59,,,40,60,,,40,1*4A

$GBGSV,7,3,25,13,,,39,38,,,38,39,,,38,16,,,38,1*70

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,31,1*70

$GBRMC,120359.508,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120359.508,0.000,1537.287,1537.287,49.191,2097152,2097152,2097152*54

[W][05:18:20][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:20][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:03:55:804 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:03:55:809 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:03:55:815 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:03:55:934 ==>> 1A A1 00 40 00 
Get AD_V14 2678mV
OVER 150


2025-07-31 20:03:56:055 ==>> 原始值:【2678】, 乘以分压基数【2】还原值:【5356】
2025-07-31 20:03:56:074 ==>> 【读取AccKey1电压(ADV14)前】通过,【5356mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:03:56:077 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:03:56:082 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:56:344 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:21][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:21][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:21][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:21][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:21][COMM]adc read battery ts volt:16 mv
[D][05:18:21][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:21][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:03:56:494 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:03:56:603 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:03:56:610 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:03:56:632 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:03:56:720 ==>> $GBGGA,120400.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,41,24,,,41,14,,,40,1*73

$GBGSV,7,2,25,25,,,40,3,,,40,59,,,40,60,,,40,1*4A

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120400.508,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120400.508,0.000,1533.960,1533.960,49.076,2097152,2097152,2097152*57



2025-07-31 20:03:56:825 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:21][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:03:56:875 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:03:56:879 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:03:56:886 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:03:56:930 ==>> 1A A1 00 40 00 
Get AD_V14 2675mV
OVER 150


2025-07-31 20:03:57:126 ==>> 原始值:【2675】, 乘以分压基数【2】还原值:【5350】
2025-07-31 20:03:57:145 ==>> 【读取AccKey1电压(ADV14)后】通过,【5350mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:03:57:153 ==>> 检测【打开WIFI(2)】
2025-07-31 20:03:57:175 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:03:57:356 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:22][CAT1]<<< 
OK

[D][05:18:22][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:03:57:420 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:03:57:424 ==>> 检测【转刹把供电】
2025-07-31 20:03:57:427 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:03:57:748 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,120401.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,41,42,,,41,24,,,41,14,,,40,1*73

$GBGSV,7,2,25,25,,,40,3,,,40,59,,,40,60,,,40,1*4A

$GBGSV,7,3,25,13,,,38,38,,,38,39,,,38,16,,,38,1*71

$GBGSV,7,4,25,1,,,38,40,,,37,41,,,36,8,,,36,1*75

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,5,,,34,1*4C

$GBGSV,7,6,25,7,,,34,2,,,34,4,,,32,10,,,32,1*40

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120401.508,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120401.508,0.000,765.548,765.548,700.111,2097152,2097152,2097152*6C



2025-07-31 20:03:57:949 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:03:57:953 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:03:57:956 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:03:58:051 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:03:58:056 ==>> +WIFISCAN:4,0,CC057790A741,-72
+WIFISCAN:4,1,CC057790A740,-73
+WIFISCAN:4,2,CC057790A6E0,-79
+WIFISCAN:4,3,CC057790A6E1,-79

[D][05:18:23][CAT1]wifi scan report total[4]


2025-07-31 20:03:58:156 ==>> [D][05:18:23][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:23][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2410mV
OVER 150


2025-07-31 20:03:58:216 ==>> 原始值:【2410】, 乘以分压基数【2】还原值:【4820】
2025-07-31 20:03:58:235 ==>> 【读取AD_V15电压(前)】通过,【4820mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:03:58:242 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:03:58:250 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:03:58:336 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:03:58:441 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2438mV
OVER 150


2025-07-31 20:03:58:501 ==>> 原始值:【2438】, 乘以分压基数【2】还原值:【4876】
2025-07-31 20:03:58:517 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:03:58:526 ==>> 【读取AD_V16电压(前)】通过,【4876mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:03:58:529 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:03:58:533 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:58:867 ==>> [D][05:18:23][GNSS]recv submsg id[3]
$GBGGA,120402.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,24,,,41,42,,,41,33,,,41,60,,,40,1*70

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,38,38,,,38,1,,,38,39,,,38,1*47

$GBGSV,7,4,25,16,,,38,40,,,37,8,,,36,41,,,36,1*43

$GBGSV,7,5,25,2,,,35,26,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120402.508,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120402.508,0.000,766.374,766.374,700.866,2097152,2097152,2097152*66

[W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:23][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:23][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:23][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:23][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:23][COMM]adc read battery ts volt:14 mv
[D][05:18:23][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:23][COMM]adc read throttle brake in adc:3091  volt:5433 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:23][COMM]arm_hub adc read l

2025-07-31 20:03:58:897 ==>> ed yb adc:1434  volt:33247 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:03:59:053 ==>> 【转刹把供电电压(主控ADC)】通过,【5433mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:03:59:060 ==>> 检测【转刹把供电电压】
2025-07-31 20:03:59:082 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:03:59:355 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:24][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:24][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:24][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:24][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:24][COMM]adc read battery ts volt:19 mv
[D][05:18:24][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:24][COMM]adc read throttle brake in adc:3095  volt:5440 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:03:59:588 ==>> 【转刹把供电电压】通过,【5440mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:03:59:594 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:03:59:618 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:03:59:754 ==>> $GBGGA,120403.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,41,42,,,41,60,,,40,1*73

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,38,38,,,38,1,,,38,39,,,38,1*47

$GBGSV,7,4,25,16,,,38,40,,,37,8,,,36,41,,,36,1*43

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,2,,,34,1*4B

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120403.508,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120403.508,0.000,766.378,766.378,700.871,2097152,2097152,2097152*61

[W][05:18:24][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:03:59:862 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:03:59:868 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:03:59:872 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:03:59:964 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:00:009 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:04:00:039 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:04:00:088 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:04:00:092 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:04:00:095 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:00:189 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:04:00:234 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:04:00:312 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:04:00:316 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:04:00:322 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:04:00:445 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:04:00:520 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:04:00:584 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:04:00:591 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:04:00:613 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:04:00:625 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:04:00:730 ==>> $GBGGA,120404.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,41,42,,,41,60,,,40,1*73

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,38,38,,,38,1,,,38,39,,,38,1*47

$GBGSV,7,4,25,16,,,38,40,,,37,8,,,36,41,,,36,1*43

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,2,,,34,1*4B

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120404.508,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120404.508,0.000,766.378,766.378,700.871,2097152,2097152,2097152*66



2025-07-31 20:04:00:854 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:04:00:858 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:04:00:864 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:04:00:940 ==>> 3A A3 05 01 A3 


2025-07-31 20:04:01:030 ==>> ON_OUT5
OVER 150


2025-07-31 20:04:01:124 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:04:01:129 ==>> 检测【左刹电压测试1】
2025-07-31 20:04:01:132 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:01:443 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:26][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:26][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:26][COMM]adc read left brake adc:1725  volt:2274 mv
[D][05:18:26][COMM]adc read right brake adc:1723  volt:2271 mv
[D][05:18:26][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:26][COMM]adc read battery ts volt:13 mv
[D][05:18:26][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:26][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:26][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:26][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:26][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:26][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:26][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:04:01:665 ==>> 【左刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:01:672 ==>> 检测【右刹电压测试1】
2025-07-31 20:04:01:693 ==>> 【右刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:01:697 ==>> 检测【转把电压测试1】
2025-07-31 20:04:01:713 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:01:719 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:04:01:736 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:04:01:749 ==>> $GBGGA,120405.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,24,,,41,42,,,41,33,,,41,60,,,40,1*70

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,39,38,,,38,1,,,38,39,,,38,1*46

$GBGSV,7,4,25,16,,,38,40,,,37,8,,,36,26,,,36,1*42

$GBGSV,7,5,25,41,,,36,2,,,35,9,,,35,6,,,35,1*48

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120405.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120405.508,0.000,768.029,768.029,702.379,2097152,2097152,2097152*66



2025-07-31 20:04:01:839 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:04:01:994 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:04:01:998 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:04:02:004 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:04:02:144 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:04:02:268 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:04:02:272 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:04:02:277 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:04:02:341 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:04:02:523 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 20:04:02:527 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 20:04:02:533 ==>> #################### 【测试结束】 ####################
2025-07-31 20:04:02:551 ==>> 关闭5V供电
2025-07-31 20:04:02:558 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:04:02:628 ==>> 5A A5 04 5A A5 


2025-07-31 20:04:02:733 ==>> $GBGGA,120406.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,24,,,41,42,,,41,14,,,41,33,,,41,1*72

$GBGSV,7,2,25,60,,,40,3,,,40,59,,,40,25,,,40,1*4A

$GBGSV,7,3,25,13,,,39,38,,,38,1,,,38,39,,,38,1*46

$GBGSV,7,4,25,16,,,38,40,,,37,41,,,37,8,,,36,1*42

$GBGSV,7,5,25,26,,,35,9,,,35,6,,,35,2,,,34,1*4B

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120406.508,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120406.508,0.000,768.033,768.033,702.384,2097152,2097152,2097152*67

CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:04:03:558 ==>> 关闭5V供电成功
2025-07-31 20:04:03:566 ==>> 关闭33V供电
2025-07-31 20:04:03:578 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:04:03:635 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:04:03:861 ==>> $GBGGA,120407.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,24,,,41,42,,,41,33,,,41,60,,,40,1*70

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,39,38,,,38,1,,,38,39,,,38,1*46

$GBGSV,7,4,25,16,,,38,40,,,37,41,,,37,8,,,36,1*42

$GBGSV,7,5,25,2,,,35,26,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,6,25,7,,,34,5,,,34,4,,,33,10,,,32,1*46

$GBGSV,7,7,25,21,,,32,1*73

$GBRMC,120407.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120407.508,0.000,768.854,768.854,703.134,2097152,2097152,2097152*6E

[D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11


2025-07-31 20:04:04:043 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:04:04:565 ==>> 关闭33V供电成功
2025-07-31 20:04:04:572 ==>> 关闭3.7V供电
2025-07-31 20:04:04:578 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:04:04:643 ==>> 6A A6 02 A6 6A 


2025-07-31 20:04:04:748 ==>> $GBGGA,120408.508,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,24,,,41,42,,,41,33,,,41,60,,,40,1*70

$GBGSV,7,2,25,3,,,40,59,,,40,14,,,40,25,,,40,1*49

$GBGSV,7,3,25,13,,,39,38,,,38,1,,,38,39,,,38,1*46

$GBGSV,7,4,25,16,,,38,40,,,37,41,,,37,8,,,36,1*42

$GBGSV,7,5,25,2,,,35,26,,,35,9,,,35,6,,,35,1*4A

$GBGSV,7,6,25,7,,,34,5,,,34,10,,,32,21,,,32,1*70

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,120408.508,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120408.508,0.000,768.029,768.029,702.380,2097152,2097152,2097152*6D

Battery OFF
OVER 150


2025-07-31 20:04:05:087 ==>>   

