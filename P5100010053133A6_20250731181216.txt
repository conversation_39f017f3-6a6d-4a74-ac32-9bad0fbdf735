2025-07-31 18:12:16:619 ==>> MES查站成功:
查站序号:P5100010053133A6验证通过
2025-07-31 18:12:16:624 ==>> 扫码结果:P5100010053133A6
2025-07-31 18:12:16:625 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:12:16:627 ==>> 测试参数版本:2024.10.11
2025-07-31 18:12:16:628 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:12:16:630 ==>> 检测【打开透传】
2025-07-31 18:12:16:632 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:12:16:760 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:12:17:262 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:12:17:266 ==>> 检测【检测接地电压】
2025-07-31 18:12:17:268 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:12:17:371 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 18:12:17:568 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:12:17:571 ==>> 检测【打开小电池】
2025-07-31 18:12:17:573 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:12:17:663 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:12:17:847 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:12:17:849 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:12:17:851 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:12:17:966 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:12:18:122 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:12:18:125 ==>> 检测【等待设备启动】
2025-07-31 18:12:18:127 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:12:18:523 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:12:18:688 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Tim 

2025-07-31 18:12:19:164 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:12:19:272 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:12:19:468 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:12:20:153 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:12:20:198 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:12:20:537 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:12:21:019 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:12:21:346 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:12:21:348 ==>> 检测【产品通信】
2025-07-31 18:12:21:349 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:12:21:753 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:12:21:950 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:12:22:378 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:12:22:621 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 18:12:23:001 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:12:23:411 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:12:23:472 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:12:23:577 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:23:691 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:12:23:693 ==>> 检测【初始化完成检测】
2025-07-31 18:12:23:695 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:12:23:891 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 18:12:23:969 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:12:23:971 ==>> 检测【关闭大灯控制1】
2025-07-31 18:12:23:973 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:12:24:147 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:12:24:239 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:12:24:242 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:12:24:245 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:12:24:454 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:12:24:517 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:12:24:521 ==>> 检测【关闭仪表供电】
2025-07-31 18:12:24:523 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:12:24:559 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:24:665 ==>>                                                                                          ent = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT

2025-07-31 18:12:24:709 ==>> ]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:12:24:788 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:12:24:790 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:12:24:793 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:12:24:921 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:12:25:065 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:12:25:087 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:12:25:089 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:12:25:223 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:12:25:363 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:12:25:365 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:12:25:366 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:12:25:603 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:25:649 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:12:25:651 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:12:25:653 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:12:25:768 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:12:25:873 ==>> [D][05:17:52][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27
[D][05:17:52][COMM]read battery soc:255


2025-07-31 18:12:25:946 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:12:25:949 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:12:25:964 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:12:26:068 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:12:26:249 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:12:26:252 ==>> 该项需要延时执行
2025-07-31 18:12:26:585 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:27:129 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5018. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5020
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020

2025-07-31 18:12:27:159 ==>> 


2025-07-31 18:12:27:448 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:12:27:946 ==>>                                                                                                                                                    --- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task recei

2025-07-31 18:12:28:051 ==>> ve event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc

2025-07-31 18:12:28:156 ==>> _main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[16

2025-07-31 18:12:28:216 ==>> 29955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]read battery soc:255


2025-07-31 18:12:28:610 ==>> [D][05:17:55][COMM]6671 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:28:715 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 18:12:29:630 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:29:871 ==>> [D][05:17:56][COMM]read battery soc:255


2025-07-31 18:12:30:250 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:12:30:268 ==>> 检测【33V输入电压ADC】
2025-07-31 18:12:30:270 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:12:30:575 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3099  volt:5447 mv
[D][05:17:57][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read left brake adc:2  volt:2 mv
[D][05:17:57][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:4 mv
[D][05:17:57][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:12:30:650 ==>>                                      [D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:30:839 ==>> 【33V输入电压ADC】通过,【32703mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:12:30:842 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:12:30:843 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:12:30:967 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1639mV
Get AD_V4 1mV
Get AD_V5 2750mV
Get AD_V6 1988mV
Get AD_V7 1093mV
OVER 150


2025-07-31 18:12:31:158 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:31:160 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:12:31:217 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:31:220 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:12:31:223 ==>> 原始值:【2750】, 乘以分压基数【2】还原值:【5500】
2025-07-31 18:12:31:275 ==>> 【TP68_VCC5V5(ADV5)】通过,【5500mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:12:31:289 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:12:31:343 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:12:31:346 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:12:31:418 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:12:31:420 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:12:31:567 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1640mV
Get AD_V4 1mV
Get AD_V5 2751mV
Get AD_V6 1989mV
Get AD_V7 1093mV
OVER 150


2025-07-31 18:12:31:641 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:12:31:751 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:31:754 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:12:31:864 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:31:867 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:12:31:870 ==>> 原始值:【2751】, 乘以分压基数【2】还原值:【5502】
2025-07-31 18:12:31:923 ==>> 【TP68_VCC5V5(ADV5)】通过,【5502mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:12:31:926 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:12:31:978 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:12:31:982 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:12:32:003 ==>> [D][05:17:58][COMM]read battery soc:255
[D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10010
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10010


2025-07-31 18:12:32:044 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:12:32:047 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:12:32:168 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1640mV
Get AD_V4 1mV
Get AD_V5 2747mV
Get AD_V6 1989mV
Get AD_V7 1094mV
OVER 150


2025-07-31 18:12:32:378 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 18:12:32:431 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:32:433 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:12:32:497 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:32:500 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:12:32:503 ==>> 原始值:【2747】, 乘以分压基数【2】还原值:【5494】
2025-07-31 18:12:32:559 ==>> 【TP68_VCC5V5(ADV5)】通过,【5494mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:12:32:561 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:12:32:611 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:12:32:613 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:12:32:672 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:12:32:674 ==>> 检测【打开WIFI(1)】
2025-07-31 18:12:32:677 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:12:32:922 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,

2025-07-31 18:12:32:967 ==>> 23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:12:33:263 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:12:33:268 ==>> 检测【清空消息队列(1)】
2025-07-31 18:12:33:272 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:12:33:346 ==>>                                                                                                                                                                                                                                                                                                                                                                  [D][05:18:00][CAT1]<<< 
867222087873727

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290637

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:12:33:436 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:12:33:589 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:12:33:591 ==>> 检测【打开GPS(1)】
2025-07-31 18:12:33:593 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:12:33:666 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:12:33:771 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 18:12:33:876 ==>> [D][05:18:00][COMM]read battery soc:255


2025-07-31 18:12:33:901 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:12:33:909 ==>> 检测【打开GSM联网】
2025-07-31 18:12:33:911 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:12:34:058 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:12:34:216 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:12:34:219 ==>> 检测【打开仪表供电1】
2025-07-31 18:12:34:222 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:12:34:459 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:12:34:534 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:12:34:537 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:12:34:540 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:12:34:779 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:12:34:823 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:12:34:828 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:12:34:831 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:12:35:048 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33780]


2025-07-31 18:12:35:098 ==>> 【读取主控ADC采集的仪表电压】通过,【33780mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:12:35:101 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:12:35:103 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:12:35:260 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:12:35:382 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:12:35:385 ==>> 检测【AD_V20电压】
2025-07-31 18:12:35:390 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:12:35:488 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:12:35:579 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:12:35:668 ==>> [D][05:18:02][COMM]13728 imu init OK


2025-07-31 18:12:35:849 ==>> 本次取值间隔时间:351ms
2025-07-31 18:12:35:869 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:12:35:970 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:12:35:985 ==>> [D][05:18:02][COMM]read battery soc:255
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:12:36:090 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1631mV
OVER 150


2025-07-31 18:12:36:240 ==>> 本次取值间隔时间:265ms
2025-07-31 18:12:36:258 ==>> 【AD_V20电压】通过,【1631mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:36:261 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:12:36:264 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:12:36:363 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:12:36:554 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:12:36:558 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:12:36:561 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:12:36:783 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt


2025-07-31 18:12:37:100 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       le_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret

2025-07-31 18:12:37:205 ==>> : 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 18:12:37:590 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:12:37:839 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,36,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:12:37:899 ==>> [D][05:18:04][COMM]read battery soc:255


2025-07-31 18:12:37:901 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:12:37:905 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:12:37:907 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:12:37:959 ==>> 3A A3 02 01 A3 


2025-07-31 18:12:38:064 ==>> ON_OUT2
OVER 150


2025-07-31 18:12:38:198 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:12:38:202 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:12:38:205 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:12:38:379 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:12:38:468 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:12:38:487 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:12:38:491 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:12:38:493 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:12:38:740 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,24,,,42,60,,,41,26,,,40,39,,,39,1*7E

$GBGSV,2,2,08,59,,,38,38,,,35,21,,,30,8,,,37,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1569.539,1569.539,50.254,2097152,2097152,2097152*49

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:12:39:015 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:12:39:019 ==>> 检测【AD_V21电压】
2025-07-31 18:12:39:022 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:12:39:071 ==>> 1A A1 20 00 00 
Get AD_V21 1628mV
OVER 150


2025-07-31 18:12:39:326 ==>> 本次取值间隔时间:303ms
2025-07-31 18:12:39:350 ==>> 【AD_V21电压】通过,【1628mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:12:39:355 ==>> 检测【关闭仪表供电2】
2025-07-31 18:12:39:374 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:12:39:627 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,42,60,,,41,26,,,41,42,,,41,1*75

$GBGSV,3,2,11,39,,,39,59,,,39,13,,,39,16,,,39,1*74

$GBGSV,3,3,11,8,,,38,38,,,37,21,,,35,1*4F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1624.360,1624.360,51.912,2097152,2097152,2097152*41



2025-07-31 18:12:39:892 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:12:39:895 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:12:39:898 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:12:39:917 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 18:12:40:022 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- wr

2025-07-31 18:12:40:052 ==>> ite len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:12:40:184 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:12:40:190 ==>> 检测【打开AccKey2供电】
2025-07-31 18:12:40:206 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:12:40:337 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:12:40:461 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:12:40:464 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:12:40:467 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:12:40:787 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,42,60,,,41,42,,,41,26,,,40,1*74

$GBGSV,5,2,17,13,,,40,39,,,39,59,,,39,8,,,39,1*45

$GBGSV,5,3,17,38,,,39,3,,,39,16,,,38,21,,,37,1*45

$GBGSV,5,4,17,1,,,37,33,,,36,2,,,34,4,,,33,1*40

$GBGSV,5,5,17,5,,,33,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1577.840,1577.840,50.462,2097152,2097152,2097152*4A

[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3107  volt:5461 mv
[D][05:18:07][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:07][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:07][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:07][COMM]adc read battery ts volt:7 mv
[D][05:18:07][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1457  volt:33780 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc 

2025-07-31 18:12:40:818 ==>> read front lamp adc:4  volt:92 mv


2025-07-31 18:12:40:991 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:12:40:995 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:12:40:997 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:12:41:139 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:12:41:279 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:12:41:282 ==>> 该项需要延时执行
2025-07-31 18:12:41:644 ==>> $GBGGA,101245.433,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,60,,,40,42,,,40,26,,,40,1*70

$GBGSV,5,2,20,13,,,40,59,,,40,38,,,40,3,,,40,1*45

$GBGSV,5,3,20,39,,,39,8,,,39,16,,,39,21,,,38,1*45

$GBGSV,5,4,20,1,,,37,33,,,37,2,,,34,4,,,33,1*45

$GBGSV,5,5,20,5,,,33,40,,,29,14,,,38,9,,,37,1*7D

$GBRMC,101245.433,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101245.433,0.000,1566.222,1566.222,50.122,2097152,2097152,2097152*52



2025-07-31 18:12:41:929 ==>> [D][05:18:08][COMM]read battery soc:255


2025-07-31 18:12:42:632 ==>> $GBGGA,101246.413,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,60,,,41,26,,,41,42,,,40,1*71

$GBGSV,6,2,22,13,,,40,59,,,40,38,,,40,3,,,40,1*44

$GBGSV,6,3,22,39,,,39,8,,,39,21,,,39,16,,,38,1*44

$GBGSV,6,4,22,1,,,38,33,,,37,9,,,36,14,,,34,1*74

$GBGSV,6,5,22,2,,,34,4,,,33,5,,,33,6,,,33,1*77

$GBGSV,6,6,22,40,,,30,41,,,42,1*72

$GBRMC,101246.413,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101246.413,0.000,1553.719,1553.719,49.723,2097152,2097152,2097152*5C



2025-07-31 18:12:43:608 ==>> $GBGGA,101247.393,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,60,,,41,26,,,41,42,,,41,1*73

$GBGSV,6,2,21,38,,,41,13,,,40,59,,,40,3,,,40,1*46

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,36,2,,,35,1*41

$GBGSV,6,5,21,6,,,35,14,,,34,4,,,33,5,,,33,1*45

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101247.393,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101247.393,0.000,1563.586,1563.586,50.035,2097152,2097152,2097152*5A



2025-07-31 18:12:43:804 ==>> $GBGGA,101247.593,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,26,,,41,42,,,41,38,,,41,1*7E

$GBGSV,6,2,21,60,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,37,2,,,35,1*40

$GBGSV,6,5,21,6,,,35,14,,,35,4,,,33,5,,,33,1*44

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101247.593,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101247.593,0.000,1565.555,1565.555,50.093,2097152,2097152,2097152*50



2025-07-31 18:12:43:910 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 18:12:44:294 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:12:44:300 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:12:44:328 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:12:44:569 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3105  volt:5458 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:10 mv
[D][05:18:11][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1456  volt:33757 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:12:44:779 ==>> $GBGGA,101248.573,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,26,,,40,42,,,40,38,,,40,1*7F

$GBGSV,6,2,21,60,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,36,6,,,36,1*46

$GBGSV,6,5,21,2,,,35,14,,,35,4,,,33,5,,,33,1*40

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101248.573,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101248.573,0.000,1559.625,1559.625,49.896,2097152,2097152,2097152*54



2025-07-31 18:12:44:852 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:12:44:857 ==>> 检测【打开AccKey1供电】
2025-07-31 18:12:44:873 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:12:45:053 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:12:45:136 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:12:45:140 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:12:45:144 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:45:264 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:45:399 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:45:419 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:45:566 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:45:671 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:45:689 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:45:777 ==>> $GBGGA,101249.553,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,60,,,41,26,,,40,42,,,40,1*73

$GBGSV,6,2,21,38,,,40,13,,,40,59,,,40,3,,,40,1*47

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,36,6,,,36,1*46

$GBGSV,6,5,21,2,,,35,14,,,35,4,,,33,5,,,33,1*40

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,101249.553,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101249.553,0.000,1563.570,1563.570,50.019,2097152,2097152,2097152*50

1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:45:943 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:45:948 ==>> [D][05:18:12][COMM]read battery soc:255


2025-07-31 18:12:45:961 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:46:048 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:46:213 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:46:232 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:46:363 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:46:483 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:46:505 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:46:559 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:46:739 ==>> $GBGGA,101250.533,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,60,,,40,26,,,40,1*7E

$GBGSV,6,2,21,42,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,39,1*46

$GBGSV,6,4,21,1,,,37,33,,,37,9,,,37,6,,,36,1*48

$GBGSV,6,5,21,2,,,36,14,,,35,4,,,33,5,,,33,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,101250.533,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101250.533,0.000,1567.516,1567.516,50.143,2097152,2097152,2097152*50



2025-07-31 18:12:46:769 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:46:787 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:46:844 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:47:039 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:47:060 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:47:163 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:47:313 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:47:366 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:47:466 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:47:616 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:47:663 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:47:706 ==>> $GBGGA,101251.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,60,,,40,26,,,40,1*7E

$GBGSV,6,2,21,42,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,37,6,,,36,1*47

$GBGSV,6,5,21,2,,,36,14,,,35,5,,,34,4,,,33,1*44

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,101251.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101251.513,0.000,1569.486,1569.486,50.201,2097152,2097152,2097152*56

1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:47:917 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:47:947 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 18:12:48:060 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:48:162 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:48:313 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:48:392 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:48:463 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:48:646 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:48:705 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:48:721 ==>> $GBGGA,101252.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,13,,,41,60,,,40,1*79

$GBGSV,6,2,21,26,,,40,42,,,40,59,,,40,3,,,40,1*4C

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,36,6,,,36,1*46

$GBGSV,6,5,21,2,,,36,14,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,101252.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101252.513,0.000,1565.549,1565.549,50.086,2097152,2097152,2097152*58



2025-07-31 18:12:48:766 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:48:962 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:49:007 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:49:067 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:49:266 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:49:303 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:49:359 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:49:554 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:49:573 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:49:720 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150
$GBGGA,101253.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,13,,,40,1*7B

$GBGSV,6,2,21,60,,,40,42,,,40,59,,,40,3,,,40,1*4E

$GBGSV,6,3,21,39,,,39,8,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,36,6,,,36,1*46

$GBGSV,6,5,21,2,,,36,14,,,35,5,,,33,4,,,33,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,101253.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101253.513,0.000,1567.519,1567.519,50.145,2097152,2097152,2097152*57



2025-07-31 18:12:49:825 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:49:853 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:49:960 ==>> [D][05:18:17][COMM]read battery soc:255
1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:50:111 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:50:135 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:50:266 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:50:386 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:50:405 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:50:464 ==>> 1A A1 00 40 00 
Get AD_V14 2mV
OVER 150


2025-07-31 18:12:50:662 ==>> 原始值:【2】, 乘以分压基数【2】还原值:【4】
2025-07-31 18:12:50:692 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:12:50:722 ==>> $GBGGA,101254.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,60,,,41,1*7E

$GBGSV,6,2,21,42,,,41,13,,,40,59,,,40,3,,,40,1*4B

$GBGSV,6,3,21,8,,,40,39,,,39,21,,,39,16,,,39,1*48

$GBGSV,6,4,21,1,,,38,33,,,37,6,,,37,9,,,36,1*47

$GBGSV,6,5,21,2,,,36,14,,,36,5,,,33,4,,,33,1*40

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101254.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101254.513,0.000,1577.398,1577.398,50.468,2097152,2097152,2097152*5A



2025-07-31 18:12:50:767 ==>> 1A A1 00 40 00 
Get AD_V14 1mV
OVER 150


2025-07-31 18:12:50:950 ==>> 原始值:【1】, 乘以分压基数【2】还原值:【2】
2025-07-31 18:12:50:968 ==>> 【读取AccKey1电压(ADV14)前】失败,【2mV】与目标值【5000mV】至【5500mV】不匹配!
2025-07-31 18:12:50:975 ==>> #################### 【测试结束】 ####################
2025-07-31 18:12:50:999 ==>> 关闭5V供电
2025-07-31 18:12:51:001 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:12:51:058 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:12:51:727 ==>> $GBGGA,101255.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,41,60,,,40,1*7F

$GBGSV,6,2,21,42,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,39,1*46

$GBGSV,6,4,21,1,,,38,33,,,37,9,,,37,6,,,36,1*47

$GBGSV,6,5,21,2,,,36,14,,,35,5,,,33,4,,,33,1*43

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101255.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101255.513,0.000,1569.498,1569.498,50.213,2097152,2097152,2097152*51



2025-07-31 18:12:51:941 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 18:12:52:001 ==>> 关闭5V供电成功
2025-07-31 18:12:52:007 ==>> 关闭33V供电
2025-07-31 18:12:52:012 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:12:52:046 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:12:52:227 ==>> [D][05:18:19][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:19][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:12:52:470 ==>> [D][05:18:19][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:12:52:727 ==>> $GBGGA,101256.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,38,,,41,26,,,40,60,,,40,1*7E

$GBGSV,6,2,21,42,,,40,13,,,40,59,,,40,3,,,40,1*4A

$GBGSV,6,3,21,8,,,40,39,,,39,21,,,39,16,,,39,1*48

$GBGSV,6,4,21,1,,,37,33,,,37,9,,,37,6,,,36,1*48

$GBGSV,6,5,21,2,,,36,14,,,36,5,,,33,4,,,33,1*40

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101256.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101256.513,0.000,1569.496,1569.496,50.211,2097152,2097152,2097152*50



2025-07-31 18:12:53:013 ==>> 关闭33V供电成功
2025-07-31 18:12:53:019 ==>> 关闭3.7V供电
2025-07-31 18:12:53:047 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:12:53:060 ==>> 6A A6 02 A6 6A 


2025-07-31 18:12:53:165 ==>> Battery OFF
OVER 150


2025-07-31 18:12:53:772 ==>>  

