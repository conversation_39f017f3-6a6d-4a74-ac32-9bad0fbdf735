2025-07-31 18:27:49:578 ==>> MES查站成功:
查站序号:P510001005312B74验证通过
2025-07-31 18:27:49:585 ==>> 扫码结果:P510001005312B74
2025-07-31 18:27:49:587 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:27:49:589 ==>> 测试参数版本:2024.10.11
2025-07-31 18:27:49:591 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:27:49:593 ==>> 检测【打开透传】
2025-07-31 18:27:49:595 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:27:49:682 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:27:50:037 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:27:50:042 ==>> 检测【检测接地电压】
2025-07-31 18:27:50:044 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:27:50:176 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:27:50:319 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:27:50:322 ==>> 检测【打开小电池】
2025-07-31 18:27:50:324 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:27:50:373 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:27:50:600 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:27:50:603 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:27:50:606 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:27:50:679 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:27:50:892 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:27:50:894 ==>> 检测【等待设备启动】
2025-07-31 18:27:50:896 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:27:51:158 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:27:51:339 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer

2025-07-31 18:27:51:910 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:27:52:687 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:27:52:747 ==>>                                                    

2025-07-31 18:27:52:962 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:27:53:136 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:27:53:613 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:27:53:769 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:27:53:772 ==>> 检测【产品通信】
2025-07-31 18:27:53:774 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:27:54:327 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:27:54:524 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:27:54:794 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:27:55:198 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<


2025-07-31 18:27:55:243 ==>>                                         

2025-07-31 18:27:55:636 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:27:55:835 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:27:56:347 ==>> 摢傟m
=55u?>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[D][05:17:50][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[D][05:17:50][COMM][MC]get MC real state err,rt:-3
[D][05:17:50][COMM]frm_can_weigth_power_set 1
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[D][05:17:50][COMM]index:0,power_mode:0xFF
[D][05:17:50][COMM]index:1,sound_mode:0xFF
[D][05:17:50][COMM]index:2,gsensor_mode:0xFF
[D][05:17:50][COMM]index:3,report_freq_mode:0xFF
[D][05:17:50][COMM]index:4,report_period:0xFF
[D][05:17:50][COMM]index:5,normal_reset_mode:0xFF
[D][05:17:50][COMM]index:6,normal_reset_period:0xFF
[D][05:17:50][COMM]index:7,spock_over_speed:0xFF
[D][05:17:50][COMM]index:8,spock_limit_speed:0xFF
[D][05:17:50][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:17:50][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:17:50][COMM]index:11,ble_scan_mode:0xFF
[D][05:17:50][COMM]index:12,ble_adv_mode:0xFF
[D][05:17:50][COMM]index:13,spock_audio_volumn:0xFF
[D][05:17:50][COMM]index:14,spock_l

2025-07-31 18:27:56:381 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:27:56:386 ==>> 检测【初始化完成检测】
2025-07-31 18:27:56:388 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:27:56:452 ==>> ow_bat_alarm_soc:0xFF
[D][05:17:50][COMM]index:15,bat_auth_mode:0xFF
[D][05:17:50][COMM]index:16,imu_config_params:0xFF
[D][05:17:50][COMM]index:17,long_connect_params:0xFF
[D][05:17:50][COMM]index:18,detain_mark:0xFF
[D][05:17:50][COMM]index:19,lock_pos_report_count:0xFF
[D][05:17:50][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:17:50][COMM]index:21,mc_mode:0xFF
[D][05:17:50][COMM]index:22,S_mode:0xFF
[D][05:17:50][COMM]index:23,overweight:0xFF
[D][05:17:50][COMM]index:24,standstill_mode:0xFF
[D][05:17:50][COMM]index:25,night_mode:0xFF
[D][05:17:50][COMM]index:26,experiment1:0xFF
[D][05:17:50][COMM]index:27,experiment2:0xFF
[D][05:17:50][COMM]index:28,experiment3:0xFF
[D][05:17:50][COMM]index:29,experiment4:0xFF
[D][05:17:50][COMM]index:30,night_mode_start:0xFF
[D][05:17:50][COMM]index:31,night_mode_end:0xFF
[D][05:17:50][COMM]index:33,park_report_minutes:0xFF
[D][05:17:50][COMM]index:34,park_report_mode:0xFF
[D][05:17:50][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:17:50][COMM]index:38,charge_battery_para: FF
[D][05:17:50][COMM]index:39,multirider_mode:0xFF?

2025-07-31 18:27:56:512 ==>>   ?

2025-07-31 18:27:57:404 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:27:58:440 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:27:59:181 ==>> €獨阭
=55u?61 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 18:27:59:485 ==>> 未匹配到【初始化完成检测】数据,请核对检查!
2025-07-31 18:27:59:488 ==>> #################### 【测试结束】 ####################
2025-07-31 18:27:59:508 ==>> 关闭5V供电
2025-07-31 18:27:59:511 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:27:59:579 ==>> 5A A5 04 5A A5 


2025-07-31 18:27:59:685 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54]

2025-07-31 18:27:59:729 ==>> [COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:28:00:210 ==>> [D][05:17:54][COMM]5676 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:28:00:516 ==>> 关闭5V供电成功
2025-07-31 18:28:00:519 ==>> 关闭33V供电
2025-07-31 18:28:00:521 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:28:00:579 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:28:01:210 ==>> [D][05:17:55][COMM]6688 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:28:01:531 ==>> 关闭33V供电成功
2025-07-31 18:28:01:534 ==>> 关闭3.7V供电
2025-07-31 18:28:01:537 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:28:01:577 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:28:01:924 ==>>  

