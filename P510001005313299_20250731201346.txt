2025-07-31 20:13:46:054 ==>> MES查站成功:
查站序号:P510001005313299验证通过
2025-07-31 20:13:46:057 ==>> 扫码结果:P510001005313299
2025-07-31 20:13:46:059 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:13:46:060 ==>> 测试参数版本:2024.10.11
2025-07-31 20:13:46:062 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:13:46:064 ==>> 检测【打开透传】
2025-07-31 20:13:46:065 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:13:46:146 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:13:46:406 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:13:46:408 ==>> 检测【检测接地电压】
2025-07-31 20:13:46:411 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:13:46:547 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:13:46:692 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:13:46:695 ==>> 检测【打开小电池】
2025-07-31 20:13:46:698 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:13:46:742 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:13:46:974 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:13:46:977 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:13:46:980 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:13:47:048 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:13:47:249 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:13:47:251 ==>> 检测【等待设备启动】
2025-07-31 20:13:47:253 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:47:667 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:13:48:297 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:48:312 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:13:48:402 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:13:49:341 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:50:375 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:50:436 ==>> [W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 20:13:51:408 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:52:431 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:53:456 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:54:490 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:55:531 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:56:571 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:13:57:624 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:13:57:628 ==>> #################### 【测试结束】 ####################
2025-07-31 20:13:57:646 ==>> 关闭5V供电
2025-07-31 20:13:57:648 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:13:57:746 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:13:58:647 ==>> 关闭5V供电成功
2025-07-31 20:13:58:650 ==>> 关闭33V供电
2025-07-31 20:13:58:653 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:13:58:739 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:13:59:649 ==>> 关闭33V供电成功
2025-07-31 20:13:59:652 ==>> 关闭3.7V供电
2025-07-31 20:13:59:654 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:13:59:739 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:14:00:071 ==>>  

