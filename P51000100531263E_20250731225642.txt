2025-07-31 22:56:42:645 ==>> MES查站成功:
查站序号:P51000100531263E验证通过
2025-07-31 22:56:42:649 ==>> 扫码结果:P51000100531263E
2025-07-31 22:56:42:650 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:56:42:652 ==>> 测试参数版本:2024.10.11
2025-07-31 22:56:42:654 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:56:42:655 ==>> 检测【打开透传】
2025-07-31 22:56:42:657 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:56:42:709 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:56:42:996 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:56:43:001 ==>> 检测【检测接地电压】
2025-07-31 22:56:43:003 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:56:43:111 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:56:43:281 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:56:43:284 ==>> 检测【打开小电池】
2025-07-31 22:56:43:287 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:56:43:415 ==>> 6A A6 01 A6 6A 


2025-07-31 22:56:43:505 ==>> Battery ON
OVER 150


2025-07-31 22:56:43:554 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:56:43:556 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:56:43:558 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:56:43:610 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:56:43:828 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:56:43:831 ==>> 检测【等待设备启动】
2025-07-31 22:56:43:834 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:56:44:223 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:56:44:421 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:56:44:864 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:56:45:113 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:56:45:490 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:56:45:906 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:56:45:967 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:56:46:181 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:56:46:184 ==>> 检测【产品通信】
2025-07-31 22:56:46:186 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:56:46:378 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:56:46:456 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:56:46:459 ==>> 检测【初始化完成检测】
2025-07-31 22:56:46:462 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:56:46:665 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 22:56:46:729 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:56:46:731 ==>> 检测【关闭大灯控制1】
2025-07-31 22:56:46:733 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:56:46:877 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:56:47:013 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:56:47:016 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:56:47:018 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:56:47:255 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:56:47:289 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:56:47:291 ==>> 检测【关闭仪表供电】
2025-07-31 22:56:47:293 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:56:47:512 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:56:47:561 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:56:47:563 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:56:47:564 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:56:47:692 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:56:47:835 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:56:47:837 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:56:47:840 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:56:48:056 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:56:48:132 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:56:48:137 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:56:48:139 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:56:48:296 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:56:48:405 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:56:48:407 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:56:48:410 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:56:48:509 ==>> 5A A5 01 5A A5 


2025-07-31 22:56:48:614 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:56:48:677 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:56:48:680 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:56:48:683 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:56:48:719 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:56:48:809 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:56:48:949 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:56:48:952 ==>> 该项需要延时执行
2025-07-31 22:56:49:082 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:56:49:609 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5017. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5017. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5017. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5018. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5018. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5019. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5019. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5020. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5020. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5020. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5021. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5021. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change

2025-07-31 22:56:49:639 ==>> : 0x0000E00C71E22217->0x0008F00C71E22217 5021
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5022


2025-07-31 22:56:50:086 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:56:50:300 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:56:50:782 ==>>                                                                                                                                             COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][CO

2025-07-31 22:56:50:888 ==>> MM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][

2025-07-31 22:56:50:993 ==>> 05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:

2025-07-31 22:56:51:038 ==>> 55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 22:56:51:098 ==>>                                                                                                                                           

2025-07-31 22:56:51:203 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 22:56:52:096 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:56:52:759 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:56:52:956 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:56:52:959 ==>> 检测【33V输入电压ADC】
2025-07-31 22:56:52:973 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:56:53:231 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3141  volt:5521 mv
[D][05:17:57][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:57][COMM]adc read left brake adc:8  volt:10 mv
[D][05:17:57][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:5 mv
[D][05:17:57][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:56:53:501 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:56:53:505 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:56:53:508 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:56:53:624 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1097mV
OVER 150


2025-07-31 22:56:53:778 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:53:791 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:56:53:808 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:53:810 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:56:53:812 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 22:56:53:827 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:56:53:829 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:56:53:845 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:56:53:848 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:56:53:868 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:56:53:870 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:56:54:025 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_V4 2mV
Get AD_V5 2764mV
Get AD_V6 1992mV
Get AD_V7 1096mV
OVER 150


2025-07-31 22:56:54:129 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:56:54:152 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:54:155 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:56:54:179 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:54:181 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:56:54:198 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 22:56:54:200 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:56:54:202 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:56:54:216 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:56:54:219 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:56:54:239 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:56:54:242 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:56:54:328 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2764mV
Get AD_V6 1993mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:56:54:433 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:100

2025-07-31 22:56:54:478 ==>> 07. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10008. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10009. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10009
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10009


2025-07-31 22:56:54:516 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:54:518 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:56:54:534 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:54:537 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:56:54:540 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 22:56:54:569 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:56:54:573 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:56:54:587 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:56:54:590 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:56:54:610 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:56:54:632 ==>> 检测【打开WIFI(1)】
2025-07-31 22:56:54:636 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:56:54:785 ==>> [D][05:17:59][COMM]read battery soc:255
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 22:56:54:890 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:56:54:893 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:56:54:896 ==>> 检测【清空消息队列(1)】
2025-07-31 22:56:54:899 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:56:55:341 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D

2025-07-31 22:56:55:386 ==>> ][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:56:55:420 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:56:55:423 ==>> 检测【打开GPS(1)】
2025-07-31 22:56:55:475 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:56:55:792 ==>>                                                                                                                sg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087636645

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541296

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[3

2025-07-31 22:56:55:822 ==>> 1] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:56:55:945 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:56:55:949 ==>> 检测【打开GSM联网】
2025-07-31 22:56:55:975 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:56:56:145 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:56:56:222 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:56:56:225 ==>> 检测【打开仪表供电1】
2025-07-31 22:56:56:227 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:56:56:414 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:56:56:499 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:56:56:501 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:56:56:503 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:56:56:507 ==>>                                                                  AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:56:56:714 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:56:56:774 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 22:56:56:777 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:56:56:781 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:56:56:782 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:56:57:003 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33201]


2025-07-31 22:56:57:071 ==>> 【读取主控ADC采集的仪表电压】通过,【33201mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:56:57:073 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:56:57:075 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:56:57:303 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:56:57:359 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:56:57:362 ==>> 检测【AD_V20电压】
2025-07-31 22:56:57:366 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:56:57:470 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:56:57:515 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:56:57:620 ==>> 1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 22:56:57:651 ==>> 本次取值间隔时间:169ms
2025-07-31 22:56:57:670 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:57:673 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:56:57:676 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:56:57:815 ==>> 3A A3 02 00 A3 


2025-07-31 22:56:57:905 ==>> OFF_OUT2
OVER 150


2025-07-31 22:56:57:958 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:56:57:960 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:56:57:962 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:56:58:162 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:02][COMM]oneline display read state:0
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13729 imu init OK


2025-07-31 22:56:58:244 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:56:58:248 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:56:58:254 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:56:58:267 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:56:58:312 ==>> 3A A3 02 01 A3 


2025-07-31 22:56:58:417 ==>> ON_OUT2
OVER 150


2025-07-31 22:56:58:522 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:56:58:527 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:56:58:529 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:56:58:533 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:56:58:703 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:56:58:777 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 22:56:58:826 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:56:58:829 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:56:58:831 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:56:59:156 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]oneline display set 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_O

2025-07-31 22:56:59:201 ==>> PEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?



2025-07-31 22:56:59:356 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:56:59:360 ==>> 检测【AD_V21电压】
2025-07-31 22:56:59:370 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:56:59:413 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 22:56:59:518 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 22:56:59:608 ==>>                                                      [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 22:56:59:728 ==>> 本次取值间隔时间:369ms
2025-07-31 22:56:59:747 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:56:59:750 ==>> 检测【关闭仪表供电2】
2025-07-31 22:56:59:753 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:56:59:909 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:04][COMM]set POWER 0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:57:00:017 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:57:00:021 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:57:00:025 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:57:00:209 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:04][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:57:00:287 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:57:00:292 ==>> 检测【打开AccKey2供电】
2025-07-31 22:57:00:296 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:57:00:497 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:57:00:561 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:57:00:565 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:57:00:567 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:00:754 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:05][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:05][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:18:05][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:05][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:05][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:05][COMM]adc read battery ts volt:9 mv
[D][05:18:05][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:05][COMM]adc read throttle brake in adc:0  volt:0 mv


2025-07-31 22:57:00:814 ==>>                                                                                                               PSNAVSAT=1F



2025-07-31 22:57:00:832 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33260mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:57:00:837 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:57:00:861 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:57:01:024 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,59,,,42,25,,,41,39,,,41,40,,,41,1*72

$GBGSV,3,2,11,41,,,41,34,,,40,60,,,40,33,,,37,1*72

$GBGSV,3,3,11,23,,,36,16,,,44,10,,,38,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1653.671,1653.671,52.845,2097152,2097152,2097152*41

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 22:57:01:221 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:57:01:857 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:57:01:962 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,3,,,43,40,,,42,59,,,41,25,,,41,1*49

$GBGSV,4,2,16,39,,,41,41,,,41,34,,,41,60,,,40,1*78

$GBGSV,4,3,16,7,,,39,16,,,38,10,,,38,33,,,37,1*49

$GBGSV,4,4,16,23,,,37,2,,,37,4,,,34,5,,,33,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1614.272,1614.272,51.629,2097152,2097152,2097152*46



2025-07-31 22:57:02:294 ==>> [D][05:18:06][COMM][arm_hub_gpio_read]: Failed -2
[D][05:18:06][COMM]arm_hub adc read bat_id adc:-1  volt:0 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:57:02:392 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:57:02:395 ==>> 该项需要延时执行
2025-07-31 22:57:02:796 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 22:57:02:991 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,3,,,41,59,,,41,25,,,41,1*4F

$GBGSV,5,2,20,39,,,41,41,,,41,34,,,41,60,,,41,1*7D

$GBGSV,5,3,20,7,,,40,1,,,39,16,,,38,10,,,38,1*7C

$GBGSV,5,4,20,33,,,37,23,,,37,2,,,37,43,,,35,1*43

$GBGSV,5,5,20,4,,,34,5,,,34,44,,,34,32,,,34,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1587.847,1587.847,50.789,2097152,2097152,2097152*4C



2025-07-31 22:57:04:015 ==>> $GBGGA,145707.835,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,3,,,41,59,,,41,25,,,41,1*4F

$GBGSV,6,2,23,39,,,41,41,,,41,34,,,41,60,,,41,1*7D

$GBGSV,6,3,23,7,,,40,16,,,39,1,,,38,10,,,38,1*7C

$GBGSV,6,4,23,23,,,38,33,,,37,2,,,37,43,,,37,1*4E

$GBGSV,6,5,23,24,,,36,4,,,34,5,,,34,32,,,34,1*70

$GBGSV,6,6,23,44,,,33,6,,,50,9,,,36,1*78

$GBRMC,145707.835,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145707.835,0.000,1587.253,1587.253,50.768,2097152,2097152,2097152*53



2025-07-31 22:57:04:695 ==>> $GBGGA,145708.535,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,3,,,41,59,,,41,25,,,41,1*48

$GBGSV,7,2,25,39,,,41,41,,,41,34,,,41,60,,,41,1*7A

$GBGSV,7,3,25,7,,,40,16,,,39,11,,,39,1,,,38,1*7B

$GBGSV,7,4,25,10,,,38,43,,,38,6,,,37,23,,,37,1*43

$GBGSV,7,5,25,33,,,37,2,,,36,9,,,36,24,,,36,1*7F

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,34,32,,,34,1*73

$GBGSV,7,7,25,44,,,33,1*71

$GBRMC,145708.535,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145708.535,0.000,1573.749,1573.749,50.336,2097152,2097152,2097152*5E



2025-07-31 22:57:04:800 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:57:05:402 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:57:05:408 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:57:05:413 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:05:781 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:10][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:10][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:10][COMM]adc read battery ts volt:11 mv
[D][05:18:10][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:10][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
$GBGGA,145709.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,39,,,41,1*71

$GBGSV,7,2,25,41,,,41,34,,,41,3,,,40,60,,,40,1*43

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,16,,,38,1*4D

$GBGSV,7,4,25,1,,,38,23,,,38,10,,,37,6,,,37,1*75

$GBGSV,7,5,25,33,,,37,24,,,37,2,,,36,9,,,36,1*7E

$GBGSV,7,6,25,12,,,34,4,,,34,5,,,33,32,,,33,1*73

$GBGSV,7,7,25,44,,,33,1*71

$GBRMC,145709.515,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145709.515,0.000,1568.776,1568.776,50.179,2097152,2097152,2097152*54

[D][05:18:10][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D]

2025-07-31 22:57:05:811 ==>> [05:18:10][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:57:05:935 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:57:05:938 ==>> 检测【打开AccKey1供电】
2025-07-31 22:57:05:940 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:57:06:087 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:57:06:211 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:57:06:214 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:57:06:217 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:57:06:315 ==>> 1A A1 00 40 00 
Get AD_V14 2657mV
OVER 150


2025-07-31 22:57:06:465 ==>> 原始值:【2657】, 乘以分压基数【2】还原值:【5314】
2025-07-31 22:57:06:509 ==>> 【读取AccKey1电压(ADV14)前】通过,【5314mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:57:06:514 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:57:06:519 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:06:829 ==>> $GBGGA,145710.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,39,,,41,1*71

$GBGSV,7,2,25,41,,,41,34,,,41,3,,,41,60,,,40,1*42

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,39,1*7A

$GBGSV,7,4,25,16,,,38,23,,,38,10,,,37,6,,,37,1*43

$GBGSV,7,5,25,33,,,37,24,,,37,2,,,36,9,,,36,1*7E

$GBGSV,7,6,25,12,,,35,4,,,34,5,,,34,32,,,33,1*75

$GBGSV,7,7,25,44,,,33,1*71

$GBRMC,145710.515,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145710.515,0.000,1575.405,1575.405,50.387,2097152,2097152,2097152*5F

[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:11][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:11][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:11][COMM]adc read battery ts volt:12 mv
[D][05:18:11][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:11][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 

2025-07-31 22:57:06:889 ==>> mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 22:57:07:044 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5517mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:57:07:047 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:57:07:050 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:57:07:206 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:57:07:315 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:57:07:318 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:57:07:322 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:57:07:419 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150


2025-07-31 22:57:07:569 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 22:57:07:588 ==>> 【读取AccKey1电压(ADV14)后】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:57:07:591 ==>> 检测【打开WIFI(2)】
2025-07-31 22:57:07:596 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:57:07:704 ==>> $GBGGA,145711.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,40,3,,,40,60,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,38,1*7B

$GBGSV,7,4,25,16,,,38,23,,,37,10,,,37,33,,,37,1*7A

$GBGSV,7,5,25,24,,,37,6,,,36,2,,,36,9,,,36,1*49

$GBGSV,7,6,25,12,,,35,4,,,34,5,,,34,32,,,33,1*75

$GBGSV,7,7,25,44,,,33,1*71

$GBRMC,145711.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145711.515,0.000,1567.111,1567.111,50.120,2097152,2097152,2097152*51



2025-07-31 22:57:07:809 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12,

2025-07-31 22:57:07:839 ==>>  ret: 6


2025-07-31 22:57:07:871 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:57:07:874 ==>> 检测【转刹把供电】
2025-07-31 22:57:07:879 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:57:08:094 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:57:08:143 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:57:08:148 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:57:08:157 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:57:08:245 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:57:08:275 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:57:08:320 ==>> 1A A1 00 80 00 
Get AD_V15 2396mV
OVER 150


2025-07-31 22:57:08:410 ==>> 原始值:【2396】, 乘以分压基数【2】还原值:【4792】
2025-07-31 22:57:08:428 ==>> 【读取AD_V15电压(前)】通过,【4792mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:57:08:432 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:57:08:435 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:57:08:531 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:57:08:696 ==>> 1A A1 01 00 00 
Get AD_V16 2424mV
OVER 150
+WIFISCAN:4,0,CC057790A641,-71
+WIFISCAN:4,1,44A1917CA62B,-74
+WIFISCAN:4,2,CC057790A4A1,-84
+WIFISCAN:4,3,F86FB0660A82,-85

[D][05:18:13][CAT1]wifi scan report total[4]
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,145712.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,25,,,41,41,,,41,1*7D

$GBGSV,7,2,26,34,,,41,60,,,41,39,,,40,3,,,40,1*4F

$GBGSV,7,3,26,7,,,40,11,,,39,43,,,39,1,,,39,1*79

$GBGSV,7,4,26,16,,,38,23,,,37,10,,,37,33,,,37,1*79

$GBGSV,7,5,26,24,,,37,2,,,37,6,,,36,9,,,36,1*4B

$GBGSV,7,6,26,12,,,36,4,,,34,5,,,34,32,,,34,1*72

$GBGSV,7,7,26,44,,,34,20,,,37,1*73

$GBRMC,145712.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145712.515,0.000,1577.053,1577.053,50.430,2097152,2097152,2097152*56



2025-07-31 22:57:08:831 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:57:08:861 ==>> 原始值:【2424】, 乘以分压基数【2】还原值:【4848】
2025-07-31 22:57:08:889 ==>> 【读取AD_V16电压(前)】通过,【4848mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:57:08:892 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:57:08:896 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:09:226 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:13][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:13][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:13][COMM]adc read battery ts volt:7 mv
[D][05:18:13][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:13][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:57:09:256 ==>>                                       

2025-07-31 22:57:09:427 ==>> 【转刹把供电电压(主控ADC)】通过,【5414mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:57:09:433 ==>> 检测【转刹把供电电压】
2025-07-31 22:57:09:455 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:09:801 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:14][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:14][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:14][COMM]adc read battery ts volt:12 mv
[D][05:18:14][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3071  volt:5398 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
$GBGGA,145713.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,42,25,,,41,41,,,41,1*7D

$GBGSV,7,2,25,34,,,41,60,,,41,39,,,41,3,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,39,1*7A

$GBGSV,7,4,25,16,,,38,23,,,38,10,,,37,33,,,37,1*75

$GBGSV,7,5,25,24,,,37,2,,,37,6,,,36,9,,,36,1*48

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,32,,,34,1*71

$GBGSV,7,7,25,44,,,34,1*76

$GBRMC,145713.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145713.515,0.000,1582.032,1582.032,50.593,2097152,2097152,2097152*5F

[D][05:18:14][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1431  volt:33

2025-07-31 22:57:09:831 ==>> 178 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:57:09:954 ==>> 【转刹把供电电压】通过,【5398mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:57:09:958 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:57:09:963 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:57:10:075 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:57:10:227 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:57:10:233 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:57:10:237 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:57:10:333 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:57:10:378 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:57:10:408 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:57:10:457 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:57:10:460 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:57:10:463 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:57:10:558 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:57:10:664 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:57:10:694 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,145714.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,41,60,,,40,3,,,40,1*4C

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,38,1*7B

$GBGSV,7,4,25,16,,,38,23,,,38,10,,,37,33,,,37,1*75

$GBGSV,7,5,25,24,,,37,2,,,36,6,,,36,9,,,36,1*49

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,32,,,34,1*71

$GBGSV,7,7,25,44,,,34,1*76

$GBRMC,145714.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145714.515,0.000,1575.395,1575.395,50.377,2097152,2097152,2097152*54



2025-07-31 22:57:10:769 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:57:10:845 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[D][05:18:15][COMM]read battery soc:255


2025-07-31 22:57:10:920 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:57:10:933 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:57:10:937 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:57:10:941 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:57:11:010 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:57:11:242 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:57:11:245 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:57:11:250 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:57:11:317 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:57:11:564 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:57:11:567 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:57:11:573 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:57:11:618 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:57:11:708 ==>> $GBGGA,145715.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,41,60,,,41,3,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,39,1*7A

$GBGSV,7,4,25,16,,,38,23,,,38,10,,,37,33,,,37,1*75

$GBGSV,7,5,25,24,,,37,2,,,36,6,,,36,9,,,36,1*49

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,32,,,34,1*71

$GBGSV,7,7,25,44,,,34,1*76

$GBRMC,145715.515,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145715.515,0.000,1578.714,1578.714,50.485,2097152,2097152,2097152*5F



2025-07-31 22:57:11:846 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:57:11:850 ==>> 检测【左刹电压测试1】
2025-07-31 22:57:11:852 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:12:131 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:1716  volt:2262 mv
[D][05:18:16][COMM]adc read right brake adc:1715  volt:2260 mv
[D][05:18:16][COMM]adc read throttle adc:1713  volt:2258 mv
[D][05:18:16][COMM]adc read battery ts volt:10 mv
[D][05:18:16][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:57:12:377 ==>> 【左刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 22:57:12:380 ==>> 检测【右刹电压测试1】
2025-07-31 22:57:12:399 ==>> 【右刹电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 22:57:12:402 ==>> 检测【转把电压测试1】
2025-07-31 22:57:12:419 ==>> 【转把电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 22:57:12:422 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:57:12:424 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:57:12:507 ==>> 3A A3 03 00 A3 


2025-07-31 22:57:12:612 ==>> OFF_OUT3
OVER 150


2025-07-31 22:57:12:690 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:57:12:696 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:57:12:718 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:57:12:721 ==>> $GBGGA,145716.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,41,60,,,40,3,,,40,1*4C

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,39,1*7A

$GBGSV,7,4,25,16,,,38,10,,,38,23,,,37,33,,,37,1*75

$GBGSV,7,5,25,24,,,37,2,,,36,6,,,36,9,,,36,1*49

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,44,,,34,1*70

$GBGSV,7,7,25,32,,,33,1*70

$GBRMC,145716.515,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145716.515,0.000,1575.399,1575.399,50.381,2097152,2097152,2097152*5F



2025-07-31 22:57:12:807 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:57:12:852 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 22:57:12:961 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:57:12:967 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:57:12:990 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:57:13:018 ==>> 3A A3 05 00 A3 


2025-07-31 22:57:13:108 ==>> OFF_OUT5
OVER 150


2025-07-31 22:57:13:232 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:57:13:235 ==>> 检测【左刹电压测试2】
2025-07-31 22:57:13:238 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:57:13:530 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:17][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:17][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:17][COMM]adc read battery ts volt:10 mv
[D][05:18:17][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:17][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:57:13:635 ==>> $GBGGA,145717.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBG

2025-07-31 22:57:13:695 ==>> SV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,41,3,,,41,60,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,38,1*7B

$GBGSV,7,4,25,16,,,38,10,,,38,23,,,38,33,,,37,1*7A

$GBGSV,7,5,25,24,,,37,6,,,37,2,,,36,9,,,36,1*48

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,44,,,34,1*70

$GBGSV,7,7,25,32,,,34,1*77

$GBRMC,145717.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145717.515,0.000,1580.370,1580.370,50.536,2097152,2097152,2097152*54



2025-07-31 22:57:13:759 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 22:57:13:762 ==>> 检测【右刹电压测试2】
2025-07-31 22:57:13:779 ==>> 【右刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:57:13:783 ==>> 检测【转把电压测试2】
2025-07-31 22:57:13:801 ==>> 【转把电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:57:13:807 ==>> 检测【晶振检测】
2025-07-31 22:57:13:810 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:57:13:998 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:18][COMM][lf state:1][hf state:1]


2025-07-31 22:57:14:077 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:57:14:086 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:57:14:108 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:57:14:223 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1659mV
Get AD_V4 1652mV
Get AD_V5 2762mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 22:57:14:346 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:57:14:351 ==>> 检测【检测BootVer】
2025-07-31 22:57:14:354 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:14:731 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:19][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
[D][05:18:19][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:19][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:19][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:19][FCTY]DeviceID    = 460130071541296
[D][05:18:19][FCTY]HardwareID  = 867222087636645
[D][05:18:19][FCTY]MoBikeID    = 9999999999
[D][05:18:19][FCTY]LockID      = FFFFFFFFFF
[D][05:18:19][FCTY]BLEFWVersion= 105
[D][05:18:19][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:19][FCTY]Bat         = 3924 mv
[D][05:18:19][FCTY]Current     = 0 ma
[D][05:18:19][FCTY]VBUS        = 11800 mv
[D][05:18:19][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:19][FCTY]Ext battery vol = 32, adc = 1285
[D][05:18:19][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
[D][05:18:19][FCTY]Bike Type flag is invalied
[D][05:18:19][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:19][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:19][FCTY]CAT1_GNS

2025-07-31 22:57:14:821 ==>> S_PLATFORM = C4
[D][05:18:19][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:19][FCTY]Bat1         = 3815 mv
[D][05:18:19][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
$GBGGA,145718.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,25,,,41,41,,,41,1*7E

$GBGSV,7,2,25,34,,,41,39,,,41,3,,,41,60,,,41,1*4C

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,1,,,38,1*7B

$GBGSV,7,4,25,16,,,38,10,,,38,23,,,37,33,,,37,1*75

$GBGSV,7,5,25,24,,,37,2,,,37,6,,,36,9,,,36,1*48

$GBGSV,7,6,25,12,,,36,4,,,34,5,,,34,44,,,34,1*70

$GBGSV,7,7,25,32,,,34,1*77

$GBRMC,145718.515,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145718.515,0.000,1580.373,1580.373,50.539,2097152,2097152,2097152*54



2025-07-31 22:57:14:851 ==>>                                          

2025-07-31 22:57:14:891 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:57:14:895 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:57:14:898 ==>> 检测【检测固件版本】
2025-07-31 22:57:14:909 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:57:14:917 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:57:14:943 ==>> 检测【检测蓝牙版本】
2025-07-31 22:57:14:962 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:57:14:984 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:57:14:988 ==>> 检测【检测MoBikeId】
2025-07-31 22:57:15:006 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:57:15:010 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:57:15:014 ==>> 检测【检测蓝牙地址】
2025-07-31 22:57:15:017 ==>> 取到目标值:DC5FD95EA952
2025-07-31 22:57:15:028 ==>> 【检测蓝牙地址】通过,【DC5FD95EA952】符合目标值【】要求!
2025-07-31 22:57:15:032 ==>> 提取到蓝牙地址:DC5FD95EA952
2025-07-31 22:57:15:035 ==>> 检测【BOARD_ID】
2025-07-31 22:57:15:038 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:57:15:044 ==>> 检测【检测充电电压】
2025-07-31 22:57:15:068 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:57:15:075 ==>> 检测【检测VBUS电压1】
2025-07-31 22:57:15:097 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:57:15:100 ==>> 检测【检测充电电流】
2025-07-31 22:57:15:106 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:57:15:122 ==>> 检测【检测IMEI】
2025-07-31 22:57:15:125 ==>> 取到目标值:867222087636645
2025-07-31 22:57:15:130 ==>> 【检测IMEI】通过,【867222087636645】符合目标值【】要求!
2025-07-31 22:57:15:136 ==>> 提取到IMEI:867222087636645
2025-07-31 22:57:15:163 ==>> 检测【检测IMSI】
2025-07-31 22:57:15:167 ==>> 取到目标值:460130071541296
2025-07-31 22:57:15:188 ==>> 【检测IMSI】通过,【460130071541296】符合目标值【】要求!
2025-07-31 22:57:15:192 ==>> 提取到IMSI:460130071541296
2025-07-31 22:57:15:195 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:57:15:219 ==>> 取到目标值:460130071541296
2025-07-31 22:57:15:223 ==>> 【校验网络运营商(移动)】通过,【460130071541296】符合目标值【】要求!
2025-07-31 22:57:15:228 ==>> 检测【打开CAN通信】
2025-07-31 22:57:15:233 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:57:15:311 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:57:15:465 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:57:15:469 ==>> 检测【检测CAN通信】
2025-07-31 22:57:15:473 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:57:15:643 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:57:15:736 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:57:15:740 ==>> 检测【关闭CAN通信】
2025-07-31 22:57:15:746 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:57:15:749 ==>> $GBGGA,145719.515,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,25,,,41,41,,,41,1*7D

$GBGSV,7,2,26,34,,,41,39,,,41,3,,,41,60,,,41,1*4F

$GBGSV,7,3,26,7,,,40,11,,,40,43,,,39,1,,,39,1*77

$GBGSV,7,4,26,16,,,38,10,,,38,23,,,38,33,,,37,1*79

$GBGSV,7,5,26,24,,,37,2,,,36,6,,,36,9,,,36,1*4A

$GBGSV,7,6,26,12,,,36,4,,,34,5,,,34,44,,,34,1*73

$GBGSV,7,7,26,32,,,34,14,,,28,1*7B

$GBRMC,145719.515,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145719.515,0.000,1567.459,1567.459,50.158,2097152,2097152,2097152*56

[D][05:18:20][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 31274
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:57:15:823 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:57:16:007 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:57:16:013 ==>> 检测【打印IMU STATE】
2025-07-31 22:57:16:018 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:57:16:216 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:20][COMM]YAW data: 32763[32763]
[D][05:18:20][COMM]pitch:-66 roll:1
[D][05:18:20][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:57:16:279 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:57:16:285 ==>> 检测【六轴自检】
2025-07-31 22:57:16:294 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:57:16:506 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:21][CAT1]gsm read msg sub id: 12
[D][05:18:21][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:57:16:961 ==>> [D][05:18:21][COMM]read battery soc:255
$GBGGA,145720.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,25,,,41,41,,,41,1*7D

$GBGSV,7,2,26,34,,,41,39,,,41,3,,,41,60,,,40,1*4E

$GBGSV,7,3,26,7,,,40,11,,,39,43,,,39,1,,,39,1*79

$GBGSV,7,4,26,16,,,39,10,,,38,23,,,38,33,,,37,1*78

$GBGSV,7,5,26,24,,,37,2,,,36,6,,,36,9,,,36,1*4A

$GBGSV,7,6,26,12,,,36,4,,,34,5,,,34,44,,,34,1*73

$GBGSV,7,7,26,32,,,34,14,,,29,1*7A

$GBRMC,145720.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145720.515,0.000,1567.451,1567.451,50.150,2097152,2097152,2097152*54



2025-07-31 22:57:17:682 ==>> $GBGGA,145721.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,25,,,41,41,,,41,1*7D

$GBGSV,7,2,26,34,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,11,,,39,43,,,39,1,,,39,1*79

$GBGSV,7,4,26,16,,,38,10,,,37,23,,,37,33,,,37,1*79

$GBGSV,7,5,26,24,,,37,2,,,36,6,,,36,9,,,36,1*4A

$GBGSV,7,6,26,12,,,36,4,,,34,5,,,34,44,,,34,1*73

$GBGSV,7,7,26,32,,,34,14,,,29,1*7A

$GBRMC,145721.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145721.515,0.000,1561.071,1561.071,49.945,2097152,2097152,2097152*51



2025-07-31 22:57:18:192 ==>> [D][05:18:22][CAT1]<<< 
OK

[D][05:18:22][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:57:18:390 ==>> [D][05:18:22][COMM]Main Task receive event:142
[D][05:18:22][COMM]###### 33961 imu self test OK ######
[D][05:18:22][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,-15,4057]
[D][05:18:22][COMM]Main Task receive event:142 finished processing


2025-07-31 22:57:18:633 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:57:18:640 ==>> 检测【打印IMU STATE2】
2025-07-31 22:57:18:661 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:57:18:705 ==>> $GBGGA,145722.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,41,25,,,41,41,,,41,1*7D

$GBGSV,7,2,26,34,,,41,39,,,41,3,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,11,,,39,43,,,39,1,,,38,1*78

$GBGSV,7,4,26,16,,,38,23,,,38,10,,,37,33,,,37,1*76

$GBGSV,7,5,26,24,,,37,2,,,36,6,,,36,9,,,36,1*4A

$GBGSV,7,6,26,12,,,36,4,,,34,5,,,34,32,,,34,1*72

$GBGSV,7,7,26,44,,,33,14,,,29,1*7C

$GBRMC,145722.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145722.515,0.000,1559.478,1559.478,49.896,2097152,2097152,2097152*5D



2025-07-31 22:57:18:809 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:1
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:57:18:869 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:57:18:905 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:57:18:909 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:57:18:912 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:57:19:019 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:57:19:188 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:57:19:192 ==>> 检测【检测VBUS电压2】
2025-07-31 22:57:19:203 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:19:208 ==>> [D][05:18:23][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 22:57:19:575 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071541296
[D][05:18:23][FCTY]HardwareID  = 867222087636645
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:23][FCTY]Bat         = 3924 mv
[D][05:18:23][FCTY]Current     = 150 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:23][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:23][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18

2025-07-31 22:57:19:680 ==>> :23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3815 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 22:57:19:713 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:20:071 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071541296
[D][05:18:24][FCTY]HardwareID  = 867222087636645
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:24][FCTY]Bat         = 3924 mv
[D][05:18:24][FCTY]Current     = 150 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 4, adc = 173
[D][05:18:24][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLAT

2025-07-31 22:57:20:116 ==>> FORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3815 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:57:20:239 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:20:574 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071541296
[D][05:18:24][FCTY]HardwareID  = 867222087636645
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:24][FCTY]Bat         = 3844 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 9300 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 3, adc = 144
[D][05:18:24][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS

2025-07-31 22:57:20:619 ==>> _VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3815 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:57:20:724 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  [D][05:18:25][COMM]msg 0601 loss. last_tick:31266. cur

2025-07-31 22:57:20:754 ==>> _tick:36270. period:500
[D][05:18:25][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 36270


2025-07-31 22:57:20:773 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:21:531 ==>> [D][05:18:25][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:25][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:25][COMM]Main Task receive event:65
[D][05:18:25][COMM]main task tmp_sleep_event = 80
[D][05:18:25][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:25][COMM]Main Task receive event:65 finished processing
[D][05:18:25][COMM]Main Task receive event:60
[D][05:18:25][COMM]smart_helmet_vol=255,255
[D][05:18:25][COMM]BAT CAN get state1 Fail 204
[D][05:18:25][COMM]BAT CAN get soc Fail, 204
[W][05:18:25][GNSS]stop locating
[D][05:18:25][GNSS]stop event:8
[D][05:18:25][GNSS]GPS stop. ret=0
[D][05:18:25][GNSS]all continue location stop
[D][05:18:25][COMM]report elecbike
[D][05:18:25][CAT1]gsm read msg sub id: 24
[W][05:18:25][PROT]remove success[1629955105],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:25][PROT]add success [1629955105],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:25][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:25][COMM]Main Task receive event:60 finished processing
[W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]=

2025-07-31 22:57:21:636 ==>> =========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541296
[D][05:18:25][FCTY]HardwareID  = 867222087636645
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:25][FCTY]Bat         = 3844 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 9300 mv
[D][05:18:25][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:25][PROT]index:0
[D][05:18:25][PROT]is_send:1
[D][05:18:25][PROT]sequence_num:4
[D][05:18:25][PROT]retry_timeout:0
[D][05:18:25][PROT]retry_times:3
[D][05:18:25][PROT]send_path:0x3
[D][05:18:25][PROT]msg_type:0x5d03
[D][05:18:25][PROT]===========================================================
[W][05:18:25][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955105]
[D][05:18:25][PROT]===========================================================
[D][05:18:25][PROT]Sending tr

2025-07-31 22:57:21:741 ==>> aceid[9999999999900005]
[D][05:18:25][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:25][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:25][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:25][PROT]index:0 1629955105
[D][05:18:25][PROT]is_send:0
[D][05:18:25][PROT]sequence_num:4
[D][05:18:25][PROT]retry_timeout:0
[D][05:18:25][PROT]retry_times:3
[D][05:18:25][PROT]send_path:0x2
[D][05:18:25][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:25][PROT]===========================================================
[D][05:18:25][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:25][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955105]
[D][05:18:25][PROT]===========================================================
[D][05:18:25][PROT]sending traceid [9999999999900005]
[D][05:18:25][PROT]Send_TO_M2M [1629955105]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:25][SAL ]sock send credit cnt[6]
[D][05:18:25][SAL ]sock send ind credit cnt[6]
[D][05:18:25][M2M ]m2m send data len[198]
[D][05:18:25][SAL ]Cellular task submsg id[10]
[D][05:18:25][SAL ]cellul

2025-07-31 22:57:21:846 ==>> ar SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:25][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 3, adc = 126
[D][05:18:25][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3815 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 24, ret: 6
[D][05:18:25][CAT1]sub id: 24, ret: 6

[D][05:18:25][CAT1]gsm read msg sub id: 15
[D][05:18:25][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:25][CAT1]Sen

2025-07-31 22:57:21:941 ==>> d Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5A228DC26DA1DDEC2FC0281359DE963174334DE591976A1BC476175E9AEAB321418D39E6163B0DD3EE1317599263051D6A9FF3D848D75635B5C23CEF188C6490B20EC39FD3F7C4B9C66A054277A5DA1E750A8
[D][05:18:25][CAT1]<<< 
SEND OK

[D][05:18:25][CAT1]exec over: func id: 15, ret: 11
[D][05:18:25][CAT1]sub id: 15, ret: 11

[D][05:18:25][SAL ]Cellular task submsg id[68]
[D][05:18:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:25][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:25][M2M ]g_m2m_is_idle become true
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:25][PROT]M2M Send ok [1629955105]
[D][05:18:25][GNSS]recv submsg id[1]
[D][05:18:25][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:25][GNSS]location stop evt done evt


2025-07-31 22:57:22:098 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:57:22:468 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541296
[D][05:18:26][FCTY]HardwareID  = 867222087636645
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = DC5FD95EA952
[D][05:18:26][FCTY]Bat         = 3864 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 4900 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 2, adc = 95
[D][05:18:26][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4


2025-07-31 22:57:22:513 ==>> [D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3815 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:57:22:637 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:57:22:642 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:57:22:649 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:57:22:708 ==>> 5A A5 01 5A A5 


2025-07-31 22:57:22:813 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 22:57:22:903 ==>> [D][05:18:27][COMM]read battery soc:255
[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 23


2025-07-31 22:57:22:919 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:57:22:925 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:57:22:930 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:57:23:008 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:57:23:196 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:57:23:201 ==>> 检测【打开WIFI(3)】
2025-07-31 22:57:23:208 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:57:23:438 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:57:23:471 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:57:23:476 ==>> 检测【扩展芯片hw】
2025-07-31 22:57:23:480 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:57:23:708 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:28][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:57:23:742 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:57:23:750 ==>> 检测【扩展芯片boot】
2025-07-31 22:57:23:765 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:57:23:770 ==>> 检测【扩展芯片sw】
2025-07-31 22:57:23:783 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:57:23:788 ==>> 检测【检测音频FLASH】
2025-07-31 22:57:23:815 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:57:23:984 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:57:24:335 ==>> +WIFISCAN:4,0,CC057790A640,-70
+WIFISCAN:4,1,F62A7D2297A3,-70
+WIFISCAN:4,2,CC057790A4A1,-83
+WIFISCAN:4,3,F86FB0660A82,-85

[D][05:18:28][CAT1]wifi scan report total[4]
[D][05:18:28][GNSS]recv submsg id[3]


2025-07-31 22:57:24:500 ==>> [D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:57:25:196 ==>>                                                                                                                                                         et Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_pro

2025-07-31 22:57:25:301 ==>> cess].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fat

2025-07-31 22:57:25:406 ==>> al code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][PROT]index:1
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN

2025-07-31 22:57:25:511 ==>>  [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]f:[ec800

2025-07-31 22:57:25:616 ==>> m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].h

2025-07-31 22:57:25:691 ==>> exsend, index:5, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]read battery soc:255
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 22:57:26:570 ==>> [D][05:18:30][PROT]CLEAN,SEND:0
[D][05:18:30][PROT]index:1 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:5
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900006]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
00

2025-07-31 22:57:26:645 ==>> 63B98D113311331133113311331B88B300530FC27DEBA85D9F075C4C0A3FFA881FA2A7132A0353FBAE1B9DFDF09D961D1FE12ADB5BA78DDE33EF2B111F7A413BE806655F2F2113C60B209495B152301791D2E8B1B8F4D7C72E93D8D1C9481118791F
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 22:57:26:902 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 22:57:27:558 ==>> [D][05:18:32][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:32][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:57:28:113 ==>> [D][05:18:32][COMM]crc 108B
[D][05:18:32][COMM]flash test ok


2025-07-31 22:57:28:684 ==>> [D][05:18:33][COMM]44131 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:33][COMM]accel parse set 0
[D][05:18:33][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:57:28:838 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:57:28:843 ==>> 检测【打开喇叭声音】
2025-07-31 22:57:28:847 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:57:28:910 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:57:29:521 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:33][COMM]file:A20 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:A20 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:33][COMM]read file, len:15228, num:4
[D][05:18:33][COMM]--->crc16:0x419c
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33

2025-07-31 22:57:29:626 ==>> ][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_proces

2025-07-31 22:57:29:642 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:57:29:647 ==>> 检测【打开大灯控制】
2025-07-31 22:57:29:652 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:57:29:731 ==>> s].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
                                      

2025-07-31 22:57:29:791 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 22:57:29:915 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:57:29:920 ==>> 检测【关闭仪表供电3】
2025-07-31 22:57:29:928 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:57:30:094 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:34][COMM]set POWER 0


2025-07-31 22:57:30:186 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:57:30:193 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:57:30:200 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:57:30:371 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:57:30:473 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:57:30:479 ==>> 检测【读大灯电压】
2025-07-31 22:57:30:488 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:57:30:703 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:35][COMM]arm_hub read adc[5],val[33293]


2025-07-31 22:57:30:752 ==>> 【读大灯电压】通过,【33293mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:57:30:760 ==>> 检测【关闭大灯控制2】
2025-07-31 22:57:30:782 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:57:30:916 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:18:35][COMM]read battery soc:255


2025-07-31 22:57:31:024 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:57:31:029 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:57:31:034 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:57:31:203 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:35][COMM]arm_hub read adc[5],val[92]


2025-07-31 22:57:31:296 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:57:31:301 ==>> 检测【打开WIFI(4)】
2025-07-31 22:57:31:306 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:57:31:735 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:36][CAT1]gsm read msg sub id: 12
[D][05:18:36][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:36][CAT1]<<< 
OK

[D][05:18:36][CAT1]exec over: func id: 12, ret: 6
[D][05:18:36][PROT]CLEAN,SEND:1
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:2
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][M2M ]m2m switch to

2025-07-31 22:57:31:765 ==>> : M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198



2025-07-31 22:57:31:922 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:57:31:928 ==>> 检测【EC800M模组版本】
2025-07-31 22:57:31:934 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:57:32:220 ==>> [D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B39650B2D9F98BADDFAF4D374C996B5E2ECFE361F6CD3E1C50F7BBF518C30507BA877C68686D93A804284503A8C1C79310B721AB71C13BDA382F89D10BABF0A509E9D8D9F25BAA281CADAC5856E731639C4652
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]
[W][05:18:36][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:36][CAT1]gsm read msg sub id: 12
[D][05:18:36][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:36][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:36][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:57:32:454 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:57:32:463 ==>> 检测【配置蓝牙地址】
2025-07-31 22:57:32:472 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:57:32:576 ==>> [W][05:18:37][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:57:32:666 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:DC5FD95EA952>】
2025-07-31 22:57:32:921 ==>> [D][05:18:37][COMM]read battery soc:255
recv ble 1
recv ble 2
ble set mac ok :dc,5f,d9,5e,a9,52
enable filters ret : 0

2025-07-31 22:57:33:057 ==>> [D][05:18:37][COMM]48632 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:57:33:195 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:57:33:201 ==>> 检测【BLETEST】
2025-07-31 22:57:33:209 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:57:33:312 ==>> 4A A4 01 A4 4A 


2025-07-31 22:57:33:526 ==>> recv ble 1
recv ble 2
<BSJ*MAC:DC5FD95EA952*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9DC5FD95EA95299999OVER 150


2025-07-31 22:57:34:059 ==>> [D][05:18:38][COMM]49643 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:57:34:274 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:57:34:279 ==>> 该项需要延时执行
2025-07-31 22:57:34:636 ==>> [D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:39][COMM]accel parse set 0
[D][05:18:39][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:57:34:938 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:57:35:042 ==>> [D][05:18:39][COMM]50655 imu init OK


2025-07-31 22:57:36:951 ==>> +WIFISCAN:4,0,CC057790A641,-70
+WIFISCAN:4,1,74C330CCAB10,-72
+WIFISCAN:4,2,44A1917CA62B,-74
+WIFISCAN:4,3,CC057790A4A1,-84

[D][05:18:41][CAT1]wifi scan report total[4]
[D][05:18:41][COMM]read battery soc:255


2025-07-31 22:57:37:451 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:1
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]Send Data To Server[198][198] ... ->:
0063B9811

2025-07-31 22:57:37:526 ==>> 13311331133113311331B88B30271E71C6779E4CAF970EC622CD237629D434F78BFAF40A435DE2081B09BCDC1BCA895EECC2DB39F07643C01FEC40CE628C225598EB054F1E7185E60EB6B13AE8CE880FC8EA5AAAAFAE6F25E9D989BFC5A46
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]
[D][05:18:41][GNSS]recv submsg id[3]


2025-07-31 22:57:38:940 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 22:57:40:936 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:57:42:676 ==>> [D][05:18:47][PROT]CLEAN,SEND:1
[D][05:18:47][PROT]CLEAN:1
[D][05:18:47][PROT]index:0 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:4
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:2
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900005]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5103

2025-07-31 22:57:42:751 ==>> 065F94D3DE78F88C5A7B2FEA65F914D5A90E01C5029342747D70ADD771991206E72E516744E5BF934D84284F9BEB2468C48E566197D674EC2CF5AA64E61CE078F4C8C470670EC4C706FC32E83252AFE93
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]


2025-07-31 22:57:42:950 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:57:44:287 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:57:44:293 ==>> 检测【检测WiFi结果】
2025-07-31 22:57:44:301 ==>> WiFi信号:【CC057790A641】,信号值:-71
2025-07-31 22:57:44:323 ==>> WiFi信号:【44A1917CA62B】,信号值:-74
2025-07-31 22:57:44:341 ==>> WiFi信号:【CC057790A4A1】,信号值:-84
2025-07-31 22:57:44:346 ==>> WiFi信号:【F86FB0660A82】,信号值:-85
2025-07-31 22:57:44:355 ==>> WiFi信号:【CC057790A640】,信号值:-70
2025-07-31 22:57:44:386 ==>> WiFi信号:【F62A7D2297A3】,信号值:-70
2025-07-31 22:57:44:402 ==>> WiFi信号:【74C330CCAB10】,信号值:-72
2025-07-31 22:57:44:410 ==>> WiFi数量【7】, 最大信号值:-70
2025-07-31 22:57:44:432 ==>> 检测【检测GPS结果】
2025-07-31 22:57:44:440 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:57:44:506 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all continue location stop
[W][05:18:49][GNSS]stop locating
[D][05:18:49][GNSS]all sing location stop


2025-07-31 22:57:44:951 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:57:45:297 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:57:45:306 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:45:329 ==>> 定位已等待【1】秒.
2025-07-31 22:57:45:732 ==>> [D][05:18:50][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:50][COMM]Open GPS Module...
[D][05:18:50][COMM]LOC_MODEL_CONT
[D][05:18:50][GNSS]start event:8
[D][05:18:50][GNSS]GPS start. ret=0
[W][05:18:50][GNSS]start cont locating
[D][05:18:50][CAT1]gsm read msg sub id: 23
[D][05:18:50][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:50][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 22:57:46:299 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:46:308 ==>> 定位已等待【2】秒.
2025-07-31 22:57:46:422 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:57:46:963 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 22:57:47:069 ==>> [

2025-07-31 22:57:47:099 ==>> D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:57:47:295 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,34,,,41,39,,,40,41,,,40,40,,,39,1*7E

$GBGSV,2,2,08,23,,,38,25,,,35,59,,,42,16,,,36,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1609.914,1609.914,51.451,2097152,2097152,2097152*4B

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:51][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]exec over: func id: 23, ret: 6
[D][05:18:51][CAT1]sub id: 23, ret: 6



2025-07-31 22:57:47:310 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:47:315 ==>> 定位已等待【3】秒.
2025-07-31 22:57:47:446 ==>> [D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:57:47:551 ==>> [D][05:18:52][COMM]IMU: [-1,0,-928] ret=21 AWAKE!


2025-07-31 22:57:47:914 ==>> [D][05:18:52][PROT]CLEAN,SEND:0
[D][05:18:52][PROT]index:0 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:4
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:1
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:52][PROT]===========================================================
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900005]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B50359DA09

2025-07-31 22:57:47:988 ==>> CB5252265F0D5A4780E77CCF6B4F244898E6A02039189D0A3AD3949421CC8F60C79194C4DEA8D6956B41E0805AEFE4FFC5A2B90D8DF05F9C2135036A6A6A652D4CA7510BED7ACE8EED190B3AF28A
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 22:57:48:228 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,59,,,46,34,,,41,41,,,41,40,,,41,1*7C

$GBGSV,4,2,13,3,,,41,39,,,40,60,,,40,23,,,38,1*42

$GBGSV,4,3,13,16,,,38,10,,,38,7,,,38,25,,,37,1*4A

$GBGSV,4,4,13,11,,,37,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1645.544,1645.544,52.608,2097152,2097152,2097152*46



2025-07-31 22:57:48:318 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:48:327 ==>> 定位已等待【4】秒.
2025-07-31 22:57:48:978 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 22:57:49:252 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,59,,,42,34,,,41,40,,,41,41,,,40,1*7C

$GBGSV,5,2,17,3,,,40,39,,,40,60,,,40,7,,,39,1*71

$GBGSV,5,3,17,25,,,39,23,,,38,16,,,38,11,,,38,1*76

$GBGSV,5,4,17,10,,,37,1,,,37,2,,,36,5,,,35,1*45

$GBGSV,5,5,17,4,,,35,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1599.757,1599.757,51.131,2097152,2097152,2097152*48



2025-07-31 22:57:49:327 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:49:337 ==>> 定位已等待【5】秒.
2025-07-31 22:57:50:268 ==>> $GBGGA,145754.115,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,42,59,,,41,34,,,41,41,,,41,1*73

$GBGSV,5,2,19,39,,,41,3,,,40,60,,,40,25,,,40,1*40

$GBGSV,5,3,19,7,,,39,11,,,39,23,,,38,16,,,38,1*49

$GBGSV,5,4,19,1,,,38,10,,,37,2,,,36,5,,,34,1*45

$GBGSV,5,5,19,4,,,34,32,,,34,9,,,39,1*78

$GBRMC,145754.115,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145754.115,0.000,1596.124,1596.124,51.039,2097152,2097152,2097152*5C



2025-07-31 22:57:50:328 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:50:337 ==>> 定位已等待【6】秒.
2025-07-31 22:57:50:659 ==>> $GBGGA,145754.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,59,,,41,34,,,41,41,,,41,1*79

$GBGSV,5,2,20,39,,,41,3,,,40,60,,,40,25,,,40,1*4A

$GBGSV,5,3,20,7,,,40,11,,,39,23,,,38,16,,,38,1*4D

$GBGSV,5,4,20,1,,,38,10,,,38,2,,,36,6,,,36,1*41

$GBGSV,5,5,20,5,,,34,4,,,34,32,,,34,9,,,32,1*4B

$GBRMC,145754.515,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145754.515,0.000,1581.630,1581.630,50.592,2097152,2097152,2097152*5D



2025-07-31 22:57:50:981 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 22:57:51:334 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:51:365 ==>> 定位已等待【7】秒.
2025-07-31 22:57:51:686 ==>> $GBGGA,145755.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,59,,,41,34,,,41,41,,,41,1*79

$GBGSV,6,2,23,39,,,41,3,,,41,60,,,40,25,,,40,1*4B

$GBGSV,6,3,23,7,,,40,11,,,39,23,,,38,16,,,38,1*4D

$GBGSV,6,4,23,1,,,38,10,,,38,33,,,37,2,,,36,1*76

$GBGSV,6,5,23,6,,,36,4,,,34,32,,,34,43,,,34,1*72

$GBGSV,6,6,23,9,,,34,44,,,34,5,,,33,1*7B

$GBRMC,145755.515,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145755.515,0.000,1568.203,1568.203,50.164,2097152,2097152,2097152*51



2025-07-31 22:57:52:335 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:52:344 ==>> 定位已等待【8】秒.
2025-07-31 22:57:52:672 ==>> $GBGGA,145756.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,43,59,,,41,34,,,41,41,,,41,1*78

$GBGSV,6,2,23,39,,,41,3,,,41,25,,,41,60,,,40,1*4A

$GBGSV,6,3,23,7,,,40,11,,,40,23,,,38,16,,,38,1*43

$GBGSV,6,4,23,1,,,38,10,,,38,33,,,37,43,,,37,1*42

$GBGSV,6,5,23,6,,,36,2,,,35,9,,,35,4,,,34,1*7F

$GBGSV,6,6,23,32,,,34,44,,,34,5,,,33,1*43

$GBRMC,145756.515,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145756.515,0.000,1579.017,1579.017,50.510,2097152,2097152,2097152*55



2025-07-31 22:57:53:132 ==>> [D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][PROT]CLEAN:0
[D][05:18:57][PROT]index:2 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:6
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:3
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:2, type:0xD302, priority:0
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:18:57][PROT]sending traceid [9999999999900007]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[134]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][0

2025-07-31 22:57:53:222 ==>> 5:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:57][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE6B19241317885C93B6C717AF20314B09840C1CDF33EAACA488DA9C705DD99B94BF3F495A15400480B28391A0B546E0862B10
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][COMM]read battery soc:255
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 22:57:53:342 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:53:348 ==>> 定位已等待【9】秒.
2025-07-31 22:57:53:678 ==>> $GBGGA,145757.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,59,,,42,34,,,42,41,,,41,1*79

$GBGSV,6,2,23,39,,,41,3,,,41,25,,,41,60,,,40,1*4A

$GBGSV,6,3,23,7,,,40,11,,,40,1,,,39,23,,,38,1*74

$GBGSV,6,4,23,16,,,38,10,,,38,43,,,38,33,,,37,1*7B

$GBGSV,6,5,23,6,,,36,2,,,36,9,,,35,32,,,35,1*48

$GBGSV,6,6,23,4,,,34,44,,,34,5,,,33,1*76

$GBRMC,145757.515,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145757.515,0.000,1588.026,1588.026,50.794,2097152,2097152,2097152*5A



2025-07-31 22:57:54:353 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:54:362 ==>> 定位已等待【10】秒.
2025-07-31 22:57:54:678 ==>> $GBGGA,145758.515,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,59,,,42,34,,,41,41,,,41,1*7A

$GBGSV,6,2,23,39,,,41,25,,,41,60,,,41,3,,,40,1*4A

$GBGSV,6,3,23,7,,,40,11,,,39,1,,,39,43,,,39,1*7D

$GBGSV,6,4,23,23,,,38,16,,,38,10,,,38,33,,,37,1*7D

$GBGSV,6,5,23,6,,,36,2,,,36,9,,,36,32,,,35,1*4B

$GBGSV,6,6,23,4,,,34,44,,,34,5,,,34,1*71

$GBRMC,145758.515,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145758.515,0.000,1589.819,1589.819,50.841,2097152,2097152,2097152*52



2025-07-31 22:57:54:998 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 22:57:55:366 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:55:377 ==>> 定位已等待【11】秒.
2025-07-31 22:57:55:677 ==>> $GBGGA,145759.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,59,,,42,34,,,42,41,,,41,1*7F

$GBGSV,6,2,24,39,,,41,25,,,41,3,,,41,60,,,40,1*4D

$GBGSV,6,3,24,7,,,40,11,,,40,43,,,39,16,,,39,1*42

$GBGSV,6,4,24,1,,,38,23,,,38,10,,,38,33,,,37,1*4C

$GBGSV,6,5,24,6,,,36,9,,,36,2,,,35,32,,,35,1*4F

$GBGSV,6,6,24,4,,,34,44,,,34,5,,,34,14,,,29,1*78

$GBRMC,145759.515,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145759.515,0.000,1577.163,1577.163,50.472,2097152,2097152,2097152*5F



2025-07-31 22:57:56:375 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:56:384 ==>> 定位已等待【12】秒.
2025-07-31 22:57:56:685 ==>> $GBGGA,145800.515,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,34,,,42,41,,,42,59,,,41,1*7F

$GBGSV,7,2,25,39,,,41,25,,,41,60,,,41,3,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,40,43,,,39,16,,,39,1*42

$GBGSV,7,4,25,1,,,38,23,,,38,10,,,38,33,,,37,1*4C

$GBGSV,7,5,25,6,,,37,9,,,36,2,,,36,24,,,36,1*49

$GBGSV,7,6,25,32,,,35,4,,,34,44,,,34,5,,,34,1*71

$GBGSV,7,7,25,14,,,30,1*77

$GBRMC,145800.515,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145800.515,0.000,1578.742,1578.742,50.513,2097152,2097152,2097152*5A



2025-07-31 22:57:57:021 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 22:57:57:390 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:57:400 ==>> 定位已等待【13】秒.
2025-07-31 22:57:57:699 ==>> $GBGGA,145801.515,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,34,,,41,41,,,41,59,,,41,1*7E

$GBGSV,7,2,25,39,,,41,25,,,40,60,,,40,3,,,40,1*4D

$GBGSV,7,3,25,7,,,40,11,,,39,43,,,39,16,,,38,1*4D

$GBGSV,7,4,25,1,,,38,23,,,38,10,,,37,33,,,37,1*43

$GBGSV,7,5,25,6,,,36,9,,,36,2,,,36,24,,,36,1*48

$GBGSV,7,6,25,32,,,35,5,,,34,4,,,33,44,,,33,1*71

$GBGSV,7,7,25,14,,,30,1*77

$GBRMC,145801.515,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145801.515,0.000,778.791,778.791,712.222,2097152,2097152,2097152*6F



2025-07-31 22:57:58:311 ==>> [D][05:19:02][PROT]CLEAN,SEND:2
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:2
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[

2025-07-31 22:57:58:341 ==>> 17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]<<< 
ERROR



2025-07-31 22:57:58:401 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:58:411 ==>> 定位已等待【14】秒.
2025-07-31 22:57:58:709 ==>> $GBGGA,145802.515,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,7,2,25,41,,,41,7,,,40,60,,,40,3,,,40,1*72

$GBGSV,7,3,25,25,,,40,11,,,39,43,,,39,16,,,38,1*7D

$GBGSV,7,4,25,1,,,38,23,,,38,10,,,37,24,,,37,1*45

$GBGSV,7,5,25,33,,,37,2,,,36,9,,,36,6,,,36,1*4F

$GBGSV,7,6,25,32,,,35,5,,,34,4,,,34,44,,,33,1*76

$GBGSV,7,7,25,14,,,30,1*77

$GBRMC,145802.515,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145802.515,0.000,780.443,780.443,713.732,2097152,2097152,2097152*69



2025-07-31 22:57:59:017 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 22:57:59:402 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:57:59:423 ==>> 定位已等待【15】秒.
2025-07-31 22:57:59:695 ==>> $GBGGA,145803.515,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,42,60,,,41,3,,,41,1*4A

$GBGSV,7,2,25,39,,,41,34,,,41,25,,,41,41,,,41,1*7B

$GBGSV,7,3,25,7,,,40,11,,,40,43,,,40,16,,,39,1*4C

$GBGSV,7,4,25,10,,,38,1,,,38,23,,,38,24,,,37,1*4A

$GBGSV,7,5,25,33,,,37,2,,,36,9,,,36,6,,,36,1*4F

$GBGSV,7,6,25,32,,,35,5,,,34,4,,,34,44,,,33,1*76

$GBGSV,7,7,25,14,,,30,1*77

$GBRMC,145803.515,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145803.515,0.000,787.073,787.073,719.796,2097152,2097152,2097152*6C



2025-07-31 22:58:00:406 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:58:00:415 ==>> 定位已等待【16】秒.
2025-07-31 22:58:00:701 ==>> $GBGGA,145804.515,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,41,3,,,41,39,,,41,1*4F

$GBGSV,7,2,25,59,,,41,34,,,41,41,,,41,7,,,40,1*4C

$GBGSV,7,3,25,25,,,40,11,,,40,16,,,39,43,,,39,1*72

$GBGSV,7,4,25,1,,,38,23,,,38,10,,,37,24,,,37,1*45

$GBGSV,7,5,25,33,,,37,2,,,36,9,,,36,6,,,36,1*4F

$GBGSV,7,6,25,32,,,35,44,,,34,4,,,34,5,,,33,1*76

$GBGSV,7,7,25,14,,,30,1*77

$GBRMC,145804.515,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145804.515,0.000,783.758,783.758,716.764,2097152,2097152,2097152*69



2025-07-31 22:58:01:017 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 22:58:01:419 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:58:01:428 ==>> 定位已等待【17】秒.
2025-07-31 22:58:01:697 ==>> $GBGGA,145805.515,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,59,,,42,34,,,42,60,,,41,1*7E

$GBGSV,7,2,26,3,,,41,39,,,41,25,,,41,41,,,41,1*4C

$GBGSV,7,3,26,7,,,40,11,,,40,43,,,40,16,,,39,1*4F

$GBGSV,7,4,26,10,,,38,1,,,38,23,,,38,24,,,37,1*49

$GBGSV,7,5,26,33,,,37,2,,,36,9,,,36,6,,,36,1*4C

$GBGSV,7,6,26,12,,,36,32,,,35,5,,,34,44,,,34,1*47

$GBGSV,7,7,26,4,,,34,14,,,31,1*46

$GBRMC,145805.515,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,145805.515,0.000,787.830,787.830,720.487,2097152,2097152,2097152*63



2025-07-31 22:58:02:434 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 22:58:02:455 ==>> 定位已等待【18】秒.
2025-07-31 22:58:03:398 ==>> $GBGGA,145802.517,2301.2564531,N,11421.9432236,E,1,13,0.81,78.243,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,41,16,60,13,39,10,54,213,37,59,52,129,41,1*76

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,42,1*4D

$GBGSV,7,4,26,2,45,237,36,60,41,239,41,41,38,253,41,43,37,166,40,1*46

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,34,1*45

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBRMC,145802.517,A,2301.2564531,N,11421.9432236,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:07][GNSS]HD8040 GPS
[D][05:19:07][GNSS]GPS diff_sec 124018735, report 0x42 frame
$GBGST,145802.517,1.858,0.209,0.203,0.290,2.608,3.117,7.334*72

[D][05:19:07][COMM]Main Task receive event:131
[D][05:19:07][COMM]index:0,power_mode:0xFF
[D][05:19:07][COMM]index:1,sound_mode:0xFF
[D][05:19:07][COMM]index:2,gsensor_mode:0xFF
[D][05:19:07][COMM]index:3,report_freq_mode:0xFF
[D][05:19:07][COMM]index:4,report_period:0xFF
[D][05:19:07][COMM]inde

2025-07-31 22:58:03:443 ==>> 符合定位需求的卫星数量:【22】
2025-07-31 22:58:03:451 ==>> 
北斗星号:【40】,信号值:【42】
北斗星号:【7】,信号值:【40】
北斗星号:【6】,信号值:【36】
北斗星号:【39】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【10】,信号值:【37】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【9】,信号值:【36】
北斗星号:【1】,信号值:【38】
北斗星号:【34】,信号值:【42】
北斗星号:【2】,信号值:【36】
北斗星号:【60】,信号值:【41】
北斗星号:【41】,信号值:【41】
北斗星号:【43】,信号值:【40】
北斗星号:【23】,信号值:【38】
北斗星号:【33】,信号值:【37】
北斗星号:【24】,信号值:【37】
北斗星号:【32】,信号值:【35】
北斗星号:【11】,信号值:【40】
北斗星号:【12】,信号值:【36】

2025-07-31 22:58:03:477 ==>> 检测【CSQ强度】
2025-07-31 22:58:03:504 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:58:03:513 ==>> x:5,normal_reset_mode:0xFF
[D][05:19:07][COMM]index:6,normal_reset_period:0xFF
[D][05:19:07][COMM]index:7,spock_over_speed:0xFF
[D][05:19:07][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:07][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:07][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:07][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:07][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:07][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:07][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:07][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:07][COMM]index:16,imu_config_params:0xFF
[D][05:19:07][COMM]index:17,long_connect_params:0xFF
[D][05:19:07][COMM]index:18,detain_mark:0xFF
[D][05:19:07][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:07][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:07][COMM]index:21,mc_mode:0xFF
[D][05:19:07][COMM]index:22,S_mode:0xFF
[D][05:19:07][COMM]index:23,overweight:0xFF
[D][05:19:07][COMM]index:24,standstill_mode:0xFF
[D][05:19:07][COMM]index:25,night_mode:0xFF
[D][05:19:07][COMM]index:26,experiment1:0xFF
[D][05:19:07][COMM]index:27,experiment2:0xFF
[D][05:19:07][COMM]index:28,experiment3:0xFF
[D][05:19:07

2025-07-31 22:58:03:608 ==>> ][COMM]index:29,experiment4:0xFF
[D][05:19:07][COMM]index:30,night_mode_start:0xFF
[D][05:19:07][COMM]index:31,night_mode_end:0xFF
[D][05:19:07][COMM]index:33,park_report_minutes:0xFF
[D][05:19:07][COMM]index:34,park_report_mode:0xFF
[D][05:19:07][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:07][COMM]index:38,charge_battery_para: FF
[D][05:19:07][COMM]index:39,multirider_mode:0xFF
[D][05:19:07][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:07][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:07][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:07][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:07][COMM]index:44,riding_duration_config:0xFF
[D][05:19:07][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:07][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:07][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:07][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:07][COMM]index:49,mc_load_startup:0xFF
[D][05:19:07][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:07][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:07][COMM]index:52,traffic_mode:0xFF
[D][05:19:07][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:07][COMM]index:54,traffic_security_model_cycle:0

2025-07-31 22:58:03:713 ==>> xFF
[D][05:19:07][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:07][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:07][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:07][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:07][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:07][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:07][COMM]index:63,experiment5:0xFF
[D][05:19:07][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:07][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:07][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:07][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:07][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:07][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:07][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:07][COMM]index:72,experiment6:0xFF
[D][05:19:07][COMM]index:73,experiment7:0xFF
[D][05:19:07][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:07][COMM]index:75,zero_value_from_server:-1
[D][05:19:07][COMM]index:76,multirider_threshold:255
[D][05:19:07][COMM]index:77,experiment8:255
[D][05:19:07][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:07][COMM]index:79,temp_park_

2025-07-31 22:58:03:818 ==>> tail_light_twinkle_duration:255
[D][05:19:07][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:07][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:07][COMM]index:83,loc_report_interval:255
[D][05:19:07][COMM]index:84,multirider_threshold_p2:255
[D][05:19:07][COMM]index:85,multirider_strategy:255
[D][05:19:07][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:07][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:07][COMM]index:90,weight_param:0xFF
[D][05:19:07][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:07][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:07][COMM]index:95,current_limit:0xFF
[D][05:19:07][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:07][COMM]index:100,location_mode:0xFF

[W][05:19:07][PROT]remove success[1629955147],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:07][COMM]Main Task receive event:131 finished processing
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:07][COMM]read battery soc:255


2025-07-31 22:58:03:923 ==>> 
$GBGGA,145803.017,2301.2569365,N,11421.9434047,E,1,13,0.81,78.334,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,40,16,60,13,39,10,54,213,37,59,52,129,41,1*77

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,42,1*4D

$GBGSV,7,4,26,2,45,237,36,60,41,239,40,41,38,253,41,43,37,166,40,1*47

$GBGSV,7,5,26,4,32,111,33,23,27,305,38,33,24,195,37,5,21,256,34,1*72

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,33,1*42

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,2,1,06,40,70,177,37,39,63,42,40,25,50,6,39,34,46,92,36,5*79

$GBGSV,2,2,06,41,38,253,41,43,37,166,38,5*72

$GBRMC,145803.017,A,2301.2569365,N,11421.9434047,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145803.017,2.436,0.861,0.820,1.135,2.271,2.604,5.456*7C



2025-07-31 22:58:04:013 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:58:04:332 ==>> $GBGGA,145804.000,2301.2572110,N,11421.9434880,E,1,13,0.81,78.755,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,40,16,60,13,38,10,54,213,37,59,52,129,42,1*75

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,41,1*4E

$GBGSV,7,4,26,2,45,237,36,60,41,239,40,41,38,253,41,43,37,166,39,1*49

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,31,24,17,268,37,32,14,308,35,44,11,45,33,1*43

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,39,39,63,42,41,25,50,6,40,34,46,92,38,5*70

$GBGSV,3,2,10,41,38,253,41,43,37,166,38,23,27,305,38,33,24,195,37,5*72

$GBGSV,3,3,10,32,14,308,36,44,11,45,33,5*48

$GBRMC,145804.000,A,2301.2572110,N,11421.9434880,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,145804.000,2.170,0.242,0.234,0.337,1.887,2.091,4.357*72



2025-07-31 22:58:05:029 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 22:58:05:332 ==>> $GBGGA,145805.000,2301.2573659,N,11421.9434947,E,1,13,0.81,79.258,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,40,16,60,13,38,10,54,213,38,59,52,129,41,1*79

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,41,1*4E

$GBGSV,7,4,26,2,45,237,36,60,41,239,40,41,38,253,41,43,37,166,39,1*49

$GBGSV,7,5,26,4,32,111,33,23,27,305,38,33,24,195,37,5,21,256,34,1*72

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,33,1*42

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,40,39,63,42,41,25,50,6,41,34,46,92,40,5*70

$GBGSV,3,2,10,41,38,253,41,43,37,166,38,23,27,305,39,33,24,195,37,5*73

$GBGSV,3,3,10,32,14,308,36,44,11,45,33,5*48

$GBRMC,145805.000,A,2301.2573659,N,11421.9434947,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145805.000,2.095,0.168,0.164,0.239,1.741,1.888,3.801*72



2025-07-31 22:58:05:513 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:58:06:061 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 22:58:06:334 ==>> $GBGGA,145806.000,2301.2574708,N,11421.9435962,E,1,13,0.81,79.143,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,40,16,60,13,39,10,54,213,38,59,52,129,42,1*7B

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,42,1*4D

$GBGSV,7,4,26,2,45,237,36,60,41,239,41,41,38,253,41,43,37,166,39,1*48

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,33,1*42

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,41,39,63,42,41,25,50,6,41,34,46,92,40,5*71

$GBGSV,3,2,10,41,38,253,41,43,37,166,39,23,27,305,39,33,24,195,37,5*72

$GBGSV,3,3,10,32,14,308,36,44,11,45,33,5*48

$GBRMC,145806.000,A,2301.2574708,N,11421.9435962,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,145806.000,2.246,0.177,0.173,0.249,1.784,1.895,3.543*7C



2025-07-31 22:58:07:029 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 22:58:07:334 ==>> $GBGGA,145807.000,2301.2575561,N,11421.9435991,E,1,13,0.81,79.291,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,37,39,63,42,41,1*78

$GBGSV,7,2,26,3,60,190,41,16,60,13,39,10,54,213,38,59,52,129,42,1*7A

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,38,34,46,92,41,1*4E

$GBGSV,7,4,26,2,45,237,36,60,41,239,41,41,38,253,41,43,37,166,39,1*48

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,33,1*72

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,34,1*45

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,41,39,63,42,42,25,50,6,41,34,46,92,41,5*73

$GBGSV,3,2,10,41,38,253,41,43,37,166,39,23,27,305,39,33,24,195,37,5*72

$GBGSV,3,3,10,32,14,308,36,44,11,45,32,5*49

$GBRMC,145807.000,A,2301.2575561,N,11421.9435991,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,145807.000,2.219,0.200,0.195,0.279,1.734,1.825,3.312*7D



2025-07-31 22:58:07:563 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 22:58:07:593 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:58:08:458 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:12][CAT1]exec over: func id: 15, ret: -93
[D][05:19:12][CAT1]sub id: 15, ret: -93

[D][05:19:12][SAL ]Cellular task submsg id[68]
[D][05:19:12][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:12][SAL ]socket send fail. id[4]
[D][05:19:12][SAL ]select read evt socket_id[4], p_data[0] len[0]
$GBGGA,145808.000,2301.2576073,N,11421.9435739,E,1,13,0.81,79.566,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,37,39,63,42,41,1*78

$GBGSV,7,2,26,3,60,190,41,16,60,13,39,10,54,213,38,59,52,129,42,1*7A

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,39,34,46,92,42,1*4C

$GBGSV,7,4,26,2,45,237,36,60,41,239,40,41,38,253,41,43,37,166,39,1*49

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,30,24,17,268,38,32,14,308,35,44,11,45,34,1*4A

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,41,39,63,42,42,25,50,6,41,34,46,92,41,5*73

$GBGSV,3,2,10,41,38,253,41,43,37,166,39,23,27,305,39,33,24,195,37,5*72

$GBGSV,3,3,10,32,14,308,36,44,11,45,32,5*49

$GBRMC,145808.000,A

2025-07-31 22:58:08:563 ==>> ,2301.2576073,N,11421.9435739,E,0.003,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,145808.000,2.071,0.199,0.193,0.277,1.614,1.693,3.075*77

[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:12][M2M ]m2m select fd[4]
[D][05:19:12][M2M ]socket[4] Link is disconnected
[D][05:19:12][M2M ]tcpclient close[4]
[D][05:19:12][SAL ]socket[4] has closed
[D][05:19:12][PROT]protocol read data ok
[E][05:19:12][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:12][PROT]M2M Send Fail [1629955152]
[D][05:19:12][PROT]CLEAN,SEND:2
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:12][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 21
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:12][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 21
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:12][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 21
[D][05:19:12][

2025-07-31 22:58:08:593 ==>> CAT1]gsm read msg sub id: 10
[D][05:19:12][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:12][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:12][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 22:58:08:698 ==>>                                                                                                           

2025-07-31 22:58:08:743 ==>> 【CSQ强度】通过,【23】符合目标值【18】至【31】要求!
2025-07-31 22:58:08:752 ==>> 检测【关闭GSM联网】
2025-07-31 22:58:08:765 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:58:08:788 ==>>                                                              8]
[D][05:19:13][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:13][M2M ]m2m gsm shut done, ret[0]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:13][SAL ]open socket ind id[4], rst[0]
[D][05:19:13][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:13][SAL ]Cellular task submsg id[8]
[D][05:19:13][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:13][CAT1]gsm read msg sub id: 8
[D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:13][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 22:58:08:878 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:13][COMM]GSM test
[D][05:19:13][COMM]GSM test disable


2025-07-31 22:58:09:035 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:58:09:042 ==>> 检测【4G联网测试】
2025-07-31 22:58:09:063 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:58:09:074 ==>> [D][05:19:13][CAT1]pdpdeact urc len[22]
[D][05:19:13][COMM]read battery soc:255


2025-07-31 22:58:09:804 ==>> $GBGGA,145809.000,2301.2576166,N,11421.9435843,E,1,13,0.81,79.531,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,37,39,63,42,41,1*78

$GBGSV,7,2,26,3,60,190,41,16,60,13,39,10,54,213,38,59,52,129,42,1*7A

$GBGSV,7,3,26,25,50,6,41,9,49,333,36,1,48,125,39,34,46,92,42,1*4C

$GBGSV,7,4,26,2,45,237,36,60,41,239,41,41,38,253,41,43,37,166,39,1*48

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,30,24,17,268,38,32,14,308,35,44,11,45,34,1*4A

$GBGSV,7,7,26,11,,,40,12,,,36,1*70

$GBGSV,3,1,10,40,70,177,41,39,63,42,42,25,50,6,41,34,46,92,41,5*73

$GBGSV,3,2,10,41,38,253,41,43,37,166,38,23,27,305,39,33,24,195,37,5*73

$GBGSV,3,3,10,32,14,308,36,44,11,45,32,5*49

$GBRMC,145809.000,A,2301.2576166,N,11421.9435843,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,145809.000,1.942,0.221,0.214,0.305,1.511,1.580,2.885*75

[D][05:19:13][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:13][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:13][COMM]Main Task receive event:14
[D][05:19:13][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955153, 

2025-07-31 22:58:09:909 ==>> allstateRepSeconds = 0
[D][05:19:13][COMM]index:0,power_mode:0xFF
[D][05:19:13][COMM]index:1,sound_mode:0xFF
[D][05:19:13][COMM]index:2,gsensor_mode:0xFF
[D][05:19:13][COMM]index:3,report_freq_mode:0xFF
[D][05:19:13][COMM]index:4,report_period:0xFF
[D][05:19:13][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:13][COMM]index:6,normal_reset_period:0xFF
[D][05:19:13][COMM]index:7,spock_over_speed:0xFF
[D][05:19:13][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:13][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:13][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:13][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:13][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:13][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:13][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:13][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:13][COMM]index:16,imu_config_params:0xFF
[D][05:19:13][COMM]index:17,long_connect_params:0xFF
[D][05:19:13][COMM]index:18,detain_mark:0xFF
[D][05:19:13][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:13][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:13][COMM]index:21,mc_mode:0xFF
[D][05:19:13][COMM]index:22,S_mode:0xFF
[D][05:19:13][COMM]index:23,

2025-07-31 22:58:10:015 ==>> overweight:0xFF
[D][05:19:13][COMM]index:24,standstill_mode:0xFF
[D][05:19:13][COMM]index:25,night_mode:0xFF
[D][05:19:13][COMM]index:26,experiment1:0xFF
[D][05:19:13][COMM]index:27,experiment2:0xFF
[D][05:19:13][COMM]index:28,experiment3:0xFF
[D][05:19:13][COMM]index:29,experiment4:0xFF
[D][05:19:13][COMM]index:30,night_mode_start:0xFF
[D][05:19:13][COMM]index:31,night_mode_end:0xFF
[D][05:19:13][COMM]index:33,park_report_minutes:0xFF
[D][05:19:13][COMM]index:34,park_report_mode:0xFF
[D][05:19:13][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:13][COMM]index:38,charge_battery_para: FF
[D][05:19:13][COMM]index:39,multirider_mode:0xFF
[D][05:19:13][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:13][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:13][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:13][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:13][COMM]index:44,riding_duration_config:0xFF
[D][05:19:13][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:13][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:13][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:13][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:13][COMM]i

2025-07-31 22:58:10:120 ==>> ndex:49,mc_load_startup:0xFF
[D][05:19:13][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:13][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:13][COMM]index:52,traffic_mode:0xFF
[D][05:19:13][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:13][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:13][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:13][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:13][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:13][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:13][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:13][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:13][COMM]index:63,experiment5:0xFF
[D][05:19:13][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:13][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:13][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:13][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:13][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:13][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:13][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:13][COMM]index:72,experiment6:0xFF
[D][05:19:13][COMM]index:73,experiment7:0xFF
[D][05:19:13][COMM]index:74,load_messurement_cfg:0xff
[D]

2025-07-31 22:58:10:226 ==>> [05:19:13][COMM]index:75,zero_value_from_server:-1
[D][05:19:13][COMM]index:76,multirider_threshold:255
[D][05:19:13][COMM]index:77,experiment8:255
[D][05:19:13][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:13][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:13][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:13][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:13][COMM]index:83,loc_report_interval:255
[D][05:19:13][COMM]index:84,multirider_threshold_p2:255
[D][05:19:13][COMM]index:85,multirider_strategy:255
[D][05:19:13][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:13][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:13][COMM]index:90,weight_param:0xFF
[D][05:19:13][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:13][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:13][COMM]index:95,current_limit:0xFF
[D][05:19:13][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:13][COMM]index:100,location_mode:0xFF

[W][05:19:13][PROT]remove success[1629955153],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[2],type[4205],priority[0],

2025-07-31 22:58:10:257 ==>> index[0],used[1]
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 22:58:10:362 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 22:58:10:452 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:58:10:713 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 22:58:10:817 ==>>                                                                                                                                                                                                                                                                         [CAT1]Send Data To Server[294][294] ... ->:
0093B986113311331133113311331B88B1790748AE6A4D5B586C3E30A2F0D52683666C666A03435CB5D6C246710303C383797B8861F62DC69A95A2AD5EDD230684A5030B81CB50F0AB9F6B7D0FDA53051A4FC912B2C2D90419A66A32D06682811FA35F7B2893D636ED34C7FD7B295A19D4712BA2699DCB53F7D77750BC6C903F3CD587C418D421DF36B275E32AB0E7E7E39B35
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:15][PROT]remove success[1629955155],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:15][PROT]add success [1629955155],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:15][COMM]------>period, report file manifest
[D][05:19:15][COMM]Main Task receive event:14 finished processing
[D][05:19:15][CAT1]<<< 
SEND OK

[D][05:19:15][CAT1]exec over: func id: 15, ret: 11
[D][05:19:15][CAT1]sub id: 15, ret: 11

[D][05:19:15][SAL ]Cellular task submsg id[68]
[D][05:19:15][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]


2025-07-31 22:58:10:893 ==>> [D][05:19:15][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:15][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:15][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:15][CAT1]gsm read msg sub id: 21
[D][05:19:15][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:15][M2M ]g_m2m_is_idle become true
[D][05:19:15][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:15][PROT]M2M Send ok [1629955155]
[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]cell info report total[0]
[D][05:19:15][CAT1]exec over: func id: 21, ret: 6


2025-07-31 22:58:11:028 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 22:58:11:083 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:58:11:092 ==>> 检测【关闭GPS】
2025-07-31 22:58:11:107 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:58:11:495 ==>> $GBGGA,145811.000,2301.2576171,N,11421.9435891,E,1,13,0.81,79.619,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,16,10,25,34,41,43,23,33,32,1.45,0.81,1.21,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.45,0.81,1.21,4*03

$GBGSV,7,1,26,40,70,177,42,7,66,205,40,6,64,88,36,39,63,42,41,1*79

$GBGSV,7,2,26,3,60,190,41,16,60,13,38,10,54,213,38,59,52,129,41,1*78

$GBGSV,7,3,26,25,50,6,40,9,49,333,36,1,48,125,38,34,46,92,41,1*4F

$GBGSV,7,4,26,2,45,237,36,60,41,239,40,41,38,253,41,43,37,166,39,1*49

$GBGSV,7,5,26,4,32,111,34,23,27,305,38,33,24,195,37,5,21,256,34,1*75

$GBGSV,7,6,26,14,20,317,30,24,17,268,37,32,14,308,35,44,11,45,34,1*45

$GBGSV,7,7,26,11,,,39,12,,,36,1*7E

$GBGSV,3,1,10,40,70,177,41,39,63,42,41,25,50,6,41,34,46,92,41,5*70

$GBGSV,3,2,10,41,38,253,41,43,37,166,38,23,27,305,39,33,24,195,37,5*73

$GBGSV,3,3,10,32,14,308,36,44,11,45,32,5*49

$GBRMC,145811.000,A,2301.2576171,N,11421.9435891,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,145811.000,1.980,0.168,0.164,0.239,1.514,1.566,2.722*79

[W][05:19:15][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:15][GNSS]stop locating
[D][05:19:15][GNSS]stop event:8
[D][05:19:15][GNSS]GPS stop. ret=0
[D][05:19:15

2025-07-31 22:58:11:585 ==>> ][GNSS]all continue location stop
[W][05:19:15][GNSS]stop locating
[D][05:19:15][GNSS]all sing location stop
[D][05:19:15][CAT1]gsm read msg sub id: 24
[D][05:19:15][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]exec over: func id: 24, ret: 6
[D][05:19:16][CAT1]sub id: 24, ret: 6

                                                                                                                                           

2025-07-31 22:58:11:619 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:58:11:632 ==>> 检测【清空消息队列2】
2025-07-31 22:58:11:654 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:58:11:797 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:16][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:58:11:889 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:58:11:897 ==>> 检测【轮动检测】
2025-07-31 22:58:11:904 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:12:009 ==>> 3A A3 01 00 A3 


2025-07-31 22:58:12:115 ==>> OFF_OUT1
OVER 150


2025-07-31 22:58:12:390 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:12:511 ==>> 3A A3 01 01 A3 


2025-07-31 22:58:12:616 ==>> ON_OUT1
OVER 150


2025-07-31 22:58:13:054 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 22:58:15:064 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 22:58:15:486 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:15:607 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:58:15:990 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:16:112 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:58:17:055 ==>> [D][05:19:21][COMM]read battery soc:255


2025-07-31 22:58:19:071 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 22:58:19:116 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:19:221 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:58:19:630 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:19:707 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:58:21:070 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 22:58:22:734 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:22:811 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:58:23:068 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 22:58:23:235 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:23:312 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:58:24:769 ==>> [D][05:19:29][COMM]msg 0226 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:10001

2025-07-31 22:58:24:874 ==>> 9. period:10000
[D][05:19:29][COMM]msg 02E3 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100023. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100024. period:10000
[D][05:19:29][COMM]

2025-07-31 22:58:24:979 ==>> bat msg 02AD loss. last_tick:0. cur_tick:100024. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100024. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100025. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100025. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100026. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100026. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100026. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100027. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100027
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100027
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100028


2025-07-31 22:58:25:069 ==>> [D][05:19:29][COMM]read battery soc:255


2025-07-31 22:58:26:337 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:26:410 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:58:26:848 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:26:908 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:58:27:073 ==>> [D][05:19:31][COMM]read battery soc:255


2025-07-31 22:58:29:094 ==>> [D][05:19:33][COMM]read battery soc:255


2025-07-31 22:58:29:958 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:58:30:019 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:58:30:465 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:58:30:510 ==>> 3A A3 01 01 A3 


2025-07-31 22:58:30:615 ==>> ON_OUT1
OVER 150
[D][05:19:35][GNSS]handler GSMGet Base timeout


2025-07-31 22:58:31:097 ==>> [D][05:19:35][COMM]read battery soc:255


2025-07-31 22:58:33:091 ==>> [D][05:19:37][COMM]read battery soc:255


2025-07-31 22:58:33:559 ==>> 未匹配到【轮动检测】数据,请核对检查!
2025-07-31 22:58:33:573 ==>> #################### 【测试结束】 ####################
2025-07-31 22:58:33:698 ==>> 关闭5V供电
2025-07-31 22:58:33:710 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:58:33:822 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:58:34:703 ==>> 关闭5V供电成功
2025-07-31 22:58:34:716 ==>> 关闭33V供电
2025-07-31 22:58:34:745 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:58:34:812 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:58:35:006 ==>> [D][05:19:39][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:39][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:39][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 22:58:35:710 ==>> 关闭33V供电成功
2025-07-31 22:58:35:721 ==>> 关闭3.7V供电
2025-07-31 22:58:35:748 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:58:35:817 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:58:36:268 ==>>  

2025-07-31 22:58:36:719 ==>> 关闭3.7V供电成功
