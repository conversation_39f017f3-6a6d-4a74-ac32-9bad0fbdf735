2025-07-31 22:32:23:121 ==>> MES查站成功:
查站序号:P510001005312871验证通过
2025-07-31 22:32:23:131 ==>> 扫码结果:P510001005312871
2025-07-31 22:32:23:133 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:32:23:135 ==>> 测试参数版本:2024.10.11
2025-07-31 22:32:23:136 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:32:23:138 ==>> 检测【打开透传】
2025-07-31 22:32:23:139 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:32:23:196 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:32:23:492 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:32:23:498 ==>> 检测【检测接地电压】
2025-07-31 22:32:23:500 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:32:23:611 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:32:23:780 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:32:23:784 ==>> 检测【打开小电池】
2025-07-31 22:32:23:787 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:32:23:904 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:32:24:062 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:32:24:064 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:32:24:066 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:32:24:210 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:32:24:353 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:32:24:358 ==>> 检测【等待设备启动】
2025-07-31 22:32:24:362 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:32:24:671 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:32:24:869 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 22:32:25:375 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:32:25:390 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:32:25:570 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:32:26:295 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:32:26:430 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:32:26:686 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:32:27:157 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:32:27:220 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:32:27:223 ==>> 检测【产品通信】
2025-07-31 22:32:27:225 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:32:27:884 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:32:28:065 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:32:28:248 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:32:29:283 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:32:29:373 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFF

2025-07-31 22:32:29:478 ==>> FF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]netcore_ver 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA_BOO

2025-07-31 22:32:29:561 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:32:29:564 ==>> 检测【初始化完成检测】
2025-07-31 22:32:29:567 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:32:29:587 ==>> T_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = F6BA45689FD5
[D][05:17:49][FCTY]Bat         = 3764 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 4
[D][05:17:49][FCTY]Acckey1 vol = 5493 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]CAT1_GNSS_VERSION =
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][COMM][CHG]ext_48v_vol:0, disabl

2025-07-31 22:32:29:688 ==>> e charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][FCTY]Bat1         = 3710 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi scan.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][CAT1]gsm read msg sub id: 1
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:50][COM

2025-07-31 22:32:29:793 ==>> M]msg 0220 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0221 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0224 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0260 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1015. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1016. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1017. period:100
[D][05:17:50][COMM]msg 02E2 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1018. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick:0. cur_tick:1019. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:1019. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1019. period:100. j,i:3

2025-07-31 22:32:29:868 ==>>  56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1020. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1020. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1021. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1021
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1021
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1022
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:32:30:198 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 22:32:30:303 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 22:32:30:408 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 22:32:30:513 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                       :17:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:17:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_thresh

2025-07-31 22:32:30:588 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:32:30:619 ==>> old_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1626 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_devic

2025-07-31 22:32:30:663 ==>> e_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 22:32:30:768 ==>>                                                                                                                          

2025-07-31 22:32:30:858 ==>>                                      [W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 22:32:30:863 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:32:30:866 ==>> 检测【关闭大灯控制1】
2025-07-31 22:32:30:869 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:32:31:070 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:32:31:134 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:32:31:137 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:32:31:139 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:32:31:295 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:32:31:403 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:32:31:406 ==>> 检测【关闭仪表供电】
2025-07-31 22:32:31:409 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:32:31:595 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:32:31:677 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:32:31:680 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:32:31:683 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:32:31:700 ==>> [D][05:17:52][COMM]3649 imu

2025-07-31 22:32:31:730 ==>>  init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:32:31:865 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:32:31:969 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:32:31:972 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:32:31:974 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:32:32:187 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:32:32:261 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:32:32:264 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:32:32:265 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:32:491 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:32:32:551 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:32:32:553 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:32:32:555 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:32:32:596 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:32:32:701 ==>> [D][05:17:53][COMM]VBUS Insert EXTI C

2025-07-31 22:32:32:746 ==>> ome sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4661 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:32:32:834 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:32:32:837 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:32:32:838 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:32:32:897 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:32:33:123 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:32:33:127 ==>> 该项需要延时执行
2025-07-31 22:32:33:263 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5018. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D

2025-07-31 22:32:33:293 ==>> ][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019


2025-07-31 22:32:33:751 ==>> [D][05:17:54][COMM]5672 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:32:34:268 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:32:34:831 ==>>                                                                  ][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_slee

2025-07-31 22:32:34:936 ==>> p_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:

2025-07-31 22:32:35:041 ==>> 55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0]

2025-07-31 22:32:35:131 ==>> ,index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6683 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 22:32:35:763 ==>> [D][05:17:56][COMM]7694 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:32:36:699 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:32:36:789 ==>> [D][05:17:57][COMM]8706 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:32:37:124 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:32:37:128 ==>> 检测【33V输入电压ADC】
2025-07-31 22:32:37:131 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:37:424 ==>> [D][05:17:58][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:17:58][COMM]adc read out 24v adc:1305  volt:33007 mv
[D][05:17:58][COMM]adc read left brake adc:11  volt:14 mv
[D][05:17:58][COMM]adc read right brake adc:12  volt:15 mv
[D][05:17:58][COMM]adc read throttle adc:4  volt:5 mv
[D][05:17:58][COMM]adc read battery ts volt:13 mv
[D][05:17:58][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:58][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:32:37:660 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:32:37:663 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:32:37:666 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:32:37:806 ==>> [D][05:17:58][COMM]9717 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1646mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2757mV
Get AD_V6 1992mV
Get AD_V7 1084mV
OVER 150


2025-07-31 22:32:37:939 ==>> 【TP7_VCC3V3(ADV2)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:37:942 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:32:37:974 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:37:978 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:32:37:981 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 22:32:38:005 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:32:38:008 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:32:38:022 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:32:38:024 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:32:38:045 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:32:38:047 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:32:38:128 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10006
1A A1 00 00 FC 
Get AD_V2 1642mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1992mV
Get AD_V7 1084mV
OVER 150


2025-07-31 22:32:38:331 ==>> 【TP7_VCC3V3(ADV2)】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:38:334 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:32:38:349 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:38:353 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:32:38:356 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 22:32:38:369 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:32:38:373 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:32:38:399 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:32:38:402 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:32:38:434 ==>> 【TP1_VCC12V(ADV7)】通过,【1084mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:32:38:437 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:32:38:521 ==>> 1A A1 00 00 FC 
Get AD_V2 1642mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1988mV
Get AD_V7 1085mV
OVER 150


2025-07-31 22:32:38:711 ==>> 【TP7_VCC3V3(ADV2)】通过,【1642mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:38:715 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:32:38:730 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:38:735 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:32:38:751 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 22:32:38:758 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:32:38:765 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:32:38:784 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:32:38:787 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:32:38:808 ==>> 【TP1_VCC12V(ADV7)】通过,【1085mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:32:38:811 ==>> 检测【打开WIFI(1)】
2025-07-31 22:32:38:814 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:32:39:206 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10729 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished p

2025-07-31 22:32:39:311 ==>> rocessing
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT

2025-07-31 22:32:39:346 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:32:39:349 ==>> 检测【清空消息队列(1)】
2025-07-31 22:32:39:352 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:32:39:357 ==>> 1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 22:32:39:461 ==>> [W][05:18:00][COMM]>>>>>Input comma

2025-07-31 22:32:39:491 ==>> nd = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:32:39:629 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:32:39:633 ==>> 检测【打开GPS(1)】
2025-07-31 22:32:39:635 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:32:39:824 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:32:39:910 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:32:39:913 ==>> 检测【打开GSM联网】
2025-07-31 22:32:39:916 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:32:40:095 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 22:32:40:189 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:32:40:192 ==>> 检测【打开仪表供电1】
2025-07-31 22:32:40:194 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:32:40:395 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:32:40:458 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:32:40:462 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:32:40:464 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:32:40:744 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 22:32:40:923 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 22:32:40:989 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:32:40:992 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:32:40:995 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:32:41:532 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][COMM]arm_hub read adc[3],val[33525]
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222088006947

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071541610

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 22:32:41:778 ==>> 【读取主控ADC采集的仪表电压】通过,【33525mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:32:41:784 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:32:41:795 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:32:42:007 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:32:42:068 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:32:42:072 ==>> 检测【AD_V20电压】
2025-07-31 22:32:42:075 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:32:42:172 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:32:42:397 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:32:42:487 ==>> 本次取值间隔时间:312ms
2025-07-31 22:32:42:610 ==>> 本次取值间隔时间:116ms
2025-07-31 22:32:42:806 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][COMM]14741 imu init OK


2025-07-31 22:32:42:851 ==>> 本次取值间隔时间:232ms
2025-07-31 22:32:42:896 ==>> [D][05:18:03][COMM]S->M yaw:INVALID


2025-07-31 22:32:43:046 ==>> 本次取值间隔时间:189ms
2025-07-31 22:32:43:053 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:32:43:151 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:32:43:211 ==>> 1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 22:32:43:316 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx

2025-07-31 22:32:43:421 ==>>  ret[11] >>> AT+CGATT?

[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 22:32:43:481 ==>> 本次取值间隔时间:325ms
2025-07-31 22:32:43:500 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:43:504 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:32:43:508 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:32:43:527 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 22:32:43:571 ==>>                                                                                                                                                                                             3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:32:43:781 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:32:43:784 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:32:43:786 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:32:44:087 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enabl

2025-07-31 22:32:44:117 ==>> e_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:32:44:328 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:32:44:331 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:32:44:334 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:32:44:406 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:32:44:634 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:32:44:639 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:32:44:667 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:32:44:834 ==>> [D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:32:44:914 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:32:44:918 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:32:44:922 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:32:45:092 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:32:45:185 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:32:45:191 ==>> 检测【AD_V21电压】
2025-07-31 22:32:45:201 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:32:45:305 ==>> 1A A1 20 00 00 
Get AD_V21 1644mV
OVER 150


2025-07-31 22:32:45:410 ==>> 本次取值间隔时间:218ms
2025-07-31 22:32:45:415 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 22:32:45:432 ==>> 【AD_V21电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:32:45:435 ==>> 检测【关闭仪表供电2】
2025-07-31 22:32:45:438 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:32:45:665 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,40,,,42,41,,,42,59,,,41,60,,,41,1*7E

$GBGSV,3,2,10,25,,,40,33,,,40,39,,,38,24,,,37,1*72

$GBGSV,3,3,10,34,,,36,11,,,38,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1644.465,1644.465,52.558,2097152,2097152,2097152*40

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:32:45:721 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:32:45:724 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:32:45:727 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:32:45:815 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:32:45:890 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:32:45:999 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:32:46:002 ==>> 检测【打开AccKey2供电】
2025-07-31 22:32:46:005 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:32:46:195 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:32:46:290 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:32:46:296 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:32:46:300 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:46:667 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:07][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:18:07][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:07][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:07][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:07][COMM]adc read battery ts volt:21 mv
[D][05:18:07][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:07][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,40,,,42,41,,,41,59,,,41,60,,,41,1*7E

$GBGSV,4,2,14,25,,,40,33,,,39,39,,,39,34,,,39,1*71

$GBGSV,4,3,14,7,,,39,24,,,38,11,,,36,5,,,35,1*72

$GBGSV,4,4,14,10,,,34,3,,,36,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1607.277,1607.277,51.387,2097152,2097152,2097152*47

[D][05:18:07][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:3  vol

2025-07-31 22:32:46:697 ==>> t:69 mv


2025-07-31 22:32:46:742 ==>>                     ead battery soc:255


2025-07-31 22:32:46:837 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33083mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:32:46:841 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:32:46:844 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:32:46:971 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:32:47:150 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:32:47:154 ==>> 该项需要延时执行
2025-07-31 22:32:47:584 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,41,,,41,59,,,41,60,,,40,1*79

$GBGSV,5,2,20,25,,,40,39,,,40,34,,,40,3,,,40,1*4A

$GBGSV,5,3,20,33,,,39,7,,,39,24,,,38,1,,,38,1*72

$GBGSV,5,4,20,11,,,37,2,,,36,44,,,35,10,,,35,1*47

$GBGSV,5,5,20,43,,,35,5,,,34,4,,,31,38,,,31,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1558.845,1558.845,49.880,2097152,2097152,2097152*42



2025-07-31 22:32:48:634 ==>> $GBGGA,143252.436,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,41,,,41,59,,,40,60,,,40,1*7F

$GBGSV,6,2,24,25,,,40,39,,,40,34,,,40,3,,,40,1*4D

$GBGSV,6,3,24,33,,,39,7,,,39,24,,,38,1,,,38,1*75

$GBGSV,6,4,24,11,,,38,16,,,37,2,,,36,10,,,36,1*49

$GBGSV,6,5,24,43,,,36,6,,,36,44,,,35,12,,,35,1*41

$GBGSV,6,6,24,5,,,33,9,,,33,4,,,32,38,,,31,1*40

$GBRMC,143252.436,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143252.436,0.000,1546.053,1546.053,49.462,2097152,2097152,2097152*5E



2025-07-31 22:32:48:738 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:32:49:614 ==>> $GBGGA,143253.416,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,41,,,41,59,,,41,34,,,41,1*7E

$GBGSV,7,2,25,60,,,40,25,,,40,39,,,40,3,,,40,1*4C

$GBGSV,7,3,25,33,,,39,7,,,39,24,,,38,1,,,38,1*75

$GBGSV,7,4,25,11,,,38,16,,,38,10,,,37,43,,,37,1*73

$GBGSV,7,5,25,2,,,36,6,,,36,12,,,36,44,,,35,1*77

$GBGSV,7,6,25,13,,,35,9,,,34,5,,,33,4,,,32,1*4A

$GBGSV,7,7,25,38,,,31,1*78

$GBRMC,143253.416,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143253.416,0.000,1553.858,1553.858,49.710,2097152,2097152,2097152*5B



2025-07-31 22:32:50:152 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:32:50:157 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:32:50:162 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:50:411 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:11][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:11][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:11][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:11][COMM]adc read battery ts volt:12 mv
[D][05:18:11][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:11][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1445  volt:33502 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:32:50:591 ==>> $GBGGA,143254.396,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,41,59,,,41,34,,,41,1*7D

$GBGSV,7,2,26,3,,,41,60,,,40,25,,,40,39,,,40,1*4E

$GBGSV,7,3,26,33,,,39,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,26,11,,,38,16,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,26,2,,,36,6,,,36,12,,,36,44,,,35,1*74

$GBGSV,7,6,26,13,,,35,9,,,35,32,,,34,5,,,33,1*7B

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,143254.396,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143254.396,0.000,1553.091,1553.091,49.683,2097152,2097152,2097152*58



2025-07-31 22:32:50:684 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:32:50:688 ==>> 检测【打开AccKey1供电】
2025-07-31 22:32:50:693 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:32:50:816 ==>> $GBGGA,143254.596,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,41,59,,,41,34,,,41,1*7D

$GBGSV,7,2,26,3,,,41,25,,,41,39,,,41,60,,,40,1*4E

$GBGSV,7,3,26,33,,,39,7,,,39,24,,,38,1,,,38,1*76

$GBGSV,7,4,26,11,,,38,16,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,26,2,,,36,6,,,36,12,,,36,44,,,35,1*74

$GBGSV,7,6,26,13,,,35,9,,,35,32,,,34,5,,,33,1*7B

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,143254.596,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143254.596,0.000,1556.284,1556.284,49.789,2097152,2097152,2097152*55

[D][05:18:11][COMM]read battery soc:255


2025-07-31 22:32:50:906 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:32:50:955 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:32:50:961 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:32:50:967 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:32:51:011 ==>> 1A A1 00 40 00 
Get AD_V14 2651mV
OVER 150


2025-07-31 22:32:51:209 ==>> 原始值:【2651】, 乘以分压基数【2】还原值:【5302】
2025-07-31 22:32:51:227 ==>> 【读取AccKey1电压(ADV14)前】通过,【5302mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:32:51:231 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:32:51:234 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:51:516 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:12][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:12][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:12][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:12][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:12][COMM]adc read battery ts volt:16 mv
[D][05:18:12][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:12][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:32:51:767 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5482mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:32:51:772 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:32:51:776 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:32:51:802 ==>> $GBGGA,143255.576,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,41,59,,,41,34,,,41,1*7D

$GBGSV,7,2,26,3,,,41,39,,,41,25,,,40,60,,,40,1*4F

$GBGSV,7,3,26,7,,,40,33,,,39,24,,,38,1,,,38,1*78

$GBGSV,7,4,26,11,,,38,16,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,26,2,,,36,6,,,36,12,,,36,44,,,35,1*74

$GBGSV,7,6,26,9,,,35,13,,,34,32,,,33,5,,,33,1*7D

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,143255.576,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143255.576,0.000,1553.099,1553.099,49.691,2097152,2097152,2097152*52



2025-07-31 22:32:51:966 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:32:52:075 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:32:52:080 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:32:52:085 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:32:52:207 ==>> 1A A1 00 40 00 
Get AD_V14 2652mV
OVER 150


2025-07-31 22:32:52:327 ==>> 原始值:【2652】, 乘以分压基数【2】还原值:【5304】
2025-07-31 22:32:52:357 ==>> 【读取AccKey1电压(ADV14)后】通过,【5304mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:32:52:361 ==>> 检测【打开WIFI(2)】
2025-07-31 22:32:52:364 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:32:52:770 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6
$GBGGA,143256.556,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,42,41,,,41,59,,,41,34,,,41,1*7D

$GBGSV,7,2,26,3,,,41,39,,,41,60,,,41,25,,,40,1*4E

$GBGSV,7,3,26,7,,,39,33,,,39,11,,,39,24,,,38,1*46

$GBGSV,7,4,26,1,,,38,16,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,26,2,,,36,6,,,36,12,,,36,44,,,35,1*74

$GBGSV,7,6,26,9,,,35,13,,,33,32,,,33,5,,,33,1*7A

$GBGSV,7,7,26,4,,,33,38,,,31,1*4F

$GBRMC,143256.556,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143256.556,0.000,1554.697,1554.697,49.746,2097152,2097152,2097152*58

                                         

2025-07-31 22:32:52:895 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:32:52:899 ==>> 检测【转刹把供电】
2025-07-31 22:32:52:902 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:32:53:091 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:32:53:182 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:32:53:188 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:32:53:193 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:32:53:286 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:53:316 ==>> +WIFISCAN:4,0,CC057790A640,-77
+WIFISCAN:4,1,CC057790A641,-77
+WIFISCAN:4,2,CC057790A7C1,-79
+WIFISCAN:4,3,44A1917CAD80,-81

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 22:32:53:466 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:32:53:706 ==>> $GBGGA,143257.536,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,59,,,41,34,,,41,1*7C

$GBGSV,7,2,27,3,,,40,39,,,40,60,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,16,,,39,11,,,38,1*46

$GBGSV,7,4,27,24,,,38,1,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,44,,,35,1*76

$GBGSV,7,6,27,9,,,35,32,,,34,5,,,33,4,,,33,1*4A

$GBGSV,7,7,27,13,,,31,38,,,31,14,,,29,1*74

$GBRMC,143257.536,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143257.536,0.000,1533.985,1533.985,49.100,2097152,2097152,2097152*5B



2025-07-31 22:32:53:811 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 22:32:54:254 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:32:54:363 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:54:712 ==>> [W][05:18:15][COMM]>>>>>Input command = ?<<<<
$GBGGA,143258.516,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,59,,,41,34,,,41,1*7C

$GBGSV,7,2,27,3,,,41,39,,,41,60,,,41,25,,,41,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,16,,,38,11,,,38,1*47

$GBGSV,7,4,27,24,,,38,1,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,6,,,36,9,,,36,2,,,35,1*4D

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,13,,,31,38,,,31,14,,,30,1*7C

$GBRMC,143258.516,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143258.516,0.000,1543.198,1543.198,49.395,2097152,2097152,2097152*58



2025-07-31 22:32:54:757 ==>>                                          

2025-07-31 22:32:55:307 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:32:55:412 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:55:502 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2393mV
OVER 150


2025-07-31 22:32:55:578 ==>> 原始值:【2393】, 乘以分压基数【2】还原值:【4786】
2025-07-31 22:32:55:595 ==>> 【读取AD_V15电压(前)】通过,【4786mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:32:55:601 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:32:55:608 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:32:55:697 ==>> $GBGGA,143259.516,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,42,59,,,41,34,,,41,1*7F

$GBGSV,7,2,27,39,,,41,60,,,41,25,,,41,3,,,40,1*4F

$GBGSV,7,3,27,7,,,39,33,,,39,16,,,39,11,,,39,1*47

$GBGSV,7,4,27,24,,,38,1,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,36,6,,,36,9,,,36,2,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,34,4,,,33,1*74

$GBGSV,7,7,27,13,,,31,38,,,31,14,,,31,1*7D

$GBRMC,143259.516,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143259.516,0.000,1547.801,1547.801,49.539,2097152,2097152,2097152*59



2025-07-31 22:32:55:712 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:32:55:802 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2419mV
OVER 150


2025-07-31 22:32:55:877 ==>> 原始值:【2419】, 乘以分压基数【2】还原值:【4838】
2025-07-31 22:32:55:905 ==>> 【读取AD_V16电压(前)】通过,【4838mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:32:55:910 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:32:55:913 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:56:210 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:17][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:17][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:17][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:17][COMM]adc read throttle adc:18  volt:23 mv
[D][05:18:17][COMM]adc read battery ts volt:20 mv
[D][05:18:17][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3073  volt:5401 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:32:56:450 ==>> 【转刹把供电电压(主控ADC)】通过,【5401mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:32:56:457 ==>> 检测【转刹把供电电压】
2025-07-31 22:32:56:468 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:56:785 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:17][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:17][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:17][COMM]adc read battery ts volt:21 mv
[D][05:18:17][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
$GBGGA,143300.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,41,59,,,41,34,,,41,1*7C

$GBGSV,7,2,27,39,,,41,3,,,41,60,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,40,33,,,39,16,,,39,11,,,39,1*49

$GBGSV,7,4,27,24,,,38,1,,,38,43,,,38,10,,,37,1*4E

$GBGSV,7,5,27,12,,,37,6,,,36,9,,,36,2,,,36,1*4E

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,34,4,,,33,1*74

$GBGSV,7,7,27,38,,,31,14,,,31,13,,,30,1*7C

$GBRMC,143300.516,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,

2025-07-31 22:32:56:845 ==>> 0.000,K,N*20

$GBGST,143300.516,0.000,1547.801,1547.801,49.538,2097152,2097152,2097152*55

[D][05:18:17][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 22:32:56:985 ==>> 【转刹把供电电压】通过,【5414mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:32:56:992 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:32:56:999 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:57:191 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:32:57:255 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:32:57:259 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:32:57:262 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:57:358 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:57:463 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:57:568 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:57:673 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:57:733 ==>> $GBGGA,143301.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,40,,,42,41,,,41,59,,,41,34,,,41,1*73

$GBGSV,7,2,28,39,,,41,3,,,40,60,,,40,25,,,40,1*40

$GBGSV,7,3,28,7,,,39,33,,,39,16,,,38,11,,,38,1*48

$GBGSV,7,4,28,24,,,38,1,,,38,43,,,38,10,,,37,1*41

$GBGSV,7,5,28,12,,,36,6,,,36,9,,,35,2,,,35,1*40

$GBGSV,7,6,28,44,,,35,32,,,34,5,,,33,4,,,33,1*7C

$GBGSV,7,7,28,14,,,32,38,,,31,13,,,29,35,,,43,1*79

$GBRMC,143301.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143301.516,0.000,766.337,766.337,700.835,2097152,2097152,2097152*6E



2025-07-31 22:32:57:778 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:57:838 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:18][COMM]>>>>>Input command = ?<<<<
[W][05:18:18][COMM]>>>>>Input command = ?<<<<


2025-07-31 22:32:57:883 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:57:898 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:32:57:988 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:32:58:108 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 22:32:58:112 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:32:58:115 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:32:58:119 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:32:58:213 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:32:58:305 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 22:32:58:338 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:32:58:343 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:32:58:366 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:32:58:410 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:32:58:608 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:32:58:612 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:32:58:619 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:32:58:742 ==>> $GBGGA,143302.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,59,,,41,34,,,41,41,,,41,1*7C

$GBGSV,7,2,27,3,,,40,39,,,40,60,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,16,,,38,1,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,38,,,31,13,,,29,1*77

$GBRMC,143302.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143302.516,0.000,764.802,764.802,699.431,2097152,2097152,2097152*64

3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:32:58:787 ==>>                                          

2025-07-31 22:32:58:880 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:32:58:884 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:32:58:890 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:32:58:998 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:32:59:152 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:32:59:158 ==>> 检测【左刹电压测试1】
2025-07-31 22:32:59:162 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:32:59:422 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:18:20][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:20][COMM]adc read left brake adc:1725  volt:2274 mv
[D][05:18:20][COMM]adc read right brake adc:1728  volt:2278 mv
[D][05:18:20][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:20][COMM]adc read battery ts volt:14 mv
[D][05:18:20][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:20][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1444  volt:33479 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:32:59:685 ==>> 【左刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 22:32:59:691 ==>> 检测【右刹电压测试1】
2025-07-31 22:32:59:708 ==>> 【右刹电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 22:32:59:713 ==>> $GBGGA,143303.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,39,,,41,59,,,41,1*47

$GBGSV,7,2,27,34,,,41,41,,,41,60,,,40,25,,,40,1*75

$GBGSV,7,3,27,7,,,39,33,,,39,16,,,38,1,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,5,,,34,32,,,34,4,,,33,1*74

$GBGSV,7,7,27,14,,,32,38,,,31,13,,,30,1*7F

$GBRMC,143303.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143303.516,0.000,768.630,768.630,702.931,2097152,2097152,2097152*6B



2025-07-31 22:32:59:739 ==>> 检测【转把电压测试1】
2025-07-31 22:32:59:746 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 22:32:59:752 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:32:59:758 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:32:59:797 ==>> 3A A3 03 00 A3 


2025-07-31 22:32:59:902 ==>> OFF_OUT3
OVER 150


2025-07-31 22:33:00:012 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:33:00:019 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:33:00:025 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:33:00:099 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:33:00:282 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:33:00:287 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:33:00:294 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:33:00:399 ==>> 3A A3 05 00 A3 


2025-07-31 22:33:00:504 ==>> OFF_OUT5
OVER 150


2025-07-31 22:33:00:553 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:33:00:580 ==>> 检测【左刹电压测试2】
2025-07-31 22:33:00:586 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:33:00:865 ==>> $GBGGA,143304.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,39,,,41,34,,,41,1*70

$GBGSV,7,2,27,41,,,41,3,,,40,60,,,40,25,,,40,1*40

$GBGSV,7,3,27,7,,,39,16,,,39,33,,,39,1,,,38,1*77

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,13,,,31,38,,,30,1*7F

$GBRMC,143304.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143304.516,0.000,766.331,766.331,700.828,2097152,2097152,2097152*67

[W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:21][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:21][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:21][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:21][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:21][COMM]adc read battery ts volt:14 mv
[D][05:18:21][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:21][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vb

2025-07-31 22:33:00:910 ==>> at adc:2405  volt:3875 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:21][COMM]read battery soc:255


2025-07-31 22:33:01:113 ==>> 【左刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 22:33:01:121 ==>> 检测【右刹电压测试2】
2025-07-31 22:33:01:159 ==>> 【右刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 22:33:01:163 ==>> 检测【转把电压测试2】
2025-07-31 22:33:01:200 ==>> 【转把电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 22:33:01:208 ==>> 检测【晶振检测】
2025-07-31 22:33:01:230 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:33:01:395 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 22:33:01:509 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:33:01:516 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:33:01:521 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:33:01:620 ==>> 1A A1 00 00 FC 
Get AD_V2 1642mV
Get AD_V3 1660mV
Get AD_V4 1650mV
Get AD_V5 2757mV
Get AD_V6 1993mV
Get AD_V7 1084mV
OVER 150


2025-07-31 22:33:01:710 ==>> $GBGGA,143305.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,34,,,41,41,,,41,1*7F

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,13,,,32,14,,,32,38,,,31,1*7D

$GBRMC,143305.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143305.516,0.000,766.321,766.321,700.819,2097152,2097152,2097152*64



2025-07-31 22:33:01:799 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:33:01:804 ==>> 检测【检测BootVer】
2025-07-31 22:33:01:808 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:33:02:169 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071541610
[D][05:18:22][FCTY]HardwareID  = 867222088006947
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = F6BA45689FD5
[D][05:18:22][FCTY]Bat         = 3944 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11700 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1286
[D][05:18:22][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 126 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 37

2025-07-31 22:33:02:199 ==>> 10 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:33:02:360 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:33:02:365 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:33:02:383 ==>> 检测【检测固件版本】
2025-07-31 22:33:02:387 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:33:02:404 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:33:02:408 ==>> 检测【检测蓝牙版本】
2025-07-31 22:33:02:426 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:33:02:430 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:33:02:461 ==>> 检测【检测MoBikeId】
2025-07-31 22:33:02:465 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:33:02:489 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:33:02:494 ==>> 检测【检测蓝牙地址】
2025-07-31 22:33:02:511 ==>> 取到目标值:F6BA45689FD5
2025-07-31 22:33:02:515 ==>> 【检测蓝牙地址】通过,【F6BA45689FD5】符合目标值【】要求!
2025-07-31 22:33:02:533 ==>> 提取到蓝牙地址:F6BA45689FD5
2025-07-31 22:33:02:537 ==>> 检测【BOARD_ID】
2025-07-31 22:33:02:556 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:33:02:560 ==>> 检测【检测充电电压】
2025-07-31 22:33:02:589 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:33:02:593 ==>> 检测【检测VBUS电压1】
2025-07-31 22:33:02:598 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:33:02:607 ==>> 检测【检测充电电流】
2025-07-31 22:33:02:636 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:33:02:641 ==>> 检测【检测IMEI】
2025-07-31 22:33:02:644 ==>> 取到目标值:867222088006947
2025-07-31 22:33:02:650 ==>> 【检测IMEI】通过,【867222088006947】符合目标值【】要求!
2025-07-31 22:33:02:681 ==>> 提取到IMEI:867222088006947
2025-07-31 22:33:02:709 ==>> 检测【检测IMSI】
2025-07-31 22:33:02:714 ==>> 取到目标值:460130071541610
2025-07-31 22:33:02:741 ==>> 【检测IMSI】通过,【460130071541610】符合目标值【】要求!
2025-07-31 22:33:02:745 ==>> 提取到IMSI:460130071541610
2025-07-31 22:33:02:749 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:33:02:771 ==>> 取到目标值:460130071541610
2025-07-31 22:33:02:787 ==>> 【校验网络运营商(移动)】通过,【460130071541610】符合目标值【】要求!
2025-07-31 22:33:02:792 ==>> 检测【打开CAN通信】
2025-07-31 22:33:02:796 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:33:02:799 ==>> $GBGGA,143306.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,3,,,41,59,,,41,34,,,41,1*49

$GBGSV,7,2,27,41,,,41,60,,,40,39,,,40,25,,,40,1*79

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,13,,,32,14,,,32,38,,,30,1*7C

$GBRMC,143306.516,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143306.516,0.000,766.327,766.327,700.825,2097152,2097152,2097152*68



2025-07-31 22:33:02:803 ==>> [D][05:18:23][COMM]read battery soc:255
[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:33:02:986 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:33:02:994 ==>> 检测【检测CAN通信】
2025-07-31 22:33:03:015 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:33:03:107 ==>> can send success


2025-07-31 22:33:03:152 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:33:03:197 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35123
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:33:03:266 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:33:03:271 ==>> 检测【关闭CAN通信】
2025-07-31 22:33:03:276 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:33:03:282 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:33:03:333 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:33:03:407 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:33:03:554 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:33:03:562 ==>> 检测【打印IMU STATE】
2025-07-31 22:33:03:576 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:33:03:757 ==>> $GBGGA,143307.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,41,,,41,3,,,40,1*4A

$GBGSV,7,2,27,60,,,40,39,,,40,34,,,40,25,,,40,1*7A

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,5,,,34,4,,,34,32,,,34,1*73

$GBGSV,7,7,27,13,,,33,14,,,32,38,,,31,1*7C

$GBRMC,143307.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143307.516,0.000,767.844,767.844,702.211,2097152,2097152,2097152*66

[W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:33:03:835 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:33:03:840 ==>> 检测【六轴自检】
2025-07-31 22:33:03:846 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:33:04:016 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:33:04:712 ==>> $GBGGA,143308.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,34,,,41,41,,,41,1*7F

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,4,,,34,32,,,34,5,,,33,1*74

$GBGSV,7,7,27,13,,,33,14,,,32,38,,,31,1*7C

$GBRMC,143308.516,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143308.516,0.000,767.848,767.848,702.215,2097152,2097152,2097152*6D



2025-07-31 22:33:04:802 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 22:33:05:760 ==>> $GBGGA,143309.516,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,3,,,41,59,,,41,34,,,41,1*49

$GBGSV,7,2,27,41,,,41,60,,,40,39,,,40,25,,,40,1*79

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,12,,,37,1*74

$GBGSV,7,5,27,43,,,37,2,,,36,6,,,36,9,,,35,1*49

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,13,,,33,1*45

$GBGSV,7,7,27,4,,,33,14,,,32,38,,,31,1*4A

$GBRMC,143309.516,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143309.516,0.000,769.384,769.384,703.619,2097152,2097152,2097152*65

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:33:05:835 ==>>                                                                                                                                                                      4046]
[D][05:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 22:33:06:737 ==>> $GBGGA,143310.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,3,,,41,59,,,41,34,,,41,1*49

$GBGSV,7,2,27,41,,,41,60,,,40,39,,,40,25,,,40,1*79

$GBGSV,7,3,27,7,,,39,16,,,39,33,,,39,1,,,38,1*77

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,13,,,33,1*45

$GBGSV,7,7,27,4,,,33,14,,,32,38,,,31,1*4A

$GBRMC,143310.516,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143310.516,0.000,769.385,769.385,703.620,2097152,2097152,2097152*67



2025-07-31 22:33:06:797 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 22:33:07:707 ==>> $GBGGA,143311.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,60,,,41,59,,,41,1*4B

$GBGSV,7,2,27,34,,,41,41,,,41,39,,,40,25,,,40,1*79

$GBGSV,7,3,27,7,,,39,16,,,39,33,,,39,1,,,38,1*77

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,2,,,36,6,,,36,9,,,36,44,,,36,1*4C

$GBGSV,7,6,27,12,,,36,13,,,34,4,,,34,32,,,34,1*44

$GBGSV,7,7,27,5,,,33,14,,,32,38,,,31,1*4B

$GBRMC,143311.516,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143311.516,0.000,773.980,773.980,707.822,2097152,2097152,2097152*6E



2025-07-31 22:33:08:193 ==>> [D][05:18:29][COMM]msg 0601 loss. last_tick:35105. cur_tick:40121. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40121


2025-07-31 22:33:08:719 ==>> $GBGGA,143312.516,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,41,,,41,3,,,40,1*4A

$GBGSV,7,2,27,60,,,40,39,,,40,34,,,40,25,,,40,1*7A

$GBGSV,7,3,27,7,,,39,1,,,38,16,,,38,24,,,38,1*71

$GBGSV,7,4,27,11,,,38,33,,,38,43,,,38,10,,,37,1*79

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,5,,,34,4,,,34,32,,,34,1*73

$GBGSV,7,7,27,13,,,33,14,,,32,38,,,31,1*7C

$GBRMC,143312.516,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143312.516,0.000,768.608,768.608,702.909,2097152,2097152,2097152*60



2025-07-31 22:33:08:809 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 22:33:09:707 ==>> $GBGGA,143313.516,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,34,,,41,41,,,41,1*7F

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,13,,,32,14,,,32,38,,,31,1*7D

$GBRMC,143313.516,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143313.516,0.000,767.086,767.086,701.518,2097152,2097152,2097152*6E



2025-07-31 22:33:10:715 ==>> $GBGGA,143314.516,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,34,,,41,41,,,41,1*7F

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,40,25,,,40,1*4E

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,6,,,36,12,,,36,2,,,35,9,,,35,1*4F

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,38,,,31,13,,,28,1*76

$GBRMC,143314.516,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143314.516,0.000,763.272,763.272,698.032,2097152,2097152,2097152*65



2025-07-31 22:33:10:819 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 22:33:11:711 ==>> $GBGGA,143315.516,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,34,,,41,41,,,41,3,,,40,1*41

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,40,25,,,40,1*71

$GBGSV,7,3,27,7,,,39,1,,,38,16,,,38,24,,,38,1*71

$GBGSV,7,4,27,11,,,38,33,,,38,10,,,37,43,,,37,1*76

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,38,,,31,13,,,28,1*76

$GBRMC,143315.516,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143315.516,0.000,762.502,762.502,697.328,2097152,2097152,2097152*63



2025-07-31 22:33:12:709 ==>> $GBGGA,143316.516,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,34,,,41,41,,,41,3,,,40,1*41

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,40,25,,,40,1*71

$GBGSV,7,3,27,7,,,39,1,,,38,16,,,38,24,,,38,1*71

$GBGSV,7,4,27,11,,,38,33,,,38,10,,,37,43,,,37,1*76

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,38,,,31,13,,,29,1*77

$GBRMC,143316.516,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143316.516,0.000,763.264,763.264,698.024,2097152,2097152,2097152*60



2025-07-31 22:33:12:843 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:33:13:718 ==>> $GBGGA,143317.516,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,39,,,41,34,,,41,1*70

$GBGSV,7,2,27,41,,,41,3,,,40,60,,,40,25,,,40,1*40

$GBGSV,7,3,27,7,,,39,33,,,39,1,,,38,16,,,38,1*76

$GBGSV,7,4,27,24,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,27,2,,,36,6,,,36,12,,,36,9,,,35,1*4C

$GBGSV,7,6,27,44,,,35,32,,,34,5,,,33,4,,,33,1*73

$GBGSV,7,7,27,14,,,32,13,,,31,38,,,31,1*7E

$GBRMC,143317.516,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143317.516,0.000,767.091,767.092,701.524,2097152,2097152,2097152*66



2025-07-31 22:33:14:171 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:33:14:402 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:33:15:768 ==>> $GBGGA,143314.523,2301.2573163,N,11421.9424002,E,1,20,0.63,74.664,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.12,0.63,0.93,4*00

$GBGSA,A,3,12,43,24,14,44,13,32,38,,,,,1.12,0.63,0.93,4*0A

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,60,190,40,39,60,34,41,1*40

$GBGSV,7,2,27,6,58,5,36,16,57,9,38,59,52,129,41,10,50,206,37,1*4A

$GBGSV,7,3,27,9,50,342,36,25,50,352,40,1,48,125,38,2,45,237,35,1*4A

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,41,238,40,11,37,139,38,1*7C

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,37,1*73

$GBGSV,7,6,27,24,26,61,38,5,21,256,33,14,17,178,32,44,16,52,35,1*41

$GBGSV,7,7,27,13,13,212,32,32,10,316,34,38,6,188,31,1*7E

$GBRMC,143314.523,A,2301.2573163,N,11421.9424002,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[D][05:18:35][GNSS]HD8040 GPS
[W][05:18:35][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:35][GNSS]GPS diff_sec 124017279, report 0x42 frame
$GBGST,143314.523,1.541,0.214,0.201,0.292,2.054,2.501,6.303*7E

[D][05:18:35][COMM]read battery soc:255
[D][05:18:35][COMM]Main Task receive event:131
[D][05:18:35][COMM]index:0,power_mode:0xFF
[D][05:18:35][COMM]index:1,sound_mode:0xFF
[D][05:18:35][COMM

2025-07-31 22:33:15:873 ==>> ]index:2,gsensor_mode:0xFF
[D][05:18:35][COMM]index:3,report_freq_mode:0xFF
[D][05:18:35][COMM]index:4,report_period:0xFF
[D][05:18:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:35][COMM]index:6,normal_reset_period:0xFF
[D][05:18:35][COMM]index:7,spock_over_speed:0xFF
[D][05:18:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:35][COMM]index:16,imu_config_params:0xFF
[D][05:18:35][COMM]index:17,long_connect_params:0xFF
[D][05:18:35][COMM]index:18,detain_mark:0xFF
[D][05:18:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:35][COMM]index:21,mc_mode:0xFF
[D][05:18:35][COMM]index:22,S_mode:0xFF
[D][05:18:35][COMM]index:23,overweight:0xFF
[D][05:18:35][COMM]index:24,standstill_mode:0xFF
[D][05:18:35][COMM]index:25,night_mode:0xFF
[D][05:18:35]

2025-07-31 22:33:15:978 ==>> [COMM]index:26,experiment1:0xFF
[D][05:18:35][COMM]index:27,experiment2:0xFF
[D][05:18:35][COMM]index:28,experiment3:0xFF
[D][05:18:35][COMM]index:29,experiment4:0xFF
[D][05:18:35][COMM]index:30,night_mode_start:0xFF
[D][05:18:35][COMM]index:31,night_mode_end:0xFF
[D][05:18:35][COMM]index:33,park_report_minutes:0xFF
[D][05:18:35][COMM]index:34,park_report_mode:0xFF
[D][05:18:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:35][COMM]index:38,charge_battery_para: FF
[D][05:18:35][COMM]index:39,multirider_mode:0xFF
[D][05:18:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:35][COMM]index:44,riding_duration_config:0xFF
[D][05:18:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:35][COMM]index:49,mc_load_startup:0xFF
[D][05:18:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:35][COMM]

2025-07-31 22:33:16:083 ==>> index:52,traffic_mode:0xFF
[D][05:18:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:35][COMM]index:63,experiment5:0xFF
[D][05:18:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:35][COMM]index:72,experiment6:0xFF
[D][05:18:35][COMM]index:73,experiment7:0xFF
[D][05:18:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:35][COMM]index:75,zero_value_from_server:-1
[D][05:18:35][COMM]index:76,multirider_threshold:255
[

2025-07-31 22:33:16:188 ==>> D][05:18:35][COMM]index:77,experiment8:255
[D][05:18:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:35][COMM]index:83,loc_report_interval:255
[D][05:18:35][COMM]index:84,multirider_threshold_p2:255
[D][05:18:35][COMM]index:85,multirider_strategy:255
[D][05:18:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:35][COMM]index:90,weight_param:0xFF
[D][05:18:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:35][COMM]index:95,current_limit:0xFF
[D][05:18:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:35][COMM]index:100,location_mode:0xFF

[W][05:18:35][PROT]remove success[1629955115],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:35][COMM]Main Task receive event:131 finished processing
[D][05:

2025-07-31 22:33:16:293 ==>> 18:35][PROT]index:0 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:4
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:1
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900005]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[294]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e20] format[0]
[D][05:18:35][COMM]Main Task receive event:20
[D][05:18:35][GNSS]stop event:1
[D][05:18:35][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:35][COMM]----- get Acckey 1 

2025-07-31 22:33:16:398 ==>> and value:1------------
[D][05:18:35][COMM]----- get Acckey 2 and value:0------------
[D][05:18:35][COMM]------------ready to Power on Acckey 2------------
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][COMM]First location,do verification
[D][05:18:35][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:35][COMM][frmJournal_read][220] read fail
[D][05:18:35][COMM]get hw mark: ffffffff
[D][05:18:35][COMM]bat type 255
[D][05:18:35][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:35][PROT]remove success[1629955115],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[0306],priority[3],index[1],used[1]
[D][05:18:35][COMM]photo upload succeed!!! send ack
[D][05:18:35][COMM]photo upload taskid:[00000000 00000000]
[W][05:18:35][PROT]remove success[1629955115]

2025-07-31 22:33:16:503 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 22:33:16:608 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 22:33:16:638 ==>>                                                     

2025-07-31 22:33:16:868 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 22:33:17:363 ==>> $GBGGA,143317.000,2301.2574666,N,11421.9430519,E,1,20,0.63,76.707,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,10,09,25,34,41,11,33,1.12,0.63,0.93,4*00

$GBGSA,A,3,12,43,24,14,44,13,32,38,,,,,1.12,0.63,0.93,4*0A

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,60,190,41,39,60,34,40,1*40

$GBGSV,7,2,27,6,58,5,36,16,57,9,38,59,52,129,41,10,50,206,37,1*4A

$GBGSV,7,3,27,9,50,342,35,25,50,352,40,1,48,125,38,2,45,237,36,1*4A

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,41,238,40,11,37,139,38,1*7C

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,36,43,28,171,38,1*7D

$GBGSV,7,6,27,24,26,61,38,5,21,256,33,14,17,178,32,44,16,52,35,1*41

$GBGSV,7,7,27,13,13,212,33,32,10,316,34,38,6,188,31,1*7F

$GBGSV,3,1,11,40,64,170,41,39,60,34,40,25,50,352,41,34,43,107,40,5*4B

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,34,32,10,316,32,38,6,188,27,5*49

$GBRMC,143317.000,A,2301.2574666,N,11421.9430519,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[W][05:18:38][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143317.000,3.114,0.207,0.197,0.277,2.293,2.356,3.904*7F



2025-07-31 22:33:18:383 ==>> $GBGGA,143318.000,2301.2575065,N,11421.9431746,E,1,25,0.57,77.082,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,61,190,40,39,60,34,40,1*40

$GBGSV,7,2,27,6,58,5,36,16,57,9,38,10,50,206,37,9,50,342,35,1*71

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,38,1*7D

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,40,11,37,139,38,1*7E

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,33,14,17,178,32,44,16,52,35,1*41

$GBGSV,7,7,27,13,13,212,34,32,10,316,34,38,6,188,31,1*78

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,34,32,10,316,32,38,6,188,27,5*49

$GBRMC,143318.000,A,2301.2575065,N,11421.9431746,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[W][05:18:39][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143318.000,2.949,0.217,0.210,0.294,

2025-07-31 22:33:18:413 ==>> 2.168,2.215,3.608*73



2025-07-31 22:33:18:858 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:33:19:379 ==>> $GBGGA,143319.000,2301.2575550,N,11421.9432425,E,1,25,0.57,77.503,M,-1.770,M,,*51

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,61,190,41,39,60,34,40,1*41

$GBGSV,7,2,27,6,58,5,36,16,57,9,38,10,50,206,37,9,50,342,36,1*72

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,38,1*7D

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,41,11,37,139,38,1*7F

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,36,43,28,171,38,1*7D

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,32,44,16,52,35,1*46

$GBGSV,7,7,27,13,13,212,33,32,10,316,34,38,6,188,31,1*7F

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143319.000,A,2301.2575550,N,11421.9432425,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:40][GNSS]single mode encounter continous mode, immediately report.

2025-07-31 22:33:19:409 ==>> 
$GBGST,143319.000,2.985,0.230,0.222,0.311,2.165,2.201,3.461*7F



2025-07-31 22:33:20:389 ==>> $GBGGA,143320.000,2301.2575849,N,11421.9433091,E,1,25,0.57,77.743,M,-1.770,M,,*52

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,61,190,41,39,60,34,41,1*40

$GBGSV,7,2,27,6,58,5,36,16,57,9,38,10,50,206,37,9,50,342,36,1*72

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,38,1*7D

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,41,11,37,139,38,1*7F

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,36,43,28,171,38,1*7D

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,33,44,16,52,35,1*47

$GBGSV,7,7,27,13,13,212,33,32,10,316,34,38,6,188,31,1*7F

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143320.000,A,2301.2575849,N,11421.9433091,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

[W][05:18:41][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143320.000,3.110,0.212,0.206,0.288,2.219,2.248,3.398*73



2025-07-31 22:33:20:869 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 22:33:21:466 ==>> [D][05:18:42][PROT]CLEAN,SEND:0
[D][05:18:42][PROT]index:1 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:5
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:10
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900006]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][PROT]CLEAN:0
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B3EDDAB1E

2025-07-31 22:33:21:572 ==>> B32ECBDCCFC527E867E1CFE339928548A46953825C74F5054905C42162404479C5E1502649B93C3297D633654B0C71696B7F9B54843883697EE1C4E249B9D42695D4CD3180886DDF8EE84CB84EFD6
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
$GBGGA,143321.000,2301.2576020,N,11421.9433620,E,1,25,0.57,77.967,M,-1.770,M,,*53

[D][05:18:42][PROT]M2M Send ok [1629955122]
$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,42,7,62,196,40,3,61,190,41,39,60,34,41,1*4D

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,38,9,50,342,36,1*7C

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,39,1*7C

$GBGSV,7,4,27,34,43,107,41,41,42,267,42,60,42,239,40,11,37,139,38,1*7D

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,

2025-07-31 22:33:21:647 ==>> 38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,33,44,16,52,36,1*44

$GBGSV,7,7,27,13,13,212,33,32,10,316,34,38,6,188,32,1*7C

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143321.000,A,2301.2576020,N,11421.9433620,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:42][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143321.000,3.230,0.227,0.219,0.307,2.273,2.297,3.360*74



2025-07-31 22:33:22:390 ==>> $GBGGA,143322.000,2301.2576002,N,11421.9433770,E,1,25,0.57,77.968,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,42,7,62,196,40,3,61,190,41,39,60,34,41,1*4D

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,37,9,50,342,36,1*73

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,39,1*7C

$GBGSV,7,4,27,34,43,107,41,41,42,267,42,60,42,239,40,11,37,139,38,1*7D

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,33,44,16,52,35,1*47

$GBGSV,7,7,27,13,13,212,33,32,10,316,34,38,6,188,32,1*7C

$GBGSV,3,1,11,40,64,170,41,39,60,34,42,25,50,352,41,34,43,107,40,5*49

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143322.000,A,2301.2576002,N,11421.9433770,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:43][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143322.000,3.177,0.205,0.199,0.277,2.235,2.255,3.263*74



2025-07-31 22:33:22:883 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 22:33:23:390 ==>> $GBGGA,143323.000,2301.2576031,N,11421.9434172,E,1,25,0.57,78.119,M,-1.770,M,,*58

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,42,7,62,196,40,3,61,190,41,39,60,34,41,1*4D

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,37,9,50,342,35,1*70

$GBGSV,7,3,27,25,50,352,41,59,49,130,41,2,46,237,36,1,45,125,38,1*7C

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,40,11,37,139,38,1*7E

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,33,44,16,52,35,1*47

$GBGSV,7,7,27,13,13,212,32,32,10,316,34,38,6,188,32,1*7D

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,31,38,6,188,27,5*4B

$GBRMC,143323.000,A,2301.2576031,N,11421.9434172,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:44][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143323.000,3.328,0.204,0.198,0.275,2.311,2.329,3.277*77



2025-07-31 22:33:24:391 ==>> $GBGGA,143324.000,2301.2576158,N,11421.9434414,E,1,25,0.57,78.261,M,-1.770,M,,*58

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,42,7,62,196,39,3,61,190,41,39,60,34,41,1*43

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,37,9,50,342,36,1*73

$GBGSV,7,3,27,25,50,352,41,59,49,130,41,2,46,237,36,1,45,125,38,1*7C

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,40,11,37,139,38,1*7E

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,33,44,16,52,35,1*47

$GBGSV,7,7,27,13,13,212,32,32,10,316,34,38,6,188,32,1*7D

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,37,5*40

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143324.000,A,2301.2576158,N,11421.9434414,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[W][05:18:45][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143324.000,3.253,0.190,0.185,0.259,2.264,2.281,3.195*7E



2025-07-31 22:33:24:543 ==>> 未匹配到【六轴自检】数据,请核对检查!
2025-07-31 22:33:24:553 ==>> #################### 【测试结束】 ####################
2025-07-31 22:33:24:649 ==>> 关闭5V供电
2025-07-31 22:33:24:655 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:33:24:701 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:33:24:881 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:33:25:385 ==>> $GBGGA,143325.000,2301.2576280,N,11421.9434497,E,1,25,0.57,78.380,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,41,7,62,196,39,3,61,190,40,39,60,34,40,1*40

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,37,9,50,342,35,1*70

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,36,1,45,125,39,1*7C

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,40,11,37,139,38,1*7E

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,32,44,16,52,35,1*46

$GBGSV,7,7,27,13,13,212,31,32,10,316,34,38,6,188,31,1*7D

$GBGSV,3,1,11,40,64,170,41,39,60,34,41,25,50,352,41,34,43,107,40,5*4A

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,37,5*40

$GBGSV,3,3,11,44,16,52,35,32,10,316,31,38,6,188,27,5*4B

$GBRMC,143325.000,A,2301.2576280,N,11421.9434497,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:46][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143325.000,3.313,0.212,0.206,0.286,2.293,2.307,3.186*

2025-07-31 22:33:25:415 ==>> 7C



2025-07-31 22:33:25:660 ==>> 关闭5V供电成功
2025-07-31 22:33:25:668 ==>> 关闭33V供电
2025-07-31 22:33:25:677 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:33:25:801 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:33:26:027 ==>> [D][05:18:46][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:46][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:18:46][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:46][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:46][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 22:33:26:589 ==>> $GBGGA,143326.000,2301.2576271,N,11421.9434731,E,1,25,0.57,78.557,M,-1.770,M,,*54

$GBGSA,A,3,40,07,03,39,06,16,10,09,25,59,02,01,1.04,0.57,0.87,4*0B

$GBGSA,A,3,34,41,60,11,33,12,43,24,14,44,13,32,1.04,0.57,0.87,4*00

$GBGSA,A,3,38,,,,,,,,,,,,1.04,0.57,0.87,4*0B

$GBGSV,7,1,27,40,64,170,42,7,62,196,39,3,61,190,40,39,60,34,40,1*43

$GBGSV,7,2,27,6,58,5,36,16,57,9,39,10,50,206,37,9,50,342,35,1*70

$GBGSV,7,3,27,25,50,352,40,59,49,130,41,2,46,237,35,1,45,125,39,1*7F

$GBGSV,7,4,27,34,43,107,41,41,42,267,41,60,42,239,41,11,37,139,38,1*7F

$GBGSV,7,5,27,33,33,201,39,4,32,111,34,12,31,74,37,43,28,171,38,1*7C

$GBGSV,7,6,27,24,26,61,38,5,21,256,34,14,17,178,32,44,16,52,35,1*46

$GBGSV,7,7,27,13,13,212,31,32,10,316,34,38,6,188,31,1*7D

$GBGSV,3,1,11,40,64,170,41,39,60,34,42,25,50,352,41,34,43,107,40,5*49

$GBGSV,3,2,11,41,42,267,42,33,33,201,39,43,28,171,34,24,26,61,38,5*4F

$GBGSV,3,3,11,44,16,52,35,32,10,316,32,38,6,188,27,5*48

$GBRMC,143326.000,A,2301.2576271,N,11421.9434731,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:47][GNSS]single mode encounter continous mode, immediately report.
$GBGST,143326.000,3.301,0.235,0.227,0.315,2.283,2.296,3.147*74

[D][05:18:47][PROT]CLEAN,SEND:1
[D][0

2025-07-31 22:33:26:664 ==>> 关闭33V供电成功
2025-07-31 22:33:26:676 ==>> 关闭3.7V供电
2025-07-31 22:33:26:702 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:33:26:728 ==>> 5:18:47][PROT]index:1 1629955127
[D][05:18:47][PROT]is_send:0
[D][05:18:47][PROT]sequence_num:5
[D][05:18:47][PROT]retry_timeout:0
[D][05:18:47][PROT]retry_times:9
[D][05:18:47][PROT]send_path:0x2
[D][05:18:47][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:47][PROT]===========================================================
[W][05:18:47][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955127]
[D][05:18:47][PROT]===========================================================
[D][05:18:47][PROT]sending traceid [9999999999900006]
[D][05:18:47][PROT]Send_TO_M2M [1629955127]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:47][SAL ]sock send credit cnt[6]
[D][05:18:47][SAL ]sock send ind credit cnt[6]
[D][05:18:47][M2M ]m2m send data len[198]
[D][05:18:47][SAL ]Cellular task submsg id[10]
[D][05:18:47][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:47][CAT1]gsm read msg sub id: 15
[D][05:18:47][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B3B70FF520E943727BB4E464E4A9BD89B76F545E01BD9

2025-07-31 22:33:26:769 ==>> 91D3C73C8A9A3C7997739D620B84A242DC0C93770AEF29B97D1BB00ACAF95680EE0F02D5794C5AA98C17BF88849ABA952F58A35C93D2110415E1C3681
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]
6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:33:27:263 ==>>  

2025-07-31 22:33:27:669 ==>> 关闭3.7V供电成功
