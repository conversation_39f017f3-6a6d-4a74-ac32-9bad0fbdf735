2025-07-31 23:10:18:834 ==>> MES查站成功:
查站序号:P51000100531265A验证通过
2025-07-31 23:10:18:838 ==>> 扫码结果:P51000100531265A
2025-07-31 23:10:18:839 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:10:18:841 ==>> 测试参数版本:2024.10.11
2025-07-31 23:10:18:842 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:10:18:844 ==>> 检测【打开透传】
2025-07-31 23:10:18:845 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:10:18:924 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:10:19:112 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:10:19:115 ==>> 检测【检测接地电压】
2025-07-31 23:10:19:116 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:10:19:224 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:10:19:401 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:10:19:403 ==>> 检测【打开小电池】
2025-07-31 23:10:19:406 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:10:19:515 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:10:19:669 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:10:19:672 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:10:19:675 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:10:19:714 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:10:19:945 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:10:19:947 ==>> 检测【等待设备启动】
2025-07-31 23:10:19:950 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:10:20:288 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:10:20:485 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:10:20:970 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:10:21:135 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:10:21:180 ==>>                              w, GPS Will Not Open


2025-07-31 23:10:21:575 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:10:22:001 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:10:22:061 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:10:22:277 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:10:22:280 ==>> 检测【产品通信】
2025-07-31 23:10:22:284 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:10:22:503 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 23:10:22:553 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:10:22:555 ==>> 检测【初始化完成检测】
2025-07-31 23:10:22:557 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:10:22:746 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:10:22:824 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:10:22:827 ==>> 检测【关闭大灯控制1】
2025-07-31 23:10:22:828 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:10:22:989 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:10:23:094 ==>> [D

2025-07-31 23:10:23:099 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:10:23:100 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:10:23:102 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:10:23:124 ==>> ][05:17:51][COMM]2629 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:23:351 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:10:23:630 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:10:23:633 ==>> 检测【关闭仪表供电】
2025-07-31 23:10:23:635 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:10:23:811 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:10:23:917 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:10:23:919 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:10:23:920 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:10:24:127 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3641 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:24:196 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:10:24:218 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:10:24:219 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:10:24:403 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:10:24:466 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:10:24:469 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:10:24:471 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:24:585 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:10:24:739 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:10:24:742 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:10:24:743 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:10:24:816 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:10:24:906 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:53][COMM]read battery soc:255


2025-07-31 23:10:25:014 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:10:25:017 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:10:25:018 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:10:25:147 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:25:332 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:10:25:334 ==>> 该项需要延时执行
2025-07-31 23:10:25:663 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5005. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5006. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5007. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5007. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5008. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5008. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5009. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault change:

2025-07-31 23:10:25:694 ==>>  0x0001802E->0x01B987FE 5010


2025-07-31 23:10:26:164 ==>> [D][05:17:54][COMM]5663 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:26:497 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:10:27:002 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_a

2025-07-31 23:10:27:106 ==>> udio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[

2025-07-31 23:10:27:212 ==>> D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_cur

2025-07-31 23:10:27:272 ==>> rent_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:10:27:302 ==>>                                                                                                                                          

2025-07-31 23:10:28:184 ==>> [D][05:17:56][COMM]7685 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:28:919 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:10:29:178 ==>> [D][05:17:57][COMM]8696 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:29:343 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:10:29:347 ==>> 检测【33V输入电压ADC】
2025-07-31 23:10:29:349 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:29:634 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:17:57][COMM]adc read out 24v adc:1332  volt:33690 mv
[D][05:17:57][COMM]adc read left brake adc:15  volt:19 mv
[D][05:17:57][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:57][COMM]adc read throttle adc:15  volt:19 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:10:29:882 ==>> 【33V输入电压ADC】通过,【33184mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:10:29:885 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:10:29:887 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:10:30:035 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1661mV
Get AD_V4 1mV
Get AD_V5 2746mV
Get AD_V6 1984mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:10:30:180 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:30:182 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:10:30:200 ==>> [D][05:17:58][COMM]9708 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:10:30:261 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:30:263 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:10:30:265 ==>> 原始值:【2746】, 乘以分压基数【2】还原值:【5492】
2025-07-31 23:10:30:300 ==>> 【TP68_VCC5V5(ADV5)】通过,【5492mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:10:30:302 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:10:30:361 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1984mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:10:30:364 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:10:30:470 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:10:30:495 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:10:30:569 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10015
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10015
1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2744mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:10:30:777 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:30:779 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:10:30:827 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:30:830 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:10:30:833 ==>> 原始值:【2744】, 乘以分压基数【2】还原值:【5488】
2025-07-31 23:10:30:882 ==>> 【TP68_VCC5V5(ADV5)】通过,【5488mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:10:30:885 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:10:30:923 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][COMM]read battery soc:255


2025-07-31 23:10:30:932 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:10:30:934 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:10:30:984 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:10:30:987 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:10:31:137 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2744mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:10:31:289 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:31:291 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:10:31:333 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:31:350 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:10:31:355 ==>> 原始值:【2744】, 乘以分压基数【2】还原值:【5488】
2025-07-31 23:10:31:374 ==>> 【TP68_VCC5V5(ADV5)】通过,【5488mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:10:31:379 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:10:31:420 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:10:31:423 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:10:31:440 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10719 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0

2025-07-31 23:10:31:484 ==>> ,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:10:31:524 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:10:31:527 ==>> 检测【打开WIFI(1)】
2025-07-31 23:10:31:530 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:10:31:881 ==>>                                                                                                                                                                 AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087952927

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:10:32:052 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:10:32:055 ==>> 检测【清空消息队列(1)】
2025-07-31 23:10:32:057 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:10:32:219 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:10:32:334 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:10:32:337 ==>> 检测【打开GPS(1)】
2025-07-31 23:10:32:356 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:10:32:521 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 23:10:32:614 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:10:32:617 ==>> 检测【打开GSM联网】
2025-07-31 23:10:32:621 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:10:32:843 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:10:32:891 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:10:32:919 ==>> 检测【打开仪表供电1】
2025-07-31 23:10:32:922 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:10:32:924 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 23:10:33:113 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:10:33:173 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:10:33:179 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:10:33:189 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:10:33:419 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:10:33:462 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:10:33:466 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:10:33:470 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:10:33:599 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33340]


2025-07-31 23:10:33:740 ==>> 【读取主控ADC采集的仪表电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:10:33:744 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:10:33:746 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:33:916 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:10:34:016 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:10:34:019 ==>> 检测【AD_V20电压】
2025-07-31 23:10:34:022 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:34:128 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:34:131 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:10:34:233 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13731 imu init OK
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:10:34:293 ==>> [D][05:18:02][COMM]S->M yaw:INVALID


2025-07-31 23:10:34:458 ==>> 本次取值间隔时间:318ms
2025-07-31 23:10:34:477 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:34:578 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:34:945 ==>> 本次取值间隔时间:359ms
2025-07-31 23:10:34:960 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg su

2025-07-31 23:10:34:990 ==>> b id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK


2025-07-31 23:10:35:095 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 23:10:35:185 ==>> 本次取值间隔时间:238ms
2025-07-31 23:10:35:411 ==>> 本次取值间隔时间:218ms
2025-07-31 23:10:35:457 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:10:35:669 ==>> 本次取值间隔时间:249ms
2025-07-31 23:10:35:675 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:35:774 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:35:778 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:35:864 ==>> 本次取值间隔时间:87ms
2025-07-31 23:10:35:926 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:10:35:971 ==>> 本次取值间隔时间:95ms
2025-07-31 23:10:36:034 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:36:138 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:36:231 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 23:10:36:565 ==>> 本次取值间隔时间:419ms
2025-07-31 23:10:36:583 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:36:690 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:36:828 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:10:36:843 ==>> 本次取值间隔时间:139ms
2025-07-31 23:10:36:963 ==>> 本次取值间隔时间:108ms
2025-07-31 23:10:37:083 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,40,,,41,34,,,39,60,,,39,39,,,38,1*72

$GBGSV,2,2,06,25,,,35,59,,,38,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1591.950,1591.950,50.879,2097152,2097152,2097152*4C

[D][05:18:05][CAT1]<<< 
OK

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[D][05:18:05][COMM]read battery soc:255


2025-07-31 23:10:37:188 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:10:37:388 ==>> 本次取值间隔时间:419ms
2025-07-31 23:10:37:635 ==>> 本次取值间隔时间:242ms
2025-07-31 23:10:37:639 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:37:742 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:37:787 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:37:817 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:10:37:922 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,40,34,,,39,60,,,39,39,,,39,1*76

$GBGSV,3,2,12,59,,,39,11,,,37,7,,,37,25,,,36,1*47

$GBGSV,3,3,12,6,,,34,1,,,32,4,,,32,5,,,31,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1502.876,1502.87

2025-07-31 23:10:37:952 ==>> 6,48.092,2097152,2097152,2097152*48



2025-07-31 23:10:37:967 ==>> 本次取值间隔时间:214ms
2025-07-31 23:10:37:985 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:38:089 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:38:247 ==>> 本次取值间隔时间:149ms
2025-07-31 23:10:38:322 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:10:38:647 ==>> 本次取值间隔时间:394ms
2025-07-31 23:10:38:965 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,40,,,40,34,,,39,39,,,39,59,,,39,1*7C

$GBGSV,4,2,15,60,,,38,7,,,38,3,,,38,11,,,37,1*79

$GBGSV,4,3,15,25,,,37,43,,,37,41,,,37,6,,,35,1*44

$GBGSV,4,4,15,1,,,33,4,,,31,5,,,31,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1517.369,1517.369,48.538,2097152,2097152,2097152*4D



2025-07-31 23:10:38:995 ==>>                                          

2025-07-31 23:10:39:040 ==>> 本次取值间隔时间:380ms
2025-07-31 23:10:39:381 ==>> 本次取值间隔时间:330ms
2025-07-31 23:10:39:385 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:39:490 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:39:706 ==>> [W][05:18:08][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:39:871 ==>> 本次取值间隔时间:371ms
2025-07-31 23:10:39:976 ==>> $GBGGA,151043.843,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,40,34,,,39,39,,,39,59,,,39,1*70

$GBGSV,5,2,18,3,,,39,60,,,38,7,,,38,25,,,38,1*7C

$GBGSV,5,3,18,41,,,38,11,,,37,43,,,37,23,,,36,1*74

$GBGSV,5,4,18,6,,,35,1,,,33,24,,,33,4,,,31,1*4F

$GBGSV,5,5,18,5,,,31,16,,,37,1*4B

$GBRMC,151043.843,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151043.843,0.000,1514.446,1514.446,48.447,2097152,2097152,2097152*57



2025-07-31 23:10:40:268 ==>> 本次取值间隔时间:391ms
2025-07-31 23:10:40:657 ==>> 本次取值间隔时间:381ms
2025-07-31 23:10:40:687 ==>> $GBGGA,151044.543,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,40,34,,,39,39,,,39,3,,,39,1*44

$GBGSV,5,2,20,60,,,39,59,,,38,7,,,38,25,,,38,1*48

$GBGSV,5,3,20,41,,,38,16,,,38,11,,,37,43,,,37,1*77

$GBGSV,5,4,20,23,,,36,6,,,35,10,,,35,1,,,34,1*70

$GBGSV,5,5,20,24,,,34,5,,,31,12,,,31,4,,,30,1*74

$GBRMC,151044.543,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151044.543,0.000,1504.940,1504.940,48.149,2097152,2097152,2097152*56



2025-07-31 23:10:41:020 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 23:10:41:140 ==>> 本次取值间隔时间:481ms
2025-07-31 23:10:41:145 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:10:41:249 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:10:41:280 ==>> [W][05:18:09][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:41:325 ==>> 1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 23:10:41:674 ==>> 本次取值间隔时间:414ms
2025-07-31 23:10:41:689 ==>> $GBGGA,151045.523,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,34,,,39,39,,,39,3,,,39,1*47

$GBGSV,6,2,21,59,,,39,60,,,38,7,,,38,25,,,38,1*4A

$GBGSV,6,3,21,41,,,38,16,,,37,11,,,37,43,,,37,1*7A

$GBGSV,6,4,21,23,,,36,6,,,35,10,,,35,1,,,34,1*72

$GBGSV,6,5,21,24,,,34,12,,,32,5,,,31,2,,,31,1*72

$GBGSV,6,6,21,4,,,30,1*42

$GBRMC,151045.523,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151045.523,0.000,1496.457,1496.457,47.884,2097152,2097152,2097152*56



2025-07-31 23:10:41:706 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:41:711 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:10:41:715 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:10:41:794 ==>> 3

2025-07-31 23:10:41:824 ==>> A A3 02 00 A3 


2025-07-31 23:10:41:914 ==>> OFF_OUT2
OVER 150


2025-07-31 23:10:41:983 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:10:41:986 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:10:41:990 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:10:42:235 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:0
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:10:42:514 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:10:42:519 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:10:42:522 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:10:42:661 ==>> $GBGGA,151046.503,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,34,,,39,39,,,39,3,,,39,1*44

$GBGSV,6,2,22,59,,,39,60,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,22,41,,,38,11,,,38,16,,,37,43,,,37,1*76

$GBGSV,6,4,22,23,,,37,10,,,36,6,,,35,1,,,35,1*72

$GBGSV,6,5,22,24,,,34,33,,,33,12,,,32,5,,,31,1*41

$GBGSV,6,6,22,2,,,31,4,,,30,1*71

$GBRMC,151046.503,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151046.503,0.000,1500.048,1500.048,48.000,2097152,2097152,2097152*5C

3A A3 02 01 A3 


2025-07-31 23:10:42:721 ==>> ON_OUT2
OVER 150


2025-07-31 23:10:42:784 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:10:42:791 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:10:42:814 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:10:43:042 ==>> [D][05:18:11][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:11][COMM]oneline display read state:1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:11][COMM]read battery soc:255


2025-07-31 23:10:43:325 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:10:43:331 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:10:43:347 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:10:43:515 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:11][COMM]oneline display set 1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:10:43:610 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:10:43:616 ==>> 检测【AD_V21电压】
2025-07-31 23:10:43:623 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:10:43:628 ==>> $GBGGA,151047.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,39,,,40,34,,,39,3,,,39,1*4A

$GBGSV,6,2,22,59,,,39,60,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,22,41,,,38,11,,,38,16,,,37,43,,,37,1*76

$GBGSV,6,4,22,23,,,36,10,,,36,6,,,35,1,,,34,1*72

$GBGSV,6,5

2025-07-31 23:10:43:665 ==>> ,22,24,,,34,33,,,34,12,,,32,5,,,31,1*46

$GBGSV,6,6,22,2,,,31,4,,,30,1*71

$GBRMC,151047.503,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151047.503,0.000,1500.050,1500.050,48.001,2097152,2097152,2097152*5C

1A A1 20 00 00 
Get AD_V21 1017mV
OVER 150


2025-07-31 23:10:44:062 ==>> 本次取值间隔时间:447ms
2025-07-31 23:10:44:080 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:10:44:216 ==>> 1A A1 20 00 00 
Get AD_V21 1645mV
OVER 150


2025-07-31 23:10:44:366 ==>> 本次取值间隔时间:284ms
2025-07-31 23:10:44:389 ==>> 【AD_V21电压】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:44:393 ==>> 检测【关闭仪表供电2】
2025-07-31 23:10:44:398 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:10:44:697 ==>> $GBGGA,151048.503,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,39,,,40,34,,,39,3,,,39,1*4A

$GBGSV,6,2,22,59,,,39,60,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,22,41,,,38,11,,,38,16,,,37,43,,,37,1*76

$GBGSV,6,4,22,23,,,37,10,,,36,6,,,35,1,,,35,1*72

$GBGSV,6,5,22,24,,,34,33,,,34,12,,,32,5,,,31,1*46

$GBGSV,6,6,22,2,,,31,4,,,31,1*70

$GBRMC,151048.503,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151048.503,0.000,1505.697,1505.697,48.176,2097152,2097152,2097152*52

[W][05:18:13][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:13][COMM]set POWER 0
[D][05:18:13][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:10:44:933 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:10:44:936 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:10:44:940 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:10:45:018 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 23:10:45:108 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:13][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:10:45:217 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:10:45:220 ==>> 检测【打开AccKey2供电】
2025-07-31 23:10:45:225 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:10:45:400 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:10:45:496 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:10:45:500 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:10:45:504 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:45:670 ==>> $GBGGA,151049.503,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,39,,,39,34,,,39,3,,,39,1*44

$GBGSV,6,2,22,59,,,39,60,,,38,7,,,38,25,,,38,1*49

$GBGSV,6,3,22,41,,,38,43,,,38,11,,,37,16,,,37,1*76

$GBGSV,6,4,22,23,,,37,10,,,36,6,,,35,1,,,35,1*72

$GBGSV,6,5,22,24,,,34,33,,,34,12,,,32,5,,,31,1*46

$GBGSV,6,6,22,2,,,31,4,,,31,1*70

$GBRMC,151049.503,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151049.503,0.000,1501.924,1501.924,48.052,2097152,2097152,2097152*54



2025-07-31 23:10:45:895 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:14][COMM]adc read out 24v adc:1325  volt:33513 mv
[D][05:18:14][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read right brake adc:20  volt:26 mv
[D][05:18:14][COMM]adc read throttle adc:15  volt:19 mv
[D][05:18:14][COMM]adc read battery ts volt:17 mv
[D][05:18:14][COMM]adc read in 24v adc:1310  volt:33133 mv
[D][05:18:14][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1437  volt:33317 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:10:46:034 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33513mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:10:46:037 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:10:46:040 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:10:46:181 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:10:46:312 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:10:46:315 ==>> 该项需要延时执行
2025-07-31 23:10:46:656 ==>> $GBGGA,151050.503,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,38,7,,,38,25,,,38,1*48

$GBGSV,6,3,23,41,,,38,43,,,38,11,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,1,,,35,1*73

$GBGSV,6,5,23,24,,,35,33,,,34,12,,,33,9,,,33,1*49

$GBGSV,6,6,23,5,,,31,2,,,31,4,,,31,1*46

$GBRMC,151050.503,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151050.503,0.000,1503.317,1503.317,48.096,2097152,2097152,2097152*54



2025-07-31 23:10:47:035 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 23:10:47:671 ==>> $GBGGA,151051.503,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,38,7,,,38,25,,,38,1*48

$GBGSV,6,3,23,41,,,38,43,,,38,11,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,1,,,35,1*73

$GBGSV,6,5,23,24,,,35,33,,,34,12,,,33,9,,,33,1*49

$GBGSV,6,6,23,5,,,31,2,,,31,4,,,31,1*46

$GBRMC,151051.503,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151051.503,0.000,1503.317,1503.317,48.096,2097152,2097152,2097152*55



2025-07-31 23:10:48:664 ==>> $GBGGA,151052.503,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,39,7,,,38,25,,,38,1*49

$GBGSV,6,3,23,41,,,38,43,,,38,11,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,1,,,35,1*73

$GBGSV,6,5,23,24,,,35,33,,,34,12,,,33,9,,,33,1*49

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151052.503,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151052.503,0.000,1506.920,1506.920,48.209,2097152,2097152,2097152*52



2025-07-31 23:10:49:045 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 23:10:49:318 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:10:49:323 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:10:49:336 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:49:698 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:17][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:17][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:17][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:18][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:18][COMM]adc read battery ts volt:13 mv
[D][05:18:18][COMM]adc read in 24v adc:1311  volt:33159 mv
[D][05:18:18][COMM]adc read throttle brake in adc:11  volt:19 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,151053.503,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,39,7,,,38,25,,,38,1*49

$GBGSV,6,3,23,41,,,38,43,,,38,11,,,37,16,,,37,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,34,33,,,34,12,,,33,9,,,33,1*7F



2025-07-31 23:10:49:743 ==>> $GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151053.503,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151053.503,0.000,1503.316,1503.316,48.095,2097152,2097152,2097152*54



2025-07-31 23:10:49:861 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:10:49:864 ==>> 检测【打开AccKey1供电】
2025-07-31 23:10:49:867 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:10:50:001 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:18][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:10:50:142 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:10:50:145 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:10:50:151 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:10:50:228 ==>> 1A A1 00 40 00 
Get AD_V14 2644mV
OVER 150


2025-07-31 23:10:50:393 ==>> 原始值:【2644】, 乘以分压基数【2】还原值:【5288】
2025-07-31 23:10:50:422 ==>> 【读取AccKey1电压(ADV14)前】通过,【5288mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:10:50:426 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:10:50:442 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:50:778 ==>> $GBGGA,151054.503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,39,25,,,39,7,,,38,1*48

$GBGSV,6,3,23,41,,,38,43,,,38,11,,,37,16,,,37,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,30,1*44

$GBRMC,151054.503,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151054.503,0.000,1505.123,1505.123,48.157,2097152,2097152,2097152*5C

[D][05:18:19][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:19][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:19][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:19][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:19][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:19][COMM]adc read battery ts volt:17 mv
[D][05:18:19][COMM]adc read in 24v adc:1309  volt:33108 mv
[D][05:18:19][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:19][COMM]arm_hub adc read bat_

2025-07-31 23:10:50:823 ==>> id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:10:50:980 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5517mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:10:50:984 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:10:50:987 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:10:51:034 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 23:10:51:200 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:19][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:10:51:266 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:10:51:270 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:10:51:273 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:10:51:305 ==>> 1A A1 00 40 00 
Get AD_V14 2643mV
OVER 150


2025-07-31 23:10:51:531 ==>> 原始值:【2643】, 乘以分压基数【2】还原值:【5286】
2025-07-31 23:10:51:550 ==>> 【读取AccKey1电压(ADV14)后】通过,【5286mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:10:51:555 ==>> 检测【打开WIFI(2)】
2025-07-31 23:10:51:558 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:10:51:666 ==>> $GBGGA,151055.503,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,39,34,,,39,3,,,39,1*45

$GBGSV,6,2,23,59,,,39,60,,,39,25,,,38,7,,,38,1*49

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,37,16,,,37,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151055.503,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151055.503,0.000,1503.312,1503.312,48.091,2097152,2097152,2097152*56



2025-07-31 23:10:51:771 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:20][CAT1]gsm read msg sub id: 12
[D][05:18:20][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:20][CAT1]<<< 
OK


2025-07-31 23:10:51:802 ==>> 
[D][05:18:20][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:10:51:822 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:10:51:826 ==>> 检测【转刹把供电】
2025-07-31 23:10:51:832 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:10:51:982 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:10:52:131 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:10:52:137 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:10:52:142 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:10:52:241 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:10:52:287 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:10:52:317 ==>> 1A A1 00 80 00 
Get AD_V15 2387mV
OVER 150


2025-07-31 23:10:52:392 ==>> 原始值:【2387】, 乘以分压基数【2】还原值:【4774】
2025-07-31 23:10:52:410 ==>> 【读取AD_V15电压(前)】通过,【4774mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:10:52:416 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:10:52:425 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:10:52:512 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:10:52:708 ==>> 1A A1 01 00 00 
Get AD_V16 2413mV
OVER 150
$GBGGA,151056.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,39,25,,,38,7,,,38,1*49

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151056.503,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151056.503,0.000,1506.920,1506.920,48.209,2097152,2097152,2097152*56

[W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F62A7D2297A3,-65
+WIFISCAN:4,1,CC057790A7C1,-77
+WIFISCAN:4,2,44A1917CAD81,-79
+WIFISCAN:4,3,F86FB0660A82,-82

[D][05:18:21][CAT1]wifi scan report total[4]


2025-07-31 23:10:52:827 ==>> 原始值:【2413】, 乘以分压基数【2】还原值:【4826】
2025-07-31 23:10:52:852 ==>> 【读取AD_V16电压(前)】通过,【4826mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:10:52:860 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:10:52:876 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:53:138 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:21][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:21][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:21][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:21][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:21][COMM]adc read battery ts volt:20 mv
[D][05:18:21][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:21][COMM]adc read throttle brake in adc:3083  volt:5419 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:21][COMM]read battery soc:255


2025-07-31 23:10:53:243 ==>> [D][05:18:21][GNSS]recv submsg id[3]


2025-07-31 23:10:53:526 ==>> 【转刹把供电电压(主控ADC)】通过,【5419mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:10:53:532 ==>> 检测【转刹把供电电压】
2025-07-31 23:10:53:537 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:53:668 ==>> $GBGGA,151057.503,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,3,,,39,1*4B

$GBGSV,6,2,23,59,,,39,60,,,39,25,,,38,7,,,38,1*49

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151057.503,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151057.503,0.000,1506.920,1506.920,48.209,2097152,2097152,2097152*57



2025-07-31 23:10:53:893 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3142  volt:5523 mv
[D][05:18:22][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:22][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:22][COMM]adc read right brake adc:18  volt:23 mv
[D][05:18:22][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:22][COMM]adc read battery ts volt:21 mv
[D][05:18:22][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:22][COMM]adc read throttle brake in adc:3084  volt:5421 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:10:54:063 ==>> 【转刹把供电电压】通过,【5421mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:10:54:069 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:10:54:092 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:54:183 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:10:54:337 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:10:54:341 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:10:54:347 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:54:447 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:10:54:524 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:10:54:572 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:10:54:578 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:10:54:601 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:54:629 ==>> $GBGGA,151058.503,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,39,,,40,34,,,39,59,,,39,1*73

$GBGSV,6,2,24,60,,,39,25,,,39,3,,,38,7,,,38,1*71

$GBGSV,6,3,24,41,,,38,11,,,38,43,,,38,16,,,37,1*7F

$GBGSV,6,4,24,23,,,37,10,,,36,6,,,35,24,,,35,1*43

$GBGSV,6,5,24,1,,,35,33,,,34,12,,,33,9,,,33,1*79

$GBGSV,6,6,24,2,,,32,5,,,31,4,,,31,29,,,36,1*4C

$GBRMC,151058

2025-07-31 23:10:54:659 ==>> .503,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151058.503,0.000,1506.920,1506.920,48.209,2097152,2097152,2097152*58



2025-07-31 23:10:54:674 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:10:54:749 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:10:54:779 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:54:884 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:10:54:995 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:55:087 ==>> [D][05:18:23][COMM]read battery soc:255
[W][05:18:23][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:55:102 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:10:55:192 ==>> [W][05:18:23][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:10:55:208 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:10:55:222 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:10:55:312 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:10:55:387 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:10:55:417 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:10:55:459 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:10:55:466 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:10:55:494 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:10:55:522 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 23:10:55:627 ==>> $GBGGA,151059.503,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,59,,,39,1*74

$GBGSV,6,2,23,60,,,39,3,,,39,25,,,38,7,,,38,1*76

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,37,16,,,37,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45


2025-07-31 23:10:55:657 ==>> 
$GBRMC,151059.503,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151059.503,0.000,1505.117,1505.117,48.151,2097152,2097152,2097152*57



2025-07-31 23:10:55:741 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:10:55:745 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:10:55:765 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:10:55:822 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:10:56:035 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:10:56:041 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:10:56:049 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:10:56:122 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:10:56:331 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:10:56:338 ==>> 检测【左刹电压测试1】
2025-07-31 23:10:56:357 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:56:696 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3140  volt:5519 mv
[D][05:18:24][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:24][COMM]adc read left brake adc:1736  volt:2288 mv
[D][05:18:24][COMM]adc read right brake adc:1735  volt:2287 mv
[D][05:18:25][COMM]adc read throttle adc:1733  volt:2284 mv
[D][05:18:25][COMM]adc read battery ts volt:21 mv
[D][05:18:25][COMM]adc read in 24v adc:1310  volt:33133 mv
[D][05:18:25][COMM]adc read throttle brake in adc:16  volt:28 mv
[D][05:18:25][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:25][COMM]arm_hub adc read vbat adc:2436  volt:3925 mv
[D][05:18:25][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:25][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:25][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,151100.503,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,34,,,39,59,,,39,1*74

$GBGSV,6,2,23,60,,,39,3,,,39,25,,,38,7,,,38,1*76

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33

2025-07-31 23:10:56:741 ==>> ,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151100.503,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151100.503,0.000,1506.920,1506.920,48.209,2097152,2097152,2097152*54



2025-07-31 23:10:56:878 ==>> 【左刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 23:10:56:882 ==>> 检测【右刹电压测试1】
2025-07-31 23:10:56:937 ==>> 【右刹电压测试1】通过,【2287】符合目标值【2250】至【2500】要求!
2025-07-31 23:10:56:942 ==>> 检测【转把电压测试1】
2025-07-31 23:10:56:967 ==>> 【转把电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 23:10:56:973 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:10:56:981 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:10:57:026 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:10:57:057 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 23:10:57:265 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:10:57:269 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:10:57:272 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:10:57:318 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:10:57:569 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:10:57:576 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:10:57:583 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:10:57:680 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150
$GBGGA,151101.503,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,39,34,,,39,59,,,39,1*7A

$GBGSV,6,2,23,60,,,39,3,,,39,25,,,38,7,,,38,1*76

$GBGSV,6,3,23,41,,,38,11,,,38,43,,,37,16,,,37,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,6,,,35,24,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,9,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151101.503,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151101.503,0.000,750.250,750.250,686.121,2097152,2097152,2097152*68



2025-07-31 23:10:57:858 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:10:57:863 ==>> 检测【左刹电压测试2】
2025-07-31 23:10:57:869 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:10:58:124 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:26][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:26][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:26][COMM]adc read left brake adc:17  volt:22 mv
[D][05:18:26][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:26][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:26][COMM]adc read battery ts volt:11 mv
[D][05:18:26][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:18:26][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:26][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:26][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:26][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:26][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:26][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:10:58:384 ==>> 【左刹电压测试2】通过,【22】符合目标值【0】至【50】要求!
2025-07-31 23:10:58:389 ==>> 检测【右刹电压测试2】
2025-07-31 23:10:58:404 ==>> 【右刹电压测试2】通过,【14】符合目标值【0】至【50】要求!
2025-07-31 23:10:58:413 ==>> 检测【转把电压测试2】
2025-07-31 23:10:58:428 ==>> 【转把电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 23:10:58:441 ==>> 检测【晶振检测】
2025-07-31 23:10:58:446 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:10:58:695 ==>> $GBGGA,151102.503,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,59,,,39,34,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,9,,,33,12,,,33,1*7E

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151102.503,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151102.503,0.000,751.150,751.150,686.945,2097152,2097152,2097152*61

[W][05:18:27][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:27][COMM][lf state:1][hf state:1]


2025-07-31 23:10:58:968 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:10:58:972 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:10:58:978 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:10:59:030 ==>> 1A A1 00 00 FC 
Get AD_V2 1659mV
Get AD_V3 1661mV
Get AD_V4 1647mV
Get AD_V5 2746mV
Get AD_V6 2023mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:10:59:075 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 23:10:59:244 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:10:59:248 ==>> 检测【检测BootVer】
2025-07-31 23:10:59:253 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:10:59:587 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = ***************
[D][05:18:27][FCTY]HardwareID  = 867222087952927
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:27][FCTY]Bat         = 4064 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 33, adc = 1307
[D][05:18:27][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 75 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3818 mv
[D][05:18

2025-07-31 23:10:59:677 ==>> :27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 23:10:59:776 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:10:59:780 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:10:59:785 ==>> 检测【检测固件版本】
2025-07-31 23:10:59:813 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:10:59:818 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:10:59:822 ==>> 检测【检测蓝牙版本】
2025-07-31 23:10:59:831 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:10:59:835 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:10:59:842 ==>> 检测【检测MoBikeId】
2025-07-31 23:10:59:861 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:10:59:865 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:10:59:891 ==>> 检测【检测蓝牙地址】
2025-07-31 23:10:59:895 ==>> 取到目标值:E5AD143C355D
2025-07-31 23:10:59:901 ==>> 【检测蓝牙地址】通过,【E5AD143C355D】符合目标值【】要求!
2025-07-31 23:10:59:913 ==>> 提取到蓝牙地址:E5AD143C355D
2025-07-31 23:10:59:917 ==>> 检测【BOARD_ID】
2025-07-31 23:10:59:922 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:10:59:926 ==>> 检测【检测充电电压】
2025-07-31 23:10:59:942 ==>> 【检测充电电压】通过,【4064mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:10:59:947 ==>> 检测【检测VBUS电压1】
2025-07-31 23:10:59:964 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:10:59:969 ==>> 检测【检测充电电流】
2025-07-31 23:10:59:983 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:10:59:987 ==>> 检测【检测IMEI】
2025-07-31 23:10:59:993 ==>> 取到目标值:867222087952927
2025-07-31 23:11:00:010 ==>> 【检测IMEI】通过,【867222087952927】符合目标值【】要求!
2025-07-31 23:11:00:015 ==>> 提取到IMEI:867222087952927
2025-07-31 23:11:00:019 ==>> 检测【检测IMSI】
2025-07-31 23:11:00:024 ==>> 取到目标值:***************
2025-07-31 23:11:00:052 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 23:11:00:058 ==>> 提取到IMSI:***************
2025-07-31 23:11:00:078 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:11:00:082 ==>> 取到目标值:***************
2025-07-31 23:11:00:087 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 23:11:00:106 ==>> 检测【打开CAN通信】
2025-07-31 23:11:00:111 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:11:00:225 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:11:00:363 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:11:00:373 ==>> 检测【检测CAN通信】
2025-07-31 23:11:00:397 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:11:00:436 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:11:00:495 ==>> [D][05:18:28][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 40007
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:11:00:570 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:11:00:634 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:11:00:640 ==>> 检测【关闭CAN通信】
2025-07-31 23:11:00:645 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:11:00:675 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,151104.503,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,60,,,39,3,,,39,1*4A

$GBGSV,6,2,23,59,,,39,34,,,39,25,,,39,7,,,38,1*49

$GBGSV,6,3,23,16,,,38,11,,,38,43,,,38,41,,,38,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,9,,,34,33,,,34,12,,,33,1*79

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151104.503,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151104.503,0.000,754.751,754.751,690.237,2097152,2097152,2097152*6E

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:11:00:720 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:11:00:912 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:11:00:917 ==>> 检测【打印IMU STATE】
2025-07-31 23:11:00:925 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:11:01:116 ==>> [D][05:18:29][COMM]read battery soc:255
[W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:11:01:201 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:11:01:206 ==>> 检测【六轴自检】
2025-07-31 23:11:01:210 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:11:01:407 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:11:01:890 ==>> $GBGGA,151105.503,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,60,,,39,3,,,39,1*4A

$GBGSV,6,2,23,59,,,39,34,,,39,7,,,38,16,,,38,1*48

$GBGSV,6,3,23,25,,,38,11,,,38,43,,,38,41,,,38,1*77

$GBGSV,6,4,23,23,,,37,10,,,36,1,,,36,24,,,35,1*40

$GBGSV,6,5,23,6,,,35,9,,,34,33,,,34,2,,,33,1*4F

$GBGSV,6,6,23,12,,,33,5,,,31,4,,,31,1*75

$GBRMC,151105.503,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151105.503,0.000,755.645,755.645,691.055,2097152,2097152,2097152*68



2025-07-31 23:11:02:678 ==>> $GBGGA,151106.503,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,40,60,,,39,3,,,39,39,,,39,1*45

$GBGSV,6,2,23,59,,,39,34,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,2,,,33,9,,,33,1*4F

$GBGSV,6,6,23,12,,,33,5,,,31,4,,,31,1*75

$GBRMC,151106.503,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151106.503,0.000,751.144,751.144,686.938,2097152,2097152,2097152*6F



2025-07-31 23:11:03:110 ==>> [D][05:18:31][COMM]read battery soc:255
[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:11:03:322 ==>> [D][05:18:31][COMM]Main Task receive event:142
[D][05:18:31][COMM]###### 42814 imu self test OK ######
[D][05:18:31][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-8,-8,4065]
[D][05:18:31][COMM]Main Task receive event:142 finished processing


2025-07-31 23:11:03:539 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:11:03:544 ==>> 检测【打印IMU STATE2】
2025-07-31 23:11:03:552 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:11:03:772 ==>> $GBGGA,151107.503,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,40,60,,,39,3,,,39,39,,,39,1*45

$GBGSV,6,2,23,59,,,39,34,,,39,7,,,38,25,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,2,,,33,9,,,33,1*4F

$GBGSV,6,6,23,12,,,33,5,,,31,4,,,31,1*75

$GBRMC,151107.503,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151107.503,0.000,751.144,751.144,686.938,2097152,2097152,2097152*6E

[D][05:18:32][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:32][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:32][COMM]YAW data: 32763[32763]
[D][05:18:32][COMM]pitch:-66 roll:0
[D][05:18:32][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:11:03:864 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:11:03:869 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:11:03:876 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:11:03:923 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:11:04:028 ==>> [D][05:18:32][FCTY]get_ext_48v_vol retry i = 0,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 1,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry

2025-07-31 23:11:04:088 ==>>  i = 2,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 3,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 4,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 5,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 6,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 7,volt = 17
[D][05:18:32][FCTY]get_ext_48v_vol retry i = 8,volt = 17


2025-07-31 23:11:04:171 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:11:04:178 ==>> 检测【检测VBUS电压2】
2025-07-31 23:11:04:185 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:11:04:481 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = ***************
[D][05:18:32][FCTY]HardwareID  = 867222087952927
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:32][FCTY]Bat         = 4064 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 11800 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 7, adc = 310
[D][05:18:32][FCTY]Acckey1 vol = 5505 mv, Acckey2 vol = 252 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:3

2025-07-31 23:11:04:526 ==>> 2][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3818 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:11:04:712 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:11:05:089 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = ***************
[D][05:18:33][FCTY]HardwareID  = 867222087952927
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:33][FCTY]Bat         = 3944 mv
[D][05:18:33][FCTY]Current     = 50 ma
[D][05:18:33][FCTY]VBUS        = 7300 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 4, adc = 180
[D][05:18:33][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 404 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3818 mv
[D][05:18:33][FCTY]==============

2025-07-31 23:11:05:119 ==>> ====== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:11:05:242 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:11:05:604 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = ***************
[D][05:18:33][FCTY]HardwareID  = 867222087952927
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:33][FCTY]Bat         = 3944 mv
[D][05:18:33][FCTY]Current     = 50 ma
[D][05:18:33][FCTY]VBUS        = 7300 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 3, adc = 156
[D][05:18:33][FCTY]Acckey1 vol = 5519 mv, Acckey2 vol = 354 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]B

2025-07-31 23:11:05:649 ==>> at1         = 3818 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][COMM]msg 0601 loss. last_tick:40001. cur_tick:45002. period:500
[D][05:18:33][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 45002


2025-07-31 23:11:05:774 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:11:05:844 ==>> [D][05:18:34][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:34][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 23:11:06:344 ==>> [D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[W][05:18:34][GNSS]stop locating
[D][05:18:34][GNSS]stop event:8
[D][05:18:34][GNSS]GPS stop. ret=0
[D][05:18:34][GNSS]all continue location stop
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:34][PROT]index:0
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:4
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PR

2025-07-31 23:11:06:449 ==>> OT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900005]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][PROT]index:0 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:4
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900005]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[

2025-07-31 23:11:06:554 ==>> 198]
[D][05:18:34][CAT1]gsm read msg sub id: 24
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[W][05:18:34][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:34][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========
[D][05:18:34][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:34][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:34][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:34][FCTY]DeviceID    = ***************
[D][05:18:34][FCTY]HardwareID  = 867222087952927
[D][05:18:34][FCTY]MoBikeID    = 9999999999
[D][05:18:34][FCTY]LockID      = FFFFFFFFFF
[D][05:18:34][FCTY]BLEFWVersion= 105
[D][05:18:34][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:34][FCTY]Bat         = 3804 mv
[D][05:18:34][FCTY]Current     = 0 ma
[D][05:18:34][FCTY]VBUS        = 4900 mv
[D][05:18:34][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:34][FCTY]Ext battery vol = 3, adc = 131
[D][05:18:34][FCTY]Acckey1 vol = 5507 mv, Acckey2

2025-07-31 23:11:06:614 ==>>  vol = 354 mv
[D][05:18:34][FCTY]Bike Type flag is invalied
[D][05:18:34][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:34][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:34][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:34][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:34][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:34][FCTY]Bat1         = 3818 mv
[D][05:18:34][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:34][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:11:06:841 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:11:06:915 ==>> [D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:07:200 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:35][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:35][FCTY]==========Modules-nRF5340 ==========
[D][05:18:35][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:35][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:35][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:35][FCTY]DeviceID    = ***************
[D][05:18:35][FCTY]HardwareID  = 867222087952927
[D][05:18:35][FCTY]MoBikeID    = 9999999999
[D][05:18:35][FCTY]LockID      = FFFFFFFFFF
[D][05:18:35][FCTY]BLEFWVersion= 105
[D][05:18:35][FCTY]BLEMacAddr   = E5AD143C355D
[D][05:18:35][FCTY]Bat         = 3804 mv
[D][05:18:35][FCTY]Current     = 0 ma
[D][05:18:35][FCTY]VBUS        = 4900 mv
[D][05:18:35][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:35][FCTY]Ext battery vol = 2, adc = 112
[D][05:18:35][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 227 mv
[D][05:18:35][FCTY]Bike Type flag is invalied
[D][05:18:35][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:35][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:35][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:35][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:35][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:35][FCTY]CAT1_

2025-07-31 23:11:07:245 ==>> GNSS_VERSION = V3465b5b1
[D][05:18:35][FCTY]Bat1         = 3818 mv
[D][05:18:35][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:35][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:11:07:374 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:11:07:380 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:11:07:385 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:11:07:515 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:11:07:620 ==>> [D][05:18:36][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 23
[D][05:18:36][COMM]read battery soc:255


2025-07-31 23:11:07:692 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:11:07:698 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:11:07:703 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:11:07:817 ==>> 5A A5 04 5A A5 


2025-07-31 23:11:07:922 ==>> CLOSE_POWER_OUT2
OVER 150
[D][05:18:36][COMM]47441 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto

2025-07-31 23:11:07:952 ==>>  init
[D][05:18:36][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 23:11:07:980 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:11:07:985 ==>> 检测【打开WIFI(3)】
2025-07-31 23:11:07:993 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:11:08:180 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:11:08:259 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:11:08:264 ==>> 检测【扩展芯片hw】
2025-07-31 23:11:08:273 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:11:08:951 ==>> [D][05:18:37][COMM]48452 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:09:179 ==>> [D][05:18:37][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:37][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:0------------
[D][05:18:37][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:11:09:284 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:11:09:726 ==>>                                                                                                                           --
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]more than the number of battery plugs
[D][05:18:37][COMM]VBUS is 1
[D][05:18:37][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:37][COMM]file:B50 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:37][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:18:37][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:37][COMM]Bat auth off fail, error:-1
[D][05:18:37][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:37][COMM]----- get Acckey 1 and value:1------------
[D][05:18:37][COMM]----- get Acckey 2 and value:1------------
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:B50 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:37][COMM]f:[e

2025-07-31 23:11:09:831 ==>> c800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:37][COMM]read file, len:10800, num:3
[D][05:18:37][COMM]Main Task receive event:65
[D][05:18:37][COMM]main task tmp_sleep_event = 80
[D][05:18:37][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:37][COMM]Main Task receive event:65 finished processing
[D][05:18:37][COMM]Main Task receive event:66
[D][05:18:37][COMM]Try to Auto Lock Bat
[D][05:18:37][COMM]Main Task receive event:66 finished processing
[D][05:18:37][COMM]Main Task receive event:60
[D][05:18:37][COMM]smart_helmet_vol=255,255
[D][05:18:37][COMM]BAT CAN get state1 Fail 204
[D][05:18:37][COMM]BAT CAN get soc Fail, 204
[D][05:18:37][COMM]get soc error
[E][05:18:37][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:37][COMM]report elecbike
[W][05:18:37][PROT]remove success[1629955117],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:37][COMM]Main Task receive event:60 finished processing
[D][05:18:37][COMM]Main Task receive event:61
[D][05:18:37][COMM][D301]:type:3, trace id:280
[D][05:18:37][COMM]id[], hw[000
[

2025-07-31 23:11:09:936 ==>> D][05:18:37][COMM]get mcMaincircuitVolt error
[D][05:18:37][COMM]get mcSubcircuitVolt error
[D][05:18:37][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:37][COMM]BAT CAN get state1 Fail 204
[D][05:18:37][COMM]BAT CAN get soc Fail, 204
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:37][PROT]index:1
[D][05:18:37][COMM]Receive Bat Lock cmd 0
[D][05:18:37][PROT]is_send:1
[D][05:18:37][PROT]sequence_num:5
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_path:0x3
[D][05:18:37][PROT]msg_type:0x5d03
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]Sending traceid[9999999999900006]
[D][05:18:37][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:37][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:37][PROT]ble is not inited or not connected or

2025-07-31 23:11:10:041 ==>>  cccd not enabled
[D][05:18:37][COMM]VBUS is 1
[D][05:18:37][COMM]get bat work state err
[W][05:18:37][PROT]remove success[1629955117],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:37][COMM]Main Task receive event:61 finished processing
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:37][COMM]--->crc16:0xb8a
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:38][COMM]read battery soc:255
                                                                                  

2025-07-31 23:11:10:086 ==>>                                                                                                                                                                                                                    

2025-07-31 23:11:10:315 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:11:10:405 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:38][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:38][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:38][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:38][COMM]f:[drv_audio_ack_receive].wait ack timeout!![49839]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:38][GNSS]recv submsg id[1]
[D][05:18:38][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:38][GNSS]stop gps fail


2025-07-31 23:11:10:510 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:39][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:11:10:589 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:11:10:594 ==>> 检测【扩展芯片boot】
2025-07-31 23:11:10:607 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:11:10:612 ==>> 检测【扩展芯片sw】
2025-07-31 23:11:10:626 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:11:10:631 ==>> 检测【检测音频FLASH】
2025-07-31 23:11:10:639 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:11:10:782 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:11:10:964 ==>> [D][05:18:39][COMM]50473 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:11:376 ==>> [D][05:18:39][COMM]f:[drv_audio_ack_receive].wait ack timeout!![50868]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 23:11:11:618 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:11:11:968 ==>> [D][05:18:40][COMM]51485 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:12:399 ==>> [D][05:18:40][COMM]f:[drv_audio_ack_receive].wait ack timeout!![51896]
[D][05:18:40][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:11:12:986 ==>> [D][05:18:41][COMM]52496 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:13:653 ==>> [D][05:18:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:42][COMM]read battery soc:255


2025-07-31 23:11:14:006 ==>> [D][05:18:42][COMM]53507 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:14:683 ==>> [D][05:18:43][COMM]crc 108B
[D][05:18:43][COMM]flash test ok


2025-07-31 23:11:15:005 ==>> [D][05:18:43][COMM]54518 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:15:667 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:11:15:700 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:11:15:706 ==>> 检测【打开喇叭声音】
2025-07-31 23:11:15:714 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:11:15:911 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:44][COMM]file:A20 exist
[D][05:18:44][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:44][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 23:11:15:974 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:11:15:980 ==>> 检测【打开大灯控制】
2025-07-31 23:11:15:988 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:11:16:016 ==>> [D][05:18:44][COMM]55529 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:16:106 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:11:16:244 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:11:16:250 ==>> 检测【关闭仪表供电3】
2025-07-31 23:11:16:260 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:11:16:400 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:44][COMM]set POWER 0


2025-07-31 23:11:16:523 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:11:16:529 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:11:16:553 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:11:16:675 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:11:16:808 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:11:16:814 ==>> 检测【读大灯电压】
2025-07-31 23:11:16:819 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:11:17:029 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:45][COMM]arm_hub read adc[5],val[33085]
[D][05:18:45][COMM]56541 imu init OK
[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:11:17:095 ==>> 【读大灯电压】通过,【33085mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:11:17:103 ==>> 检测【关闭大灯控制2】
2025-07-31 23:11:17:125 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:11:17:288 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:11:17:367 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:11:17:372 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:11:17:397 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:11:17:501 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:46][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:11:17:650 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:11:17:656 ==>> 检测【打开WIFI(4)】
2025-07-31 23:11:17:682 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:11:17:691 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:11:17:801 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:11:18:028 ==>> [D][05:18:46][COMM]imu error,enter wait


2025-07-31 23:11:18:033 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:11:18:039 ==>> 检测【EC800M模组版本】
2025-07-31 23:11:18:058 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:19:069 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:19:661 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:11:19:843 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:20:102 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:20:132 ==>> [D][05:18:48][CAT1]exec over: func id: 15, ret: -93
[D][05:18:48][CAT1]sub id: 15, ret: -93

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:48][SAL ]socket send fail. id[4]
[D][05:18:48][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:48][CAT1]gsm read msg sub id: 12
[D][05:18:48][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:48][M2M ]m2m select fd[4]
[D][05:18:48][M2M ]socket[4] Link is disconnected
[D][05:18:48][M2M ]tcpclient close[4]
[D][05:18:48][SAL ]socket[4] has closed
[D][05:18:48][PROT]protocol read data ok
[E][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:48][PROT]M2M Send Fail [1629955128]
[D][05:18:48][PROT]CLEAN,SEND:0
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 23:11:21:127 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:21:136 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:21:817 ==>> [D][05:18:50][COMM]f:[drv_audio_ack_receive].wait ack timeout!![61153]
[D][05:18:50][COMM]accel parse set 0
[D][05:18:50][COMM][Audio].l:[1032].open hexlog save
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:50][COMM]file:A20 exist
[D][05:18:50][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:50][COMM]read file, len:15228, num:4
[D][05:18:50][COMM]read battery soc:255
[D][05:18:50][COMM]--->crc16:0x419c
[D][05:18:50][COMM]read file success
[W][05:18:50][COMM][Audio].l:[936].close hexlog save
[D][05:18:50][COMM]accel parse set 1
[D][05:18:50][COMM][Audio]mon:9,05:18:50
[D][05:18:50][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:11:21:907 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:22:165 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:22:755 ==>> [D][05:18:51][COMM]f:[drv_audio_ack_receive].wait ack timeout!![62253]
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:51][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:11:23:056 ==>> [D][05:18:51][CAT1]SEND RAW data timeout
[D][05:18:51][CAT1]exec over: func id: 12, ret: -52
[W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 23:11:23:191 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:23:676 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 23:11:23:781 ==>> [D][05:18:52][COMM]f:[drv_audio_ack_receive].wait ack timeout!![63281]
[D][05:18:52][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:52][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:11:24:217 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:24:811 ==>> [D][05:18:53][COMM]f:[drv_audio_ack_receive].wait ack timeout!![64310]
[D][05:18:53][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:11:25:070 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:25:251 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:25:687 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:11:26:082 ==>> [D][05:18:54][CAT1]SEND RAW data timeout
[D][05:18:54][CAT1]exec over: func id: 12, ret: -52
[W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:11:26:127 ==>>                                                                    

2025-07-31 23:11:26:294 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:27:333 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:27:704 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 23:11:28:086 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:28:378 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:28:626 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:29:086 ==>> [D][05:18:57][CAT1]SEND RAW data timeout
[D][05:18:57][CAT1]exec over: func id: 12, ret: -52
[W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:57][CAT1]gsm read msg sub id: 10
[D][05:18:57][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:11:29:425 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:29:705 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 23:11:30:470 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:31:134 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:31:519 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:31:702 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 23:11:32:555 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:33:169 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:33:588 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:33:618 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:33:723 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:11:34:637 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:35:197 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:35:673 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:35:718 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 23:11:36:115 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:36:717 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:37:165 ==>> [D][05:19:05][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:05][COMM]f:[drv_audio_ack_receive].wait ack timeout!![76651]
[D][05:19:05][COMM]accel parse set 0
[D][05:19:05][COMM][Audio].l:[1032].open hexlog save
[D][05:19:05][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 23:11:37:240 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:37:715 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 23:11:37:759 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:38:630 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:38:795 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:39:307 ==>> [D][05:19:07][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:39:736 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 23:11:39:841 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:40:869 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:41:117 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:41:347 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:41:744 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 23:11:41:910 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:42:935 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:43:398 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:43:626 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:43:731 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 23:11:43:975 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:45:019 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:45:096 ==>> [D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:11:45:443 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:45:743 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 23:11:46:043 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:46:120 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:47:076 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:47:502 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:47:760 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 23:11:48:115 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:11:48:624 ==>> [D][05:19:17][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:49:165 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 23:11:49:171 ==>> #################### 【测试结束】 ####################
2025-07-31 23:11:49:297 ==>> 关闭5V供电
2025-07-31 23:11:49:306 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:11:49:420 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:11:49:525 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:11:49:767 ==>> [D][05:19:18][COMM]read battery soc:255


2025-07-31 23:11:50:306 ==>> 关闭5V供电成功
2025-07-31 23:11:50:319 ==>> 关闭33V供电
2025-07-31 23:11:50:341 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:11:50:426 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:11:50:621 ==>> [D][05:19:19][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 8,volt = 13
[D][05:19:19][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 23:11:51:123 ==>> [D][05:19:19][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:11:51:318 ==>> 关闭33V供电成功
2025-07-31 23:11:51:337 ==>> 关闭3.7V供电
2025-07-31 23:11:51:346 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:11:51:423 ==>> 6A A6 02 A6 6A 


2025-07-31 23:11:51:513 ==>> Battery OFF
OVER 150


2025-07-31 23:11:51:997 ==>>  

2025-07-31 23:11:52:329 ==>> 关闭3.7V供电成功
