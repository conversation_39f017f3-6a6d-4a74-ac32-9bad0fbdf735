2025-07-31 20:45:27:142 ==>> MES查站成功:
查站序号:P5100010053132B9验证通过
2025-07-31 20:45:27:158 ==>> 扫码结果:P5100010053132B9
2025-07-31 20:45:27:160 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:45:27:162 ==>> 测试参数版本:2024.10.11
2025-07-31 20:45:27:163 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:45:27:165 ==>> 检测【打开透传】
2025-07-31 20:45:27:166 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:45:27:255 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:45:27:569 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:45:27:596 ==>> 检测【检测接地电压】
2025-07-31 20:45:27:597 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:45:27:652 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:45:27:878 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:45:27:880 ==>> 检测【打开小电池】
2025-07-31 20:45:27:883 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:45:27:958 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:45:28:169 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:45:28:171 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:45:28:174 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:45:28:261 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:45:28:458 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:45:28:462 ==>> 检测【等待设备启动】
2025-07-31 20:45:28:464 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:45:28:779 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:45:28:945 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:45:29:500 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:45:29:591 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:45:29:651 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:45:30:050 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:45:30:539 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:45:30:542 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:45:30:828 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:45:30:830 ==>> 检测【产品通信】
2025-07-31 20:45:30:832 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:45:31:045 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 20:45:31:117 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:45:31:119 ==>> 检测【初始化完成检测】
2025-07-31 20:45:31:124 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:45:31:150 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:45:31:391 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:45:31:587 ==>> [D][05:17:51][COMM]2623 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:31:657 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:45:31:659 ==>> 检测【关闭大灯控制1】
2025-07-31 20:45:31:660 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:45:31:691 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[16

2025-07-31 20:45:31:736 ==>> 29955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:45:31:841 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:45:31:946 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:45:31:950 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:45:31:953 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:45:32:147 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:45:32:234 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:45:32:236 ==>> 检测【关闭仪表供电】
2025-07-31 20:45:32:239 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:45:32:450 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:45:32:520 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:45:32:523 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:45:32:526 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:45:32:555 ==>> [D][05:17:52][COMM]3634 imu init 

2025-07-31 20:45:32:585 ==>> OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:32:720 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:45:32:812 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:45:32:815 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:45:32:817 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:45:33:026 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:45:33:114 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:45:33:118 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:45:33:120 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:45:33:316 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:45:33:405 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:45:33:407 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:45:33:408 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:45:33:451 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:45:33:616 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4645 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:45:33:689 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:45:33:692 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:45:33:693 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:45:33:751 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:45:34:016 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:45:34:019 ==>> 该项需要延时执行
2025-07-31 20:45:34:145 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5009. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5010. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5010. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5011. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5011. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5012. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5012
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 20:45:34:605 ==>> [D][05:17:54][COMM]5656 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:35:122 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:45:35:698 ==>>                                      ice_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1

2025-07-31 20:45:35:802 ==>> ------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM

2025-07-31 20:45:35:907 ==>> ]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:5

2025-07-31 20:45:35:982 ==>> 5][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6667 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:36:646 ==>> [D][05:17:56][COMM]7678 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:37:653 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8689 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:45:38:022 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:45:38:025 ==>> 检测【33V输入电压ADC】
2025-07-31 20:45:38:028 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:38:369 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:17:58][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:58][COMM]adc read battery ts volt:5 mv
[D][05:17:58][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:45:38:567 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:45:38:569 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:45:38:571 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:45:38:674 ==>> [D][05:17:58][COMM]9700 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2785mV
Get AD_V6 1990mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:45:38:860 ==>> 【TP7_VCC3V3(ADV2)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:38:862 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:45:38:896 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:38:899 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:45:38:901 ==>> 原始值:【2785】, 乘以分压基数【2】还原值:【5570】
2025-07-31 20:45:38:933 ==>> 【TP68_VCC5V5(ADV5)】通过,【5570mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:45:38:935 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:45:38:970 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:45:38:991 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:45:39:012 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:45:39:014 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:45:39:034 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10023


2025-07-31 20:45:39:139 ==>> 1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 

2025-07-31 20:45:39:169 ==>> 2786mV
Get AD_V6 1991mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:45:39:307 ==>> 【TP7_VCC3V3(ADV2)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:39:310 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:45:39:337 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:39:339 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:45:39:341 ==>> 原始值:【2786】, 乘以分压基数【2】还原值:【5572】
2025-07-31 20:45:39:368 ==>> 【TP68_VCC5V5(ADV5)】通过,【5572mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:45:39:370 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:45:39:397 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:45:39:400 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:45:39:431 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:45:39:434 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:45:39:574 ==>> 1A A1 00 00 FC 
Get AD_V2 1651mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2782mV
Get AD_V6 1988mV
Get AD_V7 1087mV
OVER 150


2025-07-31 20:45:39:729 ==>> 【TP7_VCC3V3(ADV2)】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:39:731 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:45:39:757 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:39:773 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:45:39:776 ==>> 原始值:【2782】, 乘以分压基数【2】还原值:【5564】
2025-07-31 20:45:39:786 ==>> 【TP68_VCC5V5(ADV5)】通过,【5564mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:45:39:791 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:45:39:813 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:45:39:817 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:45:39:849 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:45:39:852 ==>> 检测【打开WIFI(1)】
2025-07-31 20:45:39:854 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:45:39:876 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10710 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time e

2025-07-31 20:45:39:921 ==>> rr 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 20:45:40:423 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087736387

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539126

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:45:40:645 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:45:40:648 ==>> 检测【清空消息队列(1)】
2025-07-31 20:45:40:651 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:45:40:654 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:45:40:909 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:45:41:189 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:45:41:192 ==>> 检测【打开GPS(1)】
2025-07-31 20:45:41:195 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:45:41:346 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 20:45:41:478 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:45:41:483 ==>> 检测【打开GSM联网】
2025-07-31 20:45:41:486 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:45:42:044 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv g

2025-07-31 20:45:42:149 ==>> ms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][COMM]12724 imu init OK
[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.243.76.180"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20

2025-07-31 20:45:42:224 ==>> ] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:45:42:282 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:45:42:286 ==>> 检测【打开仪表供电1】
2025-07-31 20:45:42:289 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:45:42:450 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:45:42:570 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:45:42:576 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:45:42:579 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:45:42:645 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:45:42:750 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:45:42:852 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:45:42:858 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:45:42:863 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:45:43:050 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub read adc[3],val[33178]


2025-07-31 20:45:43:198 ==>> 【读取主控ADC采集的仪表电压】通过,【33178mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:45:43:201 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:45:43:205 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:43:537 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,43,24,,,40,39,,,40,60,,,40,1*74

$GBGSV,3,2,09,42,,,39,41,,,37,40,,,37,25,,,41,1*71

$GBGSV,3,3,09,14,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1634.588,1634.588,52.237,2097152,2097152,2097152*4E

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:45:43:642 ==>>                                          [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:45:43:767 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:45:43:771 ==>> 检测【AD_V20电压】
2025-07-31 20:45:43:775 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:43:868 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:43:959 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:45:44:019 ==>> 本次取值间隔时间:136ms
2025-07-31 20:45:44:069 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:44:172 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:44:279 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[D][05:18:04][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:44:444 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,43,25,,,41,24,,,41,39,,,40,1*7C

$GBGSV,4,2,16,60,,,40,14,,,39,42,,,39,41,,,38,1*78

$GBGSV,4,3,16,40,,,38,9,,,37,16,,,35,44,,,33,1*45

$GBGSV,4,4,16,3,,,43,38,,,41,13,,,37,4,,,36,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1603.038,1603.038,51.264,2097152,2097152,2097152*4B



2025-07-31 20:45:44:489 ==>> 本次取值间隔时间:312ms
2025-07-31 20:45:44:523 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:44:624 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:44:853 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:45:003 ==>> 本次取值间隔时间:373ms
2025-07-31 20:45:45:186 ==>> 本次取值间隔时间:182ms
2025-07-31 20:45:45:472 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,43,25,,,41,24,,,41,60,,,41,1*74

$GBGSV,6,2,22,59,,,41,3,,,40,39,,,40,14,,,40,1*43

$GBGSV,6,3,22,42,,,39,1,,,39,41,,,38,40,,,38,1*45

$GBGSV,6,4,22,13,,,37,9,,,37,16,,,36,4,,,35,1*7F

$GBGSV,6,5,22,5,,,35,44,,,34,2,,,34,34,,,34,1*74

$GBGSV,6,6,22,38,,,33,23,,,52,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1571.464,1571.464,50.269,2097152,2097152,2097152*47



2025-07-31 20:45:45:577 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:45:45:652 ==>> 本次取值间隔时间:453ms
2025-07-31 20:45:45:864 ==>> 本次取值间隔时间:206ms
2025-07-31 20:45:45:868 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:45:972 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:46:064 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:45:46:259 ==>> 本次取值间隔时间:285ms
2025-07-31 20:45:46:294 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:46:395 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:46:485 ==>> 本次取值间隔时间:87ms
2025-07-31 20:45:46:545 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
$GBGGA,124550.295,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,25,,,41,24,,,41,60,,,41,1*71

$GBGSV,7,2,26,59,,,41,3,,,40,39,,,40,14,,,39,1*48

$GBGSV,7,3,26,42,,,39,1,,,39,41,,,38,40,,,38,1*40

$GBGSV,7,4,26,13,,,37,9,,,37,16,,,37,5,,,35,1*7A

$GBGSV,7,5,26,2,,,35,4,,,34,44,,,34,34,,,34,1*70

$GBGSV,7,6,26,38,,,34,23,,,32,10,,,31,7,,,7,1*7C

$GBGSV,7,7,26,12,,,38,6,,,37,1*48

$GBRMC,124550.295,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124550.295,0.000,1497.882,1497.882,48.113,2097152,2097152,2097152*57

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:46:590 ==>> 本次取值间隔时间:96ms
2025-07-31 20:45:46:618 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:46:729 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:46:803 ==>> $GBGGA,124550.595,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,60,,,41,1*70

$GBGSV,7,2,27,59,,,41,3,,,41,39,,,40,14,,,39,1*48

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,13,,,37,9,,,37,16,,,37,6,,,35,1*78

$GBGSV,7,5,27,5,,,35,2,,,35,38,,,35,4,,,34,1*48

$GBGSV,7,6,27,44,,,34,34,,,34,7,,,34,23,,,32,1*45

$GBGSV,7,7,27,10,,,31,26,,,0,12,,,37,1*43

$GBRMC,124550.595,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124550.595,0.000,1484.776,1484.776,47.743,2097152,2097152,2097152*5C



2025-07-31 20:45:46:810 ==>> 本次取值间隔时间:73ms
2025-07-31 20:45:46:908 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:46:967 ==>> 本次取值间隔时间:155ms
2025-07-31 20:45:47:441 ==>> 本次取值间隔时间:460ms
2025-07-31 20:45:47:546 ==>> 本次取值间隔时间:102ms
2025-07-31 20:45:47:554 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:45:47:651 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:45:47:819 ==>> [D][05:18:07][COMM]read battery soc:255
[D][05:18:07][COMM]S->M yaw:INVALID
$GBGGA,124551.575,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,60,,,41,1*70

$GBGSV,7,2,27,59,,,41,3,,,41,39,,,40,14,,,40,1*46

$GBGSV,7,3,27,42,,,39,1,,,38,41,,,38,40,,,38,1*40

$GBGSV,7,4,27,13,,,37,16,,,37,9,,,36,6,,,36,1*7A

$GBGSV,7,5,27,2,,,36,38,,,36,7,,,35,8,,,35,1*47

$GBGSV,7,6,27,5,,,34,4,,,34,44,,,34,26,,,34,1*77

$GBGSV,7,7,27,34,,,33,23,,,33,10,,,33,1*74

$GBRMC,124551.575,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124551.575,0.000,1541.639,1541.639,49.322,2097152,2097152,2097152*5E

[W][05:18:07][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 20:45:48:077 ==>> 本次取值间隔时间:421ms
2025-07-31 20:45:48:106 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:48:110 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:45:48:112 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:45:48:154 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:45:48:423 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:45:48:427 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:45:48:433 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:45:48:806 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,124552.555,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,40,39,,,40,14,,,39,1*43

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,13,,,37,16,,,37,9,,,36,6,,,36,1*7A

$GBGSV,7,5,27,2,,,36,38,,,36,7,,,36,8,,,35,1*44

$GBGSV,7,6,27,4,,,35,5,,,34,44,,,34,26,,,33,1*71

$GBGSV,7,7,27,34,,,33,23,,,33,10,,,33,1*74

$GBRMC,124552.555,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124552.555,0.000,1541.636,1541.636,49.319,2097152,2097152,2097152*57

[D][05:18:08][COMM]M->S yaw:INVALID


2025-07-31 20:45:48:984 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:45:48:988 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:45:48:992 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:45:49:049 ==>> 3A A3 02 01 A3 


2025-07-31 20:45:49:154 ==>> ON_OUT2
OVER 150


2025-07-31 20:45:49:277 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:45:49:281 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:45:49:285 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:45:49:475 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:49:565 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:45:49:573 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:45:49:578 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:45:49:820 ==>> [D][05:18:09][COMM]read battery soc:255
$GBGGA,124553.535,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,40,14,,,39,1*42

$GBGSV,7,3,27,42,,,39,1,,,38,41,,,38,40,,,38,1*40

$GBGSV,7,4,27,13,,,37,16,,,37,9,,,36,6,,,36,1*7A

$GBGSV,7,5,27,2,,,36,38,,,36,7,,,36,8,,,35,1*44

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,26,,,33,34,,,33,23,,,33,1*71

$GBRMC,124553.535,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124553.535,0.000,1538.561,1538.561,49.217,2097152,2097152,2097152*5F

[D][05:18:09][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:45:50:109 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:45:50:114 ==>> 检测【AD_V21电压】
2025-07-31 20:45:50:117 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:45:50:265 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 20:45:50:462 ==>> 本次取值间隔时间:351ms
2025-07-31 20:45:50:495 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:45:50:498 ==>> 检测【关闭仪表供电2】
2025-07-31 20:45:50:502 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:45:50:769 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:10][COMM]set POWER 0
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,124554.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,39,,,40,14,,,40,1*4C

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,9,,,37,2,,,37,1*71

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,35,5,,,34,44,,,34,10,,,34,1*73

$GBGSV,7,7,27,26,,,33,34,,,33,23,,,33,1*71

$GBRMC,124554.515,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124554.515,0.000,1550.848,1550.848,49.613,2097152,2097152,2097152*5A



2025-07-31 20:45:51:046 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:45:51:050 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:45:51:053 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:45:51:253 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:11][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:45:51:350 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:45:51:354 ==>> 检测【打开AccKey2供电】
2025-07-31 20:45:51:357 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:45:51:524 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:45:51:659 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:45:51:664 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:45:51:667 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:51:749 ==>> [D][05:18:11][COMM]read battery soc:255
$GBGGA,124555.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,39,,,40,14,,,39,1*42

$GBGSV,7,3,27,42,,,39,1,,,38,41,,,38,40,,,38,1*40

$GBGSV,7,4,27,16,,,37,13,,,37,9,,,37,2,,,37,1*7E

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,26,,,33,34,,,33,23,,,33,1*71

$GBRMC,124555.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124555.515,0.000,1544.706,1544.706,49.416,2097152,2097152,2097152*5C



2025-07-31 20:45:51:975 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:11][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:18:11][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:7 mv
[D][05:18:11][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3351  volt:2699 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:45:52:222 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33234mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:45:52:225 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:45:52:230 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:45:52:445 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:45:52:541 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:45:52:544 ==>> 该项需要延时执行
2025-07-31 20:45:52:764 ==>> $GBGGA,124556.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,40,39,,,40,14,,,39,1*43

$GBGSV,7,3,27,42,,,39,1,,,38,41,,,38,40,,,38,1*40

$GBGSV,7,4,27,16,,,37,13,,,37,9,,,36,2,,,36,1*7E

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,34,1*41

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,26,,,33,34,,,33,23,,,33,1*71

$GBRMC,124556.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124556.515,0.000,1538.565,1538.565,49.221,2097152,2097152,2097152*5D

                                     

2025-07-31 20:45:53:727 ==>> $GBGGA,124557.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,40,39,,,40,14,,,39,1*43

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,37,13,,,37,9,,,36,2,,,36,1*7E

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,35,5,,,34,44,,,34,10,,,34,1*73

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124557.515,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124557.515,0.000,1544.702,1544.702,49.412,2097152,2097152,2097152*5A

[D][05:18:13][COMM]read battery soc:255


2025-07-31 20:45:53:802 ==>> [D][05:18:13][COMM]M->S yaw:INVALID


2025-07-31 20:45:54:731 ==>> $GBGGA,124558.515,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,40,39,,,40,14,,,40,1*4D

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,9,,,37,2,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,35,5,,,34,44,,,34,10,,,34,1*73

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124558.515,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124558.515,0.000,1549.309,1549.309,49.560,2097152,2097152,2097152*51



2025-07-31 20:45:55:553 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:45:55:558 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:45:55:562 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:55:886 ==>> $GBGGA,124559.515,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,39,,,40,14,,,40,1*4C

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,9,,,37,2,,,36,1*70

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124559.515,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124559.515,0.000,1549.312,1549.312,49.564,2097152,2097152,2097152*54

[D][05:18:15][COMM]read battery soc:255
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:3 mv
[D][05:18:15][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:15][COMM]arm_hub adc rea

2025-07-31 20:45:55:916 ==>> d led yb adc:1431  volt:33178 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3351  volt:2699 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:45:56:100 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:45:56:105 ==>> 检测【打开AccKey1供电】
2025-07-31 20:45:56:110 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:45:56:250 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:45:56:389 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:45:56:393 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:45:56:397 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:45:56:461 ==>> 1A A1 00 40 00 
Get AD_V14 2611mV
OVER 150


2025-07-31 20:45:56:642 ==>> 原始值:【2611】, 乘以分压基数【2】还原值:【5222】
2025-07-31 20:45:56:679 ==>> 【读取AccKey1电压(ADV14)前】通过,【5222mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:45:56:699 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:45:56:705 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:56:732 ==>> $GBGGA,124600.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,39,,,40,14,,,40,1*4C

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,9,,,37,2,,,37,1*71

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124600.515,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124600.515,0.000,1550.847,1550.847,49.612,2097152,2097152,2097152*59



2025-07-31 20:45:56:972 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:16][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:16][COMM]adc read battery ts volt:3 mv
[D][05:18:16][COMM]adc read in 24v adc:1279  volt:32349 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:45:57:235 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5554mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:45:57:239 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:45:57:242 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:45:57:426 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:17][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:45:57:516 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:45:57:520 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:45:57:522 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:45:57:651 ==>> 1A A1 00 40 00 
Get AD_V14 2606mV
OVER 150


2025-07-31 20:45:57:756 ==>> [D][05:18:17][COMM]read battery soc:255
$GBGGA,124601.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,41,24,,,41,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,39,,,40,14,,,40,1*4C

$GBGSV,7,3,27,42,,,39,1,,,39,41,,,38,40,,,38,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,9,,,37,2,,,37,1*71

$GBGSV,7,5,27,6,,,36,38,,,36,7,,,36,8,,,35,1*40

$GBGSV,7,6,27,4,,,34,5,,,34,44,,,34,10,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124601.515,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124601.515,0.000,773.974,773.974,707.816,2097152,2097152,2097152*6F



2025-07-31 20:45:57:771 ==>> 原始值:【2606】, 乘以分压基数【2】还原值:【5212】
2025-07-31 20:45:57:804 ==>> 【读取AccKey1电压(ADV14)后】通过,【5212mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:45:57:811 ==>> 检测【打开WIFI(2)】
2025-07-31 20:45:57:817 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:45:57:981 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:18][CAT1]<<< 
OK

[D][05:18:18][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:45:58:089 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:45:58:092 ==>> 检测【转刹把供电】
2025-07-31 20:45:58:095 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:45:58:221 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:45:58:390 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:45:58:394 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:45:58:396 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:45:58:496 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:45:58:557 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2425mV
OVER 150


2025-07-31 20:45:58:647 ==>> 原始值:【2425】, 乘以分压基数【2】还原值:【4850】
2025-07-31 20:45:58:677 ==>> 【读取AD_V15电压(前)】通过,【4850mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:45:58:682 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:45:58:686 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:45:58:782 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:45:58:788 ==>> $GBGGA,124602.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,59,,,41,24,,,41,1*4E

$GBGSV,7,2,27,25,,,41,60,,,40,39,,,40,14,,,40,1*79

$GBGSV,7,3,27,42,,,39,40,,,38,1,,,38,16,,,38,1*42

$GBGSV,7,4,27,41,,,38,13,,,37,9,,,37,2,,,36,1*72

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,8,,,35,1*40

$GBGSV,7,6,27,10,,,34,5,,,34,44,,,34,4,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124602.515,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124602.515,0.000,771.673,771.673,705.712,2097152,2097152,2097152*65

[D][05:18:18][COMM]S->M yaw:INVALID
+WIFISCAN:4,0,CC057790A5C0,-71
+WIFISCAN:4,1,CC057790A741,-71
+WIFISCAN:4,2,CC057790A740,-72
+WIFISCAN:4,3,CC057790A5C1,-81

[D][05:18:18][CAT1]wifi scan report total[4]
[D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 20:45:58:872 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2454mV
OVER 150


2025-07-31 20:45:58:933 ==>> 原始值:【2454】, 乘以分压基数【2】还原值:【4908】
2025-07-31 20:45:59:040 ==>> 【读取AD_V16电压(前)】通过,【4908mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:45:59:043 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:45:59:046 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:59:372 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:4 mv
[D][05:18:19][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3099  volt:5447 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3351  volt:2699 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:45:59:626 ==>> 【转刹把供电电压(主控ADC)】通过,【5447mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:45:59:630 ==>> 检测【转刹把供电电压】
2025-07-31 20:45:59:632 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:45:59:749 ==>> $GBGGA,124603.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,27,25,,,41,60,,,40,39,,,40,14,,,40,1*79

$GBGSV,7,3,27,1,,,39,42,,,39,40,,,38,16,,,38,1*43

$GBGSV,7,4,27,41,,,38,2,,,37,13,,,37,9,,,37,1*73

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,8,,,35,1*40

$GBGSV,7,6,27,10,,,34,5,,,34,44,,,34,4,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124603.515,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124603.515,0.000,773.975,773.975,707.817,2097152,2097152,2097152*6C

[D][05:18:19][COMM]read battery soc:255
[D][05:18:19][COMM]M->S yaw:INVALID


2025-07-31 20:45:59:975 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3166  volt:5565 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:19][COMM]adc read battery ts volt:6 mv
[D][05:18:19][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3095  volt:5440 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:46:00:174 ==>> 【转刹把供电电压】通过,【5440mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:46:00:179 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:46:00:184 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:46:00:321 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:46:00:492 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:46:00:502 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:46:00:506 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:46:00:598 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:46:00:659 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:46:00:742 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:46:00:745 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:46:00:756 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:46:00:782 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,124604.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,25,,,41,39,,,40,14,,,40,1*78

$GBGSV,7,3,27,40,,,39,1,,,39,42,,,39,16,,,38,1*42

$GBGSV,7,4,27,41,,,38,13,,,37,2,,,36,7,,,36,1*7D

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,8,,,35,1*4E

$GBGSV,7,6,27,4,,,35,10,,,34,5,,,34,44,,,34,1*73

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124604.515,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124604.515,0.000,773.974,773.974,707.816,2097152,2097152,2097152*6A



2025-07-31 20:46:00:854 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:46:00:960 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:46:00:975 ==>> [D][05:18:20][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:20][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:46:01:065 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:46:01:125 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:46:01:155 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:46:01:205 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:46:01:209 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:46:01:212 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:46:01:260 ==>> 3A A3 03 01 A3 


2025-07-31 20:46:01:350 ==>> ON_OUT3
OVER 150


2025-07-31 20:46:01:531 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:46:01:535 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:46:01:538 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:46:01:665 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:46:01:755 ==>> $GBGGA,124605.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,59,,,41,24,,,41,1*4E

$GBGSV,7,2,27,25,,,41,60,,,40,39,,,40,14,,,40,1*79

$GBGSV,7,3,27,1,,,39,42,,,39,40,,,38,16,,,38,1*43

$GBGSV,7,4,27,41,,,38,13,,,37,9,,,37,2,,,36,1*72

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,8,,,35,1*40

$GBGSV,7,6,27,5,,,35,4,,,35,10,,,34,44,,,34,1*72

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124605.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124605.515,0.000,773.969,773.969,707.811,2097152,2097152,2097152*6C

[D][05:18:21][COMM]read battery soc:255


2025-07-31 20:46:01:853 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:46:01:857 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:46:01:862 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:46:01:951 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:46:02:176 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:46:02:180 ==>> 检测【左刹电压测试1】
2025-07-31 20:46:02:186 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:46:02:475 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1715  volt:2260 mv
[D][05:18:22][COMM]adc read right brake adc:1713  volt:2258 mv
[D][05:18:22][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:22][COMM]adc read battery ts volt:4 mv
[D][05:18:22][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2405  volt:3875 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:46:02:727 ==>> 【左刹电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 20:46:02:731 ==>> 检测【右刹电压测试1】
2025-07-31 20:46:02:748 ==>> $GBGGA,124606.515,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,39,,,40,14,,,40,1*77

$GBGSV,7,3,28,42,,,39,40,,,38,1,,,38,41,,,38,1*4F

$GBGSV,7,4,28,13,,,37,9,,,37,16,,,37,2,,,36,1*70

$GBGSV,7,5,28,7,,,36,38,,,36,6,,,36,10,,,34,1*77

$GBGSV,7,6,28,8,,,34,5,,,34,44,,,34,4,,,34,1*44

$GBGSV,7,7,28,34,,,34,26,,,33,23,,,33,36,,,37,1*78

$GBRMC,124606.515,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124606.515,0.000,770.911,770.911,705.015,2097152,2097152,2097152*61



2025-07-31 20:46:02:776 ==>> 【右刹电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 20:46:02:780 ==>> 检测【转把电压测试1】
2025-07-31 20:46:02:819 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:46:02:824 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:46:02:828 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:46:02:958 ==>> 3A A3 03 00 A3 


2025-07-31 20:46:03:063 ==>> OFF_OUT3
OVER 150


2025-07-31 20:46:03:105 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:46:03:111 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:46:03:119 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:46:03:153 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:46:03:410 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:46:03:414 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:46:03:421 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:46:03:564 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:46:03:705 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:46:03:711 ==>> 检测【左刹电压测试2】
2025-07-31 20:46:03:719 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:46:03:744 ==>> $GBGGA,124607.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,25,,,41,39,,,40,14,,,40,1*78

$GBGSV,7,3,27,1,,,39,42,,,39,40,,,38,41,,,38,1*41

$GBGSV,7,4,27,13,,,37,9,,,37,16,,,37,2,,,36,1*7F

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,10,,,34,1*78

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124607.515,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124607.515,0.000,771.678,771.678,705.717,2097152,2097152,2097152*65

[D][05:18:23][COMM]read battery soc:255


2025-07-31 20:46:03:969 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:23][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:23][COMM]adc read battery ts volt:7 mv
[D][05:18:23][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2404  volt:3873 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3352  volt:2700 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:46:04:257 ==>> 【左刹电压测试2】通过,【2】符合目标值【0】至【50】要求!
2025-07-31 20:46:04:262 ==>> 检测【右刹电压测试2】
2025-07-31 20:46:04:287 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:46:04:291 ==>> 检测【转把电压测试2】
2025-07-31 20:46:04:313 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:46:04:317 ==>> 检测【晶振检测】
2025-07-31 20:46:04:323 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:46:04:524 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]


2025-07-31 20:46:04:596 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:46:04:602 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:46:04:609 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:46:04:736 ==>> 1A A1 00 00 FC 
Get AD_V2 1650mV
Get AD_V3 1660mV
Get AD_V4 1643mV
Get AD_V5 2785mV
Get AD_V6 1989mV
Get AD_V7 1087mV
OVER 150
$GBGGA,124608.515,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,24,,,41,25,,,41,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,40,1,,,39,1*49

$GBGSV,7,3,27,42,,,39,14,,,39,40,,,38,41,,,38,1*75

$GBGSV,7,4,27,13,,,37,9,,,37,16,,,37,2,,,36,1*7F

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,10,,,34,1*78

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,33,1*76

$GBRMC,124608.515,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124608.515,0.000,769.373,769.373,703.609,2097152,2097152,2097152*62



2025-07-31 20:46:04:883 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:46:04:887 ==>> 检测【检测BootVer】
2025-07-31 20:46:04:891 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:46:05:212 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539126
[D][05:18:25][FCTY]HardwareID  = 867222087736387
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C6DD9F8AF215
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11700 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1290
[D][05:18:25][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D]

2025-07-31 20:46:05:257 ==>> [05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3815 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:46:05:420 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:46:05:425 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:46:05:431 ==>> 检测【检测固件版本】
2025-07-31 20:46:05:444 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:46:05:448 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:46:05:451 ==>> 检测【检测蓝牙版本】
2025-07-31 20:46:05:469 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:46:05:473 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:46:05:477 ==>> 检测【检测MoBikeId】
2025-07-31 20:46:05:493 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:46:05:500 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:46:05:504 ==>> 检测【检测蓝牙地址】
2025-07-31 20:46:05:510 ==>> 取到目标值:C6DD9F8AF215
2025-07-31 20:46:05:520 ==>> 【检测蓝牙地址】通过,【C6DD9F8AF215】符合目标值【】要求!
2025-07-31 20:46:05:526 ==>> 提取到蓝牙地址:C6DD9F8AF215
2025-07-31 20:46:05:531 ==>> 检测【BOARD_ID】
2025-07-31 20:46:05:551 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:46:05:555 ==>> 检测【检测充电电压】
2025-07-31 20:46:05:584 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:46:05:588 ==>> 检测【检测VBUS电压1】
2025-07-31 20:46:05:606 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:46:05:613 ==>> 检测【检测充电电流】
2025-07-31 20:46:05:631 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:46:05:638 ==>> 检测【检测IMEI】
2025-07-31 20:46:05:645 ==>> 取到目标值:867222087736387
2025-07-31 20:46:05:657 ==>> 【检测IMEI】通过,【867222087736387】符合目标值【】要求!
2025-07-31 20:46:05:678 ==>> 提取到IMEI:867222087736387
2025-07-31 20:46:05:691 ==>> 检测【检测IMSI】
2025-07-31 20:46:05:697 ==>> 取到目标值:460130071539126
2025-07-31 20:46:05:703 ==>> 【检测IMSI】通过,【460130071539126】符合目标值【】要求!
2025-07-31 20:46:05:709 ==>> 提取到IMSI:460130071539126
2025-07-31 20:46:05:724 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:46:05:728 ==>> 取到目标值:460130071539126
2025-07-31 20:46:05:732 ==>> 【校验网络运营商(移动)】通过,【460130071539126】符合目标值【】要求!
2025-07-31 20:46:05:736 ==>> 检测【打开CAN通信】
2025-07-31 20:46:05:757 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:46:05:762 ==>> $GBGGA,124609.515,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,24,,,41,25,,,41,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,40,1,,,39,1*49

$GBGSV,7,3,27,42,,,39,14,,,39,40,,,38,41,,,38,1*75

$GBGSV,7,4,27,13,,,37,16,,,37,2,,,36,7,,,36,1*70

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,32,1*77

$GBRMC,124609.515,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124609.515,0.000,767.844,767.845,702.211,2097152,2097152,2097152*6E

[D][05:18:25][COMM]read battery soc:255


2025-07-31 20:46:05:858 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:46:06:007 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:46:06:016 ==>> 检测【检测CAN通信】
2025-07-31 20:46:06:022 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:46:06:053 ==>> can send success


2025-07-31 20:46:06:098 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:46:06:159 ==>> [D][05:18:26][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 37193
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:46:06:219 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:46:06:279 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:46:06:292 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:46:06:297 ==>> 检测【关闭CAN通信】
2025-07-31 20:46:06:301 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:46:06:355 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:46:06:602 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:46:06:612 ==>> 检测【打印IMU STATE】
2025-07-31 20:46:06:619 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:46:06:807 ==>> $GBGGA,124610.515,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,25,,,41,39,,,40,42,,,39,1*75

$GBGSV,7,3,27,14,,,39,40,,,38,1,,,38,41,,,38,1*43

$GBGSV,7,4,27,2,,,37,13,,,37,9,,,37,16,,,37,1*7E

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,10,,,34,1*78

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,34,,,34,1*78

$GBGSV,7,7,27,26,,,33,4,,,33,23,,,32,1*43

$GBRMC,124610.515,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124610.515,0.000,769.382,769.382,703.617,2097152,2097152,2097152*64

[W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:46:06:880 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:46:06:885 ==>> 检测【六轴自检】
2025-07-31 20:46:06:888 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:46:07:050 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:46:07:758 ==>> $GBGGA,124611.515,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,25,,,41,39,,,40,1,,,39,1*42

$GBGSV,7,3,27,42,,,39,14,,,39,40,,,38,41,,,38,1*75

$GBGSV,7,4,27,13,,,37,9,,,37,16,,,37,2,,,36,1*7F

$GBGSV,7,5,27,7,,,36,38,,,36,6,,,36,10,,,34,1*78

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,26,,,33,1*7C

$GBGSV,7,7,27,4,,,33,34,,,33,23,,,32,1*40

$GBRMC,124611.515,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124611.515,0.000,768.619,768.619,702.920,2097152,2097152,2097152*6F

[D][05:18:27][COMM]read battery soc:255


2025-07-31 20:46:08:796 ==>> $GBGGA,124612.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,59,,,41,24,,,41,1*7B

$GBGSV,7,2,27,25,,,41,3,,,40,39,,,40,42,,,39,1*41

$GBGSV,7,3,27,14,,,39,40,,,38,1,,,38,41,,,38,1*43

$GBGSV,7,4,27,16,,,37,2,,,36,7,,,36,13,,,36,1*71

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,26,,,33,34,,,33,23,,,32,1*70

$GBRMC,124612.515,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124612.515,0.000,766.316,766.316,700.814,2097152,2097152,2097152*68

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:46:08:901 ==>> [D][05:18:28][COMM]Main Task receive event:142
[D][05:18:28][COMM]###### 39956 imu self test OK ######
[D][0

2025-07-31 20:46:08:931 ==>> 5:18:28][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-20,-1,4071]
[D][05:18:28][COMM]Main Task receive event:142 finished processing


2025-07-31 20:46:08:970 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:46:08:979 ==>> 检测【打印IMU STATE2】
2025-07-31 20:46:08:994 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:46:09:156 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:46:09:259 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:46:09:269 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:46:09:276 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:46:09:365 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:46:09:470 ==>> [D][05:18:29][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i

2025-07-31 20:46:09:516 ==>>  = 3,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:29][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:46:09:540 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:46:09:545 ==>> 检测【检测VBUS电压2】
2025-07-31 20:46:09:550 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:46:09:923 ==>> $GBGGA,124613.515,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,59,,,41,24,,,41,1*7B

$GBGSV,7,2,27,25,,,41,3,,,40,39,,,40,42,,,39,1*41

$GBGSV,7,3,27,14,,,39,40,,,38,1,,,38,41,,,38,1*43

$GBGSV,7,4,27,13,,,37,16,,,37,2,,,36,7,,,36,1*70

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,34,26,,,33,23,,,32,1*77

$GBRMC,124613.515,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124613.515,0.000,767.846,767.846,702.212,2097152,2097152,2097152*67

[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539126
[D][05:18:29][FCTY]HardwareID  = 867222087736387
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C6DD9F8AF215
[D][05:18:29][FCTY]Bat         = 3924 mv
[D][05:18:29][FC

2025-07-31 20:46:10:013 ==>> TY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 7, adc = 304
[D][05:18:29][FCTY]Acckey1 vol = 5563 mv, Acckey2 vol = 0 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:29][FCTY]Bat1         = 3815 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:46:10:084 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:46:10:425 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539126
[D][05:18:30][FCTY]HardwareID  = 867222087736387
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = C6DD9F8AF215
[D][05:18:30][FCTY]Bat         = 3924 mv
[D][05:18:30][FCTY]Current     = 150 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 161
[D][05:18:30][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3815 mv
[

2025-07-31 20:46:10:454 ==>> D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:46:10:633 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:46:10:638 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:46:10:644 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:46:10:755 ==>> $GBGGA,124614.515,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,59,,,41,24,,,41,25,,,41,1*7A

$GBGSV,7,2,27,60,,,40,3,,,40,39,,,40,42,,,39,1*41

$GBGSV,7,3,27,14,,,39,40,,,38,1,,,38,41,,,38,1*43

$GBGSV,7,4,27,16,,,37,2,,,36,7,,,36,13,,,36,1*71

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,33,26,,,32,23,,,32,1*71

$GBRMC,124614.515,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124614.515,0.000,764.784,764.784,699.413,2097152,2097152,2097152*64

5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:46:10:906 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26
[D][05:18:30][COMM]read battery soc:255


2025-07-31 20:46:10:922 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:46:10:927 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:46:10:933 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:46:11:057 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:46:11:147 ==>> [D][05:18:31][COMM]msg 0601 loss. last_tick:37175. cur_tick:42188. period:500
[D][05:18:31][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 42188


2025-07-31 20:46:11:203 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:46:11:208 ==>> 检测【打开WIFI(3)】
2025-07-31 20:46:11:212 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:46:11:388 ==>> [D][05:18:31][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:46:11:493 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:46:11:498 ==>> 检测【扩展芯片hw】
2025-07-31 20:46:11:504 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:46:11:758 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
$GBGGA,124615.515,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,27,24,,,41,25,,,41,39,,,40,14,,,40,1*78

$GBGSV,7,3,27,42,,,39,40,,,38,1,,,38,41,,,38,1*40

$GBGSV,7,4,27,13,,,37,16,,,37,2,,,36,7,,,36,1*70

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,33,26,,,32,23,,,32,1*71

$GBRMC,124615.515,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,124615.515,0.000,767.854,767.854,702.221,2097152,2097152,2097152*61



2025-07-31 20:46:11:803 ==>>                                      

2025-07-31 20:46:12:054 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:46:12:063 ==>> 检测【扩展芯片boot】
2025-07-31 20:46:12:084 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:46:12:092 ==>> 检测【扩展芯片sw】
2025-07-31 20:46:12:121 ==>> +WIFISCAN:4,0,CC057790A740,-71
+WIFISCAN:4,1,44A1917CAD81,-75
+WIFISCAN:4,2,CC057790A5C0,-75
+WIFISCAN:4,3,CC057790A5C1,-76

[D][05:18:32][CAT1]wifi scan report total[4]


2025-07-31 20:46:12:127 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:46:12:133 ==>> 检测【检测音频FLASH】
2025-07-31 20:46:12:139 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:46:12:451 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:46:13:354 ==>>                                                                                                                                                            Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B

2025-07-31 20:46:13:460 ==>> 50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][

2025-07-31 20:46:13:565 ==>> 05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:32][PROT]index:0
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900005]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][PROT]index:0 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:32][PROT]====================================

2025-07-31 20:46:13:671 ==>> =======================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900005]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052f10] format[0]
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:18:32][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]

2025-07-31 20:46:13:776 ==>> 
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][198] ... ->:
0063B98F113311331133113311331B88B53E92EBC442ACEB06D0A7039CE4508AC9A8A2D367EFAE1B2A71F5BF4447C440731141116BD8880051EBEEC920CB9E43E88D155EB01F1C61D81162872451B7E94557A70A78AA82EE405E7DDE520E967ACDC205
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]f:[ec800m_audio_response_proc

2025-07-31 20:46:13:881 ==>> ess].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

$GBGGA,124616.515,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,3,,,41,59,,,41,24,,,41,1*4E

$GBGSV,7,2,27,25,,,41,60,,,40,39,,,40,1,,,39,1*43

$GBGSV,7,3,27,42,,,39,14,,,3

2025-07-31 20:46:13:986 ==>> 9,40,,,38,41,,,38,1*75

$GBGSV,7,4,27,16,,,37,2,,,36,7,,,36,13,,,36,1*71

$GBGSV,7,5,27,38,,,36,9,,,36,6,,,36,10,,,34,1*76

$GBGSV,7,6,27,8,,,34,5,,,34,44,,,34,4,,,34,1*4B

$GBGSV,7,7,27,34,,,33,26,,,32,23,,,32,1*71

$GBRMC,124616.515,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
$GBGST,124616.515,0.000,766.320,766.320,700.817,2097152,2097152,2097152*6F

[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_a

2025-07-31 20:46:14:061 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:46:15:425 ==>> $GBGGA,124614.502,2301.2573764,N,11421.9419793,E,1,20,0.62,74.112,M,-1.770,M,,*5F

$GBGSA,A,3,33,14,06,39,16,24,09,25,07,13,42,40,1.19,0.62,1.02,4*07

$GBGSA,A,3,08,10,41,38,44,34,26,23,,,,,1.19,0.62,1.02,4*03

$GBGSV,7,1,28,33,69,268,43,14,63,191,40,3,61,190,41,59,52,129,40,1*4F

$GBGSV,7,2,28,6,52,346,36,39,52,10,40,16,52,350,37,1,48,126,38,1*45

$GBGSV,7,3,28,24,48,17,41,9,47,323,36,2,46,238,36,25,43,288,41,1*47

$GBGSV,7,4,28,60,41,238,40,7,41,177,36,13,38,219,36,42,38,165,39,1*44

$GBGSV,7,5,28,40,37,160,38,8,35,207,34,4,32,112,34,10,32,188,34,1*78

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,33,26,10,53,32,23,5,255,32,12,,,30,1*4E

$GBRMC,124614.502,A,2301.2573764,N,11421.9419793,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:18:34][GNSS]HD8040 GPS
[D][05:18:34][GNSS]GPS diff_sec 124010860, report 0x42 frame
$GBGST,124614.502,2.107,0.210,0.198,0.291,2.256,3.257,7.211*7E

[D][05:18:34][COMM]Main Task receive event:131
[D][05:18:34][COMM]index:0,power_mode:0xFF
[D][05:18:34][COMM]index:1,sound_mode:0xFF
[D][05:18:34][COMM]index:2,gsensor_mode:0xFF
[D][05:18:34][COMM]index:3,report_freq_mode:0xFF
[D][05:18:34][COMM]index:4,report_period:0xFF
[D][05:18:34][COMM]index

2025-07-31 20:46:15:531 ==>> :5,normal_reset_mode:0xFF
[D][05:18:34][COMM]index:6,normal_reset_period:0xFF
[D][05:18:34][COMM]index:7,spock_over_speed:0xFF
[D][05:18:34][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:34][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:34][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:34][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:34][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:34][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:34][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:34][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:34][COMM]index:16,imu_config_params:0xFF
[D][05:18:34][COMM]index:17,long_connect_params:0xFF
[D][05:18:34][COMM]index:18,detain_mark:0xFF
[D][05:18:34][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:34][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:34][COMM]index:21,mc_mode:0xFF
[D][05:18:34][COMM]index:22,S_mode:0xFF
[D][05:18:34][COMM]index:23,overweight:0xFF
[D][05:18:34][COMM]index:24,standstill_mode:0xFF
[D][05:18:34][COMM]index:25,night_mode:0xFF
[D][05:18:34][COMM]index:26,experiment1:0xFF
[D][05:18:34][COMM]index:27,experiment2:0xFF
[D][05:18:34][COMM]index:28,experiment3:0xFF
[D][05:18:34][COMM]

2025-07-31 20:46:15:635 ==>> index:29,experiment4:0xFF
[D][05:18:34][COMM]index:30,night_mode_start:0xFF
[D][05:18:34][COMM]index:31,night_mode_end:0xFF
[D][05:18:34][COMM]index:33,park_report_minutes:0xFF
[D][05:18:34][COMM]index:34,park_report_mode:0xFF
[D][05:18:34][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:34][COMM]index:38,charge_battery_para: FF
[D][05:18:34][COMM]index:39,multirider_mode:0xFF
[D][05:18:34][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:34][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:34][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:34][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:34][COMM]index:44,riding_duration_config:0xFF
[D][05:18:34][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:34][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:34][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:34][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:34][COMM]index:49,mc_load_startup:0xFF
[D][05:18:34][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:34][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:34][COMM]index:52,traffic_mode:0xFF
[D][05:18:34][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:34][COMM]index:54,traffic_security_model_cycle:

2025-07-31 20:46:15:740 ==>> 0xFF
[D][05:18:34][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:34][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:34][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:34][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:34][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:34][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:34][COMM]index:63,experiment5:0xFF
[D][05:18:34][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:34][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:34][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:34][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:34][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:34][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:34][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:34][COMM]index:72,experiment6:0xFF
[D][05:18:34][COMM]index:73,experiment7:0xFF
[D][05:18:34][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:34][COMM]index:75,zero_value_from_server:-1
[D][05:18:34][COMM]index:76,multirider_threshold:255
[D][05:18:34][COMM]index:77,experiment8:255
[D][05:18:34][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:34][COMM]index:79,temp_par

2025-07-31 20:46:15:846 ==>> k_tail_light_twinkle_duration:255
[D][05:18:34][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:34][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:34][COMM]index:83,loc_report_interval:255
[D][05:18:34][COMM]index:84,multirider_threshold_p2:255
[D][05:18:34][COMM]index:85,multirider_strategy:255
[D][05:18:34][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:34][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:34][COMM]index:90,weight_param:0xFF
[D][05:18:34][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:34][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:34][COMM]index:95,current_limit:0xFF
[D][05:18:34][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:34][COMM]index:100,location_mode:0xFF

[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[4205],priority[0],index[2],used[1]
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]Main Task receive event:131 finished processing
[D][05:18:34][COMM]read battery soc:255


2025-07-31 20:46:15:950 ==>> 
$GBGGA,124615.002,2301.2577250,N,11421.9420089,E,1,21,0.61,72.825,M,-1.770,M,,*52

$GBGSA,A,3,33,14,06,39,16,24,09,25,60,07,13,42,1.19,0.61,1.02,4*06

$GBGSA,A,3,40,08,10,41,38,44,34,26,23,,,,1.19,0.61,1.02,4*04

$GBGSV,7,1,28,33,69,268,43,14,63,191,40,3,61,190,41,59,52,129,40,1*4F

$GBGSV,7,2,28,6,52,346,36,39,52,10,40,16,52,350,37,1,48,126,38,1*45

$GBGSV,7,3,28,24,48,17,42,9,47,323,36,2,46,238,36,25,43,288,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,38,8,35,207,34,4,32,112,34,10,32,188,34,1*78

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,33,26,10,53,33,23,5,255,32,12,,,30,1*4F

$GBGSV,2,1,08,33,69,268,41,39,52,10,38,24,48,17,40,25,43,288,39,5*78

$GBGSV,2,2,08,42,38,165,39,40,37,160,37,41,26,318,35,38,26,192,36,5*71

$GBRMC,124615.002,A,2301.2577250,N,11421.9420089,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124615.002,2.169,0.263,0.245,0.367,1.903,2.344,4.977*7F



2025-07-31 20:46:15:980 ==>>                                                             

2025-07-31 20:46:16:405 ==>> $GBGGA,124616.000,2301.2579725,N,11421.9417901,E,1,21,0.61,73.463,M,-1.770,M,,*58

$GBGSA,A,3,33,14,06,39,16,24,09,25,60,07,13,42,1.19,0.61,1.02,4*06

$GBGSA,A,3,40,08,10,41,38,44,34,26,23,,,,1.19,0.61,1.02,4*04

$GBGSV,7,1,28,33,69,268,43,14,63,191,39,3,61,190,41,59,52,129,40,1*41

$GBGSV,7,2,28,6,52,346,36,39,52,10,40,16,52,350,37,1,48,126,39,1*44

$GBGSV,7,3,28,24,48,17,41,9,47,323,36,2,46,238,36,25,43,288,41,1*47

$GBGSV,7,4,28,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,39,8,35,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,33,26,10,53,33,23,5,255,32,12,,,30,1*4F

$GBGSV,3,1,12,33,69,268,42,39,52,10,39,24,48,17,41,25,43,288,40,5*7F

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,26,192,35,5*79

$GBGSV,3,3,12,44,17,97,33,34,15,150,30,26,10,53,31,23,5,255,30,5*49

$GBRMC,124616.000,A,2301.2579725,N,11421.9417901,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124616.000,2.803,0.208,0.199,0.284,2.152,2.428,4.498*76



2025-07-31 20:46:16:675 ==>> [D][05:18:36][COMM]47580 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:36][COMM]accel parse set 0
[D][05:18:36][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:46:16:945 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 20:46:17:214 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:46:17:220 ==>> 检测【打开喇叭声音】
2025-07-31 20:46:17:225 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:46:17:399 ==>> $GBGGA,124617.000,2301.2580333,N,11421.9416637,E,1,21,0.61,74.043,M,-1.770,M,,*56

$GBGSA,A,3,33,14,06,39,16,24,09,25,60,07,13,42,1.19,0.61,1.02,4*06

$GBGSA,A,3,40,08,10,41,38,44,34,26,23,,,,1.19,0.61,1.02,4*04

$GBGSV,7,1,28,33,69,268,43,14,63,191,40,3,61,190,41,59,52,129,41,1*4E

$GBGSV,7,2,28,6,52,346,36,39,52,10,40,16,52,350,37,1,48,126,38,1*45

$GBGSV,7,3,28,24,48,17,41,9,47,323,36,2,46,238,36,25,43,288,41,1*47

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*48

$GBGSV,7,5,28,40,37,160,38,8,35,207,34,4,32,112,34,10,32,188,34,1*78

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,33,26,10,53,33,23,5,255,32,12,,,30,1*4F

$GBGSV,3,1,12,33,69,268,43,39,52,10,40,24,48,17,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,26,192,35,5*79

$GBGSV,3,3,12,44,17,97,34,34,15,150,30,26,10,53,31,23,5,255,30,5*4E

$GBRMC,124617.000,A,2301.2580333,N,11421.9416637,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124617.000,2.859,0.224,0.214,0.305,2.129,2.332,4.090*74



2025-07-31 20:46:18:082 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:37][COMM]48591 imu init OK
[D][05:18:37][COMM]--->crc16:0x419c
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:37][CO

2025-07-31 20:46:18:187 ==>> MM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:37][PROT]CLEAN,SEND:0
[D][05

2025-07-31 20:46:18:284 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:46:18:297 ==>> 检测【打开大灯控制】
2025-07-31 20:46:18:327 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:46:18:333 ==>> :18:37][PROT]index:0 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:4
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:2
[D][05:18:37][PROT]send_path:0x2
[D][05:18:37][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900005]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]tx ret[17] >

2025-07-31 20:46:18:397 ==>> >> AT+QISEND=0,198

[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B54CE9A0913A4DED2C17B36E7306E0CAE75BE80FDEFA1CF74195BC908EA5B501FA2A9E033455F287DF16A050829E43D01AFBDA833B0CC4F47F884B711DC0010793DD72C487C294E519F60E1AC751FAF215743D
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:37][PROT]M2M Send ok [1629955117]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:37][COMM]f

2025-07-31 20:46:18:502 ==>> :[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                

2025-07-31 20:46:18:547 ==>>                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:46:18:577 ==>>                                                            

2025-07-31 20:46:18:943 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 20:46:19:318 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:46:19:393 ==>> $GBGGA,124619.000,2301.2580592,N,11421.9415917,E,1,24,0.57,74.744,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,268,43,14,63,191,40,3,62,190,40,6,52,346,36,1*7D

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,41,24,48,17,42,1*7E

$GBGSV,7,3,28,9,47,323,36,1,46,125,39,2,46,238,36,25,43,288,41,1*41

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,34,26,10,53,33,23,5,255,32,12,,,30,1*48

$GBGSV,3,1,12,33,69,268,43,39,52,10,40,24,48,17,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,26,192,35,5*79

$GBGSV,3,3,12,44,17,97,34,34,15,150,29,26,10,53,32,23,5,255,30,5*45

$GBRMC,124619.000,A,2301.2580592,N,11421.9415917,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124619.000,2.718,0.227,0.220,0.309,1.996,2.131,3.547*7E



2025-07-31 20:46:19:483 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:46:19:657 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:46:19:664 ==>> 检测【关闭仪表供电3】
2025-07-31 20:46:19:673 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:46:19:845 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 20:46:19:995 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:46:20:001 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:46:20:012 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:46:20:115 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:46:20:329 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:46:20:335 ==>> 检测【读大灯电压】
2025-07-31 20:46:20:341 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:46:20:400 ==>> $GBGGA,124620.000,2301.2580718,N,11421.9415336,E,1,24,0.57,74.945,M,-1.770,M,,*57

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,267,43,14,63,191,40,3,62,190,41,6,52,346,36,1*73

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,40,24,48,17,42,1*7F

$GBGSV,7,3,28,9,47,323,36,1,46,125,39,2,46,238,36,25,43,288,41,1*41

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*48

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,26,192,36,5,22,257,34,44,17,97,34,1*70

$GBGSV,7,7,28,34,15,150,34,26,10,53,33,23,5,255,32,12,,,31,1*49

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,17,41,25,43,288,41,5*7E

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,26,192,35,5*79

$GBGSV,3,3,12,44,17,97,34,34,15,150,29,26,10,53,32,23,5,255,29,5*4D

$GBRMC,124620.000,A,2301.2580718,N,11421.9415336,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124620.000,2.557,0.216,0.209,0.294,1.885,2.002,3.320*74



2025-07-31 20:46:20:550 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33317]
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:46:20:646 ==>> 【读大灯电压】通过,【33317mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:46:20:652 ==>> 检测【关闭大灯控制2】
2025-07-31 20:46:20:660 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:46:20:841 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:46:20:931 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 20:46:20:964 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:46:20:970 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:46:20:979 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:46:21:141 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[115]


2025-07-31 20:46:21:283 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:46:21:293 ==>> 检测【打开WIFI(4)】
2025-07-31 20:46:21:326 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:46:21:398 ==>> $GBGGA,124621.000,2301.2581109,N,11421.9415023,E,1,24,0.57,75.060,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*7C

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,41,24,48,17,42,1*7E

$GBGSV,7,3,28,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*40

$GBGSV,7,4,28,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,25,192,36,5,22,257,34,44,17,97,35,1*72

$GBGSV,7,7,28,34,15,150,34,26,10,53,33,23,5,255,32,12,,,31,1*49

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,17,41,25,43,288,41,5*7E

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,15,150,29,26,10,53,32,23,5,255,29,5*4D

$GBRMC,124621.000,A,2301.2581109,N,11421.9415023,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124621.000,2.508,0.234,0.226,0.319,1.843,1.946,3.177*76



2025-07-31 20:46:21:563 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6
[D][05:18:41][COMM]52594 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:46:21:668 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:46:21:677 ==>> 检测【EC800M模组版本】
2025-07-31 20:46:21:701 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:46:21:848 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:46:22:045 ==>> [D][05:18:42][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:46:22:276 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:46:22:287 ==>> 检测【配置蓝牙地址】
2025-07-31 20:46:22:308 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:46:22:488 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:C6DD9F8AF215>】
2025-07-31 20:46:22:506 ==>> $GBGGA,124622.000,2301.2581223,N,11421.9414608,E,1,24,0.57,75.085,M,-1.770,M,,*54

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,267,43,14,63,191,40,3,62,190,41,6,52,346,36,1*73

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,41,24,48,17,41,1*7D

$GBGSV,7,3,28,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*40

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*48

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,25,192,36,5,22,257,34,44,17,97,34,1*73

$GBGSV,7,7,28,34,15,150,34,26,10,53,33,23,5,255,32,12,,,30,1*48

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,17,41,25,43,288,41,5*7E

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,15,150,29,26,10,53,31,23,5,255,29,5*4E

$GBRMC,124622.000,A,2301.2581223,N,11421.9414608,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124622.000,2.596,0.214,0.207,0.289,1.890,1.979,3.127*7C

+WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,CC057790A740,-72
+WIFISCAN:4,2,44A1917CAD81,-78
+WIFISCAN:4,3,CC057790A5C1,-81

[D][05:18:42][CAT1]wifi 

2025-07-31 20:46:22:578 ==>> scan report total[4]
[W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<
                                                                                                  recv ble 1
recv ble 2
ble set mac ok :c6,dd,9f,8a,f2,15
enable filters ret : 0

2025-07-31 20:46:22:799 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:46:22:805 ==>> 检测【BLETEST】
2025-07-31 20:46:22:814 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:46:22:841 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 20:46:22:852 ==>> 4A A4 01 A4 4A 


2025-07-31 20:46:22:958 ==>> recv ble 1
recv ble 2
<BSJ*MAC:C6DD9F8AF215*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9C6DD9F8AF21599999OVER 150


2025-07-31 20:46:23:403 ==>> [D][05:18:43][COMM]read battery soc:255
[D][05:18:43][PROT]CLEAN,SEND:0
[D][05:18:43][PROT]index:0 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:4
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900005]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B52C5204022744824EB7987BC353B8914

2025-07-31 20:46:23:508 ==>> F734412AC1AE873C23E5E8ACE6946ECDDA4A3EFBB4B904D94A6599008DC7A6C0AD65649586CBE48F8DD6D742BDEC110D55AD4FD902CA2A58D222E59FBDCB6A9813AFE
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:43][PROT]M2M Send ok [1629955123]
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1012].open hexlog save
$GBGGA,124623.000,2301.2581233,N,11421.9414272,E,1,24,0.57,75.191,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06

2025-07-31 20:46:23:613 ==>> ,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*7C

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,41,24,48,17,41,1*7D

$GBGSV,7,3,28,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,40,1*41

$GBGSV,7,4,28,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,25,192,36,5,22,257,34,44,17,97,34,1*73

$GBGSV,7,7,28,34,15,150,34,26,10,53,32,23,5,255,32,12,,,30,1*49

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,17,41,25,43,288,41,5*7E

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,15,150,29,26,10,53,31,23,5,255,30,5*46

$GBRMC,124623.000,A,2301.2581233,N,11421.9414272,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124623.000,2.585,0.215,0.208,0.293,1.876,1.956,3.047*78

                

2025-07-31 20:46:23:643 ==>>                       

2025-07-31 20:46:23:852 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:46:23:858 ==>> 该项需要延时执行
2025-07-31 20:46:24:407 ==>> $GBGGA,124624.000,2301.2581270,N,11421.9414305,E,1,24,0.57,75.325,M,-1.770,M,,*55

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,28,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*7C

$GBGSV,7,2,28,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*73

$GBGSV,7,3,28,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,40,1*41

$GBGSV,7,4,28,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*49

$GBGSV,7,5,28,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,28,41,26,318,38,38,25,192,36,5,22,257,34,44,17,97,34,1*73

$GBGSV,7,7,28,34,15,150,33,26,10,53,32,23,5,255,32,12,,,30,1*4E

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,15,150,30,26,10,53,31,23,5,255,30,5*4E

$GBRMC,124624.000,A,2301.2581270,N,11421.9414305,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124624.000,2.527,0.224,0.216,0.306,1.835,1.909,2.955*71



2025-07-31 20:46:24:893 ==>> [D][05:18:44][COMM]S->M yaw:INVALID


2025-07-31 20:46:24:953 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:46:25:397 ==>> $GBGGA,124625.000,2301.2581470,N,11421.9414294,E,1,24,0.57,75.396,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7C

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*4F

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,27,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*76

$GBGSV,7,6,27,41,26,318,37,38,25,192,36,44,17,97,34,34,15,150,34,1*41

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,41,40,37,160,38,41,26,318,35,38,25,192,35,5*7B

$GBGSV,3,3,12,44,17,97,34,34,15,150,30,26,10,53,31,23,5,255,30,5*4E

$GBRMC,124625.000,A,2301.2581470,N,11421.9414294,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124625.000,2.561,0.192,0.187,0.264,1.852,1.919,2.920*70



2025-07-31 20:46:25:934 ==>> [D][05:18:45][COMM]M->S yaw:INVALID


2025-07-31 20:46:26:394 ==>> $GBGGA,124626.000,2301.2581565,N,11421.9414070,E,1,24,0.57,75.414,M,-1.770,M,,*50

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,39,2,46,238,35,25,43,288,41,1*4D

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*47

$GBGSV,7,5,27,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*76

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,41,40,37,160,38,41,26,318,35,38,25,192,35,5*7B

$GBGSV,3,3,12,44,17,97,34,34,16,150,29,26,10,53,31,23,5,255,30,5*45

$GBRMC,124626.000,A,2301.2581565,N,11421.9414070,E,0.004,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.004,N,0.008,K,A*23

$GBGST,124626.000,2.795,0.218,0.211,0.296,1.989,2.049,2.997*7E



2025-07-31 20:46:26:953 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:46:27:395 ==>> $GBGGA,124627.000,2301.2581612,N,11421.9413951,E,1,23,0.59,75.442,M,-1.770,M,,*55

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,25,60,07,1.11,0.59,0.95,4*01

$GBGSA,A,3,13,42,40,08,10,41,38,44,34,26,23,,1.11,0.59,0.95,4*0C

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*72

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,9,47,323,36,2,46,238,36,25,43,288,41,60,43,241,40,1*72

$GBGSV,7,4,26,7,41,177,36,13,38,219,37,42,38,165,39,40,37,160,38,1*49

$GBGSV,7,5,26,8,34,207,34,4,32,112,34,10,32,188,34,41,26,318,38,1*7B

$GBGSV,7,6,26,38,25,192,36,44,17,97,34,34,16,150,33,26,10,53,32,1*79

$GBGSV,7,7,26,23,5,255,32,12,,,31,1*74

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,30,5*4D

$GBRMC,124627.000,A,2301.2581612,N,11421.9413951,E,0.003,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,124627.000,2.675,0.206,0.198,0.280,1.915,1.972,2.898*73



2025-07-31 20:46:28:536 ==>> [D][05:18:48][PROT]CLEAN,SEND:0
[D][05:18:48][PROT]CLEAN:0
[D][05:18:48][PROT]index:1 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:5
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:3
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:18:48][PROT]sending traceid [9999999999900006]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[134]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
$GBGGA,124628.000,2301.2581636,N,11421.9413842,E,1,23,0.59,75.482,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,2

2025-07-31 20:46:28:641 ==>> 4,09,25,60,07,1.11,0.59,0.95,4*01

$GBGSA,A,3,13,42,40,08,10,41,38,44,34,26,23,,1.11,0.59,0.95,4*0C

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,21,48,327,,1*42

$GBGSV,7,3,27,24,48,18,41,9,47,323,36,2,46,238,36,25,43,288,41,1*47

[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]gsm read msg sub id: 15
$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,27,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*76

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,33,1*4A

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,134

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,41,40,37,160,38,41,26,318,35,38,25,192,35,5*7B

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,30,5*4D

$GBRMC,124628.000,A,2301.2581636,N,11421.9413842,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124628.000,2.695,0.204,0.197,0.276,1.924,1.977,2.875*72

[D][05:18:48][CAT1]Send Data To Server[134][137] ... ->

2025-07-31 20:46:28:731 ==>> :
0043B68C113311331133113311331B88B355BA4EF588457A6D566BF163FCB4D2E8DD9F7D477496DC6E03DF1C6AAC36CD1C1DC4BA89A0BB9955600BC2479861759DA1AB
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
                                     

2025-07-31 20:46:28:977 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:46:29:406 ==>> $GBGGA,124629.000,2301.2581546,N,11421.9413923,E,1,23,0.59,75.561,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,25,60,07,1.11,0.59,0.95,4*01

$GBGSA,A,3,13,42,40,08,10,41,38,44,34,26,23,,1.11,0.59,0.95,4*0C

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*72

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,9,47,323,36,2,46,238,36,25,43,288,41,60,43,241,40,1*72

$GBGSV,7,4,26,7,41,177,36,13,38,219,37,42,38,165,39,40,37,160,38,1*49

$GBGSV,7,5,26,8,34,207,34,4,32,112,34,10,32,188,34,41,26,318,38,1*7B

$GBGSV,7,6,26,38,25,192,36,44,17,97,35,34,16,150,34,26,10,53,32,1*7F

$GBGSV,7,7,26,23,5,255,32,12,,,31,1*74

$GBGSV,3,1,12,33,69,267,42,39,52,10,40,24,48,18,41,25,43,288,41,5*70

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,33,34,16,150,29,26,10,53,31,23,5,255,30,5*42

$GBRMC,124629.000,A,2301.2581546,N,11421.9413923,E,0.000,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,124629.000,2.704,0.207,0.199,0.281,1.928,1.977,2.850*74



2025-07-31 20:46:29:891 ==>> [D][05:18:49][COMM]M->S yaw:INVALID


2025-07-31 20:46:30:398 ==>> $GBGGA,124630.000,2301.2581556,N,11421.9413884,E,1,24,0.57,75.617,M,-1.770,M,,*52

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,40,3,62,190,40,6,52,346,36,1*7D

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*4F

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*46

$GBGSV,7,5,27,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*76

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,35,34,16,150,34,1*4C

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,33,34,16,150,29,26,10,53,31,23,5,255,30,5*42

$GBRMC,124630.000,A,2301.2581556,N,11421.9413884,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124630.000,2.631,0.223,0.216,0.304,1.882,1.929,2.784*79

[D][05:18:50][COMM]S->M yaw:INVALID


2025-07-31 20:46:30:982 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:46:31:440 ==>> $GBGGA,124631.000,2301.2581596,N,11421.9413702,E,1,24,0.57,75.632,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,40,3,62,190,40,6,52,346,36,1*7D

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7C

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*46

$GBGSV,7,5,27,40,37,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*76

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,35,34,16,150,34,1*4C

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,37,160,38,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,30,5*4D

$GBRMC,124631.000,A,2301.2581596,N,11421.9413702,E,0.003,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,124631.000,2.533,0.216,0.208,0.295,1.820,1.865,2.706*72

[D][05:18:51][COMM]M->S yaw:INVALID


2025-07-31 20:46:31:470 ==>>                                      

2025-07-31 20:46:32:396 ==>> $GBGGA,124632.000,2301.2581596,N,11421.9413587,E,1,24,0.57,75.621,M,-1.770,M,,*57

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7C

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*4F

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,36,42,38,165,39,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,4,32,112,34,10,32,188,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,30,5*4D

$GBRMC,124632.000,A,2301.2581596,N,11421.9413587,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124632.000,2.810,0.232,0.223,0.318,1.986,2.028,2.835*76



2025-07-31 20:46:32:975 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:46:33:418 ==>> $GBGGA,124633.000,2301.2581622,N,11421.9413555,E,1,24,0.57,75.626,M,-1.770,M,,*52

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7C

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,40,1*4E

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,36,42,38,165,39,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,33,1*7E

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,35,34,16,150,34,1*4C

$GBGSV,7,7,27,26,10,53,32,23,5,255,32,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,42,39,52,10,40,24,48,18,41,25,43,288,41,5*70

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124633.000,A,2301.2581622,N,11421.9413555,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124633.000,2.780,0.214,0.207,0.296,1.967,2.007,2.801*71



2025-07-31 20:46:33:523 ==>>                                                                                                                            

2025-07-31 20:46:33:628 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                         0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:18:53][PROT]sending traceid [9999999999900006]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[134]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:53][CAT1]<<< 
ERROR



2025-07-31 20:46:33:658 ==>>                                      

2025-07-31 20:46:33:854 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:46:33:861 ==>> 检测【检测WiFi结果】
2025-07-31 20:46:33:867 ==>> WiFi信号:【CC057790A5C0】,信号值:-71
2025-07-31 20:46:33:874 ==>> WiFi信号:【CC057790A741】,信号值:-71
2025-07-31 20:46:33:904 ==>> WiFi信号:【CC057790A740】,信号值:-72
2025-07-31 20:46:33:911 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 20:46:33:934 ==>> WiFi信号:【44A1917CAD81】,信号值:-75
2025-07-31 20:46:33:945 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 20:46:33:955 ==>> WiFi数量【6】, 最大信号值:-63
2025-07-31 20:46:33:982 ==>> 检测【检测GPS结果】
2025-07-31 20:46:34:000 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 20:46:34:011 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【14】,信号值:【40】
北斗星号:【3】,信号值:【41】
北斗星号:【59】,信号值:【40】
北斗星号:【6】,信号值:【36】
北斗星号:【39】,信号值:【40】
北斗星号:【16】,信号值:【37】
北斗星号:【1】,信号值:【38】
北斗星号:【24】,信号值:【41】
北斗星号:【9】,信号值:【36】
北斗星号:【2】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【60】,信号值:【40】
北斗星号:【7】,信号值:【36】
北斗星号:【13】,信号值:【36】
北斗星号:【42】,信号值:【39】
北斗星号:【40】,信号值:【38】
北斗星号:【41】,信号值:【38】
北斗星号:【38】,信号值:【36】

2025-07-31 20:46:34:024 ==>> 检测【CSQ强度】
2025-07-31 20:46:34:037 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:46:34:043 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:46:34:394 ==>> $GBGGA,124634.000,2301.2581616,N,11421.9413549,E,1,24,0.57,75.633,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,40,3,62,190,40,6,52,346,36,1*7D

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,42,1*7F

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*46

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,33,1*7E

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,32,23,5,255,33,12,,,31,1*76

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124634.000,A,2301.2581616,N,11421.9413549,E,0.000,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,124634.000,2.604,0.225,0.217,0.307,1.860,1.899,2.689*75

[D][05:18:54][COMM]S->M yaw:INVALID


2025-07-31 20:46:34:978 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:46:35:399 ==>> $GBGGA,124635.000,2301.2581584,N,11421.9413465,E,1,24,0.57,75.672,M,-1.770,M,,*58

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,36,42,38,165,39,1*46

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,32,23,5,255,33,12,,,31,1*76

$GBGSV,3,1,12,33,69,267,42,39,52,10,40,24,48,18,41,25,43,288,41,5*70

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,33,34,16,150,29,26,10,53,31,23,5,255,31,5*43

$GBRMC,124635.000,A,2301.2581584,N,11421.9413465,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,124635.000,2.585,0.216,0.209,0.295,1.847,1.885,2.664*70

[D][05:18:55][COMM]M->S yaw:INVALID


2025-07-31 20:46:35:935 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:46:36:121 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:46:36:407 ==>> $GBGGA,124636.000,2301.2581537,N,11421.9413452,E,1,24,0.57,75.710,M,-1.770,M,,*52

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,42,1*7E

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,29,26,10,53,31,23,5,255,31,5*44

$GBRMC,124636.000,A,2301.2581537,N,11421.9413452,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124636.000,2.605,0.212,0.205,0.290,1.859,1.895,2.661*7E



2025-07-31 20:46:36:996 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:46:37:403 ==>> $GBGGA,124637.000,2301.2581482,N,11421.9413529,E,1,24,0.57,75.753,M,-1.770,M,,*56

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,40,1*4D

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,36,42,38,165,39,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124637.000,A,2301.2581482,N,11421.9413529,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124637.000,2.627,0.240,0.230,0.329,1.871,1.906,2.660*7D



2025-07-31 20:46:38:013 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:46:38:400 ==>> $GBGGA,124638.000,2301.2581515,N,11421.9413477,E,1,24,0.57,75.767,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,40,1*4D

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,38,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,41,40,38,160,38,41,26,318,35,38,25,192,35,5*74

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124638.000,A,2301.2581515,N,11421.9413477,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124638.000,2.539,0.209,0.202,0.287,1.816,1.850,2.599*71

[W][05:18:58][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:46:38:998 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:46:39:397 ==>> $GBGGA,124639.000,2301.2581570,N,11421.9413345,E,1,24,0.57,75.791,M,-1.770,M,,*56

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,42,14,63,191,39,3,62,190,41,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124639.000,A,2301.2581570,N,11421.9413345,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124639.000,2.626,0.218,0.210,0.300,1.869,1.901,2.637*7A



2025-07-31 20:46:40:083 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:46:40:414 ==>> $GBGGA,124640.000,2301.2581592,N,11421.9413293,E,1,24,0.57,75.811,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124640.000,A,2301.2581592,N,11421.9413293,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,124640.000,2.710,0.218,0.210,0.299,1.919,1.950,2.673*73

[W][05:19:00][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:46:40:914 ==>> [D][05:19:00][COMM]S->M yaw:INVALID


2025-07-31 20:46:41:003 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 20:46:41:399 ==>> $GBGGA,124641.000,2301.2581683,N,11421.9413307,E,1,24,0.57,75.805,M,-1.770,M,,*52

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*72

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,36,25,43,288,41,1*4F

$GBGSV,7,4,27,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*47

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*79

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,34,34,16,150,34,1*4D

$GBGSV,7,7,27,26,10,53,33,23,5,255,33,12,,,31,1*77

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124641.000,A,2301.2581683,N,11421.9413307,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,124641.000,2.789,0.221,0.213,0.303,1.966,1.996,2.708*76



2025-07-31 20:46:42:082 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:46:42:141 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:46:42:399 ==>> $GBGGA,124642.000,2301.2581728,N,11421.9413228,E,1,24,0.57,75.796,M,-1.770,M,,*58

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,01,25,60,1.10,0.57,0.94,4*09

$GBGSA,A,3,07,13,42,40,08,10,41,38,44,34,26,23,1.10,0.57,0.94,4*05

$GBGSV,7,1,27,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*73

$GBGSV,7,2,27,39,52,10,40,16,52,350,37,59,50,128,41,24,48,18,41,1*7D

$GBGSV,7,3,27,9,47,323,36,1,46,125,38,2,46,238,35,25,43,288,41,1*4C

$GBGSV,7,4,27,60,43,241,40,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,27,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,33,1*7E

$GBGSV,7,6,27,41,26,318,38,38,25,192,36,44,17,97,35,34,16,150,34,1*4C

$GBGSV,7,7,27,26,10,53,32,23,5,255,33,12,,,31,1*76

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124642.000,A,2301.2581728,N,11421.9413228,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124642.000,2.784,0.253,0.243,0.346,1.963,1.991,2.696*7D

[W][05:19:02][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:46:43:025 ==>> [D][05:19:03][COMM]read battery soc:255
[D][05:19:03][COMM]M->S yaw:INVALID


2025-07-31 20:46:43:406 ==>> $GBGGA,124643.000,2301.2581735,N,11421.9413161,E,1,23,0.59,75.815,M,-1.770,M,,*56

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,25,60,07,1.12,0.59,0.95,4*02

$GBGSA,A,3,13,42,40,08,10,41,38,44,34,26,23,,1.12,0.59,0.95,4*0F

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*72

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,9,47,323,36,2,46,238,35,25,43,288,41,60,43,241,40,1*71

$GBGSV,7,4,26,7,41,177,36,13,38,219,36,42,38,165,39,40,38,160,38,1*47

$GBGSV,7,5,26,8,34,207,34,10,32,188,34,4,32,112,34,41,26,318,38,1*7B

$GBGSV,7,6,26,38,25,192,36,44,17,97,35,34,16,150,34,26,10,53,32,1*7F

$GBGSV,7,7,26,23,5,255,33,12,,,31,1*75

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*75

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124643.000,A,2301.2581735,N,11421.9413161,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,124643.000,2.763,0.209,0.200,0.285,1.950,1.977,2.676*75



2025-07-31 20:46:43:772 ==>> [D][05:19:03][CAT1]exec over: func id: 15, ret: -93
[D][05:19:03][CAT1]sub id: 15, ret: -93

[D][05:19:03][SAL ]Cellular task submsg id[68]
[D][05:19:03][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:03][SAL ]socket send fail. id[4]
[D][05:19:03][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][M2M ]m2m select fd[4]
[D][05:19:03][M2M ]socket[4] Link is disconnected
[D][05:19:03][M2M ]tcpclient close[4]
[D][05:19:03][SAL ]socket[4] has closed
[D][05:19:03][PROT]protocol read data ok
[E][05:19:03][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:03][PROT]M2M Send Fail [1629955143]
[D][05:19:03][PROT]CLEAN,SEND:1
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 21
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, re

2025-07-31 20:46:43:862 ==>> t: 21
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 21
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 21
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:03][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 21
[D][05:19:03][CAT1]gsm read msg sub id: 10
[D][05:19:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:03][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:46:44:118 ==>> 【CSQ强度】通过,【28】符合目标值【18】至【31】要求!
2025-07-31 20:46:44:132 ==>> 检测【关闭GSM联网】
2025-07-31 20:46:44:161 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:46:44:210 ==>> [D][05:19:04][CAT1]<<< 
OK

[D][05:19:04][CAT1]exec over: func id: 10, ret: 6
[D][05:19:04][CAT1]sub id: 10, ret: 6

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:04][M2M ]m2m gsm shut done, ret[0]
[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:04][SAL ]open socket ind id[4], rst[0]
[D][05:19:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:04][SAL ]Cellular task submsg id[8]
[D][05:19:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:04][CAT1]gsm read msg sub id: 8
[D][05:19:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:04][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:04][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:46:44:300 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:04][COMM]GSM test
[D][05:19:04][COMM]GSM test disable


2025-07-31 20:46:44:401 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:46:44:409 ==>> 检测【4G联网测试】
2025-07-31 20:46:44:431 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:46:45:142 ==>> [D][05:19:04][CAT1]pdpdeact urc len[22]
$GBGGA,124644.000,2301.2581782,N,11421.9413208,E,1,23,0.59,75.859,M,-1.770,M,,*59

$GBGSA,A,3,33,14,03,06,39,16,59,24,09,25,60,07,1.12,0.59,0.95,4*02

$GBGSA,A,3,13,42,40,08,10,41,38,44,34,26,23,,1.12,0.59,0.95,4*0F

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*72

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,9,47,323,36,2,46,238,35,25,43,288,41,60,43,241,40,1*71

$GBGSV,7,4,26,7,41,177,36,13,38,219,36,42,38,165,39,40,38,160,38,1*47

$GBGSV,7,5,26,8,34,207,34,10,32,188,34,4,32,112,33,41,26,318,38,1*7C

$GBGSV,7,6,26,38,25,192,36,44,17,97,35,34,16,150,34,26,10,53,32,1*7F

$GBGSV,7,7,26,23,5,255,33,12,,,31,1*75

$GBGSV,3,1,12,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*71

$GBGSV,3,2,12,42,38,165,40,40,38,160,37,41,26,318,35,38,25,192,35,5*7A

$GBGSV,3,3,12,44,17,97,34,34,16,150,30,26,10,53,31,23,5,255,31,5*4C

$GBRMC,124644.000,A,2301.2581782,N,11421.9413208,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,124644.000,2.727,0.221,0.211,0.301,1.928,1.955,2.648*77

[D][05:19:04][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:19:04][COMM]>>>>>Input comman

2025-07-31 20:46:45:246 ==>> d = AT+ALLSTATE<<<<<
[D][05:19:04][COMM]Main Task receive event:14
[D][05:19:04][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955144, allstateRepSeconds = 0
[D][05:19:04][COMM]index:0,power_mode:0xFF
[D][05:19:04][COMM]index:1,sound_mode:0xFF
[D][05:19:04][COMM]index:2,gsensor_mode:0xFF
[D][05:19:04][COMM]index:3,report_freq_mode:0xFF
[D][05:19:04][COMM]index:4,report_period:0xFF
[D][05:19:04][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:04][COMM]index:6,normal_reset_period:0xFF
[D][05:19:04][COMM]index:7,spock_over_speed:0xFF
[D][05:19:04][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:04][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:04][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:04][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:04][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:04][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:04][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:04][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:04][COMM]index:16,imu_config_params:0xFF
[D][05:19:04][COMM]index:17,long_connect_params:0xFF
[D][05:19:04][COMM]index:18,detain_mark:0xFF
[D][05:19:04][COMM]index:19,lock_pos_report_count:0xFF
[D][05

2025-07-31 20:46:45:352 ==>> :19:04][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:04][COMM]index:21,mc_mode:0xFF
[D][05:19:04][COMM]index:22,S_mode:0xFF
[D][05:19:04][COMM]index:23,overweight:0xFF
[D][05:19:04][COMM]index:24,standstill_mode:0xFF
[D][05:19:04][COMM]index:25,night_mode:0xFF
[D][05:19:04][COMM]index:26,experiment1:0xFF
[D][05:19:04][COMM]index:27,experiment2:0xFF
[D][05:19:04][COMM]index:28,experiment3:0xFF
[D][05:19:04][COMM]index:29,experiment4:0xFF
[D][05:19:04][COMM]index:30,night_mode_start:0xFF
[D][05:19:04][COMM]index:31,night_mode_end:0xFF
[D][05:19:04][COMM]index:33,park_report_minutes:0xFF
[D][05:19:04][COMM]index:34,park_report_mode:0xFF
[D][05:19:04][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:04][COMM]index:38,charge_battery_para: FF
[D][05:19:04][COMM]index:39,multirider_mode:0xFF
[D][05:19:04][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:04][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:04][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:04][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:04][COMM]index:44,riding_duration_config:0xFF
[D][05:19:04][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:04][COMM]index:46,camera_park_typ

2025-07-31 20:46:45:458 ==>> e_cfg:0xFF
[D][05:19:04][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:04][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:04][COMM]index:49,mc_load_startup:0xFF
[D][05:19:04][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:04][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:04][COMM]index:52,traffic_mode:0xFF
[D][05:19:04][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:04][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:04][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:04][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:04][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:04][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:04][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:04][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:04][COMM]index:63,experiment5:0xFF
[D][05:19:04][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:04][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:04][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:04][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:04][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:04][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:04][COMM]index:71,camera_park_self_ch

2025-07-31 20:46:45:562 ==>> eck_cfg:0xFF
[D][05:19:04][COMM]index:72,experiment6:0xFF
[D][05:19:04][COMM]index:73,experiment7:0xFF
[D][05:19:04][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:04][COMM]index:75,zero_value_from_server:-1
[D][05:19:04][COMM]index:76,multirider_threshold:255
[D][05:19:04][COMM]index:77,experiment8:255
[D][05:19:04][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:04][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:04][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:04][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:04][COMM]index:83,loc_report_interval:255
[D][05:19:04][COMM]index:84,multirider_threshold_p2:255
[D][05:19:04][COMM]index:85,multirider_strategy:255
[D][05:19:04][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:04][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:04][COMM]index:90,weight_param:0xFF
[D][05:19:04][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:04][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:04][COMM]index:95,current_limit:0xFF
[D][05:19:04][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:04][COMM]index:100,location_mode:0xFF


2025-07-31 20:46:45:623 ==>> 
[W][05:19:04][PROT]remove success[1629955144],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:04][PROT]add success [1629955144],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:04][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:04][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:05][COMM]read battery soc:255


2025-07-31 20:46:45:863 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 20:46:45:938 ==>>                                                                                                                                            [D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:05][CAT1]<<< 
OK

[D][05:19:05][CAT1]exec over: func id: 8, ret: 6
[D][05:19:05][CAT1]gsm read msg sub id: 13
[D][05:19:05][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:05][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:19:05][CAT1]exec over: func id: 13, ret: 21
[D][05:19:05][M2M ]get csq[26]


2025-07-31 20:46:46:397 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        ][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][PROT]sending traceid [9999999999900008]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[294]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:0

2025-07-31 20:46:46:503 ==>> 6][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd8] format[0]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:06][CAT1]Send Data To Server[294][297] ... ->:
0093B982113311331133113311331B88B2E233EFB3FC0EDD31A7B6BA064E4226FDC72F0ECB8F4405D7E27B3F29B65C248DFAD443C2D5A424D19FEF49B894875718F34A358A1C7301A287173A952771046258BF8298FC78731A5C9CB9547461C6256DB4CDF63042677F9CA8B7824C61553C3D9B55D32509DBF24AD6767968C039ECCCFCC8191ECEAB63534955F9D6483DB58A10
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:06][PROT]remove success[1629955146],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:06][PROT]add success [1629955146],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:06][COMM]------>period, report file manifest
[D][05:19:06][COMM]Main Task receive event:14 finished processing
[D][05:19:06][CAT1]<<< 
SEND OK

[D][05:19:06][CAT1]exec over: func id: 15, ret: 11
[D][05:19:06][CAT1]sub id: 15, ret: 11

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:06][M2M ]m2m_task

2025-07-31 20:46:46:609 ==>> : control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:06][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:06][CAT1]gsm read msg sub id: 21
[D][05:19:06][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:06][PROT]M2M Send ok [1629955146]
[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]cell info report total[0]
[D][05:19:06][CAT1]exec over: func id: 21, ret: 6
$GBGGA,124646.000,2301.2581840,N,11421.9413205,E,1,25,0.61,75.919,M,-1.770,M,,*5F

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.30,0.61,1.15,4*02

$GBGSA,A,3,60,07,13,42,40,08,10,41,38,44,12,34,1.30,0.61,1.15,4*0A

$GBGSA,A,3,26,,,,,,,,,,,,1.30,0.61,1.15,4*0C

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,40,6,52,346,36,1*72

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,2,48,239,35,9,47,323,36,1,46,125,39,25,43,288,41,1*43

$GBGSV,7,4,26,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,26,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*78

$GBGSV,7,6,26,41,26,318,37,38,25,192,36,44,17,96,34,12,16,122,31,1*46

$GBGS

2025-07-31 20:46:46:668 ==>> V,7,7,26,34,16,150,33,26,10,53,32,1*44

$GBGSV,3,1,11,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*72

$GBGSV,3,2,11,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*76

$GBGSV,3,3,11,44,17,96,34,34,16,150,30,26,10,53,31,5*4A

$GBRMC,124646.000,A,2301.2581840,N,11421.9413205,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,124646.000,2.490,0.335,0.306,0.469,1.782,1.809,2.502*7B



2025-07-31 20:46:47:018 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 20:46:47:399 ==>> $GBGGA,124647.000,2301.2581850,N,11421.9413140,E,1,25,0.61,75.935,M,-1.770,M,,*53

$GBGSA,A,3,33,14,03,06,39,16,59,24,02,09,01,25,1.30,0.61,1.15,4*02

$GBGSA,A,3,60,07,13,42,40,08,10,41,38,44,12,34,1.30,0.61,1.15,4*0A

$GBGSA,A,3,26,,,,,,,,,,,,1.30,0.61,1.15,4*0C

$GBGSV,7,1,26,33,69,267,43,14,63,191,39,3,62,190,41,6,52,346,36,1*73

$GBGSV,7,2,26,39,52,10,40,16,52,350,37,59,50,128,40,24,48,18,41,1*7D

$GBGSV,7,3,26,2,48,239,36,9,47,323,36,1,46,125,39,25,43,288,41,1*40

$GBGSV,7,4,26,60,43,241,41,7,41,177,36,13,38,219,37,42,38,165,39,1*46

$GBGSV,7,5,26,40,38,160,38,8,34,207,34,10,32,188,34,4,32,112,34,1*78

$GBGSV,7,6,26,41,26,318,38,38,25,192,36,44,17,96,34,12,16,122,31,1*49

$GBGSV,7,7,26,34,16,150,33,26,10,53,32,1*44

$GBGSV,3,1,11,33,69,267,43,39,52,10,40,24,48,18,41,25,43,288,41,5*72

$GBGSV,3,2,11,42,38,165,40,40,38,160,38,41,26,318,35,38,25,192,35,5*76

$GBGSV,3,3,11,44,17,96,34,34,16,150,30,26,10,53,31,5*4A

$GBRMC,124647.000,A,2301.2581850,N,11421.9413140,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,124647.000,2.534,0.215,0.203,0.302,1.809,1.835,2.522*79



2025-07-31 20:46:47:456 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:46:47:464 ==>> 检测【关闭GPS】
2025-07-31 20:46:47:477 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:46:47:811 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:07][GNSS]stop locating
[D][05:19:07][GNSS]stop event:8
[D][05:19:07][GNSS]GPS stop. ret=0
[D][05:19:07][GNSS]all continue location stop
[W][05:19:07][GNSS]stop locating
[D][05:19:07][GNSS]all sing location stop
[D][05:19:07][CAT1]gsm read msg sub id: 24
[D][05:19:07][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:07][CAT1]<<< 
OK

[D][05:19:07][CAT1]exec over: func id: 24, ret: 6
[D][05:19:07][CAT1]sub id: 24, ret: 6



2025-07-31 20:46:47:990 ==>> [D][05:19:08][GNSS]recv submsg id[1]
[D][05:19:08][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:08][GNSS]location stop evt done evt


2025-07-31 20:46:48:002 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:46:48:011 ==>> 检测【清空消息队列2】
2025-07-31 20:46:48:023 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:46:48:141 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:08][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:46:48:299 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:46:48:313 ==>> 检测【轮动检测】
2025-07-31 20:46:48:344 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:46:48:370 ==>> 3A A3 01 00 A3 


2025-07-31 20:46:48:458 ==>> OFF_OUT1
OVER 150


2025-07-31 20:46:48:519 ==>> [D][05:19:08][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:46:48:804 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:46:48:864 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:46:49:031 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 20:46:49:090 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:46:49:098 ==>> 检测【关闭小电池】
2025-07-31 20:46:49:106 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:46:49:137 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:46:49:378 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:46:49:386 ==>> 检测【进入休眠模式】
2025-07-31 20:46:49:402 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:46:49:582 ==>> [D][05:19:09][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:19:09][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]main task tmp_sleep_event = 8
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:46:50:220 ==>> [D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 12, ret: 6
[D][05:19:10][M2M ]tcpclient close[4]
[D][05:19:10][SAL ]Cellular task submsg id[12]
[D][05:19:10][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:10][CAT1]gsm read msg sub id: 9
[D][05:19:10][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:10][CAT1]<<< 
OK

[D][05:19:10][CAT1]exec over: func id: 9, ret: 6
[D][05:19:10][CAT1]sub id: 9, ret: 6

[D][05:19:10][SAL ]Cellular task submsg id[68]
[D][05:19:10][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:10][SAL ]socket close ind. id[4]
[D][05:19:10][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:10][COMM]1x1 frm_can_tp_send ok
[D][05:19:10][CAT1]pdpdeact urc len[22]


2025-07-31 20:46:50:527 ==>> [E][05:19:10][COMM]1x1 rx timeout
[D][05:19:10][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:46:51:042 ==>> [E][05:19:10][COMM]1x1 rx timeout
[E][05:19:10][COMM]1x1 tp timeout
[E][05:19:10][COMM]1x1 error -3.
[W][05:19:10][COMM]CAN STOP!
[D][05:19:10][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 1------------
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------
[D][05:19:10][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:10][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1290
[D][05:19:10][COMM]bat sleep fail, reason:-1
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:10][COMM]accel parse set 0
[D][05:19:10][COMM]imu rest ok. 81981
[D][05:19:10][COMM]imu sleep 0
[W][05:19:10][COMM]now sleep


2025-07-31 20:46:51:227 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:46:51:234 ==>> 检测【检测33V休眠电流】
2025-07-31 20:46:51:246 ==>> 开始33V电流采样
2025-07-31 20:46:51:274 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:46:51:333 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:46:52:341 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:46:52:432 ==>> Current33V:????:13.69

2025-07-31 20:46:52:855 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:46:52:863 ==>> 【检测33V休眠电流】通过,【13.69uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:46:52:871 ==>> 该项需要延时执行
2025-07-31 20:46:54:872 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:46:54:885 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:46:54:912 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:46:54:964 ==>> 1A A1 00 00 FC 
Get AD_V2 1651mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 2024mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:46:55:911 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:46:55:924 ==>> 检测【打开小电池2】
2025-07-31 20:46:55:948 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:46:56:057 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:46:56:200 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:46:56:209 ==>> 该项需要延时执行
2025-07-31 20:46:56:704 ==>> 此处延时了:【500】毫秒
2025-07-31 20:46:56:716 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:46:56:771 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:46:56:788 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:46:56:990 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:46:56:998 ==>> 该项需要延时执行
2025-07-31 20:46:57:443 ==>> [D][05:19:17][COMM]------------ready to Power on Acckey 1------------
[D][05:19:17][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:17][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:17][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:17][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:17][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:17][COMM]----- get Acckey 1 and value:1------------
[W][05:19:17][COMM]CAN START!
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:17][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 88321
[D][05:19:17][COMM][Audio]exec status ready.
[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 6
[D][05:19:17][COMM

2025-07-31 20:46:57:488 ==>> ]imu wakeup ok. 88336
[D][05:19:17][COMM]imu wakeup 1
[W][05:19:17][COMM]wake up system, wakeupEvt=0x80
[D][05:19:17][COMM]frm_can_weigth_power_set 1
[D][05:19:17][COMM]Clear Sleep Block Evt
[D][05:19:17][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:17][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:46:57:503 ==>> 此处延时了:【500】毫秒
2025-07-31 20:46:57:515 ==>> 检测【进入休眠模式2】
2025-07-31 20:46:57:528 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:46:57:593 ==>> [W

2025-07-31 20:46:57:623 ==>> ][05:19:17][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:46:57:848 ==>> [E][05:19:17][COMM]1x1 rx timeout
[D][05:19:17][COMM]1x1 frm_can_tp_send ok
[D][05:19:17][COMM]msg 02A0 loss. last_tick:88306. cur_tick:88815. period:50
[D][05:19:17][COMM]msg 02A4 loss. last_tick:88306. cur_tick:88815. period:50
[D][05:19:17][COMM]msg 02A5 loss. last_tick:88306. cur_tick:88816. period:50
[D][05:19:17][COMM]msg 02A6 loss. last_tick:88306. cur_tick:88816. period:50
[D][05:19:17][COMM]msg 02A7 loss. last_tick:88306. cur_tick:88816. period:50
[D][05:19:17][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 88817
[D][05:19:17][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 88817


2025-07-31 20:46:58:178 ==>> [E][05:19:18][COMM]1x1 rx timeout
[E][05:19:18][COMM]1x1 tp timeout
[E][05:19:18][COMM]1x1 error -3.
[D][05:19:18][COMM]Main Task receive event:28 finished processing
[D][05:19:18][COMM]Main Task receive event:28
[D][05:19:18][COMM]prepare to sleep
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 6
[D][05:19:18][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:18][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:46:58:481 ==>> [D][05:19:18][COMM]msg 0220 loss. last_tick:88306. cur_tick:89311. period:100
[D][05:19:18][COMM]msg 0221 loss. last_tick:88306. cur_tick:89312. period:100
[D][05:19:18][COMM]msg 0224 loss. last_tick:88306. cur_tick:89312. period:100
[D][05:19:18][COMM]msg 0260 loss. last_tick:88306. cur_tick:89313. period:100
[D][05:19:18][COMM]msg 0280 loss. last_tick:88306. cur_tick:89313. period:100
[D][05:19:18][COMM]msg 02C0 loss. last_tick:88306. cur_tick:89313. period:100
[D][05:19:18][COMM]msg 02C1 loss. last_tick:88306. cur_tick:89314. period:100
[D][05:19:18][COMM]msg 02C2 loss. last_tick:88306. cur_tick:89314. period:100
[D][05:19:18][COMM]msg 02E0 loss. last_tick:88306. cur_tick:89314. period:100
[D][05:19:18][COMM]msg 02E1 loss. last_tick:88306. cur_tick:89315. period:100
[D][05:19:18][COMM]msg 02E2 loss. last_tick:88306. cur_tick:89315. period:100
[D][05:19:18][COMM]msg 0300 loss. last_tick:88306. cur_tick:89316. period:100
[D][05:19:18][COMM]msg 0301 loss. last_tick:88306. cur_tick:89316. period:100
[D][05:19:18][COMM]bat msg 0240 loss. last_tick:88306. cur_tick:89316. period:100. j,i:1 54
[D][05:19:18][COMM]bat msg 0241 loss. last_tick:88306. cur_tick:89317. period:100. j,i:2 55
[D][05:19:18][COMM]bat msg 0242 loss. 

2025-07-31 20:46:58:586 ==>> last_tick:88306. cur_tick:89317. period:100. j,i:3 56
[D][05:19:18][COMM]bat msg 0244 loss. last_tick:88306. cur_tick:89317. period:100. j,i:5 58
[D][05:19:18][COMM]bat msg 024E loss. last_tick:88306. cur_tick:89318. period:100. j,i:15 68
[D][05:19:18][COMM]bat msg 024F loss. last_tick:88306. cur_tick:89318. period:100. j,i:16 69
[D][05:19:18][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 89319
[D][05:19:18][COMM]CAN message bat fault change: 0x00000000->0x0001802E 89319
[D][05:19:18][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 89319
                                                                              

2025-07-31 20:46:58:841 ==>> [D][05:19:18][COMM]msg 0222 loss. last_tick:88306. cur_tick:89813. period:150
[D][05:19:18][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 89814
[D][05:19:18][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:18][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:18][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:46:59:052 ==>> [E][05:19:18][COMM]1x1 rx timeout
[E][05:19:18][COMM]1x1 tp timeout
[E][05:19:18][COMM]1x1 error -3.
[W][05:19:18][COMM]CAN STOP!
[D][05:19:18][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:18][COMM]------------ready to Power off Acckey 1------------
[D][05:19:18][COMM]------------ready to Power off Acckey 2------------
[D][05:19:18][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:18][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 98
[D][05:19:18][COMM]bat sleep fail, reason:-1
[D][05:19:18][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:18][COMM]accel parse set 0
[D][05:19:18][COMM]imu rest ok. 90004
[D][05:19:19][COMM]imu sleep 0
[W][05:19:19][COMM]now sleep


2025-07-31 20:46:59:373 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:46:59:381 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:46:59:393 ==>> 开始小电池电流采样
2025-07-31 20:46:59:413 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:46:59:482 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:47:00:494 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:47:00:571 ==>> CurrentBattery:ƽ��:67.71

2025-07-31 20:47:00:999 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:47:01:008 ==>> 【检测小电池休眠电流】通过,【67.71uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:47:01:016 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:47:01:036 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:47:01:060 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:47:01:301 ==>> [D][05:19:21][COMM]------------ready to Power on Acckey 1------------
[D][05:19:21][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:21][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:21][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 1
[D][05:19:21][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:21][COMM]----- get Acckey 1 and value:1------------
[W][05:19:21][COMM]CAN START!
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:21][COMM]CAN message bat fault change: 0x0001802E->0x00000000 92201
[D][05:19:21][COMM][Audio]exec status ready.
[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 12, ret: 6
[D][05:19:21][COMM]imu wakeup ok. 92215
[D][05:19:21][COMM]imu wakeup 1
[W][05:19:21][COMM]wake up system, wakeupEvt=0x80
[D][05:19:21][COMM]frm_can_weigth_power_set 1
[D][05:19:21][COMM]Clear Sleep Block Evt
[D][05:19:21][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:21][COMM]1x1 frm_can_tp_send ok
[D][05:19:21][COMM]read battery soc:0


2025-07-31 20:47:01:336 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:47:01:344 ==>> 该项需要延时执行
2025-07-31 20:47:01:744 ==>> [D][05:19:21][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[E][05:19:21][COMM]1x1 rx timeout
[D][05:19:21][COMM]1x1 frm_can_tp_send ok
[D][05:19:21][COMM]msg 02A0 loss. last_tick:92184. cur_tick:92695. period:50
[D][05:19:21][COMM]msg 02A4 loss. last_tick:92184. cur_tick:92696. period:50
[D][05:19:21][COMM]msg 02A5 loss. last_tick:92184. cur_tick:92696. period:50
[D][05:19:21][COMM]msg 02A6 loss. last_tick:92184. cur_tick:92697. period:50
[D][05:19:21][COMM]msg 02A7 loss. last_tick:92184. cur_tick:92697. period:50
[D][05:19:21][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 92697
[D][05:19:21][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 92698


2025-07-31 20:47:01:849 ==>> 此处延时了:【500】毫秒
2025-07-31 20:47:01:865 ==>> 检测【检测唤醒】
2025-07-31 20:47:01:889 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:47:02:541 ==>> [D][05:19:21][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[E][05:19:22][COMM]1x1 rx timeout
[E][05:19:22][COMM]1x1 tp timeout
[E][05:19:22][COMM]1x1 error -3.
[D][05:19:22][COMM]Main Task receive event:28 finished processing
[D][05:19:22][COMM]Main Task receive event:65
[D][05:19:22][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:22][COMM]Main Task receive event:65 finished processing
[D][05:19:22][COMM]Main Task receive event:60
[D][05:19:22][COMM]smart_helmet_vol=255,255
[D][05:19:22][COMM]report elecbike
[W][05:19:22][PROT]remove success[1629955162],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:22][COMM]Main Task receive event:60 finished processing
[D][05:19:22][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:22][PROT]index:0
[D][05:19:22][PROT]is_send:1
[D][05:19:22][PROT]sequence_num:9
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:3
[D][05:19:22][PROT]send_path:0x3
[D][05:19:22][PROT]msg_type:0x5d03
[D][05:19:22][PROT]=========

2025-07-31 20:47:02:648 ==>> ==================================================
[W][05:19:22][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]Sending traceid[999999999990000A]
[D][05:19:22][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:22][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:22][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:22][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:22][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:22][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:22][SAL ]open socket ind id[4], rst[0]
[D][05:19:22][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:22][SAL ]Cellular task submsg id[8]
[D][05:19:22][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:22][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:22][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:22][CAT1]gsm read msg sub id: 8
[D][05:19:22][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:

2025-07-31 20:47:02:753 ==>> 22][CAT1]<<< 
+CGATT: 0

OK

[W][05:19:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:22][FCTY]==========Modules-nRF5340 ==========
[D][05:19:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:22][FCTY]DeviceID    = 460130071539126
[D][05:19:22][FCTY]HardwareID  = 867222087736387
[D][05:19:22][FCTY]MoBikeID    = 9999999999
[D][05:19:22][FCTY]LockID      = FFFFFFFFFF
[D][05:19:22][FCTY]BLEFWVersion= 105
[D][05:19:22][FCTY]BLEMacAddr   = C6DD9F8AF215
[D][05:19:22][FCTY]Bat         = 3924 mv
[D][05:19:22][FCTY]Current     = 0 ma
[D][05:19:22][FCTY]VBUS        = 11700 mv
[D][05:19:22][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:22][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:22][FCTY]Ext battery vol = 32, adc = 1284
[D][05:19:22][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:19:22][FCTY]Bike Type flag is invalied
[D][05:19:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:22][FCTY]CAT1_K

2025-07-31 20:47:02:859 ==>> ERNEL_RTK = 1.2.4
[D][05:19:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:22][FCTY]Bat1         = 3815 mv
[D][05:19:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:22][FCTY]==========Modules-nRF5340 ==========
[D][05:19:22][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:22][CAT1]<<< 
+CME ERROR: 100

[D][05:19:22][COMM]msg 0220 loss. last_tick:92183. cur_tick:93191. period:100
[D][05:19:22][COMM]msg 0221 loss. last_tick:92183. cur_tick:93191. period:100
[D][05:19:22][COMM]msg 0224 loss. last_tick:92184. cur_tick:93192. period:100
[D][05:19:22][COMM]msg 0260 loss. last_tick:92184. cur_tick:93192. period:100
[D][05:19:22][COMM]msg 0280 loss. last_tick:92184. cur_tick:93192. period:100
[D][05:19:22][COMM]msg 02C0 loss. last_tick:92184. cur_tick:93193. period:100
[D][05:19:22][COMM]msg 02C1 loss. last_tick:92184. cur_tick:93193. period:100
[D][05:19:22][COMM]msg 02C2 loss. last_tick:92184. cur_tick:93193. period:100
[D][05:19:22][COMM]msg 02E0 loss. last_tick:92184. cur_tick:93194. period:100
[D][05:19:22][COMM]msg 02E1 loss. last_tick:92184. cur_tick:93194. period:100
[D][05:19:22][COMM]msg 02E

2025-07-31 20:47:02:963 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:47:02:971 ==>> 检测【关机】
2025-07-31 20:47:02:999 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:47:03:013 ==>> 2 loss. last_tick:92184. cur_tick:93194. period:100
[D][05:19:22][COMM]msg 0300 loss. last_tick:92184. cur_tick:93195. period:100
[D][05:19:22][COMM]msg 0301 loss. last_tick:92184. cur_tick:93195. period:100
[D][05:19:22][COMM]bat msg 0240 loss. last_tick:92184. cur_tick:93196. period:100. j,i:1 54
[D][05:19:22][COMM]bat msg 0241 loss. last_tick:92184. cur_tick:93196. period:100. j,i:2 55
[D][05:19:22][COMM]bat msg 0242 loss. last_tick:92184. cur_tick:93197. period:100. j,i:3 56
[D][05:19:22][COMM]bat msg 0244 loss. last_tick:92184. cur_tick:93197. period:100. j,i:5 58
[D][05:19:22][COMM]bat msg 024E loss. last_tick:92184. cur_tick:93197. period:100. j,i:15 68
[D][05:19:22][COMM]bat msg 024F loss. last_tick:92184. cur_tick:93198. period:100. j,i:16 69
[D][05:19:22][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 93198
[D][05:19:22][COMM]CAN message bat fault change: 0x00000000->0x0001802E 93198
[D][05:19:22][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 93199


2025-07-31 20:47:03:235 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 20:47:03:341 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:47:03:445 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           t state2 fail 204
[D][05:19:22][COMM]get soh error
[E][05:19:22][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:22][COMM]report elecbike
[W][05:19:22][PROT]remove

2025-07-31 20:47:03:551 ==>>  success[1629955162],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:22][PROT]add success [1629955162],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:22][COMM]Main Task receive event:60 finished processing
[D][05:19:22][COMM]Main Task receive event:61
[D][05:19:22][COMM][D301]:type:3, trace id:280
[D][05:19:22][COMM]id[], hw[000
[D][05:19:22][COMM]get mcMaincircuitVolt error
[D][05:19:22][COMM]get mcSubcircuitVolt error
[D][05:19:22][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:22][COMM]BAT CAN get state1 Fail 204
[D][05:19:22][COMM]BAT CAN get soc Fail, 204
[D][05:19:22][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:22][PROT]index:1
[D][05:19:22][PROT]is_send:1
[D][05:19:22][PROT]sequence_num:10
[D][05:19:22][PROT]retry_timeout:0
[D][05:19:22][PROT]retry_times:3
[D][05:19:22][PROT]send_path:0x3
[D][05:19:22][PROT]msg_type:0x5d03
[D][05:19:22][PROT]===========================================================
[W][05:19:22][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955162]
[D][05:19:22][PROT]===========================================================
[D][05:19:22][PROT]Sending traceid[999999999990000B]
[D][05:19:22][BLE ]B

2025-07-31 20:47:03:656 ==>> LE_WRN [ble_service_get_current_send_enabled:28] ble                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:47:03:761 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:47:03:851 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:47:03:986 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:47:04:188 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:24][COMM]arm_hub_enable: hub power: 0
[D][05:19:24][HSDK]hexlog index save 0 5120 187 @ 0 : 0
[D][05:19:24][HSDK]write save hexlog index [0]
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:24][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:47:04:621 ==>> [W][05:19:24][COMM]Power Off


2025-07-31 20:47:04:826 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:47:04:835 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:47:04:849 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:47:04:953 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:47:05:058 ==>> [D][05:19:25][FCTY]get_ext_48v_vol retry i = 0,volt = 18
[D][05:19:25][FCTY]get_ext_48v_vol retry i = 1,volt = 18
[D][05:19:25][FCTY]get_ext_4

2025-07-31 20:47:05:131 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:47:05:143 ==>> 检测【检测小电池关机电流】
2025-07-31 20:47:05:157 ==>> 开始小电池电流采样
2025-07-31 20:47:05:183 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:47:05:238 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:47:06:242 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:47:06:289 ==>> CurrentBattery:ƽ��:67.19

2025-07-31 20:47:06:755 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:47:06:766 ==>> 【检测小电池关机电流】通过,【67.19uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:47:07:134 ==>> MES过站成功
2025-07-31 20:47:07:143 ==>> #################### 【测试结束】 ####################
2025-07-31 20:47:07:188 ==>> 关闭5V供电
2025-07-31 20:47:07:202 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:47:07:263 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:47:08:195 ==>> 关闭5V供电成功
2025-07-31 20:47:08:209 ==>> 关闭33V供电
2025-07-31 20:47:08:224 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:47:08:257 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:47:09:200 ==>> 关闭33V供电成功
2025-07-31 20:47:09:214 ==>> 关闭3.7V供电
2025-07-31 20:47:09:221 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:47:09:262 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:47:10:212 ==>> 关闭3.7V供电成功
