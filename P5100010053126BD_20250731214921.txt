2025-07-31 21:49:21:161 ==>> MES查站成功:
查站序号:P5100010053126BD验证通过
2025-07-31 21:49:21:169 ==>> 扫码结果:P5100010053126BD
2025-07-31 21:49:21:170 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:49:21:172 ==>> 测试参数版本:2024.10.11
2025-07-31 21:49:21:174 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:49:21:175 ==>> 检测【打开透传】
2025-07-31 21:49:21:177 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:49:21:280 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:49:21:576 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:49:21:582 ==>> 检测【检测接地电压】
2025-07-31 21:49:21:585 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:49:21:691 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:49:21:873 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:49:21:876 ==>> 检测【打开小电池】
2025-07-31 21:49:21:879 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:49:21:991 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:49:22:161 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:49:22:162 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:49:22:164 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:49:22:279 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:49:22:635 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:49:22:637 ==>> 检测【等待设备启动】
2025-07-31 21:49:22:640 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:49:22:765 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:49:22:947 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:49:23:655 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:49:23:657 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:49:24:049 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:49:24:520 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:49:24:758 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:49:24:761 ==>> 检测【产品通信】
2025-07-31 21:49:24:764 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:49:24:976 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:49:25:071 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:49:25:073 ==>> 检测【初始化完成检测】
2025-07-31 21:49:25:076 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:49:25:171 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:49:25:276 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_po

2025-07-31 21:49:25:321 ==>> weron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:49:25:414 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:49:25:416 ==>> 检测【关闭大灯控制1】
2025-07-31 21:49:25:418 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:49:25:591 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:25:696 ==>>                                            121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main T

2025-07-31 21:49:25:720 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:49:25:723 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:49:25:725 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:49:25:728 ==>> ask receive event:121 finished processing


2025-07-31 21:49:25:876 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:49:26:008 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:49:26:011 ==>> 检测【关闭仪表供电】
2025-07-31 21:49:26:012 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:49:26:180 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:49:26:299 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:49:26:302 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:49:26:304 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:49:26:471 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:49:26:576 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:26:611 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:49:26:613 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:49:26:615 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:49:26:741 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:49:26:914 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:49:26:916 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:49:26:919 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:49:27:045 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:49:27:191 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:49:27:193 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:49:27:196 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:49:27:287 ==>> 5A A5 01 5A A5 


2025-07-31 21:49:27:392 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:49:27:481 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:49:27:485 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:49:27:487 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:49:27:497 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 21:49:27:602 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:27:769 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:49:27:772 ==>> 该项需要延时执行
2025-07-31 21:49:28:119 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5004. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5004. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5005. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5005. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5006. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5006. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5007. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5007. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5008. period:500. j,i:24 77
[D][05:17:53][COMM]CAN

2025-07-31 21:49:28:149 ==>>  message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5008
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5008


2025-07-31 21:49:28:608 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:29:064 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:49:29:562 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM]

2025-07-31 21:49:29:667 ==>> [Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_ind

2025-07-31 21:49:29:772 ==>> ex:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]

2025-07-31 21:49:29:877 ==>> BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                                                         

2025-07-31 21:49:30:645 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:31:491 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:49:31:656 ==>> [D][05:17:57][COMM]8696 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:31:776 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:49:31:779 ==>> 检测【33V输入电压ADC】
2025-07-31 21:49:31:793 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:49:32:100 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:17:58][COMM]adc read out 24v adc:1322  volt:33437 mv
[D][05:17:58][COMM]adc read left brake adc:21  volt:27 mv
[D][05:17:58][COMM]adc read right brake adc:18  volt:23 mv
[D][05:17:58][COMM]adc read throttle adc:23  volt:30 mv
[D][05:17:58][COMM]adc read battery ts volt:21 mv
[D][05:17:58][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:17:58][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:49:32:320 ==>> 【33V输入电压ADC】通过,【32678mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:49:32:322 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:49:32:325 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:49:32:391 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2758mV
Get AD_V6 1995mV
Get AD_V7 1097mV
OVER 150


2025-07-31 21:49:32:592 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:32:603 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:49:32:622 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:32:629 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:49:32:637 ==>> 原始值:【2758】, 乘以分压基数【2】还原值:【5516】
2025-07-31 21:49:32:642 ==>> 【TP68_VCC5V5(ADV5)】通过,【5516mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:49:32:646 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:49:32:673 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:49:32:681 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:49:32:684 ==>> [D][05:17:58][COMM]9707 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:49:32:710 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:49:32:712 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:49:32:783 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1662mV
Get AD_V4 1mV
Get AD_V5 2756mV
Get AD_V6 1995mV
Get AD_V7 1096mV
OVER 150


2025-07-31 21:49:32:991 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:32:993 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:49:33:012 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:33:015 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:49:33:017 ==>> 原始值:【2756】, 乘以分压基数【2】还原值:【5512】
2025-07-31 21:49:33:023 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10015
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10015


2025-07-31 21:49:33:033 ==>> 【TP68_VCC5V5(ADV5)】通过,【5512mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:49:33:035 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:49:33:056 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:49:33:060 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:49:33:086 ==>> 【TP1_VCC12V(ADV7)】通过,【1096mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:49:33:092 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:49:33:203 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 1996mV
Get AD_V7 1097mV
OVER 150


2025-07-31 21:49:33:361 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:33:386 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:49:33:408 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:33:410 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:49:33:411 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 21:49:33:431 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:49:33:433 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:49:33:455 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:49:33:475 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:49:33:485 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:49:33:487 ==>> 检测【打开WIFI(1)】
2025-07-31 21:49:33:489 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:49:33:504 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][COMM]read battery soc:255


2025-07-31 21:49:33:894 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10718 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M

2025-07-31 21:49:33:924 ==>> 2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:49:34:024 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:49:34:027 ==>> 检测【清空消息队列(1)】
2025-07-31 21:49:34:030 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:49:34:330 ==>>                                                                                                [CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087843787

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541237

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:49:34:578 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:49:34:581 ==>> 检测【打开GPS(1)】
2025-07-31 21:49:34:585 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:49:34:661 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:49:34:766 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:49:34:887 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:49:34:891 ==>> 检测【打开GSM联网】
2025-07-31 21:49:34:894 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:49:35:072 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:49:35:206 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:49:35:210 ==>> 检测【打开仪表供电1】
2025-07-31 21:49:35:213 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:49:35:269 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:49:35:374 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:49:35:509 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 21:49:35:525 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:49:35:527 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:49:35:531 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:49:35:675 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:49:35:860 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:49:35:863 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:49:35:867 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:49:36:069 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33224]


2025-07-31 21:49:36:172 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:49:36:175 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:49:36:179 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:49:36:387 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:49:36:486 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:49:36:489 ==>> 检测【AD_V20电压】
2025-07-31 21:49:36:492 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:49:36:597 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:49:36:717 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13730 imu init OK
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:49:36:931 ==>> 本次取值间隔时间:326ms
2025-07-31 21:49:36:956 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:49:36:961 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:49:37:066 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:49:37:187 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1653mV
OVER 150


2025-07-31 21:49:37:217 ==>> 本次取值间隔时间:142ms
2025-07-31 21:49:37:253 ==>> 【AD_V20电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:49:37:255 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:49:37:257 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:49:37:382 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:49:37:551 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:49:37:553 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:49:37:581 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:49:37:670 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 21:49:38:090 ==>>                                                                                                                                                                                                                                  ]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F38373334303F3007

[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][0

2025-07-31 21:49:38:195 ==>> 5:18:03][CAT1]tx ret[8] >>> AT+CSQ

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.75.122.195"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:49:38:255 ==>>                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:49:38:575 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:49:38:819 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:49:39:452 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:49:39:602 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:49:39:677 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,42,25,,,42,59,,,41,41,,,41,1*73

$GBGSV,3,2,09,39,,,40,40,,,40,60,,,40,24,,,39,1*7E

$GBGSV,3,3,09,34,,,39,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1676.659,1676.659,53.537,2097152,2097152,2097152*48

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 21:49:39:782 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_displ

2025-07-31 21:49:39:812 ==>> ay:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:49:40:598 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,42,25,,,42,3,,,42,59,,,41,1*4F

$GBGSV,5,2,17,41,,,41,60,,,41,39,,,40,40,,,40,1*7A

$GBGSV,5,3,17,24,,,39,34,,,38,16,,,38,44,,,38,1*71

$GBGSV,5,4,17,11,,,36,7,,,36,1,,,38,2,,,37,1*4A

$GBGSV,5,5,17,43,,,36,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1640.516,1640.516,52.431,2097152,2097152,2097152*4E



2025-07-31 21:49:40:628 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 21:49:40:632 ==>> #################### 【测试结束】 ####################
2025-07-31 21:49:40:682 ==>> 关闭5V供电
2025-07-31 21:49:40:686 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:49:40:793 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:49:41:627 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,25,,,41,3,,,41,60,,,41,1*4B

$GBGSV,5,2,19,59,,,40,41,,,40,39,,,40,40,,,40,1*7E

$GBGSV,5,3,19,24,,,39,34,,,38,16,,,38,1,,,38,1*4E

$GBGSV,5,4,19,44,,,37,7,,,37,11,,,36,2,,,35,1*79

$GBGSV,5,5,19,43,,,32,5,,,32,4,,,32,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1568.879,1568.879,50.198,2097152,2097152,2097152*4A

[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:49:41:687 ==>> 关闭5V供电成功
2025-07-31 21:49:41:691 ==>> 关闭33V供电
2025-07-31 21:49:41:695 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:49:41:777 ==>> 5A A5 02 5A A5 


2025-07-31 21:49:41:882 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:49:41:987 ==>> [D][05:18:08][FCTY]get_ext_48v_vol retry i = 0,volt = 15
[D][05:18:08][FCTY]get_e

2025-07-31 21:49:42:047 ==>> xt_48v_vol retry i = 1,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 2,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 3,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 4,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 5,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 6,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 7,volt = 15
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 8,volt = 15


2025-07-31 21:49:42:305 ==>> [D][05:18:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 21:49:42:655 ==>> $GBGGA,134946.457,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,42,25,,,41,3,,,40,60,,,40,1*43

$GBGSV,6,2,21,59,,,40,41,,,40,39,,,40,40,,,40,1*76

$GBGSV,6,3,21,24,,,39,34,,,38,16,,,38,1,,,38,1*46

$GBGSV,6,4,21,7,,,38,44,,,37,11,,,36,2,,,36,1*7D

$GBGSV,6,5,21,38,,,32,43,,,32,5,,,32,4,,,32,1*7B

$GBGSV,6,6,21,23,,,39,1*7E

$GBRMC,134946.457,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,134946.457,0.000,1556.772,1556.772,49.814,2097152,2097152,2097152*5A



2025-07-31 21:49:42:700 ==>> 关闭33V供电成功
2025-07-31 21:49:42:705 ==>> 关闭3.7V供电
2025-07-31 21:49:42:731 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:49:42:760 ==>> 6

2025-07-31 21:49:42:790 ==>> A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:49:43:284 ==>>  

