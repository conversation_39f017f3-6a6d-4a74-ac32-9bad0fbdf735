2025-07-31 20:19:14:862 ==>> MES查站成功:
查站序号:P5100010053126A8验证通过
2025-07-31 20:19:14:866 ==>> 扫码结果:P5100010053126A8
2025-07-31 20:19:14:868 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:19:14:869 ==>> 测试参数版本:2024.10.11
2025-07-31 20:19:14:871 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:19:14:873 ==>> 检测【打开透传】
2025-07-31 20:19:14:875 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:19:14:947 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:19:15:221 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:19:15:225 ==>> 检测【检测接地电压】
2025-07-31 20:19:15:227 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:19:15:348 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:19:15:544 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:19:15:546 ==>> 检测【打开小电池】
2025-07-31 20:19:15:548 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:19:15:642 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:19:15:852 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:19:15:854 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:19:15:858 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:19:15:949 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:19:16:163 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:19:16:166 ==>> 检测【等待设备启动】
2025-07-31 20:19:16:169 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:19:16:414 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:19:16:596 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:19:17:199 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:19:17:229 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:19:17:289 ==>>                                                    

2025-07-31 20:19:17:691 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:19:18:167 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:19:18:262 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:19:18:264 ==>> 检测【产品通信】
2025-07-31 20:19:18:266 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:19:18:411 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:19:18:534 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:19:18:536 ==>> 检测【初始化完成检测】
2025-07-31 20:19:18:538 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:19:18:810 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
                                                             

2025-07-31 20:19:19:073 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:19:19:076 ==>> 检测【关闭大灯控制1】
2025-07-31 20:19:19:077 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:19:19:241 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:19:346 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:19:19:351 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:19:19:353 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:19:19:355 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:19:19:543 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:19:19:622 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:19:19:624 ==>> 检测【关闭仪表供电】
2025-07-31 20:19:19:626 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:19:19:833 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:19:19:896 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:19:19:898 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:19:19:901 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:19:20:000 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:19:20:186 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:19:20:188 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:19:20:214 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:19:20:259 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:20:304 ==>>                OMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:19:20:458 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:19:20:461 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:19:20:463 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:19:20:629 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:19:20:768 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:19:20:771 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:19:20:773 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:19:20:844 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:19:20:934 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:19:21:070 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:19:21:072 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:19:21:074 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:19:21:146 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:19:21:251 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:21:366 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:19:21:368 ==>> 该项需要延时执行
2025-07-31 20:19:21:786 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]

2025-07-31 20:19:21:816 ==>> CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 20:19:22:266 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:22:525 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:19:23:003 ==>>                                                                                                                                        :54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904]

2025-07-31 20:19:23:108 ==>> .echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][

2025-07-31 20:19:23:213 ==>> PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state

2025-07-31 20:19:23:303 ==>> 1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 20:19:23:469 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:19:24:291 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:24:927 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:19:25:295 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:25:370 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:19:25:373 ==>> 检测【33V输入电压ADC】
2025-07-31 20:19:25:376 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:25:662 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:17:57][COMM]adc read out 24v adc:1323  volt:33462 mv
[D][05:17:57][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read throttle adc:6  volt:7 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2395  volt:3859 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 20:19:25:910 ==>> 【33V输入电压ADC】通过,【32804mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:19:25:913 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:19:25:914 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:19:26:047 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1996mV
Get AD_V7 1100mV
OVER 150


2025-07-31 20:19:26:186 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:26:189 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:19:26:205 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:26:207 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:19:26:210 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:19:26:232 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:19:26:234 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:19:26:251 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:19:26:254 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:19:26:274 ==>> 【TP1_VCC12V(ADV7)】通过,【1100mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:19:26:276 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:19:26:308 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:19:26:353 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1666mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1993mV
Get AD_V7 1100mV
OVER 150


2025-07-31 20:19:26:617 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:26:619 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:19:26:661 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:26:664 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:19:26:666 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:19:26:677 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020


2025-07-31 20:19:26:707 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:19:26:709 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:19:26:752 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:19:26:755 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:19:26:801 ==>> 【TP1_VCC12V(ADV7)】通过,【1100mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:19:26:804 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:19:26:951 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1992mV
Get AD_V7 1100mV
OVER 150
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:19:27:112 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:27:115 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:19:27:163 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:27:165 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:19:27:168 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:19:27:208 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:19:27:211 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:19:27:254 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:19:27:256 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:19:27:360 ==>> 【TP1_VCC12V(ADV7)】通过,【1100mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:19:27:362 ==>> 检测【打开WIFI(1)】
2025-07-31 20:19:27:365 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:19:27:691 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:

2025-07-31 20:19:27:796 ==>> 17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16

2025-07-31 20:19:27:826 ==>> ] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:19:27:918 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:19:27:922 ==>> 检测【清空消息队列(1)】
2025-07-31 20:19:27:925 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:19:28:146 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:19:28:214 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:19:28:216 ==>> 检测【打开GPS(1)】
2025-07-31 20:19:28:220 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:19:28:311 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:19:28:416 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont 

2025-07-31 20:19:28:446 ==>> locating


2025-07-31 20:19:28:495 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:19:28:508 ==>> 检测【打开GSM联网】
2025-07-31 20:19:28:511 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:19:28:628 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:19:28:779 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:19:28:782 ==>> 检测【打开仪表供电1】
2025-07-31 20:19:28:785 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:19:28:966 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][COMM]read battery soc:255


2025-07-31 20:19:29:061 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:19:29:064 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:19:29:066 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:19:29:239 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:19:29:332 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:19:29:335 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:19:29:337 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:19:29:405 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 20:19:29:510 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33224]


2025-07-31 20:19:29:608 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:19:29:613 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:19:29:617 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:19:29:985 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087953545

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071541254

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT

2025-07-31 20:19:30:015 ==>> +QICSGP=1,1,"cmiot","","",0



2025-07-31 20:19:30:137 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:19:30:141 ==>> 检测【AD_V20电压】
2025-07-31 20:19:30:144 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:19:30:243 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:19:30:348 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:19:30:604 ==>> 本次取值间隔时间:354ms
2025-07-31 20:19:30:674 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:19:30:777 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:19:30:867 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1656mV
OVER 150


2025-07-31 20:19:30:942 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 20:19:31:124 ==>> 本次取值间隔时间:342ms
2025-07-31 20:19:31:165 ==>> 【AD_V20电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:31:167 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:19:31:171 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:19:31:249 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:19:31:324 ==>> [D][05:18:03][COMM]14730 imu init OK


2025-07-31 20:19:31:458 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:19:31:461 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:19:31:464 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:19:31:551 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:19:31:870 ==>> [D][05:18:04][CAT1]<<< 
OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:

2025-07-31 20:19:31:915 ==>> 04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:04][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:04][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:04][COMM]Main Task receive event:4
[D][05:18:04][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:04][GNSS]rtk_id: 303E383D3535373F3836323432333207



2025-07-31 20:19:31:990 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:19:32:042 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:19:32:045 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:19:32:047 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:19:32:140 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:19:32:245 ==>> [D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle 

2025-07-31 20:19:32:275 ==>> become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:19:32:351 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:19:32:355 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:19:32:359 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:19:32:625 ==>>                                                                           S_IND[5] rst[6]
[D][05:18:04][GNSS]location recv gms init done evt
[D][05:18:04][GNSS]GPS start. ret=0
[D][05:18:04][CAT1]gsm read msg sub id: 23
[D][05:18:04][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:04][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:19:32:888 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:19:32:891 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:19:32:893 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:19:33:042 ==>> [D][05:18:05][COMM]read battery soc:255
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:19:33:165 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:19:33:169 ==>> 检测【AD_V21电压】
2025-07-31 20:19:33:171 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:19:33:270 ==>> 1A A1 20 00 00 
Get AD_V21 1654mV
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:19:33:635 ==>> 本次取值间隔时间:466ms
2025-07-31 20:19:33:654 ==>> 【AD_V21电压】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:33:657 ==>> 检测【关闭仪表供电2】
2025-07-31 20:19:33:659 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:19:33:838 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:19:33:928 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:19:33:933 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:19:33:936 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:19:33:945 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:19:34:203 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,60,,,44,25,,,41,42,,,40,39,,,40,1*7F

$GBGSV,3,2,12,38,,,38,40,,,38,59,,,38,33,,,36,1*79

$GBGSV,3,3,12,34,,,33,41,,,33,13,,,48,14,,,40,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1579.579,1579.579,50.547,2097152,2097152,2097152*4C

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:19:34:308 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:19:34:465 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:19:34:468 ==>> 检测【打开AccKey2供电】
2025-07-31 20:19:34:471 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:19:34:630 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:19:34:742 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:19:34:745 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:19:34:748 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:35:133 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:07][COMM]adc read out 24v adc:1328  volt:33589 mv
[D][05:18:07][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:07][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:07][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:07][COMM]adc read battery ts volt:14 mv
[D][05:18:07][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:07][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2395  volt:3859 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
[D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,60,,,42,14,,,42,25,,,41,42,,,41,1*76

$GBGSV,4,2,16,24,,,41,39,,,40,59,,,40,13,,,39,1*7A

$GBGSV,4,3,16,38,,,38,40,,,38,33,,,38,8,,,36,1*4F

$GBGSV,4,4,16,41,,,35,34,,,33,44,,,33,4,,,37,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M

2025-07-31 20:19:35:163 ==>> ,0.000,N,0.000,K,N*20

$GBGST,,0.000,1594.762,1594.762,51.015,2097152,2097152,2097152*4F



2025-07-31 20:19:35:272 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33589mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:19:35:276 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:19:35:280 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:19:35:405 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:19:35:560 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:19:35:563 ==>> 该项需要延时执行
2025-07-31 20:19:36:102 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,3,,,44,60,,,42,14,,,42,25,,,42,1*41

$GBGSV,5,2,20,24,,,42,42,,,41,59,,,41,33,,,41,1*7C

$GBGSV,5,3,20,39,,,40,1,,,40,13,,,39,38,,,38,1*41

$GBGSV,5,4,20,40,,,38,41,,,37,8,,,36,2,,,35,1*72

$GBGSV,5,5,20,4,,,34,34,,,34,44,,,34,5,,,29,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1594.110,1594.110,51.032,2097152,2097152,2097152*4A



2025-07-31 20:19:36:988 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:19:37:093 ==>> $GBGGA,121940.918,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,3,,,42,60,,,42,14,,,42,25,,,42,1*40

$GBGSV,6,2,24,24,,,42,33,,,42,42,,,41,59,,,41,1*78

$GBGSV,6,3,24,39,,,40,1,,,39,13,,,39,38,,,38,1*48

$GBGSV,6,4,24,40,,,38,41,,,37,8,,,36,2,,,36,1*76

$GBGSV,6,5,24,6,,,36,34,,,34,44,,,34,16,,,34,1*47

$GBGSV,6,6,24,4,,,33,5,,,31,9,,,38,7,,,37,1*72

$GB

2025-07-31 20:19:37:124 ==>> RMC,121940.918,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121940.918,0.000,1581.093,1581.093,50.602,2097152,2097152,2097152*5F



2025-07-31 20:19:37:734 ==>> $GBGGA,121941.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,25,,,42,24,,,42,33,,,42,1*70

$GBGSV,7,2,26,3,,,41,60,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,9,,,37,41,,,37,7,,,37,1*71

$GBGSV,7,5,26,6,,,37,8,,,36,2,,,36,16,,,36,1*4A

$GBGSV,7,6,26,26,,,36,34,,,34,44,,,34,4,,,33,1*41

$GBGSV,7,7,26,5,,,32,10,,,32,1*46

$GBRMC,121941.518,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121941.518,0.000,1567.454,1567.454,50.154,2097152,2097152,2097152*56



2025-07-31 20:19:38:568 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:19:38:573 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:19:38:585 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:38:863 ==>> $GBGGA,121942.518,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,33,,,42,25,,,41,24,,,41,1*70

$GBGSV,7,2,26,3,,,41,60,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,39,1,,,39,13,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,9,,,37,41,,,37,6,,,37,1*70

$GBGSV,7,5,26,2,,,37,16,,,37,7,,,36,8,,,36,1*4A

$GBGSV,7,6,26,26,,,36,44,,,34,34,,,33,4,,,33,1*46

$GBGSV,7,7,26,5,,,33,10,,,33,1*46

$GBRMC,121942.518,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121942.518,0.000,1565.849,1565.849,50.092,2097152,2097152,2097152*5E

[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:11][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:11][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:11][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:11][COMM]adc read battery ts volt:14 mv
[D][05:18:11][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:11][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]ar

2025-07-31 20:19:38:908 ==>> m_hub adc read vbat adc:2395  volt:3859 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:19:39:013 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:19:39:121 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:19:39:134 ==>> 检测【打开AccKey1供电】
2025-07-31 20:19:39:139 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:19:39:331 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:19:39:395 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:19:39:400 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:19:39:405 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:19:39:436 ==>> 1A A1 00 40 00 
Get AD_V14 2589mV
OVER 150


2025-07-31 20:19:39:652 ==>> 原始值:【2589】, 乘以分压基数【2】还原值:【5178】
2025-07-31 20:19:39:671 ==>> 【读取AccKey1电压(ADV14)前】通过,【5178mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:19:39:675 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:19:39:681 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:39:728 ==>> $GBGGA,121943.518,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,33,,,42,60,,,42,25,,,41,1*73

$GBGSV,7,2,26,24,,,41,3,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,39,1,,,39,13,,,38,38,,,38,1*44

$GBGSV,7,4,26,40,,,38,9,,,37,41,,,37,2,,,37,1*74

$GBGSV,7,5,26,16,,,37,6,,,36,7,,,36,8,,,36,1*4F

$GBGSV,7,6,26,26,,,36,44,,,34,34,,,34,10,,,34,1*73

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,121943.518,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121943.518,0.000,1567.440,1567.440,50.140,2097152,2097152,2097152*51



2025-07-31 20:19:39:953 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:12][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:12][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:12][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:12][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:12][COMM]adc read battery ts volt:14 mv
[D][05:18:12][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:12][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2395  volt:3859 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:19:40:200 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5517mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:19:40:205 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:19:40:210 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:19:40:439 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:19:40:473 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:19:40:476 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:19:40:478 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:19:40:544 ==>> 1A A1 00 40 00 
Get AD_V14 2591mV
OVER 150


2025-07-31 20:19:40:724 ==>> 原始值:【2591】, 乘以分压基数【2】还原值:【5182】
2025-07-31 20:19:40:739 ==>> $GBGGA,121944.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,33,,,42,60,,,41,25,,,41,1*70

$GBGSV,7,2,26,24,,,41,3,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,39,1,,,39,13,,,38,38,,,38,1*44

$GBGSV,7,4,26,40,,,38,16,,,38,9,,,37,41,,,37,1*4E

$GBGSV,7,5,26,2,,,36,6,,,36,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,121944.518,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121944.518,0.000,1567.436,1567.436,50.136,2097152,2097152,2097152*57



2025-07-31 20:19:40:743 ==>> 【读取AccKey1电压(ADV14)后】通过,【5182mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:19:40:746 ==>> 检测【打开WIFI(2)】
2025-07-31 20:19:40:748 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:19:40:967 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:19:41:012 ==>>                                          

2025-07-31 20:19:41:016 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:19:41:020 ==>> 检测【转刹把供电】
2025-07-31 20:19:41:023 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:19:41:225 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:19:41:313 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:19:41:317 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:19:41:319 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:19:41:420 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:19:41:510 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:19:41:540 ==>> 00 00 00 00 00 
head err!


2025-07-31 20:19:41:736 ==>> $GBGGA,121945.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,60,,,41,25,,,41,1*71

$GBGSV,7,2,26,24,,,41,3,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,9,,,37,41,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,36,7,,,36,8,,,36,1*7A

$GBGSV,7,6,26,26,,,36,44,,,34,34,,,34,10,,,34,1*73

$GBGSV,7,7,26,4,,,33,5,,,33,1*73

$GBRMC,121945.518,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121945.518,0.000,1570.631,1570.631,50.243,2097152,2097152,2097152*57



2025-07-31 20:19:42:356 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:19:42:461 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:19:42:551 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2390mV
OVER 150


2025-07-31 20:19:42:626 ==>> 原始值:【2390】, 乘以分压基数【2】还原值:【4780】
2025-07-31 20:19:42:657 ==>> 【读取AD_V15电压(前)】通过,【4780mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:19:42:661 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:19:42:665 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:19:42:731 ==>> $GBGGA,121946.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,60,,,41,25,,,41,1*70

$GBGSV,7,2,26,24,,,41,3,,,41,42,,,41,59,,,41,1*48

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,9,,,37,41,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,36,7,,,36,8,,,36,1*7A

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121946.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121946.518,0.000,1572.217,1572.217,50.286,2097152,2097152,2097152*5D



2025-07-31 20:19:42:761 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:19:42:836 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2421mV
OVER 150


2025-07-31 20:19:42:926 ==>> 原始值:【2421】, 乘以分压基数【2】还原值:【4842】
2025-07-31 20:19:42:967 ==>> 【读取AD_V16电压(前)】通过,【4842mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:19:42:970 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:19:42:975 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:43:016 ==>> +WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,CC057790A620,-51
+WIFISCAN:4,2,CC057790A740,-67
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:15][CAT1]wifi scan report total[4]
[D][05:18:15][COMM]read battery soc:255


2025-07-31 20:19:43:257 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:15][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:15][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:15][COMM]adc read battery ts volt:12 mv
[D][05:18:15][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3078  volt:5410 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2395  volt:3859 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:19:43:362 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 20:19:43:514 ==>> 【转刹把供电电压(主控ADC)】通过,【5410mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:19:43:518 ==>> 检测【转刹把供电电压】
2025-07-31 20:19:43:523 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:43:862 ==>> $GBGGA,121947.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,60,,,41,1*79

$GBGSV,7,2,26,25,,,41,24,,,41,3,,,41,42,,,41,1*43

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,9,,,37,41,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121947.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121947.518,0.000,1577.004,1577.004,50.443,2097152,2097152,2097152*53

[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:16][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:16][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:16][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:16][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:16][COMM]adc read battery ts volt:14 mv
[D][05:18:16][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3086  volt:5424 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv


2025-07-31 20:19:43:907 ==>> 
[D][05:18:16][COMM]arm_hub adc read vbat adc:2395  volt:3859 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:19:44:048 ==>> 【转刹把供电电压】通过,【5424mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:19:44:052 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:19:44:055 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:19:44:221 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:19:44:336 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:19:44:340 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:19:44:344 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:19:44:452 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:19:44:542 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:19:44:581 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:19:44:584 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:19:44:589 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:19:44:694 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:19:44:769 ==>> $GBGGA,121948.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,25,,,42,1*7B

$GBGSV,7,2,26,60,,,41,24,,,41,3,,,41,42,,,41,1*42

$GBGSV,7,3,26,39,,,40,13,,,38,1,,,38,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,9,,,37,41,,,37,1*4E

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,32,1*75

$GBRMC,121948.518,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121948.518,0.000,1575.416,1575.416,50.398,2097152,2097152,2097152*5D

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:19:44:818 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:19:44:821 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:19:44:826 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:19:44:949 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:19:45:024 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:19:45:088 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:19:45:095 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:19:45:119 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:19:45:129 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:19:45:357 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:19:45:363 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:19:45:367 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:19:45:444 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:19:45:628 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:19:45:632 ==>> 检测【左刹电压测试1】
2025-07-31 20:19:45:636 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:45:732 ==>> $GBGGA,121949.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,59,,,41,25,,,41,1*7A

$GBGSV,7,2,26,60,,,41,24,,,41,3,,,41,42,,,41,1*42

$GBGSV,7,3,26,39,,,40,13,,,38,1,,,38,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,34,34,,,34,10,,,34,1*73

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121949.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121949.518,0.000,1572.217,1572.217,50.286,2097152,2097152,2097152*52



2025-07-31 20:19:45:957 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:18][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:18][COMM]adc read left brake adc:1727  volt:2276 mv
[D][05:18:18][COMM]adc read right brake adc:1726  volt:2275 mv
[D][05:18:18][COMM]adc read throttle adc:1727  volt:2276 mv
[D][05:18:18][COMM]adc read battery ts volt:15 mv
[D][05:18:18][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:18][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2394  volt:3857 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:19:46:156 ==>> 【左刹电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 20:19:46:160 ==>> 检测【右刹电压测试1】
2025-07-31 20:19:46:180 ==>> 【右刹电压测试1】通过,【2275】符合目标值【2250】至【2500】要求!
2025-07-31 20:19:46:184 ==>> 检测【转把电压测试1】
2025-07-31 20:19:46:199 ==>> 【转把电压测试1】通过,【2276】符合目标值【2250】至【2500】要求!
2025-07-31 20:19:46:202 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:19:46:207 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:19:46:351 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:19:46:500 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:19:46:504 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:19:46:508 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:19:46:653 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:19:46:728 ==>> $GBGGA,121950.518,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,25,,,42,1*7B

$GBGSV,7,2,26,3,,,42,60,,,41,24,,,41,42,,,41,1*41

$GBGSV,7,3,26,39,,,40,13,,,38,1,,,38,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121950.518,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121950.518,0.000,1580.197,1580.197,50.549,2097152,2097152,2097152*5E



2025-07-31 20:19:46:828 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:19:46:834 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:19:46:839 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:19:46:940 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:19:47:030 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:19:47:132 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:19:47:142 ==>> 检测【左刹电压测试2】
2025-07-31 20:19:47:147 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:19:47:450 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:19][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:19][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:19][COMM]adc read battery ts volt:14 mv
[D][05:18:19][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2394  volt:3857 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:19:47:700 ==>> 【左刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 20:19:47:707 ==>> 检测【右刹电压测试2】
2025-07-31 20:19:47:739 ==>> $GBGGA,121951.518,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,59,,,41,25,,,41,1*7A

$GBGSV,7,2,26,3,,,41,60,,,41,24,,,41,42,,,41,1*42

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,34,1*73

$GBRMC,121951.518,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121951.518,0.000,1576.995,1576.995,50.434,2097152,2097152,2097152*54



2025-07-31 20:19:47:783 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:19:47:790 ==>> 检测【转把电压测试2】
2025-07-31 20:19:47:841 ==>> 【转把电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 20:19:47:849 ==>> 检测【晶振检测】
2025-07-31 20:19:47:855 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:19:48:039 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 20:19:48:192 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:19:48:196 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:19:48:202 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:19:48:250 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1667mV
Get AD_V4 1651mV
Get AD_V5 2759mV
Get AD_V6 1992mV
Get AD_V7 1100mV
OVER 150


2025-07-31 20:19:48:468 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:19:48:472 ==>> 检测【检测BootVer】
2025-07-31 20:19:48:475 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:19:48:874 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071541254
[D][05:18:21][FCTY]HardwareID  = 867222087953545
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = FB12B0AFE0B8
[D][05:18:21][FCTY]Bat         = 3944 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11900 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1302
[D][05:18:21][FCTY]Acckey1 vol = 5526 mv, Acckey2 vol = 252 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION

2025-07-31 20:19:48:964 ==>>  = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3799 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121952.518,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,25,,,42,24,,,42,1*71

$GBGSV,7,2,26,59,,,41,3,,,41,60,,,41,42,,,41,1*48

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121952.518,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121952.518,0.000,1581.790,1581.790,50.598,2097152,2097152,2097152*50



2025-07-31 20:19:49:001 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:19:49:006 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:19:49:012 ==>> 检测【检测固件版本】
2025-07-31 20:19:49:023 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:19:49:026 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:19:49:030 ==>> 检测【检测蓝牙版本】
2025-07-31 20:19:49:033 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:19:49:051 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:19:49:059 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:19:49:064 ==>> 检测【检测MoBikeId】
2025-07-31 20:19:49:071 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:19:49:075 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:19:49:080 ==>> 检测【检测蓝牙地址】
2025-07-31 20:19:49:087 ==>> 取到目标值:FB12B0AFE0B8
2025-07-31 20:19:49:101 ==>> 【检测蓝牙地址】通过,【FB12B0AFE0B8】符合目标值【】要求!
2025-07-31 20:19:49:106 ==>> 提取到蓝牙地址:FB12B0AFE0B8
2025-07-31 20:19:49:113 ==>> 检测【BOARD_ID】
2025-07-31 20:19:49:131 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:19:49:135 ==>> 检测【检测充电电压】
2025-07-31 20:19:49:150 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:19:49:154 ==>> 检测【检测VBUS电压1】
2025-07-31 20:19:49:169 ==>> 【检测VBUS电压1】通过,【11900mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:19:49:173 ==>> 检测【检测充电电流】
2025-07-31 20:19:49:198 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:19:49:202 ==>> 检测【检测IMEI】
2025-07-31 20:19:49:205 ==>> 取到目标值:867222087953545
2025-07-31 20:19:49:227 ==>> 【检测IMEI】通过,【867222087953545】符合目标值【】要求!
2025-07-31 20:19:49:233 ==>> 提取到IMEI:867222087953545
2025-07-31 20:19:49:241 ==>> 检测【检测IMSI】
2025-07-31 20:19:49:247 ==>> 取到目标值:460130071541254
2025-07-31 20:19:49:262 ==>> 【检测IMSI】通过,【460130071541254】符合目标值【】要求!
2025-07-31 20:19:49:271 ==>> 提取到IMSI:460130071541254
2025-07-31 20:19:49:299 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:19:49:304 ==>> 取到目标值:460130071541254
2025-07-31 20:19:49:308 ==>> 【校验网络运营商(移动)】通过,【460130071541254】符合目标值【】要求!
2025-07-31 20:19:49:314 ==>> 检测【打开CAN通信】
2025-07-31 20:19:49:318 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:19:49:448 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:19:49:572 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:19:49:576 ==>> 检测【检测CAN通信】
2025-07-31 20:19:49:582 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:19:49:661 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:19:49:766 ==>> $GBGGA,121953.518,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,25,,,41,24,,,41,1*71

$GBGSV,7,2,26,59,,,41,3,,,41,60,,,41,42,,,41,1*48

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121953.518,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121953.518,0.000,1577.002,1577.002,50.440,2097152,2097152,2097152*55

[D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33118
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:19:49:841 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:19:49:846 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:19:49:852 ==>> 检测【关闭CAN通信】
2025-07-31 20:19:49:874 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:19:49:901 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:19:49:946 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:19:50:130 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:19:50:137 ==>> 检测【打印IMU STATE】
2025-07-31 20:19:50:143 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:19:50:337 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:1
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:19:50:400 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:19:50:405 ==>> 检测【六轴自检】
2025-07-31 20:19:50:411 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:19:50:749 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


$GBGGA,121954.518,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,25,,,41,1*78

$GBGSV,7,2,26,24,,,41,3,,,41,60,,,41,42,,,41,1*42

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,34,1*73

$GBRMC,121954.518,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121954.518,0.000,1580.190,1580.190,50.541,2097152,2097152,2097152*52



2025-07-31 20:19:51:024 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:19:51:759 ==>> $GBGGA,121955.518,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,59,,,42,24,,,42,1*7A

$GBGSV,7,2,26,3,,,42,25,,,41,60,,,41,42,,,41,1*40

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,34,,,34,10,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121955.518,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121955.518,0.000,1583.387,1583.387,50.651,2097152,2097152,2097152*51



2025-07-31 20:19:52:354 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:19:52:567 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35953 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-12,-22,4060]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 20:19:52:672 ==>> $GBGGA,121956.518,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,43,25,,,42,42,,,42,1*70

$GBGSV,7,2,26

2025-07-31 20:19:52:732 ==>> ,59,,,41,24,,,41,3,,,41,60,,,41,1*48

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,10,,,35,34,,,34,1*73

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,121956.518,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121956.518,0.000,1584.979,1584.979,50.700,2097152,2097152,2097152*57



2025-07-31 20:19:52:752 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:19:52:758 ==>> 检测【打印IMU STATE2】
2025-07-31 20:19:52:765 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:19:52:942 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:19:53:036 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:19:53:040 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:19:53:043 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:19:53:049 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:19:53:152 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:19:53:257 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vo

2025-07-31 20:19:53:302 ==>> l retry i = 3,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 14
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 8,volt = 14


2025-07-31 20:19:53:306 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:19:53:310 ==>> 检测【检测VBUS电压2】
2025-07-31 20:19:53:314 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:19:53:762 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541254
[D][05:18:25][FCTY]HardwareID  = 867222087953545
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FB12B0AFE0B8
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11900 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 8, adc = 318
[D][05:18:25][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLA

2025-07-31 20:19:53:852 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:19:53:868 ==>> TFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3799 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
$GBGGA,121957.518,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,25,,,42,59,,,42,1*7B

$GBGSV,7,2,26,24,,,42,3,,,42,42,,,41,60,,,41,1*42

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,10,,,34,34,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,34,1*73

$GBRMC,121957.518,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121957.518,0.000,1586.575,1586.575,50.751,2097152,2097152,2097152*52



2025-07-31 20:19:54:201 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541254
[D][05:18:26][FCTY]HardwareID  = 867222087953545
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = FB12B0AFE0B8
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11900 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 184
[D][05:18:26][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1

2025-07-31 20:19:54:246 ==>> _GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3799 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:19:54:419 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:19:54:887 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071541254
[D][05:18:27][FCTY]HardwareID  = 867222087953545
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = FB12B0AFE0B8
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 152
[D][05:18:27][FCTY]Acckey1 vol = 5524 mv, Acckey2 vol = 202 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION 

2025-07-31 20:19:54:976 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:19:54:981 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:19:54:988 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:19:55:011 ==>> = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3799 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,121958.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,25,,,42,24,,,42,1*71

$GBGSV,7,2,26,59,,,41,3,,,41,42,,,41,60,,,41,1*48

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,37,7,,,36,8,,,36,1*7B

$GBGSV,7,6,26,26,,,36,44,,,35,10,,,34,34,,,34,1*72

$GBGSV,7,7,26,5,,,34,4,,,34,1*73

$GBRMC,121958.518,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121958.518,0.000,1583.381,1583.381,50.645,2097152,2097152,2097152*59

[D][05:18:27][COMM]msg 0601 loss. last_tick:33116. cur_tick:38117. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38117


2025-07-31 20:19:55:052 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:19:55:291 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:19:55:295 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:19:55:302 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:19:55:344 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:19:55:449 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 21
[D][05:18:27][COMM]read battery soc:255
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_ev

2025-07-31 20:19:55:554 ==>> ent = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[

2025-07-31 20:19:55:589 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:19:55:594 ==>> 检测【打开WIFI(3)】
2025-07-31 20:19:55:597 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:19:55:659 ==>> D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:27][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SA

2025-07-31 20:19:55:764 ==>> L ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5173FFE9823345B406D36BAA988786D9BA6681BED8255943EF84E79D3A1E4AA946DD24F62007C981673B21AB4C8A589F9B438104C93977DBA1019C3E4A08E8383C3BB70D735C42F708B807CE86CC354B3A77A
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]sub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]


2025-07-31 20:19:55:870 ==>>                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 20:19:55:945 ==>>                                                                                                                                                                                                                                                     [D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:19:56:116 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:19:56:121 ==>> 检测【扩展芯片hw】
2025-07-31 20:19:56:127 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:19:56:343 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:28][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:19:56:402 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:19:56:407 ==>> 检测【扩展芯片boot】
2025-07-31 20:19:56:431 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:19:56:436 ==>> 检测【扩展芯片sw】
2025-07-31 20:19:56:451 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:19:56:458 ==>> 检测【检测音频FLASH】
2025-07-31 20:19:56:482 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:19:57:435 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
$GBGGA,122000.518,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,25,,,42,24,,,41,1*72

$GBGSV,7,2,26,59,,,41,3,,,41,42,,,41,60,,,41,1*48

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,37,2,,,36,7,,,36,26,,,36,1*46

$GBGSV,7,6,26,8,,,35,44,,,35,10,,,34,34,,,34,1*4D

$GBGSV,7,7,26,5,,,34,4,,,33,1*74

$GBRMC,122000.518,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122000.518,0.000,1575.412,1575.412,50.394,2097152,2097152,2097152*57

[D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -51

2025-07-31 20:19:57:539 ==>> 6, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66

2025-07-31 20:19:57:644 ==>> 
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get bat work state err
[W][05:18

2025-07-31 20:19:57:749 ==>> :29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][PROT]index:1
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:29][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2

2025-07-31 20:19:57:854 ==>> M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send

2025-07-31 20:19:57:959 ==>> :AT+AUDIODATAHEX=0,10800,0

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]

2025-07-31 20:19:58:064 ==>> f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:29][COMM]read battery soc:255
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:19:58:793 ==>> $GBGGA,122002.518,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,14,,,42,25,,,42,1*71

$GBGSV,7,2,26,60,,,41,3,,,41,59,,,41,42,,,41,1*48

$GBGSV,7,3,26,39,,,40,13,,,39,1,,,39,38,,,38,1*4B

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,2,,,37,1*4A

$GBGSV,7,5,26,9,,,37,6,,,37,26,,,36,8,,,36,1*43

$GBGSV,7,6,26,7,,,36,44,,,35,10,,,34,5,,,34,1*73

$GBGSV,7,7,26,34,,,34,4,,,33,1*46

$GBRMC,122002.518,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122002.518,0.000,789.415,789.415,721.936,2097152,2097152,2097152*66

+WIFISCAN:4,0,F88C21BCF57D,-32
+WIFISCAN:4,1,F42A7D1297A3,-63
+WIFISCAN:4,2,CC057790A740,-69
+WIFISCAN:4,3,CC057790A5C0,-70

[D][05:18:31][CAT1]wifi scan report total[4]


2025-07-31 20:19:59:160 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 20:19:59:445 ==>> [D][05:18:31][GNSS]recv submsg id[3]


2025-07-31 20:19:59:778 ==>> $GBGGA,122003.518,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,14,,,42,60,,,41,3,,,41,1*45

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,25,,,41,1*7C

$GBGSV,7,3,26,39,,,40,13,,,38,38,,,38,40,,,38,1*7E

$GBGSV,7,4,26,1,,,38,16,,,38,41,,,38,9,,,37,1*74

$GBGSV,7,5,26,6,,,37,2,,,36,26,,,36,8,,,36,1*49

$GBGSV,7,6,26,7,,,36,44,,,35,10,,,34,5,,,34,1*73

$GBGSV,7,7,26,34,,,34,4,,,33,1*46

$GBRMC,122003.518,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122003.518,0.000,785.432,785.432,718.294,2097152,2097152,2097152*6E

[D][05:18:32][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:19:59:808 ==>>                                                             

2025-07-31 20:20:00:651 ==>> [D][05:18:32][PROT]CLEAN,SEND:0
[D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3DCE7BC3E4A389DCB18EB3E6DCC19AA607757D45159E6B3E563F309EC0497E755B46A44E9A1DEF

2025-07-31 20:20:00:726 ==>> A0B06FDE89450B43171ABB701C3E04000EF9834EBB72367E99B18A659AEE1FE92822CAE1FE5410351227AE7
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]


2025-07-31 20:20:00:951 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok
[D][05:18:33][COMM]44223 imu init OK
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:33][COMM]accel parse set 0
[D][05:18:33][COMM][Audio].l:[1012].open hexlog sav

2025-07-31 20:20:00:966 ==>> e


2025-07-31 20:20:01:180 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 20:20:01:508 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:20:01:517 ==>> 检测【打开喇叭声音】
2025-07-31 20:20:01:522 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:20:02:240 ==>> $GBGGA,122005.518,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,33,,,42,60,,,41,3,,,41,1*44

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,25,,,41,1*7C

$GBGSV,7,3,26,39,,,40,1,,,39,13,,,38,38,,,38,1*4A

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,2,,,37,1*4A

$GBGSV,7,5,26,9,,,37,6,,,37,26,,,36,8,,,36,1*43

$GBGSV,7,6,26,7,,,36,44,,,35,10,,,34,5,,,34,1*73

$GBGSV,7,7,26,34,,,34,4,,,33,1*46

$GBRMC,122005.518,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122005.518,0.000,786.224,786.224,719.017,2097152,2097152,2097152*60

[W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34

2025-07-31 20:20:02:310 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:20:02:319 ==>> 检测【打开大灯控制】
2025-07-31 20:20:02:336 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:20:02:347 ==>> ][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]45234 imu init OK
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m

2025-07-31 20:20:02:452 ==>> _audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05

2025-07-31 20:20:02:510 ==>> :18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:20:02:585 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:20:02:690 ==>> $GBGGA,122006.518,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,3,,,42,14,,,42,33,,,42,60,

2025-07-31 20:20:02:750 ==>> ,,41,1*47

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,25,,,41,1*7C

$GBGSV,7,3,26,39,,,40,13,,,39,38,,,38,40,,,38,1*7F

$GBGSV,7,4,26,1,,,38,16,,,38,41,,,38,2,,,37,1*7F

$GBGSV,7,5,26,9,,,37,6,,,37,26,,,36,8,,,36,1*43

$GBGSV,7,6,26,7,,,36,44,,,35,10,,,34,5,,,34,1*73

$GBGSV,7,7,26,4,,,34,34,,,34,1*41

$GBRMC,122006.518,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122006.518,0.000,787.815,787.815,720.472,2097152,2097152,2097152*6E



2025-07-31 20:20:02:859 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:20:02:865 ==>> 检测【关闭仪表供电3】
2025-07-31 20:20:02:887 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:20:03:024 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0


2025-07-31 20:20:03:133 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:20:03:138 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:20:03:147 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:20:03:189 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 20:20:03:294 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:20:03:407 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:20:03:413 ==>> 检测【读大灯电压】
2025-07-31 20:20:03:417 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:20:03:762 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[32923]
$GBGGA,122007.518,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,14,,,42,33,,,42,60,,,41,3,,,41,1*44

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,25,,,41,1*7C

$GBGSV,7,3,26,39,,,40,13,,,38,38,,,38,40,,,38,1*7E

$GBGSV,7,4,26,16,,,38,1,,,38,41,,,38,2,,,37,1*7F

$GBGSV,7,5,26,9,,,37,6,,,37,26,,,36,8,,,36,1*43

$GBGSV,7,6,26,7,,,36,44,,,35,5,,,34,10,,,34,1*73

$GBGSV,7,7,26,4,,,34,34,,,33,1*46

$GBRMC,122007.518,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122007.518,0.000,785.428,785.428,718.289,2097152,2097152,2097152*66



2025-07-31 20:20:03:950 ==>> 【读大灯电压】通过,【32923mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:20:03:956 ==>> 检测【关闭大灯控制2】
2025-07-31 20:20:03:964 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:20:04:110 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:20:04:238 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:20:04:249 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:20:04:279 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:20:04:430 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[69]


2025-07-31 20:20:04:533 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:20:04:538 ==>> 检测【打开WIFI(4)】
2025-07-31 20:20:04:543 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:20:04:841 ==>> $GBGGA,122004.524,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,3,62,190,41,6,53,340,37,59,52,129,41,16,52,344,38,1*70

$GBGSV,7,2,27,39,52,4,40,42,50,161,41,2,48,240,36,1,46,124,38,1*79

$GBGSV,7,3,27,60,41,238,41,25,40,273,42,40,39,170,38,7,38,186,36,1*4E

$GBGSV,7,4,27,9,38,315,37,4,31,113,34,13,29,210,38,10,28,194,34,1*72

$GBGSV,7,5,27,38,26,201,38,8,26,204,36,5,24,258,34,26,22,230,36,1*72

$GBGSV,7,6,27,41,17,322,38,34,9,158,33,33,,,43,14,,,42,1*4F

$GBGSV,7,7,27,24,,,41,44,,,35,12,,,31,1*77

$GBRMC,122004.524,V,,,,,,,310725,1.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,122004.524,0.243,0.183,0.170,0.240,3.456,5.319,13*5A

[W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:20:05:193 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:20:05:243 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:20:05:251 ==>> 检测【EC800M模组版本】
2025-07-31 20:20:05:259 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:20:05:558 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:37][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 132
+WIFISCAN:4,0,CC057790A740,-70
+WIFISCAN:4,1,CC057790A741,-70
+WIFISCAN:4,2,CC057790A6E0,-79
+WIFISCAN:4,3,CC057790A7C1,-80

[D][05:18:37][CAT1]wifi scan report total[4]
[D][05:18:37][GNSS]recv submsg id[3]


2025-07-31 20:20:05:789 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:20:05:801 ==>> 检测【配置蓝牙地址】
2025-07-31 20:20:05:821 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:20:05:996 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FB12B0AFE0B8>】
2025-07-31 20:20:06:254 ==>> recv ble 1
recv ble 2
ble set mac ok :fb,12,b0,af,e0,b8
enable filters ret : 0

2025-07-31 20:20:06:509 ==>> [D][05:18:38][PROT]CLEAN,SEND:1
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:2
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3C49D4E9C4C53D1BBE84D936707A295F358C98C46EB30151C2

2025-07-31 20:20:06:533 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:20:06:544 ==>> 检测【BLETEST】
2025-07-31 20:20:06:570 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:20:06:614 ==>> C3B83ED754BF98C5FDDE0AC81FBD1FC02140D6096EA98C1B14D0AFAA4D472C39293D419F905A2CEF770D56263C06673E268F9AD9A8A398356C1
[D][05:18:38][CAT1]<<< 
SEND OK

[D][05:18:38][CAT1]exec over: func id: 15, ret: 11
[D][05:18:38][CAT1]sub id: 15, ret: 11

[D][05:18:38][SAL ]Cellular task submsg id[68]
[D][05:18:38][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
$GBGGA,122005.504,2301.2580045,N,11421.9419371,E,1,09,1.19,63.890,M,-1.770,M,,*5A

$GBGSA,A,3,39,42,13,08,25,38,40,41,34,,,,2.06,1.19,1.68,4*06

$GBGSV,7,1,27,14,75,201,42,33,66,300,42,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,59,52,129,41,16,52,344,38,39,52,4,40,1*44

$GBGSV,7,3,27,42,50,161,41,2,48,240,37,1,46,124,39,13,46,220,38,1*7E

$GBGSV,7,4,27,8,42,207,36,60,41,238,41,25,40,273,41,7,38,186,36,1*77

$GBGSV,7,5,27,9,38,315,37,38,31,191,38,40,31,160,38,4,31,113,34,1*73

[D][05:18:38][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
$GBGSV,7,6,27,10,28,194,34,5,24,258,34,26,22,230,36,41,17,322,38,1*42

$GBGSV,7,7,27,34,9,158,34,44,,,35,12,,,31,1*71

$GBGSV,2,1,05,39,52,4,40,42,50,161,42,25,40,273,39,41,17,322,34,5*72

$GBGSV,2,2,05,34,9,158,32,5*74

$GBRMC,122005.504,A,2301.2580045,N,11421.9419371,E,0.001,

2025-07-31 20:20:06:720 ==>> 0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

[D][05:18:38][GNSS]HD8040 GPS
[D][05:18:38][GNSS]GPS diff_sec 124009287, report 0x42 frame
$GBGST,122005.504,3.188,0.383,0.325,0.483,2.800,3.492,7.549*78

[D][05:18:38][M2M ]g_m2m_is_idle become true
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:38][PROT]M2M Send ok [1629955118]
[D][05:18:38][COMM]49151 imu init OK
[D][05:18:38][COMM]Main Task receive event:131
[D][05:18:38][COMM]index:0,power_mode:0xFF
[D][05:18:38][COMM]index:1,sound_mode:0xFF
[D][05:18:38][COMM]index:2,gsensor_mode:0xFF
[D][05:18:38][COMM]index:3,report_freq_mode:0xFF
[D][05:18:38][COMM]index:4,report_period:0xFF
[D][05:18:38][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:38][COMM]index:6,normal_reset_period:0xFF
[D][05:18:38][COMM]index:7,spock_over_speed:0xFF
[D][05:18:38][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:38][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:38][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:38][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:38][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:38][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:38][COMM]index:14,spock_low_bat_alarm_soc:0xFF


2025-07-31 20:20:06:826 ==>> [D][05:18:38][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:38][COMM]index:16,imu_config_params:0xFF
[D][05:18:38][COMM]index:17,long_connect_params:0xFF
[D][05:18:38][COMM]index:18,detain_mark:0xFF
[D][05:18:38][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:38][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:38][COMM]index:21,mc_mode:0xFF
[D][05:18:38][COMM]index:22,S_mode:0xFF
[D][05:18:38][COMM]index:23,overweight:0xFF
[D][05:18:38][COMM]index:24,standstill_mode:0xFF
[D][05:18:38][COMM]index:25,night_mode:0xFF
[D][05:18:38][COMM]index:26,experiment1:0xFF
[D][05:18:38][COMM]index:27,experiment2:0xFF
[D][05:18:38][COMM]index:28,experiment3:0xFF
[D][05:18:38][COMM]index:29,experiment4:0xFF
[D][05:18:38][COMM]index:30,night_mode_start:0xFF
[D][05:18:38][COMM]index:31,night_mode_end:0xFF
[D][05:18:38][COMM]index:33,park_report_minutes:0xFF
[D][05:18:38][COMM]index:34,park_report_mode:0xFF
[D][05:18:38][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:38][COMM]index:38,charge_battery_para: FF
[D][05:18:38][COMM]index:39,multirider_mode:0xFF
[D][05:18:38][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:38][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:38][COM

2025-07-31 20:20:06:931 ==>> M]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:38][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:38][COMM]index:44,riding_duration_config:0xFF
[D][05:18:38][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:38][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:38][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:38][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:38][COMM]index:49,mc_load_startup:0xFF
[D][05:18:38][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:38][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:38][COMM]index:52,traffic_mode:0xFF
[D][05:18:38][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:38][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:38][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:38][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:38][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:38][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:38][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:38][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:38][COMM]index:63,experiment5:0xFF
[D][05:18:38][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:38][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18

2025-07-31 20:20:07:038 ==>> :38][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:38][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:38][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:38][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:38][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:38][COMM]index:72,experiment6:0xFF
[D][05:18:38][COMM]index:73,experiment7:0xFF
[D][05:18:38][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:38][COMM]index:75,zero_value_from_server:-1
[D][05:18:38][COMM]index:76,multirider_threshold:255
[D][05:18:38][COMM]index:77,experiment8:255
[D][05:18:38][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:38][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:38][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:38][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:38][COMM]index:83,loc_report_interval:255
[D][05:18:38][COMM]index:84,multirider_threshold_p2:255
[D][05:18:38][COMM]index:85,multirider_strategy:255
[D][05:18:38][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:38][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:38][COMM]index:90,weight_param:0xFF
[D][05:18:38][COMM]index:93,lock_a

2025-07-31 20:20:07:143 ==>> nti_theft_mode:0xFF
[D][05:18:38][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:38][COMM]index:95,current_limit:0xFF
[D][05:18:38][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:38][COMM]index:100,location_mode:0xFF

[W][05:18:38][PROT]remove success[1629955118],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:38][PROT]add success [1629955118],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:38][COMM]Main Task receive event:131 finished processing
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<
$GBGGA,122006.004,2301.2581459,N,11421.9416908,E,1,14,0.91,67.841,M,-1.770,M,,*5A

$GBGSA,A,3,14,33,24,39,59,42,134A A4 01 A4 4A 


2025-07-31 20:20:07:248 ==>> recv ble 1
recv ble 2
<BSJ*MAC:FB12B0AFE0B8*RSSI:-25*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FB12B0AFE0B899999OVER 150


2025-07-31 20:20:07:353 ==>>                                                                                                   $GBGGA,122007.000,2301.2581500,N,11421.9414910,E,1,15,0.80,69.758,M,-1.770,M,,*51

$GBGSA,A,3,14,33,24,39,59,42,13,60,08,25,38,40,1.42,0.80,1.18,4*08

$GBGSA,A,3,41,44,34,,,,,,,,,,1.42,0.80,1.18,4*0D

$GBGSV,7,1,27,14,75,201,42,33,66,300,42,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27

2025-07-31 20:20:07:459 ==>> ,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,2,48,240,37,1,46,124,39,13,46,220,39,1*7F

$GBGSV,7,4,27,60,43,241,41,8,42,207,36,25,40,273,41,9,38,315,37,1*7C

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,33,1*72

$GBGSV,7,6,27,10,28,194,34,5,24,258,34,26,22,230,36,41,17,322,38,1*42

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,09,33,66,300,42,24,55,5,43,39,52,4,41,42,50,161,42,5*77

$GBGSV,3,2,09,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*78

$GBGSV,3,3,09,34,9,158,32,5*78

$GBRMC,122007.000,A,2301.2581500,N,11421.9414910,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122007.000,3.239,0.210,0.202,0.284,2.417,2.676,4.806*7A

[D][05:18:39][COMM]read battery soc:255
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[

2025-07-31 20:20:07:489 ==>> ec800m_audio_end].l:[863].recv ok
[D][05:18:39][COMM]accel parse set 0
[D][05:18:39][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:20:07:568 ==>> 【BLETEST】通过,【-25dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:20:07:573 ==>> 该项需要延时执行
2025-07-31 20:20:07:791 ==>> [D][05:18:40][COMM]51203 imu init OK


2025-07-31 20:20:08:388 ==>> $GBGGA,122008.000,2301.2581742,N,11421.9414346,E,1,17,0.77,70.784,M,-1.770,M,,*50

$GBGSA,A,3,14,33,24,06,16,39,59,42,13,60,08,25,1.40,0.77,1.17,4*03

$GBGSA,A,3,38,40,41,44,34,,,,,,,,1.40,0.77,1.17,4*07

$GBGSV,7,1,27,14,75,201,42,33,66,300,42,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,2,48,240,36,1,46,124,39,13,46,220,38,1*7F

$GBGSV,7,4,27,60,43,241,41,8,42,207,36,25,40,273,41,9,38,315,37,1*7C

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,194,34,5,24,258,34,26,22,230,36,41,17,322,38,1*42

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,10,33,66,300,43,24,55,5,43,39,52,4,41,42,50,161,43,5*7F

$GBGSV,3,2,10,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*70

$GBGSV,3,3,10,44,13,107,30,34,9,158,32,5*47

$GBRMC,122008.000,A,2301.2581742,N,11421.9414346,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,122008.000,3.408,0.268,0.250,0.354,2.449,2.646,4.454*76



2025-07-31 20:20:09:400 ==>> $GBGGA,122009.000,2301.2580988,N,11421.9413638,E,1,22,0.69,71.409,M,-1.770,M,,*5D

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.29,0.69,1.09,4*0E

$GBGSA,A,3,13,60,08,25,38,40,10,41,44,34,,,1.29,0.69,1.09,4*02

$GBGSV,7,1,27,14,75,201,42,33,66,299,42,3,62,190,41,24,55,5,41,1*4F

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,317,37,2,48,240,37,1,46,124,39,1*41

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,26,22,230,36,41,17,322,38,1*40

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,10,33,66,299,44,24,55,5,44,39,52,4,41,42,50,161,43,5*7E

$GBGSV,3,2,10,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*70

$GBGSV,3,3,10,44,13,107,30,34,9,158,32,5*47

$GBRMC,122009.000,A,2301.2580988,N,11421.9413638,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,122009.000,3.271,0.210,0.201,0.283,2.343,2.506,4.104*75

[D][05:18:41][COMM]read battery soc:255


2025-07-31 20:20:10:384 ==>> $GBGGA,122010.000,2301.2580630,N,11421.9413171,E,1,24,0.63,72.002,M,-1.770,M,,*53

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,317,37,2,48,240,36,1,46,124,39,1*40

$GBGSV,7,4,27,13,46,220,39,60,43,241,41,8,42,207,36,25,40,273,42,1*44

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,33,1*72

$GBGSV,7,6,27,10,28,187,34,5,24,258,35,41,17,322,38,26,17,46,36,1*74

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,10,33,66,299,44,24,55,5,43,39,52,4,41,42,50,161,42,5*78

$GBGSV,3,2,10,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*70

$GBGSV,3,3,10,44,13,107,30,34,9,158,32,5*47

$GBRMC,122010.000,A,2301.2580630,N,11421.9413171,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,122010.000,3.233,0.236,0.221,0.317,2.300,2.435,3.858*70



2025-07-31 20:20:11:080 ==>> [D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][PROT]index:1 1629955123
[D][05:18:43][PROT]is_send:0
[D][05:18:43][PROT]sequence_num:5
[D][05:18:43][PROT]retry_timeout:0
[D][05:18:43][PROT]retry_times:1
[D][05:18:43][PROT]send_path:0x2
[D][05:18:43][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:43][PROT]===========================================================
[W][05:18:43][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955123]
[D][05:18:43][PROT]===========================================================
[D][05:18:43][PROT]sending traceid [9999999999900006]
[D][05:18:43][PROT]Send_TO_M2M [1629955123]
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:43][SAL ]sock send credit cnt[6]
[D][05:18:43][SAL ]sock send ind credit cnt[6]
[D][05:18:43][M2M ]m2m send data len[198]
[D][05:18:43][SAL ]Cellular task submsg id[10]
[D][05:18:43][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:43][CAT1]gsm read msg sub id: 15
[D][05:18:43][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:43][CAT1]Send Data To Server[198][2

2025-07-31 20:20:11:155 ==>> 01] ... ->:
0063B981113311331133113311331B88B3813783BD2E7378A163865286C25A63F91126B1D5D4AA6E69F15257F7DA7EE22A27282AADD32140878982B142572B27FB9A53044685DE824FB1A894A76B48289E0ABB4CD658C6184E60F6649EE7EA3E11C696
[D][05:18:43][CAT1]<<< 
SEND OK

[D][05:18:43][CAT1]exec over: func id: 15, ret: 11
[D][05:18:43][CAT1]sub id: 15, ret: 11

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:43][M2M ]g_m2m_is_idle become true
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:43][PROT]M2M Send ok [1629955123]


2025-07-31 20:20:11:410 ==>> $GBGGA,122011.000,2301.2580709,N,11421.9412622,E,1,24,0.63,72.234,M,-1.770,M,,*5E

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,42,24,55,5,42,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,42,9,48,317,37,2,48,240,37,1,46,124,39,1*42

$GBGSV,7,4,27,13,46,220,39,60,43,241,41,8,42,207,36,25,40,273,41,1*47

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,35,5,24,258,34,41,17,322,38,26,17,46,36,1*74

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,11,33,66,299,44,24,55,5,43,39,52,4,41,42,50,161,43,5*78

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,33,44,13,107,30,34,9,158,32,5*46

$GBRMC,122011.000,A,2301.2580709,N,11421.9412622,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,122011.000,3.227,0.248,0.232,0.331,2.281,2.396,3.684*72

[D][05:18:43][COMM]read battery soc:255


2025-07-31 20:20:12:411 ==>> $GBGGA,122012.000,2301.2580808,N,11421.9412286,E,1,24,0.63,72.419,M,-1.770,M,,*50

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,42,9,48,318,37,2,48,240,37,1,46,124,39,1*4D

$GBGSV,7,4,27,13,46,220,39,60,43,241,41,8,42,207,36,25,40,273,41,1*47

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,11,33,66,299,44,24,55,5,43,39,52,4,41,42,50,161,42,5*79

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,35,41,17,322,33,5*70

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122012.000,A,2301.2580808,N,11421.9412286,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122012.000,3.125,0.220,0.208,0.297,2.213,2.314,3.508*7C



2025-07-31 20:20:13:408 ==>> $GBGGA,122013.000,2301.2580996,N,11421.9412371,E,1,24,0.63,72.593,M,-1.770,M,,*5D

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,37,1,46,124,39,1*4E

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,11,33,66,299,44,24,55,5,43,39,52,4,41,42,50,161,43,5*78

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122013.000,A,2301.2580996,N,11421.9412371,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,122013.000,3.054,0.221,0.209,0.298,2.165,2.255,3.373*79

[D][05:18:45][COMM]read battery soc:255


2025-07-31 20:20:14:405 ==>> $GBGGA,122014.000,2301.2580980,N,11421.9412579,E,1,24,0.63,72.727,M,-1.770,M,,*5E

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,41,1*4E

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,36,1,46,124,39,1*4F

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,11,33,66,299,44,24,55,5,43,39,52,4,41,42,50,161,42,5*79

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122014.000,A,2301.2580980,N,11421.9412579,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122014.000,3.102,0.223,0.210,0.300,2.184,2.265,3.315*7A



2025-07-31 20:20:15:415 ==>> $GBGGA,122015.000,2301.2581010,N,11421.9412655,E,1,24,0.63,72.810,M,-1.770,M,,*58

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,42,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,37,1,46,124,39,1*4E

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,42,1*45

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,33,1*72

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,11,33,66,299,43,24,55,5,43,39,52,4,41,42,50,161,42,5*7E

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,34,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122015.000,A,2301.2581010,N,11421.9412655,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,122015.000,3.036,0.248,0.232,0.332,2.142,2.215,3.217*7F

[D][05:18:47][COMM]read battery soc:255


2025-07-31 20:20:16:408 ==>> [D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][PROT]CLEAN:1
[D][05:18:48][PROT]index:0 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:4
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:2
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900005]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B519C4E6348

2025-07-31 20:20:16:513 ==>> 9F2B8BB4A75564E2E11B7BCED34997723CA4F25D02ADF5AFFB05C879607C0DB6B83C44F07530B7D95EE639F82AA80F4CF982702CEB19125D15AF3336CBA4B365B98305B95F52DBDD3584C37D525
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
$GBGGA,122016.000,2301.2581012,N,11421.9412738,E,1,24,0.63,72.863,M,-1.770,M,,*57

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,38,40,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,42,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,42,9,48,318,37,2,48,240,37,1,46,124,39,1*4D

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,35,25,40,273,42,1*46

$GBGSV,7,5,27,7,35,175,36,38,31,191,38,40,31,160,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,2

2025-07-31 20:20:16:573 ==>> 58,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,11,33,66,299,43,24,55,5,43,39,52,4,41,42,50,161,42,5*7E

$GBGSV,3,2,11,25,40,273,40,38,31,191,36,40,31,160,35,41,17,322,33,5*70

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122016.000,A,2301.2581012,N,11421.9412738,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,122016.000,3.046,0.204,0.194,0.275,2.143,2.210,3.168*71



2025-07-31 20:20:17:408 ==>> $GBGGA,122017.000,2301.2581133,N,11421.9412945,E,1,24,0.63,72.956,M,-1.770,M,,*57

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,42,3,62,190,41,24,55,5,41,1*4F

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,37,1,46,124,38,1*4F

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,40,31,160,38,38,31,191,38,4,31,113,33,1*72

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,33,12,,,30,1*43

$GBGSV,3,1,11,33,66,299,43,24,55,5,43,39,52,4,41,42,50,161,42,5*7E

$GBGSV,3,2,11,25,40,273,40,40,31,160,35,38,31,191,36,41,17,322,33,5*70

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122017.000,A,2301.2581133,N,11421.9412945,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122017.000,3.220,0.217,0.205,0.294,2.236,2.296,3.205*73

[D][05:18:49][COMM]read battery soc:255


2025-07-31 20:20:17:575 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:20:17:583 ==>> 检测【检测WiFi结果】
2025-07-31 20:20:17:591 ==>> WiFi信号:【F88C21BCF57D】,信号值:-32
2025-07-31 20:20:17:622 ==>> WiFi信号:【CC057790A620】,信号值:-51
2025-07-31 20:20:17:629 ==>> WiFi信号:【CC057790A740】,信号值:-67
2025-07-31 20:20:17:635 ==>> WiFi信号:【CC057790A6E1】,信号值:-80
2025-07-31 20:20:17:653 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 20:20:17:661 ==>> WiFi信号:【CC057790A5C0】,信号值:-70
2025-07-31 20:20:17:668 ==>> WiFi信号:【CC057790A741】,信号值:-70
2025-07-31 20:20:17:674 ==>> WiFi信号:【CC057790A6E0】,信号值:-79
2025-07-31 20:20:17:700 ==>> WiFi信号:【CC057790A7C1】,信号值:-80
2025-07-31 20:20:17:710 ==>> WiFi数量【9】, 最大信号值:-32
2025-07-31 20:20:17:736 ==>> 检测【检测GPS结果】
2025-07-31 20:20:17:748 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 20:20:17:779 ==>> 
北斗星号:【14】,信号值:【42】
北斗星号:【33】,信号值:【43】
北斗星号:【3】,信号值:【41】
北斗星号:【24】,信号值:【43】
北斗星号:【6】,信号值:【37】
北斗星号:【16】,信号值:【38】
北斗星号:【39】,信号值:【41】
北斗星号:【59】,信号值:【41】
北斗星号:【42】,信号值:【43】
北斗星号:【2】,信号值:【36】
北斗星号:【1】,信号值:【39】
北斗星号:【13】,信号值:【38】
北斗星号:【60】,信号值:【41】
北斗星号:【8】,信号值:【36】
北斗星号:【25】,信号值:【40】
北斗星号:【9】,信号值:【37】
北斗星号:【7】,信号值:【36】
北斗星号:【38】,信号值:【36】
北斗星号:【26】,信号值:【36】

2025-07-31 20:20:17:787 ==>> 检测【CSQ强度】
2025-07-31 20:20:17:794 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:20:17:823 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:50][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:50][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:20:17:875 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 20:20:17:884 ==>> 检测【关闭GSM联网】
2025-07-31 20:20:17:901 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:20:18:032 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:50][COMM]GSM test
[D][05:18:50][COMM]GSM test disable


2025-07-31 20:20:18:159 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:20:18:222 ==>> 检测【4G联网测试】
2025-07-31 20:20:18:249 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:20:18:409 ==>> $GBGGA,122018.000,2301.2581097,N,11421.9412957,E,1,24,0.63,73.011,M,-1.770,M,,*5F

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,42,3,62,190,42,24,55,5,41,1*4C

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,37,1,46,124,39,1*4E

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,40,31,160,38,38,31,191,38,4,31,113,34,1*75

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,11,33,66,299,43,24,55,5,43,39,52,4,41,42,50,161,42,5*7E

$GBGSV,3,2,11,25,40,273,40,40,31,160,34,38,31,191,36,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122018.000,A,2301.2581097,N,11421.9412957,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,122018.000,3.112,0.214,0.202,0.290,2.173,2.230,3.113*74

[W][05:18:50][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 20:20:19:193 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ck_audio_volumn:0xFF
[D][05:18:50][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:50][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:50][COMM]index:16,imu_config_params:0xFF
[D][05:18:50][COMM]index:17,long_connect_params:0xFF
[D][05:18:50][COMM]index:18,detain_mark:0xFF
[D][05:18:50][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:50][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:50][COMM]index:21,mc_mode:0xFF
[D][05:18:50

2025-07-31 20:20:19:298 ==>> ][COMM]index:22,S_mode:0xFF
[D][05:18:50][COMM]index:23,overweight:0xFF
[D][05:18:50][COMM]index:24,standstill_mode:0xFF
[D][05:18:50][COMM]index:25,night_mode:0xFF
[D][05:18:50][COMM]index:26,experiment1:0xFF
[D][05:18:50][COMM]index:27,experiment2:0xFF
[D][05:18:50][COMM]index:28,experiment3:0xFF
[D][05:18:50][COMM]index:29,experiment4:0xFF
[D][05:18:50][COMM]index:30,night_mode_start:0xFF
[D][05:18:50][COMM]index:31,night_mode_end:0xFF
[D][05:18:50][COMM]index:33,park_report_minutes:0xFF
[D][05:18:50][COMM]index:34,park_report_mode:0xFF
[D][05:18:50][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:50][COMM]index:38,charge_battery_para: FF
[D][05:18:50][COMM]index:39,multirider_mode:0xFF
[D][05:18:50][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:50][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:50][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:50][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:50][COMM]index:44,riding_duration_config:0xFF
[D][05:18:50][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:50][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:50][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:50][COMM]index:48,shlmt_sensor_e

2025-07-31 20:20:19:403 ==>> n:0xFF
[D][05:18:50][COMM]index:49,mc_load_startup:0xFF
[D][05:18:50][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:50][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:50][COMM]index:52,traffic_mode:0xFF
[D][05:18:50][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:50][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:50][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:50][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:50][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:50][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:50][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:50][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:50][COMM]index:63,experiment5:0xFF
[D][05:18:50][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:50][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:50][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:50][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:50][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:50][COMM]index:72,experiment6:0xFF
[D][05:18:50][COMM]index:73,experim

2025-07-31 20:20:19:508 ==>> ent7:0xFF
[D][05:18:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:50][COMM]index:75,zero_value_from_server:-1
[D][05:18:50][COMM]index:76,multirider_threshold:255
[D][05:18:50][COMM]index:77,experiment8:255
[D][05:18:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:50][COMM]index:83,loc_report_interval:255
[D][05:18:50][COMM]index:84,multirider_threshold_p2:255
[D][05:18:50][COMM]index:85,multirider_strategy:255
[D][05:18:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:50][COMM]index:90,weight_param:0xFF
[D][05:18:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:50][COMM]index:95,current_limit:0xFF
[D][05:18:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:50][COMM]index:100,location_mode:0xFF

[W][05:18:50][PROT]remove success[1629955130],send_path[2],type[0000],priority[0],index[0],used[

2025-07-31 20:20:19:613 ==>> 0]
[W][05:18:50][PROT]add success [1629955130],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:50][PROT]index:0 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:8
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:1
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900009]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:50][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[294]
[D][05:18:50][CAT1]gsm read msg sub id: 13
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:50][CAT1]tx ret[8

2025-07-31 20:20:19:718 ==>> ] >>> AT+CSQ

[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:50][CAT1]exec over: func id: 13, ret: 21
[D][05:18:50][M2M ]get csq[24]
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:50][CAT1]Send Data To Server[294][297] ... ->:
0093B98E113311331133113311331B88B1FED3BCD7ED68F6F593A32EEB95FCA0923346B7B53CA15AC22F3499AD2A81C06B1238B699354D71A35A5D7CDBB49C52ABB39609FB8A647B6F30DB6D11C253410A1AD5501DB90A58283822F8E63FDE83E9F97C36D461C00BDA0DDE1DA01A02F64DBB39AF6D7D39133A82DB73CC84DD5F65DC87DA62DCBDBABB50C184FAD2501760F6D7
[D][05:18:50][CAT1]<<< 
SEND OK

[D][05:18:50][CAT1]exec over: func id: 15, ret: 11
[D][05:18:50][CAT1]sub id: 15, ret: 11

[D][05:18:50][SAL ]Cellular task submsg id[68]
[D][05:18:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:50][M2M ]g_m2m_is_idle become true
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:50][PROT]M2M Send ok [1629955130]
>>>>>RESEND ALLSTATE<<<<<
[D][05:18:51][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D]

2025-07-31 20:20:19:793 ==>> [05:18:51][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:51][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:51][PROT]remove success[1629955131],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:51][PROT]add success [1629955131],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:18:51][COMM]------>period, report file manifest
[D][05:18:51][COMM]Main Task receive event:14 finished processing
[D][05:18:51][CAT1]gsm read msg sub id: 21
[D][05:18:51][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]cell info report total[0]
[D][05:18:51][CAT1]exec over: func id: 21, ret: 6


2025-07-31 20:20:19:898 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:20:19:928 ==>>              

2025-07-31 20:20:20:197 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:20:20:204 ==>> 检测【关闭GPS】
2025-07-31 20:20:20:211 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:20:20:413 ==>> $GBGGA,122020.000,2301.2580964,N,11421.9413124,E,1,24,0.63,73.078,M,-1.770,M,,*52

$GBGSA,A,3,14,33,03,24,06,16,39,59,42,09,02,01,1.21,0.63,1.03,4*06

$GBGSA,A,3,13,60,08,25,07,40,38,10,41,26,44,34,1.21,0.63,1.03,4*09

$GBGSV,7,1,27,14,75,201,42,33,66,299,43,3,62,190,41,24,55,5,42,1*4D

$GBGSV,7,2,27,6,53,340,37,16,52,344,38,39,52,4,40,59,51,128,41,1*46

$GBGSV,7,3,27,42,50,161,41,9,48,318,37,2,48,240,37,1,46,124,38,1*4F

$GBGSV,7,4,27,13,46,220,38,60,43,241,41,8,42,207,36,25,40,273,41,1*46

$GBGSV,7,5,27,7,35,175,36,40,31,160,38,38,31,191,38,4,31,113,33,1*72

$GBGSV,7,6,27,10,28,187,34,5,24,258,34,41,17,322,38,26,17,46,36,1*75

$GBGSV,7,7,27,44,13,107,35,34,9,158,34,12,,,30,1*44

$GBGSV,3,1,11,33,66,299,43,24,55,5,43,39,52,4,41,42,50,161,42,5*7E

$GBGSV,3,2,11,25,40,273,40,40,31,160,34,38,31,191,36,41,17,322,33,5*71

$GBGSV,3,3,11,26,17,46,34,44,13,107,30,34,9,158,32,5*41

$GBRMC,122020.000,A,2301.2580964,N,11421.9413124,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,122020.000,3.075,0.231,0.217,0.311,2.147,2.198,3.032*70



2025-07-31 20:20:20:701 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]stop event:8
[D][05:18:52][GNSS]GPS stop. ret=0
[D][05:18:52][GNSS]all continue location stop
[W][05:18:52][GNSS]stop locating
[D][05:18:52][GNSS]all sing location stop
[D][05:18:52][CAT1]gsm read msg sub id: 24
[D][05:18:52][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 24, ret: 6
[D][05:18:53][CAT1]sub id: 24, ret: 6



2025-07-31 20:20:20:735 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:20:20:742 ==>> 检测【清空消息队列2】
2025-07-31 20:20:20:753 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:20:20:931 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:53][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:20:21:010 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:20:21:017 ==>> 检测【轮动检测】
2025-07-31 20:20:21:024 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:20:21:143 ==>> 3A A3 01 00 A3 


2025-07-31 20:20:21:248 ==>> OFF_OUT1
OVER 150


2025-07-31 20:20:21:310 ==>> [D][05:18:53][COMM]read battery soc:255
[D][05:18:53][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:20:21:520 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:20:21:580 ==>> [D][05:18:53][GNSS]recv submsg id[1]
[D][05:18:53][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:53][GNSS]location stop evt done evt


2025-07-31 20:20:21:640 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:20:21:794 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:20:21:801 ==>> 检测【关闭小电池】
2025-07-31 20:20:21:808 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:20:21:850 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:20:22:069 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:20:22:077 ==>> 检测【进入休眠模式】
2025-07-31 20:20:22:089 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:20:22:294 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:18:54][COMM]Main Task receive event:28
[D][05:18:54][COMM]main task tmp_sleep_event = 8
[D][05:18:54][COMM]prepare to sleep
[D][05:18:54][CAT1]gsm read msg sub id: 12
[D][05:18:54][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:20:22:826 ==>> [D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 12, ret: 6
[D][05:18:55][M2M ]tcpclient close[4]
[D][05:18:55][SAL ]Cellular task submsg id[12]
[D][05:18:55][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:18:55][CAT1]gsm read msg sub id: 9
[D][05:18:55][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 9, ret: 6
[D][05:18:55][CAT1]sub id: 9, ret: 6

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:55][SAL ]socket close ind. id[4]
[D][05:18:55][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:55][COMM]1x1 frm_can_tp_send ok
[D][05:18:55][CAT1]pdpdeact urc len[22]


2025-07-31 20:20:23:128 ==>> [E][05:18:55][COMM]1x1 rx timeout
[D][05:18:55][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:20:23:293 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:20:23:641 ==>> [E][05:18:55][COMM]1x1 rx timeout
[E][05:18:55][COMM]1x1 tp timeout
[E][05:18:55][COMM]1x1 error -3.
[W][05:18:55][COMM]CAN STOP!
[D][05:18:55][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:18:55][COMM]------------ready to Power off Acckey 1------------
[D][05:18:55][COMM]------------ready to Power off Acckey 2------------
[D][05:18:55][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:18:55][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1304
[D][05:18:55][COMM]bat sleep fail, reason:-1
[D][05:18:55][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:18:55][COMM]accel parse set 0
[D][05:18:55][COMM]imu rest ok. 66944
[D][05:18:55][COMM]imu sleep 0
[W][05:18:55][COMM]now sleep


2025-07-31 20:20:23:884 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:20:23:891 ==>> 检测【检测33V休眠电流】
2025-07-31 20:20:23:901 ==>> 开始33V电流采样
2025-07-31 20:20:23:927 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:23:986 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:20:24:999 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:20:25:060 ==>> Current33V:????:15.39

2025-07-31 20:20:25:510 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:25:517 ==>> 【检测33V休眠电流】通过,【15.39uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:20:25:523 ==>> 该项需要延时执行
2025-07-31 20:20:27:523 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:20:27:534 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:20:27:559 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:20:27:648 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1669mV
Get AD_V4 0mV
Get AD_V5 2743mV
Get AD_V6 2019mV
Get AD_V7 1101mV
OVER 150


2025-07-31 20:20:28:555 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:20:28:562 ==>> 检测【打开小电池2】
2025-07-31 20:20:28:569 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:20:28:645 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:20:28:837 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:20:28:844 ==>> 该项需要延时执行
2025-07-31 20:20:29:342 ==>> 此处延时了:【500】毫秒
2025-07-31 20:20:29:353 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:20:29:377 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:20:29:450 ==>> 5A A5 02 5A A5 


2025-07-31 20:20:29:540 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:20:29:779 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:20:29:786 ==>> 该项需要延时执行
2025-07-31 20:20:30:233 ==>> [D][05:19:02][COMM]------------ready to Power on Acckey 1------------
[D][05:19:02][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:02][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:02][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:02][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:02][COMM]----- get Acckey 1 and value:1------------
[W][05:19:02][COMM]CAN START!
[D][05:19:02][CAT1]gsm read msg sub id: 12
[D][05:19:02][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:02][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 73456
[D][05:19:02][COMM][Audio]exec status ready.
[D][05:19:02][CAT1]<<< 
OK

[D][05:19:02][CAT1]exec over: func id: 12, ret: 6
[D][05:19:02][COMM]imu wakeup ok. 73471
[D][

2025-07-31 20:20:30:278 ==>> 05:19:02][COMM]imu wakeup 1
[W][05:19:02][COMM]wake up system, wakeupEvt=0x80
[D][05:19:02][COMM]frm_can_weigth_power_set 1
[D][05:19:02][COMM]Clear Sleep Block Evt
[D][05:19:02][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:20:30:293 ==>> 此处延时了:【500】毫秒
2025-07-31 20:20:30:304 ==>> 检测【进入休眠模式2】
2025-07-31 20:20:30:328 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:20:30:488 ==>> [D][05:19:02][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:02][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:02][COMM]1x1 rx timeout
[D][05:19:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:20:30:593 ==>>                                                                50. period:50
[D][05:19:02][COMM]msg 02A4 loss. last_tick:73441. cur_tick:73950. period:50
[D][05:19:02][COMM]msg 02A5 loss. last_tick:73441. cur_tick:73950. period:50
[D][05:19:02][COMM]msg 02A6 loss. last_tick:73441. cur_tick:73951. period:50
[D][05:19:02][COMM]msg 02A7 loss. last_tick:73441. cur_tick:73951. period:

2025-07-31 20:20:30:638 ==>> 50
[D][05:19:02][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 73952
[D][05:19:02][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 73952


2025-07-31 20:20:30:960 ==>> [E][05:19:03][COMM]1x1 rx timeout
[E][05:19:03][COMM]1x1 tp timeout
[E][05:19:03][COMM]1x1 error -3.
[D][05:19:03][COMM]Main Task receive event:28 finished processing
[D][05:19:03][COMM]Main Task receive event:28
[D][05:19:03][COMM]prepare to sleep
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 6
[D][05:19:03][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:20:31:264 ==>> [D][05:19:03][COMM]msg 0220 loss. last_tick:73441. cur_tick:74446. period:100
[D][05:19:03][COMM]msg 0221 loss. last_tick:73441. cur_tick:74446. period:100
[D][05:19:03][COMM]msg 0224 loss. last_tick:73441. cur_tick:74447. period:100
[D][05:19:03][COMM]msg 0260 loss. last_tick:73441. cur_tick:74447. period:100
[D][05:19:03][COMM]msg 0280 loss. last_tick:73441. cur_tick:74447. period:100
[D][05:19:03][COMM]msg 02C0 loss. last_tick:73441. cur_tick:74448. period:100
[D][05:19:03][COMM]msg 02C1 loss. last_tick:73441. cur_tick:74448. period:100
[D][05:19:03][COMM]msg 02C2 loss. last_tick:73441. cur_tick:74449. period:100
[D][05:19:03][COMM]msg 02E0 loss. last_tick:73441. cur_tick:74449. period:100
[D][05:19:03][COMM]msg 02E1 loss. last_tick:73441. cur_tick:74449. period:100
[D][05:19:03][COMM]msg 02E2 loss. last_tick:73441. cur_tick:74450. period:100
[D][05:19:03][COMM]msg 0300 loss. last_tick:73441. cur_tick:74450. period:100
[D][05:19:03][COMM]msg 0301 loss. last_tick:73441. cur_tick:74450. period:100
[D][05:19:03][COMM]bat msg 0240 loss. last_tick:73441. cur_tick:74451. period:100. j,i:1 54
[D][05:19:03][COMM]bat msg 0241 loss. last_tick:73441. cur_tick:74451. period:10

2025-07-31 20:20:31:339 ==>> 0. j,i:2 55
[D][05:19:03][COMM]bat msg 0242 loss. last_tick:73441. cur_tick:74451. period:100. j,i:3 56
[D][05:19:03][COMM]bat msg 0244 loss. last_tick:73441. cur_tick:74452. period:100. j,i:5 58
[D][05:19:03][COMM]bat msg 024E loss. last_tick:73441. cur_tick:74452. period:100. j,i:15 68
[D][05:19:03][COMM]bat msg 024F loss. last_tick:73441. cur_tick:74453. period:100. j,i:16 69
[D][05:19:03][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 74453
[D][05:19:03][COMM]CAN message bat fault change: 0x00000000->0x0001802E 74454
[D][05:19:03][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 74454


2025-07-31 20:20:31:369 ==>>                                                                               

2025-07-31 20:20:31:551 ==>> [D][05:19:03][COMM]msg 0222 loss. last_tick:73441. cur_tick:74948. period:150
[D][05:19:03][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 74949


2025-07-31 20:20:31:641 ==>>                                                                                                                    roff type 16.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:20:31:746 ==>> [E][05:19:04][COMM]1x1 rx timeout
[E][05:19:04][COMM]1x1 tp timeout

2025-07-31 20:20:31:821 ==>> 
[E][05:19:04][COMM]1x1 error -3.
[W][05:19:04][COMM]CAN STOP!
[D][05:19:04][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 1------------
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------
[D][05:19:04][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:04][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 106
[D][05:19:04][COMM]bat sleep fail, reason:-1
[D][05:19:04][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM]imu rest ok. 75148
[D][05:19:04][COMM]imu sleep 0
[W][05:19:04][COMM]now sleep


2025-07-31 20:20:31:854 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:20:31:861 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:20:31:868 ==>> 开始小电池电流采样
2025-07-31 20:20:31:886 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:31:956 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:20:32:961 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:20:33:038 ==>> CurrentBattery:ƽ��:66.82

2025-07-31 20:20:33:472 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:33:479 ==>> 【检测小电池休眠电流】通过,【66.82uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:20:33:493 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:20:33:523 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:20:33:549 ==>> 5A A5 01 5A A5 


2025-07-31 20:20:33:652 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:20:33:765 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:20:33:773 ==>> 该项需要延时执行
2025-07-31 20:20:33:892 ==>> [D][05:19:06][COMM]------------ready to Power on Acckey 1------------
[D][05:19:06][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:06][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:06][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:06][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:06][COMM]----- get Acckey 1 and value:1------------
[W][05:19:06][COMM]CAN START!
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:06][COMM]CAN message bat fault change: 0x0001802E->0x00000000 77139
[D][05:19:06][COMM][Audio]exec status ready.
[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 6
[D][05:19:06][COMM]imu wakeup ok. 77153
[D][05:19:06][COMM]imu wakeup 1
[W][05:19:06][COMM]wake up system, wakeupEvt=0x80
[D][05:19:06][COMM]frm_can_weigth_power_set 1
[D][05:19:06][COMM]Clear Sleep Block Evt
[D][05:19:06][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:06][COMM]1x1 frm_can_tp_send ok
[D][05:19:06][COMM]read battery soc:0


2025-07-31 20:20:34:151 ==>> [E][05:19:06][COMM]1x1 rx timeout
[D][05:19:06][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:20:34:256 ==>> [D][05:19:06][COMM]msg 02A0 loss. last_tick:77121. cur_tick:77633. period:50
[D][05:19:06][COMM]msg 02A4 loss. last_tick:77121. cur_tick:77633. period:50
[D][05:19:06][COMM]msg 02A5 loss

2025-07-31 20:20:34:271 ==>> 此处延时了:【500】毫秒
2025-07-31 20:20:34:291 ==>> 检测【检测唤醒】
2025-07-31 20:20:34:303 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:20:34:321 ==>> . last_tick:77121. cur_tick:77634. period:50
[D][05:19:06][COMM]msg 02A6 loss. last_tick:77121. cur_tick:77634. period:50
[D][05:19:06][COMM]msg 02A7 loss. last_tick:77121. cur_tick:77634. period:50
[D][05:19:06][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 77635
[D][05:19:06][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 77635


2025-07-31 20:20:35:026 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:06][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:06][FCTY]==========Modules-nRF5340 ==========
[D][05:19:06][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:06][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:06][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:06][FCTY]DeviceID    = 460130071541254
[D][05:19:06][FCTY]HardwareID  = 867222087953545
[D][05:19:06][FCTY]MoBikeID    = 9999999999
[D][05:19:06][FCTY]LockID      = FFFFFFFFFF
[D][05:19:06][FCTY]BLEFWVersion= 105
[D][05:19:06][FCTY]BLEMacAddr   = FB12B0AFE0B8
[D][05:19:06][FCTY]Bat         = 3884 mv
[D][05:19:06][FCTY]Current     = 0 ma
[D][05:19:06][FCTY]VBUS        = 2600 mv
[D][05:19:06][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:06][FCTY]Ext battery vol = 32, adc = 1295
[D][05:19:06][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 252 mv
[D][05:19:06][FCTY]Bike Type flag is invalied
[D][05:19:06][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:06][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:06][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:06][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:06][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:06][FCTY

2025-07-31 20:20:35:078 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:20:35:088 ==>> 检测【关机】
2025-07-31 20:20:35:119 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:20:35:134 ==>> ]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:06][FCTY]Bat1         = 3799 mv
[D][05:19:06][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:06][FCTY]==========Modules-nRF5340 ==========
[E][05:19:06][COMM]1x1 rx timeout
[E][05:19:06][COMM]1x1 tp timeout
[E][05:19:06][COMM]1x1 error -3.
[D][05:19:06][COMM]Main Task receive event:28 finished processing
[D][05:19:06][COMM]Main Task receive event:65
[D][05:19:06][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:06][COMM]Main Task receive event:65 finished processing
[D][05:19:06][COMM]Main Task receive event:60
[D][05:19:06][COMM]smart_helmet_vol=255,255
[D][05:19:06][COMM]report elecbike
[W][05:19:06][PROT]remove success[1629955146],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:06][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:06][PROT]add success [1629955146],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:06][COMM]Main Task receive event:60 finished processing
[D][05:19:06][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:06][PROT]index:0
[D][05:19:06][PROT]is_send:1
[D][05:19:06][PROT]sequence_num:10
[D][05:19:06][PROT

2025-07-31 20:20:35:236 ==>> ]retry_timeout:0
[D][05:19:06][PROT]retry_times:3
[D][05:19:06][PROT]send_path:0x3
[D][05:19:06][PROT]msg_type:0x5d03
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][PROT]Sending traceid[999999999990000B]
[D][05:19:06][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:06][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:06][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:06][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:06][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:06][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:06][SAL ]open socket ind id[4], rst[0]
[D][05:19:06][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:06][SAL ]Cellular task submsg id[8]
[D][05:19:06][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:06][SAL ]domain[bikeapi.mobike.com] port[9999] type[

2025-07-31 20:20:35:341 ==>> 1]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:06][CAT1]gsm read msg sub id: 8
[D][05:19:06][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:07][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:07][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:07][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:07][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:07][CAT1]<<< 
+CME ERROR: 100

[D][05:19:07][COMM]msg 0220 loss. last_tick:77121. cur_tick:78128. period:100
[D][05:19:07][COMM]msg 0221 loss. last_tick:77121. cur_tick:78129. period:100
[D][05:19:07][COMM]msg 0224 loss. last_tick:77121. cur_tick:78129. period:100
[D][05:19:07][COMM]msg 0260 loss. last_tick:77121. cur_tick:78129. period:100
[D][05:19:07][COMM]msg 0280 loss. last_tick:77121. cur_tick:78130. period:100
[D][05:19:07][COMM]msg 02C0 loss. last_tick:77121. cur_tick:78130. period:100
[D][05:19:07][COMM]msg 02C1 loss. last_tick:77121. cur_tick:78131. period:100
[D][05:19:07][COMM]msg 02C2 loss. last_tick:77121. cur_tick:78131. period:100
[D][05:19:07][COMM]msg 02E0 loss. last_tick:

2025-07-31 20:20:35:446 ==>> 77121. cur_tick:78131. period:100
[D][05:19:07][COMM]msg 02E1 loss. last_tick:77121. cur_tick:78132. period:100
[D][05:19:07][COMM]msg 02E2 loss. last_tick:77121. cur_tick:78132. period:100
[D][05:19:07][COMM]msg 0300 loss. last_tick:77121. cur_tick:78132. period:100
[D][05:19:07][COMM]msg 0301 loss. last_tick:77121. cur_tick:78133. period:100
[D][05:19:07][COMM]bat msg 0240 loss. last_tick:77121. cur_tick:78133. period:100. j,i:1 54
[D][05:19:07][COMM]bat msg 0241 loss. last_tick:77121. cur_tick:78134. period:100. j,i:2 55
[D][05:19:07][COMM]bat msg 0242 loss. last_tick:77121. cur_tick:78134. period:100. j,i:3 56
[D][05:19:07][COMM]bat msg 0244 loss. last_tick:77121. cur_tick:78134. period:100. j,i:5 58
[D][05:19:07][COMM]bat msg 024E loss. last_tick:77121. cur_tick:78135. period:100. j,i:15 68
[D][05:19:07][COMM]bat msg 024F loss. last_tick:77121. cur_tick:78135. period:100. j,i:16 69
[D][05:19:07][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 78135
[D][05:19:07][COMM]CAN message bat fault change: 0x00000000->0x0001802E 78136
[D][05:19:07][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 78136


2025-07-31 20:20:35:551 ==>>                                                                                                                              

2025-07-31 20:20:35:656 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:20:35:761 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              BAT CAN get soc Fail, 204
[D][05:19:07][COMM]BAT CAN get state2 fail 204
[D][05:19:07][COMM]get soh error
[E][05:19:07][COMM]Fatal!!! missing comm with Bat, set fatal cod

2025-07-31 20:20:35:806 ==>> e
[D][05:19:07][COMM]report elecbike
[W][05:19:07][PROT]remove success[1629955147],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:07][COMM]Main Task receive event:60 finished processing
[D][05:19:07][COMM]Main Task

2025-07-31 20:20:36:109 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:20:36:154 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 20:20:36:259 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:20:36:364 ==>>                                             M]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:07][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:07][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:07][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:07][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:07][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:07][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:07][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:07][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:07][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:07][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:07][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[W][05:19:07][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:07][COMM]arm_hub_enable: hub power: 0
[D][05:19:07][HSDK]hexlog index save 0 3328 255 @ 0 : 0
[D][05:19:07][HSDK]write save hexlog index [0]
[D][05:19:07][FCTY]F:[syncPa

2025-07-31 20:20:36:469 ==>> raFromRamToFlash].L:[962] ready to read para flash
[D][05:19:07][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:08][COMM]read battery soc:255
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:08][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:08][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 20:20:36:499 ==>>                               

2025-07-31 20:20:36:604 ==>>       :19:08][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:08][COMM]arm_hub_enable: hub power: 0
[D][05:19:08][HSDK]hexlog index save 0 3328 255 @ 0 : 0
[D][05:19:08][HSDK]write save hexlog index [0]
[D][05:19:08][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:08][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:20:37:065 ==>> [W][05:19:09][COMM]Power Off


2025-07-31 20:20:37:160 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:20:37:168 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:20:37:179 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:20:37:246 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:20:37:427 ==>> [D][05:19:09][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:09][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:20:37:437 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:20:37:446 ==>> 检测【检测小电池关机电流】
2025-07-31 20:20:37:457 ==>> 开始小电池电流采样
2025-07-31 20:20:37:471 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:37:547 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:20:38:556 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:20:38:647 ==>> CurrentBattery:ƽ��:67.88

2025-07-31 20:20:39:068 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:20:39:075 ==>> 【检测小电池关机电流】通过,【67.88uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:20:39:340 ==>> MES过站成功
2025-07-31 20:20:39:348 ==>> #################### 【测试结束】 ####################
2025-07-31 20:20:39:397 ==>> 关闭5V供电
2025-07-31 20:20:39:410 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:20:39:450 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:20:40:404 ==>> 关闭5V供电成功
2025-07-31 20:20:40:416 ==>> 关闭33V供电
2025-07-31 20:20:40:441 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:20:40:545 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:20:41:410 ==>> 关闭33V供电成功
2025-07-31 20:20:41:422 ==>> 关闭3.7V供电
2025-07-31 20:20:41:446 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:20:41:550 ==>> 6A A6 02 A6 6A 


2025-07-31 20:20:41:640 ==>> Battery OFF
OVER 150


2025-07-31 20:20:42:411 ==>> 关闭3.7V供电成功
