2025-07-31 18:01:22:149 ==>> MES查站成功:
查站序号:P510001005313485验证通过
2025-07-31 18:01:22:163 ==>> 扫码结果:P510001005313485
2025-07-31 18:01:22:164 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:01:22:166 ==>> 测试参数版本:2024.10.11
2025-07-31 18:01:22:167 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:01:22:169 ==>> 检测【打开透传】
2025-07-31 18:01:22:170 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:01:22:255 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:01:22:511 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:01:22:554 ==>> 检测【检测接地电压】
2025-07-31 18:01:22:556 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:01:22:656 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:01:22:825 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:01:22:828 ==>> 检测【打开小电池】
2025-07-31 18:01:22:831 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:01:22:949 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:01:23:103 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:01:23:106 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:01:23:109 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:01:23:146 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:01:23:380 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:01:23:382 ==>> 检测【等待设备启动】
2025-07-31 18:01:23:384 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:01:23:784 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:01:23:982 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:01:24:428 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:01:24:600 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 18:01:24:675 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:01:25:075 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:01:25:474 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:01:25:534 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:01:25:750 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:01:25:754 ==>> 检测【产品通信】
2025-07-31 18:01:25:756 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:01:25:918 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:01:26:026 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:01:26:029 ==>> 检测【初始化完成检测】
2025-07-31 18:01:26:033 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:01:26:286 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:01:26:564 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:01:26:567 ==>> 检测【关闭大灯控制1】
2025-07-31 18:01:26:594 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:01:26:596 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:26:772 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:01:26:835 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:01:26:838 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:01:26:839 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:01:27:048 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:01:27:111 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:01:27:113 ==>> 检测【关闭仪表供电】
2025-07-31 18:01:27:114 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:01:27:340 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:01:27:388 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:01:27:391 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:01:27:394 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:01:27:537 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:01:27:612 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:27:665 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:01:27:668 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:01:27:670 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:01:27:809 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:01:27:963 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:01:27:965 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:01:27:967 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:01:28:132 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:01:28:241 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:01:28:244 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:01:28:246 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:01:28:346 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:01:28:421 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 22


2025-07-31 18:01:28:496 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 18:01:28:515 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:01:28:517 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:01:28:519 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:01:28:601 ==>> [D][05:17:53][COMM]4648 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:28:646 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:01:28:785 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:01:28:788 ==>> 该项需要延时执行
2025-07-31 18:01:29:153 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5014. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5015. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5017. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN 

2025-07-31 18:01:29:183 ==>> message bat fault change: 0x0001802E->0x01B987FE 5017


2025-07-31 18:01:29:631 ==>> [D][05:17:54][COMM]5660 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:30:014 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:01:30:521 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task 

2025-07-31 18:01:30:626 ==>> receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_

2025-07-31 18:01:30:731 ==>> GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove succ

2025-07-31 18:01:30:776 ==>> ess[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 18:01:30:821 ==>>                                                                                                                                                                                   

2025-07-31 18:01:31:654 ==>> [D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:32:510 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:01:32:676 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:32:812 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:01:32:816 ==>> 检测【33V输入电压ADC】
2025-07-31 18:01:32:819 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:01:33:062 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3170  volt:5572 mv
[D][05:17:57][COMM]adc read out 24v adc:1313  volt:33209 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:1  volt:1 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:4 mv
[D][05:17:57][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:18  volt:14 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2438  volt:3928 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:14  volt:324 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:01:33:081 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:01:33:084 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:01:33:087 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:01:33:166 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1676mV
Get AD_V4 0mV
Get AD_V5 2785mV
Get AD_V6 2041mV
Get AD_V7 1091mV
OVER 150


2025-07-31 18:01:33:353 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:33:356 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:01:33:372 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1676mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:33:375 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:01:33:377 ==>> 原始值:【2785】, 乘以分压基数【2】还原值:【5570】
2025-07-31 18:01:33:392 ==>> 【TP68_VCC5V5(ADV5)】通过,【5570mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:01:33:394 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:01:33:411 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2041mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:01:33:413 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:01:33:434 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:01:33:436 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:01:33:566 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1679mV
Get AD_V4 0mV
Get AD_V5 2790mV
Get AD_V6 1985mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:01:33:671 ==>> [D][05:17:58][COMM]9704 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:01:33:709 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:33:712 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:01:33:728 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1679mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:33:747 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:01:33:749 ==>> 原始值:【2790】, 乘以分压基数【2】还原值:【5580】
2025-07-31 18:01:33:751 ==>> 【TP68_VCC5V5(ADV5)】通过,【5580mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:01:33:753 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:01:33:766 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1985mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:01:33:768 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:01:33:790 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:01:33:793 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:01:33:867 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1676mV
Get AD_V4 0mV
Get AD_V5 2782mV
Get AD_V6 2025mV
Get AD_V7 1088mV
OVER 150


2025-07-31 18:01:33:972 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1

2025-07-31 18:01:34:017 ==>> 000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10006
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 18:01:34:070 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:34:072 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:01:34:088 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1676mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:34:091 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:01:34:092 ==>> 原始值:【2782】, 乘以分压基数【2】还原值:【5564】
2025-07-31 18:01:34:107 ==>> 【TP68_VCC5V5(ADV5)】通过,【5564mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:01:34:110 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:01:34:126 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2025mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:01:34:128 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:01:34:149 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:01:34:151 ==>> 检测【打开WIFI(1)】
2025-07-31 18:01:34:153 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:01:34:309 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:01:34:415 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 18:01:34:419 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:01:34:432 ==>> 检测【清空消息队列(1)】
2025-07-31 18:01:34:436 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:01:34:506 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:01:34:932 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:17:59][CAT1]<<< 

OK

[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10715 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COM

2025-07-31 18:01:34:992 ==>> M]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:01:35:207 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:01:35:211 ==>> 检测【打开GPS(1)】
2025-07-31 18:01:35:214 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:01:35:358 ==>>                                                                                                                                                                                                                                                                                                                                                        AT+GSN

[D][05:18:00][CAT1]<<< 
867222087783249

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539038

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:01:35:464 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 18:01:35:691 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 18:01:35:740 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:01:35:743 ==>> 检测【打开GSM联网】
2025-07-31 18:01:35:767 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:01:35:934 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:01:36:014 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:01:36:016 ==>> 检测【打开仪表供电1】
2025-07-31 18:01:36:020 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:01:36:319 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 18:01:36:517 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 18:01:36:550 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:01:36:553 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:01:36:556 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:01:36:743 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:01:36:826 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:01:36:829 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:01:36:831 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:01:37:033 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[32992]


2025-07-31 18:01:37:097 ==>> 【读取主控ADC采集的仪表电压】通过,【32992mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:01:37:100 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:01:37:102 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:01:37:247 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:01:37:369 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:01:37:374 ==>> 检测【AD_V20电压】
2025-07-31 18:01:37:377 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:01:37:475 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:01:37:598 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 18:01:37:688 ==>> [D][05:18:02][COMM]13726 imu init OK


2025-07-31 18:01:37:793 ==>> [D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT

2025-07-31 18:01:37:823 ==>> =1



2025-07-31 18:01:37:928 ==>> 本次取值间隔时间:445ms
2025-07-31 18:01:37:946 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:01:38:053 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:01:38:145 ==>> 1A A1 10 00 00 
Get AD_V20 0mV
OVER 150


2025-07-31 18:01:38:433 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[

2025-07-31 18:01:38:478 ==>> D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 27,99

OK



2025-07-31 18:01:38:508 ==>> 本次取值间隔时间:451ms
2025-07-31 18:01:38:526 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:01:38:583 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 18:01:38:628 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:01:38:750 ==>> 1A A1 10 00 00 
Get AD_V20 1668mV
OVER 150


2025-07-31 18:01:38:855 ==>>         8:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display 

2025-07-31 18:01:38:960 ==>> ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:01:39:050 ==>> 本次取值间隔时间:414ms
2025-07-31 18:01:39:069 ==>> 【AD_V20电压】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:01:39:073 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:01:39:077 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:01:39:157 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:01:39:347 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:01:39:388 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:01:39:392 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:01:39:554 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:01:39:644 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:01:40:292 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:01:40:382 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:01:40:518 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,26,,,42,24,,,42,38,,,41,60,,,41,1*72

$GBGSV,3,2,09,42,,,40,39,,,38,21,,,42,13,,,39,1*70

$GBGSV,3,3,09,59,,,39,1*79

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1685.887,1685.887,53.847,2097152,2097152,2097152*42

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 18:01:40:623 ==>> [D][05:18:05][COMM]read battery soc:255
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:01:40:683 ==>>                                                                                             

2025-07-31 18:01:41:414 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:01:41:444 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,24,,,42,26,,,41,38,,,41,60,,,41,1*7A

$GBGSV,4,2,14,13,,,41,3,,,41,21,,,40,42,,,40,1*41

$GBGSV,4,3,14,59,,,40,39,,,39,16,,,39,8,,,38,1*42

$GBGSV,4,4,14,1,,,37,6,,,37,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1649.374,1649.374,52.689,2097152,2097152,2097152*4F



2025-07-31 18:01:41:640 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:01:42:447 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 18:01:42:451 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,42,26,,,41,38,,,41,60,,,41,1*77

$GBGSV,5,2,18,13,,,41,3,,,40,21,,,40,42,,,40,1*4D

$GBGSV,5,3,18,59,,,40,39,,,39,16,,,39,8,,,39,1*4E

$GBGSV,5,4,18,1,,,37,6,,,37,2,,,37,33,,,34,1*48

$GBGSV,5,5,18,4,,,33,5,,,31,1*7C

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1593.844,1593.844,50.989,2097152,2097152,2097152*42



2025-07-31 18:01:42:454 ==>> #################### 【测试结束】 ####################
2025-07-31 18:01:42:477 ==>> 关闭5V供电
2025-07-31 18:01:42:482 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:01:42:552 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:01:42:597 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 18:01:43:463 ==>> $GBGGA,100147.274,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,42,38,,,42,26,,,41,60,,,41,1*74

$GBGSV,5,2,18,13,,,41,3,,,40,21,,,40,42,,,40,1*4D

$GBGSV,5,3,18,59,,,40,39,,,40,8,,,40,16,,,39,1*4E

$GBGSV,5,4,18,1,,,38,6,,,37,2,,,37,4,,,34,1*73

$GBGSV,5,5,18,33,,,33,5,,,33,1*4A

$GBRMC,100147.274,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100147.274,0.000,1607.653,1607.653,51.420,2097152,2097152,2097152*51



2025-07-31 18:01:43:478 ==>> 关闭5V供电成功
2025-07-31 18:01:43:482 ==>> 关闭33V供电
2025-07-31 18:01:43:495 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:01:43:553 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:01:43:811 ==>> [D][05:18:08][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 8,volt = 12
$GBGGA,100147.574,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,42,26,,,41,60,,,41,1*7F

$GBGSV,5,2,20,13,,,41,3,,,40,21,,,40,42,,,40,1*46

$GBGSV,5,3,20,59,,,40,39,,,40,8,,,40,16,,,39,1*45

$GBGSV,5,4,20,1,,,38,6,,,37,2,,,37,14,,,35,1*48

$GBGSV,5,5,20,4,,,34,33,,,33,5,,,33,9,,,37,1*4F

$GBRMC,100147.574,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,100147.574,0.000,1599.413,1599.413,51.160,2097152,2097152,2097152*57



2025-07-31 18:01:43:916 ==>> [D][05:18:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:01:44:488 ==>> 关闭33V供电成功
2025-07-31 18:01:44:492 ==>> 关闭3.7V供电
2025-07-31 18:01:44:496 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:01:44:550 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:01:45:185 ==>>  

