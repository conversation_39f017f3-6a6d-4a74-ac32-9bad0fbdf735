2025-07-31 22:54:47:690 ==>> MES查站成功:
查站序号:P510001005312640验证通过
2025-07-31 22:54:47:694 ==>> 扫码结果:P510001005312640
2025-07-31 22:54:47:695 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:54:47:697 ==>> 测试参数版本:2024.10.11
2025-07-31 22:54:47:698 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:54:47:700 ==>> 检测【打开透传】
2025-07-31 22:54:47:701 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:54:47:820 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:54:47:986 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:54:47:991 ==>> 检测【检测接地电压】
2025-07-31 22:54:47:994 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:54:48:107 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 22:54:48:276 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:54:48:280 ==>> 检测【打开小电池】
2025-07-31 22:54:48:285 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:54:48:411 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:54:48:632 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:54:48:634 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:54:48:641 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:54:48:716 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:54:48:930 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:54:48:932 ==>> 检测【等待设备启动】
2025-07-31 22:54:48:935 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:54:49:183 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:54:49:378 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 22:54:49:895 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:54:49:955 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:54:50:060 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer st

2025-07-31 22:54:50:562 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:54:50:759 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:54:50:988 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:54:51:497 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM


2025-07-31 22:54:51:832 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:54:52:029 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:54:52:314 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:54:52:561 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:54:52:564 ==>> 检测【产品通信】
2025-07-31 22:54:52:567 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:54:52:682 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:54:52:837 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:54:52:840 ==>> 检测【初始化完成检测】
2025-07-31 22:54:52:843 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:54:53:051 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:54:53:116 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:54:53:119 ==>> 检测【关闭大灯控制1】
2025-07-31 22:54:53:141 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:54:53:308 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:54:53:401 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:54:53:404 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:54:53:407 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:54:53:785 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:54:53:981 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:54:54:437 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:54:54:710 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 22:54:55:088 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:54:55:467 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:54:55:572 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:54:55:677 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<


2025-07-31 22:54:56:493 ==>> 未匹配到【打开仪表指令模式1】数据,请核对检查!
2025-07-31 22:54:56:497 ==>> #################### 【测试结束】 ####################
2025-07-31 22:54:56:515 ==>> 关闭5V供电
2025-07-31 22:54:56:526 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:54:56:616 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:54:56:721 ==>> [W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]


2025-07-31 22:54:57:525 ==>> 关闭5V供电成功
2025-07-31 22:54:57:529 ==>> 关闭33V供电
2025-07-31 22:54:57:532 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:54:57:615 ==>> 5A A5 02 5A A5 


2025-07-31 22:54:57:720 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:54:58:537 ==>> 关闭33V供电成功
2025-07-31 22:54:58:540 ==>> 关闭3.7V供电
2025-07-31 22:54:58:542 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:54:58:615 ==>> 6A A6 02 A6 6A 


2025-07-31 22:54:58:705 ==>> Battery OFF
OVER 150


2025-07-31 22:54:58:900 ==>>  

