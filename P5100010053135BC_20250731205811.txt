2025-07-31 20:58:11:581 ==>> MES查站成功:
查站序号:P5100010053135BC验证通过
2025-07-31 20:58:11:593 ==>> 扫码结果:P5100010053135BC
2025-07-31 20:58:11:618 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:58:11:620 ==>> 测试参数版本:2024.10.11
2025-07-31 20:58:11:621 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:58:11:622 ==>> 检测【打开透传】
2025-07-31 20:58:11:624 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:58:11:660 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:58:11:997 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:58:12:025 ==>> 检测【检测接地电压】
2025-07-31 20:58:12:027 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:58:12:169 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:58:12:318 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:58:12:322 ==>> 检测【打开小电池】
2025-07-31 20:58:12:325 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:58:12:457 ==>> 6A A6 01 A6 6A 


2025-07-31 20:58:12:562 ==>> Battery ON
OVER 150


2025-07-31 20:58:12:630 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:58:12:632 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:58:12:633 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:58:12:758 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:58:12:940 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:58:12:943 ==>> 检测【等待设备启动】
2025-07-31 20:58:12:946 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:58:13:294 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:58:13:489 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:58:13:969 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:58:14:137 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:58:14:197 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:58:14:608 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:58:15:008 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:58:15:083 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:58:15:299 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:58:15:302 ==>> 检测【产品通信】
2025-07-31 20:58:15:303 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:58:15:433 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:58:15:583 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:58:15:585 ==>> 检测【初始化完成检测】
2025-07-31 20:58:15:589 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:58:15:797 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15
[D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:58:15:866 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:58:15:868 ==>> 检测【关闭大灯控制1】
2025-07-31 20:58:15:870 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:58:16:028 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:58:16:132 ==>> [D][05:17:51][COMM]2639 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:16:156 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:58:16:159 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:58:16:161 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:58:16:297 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:58:16:402 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:58:16:446 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:58:16:449 ==>> 检测【关闭仪表供电】
2025-07-31 20:58:16:452 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:58:16:661 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:58:16:739 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:58:16:742 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:58:16:744 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:58:16:951 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:58:17:035 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:58:17:041 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:58:17:044 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:58:17:133 ==>> [D][05:17:52][COMM]3651 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:17:223 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:58:17:320 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:58:17:324 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:58:17:326 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:17:527 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:58:17:598 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:58:17:605 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:58:17:610 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:58:17:662 ==>> 5A A5 01 5A A5 


2025-07-31 20:58:17:768 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:58:17:828 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 23


2025-07-31 20:58:17:882 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:58:17:889 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:58:17:893 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:58:17:918 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:58:17:963 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:58:18:158 ==>> [D][05:17:53][COMM]4662 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:58:18:173 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:58:18:180 ==>> 该项需要延时执行
2025-07-31 20:58:18:659 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00

2025-07-31 20:58:18:689 ==>> C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020


2025-07-31 20:58:19:165 ==>> [D][05:17:54][COMM]5673 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:19:425 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:58:19:925 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec80

2025-07-31 20:58:20:030 ==>> 0m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:1

2025-07-31 20:58:20:136 ==>> 7:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 

2025-07-31 20:58:20:195 ==>> 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 20:58:20:225 ==>>                                                                                                                                           

2025-07-31 20:58:21:188 ==>> [D][05:17:56][COMM]7695 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:21:929 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:58:22:187 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:58:22:191 ==>> 检测【33V输入电压ADC】
2025-07-31 20:58:22:195 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:22:197 ==>> [D][05:17:57][COMM]8706 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:22:475 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:17:57][COMM]adc read out 24v adc:1325  volt:33513 mv
[D][05:17:57][COMM]adc read left brake adc:18  volt:23 mv
[D][05:17:57][COMM]adc read right brake adc:15  volt:19 mv
[D][05:17:57][COMM]adc read throttle adc:15  volt:19 mv
[D][05:17:57][COMM]adc read battery ts volt:20 mv
[D][05:17:57][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:17:57][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2432  volt:3918 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:58:22:758 ==>> 【33V输入电压ADC】通过,【32830mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:58:22:761 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:58:22:763 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:58:22:870 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2753mV
Get AD_V6 2021mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:58:23:077 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:23:079 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:58:23:171 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:23:174 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:58:23:195 ==>> 原始值:【2753】, 乘以分压基数【2】还原值:【5506】
2025-07-31 20:58:23:208 ==>> [D][05:17:58][COMM]9717 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:58:23:223 ==>> 【TP68_VCC5V5(ADV5)】通过,【5506mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:58:23:225 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:58:23:280 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:58:23:283 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:58:23:336 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:58:23:361 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:58:23:467 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2752mV
Get AD_V6 1992mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:58:23:527 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10008
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 20:58:23:720 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:23:723 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:58:23:745 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:23:759 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:58:23:763 ==>> 原始值:【2752】, 乘以分压基数【2】还原值:【5504】
2025-07-31 20:58:23:770 ==>> 【TP68_VCC5V5(ADV5)】通过,【5504mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:58:23:776 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:58:23:795 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:58:23:797 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:58:23:826 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:58:23:829 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:58:23:923 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:58:23:968 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1643mV
Get AD_V4 0mV
Get AD_V5 2752mV
Get AD_V6 2022mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:58:24:119 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:24:123 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:58:24:146 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:24:150 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:58:24:153 ==>> 原始值:【2752】, 乘以分压基数【2】还原值:【5504】
2025-07-31 20:58:24:187 ==>> 【TP68_VCC5V5(ADV5)】通过,【5504mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:58:24:193 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:58:24:216 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2022mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:58:24:218 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:58:24:249 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:58:24:251 ==>> 检测【打开WIFI(1)】
2025-07-31 20:58:24:254 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:58:24:502 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10728 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,

2025-07-31 20:58:24:547 ==>> 0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:58:24:788 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:58:24:793 ==>> 检测【清空消息队列(1)】
2025-07-31 20:58:24:796 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:58:24:927 ==>>                                                                                                                                                                                                                                                                                                                                                                  [D][05:18:00][CAT1]<<< 
867222088007986

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539001

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:58:25:032 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:58:25:077 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:58:25:080 ==>> 检测【打开GPS(1)】
2025-07-31 20:58:25:082 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:58:25:560 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 5, ret: 6
[D][05:18:00][CAT1]sub id: 5, ret: 6

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:00][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:00][M2M ]M2M_GSM_INIT OK
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:00][SAL ]open socket ind id[4], rst[0]
[

2025-07-31 20:58:25:617 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:58:25:621 ==>> 检测【打开GSM联网】
2025-07-31 20:58:25:624 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:58:25:666 ==>> D][05:18:00][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:00][SAL ]Cellular task submsg id[8]
[D][05:18:00][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:00][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:00][CAT1]gsm read msg sub id: 8
[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:00][COMM]Main Task receive event:4
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:00][COMM]init key as 
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:00][COMM]Main Task receive event:4 finished processing
[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:00][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:00][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:58:25:771 ==>>                                                                                                                                                                                                        rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:01][COMM]>>>>>In

2025-07-31 20:58:25:802 ==>> put command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:58:25:917 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:58:25:920 ==>> 检测【打开仪表供电1】
2025-07-31 20:58:25:922 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:58:25:937 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:58:26:436 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:58:26:748 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:58:26:753 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:58:26:757 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:58:26:953 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:58:27:045 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:58:27:048 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:58:27:051 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:58:27:151 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:58:27:257 ==>>                                       [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33618]


2025-07-31 20:58:27:335 ==>> 【读取主控ADC采集的仪表电压】通过,【33618mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:58:27:340 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:58:27:345 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:58:27:560 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:58:27:624 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:58:27:628 ==>> 检测【AD_V20电压】
2025-07-31 20:58:27:630 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:58:27:727 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:58:27:806 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:58:28:030 ==>> 本次取值间隔时间:288ms
2025-07-31 20:58:28:045 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,24,,,40,39,,,38,40,,,37,41,,,37,1*70

$GBGSV,2,2,07,33,,,33,16,,,36,25,,,36,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1533.928,1533.928,49.043,2097152,2097152,2097152*45

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:58:28:210 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:58:28:270 ==>> 本次取值间隔时间:229ms
2025-07-31 20:58:28:465 ==>> 本次取值间隔时间:186ms
2025-07-31 20:58:28:815 ==>> 本次取值间隔时间:344ms
2025-07-31 20:58:28:819 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:58:28:923 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:58:28:939 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,60,,,42,24,,,41,25,,,41,59,,,41,1*7E

$GBGSV,4,2,14,39,,,39,33,,,39,40,,,38,41,,,38,1*7E

$GBGSV,4,3,14,14,,,38,3,,,38,16,,,36,4,,,35,1*72

$GBGSV,4,4,14,44,,,17,1,,,41,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1540.499,1540.499,49.440,2097152,2097152,2097152*42



2025-07-31 20:58:29:152 ==>> [D][05:18:04][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:04][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:58:29:391 ==>> 本次取值间隔时间:466ms
2025-07-31 20:58:29:889 ==>> 本次取值间隔时间:497ms
2025-07-31 20:58:29:979 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,60,,,41,24,,,41,25,,,41,33,,,41,1*73

$GBGSV,5,2,17,59,,,40,3,,,40,39,,,39,1,,,38,1*72

$GBGSV,5,3,17,40,,,38,41,,,38,14,,,38,16,,,37,1*7A

$GBGSV,5,4,17,44,,,35,4,,,34,5,,,34,2,,,34,1*43

$GBGSV,5,5,17,34,,,34,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1568.086,1568.086,50.150,2097152,2097152,2097152*4E

[D][05:18:05][COMM]read battery soc:255


2025-07-31 20:58:30:024 ==>> 本次取值间隔时间:123ms
2025-07-31 20:58:30:204 ==>> 本次取值间隔时间:169ms
2025-07-31 20:58:30:208 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:58:30:309 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:58:30:324 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:58:30:369 ==>> 1A A1 10 00 00 
Get AD_V20 1636mV
OVER 150


2025-07-31 20:58:30:444 ==>> 本次取值间隔时间:120ms
2025-07-31 20:58:30:485 ==>> 【AD_V20电压】通过,【1636mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:30:488 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:58:30:493 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:58:30:566 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:58:30:789 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:58:30:792 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:58:30:797 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:58:31:017 ==>> $GBGGA,125834.791,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,24,,,41,25,,,41,3,,,41,1*4A

$GBGSV,5,2,18,60,,,40,59,,,40,39,,,39,40,,,38,1*7D

$GBGSV,5,3,18,41,,,38,14,,,38,1,,,37,16,,,37,1*4F

$GBGSV,5,4,18,44,,,35,2,,,34,34,,,34,23,,,34,1*4B

$GBGSV,5,5,18,4,,,33,5,,,33,1*7E

$GBRMC,125834.791,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125834.791,0.000,1554.689,1554.689,49.738,2097152,2097152,2097152*56

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:58:31:076 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:58:31:079 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:58:31:083 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:58:31:167 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:58:31:363 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:58:31:366 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:58:31:369 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:58:31:562 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:58:31:661 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:58:31:665 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:58:31:669 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:58:31:787 ==>> $GBGGA,125835.591,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,42,24,,,41,25,,,41,3,,,40,1*42

$GBGSV,6,2,21,60,,,40,59,,,40,39,,,39,40,,,38,1*74

$GBGSV,6,3,21,41,,,38,14,,,38,16,,,37,1,,,36,1*47

$GBGSV,6,4,21,7,,,36,13,,,36,44,,,35,2,,,35,1*70

$GBGSV,6,5,21,34,,,34,23,,,34,4,,,33,5,,,33,1*71

$GBGSV,6,6,21,6,,,31,1*41

$GBRMC,125835.591,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125835.591,0.000,1533.964,1533.964,49.079,2097152,2097152,2097152*57



2025-07-31 20:58:31:892 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:58:31:952 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:58:31:955 ==>> 检测【AD_V21电压】
2025-07-31 20:58:31:957 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:58:31:966 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:58:32:041 ==>> 本次取值间隔时间:83ms
2025-07-31 20:58:32:056 ==>> 1A A1 20 00 00 
Get AD_V21 1062mV
OVER 150


2025-07-31 20:58:32:161 ==>> 本次取值间隔时间:115ms
2025-07-31 20:58:32:203 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:58:32:266 ==>> 1A A1 20 00 00 
Get AD_V21 1632mV
OVER 150


2025-07-31 20:58:32:604 ==>> 本次取值间隔时间:400ms
2025-07-31 20:58:32:633 ==>> 【AD_V21电压】通过,【1632mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:32:636 ==>> 检测【关闭仪表供电2】
2025-07-31 20:58:32:640 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:58:32:772 ==>> $GBGGA,125836.571,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,24,,,41,25,,,41,3,,,40,1*46

$GBGSV,6,2,24,60,,,40,59,,,40,39,,,39,40,,,38,1*71

$GBGSV,6,3,24,41,,,38,14,,,38,16,,,37,1,,,36,1*42

$GBGSV,6,4,24,7,,,36,13,,,36,44,,,35,2,,,35,1*75

$GBGSV,6,5,24,38,,,35,34,,,34,23,,,33,4,,,33,1*4B

$GBGSV,6,6,24,5,,,33,6,,,33,10,,,33,11,,,26,1*76

$GBRMC,125836.571,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125836.571,0.000,1508.086,1508.086,48.285,2097152,2097152,2097152*5A



2025-07-31 20:58:32:877 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:58:32:924 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:58:32:927 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:58:32:931 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:58:33:155 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:58:33:226 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:58:33:230 ==>> 检测【打开AccKey2供电】
2025-07-31 20:58:33:247 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:58:33:446 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:58:33:525 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:58:33:529 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:58:33:531 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:33:891 ==>> $GBGGA,125837.551,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,25,,,41,3,,,40,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,40,,,38,1*72

$GBGSV,7,3,26,41,,,38,14,,,38,16,,,37,1,,,36,1*41

$GBGSV,7,4,26,7,,,36,13,,,36,44,,,35,2,,,35,1*76

$GBGSV,7,5,26,38,,,35,6,,,35,34,,,34,23,,,33,1*4C

$GBGSV,7,6,26,4,,,33,5,,,33,10,,,33,9,,,33,1*4A

$GBGSV,7,7,26,26,,,31,11,,,27,1*71

$GBRMC,125837.551,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125837.551,0.000,1497.315,1497.315,47.933,2097152,2097152,2097152*50

[W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:09][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:18:09][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:09][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:09][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:09][COMM]adc read battery ts volt:15 mv
[D][05:18:09][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:09][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2429  volt:3913 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1448  volt:335

2025-07-31 20:58:33:920 ==>> 72 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:58:33:965 ==>>                                          

2025-07-31 20:58:34:073 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33159mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:58:34:077 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:58:34:079 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:58:34:257 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:58:34:368 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:58:34:371 ==>> 该项需要延时执行
2025-07-31 20:58:34:748 ==>> $GBGGA,125838.531,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,25,,,41,3,,,40,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,40,,,38,1*72

$GBGSV,7,3,26,41,,,38,14,,,38,16,,,37,1,,,36,1*41

$GBGSV,7,4,26,7,,,36,13,,,36,44,,,35,2,,,35,1*76

$GBGSV,7,5,26,38,,,35,6,,,35,34,,,34,9,,,34,1*73

$GBGSV,7,6,26,23,,,33,4,,,33,5,,,33,10,,,33,1*72

$GBGSV,7,7,26,26,,,31,11,,,28,1*7E

$GBRMC,125838.531,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125838.531,0.000,1500.498,1500.498,48.029,2097152,2097152,2097152*54



2025-07-31 20:58:35:716 ==>> $GBGGA,125839.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,40,3,,,40,1*44

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,16,,,36,1,,,36,1*4E

$GBGSV,7,4,27,7,,,36,42,,,36,13,,,35,2,,,35,1*71

$GBGSV,7,5,27,38,,,35,6,,,35,9,,,35,44,,,34,1*74

$GBGSV,7,6,27,34,,,34,23,,,33,4,,,33,10,,,33,1*46

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125839.511,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125839.511,0.000,1492.521,1492.521,47.770,2097152,2097152,2097152*53



2025-07-31 20:58:35:960 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:58:36:737 ==>> $GBGGA,125840.511,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,3,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,38,16,,,36,1,,,36,1*41

$GBGSV,7,4,27,7,,,36,42,,,36,6,,,36,13,,,35,1*76

$GBGSV,7,5,27,2,,,35,38,,,35,9,,,35,44,,,34,1*70

$GBGSV,7,6,27,34,,,34,23,,,33,4,,,33,10,,,33,1*46

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125840.511,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125840.511,0.000,1497.131,1497.131,47.920,2097152,2097152,2097152*56



2025-07-31 20:58:37:372 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:58:37:378 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:58:37:383 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:37:758 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3112  volt:5470 mv
[D][05:18:13][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:13][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:13][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:13][COMM]adc read throttle adc:24  volt:31 mv
[D][05:18:13][COMM]adc read battery ts volt:27 mv
[D][05:18:13][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:13][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2453  volt:3952 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1451  volt:33641 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,125841.511,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,25,,,40,3,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,37,14,,,37,16,,,36,1,,,36,1*41

$GBGSV,7,4,27,42,,,36,7,,,35,6,,,35,13,,,35,1*76

$GBGSV,7,5,27,38,,,35

2025-07-31 20:58:37:803 ==>> ,9,,,35,2,,,34,44,,,34,1*71

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,23,,,32,1*47

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125841.511,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125841.511,0.000,1484.844,1484.844,47.525,2097152,2097152,2097152*5E



2025-07-31 20:58:37:927 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【303mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:58:37:930 ==>> 检测【打开AccKey1供电】
2025-07-31 20:58:37:933 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:58:37:970 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:58:38:152 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:58:38:216 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:58:38:221 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:58:38:226 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:58:38:362 ==>> 1A A1 00 40 00 
Get AD_V14 2606mV
OVER 150


2025-07-31 20:58:38:482 ==>> 原始值:【2606】, 乘以分压基数【2】还原值:【5212】
2025-07-31 20:58:38:514 ==>> 【读取AccKey1电压(ADV14)前】通过,【5212mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:58:38:520 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:58:38:522 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:38:888 ==>> $GBGGA,125842.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,40,3,,,40,1*44

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,37,14,,,37,42,,,37,16,,,36,1*77

$GBGSV,7,4,27,1,,,36,7,,,36,6,,,36,13,,,35,1*41

$GBGSV,7,5,27,38,,,35,9,,,35,2,,,35,44,,,34,1*70

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,23,,,32,1*47

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125842.511,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125842.511,0.000,1492.523,1492.523,47.771,2097152,2097152,2097152*5E

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:14][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:14][COMM]adc read left brake adc:15  volt:19 mv
[D][05:18:14][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read throttle adc:18  volt:23 mv
[D][05:18:14][COMM]adc read battery ts volt:13 mv
[D][05:18:14][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:14][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2478  volt:3992 mv
[D][05:18:14][COMM]arm_

2025-07-31 20:58:38:933 ==>> hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:58:39:062 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5484mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:58:39:069 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:58:39:090 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:58:39:254 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:58:39:344 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:58:39:348 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:58:39:351 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:58:39:463 ==>> 1A A1 00 40 00 
Get AD_V14 2611mV
OVER 150


2025-07-31 20:58:39:599 ==>> 原始值:【2611】, 乘以分压基数【2】还原值:【5222】
2025-07-31 20:58:39:627 ==>> 【读取AccKey1电压(ADV14)后】通过,【5222mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:58:39:631 ==>> 检测【打开WIFI(2)】
2025-07-31 20:58:39:633 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:58:39:736 ==>> $GBGGA,125843.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,40,3,,,40,1*44

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,16,,,36,1*78

$GBGSV,7,4,27,1,,,36,6,,,36,9,,,36,7,,,35,1*7A

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,23,,,32,1*47

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125843.511,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125843.511,0.000,1494.059,1494.059,47.821,2097152,2097152,2097152*55



2025-07-31 20:58:39:841 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WI

2025-07-31 20:58:39:886 ==>> FISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:58:39:991 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:58:40:126 ==>> [D][05:18:15][COMM]IMU: [2,-9,-936] ret=21 AWAKE!


2025-07-31 20:58:40:171 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:58:40:177 ==>> 检测【转刹把供电】
2025-07-31 20:58:40:182 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:58:40:351 ==>> [D][05:18:15][COMM]IMU: [20,19,-1010] ret=24 AWAKE!
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:58:40:461 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:58:40:465 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:58:40:469 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:58:40:562 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:58:40:742 ==>> 1A A1 00 80 00 
Get AD_V15 2392mV
OVER 150
+WIFISCAN:4,0,CC057790A621,-50
+WIFISCAN:4,1,CC057790A620,-52
+WIFISCAN:4,2,44A1917CAD81,-82
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:16][CAT1]wifi scan report total[4]
$GBGGA,125844.511,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,3,,,41,1*44

$GBGSV,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,1,,,37,1*4F

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,36,9,,,35,1*4C

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,23,,,32,1*47

$GBGSV,7,7,27,5,,,32,26,,,31,11,,,28,1*4B

$GBRMC,125844.511,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125844.511,0.000,1498.670,1498.670,47.973,2097152,2097152,2097152*54

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:58:40:892 ==>> 原始值:【2392】, 乘以分压基数【2】还原值:【4784】
2025-07-31 20:58:40:936 ==>> 【读取AD_V15电压(前)】通过,【4784mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:58:40:939 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:58:40:955 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:58:41:047 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:58:41:124 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:58:41:169 ==>> 1A A1 01 00 00 
Get AD_V16 2419mV
OVER 150


2025-07-31 20:58:41:199 ==>> 原始值:【2419】, 乘以分压基数【2】还原值:【4838】
2025-07-31 20:58:41:235 ==>> 【读取AD_V16电压(前)】通过,【4838mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:58:41:239 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:58:41:244 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:41:264 ==>> [D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 20:58:41:589 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:18:16][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:16][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:16][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:16][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:16][COMM]adc read battery ts volt:15 mv
[D][05:18:16][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3072  volt:5400 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2494  volt:4018 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:58:41:694 ==>>                                                                                                                                       V,7,2,27,60,,,40,59,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,1,,,36,1*4E

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,36,9,,,36,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,23,,,32,1*47

$GBGSV,7,7,27,5,,,32,26,,,32,1

2025-07-31 20:58:41:740 ==>> 1,,,28,1*48

$GBRMC,125845.511,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125845.511,0.000,1498.665,1498.665,47.968,2097152,2097152,2097152*5F



2025-07-31 20:58:41:786 ==>> 【转刹把供电电压(主控ADC)】通过,【5400mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:58:41:792 ==>> 检测【转刹把供电电压】
2025-07-31 20:58:41:796 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:42:085 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:17][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:17][COMM]adc read left brake adc:22  volt:29 mv
[D][05:18:17][COMM]adc read right brake adc:19  volt:25 mv
[D][05:18:17][COMM]adc read throttle adc:19  volt:25 mv
[D][05:18:17][COMM]adc read battery ts volt:19 mv
[D][05:18:17][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3080  volt:5414 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2435  volt:3923 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]read battery soc:255
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:58:42:338 ==>> 【转刹把供电电压】通过,【5414mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:58:42:343 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:58:42:348 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:42:543 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:58:42:632 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:58:42:642 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:58:42:653 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:42:738 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:58:42:744 ==>> $GBGGA,125846.511,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,3,,,40,25,,,40,1*44

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,38,1*7D

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,1,,,36,1*4E

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,36,9,,,35,1*4C

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,5,,,33,1*72

$GBGSV,7,7,27,23,,,32,26,,,32,11,,,28,1*7C

$GBRMC,125846.511,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125846.511,0.000,1495.588,1495.588,47.863,2097152,2097152,2097152*56



2025-07-31 20:58:42:828 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:58:42:843 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:42:858 ==>> 00 00 00 00 00 
head err!


2025-07-31 20:58:42:948 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:58:43:054 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:43:069 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:58:43:159 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:58:43:265 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:58:43:295 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:58:43:300 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:58:43:329 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:58:43:400 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:58:43:430 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:58:43:460 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:58:43:535 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:58:43:538 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:58:43:543 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:58:43:657 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:58:43:732 ==>> $GBGGA,125847.511,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,41,3,,,41,25,,,41,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,40,,,38,1*7D

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,1,,,36,1*4E

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,35,9,,,35,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,5,,,32,1*73

$GBGSV,7,7,27,23,,,32,26,,,32,11,,,28,1*7C

$GBRMC,125847.511,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125847.511,0.000,1497.134,1497.134,47.923,2097152,2097152,2097152*52



2025-07-31 20:58:43:823 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:58:43:827 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:58:43:832 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:58:43:990 ==>> 3A A3 04 01 A3 
[D][05:18:19][COMM]read battery soc:255


2025-07-31 20:58:44:065 ==>> ON_OUT4
OVER 150


2025-07-31 20:58:44:113 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:58:44:116 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:58:44:121 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:58:44:170 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:58:44:396 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:58:44:400 ==>> 检测【左刹电压测试1】
2025-07-31 20:58:44:403 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:44:769 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3123  volt:5489 mv
[D][05:18:20][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:20][COMM]adc read left brake adc:1731  volt:2282 mv
[D][05:18:20][COMM]adc read right brake adc:1737  volt:2289 mv
[D][05:18:20][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:20][COMM]adc read battery ts volt:25 mv
[D][05:18:20][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:20][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2498  volt:4025 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,125848.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,3,,,40,25,,,40,1*44

$GBGSV,7,2,27,59,,,40,60,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,38,42,,,37,1,,,36,1*41

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,36,9,,,36,1*4F

$GBGSV,7,5,27,13,,,36,38,,,35,2,,,35,44,,,34,1*48

$GBGSV,7,6,27,34,,,34,4,,,33,10,,,33,5,,,32,1*73

$GBGSV,7,7,27,23,,,32,26,,,

2025-07-31 20:58:44:815 ==>> 32,11,,,28,1*7C

$GBRMC,125848.511,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125848.511,0.000,1500.198,1500.198,48.014,2097152,2097152,2097152*5F



2025-07-31 20:58:44:936 ==>> 【左刹电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 20:58:44:940 ==>> 检测【右刹电压测试1】
2025-07-31 20:58:44:973 ==>> 【右刹电压测试1】通过,【2289】符合目标值【2250】至【2500】要求!
2025-07-31 20:58:44:976 ==>> 检测【转把电压测试1】
2025-07-31 20:58:45:006 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 20:58:45:009 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:58:45:013 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:58:45:059 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:58:45:285 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:58:45:290 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:58:45:294 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:58:45:362 ==>> 3A A3 04 00 A3 


2025-07-31 20:58:45:467 ==>> OFF_OUT4
OVER 150


2025-07-31 20:58:45:574 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:58:45:578 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:58:45:583 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:58:45:726 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150
$GBGGA,125849.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,3,,,40,25,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,27,41,,,38,14,,,37,42,,,37,1,,,36,1*4E

$GBGSV,7,4,27,16,,,36,6,,,36,7,,,36,9,,,36,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,10,,,33,4,,,32,5,,,32,1*72

$GBGSV,7,7,27,23,,,32,26,,,32,11,,,28,1*7C

$GBRMC,125849.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125849.511,0.000,1494.055,1494.055,47.818,2097152,2097152,2097152*55



2025-07-31 20:58:45:862 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:58:45:869 ==>> 检测【左刹电压测试2】
2025-07-31 20:58:45:894 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:58:46:179 ==>> [D][05:18:21][COMM]read battery soc:255
[W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3110  volt:5466 mv
[D][05:18:21][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:21][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:21][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:21][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:21][COMM]adc read battery ts volt:21 mv
[D][05:18:21][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:21][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:15  volt:12 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2494  volt:4018 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:58:46:400 ==>> 【左刹电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 20:58:46:404 ==>> 检测【右刹电压测试2】
2025-07-31 20:58:46:425 ==>> 【右刹电压测试2】通过,【19】符合目标值【0】至【50】要求!
2025-07-31 20:58:46:429 ==>> 检测【转把电压测试2】
2025-07-31 20:58:46:452 ==>> 【转把电压测试2】通过,【14】符合目标值【0】至【50】要求!
2025-07-31 20:58:46:455 ==>> 检测【晶振检测】
2025-07-31 20:58:46:460 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:58:46:756 ==>> $GBGGA,125850.511,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,3,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,39,39,,,39,40,,,38,1*72

$GBGSV,7,3,28,41,,,37,14,,,37,42,,,37,1,,,36,1*4E

$GBGSV,7,4,28,16,,,36,6,,,36,7,,,36,9,,,35,1*43

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,32,5,,,32,1*7D

$GBGSV,7,7,28,23,,,32,26,,,32,12,,,29,11,,,28,1*7B

$GBRMC,125850.511,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125850.511,0.000,1480.685,1480.685,47.401,2097152,2097152,2097152*59

[D][05:18:22][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 20:58:46:994 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:58:46:999 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:58:47:002 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:58:47:072 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1645mV
Get AD_V4 1646mV
Get AD_V5 2755mV
Get AD_V6 2022mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:58:47:284 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:58:47:288 ==>> 检测【检测BootVer】
2025-07-31 20:58:47:292 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:58:47:620 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071539001
[D][05:18:22][FCTY]HardwareID  = 867222088007986
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:18:22][FCTY]Bat         = 4064 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1290
[D][05:18:22][FCTY]Acckey1 vol = 5475 mv, Acckey2 vol = 75 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PL

2025-07-31 20:58:47:725 ==>> ATFORM = C4
[D][05:18:22][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3846 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 20:58:47:858 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:58:47:863 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:58:47:869 ==>> 检测【检测固件版本】
2025-07-31 20:58:47:907 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:58:47:912 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:58:47:915 ==>> 检测【检测蓝牙版本】
2025-07-31 20:58:47:957 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:58:47:961 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:58:47:965 ==>> 检测【检测MoBikeId】
2025-07-31 20:58:47:997 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:58:48:008 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:58:48:011 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:58:48:018 ==>> 检测【检测蓝牙地址】
2025-07-31 20:58:48:024 ==>> 取到目标值:E389E4EC37FD
2025-07-31 20:58:48:053 ==>> 【检测蓝牙地址】通过,【E389E4EC37FD】符合目标值【】要求!
2025-07-31 20:58:48:056 ==>> 提取到蓝牙地址:E389E4EC37FD
2025-07-31 20:58:48:060 ==>> 检测【BOARD_ID】
2025-07-31 20:58:48:103 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:58:48:107 ==>> 检测【检测充电电压】
2025-07-31 20:58:48:153 ==>> 【检测充电电压】通过,【4064mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:58:48:158 ==>> 检测【检测VBUS电压1】
2025-07-31 20:58:48:203 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:58:48:208 ==>> 检测【检测充电电流】
2025-07-31 20:58:48:249 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:58:48:253 ==>> 检测【检测IMEI】
2025-07-31 20:58:48:256 ==>> 取到目标值:867222088007986
2025-07-31 20:58:48:296 ==>> 【检测IMEI】通过,【867222088007986】符合目标值【】要求!
2025-07-31 20:58:48:300 ==>> 提取到IMEI:867222088007986
2025-07-31 20:58:48:307 ==>> 检测【检测IMSI】
2025-07-31 20:58:48:325 ==>> 取到目标值:460130071539001
2025-07-31 20:58:48:454 ==>> 【检测IMSI】通过,【460130071539001】符合目标值【】要求!
2025-07-31 20:58:48:458 ==>> 提取到IMSI:460130071539001
2025-07-31 20:58:48:462 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:58:48:466 ==>> 取到目标值:460130071539001
2025-07-31 20:58:48:505 ==>> 【校验网络运营商(移动)】通过,【460130071539001】符合目标值【】要求!
2025-07-31 20:58:48:509 ==>> 检测【打开CAN通信】
2025-07-31 20:58:48:512 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:58:48:565 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:58:48:670 ==>> $GBGGA,125852.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,

2025-07-31 20:58:48:730 ==>> 40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,37,42,,,37,1,,,36,1*41

$GBGSV,7,4,28,16,,,36,7,,,36,6,,,35,9,,,35,1*40

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,32,5,,,32,1*7D

$GBGSV,7,7,28,23,,,32,26,,,31,12,,,30,11,,,28,1*70

$GBRMC,125852.511,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125852.511,0.000,1482.168,1482.168,47.450,2097152,2097152,2097152*5F



2025-07-31 20:58:48:812 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:58:48:817 ==>> 检测【检测CAN通信】
2025-07-31 20:58:48:823 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:58:48:880 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:48:940 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35458
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:49:000 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:49:060 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:49:120 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:49:134 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:58:49:139 ==>> 检测【关闭CAN通信】
2025-07-31 20:58:49:146 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:58:49:180 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:58:49:271 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:58:49:416 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:58:49:421 ==>> 检测【打印IMU STATE】
2025-07-31 20:58:49:425 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:58:49:772 ==>> $GBGGA,125853.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,37,42,,,37,1,,,36,1*41

$GBGSV,7,4,28,16,,,36,7,,,36,6,,,36,9,,,36,1*40

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,5,,,32,1*7C

$GBGSV,7,7,28,23,,,32,26,,,31,12,,,30,11,,,28,1*70

$GBRMC,125853.511,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125853.511,0.000,1486.608,1486.608,47.589,2097152,2097152,2097152*5B

[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:58:49:967 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:58:49:972 ==>> 检测【六轴自检】
2025-07-31 20:58:49:978 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:58:50:002 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 20:58:50:153 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:58:50:728 ==>> $GBGGA,125854.511,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,3,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,37,42,,,37,16,,,37,1*76

$GBGSV,7,4,28,1,,,36,7,,,36,6,,,36,9,,,35,1*75

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,5,,,32,1*7C

$GBGSV,7,7,28,23,,,32,26,,,31,12,,,30,11,,,29,1*71

$GBRMC,125854.511,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125854.511,0.000,1488.085,1488.085,47.633,2097152,2097152,2097152*5E



2025-07-31 20:58:51:733 ==>> $GBGGA,125855.511,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,37,42,,,37,1,,,37,1*40

$GBGSV,7,4,28,16,,,36,7,,,36,6,,,36,9,,,35,1*43

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,23,,,33,1*49

$GBGSV,7,7,28,5,,,32,26,,,31,12,,,30,11,,,29,1*45

$GBRMC,125855.511,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125855.511,0.000,1489.563,1489.563,47.679,2097152,2097152,2097152*51



2025-07-31 20:58:51:838 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:58:52:078 ==>> [D][05:18:27][COMM]read battery soc:255
[D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38584 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-19,-16,4056]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:58:52:321 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:58:52:329 ==>> 检测【打印IMU STATE2】
2025-07-31 20:58:52:335 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:58:52:557 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:1
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:58:52:600 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:58:52:607 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:58:52:613 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:58:52:662 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150
$GBGGA,125856.511,,,,,0,00,,,M,,M,,*62

$GBG

2025-07-31 20:58:52:737 ==>> SA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,25,,,40,1*4A

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,37,42,,,37,1,,,36,1*41

$GBGSV,7,4,28,16,,,36,7,,,36,6,,,36,9,,,36,1*40

$GBGSV,7,5,28,13,,,35,38,,,35,2,,,35,44,,,34,1*44

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,23,,,33,1*49

$GBGSV,7,7,28,5,,,32,26,,,31,12,,,30,11,,,29,1*45

$GBRMC,125856.511,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125856.511,0.000,1488.080,1488.080,47.628,2097152,2097152,2097152*56



2025-07-31 20:58:52:827 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  l retry i = 8,volt = 11


2025-07-31 20:58:52:886 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:58:52:891 ==>> 检测【检测VBUS电压2】
2025-07-31 20:58:52:898 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:58:53:247 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539001
[D][05:18:28][FCTY]HardwareID  = 867222088007986
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:18:28][FCTY]Bat         = 4044 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 7, adc = 312
[D][05:18:28][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 202 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM 

2025-07-31 20:58:53:292 ==>> = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3846 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:58:53:434 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:58:53:882 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539001
[D][05:18:29][FCTY]HardwareID  = 867222088007986
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
$GBGGA,125857.511,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,3,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,38,42,,,37,16,,,37,1*79

$GBGSV,7,4,28,1,,,36,7,,,36,6,,,36,9,,,36,1*76

$GBGSV,7,5,28,13,,,36,38,,,35,2,,,35,44,,,34,1*47

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,23,,,33,1*49

$GBGSV,7,7,28,5,,,32,26,,,31,12,,,31,11,,,29,1*44

$GBRMC,125857.5

2025-07-31 20:58:53:972 ==>> 11,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125857.511,0.000,1495.483,1495.483,47.865,2097152,2097152,2097152*50

[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 171
[D][05:18:29][FCTY]Acckey1 vol = 5472 mv, Acckey2 vol = 202 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3846 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:58:54:017 ==>>                                                                                                                                                                           

2025-07-31 20:58:54:261 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:58:55:108 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539001
[D][05:18:29][FCTY]HardwareID  = 867222088007986
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:18:29][FCTY]Bat         = 3844 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 8200 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 145
[D][05:18:29][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 25 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat

2025-07-31 20:58:55:213 ==>> 1         = 3846 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 
$GBGGA,125858.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,3,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,40,,,38,1*7C

$GBGSV,7,3,28,41,,,38,14,,,38,42,,,37,16,,,37,1*79

$GBGSV,7,4,28,1,,,36,7,,,36,6,,,36,9,,,36,1*76

$GBGSV,7,5,28,13,,,36,38,,,35,2,,,35,44,,,34,1*47

$GBGSV,7,6,28,34,,,34,10,,,33,4,,,33,23,,,33,1*49

$GBGSV,7,7,28,5,,,32,26,,,32,12,,,31,11,,,29,1*47

$GBRMC,125858.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125858.511,0.000,1496.961,1496.961,47.910,2097152,2097152,2097152*5C

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_

2025-07-31 20:58:55:318 ==>> helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending tracei

2025-07-31 20:58:55:328 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:58:55:423 ==>> d[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:18:30][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEN

2025-07-31 20:58:55:528 ==>> D socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B57C79F42F2D9DA13C3F69076468541984F07B7E3DFA7C70E2CAE7BCE15DA27B8567212028C15AFCCA3713C4ED9CD8FE1E22244502614ECB0DD2C847B46F6F5E5063E0323954D995676F1D8E3BA2A858D36585
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]


2025-07-31 20:58:55:573 ==>>                                                                                                                                            

2025-07-31 20:58:55:829 ==>>                      >>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539001
[D][05:18:31][FCTY]HardwareID  = 867222088007986
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:18:31][FCTY]Bat         = 3844 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 110
[D][05:18:31][FCTY]Acckey1 vol = 5479 mv, Acckey2 vol = 50 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3846 

2025-07-31 20:58:55:859 ==>> mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:58:55:886 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:58:55:892 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:58:55:899 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:58:55:965 ==>> 5A A5 01 5A A5 


2025-07-31 20:58:56:070 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:58:56:145 ==>> [D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 23
[D][05:18:31][COMM]read battery soc:255


2025-07-31 20:58:56:185 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:58:56:195 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:58:56:204 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:58:56:250 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:58:56:484 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:58:56:491 ==>> 检测【打开WIFI(3)】
2025-07-31 20:58:56:500 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:58:56:685 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:58:56:782 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:58:56:787 ==>> 检测【扩展芯片hw】
2025-07-31 20:58:56:795 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:58:56:973 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:58:57:063 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:58:57:068 ==>> 检测【扩展芯片boot】
2025-07-31 20:58:57:096 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:58:57:101 ==>> 检测【扩展芯片sw】
2025-07-31 20:58:57:126 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:58:57:132 ==>> 检测【检测音频FLASH】
2025-07-31 20:58:57:139 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:58:57:346 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:58:57:622 ==>> +WIFISCAN:4,0,CC057790A621,-54
+WIFISCAN:4,1,F42A7D1297A3,-67
+WIFISCAN:4,2,CC057790A5C0,-70
+WIFISCAN:4,3,44A1917CAD80,-77

[D][05:18:33][CAT1]wifi scan report total[4]


2025-07-31 20:58:57:727 ==>> [D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:58:58:441 ==>>                                                                                                          value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]more than the number of battery plugs
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:33][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:33][COMM]Bat auth off fail, error:-1
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18

2025-07-31 20:58:58:546 ==>> :33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get soc error
[E][05:18:33][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]index:1
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:5
[D][0

2025-07-31 20:58:58:651 ==>> 5:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][COMM]Receive Bat Lock cmd 0
[D][05:18:33][COMM]VBUS is 1
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][COMM]Main Task receive event:61
[D][05:18:33][COMM][D301]:type:3, trace id:280
[D][05:18:33][COMM]id[], hw[000
[D][05:18:33][COMM]get mcMaincircuitVolt error
[D][05:18:33][COMM]get mcSubcircuitVolt error
[D][05:18:33][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:33][COMM]BA

2025-07-31 20:58:58:756 ==>> T CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get bat work state err
[D][05:18:33][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:33][PROT]remove success[1629955113],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:33][COMM]Main Task receive event:61 finished processing
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_st

2025-07-31 20:58:58:861 ==>> art].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, 

2025-07-31 20:58:58:951 ==>> len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]read battery soc:255
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:33][GNSS]recv submsg id[3]


2025-07-31 20:59:00:285 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3D0377EEA69B076834928A5B65AB15B1AA42B1DA

2025-07-31 20:59:00:360 ==>> FCFBB0C5D9F6DA06FA7B116D0E235B366541B9AE7C4AFB0CF9EE72444F1EFC708A65EB389A493DA87BF761A57BE6582EF3222569D76991E79E06CB60E57DC
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]
[D][05:18:35][COMM]read battery soc:255


2025-07-31 20:59:00:769 ==>> [D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:59:01:466 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 20:59:01:766 ==>> [D][05:18:37][COMM]48293 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:59:01:871 ==>> [D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end

2025-07-31 20:59:01:931 ==>> ].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:37][COMM]accel parse set 0
[D][05:18:37][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:59:02:158 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:59:02:185 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:59:02:191 ==>> 检测【打开喇叭声音】
2025-07-31 20:59:02:197 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:59:02:887 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:37][COMM]read file, len:15228, num:4
[D][05:18:37][COMM]--->crc16:0x419c
[D][05:18:37][COMM]read file success
[W][05:18:37][COMM][Audio].l:[936].close hexlog save
[D][05:18:37][COMM]accel parse set 1
[D][05:18:37][COMM][Audio]mon:9,05:18:37
[D][05:18:37][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[704].a

2025-07-31 20:59:02:988 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:59:02:995 ==>> 检测【打开大灯控制】
2025-07-31 20:59:03:004 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:59:03:028 ==>> udio cmd send:AT+AUDIOSEND=1

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:37][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D

2025-07-31 20:59:03:097 ==>> ][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]49303 imu init OK
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:59:03:172 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:59:03:271 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:59:03:278 ==>> 检测【关闭仪表供电3】
2025-07-31 20:59:03:285 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:59:03:457 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 20:59:03:559 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:59:03:566 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:59:03:574 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:59:03:731 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:59:03:856 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:59:03:863 ==>> 检测【读大灯电压】
2025-07-31 20:59:03:868 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:59:04:046 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[33247]


2025-07-31 20:59:04:151 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 20:59:04:164 ==>> 【读大灯电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:59:04:170 ==>> 检测【关闭大灯控制2】
2025-07-31 20:59:04:184 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:59:04:346 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:59:04:451 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:59:04:457 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:59:04:465 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:59:04:647 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:59:04:735 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:59:04:741 ==>> 检测【打开WIFI(4)】
2025-07-31 20:59:04:749 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:59:04:979 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:40][CAT1]<<< 
OK

[D][05:18:40][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:59:05:066 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:59:05:075 ==>> 检测【EC800M模组版本】
2025-07-31 20:59:05:101 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:59:05:440 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:40][CAT1]gsm read msg sub id: 12
[D][05:18:40][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:40][PROT]CLEAN,SEND:1
[D][05:18:40][PROT]index:1 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:2
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEN

2025-07-31 20:59:05:470 ==>> D_ACK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:59:05:575 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:59:05:650 ==>>                                                                                                                                                                                     8:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 20:59:06:095 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:59:06:337 ==>> [D][05:18:41][COMM]read battery soc:255
[W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:41][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:59:06:378 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:59:06:385 ==>> 检测【配置蓝牙地址】
2025-07-31 20:59:06:393 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:59:06:418 ==>>                                                                                                   

2025-07-31 20:59:06:532 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:59:06:592 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E389E4EC37FD>】
2025-07-31 20:59:06:772 ==>> recv ble 1
recv ble 2
ble set mac ok :e3,89,e4,ec,37,fd
enable filters ret : 0

2025-07-31 20:59:06:877 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:59:06:882 ==>> 检测【BLETEST】
2025-07-31 20:59:06:890 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:59:06:954 ==>> 4A A4 01 A4 4A 


2025-07-31 20:59:07:059 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E389E4EC37FD*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E389E4EC37FD99999OVER 150


2025-07-31 20:59:07:384 ==>> [D][05:18:42][COMM]53912 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:59:07:935 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:59:07:941 ==>> 该项需要延时执行
2025-07-31 20:59:08:002 ==>> [D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:59:08:182 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:59:08:377 ==>> [D][05:18:43][COMM]54923 imu init OK


2025-07-31 20:59:09:901 ==>> +WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,44A1917CAD80,-77
+WIFISCAN:4,2,44A1917CAD81,-77
+WIFISCAN:4,3,CC057790A5C1,-82

[D][05:18:45][CAT1]wifi scan report total[4]


2025-07-31 20:59:10:207 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:59:10:387 ==>> [D][05:18:45][GNSS]recv submsg id[3]


2025-07-31 20:59:10:925 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3E4F6F817EFCC0ADCAFF6CF38EF3E917

2025-07-31 20:59:11:000 ==>> 4823E41DD5D5FEFAC1FD1EF1AEB570C95318B5292C49ED9D38E436D0DB68361753FBDF26A4406672731C076D067B613F91FE28B0EE53FE9746E421728F90B7D177B8A
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 20:59:12:207 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:59:14:198 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:59:16:166 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5096CEE91F1AA3

2025-07-31 20:59:16:271 ==>> E9DF84E88AEBF1A882CEFBA8B869F8542987F71B892B8A81446B31051A350E336A686DB87C079C95547C70B5D611C2C8411CB3F1612796699CF7C6B2427D50669D997C7E4D3D03EE6601049
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
                                         

2025-07-31 20:59:17:943 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:59:17:949 ==>> 检测【检测WiFi结果】
2025-07-31 20:59:17:958 ==>> WiFi信号:【CC057790A621】,信号值:-50
2025-07-31 20:59:17:967 ==>> WiFi信号:【CC057790A620】,信号值:-52
2025-07-31 20:59:17:993 ==>> WiFi信号:【44A1917CAD81】,信号值:-82
2025-07-31 20:59:17:998 ==>> WiFi信号:【F86FB0660A82】,信号值:-84
2025-07-31 20:59:18:007 ==>> WiFi信号:【F42A7D1297A3】,信号值:-67
2025-07-31 20:59:18:034 ==>> WiFi信号:【CC057790A5C0】,信号值:-70
2025-07-31 20:59:18:042 ==>> WiFi信号:【44A1917CAD80】,信号值:-77
2025-07-31 20:59:18:051 ==>> WiFi信号:【CC057790A5C1】,信号值:-82
2025-07-31 20:59:18:069 ==>> WiFi数量【8】, 最大信号值:-50
2025-07-31 20:59:18:079 ==>> 检测【检测GPS结果】
2025-07-31 20:59:18:100 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:59:18:154 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all continue location stop
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all sing location stop


2025-07-31 20:59:18:229 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:59:18:944 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:59:18:953 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:18:962 ==>> 定位已等待【1】秒.
2025-07-31 20:59:19:361 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:54][COMM]Open GPS Module...
[D][05:18:54][COMM]LOC_MODEL_CONT
[D][05:18:54][GNSS]start event:8
[D][05:18:54][GNSS]GPS start. ret=0
[D][05:18:54][CAT1]gsm read msg sub id: 23
[W][05:18:54][GNSS]start cont locating
[D][05:18:54][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:54][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:59:19:951 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:19:961 ==>> 定位已等待【2】秒.
2025-07-31 20:59:20:057 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:59:20:239 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:59:20:727 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:59:20:937 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,41,25,,,40,24,,,40,39,,,39,1*76

$GBGSV,2,2,07,41,,,38,42,,,37,14,,,38,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1623.702,1623.702,51.862,2097152,2097152,2097152*47

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 23, ret: 6
[D][05:18:56][CAT1]sub id: 23, ret: 6



2025-07-31 20:59:20:952 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:20:962 ==>> 定位已等待【3】秒.
2025-07-31 20:59:21:383 ==>> [D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e20] format[0]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]Send Data To Server[198][198] ... ->:
0063B98E113311331133113311331B88B5B

2025-07-31 20:59:21:457 ==>> F06501C0274028F8526FBC7DEC1141A2DDA10C0147708F79344BCE91873C507555273C157B6E3D414B65E057DF7272C3A82D60D3E5C88FDA1307D36CBF6ADED512709010DC92AFB11B2ADD68E4C9C435982
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 20:59:21:488 ==>>                                                                                             

2025-07-31 20:59:21:848 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,33,,,42,24,,,41,25,,,40,60,,,40,1*73

$GBGSV,3,2,12,59,,,40,39,,,39,41,,,38,14,,,37,1*73

$GBGSV,3,3,12,42,,,37,7,,,34,1,,,41,2,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.546,1608.546,51.421,2097152,2097152,2097152*4C



2025-07-31 20:59:21:953 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:21:963 ==>> 定位已等待【4】秒.
2025-07-31 20:59:22:241 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:59:22:896 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,3,,,41,24,,,40,25,,,40,1*4A

$GBGSV,5,2,18,60,,,40,59,,,40,39,,,39,41,,,38,1*7C

$GBGSV,5,3,18,40,,,38,14,,,37,42,,,37,2,,,35,1*41

$GBGSV,5,4,18,44,,,35,7,,,35,38,,,34,5,,,33,1*70

$GBGSV,5,5,18,4,,,33,1,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1553.462,1553.462,49.691,2097152,2097152,2097152*4C



2025-07-31 20:59:22:956 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:22:965 ==>> 定位已等待【5】秒.
2025-07-31 20:59:23:927 ==>> $GBGGA,125927.716,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,3,,,41,24,,,40,25,,,40,1*40

$GBGSV,6,2,22,60,,,40,59,,,40,39,,,39,41,,,37,1*79

$GBGSV,6,3,22,40,,,37,14,,,37,42,,,37,16,,,37,1*73

$GBGSV,6,4,22,2,,,35,44,,,35,7,,,35,38,,,35,1*7A

$GBGSV,6,5,22,5,,,33,4,,,33,6,,,32,23,,,31,1*40

$GBGSV,6,6,22,1,,,39,11,,,38,1*46

$GBRMC,125927.716,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125927.716,0.000,1525.677,1525.677,48.819,2097152,2097152,2097152*57



2025-07-31 20:59:23:957 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:23:966 ==>> 定位已等待【6】秒.
2025-07-31 20:59:24:247 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:59:24:711 ==>> $GBGGA,125928.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,41,3,,,41,24,,,40,25,,,40,1*42

$GBGSV,6,2,23,60,,,40,59,,,40,39,,,39,40,,,38,1*76

$GBGSV,6,3,23,41,,,37,14,,,37,42,,,37,1,,,36,1*44

$GBGSV,6,4,23,16,,,36,2,,,35,7,,,35,38,,,35,1*7F

$GBGSV,6,5,23,44,,,34,9,,,34,10,,,34,6,,,33,1*7D

$GBGSV,6,6,23,5,,,32,4,,,32,23,,,32,1*76

$GBRMC,125928.516,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125928.516,0.000,1510.529,1510.529,48.328,2097152,2097152,2097152*53



2025-07-31 20:59:24:966 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:24:975 ==>> 定位已等待【7】秒.
2025-07-31 20:59:25:705 ==>> $GBGGA,125929.516,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,41,24,,,41,25,,,40,1*47

$GBGSV,6,2,24,60,,,40,59,,,40,39,,,39,40,,,38,1*71

$GBGSV,6,3,24,41,,,37,14,,,37,42,,,37,1,,,37,1*42

$GBGSV,6,4,24,16,,,36,2,,,35,7,,,35,38,,,35,1*78

$GBGSV,6,5,24,44,,,35,9,,,35,6,,,34,10,,,33,1*7A

$GBGSV,6,6,24,5,,,32,4,,,32,23,,,32,12,,,36,1*77

$GBRMC,125929.516,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125929.516,0.000,1519.545,1519.545,48.619,2097152,2097152,2097152*55



2025-07-31 20:59:25:977 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:25:987 ==>> 定位已等待【8】秒.
2025-07-31 20:59:26:237 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 20:59:26:618 ==>> [D][05:19:01][PROT]CLEAN,SEND:0
[D][05:19:01][PROT]CLEAN:0
[D][05:19:01][PROT]index:2 1629955141
[D][05:19:01][PROT]is_send:0
[D][05:19:01][PROT]sequence_num:6
[D][05:19:01][PROT]retry_timeout:0
[D][05:19:01][PROT]retry_times:3
[D][05:19:01][PROT]send_path:0x2
[D][05:19:01][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:01][PROT]===========================================================
[D][05:19:01][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:19:01][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955141]
[D][05:19:01][PROT]===========================================================
[D][05:19:01][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:01][PROT]sending traceid [9999999999900007]
[D][05:19:01][PROT]Send_TO_M2M [1629955141]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:01][SAL ]sock send credit cnt[6]
[D][05:19:01][SAL ]sock send ind credit cnt[6]
[D][05:19:01][M2M ]m2m send data len[134]
[D][05:19:01][SAL ]Cellular task submsg id[10]
[D][05:19:01][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0

2025-07-31 20:59:26:723 ==>> ]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:01][CAT1]gsm read msg sub id: 15
[D][05:19:01][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:01][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BED69179D7F00A380B5C44BE7A05A0628860083693F617E0DA80C4E8F3EFCEF12BEE748FD70C06D3F2ED6F7D89B527CC867F16
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]
                                                                                                                                                                                                                                                                                                    

2025-07-31 20:59:26:768 ==>>                                                                                                                                                                                                                                                                

2025-07-31 20:59:26:981 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:26:990 ==>> 定位已等待【9】秒.
2025-07-31 20:59:27:754 ==>> $GBGGA,125931.516,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,24,,,41,3,,,40,25,,,40,1*41

$GBGSV,6,2,23,60,,,40,59,,,40,39,,,39,40,,,38,1*76

$GBGSV,6,3,23,41,,,38,14,,,37,42,,,37,16,,,36,1*7D

$GBGSV,6,4,23,1,,,36,7,,,36,2,,,35,38,,,35,1*4A

$GBGSV,6,5,23,9,,,35,6,,,35,44,,,34,10,,,33,1*7D

$GBGSV,6,6,23,23,,,33,5,,,32,4,,,32,1*77

$GBRMC,125931.516,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125931.516,0.000,759.249,759.249,694.351,2097152,2097152,2097152*62



2025-07-31 20:59:27:982 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:27:992 ==>> 定位已等待【10】秒.
2025-07-31 20:59:28:257 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 20:59:28:732 ==>> $GBGGA,125932.516,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,41,60,,,40,3,,,40,1*47

$GBGSV,7,2,25,59,,,40,25,,,40,39,,,39,40,,,38,1*70

$GBGSV,7,3,25,41,,,38,42,,,37,14,,,37,7,,,36,1*4A

$GBGSV,7,4,25,16,,,36,6,,,36,1,,,36,2,,,35,1*43

$GBGSV,7,5,25,9,,,35,38,,,35,44,,,35,34,,,34,1*47

$GBGSV,7,6,25,10,,,33,8,,,33,23,,,33,5,,,32,1*7C

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,125932.516,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125932.516,0.000,755.614,755.614,691.027,2097152,2097152,2097152*66



2025-07-31 20:59:28:990 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:29:002 ==>> 定位已等待【11】秒.
2025-07-31 20:59:29:736 ==>> $GBGGA,125933.516,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,25,,,41,3,,,40,60,,,40,1*46

$GBGSV,7,2,25,59,,,40,24,,,40,39,,,39,40,,,38,1*71

$GBGSV,7,3,25,41,,,38,16,,,37,42,,,37,14,,,37,1*7B

$GBGSV,7,4,25,7,,,36,9,,,36,1,,,36,6,,,36,1*7B

$GBGSV,7,5,25,2,,,35,38,,,35,44,,,35,34,,,34,1*4C

$GBGSV,7,6,25,10,,,33,8,,,33,4,,,33,23,,,33,1*7C

$GBGSV,7,7,25,5,,,32,1*45

$GBRMC,125933.516,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125933.516,0.000,758.093,758.093,693.294,2097152,2097152,2097152*6F



2025-07-31 20:59:29:991 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:30:001 ==>> 定位已等待【12】秒.
2025-07-31 20:59:30:266 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 20:59:30:757 ==>> $GBGGA,125934.516,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,24,,,41,25,,,41,1*45

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,40,,,38,1*72

$GBGSV,7,3,26,14,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,7,4,26,42,,,37,7,,,36,13,,,36,9,,,36,1*7A

$GBGSV,7,5,26,6,,,36,2,,,35,38,,,35,44,,,35,1*7C

$GBGSV,7,6,26,34,,,34,10,,,33,8,,,33,4,,,33,1*7E

$GBGSV,7,7,26,23,,,33,5,,,32,1*47

$GBRMC,125934.516,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125934.516,0.000,760.770,760.770,695.742,2097152,2097152,2097152*60



2025-07-31 20:59:31:001 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:31:011 ==>> 定位已等待【13】秒.
2025-07-31 20:59:31:849 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

$GBGGA,125935.516,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*1

2025-07-31 20:59:31:924 ==>> 4

$GBGSV,7,1,26,33,,,42,3,,,41,24,,,41,60,,,40,1*45

$GBGSV,7,2,26,59,,,40,25,,,40,39,,,39,40,,,38,1*73

$GBGSV,7,3,26,41,,,38,1,,,37,42,,,37,14,,,37,1*4E

$GBGSV,7,4,26,7,,,36,13,,,36,9,,,36,16,,,36,1*7A

$GBGSV,7,5,26,6,,,36,2,,,35,38,,,35,44,,,34,1*7D

$GBGSV,7,6,26,34,,,34,10,,,33,4,,,33,23,,,33,1*47

$GBGSV,7,7,26,5,,,32,8,,,32,1*7F

$GBRMC,125935.516,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125935.516,0.000,756.793,756.793,692.105,2097152,2097152,2097152*63

[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 20:59:32:014 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:32:032 ==>> 定位已等待【14】秒.
2025-07-31 20:59:32:261 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 20:59:33:026 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 20:59:33:036 ==>> 定位已等待【15】秒.
2025-07-31 20:59:33:410 ==>> $GBGGA,125932.523,2301.2571492,N,11421.9421092,E,1,14,0.77,75.136,M,-1.770,M,,*57

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,41,14,57,189,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,36,16,52,352,36,1,48,126,36,1*44

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,45,23,40,7,43,178,35,1*4C

$GBGSV,7,4,26,60,41,238,39,40,41,161,38,9,39,322,35,10,34,189,33,1*4D

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,32,1*70

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBRMC,125932.523,A,2301.2571492,N,11421.9421092,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:08][GNSS]HD8040 GPS
[D][05:19:08][GNSS]GPS diff_sec 124011624, report 0x42 frame
$GBGST,125932.523,2.281,0.201,0.189,0.289,2.705,3.712,8.829*7A

[D][05:19:08][COMM]Main Task receive event:131
[D][05:19:08][COMM]index:0,power_mode:0xFF
[D][05:19:08][COMM]index:1,sound_mode:0xFF
[D][05:19:08][COMM]index:2,gsensor_mode:0xFF
[D][05:19:08][COMM]index:3,report_freq_mode:0xFF
[D][05:19:08][COMM]index:4,report_period:0xFF
[D][05:19:08][COMM]index:5,normal_reset_mode:0xFF


2025-07-31 20:59:33:515 ==>> [D][05:19:08][COMM]index:6,normal_reset_period:0xFF
[D][05:19:08][COMM]index:7,spock_over_speed:0xFF
[D][05:19:08][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:08][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:08][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:08][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:08][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:08][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:08][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:08][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:08][COMM]index:16,imu_config_params:0xFF
[D][05:19:08][COMM]index:17,long_connect_params:0xFF
[D][05:19:08][COMM]index:18,detain_mark:0xFF
[D][05:19:08][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:08][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:08][COMM]index:21,mc_mode:0xFF
[D][05:19:08][COMM]index:22,S_mode:0xFF
[D][05:19:08][COMM]index:23,overweight:0xFF
[D][05:19:08][COMM]index:24,standstill_mode:0xFF
[D][05:19:08][COMM]index:25,night_mode:0xFF
[D][05:19:08][COMM]index:26,experiment1:0xFF
[D][05:19:08][COMM]index:27,experiment2:0xFF
[D][05:19:08][COMM]index:28,experiment3:0xFF
[D][05:19:08][COMM]index:29,experiment4:0xFF

2025-07-31 20:59:33:620 ==>> 
[D][05:19:08][COMM]index:30,night_mode_start:0xFF
[D][05:19:08][COMM]index:31,night_mode_end:0xFF
[D][05:19:08][COMM]index:33,park_report_minutes:0xFF
[D][05:19:08][COMM]index:34,park_report_mode:0xFF
[D][05:19:08][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:08][COMM]index:38,charge_battery_para: FF
[D][05:19:08][COMM]index:39,multirider_mode:0xFF
[D][05:19:08][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:08][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:08][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:08][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:08][COMM]index:44,riding_duration_config:0xFF
[D][05:19:08][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:08][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:08][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:08][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:08][COMM]index:49,mc_load_startup:0xFF
[D][05:19:08][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:08][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:08][COMM]index:52,traffic_mode:0xFF
[D][05:19:08][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:08][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:08][COMM]index:

2025-07-31 20:59:33:725 ==>> 55,wheel_alarm_play_switch:255
[D][05:19:08][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:08][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:08][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:08][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:08][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:08][COMM]index:63,experiment5:0xFF
[D][05:19:08][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:08][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:08][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:08][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:08][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:08][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:08][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:08][COMM]index:72,experiment6:0xFF
[D][05:19:08][COMM]index:73,experiment7:0xFF
[D][05:19:08][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:08][COMM]index:75,zero_value_from_server:-1
[D][05:19:08][COMM]index:76,multirider_threshold:255
[D][05:19:08][COMM]index:77,experiment8:255
[D][05:19:08][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:08][COMM]index:79,temp_park_tail_light_twinkle_duration:25

2025-07-31 20:59:33:830 ==>> 5
[D][05:19:08][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:08][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:08][COMM]index:83,loc_report_interval:255
[D][05:19:08][COMM]index:84,multirider_threshold_p2:255
[D][05:19:08][COMM]index:85,multirider_strategy:255
[D][05:19:08][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:08][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:08][COMM]index:90,weight_param:0xFF
[D][05:19:08][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:08][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:08][COMM]index:95,current_limit:0xFF
[D][05:19:08][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:08][COMM]index:100,location_mode:0xFF

[W][05:19:08][PROT]remove success[1629955148],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:08][PROT]add success [1629955148],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:08][COMM]Main Task receive event:131 finished processing
[D][05:19:08][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:08][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,125933.023,2301.2573962,N,11421.9419618,E,1,14,0.77,74.491,M,-1.

2025-07-31 20:59:33:935 ==>> 770,M,,*55

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,39,1*4B

$GBGSV,7,2,26,39,52,12,39,6,52,348,36,16,52,352,36,1,48,126,36,1*44

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,45,23,40,7,43,178,35,1*4C

$GBGSV,7,4,26,60,41,238,39,40,41,161,38,9,39,322,35,10,34,189,33,1*4D

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,32,1*70

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,2,1,06,33,68,250,36,39,52,12,39,25,45,295,39,24,45,23,39,5*71

$GBGSV,2,2,06,41,30,315,37,44,19,91,35,5*47

$GBRMC,125933.023,A,2301.2573962,N,11421.9419618,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125933.023,2.343,0.987,0.885,1.349,2.193,2.539,5.836*7D



2025-07-31 20:59:34:040 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 20:59:34:047 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【41】
北斗星号:【14】,信号值:【37】
北斗星号:【59】,信号值:【40】
北斗星号:【39】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【16】,信号值:【36】
北斗星号:【1】,信号值:【36】
北斗星号:【2】,信号值:【35】
北斗星号:【25】,信号值:【40】
北斗星号:【24】,信号值:【40】
北斗星号:【7】,信号值:【35】
北斗星号:【60】,信号值:【39】
北斗星号:【40】,信号值:【38】
北斗星号:【9】,信号值:【35】
北斗星号:【41】,信号值:【37】
北斗星号:【38】,信号值:【35】
北斗星号:【13】,信号值:【35】
北斗星号:【42】,信号值:【37】

2025-07-31 20:59:34:060 ==>> 检测【CSQ强度】
2025-07-31 20:59:34:078 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:59:34:389 ==>> $GBGGA,125934.003,2301.2576666,N,11421.9418620,E,1,14,0.77,74.342,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,41,14,57,189,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,35,1*44

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,45,23,40,7,43,178,35,1*4C

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7F

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,39,39,52,12,40,25,45,295,40,24,45,23,40,5*7E

$GBGSV,3,2,09,40,41,161,38,41,30,315,35,38,23,192,32,44,19,91,34,5*47

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125934.003,A,2301.2576666,N,11421.9418620,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125934.003,3.577,0.206,0.194,0.291,2.662,2.872,5.408*7C

[W][05:19:09][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:09][COMM]read battery soc:255


2025-07-31 20:59:35:364 ==>> $GBGGA,125935.000,2301.2578385,N,11421.9417890,E,1,14,0.77,74.512,M,-1.770,M,,*50

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,40,1*45

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,35,1*44

$GBGSV,7,3,26,2,45,238,34,25,45,295,40,24,45,23,40,7,43,178,35,1*4D

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,32,1*70

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,41,39,52,12,40,25,45,295,40,24,45,23,40,5*71

$GBGSV,3,2,09,40,41,161,38,41,30,315,36,38,23,192,32,44,19,91,34,5*44

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125935.000,A,2301.2578385,N,11421.9417890,E,0.000,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,125935.000,3.097,0.184,0.175,0.262,2.333,2.506,4.724*7C



2025-07-31 20:59:36:116 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:59:36:405 ==>> $GBGGA,125936.000,2301.2579419,N,11421.9417104,E,1,14,0.77,74.547,M,-1.770,M,,*54

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,40,1*45

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,35,1*44

$GBGSV,7,3,26,2,45,238,34,25,45,295,40,24,45,23,40,7,43,178,35,1*4D

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7F

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,40,24,45,23,40,5*73

$GBGSV,3,2,09,40,41,161,38,41,30,315,36,38,23,192,32,44,19,91,34,5*44

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125936.000,A,2301.2579419,N,11421.9417104,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125936.000,3.205,0.193,0.184,0.272,2.345,2.482,4.413*74

[D][05:19:11][COMM]read battery soc:255
[W][05:19:11][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:59:37:365 ==>> $GBGGA,125937.000,2301.2580256,N,11421.9416358,E,1,14,0.77,74.673,M,-1.770,M,,*50

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,40,1*45

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,36,1*47

$GBGSV,7,3,26,2,45,238,34,25,45,295,40,24,45,23,40,7,43,178,35,1*4D

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,32,1*70

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,45,23,40,5*72

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,30,5*70

$GBRMC,125937.000,A,2301.2580256,N,11421.9416358,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125937.000,3.040,0.196,0.186,0.274,2.227,2.340,4.074*7E



2025-07-31 20:59:38:195 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:59:38:408 ==>> $GBGGA,125938.000,2301.2581040,N,11421.9415972,E,1,14,0.77,74.625,M,-1.770,M,,*59

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,39,1*4B

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,36,1*47

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,45,23,40,7,43,178,35,1*4C

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7F

$GBGSV,7,6,26,13,20,208,35,44,19,91,35,8,17,203,32,23,7,259,33,1*4F

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,45,23,40,5*72

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125938.000,A,2301.2581040,N,11421.9415972,E,0.003,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,125938.000,3.003,0.179,0.171,0.253,2.186,2.280,3.853*75

[D][05:19:13][COMM]read battery soc:255
                                                     

2025-07-31 20:59:39:148 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:59:39:359 ==>> $GBGGA,125939.000,2301.2581562,N,11421.9416058,E,1,14,0.77,74.704,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,189,37,59,52,129,40,1*45

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,36,1*47

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,45,23,40,7,43,178,35,1*4C

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7F

$GBGSV,7,6,26,13,20,208,35,44,19,91,35,8,17,203,32,23,7,259,33,1*4F

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,45,23,40,5*72

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125939.000,A,2301.2581562,N,11421.9416058,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125939.000,3.094,0.190,0.181,0.268,2.221,2.301,3.739*7F



2025-07-31 20:59:40:279 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:59:40:400 ==>> $GBGGA,125940.000,2301.2582095,N,11421.9415866,E,1,14,0.77,74.744,M,-1.770,M,,*5F

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*46

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,44,23,40,7,43,178,35,1*4D

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*43

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,33,1*71

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,44,23,40,5*73

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,30,5*70

$GBRMC,125940.000,A,2301.2582095,N,11421.9415866,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125940.000,3.176,0.190,0.181,0.268,2.255,2.325,3.659*7E

[D][05:19:15][COMM]read battery soc:255


2025-07-31 20:59:40:491 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:59:41:369 ==>> $GBGGA,125941.000,2301.2582198,N,11421.9415728,E,1,14,0.77,74.847,M,-1.770,M,,*5B

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*46

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,44,23,40,7,43,178,35,1*4D

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,34,1*44

$GBGSV,7,5,26,4,32,112,33,41,30,315,37,38,23,192,35,5,22,257,32,1*70

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,44,23,40,5*73

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125941.000,A,2301.2582198,N,11421.9415728,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,125941.000,3.298,0.321,0.297,0.444,2.312,2.373,3.620*77



2025-07-31 20:59:41:911 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:

2025-07-31 20:59:41:971 ==>> 19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 12
[D][05:19:17][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:17][CAT1]exec over: func id: 12, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:59:42:229 ==>> 【CSQ强度】通过,【22】符合目标值【18】至【31】要求!
2025-07-31 20:59:42:236 ==>> 检测【关闭GSM联网】
2025-07-31 20:59:42:261 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:59:42:410 ==>> [D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 10, ret: 6
[D][05:19:17][CAT1]sub id: 10, ret: 6

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:17][M2M ]m2m gsm shut done, ret[0]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1

$GBGGA,125942.000,2301.2582293,N,11421.9415314,E,1,14,0.77,74.714,M,-1.770,M,,*52

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,27,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*45

$GBGSV,7,2,27,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*47

$GBGSV,7,3,27,2,45,238,35

2025-07-31 20:59:42:500 ==>> ,25,45,295,40,24,44,23,40,7,43,178,35,1*4C

$GBGSV,7,4,27,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,33,1*42

$GBGSV,7,5,27,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7E

$GBGSV,7,6,27,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4F

$GBGSV,7,7,27,42,4,321,37,34,,,34,26,,,36,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,44,23,41,5*72

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125942.000,A,2301.2582293,N,11421.9415314,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,125942.000,3.518,0.200,0.190,0.281,2.422,2.476,3.640*72

[D][05:19:17][COMM]read battery soc:255
                                         

2025-07-31 20:59:42:590 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:18][COMM]GSM test
[D][05:19:18][COMM]GSM test disable


2025-07-31 20:59:42:786 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:59:42:803 ==>> 检测【4G联网测试】
2025-07-31 20:59:42:835 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:59:42:953 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 20:59:43:833 ==>> [D][05:19:18][COMM]Main Task receive event:14
[D][05:19:18][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955158, allstateRepSeconds = 0
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[

2025-07-31 20:59:43:939 ==>> D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experiment4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49

2025-07-31 20:59:44:044 ==>> ,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index

2025-07-31 20:59:44:149 ==>> :74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[D][05:19:18][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:19:18][PROT]remove success[1629955158],send

2025-07-31 20:59:44:254 ==>> _path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,125943.000,2301.2582298,N,11421.9415186,E,1,14,0.77,74.642,M,-1.770,M,,*53

$GBGSA,A,3,33,14,39,06,16,25,24,07,40,10,41,38,1.51,0.77,1.30,4*0C

$GBGSA,A,3,44,23,,,,,,,,,,,1.51,0.77,1.30,4*0E

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,36,16,52,352,37,1,48,126,36,1*45

$GBGSV,7,3,26,2,45,238,35,25,45,295,40,24,44,23,40,7,43,178,36,1*4E

$GBGSV,7,4,26,60,41,238,40,40,41,161,38,9,39,322,35,10,34,189,34,1*44

$GBGSV,7,5,26,4,32,112,33,41,30,315,38,38,23,192,35,5,22,257,32,1*7F

$GBGSV,7,6,26,13,20,208,35,44,19,91,34,8,17,203,32,23,7,259,33,1*4E

$GBGSV,7,7,26,42,4,321,37,34,,,34,1*74

$GBGSV,3,1,09,33,68,250,43,39,52,12,40,25,45,295,41,24,44,23,40,5*73

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125943.000,A,2301.2582298,N,11421.9415186,E,0.003,0.00,310725,,,A,S*35

$GBVTG,0.00,

2025-07-31 20:59:44:359 ==>> T,,M,0.003,N,0.005,K,A*29

$GBGST,125943.000,3.594,0.297,0.276,0.412,2.455,2.504,3.610*7F

[D][05:19:18][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 8, ret: 6
[D][05:19:19][CAT1]gsm read msg sub id: 13
[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:19][CAT1]exec over: func id: 13, ret: 21
[D][05:19:19][M2M ]get csq[22]
                                                                                                                                                                                                                                                

2025-07-31 20:59:44:464 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:59:44:569 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:59:44:673 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 20:59:44:704 ==>>                                                                                                                                                                

2025-07-31 20:59:45:406 ==>> $GBGGA,125945.000,2301.2582315,N,11421.9415007,E,1,15,0.76,74.632,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,39,06,16,09,25,24,07,40,10,41,1.50,0.76,1.30,4*0E

$GBGSA,A,3,38,44,23,,,,,,,,,,1.50,0.76,1.30,4*05

$GBGSV,7,1,27,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,39,1*4B

$GBGSV,7,2,27,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*47

$GBGSV,7,3,27,9,47,326,35,2,45,238,35,25,45,295,40,24,44,23,40,1*4F

$GBGSV,7,4,27,7,43,178,36,60,41,238,40,40,41,161,38,10,34,189,34,1*48

$GBGSV,7,5,27,42,32,166,37,4,32,112,33,41,30,315,38,38,23,192,35,1*48

$GBGSV,7,6,27,5,22,257,32,13,20,208,35,44,19,91,34,8,17,203,32,1*43

$GBGSV,7,7,27,23,7,259,33,34,,,34,43,,,37,1*78

$GBGSV,3,1,09,33,68,250,43,39,52,12,41,25,45,295,41,24,44,23,40,5*72

$GBGSV,3,2,09,40,41,161,38,41,30,315,37,38,23,192,32,44,19,91,34,5*45

$GBGSV,3,3,09,23,7,259,31,5*71

$GBRMC,125945.000,A,2301.2582315,N,11421.9415007,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125945.000,3.535,0.234,0.219,0.328,2.416,2.458,3.486*7E



2025-07-31 20:59:46:429 ==>> $GBGGA,125946.000,2301.2582216,N,11421.9415002,E,1,16,0.74,74.580,M,-1.770,M,,*51

$GBGSA,A,3,33,14,39,06,16,09,25,24,07,40,10,42,1.49,0.74,1.30,4*07

$GBGSA,A,3,41,38,44,23,,,,,,,,,1.49,0.74,1.30,4*0A

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,39,1*4A

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*46

$GBGSV,7,3,26,9,47,326,35,2,45,238,35,25,45,296,40,24,44,23,40,1*4D

$GBGSV,7,4,26,7,43,178,36,60,41,238,40,40,41,161,38,10,34,189,34,1*49

$GBGSV,7,5,26,42,32,166,37,4,32,112,33,41,30,315,38,38,23,192,35,1*49

$GBGSV,7,6,26,5,22,257,33,13,20,208,35,44,19,91,34,8,17,203,33,1*42

$GBGSV,7,7,26,23,7,259,33,34,,,34,1*7A

$GBGSV,3,1,10,33,68,250,43,39,52,12,41,25,45,296,41,24,44,23,40,5*79

$GBGSV,3,2,10,40,41,161,38,42,32,166,36,41,30,315,37,38,23,192,32,5*79

$GBGSV,3,3,10,44,19,91,34,23,7,259,31,5*7E

$GBRMC,125946.000,A,2301.2582216,N,11421.9415002,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125946.000,3.460,0.386,0.348,0.537,2.372,2.412,3.407*7B

[D][05:19:21][COMM]read battery soc:255


2025-07-31 20:59:47:393 ==>> $GBGGA,125947.000,2301.2582442,N,11421.9414982,E,1,16,0.74,74.587,M,-1.770,M,,*50

$GBGSA,A,3,33,14,39,06,16,09,25,24,07,40,10,42,1.49,0.74,1.30,4*07

$GBGSA,A,3,41,38,44,23,,,,,,,,,1.49,0.74,1.30,4*0A

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,37,1,48,126,36,1*46

$GBGSV,7,3,26,9,47,326,35,2,45,238,35,25,45,296,40,24,44,23,40,1*4D

$GBGSV,7,4,26,7,43,178,36,60,41,238,40,40,41,161,38,10,34,189,33,1*4E

$GBGSV,7,5,26,42,32,166,37,4,32,112,33,41,30,315,38,38,23,192,35,1*49

$GBGSV,7,6,26,5,22,257,32,13,20,208,35,44,19,91,34,8,17,203,32,1*42

$GBGSV,7,7,26,23,7,259,33,34,,,34,1*7A

$GBGSV,3,1,10,33,68,250,43,39,52,12,41,25,45,296,41,24,44,23,40,5*79

$GBGSV,3,2,10,40,41,161,38,42,32,166,37,41,30,315,37,38,23,192,32,5*78

$GBGSV,3,3,10,44,19,91,35,23,7,259,31,5*7F

$GBRMC,125947.000,A,2301.2582442,N,11421.9414982,E,0.004,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.004,N,0.008,K,A*23

$GBGST,125947.000,3.452,0.286,0.262,0.400,2.363,2.402,3.359*7B



2025-07-31 20:59:48:423 ==>> $GBGGA,125948.000,2301.2582605,N,11421.9415009,E,1,16,0.74,74.637,M,-1.770,M,,*5D

$GBGSA,A,3,33,14,39,06,16,09,25,24,07,40,10,42,1.49,0.74,1.30,4*07

$GBGSA,A,3,41,38,44,23,,,,,,,,,1.49,0.74,1.30,4*0A

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,36,16,52,352,37,1,48,126,36,1*45

$GBGSV,7,3,26,9,47,326,35,2,45,238,35,25,45,296,40,24,44,23,40,1*4D

$GBGSV,7,4,26,7,43,178,35,60,41,238,39,40,41,161,38,10,34,189,33,1*43

$GBGSV,7,5,26,42,32,166,37,4,32,112,33,41,30,315,38,38,23,192,35,1*49

$GBGSV,7,6,26,5,22,257,32,13,20,208,35,44,19,91,34,8,17,203,33,1*43

$GBGSV,7,7,26,23,7,259,33,34,,,34,1*7A

$GBGSV,3,1,10,33,68,250,43,39,52,12,41,25,45,296,41,24,44,23,41,5*78

$GBGSV,3,2,10,40,41,161,38,42,32,166,37,41,30,315,37,38,23,192,32,5*78

$GBGSV,3,3,10,44,19,91,34,23,7,259,31,5*7E

$GBRMC,125948.000,A,2301.2582605,N,11421.9415009,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125948.000,3.443,0.293,0.268,0.409,2.355,2.392,3.318*7D

[D][05:19:23][COMM]read battery soc:255


2025-07-31 20:59:48:824 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:59:49:027 ==>> [W][05:19:24][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 20:59:49:970 ==>> [D][05:19:24][COMM]Main Task receive event:14
[D][05:19:24][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955164, allstateRepSeconds = 0
[D][05:19:24][COMM]index:0,power_mode:0xFF
[D][05:19:24][COMM]index:1,sound_mode:0xFF
[D][05:19:24][COMM]index:2,gsensor_mode:0xFF
[D][05:19:24][COMM]index:3,report_freq_mode:0xFF
[D][05:19:24][COMM]index:4,report_period:0xFF
[D][05:19:24][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:24][COMM]index:6,normal_reset_period:0xFF
[D][05:19:24][COMM]index:7,spock_over_speed:0xFF
[D][05:19:24][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:24][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:24][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:24][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:24][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:24][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:24][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:24][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:24][COMM]index:16,imu_config_params:0xFF
[D][05:19:24][COMM]index:17,long_connect_params:0xFF
[D][05:19:24][COMM]index:18,detain_mark:0xFF
[D][05:19:24][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:24][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:24][COMM]index:21,mc_mode:0xFF
[D][05:19:

2025-07-31 20:59:50:076 ==>> 24][COMM]index:22,S_mode:0xFF
[D][05:19:24][COMM]index:23,overweight:0xFF
[D][05:19:24][COMM]index:24,standstill_mode:0xFF
[D][05:19:24][COMM]index:25,night_mode:0xFF
[D][05:19:24][COMM]index:26,experiment1:0xFF
[D][05:19:24][COMM]index:27,experiment2:0xFF
[D][05:19:24][COMM]index:28,experiment3:0xFF
[D][05:19:24][COMM]index:29,experiment4:0xFF
[D][05:19:24][COMM]index:30,night_mode_start:0xFF
[D][05:19:24][COMM]index:31,night_mode_end:0xFF
[D][05:19:24][COMM]index:33,park_report_minutes:0xFF
[D][05:19:24][COMM]index:34,park_report_mode:0xFF
[D][05:19:24][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:24][COMM]index:38,charge_battery_para: FF
[D][05:19:24][COMM]index:39,multirider_mode:0xFF
[D][05:19:24][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:24][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:24][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:24][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:24][COMM]index:44,riding_duration_config:0xFF
[D][05:19:24][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:24][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:24][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:24][COMM]index:48,shlmt_sensor_

2025-07-31 20:59:50:182 ==>> en:0xFF
[D][05:19:24][COMM]index:49,mc_load_startup:0xFF
[D][05:19:24][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:24][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:24][COMM]index:52,traffic_mode:0xFF
[D][05:19:24][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:24][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:24][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:24][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:24][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:24][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:24][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:24][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:24][COMM]index:63,experiment5:0xFF
[D][05:19:24][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:24][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:24][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:24][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:24][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:24][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:24][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:24][COMM]index:72,experiment6:0xFF
[D][05:19:24][COMM]index:73,experiment7:0xFF
[D][0

2025-07-31 20:59:50:289 ==>> 5:19:24][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:24][COMM]index:75,zero_value_from_server:-1
[D][05:19:24][COMM]index:76,multirider_threshold:255
[D][05:19:24][COMM]index:77,experiment8:255
[D][05:19:24][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:24][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:24][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:24][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:24][COMM]index:83,loc_report_interval:255
[D][05:19:24][COMM]index:84,multirider_threshold_p2:255
[D][05:19:24][COMM]index:85,multirider_strategy:255
[D][05:19:24][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:24][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:24][COMM]index:90,weight_param:0xFF
[D][05:19:24][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:24][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:24][COMM]index:95,current_limit:0xFF
[D][05:19:24][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:24][COMM]index:100,location_mode:0xFF

[W][05:19:24][PROT]remove success[1629955164],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:24][PROT]add 

2025-07-31 20:59:50:393 ==>> success [1629955164],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:24][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:24][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:24][PROT]index:0 1629955164
[D][05:19:24][PROT]is_send:0
[D][05:19:24][PROT]sequence_num:10
[D][05:19:24][PROT]retry_timeout:0
[D][05:19:24][PROT]retry_times:1
[D][05:19:24][PROT]send_path:0x2
[D][05:19:24][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:24][PROT]===========================================================
[W][05:19:24][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955164]
[D][05:19:24][PROT]===========================================================
[D][05:19:24][PROT]sending traceid [999999999990000B]
[D][05:19:24][PROT]Send_TO_M2M [1629955164]
[D][05:19:24][CAT1]gsm read msg sub id: 13
[D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:24][SAL ]sock send credit cnt[6]
[D][05:19:24][SAL ]sock send ind credit cnt[6]
[D][05:19:24][M2M ]m2m send data len[294]
[D][05:19:24][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:24][SAL ]Cellular task submsg id[10]
[D][05:19:24][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df0] format[0]
[

2025-07-31 20:59:50:498 ==>> D][05:19:24][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:24][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:24][CAT1]exec over: func id: 13, ret: 21
[D][05:19:24][M2M ]get csq[21]
[D][05:19:24][CAT1]gsm read msg sub id: 15
[D][05:19:24][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:24][CAT1]Send Data To Server[294][294] ... ->:
0093B989113311331133113311331B88BD69E695D2922BB7627A19A84ABCEF401A8A9BF5813D847D51F9EC9826F33B823519FA4D6FC49FAE15342C693BBBBCB11697076BD103936B774BB79BE0C83C77873BB7682DDEDBF6D2D194F670DC5B6504E6164BF23D282C785338ACAF0787432F5F3ED1BF6AE427AE529486146AD598CA0C6B02A3AD25CCA8DD8995E4A07878EC84FF
$GBGGA,125949.000,2301.2582837,N,11421.9415065,E,1,16,0.74,74.760,M,-1.770,M,,*5A

$GBGSA,A,3,33,14,39,06,16,09,25,24,07,40,10,42,1.49,0.74,1.30,4*07

$GBGSA,A,3,41,38,44,23,,,,,,,,,1.49,0.74,1.30,4*0A

$GBGSV,7,1,26,33,68,250,42,3,61,190,40,14,57,188,37,59,52,129,40,1*44

$GBGSV,7,2,26,39,52,12,39,6,52,348,35,16,52,352,36,1,48,126,36,1*47

$GBGSV,7,3,26,9,47,326,35,2,45,238,35,25,45,296,40,24,44,23,40,1*4D

$GBGSV,7,4,26,7,43,178,35,60,41,238,39,40,41,161,37,10,34,189,33,1*4C

$GBGSV,7,5,26,42,32,166,37,4,32,112,33,41,30,315,37,38,23,192,35,1*46

$GBGS

2025-07-31 20:59:50:588 ==>> V,7,6,26,5,22,257,32,13,20,208,35,44,19,91,34,8,17,203,32,1*42

$GBGSV,7,7,26,23,7,259,33,34,,,34,1*7A

$GBGSV,3,1,10,33,68,250,44,39,52,12,41,25,45,296,41,24,44,23,41,5*7F

$GBGSV,3,2,10,40,41,161,38,42,32,166,37,41,30,315,37,38,23,192,32,5*78

$GBGSV,3,3,10,44,19,91,34,23,7,259,31,5*7E

$GBRMC,125949.000,A,2301.2582837,N,11421.9415065,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125949.000,3.587,0.198,0.187,0.279,2.429,2.464,3.354*74

[D][05:19:24][CAT1]<<< 
SEND OK

[D][05:19:24][CAT1]exec over: func id: 15, ret: 11
[D][05:19:24][CAT1]sub id: 15, ret: 11

[D][05:19:24][SAL ]Cellular task submsg id[68]
[D][05:19:24][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:24][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:24][M2M ]g_m2m_is_idle become true
[D][0

2025-07-31 20:59:50:693 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 20:59:50:724 ==>>           

2025-07-31 20:59:50:873 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:59:50:881 ==>> 检测【关闭GPS】
2025-07-31 20:59:50:888 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:59:51:232 ==>> [W][05:19:26][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:26][GNSS]stop locating
[D][05:19:26][GNSS]stop event:8
[D][05:19:26][GNSS]GPS stop. ret=0
[D][05:19:26][GNSS]all continue location stop
[W][05:19:26][GNSS]stop locating
[D][05:19:26][GNSS]all sing location stop
[D][05:19:26][CAT1]gsm read msg sub id: 24
[D][05:19:26][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:26][CAT1]<<< 
OK

[D][05:19:26][CAT1]exec over: func id: 24, ret: 6
[D][05:19:26][CAT1]sub id: 24, ret: 6



2025-07-31 20:59:51:409 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:59:51:423 ==>> 检测【清空消息队列2】
2025-07-31 20:59:51:438 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:59:51:548 ==>> [W][05:19:27][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:27][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:59:51:638 ==>>                 SS]recv submsg id[1]
[D][05:19:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:27][GNSS]location stop evt done evt


2025-07-31 20:59:51:695 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:59:51:704 ==>> 检测【轮动检测】
2025-07-31 20:59:51:722 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:59:51:743 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:59:51:847 ==>> [D][05:19:27][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:59:52:200 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:59:52:260 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:59:52:365 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 20:59:52:488 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:59:52:496 ==>> 检测【关闭小电池】
2025-07-31 20:59:52:509 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:59:52:562 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:59:52:768 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:59:52:776 ==>> 检测【进入休眠模式】
2025-07-31 20:59:52:791 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:59:52:985 ==>> [W][05:19:28][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:28][COMM]Main Task receive event:28
[D][05:19:28][COMM]main task tmp_sleep_event = 8
[D][05:19:28][COMM]prepare to sleep
[D][05:19:28][CAT1]gsm read msg sub id: 12
[D][05:19:28][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:59:53:866 ==>> [D][05:19:28][CAT1]<<< 
OK

[D][05:19:28][CAT1]exec over: func id: 12, ret: 6
[D][05:19:28][M2M ]tcpclient close[4]
[D][05:19:28][SAL ]Cellular task submsg id[12]
[D][05:19:28][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:28][CAT1]gsm read msg sub id: 9
[D][05:19:28][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100002. period:10000
[D][05:19:28][COMM]msg 0227 loss. last_tick:0. cur_tick:100002. period:10000
[D][05:19:28][COMM]msg 0228 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0261 loss. last_tick:0. cur_tick:100003. period:10000
[D][05:19:28][COMM]msg 0262 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0263 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0281 loss. last_tick:0. cur_tick:100004. period:10000
[D][05:19:28][COMM]msg 0282 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 0283 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 02A1 loss. last_tick:0. cur_tick:100005. period:10000
[D][05:19:28][COMM]msg 02A2 loss. last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02A3 loss. 

2025-07-31 20:59:53:971 ==>> last_tick:0. cur_tick:100006. period:10000
[D][05:19:28][COMM]msg 02C3 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02C4 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02C5 loss. last_tick:0. cur_tick:100007. period:10000
[D][05:19:28][COMM]msg 02E3 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02E4 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 02E5 loss. last_tick:0. cur_tick:100008. period:10000
[D][05:19:28][COMM]msg 0302 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0303 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 0304 loss. last_tick:0. cur_tick:100009. period:10000
[D][05:19:28][COMM]msg 02E6 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 02E7 loss. last_tick:0. cur_tick:100010. period:10000
[D][05:19:28][COMM]msg 0305 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 0306 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A8 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:28][COMM]msg 02A9 loss. last_tick:0. cur_tick:100011. period:10

2025-07-31 20:59:54:076 ==>> 000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100013. period:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100013. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100013. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100014. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100014. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100015. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100015. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100015. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100016
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100016
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01

2025-07-31 20:59:54:136 ==>> ->0x0000000300010F03 100016
[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]exec over: func id: 9, ret: 6
[D][05:19:29][CAT1]sub id: 9, ret: 6

[D][05:19:29][SAL ]Cellular task submsg id[68]
[D][05:19:29][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:29][SAL ]socket close ind. id[4]
[D][05:19:29][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:29][COMM]1x1 frm_can_tp_send ok
[D][05:19:29][CAT1]pdpdeact urc len[22]


2025-07-31 20:59:54:166 ==>>                                                                               

2025-07-31 20:59:54:406 ==>> [E][05:19:29][COMM]1x1 rx timeout
[E][05:19:29][COMM]1x1 tp timeout
[E][05:19:29][COMM]1x1 error -3.
[W][05:19:29][COMM]CAN STOP!
[D][05:19:29][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:29][COMM]------------ready to Power off Acckey 1------------
[D][05:19:29][COMM]------------ready to Power off Acckey 2------------
[D][05:19:29][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:29][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1286
[D][05:19:29][COMM]bat sleep fail, reason:-1
[D][05:19:29][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:29][COMM]accel parse set 0
[D][05:19:29][COMM]imu rest ok. 100836
[D][05:19:29][COMM]imu sleep 0
[W][05:19:29][COMM]now sleep


2025-07-31 20:59:54:594 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:59:54:608 ==>> 检测【检测33V休眠电流】
2025-07-31 20:59:54:636 ==>> 开始33V电流采样
2025-07-31 20:59:54:649 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:59:54:706 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:59:55:711 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:59:55:772 ==>> Current33V:????:15.24

2025-07-31 20:59:56:217 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:59:56:225 ==>> 【检测33V休眠电流】通过,【15.24uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:59:56:250 ==>> 该项需要延时执行
2025-07-31 20:59:58:230 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:59:58:243 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:59:58:257 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:59:58:380 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1646mV
Get AD_V4 1mV
Get AD_V5 2741mV
Get AD_V6 2020mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:59:59:268 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:59:59:276 ==>> 检测【打开小电池2】
2025-07-31 20:59:59:288 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:59:59:362 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:59:59:575 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:59:59:584 ==>> 该项需要延时执行
2025-07-31 21:00:00:090 ==>> 此处延时了:【500】毫秒
2025-07-31 21:00:00:102 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 21:00:00:116 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:00:00:169 ==>> 5A A5 02 5A A5 


2025-07-31 21:00:00:259 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:00:00:377 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:00:00:385 ==>> 该项需要延时执行
2025-07-31 21:00:00:607 ==>> [D][05:19:35][COMM]------------ready to Power on Acckey 1------------
[D][05:19:35][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:35][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 0,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 1,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 2,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 3,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 4,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 5,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 6,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 7,volt = 9
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 8,volt = 9
[D][05:19:35][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 9
[D][05:19:35][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:35][COMM]----- get Acckey 1 and value:1------------
[W][05:19:35][COMM]CAN START!
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:35][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 106956
[D][05:19:35][COMM][Audio]exec status ready.
[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 6
[D][05

2025-07-31 21:00:00:653 ==>> :19:35][COMM]imu wakeup ok. 106970
[D][05:19:35][COMM]imu wakeup 1
[W][05:19:35][COMM]wake up system, wakeupEvt=0x80
[D][05:19:35][COMM]frm_can_weigth_power_set 1
[D][05:19:35][COMM]Clear Sleep Block Evt
[D][05:19:35][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:35][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:00:00:879 ==>> 此处延时了:【500】毫秒
2025-07-31 21:00:00:892 ==>> 检测【进入休眠模式2】
2025-07-31 21:00:00:919 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:00:01:030 ==>> [D][05:19:36][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[E][05:19:36][COMM]1x1 rx timeout
[D][05:19:36][COMM]1x1 frm_can_tp_send ok
[D][05:19:36][COMM]msg 02A0 loss. last_tick:106941. cur_tick:107450. period:50
[D][05:19:36][COMM]msg 02A4 loss. last_tick:106941. cur_tick:107450. period:50
[D][05:19:36][COMM]msg 02A5 loss. last_tick:106941. cur_tick:107451. period:50
[D][05:19:36][COMM]msg 02A6 loss. last_tick:106941. cur_tick:107451. period:50
[D][05:19:36][COMM]msg 02A7 loss. last_tick:106941. cur_tick:107452. period:50
[D][05:19:36][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 107452
[D][05:19:36][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 107452


2025-07-31 21:00:01:105 ==>> [W][05:19:36][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:00:01:347 ==>> [E][05:19:36][COMM]1x1 rx timeout
[E][05:19:36][COMM]1x1 tp timeout
[E][05:19:36][COMM]1x1 error -3.
[D][05:19:36][COMM]Main Task receive event:28 finished processing
[D][05:19:36][COMM]Main Task receive event:28
[D][05:19:36][COMM]prepare to sleep
[D][05:19:36][CAT1]gsm read msg sub id: 12
[D][05:19:36][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:36][CAT1]<<< 
OK

[D][05:19:36][CAT1]exec over: func id: 12, ret: 6
[D][05:19:36][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:36][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:00:01:650 ==>> [D][05:19:36][COMM]msg 0220 loss. last_tick:106941. cur_tick:107946. period:100
[D][05:19:36][COMM]msg 0221 loss. last_tick:106941. cur_tick:107947. period:100
[D][05:19:36][COMM]msg 0224 loss. last_tick:106941. cur_tick:107947. period:100
[D][05:19:36][COMM]msg 0260 loss. last_tick:106941. cur_tick:107948. period:100
[D][05:19:36][COMM]msg 0280 loss. last_tick:106941. cur_tick:107948. period:100
[D][05:19:36][COMM]msg 02C0 loss. last_tick:106941. cur_tick:107948. period:100
[D][05:19:36][COMM]msg 02C1 loss. last_tick:106941. cur_tick:107949. period:100
[D][05:19:36][COMM]msg 02C2 loss. last_tick:106941. cur_tick:107949. period:100
[D][05:19:36][COMM]msg 02E0 loss. last_tick:106941. cur_tick:107949. period:100
[D][05:19:36][COMM]msg 02E1 loss. last_tick:106941. cur_tick:107950. period:100
[D][05:19:36][COMM]msg 02E2 loss. last_tick:106941. cur_tick:107950. period:100
[D][05:19:36][COMM]msg 0300 loss. last_tick:106941. cur_tick:107950. period:100
[D][05:19:36][COMM]msg 0301 loss. last_tick:106941. cur_tick:107951. period:100
[D][05:19:36][COMM]bat msg 0240 loss. last_tick:106941. cur_tick:107951. period:100. j,i:1 54
[D][05:19:36][COMM]bat msg 0241 loss. last_tick:106941. cur_tick:107952. period:100. j,i:2 55
[D][05:19:36][COMM]bat msg 0242 loss. last_tick

2025-07-31 21:00:01:740 ==>> :106941. cur_tick:107952. period:100. j,i:3 56
[D][05:19:36][COMM]bat msg 0244 loss. last_tick:106941. cur_tick:107952. period:100. j,i:5 58
[D][05:19:36][COMM]bat msg 024E loss. last_tick:106941. cur_tick:107953. period:100. j,i:15 68
[D][05:19:36][COMM]bat msg 024F loss. last_tick:106941. cur_tick:107953. period:100. j,i:16 69
[D][05:19:36][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 107954
[D][05:19:36][COMM]CAN message bat fault change: 0x00000000->0x0001802E 107954
[D][05:19:36][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 107954
                                                                              

2025-07-31 21:00:02:010 ==>> [D][05:19:37][COMM]msg 0222 loss. last_tick:106941. cur_tick:108449. period:150
[D][05:19:37][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 108450
[D][05:19:37][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:37][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:37][COMM]------------ready to Power off Acckey 2------------


2025-07-31 21:00:02:205 ==>> [E][05:19:37][COMM]1x1 rx timeout
[E][05:19:37][COMM]1x1 tp timeout
[E][05:19:37][COMM]1x1 error -3.
[W][05:19:37][COMM]CAN STOP!
[D][05:19:37][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:37][COMM]------------ready to Power off Acckey 1------------
[D][05:19:37][COMM]------------ready to Power off Acckey 2------------
[D][05:19:37][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:37][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 105
[D][05:19:37][COMM]bat sleep fail, reason:-1
[D][05:19:37][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:37][COMM]accel parse set 0
[D][05:19:37][COMM]imu rest ok. 108641
[D][05:19:37][COMM]imu sleep 0
[W][05:19:37][COMM]now sleep


2025-07-31 21:00:02:451 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:00:02:459 ==>> 检测【检测小电池休眠电流】
2025-07-31 21:00:02:473 ==>> 开始小电池电流采样
2025-07-31 21:00:02:500 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:00:02:551 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:00:03:553 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:00:03:631 ==>> CurrentBattery:ƽ��:70.52

2025-07-31 21:00:04:056 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:00:04:065 ==>> 【检测小电池休眠电流】通过,【70.52uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:00:04:073 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 21:00:04:093 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:00:04:161 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:00:04:356 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:00:04:365 ==>> 该项需要延时执行
2025-07-31 21:00:04:402 ==>> [D][05:19:39][COMM]------------ready to Power on Acckey 1------------
[D][05:19:39][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:39][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:39][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:39][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:39][COMM]----- get Acckey 1 and value:1------------
[W][05:19:39][COMM]CAN START!
[D][05:19:39][GNSS]handler GSMGet Base timeout
[D][05:19:39][CAT1]gsm read msg sub id: 12
[D][05:19:39][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:39][COMM]CAN message bat fault change: 0x0001802E->0x00000000 110781
[D][05:19:39][COMM][Audio]exec status ready.
[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 12, ret: 6
[D][05:19:39][COMM]imu wakeup ok. 110795
[D][05:19:39][COMM]imu wakeup 1
[W][05:19:39][COMM]wake up system, wakeupEvt=0x80
[D][05:19:39][COMM]frm_can_weigth_power_set 1
[D][05:19:39][COMM]Clear Sleep Block Evt
[D][05:19:39][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:39][COMM]1x1 frm_can_tp_send ok
[D][05:19:39][COMM]read battery soc:0


2025-07-31 21:00:04:552 ==>> [D][05:19:40][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 21:00:04:657 ==>> [E][05:19:40][COMM]1x1 rx timeout
[D][05:19:40][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:00:04:762 ==>> [D][05:19:40][COMM]msg 02A0 loss. l

2025-07-31 21:00:04:837 ==>> ast_tick:110762. cur_tick:111275. period:50
[D][05:19:40][COMM]msg 02A4 loss. last_tick:110762. cur_tick:111276. period:50
[D][05:19:40][COMM]msg 02A5 loss. last_tick:110762. cur_tick:111276. period:50
[D][05:19:40][COMM]msg 02A6 loss. last_tick:110762. cur_tick:111277. period:50
[D][05:19:40][COMM]msg 02A7 loss. last_tick:110762. cur_tick:111277. period:50
[D][05:19:40][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 111277
[D][05:19:40][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 111278


2025-07-31 21:00:04:867 ==>> 此处延时了:【500】毫秒
2025-07-31 21:00:04:880 ==>> 检测【检测唤醒】
2025-07-31 21:00:04:906 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:00:05:620 ==>> [W][05:19:40][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:40][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:40][FCTY]==========Modules-nRF5340 ==========
[D][05:19:40][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:40][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:40][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:40][FCTY]DeviceID    = 460130071539001
[D][05:19:40][FCTY]HardwareID  = 867222088007986
[D][05:19:40][FCTY]MoBikeID    = 9999999999
[D][05:19:40][FCTY]LockID      = FFFFFFFFFF
[D][05:19:40][FCTY]BLEFWVersion= 105
[D][05:19:40][FCTY]BLEMacAddr   = E389E4EC37FD
[D][05:19:40][FCTY]Bat         = 3824 mv
[D][05:19:40][FCTY]Current     = 0 ma
[D][05:19:40][FCTY]VBUS        = 2600 mv
[D][05:19:40][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:40][FCTY]Ext battery vol = 32, adc = 1294
[D][05:19:40][FCTY]Acckey1 vol = 5500 mv, Acckey2 vol = 278 mv
[D][05:19:40][FCTY]Bike Type flag is invalied
[D][05:19:40][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:40][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:40][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:40][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:40][FC

2025-07-31 21:00:05:672 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 21:00:05:694 ==>> 检测【关机】
2025-07-31 21:00:05:717 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:00:05:726 ==>> TY]CAT1_GNSS_PLATFORM = C4
[D][05:19:40][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:40][FCTY]Bat1         = 3846 mv
[D][05:19:40][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:40][FCTY]==========Modules-nRF5340 ==========
[E][05:19:40][COMM]1x1 rx timeout
[E][05:19:40][COMM]1x1 tp timeout
[E][05:19:40][COMM]1x1 error -3.
[D][05:19:40][COMM]Main Task receive event:28 finished processing
[D][05:19:40][COMM]Main Task receive event:65
[D][05:19:40][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:40][COMM]Main Task receive event:65 finished processing
[D][05:19:40][COMM]Main Task receive event:60
[D][05:19:40][COMM]smart_helmet_vol=255,255
[D][05:19:40][COMM]report elecbike
[W][05:19:40][PROT]remove success[1629955180],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:40][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:40][PROT]index:0
[D][05:19:40][PROT]is_send:1
[D][05:19:40][PROT]sequence_num:12
[D][05:19:40][PROT]retry_timeout:0
[D][05:19:40][PROT]retry_times:3
[D][05:19:40][PROT]send_path:0x3
[D][05:19:40][PROT]msg_type:0x5d03
[D][05:19:40][PROT]===========================================================


2025-07-31 21:00:05:830 ==>> 
[D][05:19:40][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:19:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955180]
[D][05:19:40][PROT]===========================================================
[D][05:19:40][PROT]Sending traceid[999999999990000D]
[D][05:19:40][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:40][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:40][PROT]ble is not inited or not connected or cccd not enabled
[W][05:19:40][PROT]add success [1629955180],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:40][COMM]Main Task receive event:60 finished processing
[D][05:19:40][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:40][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:40][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:40][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:40][SAL ]open socket ind id[4], rst[0]
[D][05:19:40][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:40][SAL ]Cellular task submsg id[8]
[D][05:19:40][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:40][SAL ]do

2025-07-31 21:00:05:935 ==>> main[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:40][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:40][CAT1]gsm read msg sub id: 8
[D][05:19:40][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:40][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:40][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:40][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:40][CAT1]<<< 
+CME ERROR: 100

[D][05:19:40][COMM]msg 0220 loss. last_tick:110762. cur_tick:111769. period:100
[D][05:19:40][COMM]msg 0221 loss. last_tick:110762. cur_tick:111770. period:100
[D][05:19:40][COMM]msg 0224 loss. last_tick:110762. cur_tick:111770. period:100
[D][05:19:40][COMM]msg 0260 loss. last_tick:110762. cur_tick:111770. period:100
[D][05:19:40][COMM]msg 0280 loss. last_tick:110762. cur_tick:111771. period:100
[D][05:19:40][COMM]msg 02C0 loss. last_tick:110762. cur_tick:111771. period:100
[D][05:19:40][COMM]msg 02C1 loss. last_tick:110762. cur_tick:111772. period:100
[D][05:19:40][COMM]msg 02C2 loss. last_tick:110762. cur_tick:111772. period:100
[D][05:19:40][COMM]msg 02E0 loss. last_tick:110762. cur_tick:111772. period:100
[D][05:19:40][COMM]msg 02E1 loss. last_tick:110762. cur_tick:111773. period:100
[D

2025-07-31 21:00:06:040 ==>> ][05:19:40][COMM]msg 02E2 loss. last_tick:110762. cur_tick:111773. period:100
[D][05:19:40][COMM]msg 0300 loss. last_tick:110762. cur_tick:111773. period:100
[D][05:19:40][COMM]msg 0301 loss. last_tick:110762. cur_tick:111774. period:100
[D][05:19:40][COMM]bat msg 0240 loss. last_tick:110762. cur_tick:111774. period:100. j,i:1 54
[D][05:19:40][COMM]bat msg 0241 loss. last_tick:110762. cur_tick:111774. period:100. j,i:2 55
[D][05:19:40][COMM]bat msg 0242 loss. last_tick:110762. cur_tick:111775. period:100. j,i:3 56
[D][05:19:40][COMM]bat msg 0244 loss. last_tick:110762. cur_tick:111775. period:100. j,i:5 58
[D][05:19:40][COMM]bat msg 024E loss. last_tick:110763. cur_tick:111776. period:100. j,i:15 68
[D][05:19:40][COMM]bat msg 024F loss. last_tick:110763. cur_tick:111776. period:100. j,i:16 69
[D][05:19:40][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 111777
[D][05:19:40][COMM]CAN message bat fault change: 0x00000000->0x0001802E 111777
[D][05:19:40][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 111777


2025-07-31 21:00:06:669 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:00:06:714 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:00:06:774 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 21:00:06:879 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  9:41][PROT]retry_times:3
[D][05:19:41][PROT]send_path:0x3
[D][05:19:41][PROT]msg_type:0x5d03
[D][05:19:41][PROT]===========================================================
[W][05:19:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955181]
[D][05:19:41][PROT]===========================================================
[D][05:19:41][PROT]Sending traceid[999999999990000E]
[D][05:19:41][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:41][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect



2025-07-31 21:00:06:984 ==>> [W][05:19:41][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:41][COMM]Receive Bat Lock cmd 0
[D][05:19:41][COMM]VBUS is 1
[D][05:19:41][COMM][D301]:type:3, trace id:280
[D][05:19:41][COMM]id[], hw[000
[D][05:19:41][COMM]get mcMaincircuitVolt error
[D][05:19:41][COMM]get mcSubcircuitVolt error
[D][05:19:41][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:41][COMM]BAT CAN get state1 Fail 204
[D][05:19:41][COMM]BAT CAN get soc Fail, 204
[D][05:19:41][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:41][COMM]BAT CAN get state2 fail 204
[D][05:19:41][COMM]get bat work mode err
[W][05:19:41][PROT]remove success[1629955181],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:41][PROT]add success [1629955181],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:41][COMM]Main Task receive event:61 finished processing
[D][05:19:41][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:41][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:41][COMM]--->crc16:0xb8a
[D][05:19:41][COMM]read file succes

2025-07-31 21:00:07:089 ==>> s
[D][05:19:41][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:19:41][COMM][Audio].l:[936].close hexlog save
[D][05:19:41][COMM]accel parse set 1
[D][05:19:41][COMM][Audio]mon:9,05:19:41
[D][05:19:41][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:41][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:41][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:41][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:41][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:41][COMM]f:[ec800m_audio_send_hexdata_

2025-07-31 21:00:07:194 ==>> start].l:[756].recv >
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:41][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:41][COMM]arm_hub_enable: hub power: 0
[D][05:19:41][HSDK]hexlog index save 0 6656 6 @ 0 : 0
[D][05:19:41][HSDK]write save hexlog index [0]
[D][05:19:41][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:41][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:41][COMM]read battery s

2025-07-31 21:00:07:254 ==>> oc:255
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:41][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 21:00:07:359 ==>>                               [W][05:19:42][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:42][COMM]arm_hub_enable: hub power: 0
[D][05:19:42][HSDK]hexlog index save 0 6656 6 @ 0 : 0
[D][05:19:42][HSDK]write save hexlog index [0

2025-07-31 21:00:07:389 ==>> ]
[D][05:19:42][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:42][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 21:00:07:479 ==>> [D][05:19:43][COMM]exit wheel stolen mode.


2025-07-31 21:00:07:584 ==>> [D][05:19:43][COMM]Main Task receive event:68
[D][05:19:43][COMM]

2025-07-31 21:00:07:629 ==>> handlerWheelStolen evt type = 2.
[E][05:19:43][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:43][GNSS]stop locating
[D][05:19:43][GNSS]all continue location stop
[D][05:19:43][COMM]Main Task receive event:68 finished processing


2025-07-31 21:00:07:734 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:00:07:827 ==>> [W][05:19:43][COMM]Power Off


2025-07-31 21:00:07:992 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:43][COMM]arm_hub_enable: hub power: 0
[D][05:19:43][HSDK]hexlog index save 0 6656 6 @ 0 : 0
[D][05:19:43][HSDK]write save hexlog index [0]
[D][05:19:43][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:43][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 21:00:08:032 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 21:00:08:042 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 21:00:08:051 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:00:08:157 ==>> 5A A5 02 5A A5 


2025-07-31 21:00:08:262 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:00:08:324 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:00:08:332 ==>> 检测【检测小电池关机电流】
2025-07-31 21:00:08:345 ==>> 开始小电池电流采样
2025-07-31 21:00:08:374 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:00:08:427 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:00:09:432 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:00:09:492 ==>> CurrentBattery:ƽ��:68.50

2025-07-31 21:00:09:942 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:00:09:951 ==>> 【检测小电池关机电流】通过,【68.5uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 21:00:10:234 ==>> ?

2025-07-31 21:00:10:610 ==>> MES过站成功
2025-07-31 21:00:10:619 ==>> #################### 【测试结束】 ####################
2025-07-31 21:00:10:668 ==>> 关闭5V供电
2025-07-31 21:00:10:682 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:00:10:769 ==>> 5A A5 04 5A A5 


2025-07-31 21:00:10:859 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:00:11:677 ==>> 关闭5V供电成功
2025-07-31 21:00:11:691 ==>> 关闭33V供电
2025-07-31 21:00:11:706 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:00:11:755 ==>> 5A A5 02 5A A5 


2025-07-31 21:00:11:860 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:00:12:681 ==>> 关闭33V供电成功
2025-07-31 21:00:12:697 ==>> 关闭3.7V供电
2025-07-31 21:00:12:705 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:00:12:758 ==>> 6A A6 02 A6 6A 


2025-07-31 21:00:12:863 ==>> Battery OFF
OVER 150


2025-07-31 21:00:13:619 ==>>  

2025-07-31 21:00:13:694 ==>> 关闭3.7V供电成功
