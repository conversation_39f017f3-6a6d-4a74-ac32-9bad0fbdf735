2025-07-31 21:37:38:183 ==>> MES查站成功:
查站序号:P510001005312867验证通过
2025-07-31 21:37:38:188 ==>> 扫码结果:P510001005312867
2025-07-31 21:37:38:189 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:37:38:191 ==>> 测试参数版本:2024.10.11
2025-07-31 21:37:38:192 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:37:38:193 ==>> 检测【打开透传】
2025-07-31 21:37:38:196 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:37:38:285 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:37:38:623 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:37:38:632 ==>> 检测【检测接地电压】
2025-07-31 21:37:38:634 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:37:38:684 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 21:37:38:929 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:37:38:932 ==>> 检测【打开小电池】
2025-07-31 21:37:38:935 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:37:38:975 ==>> 6A A6 01 A6 6A 


2025-07-31 21:37:39:080 ==>> Battery ON
OVER 150


2025-07-31 21:37:39:140 ==>>  

2025-07-31 21:37:39:221 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:37:39:223 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:37:39:224 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:37:39:275 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:37:39:503 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:37:39:508 ==>> 检测【等待设备启动】
2025-07-31 21:37:39:514 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:37:39:831 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:37:40:029 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:37:40:539 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:37:40:663 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:37:40:723 ==>>                                                    

2025-07-31 21:37:41:120 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:37:41:579 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:37:41:594 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:37:41:884 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:37:41:886 ==>> 检测【产品通信】
2025-07-31 21:37:41:887 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:37:42:037 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:37:42:172 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:37:42:175 ==>> 检测【初始化完成检测】
2025-07-31 21:37:42:182 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:37:42:237 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:37:42:402 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:37:42:462 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:37:42:465 ==>> 检测【关闭大灯控制1】
2025-07-31 21:37:42:466 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:37:42:674 ==>> [D][05:17:51][COMM]2627 imu init OK
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:37:42:766 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:37:42:773 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:37:42:779 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:37:42:785 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1

2025-07-31 21:37:42:824 ==>> ],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:37:42:914 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:37:43:057 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:37:43:064 ==>> 检测【关闭仪表供电】
2025-07-31 21:37:43:068 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:37:43:277 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:37:43:362 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:37:43:366 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:37:43:372 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:37:43:536 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:37:43:642 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:

2025-07-31 21:37:43:670 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:37:43:675 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:37:43:680 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:37:43:683 ==>> [-1]. goto init


2025-07-31 21:37:43:836 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:37:43:959 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:37:43:965 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:37:43:969 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:37:44:157 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:37:44:248 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:37:44:253 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:37:44:257 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:37:44:383 ==>> 5A A5 01 5A A5 


2025-07-31 21:37:44:473 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:37:44:537 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:37:44:542 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:37:44:546 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:37:44:550 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 21:37:44:698 ==>> [D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:37:44:828 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:37:44:850 ==>> 该项需要延时执行
2025-07-31 21:37:45:203 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5004. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5004. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5005. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5005. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5005. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5006. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5006. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5007. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5007. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5008. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5008. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5009
[D][05:17:53][COMM]CAN message bat fault ch

2025-07-31 21:37:45:233 ==>> ange: 0x0001802E->0x01B987FE 5009


2025-07-31 21:37:45:680 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:37:46:138 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:37:46:640 ==>>        17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:1

2025-07-31 21:37:46:745 ==>> 7:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][P

2025-07-31 21:37:46:850 ==>> ROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55]

2025-07-31 21:37:46:910 ==>> [COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:37:46:940 ==>>                                                                                                  

2025-07-31 21:37:47:710 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:37:48:560 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:37:48:725 ==>> [D][05:17:57][COMM]8698 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:37:48:830 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:37:48:835 ==>> 检测【33V输入电压ADC】
2025-07-31 21:37:48:839 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:37:49:091 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:17:57][COMM]adc read out 24v adc:1314  volt:33234 mv
[D][05:17:57][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read right brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:11 mv
[D][05:17:57][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 21:37:49:388 ==>> 【33V输入电压ADC】通过,【32779mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:37:49:390 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:37:49:393 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:37:49:488 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:37:49:675 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:49:682 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:37:49:709 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:49:716 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:37:49:723 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 21:37:49:728 ==>> [D][05:17:58][COMM]9709 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:37:49:743 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:37:49:750 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:37:49:775 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:37:49:778 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:37:49:813 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:37:49:815 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:37:49:884 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 1995mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:37:50:094 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10013. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10013
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10014


2025-07-31 21:37:50:103 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:50:105 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:37:50:136 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:50:156 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:37:50:160 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 21:37:50:209 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:37:50:212 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:37:50:312 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:37:50:316 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:37:50:350 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:37:50:353 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:37:50:491 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1990mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:37:50:566 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 21:37:50:646 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:50:648 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:37:50:676 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:50:678 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:37:50:689 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 21:37:50:706 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:37:50:709 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:37:50:737 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:37:50:739 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:37:50:774 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:37:50:782 ==>> 检测【打开WIFI(1)】
2025-07-31 21:37:50:785 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:37:51:023 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10719 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M

2025-07-31 21:37:51:068 ==>>  ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:37:51:331 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:37:51:336 ==>> 检测【清空消息队列(1)】
2025-07-31 21:37:51:340 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:37:51:675 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088008802

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541621

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:00][CAT1]tx ret[14] >>> A

2025-07-31 21:37:51:735 ==>> T+QSCLKEX=1

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

                                         

2025-07-31 21:37:51:916 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:37:51:919 ==>> 检测【打开GPS(1)】
2025-07-31 21:37:51:922 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:37:52:069 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 21:37:52:233 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:37:52:236 ==>> 检测【打开GSM联网】
2025-07-31 21:37:52:238 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:37:52:452 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:37:52:557 ==>> [D][05:18:01][COMM]read battery soc:255

2025-07-31 21:37:52:587 ==>> 


2025-07-31 21:37:52:595 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:37:52:598 ==>> 检测【打开仪表供电1】
2025-07-31 21:37:52:601 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:37:52:767 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:37:52:891 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:37:52:893 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:37:52:897 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:37:53:114 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:37:53:176 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:37:53:179 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:37:53:181 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:37:53:373 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33363]


2025-07-31 21:37:53:470 ==>> 【读取主控ADC采集的仪表电压】通过,【33363mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:37:53:473 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:37:53:477 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:54:044 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:

2025-07-31 21:37:54:150 ==>> 18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.111.205.187"

OK

[D][05:18:02][COMM]13733 imu init OK
[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][COMM]S->M yaw

2025-07-31 21:37:54:254 ==>> :INVALID
[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1

                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 21:37:54:279 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:37:54:283 ==>> 检测【AD_V20电压】
2025-07-31 21:37:54:287 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:54:390 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:54:480 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:37:54:586 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 21:37:54:661 ==>> 本次取值间隔时间:262ms
2025-07-31 21:37:54:697 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:54:736 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:37:54:811 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:54:916 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:37:55:050 ==>> 本次取值间隔时间:235ms
2025-07-31 21:37:55:085 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:55:190 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:55:282 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:37:55:387 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:37:55:599 ==>> [D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,59,,,44,33,,,43,25,,,42,24,,,40,1*75

$GBGSV,3,2,09,34,,,37,14,,,19,7,,,37,16,,,37,1*40

$GBGSV,3,3,09,23,,,37,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1554.971,1554.971,50.019,2097152,2097152,2097152*42

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 21:37:55:688 ==>> 本次取值间隔时间:494ms
2025-07-31 21:37:55:703 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:37:55:730 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:55:839 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:55:869 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:37:55:914 ==>> 本次取值间隔时间:68ms
2025-07-31 21:37:55:973 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:37:56:064 ==>> 本次取值间隔时间:150ms
2025-07-31 21:37:56:099 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:56:203 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:56:279 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:37:56:539 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,59,,,42,33,,,42,25,,,41,60,,,41,1*7A

$GBGSV,4,2,15,24,,,40,7,,,38,23,,,38,14,,,38,1*4E

$GBGSV,4,3,15,16,,,37,34,,,37,2,,,36,4,,,35,1*70

$GBGSV,4,4,15,44,,,33,41,,,15,40,,,41,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1519.335,1519.335,48.783,2097152,2097152,2097152*4F



2025-07-31 21:37:56:584 ==>>                                          

2025-07-31 21:37:56:614 ==>> 本次取值间隔时间:400ms
2025-07-31 21:37:56:645 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:56:750 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:56:983 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:37:57:058 ==>> 本次取值间隔时间:295ms
2025-07-31 21:37:57:134 ==>> 本次取值间隔时间:62ms
2025-07-31 21:37:57:518 ==>> 本次取值间隔时间:379ms
2025-07-31 21:37:57:593 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,25,,,42,59,,,41,60,,,40,1*76

$GBGSV,5,2,19,24,,,40,39,,,40,40,,,39,1,,,39,1*40

$GBGSV,5,3,19,41,,,39,7,,,38,23,,,38,14,,,38,1*4F

$GBGSV,5,4,19,34,,,38,16,,,37,2,,,36,44,,,35,1*41

$GBGSV,5,5,19,4,,,34,38,,,34,5,,,33,1*74

$GBRMC,,V,,,,,,,310725,0.1,E,N,V*53

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1577.581,1577.581,50.450,2097152,2097152,2097152*4B



2025-07-31 21:37:57:729 ==>> 本次取值间隔时间:204ms
2025-07-31 21:37:57:733 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:37:57:834 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:37:57:852 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:37:57:955 ==>> 1

2025-07-31 21:37:57:985 ==>> A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 21:37:58:090 ==>> 本次取值间隔时间:243ms
2025-07-31 21:37:58:124 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:37:58:127 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:37:58:129 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:37:58:182 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:37:58:418 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:37:58:421 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:37:58:425 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:37:58:672 ==>> $GBGGA,133802.393,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,25,,,42,60,,,41,3,,,41,1*44

$GBGSV,7,2,25,59,,,41,40,,,40,24,,,40,39,,,40,1*71

$GBGSV,7,3,25,1,,,39,41,,,39,7,,,38,34,,,38,1*71

$GBGSV,7,4,25,14,,,38,23,,,38,16,,,37,2,,,36,1*42

$GBGSV,7,5,25,44,,,36,6,,,35,38,,,34,12,,,34,1*4E

$GBGSV,7,6,25,4,,,34,5,,,33,13,,,31,10,,,6,1*41

$GBGSV,7,7,25,9,,,40,1*4C

$GBRMC,133802.393,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133802.393,0.000,753.686,753.686,689.281,2097152,2097152,2097152*6F

[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:37:58:778 ==>>                                                                                                                                                                                                                      ,,,38,34,,,38,1*7D

$GBGSV,7,4,27,14,,,38,23,,,38,16,,,37,2,,,36,1*40

$GBGSV,7,5,27,10,,,36,9,,,36,44,,,36,6,,,35,1*7C

$GBGSV,7,6,27,38,,,34,4,,,34,12,,,34,5,,,33,1*7C

$GBGSV,7,7,27,13,,,31,8,,,6,42,,,47,1*78

$GBRMC,133802.593,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0

2025-07-31 21:37:58:808 ==>> .000,N,0.000,K,N*20

$GBGST,133802.593,0.000,753.008,753.008,688.661,2097152,2097152,2097152*62



2025-07-31 21:37:58:960 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:37:58:963 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:37:58:969 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:37:59:079 ==>> 3A A3 02 01 A3 


2025-07-31 21:37:59:184 ==>> ON_OUT2
OVER 150


2025-07-31 21:37:59:249 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:37:59:252 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:37:59:254 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:37:59:488 ==>> [D][05:18:08][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:37:59:538 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:37:59:541 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:37:59:544 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:37:59:823 ==>> $GBGGA,133803.573,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,25,,,42,33,,,42,3,,,41,60,,,41,1*47

$GBGSV,7,2,27,59,,,41,40,,,40,39,,,40,24,,,40,1*73

$GBGSV,7,3,27,41,,,40,1,,,39,7,,,38,34,,,38,1*7D

$GBGSV,7,4,27,14,,,38,23,,,38,16,,,37,44,,,37,1*73

$GBGSV,7,5,27,2,,,36,10,,,36,9,,,36,6,,,35,1*4E

$GBGSV,7,6,27,12,,,35,38,,,34,4,,,34,11,,,34,1*4F

$GBGSV,7,7,27,5,,,33,13,,,33,8,,,32,1*7D

$GBRMC,133803.573,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133803.573,0.000,773.977,773.977,707.819,2097152,2097152,2097152*6A

[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:38:00:087 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:38:00:091 ==>> 检测【AD_V21电压】
2025-07-31 21:38:00:093 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:38:00:176 ==>> 1A A1 20 00 00 
Get AD_V21 1044mV
OVER 150


2025-07-31 21:38:00:221 ==>> 本次取值间隔时间:129ms
2025-07-31 21:38:00:261 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:38:00:358 ==>> 本次取值间隔时间:85ms
2025-07-31 21:38:00:388 ==>> 1A A1 20 00 00 
Get AD_V21 1644mV
OVER 150


2025-07-31 21:38:00:599 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:38:00:749 ==>> 本次取值间隔时间:389ms
2025-07-31 21:38:00:780 ==>> 【AD_V21电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:38:00:784 ==>> 检测【关闭仪表供电2】
2025-07-31 21:38:00:787 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:38:00:794 ==>> $GBGGA,133804.553,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,41,59,,,41,25,,,41,1*4F

$GBGSV,8,2,29,60,,,40,39,,,40,40,,,39,1,,,39,1*4E

$GBGSV,8,3,29,24,,,39,41,,,39,7,,,38,34,,,38,1*45

$GBGSV,8,4,29,14,,,38,23,,,38,16,,,37,44,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,12,,,36,10,,,35,1*7A

$GBGSV,8,6,29,6,,,35,38,,,34,4,,,34,11,,,34,1*7B

$GBGSV,8,7,29,5,,,33,13,,,33,8,,,32,42,,,32,1*7B

$GBGSV,8,8,29,43,,,28,1*70

$GBRMC,133804.553,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133804.553,0.000,759.858,759.858,694.909,2097152,2097152,2097152*64



2025-07-31 21:38:00:975 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:38:01:064 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:38:01:068 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:38:01:071 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:38:01:264 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:38:01:358 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:38:01:361 ==>> 检测【打开AccKey2供电】
2025-07-31 21:38:01:365 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:38:01:538 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:38:01:646 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:38:01:651 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:38:01:654 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:01:748 ==>> $GBGGA,133805.533,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,41,60,,,41,59,,,41,1*4E

$GBGSV,8,2,29,25,,,41,24,,,40,39,,,40,40,,,39,1*77

$GBGSV,8,3,29,1,,,39,41,,,39,7,,,38,16,,,38,1*72

$GBGSV,8,4,29,34,,,38,14,,,38,23,,,38,44,,,37,1*7D

$GBGSV,8,5,29,2,,,36,9,,,36,12,,,36,10,,,35,1*7A

$GBGSV,8,6,29,6,,,35,13,,,34,5,,,34,38,,,34,1*78

$GBGSV,8,7,29,4,,,34,11,,,34,42,,,33,8,,,32,1*79

$GBGSV,8,8,29,43,,,29,1*71

$GBRMC,133805.533,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133805.533,0.000,764.845,764.845,699.469,2097152,2097152,2097152*65



2025-07-31 21:38:01:990 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:18:10][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:18:10][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:10][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:10][COMM]adc read battery ts volt:14 mv
[D][05:18:10][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:10][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:38:02:191 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33083mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:38:02:194 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:38:02:196 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:38:02:345 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:38:02:487 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:38:02:490 ==>> 该项需要延时执行
2025-07-31 21:38:02:750 ==>> [D][05:18:11][COMM]read battery soc:255
$GBGGA,133806.513,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,60,,,41,59,,,41,1*4F

$GBGSV,8,2,29,25,,,41,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,1,,,39,41,,,39,7,,,38,34,,,38,1*72

$GBGSV,8,4,29,14,,,38,23,,,38,16,,,37,44,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,6,,,36,12,,,36,1*4E

$GBGSV,8,6,29,10,,,35,5,,,34,13,,,34,38,,,34,1*4F

$GBGSV,8,7,29,4,,,34,42,,,34,11,,,34,8,,,32,1*7E

$GBGSV,8,8,29,43,,,29,1*71

$GBRMC,133806.513,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133806.513,0.000,766.986,766.986,701.428,2097152,2097152,2097152*61



2025-07-31 21:38:03:757 ==>> $GBGGA,133807.513,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,25,,,41,1*4E

$GBGSV,8,2,29,60,,,40,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,34,,,38,1*7C

$GBGSV,8,4,29,14,,,38,23,,,38,44,,,37,16,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,6,,,36,12,,,36,1*4E

$GBGSV,8,6,29,10,,,35,5,,,34,13,,,34,38,,,34,1*4F

$GBGSV,8,7,29,4,,,34,42,,,34,11,,,34,8,,,31,1*7D

$GBGSV,8,8,29,43,,,30,1*79

$GBRMC,133807.513,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133807.513,0.000,766.985,766.985,701.426,2097152,2097152,2097152*6E



2025-07-31 21:38:04:742 ==>> [D][05:18:13][COMM]read battery soc:255
$GBGGA,133808.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,25,,,42,3,,,41,59,,,41,1*4D

$GBGSV,8,2,29,60,,,40,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,16,,,38,1*7C

$GBGSV,8,4,29,34,,,38,14,,,38,23,,,38,2,,,37,1*4F

$GBGSV,8,5,29,44,,,37,9,,,36,6,,,36,12,,,36,1*7D

$GBGSV,8,6,29,10,,,35,42,,,35,5,,,34,13,,,34,1*43

$GBGSV,8,7,29,38,,,34,4,,,34,11,,,34,8,,,32,1*73

$GBGSV,8,8,29,43,,,30,1*79

$GBRMC,133808.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133808.513,0.000,770.549,770.549,704.685,2097152,2097152,2097152*6F



2025-07-31 21:38:05:491 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:38:05:497 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:38:05:524 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:05:839 ==>> $GBGGA,133809.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,25,,,41,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,44,,,38,1*7B

$GBGSV,8,4,29,16,,,38,34,,,38,14,,,38,23,,,38,1*75

$GBGSV,8,5,29,2,,,37,9,,,36,12,,,36,6,,,36,1*4F

$GBGSV,8,6,29,10,,,35,38,,,35,42,,,35,11,,,35,1*7F

$GBGSV,8,7,29,5,,,34,13,,,34,4,,,34,8,,,32,1*4F

$GBGSV,8,8,29,43,,,30,1*79

$GBRMC,133809.513,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133809.513,0.000,772.686,772.686,706.639,2097152,2097152,2097152*6B

[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:14][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:14][COMM]adc read battery ts volt:17 mv
[D][05:18:14][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id ad

2025-07-31 21:38:05:884 ==>> c:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:38:06:039 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:38:06:045 ==>> 检测【打开AccKey1供电】
2025-07-31 21:38:06:049 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:38:06:269 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:38:06:332 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:38:06:336 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:38:06:338 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:38:06:374 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 21:38:06:585 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 21:38:06:619 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:38:06:625 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:38:06:630 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:06:735 ==>> [D][05:18:15][COMM]read battery soc:255
$GBGGA,133810.513,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,59,,,42,25,,,42,33,,,42,60,,,41,1*7A

$GBGSV,8,2,29,3,,,41,40,,,40,24,,,40,39,,,40,1*4D

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,44,,,38,1*7B

$GBGSV,8,4,29,16,,,38,34,,,38,14,,,38,23,,,38,1*75

$GBGSV,8,5,29,2,,,37,9,,,36,12,,,36,6,,,36,1*4F

$GBGSV,8,6,29,10,,,35,42,,,35,4,,,35,11,,,35,1*40

$GBGSV,8,7,29,5,,,34,13,,,34,38,,,34,8,,,32,1*70

$GBGSV,8,8,29,43,,,30,1*79

$GBRMC,133810.513,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133810.513,0.000,773.401,773.401,707.293,2097152,2097152,2097152*66



2025-07-31 21:38:06:945 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:15][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:15][COMM]adc read battery ts volt:17 mv
[D][05:18:15][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:15][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:38:07:664 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5517mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:38:07:691 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:38:07:695 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:38:07:732 ==>> $GBGGA,133811.513,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,41,59,,,41,25,,,41,1*4F

$GBGSV,8,2,29,60,,,40,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,16,,,38,1*7C

$GBGSV,8,4,29,34,,,38,14,,,38,23,,,38,2,,,37,1*4F

$GBGSV,8,5,29,44,,,37,9,,,36,12,,,36,6,,,36,1*7D

$GBGSV,8,6,29,10,,,35,42,,,35,11,,,35,5,,,34,1*40

$GBGSV,8,7,29,13,,,34,38,,,34,4,,,34,8,,,32,1*71

$GBGSV,8,8,29,43,,,30,1*79

$GBRMC,133811.513,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133811.513,0.000,769.829,769.829,704.026,2097152,2097152,2097152*68



2025-07-31 21:38:07:837 ==>> [D][05:18:16][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:38:07:964 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:38:07:974 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:38:07:989 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:38:08:080 ==>> 1A A1 00 40 00 
Get AD_V14 2653mV
OVER 150


2025-07-31 21:38:08:215 ==>> 原始值:【2653】, 乘以分压基数【2】还原值:【5306】
2025-07-31 21:38:08:260 ==>> 【读取AccKey1电压(ADV14)后】通过,【5306mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:38:08:267 ==>> 检测【打开WIFI(2)】
2025-07-31 21:38:08:283 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:38:08:491 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:38:08:563 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:38:08:567 ==>> 检测【转刹把供电】
2025-07-31 21:38:08:570 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:38:08:732 ==>> [D][05:18:17][COMM]read battery soc:255
$GBGGA,133812.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,25,,,42,33,,,42,3,,,41,59,,,41,1*4C

$GBGSV,8,2,29,60,,,40,24,,,40,39,,,40,41,,,40,1*78

$GBGSV,8,3,29,40,,,39,1,,,39,7,,,38,34,,,38,1*73

$GBGSV,8,4,29,14,,,38,23,,,38,44,,,37,16,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,12,,,36,6,,,36,1*4E

$GBGSV,8,6,29,10,,,35,42,,,35,5,,,34,13,,,34,1*43

$GBGSV,8,7,29,38,,,34,4,,,34,11,,,34,8,,,32,1*73

$GBGSV,8,8,29,43,,,29,1*71

$GBRMC,133812.513,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133812.513,0.000,766.983,766.983,701.424,2097152,2097152,2097152*68



2025-07-31 21:38:08:806 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:38:08:864 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:38:08:868 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:38:08:872 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:38:08:971 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:38:09:076 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2395mV
OVER 150


2025-07-31 21:38:09:136 ==>> 原始值:【2395】, 乘以分压基数【2】还原值:【4790】
2025-07-31 21:38:09:181 ==>> 【读取AD_V15电压(前)】通过,【4790mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:38:09:185 ==>> +WIFISCAN:4,0,CC057790A5C1,-77
+WIFISCAN:4,1,44A1917CA62B,-77
+WIFISCAN:4,2,CC057790A7C1,-77
+WIFISCAN:4,3,CC057790A7C0,-79

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 21:38:09:190 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:38:09:213 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:38:09:286 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:38:09:346 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:38:09:376 ==>> 1A A1 01 00 00 
Get AD_V16 2427mV
OVER 150


2025-07-31 21:38:09:451 ==>> 原始值:【2427】, 乘以分压基数【2】还原值:【4854】
2025-07-31 21:38:09:488 ==>> 【读取AD_V16电压(前)】通过,【4854mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:38:09:491 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:38:09:495 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:09:861 ==>> $GBGGA,133813.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,59,,,41,25,,,41,60,,,40,1*7B

$GBGSV,8,2,29,3,,,40,40,,,40,24,,,40,39,,,40,1*4C

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,34,,,38,1*7C

$GBGSV,8,4,29,14,,,38,23,,,38,44,,,37,16,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,12,,,36,6,,,36,1*4E

$GBGSV,8,6,29,10,,,35,42,,,35,5,,,34,13,,,34,1*43

$GBGSV,8,7,29,38,,,34,4,,,34,11,,,34,8,,,31,1*70

$GBGSV,8,8,29,43,,,28,1*70

$GBRMC,133813.513,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133813.513,0.000,764.847,764.847,699.471,2097152,2097152,2097152*69

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:18][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:18][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:18][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:18][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:18][COMM]adc read battery ts volt:9 mv
[D][05:18:18][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3083  volt:5419 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 m

2025-07-31 21:38:09:907 ==>> v
[D][05:18:18][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 21:38:10:028 ==>> 【转刹把供电电压(主控ADC)】通过,【5419mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:38:10:032 ==>> 检测【转刹把供电电压】
2025-07-31 21:38:10:038 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:10:292 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:19][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:19][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:19][COMM]adc read battery ts volt:16 mv
[D][05:18:19][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3077  volt:5408 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1438  volt:33340 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:38:10:585 ==>> 【转刹把供电电压】通过,【5408mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:38:10:589 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:38:10:592 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:10:822 ==>> [D][05:18:19][COMM]read battery soc:255
$GBGGA,133814.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,41,59,,,41,25,,,41,1*4F

$GBGSV,8,2,29,60,,,40,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,34,,,38,1*7C

$GBGSV,8,4,29,14,,,38,23,,,38,44,,,37,16,,,37,1*72

$GBGSV,8,5,29,2,,,36,9,,,36,12,,,36,6,,,36,1*4E

$GBGSV,8,6,29,10,,,35,42,,,35,5,,,34,13,,,34,1*43

$GBGSV,8,7,29,38,,,34,4,,,34,11,,,34,8,,,31,1*70

$GBGSV,8,8,29,43,,,28,1*70

$GBRMC,133814.513,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133814.513,0.000,765.562,765.562,700.126,2097152,2097152,2097152*68

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:38:10:926 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:38:10:930 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:38:10:933 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:11:035 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:38:11:065 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:38:11:140 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:11:248 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:38:11:357 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:11:435 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:38:11:465 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:38:11:540 ==>> [W][05:18:20][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:38:11:570 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:11:585 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:38:11:675 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:38:11:690 ==>> $GBGGA,133815.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,8,2,29,25,,,41,40,,,40,24,,,40,1,,,40,1*42

$GBGSV,8,3,29,39,,,40,41,,,40,7,,,38,34,,,38,1*49

$GBGSV,8,4,29,14,,,38,23,,,38,2,,,37,44,,,37,1*47

$GBGSV,8,5,29,16,,,37,10,,,36,9,,,36,12,,,36,1*4D

$GBGSV,8,6,29,6,,,36,38,,,35,42,,,35,5,,,34,1*7F

$GBGSV,8,7,29,13,,

2025-07-31 21:38:11:735 ==>> ,34,4,,,34,11,,,34,8,,,31,1*79

$GBGSV,8,8,29,43,,,28,1*70

$GBRMC,133815.513,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133815.513,0.000,769.130,769.130,703.388,2097152,2097152,2097152*6C



2025-07-31 21:38:11:810 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:38:11:839 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:38:11:846 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:38:11:850 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:11:946 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:38:12:054 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:12:144 ==>> [W][05:18:21][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:38:12:159 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:38:12:249 ==>> [W][05:18:21][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:38:12:264 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:38:12:279 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:38:12:369 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:38:12:444 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:38:12:474 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:38:12:523 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:38:12:552 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:38:12:556 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:38:12:579 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:38:12:684 ==>> $GBGGA,133816.513,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,60,,,41,3,,,41,59,,,41,1*4E

$GBGSV,8,2,29,25,,,41,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,1,,,39,41,,,39,7,,,38,16,,,38,1*72

$GBGSV,8,4,29,34,,,38,14,,,38,23,,,38,2,,,37,1*4F

$GBGSV,8,5,29,44,,,37,10,,,36,9,,,36,6,,,36,1*7F

$GB

2025-07-31 21:38:12:729 ==>> GSV,8,6,29,12,,,36,42,,,36,38,,,35,11,,,35,1*7D

$GBGSV,8,7,29,5,,,34,13,,,34,4,,,34,8,,,32,1*4F

$GBGSV,8,8,29,43,,,27,1*7F

$GBRMC,133816.513,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133816.513,0.000,769.841,769.841,704.038,2097152,2097152,2097152*60

[D][05:18:21][COMM]read battery soc:255


2025-07-31 21:38:12:815 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:38:12:835 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:38:12:839 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:38:12:879 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:38:13:106 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:38:13:125 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:38:13:133 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:38:13:183 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:38:13:396 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:38:13:401 ==>> 检测【左刹电压测试1】
2025-07-31 21:38:13:408 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:13:765 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3131  volt:5503 mv
[D][05:18:22][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:22][COMM]adc read left brake adc:1730  volt:2280 mv
[D][05:18:22][COMM]adc read right brake adc:1728  volt:2278 mv
[D][05:18:22][COMM]adc read throttle adc:1719  volt:2266 mv
[D][05:18:22][COMM]adc read battery ts volt:9 mv
[D][05:18:22][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:22][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,133817.513,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,59,,,42,33,,,42,3,,,41,25,,,41,1*4C

$GBGSV,8,2,29,60,,,40,40,,,40,24,,,40,39,,,40,1*79

$GBGSV,8,3,29,41,,,40,1,,,39,7,,,38,16,,,38,1*7C

$GBGSV,8,4,29,34,,,38,14,,,38,23,,,38,2,,,37,1*4F

$GBGSV,8,5,29,44,,,37,10,,,36,9,,,36,6,,,36,1*7F

$GBGSV,8,6,29,12,,,36,38,,,35,42,,,35,11,,,35,1*7E

$GBGSV,8,7,29,5,,

2025-07-31 21:38:13:810 ==>> ,34,13,,,34,4,,,34,8,,,31,1*4C

$GBGSV,8,8,29,43,,,27,1*7F

$GBRMC,133817.513,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,133817.513,0.000,769.133,769.133,703.391,2097152,2097152,2097152*66



2025-07-31 21:38:13:947 ==>> 【左刹电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 21:38:13:951 ==>> 检测【右刹电压测试1】
2025-07-31 21:38:13:982 ==>> 【右刹电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 21:38:13:986 ==>> 检测【转把电压测试1】
2025-07-31 21:38:14:013 ==>> 【转把电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 21:38:14:037 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:38:14:044 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:38:14:080 ==>> 3A A3 03 00 A3 


2025-07-31 21:38:14:185 ==>> OFF_OUT3
OVER 150


2025-07-31 21:38:14:310 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:38:14:337 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:38:14:343 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:38:14:381 ==>> 3A A3 04 00 A3 


2025-07-31 21:38:14:486 ==>> OFF_OUT4
OVER 150


2025-07-31 21:38:14:594 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:38:14:601 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:38:14:609 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:38:14:681 ==>> [D][05:18:23][COMM]read battery soc:255
3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:38:14:880 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:38:14:885 ==>> 检测【左刹电压测试2】
2025-07-31 21:38:14:891 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:15:666 ==>> $GBGGA,133814.520,2301.2584352,N,11421.9426234,E,1,12,0.78,85.030,M,-1.770,M,,*5F

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,59,52,129,42,7,51,183,38,40,50,163,40,1*46

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,37,1*43

$GBGSV,8,4,29,10,41,194,35,60,41,238,41,41,40,299,40,14,39,185,38,1*7F

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,5,22,256,34,1*40

$GBGSV,8,6,29,8,21,206,31,44,21,74,37,42,17,322,35,23,14,274,38,1*71

$GBGSV,8,7,29,13,13,206,34,38,12,197,35,12,,,36,11,,,34,1*71

$GBGSV,8,8,29,43,,,27,1*7F

$GBRMC,133814.520,A,2301.2584352,N,11421.9426234,E,0.004,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

[D][05:18:23][GNSS]HD8040 GPS
[D][05:18:23][GNSS]GPS diff_sec 124013991, report 0x42 frame
$GBGST,133814.520,1.785,0.213,0.212,0.313,2.388,2.923,9.709*79

[D][05:18:23][COMM]Main Task receive event:131
[D][05:18:23][COMM]index:0,power_mode:0xFF
[D][05:18:23][COMM]index:1,sound_mode:0xFF
[D][05:18:23][COMM]index:2,gsensor_mode:0xFF
[D][05:18:23][COMM]index:3,report_freq_mode:0xFF
[D][05:18:23][COMM]index:4,report_period:0xFF
[D][05:18:23][COMM]index:5,normal_

2025-07-31 21:38:15:771 ==>> reset_mode:0xFF
[D][05:18:23][COMM]index:6,normal_reset_period:0xFF
[D][05:18:23][COMM]index:7,spock_over_speed:0xFF
[D][05:18:23][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:23][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:23][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:23][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:23][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:23][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:23][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:23][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:23][COMM]index:16,imu_config_params:0xFF
[D][05:18:23][COMM]index:17,long_connect_params:0xFF
[D][05:18:23][COMM]index:18,detain_mark:0xFF
[D][05:18:23][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:23][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:23][COMM]index:21,mc_mode:0xFF
[D][05:18:23][COMM]index:22,S_mode:0xFF
[D][05:18:23][COMM]index:23,overweight:0xFF
[D][05:18:23][COMM]index:24,standstill_mode:0xFF
[D][05:18:23][COMM]index:25,night_mode:0xFF
[D][05:18:23][COMM]index:26,experiment1:0xFF
[D][05:18:23][COMM]index:27,experiment2:0xFF
[D][05:18:23][COMM]index:28,experiment3:0xFF
[D][05:18:23][COMM]index:29

2025-07-31 21:38:15:877 ==>> ,experiment4:0xFF
[D][05:18:23][COMM]index:30,night_mode_start:0xFF
[D][05:18:23][COMM]index:31,night_mode_end:0xFF
[D][05:18:23][COMM]index:33,park_report_minutes:0xFF
[D][05:18:23][COMM]index:34,park_report_mode:0xFF
[D][05:18:23][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:23][COMM]index:38,charge_battery_para: FF
[D][05:18:23][COMM]index:39,multirider_mode:0xFF
[D][05:18:23][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:23][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:23][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:23][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:23][COMM]index:44,riding_duration_config:0xFF
[D][05:18:23][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:23][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:23][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:23][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:23][COMM]index:49,mc_load_startup:0xFF
[D][05:18:23][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:23][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:23][COMM]index:52,traffic_mode:0xFF
[D][05:18:23][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:23][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:1

2025-07-31 21:38:15:906 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:38:15:981 ==>> 8:23][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:23][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:23][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:23][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:23][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:23][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:23][COMM]index:63,experiment5:0xFF
[D][05:18:23][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:23][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:23][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:23][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:23][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:23][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:23][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:23][COMM]index:72,experiment6:0xFF
[D][05:18:23][COMM]index:73,experiment7:0xFF
[D][05:18:23][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:23][COMM]index:75,zero_value_from_server:-1
[D][05:18:23][COMM]index:76,multirider_threshold:255
[D][05:18:23][COMM]index:77,experiment8:255
[D][05:18:23][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:23][COMM]index:79,temp_park_tail_light_t

2025-07-31 21:38:16:086 ==>> winkle_duration:255
[D][05:18:23][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:23][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:23][COMM]index:83,loc_report_interval:255
[D][05:18:23][COMM]index:84,multirider_threshold_p2:255
[D][05:18:23][COMM]index:85,multirider_strategy:255
[D][05:18:23][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:23][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:23][COMM]index:90,weight_param:0xFF
[D][05:18:23][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:23][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:23][COMM]index:95,current_limit:0xFF
[D][05:18:23][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:23][COMM]index:100,location_mode:0xFF

[W][05:18:23][PROT]remove success[1629955103],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:23][PROT]add success [1629955103],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:23][COMM]Main Task receive event:131 finished processing
[D][05:18:23][PROT]index:0 1629955103
[D][05:18:23][PROT]is_send:0
[D][05:18:23][PROT]sequence_num:4
[D][05:18:23][PROT]retry_timeout:0
[D][05:18:23][PROT]retry_time

2025-07-31 21:38:16:191 ==>> s:1
[D][05:18:23][PROT]send_path:0x2
[D][05:18:23][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:23][PROT]===========================================================
[W][05:18:23][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955103]
[D][05:18:23][PROT]===========================================================
[D][05:18:23][PROT]sending traceid [9999999999900005]
[D][05:18:23][PROT]Send_TO_M2M [1629955103]
[D][05:18:23][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:23][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:23][SAL ]sock send credit cnt[6]
[D][05:18:23][SAL ]sock send ind credit cnt[6]
[D][05:18:23][M2M ]m2m send data len[294]
[D][05:18:23][SAL ]Cellular task submsg id[10]
[D][05:18:23][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:23][CAT1]gsm read msg sub id: 15
[D][05:18:23][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:23][CAT1]Send Data To Server[294][294] ... ->:
0093B98A113311331133113311331B88B5D0465A606B05BE14C34CB108F0C49EDE48ED2E65596B800F438E6ED63ECD845FF1D0034C8C530D1273A57076C928

2025-07-31 21:38:16:296 ==>> 05CB7223FFCCFF1106957BEE7666059398CE290AB85B736909B44C44ABE4B7F5EFD1F39E3C533DFB3D4AF7848DEAF909FF36DD62E574F5DCA36E03B772BAC82EA331A51065F25B754817FCFF834B3809BF85EDE4
[D][05:18:23][CAT1]<<< 
SEND OK

[D][05:18:23][CAT1]exec over: func id: 15, ret: 11
[D][05:18:23][CAT1]sub id: 15, ret: 11

[D][05:18:23][SAL ]Cellular task submsg id[68]
[D][05:18:23][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:23][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:23][M2M ]g_m2m_is_idle become true
[D][05:18:23][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:23][PROT]M2M Send ok [1629955103]
[D][05:18:24][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:24][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:

2025-07-31 21:38:16:521 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          [W][05:18:25][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:25][COMM]adc read vcc5v mc adc:3135  volt:5510 mv
[D][05:18:25][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:

2025-07-31 21:38:16:596 ==>> 18:25][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:25][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:25][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:25][COMM]adc read battery ts volt:15 mv
[D][05:18:25][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:25][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:25][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:25][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:25][COMM]arm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:25][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:25][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:38:16:656 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:38:16:733 ==>> 【左刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 21:38:16:737 ==>> 检测【右刹电压测试2】
2025-07-31 21:38:16:789 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 21:38:16:794 ==>> 检测【转把电压测试2】
2025-07-31 21:38:16:841 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 21:38:16:847 ==>> 检测【晶振检测】
2025-07-31 21:38:16:868 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:38:17:039 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:26][COMM][lf state:1][hf state:1]


2025-07-31 21:38:17:152 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:38:17:158 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:38:17:175 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:38:17:355 ==>> $GBGGA,133817.000,2301.2585545,N,11421.9422211,E,1,12,0.78,80.368,M,-1.770,M,,*52

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,59,52,129,42,7,51,183,38,40,50,163,40,1*46

$GBGSV,8,3,29,1,48,126,39,25,47,319,42,9,47,333,37,2,45,237,37,1*41

$GBGSV,8,4,29,10,41,194,35,60,41,238,40,41,40,299,40,14,39,185,38,1*7E

$GBGSV,8,5,29,24,36,39,40,4,32,112,35,34,30,133,38,5,22,256,34,1*41

$GBGSV,8,6,29,8,21,206,31,44,21,74,38,42,17,322,35,23,14,274,38,1*7E

$GBGSV,8,7,29,13,13,206,35,38,12,197,34,12,,,36,11,,,34,1*71

$GBGSV,8,8,29,43,,,26,1*7E

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*41

$GBGSV,3,2,09,41,40,299,42,24,36,39,38,34,30,133,35,44,21,74,36,5*7B

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133817.000,A,2301.2585545,N,11421.9422211,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,133817.000,4.186,0.197,0.197,0.288,2.839,2.925,5.453*78

1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_

2025-07-31 21:38:17:385 ==>> V4 1650mV
Get AD_V5 2760mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:38:17:469 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:38:17:476 ==>> 检测【检测BootVer】
2025-07-31 21:38:17:482 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:38:17:839 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071541621
[D][05:18:26][FCTY]HardwareID  = 867222088008802
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E5CF4BC85780
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:26][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 101 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D]

2025-07-31 21:38:17:884 ==>> [05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3810 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:38:18:024 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:38:18:029 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:38:18:033 ==>> 检测【检测固件版本】
2025-07-31 21:38:18:055 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:38:18:059 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:38:18:080 ==>> 检测【检测蓝牙版本】
2025-07-31 21:38:18:086 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:38:18:094 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:38:18:113 ==>> 检测【检测MoBikeId】
2025-07-31 21:38:18:119 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:38:18:124 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:38:18:142 ==>> 检测【检测蓝牙地址】
2025-07-31 21:38:18:146 ==>> 取到目标值:E5CF4BC85780
2025-07-31 21:38:18:158 ==>> 【检测蓝牙地址】通过,【E5CF4BC85780】符合目标值【】要求!
2025-07-31 21:38:18:162 ==>> 提取到蓝牙地址:E5CF4BC85780
2025-07-31 21:38:18:166 ==>> 检测【BOARD_ID】
2025-07-31 21:38:18:188 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:38:18:192 ==>> 检测【检测充电电压】
2025-07-31 21:38:18:221 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:38:18:226 ==>> 检测【检测VBUS电压1】
2025-07-31 21:38:18:251 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:38:18:256 ==>> 检测【检测充电电流】
2025-07-31 21:38:18:284 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:38:18:290 ==>> 检测【检测IMEI】
2025-07-31 21:38:18:299 ==>> 取到目标值:867222088008802
2025-07-31 21:38:18:319 ==>> 【检测IMEI】通过,【867222088008802】符合目标值【】要求!
2025-07-31 21:38:18:326 ==>> 提取到IMEI:867222088008802
2025-07-31 21:38:18:346 ==>> 检测【检测IMSI】
2025-07-31 21:38:18:363 ==>> 取到目标值:460130071541621
2025-07-31 21:38:18:371 ==>> $GBGGA,133818.000,2301.2585391,N,11421.9421720,E,1,12,0.78,79.754,M,-1.770,M,,*5B

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,59,52,129,42,7,51,183,38,40,50,163,40,1*46

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,37,1*43

$GBGSV,8,4,29,10,41,194,35,60,41,238,40,41,40,299,40,14,39,185,38,1*7E

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,5,22,256,34,1*40

$GBGSV,8,6,29,8,21,206,31,44,21,74,37,42,17,322,35,23,14,274,38,1*71

$GBGSV,8,7,29,13,13,206,34,38,12,197,35,12,,,36,11,,,35,1*70

$GBGSV,8,8,29,43,,,27,1*7F

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*41

$GBGSV,3,2,09,41,40,299,42,24,36,39,39,34,30,133,35,44,21,74,36,5*7A

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133818.000,A,2301.2585391,N,11421.9421720,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,133818.000,4.222,0.192,0.191,0.283,2.824,2.892,5.072*74



2025-07-31 21:38:18:376 ==>> 【检测IMSI】通过,【460130071541621】符合目标值【】要求!
2025-07-31 21:38:18:391 ==>> 提取到IMSI:460130071541621
2025-07-31 21:38:18:395 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:38:18:399 ==>> 取到目标值:460130071541621
2025-07-31 21:38:18:415 ==>> 【校验网络运营商(移动)】通过,【460130071541621】符合目标值【】要求!
2025-07-31 21:38:18:422 ==>> 检测【打开CAN通信】
2025-07-31 21:38:18:436 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:38:18:462 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:38:18:657 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 21:38:18:709 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:38:18:718 ==>> 检测【检测CAN通信】
2025-07-31 21:38:18:723 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:38:18:762 ==>> can send success


2025-07-31 21:38:18:807 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:38:18:867 ==>> [D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38832
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:38:18:927 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:38:18:987 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:38:19:009 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:38:19:033 ==>> 检测【关闭CAN通信】
2025-07-31 21:38:19:041 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:38:19:064 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:38:19:077 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:38:19:324 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:38:19:333 ==>> 检测【打印IMU STATE】
2025-07-31 21:38:19:339 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:38:19:365 ==>> $GBGGA,133819.000,2301.2585630,N,11421.9421251,E,1,12,0.78,79.715,M,-1.770,M,,*52

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,59,52,129,41,7,51,183,38,40,50,163,40,1*45

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,37,1*43

$GBGSV,8,4,29,10,41,194,35,60,41,238,41,41,40,299,40,14,39,185,38,1*7F

$GBGSV,8,5,29,24,36,39,39,4,32,112,34,34,30,133,38,5,22,256,34,1*4E

$GBGSV,8,6,29,8,21,206,31,44,21,74,37,11,19,156,34,42,17,322,35,1*72

$GBGSV,8,7,29,23,14,274,38,13,13,206,34,38,12,197,34,12,,,36,1*49

$GBGSV,8,8,29,43,,,27,1*7F

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*41

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*77

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133819.000,A,2301.2585630,N,11421.9421251,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,133819.000,4.025,0.291,0.289,0.436,2.708,2.767,4.733*75



2025-07-31 21:38:19:470 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:38:19:633 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:38:19:639 ==>> 检测【六轴自检】
2025-07-31 21:38:19:667 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:38:19:876 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:38:20:419 ==>> $GBGGA,133820.000,2301.2585437,N,11421.9421042,E,1,12,0.78,79.803,M,-1.770,M,,*55

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,59,52,129,42,7,51,183,38,40,50,163,40,1*46

$GBGSV,8,3,29,1,48,126,39,25,47,319,42,9,47,333,36,2,45,237,37,1*40

$GBGSV,8,4,29,10,41,194,36,60,41,238,41,41,40,299,39,14,39,185,38,1*72

$GBGSV,8,5,29,24,36,39,40,4,32,112,35,34,30,133,38,5,22,256,34,1*41

$GBGSV,8,6,29,8,21,206,31,44,21,74,37,11,19,156,34,42,17,322,35,1*72

$GBGSV,8,7,29,23,14,274,38,13,13,206,35,38,12,197,35,12,,,36,1*49

$GBGSV,8,8,29,43,,,28,1*70

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*40

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*77

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133820.000,A,2301.2585437,N,11421.9421042,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,133820.000,4.209,0.228,0.226,0.340,2.783,2.832,4.604*7B



2025-07-31 21:38:20:659 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 21:38:21:359 ==>> $GBGGA,133821.000,2301.2585304,N,11421.9420841,E,1,12,0.78,79.683,M,-1.770,M,,*5F

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,59,52,129,41,7,51,183,38,40,50,163,40,1*45

$GBGSV,8,3,29,1,48,126,39,25,47,319,42,9,47,333,36,2,45,237,37,1*40

$GBGSV,8,4,29,10,41,194,36,60,41,238,41,41,40,299,39,14,39,185,38,1*72

$GBGSV,8,5,29,24,36,39,40,4,32,112,35,34,30,133,38,5,22,256,34,1*41

$GBGSV,8,6,29,8,21,206,32,44,21,74,37,11,19,156,34,42,17,322,35,1*71

$GBGSV,8,7,29,23,14,274,38,13,13,206,35,38,12,197,34,12,,,36,1*48

$GBGSV,8,8,29,43,,,29,1*71

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*41

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*74

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133821.000,A,2301.2585304,N,11421.9420841,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,133821.000,4.119,0.242,0.241,0.368,2.730,2.774,4.421*7F



2025-07-31 21:38:21:586 ==>> [D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:38:21:751 ==>> [D][05:18:30][COMM]Main Task receive event:142
[D][05:18:30][COMM]###### 41709 imu self test OK ######
[D][05:18:30][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,0,4053]
[D][05:18:30][COMM]Main Task receive event:142 finished processing


2025-07-31 21:38:21:995 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:38:22:001 ==>> 检测【打印IMU STATE2】
2025-07-31 21:38:22:005 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:38:22:357 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:31][COMM]YAW data: 32763[32763]
[D][05:18:31][COMM]pitch:-66 roll:0
[D][05:18:31][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,133822.000,2301.2585389,N,11421.9420571,E,1,12,0.78,79.390,M,-1.770,M,,*50

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,37,59,52,129,41,7,51,183,38,40,50,163,40,1*4A

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,37,2,45,237,37,1*42

$GBGSV,8,4,29,10,41,194,36,60,41,238,41,41,40,299,40,14,39,185,38,1*7C

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,12,26,101,36,1*71

$GBGSV,8,6,29,5,24,257,34,8,21,206,31,44,21,74,37,11,19,156,34,1*43

$GBGSV,8,7,29,42,17,322,35,23,14,274,38,13,13,206,35,38,12,197,35,1*7A

$GBGSV,8,8,29,43,,,29,1*71

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*40

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*74

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133822.000,A,2301.2585389,N,11421.9420571,E,0.003,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,

2025-07-31 21:38:22:387 ==>> 0.003,N,0.005,K,A*29

$GBGST,133822.000,4.183,0.204,0.203,0.306,2.752,2.792,4.325*7C



2025-07-31 21:38:22:544 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:38:22:549 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:38:22:559 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:38:22:688 ==>> [D][05:18:31][COMM]read battery soc:255
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:38:22:864 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:38:22:869 ==>> 检测【检测VBUS电压2】
2025-07-31 21:38:22:877 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:38:22:899 ==>> [D][05:18:31][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 21:38:23:355 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071541621
[D][05:18:32][FCTY]HardwareID  = 867222088008802
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = E5CF4BC85780
[D][05:18:32][FCTY]Bat         = 3944 mv
[D][05:18:32][FCTY]Current     = 150 ma
[D][05:18:32][FCTY]VBUS        = 8500 mv
[D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:18:32][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 7, adc = 309
[D][05:18:32][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KE

2025-07-31 21:38:23:430 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:38:23:461 ==>> RNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3810 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
$GBGGA,133823.000,2301.2585631,N,11421.9420126,E,1,12,0.78,79.142,M,-1.770,M,,*5C

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,37,59,52,129,42,7,51,183,38,40,50,163,40,1*49

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,37,1*43

$GBGSV,8,4,29,10,41,194,35,60,41,238,41,41,40,299,40,14,39,185,38,1*7F

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,12,26,101,36,1*71

$GBGSV,8,6,29,5,24,257,34,8,21,206,31,44,21,74,37,11,19,156,35,1*42

$GBGSV,8,7,29,42,17,322,35,23,14,274,39,13,13,206,35,38,12,197,35,1*7B

$GBGSV,8,8,29,43,,,30,1*79

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*40

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*74

$GBGSV,3,3,09,23,14,274,35,5*48

$GB

2025-07-31 21:38:23:505 ==>> RMC,133823.000,A,2301.2585631,N,11421.9420126,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,133823.000,4.104,0.214,0.212,0.323,2.708,2.744,4.189*74



2025-07-31 21:38:23:775 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071541621
[D][05:18:32][FCTY]HardwareID  = 867222088008802
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = E5CF4BC85780
[D][05:18:32][FCTY]Bat         = 3944 mv
[D][05:18:32][FCTY]Current     = 150 ma
[D][05:18:32][FCTY]VBUS        = 8500 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 4, adc = 174
[D][05:18:32][FCTY]Acckey1 vol = 5508 mv, Acckey2 vol = 126 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][

2025-07-31 21:38:23:820 ==>> 05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3810 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:38:23:865 ==>>                                                                                                                                                                           

2025-07-31 21:38:23:973 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:38:24:447 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130071541621
[D][05:18:33][FCTY]HardwareID  = 867222088008802
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = E5CF4BC85780
[D][05:18:33][FCTY]Bat         = 3844 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 4900 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 3, adc = 143
[D][05:18:33][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 0 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3810 mv
[D][05:18

2025-07-31 21:38:24:525 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:38:24:553 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:38:24:560 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:38:24:568 ==>> :33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
$GBGGA,133824.000,2301.2585666,N,11421.9420006,E,1,12,0.78,79.037,M,-1.770,M,,*59

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,59,52,129,41,7,51,183,38,40,50,163,40,1*45

$GBGSV,8,3,29,1,48,126,40,25,47,319,42,9,47,333,37,2,45,237,37,1*4F

$GBGSV,8,4,29,10,41,194,35,60,41,238,41,41,40,299,40,14,39,185,38,1*7F

$GBGSV,8,5,29,24,36,39,40,4,32,112,35,34,30,133,38,12,26,101,36,1*70

$GBGSV,8,6,29,5,24,257,34,8,21,206,31,44,21,74,37,11,19,156,35,1*42

$GBGSV,8,7,29,42,17,322,35,23,14,274,39,13,13,206,35,38,12,197,35,1*7B

$GBGSV,8,8,29,43,,,31,1*78

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*40

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*74

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133824.000,A,2301.2585666,N,11421.9420006,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,133824.000,4.089,0.185,0.184,0.279,2.696,2.729,4.097*7D



2025-07-31 21:38:24:588 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:38:24:657 ==>>                                                                                         ]frm_peripheral_device_poweroff type 16.... 
[D][05:18:33][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 3


2025-07-31 21:38:24:814 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:38:24:819 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:38:24:841 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:38:24:886 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:38:25:096 ==>>                                          [D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]index:0
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][

2025-07-31 21:38:25:103 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:38:25:128 ==>> 检测【打开WIFI(3)】
2025-07-31 21:38:25:133 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:38:25:201 ==>> PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][PROT]index:0 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]===========================================================
[D][05:18:33][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m s

2025-07-31 21:38:25:306 ==>> end data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B35340AB174664E9ABCE381ECB81ED20572C7625D9A800EBDB8FC647C4AEFBDD503EA2C2886B48308FB521B26175326C20B81F8FAEDB0E725B5170E37984FFA71A48FC7E51E526EAF8B4ABF6E3DBB88CA902DD
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular task submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 21:38:25:411 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 21:38:25:456 ==>>                                                                                                                                                                                                                                                       

2025-07-31 21:38:26:132 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:38:26:826 ==>> $GBGGA,133826.000,2301.2585818,N,11421.9419960,E,1,12,0.78,78.806,M,-1.770,M,,*54

$GBGSA,A,3,33,39,16,07,40,25,41,14,24,34,44,23,1.78,0.78,1.60,4*07

$GBGSV,8,1,29,3,61,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,59,52,129,41,7,51,183,38,40,50,163,40,1*45

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,36,1*42

$GBGSV,8,4,29,10,41,194,36,60,41,238,41,41,40,299,40,14,39,185,38,1*7C

$GBGSV,8,5,29,24,36,39,40,4,32,112,35,34,30,133,38,12,26,101,36,1*70

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,31,44,21,74,37,1*48

$GBGSV,8,7,29,11,19,156,34,42,17,322,35,23,14,274,39,38,12,197,35,1*74

$GBGSV,8,8,29,43,,,32,1*7B

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*40

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*74

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133826.000,A,2301.2585818,N,11421.9419960,E,0.004,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,133826.000,4.310,0.188,0.187,0.287,2.795,2.823,4.055*7A

[D][05:18:35][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:35][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:35][COMM]----- get Acckey 1 and value:1------------
[D][05:18:35][COMM]----- get 

2025-07-31 21:38:26:931 ==>> Acckey 2 and value:0------------
[D][05:18:35][COMM]------------ready to Power on Acckey 2------------
+WIFISCAN:4,0,CC057790A621,-55
+WIFISCAN:4,1,CC057790A620,-55
+WIFISCAN:4,2,F42A7D1297A3,-65
+WIFISCAN:4,3,44A1917CA62B,-77

[D][05:18:35][CAT1]wifi scan report total[4]
[D][05:18:35][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:35][COMM]----- get Acckey 1 and value:1------------
[D][05:18:35][COMM]----- get Acckey 2 and value:1------------
[D][05:18:35][COMM]more than the number of battery plugs
[D][05:18:35][COMM]VBUS is 1
[D][05:18:35][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:35][COMM]file:B50 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:35][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:35][COMM]Bat auth off fail, error:-1
[D][05:18:35][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:35][COMM]----- get Acckey 1 and value:1------------
[D][05:18:35][COMM]----- get Acckey 2 and value:1------------
[D][05:18:35][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:35][COMM]----- get Acckey 1 and value:1-------

2025-07-31 21:38:27:036 ==>> -----
[D][05:18:35][COMM]----- get Acckey 2 and value:1------------
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:B50 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:35][COMM]read file, len:10800, num:3
[W][05:18:35][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:35][COMM]Main Task receive event:65
[D][05:18:35][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:35][COMM]Main Task receive event:65 finished processing
[D][05:18:35][COMM]--->crc16:0xb8a
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:35][C

2025-07-31 21:38:27:141 ==>> OMM]Main Task receive event:66
[D][05:18:35][COMM]Try to Auto Lock Bat
[D][05:18:35][COMM]Main Task receive event:66 finished processing
[D][05:18:35][COMM]Main Task receive event:60
[D][05:18:35][COMM]smart_helmet_vol=255,255
[D][05:18:35][COMM]BAT CAN get state1 Fail 204
[D][05:18:35][COMM]BAT CAN get soc Fail, 204
[D][05:18:35][COMM]get soc error
[E][05:18:35][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:35][COMM]report elecbike
[W][05:18:35][PROT]remove success[1629955115],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:35][COMM]Main Task receive event:60 finished processing
[D][05:18:35][COMM]Main Task receive event:61
[D][05:18:35][COMM][D301]:type:3, trace id:280
[D][05:18:35][COMM]id[], hw[000
[D][05:18:35][CAT1]<<< 
OK

[D][05:18:35][CAT1]exec over: func id: 12, ret: 6
[D][05:18:35][COMM]get mcMaincircuitVolt error
[D][05:18:35][COMM]get mcSubcircuitVolt error
[D][05:18:35][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:35][COMM]BAT CAN get state1 Fail 204
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05

2025-07-31 21:38:27:200 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:38:27:207 ==>> 检测【扩展芯片hw】
2025-07-31 21:38:27:213 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:38:27:246 ==>> :18:35][PROT]index:1
[D][05:18:35][PROT]is_send:1
[D][05:18:35][PROT]sequence_num:6
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x3
[D][05:18:35][PROT]msg_type:0x5d03
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]Sending traceid[9999999999900007]
[D][05:18:35][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:35][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:35][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:35][COMM]Receive Bat Lock cmd 0
[D][05:18:35][COMM]VBUS is 1
[D][05:18:35][COMM]BAT CAN get soc Fail, 204
[D][05:18:35][COMM]get bat work state err
[W][05:18:35][PROT]remove success[1629955115],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[D302],priority

2025-07-31 21:38:27:291 ==>> [0],index[2],used[1]
[D][05:18:35][COMM]Main Task receive event:61 finished processing
[D][05:18:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:35][COMM]read battery soc:255


2025-07-31 21:38:27:396 ==>>                                                                                                                                                                                                                                                      

2025-07-31 21:38:27:501 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:38:27:606 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 21:38:27:711 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             [W][05:18:36][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:36][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
                                                                                                                                                                                

2025-07-31 21:38:27:877 ==>> [D][05:18:36][GNSS]recv submsg id[3]


2025-07-31 21:38:28:072 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:38:28:081 ==>> 检测【扩展芯片boot】
2025-07-31 21:38:28:173 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:38:28:179 ==>> 检测【扩展芯片sw】
2025-07-31 21:38:28:259 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:38:28:268 ==>> 检测【检测音频FLASH】
2025-07-31 21:38:28:292 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:38:28:366 ==>> $GBGGA,133828.000,2301.2585903,N,11421.9420008,E,1,15,0.70,78.710,M,-1.770,M,,*5B

$GBGSA,A,3,33,39,16,07,40,59,25,60,41,14,24,34,1.54,0.70,1.37,4*08

$GBGSA,A,3,44,38,23,,,,,,,,,,1.54,0.70,1.37,4*00

$GBGSV,8,1,29,3,61,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,37,1*43

$GBGSV,8,4,29,60,42,240,40,10,41,194,35,41,40,299,39,14,39,185,38,1*7C

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,12,26,101,36,1*71

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,35,42,17,322,35,38,15,191,34,23,14,274,39,1*75

$GBGSV,8,8,29,43,,,32,1*7B

$GBGSV,3,1,09,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*41

$GBGSV,3,2,09,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*77

$GBGSV,3,3,09,23,14,274,35,5*48

$GBRMC,133828.000,A,2301.2585903,N,11421.9420008,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,133828.000,4.408,0.171,0.170,0.259,2.836,2.860,4.000*76



2025-07-31 21:38:28:456 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:38:28:726 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:38:29:423 ==>> $GBGGA,133829.000,2301.2586033,N,11421.9420091,E,1,15,0.70,78.657,M,-1.770,M,,*51

$GBGSA,A,3,33,39,16,07,40,59,25,60,41,14,24,34,1.54,0.70,1.37,4*08

$GBGSA,A,3,44,38,23,,,,,,,,,,1.54,0.70,1.37,4*00

$GBGSV,8,1,29,3,61,190,40,33,56,218,42,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,37,7,51,183,38,40,50,163,40,59,50,129,41,1*48

$GBGSV,8,3,29,1,48,126,39,25,47,319,41,9,47,333,36,2,45,237,36,1*42

$GBGSV,8,4,29,60,42,240,40,10,41,194,35,41,40,299,40,14,39,185,38,1*72

$GBGSV,8,5,29,24,36,39,40,4,32,112,34,34,30,133,38,12,26,101,36,1*71

$GBGSV,8,6,29,13,25,216,35,5,24,257,33,8,21,206,31,44,21,74,37,1*4F

$GBGSV,8,7,29,11,19,156,34,42,17,322,35,38,15,191,34,23,14,274,39,1*74

$GBGSV,8,8,29,43,,,32,1*7B

$GBGSV,3,1,10,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*49

$GBGSV,3,2,10,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*7F

$GBGSV,3,3,10,38,15,191,30,23,14,274,35,5*75

$GBRMC,133829.000,A,2301.2586033,N,11421.9420091,E,0.003,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,133829.000,4.460,0.227,0.224,0.334,2.859,2.881,3.979*77

[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:38:30:204 ==>> [D][05:18:38][PROT]CLEAN,SEND:0
[D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:6
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:3
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900007]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BEF0A7EA98686E611218B742EC456DBC7B046F66DF4EA76602D

2025-07-31 21:38:30:278 ==>> 0C05B091AEB4D93357DAEF54E1824BBEEC487FE91A834084010455F97766298555443167B0BC58DA3272B523643CE42482385058F40615B47A8
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 21:38:30:383 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:38:30:458 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     [D][05:18:39][COMM]50331 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:38:30:747 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 21:38:30:852 ==>> [D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave


2025-07-31 21:38:30:897 ==>> 
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:39][COMM]accel parse set 0
[D][05:18:39][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:38:31:422 ==>> $GBGGA,133831.000,2301.2585899,N,11421.9419691,E,1,26,0.55,78.399,M,-1.770,M,,*5F

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,01,60,1.22,0.55,1.08,4*0E

$GBGSA,A,3,10,41,14,24,04,34,12,13,08,44,11,42,1.22,0.55,1.08,4*0B

$GBGSA,A,3,38,23,,,,,,,,,,,1.22,0.55,1.08,4*0A

$GBGSV,8,1,29,3,62,190,41,33,56,218,43,39,54,21,40,6,53,356,36,1*48

$GBGSV,8,2,29,16,53,359,37,7,51,183,38,40,50,163,40,59,50,129,41,1*48

$GBGSV,8,3,29,25,47,319,41,9,47,333,36,1,45,125,39,2,45,237,37,1*4D

$GBGSV,8,4,29,60,42,240,41,10,41,194,35,41,40,299,39,14,39,185,38,1*7D

$GBGSV,8,5,29,24,36,39,39,4,31,113,34,34,30,133,38,12,26,101,36,1*7D

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,17,167,35,38,15,191,34,23,14,274,39,1*77

$GBGSV,8,8,29,43,,,33,1*7A

$GBGSV,3,1,10,33,56,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*48

$GBGSV,3,2,10,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7C

$GBGSV,3,3,10,38,15,191,31,23,14,274,35,5*74

$GBRMC,133831.000,A,2301.2585899,N,11421.9419691,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,133831.000,3.496,0.191,0.190,0.284,2.375,2.397,3.485*7F

[D][05:18:40][COMM]51343 imu init OK


2025-07-31 21:38:32:410 ==>> $GBGGA,133832.000,2301.2585837,N,11421.9419545,E,1,26,0.55,78.214,M,-1.770,M,,*56

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,01,60,1.22,0.55,1.08,4*0E

$GBGSA,A,3,10,41,14,24,04,34,12,13,08,44,11,42,1.22,0.55,1.08,4*0B

$GBGSA,A,3,38,23,,,,,,,,,,,1.22,0.55,1.08,4*0A

$GBGSV,8,1,29,3,62,190,41,33,56,218,42,39,54,21,40,6,53,356,36,1*49

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,41,9,47,333,36,1,45,125,39,2,45,237,37,1*4D

$GBGSV,8,4,29,60,42,240,41,10,41,194,35,41,40,299,40,14,39,185,38,1*73

$GBGSV,8,5,29,24,36,39,39,4,31,113,34,34,30,133,38,12,26,101,36,1*7D

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,17,167,35,38,15,191,34,23,14,274,38,1*76

$GBGSV,8,8,29,43,,,33,1*7A

$GBGSV,3,1,11,33,56,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*48

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*7E

$GBGSV,3,3,11,42,17,167,31,38,15,191,31,23,14,274,35,5*47

$GBRMC,133832.000,A,2301.2585837,N,11421.9419545,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,133832.000,3.594,0.230,0.228,0.341,2.424,2.445,3.490*70



2025-07-31 21:38:32:484 ==>>                            
[D][05:18:41][COMM]flash test ok


2025-07-31 21:38:32:742 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:38:33:335 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:38:33:367 ==>> 检测【打开喇叭声音】
2025-07-31 21:38:33:379 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:38:33:425 ==>> $GBGGA,133833.000,2301.2585778,N,11421.9419406,E,1,27,0.55,78.053,M,-1.770,M,,*55

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.20,0.55,1.07,4*07

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.20,0.55,1.07,4*06

$GBGSA,A,3,42,38,23,,,,,,,,,,1.20,0.55,1.07,4*01

$GBGSV,8,1,29,3,62,190,41,33,55,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,42,9,47,333,36,2,47,238,37,1,45,125,40,1*4D

$GBGSV,8,4,29,60,42,240,41,10,41,194,35,41,40,299,39,14,39,185,38,1*7D

$GBGSV,8,5,29,24,36,39,40,4,31,113,34,34,30,133,38,12,26,101,36,1*73

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,35,42,17,167,35,38,15,191,35,23,14,274,38,1*76

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*4B

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,35,5*7E

$GBGSV,3,3,11,42,17,167,32,38,15,191,31,23,14,274,35,5*44

$GBRMC,133833.000,A,2301.2585778,N,11421.9419406,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,133833.000,3.495,0.190,0.190,0.283,2.371,2.391,3.410*76



2025-07-31 21:38:34:081 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:42][COMM]read file, len:15228, num:4
[D][05:18:42][COMM]--->crc16:0x419c
[D][05:18:42][COMM]read file success
[W][05:18:42][COMM][Audio].l:[936].close hexlog save
[D][05:18:42][COMM]accel parse set 1
[D][05:18:42][COMM][Audio]mon:9,05:18:42
[D][05:18:42][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:42][COM

2025-07-31 21:38:34:144 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:38:34:153 ==>> 检测【打开大灯控制】
2025-07-31 21:38:34:172 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:38:34:186 ==>> M]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_p

2025-07-31 21:38:34:291 ==>> lay_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:42][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:42][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 21:38:34:396 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:38:34:471 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:38:34:691 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:38:34:697 ==>> 检测【关闭仪表供电3】
2025-07-31 21:38:34:707 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:38:34:763 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 21:38:34:868 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:43][COMM]set POWER 0


2025-07-31 21:38:34:978 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:38:35:006 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:38:35:025 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:38:35:525 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][PROT]index:1 1629955124
[D][05:18:44][PROT]is_send:0
[D][05:18:44][PROT]sequence_num:6
[D][05:18:44][PROT]retry_timeout:0
[D][05:18:44][PROT]retry_times:2
[D][05:18:44][PROT]send_path:0x2
[D][05:18:44][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:44][PROT]===========================================================
[W][05:18:44][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955124]
[D][05:18:44][PROT]===========================================================
[D][05:18:44][PROT]sending traceid [9999999999900007]
[D][05:18:44][PROT]Send_TO_M2M [1629955124]
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:44][SAL ]sock send credit cnt[6]
[D][05:18:44][SAL ]sock send ind credit cnt[6]
[D][05:18:44][M2M ]m2m send data len[198]
[D][05:18:44][SAL ]Cellular task submsg id[10]
[D][05:18:44][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:44][CAT1]gsm read msg sub id: 15
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:44][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:44][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BE76

2025-07-31 21:38:35:630 ==>> A5FF1012334FDC57C618CDA020B7AC0F8978203F6D2B55C4098E9DDD3615C666477F118D6393E1A7C60A4F0CD3C6BD783A84B04B5F241CFA886EFD5D05CFF3AB95C7C1B1B10DAE8983E22451D15DBEC2FE
$GBGGA,133835.000,2301.2585654,N,11421.9419327,E,1,28,0.54,77.799,M,-1.770,M,,*58

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,8,1,29,3,62,190,41,33,55,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,42,9,47,333,36,2,47,238,36,1,45,125,39,1*42

$GBGSV,8,4,29,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*70

$GBGSV,8,5,29,24,36,39,40,4,31,113,34,34,30,133,38,12,26,101,36,1*73

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,17,167,35,38,15,191,34,23,14,274,39,1*77

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,17,167,32,38,15,191,31,23,14,274,35,5*44

$GBRMC,133835.000,A,

2025-07-31 21:38:35:705 ==>> 2301.2585654,N,11421.9419327,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,133835.000,3.450,0.191,0.191,0.279,2.344,2.362,3.330*73

[D][05:18:44][CAT1]<<< 
SEND OK

[D][05:18:44][CAT1]exec over: func id: 15, ret: 11
[D][05:18:44][CAT1]sub id: 15, ret: 11

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:44][M2M ]g_m2m_is_idle become true
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:44][PROT]M2M Send ok [1629955124]


2025-07-31 21:38:35:816 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:38:35:829 ==>> 检测【读大灯电压】
2025-07-31 21:38:35:839 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:38:35:962 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[33038]


2025-07-31 21:38:36:183 ==>> 【读大灯电压】通过,【33038mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:38:36:211 ==>> 检测【关闭大灯控制2】
2025-07-31 21:38:36:242 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:38:36:424 ==>> $GBGGA,133836.000,2301.2585576,N,11421.9419217,E,1,28,0.54,77.717,M,-1.770,M,,*5C

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,8,1,29,3,62,190,41,33,55,218,43,39,54,21,41,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,42,9,47,333,36,2,47,238,37,1,45,125,39,1*43

$GBGSV,8,4,29,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*70

$GBGSV,8,5,29,24,36,39,40,4,31,113,34,34,30,133,38,12,26,101,36,1*73

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,35,42,17,167,35,38,15,191,35,23,14,274,39,1*77

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,17,167,32,38,15,191,31,23,14,274,35,5*44

$GBRMC,133836.000,A,2301.2585576,N,11421.9419217,E,0.003,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,133836.000,3.540,0.185,0.185,0.271,2.391,2.408,3.348*74

[W][05:18:45][COMM]>>>>>Input command = AT+ARMLAMP=

2025-07-31 21:38:36:454 ==>> 0<<<<<


2025-07-31 21:38:36:529 ==>> [D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:38:36:725 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:38:36:737 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:38:36:764 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:38:36:790 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 21:38:36:864 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:45][COMM]arm_hub read adc[5],val[92]


2025-07-31 21:38:37:012 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:38:37:019 ==>> 检测【打开WIFI(4)】
2025-07-31 21:38:37:033 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:38:37:399 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:46][CAT1]<<< 
OK

[D][05:18:46][CAT1]exec over: func id: 12, ret: 6
$GBGGA,133837.000,2301.2585608,N,11421.9419182,E,1,28,0.54,77.657,M,-1.770,M,,*5D

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,8,1,29,3,62,190,41,33,55,218,43,39,54,21,41,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,42,9,47,333,36,2,47,238,37,1,45,125,39,1*43

$GBGSV,8,4,29,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*70

$GBGSV,8,5,29,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*72

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,17,167,36,38,15,191,34,23,14,274,39,1*74

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7E


2025-07-31 21:38:37:444 ==>> 

$GBGSV,3,3,11,42,17,167,32,38,15,191,31,23,14,274,35,5*44

$GBRMC,133837.000,A,2301.2585608,N,11421.9419182,E,0.000,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,133837.000,3.534,0.175,0.175,0.257,2.387,2.403,3.323*73



2025-07-31 21:38:37:549 ==>> [D][05:18:46][COMM]57515 imu init OK
[D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:38:37:668 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:38:37:674 ==>> 检测【EC800M模组版本】
2025-07-31 21:38:37:684 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:38:37:928 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:46][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:46][CAT1]exec over: func id: 12, ret: 132


2025-07-31 21:38:38:111 ==>> +WIFISCAN:4,0,CC057790A621,-56
+WIFISCAN:4,1,CC057790A620,-56
+WIFISCAN:4,2,F42A7D1297A3,-67
+WIFISCAN:4,3,CC057790A640,-74

[D][05:18:47][CAT1]wifi scan report total[4]


2025-07-31 21:38:38:225 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 21:38:38:232 ==>> 检测【配置蓝牙地址】
2025-07-31 21:38:38:242 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 21:38:38:416 ==>> $GBGGA,133838.000,2301.2585642,N,11421.9419143,E,1,28,0.54,77.576,M,-1.770,M,,*51

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,8,1,29,3,62,190,41,33,55,218,43,39,54,21,41,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,42,9,47,333,36,2,47,238,37,1,45,125,39,1*43

$GBGSV,8,4,29,60,42,240,41,10,41,194,35,41,40,299,40,14,39,185,38,1*73

$GBGSV,8,5,29,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*72

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,16,167,35,38,15,191,34,23,14,274,39,1*76

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133838.000,A,2301.2585642,N,11421.9419143,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,133838.000

2025-07-31 21:38:38:431 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E5CF4BC85780>】
2025-07-31 21:38:38:446 ==>> ,3.501,0.194,0.194,0.285,2.369,2.385,3.288*7C

[W][05:18:47][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 21:38:38:551 ==>> [D][05:18:47][COMM]58527 imu init OK
[D][05:18:47][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:38:38:581 ==>> recv ble 1
recv ble 2
ble set mac ok :e5,cf,4b,c8,57,80
enable filters ret : 0

2025-07-31 21:38:38:721 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 21:38:38:727 ==>> 检测【BLETEST】
2025-07-31 21:38:38:751 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 21:38:38:776 ==>> [D][05:18:47][COMM]read battery soc:255
4A A4 01 A4 4A 


2025-07-31 21:38:38:881 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E5CF4BC85780*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E5CF4BC8578099999OVER 150


2025-07-31 21:38:38:926 ==>> [D][05:18:47][GNSS]recv submsg id[3]


2025-07-31 21:38:39:198 ==>> [D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:48][COMM]accel parse set 0
[D][05:18:48][COMM][Audio].l:[1012].open hexlog save


2025-07-31 21:38:39:423 ==>>                                                                                                                                                                                                                                                                                                                                                                     ,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,41,9,47,333,36,2,47,238,37,1,45,125,39,1*40

$GBGSV,8,4,29,60,42,240,41,10,41,194,35,41,40,299,40,14,39,185,38,1*73

$GBGSV,8,5,29,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*72

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,16,167,35,38,15,191,34,23,14,274,39,1*76

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7E

$GBGSV,3,3,11,42,16,167,32,38,15,191,31,23,14,274,35,5*45

$GBRMC,133839.000,A,2301.2585592,N,11421.9419029,E,0.003,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,133839.000,3.588,0.182,0.181,0.267,2.413,2.429,3.310*78



2025-07-31 21:38:39:528 ==>> [D][05:18:48][COMM]59539 imu init OK


2025-07-31 21:38:39:758 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 21:38:39:766 ==>> 该项需要延时执行
2025-07-31 21:38:40:422 ==>> $GBGGA,133840.000,2301.2585568,N,11421.9418991,E,1,28,0.54,77.390,M,-1.770,M,,*5D

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,8,1,29,3,62,190,40,33,55,218,43,39,54,21,40,6,53,356,36,1*4A

$GBGSV,8,2,29,16,53,359,38,7,51,183,38,40,50,163,40,59,50,129,41,1*47

$GBGSV,8,3,29,25,47,319,41,9,47,333,36,2,47,238,36,1,45,125,39,1*41

$GBGSV,8,4,29,60,42,240,40,10,41,194,35,41,40,299,40,14,39,185,38,1*72

$GBGSV,8,5,29,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*72

$GBGSV,8,6,29,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,37,1*4B

$GBGSV,8,7,29,11,19,156,34,42,16,167,35,38,15,191,35,23,14,274,39,1*77

$GBGSV,8,8,29,43,9,182,33,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7E

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133840.000,A,2301.2585568,N,11421.9418991,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,133840.000,3.846,0.180,0.180,0.267,2.546,2.560,3.414*74



2025-07-31 21:38:40:647 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              : M2M_GSM_SOCKET_SEND_ACK
[D][05:18:49][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88BE7E97F22EBEDCCDA1180EEAFBA3F92671BB991B3884C4476CF992AD62A4D8842D776119AB84A6C946FEAF39

2025-07-31 21:38:40:722 ==>> 59207CAE46E9295C0A4BD6D397927994D6051E61BC4562866084E811BA50327F3DCE9C4C99C08A
[D][05:18:49][CAT1]<<< 
SEND OK

[D][05:18:49][CAT1]exec over: func id: 15, ret: 11
[D][05:18:49][CAT1]sub id: 15, ret: 11

[D][05:18:49][SAL ]Cellular task submsg id[68]
[D][05:18:49][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:49][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:49][M2M ]g_m2m_is_idle become true
[D][05:18:49][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:49][PROT]M2M Send ok [1629955129]


2025-07-31 21:38:40:767 ==>>                                 soc:255


2025-07-31 21:38:41:409 ==>> $GBGGA,133841.000,2301.2585572,N,11421.9418982,E,1,28,0.54,77.387,M,-1.770,M,,*53

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,7,1,28,3,62,190,40,33,55,218,43,39,54,21,40,6,53,356,36,1*44

$GBGSV,7,2,28,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,41,1*46

$GBGSV,7,3,28,25,47,319,41,9,47,333,36,2,47,238,36,1,45,125,39,1*4F

$GBGSV,7,4,28,60,42,240,41,10,41,194,35,41,40,299,39,14,39,185,38,1*73

$GBGSV,7,5,28,24,36,39,40,4,31,113,34,34,30,133,38,12,26,101,36,1*7D

$GBGSV,7,6,28,13,25,216,35,8,21,206,32,44,21,74,37,11,19,156,34,1*7C

$GBGSV,7,7,28,42,16,167,35,38,15,191,34,23,14,274,39,43,9,182,32,1*41

$GBGSV,3,1,12,33,55,218,43,39,54,21,42,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,12,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7D

$GBGSV,3,3,12,42,16,167,32,38,15,191,32,23,14,274,35,43,9,182,27,5*45

$GBRMC,133841.000,A,2301.2585572,N,11421.9418982,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,133841.000,3.983,0.227,0.227,0.325,2.614,2.628,3.461*73



2025-07-31 21:38:42:397 ==>> $GBGGA,133842.000,2301.2585555,N,11421.9418946,E,1,28,0.54,77.368,M,-1.770,M,,*5C

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.15,0.54,1.01,4*06

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.15,0.54,1.01,4*07

$GBGSA,A,3,42,38,23,43,,,,,,,,,1.15,0.54,1.01,4*07

$GBGSV,7,1,28,3,62,190,41,33,55,218,42,39,54,21,40,6,53,356,36,1*44

$GBGSV,7,2,28,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,41,1*46

$GBGSV,7,3,28,25,47,319,41,9,47,333,36,2,47,238,36,1,45,125,38,1*4E

$GBGSV,7,4,28,60,42,240,40,10,41,194,35,41,40,299,40,14,39,185,38,1*7C

$GBGSV,7,5,28,24,36,39,39,4,31,113,34,34,30,133,38,12,26,101,35,1*70

$GBGSV,7,6,28,13,25,216,34,8,21,206,32,44,21,74,37,11,19,156,34,1*7D

$GBGSV,7,7,28,42,16,167,35,38,15,191,34,23,14,274,39,43,9,182,32,1*41

$GBGSV,3,1,12,33,55,218,43,39,54,21,41,40,50,163,40,25,47,319,40,5*48

$GBGSV,3,2,12,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7D

$GBGSV,3,3,12,42,16,167,32,38,15,191,32,23,14,274,35,43,9,182,27,5*45

$GBRMC,133842.000,A,2301.2585555,N,11421.9418946,E,0.003,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,133842.000,4.207,0.215,0.214,0.308,2.724,2.736,3.54

2025-07-31 21:38:42:427 ==>> 9*79



2025-07-31 21:38:42:777 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:38:43:395 ==>> $GBGGA,133843.000,2301.2585642,N,11421.9418861,E,1,27,0.55,77.344,M,-1.770,M,,*5C

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,60,1.17,0.55,1.03,4*00

$GBGSA,A,3,10,41,14,24,04,34,12,13,08,44,11,42,1.17,0.55,1.03,4*06

$GBGSA,A,3,38,23,43,,,,,,,,,,1.17,0.55,1.03,4*00

$GBGSV,7,1,27,3,62,190,40,33,55,218,42,39,54,21,40,6,53,356,36,1*4A

$GBGSV,7,2,27,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,41,1*49

$GBGSV,7,3,27,25,47,319,41,9,47,333,36,2,47,238,37,60,42,240,40,1*7F

$GBGSV,7,4,27,10,41,194,35,41,40,299,40,14,39,185,38,24,36,39,40,1*4C

$GBGSV,7,5,27,4,31,113,35,34,30,133,38,12,26,101,35,13,25,216,35,1*4B

$GBGSV,7,6,27,8,21,206,32,44,21,74,37,11,19,156,34,42,16,167,35,1*72

$GBGSV,7,7,27,38,15,191,34,23,14,274,39,43,9,182,32,1*79

$GBGSV,3,1,12,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*49

$GBGSV,3,2,12,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7E

$GBGSV,3,3,12,42,16,167,32,38,15,191,32,23,14,274,35,43,9,182,27,5*45

$GBRMC,133843.000,A,2301.2585642,N,11421.9418861,E,0.003,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,133843.000,4.247,0.208,0.206,0.296,2.743,

2025-07-31 21:38:43:425 ==>> 2.755,3.553*7A



2025-07-31 21:38:44:403 ==>> $GBGGA,133844.000,2301.2585665,N,11421.9418722,E,1,27,0.55,77.281,M,-1.770,M,,*5E

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,60,1.17,0.55,1.03,4*00

$GBGSA,A,3,10,41,14,24,04,34,12,13,08,44,11,42,1.17,0.55,1.03,4*06

$GBGSA,A,3,38,23,43,,,,,,,,,,1.17,0.55,1.03,4*00

$GBGSV,7,1,28,3,62,190,40,33,55,218,43,39,54,21,40,6,53,356,36,1*44

$GBGSV,7,2,28,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,41,1*46

$GBGSV,7,3,28,25,47,319,41,9,47,333,36,2,47,238,37,60,42,240,41,1*71

$GBGSV,7,4,28,10,41,194,35,41,40,299,40,14,39,185,38,24,36,39,40,1*43

$GBGSV,7,5,28,4,31,113,35,34,30,133,38,12,26,101,36,13,25,216,35,1*47

$GBGSV,7,6,28,5,24,257,34,8,21,206,32,44,21,74,37,11,19,156,34,1*4E

$GBGSV,7,7,28,42,16,167,35,38,15,191,34,23,14,274,39,43,9,182,32,1*41

$GBGSV,3,1,12,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*49

$GBGSV,3,2,12,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7D

$GBGSV,3,3,12,42,16,167,32,38,15,191,32,23,14,274,35,43,9,182,27,5*45

$GBRMC,133844.000,A,2301.2585665,N,11421.9418722,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,133844.000,4.382,0.225,0.223,0.320,2.807,2.818,3.601*7C



2025-07-31 21:38:44:778 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:38:45:409 ==>> $GBGGA,133845.000,2301.2585656,N,11421.9418677,E,1,25,0.56,77.228,M,-1.770,M,,*5C

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,60,1.24,0.56,1.11,4*00

$GBGSA,A,3,10,41,14,24,04,34,12,13,44,11,42,38,1.24,0.56,1.11,4*05

$GBGSA,A,3,23,,,,,,,,,,,,1.24,0.56,1.11,4*0C

$GBGSV,7,1,26,3,62,190,41,33,55,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,7,2,26,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,42,1*4B

$GBGSV,7,3,26,25,47,319,42,9,47,333,37,2,47,238,37,60,42,240,41,1*7D

$GBGSV,7,4,26,10,41,194,36,41,40,299,40,14,39,185,38,24,36,39,40,1*4E

$GBGSV,7,5,26,4,31,113,35,34,30,133,38,12,26,101,36,13,25,216,35,1*49

$GBGSV,7,6,26,5,24,257,34,44,21,74,37,11,19,156,34,42,16,167,35,1*79

$GBGSV,7,7,26,38,15,191,34,23,14,274,39,1*7C

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7E

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133845.000,A,2301.2585656,N,11421.9418677,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,133845.000,3.609,0.193,0.192,0.290,2.421,2.434,3.236*7B



2025-07-31 21:38:45:869 ==>> [D][05:18:54][PROT]CLEAN,SEND:1
[D][05:18:54][PROT]CLEAN:1
[D][05:18:54][PROT]index:0 1629955134
[D][05:18:54][PROT]is_send:0
[D][05:18:54][PROT]sequence_num:5
[D][05:18:54][PROT]retry_timeout:0
[D][05:18:54][PROT]retry_times:2
[D][05:18:54][PROT]send_path:0x2
[D][05:18:54][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:54][PROT]===========================================================
[W][05:18:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955134]
[D][05:18:54][PROT]===========================================================
[D][05:18:54][PROT]sending traceid [9999999999900006]
[D][05:18:54][PROT]Send_TO_M2M [1629955134]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:54][SAL ]sock send credit cnt[6]
[D][05:18:54][SAL ]sock send ind credit cnt[6]
[D][05:18:54][M2M ]m2m send data len[198]
[D][05:18:54][SAL ]Cellular task submsg id[10]
[D][05:18:54][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:54][CAT1]gsm read msg sub id: 15
[D][05:18:54][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:54][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B36DFB84F2EC145ED93408144D50295888CF628C827B4EF2DDE6D6E24EF390487ACABF5B604672BD979

2025-07-31 21:38:45:944 ==>> C0620EB8A61D7EB7989609127A45564C5B4EE359F0DE557CB7DDF033A31EA7B2D0292C2BCE56878CAAF
[D][05:18:54][CAT1]<<< 
SEND OK

[D][05:18:54][CAT1]exec over: func id: 15, ret: 11
[D][05:18:54][CAT1]sub id: 15, ret: 11

[D][05:18:54][SAL ]Cellular task submsg id[68]
[D][05:18:54][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:54][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:54][M2M ]g_m2m_is_idle become true
[D][05:18:54][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:54][PROT]M2M Send ok [1629955134]


2025-07-31 21:38:46:399 ==>> $GBGGA,133846.000,2301.2585718,N,11421.9418660,E,1,26,0.55,77.213,M,-1.770,M,,*5A

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.22,0.55,1.09,4*0B

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,44,11,42,1.22,0.55,1.09,4*04

$GBGSA,A,3,38,23,,,,,,,,,,,1.22,0.55,1.09,4*0B

$GBGSV,7,1,27,3,62,190,40,33,55,218,43,39,54,21,40,6,53,356,36,1*4B

$GBGSV,7,2,27,16,53,0,38,7,51,183,38,40,50,163,40,59,50,129,41,1*49

$GBGSV,7,3,27,25,47,319,41,9,47,333,36,2,47,238,36,1,45,125,40,1*4E

$GBGSV,7,4,27,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*71

$GBGSV,7,5,27,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*73

$GBGSV,7,6,27,13,25,216,35,5,24,257,34,44,21,74,37,11,19,156,35,1*78

$GBGSV,7,7,27,42,16,167,35,38,15,191,35,23,14,274,39,1*4B

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,16,167,32,38,15,191,31,23,14,274,36,5*46

$GBRMC,133846.000,A,2301.2585718,N,11421.9418660,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,133846.000,3.378,0.160,0.159,0.242,2.298,2.310,3.114*79



2025-07-31 21:38:46:783 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:38:47:405 ==>> $GBGGA,133847.000,2301.2585661,N,11421.9418696,E,1,27,0.55,77.182,M,-1.770,M,,*57

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.20,0.55,1.07,4*07

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.20,0.55,1.07,4*06

$GBGSA,A,3,42,38,23,,,,,,,,,,1.20,0.55,1.07,4*01

$GBGSV,7,1,28,3,62,190,41,33,55,218,43,39,54,21,40,6,53,356,36,1*45

$GBGSV,7,2,28,16,53,0,38,7,51,184,39,40,50,163,40,59,50,129,42,1*43

$GBGSV,7,3,28,25,47,319,42,9,47,333,37,2,47,238,37,1,45,125,39,1*4C

$GBGSV,7,4,28,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*7E

$GBGSV,7,5,28,24,36,39,40,4,31,113,35,34,30,133,39,12,26,101,36,1*7D

$GBGSV,7,6,28,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,38,1*4A

$GBGSV,7,7,28,11,19,156,35,42,16,167,35,38,15,191,35,23,14,274,39,1*78

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133847.000,A,2301.2585661,N,11421.9418696,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,133847.000,3.473,0.179,0.179,0.268,2.349,2.361,3.150*7D



2025-07-31 21:38:48:408 ==>> $GBGGA,133848.000,2301.2585637,N,11421.9418713,E,1,27,0.55,77.134,M,-1.770,M,,*5A

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.20,0.55,1.07,4*07

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.20,0.55,1.07,4*06

$GBGSA,A,3,42,38,23,,,,,,,,,,1.20,0.55,1.07,4*01

$GBGSV,7,1,28,3,62,190,41,33,55,218,43,39,54,21,41,6,53,356,36,1*44

$GBGSV,7,2,28,16,53,0,38,7,51,184,39,40,50,163,40,59,50,129,41,1*40

$GBGSV,7,3,28,25,47,319,42,9,47,333,37,2,47,238,36,1,45,125,39,1*4D

$GBGSV,7,4,28,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*7E

$GBGSV,7,5,28,24,36,39,40,4,31,113,35,34,30,133,38,12,26,101,36,1*7C

$GBGSV,7,6,28,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,38,1*4A

$GBGSV,7,7,28,11,19,156,35,42,16,167,36,38,15,191,35,23,14,274,39,1*7B

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,36,44,21,74,36,5*7E

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133848.000,A,2301.2585637,N,11421.9418713,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,133848.000,3.607,0.176,0.175,0.264,2.419,2.431,3.206*7C



2025-07-31 21:38:48:796 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 21:38:49:407 ==>> $GBGGA,133849.000,2301.2585606,N,11421.9418758,E,1,27,0.55,77.098,M,-1.770,M,,*51

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.20,0.55,1.07,4*07

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.20,0.55,1.07,4*06

$GBGSA,A,3,42,38,23,,,,,,,,,,1.20,0.55,1.07,4*01

$GBGSV,8,1,29,21,69,310,,3,62,190,41,33,55,218,43,39,54,21,41,1*71

$GBGSV,8,2,29,6,53,356,36,16,53,0,38,7,51,184,39,40,50,163,40,1*7D

$GBGSV,8,3,29,59,50,129,41,25,47,319,42,9,47,333,37,2,47,238,37,1*78

$GBGSV,8,4,29,1,45,125,39,60,42,240,41,10,41,194,36,41,40,299,40,1*44

$GBGSV,8,5,29,14,39,185,38,24,36,39,40,4,31,113,35,34,30,133,38,1*78

$GBGSV,8,6,29,12,26,101,36,13,25,216,35,5,24,257,34,8,21,206,32,1*7D

$GBGSV,8,7,29,44,21,74,37,11,19,156,35,42,16,167,35,38,15,191,35,1*4D

$GBGSV,8,8,29,23,14,274,39,1*42

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133849.000,A,2301.2585606,N,11421.9418758,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,133849.000,3.712,0.206,0.206,0.310,2.474,2.485,3.248*77



2025-07-31 21:38:49:762 ==>> 此处延时了:【10000】毫秒
2025-07-31 21:38:49:769 ==>> 检测【检测WiFi结果】
2025-07-31 21:38:49:776 ==>> WiFi信号:【CC057790A5C1】,信号值:-77
2025-07-31 21:38:49:798 ==>> WiFi信号:【44A1917CA62B】,信号值:-77
2025-07-31 21:38:49:814 ==>> WiFi信号:【CC057790A7C1】,信号值:-77
2025-07-31 21:38:49:821 ==>> WiFi信号:【CC057790A7C0】,信号值:-79
2025-07-31 21:38:49:845 ==>> WiFi信号:【CC057790A621】,信号值:-55
2025-07-31 21:38:49:858 ==>> WiFi信号:【CC057790A620】,信号值:-55
2025-07-31 21:38:49:881 ==>> WiFi信号:【F42A7D1297A3】,信号值:-65
2025-07-31 21:38:49:902 ==>> WiFi信号:【CC057790A640】,信号值:-74
2025-07-31 21:38:49:914 ==>> WiFi数量【8】, 最大信号值:-55
2025-07-31 21:38:49:939 ==>> 检测【检测GPS结果】
2025-07-31 21:38:49:950 ==>> 符合定位需求的卫星数量:【23】
2025-07-31 21:38:49:977 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【60】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【59】,信号值:【42】
北斗星号:【25】,信号值:【41】
北斗星号:【40】,信号值:【40】
北斗星号:【24】,信号值:【40】
北斗星号:【1】,信号值:【39】
北斗星号:【39】,信号值:【40】
北斗星号:【41】,信号值:【40】
北斗星号:【7】,信号值:【38】
北斗星号:【34】,信号值:【38】
北斗星号:【14】,信号值:【38】
北斗星号:【23】,信号值:【38】
北斗星号:【2】,信号值:【37】
北斗星号:【44】,信号值:【37】
北斗星号:【16】,信号值:【38】
北斗星号:【10】,信号值:【35】
北斗星号:【9】,信号值:【36】
北斗星号:【12】,信号值:【36】
北斗星号:【6】,信号值:【36】
北斗星号:【38】,信号值:【35】
北斗星号:【42】,信号值:【35】

2025-07-31 21:38:49:990 ==>> 检测【CSQ强度】
2025-07-31 21:38:50:015 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 21:38:50:041 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21


2025-07-31 21:38:50:097 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 21:38:50:112 ==>> 检测【关闭GSM联网】
2025-07-31 21:38:50:132 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 21:38:50:405 ==>> $GBGGA,133850.000,2301.2585606,N,11421.9418738,E,1,27,0.55,77.100,M,-1.770,M,,*5F

$GBGSA,A,3,03,33,39,06,16,07,40,59,25,09,02,01,1.20,0.55,1.07,4*07

$GBGSA,A,3,60,10,41,14,24,04,34,12,13,08,44,11,1.20,0.55,1.07,4*06

$GBGSA,A,3,42,38,23,,,,,,,,,,1.20,0.55,1.07,4*01

$GBGSV,7,1,28,3,62,190,41,33,55,218,43,39,54,21,41,6,53,356,36,1*44

$GBGSV,7,2,28,16,53,0,38,7,51,184,39,40,50,163,40,59,50,129,41,1*40

$GBGSV,7,3,28,25,47,319,42,9,47,333,37,2,47,238,37,1,45,125,39,1*4C

$GBGSV,7,4,28,60,42,240,41,10,41,194,36,41,40,299,40,14,39,185,38,1*7E

$GBGSV,7,5,28,24,36,39,40,4,31,113,35,34,30,133,39,12,26,101,36,1*7D

$GBGSV,7,6,28,13,25,216,35,5,24,257,34,8,21,206,32,44,21,74,38,1*4A

$GBGSV,7,7,28,11,19,156,35,42,16,167,36,38,15,191,35,23,14,274,39,1*7B

$GBGSV,3,1,11,33,55,218,43,39,54,21,41,40,50,163,41,25,47,319,40,5*4A

$GBGSV,3,2,11,41,40,299,42,24,36,39,40,34,30,133,35,44,21,74,36,5*7D

$GBGSV,3,3,11,42,16,167,32,38,15,191,32,23,14,274,35,5*46

$GBRMC,133850.000,A,2301.2585606,N,11421.9418738,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,133850.000,3.490,0.191,0.190,0.286,2.357,2.368,3.132*75

[W][05:18:59][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:59][COMM]GSM test
[D][0

2025-07-31 21:38:50:435 ==>> 5:18:59][COMM]GSM test disable


2025-07-31 21:38:50:654 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 21:38:50:666 ==>> 检测【4G联网测试】
2025-07-31 21:38:50:696 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 21:38:51:889 ==>> [D][05:18:59][COMM]read battery soc:255
[D][05:18:59][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:18:59][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:59][PROT]CLEAN,SEND:0
[D][05:18:59][PROT]index:0 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:5
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:1
[D][05:18:59][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][PROT]sending traceid [9999999999900006]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[198]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:59][CAT1]gsm read msg sub id: 15
[D][05:18:59][COMM]Main Task receive event:14
[D][05:18:59][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:59][COMM]handle

2025-07-31 21:38:51:994 ==>> rPeriodRep, g_elecBatMissedCount = 0, time = 1629955139, allstateRepSeconds = 0
[D][05:18:59][COMM]index:0,power_mode:0xFF
[D][05:18:59][COMM]index:1,sound_mode:0xFF
[D][05:18:59][COMM]index:2,gsensor_mode:0xFF
[D][05:18:59][COMM]index:3,report_freq_mode:0xFF
[D][05:18:59][COMM]index:4,report_period:0xFF
[D][05:18:59][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:59][COMM]index:6,normal_reset_period:0xFF
[D][05:18:59][COMM]index:7,spock_over_speed:0xFF
[D][05:18:59][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:59][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:59][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:59][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:59][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:59][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:59][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:59][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:59][COMM]index:16,imu_config_params:0xFF
[D][05:18:59][COMM]index:17,long_connect_params:0xFF
[D][05:18:59][COMM]index:18,detain_mark:0xFF
[D][05:18:59][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:59][COMM]index:20,lock_pos_report_in

2025-07-31 21:38:52:099 ==>> terval:0xFF
[D][05:18:59][COMM]index:21,mc_mode:0xFF
[D][05:18:59][COMM]index:22,S_mode:0xFF
[D][05:18:59][COMM]index:23,overweight:0xFF
[D][05:18:59][COMM]index:24,standstill_mode:0xFF
[D][05:18:59][COMM]index:25,night_mode:0xFF
[D][05:18:59][CAT1]Send Data To Server[198][201] ... ->:
0063B983113311331133113311331B88B32D46EE9B08DEF24A3B3EB9DE7A964E5D99F99AB3AA10DA09C971562128AE4E0A47E0E30617FD55629095A8A1EE4F63B2838AF24619EEB443C2FBF7F3DB5595D28544E3272A470831E62A276EFACEA5BEB466
[D][05:18:59][COMM]index:26,experiment1:0xFF
[D][05:18:59][COMM]index:27,experiment2:0xFF
[D][05:18:59][COMM]index:28,experiment3:0xFF
[D][05:18:59][COMM]index:29,experiment4:0xFF
[D][05:18:59][COMM]index:30,night_mode_start:0xFF
[D][05:18:59][COMM]index:31,night_mode_end:0xFF
[D][05:18:59][COMM]index:33,park_report_minutes:0xFF
[D][05:18:59][COMM]index:34,park_report_mode:0xFF
[D][05:18:59][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:59][COMM]index:38,charge_battery_para: FF
[D][05:18:59][COMM]index:39,multirider_mode:0xFF
[D][05:18:59][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:59][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:59][COMM]index:42,set_time_ble_mode_begin_min:0

2025-07-31 21:38:52:204 ==>> xFF
[D][05:18:59][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:59][COMM]index:44,riding_duration_config:0xFF
[D][05:18:59][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:59][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:59][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:59][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:59][COMM]index:49,mc_load_startup:0xFF
[D][05:18:59][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:59][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:59][COMM]index:52,traffic_mode:0xFF
[D][05:18:59][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:59][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:59][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:59][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:59][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:59][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:59][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:59][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:59][COMM]index:63,experiment5:0xFF
[D][05:18:59][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:59][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:59][COMM]index:66,camera_park_distan

2025-07-31 21:38:52:309 ==>> ce_cfg:0xFF
[D][05:18:59][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:59][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:59][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:59][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:59][COMM]index:72,experiment6:0xFF
[D][05:18:59][COMM]index:73,experiment7:0xFF
[D][05:18:59][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:59][COMM]index:75,zero_value_from_server:-1
[D][05:18:59][COMM]index:76,multirider_threshold:255
[D][05:18:59][COMM]index:77,experiment8:255
[D][05:18:59][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:59][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:59][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:59][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:59][COMM]index:83,loc_report_interval:255
[D][05:18:59][COMM]index:84,multirider_threshold_p2:255
[D][05:18:59][COMM]index:85,multirider_strategy:255
[D][05:18:59][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:59][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:59][COMM]index:90,weight_param:0xFF
[D][05:18:59][COMM]index:93,lock_anti_theft_mode:0xFF
[D][0

2025-07-31 21:38:52:414 ==>> 5:18:59][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:59][COMM]index:95,current_limit:0xFF
[D][05:18:59][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:59][COMM]index:100,location_mode:0xFF

[W][05:18:59][PROT]remove success[1629955139],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:59][PROT]add success [1629955139],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:59][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:59][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:59][CAT1]<<< 
SEND OK

[D][05:18:59][CAT1]exec over: func id: 15, ret: 11
[D][05:18:59][CAT1]sub id: 15, ret: 11

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:59][CAT1]gsm read msg sub id: 13
[D][05:18:59][M2M ]g_m2m_is_idle become true
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:59][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:59][PROT]index:0 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:8
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:1
[D][05:18:59

2025-07-31 21:38:52:504 ==>> ][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][PROT]sending traceid [9999999999900009]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][PROT]M2M Send ok [1629955139]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[294]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D

2025-07-31 21:38:52:609 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 21:38:52:639 ==>>                                                                                                                            

2025-07-31 21:38:52:702 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 21:38:52:720 ==>> 检测【关闭GPS】
2025-07-31 21:38:52:738 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 21:38:53:038 ==>> [D][05:19:01][COMM]read battery soc:255
[W][05:19:01][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:01][GNSS]stop locating
[D][05:19:01][GNSS]stop event:8
[D][05:19:01][GNSS]GPS stop. ret=0
[D][05:19:01][GNSS]all continue location stop
[W][05:19:01][GNSS]stop locating
[D][05:19:01][GNSS]all sing location stop
[D][05:19:01][CAT1]gsm read msg sub id: 24
[D][05:19:01][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 24, ret: 6
[D][05:19:01][CAT1]sub id: 24, ret: 6



2025-07-31 21:38:53:249 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 21:38:53:257 ==>> 检测【清空消息队列2】
2025-07-31 21:38:53:270 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:38:53:481 ==>> [D][05:19:02][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[W][05:19:02][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:02][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:38:53:551 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:38:53:572 ==>> 检测【轮动检测】
2025-07-31 21:38:53:602 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 21:38:53:677 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 21:38:53:737 ==>> [D][05:19:02][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 21:38:53:995 ==>> [D][05:19:02][GNSS]recv submsg id[1]
[D][05:19:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:02][GNSS]location stop evt done evt


2025-07-31 21:38:54:055 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 21:38:54:175 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 21:38:54:346 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 21:38:54:354 ==>> 检测【关闭小电池】
2025-07-31 21:38:54:367 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:38:54:484 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:38:54:634 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 21:38:54:666 ==>> 检测【进入休眠模式】
2025-07-31 21:38:54:697 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:38:54:881 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:03][COMM]read battery soc:255
[D][05:19:03][COMM]Main Task receive event:28
[D][05:19:03][COMM]main task tmp_sleep_event = 8
[D][05:19:03][COMM]prepare to sleep
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 21:38:55:756 ==>> [D][05:19:04][CAT1]<<< 
OK

[D][05:19:04][CAT1]exec over: func id: 12, ret: 6
[D][05:19:04][M2M ]tcpclient close[4]
[D][05:19:04][SAL ]Cellular task submsg id[12]
[D][05:19:04][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:04][CAT1]gsm read msg sub id: 9
[D][05:19:04][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:04][CAT1]<<< 
OK

[D][05:19:04][CAT1]exec over: func id: 9, ret: 6
[D][05:19:04][CAT1]sub id: 9, ret: 6

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:04][SAL ]socket close ind. id[4]
[D][05:19:04][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:04][COMM]1x1 frm_can_tp_send ok
[D][05:19:04][CAT1]pdpdeact urc len[22]


2025-07-31 21:38:56:061 ==>> [E][05:19:05][COMM]1x1 rx timeout
[D][05:19:05][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:38:56:585 ==>> [E][05:19:05][COMM]1x1 rx timeout
[E][05:19:05][COMM]1x1 tp timeout
[E][05:19:05][COMM]1x1 error -3.
[W][05:19:05][COMM]CAN STOP!
[D][05:19:05][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:05][COMM]------------ready to Power off Acckey 1------------
[D][05:19:05][COMM]------------ready to Power off Acckey 2------------
[D][05:19:05][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:05][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1293
[D][05:19:05][COMM]bat sleep fail, reason:-1
[D][05:19:05][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:05][COMM]accel parse set 0
[D][05:19:05][COMM]imu rest ok. 76458
[D][05:19:05][COMM]imu sleep 0
[W][05:19:05][COMM]now sleep


2025-07-31 21:38:56:754 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:38:56:761 ==>> 检测【检测33V休眠电流】
2025-07-31 21:38:56:788 ==>> 开始33V电流采样
2025-07-31 21:38:56:807 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:38:56:858 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 21:38:57:868 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 21:38:57:899 ==>> Current33V:????:12.09

2025-07-31 21:38:58:377 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 21:38:58:385 ==>> 【检测33V休眠电流】通过,【12.09uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:38:58:392 ==>> 该项需要延时执行
2025-07-31 21:39:00:388 ==>> 此处延时了:【2000】毫秒
2025-07-31 21:39:00:409 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 21:39:00:422 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:39:00:496 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2750mV
Get AD_V6 1894mV
Get AD_V7 1095mV
OVER 150


2025-07-31 21:39:01:432 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:39:01:461 ==>> 检测【打开小电池2】
2025-07-31 21:39:01:469 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:39:01:496 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:39:01:725 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:39:01:733 ==>> 该项需要延时执行
2025-07-31 21:39:02:234 ==>> 此处延时了:【500】毫秒
2025-07-31 21:39:02:247 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 21:39:02:273 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:39:02:372 ==>> 5A A5 02 5A A5 


2025-07-31 21:39:02:477 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:39:02:528 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:39:02:554 ==>> 该项需要延时执行
2025-07-31 21:39:03:033 ==>> 此处延时了:【500】毫秒
2025-07-31 21:39:03:054 ==>> 检测【进入休眠模式2】
2025-07-31 21:39:03:080 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 21:39:03:169 ==>> [D][05:19:11][COMM]------------ready to Power on Acckey 1------------
[D][05:19:11][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:11][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:11][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:11][COMM]----- get Acckey 1 and value:1------------
[W][05:19:11][COMM]CAN START!
[D][05:19:11][CAT1]gsm read msg sub id: 12
[D][05:19:11][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:11][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 82970
[D][05:19:11][COMM][Audio]exec status ready.
[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 12, ret: 6
[D][05:19:11][COMM]imu wakeup ok. 82985
[D][05:19:11][C

2025-07-31 21:39:03:214 ==>> OMM]imu wakeup 1
[W][05:19:11][COMM]wake up system, wakeupEvt=0x80
[D][05:19:11][COMM]frm_can_weigth_power_set 1
[D][05:19:11][COMM]Clear Sleep Block Evt
[D][05:19:11][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:11][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:39:03:289 ==>> [W][05:19:12][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 21:39:03:394 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:39:03:499 ==>> [D][05:19:12][COMM]msg 02A0 loss. last_tick:82

2025-07-31 21:39:03:559 ==>> 956. cur_tick:83464. period:50
[D][05:19:12][COMM]msg 02A4 loss. last_tick:82956. cur_tick:83464. period:50
[D][05:19:12][COMM]msg 02A5 loss. last_tick:82956. cur_tick:83465. period:50
[D][05:19:12][COMM]msg 02A6 loss. last_tick:82956. cur_tick:83465. period:50
[D][05:19:12][COMM]msg 02A7 loss. last_tick:82956. cur_tick:83465. period:50
[D][05:19:12][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 83466
[D][05:19:12][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 83466


2025-07-31 21:39:03:895 ==>> [E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[E][05:19:12][COMM]1x1 error -3.
[D][05:19:12][COMM]Main Task receive event:28 finished processing
[D][05:19:12][COMM]Main Task receive event:28
[D][05:19:12][COMM]prepare to sleep
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:39:04:199 ==>> [D][05:19:12][COMM]msg 0220 loss. last_tick:82955. cur_tick:83960. period:100
[D][05:19:12][COMM]msg 0221 loss. last_tick:82955. cur_tick:83961. period:100
[D][05:19:12][COMM]msg 0224 loss. last_tick:82956. cur_tick:83961. period:100
[D][05:19:12][COMM]msg 0260 loss. last_tick:82956. cur_tick:83962. period:100
[D][05:19:12][COMM]msg 0280 loss. last_tick:82956. cur_tick:83962. period:100
[D][05:19:12][COMM]msg 02C0 loss. last_tick:82956. cur_tick:83962. period:100
[D][05:19:12][COMM]msg 02C1 loss. last_tick:82956. cur_tick:83963. period:100
[D][05:19:12][COMM]msg 02C2 loss. last_tick:82956. cur_tick:83963. period:100
[D][05:19:12][COMM]msg 02E0 loss. last_tick:82956. cur_tick:83963. period:100
[D][05:19:12][COMM]msg 02E1 loss. last_tick:82956. cur_tick:83964. period:100
[D][05:19:12][COMM]msg 02E2 loss. last_tick:82956. cur_tick:83964. period:100
[D][05:19:12][COMM]msg 0300 loss. last_tick:82956. cur_tick:83964. period:100
[D][05:19:12][COMM]msg 0301 loss. last_tick:82956. cur_tick:83965. period:100
[D][05:19:12][COMM]bat msg 0240 loss. last_tick:82956. cur_tick:83965. period:100. j,i:1 54
[D][05:19:12][COMM]bat msg 0241 loss. last_tick:82956. cur_tick:83965. period:1

2025-07-31 21:39:04:274 ==>> 00. j,i:2 55
[D][05:19:12][COMM]bat msg 0242 loss. last_tick:82956. cur_tick:83966. period:100. j,i:3 56
[D][05:19:12][COMM]bat msg 0244 loss. last_tick:82956. cur_tick:83966. period:100. j,i:5 58
[D][05:19:12][COMM]bat msg 024E loss. last_tick:82956. cur_tick:83967. period:100. j,i:15 68
[D][05:19:12][COMM]bat msg 024F loss. last_tick:82956. cur_tick:83967. period:100. j,i:16 69
[D][05:19:12][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 83968
[D][05:19:12][COMM]CAN message bat fault change: 0x00000000->0x0001802E 83968
[D][05:19:12][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 83969


2025-07-31 21:39:04:305 ==>>                                                                               

2025-07-31 21:39:04:574 ==>> [D][05:19:13][COMM]msg 0222 loss. last_tick:82955. cur_tick:84463. period:150
[D][05:19:13][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 84464
[D][05:19:13][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:13][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:13][COMM]------------ready to Power off Acckey 2------------


2025-07-31 21:39:04:785 ==>> [E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[W][05:19:13][COMM]CAN STOP!
[D][05:19:13][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:13][COMM]------------ready to Power off Acckey 1------------
[D][05:19:13][COMM]------------ready to Power off Acckey 2------------
[D][05:19:13][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:13][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 104
[D][05:19:13][COMM]bat sleep fail, reason:-1
[D][05:19:13][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:13][COMM]accel parse set 0
[D][05:19:13][COMM]imu rest ok. 84656
[D][05:19:13][COMM]imu sleep 0
[D][05:19:13][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[W][05:19:13][COMM]now sleep


2025-07-31 21:39:04:919 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 21:39:04:927 ==>> 检测【检测小电池休眠电流】
2025-07-31 21:39:04:942 ==>> 开始小电池电流采样
2025-07-31 21:39:04:956 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:39:05:026 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:39:06:032 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:39:06:079 ==>> CurrentBattery:ƽ��:59.86

2025-07-31 21:39:06:545 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:39:06:562 ==>> 【检测小电池休眠电流】通过,【59.86uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 21:39:06:575 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 21:39:06:597 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:39:06:682 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:39:06:847 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:39:06:863 ==>> 该项需要延时执行
2025-07-31 21:39:06:909 ==>> [D][05:19:15][COMM]------------ready to Power on Acckey 1------------
[D][05:19:15][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:15][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:15][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 20
[D][05:19:15][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:15][COMM]----- get Acckey 1 and value:1------------
[W][05:19:15][COMM]CAN START!
[D][05:19:15][COMM]read battery soc:0
[D][05:19:15][CAT1]gsm read msg sub id: 12
[D][05:19:15][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:15][COMM]CAN message bat fault change: 0x0001802E->0x00000000 86754
[D][05:19:15][COMM][Audio]exec status ready.
[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]exec over: func id: 12, ret: 6
[D][05:19:15][COMM]imu wakeup ok. 86768
[D][05:19:15][COMM]imu wakeup 1
[W][05:19:15][COMM]wake up system, wakeupEvt=0x80
[D][05:19:15][COMM]frm_can_weigth_power_set 1
[D][05:19:15][COMM]Clear Sleep Block Evt
[D][05:19:15][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:15][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:39:07:073 ==>> [D][05:19:16][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 21:39:07:178 ==>> [E][05:19:16][COMM]1x1 rx timeout
[D][05:19:16][COMM]1x1 frm_can_tp_send ok


2025-07-31 21:39:07:283 ==>> [D][05:19:16][COMM]msg 02A0 loss. last_tick:86736. cur_tick:87247. period:50
[D][05:19:16][C

2025-07-31 21:39:07:344 ==>> OMM]msg 02A4 loss. last_tick:86736. cur_tick:87248. period:50
[D][05:19:16][COMM]msg 02A5 loss. last_tick:86736. cur_tick:87248. period:50
[D][05:19:16][COMM]msg 02A6 loss. last_tick:86736. cur_tick:87248. period:50
[D][05:19:16][COMM]msg 02A7 loss. last_tick:86736. cur_tick:87249. period:50
[D][05:19:16][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 87249
[D][05:19:16][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 87249


2025-07-31 21:39:07:358 ==>> 此处延时了:【500】毫秒
2025-07-31 21:39:07:371 ==>> 检测【检测唤醒】
2025-07-31 21:39:07:398 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:39:08:128 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:16][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:16][FCTY]==========Modules-nRF5340 ==========
[D][05:19:16][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:16][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:16][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:16][FCTY]DeviceID    = 460130071541621
[D][05:19:16][FCTY]HardwareID  = 867222088008802
[D][05:19:16][FCTY]MoBikeID    = 9999999999
[D][05:19:16][FCTY]LockID      = FFFFFFFFFF
[D][05:19:16][FCTY]BLEFWVersion= 105
[D][05:19:16][FCTY]BLEMacAddr   = E5CF4BC85780
[D][05:19:16][FCTY]Bat         = 3864 mv
[D][05:19:16][FCTY]Current     = 0 ma
[D][05:19:16][FCTY]VBUS        = 2600 mv
[D][05:19:16][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:16][FCTY]Ext battery vol = 32, adc = 1296
[D][05:19:16][FCTY]Acckey1 vol = 5510 mv, Acckey2 vol = 101 mv
[D][05:19:16][FCTY]Bike Type flag is invalied
[D][05:19:16][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:16][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:16][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:16][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:16][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:16][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:16][FCTY]Bat1         = 3810 mv
[D][05:

2025-07-31 21:39:08:177 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 21:39:08:189 ==>> 检测【关机】
2025-07-31 21:39:08:198 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:39:08:234 ==>> 19:16][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:16][FCTY]==========Modules-nRF5340 ==========
[E][05:19:16][COMM]1x1 rx timeout
[E][05:19:16][COMM]1x1 tp timeout
[E][05:19:16][COMM]1x1 error -3.
[D][05:19:16][COMM]Main Task receive event:28 finished processing
[D][05:19:16][COMM]Main Task receive event:65
[D][05:19:16][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:16][COMM]Main Task receive event:65 finished processing
[D][05:19:16][COMM]Main Task receive event:60
[D][05:19:16][COMM]smart_helmet_vol=255,255
[D][05:19:16][COMM]report elecbike
[W][05:19:16][PROT]remove success[1629955156],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:16][COMM]Main Task receive event:60 finished processing
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:16][PROT]index:0
[D][05:19:16][PROT]is_send:1
[D][05:19:16][PROT]sequence_num:10
[D][05:19:16][PROT]retry_timeout:0
[D][05:19:16][PROT]retry_t

2025-07-31 21:39:08:338 ==>> imes:3
[D][05:19:16][PROT]send_path:0x3
[D][05:19:16][PROT]msg_type:0x5d03
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][PROT]Sending traceid[999999999990000B]
[D][05:19:16][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:16][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:16][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:16][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:16][SAL ]open socket ind id[4], rst[0]
[D][05:19:16][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:16][SAL ]Cellular task submsg id[8]
[D][05:19:16][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:16][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:16][CAT1]gsm read msg sub id: 8
[D][05:19:16][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D]

2025-07-31 21:39:08:443 ==>> [05:19:16][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:16][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:16][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:16][CAT1]<<< 
+CME ERROR: 100

[D][05:19:16][COMM]msg 0220 loss. last_tick:86736. cur_tick:87746. period:100
[D][05:19:16][COMM]msg 0221 loss. last_tick:86736. cur_tick:87746. period:100
[D][05:19:16][COMM]msg 0224 loss. last_tick:86736. cur_tick:87746. period:100
[D][05:19:16][COMM]msg 0260 loss. last_tick:86736. cur_tick:87747. period:100
[D][05:19:16][COMM]msg 0280 loss. last_tick:86736. cur_tick:87747. period:100
[D][05:19:16][COMM]msg 02C0 loss. last_tick:86736. cur_tick:87748. period:100
[D][05:19:16][COMM]msg 02C1 loss. last_tick:86736. cur_tick:87748. period:100
[D][05:19:16][COMM]msg 02C2 loss. last_tick:86736. cur_tick:87748. period:100
[D][05:19:16][COMM]msg 02E0 loss. last_tick:86736. cur_tick:87749. period:100
[D][05:19:16][COMM]msg 02E1 loss. last_tick:86736. cur_tick:87749. period:100
[D][05:19:16][COMM]msg 02E2 loss. last_tick:86736. cur_tick:87749. period:100
[D][05:19:16][COMM]msg 0300 loss. last_tick:86736. cur_tick:87750. period:100
[D][05:19:16][COMM]msg 0301 loss. last_tick:86736. cur_tick:87750. p

2025-07-31 21:39:08:533 ==>> eriod:100
[D][05:19:16][COMM]bat msg 0240 loss. last_tick:86736. cur_tick:87751. period:100. j,i:1 54
[D][05:19:16][COMM]bat msg 0241 loss. last_tick:86736. cur_tick:87751. period:100. j,i:2 55
[D][05:19:16][COMM]bat msg 0242 loss. last_tick:86736. cur_tick:87751. period:100. j,i:3 56
[D][05:19:16][COMM]bat msg 0244 loss. last_tick:86736. cur_tick:87752. period:100. j,i:5 58
[D][05:19:16][COMM]bat msg 024E loss. last_tick:86736. cur_tick:87752. period:100. j,i:15 68
[D][05:19:16][COMM]bat msg 024F loss. last_tick:86736. cur_tick:87752. period:100. j,i:16 69
[D][05:19:16][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 87753
[D][05:19:16][COMM]CAN message bat fault change: 0x00000000->0x0001802E 87753
[D][05:19:16][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 87753


2025-07-31 21:39:09:166 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 21:39:09:211 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:39:09:271 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 21:39:09:376 ==>>                                                                                                                                                                                                                                                                               
[D][05:19:17][COMM]Main Task receive event:65 finished processing
[D][05:19:17][COMM]Main Task receive event:66
[D][05:19:17][COMM]Try to Auto Lock Bat
[D][05:19:17][COMM]Main Task receive event:66 finished processing
[D][05:19:17][COMM]Receive Bat Lock cmd 0
[D][05:19:17][COMM]VBUS is 1
[D][05:19:17][COMM]Main Task receive event:60
[D][05:19:17][COMM]smart_helmet_vol=255,255
[D][05:19:17][COMM]BAT CAN get state1 Fail 204
[D][05:19:17][COMM]BAT CAN get soc Fail, 204
[D][05:19:17][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:17][COMM]BAT CAN get state2 fail 204
[D][05:19:17][COMM]get soh error
[E][05:19:17][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:17][COMM]report elecbike
[W][05:19:17][PROT]remove success[1

2025-07-31 21:39:09:481 ==>> 629955157],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:17][COMM]Main Task receive event:60 finished processing
[D][05:19:17][COMM]Main Task receive event:61
[D][05:19:17][COMM][D301]:type:3, trace id:280
[D][05:19:17][COMM]id[], hw[000
[D][05:19:17][COMM]get mcMaincircuitVolt error
[D][05:19:17][COMM]get mcSubcircuitVolt error
[D][05:19:17][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:17][COMM]BAT CAN get state1 Fail 204
[D][05:19:17][COMM]BAT CAN get soc Fail, 204
[D][05:19:17][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:17][COMM]BAT CAN get state2 fail 204
[D][05:19:17][COMM]get bat work mode err
[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:17][PROT]index:1
[D][05:19:17][PROT]is_s

2025-07-31 21:39:09:586 ==>> end:1
[D][05:19:17][PROT]sequence_num:11
[D][05:19:17][PROT]retry_timeout:0
[D][05:19:17][PROT]retry_times:3
[D][05:19:17][PROT]send_path:0x3
[D][05:19:17][PROT]msg_type:0x5d03
[D][05:19:17][PROT]===========================================================
[W][05:19:17][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955157]
[D][05:19:17][PROT]===========================================================
[D][05:19:17][PROT]Sending traceid[999999999990000C]
[D][05:19:17][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:17][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:17][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:17][COMM]Main Task receive event:61 finished processing
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:17][M2M ]m

2025-07-31 21:39:09:691 ==>> 2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:17][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:17][COMM]arm_hub_enable: hub power: 0
[D][05:19:17][HSDK]hexlog index save 0 6144 216 @ 0 : 0
[D][05:19:17][HSDK]write save hexlog index [0]
[D][05:19:17][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:17][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:17][COMM]read battery soc:255
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[

2025-07-31 21:39:09:781 ==>> D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:19:18][COMM]exit wheel stolen mode.
[D][05:19:18][COMM]Main Task receive event:68
[D][05:19:18][COMM]handlerWheelStolen evt type = 2.
[E][05:19:18][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:18][GNSS]stop locating
[D][05:19:18][GNSS]all continue location stop
[D][05:19:18][COMM]Main Task receive event:68 finished processing


2025-07-31 21:39:09:886 ==>>                               [W][05:19:18][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:18][COMM]arm_hub_enable: hub power: 0
[D][05:19:18][HSDK]hexlog index save 0 6144 216 @ 0 : 0
[D][05:19:18][HSDK]write save hexlog index [0]
[D][05:19:18][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:18][FCTY]F:[syncParaF

2025-07-31 21:39:09:917 ==>> romRamToFlash].L:[969] ready to write para flash


2025-07-31 21:39:10:238 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 21:39:10:358 ==>> [W][05:19:19][COMM]Power Off


2025-07-31 21:39:10:463 ==>> [W][05:19:19][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:19][COMM]arm_hub_en

2025-07-31 21:39:10:508 ==>> able: hub power: 0
[D][05:19:19][HSDK]hexlog index save 0 6144 216 @ 0 : 0
[D][05:19:19][HSDK]write save hexlog index [0]
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 21:39:10:535 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 21:39:10:544 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 21:39:10:569 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:39:10:674 ==>> 5A A5 02 5A A5 


2025-07-31 21:39:10:779 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:39:10:829 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:39:10:837 ==>> 检测【检测小电池关机电流】
2025-07-31 21:39:10:861 ==>> 开始小电池电流采样
2025-07-31 21:39:10:885 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:39:10:944 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 21:39:11:004 ==>> [D][05:19:19][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 2,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 3,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 4,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 5,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 6,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 7,volt = 19
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 8,volt = 19
[D][05:19:19][COMM]read battery soc:255
[W][05:19:19][COMM]Power Off


2025-07-31 21:39:11:952 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 21:39:11:998 ==>> CurrentBattery:ƽ��:58.69

2025-07-31 21:39:12:462 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 21:39:12:470 ==>> 【检测小电池关机电流】通过,【58.69uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 21:39:12:840 ==>> MES过站成功
2025-07-31 21:39:12:870 ==>> #################### 【测试结束】 ####################
2025-07-31 21:39:12:916 ==>> 关闭5V供电
2025-07-31 21:39:12:944 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:39:12:989 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:39:13:919 ==>> 关闭5V供电成功
2025-07-31 21:39:13:941 ==>> 关闭33V供电
2025-07-31 21:39:13:957 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:39:13:980 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:39:14:934 ==>> 关闭33V供电成功
2025-07-31 21:39:14:955 ==>> 关闭3.7V供电
2025-07-31 21:39:14:971 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:39:15:087 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:39:15:911 ==>>  

2025-07-31 21:39:15:940 ==>> 关闭3.7V供电成功
