2025-07-31 20:49:40:869 ==>> MES查站成功:
查站序号:P5100010053134D8验证通过
2025-07-31 20:49:40:882 ==>> 扫码结果:P5100010053134D8
2025-07-31 20:49:40:883 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:49:40:884 ==>> 测试参数版本:2024.10.11
2025-07-31 20:49:40:886 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:49:40:887 ==>> 检测【打开透传】
2025-07-31 20:49:40:889 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:49:40:957 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:49:41:286 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:49:41:311 ==>> 检测【检测接地电压】
2025-07-31 20:49:41:313 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:49:41:353 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:49:41:599 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:49:41:601 ==>> 检测【打开小电池】
2025-07-31 20:49:41:604 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:49:41:653 ==>> 6A A6 01 A6 6A 


2025-07-31 20:49:41:759 ==>> Battery ON
OVER 150


2025-07-31 20:49:41:888 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:49:41:891 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:49:41:893 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:49:41:955 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 20:49:42:177 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:49:42:180 ==>> 检测【等待设备启动】
2025-07-31 20:49:42:182 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:49:42:461 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:49:42:642 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:49:43:210 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:49:43:287 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:49:43:347 ==>> [W][05:17:49][GNSS]start sing locating


2025-07-31 20:49:43:742 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:49:44:214 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:49:44:272 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:49:44:275 ==>> 检测【产品通信】
2025-07-31 20:49:44:276 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:49:44:439 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:49:44:554 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:49:44:556 ==>> 检测【初始化完成检测】
2025-07-31 20:49:44:560 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:49:44:784 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:49:44:859 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:49:44:908 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:49:44:910 ==>> 检测【关闭大灯控制1】
2025-07-31 20:49:44:912 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:49:45:025 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:49:45:201 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:49:45:205 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:49:45:207 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:49:45:269 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:45:375 ==>>      5:17:5

2025-07-31 20:49:45:450 ==>> 1][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:49:45:508 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:49:45:512 ==>> 检测【关闭仪表供电】
2025-07-31 20:49:45:514 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:49:45:647 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:49:45:802 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:49:45:804 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:49:45:807 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:49:45:938 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:49:46:100 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:49:46:103 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:49:46:104 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:49:46:284 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<
[D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:46:385 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:49:46:387 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:49:46:390 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:49:46:527 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:49:46:673 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:49:46:676 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:49:46:678 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:49:46:754 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:49:46:860 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 27
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:49:46:959 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:49:46:961 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:49:46:962 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:49:47:056 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:49:47:248 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:49:47:251 ==>> 该项需要延时执行
2025-07-31 20:49:47:282 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:47:814 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change:

2025-07-31 20:49:47:844 ==>>  0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 20:49:48:300 ==>> [D][05:17:54][COMM]5659 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:48:405 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0....

2025-07-31 20:49:48:450 ==>>  
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:49:48:937 ==>>                                                                                                            lue:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo

2025-07-31 20:49:49:042 ==>>  is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D]

2025-07-31 20:49:49:147 ==>> [05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fa

2025-07-31 20:49:49:207 ==>> il 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 20:49:49:312 ==>> [D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:50:322 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7682 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:50:858 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:49:51:256 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:49:51:259 ==>> 检测【33V输入电压ADC】
2025-07-31 20:49:51:262 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:49:51:346 ==>> [D][05:17:57][COMM]8693 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:49:51:571 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:17:57][COMM]adc read out 24v adc:1318  volt:33336 mv
[D][05:17:57][COMM]adc read left brake adc:15  volt:19 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:57][COMM]adc read battery ts volt:13 mv
[D][05:17:57][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:49:51:791 ==>> 【33V输入电压ADC】通过,【32678mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:49:51:795 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:49:51:797 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:49:51:873 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1676mV
Get AD_V4 0mV
Get AD_V5 2788mV
Get AD_V6 2023mV
Get AD_V7 1101mV
OVER 150


2025-07-31 20:49:52:107 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:52:111 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:49:52:136 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1676mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:52:138 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:49:52:139 ==>> 原始值:【2788】, 乘以分压基数【2】还原值:【5576】
2025-07-31 20:49:52:162 ==>> 【TP68_VCC5V5(ADV5)】通过,【5576mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:49:52:165 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:49:52:188 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2023mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:49:52:191 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:49:52:220 ==>> 【TP1_VCC12V(ADV7)】通过,【1101mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:49:52:244 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:49:52:363 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1673mV
Get AD_V4 0mV
Get AD_V5 2785mV
Get AD_V6 1987mV
Get AD_V7 1102mV
OVER 150


2025-07-31 20:49:52:517 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:52:521 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:49:52:543 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1673mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:52:547 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:49:52:550 ==>> 原始值:【2785】, 乘以分压基数【2】还原值:【5570】
2025-07-31 20:49:52:570 ==>> 【TP68_VCC5V5(ADV5)】通过,【5570mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:49:52:572 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:49:52:596 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:49:52:598 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:49:52:627 ==>> 【TP1_VCC12V(ADV7)】通过,【1102mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:49:52:629 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:49:52:708 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 20:49:52:768 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1673mV
Get AD_V4 0mV
Get AD_V5 2781mV
Get AD_V6 2024mV
Get AD_V7 1101mV
OVER 150


2025-07-31 20:49:52:873 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:49:52:945 ==>> 【TP7_VCC3V3(ADV2)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:52:947 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:49:53:018 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1673mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:53:026 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:49:53:042 ==>> 原始值:【2781】, 乘以分压基数【2】还原值:【5562】
2025-07-31 20:49:53:069 ==>> 【TP68_VCC5V5(ADV5)】通过,【5562mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:49:53:072 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:49:53:126 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:49:53:130 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:49:53:187 ==>> 【TP1_VCC12V(ADV7)】通过,【1101mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:49:53:190 ==>> 检测【打开WIFI(1)】
2025-07-31 20:49:53:193 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:49:53:607 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:5

2025-07-31 20:49:53:638 ==>> 9][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:49:53:772 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:49:53:790 ==>> 检测【清空消息队列(1)】
2025-07-31 20:49:53:794 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:49:54:053 ==>>                                                                                                                                                                                                                     

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087738805

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539181

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[1

2025-07-31 20:49:54:138 ==>> 9] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:49:54:212 ==>>                                                                               [D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:49:54:362 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:49:54:384 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:49:54:387 ==>> 检测【打开GPS(1)】
2025-07-31 20:49:54:390 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:49:54:559 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:49:54:662 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:49:54:665 ==>> 检测【打开GSM联网】
2025-07-31 20:49:54:667 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:49:55:081 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
+

2025-07-31 20:49:55:157 ==>> CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CSQ: 27,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.113.26.209"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:49:55:215 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:49:55:232 ==>> 检测【打开仪表供电1】
2025-07-31 20:49:55:236 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:49:55:261 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01]

2025-07-31 20:49:55:307 ==>> [SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:49:55:612 ==>>                                                   ][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:49:55:772 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:49:55:776 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:49:55:780 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:49:55:959 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:49:56:063 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:49:56:066 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:49:56:069 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:49:56:249 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33038]


2025-07-31 20:49:56:309 ==>>                                                                

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:49:56:346 ==>> 【读取主控ADC采集的仪表电压】通过,【33038mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:49:56:351 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:49:56:354 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:49:56:371 ==>>                                       

2025-07-31 20:49:56:550 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:49:56:644 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:49:56:647 ==>> 检测【AD_V20电压】
2025-07-31 20:49:56:650 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:49:56:747 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:49:56:900 ==>> 1A A1 10 00 00 
Get AD_V20 1666mV
OVER 150
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255


2025-07-31 20:49:56:961 ==>>                      < 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:49:57:185 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,44,59,,,41,24,,,40,25,,,40,1*75

$GBGSV,3,2,09,39,,,40,60,,,39,40,,,39,5,,,42,1*41

$GBGSV,3,3,09,14,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1676.027,1676.027,53.543,2097152,2097152,2097152*4B

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:49:57:245 ==>> 本次取值间隔时间:490ms
2025-07-31 20:49:57:282 ==>> 【AD_V20电压】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:57:286 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:49:57:288 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:49:57:379 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:49:57:572 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:49:57:575 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:49:57:577 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:49:57:785 ==>> [D][05:18:04][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:04][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:49:57:857 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:49:57:861 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:49:57:863 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:49:57:951 ==>> 3A A3 02 01 A3 


2025-07-31 20:49:58:056 ==>> ON_OUT2
OVER 150


2025-07-31 20:49:58:132 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,43,59,,,41,24,,,41,25,,,41,1*7B

$GBGSV,5,2,17,14,,,41,39,,,40,1,,,40,3,,,40,1*7B

$GBGSV,5,3,17,60,,,39,40,,,39,16,,,38,9,,,37,1*45

$GBGSV,5,4,17,2,,,36,44,,,35,5,,,34,41,,,39,1*7D

$GBGSV,5,5,17,38,,,37,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.848,1616.848,51.697,2097152,2097152,2097152*43



2025-07-31 20:49:58:144 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:49:58:149 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:49:58:165 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:49:58:359 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:49:58:433 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:49:58:436 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:49:58:440 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:49:58:652 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:49:58:734 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:49:58:737 ==>> 检测【AD_V21电压】
2025-07-31 20:49:58:739 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:49:58:864 ==>> 1A A1 20 00 00 
Get AD_V21 1660mV
OVER 150


2025-07-31 20:49:58:879 ==>> 本次取值间隔时间:133ms
2025-07-31 20:49:58:894 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:49:58:904 ==>> 【AD_V21电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:49:58:907 ==>> 检测【关闭仪表供电2】
2025-07-31 20:49:58:909 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:49:59:151 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,44,59,,,41,24,,,41,25,,,41,1*78

$GBGSV,5,2,20,14,,,41,3,,,41,39,,,40,1,,,40,1*7E

$GBGSV,5,3,20,60,,,40,40,,,39,16,,,38,9,,,37,1*4F

$GBGSV,5,4,20,2,,,37,34,,,37,38,,,36,44,,,36,1*4B

$GBGSV,5,5,20,41,,,35,4,,,35,5,,,34,27,,,37,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1599.402,1599.402,51.149,2097152,2097152,2097152*47



2025-07-31 20:49:59:205 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:49:59:208 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:49:59:210 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:49:59:346 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:49:59:502 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:49:59:507 ==>> 检测【打开AccKey2供电】
2025-07-31 20:49:59:512 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:49:59:617 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:49:59:776 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:49:59:780 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:49:59:782 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:00:075 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:06][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:18:06][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:06][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:06][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:06][COMM]adc read battery ts volt:9 mv
[D][05:18:06][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:06][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2439  volt:3930 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:50:00:166 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:50:00:329 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33260mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:50:00:332 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:50:00:335 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:50:00:528 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:50:00:615 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:50:00:618 ==>> 该项需要延时执行
2025-07-31 20:50:00:771 ==>> $GBGGA,125004.561,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,43,59,,,41,24,,,41,25,,,41,1*78

$GBGSV,6,2,24,14,,,41,3,,,41,60,,,41,42,,,41,1*42

$GBGSV,6,3,24,39,,,40,1,,,39,40,,,39,16,,,38,1*42

$GBGSV,6,4,24,9,,,37,2,,,37,41,,,37,34,,,36,1*7A

$GBGSV,6,5,24,38,,,36,44,,,36,13,,,36,4,,,35,1*4D

$GBGSV,6,6,24,5,,,35,26,,,34,6,,,33,23,,,29,1*7C

$GBRMC,125004.561,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125004.561,0.000,1566.795,1566.795,50.138,2097152,2097152,2097152*5E



2025-07-31 20:50:00:876 ==>> [D][05:18:07][COMM]read batt

2025-07-31 20:50:00:905 ==>> ery soc:255


2025-07-31 20:50:01:745 ==>> $GBGGA,125005.541,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,59,,,41,25,,,41,1*78

$GBGSV,7,2,26,14,,,41,3,,,41,60,,,41,42,,,40,1*40

$GBGSV,7,3,26,39,,,40,1,,,39,40,,,39,16,,,38,1*41

$GBGSV,7,4,26,41,,,38,9,,,37,2,,,37,34,,,36,1*76

$GBGSV,7,5,26,38,,,36,44,,,36,13,,,36,4,,,35,1*4E

$GBGSV,7,6,26,6,,,35,5,,,34,26,,,34,10,,,33,1*73

$GBGSV,7,7,26,8,,,33,23,,,31,1*49

$GBRMC,125005.541,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125005.541,0.000,1557.888,1557.888,49.850,2097152,2097152,2097152*52



2025-07-31 20:50:02:723 ==>> $GBGGA,125006.521,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,44,24,,,41,59,,,41,25,,,41,1*7C

$GBGSV,7,2,26,14,,,41,3,,,40,60,,,40,42,,,40,1*40

$GBGSV,7,3,26,39,,,40,1,,,39,40,,,39,16,,,38,1*41

$GBGSV,7,4,26,41,,,38,9,,,37,2,,,37,34,,,36,1*76

$GBGSV,7,5,26,38,,,36,44,,,36,13,,,36,4,,,35,1*4E

$GBGSV,7,6,26,6,,,35,5,,,34,26,,,34,8,,,34,1*4D

$GBGSV,7,7,26,10,,,33,23,,,31,1*70

$GBRMC,125006.521,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125006.521,0.000,1556.289,1556.289,49.794,2097152,2097152,2097152*50



2025-07-31 20:50:02:903 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:50:03:618 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:50:03:622 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:50:03:626 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:03:741 ==>> $GBGGA,125007.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,59,,,42,25,,,41,1*7B

$GBGSV,7,2,26,14,,,41,3,,,41,60,,,41,42,,,40,1*40

$GBGSV,7,3,26,39,,,40,1,,,39,40,,,39,41,,,39,1*42

$GBGSV,7,4,26,16,,,38,9,,,37,2,,,37,13,,,37,1*70

$GBGSV,7,5,26,38,,,36,44,,,36,6,,,36,34,,,35,1*49

$GBGSV,7,6,26,4,,,35,5,,,34,26,,,34,8,,,34,1*4F

$GBGSV,7,7,26,10,,,34,23,,,32,1*74

$GBRMC,125007.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125007.501,0.000,782.262,782.262,715.395,2097152,2097152,2097152*68



2025-07-31 20:50:03:982 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:10][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:10][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:10][COMM]adc read battery ts volt:16 mv
[D][05:18:10][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:10][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2433  volt:3920 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:50:04:166 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:50:04:169 ==>> 检测【打开AccKey1供电】
2025-07-31 20:50:04:173 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:50:04:319 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:10][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:50:04:453 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:50:04:457 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:50:04:487 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:50:04:561 ==>> 1A A1 00 40 00 
Get AD_V14 2681mV
OVER 150


2025-07-31 20:50:04:666 ==>> $GBGGA,125008.501,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,59,,,42,24,,,42,60,,,41,1*74

$GBGSV,7,2,28,3,,,4

2025-07-31 20:50:04:711 ==>> 原始值:【2681】, 乘以分压基数【2】还原值:【5362】
2025-07-31 20:50:04:726 ==>> 1,14,,,41,25,,,41,39,,,40,1*43

$GBGSV,7,3,28,42,,,40,40,,,39,1,,,39,41,,,39,1*40

$GBGSV,7,4,28,16,,,38,2,,,37,13,,,37,38,,,36,1*4D

$GBGSV,7,5,28,9,,,36,44,,,36,6,,,36,34,,,36,1*76

$GBGSV,7,6,28,4,,,35,10,,,34,5,,,34,8,,,34,1*44

$GBGSV,7,7,28,26,,,33,23,,,32,12,,,30,11,,,27,1*7D

$GBRMC,125008.501,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125008.501,0.000,767.809,767.809,702.182,2097152,2097152,2097152*65



2025-07-31 20:50:04:747 ==>> 【读取AccKey1电压(ADV14)前】通过,【5362mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:50:04:750 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:50:04:754 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:05:073 ==>> [D][05:18:11][COMM]read battery soc:255
[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:11][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:11][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:11][COMM]adc read battery ts volt:13 mv
[D][05:18:11][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:11][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2433  volt:3920 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:50:05:346 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5551mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:50:05:352 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:50:05:356 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:50:05:546 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:50:05:726 ==>> $GBGGA,125009.501,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,38,,,36,9,,,36,44,,,36,6,,,36,1*74

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,31,1*48

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125009.501,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125009.501,0.000,767.005,767.005,701.446,2097152,2097152,2097152*6A



2025-07-31 20:50:05:755 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:50:05:762 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:50:05:767 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:50:05:861 ==>> 1A A1 00 40 00 
Get AD_V14 2674mV
OVER 150


2025-07-31 20:50:06:011 ==>> 原始值:【2674】, 乘以分压基数【2】还原值:【5348】
2025-07-31 20:50:06:033 ==>> 【读取AccKey1电压(ADV14)后】通过,【5348mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:50:06:036 ==>> 检测【打开WIFI(2)】
2025-07-31 20:50:06:040 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:50:06:277 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:50:06:315 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:50:06:321 ==>> 检测【转刹把供电】
2025-07-31 20:50:06:325 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:50:06:521 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:50:06:597 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:50:06:602 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:50:06:606 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:50:06:701 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:50:06:791 ==>> $GBGGA,125010.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,44,60,,,41,3,,,41,59,,,41,1*48

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,38,,,36,44,,,36,6,,,36,1*75

$GBGSV,8,6,29,34,,,36,4,,,35,10,,,34,5,,,34,1*77

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125010.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125010.501,0.000,769.146,769.146,703.404,2097152,2097152,2097152*66

[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2427mV
OVER 150


2025-07-31 20:50:06:866 ==>> 原始值:【2427】, 乘以分压基数【2】还原值:【4854】
2025-07-31 20:50:06:893 ==>> 【读取AD_V15电压(前)】通过,【4854mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:50:06:897 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:50:06:902 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:50:06:906 ==>> [D][05:18:13][COMM]read 

2025-07-31 20:50:06:926 ==>> battery soc:255


2025-07-31 20:50:07:001 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:50:07:031 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:50:07:061 ==>> 1A A1 01 00 00 
Get AD_V16 2455mV
OVER 150


2025-07-31 20:50:07:166 ==>> 原始值:【2455】, 乘以分压基数【2】还原值:【4910】
2025-07-31 20:50:07:205 ==>> 【读取AD_V16电压(前)】通过,【4910mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:50:07:208 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:50:07:211 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:07:216 ==>> +WIFISCAN:4,0,F88C21BCF57D,-36
+WIFISCAN:4,1,F62A7D2297A3,-63
+WIFISCAN:4,2,CC057790A741,-70
+WIFISCAN:4,3,CC057790A5C1,-80

[D][05:18:13][CAT1]wifi scan report total[4]


2025-07-31 20:50:07:497 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:13][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:13][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:13][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:13][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:13][COMM]adc read battery ts volt:13 mv
[D][05:18:13][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:13][COMM]adc read throttle brake in adc:3110  volt:5466 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2448  volt:3944 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 20:50:07:739 ==>> $GBGGA,125011.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,59,,,42,24,,,42,60,,,41,1*7A

$GBGSV,8,2,29,3,,,41,14,,,41,25,,,41,39,,,40,1*4D

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,38,,,36,44,,,36,6,,,36,1*75

$GBGSV,8,6,29,5,,,35,34,,,35,10,,,34,8,,,34,1*78

$GBGSV,8,7,29,4,,,34,26,,,33,23,,,32,12,,,30,1*45

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125011.501,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125011.501,0.000,769.148,769.148,703.406,2097152,2097152,2097152*65



2025-07-31 20:50:07:767 ==>> 【转刹把供电电压(主控ADC)】通过,【5466mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:50:07:771 ==>> 检测【转刹把供电电压】
2025-07-31 20:50:07:773 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:08:070 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:14][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:14][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:14][COMM]adc read battery ts volt:12 mv
[D][05:18:14][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3109  volt:5465 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:50:08:320 ==>> 【转刹把供电电压】通过,【5465mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:50:08:324 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:50:08:327 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:50:08:519 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:50:08:607 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:50:08:610 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:50:08:615 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:50:08:716 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:50:08:791 ==>> $GBGGA,125012.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,24,,,42,60,,,41,3,,,41,1*46

$GBGSV,8,2,29,59,,,41,14,,,41,25,,,41,39,,,40,1*72

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,7,,,37,13,,,37,9,,,37,1*75

$GBGSV,8,5,29,6,,,37,2,,,36,38,,,36,44,,,36,1*7E

$GBGSV,8,6,29,10,,,35,34,,,35,5,,,34,8,,,34,1*78

$GBGSV,8,7,29,4,,,34,26,,,33,23,,,32,12,,,30,1*45

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125012.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125012.501,0.000,768.432,768.432,702.751,2097152,2097152,2097152*66

[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:50:08:889 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:50:08:893 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:50:08:898 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:50:08:902 ==>> [D][05:18:15][COMM]read battery s

2025-07-31 20:50:08:926 ==>> oc:255


2025-07-31 20:50:09:001 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:50:09:031 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:50:09:061 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:50:09:163 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:50:09:167 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:50:09:170 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:50:09:260 ==>> 3A A3 03 01 A3 


2025-07-31 20:50:09:365 ==>> ON_OUT3
OVER 150


2025-07-31 20:50:09:474 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:50:09:478 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:50:09:481 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:50:09:561 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:50:09:756 ==>> $GBGGA,125013.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,39,,,40,42,,,40,1*7A

$GBGSV,8,3,29,14,,,40,40,,,39,1,,,39,41,,,39,1*4D

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,38,,,36,44,,,36,6,,,36,1*75

$GBGSV,8,6,29,34,,,35,4,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125013.501,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125013.501,0.000,767.002,767.002,701.443,2097152,2097152,2097152*64



2025-07-31 20:50:09:778 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:50:09:786 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:50:09:795 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:50:09:861 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:50:10:094 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:50:10:097 ==>> 检测【左刹电压测试1】
2025-07-31 20:50:10:101 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:10:368 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:16][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:16][COMM]adc read left brake adc:1715  volt:2260 mv
[D][05:18:16][COMM]adc read right brake adc:1719  volt:2266 mv
[D][05:18:16][COMM]adc read throttle adc:1715  volt:2260 mv
[D][05:18:16][COMM]adc read battery ts volt:13 mv
[D][05:18:16][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:16][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2383  volt:3839 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1425  volt:33038 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:50:10:664 ==>> 【左刹电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 20:50:10:669 ==>> 检测【右刹电压测试1】
2025-07-31 20:50:10:727 ==>> 【右刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 20:50:10:731 ==>> 检测【转把电压测试1】
2025-07-31 20:50:10:737 ==>> $GBGGA,125014.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,39,,,40,14,,,40,1*79

$GBGSV,8,3,29,40,,,39,1,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,6,,,37,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,5,,,35,34,,,35,10,,,34,8,,,34,1*78

$GBGSV,8,7,29,4,,,34,26,,,33,23,,,32,12,,,30,1*45

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125014.501,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125014.501,0.000,766.291,766.291,700.793,2097152,2097152,2097152*6C



2025-07-31 20:50:10:773 ==>> 【转把电压测试1】通过,【2260】符合目标值【2250】至【2500】要求!
2025-07-31 20:50:10:777 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:50:10:783 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:50:10:838 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:50:10:944 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:50:11:060 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:50:11:066 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:50:11:071 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:50:11:157 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:50:11:348 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:50:11:352 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:50:11:355 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:50:11:458 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:50:11:635 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:50:11:639 ==>> 检测【左刹电压测试2】
2025-07-31 20:50:11:643 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:50:11:728 ==>> $GBGGA,125015.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,59,,,41,24,,,41,1*7A

$GBGSV,8,2,29,25,,,41,3,,,40,39,,,40,14,,,40,1*4D

$GBGSV,8,3,29,40,,,39,1,,,39,42,,,39,16,,,38,1*43

$GBGSV,8,4,29,41,,,38,2,,,37,7,,,37,13,,,37,1*7C

$GBGSV,8,5,29,6,,,37,38,,,36,44,,,36,9,,,36,1*75

$GBGSV,8,6,29,5,,,35,34,,,35,10,,,34,8,,,34,1*78

$GBGSV,8,7,29,4,,,34,26,,,33,23,,,32,12,,,30,1*45

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125015.501,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125015.501,0.000,764.149,764.149,698.834,2097152,2097152,2097152*6F



2025-07-31 20:50:11:969 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:18][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:18][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:18][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:18][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:18][COMM]adc read battery ts volt:19 mv
[D][05:18:18][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:18:18][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2453  volt:3952 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1426  volt:33062 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:50:12:186 ==>> 【左刹电压测试2】通过,【6】符合目标值【0】至【50】要求!
2025-07-31 20:50:12:190 ==>> 检测【右刹电压测试2】
2025-07-31 20:50:12:222 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:50:12:225 ==>> 检测【转把电压测试2】
2025-07-31 20:50:12:259 ==>> 【转把电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 20:50:12:267 ==>> 检测【晶振检测】
2025-07-31 20:50:12:292 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:50:12:426 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:18][COMM][lf state:1][hf state:1]


2025-07-31 20:50:12:543 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:50:12:547 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:50:12:553 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:50:12:728 ==>> 1A A1 00 00 FC 
Get AD_V2 1658mV
Get AD_V3 1673mV
Get AD_V4 1650mV
Get AD_V5 2782mV
Get AD_V6 2021mV
Get AD_V7 1101mV
OVER 150
$GBGGA,125016.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,6,,,37,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,34,,,35,10,,,34,5,,,34,8,,,34,1*79

$GBGSV,8,7,29,4,,,34,26,,,33,23,,,32,12,,,31,1*44

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125016.501,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125016.501,0.000,767.719,767.719,702.099,2097152,2097152,2097152*61



2025-07-31 20:50:12:829 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:50:12:832 ==>> 检测【检测BootVer】
2025-07-31 20:50:12:837 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:50:12:938 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:50:13:240 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:19][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========
[D][05:18:19][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:19][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:19][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:19][FCTY]DeviceID    = 460130071539181
[D][05:18:19][FCTY]HardwareID  = 867222087738805
[D][05:18:19][FCTY]MoBikeID    = 9999999999
[D][05:18:19][FCTY]LockID      = FFFFFFFFFF
[D][05:18:19][FCTY]BLEFWVersion= 105
[D][05:18:19][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:18:19][FCTY]Bat         = 4044 mv
[D][05:18:19][FCTY]Current     = 150 ma
[D][05:18:19][FCTY]VBUS        = 11800 mv
[D][05:18:19][FCTY]TEMP= 0,BATID= 470,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:19][FCTY]Ext battery vol = 32, adc = 1285
[D][05:18:19][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 151 mv
[D][05:18:19][FCTY]Bike Type flag is invalied
[D][05:18:19][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:19][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:19][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:19][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:19][FCTY]CAT1

2025-07-31 20:50:13:285 ==>> _GNSS_VERSION = V3465b5b1
[D][05:18:19][FCTY]Bat1         = 3776 mv
[D][05:18:19][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:19][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:50:13:377 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:50:13:385 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:50:13:406 ==>> 检测【检测固件版本】
2025-07-31 20:50:13:409 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:50:13:413 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:50:13:416 ==>> 检测【检测蓝牙版本】
2025-07-31 20:50:13:434 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:50:13:440 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:50:13:463 ==>> 检测【检测MoBikeId】
2025-07-31 20:50:13:466 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:50:13:472 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:50:13:475 ==>> 检测【检测蓝牙地址】
2025-07-31 20:50:13:481 ==>> 取到目标值:FDD99F9FF918
2025-07-31 20:50:13:494 ==>> 【检测蓝牙地址】通过,【FDD99F9FF918】符合目标值【】要求!
2025-07-31 20:50:13:497 ==>> 提取到蓝牙地址:FDD99F9FF918
2025-07-31 20:50:13:502 ==>> 检测【BOARD_ID】
2025-07-31 20:50:13:521 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:50:13:524 ==>> 检测【检测充电电压】
2025-07-31 20:50:13:548 ==>> 【检测充电电压】通过,【4044mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:50:13:554 ==>> 检测【检测VBUS电压1】
2025-07-31 20:50:13:576 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:50:13:580 ==>> 检测【检测充电电流】
2025-07-31 20:50:13:602 ==>> 【检测充电电流】通过,【150ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:50:13:606 ==>> 检测【检测IMEI】
2025-07-31 20:50:13:612 ==>> 取到目标值:867222087738805
2025-07-31 20:50:13:627 ==>> 【检测IMEI】通过,【867222087738805】符合目标值【】要求!
2025-07-31 20:50:13:631 ==>> 提取到IMEI:867222087738805
2025-07-31 20:50:13:635 ==>> 检测【检测IMSI】
2025-07-31 20:50:13:641 ==>> 取到目标值:460130071539181
2025-07-31 20:50:13:658 ==>> 【检测IMSI】通过,【460130071539181】符合目标值【】要求!
2025-07-31 20:50:13:671 ==>> 提取到IMSI:460130071539181
2025-07-31 20:50:13:677 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:50:13:683 ==>> 取到目标值:460130071539181
2025-07-31 20:50:13:689 ==>> 【校验网络运营商(移动)】通过,【460130071539181】符合目标值【】要求!
2025-07-31 20:50:13:715 ==>> 检测【打开CAN通信】
2025-07-31 20:50:13:719 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:50:13:729 ==>> $GBGGA,125017.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,42,,,40,40,,,39,1,,,39,41,,,39,1*4E

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,6,,,37,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,34,,,35,4,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125017.501,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125017.501,0.000,768.430,768.430,702.749,2097152,2097152,2097152*6A



2025-07-31 20:50:13:759 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:50:13:970 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:50:13:974 ==>> 检测【检测CAN通信】
2025-07-31 20:50:13:977 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:50:14:081 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:50:14:141 ==>> [D][05:18:20][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 31493
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:50:14:201 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:50:14:261 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:50:14:265 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:50:14:270 ==>> 检测【关闭CAN通信】
2025-07-31 20:50:14:293 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:50:14:320 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:50:14:365 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:50:14:548 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:50:14:554 ==>> 检测【打印IMU STATE】
2025-07-31 20:50:14:560 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:50:14:809 ==>> $GBGGA,125014.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,60,,,41,3,,,41,59,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,1,,,40,1*43

$GBGSV,8,3,29,39,,,40,42,,,40,40,,,39,41,,,39,1*7B

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,9,,,37,6,,,37,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,5,,,35,34,,,35,4,,,35,10,,,34,1*75

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,31,1*48

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125014.506,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125014.506,7.974,769.857,769.857,704.054,2097152,2097152,2097152*6E

[W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:0
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:50:14:914 ==>> [D

2025-07-31 20:50:14:944 ==>> ][05:18:21][COMM]read battery soc:255


2025-07-31 20:50:15:091 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:50:15:095 ==>> 检测【六轴自检】
2025-07-31 20:50:15:101 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:50:15:248 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:21][CAT1]gsm read msg sub id: 12
[D][05:18:21][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:50:15:755 ==>> $GBGGA,125015.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,6,,,36,9,,,36,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125015.506,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125015.506,7.971,765.580,765.580,700.143,2097152,2097152,2097152*69



2025-07-31 20:50:16:745 ==>> $GBGGA,125016.506,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,14,,,41,25,,,41,39,,,40,1*78

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,7,,,37,13,,,37,6,,,36,1*7B

$GBGSV,8,5,29,2,,,36,9,,,36,38,,,36,44,,,36,1*70

$GBGSV,8,6,29,34,,,35,10,,,34,4,,,34,5,,,34,1*75

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125016.506,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125016.506,7.982,764.154,764.154,698.840,2097152,2097152,2097152*6C



2025-07-31 20:50:16:974 ==>> [D][05:18:23][COMM]read battery soc:255
[D][05:18:23][CAT1]<<< 
OK

[D][05:18:23][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:50:17:080 ==>> [D][05:18:23][COMM]Main Task receive event:142
[D][05:18:23][COMM]#####

2025-07-31 20:50:17:109 ==>> # 34450 imu self test OK ######
[D][05:18:23][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-16,-10,4049]
[D][05:18:23][COMM]Main Task receive event:142 finished processing


2025-07-31 20:50:17:221 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:50:17:226 ==>> 检测【打印IMU STATE2】
2025-07-31 20:50:17:232 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:50:17:456 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:50:17:513 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:50:17:536 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:50:17:542 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:50:17:654 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:50:17:759 ==>> $GBGGA,125017.506,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,

2025-07-31 20:50:17:798 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:50:17:805 ==>> 检测【检测VBUS电压2】
2025-07-31 20:50:17:814 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:50:17:864 ==>> ,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,39,,,40,42,,,40,1*7A

$GBGSV,8,3,29,14,,,40,1,,,39,40,,,39,41,,,39,1*4D

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,6,,,36,9,,,36,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125017.506,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125017.506,7.976,765.579,765.579,700.143,2097152,2097152,2097152*6C

[D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 13


2025-07-31 20:50:18:166 ==>> [D][05:18:24][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071539181
[D][05:18:24][FCTY]HardwareID  = 867222087738805
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:18:24][FCTY]Bat         = 4044 mv
[D][05:18:24][FCTY]Current     = 150 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 8, adc = 335
[D][05:18:24][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FC

2025-07-31 20:50:18:211 ==>> TY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3776 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 20:50:18:342 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:50:18:787 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071539181
[D][05:18:24][FCTY]HardwareID  = 867222087738805
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:18:24][FCTY]Bat         = 4044 mv
[D][05:18:24][FCTY]Current     = 150 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 4, adc = 171
[D][05:18:24][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 0 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3776 mv
[D][05:18:2

2025-07-31 20:50:18:877 ==>> 4][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]==========Modules-nRF5340 ==========
$GBGGA,125018.506,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,59,,,41,60,,,41,24,,,41,1*7A

$GBGSV,8,2,29,25,,,41,3,,,40,39,,,40,14,,,40,1*4D

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,9,,,37,1*45

$GBGSV,8,5,29,13,,,37,6,,,36,38,,,36,44,,,36,1*4E

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,33,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125018.506,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125018.506,7.973,764.863,764.863,699.487,2097152,2097152,2097152*6A



2025-07-31 20:50:18:891 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:50:19:237 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539181
[D][05:18:25][FCTY]HardwareID  = 867222087738805
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:18:25][FCTY]Bat         = 4044 mv
[D][05:18:25][FCTY]Current     = 150 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 3, adc = 147
[D][05:18:25][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFOR

2025-07-31 20:50:19:297 ==>> M = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3776 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][COMM]msg 0601 loss. last_tick:31490. cur_tick:36491. period:500
[D][05:18:25][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 36491


2025-07-31 20:50:19:432 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:50:20:186 ==>> [D][05:18:25][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:25][COMM]frm_peripheral_device_poweroff type 16.... 
[W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539181
[D][05:18:25][FCTY]HardwareID  = 867222087738805
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:18:25][FCTY]Bat         = 3844 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 4900 mv
$GBGGA,125019.506,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,59,,,41,24,,,41,25,,,41,1*7B

$GBGSV,8,2,29,3,,,40,60,,,40,14,,,40,39,,,39,1*43

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,38,1*41

$GBGSV,8,4,29,16,,,37,7,,,37,6,,,36,2,,,36,1*45

$GBGSV,8,5,29,9,,,36,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,3

2025-07-31 20:50:20:291 ==>> 3,23,,,32,12,,,30,1*49

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125019.506,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125019.506,7.981,759.865,759.865,694.917,2097152,2097152,2097152*6F

[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 123
[D][05:18:26][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 126 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3776 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][COMM]Main Task receive event:65
[D][05:18:26][COMM]main task tmp_sleep_event = 80
[D][05:18:26][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:26][COMM]Main Task receive event:65 finished processing
[D][05:18:26][COMM]Main Task receive event:60
[D][05:18:26][

2025-07-31 20:50:20:396 ==>> COMM]smart_helmet_vol=255,255
[D][05:18:26][COMM]BAT CAN get state1 Fail 204
[D][05:18:26][COMM]BAT CAN get soc Fail, 204
[W][05:18:26][GNSS]stop locating
[D][05:18:26][GNSS]stop event:8
[D][05:18:26][GNSS]all continue location stop
[W][05:18:26][GNSS]sing locating running
[D][05:18:26][COMM]report elecbike
[W][05:18:26][PROT]remove success[1629955106],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:26][COMM]Main Task receive event:60 finished processing
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]index:0
[D][05:18:26][PROT]is_send:1
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x3
[D][05:18:26][PROT]msg_type:0x5d03
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]Sending traceid[9999999999900005]
[D][05:18:26][BLE ]BLE_WRN [ble_service_get_current_send_

2025-07-31 20:50:20:501 ==>> enabled:28] ble is not connect

[D][05:18:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:26][PROT]index:0 1629955106
[D][05:18:26][PROT]is_send:0
[D][05:18:26][PROT]sequence_num:4
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retry_times:3
[D][05:18:26][PROT]send_path:0x2
[D][05:18:26][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]sending traceid [9999999999900005]
[D][05:18:26][PROT]Send_TO_M2M [1629955106]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:26][SAL ]sock send credit cnt[6]
[D][05:18:26][SAL ]sock send ind credit cnt[6]
[D][05:18:26][M2M ]m2m send data len[198]
[D][05:18:26][SAL ]Cellular task submsg id[10]
[D][05:18:26][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:26][CAT1]gsm read msg sub

2025-07-31 20:50:20:521 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:50:20:530 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:50:20:539 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:50:20:606 ==>>  id: 15
[D][05:18:26][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:26][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5A1C6501072F5ECAE94AB481166E5510ABBCF8704358ABED3CFAC52C88CBBEAE267E086C71CC983C1D9128EB9CE9198864DC83924DE9436045544830FD3517D81B84071A3F0CC7A3989C822E6F85BB71CB60E
[D][05:18:26][CAT1]<<< 
SEND OK

[D][05:18:26][CAT1]exec over: func id: 15, ret: 11
[D][05:18:26][CAT1]sub id: 15, ret: 11

[D][05:18:26][SAL ]Cellular task submsg id[68]
[D][05:18:26][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:26][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:26][M2M ]g_m2m_is_idle become true
[D][05:18:26][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:26][PROT]M2M Send ok [1629955106]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:50:20:711 ==>>                                                  A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,59,,,41,24,,,41,25,,,41,1*7B

$GBGSV,8,2,29,3,,,40,60,,,40,14,,,40,39,,,39,1*43

$GBGSV,8,3,29,42,,,39,1,,,38,40,,,38,41,,,38,1*41

$GBGSV,8,4,29,16,,,37,6,,,36,2,,,36,7,,,36,1*44

$GBGSV,8,5,29,9,,,36,13,,,36,44,,,36,4,,,35,1*7C

$GBGSV,8,6,29,38,,,35,10,,,34,5,,,34,8,,,34,1*75

$GBGSV,8,7,29,34,,,34,26,,,32,23,,,32,12,

2025-07-31 20:50:20:786 ==>> ,,30,1*77

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125020.506,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125020.506,7.891,755.587,755.587,691.005,2097152,2097152,2097152*6A

[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
                                         

2025-07-31 20:50:20:846 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:50:20:851 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:50:20:857 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:50:20:951 ==>> 5A A5 04 5A A5 


2025-07-31 20:50:21:056 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:50:21:173 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:50:21:185 ==>> 检测【打开WIFI(3)】
2025-07-31 20:50:21:196 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:50:21:371 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:50:21:460 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:50:21:465 ==>> 检测【扩展芯片hw】
2025-07-31 20:50:21:469 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:50:21:768 ==>> [D][05:18:27][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:27][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:27][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
$GBGGA,125021.506,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,24,,,41,1*4F

$GBGSV,8,2,29,25,,,41,39,,,40,60,,,40,42,,,40,1*7B

$GBGSV,8,3,29,14,,,40,40,,,39,1,,,38,41,,,38,1*4D

$GBGSV,8,4,29,16,,,37,6,,,36,2,,,36,7,,,36,1*44

$GBGSV,8,5,29,9,,,36,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,8,6,29,34,,,35,10,,,34,4,,,34,5,,,34,1*75

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,32,12,,,30,1*48

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125021.506,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125021.506,7.884,759.159,759.159,694.272,2097152,2097152,2097152*68



2025-07-31 20:50:22:014 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:50:22:019 ==>> 检测【扩展芯片boot】
2025-07-31 20:50:22:048 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:50:22:052 ==>> 检测【扩展芯片sw】
2025-07-31 20:50:22:081 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:50:22:091 ==>> 检测【检测音频FLASH】
2025-07-31 20:50:22:096 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:50:23:041 ==>> [D][05:18:28][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:28][COMM]----- get Acckey 1 and value:1------------
[D][05:18:28][COMM]----- get Acckey 2 and value:0------------
[D][05:18:28][COMM]------------ready to Power on Acckey 2------------
[W][05:18:28][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
+WIFISCAN:4,0,44A1917CAD81,-76
+WIFISCAN:4,1,CC057790A7C0,-77
+WIFISCAN:4,2,CC057790A5C0,-79
+WIFISCAN:4,3,CC057790A5C1,-79

[D][05:18:28][CAT1]wifi scan report total[4]
[D][05:18:28][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:28][COMM]----- get Acckey 1 and value:1------------
[D][05:18:28][COMM]----- get Acckey 2 and value:1------------
[D][05:18:28][COMM]more than the number of battery plugs
[D][05:18:28][COMM]VBUS is 1
[D][05:18:28][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:28][COMM]file:B50 exist
[D][05:18:28][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:28][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:28][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:28][COMM]Bat auth off fail, error:-1
[D][05:18:28][COMM]frm_peripheral_devi

2025-07-31 20:50:23:146 ==>> ce_poweron type 0.... 
[D][05:18:28][COMM]----- get Acckey 1 and value:1------------
[D][05:18:28][COMM]----- get Acckey 2 and value:1------------
[D][05:18:28][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:28][COMM]----- get Acckey 1 and value:1------------
[D][05:18:28][COMM]----- get Acckey 2 and value:1------------
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:28][COMM]file:B50 exist
[D][05:18:28][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:28][COMM]read file, len:10800, num:3
[D][05:18:28][COMM]--->crc16:0xb8a
[D][05:18:28][COMM]read file success
[W][05:18:28][COMM][Audio].l:[936].close hexlog save
[D][05:18:28][COMM]accel parse set 1
[D][05:18:28][COMM][Audio]mon:9,05:18:28
[D][05:18:28][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:28][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:28][COMM]Main Task receive event:65
[D][05:18:28][COMM]main task tmp_sleep_event = 80

2025-07-31 20:50:23:251 ==>> 
[D][05:18:28][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:28][COMM]Main Task receive event:65 finished processing
[D][05:18:28][COMM]Main Task receive event:66
[D][05:18:28][COMM]Try to Auto Lock Bat
[D][05:18:28][COMM]Main Task receive event:66 finished processing
[D][05:18:28][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:28][COMM]Main Task receive event:60
[D][05:18:28][COMM]smart_helmet_vol=255,255
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[D][05:18:28][COMM]get soc error
[E][05:18:28][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:28][COMM]report elecbike
[W][05:18:28][PROT]remove success[1629955108],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:28][COMM]Main Task receive event:60 finished processing
[D][05:18:28][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:28][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:28][COMM]Main Task receive event:61
[D]

2025-07-31 20:50:23:356 ==>> [05:18:28][COMM][D301]:type:3, trace id:280
[D][05:18:28][COMM]id[], hw[000
[D][05:18:28][COMM]get mcMaincircuitVolt error
[D][05:18:28][COMM]get mcSubcircuitVolt error
[D][05:18:28][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:28][COMM]BAT CAN get state1 Fail 204
[D][05:18:28][COMM]BAT CAN get soc Fail, 204
[D][05:18:28][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:28][PROT]index:1
[D][05:18:28][PROT]is_send:1
[D][05:18:28][PROT]sequence_num:5
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:3
[D][05:18:28][PROT]send_path:0x3
[D][05:18:28][PROT]msg_type:0x5d03
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]Sending traceid[9999999999900006]
[D][05:18:28][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:28][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:28][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:28][M2M ]m2m_task: control_queue type:[M2M_GSM_PO

2025-07-31 20:50:23:461 ==>> WER_ON]
[D][05:18:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:28][COMM]Receive Bat Lock cmd 0
[D][05:18:28][COMM]VBUS is 1
[D][05:18:28][COMM]get bat work state err
[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:28][COMM]Main Task receive event:61 finished processing
[D][05:18:28][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:28][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:28][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:28][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:28][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:28][COMM]f:

2025-07-31 20:50:23:566 ==>> [ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:28][GNSS]recv submsg id[3]
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:28][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
$GBGGA,125022.506,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,24,,,41,1*4F

$GBGSV,8,2,29,25,,,41,60,,,40,14,,,40,39,,,39,1*76

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,37,7,,,37,13,,,37,6,,,36,1*74

$GBGSV,8,5,29,2,,,36,9,,,36,38,,,36,44,,,36,1*70

$GBGSV,8,6,29,34,,,35,10,,,34,4,,,34,5,,,34,1*75

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,32,12,,,30,1*48

$GBGSV,8,8,29,11,,,28,1*77

$GBRMC,125022.506,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBG

2025-07-31 20:50:23:641 ==>> ST,125022.506,7.884,760.585,760.585,695.575,2097152,2097152,2097152*6A

[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:29][COMM]read battery soc:255


2025-07-31 20:50:23:746 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:50:24:753 ==>> $GBGGA,125024.506,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,39,,,40,14,,,40,1*79

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,7,,,37,6,,,36,2,,,36,1*4A

$GBGSV,8,5,29,9,,,36,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,32,12,,,31,1*49

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125024.506,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125024.506,7.881,764.147,764.147,698.832,2097152,2097152,2097152*6A



2025-07-31 20:50:24:798 ==>>                                          

2025-07-31 20:50:25:165 ==>> [D][05:18:31][PROT]CLEAN,SEND:0
[D][05:18:31][PROT]index:1 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [9999999999900006]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3D5D1D2D76D189527933E9495B1145B35810C701F7212F0AB7DF9BA48C8EE651AF6A64823B4275

2025-07-31 20:50:25:240 ==>> 9BA85E5947FD6DE1383A918A3235DE548181505621E9ADBF124ADF4F76276283A88AFDF1EBFFEDBC6E77575
[D][05:18:31][CAT1]<<< 
SEND OK

[D][05:18:31][CAT1]exec over: func id: 15, ret: 11
[D][05:18:31][CAT1]sub id: 15, ret: 11

[D][05:18:31][SAL ]Cellular task submsg id[68]
[D][05:18:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:31][M2M ]g_m2m_is_idle become true
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 20:50:25:300 ==>> [D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:50:25:785 ==>> $GBGGA,125025.506,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,14,,,40,39,,,39,1*77

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,2,,,37,7,,,37,13,,,37,1*7E

$GBGSV,8,5,29,6,,,36,9,,,36,38,,,36,44,,,36,1*74

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,32,12,,,30,1*48

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125025.506,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125025.506,7.888,764.148,764.148,698.833,2097152,2097152,2097152*63

                                                                   

2025-07-31 20:50:26:320 ==>> [D][05:18:32][COMM]43674 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:50:26:425 ==>> [D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:32][COMM]f

2025-07-31 20:50:26:486 ==>> :[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:32][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:32][COMM]accel parse set 0
[D][05:18:32][COMM][Audio].l:[1012].open hexlog save
[D][05:18:32][COMM]crc 108B
[D][05:18:32][COMM]flash test ok


2025-07-31 20:50:26:756 ==>> $GBGGA,125026.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,60,,,41,1*4F

$GBGSV,8,2,29,24,,,41,25,,,41,14,,,40,39,,,39,1*77

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,38,7,,,37,6,,,36,2,,,36,1*4A

$GBGSV,8,5,29,9,,,36,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,8,6,29,4,,,35,34,,,35,10,,,34,5,,,34,1*74

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,31,12,,,30,1*4B

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125026.506,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125026.506,7.582,762.011,762.011,696.879,2097152,2097152,2097152*67



2025-07-31 20:50:26:801 ==>>                                          

2025-07-31 20:50:27:163 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:50:27:168 ==>> 检测【打开喇叭声音】
2025-07-31 20:50:27:176 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:50:27:877 ==>> [D][05:18:33][COMM]44685 imu init OK
[W][05:18:33][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:33][COMM]file:A20 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:A20 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:33][COMM]read file, len:15228, num:4
[D][05:18:33][COMM]--->crc16:0x419c
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A

2025-07-31 20:50:27:962 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:50:27:968 ==>> 检测【打开大灯控制】
2025-07-31 20:50:27:979 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:50:28:001 ==>> 
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_proc

2025-07-31 20:50:28:087 ==>> ess].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
$GBGGA,125027.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,43,3,,,41,59,,,41,24,,,41,1*4F

$GBGSV,8,2,29,25,,,41,14,,,40,60,,,40,39,,,39,1*76

$GBGSV,8,3,29,1,,,39,40,,,39,42,,,39,41,,,39,1*40

$GBGSV,8,4,29,16,,,37,7,,,37,6,,,36,2,,,36,1*45

$GBGSV,8,5,29,9,,,36,13,,,36,38,,,36,44,,,36,1*40

$GBGSV,8,6,29,34,,,35,10,,,34,4,,,34,5,,,34,1*75

$GBGSV,8,7,29,8,,,34,26,,,32,23,,,31,12,,,30,1*4B

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125027.506,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125027.506,7.586,759.870,759.870,694.921,2097152,2097152,2097152*6C

[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. se

2025-07-31 20:50:28:147 ==>> nd ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:50:28:222 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:50:28:530 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:50:28:536 ==>> 检测【关闭仪表供电3】
2025-07-31 20:50:28:540 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:50:29:380 ==>> $GBGGA,125028.506,2301.2570740,N,11421.9413087,E,1,08,1.38,73.446,M,-1.770,M,,*59

$GBGSA,A,3,33,14,39,16,09,24,25,40,,,,,4.16,1.38,3.92,4*0D

$GBGSV,8,1,29,33,69,262,43,14,61,190,40,3,61,190,41,6,54,39,36,1*45

$GBGSV,8,2,29,59,52,129,41,39,52,10,39,16,52,350,37,1,48,126,39,1*75

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,2,46,238,36,7,44,190,37,1*73

$GBGSV,8,4,29,25,44,290,41,60,41,238,41,40,38,160,39,10,33,198,34,1*73

$GBGSV,8,5,29,4,32,112,34,26,31,241,32,5,24,258,34,13,22,209,36,1*77

$GBGSV,8,6,29,38,20,200,36,8,19,203,34,42,1,321,39,41,,,39,1*49

$GBGSV,8,7,29,44,,,36,34,,,35,23,,,31,12,,,30,1*75

$GBGSV,8,8,29,11,,,29,1*76

$GBRMC,125028.506,A,2301.2570740,N,11421.9413087,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:35][GNSS]HD8040 GPS
[D][05:18:35][GNSS]GPS diff_sec 124011113, report 0x42 frame
$GBGST,125028.506,0.380,0.272,0.239,0.417,1.359,2.759,12*56

[W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0
[D][05:18:35][COMM]Main Task receive event:131
[D][05:18:35][COMM]index:0,power_mode:0xFF
[D][05:18:35][COMM]index:1,sound_mode:0xFF
[D][05:18:35][COMM]index:2,gsensor_mode:0xF

2025-07-31 20:50:29:486 ==>> F
[D][05:18:35][COMM]index:3,report_freq_mode:0xFF
[D][05:18:35][COMM]index:4,report_period:0xFF
[D][05:18:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:35][COMM]index:6,normal_reset_period:0xFF
[D][05:18:35][COMM]index:7,spock_over_speed:0xFF
[D][05:18:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:35][COMM]index:16,imu_config_params:0xFF
[D][05:18:35][COMM]index:17,long_connect_params:0xFF
[D][05:18:35][COMM]index:18,detain_mark:0xFF
[D][05:18:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:35][COMM]index:21,mc_mode:0xFF
[D][05:18:35][COMM]index:22,S_mode:0xFF
[D][05:18:35][COMM]index:23,overweight:0xFF
[D][05:18:35][COMM]index:24,standstill_mode:0xFF
[D][05:18:35][COMM]index:25,night_mode:0xFF
[D][05:18:35][COMM]index:26,e

2025-07-31 20:50:29:590 ==>> xperiment1:0xFF
[D][05:18:35][COMM]index:27,experiment2:0xFF
[D][05:18:35][COMM]index:28,experiment3:0xFF
[D][05:18:35][COMM]index:29,experiment4:0xFF
[D][05:18:35][COMM]index:30,night_mode_start:0xFF
[D][05:18:35][COMM]index:31,night_mode_end:0xFF
[D][05:18:35][COMM]index:33,park_report_minutes:0xFF
[D][05:18:35][COMM]index:34,park_report_mode:0xFF
[D][05:18:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:35][COMM]index:38,charge_battery_para: FF
[D][05:18:35][COMM]index:39,multirider_mode:0xFF
[D][05:18:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:35][COMM]index:44,riding_duration_config:0xFF
[D][05:18:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:35][COMM]index:49,mc_load_startup:0xFF
[D][05:18:35][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:35][COMM]index:52,traffic_mode:0

2025-07-31 20:50:29:633 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:50:29:642 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:50:29:651 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:50:29:695 ==>> xFF
[D][05:18:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:35][COMM]index:63,experiment5:0xFF
[D][05:18:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:35][COMM]index:72,experiment6:0xFF
[D][05:18:35][COMM]index:73,experiment7:0xFF
[D][05:18:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:35][COMM]index:75,zero_value_from_server:-1
[D][05:18:35][COMM]index:76,multirider_threshold:255
[D][05:18:35][COMM]inde

2025-07-31 20:50:29:800 ==>> x:77,experiment8:255
[D][05:18:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:35][COMM]index:83,loc_report_interval:255
[D][05:18:35][COMM]index:84,multirider_threshold_p2:255
[D][05:18:35][COMM]index:85,multirider_strategy:255
[D][05:18:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:35][COMM]index:90,weight_param:0xFF
[D][05:18:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:35][COMM]index:95,current_limit:0xFF
[D][05:18:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:35][COMM]index:100,location_mode:0xFF

[W][05:18:35][PROT]remove success[1629955115],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:35][COMM]Main Task receive event:131 finished processing
[D][05:18:35][M2M ]m2m_task: cont

2025-07-31 20:50:29:905 ==>> rol_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:35][COMM]read battery soc:255
$GBGGA,125029.006,2301.2576553,N,11421.9413529,E,1,08,1.38,75.292,M,-1.770,M,,*53

$GBGSA,A,3,33,14,39,16,09,24,25,40,,,,,4.16,1.38,3.92,4*0D

$GBGSV,8,1,29,33,69,262,43,14,61,190,40,3,61,190,41,6,54,39,36,1*45

$GBGSV,8,2,29,59,52,129,41,39,52,10,40,16,52,350,37,1,48,126,39,1*7B

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,2,46,238,36,7,44,190,37,1*73

$GBGSV,8,4,29,25,44,290,41,60,41,238,41,40,38,160,39,10,33,198,34,1*73

$GBGSV,8,5,29,4,32,112,34,26,31,241,32,5,24,258,34,13,22,209,37,1*76

$GBGSV,8,6,29,38,20,200,36,8,19,203,34,42,1,321,39,41,,,39,1*49

$GBGSV,8,7,29,44,,,36,34,,,35,23,,,31,12,,,30,1*75

$GBGSV,8,8,29,11,,,29,1*76

$GBGSV,2,1,05,33,69,262,41,39,52,10,40,24,47,19,39,25,44,290,38,5*70

$GBGSV,2,2,05,40,38,160,38,5*44

$GBRMC,125029.006,A,2301.2576553,N,11421.9413529,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125029.006,1.189,1.543,1.225,2.535,1.400,2.108,8.114*7B



2025-07-31 20:50:29:995 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:50:30:181 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:50:30:191 ==>> 检测【读大灯电压】
2025-07-31 20:50:30:198 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:50:30:506 ==>> [D][05:18:36][PROT]CLEAN,SEND:1
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:2
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B37FA20AE18E03B34447BA3E3F4765516E397AEDC2BF454E656D4D3F2DE166EE27E17A2BC3608B22F62EF8FCD4BC

2025-07-31 20:50:30:611 ==>> 05ED53E327516520799701121E0A6D7DE651A365E5C30696DEB60AEE7D4AA9CB24B00147C9
$GBGGA,125030.000,2301.2578105,N,11421.9413445,E,1,16,0.78,75.895,M,-1.770,M,,*58

$GBGSA,A,3,33,03,14,39,16,59,02,09,24,01,25,60,1.97,0.78,1.81,4*0A

$GBGSA,A,3,40,04,38,05,,,,,,,,,1.97,0.78,1.81,4*0E

$GBGSV,8,1,29,33,69,262,43,3,62,190,40,14,61,190,40,6,54,39,36,1*47

$GBGSV,8,2,29,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*77

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,38,7,44,190,36,1*70

$GBGSV,8,4,29,25,44,290,41,60,43,241,40,40,38,160,39,10,33,198,34,1*7E

$GBGSV,8,5,29,4,31,113,34,26,31,241,32,41,27,317,39,38,25,192,36,1*4E

$GBGSV,8,6,29,5,24,258,34,13,22,209,36,8,19,203,34,44,17,95,36,1*4D

$GBGSV,8,7,29,34,17,149,35,42,1,321,39,12,,,30,23,,,30,1*46

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,2,1,05,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,39,5*7C

$GBGSV,2,2,05,40,38,160,38,5*44

$GBRMC,125030.000,A,2301.2578105,N,11421.9413445,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,125030.000,1.624,0.330,0.331,0.515,1.512,1.948,6.288*76

[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1

2025-07-31 20:50:30:671 ==>> ]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]
[W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[32807]
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:50:30:726 ==>> 【读大灯电压】通过,【32807mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:50:30:736 ==>> 检测【关闭大灯控制2】
2025-07-31 20:50:30:765 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:50:30:822 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 20:50:30:927 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:50:31:014 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:50:31:023 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:50:31:032 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:50:31:405 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
$GBGGA,125031.000,2301.2578653,N,11421.9413256,E,1,21,0.61,76.112,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,39,16,59,02,09,24,01,25,60,1.27,0.61,1.12,4*03

$GBGSA,A,3,40,13,04,41,38,05,44,34,26,,,,1.27,0.61,1.12,4*03

$GBGSV,8,1,29,33,69,262,43,3,62,190,40,14,61,190,40,6,54,39,36,1*47

$GBGSV,8,2,29,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*77

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,38,7,44,190,37,1*71

$GBGSV,8,4,29,25,44,290,41,60,43,241,40,40,38,160,39,13,37,218,36,1*70

$GBGSV,8,5,29,10,33,198,34,4,31,113,34,41,27,317,39,38,25,192,35,1*4B

$GBGSV,8,6,29,5,24,258,34,8,19,203,34,44,17,95,36,34,17,149,35,1*4A

$GBGSV,8,7,29,26,9,54,32,42,1,321,39,12,,,30,23,,,30,1*40

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,3,1,09,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,39,5*71

$GBGSV,3,2,09,40,38,160,38,41,27,317,35,38,25,192,34,44,17,95,33,5*41

$GBGSV,3,3,09,34,17,149,29,5*4D

$GBRMC,125031.000,A,2301.2578653,N,11421.9413256,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,125031.000,1.976,0.259,0.258,0.367,1.651,1.916,4.901*7B

[D][05:18:37][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:50:31:450 ==>>                                                                                                   

2025-07-31 20:50:31:548 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:50:31:557 ==>> 检测【打开WIFI(4)】
2025-07-31 20:50:31:565 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:50:31:771 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:50:31:883 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:50:31:893 ==>> 检测【EC800M模组版本】
2025-07-31 20:50:31:912 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:50:32:044 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:50:32:480 ==>> $GBGGA,125032.000,2301.2579393,N,11421.9413104,E,1,24,0.57,75.719,M,-1.770,M,,*51

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.24,0.57,1.10,4*07

$GBGSA,A,3,60,40,13,08,10,04,41,38,05,44,34,26,1.24,0.57,1.10,4*08

$GBGSV,8,1,29,33,69,262,44,3,62,190,41,14,61,190,40,6,52,347,36,1*7D

$GBGSV,8,2,29,39,52,10,40,16,52,350,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,7,44,190,37,1*70

$GBGSV,8,4,29,25,44,290,41,60,43,241,41,40,38,160,39,13,37,218,37,1*70

$GBGSV,8,5,29,42,36,165,40,8,33,207,34,10,33,188,34,4,31,113,35,1*7B

$GBGSV,8,6,29,41,27,317,39,38,25,192,36,5,24,258,34,44,17,95,36,1*7B

$GBGSV,8,7,29,34,17,149,35,26,9,54,32,23,5,256,31,12,,,30,1*73

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,3,1,10,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*77

$GBGSV,3,2,10,40,38,160,37,41,27,317,36,38,25,192,34,44,17,95,33,5*45

$GBGSV,3,3,10,34,17,149,30,26,9,54,31,5*73

$GBRMC,125032.000,A,2301.2579393,N,11421.9413104,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125032.000,1.996,0.232,0.228,0.325,1.608,1.800,4.131*7B

[D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_O

2025-07-31 20:50:32:525 ==>> CPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132
[D][05:18:38][COMM]49768 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:50:32:668 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:50:32:674 ==>> 检测【配置蓝牙地址】
2025-07-31 20:50:32:682 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:50:32:843 ==>> [W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:39][COMM]read battery soc:255


2025-07-31 20:50:32:873 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FDD99F9FF918>】
2025-07-31 20:50:33:068 ==>> [D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:39][COMM]accel parse set 0
[D][05:18:39][COMM][Audio].l:[1012].open hexlog save
+WIFISCAN:4,0,F88C21BCF57D,-39
+WIFISCAN:4,1,F42A7D1297A3,-63
+WIFISCAN:4,2,74C330CCAB10,-74
+WIFISCAN:4,3,CC057790A5C1,-78

[D][05:18:39][CAT1]wifi scan report total[4]
recv ble 1
recv ble 2
ble set mac ok :fd,d9,9f,9f,f9,18
enable filters ret : 0

2025-07-31 20:50:33:149 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:50:33:155 ==>> 检测【BLETEST】
2025-07-31 20:50:33:161 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:50:33:263 ==>> 4A A4 01 A4 4A 


2025-07-31 20:50:33:368 ==>> $GBGGA,125033.000,2301.2580604,N,11421.9412459,E,1,26,0.53,75.515,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.07,0.53,0.92,4*09

$GBGSA,A,3,60,40,13,42,08,10,04,41,38,05,44,34,1.07,0.53,0.92,4*04

$GBGSA,A,3,26,23,,,,,,,,,,,1.07,0.53,0.92,4*06

$GBGSV,8,1,29,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*7A

$GBGSV,8,2,29,39,52,10,39,16,52,350,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,7,44,190,37,1*70

$GBGSV,8,4,29,25,44,290,41,60,43,241,41,40,38,160,39,13,37,218,37,1*70

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,35

2025-07-31 20:50:33:458 ==>> ,1*75

$GBGSV,8,6,29,41,27,317,39,38,25,192,35,5,24,258,34,44,17,95,36,1*78

$GBGSV,8,7,29,34,17,149,35,26,9,54,32,23,5,256,31,12,,,30,1*73

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,11,40,38,160,37,42,36,165,38,41,27,317,36,38,25,192,34,5*74

$GBGSV,3,3,11,44,17,95,33,34,17,149,30,26,9,54,31,5*78

$GBRMC,125033.000,A,2301.2580604,N,11421.9412459,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125033.000,2.175,0.210,0.209,0.292,1.686,1.834,3.748*7C

recv ble 1
recv ble 2
<BSJ*MAC:FDD99F9FF918*RSSI:-28*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FDD99F9FF91899999OVER 150
                                      

2025-07-31 20:50:33:533 ==>> [D][05:18:39][GNSS]recv submsg id[3]


2025-07-31 20:50:34:189 ==>> 【BLETEST】通过,【-28dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:50:34:197 ==>> 该项需要延时执行
2025-07-31 20:50:34:427 ==>> $GBGGA,125034.000,2301.2580864,N,11421.9412360,E,1,26,0.53,75.521,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.07,0.53,0.92,4*09

$GBGSA,A,3,60,40,13,42,08,10,04,41,38,05,44,34,1.07,0.53,0.92,4*04

$GBGSA,A,3,26,23,,,,,,,,,,,1.07,0.53,0.92,4*06

$GBGSV,8,1,29,33,69,262,42,3,62,190,41,14,61,190,40,6,52,347,36,1*7B

$GBGSV,8,2,29,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*77

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,38,7,44,190,36,1*70

$GBGSV,8,4,29,25,44,290,40,60,43,241,40,40,38,160,38,13,37,218,36,1*70

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,34,1*74

$GBGSV,8,6,29,41,27,317,38,38,25,192,35,5,24,258,34,44,17,95,36,1*79

$GBGSV,8,7,29,34,17,149,35,26,9,54,32,23,5,256,30,12,,,30,1*72

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,11,40,38,160,37,42,36,165,39,41,27,317,36,38,25,192,34,5*75

$GBGSV,3,3,11,44,17,95,34,34,17,149,30,26,9,54,31,5*7F

$GBRMC,125034.000,A,2301.2580864,N,11421.9412360,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125034.000,2.452,0.195,0.195,0.273,1.838,1.956,3.581*75



2025-07-31 20:50:34:862 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:50:35:619 ==>> $GBGGA,125035.000,2301.2580931,N,11421.9412485,E,1,26,0.53,75.544,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.07,0.53,0.92,4*09

$GBGSA,A,3,60,40,13,42,08,10,04,41,38,05,44,34,1.07,0.53,0.92,4*04

$GBGSA,A,3,26,23,,,,,,,,,,,1.07,0.53,0.92,4*06

$GBGSV,8,1,29,33,69,262,42,3,62,190,40,14,61,190,40,6,52,347,36,1*7A

$GBGSV,8,2,29,39,52,10,38,16,52,350,37,59,50,128,41,2,48,239,36,1*76

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,7,44,190,36,1*71

$GBGSV,8,4,29,25,44,290,40,60,43,241,40,40,38,160,38,13,37,218,36,1*70

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,35,1*75

$GBGSV,8,6,29,41,27,317,38,38,25,192,35,5,24,258,34,44,17,95,36,1*79

$GBGSV,8,7,29,34,17,149,35,26,9,54,32,23,5,256,30,12,,,30,1*72

$GBGSV,8,8,29,11,,,28,1*77

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,11,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*7A

$GBGSV,3,3,11,44,17,95,34,34,17,149,30,26,9,54,31,5*7F

$GBRMC,125035.000,A,2301.2580931,N,11421.9412485,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:18:41][GNSS][RTK]first enter, init gPos info.
$GBGST,125035.000,2.441,0.197,0.

2025-07-31 20:50:35:724 ==>> 196,0.276,1.816,1.917,3.375*76

[D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:1
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B9

2025-07-31 20:50:35:799 ==>> 81113311331133113311331B88B3932D1BD82A4FDC50B3C7AE016CBCEB46AC2B7D115CAE2E903BB48C1071D9B9CF9EA9869B555A8449C993008C71938610C96ABC82ABC08FBE595DF1FCCE2C062AA0E9C2B0A8C9F664D1425439BCFB06B28D9E
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 20:50:36:424 ==>> $GBGGA,125036.000,2301.2581020,N,11421.9412470,E,1,27,0.53,75.547,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.06,0.53,0.92,4*08

$GBGSA,A,3,60,07,40,13,42,08,10,04,41,38,05,44,1.06,0.53,0.92,4*05

$GBGSA,A,3,34,26,23,,,,,,,,,,1.06,0.53,0.92,4*00

$GBGSV,8,1,29,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*7A

$GBGSV,8,2,29,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*77

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,25,44,290,41,1*42

$GBGSV,8,4,29,60,43,241,41,7,41,177,36,40,38,160,39,13,37,218,36,1*4E

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,34,1*74

$GBGSV,8,6,29,41,27,317,39,38,25,192,36,5,24,258,34,44,17,95,36,1*7B

$GBGSV,8,7,29,34,17,149,35,12,17,120,30,26,9,54,32,11,6,169,28,1*43

$GBGSV,8,8,29,23,5,256,30,1*7B

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,41,25,44,290,40,5*77

$GBGSV,3,2,11,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*7A

$GBGSV,3,3,11,44,17,95,34,34,17,149,30,26,9,54,31,5*7F

$GBRMC,125036.000,A,2301.2581020,N,11421.9412470,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125036.000,2.414,0.211,0.210,0.292,1.787,1.877,3.216*7B



2025-07-31 20:50:36:855 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:50:37:425 ==>> $GBGGA,125037.000,2301.2581045,N,11421.9412454,E,1,29,0.51,75.661,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,0.99,0.51,0.84,4*0A

$GBGSA,A,3,60,07,40,13,42,08,10,04,41,38,05,44,0.99,0.51,0.84,4*07

$GBGSA,A,3,34,12,26,11,23,,,,,,,,0.99,0.51,0.84,4*01

$GBGSV,8,1,29,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*7A

$GBGSV,8,2,29,39,52,10,40,16,52,350,37,59,50,128,41,2,48,239,36,1*79

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,25,44,290,41,1*42

$GBGSV,8,4,29,60,43,241,41,7,41,177,36,40,38,160,39,13,37,218,36,1*4E

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,34,1*74

$GBGSV,8,6,29,41,27,317,39,38,25,192,36,5,24,258,34,44,17,95,36,1*7B

$GBGSV,8,7,29,34,17,149,35,12,17,120,30,26,9,54,32,11,6,169,28,1*43

$GBGSV,8,8,29,23,5,256,30,1*7B

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,41,25,44,290,40,5*77

$GBGSV,3,2,11,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*7A

$GBGSV,3,3,11,44,17,95,34,34,17,149,30,26,9,54,31,5*7F

$GBRMC,125037.000,A,2301.2581045,N,11421.9412454,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125037.000,3.129,0.214,0.213,0.293,2.200,2.271,3.444*70



2025-07-31 20:50:38:416 ==>> $GBGGA,125038.000,2301.2581173,N,11421.9412564,E,1,29,0.51,75.697,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,0.99,0.51,0.84,4*0A

$GBGSA,A,3,60,07,40,13,42,08,10,04,41,38,05,44,0.99,0.51,0.84,4*07

$GBGSA,A,3,34,12,26,11,23,,,,,,,,0.99,0.51,0.84,4*01

$GBGSV,8,1,29,33,69,262,43,3,62,190,40,14,61,190,40,6,52,347,36,1*7B

$GBGSV,8,2,29,39,52,10,39,16,52,350,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,8,3,29,9,47,324,36,24,47,19,41,1,46,125,39,25,44,290,41,1*42

$GBGSV,8,4,29,60,43,241,41,7,41,177,36,40,38,160,39,13,37,218,36,1*4E

$GBGSV,8,5,29,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,34,1*74

$GBGSV,8,6,29,41,27,317,39,38,25,192,36,5,24,258,34,44,17,95,36,1*7B

$GBGSV,8,7,29,34,17,149,35,12,17,120,30,26,9,54,32,11,6,169,28,1*43

$GBGSV,8,8,29,23,5,256,30,1*7B

$GBGSV,3,1,11,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,11,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*7A

$GBGSV,3,3,11,44,17,95,34,34,17,149,30,26,9,54,31,5*7F

$GBRMC,125038.000,A,2301.2581173,N,11421.9412564,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125038.000,3.037,0.210,0.209,0

2025-07-31 20:50:38:446 ==>> .289,2.143,2.208,3.316*7F



2025-07-31 20:50:38:855 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:50:39:422 ==>> $GBGGA,125039.000,2301.2581172,N,11421.9412566,E,1,28,0.53,75.776,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.01,0.53,0.86,4*0A

$GBGSA,A,3,60,07,40,13,42,08,10,04,41,38,44,34,1.01,0.53,0.86,4*05

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.01,0.53,0.86,4*06

$GBGSV,7,1,28,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*74

$GBGSV,7,2,28,39,52,10,39,16,52,350,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,1,46,125,39,25,44,290,41,1*4C

$GBGSV,7,4,28,60,43,241,41,7,41,177,36,40,38,160,38,13,37,218,36,1*41

$GBGSV,7,5,28,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,34,1*7A

$GBGSV,7,6,28,41,27,317,39,38,25,192,36,44,17,95,36,34,17,149,35,1*45

$GBGSV,7,7,28,12,17,120,30,26,9,54,32,11,6,169,28,23,5,256,30,1*70

$GBGSV,3,1,12,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*75

$GBGSV,3,2,12,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*79

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,54,31,23,5,256,30,5*7A

$GBRMC,125039.000,A,2301.2581172,N,11421.9412566,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125039.000,3.207,0.224,0.221,0.304,2.232,2.289,3.322*7D



2025-07-31 20:50:40:422 ==>> $GBGGA,125040.000,2301.2581303,N,11421.9412637,E,1,28,0.53,75.733,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.01,0.53,0.86,4*0A

$GBGSA,A,3,60,07,40,13,42,08,10,04,41,38,44,34,1.01,0.53,0.86,4*05

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.01,0.53,0.86,4*06

$GBGSV,7,1,28,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*74

$GBGSV,7,2,28,39,52,10,40,16,52,350,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,1,46,125,38,25,44,290,41,1*4D

$GBGSV,7,4,28,60,43,241,40,7,41,177,36,40,38,160,38,13,37,218,36,1*40

$GBGSV,7,5,28,42,36,165,39,8,33,207,34,10,33,188,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,27,317,39,38,25,192,35,44,17,95,36,34,17,149,35,1*46

$GBGSV,7,7,28,12,17,120,30,26,9,54,33,11,6,169,28,23,5,256,30,1*71

$GBGSV,3,1,12,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*75

$GBGSV,3,2,12,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*79

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,54,31,23,5,256,30,5*7A

$GBRMC,125040.000,A,2301.2581303,N,11421.9412637,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125040.000,3.222,0.225,0.221,0.305,2.236,2.289,3.271*77



2025-07-31 20:50:40:842 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]CLEAN:1
[D][05:18:46][PROT]index:0 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:4
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:2
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900005]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:47][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B5D1FB86C60A99302BB0BD

2025-07-31 20:50:40:948 ==>> FB424BB13DBB5149BE51FDA598F7CD578EB911F902FB77C202D119D17587963E48640DC9353F1C224B7553E689A9C6D2421C36A0CF593F281415A08F3D4BC1E2363AD3301FAC6C4B
[D][05:18:47][CAT1]<<< 
SEND OK

[D][05:18:47][CAT1]exec over: func id: 15, ret: 11
[D][05:18:47][CAT1]sub id: 15, ret: 11

[D][05:18:47][SAL ]Cellular task submsg id[68]
[D][05:18:47][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:47][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:47][M2M ]g_m2m_is_idle become true
[D][05:18:47][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:47][PROT]M2M Send ok [1629955127]
                                         

2025-07-31 20:50:41:407 ==>> $GBGGA,125041.000,2301.2581348,N,11421.9412553,E,1,28,0.52,75.749,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,262,43,3,62,190,41,14,61,190,40,6,52,347,36,1*74

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,40,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,40,1*71

$GBGSV,7,4,28,7,41,177,36,40,38,160,38,13,37,218,36,42,36,165,39,1*49

$GBGSV,7,5,28,8,33,207,34,10,33,188,34,4,31,113,35,41,27,317,39,1*7F

$GBGSV,7,6,28,38,25,192,35,5,24,258,34,44,17,95,36,34,17,149,35,1*72

$GBGSV,7,7,28,12,17,120,30,26,9,54,33,11,6,169,28,23,5,256,30,1*71

$GBGSV,3,1,12,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*75

$GBGSV,3,2,12,40,38,160,38,42,36,165,40,41,27,317,36,38,25,192,34,5*77

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,54,30,23,5,256,30,5*7B

$GBRMC,125041.000,A,2301.2581348,N,11421.9412553,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125041.000,3.256,0.231,0.229,0.315,2.251,2.300,3.237*7A



2025-07-31 20:50:42:420 ==>> $GBGGA,125042.000,2301.2581383,N,11421.9412338,E,1,28,0.52,75.724,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,262,43,3,62,190,40,14,61,190,40,6,52,347,36,1*75

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,40,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,40,1*71

$GBGSV,7,4,28,7,41,177,36,40,38,160,39,13,37,218,36,42,36,165,39,1*48

$GBGSV,7,5,28,8,33,207,34,10,33,188,34,4,31,113,35,41,27,317,39,1*7F

$GBGSV,7,6,28,38,25,192,36,5,24,258,34,44,17,95,36,34,17,149,35,1*71

$GBGSV,7,7,28,12,17,120,30,26,9,54,33,11,6,169,28,23,5,256,30,1*71

$GBGSV,3,1,12,33,69,262,42,39,52,10,40,24,47,19,40,25,44,290,40,5*75

$GBGSV,3,2,12,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*79

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,54,30,23,5,256,29,5*73

$GBRMC,125042.000,A,2301.2581383,N,11421.9412338,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125042.000,3.448,0.234,0.232,0.320,2.352,2.396,3.288*70



2025-07-31 20:50:42:862 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:50:43:417 ==>> $GBGGA,125043.000,2301.2581416,N,11421.9412245,E,1,28,0.52,75.683,M,-1.770,M,,*56

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,262,43,3,62,190,40,14,61,190,40,6,52,347,36,1*75

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,40,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,40,1*71

$GBGSV,7,4,28,7,41,177,36,40,38,160,39,13,37,218,36,42,36,165,39,1*48

$GBGSV,7,5,28,8,33,207,34,10,33,189,34,4,31,113,34,41,27,317,39,1*7F

$GBGSV,7,6,28,38,25,192,35,5,24,258,34,44,17,95,36,34,17,149,35,1*72

$GBGSV,7,7,28,12,17,120,30,26,9,55,33,11,6,169,28,23,5,256,30,1*70

$GBGSV,3,1,12,33,69,262,42,39,52,10,40,24,47,19,41,25,44,290,40,5*74

$GBGSV,3,2,12,40,38,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*79

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,55,30,23,5,256,29,5*72

$GBRMC,125043.000,A,2301.2581416,N,11421.9412245,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125043.000,3.440,0.227,0.225,0.308,2.346,2.387,3.249*7F



2025-07-31 20:50:44:195 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:50:44:202 ==>> 检测【检测WiFi结果】
2025-07-31 20:50:44:225 ==>> WiFi信号:【F88C21BCF57D】,信号值:-36
2025-07-31 20:50:44:232 ==>> WiFi信号:【F62A7D2297A3】,信号值:-63
2025-07-31 20:50:44:239 ==>> WiFi信号:【CC057790A741】,信号值:-70
2025-07-31 20:50:44:260 ==>> WiFi信号:【CC057790A5C1】,信号值:-80
2025-07-31 20:50:44:270 ==>> WiFi信号:【44A1917CAD81】,信号值:-76
2025-07-31 20:50:44:290 ==>> WiFi信号:【CC057790A7C0】,信号值:-77
2025-07-31 20:50:44:301 ==>> WiFi信号:【CC057790A5C0】,信号值:-79
2025-07-31 20:50:44:309 ==>> WiFi信号:【F42A7D1297A3】,信号值:-63
2025-07-31 20:50:44:327 ==>> WiFi信号:【74C330CCAB10】,信号值:-74
2025-07-31 20:50:44:339 ==>> WiFi数量【9】, 最大信号值:-36
2025-07-31 20:50:44:353 ==>> 检测【检测GPS结果】
2025-07-31 20:50:44:365 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 20:50:44:383 ==>> 
北斗星号:【33】,信号值:【43】
北斗星号:【14】,信号值:【40】
北斗星号:【3】,信号值:【41】
北斗星号:【6】,信号值:【36】
北斗星号:【59】,信号值:【41】
北斗星号:【39】,信号值:【39】
北斗星号:【16】,信号值:【37】
北斗星号:【1】,信号值:【39】
北斗星号:【9】,信号值:【36】
北斗星号:【24】,信号值:【41】
北斗星号:【2】,信号值:【36】
北斗星号:【7】,信号值:【37】
北斗星号:【25】,信号值:【41】
北斗星号:【60】,信号值:【41】
北斗星号:【40】,信号值:【39】
北斗星号:【13】,信号值:【36】
北斗星号:【38】,信号值:【36】
北斗星号:【42】,信号值:【39】
北斗星号:【41】,信号值:【39】
北斗星号:【44】,信号值:【36】
北斗星号:【34】,信号值:【35】

2025-07-31 20:50:44:401 ==>> 检测【CSQ强度】
2025-07-31 20:50:44:427 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:50:44:439 ==>> $GBGGA,125044.000,2301.2581333,N,11421.9412286,E,1,28,0.52,75.656,M,-1.770,M,,*56

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,261,42,3,62,190,41,14,61,190,40,6,52,347,36,1*76

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*79

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,40,1*71

$GBGSV,7,4,28,7,41,177,36,40,39,160,38,13,37,218,36,42,36,165,39,1*48

$GBGSV,7,5,28,8,33,207,34,10,33,189,34,4,31,113,34,41,27,317,38,1*7E

$GBGSV,7,6,28,38,25,192,35,5,24,258,34,44,17,95,35,34,17,149,35,1*71

$GBGSV,7,7,28,12,17,120,30,26,9,55,33,11,6,169,28,23,5,256,31,1*71

$GBGSV,3,1,12,33,69,261,42,39,52,10,40,24,47,19,41,25,44,290,40,5*77

$GBGSV,3,2,12,40,39,160,37,42,36,165,39,41,27,317,36,38,25,192,34,5*77

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,55,30,23,5,256,30,5*7A

$GBRMC,125044.000,A,2301.2581333,N,11421.9412286,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125044.000,3.392,0.206,0.205,0.282,2.318,2.357,3.198*7B



2025-07-31 20:50:44:572 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:50][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:50][CAT1]exec over: func id: 12, ret: 21


2025-07-31 20:50:44:768 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 20:50:44:774 ==>> 检测【关闭GSM联网】
2025-07-31 20:50:44:790 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:50:44:952 ==>> [D][05:18:51][COMM]read battery soc:255
[W][05:18:51][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:51][COMM]GSM test
[D][05:18:51][COMM]GSM test disable


2025-07-31 20:50:45:054 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:50:45:065 ==>> 检测【4G联网测试】
2025-07-31 20:50:45:075 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:50:46:122 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
$GBGGA,125045.000,2301.2581318,N,11421.9412305,E,1,28,0.52,75.648,M,-1.770,M,,*5B

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,261,43,3,62,190,41,14,61,190,40,6,52,347,36,1*77

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*79

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,40,1*71

$GBGSV,7,4,28,7,41,177,36,40,39,160,39,13,37,218,36,42,36,165,39,1*49

$GBGSV,7,5,28,8,33,207,34,10,33,189,34,4,31,113,35,41,27,317,39,1*7E

$GBGSV,7,6,28,38,25,192,35,5,24,258,34,44,17,95,36,34,17,149,35,1*72

$GBGSV,7,7,28,12,17,120,30,26,9,55,32,11,6,169,29,23,5,256,31,1*71

$GBGSV,3,1,12,33,69,261,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,12,40,39,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*78

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,55,31,23,5,256,30,5*7B

$GBRMC,125045.000,A,2301.2581318,N,11421.9412305,E,0.000,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,125045.000,3.197,0.197,0.195,0.269,2.210,2.248,3.07

2025-07-31 20:50:46:227 ==>> 6*7E

[D][05:18:51][COMM]Main Task receive event:14
[D][05:18:51][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955131, allstateRepSeconds = 0
[D][05:18:51][COMM]index:0,power_mode:0xFF
[D][05:18:51][COMM]index:1,sound_mode:0xFF
[D][05:18:51][COMM]index:2,gsensor_mode:0xFF
[D][05:18:51][COMM]index:3,report_freq_mode:0xFF
[D][05:18:51][COMM]index:4,report_period:0xFF
[D][05:18:51][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:51][COMM]index:6,normal_reset_period:0xFF
[D][05:18:51][COMM]index:7,spock_over_speed:0xFF
[D][05:18:51][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:51][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:51][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:51][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:51][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:51][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:51][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:51][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:51][COMM]index:16,imu_config_params:0xFF
[D][05:18:51][COMM]index:17,long_connect_params:0xFF
[D][05:18:51][COMM]index:18,detain_mark:0xFF
[D][05:18:51][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:51][COMM]index:2

2025-07-31 20:50:46:332 ==>> 0,lock_pos_report_interval:0xFF
[D][05:18:51][COMM]index:21,mc_mode:0xFF
[D][05:18:51][COMM]index:22,S_mode:0xFF
[D][05:18:51][COMM]index:23,overweight:0xFF
[D][05:18:51][COMM]index:24,standstill_mode:0xFF
[D][05:18:51][COMM]index:25,night_mode:0xFF
[D][05:18:51][COMM]index:26,experiment1:0xFF
[D][05:18:51][COMM]index:27,experiment2:0xFF
[D][05:18:51][COMM]index:28,experiment3:0xFF
[D][05:18:51][COMM]index:29,experiment4:0xFF
[D][05:18:51][COMM]index:30,night_mode_start:0xFF
[D][05:18:51][COMM]index:31,night_mode_end:0xFF
[D][05:18:51][COMM]index:33,park_report_minutes:0xFF
[D][05:18:51][COMM]index:34,park_report_mode:0xFF
[D][05:18:51][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:51][COMM]index:38,charge_battery_para: FF
[D][05:18:51][COMM]index:39,multirider_mode:0xFF
[D][05:18:51][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:51][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:51][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:51][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:51][COMM]index:44,riding_duration_config:0xFF
[D][05:18:51][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:51][COMM]index:46,camera_park_type_cfg:0xFF
[D][

2025-07-31 20:50:46:437 ==>> 05:18:51][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:51][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:51][COMM]index:49,mc_load_startup:0xFF
[D][05:18:51][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:51][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:51][COMM]index:52,traffic_mode:0xFF
[D][05:18:51][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:51][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:51][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:51][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:51][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:51][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:51][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:51][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:51][COMM]index:63,experiment5:0xFF
[D][05:18:51][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:51][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:51][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:51][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:51][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:51][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:51][COMM]index:71,camera_park_self_check_c

2025-07-31 20:50:46:542 ==>> fg:0xFF
[D][05:18:51][COMM]index:72,experiment6:0xFF
[D][05:18:51][COMM]index:73,experiment7:0xFF
[D][05:18:51][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:51][COMM]index:75,zero_value_from_server:-1
[D][05:18:51][COMM]index:76,multirider_threshold:255
[D][05:18:51][COMM]index:77,experiment8:255
[D][05:18:51][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:51][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:51][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:51][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:51][COMM]index:83,loc_report_interval:255
[D][05:18:51][COMM]index:84,multirider_threshold_p2:255
[D][05:18:51][COMM]index:85,multirider_strategy:255
[D][05:18:51][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:51][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:51][COMM]index:90,weight_param:0xFF
[D][05:18:51][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:51][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:51][COMM]index:95,current_limit:0xFF
[D][05:18:51][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:51][COMM]index:100,location_mode:0xFF

[W][05:18:

2025-07-31 20:50:46:647 ==>> 51][PROT]remove success[1629955131],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:18:51][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:8
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:1
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:51][PROT]===========================================================
[D][05:18:51][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:51][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:51][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900009]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[W][05:18:51][PROT]add success [1629955131],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[294]
[D][05:18:51][CAT1]gsm read msg sub id: 13
[

2025-07-31 20:50:46:752 ==>> D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[0]
[D][05:18:51][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:51][CAT1]exec over: func id: 13, ret: 21
[D][05:18:51][M2M ]get csq[21]
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:51][CAT1]Send Data To Server[294][297] ... ->:
0093B98E113311331133113311331B88B18966A903DF5B312ACABADE8838B6BB3A25649A9E4CF9231A9A378DBA45F064A1D55D564EAF63CFA289DC5D6FF41A250D751119C1259CDA33C055C7D8A5AC2B68C4D94550ACF8A24A65219C5B2EE0167263C9A5FA521DA675A00492BC52F859A4EB980CF836BF9FC8BEB10E1952DEF8EA993EE95EFA2C23604299E6665FE07724B440
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11


2025-07-31 20:50:46:857 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 20:50:46:902 ==>>                                          

2025-07-31 20:50:47:095 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:50:47:108 ==>> 检测【关闭GPS】
2025-07-31 20:50:47:134 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:50:47:487 ==>> $GBGGA,125047.000,2301.2581226,N,11421.9412306,E,1,28,0.52,75.560,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.00,0.52,0.86,4*0D

$GBGSA,A,3,07,40,13,42,08,10,04,41,38,05,44,34,1.00,0.52,0.86,4*06

$GBGSA,A,3,12,26,11,23,,,,,,,,,1.00,0.52,0.86,4*06

$GBGSV,7,1,28,33,69,261,43,3,62,190,41,14,61,190,40,6,52,347,36,1*77

$GBGSV,7,2,28,39,52,10,39,16,52,350,37,59,50,128,41,2,48,239,36,1*79

$GBGSV,7,3,28,9,47,324,36,24,47,19,41,25,44,290,41,60,43,241,41,1*70

$GBGSV,7,4,28,7,41,177,37,40,39,160,39,13,37,218,36,42,36,165,39,1*48

$GBGSV,7,5,28,8,33,207,34,10,33,189,34,4,31,113,34,41,27,317,39,1*7F

$GBGSV,7,6,28,38,25,192,36,5,24,258,34,44,17,95,36,34,17,149,35,1*71

$GBGSV,7,7,28,12,17,120,30,26,9,55,32,11,6,169,28,23,5,256,31,1*70

$GBGSV,3,1,12,33,69,261,42,39,52,10,40,24,47,19,40,25,44,290,40,5*76

$GBGSV,3,2,12,40,39,160,38,42,36,165,39,41,27,317,36,38,25,192,34,5*78

$GBGSV,3,3,12,44,17,95,34,34,17,149,30,26,9,55,31,23,5,256,30,5*7B

$GBRMC,125047.000,A,2301.2581226,N,11421.9412306,E,0.002,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125047.000,3.394,0.212,0.210,0.288,2.315,2.348,3.128*7D

[W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all con

2025-07-31 20:50:47:562 ==>> tinue location stop
[W][05:18:53][GNSS]sing locating running
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]stop event:1
[D][05:18:53][GNSS]GPS stop. ret=0
[D][05:18:53][GNSS]all sing location stop
[D][05:18:53][CAT1]gsm read msg sub id: 24
[D][05:18:53][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:53][CAT1]<<< 
OK

[D][05:18:53][CAT1]exec over: func id: 24, ret: 6
[D][05:18:53][CAT1]sub id: 24, ret: 6



2025-07-31 20:50:47:622 ==>>                                                                                                                                            

2025-07-31 20:50:47:632 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:50:47:646 ==>> 检测【清空消息队列2】
2025-07-31 20:50:47:672 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:50:47:850 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:54][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:50:47:918 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:50:47:932 ==>> 检测【轮动检测】
2025-07-31 20:50:47:950 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:50:48:064 ==>> 3A A3 01 00 A3 


2025-07-31 20:50:48:154 ==>> OFF_OUT1
OVER 150


2025-07-31 20:50:48:229 ==>> [D][05:18:54][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:50:48:424 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:50:48:559 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:50:48:704 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:50:48:717 ==>> 检测【关闭小电池】
2025-07-31 20:50:48:733 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:50:48:760 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:50:48:920 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:50:48:989 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:50:49:003 ==>> 检测【进入休眠模式】
2025-07-31 20:50:49:030 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:50:49:118 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:50:49:223 ==>> [D][05:18:55][COMM]Main Task receive event:28
[D][05:18:55][COMM]main task tmp_sleep_event = 8
[D][05:18:55][COMM]prepare to sleep
[D][05:18:55][CAT1]gsm read msg sub id: 12
[D][05:18:55][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:50:49:817 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 12, ret: 6
[D][05:18:56][M2M ]tcpclient close[4]
[D][05:18:56][SAL ]Cellular task submsg id[12]
[D][05:18:56][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c98], socket[0]
[D][05:18:56][CAT1]gsm read msg sub id: 9
[D][05:18:56][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 9, ret: 6
[D][05:18:56][CAT1]sub id: 9, ret: 6

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:56][SAL ]socket close ind. id[4]
[D][05:18:56][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:56][COMM]1x1 frm_can_tp_send ok
[D][05:18:56][CAT1]pdpdeact urc len[22]


2025-07-31 20:50:50:077 ==>> [E][05:18:56][COMM]1x1 rx timeout
[D][05:18:56][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:50:50:620 ==>> [E][05:18:56][COMM]1x1 rx timeout
[E][05:18:56][COMM]1x1 tp timeout
[E][05:18:56][COMM]1x1 error -3.
[D][05:18:56][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:18:56][COMM]CAN STOP!
[D][05:18:56][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:18:56][COMM]------------ready to Power off Acckey 1------------
[D][05:18:56][COMM]------------ready to Power off Acckey 2------------
[D][05:18:56][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:18:56][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1281
[D][05:18:56][COMM]bat sleep fail, reason:-1
[D][05:18:56][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:18:56][COMM]accel parse set 0
[D][05:18:56][COMM]imu rest ok. 67871
[D][05:18:56][COMM]imu sleep 0
[W][05:18:56][COMM]now sleep


2025-07-31 20:50:50:826 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:50:50:834 ==>> 检测【检测33V休眠电流】
2025-07-31 20:50:50:856 ==>> 开始33V电流采样
2025-07-31 20:50:50:873 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:50:50:940 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:50:51:941 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:50:52:004 ==>> Current33V:????:19.16

2025-07-31 20:50:52:454 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:50:52:463 ==>> 【检测33V休眠电流】通过,【19.16uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:50:52:471 ==>> 该项需要延时执行
2025-07-31 20:50:54:471 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:50:54:484 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:50:54:516 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:50:54:577 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1677mV
Get AD_V4 1mV
Get AD_V5 2771mV
Get AD_V6 2018mV
Get AD_V7 1103mV
OVER 150


2025-07-31 20:50:55:508 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:50:55:520 ==>> 检测【打开小电池2】
2025-07-31 20:50:55:545 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:50:55:556 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:50:55:820 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:50:55:828 ==>> 该项需要延时执行
2025-07-31 20:50:56:329 ==>> 此处延时了:【500】毫秒
2025-07-31 20:50:56:341 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:50:56:368 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:50:56:464 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:50:56:607 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:50:56:615 ==>> 该项需要延时执行
2025-07-31 20:50:56:800 ==>> [D][05:19:02][COMM]------------ready to Power on Acckey 1------------
[D][05:19:02][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:02][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:02][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 11
[D][05:19:02][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:02][COMM]----- get Acckey 1 and value:1------------
[W][05:19:02][COMM]CAN START!
[D][05:19:02][CAT1]gsm read msg sub id: 12
[D][05:19:02][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:02][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 73981
[D][05:19:02][COMM][Audio]exec status ready.
[D][05:19:02][CAT1]<<< 
OK

[D][05:19:

2025-07-31 20:50:56:845 ==>> 02][CAT1]exec over: func id: 12, ret: 6
[D][05:19:02][COMM]imu wakeup ok. 73995
[D][05:19:02][COMM]imu wakeup 1
[W][05:19:02][COMM]wake up system, wakeupEvt=0x80
[D][05:19:02][COMM]frm_can_weigth_power_set 1
[D][05:19:02][COMM]Clear Sleep Block Evt
[D][05:19:02][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:50:57:043 ==>> [E][05:19:03][COMM]1x1 rx timeout
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:50:57:118 ==>> 此处延时了:【500】毫秒
2025-07-31 20:50:57:132 ==>> 检测【进入休眠模式2】
2025-07-31 20:50:57:159 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:50:57:172 ==>>                                        tick:73966. cur_tick:74474. period:50
[D][05:19:03][COMM]msg 02A4 loss. last_tick:73966. cur_tick:74475. period:50
[D][05:19:03][COMM]msg 02A5 loss. last_tick:73966. cur_tick:74475. period:50
[D][05:19:03][COMM]msg 02A6 loss. last_tick:73966. cur_tick:74476. period:50
[D][05:19:03][COMM]msg 02A7 loss. last_tick:73966. cur_tick:74476. period:50
[D][05:19:0

2025-07-31 20:50:57:198 ==>> 3][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 74477
[D][05:19:03][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 74477


2025-07-31 20:50:57:268 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:50:57:523 ==>> [E][05:19:03][COMM]1x1 rx timeout
[E][05:19:03][COMM]1x1 tp timeout
[E][05:19:03][COMM]1x1 error -3.
[D][05:19:03][COMM]Main Task receive event:28 finished processing
[D][05:19:03][COMM]Main Task receive event:28
[D][05:19:03][COMM]prepare to sleep
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 6
[D][05:19:03][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:50:57:825 ==>> [D][05:19:03][COMM]msg 0220 loss. last_tick:73966. cur_tick:74971. period:100
[D][05:19:03][COMM]msg 0221 loss. last_tick:73966. cur_tick:74971. period:100
[D][05:19:03][COMM]msg 0224 loss. last_tick:73966. cur_tick:74972. period:100
[D][05:19:03][COMM]msg 0260 loss. last_tick:73966. cur_tick:74972. period:100
[D][05:19:03][COMM]msg 0280 loss. last_tick:73966. cur_tick:74972. period:100
[D][05:19:03][COMM]msg 02C0 loss. last_tick:73966. cur_tick:74973. period:100
[D][05:19:03][COMM]msg 02C1 loss. last_tick:73966. cur_tick:74973. period:100
[D][05:19:03][COMM]msg 02C2 loss. last_tick:73966. cur_tick:74973. period:100
[D][05:19:03][COMM]msg 02E0 loss. last_tick:73966. cur_tick:74974. period:100
[D][05:19:03][COMM]msg 02E1 loss. last_tick:73966. cur_tick:74974. period:100
[D][05:19:03][COMM]msg 02E2 loss. last_tick:73966. cur_tick:74974. period:100
[D][05:19:03][COMM]msg 0300 loss. last_tick:73966. cur_tick:74975. period:100
[D][05:19:03][COMM]msg 0301 loss. last_tick:73966. cur_tick:74975. period:100
[D][05:19:03][COMM]bat msg 0240 loss. last_tick:73966. cur_tick:74975. period:100. j,i:1 54
[D][05:19:03][COMM]bat msg 0241 loss. last_tick:73966. cur_tick:74976. period:100. j,i:2 55
[D][05:19:03][COMM]bat msg 0242 loss. last_tick:73966. cur_tick:74976. 

2025-07-31 20:50:57:930 ==>> period:100. j,i:3 56
[D][05:19:03][COMM]bat msg 0244 loss. last_tick:73966. cur_tick:74977. period:100. j,i:5 58
[D][05:19:03][COMM]bat msg 024E loss. last_tick:73966. cur_tick:74977. period:100. j,i:15 68
[D][05:19:03][COMM]bat msg 024F loss. last_tick:73966. cur_tick:74978. period:100. j,i:16 69
[D][05:19:03][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 74978
[D][05:19:03][COMM]CAN message bat fault change: 0x00000000->0x0001802E 74979
[D][05:19:03][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 74979
                                                                              

2025-07-31 20:50:58:111 ==>> [D][05:19:04][COMM]msg 0222 loss. last_tick:73966. cur_tick:75473. period:150
[D][05:19:04][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 75474


2025-07-31 20:50:58:201 ==>>    [05:19:04][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:04][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:50:58:306 ==>> [E][05:19:04][COMM]1x1 rx timeout
[E][05:19:04][COMM]1x1 tp timeout
[E][05:19:04][COMM]1x1 error -3.
[W][05:19:04][COMM]CAN STOP!
[D][05:19:04

2025-07-31 20:50:58:382 ==>> ][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 1------------
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------
[D][05:19:04][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:04][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 95
[D][05:19:04][COMM]bat sleep fail, reason:-1
[D][05:19:04][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM]imu rest ok. 75654
[D][05:19:04][COMM]imu sleep 0
[W][05:19:04][COMM]now sleep


2025-07-31 20:50:58:419 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:50:58:428 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:50:58:443 ==>> 开始小电池电流采样
2025-07-31 20:50:58:463 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:50:58:531 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:50:59:541 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:50:59:604 ==>> CurrentBattery:ƽ��:68.14

2025-07-31 20:51:00:052 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:51:00:060 ==>> 【检测小电池休眠电流】通过,【68.14uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:51:00:068 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:51:00:075 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:51:00:159 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:51:00:348 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:51:00:362 ==>> 该项需要延时执行
2025-07-31 20:51:00:384 ==>> [D][05:19:06][COMM]------------ready to Power on Acckey 1------------
[D][05:19:06][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:06][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:06][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:06][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:06][COMM]----- get Acckey 1 and value:1------------
[W][05:19:06][COMM]CAN START!
[D][05:19:06][COMM]read battery soc:0
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:06][COMM]CAN message bat fault change: 0x0001802E->0x00000000 77615
[D][05:19:06][COMM][Audio]exec status ready.
[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 6
[D][05:19:06][COMM]imu wakeup ok. 77629
[D][05:19:06][COMM]imu wakeup 1
[W][05:19:06][COMM]wake up system, wakeupEvt=0x80
[D][05:19:06][COMM]frm_can_weigth_power_set 1
[D][05:19:06][COMM]Clear Sleep Block Evt
[D][05:19:06][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:06][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:51:00:686 ==>> [D][05:19:06][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[E][05:19:07][COMM]1x1 rx timeout
[D][05:19:07][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:51:00:792 ==>>                                                            :78109. period:50
[D][05:19:07][COMM]msg 02A4 loss. last_tick:77598. cur_tick:78109. period:50
[D][05:19:07][COMM]msg 02A5 loss. last_tick:77598. cur_tick:78110. period:50
[D][05:19:07][COMM]msg 02A6 loss. last_tick:77598. cur_tick:78110. period:50
[D][05:19:07][COMM]msg 02A7 loss. last_tick:77598. cur_tick:78111. period:50
[D][05:19:07][COMM]CAN message fault chan

2025-07-31 20:51:00:821 ==>> ge: 0x0000000000000000->0x0000E00000220000 78111
[D][05:19:07][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 78112


2025-07-31 20:51:00:851 ==>> 此处延时了:【500】毫秒
2025-07-31 20:51:00:863 ==>> 检测【检测唤醒】
2025-07-31 20:51:00:877 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:51:01:616 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:07][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:07][FCTY]==========Modules-nRF5340 ==========
[D][05:19:07][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:07][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:07][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:07][FCTY]DeviceID    = 460130071539181
[D][05:19:07][FCTY]HardwareID  = 867222087738805
[D][05:19:07][FCTY]MoBikeID    = 9999999999
[D][05:19:07][FCTY]LockID      = FFFFFFFFFF
[D][05:19:07][FCTY]BLEFWVersion= 105
[D][05:19:07][FCTY]BLEMacAddr   = FDD99F9FF918
[D][05:19:07][FCTY]Bat         = 3864 mv
[D][05:19:07][FCTY]Current     = 0 ma
[D][05:19:07][FCTY]VBUS        = 2600 mv
[D][05:19:07][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:07][FCTY]Ext battery vol = 32, adc = 1294
[D][05:19:07][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 0 mv
[D][05:19:07][FCTY]Bike Type flag is invalied
[D][05:19:07][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:07][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:07][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:07][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:07][FCTY]CAT

2025-07-31 20:51:01:654 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:51:01:671 ==>> 检测【关机】
2025-07-31 20:51:01:686 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:51:01:721 ==>> 1_GNSS_PLATFORM = C4
[D][05:19:07][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:07][FCTY]Bat1         = 3776 mv
[D][05:19:07][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:07][FCTY]==========Modules-nRF5340 ==========
[E][05:19:07][COMM]1x1 rx timeout
[E][05:19:07][COMM]1x1 tp timeout
[D][05:19:07][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[E][05:19:07][COMM]1x1 error -3.
[D][05:19:07][COMM]Main Task receive event:28 finished processing
[D][05:19:07][COMM]Main Task receive event:65
[D][05:19:07][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:07][COMM]Main Task receive event:65 finished processing
[D][05:19:07][COMM]Main Task receive event:60
[D][05:19:07][COMM]smart_helmet_vol=255,255
[D][05:19:07][COMM]report elecbike
[W][05:19:07][PROT]remove success[1629955147],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:07][PROT]add success [1629955147],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:07][COMM]Main Task receive event:60 finished processing
[D][05:19:07][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:07][PROT]index:0
[D][05:19:07][PROT]is_send:1
[D][05:19:

2025-07-31 20:51:01:826 ==>> 07][PROT]sequence_num:10
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:3
[D][05:19:07][PROT]send_path:0x3
[D][05:19:07][PROT]msg_type:0x5d03
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][PROT]Sending traceid[999999999990000B]
[D][05:19:07][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:07][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:07][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:07][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:07][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:07][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:07][SAL ]open socket ind id[4], rst[0]
[D][05:19:07][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:07][SAL ]Cellular task submsg id[8]
[D][05:19:07][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:07][SAL 

2025-07-31 20:51:01:932 ==>> ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:07][CAT1]gsm read msg sub id: 8
[D][05:19:07][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:07][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:07][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:07][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:07][CAT1]<<< 
+CME ERROR: 100

[D][05:19:07][COMM]msg 0220 loss. last_tick:77598. cur_tick:78609. period:100
[D][05:19:07][COMM]msg 0221 loss. last_tick:77598. cur_tick:78609. period:100
[D][05:19:07][COMM]msg 0224 loss. last_tick:77598. cur_tick:78609. period:100
[D][05:19:07][COMM]msg 0260 loss. last_tick:77598. cur_tick:78610. period:100
[D][05:19:07][COMM]msg 0280 loss. last_tick:77598. cur_tick:78610. period:100
[D][05:19:07][COMM]msg 02C0 loss. last_tick:77598. cur_tick:78611. period:100
[D][05:19:07][COMM]msg 02C1 loss. last_tick:77598. cur_tick:78611. period:100
[D][05:19:07][COMM]msg 02C2 loss. last_tick:77598. cur_tick:78611. period:100
[D][05:19:07][COMM]msg 02E0 loss. last_tick:77598. cur_tick:78612. period:100
[D][05:19:07][COMM]msg 02E1 loss. last_tick:77598. cur_tick:78612. period:100
[D][05:19:07][COMM]msg 02E2 loss

2025-07-31 20:51:02:037 ==>> . last_tick:77598. cur_tick:78612. period:100
[D][05:19:07][COMM]msg 0300 loss. last_tick:77598. cur_tick:78613. period:100
[D][05:19:07][COMM]msg 0301 loss. last_tick:77598. cur_tick:78613. period:100
[D][05:19:07][COMM]bat msg 0240 loss. last_tick:77598. cur_tick:78613. period:100. j,i:1 54
[D][05:19:07][COMM]bat msg 0241 loss. last_tick:77598. cur_tick:78614. period:100. j,i:2 55
[D][05:19:07][COMM]bat msg 0242 loss. last_tick:77598. cur_tick:78614. period:100. j,i:3 56
[D][05:19:07][COMM]bat msg 0244 loss. last_tick:77598. cur_tick:78614. period:100. j,i:5 58
[D][05:19:07][COMM]bat msg 024E loss. last_tick:77598. cur_tick:78615. period:100. j,i:15 68
[D][05:19:07][COMM]bat msg 024F loss. last_tick:77598. cur_tick:78615. period:100. j,i:16 69
[D][05:19:07][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 78616
[D][05:19:07][COMM]CAN message bat fault change: 0x00000000->0x0001802E 78616
[D][05:19:07][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 78617


2025-07-31 20:51:02:660 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:51:02:690 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:51:02:765 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:51:02:870 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     05:19:08][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:08][COMM]Main Task receive event:60
[D][05:19:08][COMM]smart_helmet_vol=255,255
[D][05:19:08][COMM]BAT CAN get state1 Fail 204
[D][05:19:08][COMM]BAT CAN get soc Fail, 204
[D][05:19:08][COMM]BAT CAN get state2 fail 204
[D][05:19:08][COMM]get soh error
[E][05:19:08][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:08

2025-07-31 20:51:02:975 ==>> ][COMM]report elecbike
[W][05:19:08][PROT]remove success[1629955148],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:08][PROT]add success [1629955148],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:08][COMM]Main Task receive event:60 finished processing
[D][05:19:08][COMM]Main Task receive event:61
[D][05:19:08][COMM][D301]:type:3, trace id:280
[D][05:19:08][COMM]id[], hw[000
[D][05:19:08][COMM]get mcMaincircuitVolt error
[D][05:19:08][COMM]get mcSubcircuitVolt error
[D][05:19:08][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:08][COMM]BAT CAN get state1 Fail 204
[D][05:19:08][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:08][COMM]BAT CAN get soc Fail, 204
[D][05:19:08][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:08][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:08][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:08][PROT]index:1
[D][05:19:08][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:08][PROT]is_send:1
[D][05:19:08][COMM]f:[ec800m_audio_send

2025-07-31 20:51:03:080 ==>> _hexdata_start].l:[756].recv >
[D][05:19:08][PROT]sequence_num:11
[D][05:19:08][PROT]retry_timeout:0
[D][05:19:08][PROT]retry_times:3
[D][05:19:08][PROT]send_path:0x3
[D][05:19:08][PROT]msg_type:0x5d03
[D][05:19:08][PROT]===========================================================
[W][05:19:08][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955148]
[D][05:19:08][PROT]===========================================================
[D][05:19:08][PROT]Sending traceid[999999999990000C]
[D][05:19:08][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:08][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:08][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:08][COMM]BAT CAN get state2 fail 204
[D][05:19:08][COMM]get bat work mode err
[W][05:19:08][PROT]remove success[1629955148],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:08][PROT]add success [1629955148],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:08][COMM]Main Task

2025-07-31 20:51:03:185 ==>>  receive event:61 finished processing
[D][05:19:08][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:08][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:08][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:08][COMM]arm_hub_enable: hub power: 0
[D][05:19:08][HSDK]hexlog index save 0 5632 131 @ 0 : 0
[D][05:19:08][HSDK]write save hexlog index [0]
[D][05:19:08][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:08][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:08][COMM]read battery soc:255
[D][05:19:08]

2025-07-31 20:51:03:260 ==>> [COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:08][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:08][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:08][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:08][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 20:51:03:365 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:09][COMM]arm_hub_enable: hub power: 0
[D][05:19:09][HSDK]hexlog index save 0 5632 131 @ 0 : 0
[D][05:19:09][HSDK]write save hexlog index [0]
[D][05:19:09][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:09][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:51:03:716 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:51:03:731 ==>> [D][05:19:10][COMM]exit wheel stolen mode.
[D][05:19:10][COMM]Main Task receive event:68
[D][05:19:10][COMM]handlerWheelStolen evt type = 2.
[E][05:19:10][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:10][GNSS]stop locating
[D][05:19:10][GNSS]all continue location stop
[D][05:19:10][COMM]Main Task receive event:68 finished processing


2025-07-31 20:51:03:821 ==>> [W][05:19:10][COMM]Power Off


2025-07-31 20:51:03:986 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:10][COMM]arm_hub_enable: hub power: 0
[D][05:19:10][HSDK]hexlog index save 0 5632 131 @ 0 : 0
[D][05:19:10][HSDK]write save hexlog index [0]
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:51:03:995 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:51:04:003 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:51:04:022 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:51:04:061 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:51:04:241 ==>> [D][05:19:10][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:10][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:51:04:275 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:51:04:283 ==>> 检测【检测小电池关机电流】
2025-07-31 20:51:04:297 ==>> 开始小电池电流采样
2025-07-31 20:51:04:324 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:51:04:376 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:51:05:387 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:51:05:462 ==>> CurrentBattery:ƽ��:69.88

2025-07-31 20:51:05:895 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:51:05:904 ==>> 【检测小电池关机电流】通过,【69.88uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:51:06:240 ==>> MES过站成功
2025-07-31 20:51:06:248 ==>> #################### 【测试结束】 ####################
2025-07-31 20:51:06:281 ==>> 关闭5V供电
2025-07-31 20:51:06:295 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:51:06:357 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:51:07:289 ==>> 关闭5V供电成功
2025-07-31 20:51:07:304 ==>> 关闭33V供电
2025-07-31 20:51:07:331 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:51:07:365 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:51:08:302 ==>> 关闭33V供电成功
2025-07-31 20:51:08:317 ==>> 关闭3.7V供电
2025-07-31 20:51:08:340 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:51:08:364 ==>> 6A A6 02 A6 6A 


2025-07-31 20:51:08:454 ==>> Battery OFF
OVER 150


2025-07-31 20:51:09:209 ==>>  

