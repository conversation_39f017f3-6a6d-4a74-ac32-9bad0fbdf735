2025-07-31 20:21:16:424 ==>> MES查站成功:
查站序号:P5100010053132B2验证通过
2025-07-31 20:21:16:428 ==>> 扫码结果:P5100010053132B2
2025-07-31 20:21:16:430 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:21:16:432 ==>> 测试参数版本:2024.10.11
2025-07-31 20:21:16:434 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:21:16:435 ==>> 检测【打开透传】
2025-07-31 20:21:16:437 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:21:16:550 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:21:16:787 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:21:16:792 ==>> 检测【检测接地电压】
2025-07-31 20:21:16:793 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:21:16:839 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:21:17:073 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:21:17:075 ==>> 检测【打开小电池】
2025-07-31 20:21:17:078 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:21:17:147 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:21:17:348 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:21:17:351 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:21:17:354 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:21:17:450 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 20:21:17:619 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:21:17:622 ==>> 检测【等待设备启动】
2025-07-31 20:21:17:624 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:21:17:835 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:21:18:002 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time 

2025-07-31 20:21:18:521 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:21:18:656 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:21:18:717 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:21:19:427 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:21:19:700 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:21:19:823 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:21:20:300 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:21:20:500 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:21:20:502 ==>> 检测【产品通信】
2025-07-31 20:21:20:503 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:21:21:017 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:21:21:213 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:21:21:530 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:21:21:904 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 20:21:22:286 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:21:22:561 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:21:22:824 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]msg 0222 loss. last_tick:0. cur_tick:1515. period:150
[D][05:17:50][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 1516
                                                                                                 

2025-07-31 20:21:22:862 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:21:22:866 ==>> 检测【初始化完成检测】
2025-07-31 20:21:22:869 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:21:23:078 ==>> [D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 20:21:23:142 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:21:23:147 ==>> 检测【关闭大灯控制1】
2025-07-31 20:21:23:149 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:21:23:302 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:21:23:407 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:21:23:426 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:21:23:429 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:21:23:432 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:21:23:647 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:21:23:695 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:21:23:698 ==>> 检测【关闭仪表供电】
2025-07-31 20:21:23:701 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:21:23:872 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0
[D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:23:965 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:21:23:967 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:21:23:969 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:21:23:977 ==>>                                                                                                                                                                                                                                                                      0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:21:24:082 ==>> [W][05:17:51]

2025-07-31 20:21:24:112 ==>> [COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:21:24:243 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:21:24:246 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:21:24:265 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:21:24:400 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:21:24:524 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:21:24:526 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:21:24:528 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:21:24:736 ==>> [D][05:17:52][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:21:24:800 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:21:24:802 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:21:24:804 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:21:24:841 ==>> [

2025-07-31 20:21:24:871 ==>> D][05:17:52][COMM]3648 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:24:946 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:21:25:051 ==>> [D][05:17:52][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:52][COMM]read battery soc:255


2025-07-31 20:21:25:070 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:21:25:072 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:21:25:073 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:21:25:141 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:21:25:342 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:21:25:345 ==>> 该项需要延时执行
2025-07-31 20:21:25:877 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:26:401 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5017. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5017. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5018. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5018. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5019. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5020. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5020. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change

2025-07-31 20:21:26:431 ==>> : 0x0000E00C71E22217->0x0008F00C71E22217 5020
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5021


2025-07-31 20:21:26:628 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:21:27:134 ==>>                                                                                                              e:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 

2025-07-31 20:21:27:239 ==>> 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][PROT]index:2
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]

2025-07-31 20:21:27:344 ==>> retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0

2025-07-31 20:21:27:404 ==>> ]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]5670 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]read battery soc:255


2025-07-31 20:21:27:884 ==>> [D][05:17:55][COMM]6681 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:28:019 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:21:28:916 ==>> [D][05:17:56][COMM]7692 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:29:021 ==>> [D][05:1

2025-07-31 20:21:29:051 ==>> 7:56][COMM]read battery soc:255


2025-07-31 20:21:29:352 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:21:29:356 ==>> 检测【33V输入电压ADC】
2025-07-31 20:21:29:360 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:21:29:654 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3178  volt:5586 mv
[D][05:17:57][COMM]adc read out 24v adc:1323  volt:33462 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:9  volt:11 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2384  volt:3841 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:21:29:884 ==>> 【33V输入电压ADC】通过,【33184mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:21:29:888 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:21:29:889 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:21:29:911 ==>> [D][05:17:57][COMM]8703 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:29:956 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1669mV
Get AD_V4 0mV
Get AD_V5 2789mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:21:30:157 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:30:160 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:21:30:175 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:30:179 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:21:30:181 ==>> 原始值:【2789】, 乘以分压基数【2】还原值:【5578】
2025-07-31 20:21:30:195 ==>> 【TP68_VCC5V5(ADV5)】通过,【5578mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:21:30:197 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:21:30:213 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:21:30:216 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:21:30:372 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:21:30:374 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:21:30:459 ==>> 1A A1 00 00 FC 
Get AD_V2 1647mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2790mV
Get AD_V6 1988mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:21:30:666 ==>> 【TP7_VCC3V3(ADV2)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:30:669 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:21:30:693 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:30:695 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:21:30:697 ==>> 原始值:【2790】, 乘以分压基数【2】还原值:【5580】
2025-07-31 20:21:30:717 ==>> 【TP68_VCC5V5(ADV5)】通过,【5580mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:21:30:719 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:21:30:744 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:21:30:748 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:21:30:784 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:21:30:788 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:21:30:853 ==>> 1A A1 00 00 FC 
Get AD_V2 1645mV
Get AD_V3 1669mV
Get AD_V4 1mV
Get AD_V5 2788mV
Get AD_V6 1988mV
Get AD_V7 1091mV
OVER 150


2025-07-31 20:21:30:929 ==>> [D][05:17:58][COMM]9714 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:21:31:064 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 20:21:31:082 ==>> 【TP7_VCC3V3(ADV2)】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:31:085 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:21:31:126 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:31:129 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:21:31:131 ==>> 原始值:【2788】, 乘以分压基数【2】还原值:【5576】
2025-07-31 20:21:31:163 ==>> 【TP68_VCC5V5(ADV5)】通过,【5576mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:21:31:165 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:21:31:210 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:21:31:213 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:21:31:243 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:21:31:251 ==>> 检测【打开WIFI(1)】
2025-07-31 20:21:31:275 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:21:31:278 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10005


2025-07-31 20:21:31:440 ==>> [D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:21:31:584 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:21:31:587 ==>> 检测【清空消息队列(1)】
2025-07-31 20:21:31:608 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:21:31:730 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:21:31:892 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:21:31:897 ==>> 检测【打开GPS(1)】
2025-07-31 20:21:31:901 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:21:32:173 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10724 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][COMM][Audio]exec status ready.
[W][05:17:59][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:17:59][COMM]Open GPS Module...
[D][05:17:59][COMM]LOC_MODEL_CONT
[D][05:17:59][GNSS]start event:8
[W][05:17:59][GNSS]start cont loc

2025-07-31 20:21:32:219 ==>> ating
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:21:32:441 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:21:32:444 ==>> 检测【打开GSM联网】
2025-07-31 20:21:32:447 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:21:32:612 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087738003

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539136

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:21:32:687 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 20:21:32:746 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:21:32:749 ==>> 检测【打开仪表供电1】
2025-07-31 20:21:32:751 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:21:32:944 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:00][COMM]set POWER 1
[D][05:18:00][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:21:33:042 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:21:33:046 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:21:33:048 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:21:33:051 ==>> [D][05:18:00][COMM]read battery soc:255


2025-07-31 20:21:33:381 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 0

OK



2025-07-31 20:21:33:588 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:21:33:591 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:21:33:593 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:21:33:731 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33131]


2025-07-31 20:21:33:872 ==>> 【读取主控ADC采集的仪表电压】通过,【33131mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:21:33:875 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:21:33:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:34:038 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:34:150 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:21:34:153 ==>> 检测【AD_V20电压】
2025-07-31 20:21:34:155 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:34:264 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:34:370 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:34:616 ==>> 本次取值间隔时间:340ms
2025-07-31 20:21:34:634 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:34:741 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:34:849 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:34:924 ==>> [D][05:18:02][COMM]13736 imu init OK


2025-07-31 20:21:35:061 ==>> [D][05:18:02][COMM]read battery soc:255


2025-07-31 20:21:35:181 ==>> 本次取值间隔时间:429ms
2025-07-31 20:21:35:199 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:35:302 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:35:379 ==>> [D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 0

OK



2025-07-31 20:21:35:544 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:35:589 ==>> 本次取值间隔时间:274ms
2025-07-31 20:21:35:847 ==>> 本次取值间隔时间:245ms
2025-07-31 20:21:36:220 ==>> 本次取值间隔时间:364ms
2025-07-31 20:21:36:580 ==>> 本次取值间隔时间:346ms
2025-07-31 20:21:36:584 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:36:688 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:36:748 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:36:808 ==>> 本次取值间隔时间:119ms
2025-07-31 20:21:36:896 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:37:009 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:37:085 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][COMM]read battery soc:255


2025-07-31 20:21:37:145 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:37:266 ==>> 本次取值间隔时间:242ms
2025-07-31 20:21:37:285 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:37:389 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:37:451 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:37:662 ==>> [D][05:18:05][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:05][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:05][CAT1]tx ret[12] >>> AT+QIACT=1

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 5, ret: 6
[D][05:18:05][CAT1]sub id: 5, ret: 6

[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:05][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:05][M2M ]M2M_GSM_INIT OK
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:05][SAL ]open socket ind id[4], rst[0]
[D][05:18:05][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:05][SAL ]Cellular task submsg id[8]
[D][05:18:05][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], sock

2025-07-31 20:21:37:707 ==>> et[0]
[D][05:18:05][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:05][CAT1]gsm read msg sub id: 8
[D][05:18:05][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:05][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:05][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 20:21:37:812 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:21:37:872 ==>> 本次取值间隔时间:477ms
2025-07-31 20:21:37:890 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:37:995 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:38:042 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:38:147 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:05][GNSS]location recv gms init done evt
[D][05:18:05][GNSS]GPS start. ret=0
[D][05:18:05][CAT1]gsm read msg sub id: 23
[D][05:18:05][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:05][CAT1]<<< 
+GPSCFG:0,

2025-07-31 20:21:38:252 ==>> 0,115200,0,0,65504,0,1,1

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:05][CAT1]opened : 0, 0
[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:05][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:05][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][M2M ]g_m2m_is_idle become true
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:21:38:297 ==>> 本次取值间隔时间:292ms
2025-07-31 20:21:38:315 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:38:421 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:38:545 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
00 00 10 00 00 
head err!


2025-07-31 20:21:38:786 ==>> 本次取值间隔时间:350ms
2025-07-31 20:21:38:922 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:21:38:996 ==>> 本次取值间隔时间:208ms
2025-07-31 20:21:39:088 ==>> 本次取值间隔时间:77ms
2025-07-31 20:21:39:103 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 20:21:39:456 ==>> 本次取值间隔时间:364ms
2025-07-31 20:21:39:461 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:39:561 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:39:652 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:39:757 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,33,,,41,60,,,40,42,,,39,25,,,38,1*7A

$GBGSV,2,2,08,59,,,38,39,,,34,13,,,41,14,,,37,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1589.199,1589.199,50

2025-07-31 20:21:39:817 ==>> .803,2097152,2097152,2097152*41

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:07][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 23, ret: 6
[D][05:18:07][CAT1]sub id: 23, ret: 6



2025-07-31 20:21:39:923 ==>> [D][05:18:07][GNSS]recv s

2025-07-31 20:21:39:953 ==>> ubmsg id[1]
[D][05:18:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:21:39:983 ==>> 本次取值间隔时间:413ms
2025-07-31 20:21:40:001 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:40:104 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:40:181 ==>> 本次取值间隔时间:76ms
2025-07-31 20:21:40:347 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:40:362 ==>> 本次取值间隔时间:178ms
2025-07-31 20:21:40:709 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,41,14,,,40,24,,,40,60,,,39,1*7E

$GBGSV,3,2,11,42,,,39,25,,,39,59,,,39,13,,,36,1*77

$GBGSV,3,3,11,39,,,36,7,,,35,3,,,32,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1567.861,1567.861,50.141,2097152,2097152,2097152*4E



2025-07-31 20:21:40:799 ==>> 本次取值间隔时间:429ms
2025-07-31 20:21:40:922 ==>> 本次取值间隔时间:114ms
2025-07-31 20:21:40:926 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:41:029 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:41:149 ==>> [D][05:18:08][COMM]read battery soc:255
[W][05:18:08][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:21:41:479 ==>> 本次取值间隔时间:444ms
2025-07-31 20:21:41:569 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:21:41:677 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:21:41:783 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,41,14,,,40,24,,,40,60,,,40,1*72

$GBGSV,4,2,14,25,,,40,42,,,39,59,,,39,39,,,37,1*72

$GBGSV,4,3,14,13,,,36,7,,,35,41,,,35,3,,,33,1*72

$GBGSV,4,4,14,4,,,31,38,,,36,1*4B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1549.910,1549.910,49.589,2097152,2097152,2097152*46

[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:09][COMM]oneline display ALL on 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1660mV
OVER 150


2025-07-31 20:21:41:919 ==>> 本次取值间隔时间:233ms
2025-07-31 20:21:41:973 ==>> 【AD_V20电压】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:21:41:975 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:21:41:978 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:21:42:040 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:21:42:272 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:21:42:275 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:21:42:278 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:21:42:465 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:42:740 ==>> $GBGGA,122146.552,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,41,14,,,40,24,,,40,60,,,40,1*70

$GBGSV,5,2,17,25,,,40,42,,,39,59,,,39,39,,,38,1*7F

$GBGSV,5,3,17,13,,,36,38,,,36,7,,,35,41,,,35,1*4D

$GBGSV,5,4,17,3,,,35,1,,,35,5,,,32,4,,,30,1*70

$GBGSV,5,5,17,12,,,40,1*77

$GBRMC,122146.552,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122146.552,0.000,1531.378,1531.378,49.001,2097152,2097152,2097152*5D



2025-07-31 20:21:43:124 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 20:21:43:303 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:21:43:535 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:11][COMM]oneline display read state:1
[D][05:18:11][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:43:747 ==>> $GBGGA,122147.532,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,41,14,,,40,24,,,40,60,,,40,1*75

$GBGSV,6,2,22,25,,,40,42,,,40,59,,,39,39,,,38,1*74

$GBGSV,6,3,22,3,,,37,13,,,36,38,,,36,41,,,36,1*4D

$GBGSV,6,4,22,1,,,36,6,,,35,7,,,34,16,,,34,1*40

$GBGSV,6,5,22,2,,,34,9,,,33,5,,,32,4,,,29,1*72

$GBGSV,6,6,22,34,,,16,8,,,37,1*4A

$GBRMC,122147.532,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122147.532,0.000,1472.885,1472.885,47.245,2097152,2097152,2097152*56



2025-07-31 20:21:44:338 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:21:44:549 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:12][COMM]oneline display read state:1
[D][05:18:12][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:21:44:654 ==>> $GBGGA,122148.512,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,41,14,,,41,24,,,40,60,,,40,1*77

$GBGSV,6,2,21,25,,,40,

2025-07-31 20:21:44:714 ==>> 42,,,40,59,,,39,3,,,39,1*4F

$GBGSV,6,3,21,39,,,38,13,,,36,38,,,36,41,,,36,1*78

$GBGSV,6,4,21,1,,,35,6,,,35,16,,,35,7,,,34,1*41

$GBGSV,6,5,21,2,,,34,9,,,34,5,,,31,34,,,31,1*4F

$GBGSV,6,6,21,4,,,29,1*4A

$GBRMC,122148.512,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122148.512,0.000,1508.322,1508.322,48.282,2097152,2097152,2097152*5F



2025-07-31 20:21:45:127 ==>> [D][05:18:12][COMM]read battery soc:255


2025-07-31 20:21:45:367 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:21:45:380 ==>> #################### 【测试结束】 ####################
2025-07-31 20:21:45:398 ==>> 关闭5V供电
2025-07-31 20:21:45:406 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:21:45:442 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:21:45:727 ==>> $GBGGA,122149.512,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,41,14,,,41,24,,,40,60,,,40,1*72

$GBGSV,6,2,24,25,,,40,42,,,40,3,,,40,59,,,39,1*44

$GBGSV,6,3,24,39,,,38,13,,,36,38,,,36,41,,,36,1*7D

$GBGSV,6,4,24,1,,,36,16,,,36,6,,,35,7,,,34,1*44

$GBGSV,6,5,24,2,,,34,9,,,34,26,,,34,8,,,32,1*42

$GBGSV,6,6,24,5,,,31,34,,,31,4,,,29,40,,,36,1*7C

$GBRMC,122149.512,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122149.512,0.000,1501.543,1501.543,48.067,2097152,2097152,2097152*57



2025-07-31 20:21:46:409 ==>> 关闭5V供电成功
2025-07-31 20:21:46:414 ==>> 关闭33V供电
2025-07-31 20:21:46:419 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:21:46:544 ==>> 5A A5 02 5A A5 


2025-07-31 20:21:46:649 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:21:46:754 ==>> $GBGGA,122150.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,14,,,40,24,,,40,60,,,40,1*70

$GBGSV,6,2,24,25,,,40,42

2025-07-31 20:21:46:859 ==>> ,,,40,3,,,40,59,,,39,1*44

$GBGSV,6,3,24,39,,,38,13,,,36,38,,,36,41,,,36,1*7D

$GBGSV,6,4,24,1,,,36,16,,,36,6,,,35,9,,,35,1*4B

$GBGSV,6,5,24,7,,,34,2,,,34,26,,,34,8,,,33,1*4D

$GBGSV,6,6,24,40,,,31,5,,,31,34,,,31,4,,,28,1*7A

$GBRMC,122150.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122150.512,0.000,1494.264,1494.264,47.841,2097152,2097152,2097152*5C

[D][05:18:14][FCTY]get_ext_48v_vol retry i = 0,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 1,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 2,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 3,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 4,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 5,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 6,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 7,volt = 15
[D][05:18:14][FCTY]get_ext_48v_vol retry i = 8,volt = 15


2025-07-31 20:21:47:069 ==>> [D][05:18:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:21:47:419 ==>> 关闭33V供电成功
2025-07-31 20:21:47:424 ==>> 关闭3.7V供电
2025-07-31 20:21:47:428 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:21:47:541 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:21:47:994 ==>>  

