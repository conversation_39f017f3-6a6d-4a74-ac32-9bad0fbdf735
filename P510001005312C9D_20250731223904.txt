2025-07-31 22:39:04:749 ==>> MES查站成功:
查站序号:P510001005312C9D验证通过
2025-07-31 22:39:04:754 ==>> 扫码结果:P510001005312C9D
2025-07-31 22:39:04:757 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:39:04:760 ==>> 测试参数版本:2024.10.11
2025-07-31 22:39:04:762 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:39:04:767 ==>> 检测【打开透传】
2025-07-31 22:39:04:768 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:39:04:803 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:39:05:039 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:39:05:044 ==>> 检测【检测接地电压】
2025-07-31 22:39:05:046 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:39:05:104 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 22:39:05:327 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:39:05:330 ==>> 检测【打开小电池】
2025-07-31 22:39:05:334 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:39:05:407 ==>> 6A A6 01 A6 6A 


2025-07-31 22:39:05:512 ==>> Battery ON
OVER 150


2025-07-31 22:39:05:605 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:39:05:607 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:39:05:609 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:39:05:707 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:39:05:880 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:39:05:882 ==>> 检测【等待设备启动】
2025-07-31 22:39:05:884 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:06:294 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:06:489 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:06:920 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:06:982 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:07:163 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:07:678 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:07:845 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:07:950 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:08:356 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:08:536 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:08:982 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:09:044 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:09:211 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:09:728 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:09:910 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:10:015 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:10:396 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:10:593 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:11:055 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:11:085 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:11:268 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:11:769 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:11:952 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:12:088 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:12:442 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:12:621 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:13:120 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:13:122 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:13:302 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:13:818 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:13:985 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:14:153 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:14:509 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:14:674 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T

2025-07-31 22:39:15:181 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:15:196 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:39:15:363 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:15:857 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:16:054 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:16:236 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:39:16:239 ==>> #################### 【测试结束】 ####################
2025-07-31 22:39:16:257 ==>> 关闭5V供电
2025-07-31 22:39:16:259 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:39:16:312 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:39:16:540 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:16:723 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:17:218 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:17:263 ==>> 关闭5V供电成功
2025-07-31 22:39:17:267 ==>> 关闭33V供电
2025-07-31 22:39:17:269 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:39:17:414 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 5A A5 02 5A A5 


2025-07-31 22:39:17:504 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:39:17:904 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:39:18:085 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]T 

2025-07-31 22:39:18:268 ==>> 关闭33V供电成功
2025-07-31 22:39:18:271 ==>> 关闭3.7V供电
2025-07-31 22:39:18:274 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:39:18:409 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:39:18:764 ==>>  

