2025-07-31 23:22:46:418 ==>> MES查站成功:
查站序号:P510001005313861验证通过
2025-07-31 23:22:46:433 ==>> 扫码结果:P510001005313861
2025-07-31 23:22:46:434 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:22:46:435 ==>> 测试参数版本:2024.10.11
2025-07-31 23:22:46:437 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:22:46:438 ==>> 检测【打开透传】
2025-07-31 23:22:46:440 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:22:46:523 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:22:47:747 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:22:47:770 ==>> 检测【检测接地电压】
2025-07-31 23:22:47:772 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:22:47:828 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:22:48:052 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:22:48:054 ==>> 检测【打开小电池】
2025-07-31 23:22:48:057 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:22:48:118 ==>> 6A A6 01 A6 6A 


2025-07-31 23:22:48:223 ==>> Battery ON
OVER 150


2025-07-31 23:22:48:331 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:22:48:333 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:22:48:335 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:22:48:419 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:22:48:604 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:22:48:606 ==>> 检测【等待设备启动】
2025-07-31 23:22:48:608 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:22:48:912 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:22:49:107 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:22:49:647 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:22:49:754 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:22:49:830 ==>> [W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:22:50:224 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:22:50:684 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:22:50:699 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:22:50:959 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:22:50:961 ==>> 检测【产品通信】
2025-07-31 23:22:50:963 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:22:51:109 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 23:22:51:233 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:22:51:235 ==>> 检测【初始化完成检测】
2025-07-31 23:22:51:238 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:22:51:334 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 23:22:51:439 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:22:51:517 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:22:51:519 ==>> 检测【关闭大灯控制1】
2025-07-31 23:22:51:520 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:22:51:759 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2638 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:22:51:812 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:22:51:815 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:22:51:816 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:22:51:864 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],us

2025-07-31 23:22:51:909 ==>> ed[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:22:52:014 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:22:52:112 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:22:52:114 ==>> 检测【关闭仪表供电】
2025-07-31 23:22:52:117 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:22:52:315 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:22:52:411 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:22:52:413 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:22:52:416 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:22:52:601 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:22:52:698 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:22:52:702 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:22:52:704 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:22:52:766 ==>> [D][05:17:52][COMM]3649 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:22:52:902 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:22:52:980 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:22:52:993 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:22:52:995 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:22:53:203 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:22:53:261 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:22:53:263 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:22:53:267 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:22:53:309 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:22:53:384 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0


2025-07-31 23:22:53:444 ==>>                                    :255


2025-07-31 23:22:53:537 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:22:53:540 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:22:53:542 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:22:53:625 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:22:53:792 ==>> [D][05:17:53][COMM]4661 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 23:22:53:810 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:22:53:823 ==>> 该项需要延时执行
2025-07-31 23:22:54:309 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5017. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5018. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E-

2025-07-31 23:22:54:340 ==>> >0x01B987FE 5019


2025-07-31 23:22:54:797 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:22:54:992 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:22:55:552 ==>> [D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:

2025-07-31 23:22:55:658 ==>> 17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuit

2025-07-31 23:22:55:763 ==>> Volt error
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:

2025-07-31 23:22:55:852 ==>> 54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 23:22:56:809 ==>> [D][05:17:56][COMM]7693 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 23:22:57:472 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:22:57:808 ==>> [D][05:17:57][COMM]8704 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:22:57:824 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:22:57:828 ==>> 检测【33V输入电压ADC】
2025-07-31 23:22:57:831 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:22:58:138 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:17:57][COMM]adc read out 24v adc:1319  volt:33361 mv
[D][05:17:57][COMM]adc read left brake adc:5  volt:6 mv
[D][05:17:57][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read throttle adc:12  volt:15 mv
[D][05:17:57][COMM]adc read battery ts volt:12 mv
[D][05:17:57][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:17:57][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:22:58:353 ==>> 【33V输入电压ADC】通过,【32982mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:22:58:356 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:22:58:358 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:22:58:439 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2769mV
Get AD_V6 1994mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:22:58:633 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:58:635 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:22:58:659 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:58:661 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:22:58:683 ==>> 原始值:【2769】, 乘以分压基数【2】还原值:【5538】
2025-07-31 23:22:58:688 ==>> 【TP68_VCC5V5(ADV5)】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:22:58:691 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:22:58:715 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:22:58:717 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:22:58:751 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:22:58:753 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:22:58:848 ==>> [D][05:17:58][COMM]9715 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2769mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:22:59:036 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:59:038 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:22:59:055 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:59:057 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:22:59:060 ==>> 原始值:【2769】, 乘以分压基数【2】还原值:【5538】
2025-07-31 23:22:59:078 ==>> 【TP68_VCC5V5(ADV5)】通过,【5538mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:22:59:081 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:22:59:097 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:22:59:100 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:22:59:122 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:22:59:125 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:22:59:149 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10006
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 23:22:59:239 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1668mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 1990mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:22:59:398 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:59:401 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:22:59:417 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:22:59:419 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:22:59:423 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 23:22:59:439 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:22:59:442 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:22:59:468 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 23:22:59:472 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:22:59:474 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:22:59:496 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:22:59:512 ==>> 检测【打开WIFI(1)】
2025-07-31 23:22:59:514 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:22:59:680 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:22:59:773 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:22:59:776 ==>> 检测【清空消息队列(1)】
2025-07-31 23:22:59:779 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:23:00:088 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10726 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[

2025-07-31 23:23:00:133 ==>> D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 23:23:00:303 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:23:00:306 ==>> 检测【打开GPS(1)】
2025-07-31 23:23:00:308 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:23:00:586 ==>>                                                                                                                                                                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087589406

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539550

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]star

2025-07-31 23:23:00:615 ==>> t cont locating


2025-07-31 23:23:00:825 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:23:00:828 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:23:00:831 ==>> 检测【打开GSM联网】
2025-07-31 23:23:00:834 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:23:01:413 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikea

2025-07-31 23:23:01:518 ==>> pi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 23:23:01:611 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:23:01:615 ==>> 检测【打开仪表供电1】
2025-07-31 23:23:01:618 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:23:01:623 ==>>                                                                                                 llular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:23:02:037 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:23:02:147 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:23:02:150 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:23:02:152 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:23:02:326 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:23:02:437 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:23:02:441 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:23:02:448 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:23:02:612 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33154]


2025-07-31 23:23:02:720 ==>> 【读取主控ADC采集的仪表电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:23:02:724 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:23:02:726 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:02:762 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,41,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:23:02:822 ==>> [D][05:18:02][COMM]13737 imu init OK


2025-07-31 23:23:02:927 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:02:990 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:23:02:994 ==>> 检测【AD_V20电压】
2025-07-31 23:23:02:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:03:095 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:23:03:307 ==>> 本次取值间隔时间:210ms
2025-07-31 23:23:03:324 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:03:412 ==>> 本次取值间隔时间:99ms
2025-07-31 23:23:03:415 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:23:03:637 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,60,,,41,25,,,41,16,,,39,34,,,39,1*74

$GBGSV,3,2,10,23,,,36,33,,,35,59,,,44,10,,,40,1*7D

$GBGSV,3,3,10,7,,,40,11,,,37,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1596.112,1596.112,51.027,2097152,2097152,2097152*4E

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 23:23:03:834 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:23:03:909 ==>> 本次取值间隔时间:484ms
2025-07-31 23:23:04:245 ==>> 本次取值间隔时间:324ms
2025-07-31 23:23:04:249 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:04:355 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:23:04:430 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:23:04:490 ==>> 本次取值间隔时间:134ms
2025-07-31 23:23:04:535 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:04:539 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,25,,,41,59,,,40,60,,,40,34,,,40,1*7F

$GBGSV,5,2,17,39,,,40,3,,,40,16,,,39,11,,,39,1*49

$GBGSV,5,3,17,10,,,38,7,,,38,23,,,38,2,,,37,1*7C

$GBGSV,5,4,17,33,,,35,43,,,34,4,,,32,40,,,22,1*46

$GBGSV,5,5,17,41,,,39,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1536.634,1536.6

2025-07-31 23:23:04:566 ==>> 34,49.241,2097152,2097152,2097152*45



2025-07-31 23:23:04:641 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:23:04:730 ==>> 本次取值间隔时间:85ms
2025-07-31 23:23:04:734 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:23:04:836 ==>> 本次取值间隔时间:93ms
2025-07-31 23:23:04:878 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:04:991 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:23:05:219 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:05:249 ==>> 本次取值间隔时间:252ms
2025-07-31 23:23:05:566 ==>> [D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,25,,,41,39,,,41,59,,,40,60,,,40,1*77

$GBGSV,5,2,20,34,,,40,3,,,40,16,,,39,11,,,39,1*40

$GBGSV,5,3,20,41,,,39,7,,,39,23,,,39,10,,,38,1*41

$GBGSV,5,4,20,1,,,38,40,,,37,43,,,36,2,,,35,1*79

$GBGSV,5,5,20,33,,,35,5,,,33,4,,,32,32,,,32,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1560.900,1560.900,49.929,2097152,2097152,2097152*40



2025-07-31 23:23:05:670 ==>> 本次取值间隔时间:414ms
2025-07-31 23:23:06:063 ==>> 本次取值间隔时间:388ms
2025-07-31 23:23:06:401 ==>> 本次取值间隔时间:337ms
2025-07-31 23:23:06:405 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:23:06:508 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:23:06:613 ==>> 本次取值间隔时间:103ms
2025-07-31 23:23:06:658 ==>> $GBGGA,152310.441,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,25,,,41,39,,,41,34,,,41,59,,,40,1*75

$GBGSV,6,2,21,60,,,40,3,,,40,11,,,40,7,,,40,1*73

$GBGSV,6,3,21,16,,,39,23,,,39,40,,,39,41,,,38,1*76

$GBGSV,6,4,21,10,,,38,43,,,38,1,,,37,2,,,35,1*70

$GBGSV,6,5,21,33,,,35,5,,,33,4,,,32,32,,,32,1*70

$GBGSV,6,6,21,6,,,39,1*49

$GBRMC,152310.441,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152310.441,0.000,1571.268,1571.268,50.263,2097152,2097152,2097152*56

[W][05:18:06][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 1658mV
OVER 150


2025-07-31 23:23:07:108 ==>> 本次取值间隔时间:487ms
2025-07-31 23:23:07:126 ==>> 【AD_V20电压】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:23:07:129 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:23:07:131 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:23:07:229 ==>> 3A A3 02 00 A3 


2025-07-31 23:23:07:319 ==>> OFF_OUT2
OVER 150


2025-07-31 23:23:07:402 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:23:07:405 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:23:07:409 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:23:07:679 ==>> $GBGGA,152311.421,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,25,,,41,39,,,41,34,,,41,40,,,41,1*79

$GBGSV,7,2,25,59,,,40,60,,,40,3,,,40,7,,,40,1*7A

$GBGSV,7,3,25,23,,,40,11,,,39,16,,,39,43,,,39,1*7A

$GBGSV,7,4,25,41,,,38,10,,,38,6,,,37,1,,,37,1*71

$GBGSV,7,5,25,2,,,35,33,,,35,5,,,33,32,,,33,1*75

$GBGSV,7,6,25,9,,,33,12,,,33,4,,,32,24,,,30,1*7A

$GBGSV,7,7,25,44,,,37,1*75

$GBRMC,152311.421,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152311.421,0.000,1546.072,1546.072,49.482,2097152,2097152,2097152*50

[D][05:18:07][COMM]read battery soc:255
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:07:944 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:23:07:949 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:23:07:971 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:23:08:031 ==>> 3A A3 02 01 A3 


2025-07-31 23:23:08:121 ==>> ON_OUT2
OVER 150


2025-07-31 23:23:08:265 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:23:08:268 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:23:08:270 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:23:08:423 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:08:528 ==>>                                                          ,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,25,,,41,39,,,41,34,,,41,1*7A

$GBGSV,7,2,25,59,,,40,60,,,40,3,,,40,7,,,40,1*7A

$GBGSV,7,3,25,23,,,40,11,,,40,43,,,40,16,,,39,1*7A

$GBGSV,7,4,25,41,,,39,10,,,38,6,,,37,1,,,37,1*70

$GBGSV,7,5,25,33,,,35,2,,,34,9,,,34,12,,,34,

2025-07-31 23:23:08:546 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:23:08:551 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:23:08:555 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:23:08:573 ==>> 1*7A

$GBGSV,7,6,25,5,,,33,32,,,33,4,,,33,24,,,33,1*76

$GBGSV,7,7,25,36,,,37,1*70

$GBRMC,152312.401,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152312.401,0.000,1561.606,1561.606,49.965,2097152,2097152,2097152*55



2025-07-31 23:23:08:723 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:23:08:816 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:23:08:819 ==>> 检测【AD_V21电压】
2025-07-31 23:23:08:823 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:23:08:888 ==>> 本次取值间隔时间:61ms
2025-07-31 23:23:08:933 ==>> 1A A1 20 00 00 
Get AD_V21 1653mV
OVER 150


2025-07-31 23:23:09:240 ==>> 本次取值间隔时间:337ms
2025-07-31 23:23:09:262 ==>> 【AD_V21电压】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:23:09:265 ==>> 检测【关闭仪表供电2】
2025-07-31 23:23:09:269 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:23:09:423 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:23:09:528 ==>>                                                                                                                                                                                                           0,11,,,40,43,,,40,16,,,39,1*7A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,33,,,35,2,,,35,9,,,35,12,,,34,1*7A

$GBGSV,7,6,25,32,,,34,24,,,34,5,,,33,4,,,33,1*76

$GBGSV,7,7,25,44,,,29,1*7A

$GBRMC,152313.381,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152313.381,0.000,1557.197,1557.197,49.838,2097152,

2025-07-31 23:23:09:533 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:23:09:536 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:23:09:558 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:23:09:561 ==>> 2097152,2097152*52

[D][05:18:09][COMM]read battery soc:255


2025-07-31 23:23:09:798 ==>> $GBGGA,152313.581,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,3,,,41,43,,,41,59,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,40,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,33,,,35,2,,,35,9,,,35,12,,,34,1*7A

$GBGSV,7,6,25,32,,,34,24,,,34,5,,,33,4,,,33,1*76

$GBGSV,7,7,25,44,,,29,1*7A

$GBRMC,152313.581,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152313.581,0.000,1558.856,1558.856,49.892,2097152,2097152,2097152*54

[D][05:18:09][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:09][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:23:10:085 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:23:10:089 ==>> 检测【打开AccKey2供电】
2025-07-31 23:23:10:091 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:23:10:292 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:23:10:355 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:23:10:358 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:23:10:362 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:10:643 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:10][COMM]adc read out 24v adc:1326  volt:33538 mv
[D][05:18:10][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:10][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:10][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:10][COMM]adc read battery ts volt:12 mv
[D][05:18:10][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:10][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3353  volt:2701 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:10:733 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:23:10:887 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33538mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:23:10:891 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:23:10:893 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:23:11:086 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:23:11:176 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:23:11:179 ==>> 该项需要延时执行
2025-07-31 23:23:11:528 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 23:23:11:708 ==>> $GBGGA,152315.541,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,59,,,41,3,,,40,43,,,40,60,,,40,1*4B

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,40,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,37,6,,,37,1*70

$GBGSV,7,5,25,33,,,35,2,,,35,9,,,35,12,,,35,1*7B

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,32,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152315.541,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152315.541,0.000,1558.847,1558.847,49.882,2097152,2097152,2097152*5F



2025-07-31 23:23:12:689 ==>> $GBGGA,152316.521,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,39,,,42,25,,,41,34,,,41,1*78

$GBGSV,7,2,25,59,,,41,3,,,41,43,,,41,60,,,40,1*4B

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,40,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,33,,,35,2,,,35,12,,,35,1*78

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152316.521,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152316.521,0.000,1570.458,1570.458,50.256,2097152,2097152,2097152*51



2025-07-31 23:23:13:681 ==>> [D][05:18:13][COMM]read battery soc:255
$GBGGA,152317.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,42,25,,,41,34,,,41,1*79

$GBGSV,7,2,25,59,,,41,43,,,41,3,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,40,16,,,40,1*44

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,33,,,35,2,,,35,12,,,35,1*78

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152317.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152317.501,0.000,1568.797,1568.797,50.200,2097152,2097152,2097152*51



2025-07-31 23:23:14:185 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:23:14:191 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:23:14:197 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:14:535 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3166  volt:5565 mv
[D][05:18:14][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:14][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:14][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:14][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:14][COMM]adc read battery ts volt:8 mv
[D][05:18:14][COMM]adc read in 24v adc:1309  volt:33108 mv
[D][05:18:14][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2391  volt:3852 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:14:640 ==>>                                                                                                                                                                                                                                                GSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,33,,,35,2,,,35,12,,,35,1*78

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152318.501,V,,,,,,,,0.0,E,N,V*46

$GBVTG,

2025-07-31 23:23:14:670 ==>> 0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152318.501,0.000,1563.817,1563.817,50.036,2097152,2097152,2097152*59



2025-07-31 23:23:14:719 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:23:14:722 ==>> 检测【打开AccKey1供电】
2025-07-31 23:23:14:726 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:23:14:913 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:23:14:992 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:23:14:998 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:23:15:005 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:23:15:123 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 23:23:15:243 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 23:23:15:261 ==>> 【读取AccKey1电压(ADV14)前】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:23:15:268 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:23:15:272 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:15:530 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:18:15][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:15][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:15][COMM]adc read battery ts volt:10 mv
[D][05:18:15][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1430  volt:33154 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:15:635 ==>>                                                                                                                                                                                                                                                          ,40,11,,,40,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,33,,,35,2,,,35,12,,,35,1*78

$GBGSV,7,6,25,32,,,34,24,,,34,4,

2025-07-31 23:23:15:680 ==>> ,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152319.501,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152319.501,0.000,1563.817,1563.817,50.036,2097152,2097152,2097152*58



2025-07-31 23:23:15:789 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5559mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:23:15:795 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:23:15:817 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:23:16:013 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:23:16:062 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:23:16:067 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:23:16:072 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:23:16:118 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 23:23:16:316 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 23:23:16:335 ==>> 【读取AccKey1电压(ADV14)后】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:23:16:338 ==>> 检测【打开WIFI(2)】
2025-07-31 23:23:16:343 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:23:16:689 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6
$GBGGA,152320.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,40,1*7B

$GBGSV,7,2,25,59,,,40,3,,,40,43,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,39,11,,,39,16,,,39,1*4A

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,33,,,35,2,,,35,12,,,35,1*7B

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152320.501,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152320.501,0.000,1553.862,1553.862,49.713,2097152,2097152,2097152*5A



2025-07-31 23:23:16:873 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:23:16:876 ==>> 检测【转刹把供电】
2025-07-31 23:23:16:879 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:23:16:992 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:23:17:145 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:23:17:149 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:23:17:153 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:23:17:247 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:23:17:323 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2411mV
OVER 150


2025-07-31 23:23:17:398 ==>> 原始值:【2411】, 乘以分压基数【2】还原值:【4822】
2025-07-31 23:23:17:416 ==>> 【读取AD_V15电压(前)】通过,【4822mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:23:17:423 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:23:17:431 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:23:17:521 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:23:17:686 ==>> [D][05:18:17][COMM]read battery soc:255
$GBGGA,152321.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,59,,,40,3,,,40,43,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,39,16,,,39,1*44

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,33,,,35,2,,,35,12,,,35,1*7B

$GBGSV,7,6,25,32,,,34,24,,,34,4,,,33,5,,,32,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152321.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152321.501,0.000,1555.527,1555.527,49.773,2097152,2097152,2097152*5D

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2444mV
OVER 150


2025-07-31 23:23:17:837 ==>> 原始值:【2444】, 乘以分压基数【2】还原值:【4888】
2025-07-31 23:23:17:860 ==>> 【读取AD_V16电压(前)】通过,【4888mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:23:17:863 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:23:17:868 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:18:141 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:17][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:17][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:17][COMM]adc read battery ts volt:6 mv
[D][05:18:17][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3127  volt:5496 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:18:385 ==>> 【转刹把供电电压(主控ADC)】通过,【5496mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:23:18:390 ==>> 检测【转刹把供电电压】
2025-07-31 23:23:18:395 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:18:400 ==>> +WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,F42A7D1297A3,-68
+WIFISCAN:4,2,CC057790A5C1,-81
+WIFISCAN:4,3,CC057790A820,-87

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 23:23:18:787 ==>> $GBGGA,152322.501,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,43,,,41,59,,,40,3,,,40,60,,,40,1*4B

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,39,16,,,39,1*44

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,33,,,35,2,,,35,12,,,35,1*7B

$GBGSV,7,6,25,32,,,35,24,,,34,4,,,33,5,,,33,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152322.501,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152322.501,0.000,1560.498,1560.498,49.928,2097152,2097152,2097152*5E

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:18][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:18][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:18][COMM]adc read battery ts volt:9 mv
[D][05:18:18][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3123  volt:5489 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:18

2025-07-31 23:23:18:833 ==>> ][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:18:907 ==>> [D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 23:23:18:912 ==>> 【转刹把供电电压】通过,【5489mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:23:18:917 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:23:18:923 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:23:19:089 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:23:19:212 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:23:19:219 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:23:19:228 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:23:19:314 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:23:19:421 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:23:19:429 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:23:19:526 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:23:19:652 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:23:19:659 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:23:19:663 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:23:19:678 ==>> [D][05:18:19][COMM]read battery soc:255
$GBGGA,152323.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,43,,,41,59,,,40,3,,,40,60,,,40,1*4B

$GBGSV,7,3,25,7,,,40,23,,,40,11,,,40,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,33,,,35,2,,,35,12,,,35,1*78

$GBGSV,7,6,25,32,,,35,24,,,34,4,,,33,5,,,33,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152323.501,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152323.501,0.000,1565.473,1565.473,50.087,2097152,2097152,2097152*5B

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:23:19:753 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:23:19:783 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:23:19:828 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:23:19:876 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:23:19:880 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:23:19:883 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:23:20:026 ==>> 3A A3 03 01 A3 


2025-07-31 23:23:20:131 ==>> ON_OUT3
OVER 150


2025-07-31 23:23:20:408 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:23:20:413 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:23:20:419 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:23:20:526 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:23:20:631 ==>> $GBGGA,152324.501,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,43,,,40,59,,,40,3,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,39,11,,,39,16,,,39,1*4A

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,2,,,35,12,,,35,32,,,35,1*79

$GBGSV,7,6,25,33,,,34,24,,,3

2025-07-31 23:23:20:676 ==>> 4,4,,,33,5,,,33,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152324.501,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152324.501,0.000,1558.838,1558.838,49.873,2097152,2097152,2097152*57



2025-07-31 23:23:20:691 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:23:20:699 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:23:20:724 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:23:20:827 ==>> 3A A3 05 01 A3 


2025-07-31 23:23:20:917 ==>> ON_OUT5
OVER 150


2025-07-31 23:23:20:965 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:23:20:969 ==>> 检测【左刹电压测试1】
2025-07-31 23:23:20:975 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:21:235 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:18:20][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:20][COMM]adc read left brake adc:1747  volt:2303 mv
[D][05:18:20][COMM]adc read right brake adc:1736  volt:2288 mv
[D][05:18:20][COMM]adc read throttle adc:1740  volt:2293 mv
[D][05:18:20][COMM]adc read battery ts volt:17 mv
[D][05:18:20][COMM]adc read in 24v adc:1315  volt:33260 mv
[D][05:18:20][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:23:21:495 ==>> 【左刹电压测试1】通过,【2303】符合目标值【2250】至【2500】要求!
2025-07-31 23:23:21:499 ==>> 检测【右刹电压测试1】
2025-07-31 23:23:21:518 ==>> 【右刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 23:23:21:521 ==>> 检测【转把电压测试1】
2025-07-31 23:23:21:537 ==>> 【转把电压测试1】通过,【2293】符合目标值【2250】至【2500】要求!
2025-07-31 23:23:21:540 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:23:21:543 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:23:21:675 ==>> [D][05:18:21][COMM]read battery soc:255
$GBGGA,152325.501,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,43,,,40,59,,,40,3,,,40,60,,,40,1*4A

$GBGSV,7,3,25,7,,,40,23,,,39,11,,,39,16,,,39,1*4A

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,2,,,35,12,,,35,32,,,35,1*7A

$GBGSV,7,6,25,33,,,35,24,,,34,4,,,33,5,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152325.501,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152325.501,0.000,1557.178,1557.178,49.819,2097152,2097152,2097152*5A

3A A3 03 00 A3 


2025-07-31 23:23:21:721 ==>> OFF_OUT3
OVER 150


2025-07-31 23:23:21:825 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:23:21:828 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:23:21:832 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:23:21:931 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:23:22:112 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:23:22:116 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:23:22:120 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:23:22:232 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 23:23:22:386 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:23:22:390 ==>> 检测【左刹电压测试2】
2025-07-31 23:23:22:395 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:23:22:783 ==>> $GBGGA,152326.501,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,40,1*7B

$GBGSV,7,2,25,43,,,40,59,,,40,3,,,40,7,,,40,1*7B

$GBGSV,7,3,25,60,,,39,23,,,39,11,,,39,16,,,39,1*75

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,2,,,35,12,,,35,32,,,35,1*7A

$GBGSV,7,6,25,33,,,34,24,,,34,4,,,33,5,,,33,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152326.501,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152326.501,0.000,1552.202,1552.202,49.659,2097152,2097152,2097152*53

[D][05:18:22][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:18:22][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:22][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:22][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:22][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:22][COMM]adc read battery ts volt:14 mv
[D][05:18:22][COMM]adc read in 24v adc:1308  volt:33083 mv
[D][05:18:22][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:22][

2025-07-31 23:23:22:828 ==>> COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2392  volt:3854 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:23:22:924 ==>> 【左刹电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 23:23:22:929 ==>> 检测【右刹电压测试2】
2025-07-31 23:23:22:945 ==>> 【右刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 23:23:22:951 ==>> 检测【转把电压测试2】
2025-07-31 23:23:22:967 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 23:23:22:970 ==>> 检测【晶振检测】
2025-07-31 23:23:22:976 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:23:23:106 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 23:23:23:245 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:23:23:249 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:23:23:255 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:23:23:332 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1669mV
Get AD_V4 1647mV
Get AD_V5 2771mV
Get AD_V6 1989mV
Get AD_V7 1087mV
OVER 150


2025-07-31 23:23:23:515 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:23:23:521 ==>> 检测【检测BootVer】
2025-07-31 23:23:23:544 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:23:677 ==>> [D][05:18:23][COMM]read battery soc:255
$GBGGA,152327.501,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,40,34,,,40,1*7A

$GBGSV,7,2,25,43,,,40,59,,,40,3,,,40,7,,,40,1*7B

$GBGSV,7,3,25,60,,,40,23,,,39,11,,,39,16,,,38,1*7A

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,37,6,,,37,1*71

$GBGSV,7,5,25,9,,,35,2,,,35,32,,,35,12,,,34,1*7B

$GBGSV,7,6,25,33,,,34,24,,,34,4,,,33,5,,,33,1*77

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152327.501,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152327.501,0.000,1547.228,1547.228,49.501,2097152,2097152,2097152*5C



2025-07-31 23:23:23:947 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539550
[D][05:18:23][FCTY]HardwareID  = 867222087589406
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:23][FCTY]Bat         = 3924 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 33, adc = 1307
[D][05:18:23][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 278 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION =

2025-07-31 23:23:23:992 ==>>  V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3785 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:23:24:066 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:23:24:070 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:23:24:075 ==>> 检测【检测固件版本】
2025-07-31 23:23:24:097 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:23:24:101 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:23:24:108 ==>> 检测【检测蓝牙版本】
2025-07-31 23:23:24:132 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:23:24:153 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:23:24:158 ==>> 检测【检测MoBikeId】
2025-07-31 23:23:24:162 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:23:24:190 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:23:24:194 ==>> 检测【检测蓝牙地址】
2025-07-31 23:23:24:197 ==>> 取到目标值:E2ABD7C5BC11
2025-07-31 23:23:24:201 ==>> 【检测蓝牙地址】通过,【E2ABD7C5BC11】符合目标值【】要求!
2025-07-31 23:23:24:214 ==>> 提取到蓝牙地址:E2ABD7C5BC11
2025-07-31 23:23:24:218 ==>> 检测【BOARD_ID】
2025-07-31 23:23:24:223 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:23:24:227 ==>> 检测【检测充电电压】
2025-07-31 23:23:24:237 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:23:24:241 ==>> 检测【检测VBUS电压1】
2025-07-31 23:23:24:260 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:23:24:266 ==>> 检测【检测充电电流】
2025-07-31 23:23:24:279 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:23:24:286 ==>> 检测【检测IMEI】
2025-07-31 23:23:24:298 ==>> 取到目标值:867222087589406
2025-07-31 23:23:24:303 ==>> 【检测IMEI】通过,【867222087589406】符合目标值【】要求!
2025-07-31 23:23:24:328 ==>> 提取到IMEI:867222087589406
2025-07-31 23:23:24:332 ==>> 检测【检测IMSI】
2025-07-31 23:23:24:337 ==>> 取到目标值:460130071539550
2025-07-31 23:23:24:357 ==>> 【检测IMSI】通过,【460130071539550】符合目标值【】要求!
2025-07-31 23:23:24:361 ==>> 提取到IMSI:460130071539550
2025-07-31 23:23:24:365 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:23:24:388 ==>> 取到目标值:460130071539550
2025-07-31 23:23:24:393 ==>> 【校验网络运营商(移动)】通过,【460130071539550】符合目标值【】要求!
2025-07-31 23:23:24:397 ==>> 检测【打开CAN通信】
2025-07-31 23:23:24:404 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:23:24:431 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:23:24:630 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:23:24:638 ==>> 检测【检测CAN通信】
2025-07-31 23:23:24:660 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:23:24:672 ==>> $GBGGA,152328.501,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,59,,,41,43,,,40,3,,,40,7,,,40,1*7A

$GBGSV,7,3,25,60,,,40,11,,,40,23,,,39,16,,,39,1*75

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,37,6,,,37,1*70

$GBGSV,7,5,25,9,,,36,2,,,35,32,,,35,33,,,35,1*7A

$GBGSV,7,6,25,12,,,34,24,,,34,4,,,33,5,,,33,1*74

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152328.501,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152328.501,0.000,1560.499,1560.499,49.929,2097152,2097152,2097152*55



2025-07-31 23:23:24:717 ==>> can send success


2025-07-31 23:23:24:747 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:23:24:807 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35692
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:23:24:868 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:23:24:901 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:23:24:908 ==>> 检测【关闭CAN通信】
2025-07-31 23:23:24:914 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:23:24:927 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:23:24:987 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:23:25:032 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:23:25:171 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:23:25:175 ==>> 检测【打印IMU STATE】
2025-07-31 23:23:25:181 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:23:25:321 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:23:25:466 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:23:25:472 ==>> 检测【六轴自检】
2025-07-31 23:23:25:479 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:23:25:712 ==>> [D][05:18:25][COMM]read battery soc:255
$GBGGA,152329.501,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,40,1*7B

$GBGSV,7,2,25,59,,,40,43,,,40,3,,,40,7,,,40,1*7B

$GBGSV,7,3,25,60,,,40,11,,,39,23,,,39,16,,,39,1*7B

$GBGSV,7,4,25,41,,,38,10,,,38,1,,,38,6,,,37,1*7E

$GBGSV,7,5,25,9,,,35,2,,,35,32,,,35,33,,,35,1*79

$GBGSV,7,6,25,12,,,35,24,,,34,4,,,33,5,,,33,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152329.501,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152329.501,0.000,1555.518,1555.518,49.764,2097152,2097152,2097152*53

[W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:23:26:678 ==>> $GBGGA,152330.501,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,59,,,40,43,,,40,3,,,40,7,,,40,1*7B

$GBGSV,7,3,25,60,,,40,11,,,39,23,,,39,16,,,39,1*7B

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,32,,,36,9,,,35,2,,,35,33,,,34,1*7B

$GBGSV,7,6,25,12,,,34,24,,,34,4,,,33,5,,,32,1*75

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152330.501,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152330.501,0.000,1555.527,1555.527,49.773,2097152,2097152,2097152*5D



2025-07-31 23:23:27:312 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:23:27:507 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38380 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-31,0,4076]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 23:23:27:555 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:23:27:560 ==>> 检测【打印IMU STATE2】
2025-07-31 23:23:27:564 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:23:27:612 ==>> [D][05:18:27][COMM]read battery soc:255
$GBGGA,152331.501,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,

2025-07-31 23:23:27:672 ==>> ,,41,25,,,41,34,,,41,1*7A

$GBGSV,7,2,25,3,,,41,59,,,40,43,,,40,7,,,40,1*7A

$GBGSV,7,3,25,60,,,40,11,,,39,23,,,39,16,,,39,1*7B

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,32,,,36,9,,,35,2,,,35,33,,,35,1*7A

$GBGSV,7,6,25,12,,,35,24,,,34,4,,,33,5,,,33,1*75

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152331.501,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152331.501,0.000,778.797,778.797,712.227,2097152,2097152,2097152*61



2025-07-31 23:23:27:717 ==>>                                                                                                                                                                                                                       

2025-07-31 23:23:28:584 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:23:28:674 ==>> $GBGGA,152332.501,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,43,3,,,41,39,,,41,34,,,41,1*4F

$GBGSV,7,2,25,25,,,41,7,,,40,60,,,40,43,,,40,1*44

$GBGSV,7,3,25,59,,,40,23,,,40,11,,,40,16,,,39,1*71

$GBGSV,7,4,25,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,7,5,25,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,7,6,25,33,,,35,24,,,34,4,,,34,5,,,33,1*71

$GBGSV,7,7,25,44,,,30,1*72

$GBRMC,152332.501,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152332.501,0.000,782.935,782.935,716.012,2097152,2097152,2097152*62



2025-07-31 23:23:28:779 ==>> [

2025-07-31 23:23:28:824 ==>> W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:23:28:864 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:23:28:868 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:23:28:876 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:23:28:929 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:23:29:035 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 13
[

2025-07-31 23:23:29:080 ==>> D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 13
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 13


2025-07-31 23:23:29:143 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:23:29:148 ==>> 检测【检测VBUS电压2】
2025-07-31 23:23:29:155 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:29:499 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539550
[D][05:18:29][FCTY]HardwareID  = 867222087589406
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:29][FCTY]Bat         = 3924 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11700 mv
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 8, adc = 320
[D][05:18:29][FCTY]Acckey1 vol = 5570 mv, Acckey2 vol = 75 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]C

2025-07-31 23:23:29:544 ==>> AT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3785 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:23:29:650 ==>>                                                                                                                                                                                                                                                                                                                                             1*7A

$GBGSV,7,6,25,33,,,35,24,,,34,5,,,33,4,,,33,1*76

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,152333.501,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152333.501,0.000,

2025-07-31 23:23:29:668 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:29:680 ==>> 781.277,781.277,714.495,2097152,2097152,2097152*6A



2025-07-31 23:23:30:009 ==>> [D][05:18:29][COMM]msg 0601 loss. last_tick:35688. cur_tick:40691. period:500
[D][05:18:29][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40692
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539550
[D][05:18:29][FCTY]HardwareID  = 867222087589406
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:29][FCTY]Bat         = 3924 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 8100 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 178
[D][05:18:29][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 75 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERN

2025-07-31 23:23:30:054 ==>> EL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3785 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:23:30:193 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:30:588 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539550
[D][05:18:30][FCTY]HardwareID  = 867222087589406
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:30][FCTY]Bat         = 3824 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 8100 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 146
[D][05:18:30][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 25 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][

2025-07-31 23:23:30:693 ==>> 05:18:30][FCTY]Bat1         = 3785 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 23:23:30:727 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:31:428 ==>> [D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539550
[D][05:18:30][FCTY]HardwareID  = 867222087589406
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:30][FCTY]Bat         = 3824 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 8100 mv
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state

2025-07-31 23:23:31:533 ==>> 1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:30][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not c

2025-07-31 23:23:31:637 ==>> onnect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished proc

2025-07-31 23:23:31:743 ==>> essing
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 130
[D][05:18:30][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 252 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3785 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B

2025-07-31 23:23:31:779 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:23:31:819 ==>> 510BFEF0AC64F08B55A20D918EB24DFAB7B28CAD9B339219F23064E4E8F6364C7FBE94D9E836D87EA1BDF32BF328B04BC51D2C79179450909F313D8EC03865C3923622936FFB3F4F8DADE5F5645CBFEC5D3F9
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:31][PROT]M2M Send ok [1629955111]


2025-07-31 23:23:32:108 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539550
[D][05:18:31][FCTY]HardwareID  = 867222087589406
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:18:31][FCTY]Bat         = 3844 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:31][FCTY]Acckey1 vol = 5552 mv, Acckey2 vol = 101 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18

2025-07-31 23:23:32:153 ==>> :31][FCTY]Bat1         = 3785 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][GNSS]recv submsg id[1]
[D][05:18:31][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:31][GNSS]location stop evt done evt


2025-07-31 23:23:32:309 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:23:32:315 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:23:32:322 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:23:32:429 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:23:32:489 ==>> [D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 14


2025-07-31 23:23:32:580 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:23:32:590 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:23:32:613 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:23:32:620 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:23:32:730 ==>> 5A A5 04 5A A5 


2025-07-31 23:23:32:821 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:23:32:869 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:23:32:875 ==>> 检测【打开WIFI(3)】
2025-07-31 23:23:32:884 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:23:33:047 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:23:33:148 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:23:33:154 ==>> 检测【扩展芯片hw】
2025-07-31 23:23:33:162 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:23:33:322 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:23:33:433 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:23:33:441 ==>> 检测【扩展芯片boot】
2025-07-31 23:23:33:475 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:23:33:480 ==>> 检测【扩展芯片sw】
2025-07-31 23:23:33:493 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:23:33:499 ==>> 检测【检测音频FLASH】
2025-07-31 23:23:33:507 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:23:33:717 ==>> +WIFISCAN:4,0,CC057790A620,-61
+WIFISCAN:4,1,CC057790A5C0,-82
+WIFISCAN:4,2,CC057790A5C1,-82
+WIFISCAN:4,3,CC057790A820,-89

[D][05:18:33][CAT1]wifi scan report total[4]
[W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:23:34:099 ==>> [D][05:18:33][GNSS]recv submsg id[3]
[D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:23:34:780 ==>>                                                                                                                                                      - get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. succ

2025-07-31 23:23:34:886 ==>> ess, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0

2025-07-31 23:23:34:990 ==>> A4F4B0D0A
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][PROT]index:1
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34]

2025-07-31 23:23:35:095 ==>> [PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]Main Task receive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state e

2025-07-31 23:23:35:200 ==>> rr
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].

2025-07-31 23:23:35:275 ==>> l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:34][COMM]read battery soc:255


2025-07-31 23:23:36:493 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B3B93A71D27245060D143558BE3831CC0ACB89B8E40445438EC4C8CA44F58F5D2B584C98A23E0CABB49D7A9C484938BEB82D255C76ED6

2025-07-31 23:23:36:553 ==>> 8F68D92C6790ABE2F29D28141D38DADEDC642F1300808D3AB2F42F440
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 23:23:36:583 ==>>                                          

2025-07-31 23:23:37:120 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:23:37:271 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:23:37:812 ==>> [D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 23:23:38:274 ==>> [D][05:18:38][COMM]49037 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:23:38:545 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:23:38:551 ==>> 检测【打开喇叭声音】
2025-07-31 23:23:38:556 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:23:38:611 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 23:23:39:223 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:38][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l

2025-07-31 23:23:39:328 ==>> :[691].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len

2025-07-31 23:23:39:347 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:23:39:354 ==>> 检测【打开大灯控制】
2025-07-31 23:23:39:377 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:23:39:433 ==>> :2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:39][COMM]50047 imu init OK


2025-07-31 23:23:39:508 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:23:39:617 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:23:39:624 ==>> 检测【关闭仪表供电3】
2025-07-31 23:23:39:634 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:23:39:793 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 23:23:39:895 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:23:39:904 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:23:39:911 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:23:40:084 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:23:40:185 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:23:40:192 ==>> 检测【读大灯电压】
2025-07-31 23:23:40:201 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:23:40:391 ==>> [W

2025-07-31 23:23:40:421 ==>> ][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[32969]


2025-07-31 23:23:40:466 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:23:40:472 ==>> 检测【关闭大灯控制2】
2025-07-31 23:23:40:478 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:23:40:617 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:18:40][COMM]read battery soc:255


2025-07-31 23:23:40:754 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:23:40:760 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:23:40:769 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:23:40:911 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:23:41:036 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:23:41:045 ==>> 检测【打开WIFI(4)】
2025-07-31 23:23:41:065 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:23:41:244 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:23:41:369 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:23:41:375 ==>> 检测【EC800M模组版本】
2025-07-31 23:23:41:384 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:23:41:653 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:41][CAT1]gsm read msg sub id: 15
[W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 23:23:41:893 ==>>                                                                                                  BFFCD46EB29F6D519CD5EA940021F6FB45589A4BE1EFBFCCEA81BCA3C56FD70637D773E0187E1F058711E426A73EA99942B109A9D0E5359F3137C9BDE4A818ED3562D2520C999B6D03A66BFC6F37077
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]
[D][05:18:41][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 132


2025-07-31 23:23:42:182 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:23:42:188 ==>> 检测【配置蓝牙地址】
2025-07-31 23:23:42:197 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:23:42:389 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E2ABD7C5BC11>】
2025-07-31 23:23:42:398 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:23:42:632 ==>> [D][05:18:42][COMM]read battery soc:255
recv ble 1
recv ble 2
ble set mac ok :e2,ab,d7,c5,bc,11
enable filters ret : 0

2025-07-31 23:23:42:711 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:23:42:720 ==>> 检测【BLETEST】
2025-07-31 23:23:42:740 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:23:42:749 ==>> [D][05:18:42][COMM]53624 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:23:42:827 ==>> 4A A4 01 A4 4A 


2025-07-31 23:23:42:932 ==>> recv ble 1
recv ble 2
<BSJ*MAC:E2ABD7C5BC11*RSSI:-26*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E2ABD7C5BC1199999

2025-07-31 23:23:43:022 ==>> OVER 150


2025-07-31 23:23:43:736 ==>> 【BLETEST】通过,【-26dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:23:43:743 ==>> 该项需要延时执行
2025-07-31 23:23:43:767 ==>> [D][05:18:43][COMM]54636 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:23:44:351 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:23:44:622 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 23:23:44:727 ==>> [D][05:18:44][COMM]55647 imu init OK


2025-07-31 23:23:45:953 ==>> +WIFISCAN:4,0,CC057790A5C1,-81
+WIFISCAN:4,1,CC057790A5C0,-81
+WIFISCAN:4,2,CC057790A4A1,-85
+WIFISCAN:4,3,CC057790A820,-88

[D][05:18:45][CAT1]wifi scan report total[4]


2025-07-31 23:23:46:028 ==>> [D][05:18:45][GNSS]recv submsg id[3]


2025-07-31 23:23:46:618 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:23:47:149 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B3190C852A9D1D15E9C72E8D6EB0F77477D1995F65B0A03B29CF2BE1D8

2025-07-31 23:23:47:224 ==>> 7742C98894BCA5EE09287EA73A3F913C2BD88759809B58625A7DCAE91051EFC4B1D288C47292C1CABECBFAD1D6BF953EB20E1827E47C
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 23:23:48:624 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:23:50:630 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:23:52:391 ==>> [D][05:18:52][PROT]CLEAN,SEND:1
[D][05:18:52][PROT]CLEAN:1
[D][05:18:52][PROT]index:0 1629955132
[D][05:18:52][PROT]is_send:0
[D][05:18:52][PROT]sequence_num:4
[D][05:18:52][PROT]retry_timeout:0
[D][05:18:52][PROT]retry_times:2
[D][05:18:52][PROT]send_path:0x2
[D][05:18:52][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:52][PROT]===========================================================
[D][05:18:52][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:52][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955132]
[D][05:18:52][PROT]===========================================================
[D][05:18:52][PROT]sending traceid [9999999999900005]
[D][05:18:52][PROT]Send_TO_M2M [1629955132]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:52][SAL ]sock send credit cnt[6]
[D][05:18:52][SAL ]sock send ind credit cnt[6]
[D][05:18:52][M2M ]m2m send data len[198]
[D][05:18:52][SAL ]Cellular task submsg id[10]
[D][05:18:52][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:52][CAT1]gsm read msg sub id: 15
[D][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][CAT1]Send Data To Server[198][201] ... ->:
0063B9821133

2025-07-31 23:23:52:467 ==>> 11331133113311331B88B5C7EE3074E127E01EC133DD8F1235C87CBC3DAC77B2CD9257677BA23CEBE30B7D9B7B04A7F77C0E336855E8A6258A30C4E4A8AD8EEC68E4E2FD43D22DFEB517C3DEE90BE4CB3E9E0D6D3CE0825E48BBB22716
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]


2025-07-31 23:23:52:648 ==>> D][05:18:52][COMM]read battery soc:255


2025-07-31 23:23:53:750 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:23:53:760 ==>> 检测【检测WiFi结果】
2025-07-31 23:23:53:787 ==>> WiFi信号:【CC057790A620】,信号值:-61
2025-07-31 23:23:53:792 ==>> WiFi信号:【F42A7D1297A3】,信号值:-68
2025-07-31 23:23:53:803 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 23:23:53:830 ==>> WiFi信号:【CC057790A820】,信号值:-87
2025-07-31 23:23:53:840 ==>> WiFi信号:【CC057790A5C0】,信号值:-82
2025-07-31 23:23:53:866 ==>> WiFi信号:【CC057790A4A1】,信号值:-85
2025-07-31 23:23:53:875 ==>> WiFi数量【6】, 最大信号值:-61
2025-07-31 23:23:53:897 ==>> 检测【检测GPS结果】
2025-07-31 23:23:53:907 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:23:53:927 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all continue location stop
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all sing location stop


2025-07-31 23:23:54:648 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:23:54:753 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:23:54:763 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:54:788 ==>> 定位已等待【1】秒.
2025-07-31 23:23:55:124 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:54][COMM]Open GPS Module...
[D][05:18:54][COMM]LOC_MODEL_CONT
[D][05:18:54][GNSS]start event:8
[D][05:18:54][GNSS]GPS start. ret=0
[W][05:18:54][GNSS]start cont locating
[D][05:18:54][CAT1]gsm read msg sub id: 23
[D][05:18:54][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:54][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 23:23:55:763 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:55:773 ==>> 定位已等待【2】秒.
2025-07-31 23:23:55:838 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:23:56:485 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:23:56:712 ==>> [D][05:18:56][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,60,,,42,59,,,41,16,,,41,39,,,41,1*71

$GBGSV,3,2,10,40,,,41,23,,,39,41,,,39,25,,,37,1*70

$GBGSV,3,3,10,34,,,35,10,,,36,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1639.865,1639.865,52.417,2097152,2097152,2097152*4A

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 23, ret: 6
[D][05:18:56][CAT1]sub id: 23, ret: 6

[D][05:18:56][COMM]read battery soc:255


2025-07-31 23:23:56:772 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:56:781 ==>> 定位已等待【3】秒.
2025-07-31 23:23:57:094 ==>> [D][05:18:56][GNSS]recv submsg id[1]
[D][05:18:56][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:23:57:680 ==>> [D][05:18:57][PROT]CLEAN,SEND:0
[D][05:18:57][PROT]index:0 1629955137
[D][05:18:57][PROT]is_send:0
[D][05:18:57][PROT]sequence_num:4
[D][05:18:57][PROT]retry_timeout:0
[D][05:18:57][PROT]retry_times:1
[D][05:18:57][PROT]send_path:0x2
[D][05:18:57][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:57][PROT]===========================================================
[W][05:18:57][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955137]
[D][05:18:57][PROT]===========================================================
[D][05:18:57][PROT]sending traceid [9999999999900005]
[D][05:18:57][PROT]Send_TO_M2M [1629955137]
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:57][SAL ]sock send credit cnt[6]
[D][05:18:57][SAL ]sock send ind credit cnt[6]
[D][05:18:57][M2M ]m2m send data len[198]
[D][05:18:57][SAL ]Cellular task submsg id[10]
[D][05:18:57][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:57][CAT1]Send Data To Server[198][

2025-07-31 23:23:57:785 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:57:795 ==>> 定位已等待【4】秒.
2025-07-31 23:23:57:818 ==>> 198] ... ->:
0063B98E113311331133113311331B88B538131A7F7EE77365CDD1E575F9889DDA80A9C4FCDDB48575EB94F0E979A323BE95C72201E37A3BBCF106B934D646F22E2C185AF1940631B8D71CFDD8272C76BC2F96A720BA43EE9E75D3E8435C8719F93DCF
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,40,,,42,60,,,41,59,,,41,39,,,41,1*7C

$GBGSV,5,2,18,3,,,41,16,,,40,11,,,40,23,,,39,1*42

$GBGSV,5,3,18,25,,,39,41,,,38,10,,,38,34,,,38,1*7C

$GBGSV,5,4,18,6,,,37,7,,,37,43,,,36,2,,,34,1*48

$GBGSV,5,5,18,4,,,33,1,,,37,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1597.341,1597.341,51.076,2097152,2097152,2097152*4A



2025-07-31 23:23:58:714 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,59,,,41,39,,,41,60,,,40,1*77

$GBGSV,6,2,22,3,,,40,11,,,40,25,,,40,16,,,39,1*4F

$GBGSV,6,3,22,23,,,39,34,,,39,1,,,39,41,,,38,1*40

$GBGSV,6,4,22,7,,,38,43,,,38,10,,,37,6,,,37,1*73

$GBGSV,6,5,22,2,,,34,4,,,33,32,,,32,5,,,30,1*42

$GBGSV,6,6,22,24,,,36,22,,,36,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1569.208,1569.208,50.210,2097152,2097152,2097152*49

[D][05:18:58][COMM]read battery soc:255


2025-07-31 23:23:58:789 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:58:798 ==>> 定位已等待【5】秒.
2025-07-31 23:23:59:686 ==>> $GBGGA,152403.526,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,42,59,,,41,39,,,41,25,,,41,1*77

$GBGSV,6,2,22,34,,,41,60,,,40,3,,,40,11,,,40,1*41

$GBGSV,6,3,22,16,,,39,23,,,39,1,,,39,7,,,39,1*73

$GBGSV,6,4,22,43,,,39,41,,,38,10,,,37,6,,,37,1*40

$GBGSV,6,5,22,24,,,34,2,,,34,32,,,34,4,,,33,1*73

$GBGSV,6,6,22,5,,,31,12,,,36,1*47

$GBRMC,152403.526,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152403.526,0.000,1577.395,1577.395,50.466,2097152,2097152,2097152*50



2025-07-31 23:23:59:791 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:23:59:800 ==>> 定位已等待【6】秒.
2025-07-31 23:24:00:708 ==>> $GBGGA,152404.506,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,43,39,,,41,25,,,41,34,,,41,1*7C

$GBGSV,6,2,23,59,,,40,60,,,40,3,,,40,11,,,40,1*4A

$GBGSV,6,3,23,23,,,40,7,,,40,43,,,40,16,,,39,1*4A

$GBGSV,6,4,23,41,,,39,1,,,38,10,,,37,6,,,37,1*77

$GBGSV,6,5,23,32,,,35,12,,,34,24,,,34,2,,,34,1*43

$GBGSV,6,6,23,9,,,34,4,,,33,5,,,32,1*49

$GBRMC,152404.506,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152404.506,0.000,1571.819,1571.819,50.291,2097152,2097152,2097152*5B

[D][05:19:00][COMM]read battery soc:255


2025-07-31 23:24:00:798 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:00:808 ==>> 定位已等待【7】秒.
2025-07-31 23:24:01:643 ==>> $GBGGA,152405.506,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,43,39,,,41,25,,,41,34,,,41,1*7C

$GBGSV,6,2,23,59,,,41,60,,,40,3,,,40,11,,,40,1*4B

$GBGSV,6,3,23,23,,,40,7,,,40,43,,,40,16,,,39,1*4A

$GBGSV,6,4,23,41,,,39,1,,,38,10,,,38,6,,,37,1*78

$GBGSV,6,5,23,32,,,36,12,,,35,2,,,35,9,,,35,1*7E

$GBGSV,6,6,23,24,,,34,4,,,33,5,,,33,1*77

$GBRMC,152405.506,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152405.506,0.000,1584.423,1584.423,50.680,2097152,2097152,2097152*5E



2025-07-31 23:24:01:810 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:01:820 ==>> 定位已等待【8】秒.
2025-07-31 23:24:02:814 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:02:830 ==>> 定位已等待【9】秒.
2025-07-31 23:24:02:874 ==>> $GBGGA,152406.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,41,25,,,41,34,,,41,1*7B

$GBGSV,6,2,24,43,,,41,59,,,40,60,,,40,3,,,40,1*4B

$GBGSV,6,3,24,11,,,40,23,,,40,7,,,40,16,,,39,1*4A

$GBGSV,6,4,24,41,,,39,1,,,38,10,,,38,6,,,37,1*7F

$GBGSV,6,5,24,32,,,36,12,,,35,2,,,35,9,,,35,1*79

$GBGSV,6,6,24,24,,,34,4,,,33,5,,,33,33,,,36,1*75

$GBRMC,152406.506,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152406.506,0.000,1584.423,1584.423,50.680,2097152,2097152,2097152*5D

[D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02]

2025-07-31 23:24:02:979 ==>> [M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE50205387C14D82741066F98DB0488D6B6885C3B5C2483172A622F362092CF80CA08AAA95D274706565C0F37578A007D09D10
[D][05:19:02][COMM]read battery soc:255
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]


2025-07-31 23:24:03:686 ==>> $GBGGA,152407.506,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,43,39,,,41,25,,,41,34,,,41,1*7B

$GBGSV,6,2,24,43,,,40,59,,,40,60,,,40,3,,,40,1*4A

$GBGSV,6,3,24,11,,,40,23,,,40,7,,,40,16,,,39,1*4A

$GBGSV,6,4,24,41,,,39,1,,,38,10,,,38,6,,,37,1*7F

$GBGSV,6,5,24,32,,,36,12,,,35,9,,,35,2,,,34,1*78

$GBGSV,6,6,24,24,,,33,4,,,33,5,,,33,33,,,32,1*76

$GBRMC,152407.506,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152407.506,0.000,782.797,782.797,715.885,2097152,2097152,2097152*61



2025-07-31 23:24:03:822 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:03:834 ==>> 定位已等待【10】秒.
2025-07-31 23:24:04:709 ==>> $GBGGA,152408.506,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,10,,,38,41,,,38,6,,,37,1,,,37,1*71

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,5,,,33,4,,,33,33,,,33,1*70

$GBRMC,152408.506,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152408.506,0.000,782.781,782.781,715.870,2097152,2097152,2097152*64

[D][05:19:04][COMM]read battery soc:255


2025-07-31 23:24:04:830 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:04:840 ==>> 定位已等待【11】秒.
2025-07-31 23:24:05:697 ==>> $GBGGA,152409.506,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,6,2,24,25,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,10,,,38,41,,,38,6,,,37,1,,,37,1*71

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,4,,,33,33,,,33,5,,,32,1*71

$GBRMC,152409.506,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152409.506,0.000,782.787,782.787,715.875,2097152,2097152,2097152*60



2025-07-31 23:24:05:832 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:05:842 ==>> 定位已等待【12】秒.
2025-07-31 23:24:06:729 ==>> $GBGGA,152410.506,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,38,10,,,37,6,,,37,1,,,37,1*7E

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,4,,,33,33,,,33,5,,,32,1*71

$GBRMC,152410.506,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152410.506,0.000,781.061,781.061,714.297,2097152,2097152,2097152*6F

[D][05:19:06][COMM]read battery soc:255


2025-07-31 23:24:06:834 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:06:844 ==>> 定位已等待【13】秒.
2025-07-31 23:24:07:685 ==>> $GBGGA,152411.506,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,38,10,,,37,6,,,37,1,,,37,1*7E

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,5,,,33,4,,,33,33,,,33,1*70

$GBRMC,152411.506,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152411.506,0.000,781.919,781.919,715.082,2097152,2097152,2097152*69



2025-07-31 23:24:07:837 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:07:848 ==>> 定位已等待【14】秒.
2025-07-31 23:24:08:021 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:07][M2M

2025-07-31 23:24:08:051 ==>>  ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 23:24:08:682 ==>> $GBGGA,152412.506,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,6,2,24,25,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,10,,,38,1,,,38,41,,,38,6,,,37,1*7E

$GBGSV,6,5,24,32,,,36,2,,,35,9,,,35,12,,,35,1*79

$GBGSV,6,6,24,24,,,34,33,,,34,5,,,33,4,,,33,1*77

$GBRMC,152412.506,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152412.506,0.000,784.505,784.505,717.446,2097152,2097152,2097152*64

                                         

2025-07-31 23:24:08:849 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:08:857 ==>> 定位已等待【15】秒.
2025-07-31 23:24:09:668 ==>> $GBGGA,152413.506,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,6,2,24,25,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,39,10,,,38,6,,,37,1,,,37,1*70

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,33,,,34,5,,,33,4,,,33,1*77

$GBRMC,152413.506,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152413.506,0.000,785.366,785.366,718.233,2097152,2097152,2097152*6E



2025-07-31 23:24:09:850 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:09:861 ==>> 定位已等待【16】秒.
2025-07-31 23:24:10:684 ==>> $GBGGA,152414.506,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,6,2,24,25,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,39,10,,,38,1,,,38,6,,,37,1*7F

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,33,,,34,5,,,33,4,,,33,1*77

$GBRMC,152414.506,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152414.506,0.000,786.228,786.228,719.021,2097152,2097152,2097152*69

                                         

2025-07-31 23:24:10:853 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:10:863 ==>> 定位已等待【17】秒.
2025-07-31 23:24:11:670 ==>> $GBGGA,152415.506,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,11,,,40,43,,,40,23,,,39,10,,,38,1*73

$GBGSV,6,4,24,16,,,38,41,,,38,6,,,37,1,,,37,1*77

$GBGSV,6,5,24,9,,,36,32,,,36,12,,,35,2,,,34,1*7B

$GBGSV,6,6,24,24,,,34,33,,,34,5,,,33,4,,,33,1*77

$GBRMC,152415.506,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152415.506,0.000,781.918,781.918,715.080,2097152,2097152,2097152*6F



2025-07-31 23:24:11:853 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:11:865 ==>> 定位已等待【18】秒.
2025-07-31 23:24:12:673 ==>> $GBGGA,152416.506,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,34,,,41,25,,,41,1*7A

$GBGSV,6,2,24,7,,,40,60,,,40,3,,,40,59,,,40,1*7A

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,39,10,,,38,6,,,37,1,,,37,1*70

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,4,,,34,33,,,34,5,,,33,1*70

$GBRMC,152416.506,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152416.506,0.000,785.361,785.361,718.228,2097152,2097152,2097152*61



2025-07-31 23:24:12:703 ==>>                                          

2025-07-31 23:24:12:857 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:12:881 ==>> 定位已等待【19】秒.
2025-07-31 23:24:13:671 ==>> $GBGGA,152417.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,42,39,,,41,59,,,41,34,,,41,1*71

$GBGSV,6,2,24,25,,,41,7,,,40,60,,,40,3,,,40,1*70

$GBGSV,6,3,24,11,,,40,43,,,40,16,,,39,23,,,39,1*74

$GBGSV,6,4,24,41,,,39,10,,,38,6,,,37,1,,,37,1*70

$GBGSV,6,5,24,9,,,36,32,,,36,2,,,35,12,,,35,1*7A

$GBGSV,6,6,24,24,,,34,33,,,34,5,,,33,4,,,33,1*77

$GBRMC,152417.506,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,152417.506,0.000,785.366,785.366,718.233,2097152,2097152,2097152*6A



2025-07-31 23:24:13:870 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:13:881 ==>> 定位已等待【20】秒.
2025-07-31 23:24:14:755 ==>> $GBGGA,152414.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,41,11,52,113,40,1*7F

$GBGSV,6,3,24,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,38,1*40

$GBGSV,6,4,24,2,45,236,35,60,41,239,40,4,32,111,34,23,32,315,39,1*71

$GBGSV,6,5,24,41,32,241,39,24,22,278,34,5,21,255,33,33,5,322,34,1*73

$GBGSV,6,6,24,34,,,41,43,,,40,32,,,36,12,,,35,1*70

$GBRMC,152414.511,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,N*23

$GBGST,152414.511,0.918,0.192,0.177,0.294,3.732,4.851,15*54

[D][05:19:14][COMM]read battery soc:255


2025-07-31 23:24:14:876 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:14:908 ==>> 定位已等待【21】秒.
2025-07-31 23:24:15:740 ==>> $GBGGA,152415.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,6,3,24,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,38,1*40

$GBGSV,6,4,24,2,45,236,35,60,41,239,41,4,32,111,34,23,32,315,39,1*70

$GBGSV,6,5,24,41,32,241,39,24,22,278,34,5,21,255,33,33,5,322,34,1*73

$GBGSV,6,6,24,34,,,41,43,,,40,32,,,36,12,,,35,1*70

$GBGSV,2,1,05,40,76,191,41,39,66,51,41,25,51,23,40,23,32,315,38,5*7C

$GBGSV,2,2,05,41,32,241,38,5*4F

$GBRMC,152415.511,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,152415.511,2.232,0.260,0.235,0.399,2.743,3.441,8.885*79



2025-07-31 23:24:15:890 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:15:903 ==>> 定位已等待【22】秒.
2025-07-31 23:24:16:796 ==>> $GBGGA,152416.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,6,3,24,10,52,241,37,9,52,333,36,25,51,23,41,1,48,125,38,1*4F

$GBGSV,6,4,24,2,45,236,35,60,41,239,40,4,32,111,34,23,32,315,39,1*71

$GBGSV,6,5,24,41,32,241,39,24,22,278,34,5,21,255,33,33,5,322,34,1*73

$GBGSV,6,6,24,34,,,41,43,,,40,12,,,35,32,,,35,1*73

$GBGSV,2,1,05,40,76,191,41,39,66,51,42,25,51,23,40,23,32,315,38,5*7F

$GBGSV,2,2,05,41,32,241,38,5*4F

$GBRMC,152416.511,V,,,,,,,310725,1.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,N*20

$GBGST,152416.511,2.165,0.265,0.241,0.406,2.311,2.827,6.985*7D

[D][05:19:16][COMM]read battery soc:255


2025-07-31 23:24:16:901 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:16:913 ==>> 定位已等待【23】秒.
2025-07-31 23:24:17:751 ==>> $GBGGA,152417.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,6,3,24,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,37,1*4F

$GBGSV,6,4,24,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,6,5,24,41,32,241,39,24,22,278,34,5,21,255,33,33,5,322,34,1*73

$GBGSV,6,6,24,34,,,41,43,,,40,12,,,35,32,,,35,1*73

$GBGSV,2,1,05,40,76,191,41,39,66,51,41,25,51,23,41,23,32,315,38,5*7D

$GBGSV,2,2,05,41,32,241,38,5*4F

$GBRMC,152417.511,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,152417.511,1.901,0.227,0.208,0.347,1.965,2.376,5.885*7B



2025-07-31 23:24:17:902 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:17:914 ==>> 定位已等待【24】秒.
2025-07-31 23:24:18:051 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:24:18:401 ==>> [D][05:19:18][CAT1]<<< 
OK

[D][05:19:18][CAT1]exec over: func id: 10, ret: 6
[D][05:19:18][CAT1]sub id: 10, ret: 6

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:18][M2M ]m2m gsm shut done, ret[0]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:18][SAL ]open socket ind id[4], rst[0]
[D][05:19:18][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:18][SAL ]Cellular task submsg id[8]
[D][05:19:18][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:18][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:18][CAT1]gsm read msg sub id: 8
[D][05:19:18][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:18][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:18][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:24:18:909 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:18:920 ==>> 定位已等待【25】秒.
2025-07-31 23:24:19:354 ==>> [D][05:19:18][CAT1]pdpdeact urc len[22]
$GBGGA,152418.511,2301.2585615,N,11421.9436948,E,1,07,1.63,83.760,M,-1.770,M,,*57

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.63,3.51,4*0D

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,6,3,24,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,37,1*4F

$GBGSV,6,4,24,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,6,5,24,41,32,241,39,24,22,278,34,5,21,255,33,33,5,322,34,1*73

$GBGSV,6,6,24,34,,,41,43,,,40,12,,,35,32,,,35,1*73

$GBGSV,2,1,05,40,76,191,41,39,66,51,41,25,51,23,41,23,32,315,38,5*7D

$GBGSV,2,2,05,41,32,241,38,5*4F

$GBRMC,152418.511,A,2301.2585615,N,11421.9436948,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:19:18][GNSS]HD8040 GPS
[D][05:19:18][GNSS]GPS diff_sec 124020300, report 0x42 frame
$GBGST,152418.511,1.817,0.182,0.169,0.279,1.790,2.134,5.200*71

[D][05:19:18][COMM]Main Task receive event:131
[D][05:19:18][COMM]index:0,power_mode:0xFF
[D][05:19:18][COMM]index:1,sound_mode:0xFF
[D][05:19:18][COMM]index:2,gsensor_mode:0xFF
[D][05:19:18][COMM]index:3,report_freq_mode:0xFF
[D][05:19:18][COMM]index:4,report_period:0xFF
[D][05:19:18][COMM]index:5,normal_reset_mode:0

2025-07-31 23:24:19:459 ==>> xFF
[D][05:19:18][COMM]index:6,normal_reset_period:0xFF
[D][05:19:18][COMM]index:7,spock_over_speed:0xFF
[D][05:19:18][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:18][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:18][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:18][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:18][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:18][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:18][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:18][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:18][COMM]index:16,imu_config_params:0xFF
[D][05:19:18][COMM]index:17,long_connect_params:0xFF
[D][05:19:18][COMM]index:18,detain_mark:0xFF
[D][05:19:18][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:18][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:18][COMM]index:21,mc_mode:0xFF
[D][05:19:18][COMM]index:22,S_mode:0xFF
[D][05:19:18][COMM]index:23,overweight:0xFF
[D][05:19:18][COMM]index:24,standstill_mode:0xFF
[D][05:19:18][COMM]index:25,night_mode:0xFF
[D][05:19:18][COMM]index:26,experiment1:0xFF
[D][05:19:18][COMM]index:27,experiment2:0xFF
[D][05:19:18][COMM]index:28,experiment3:0xFF
[D][05:19:18][COMM]index:29,experimen

2025-07-31 23:24:19:564 ==>> t4:0xFF
[D][05:19:18][COMM]index:30,night_mode_start:0xFF
[D][05:19:18][COMM]index:31,night_mode_end:0xFF
[D][05:19:18][COMM]index:33,park_report_minutes:0xFF
[D][05:19:18][COMM]index:34,park_report_mode:0xFF
[D][05:19:18][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:18][COMM]index:38,charge_battery_para: FF
[D][05:19:18][COMM]index:39,multirider_mode:0xFF
[D][05:19:18][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:18][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:18][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:18][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:18][COMM]index:44,riding_duration_config:0xFF
[D][05:19:18][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:18][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:18][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:18][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:18][COMM]index:49,mc_load_startup:0xFF
[D][05:19:18][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:18][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:18][COMM]index:52,traffic_mode:0xFF
[D][05:19:18][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:18][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:18][COMM]in

2025-07-31 23:24:19:670 ==>> dex:55,wheel_alarm_play_switch:255
[D][05:19:18][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:18][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:18][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:18][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:18][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:18][COMM]index:63,experiment5:0xFF
[D][05:19:18][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:18][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:18][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:18][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:18][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:18][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:18][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:18][COMM]index:72,experiment6:0xFF
[D][05:19:18][COMM]index:73,experiment7:0xFF
[D][05:19:18][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:18][COMM]index:75,zero_value_from_server:-1
[D][05:19:18][COMM]index:76,multirider_threshold:255
[D][05:19:18][COMM]index:77,experiment8:255
[D][05:19:18][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:18][COMM]index:79,temp_park_tail_light_twinkle_duration:2

2025-07-31 23:24:19:774 ==>> 55
[D][05:19:18][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:18][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:18][COMM]index:83,loc_report_interval:255
[D][05:19:18][COMM]index:84,multirider_threshold_p2:255
[D][05:19:18][COMM]index:85,multirider_strategy:255
[D][05:19:18][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:18][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:18][COMM]index:90,weight_param:0xFF
[D][05:19:18][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:18][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:18][COMM]index:95,current_limit:0xFF
[D][05:19:18][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:18][COMM]index:100,location_mode:0xFF

[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:18][COMM]Main Task receive event:131 finished processing
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][COMM]read battery soc:255
$GBGGA,152419.011,2301.258

2025-07-31 23:24:19:879 ==>> 4755,N,11421.9437134,E,1,07,1.64,83.722,M,-1.770,M,,*54

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,6,1,24,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,6,2,24,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,6,3,24,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,37,1*4F

$GBGSV,6,4,24,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,6,5,24,41,32,240,39,24,22,278,34,5,21,255,33,33,5,322,34,1*72

$GBGSV,6,6,24,34,,,41,43,,,40,12,,,35,32,,,35,1*73

$GBGSV,2,1,05,40,76,191,41,39,66,51,41,25,51,23,41,23,32,315,38,5*7D

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152419.011,A,2301.2584755,N,11421.9437134,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152419.011,1.764,0.232,0.212,0.355,1.684,1.974,4.741*7C

                                                                                                                                                                                                                                    

2025-07-31 23:24:19:926 ==>>                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:24:19:938 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:19:962 ==>> 定位已等待【26】秒.
2025-07-31 23:24:20:445 ==>> [D][05:19:19][CAT1]opened : 0, 0
[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:19][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:20][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:20][M2M ]g_m2m_is_idle become true
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
$GBGGA,152420.000,2301.2583507,N,11421.9437395,E,1,07,1.64,83.787,M,-1.770,M,,*5A

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,38,1*40

$GBGSV,7,4,25,2,45,236,36,60,41,239,40,4,32,111,33,23,32,315,39,1*75

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,173,32,1*45

[D][05:19:20][PROT]index:2 1629955160
[D][05:19:20][PROT]is_send:0
[D][05:19:20][PROT]sequence_num:6
[D][05:19:20][PROT]retry_timeout:0
[D][05:19:20][PROT]retry_times:1
[D][05:19:20][PROT]send_path:0x2
[D][05:19:20][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:20][PROT]====================================================

2025-07-31 23:24:20:550 ==>> =======
[D][05:19:20][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:19:20][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955160]
[D][05:19:20][PROT]===========================================================
[D][05:19:20][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:20][PROT]sending traceid [9999999999900007]
[D][05:19:20][PROT]Send_TO_M2M [1629955160]
$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,76,191,41,39,66,51,41,25,51,23,41,23,32,315,38,5*7D

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152420.000,A,2301.2583507,N,11421.9437395,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152420.000,1.843,0.223,0.206,0.338,1.683,1.928,4.441*7F

[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:20][SAL ]sock send credit cnt[6]
[D][05:19:20][SAL ]sock send ind credit cnt[6]
[D][05:19:20][M2M ]m2m send data len[134]
[D][05:19:20][SAL ]Cellular task submsg id[10]
[D][05:19:20][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052dd8] format[0]
[D][05:19:20][CAT1]gsm read msg sub id: 15
[D][05:19:20][CAT1]t

2025-07-31 23:24:20:640 ==>> x ret[17] >>> AT+QISEND=0,134

[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:20][CAT1]Send Data To Server[134][134] ... ->:
0043B686113311331133113311331B88BE982F56F0E0B38F1D82FA7F03810716A220FFB0168D44A29E79FE3B5372BBA0E9E031E8FF8B11EFD12551DE31A2616F370CF6
[D][05:19:20][CAT1]<<< 
SEND OK

[D][05:19:20][CAT1]exec over: func id: 15, ret: 11
[D][05:19:20][CAT1]sub id: 15, ret: 11

[D][05:19:20][SAL ]Cellular task submsg id[68]
[D][05:19:20][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:20][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:20][M2M ]g_m2m_is_idle become true
[D][05:19:20][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:20][PROT]M2M Send ok [1629955160]


2025-07-31 23:24:20:745 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 23:24:20:930 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 23:24:20:941 ==>> 定位已等待【27】秒.
2025-07-31 23:24:21:281 ==>> $GBGGA,152421.000,2301.2583516,N,11421.9436960,E,1,07,1.64,83.730,M,-1.770,M,,*56

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,48,125,38,1*40

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,173,32,1*45

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,76,191,41,39,66,51,42,25,51,23,41,23,32,315,39,5*7F

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152421.000,A,2301.2583516,N,11421.9436960,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152421.000,1.871,0.284,0.258,0.427,1.661,1.871,4.202*70



2025-07-31 23:24:21:939 ==>> 符合定位需求的卫星数量:【20】
2025-07-31 23:24:21:946 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【39】,信号值:【42】
北斗星号:【6】,信号值:【37】
北斗星号:【7】,信号值:【40】
北斗星号:【16】,信号值:【39】
北斗星号:【3】,信号值:【40】
北斗星号:【59】,信号值:【40】
北斗星号:【11】,信号值:【40】
北斗星号:【10】,信号值:【38】
北斗星号:【9】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【35】
北斗星号:【60】,信号值:【40】
北斗星号:【23】,信号值:【39】
北斗星号:【41】,信号值:【38】
北斗星号:【34】,信号值:【41】
北斗星号:【43】,信号值:【40】
北斗星号:【12】,信号值:【35】
北斗星号:【32】,信号值:【35】

2025-07-31 23:24:21:953 ==>> 检测【CSQ强度】
2025-07-31 23:24:21:972 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:24:22:295 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:21][CAT1]gsm read msg sub id: 12
[D][05:19:21][CAT1]SEND RAW data >>> AT+CSQ

$GBGGA,152422.000,2301.2583646,N,11421.9436311,E,1,07,1.64,84.099,M,-1.770,M,,*5C

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,76,191,43,39,66,51,42,6,65,103,37,7,64,248,40,1*48

$GBGSV,7,2,25,16,64,18,39,3,60,190,41,59,52,129,40,11,52,113,41,1*7E

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,40,1*78

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,173,32,1*45

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,41,12,,,35,1*74

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,76,191,41,39,66,51,42,25,51,23,41,23,32,315,39,5*7F

$GBGSV,2,2,05,41,32,240,39,5*4F

$GBRMC,152422.000,A,2301.2583646,N,11421.9436311,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152422.000,1.853,0.206,0.193,0.306,1.612,1.801,3.970*73



2025-07-31 23:24:22:741 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 23:24:23:276 ==>> $GBGGA,152423.000,2301.2583659,N,11421.9436203,E,1,07,1.64,84.168,M,-1.770,M,,*5E

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,7,2,25,16,64,18,39,3,60,190,41,59,52,129,40,11,52,113,40,1*7F

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,172,32,1*44

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,76,191,41,39,66,51,42,25,51,23,41,23,32,315,39,5*7F

$GBGSV,2,2,05,41,32,240,39,5*4F

$GBRMC,152423.000,A,2301.2583659,N,11421.9436203,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152423.000,1.766,0.194,0.181,0.292,1.524,1.700,3.753*7B



2025-07-31 23:24:24:027 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:24:24:287 ==>> $GBGGA,152424.000,2301.2583277,N,11421.9435893,E,1,07,1.64,84.019,M,-1.770,M,,*56

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,76,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4A

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,59,52,129,40,11,52,113,40,1*7E

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,172,32,1*44

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,76,191,41,39,66,51,42,25,51,23,41,23,32,315,39,5*7F

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152424.000,A,2301.2583277,N,11421.9435893,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152424.000,1.796,0.262,0.240,0.394,1.523,1.681,3.620*7A

[W][05:19:24][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 23:24:24:746 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 23:24:25:280 ==>> [D][05:19:24][CAT1]SEND RAW data timeout
[D][05:19:24][CAT1]exec over: func id: 12, ret: -52
[D][05:19:25][CAT1]gsm read msg sub id: 12
[D][05:19:25][CAT1]SEND RAW data >>> AT+CSQ

$GBGGA,152425.000,2301.2582896,N,11421.9435749,E,1,07,1.64,83.934,M,-1.770,M,,*5A

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,77,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4B

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,59,52,129,41,11,52,113,40,1*7F

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,35,5,21,255,33,44,13,172,32,1*45

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,77,191,41,39,66,51,42,25,51,23,41,23,32,315,39,5*7E

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152425.000,A,2301.2582896,N,11421.9435749,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:19:25][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:25][CAT1]exec over: func id: 12, ret: 21
$GBGST,152425.000,1.957,0.236,0.217,0.355,1.614,1.755

2025-07-31 23:24:25:310 ==>> ,3.574*7B



2025-07-31 23:24:25:417 ==>> 【CSQ强度】通过,【24】符合目标值【18】至【31】要求!
2025-07-31 23:24:25:426 ==>> 检测【关闭GSM联网】
2025-07-31 23:24:25:451 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:24:25:551 ==>>                                                              [D][05:19:25][PROT]index:0 1629955165
[D][05:19:25][PROT]is_send:0
[D][05:19:25][PROT]sequence_num:7
[D][05:19:25][PROT]retry_timeout:0
[D][05:19:25][PROT]retry_times:1
[D][05:19:25][PROT]send_path:0x2
[D][05:19:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:25][PROT]===========================================================
[W][05:19:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955165]
[D][05:19:25][PROT]===========================================================
[D][05:19:25][PROT]sending traceid [9999999999900008]
[D][05:19:25][PROT]Send_TO_M2M [1629955165]
[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:25][SAL ]sock send credit cnt[6]
[D][05:19:25][SAL ]sock send ind credit cnt[6]
[D][05:19:25][M2M ]m2m send data len[294]
[D][05:19:25][SAL ]Cellular task submsg id[10]
[D][05:19:25][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052dd8] format[0]
[D][05:19:25][CAT1]gsm read msg sub id: 15
[D][05:19:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:25][CAT1]<<< 
ERROR



2025-07-31 23:24:25:656 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:25][COMM]GSM test
[D][05:19:25][COMM]GSM test disable


2025-07-31 23:24:25:702 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:24:25:709 ==>> 检测【4G联网测试】
2025-07-31 23:24:25:722 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:24:25:883 ==>> [W][05:19:25][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 23:24:26:552 ==>> [D][05:19:25][COMM]Main Task receive event:14
[D][05:19:25][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955165, allstateRepSeconds = 0
[D][05:19:25][COMM]index:0,power_mode:0xFF
[D][05:19:25][COMM]index:1,sound_mode:0xFF
[D][05:19:25][COMM]index:2,gsensor_mode:0xFF
[D][05:19:25][COMM]index:3,report_freq_mode:0xFF
[D][05:19:25][COMM]index:4,report_period:0xFF
[D][05:19:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:25][COMM]index:6,normal_reset_period:0xFF
[D][05:19:25][COMM]index:7,spock_over_speed:0xFF
[D][05:19:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:25][COMM]index:16,imu_config_params:0xFF
[D][05:19:25][COMM]index:17,long_connect_params:0xFF
[D][05:19:25][COMM]index:18,detain_mark:0xFF
[D][05:19:25][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:25][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:25][COMM]index:21,mc_mode:0xFF
[D][0

2025-07-31 23:24:26:657 ==>> 5:19:25][COMM]index:22,S_mode:0xFF
[D][05:19:25][COMM]index:23,overweight:0xFF
[D][05:19:25][COMM]index:24,standstill_mode:0xFF
[D][05:19:25][COMM]index:25,night_mode:0xFF
[D][05:19:25][COMM]index:26,experiment1:0xFF
[D][05:19:25][COMM]index:27,experiment2:0xFF
[D][05:19:25][COMM]index:28,experiment3:0xFF
[D][05:19:25][COMM]index:29,experiment4:0xFF
[D][05:19:25][COMM]index:30,night_mode_start:0xFF
[D][05:19:25][COMM]index:31,night_mode_end:0xFF
[D][05:19:25][COMM]index:33,park_report_minutes:0xFF
[D][05:19:25][COMM]index:34,park_report_mode:0xFF
[D][05:19:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:25][COMM]index:38,charge_battery_para: FF
[D][05:19:25][COMM]index:39,multirider_mode:0xFF
[D][05:19:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:25][COMM]index:44,riding_duration_config:0xFF
[D][05:19:25][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:25][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:25][COMM]index:48,shlmt_s

2025-07-31 23:24:26:763 ==>> ensor_en:0xFF
[D][05:19:25][COMM]index:49,mc_load_startup:0xFF
[D][05:19:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:25][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:25][COMM]index:52,traffic_mode:0xFF
[D][05:19:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:25][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:25][COMM]index:63,experiment5:0xFF
[D][05:19:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:25][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:25][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:25][COMM]index:72,experiment6:0xFF
[D][05:19:25][COMM]index:73,

2025-07-31 23:24:26:867 ==>> experiment7:0xFF
[D][05:19:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:25][COMM]index:75,zero_value_from_server:-1
[D][05:19:25][COMM]index:76,multirider_threshold:255
[D][05:19:25][COMM]index:77,experiment8:255
[D][05:19:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:25][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:25][COMM]index:83,loc_report_interval:255
[D][05:19:25][COMM]index:84,multirider_threshold_p2:255
[D][05:19:25][COMM]index:85,multirider_strategy:255
[D][05:19:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:25][COMM]index:90,weight_param:0xFF
[D][05:19:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:25][COMM]index:95,current_limit:0xFF
[D][05:19:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:25][COMM]index:100,location_mode:0xFF

[W][05:19:25][PROT]remove success[1629955165],send_path[2],type[0000],priority[0],index[0

2025-07-31 23:24:26:972 ==>> ],used[0]
[W][05:19:25][PROT]add success [1629955165],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:25][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:25][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,152426.000,2301.2582627,N,11421.9435673,E,1,07,1.64,83.884,M,-1.770,M,,*5F

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,77,191,42,39,66,51,42,6,65,103,37,7,64,248,40,1*48

$GBGSV,7,2,25,16,64,18,39,3,60,190,41,59,52,129,41,11,52,113,40,1*7E

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,34,5,21,255,33,44,13,172,32,1*44

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,77,191,41,39,66,51,42,25,51,23,41,23,32,315,38,5*7F

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152426.000,A,2301.2582627,N,11421.9435673,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,152426.000,1.795,0.273,0.250,0.409,1.489,1.624,3.393*7A



2025-07-31 23:24:27:002 ==>>                                          

2025-07-31 23:24:27:272 ==>> $GBGGA,152427.000,2301.2582360,N,11421.9435486,E,1,07,1.64,83.870,M,-1.770,M,,*5B

$GBGSA,A,3,40,39,16,11,25,23,41,,,,,,3.87,1.64,3.51,4*0A

$GBGSV,7,1,25,40,77,191,42,39,66,51,41,6,65,103,37,7,64,248,40,1*4B

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,59,52,129,41,11,52,113,40,1*7F

$GBGSV,7,3,25,10,52,241,38,9,52,333,36,25,51,23,41,1,45,126,38,1*4E

$GBGSV,7,4,25,2,45,236,35,60,41,239,40,4,32,111,33,23,32,315,39,1*76

$GBGSV,7,5,25,41,32,240,39,24,22,278,35,5,21,255,33,44,13,172,32,1*45

$GBGSV,7,6,25,33,5,322,34,34,,,41,43,,,40,12,,,35,1*75

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,77,191,41,39,66,51,41,25,51,23,41,23,32,315,39,5*7D

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152427.000,A,2301.2582360,N,11421.9435486,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152427.000,1.710,0.207,0.192,0.313,1.416,1.544,3.258*71



2025-07-31 23:24:28:023 ==>> [D][05:19:27][M2M ]get csq[-1]


2025-07-31 23:24:28:281 ==>> $GBGGA,152428.000,2301.2582085,N,11421.9434785,E,1,10,1.41,84.007,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,11,25,23,41,,,3.14,1.41,2.81,4*0B

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,51,41,6,64,14,37,1*7C

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,10,57,222,38,59,52,129,41,1*75

$GBGSV,7,3,25,11,52,113,40,9,52,333,36,25,51,23,41,34,46,74,41,1*49

$GBGSV,7,4,25,1,45,126,37,2,45,236,35,60,41,239,40,4,32,111,33,1*4A

$GBGSV,7,5,25,23,32,315,39,41,32,240,39,24,22,278,35,5,21,255,33,1*4F

$GBGSV,7,6,25,44,13,172,32,33,5,322,34,43,,,40,12,,,35,1*40

$GBGSV,7,7,25,32,,,35,1*76

$GBGSV,2,1,05,40,77,191,41,39,66,51,41,25,51,23,41,23,32,315,39,5*7D

$GBGSV,2,2,05,41,32,240,38,5*4E

$GBRMC,152428.000,A,2301.2582085,N,11421.9434785,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152428.000,1.727,0.238,0.222,0.354,1.415,1.532,3.169*7E



2025-07-31 23:24:28:356 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 23:24:28:781 ==>> [D][05:19:28][COMM]read battery soc:255


2025-07-31 23:24:29:545 ==>> [D][05:19:28][COMM]msg 0226 loss. last_tick:0. cur_tick:100011. period:10000
[D][05:19:29][COMM]msg 0227 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0228 loss. last_tick:0. cur_tick:100012. period:10000
[D][05:19:29][COMM]msg 0261 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0262 loss. last_tick:0. cur_tick:100013. period:10000
[D][05:19:29][COMM]msg 0263 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0281 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0282 loss. last_tick:0. cur_tick:100014. period:10000
[D][05:19:29][COMM]msg 0283 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02A1 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02A2 loss. last_tick:0. cur_tick:100015. period:10000
[D][05:19:29][COMM]msg 02A3 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02C3 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02C4 loss. last_tick:0. cur_tick:100016. period:10000
[D][05:19:29][COMM]msg 02C5 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02E3 loss

2025-07-31 23:24:29:650 ==>> . last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02E4 loss. last_tick:0. cur_tick:100017. period:10000
[D][05:19:29][COMM]msg 02E5 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0302 loss. last_tick:0. cur_tick:100018. period:10000
[D][05:19:29][COMM]msg 0303 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 0304 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02E6 loss. last_tick:0. cur_tick:100019. period:10000
[D][05:19:29][COMM]msg 02E7 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0305 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 0306 loss. last_tick:0. cur_tick:100020. period:10000
[D][05:19:29][COMM]msg 02A8 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02A9 loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02AA loss. last_tick:0. cur_tick:100021. period:10000
[D][05:19:29][COMM]msg 02AB loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]msg 02AD loss. last_tick:0. cur_tick:100022. period:10000
[D][05:19:29][COMM]bat msg 02AD loss. last_tick:0. cur_tick:100022. perio

2025-07-31 23:24:29:755 ==>> d:10000. j,i:0 53
[D][05:19:29][COMM]bat msg 024A loss. last_tick:0. cur_tick:100023. period:10000. j,i:11 64
[D][05:19:29][COMM]bat msg 024B loss. last_tick:0. cur_tick:100023. period:10000. j,i:12 65
[D][05:19:29][COMM]bat msg 024C loss. last_tick:0. cur_tick:100024. period:10000. j,i:13 66
[D][05:19:29][COMM]bat msg 024D loss. last_tick:0. cur_tick:100024. period:10000. j,i:14 67
[D][05:19:29][COMM]bat msg 0250 loss. last_tick:0. cur_tick:100024. period:10000. j,i:17 70
[D][05:19:29][COMM]bat msg 0251 loss. last_tick:0. cur_tick:100025. period:10000. j,i:18 71
[D][05:19:29][COMM]bat msg 025A loss. last_tick:0. cur_tick:100025. period:10000. j,i:22 75
[D][05:19:29][COMM]CAN message fault change: 0x0008F80C71E2223F->0x003FFFFFFFFFFFFF 100025
[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x01FFFFFF 100026
[D][05:19:29][COMM]CAN fault change: 0x0000000300010F01->0x0000000300010F03 100026
$GBGGA,152429.000,2301.2582010,N,11421.9434265,E,1,11,1.20,84.132,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,10,11,25,34,23,41,,2.67,1.20,2.39,4*0D

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,51,41,6,64,14,37,1*7C

$GBGSV,7,2,25,16,64,18,39,3,60,190,40,10,57,222,38,59,52,12

2025-07-31 23:24:29:830 ==>> 9,40,1*74

$GBGSV,7,3,25,11,52,113,40,9,52,333,36,25,51,23,41,43,48,160,40,1*72

$GBGSV,7,4,25,34,46,74,41,1,45,126,38,2,45,236,35,60,41,239,40,1*42

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,24,22,278,35,1*4F

$GBGSV,7,6,25,5,21,255,33,44,13,172,32,33,5,322,34,12,,,35,1*47

$GBGSV,7,7,25,32,,,34,1*77

$GBGSV,2,1,06,40,77,191,41,39,66,51,41,25,51,23,41,34,46,74,39,5*4F

$GBGSV,2,2,06,23,32,315,38,41,32,240,38,5*71

$GBRMC,152429.000,A,2301.2582010,N,11421.9434265,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152429.000,1.812,0.169,0.170,0.261,1.462,1.545,3.044*7C



2025-07-31 23:24:30:321 ==>> $GBGGA,152430.000,2301.2582069,N,11421.9433211,E,1,18,0.79,84.372,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,43,34,1.64,0.79,1.44,4*05

$GBGSA,A,3,02,01,60,23,41,24,,,,,,,1.64,0.79,1.44,4*03

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,51,41,6,64,14,37,1*7C

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,11,52,113,40,1*70

$GBGSV,7,3,25,9,52,333,36,25,51,23,41,59,49,131,40,43,48,160,40,1*74

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,5,21,255,33,1*76

$GBGSV,7,6,25,24,17,81,35,32,17,298,34,44,13,172,32,33,5,322,34,1*7A

$GBGSV,7,7,25,12,,,35,1*74

$GBGSV,2,1,07,40,77,191,41,39,66,51,41,25,51,23,41,43,48,160,39,5*74

$GBGSV,2,2,07,34,46,74,40,23,32,315,39,41,32,240,38,5*73

$GBRMC,152430.000,A,2301.2582069,N,11421.9433211,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,152430.000,1.739,0.183,0.194,0.284,1.387,1.453,2.836*71



2025-07-31 23:24:30:809 ==>> [D][05:19:30][COMM]read battery soc:255


2025-07-31 23:24:31:326 ==>> $GBGGA,152431.000,2301.2581881,N,11421.9432695,E,1,20,0.72,84.467,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.42,0.72,1.23,4*05

$GBGSA,A,3,34,02,01,60,23,41,24,32,,,,,1.42,0.72,1.23,4*0B

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,41,6,64,14,37,1*7F

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,35,1*4F

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,40,43,48,160,40,1*4C

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,37,60,41,238,40,1*4F

$GBGSV,7,5,25,4,32,111,32,23,32,315,39,41,32,240,38,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,44,13,172,32,1*7E

$GBGSV,7,7,25,33,5,322,34,1*70

$GBGSV,3,1,09,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*78

$GBGSV,3,2,09,34,46,74,41,23,32,315,38,41,32,240,38,24,17,81,35,5*73

$GBGSV,3,3,09,32,17,298,35,5*49

$GBRMC,152431.000,A,2301.2581881,N,11421.9432695,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152431.000,1.719,0.180,0.188,0.271,1.355,1.408,2.689*7D



2025-07-31 23:24:31:754 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:24:32:527 ==>> [D][05:19:31][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:19:31][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:31][COMM]Main Task receive event:14
[D][05:19:31][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955171, allstateRepSeconds = 0
[D][05:19:31][COMM]index:0,power_mode:0xFF
[D][05:19:31][COMM]index:1,sound_mode:0xFF
[D][05:19:31][COMM]index:2,gsensor_mode:0xFF
[D][05:19:31][COMM]index:3,report_freq_mode:0xFF
[D][05:19:31][COMM]index:4,report_period:0xFF
[D][05:19:31][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:31][COMM]index:6,normal_reset_period:0xFF
[D][05:19:31][COMM]index:7,spock_over_speed:0xFF
[D][05:19:31][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:31][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:31][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:31][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:31][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:31][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:31][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:31][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:31][COMM]index:16,imu_config_params:0xFF
[D][05:19:31][COMM]index:17,long_connect_params:0xFF
[D][05:19:31][CO

2025-07-31 23:24:32:632 ==>> MM]index:18,detain_mark:0xFF
[D][05:19:31][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:31][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:31][COMM]index:21,mc_mode:0xFF
[D][05:19:31][COMM]index:22,S_mode:0xFF
[D][05:19:31][COMM]index:23,overweight:0xFF
[D][05:19:31][COMM]index:24,standstill_mode:0xFF
[D][05:19:31][COMM]index:25,night_mode:0xFF
[D][05:19:31][COMM]index:26,experiment1:0xFF
[D][05:19:31][COMM]index:27,experiment2:0xFF
[D][05:19:31][COMM]index:28,experiment3:0xFF
[D][05:19:31][COMM]index:29,experiment4:0xFF
[D][05:19:31][COMM]index:30,night_mode_start:0xFF
[D][05:19:31][COMM]index:31,night_mode_end:0xFF
[D][05:19:31][COMM]index:33,park_report_minutes:0xFF
[D][05:19:31][COMM]index:34,park_report_mode:0xFF
[D][05:19:31][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:31][COMM]index:38,charge_battery_para: FF
[D][05:19:31][COMM]index:39,multirider_mode:0xFF
[D][05:19:31][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:31][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:31][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:31][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:31][COMM]index:44,riding_duration_config:0xFF
[D][05

2025-07-31 23:24:32:737 ==>> :19:31][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:31][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:31][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:31][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:31][COMM]index:49,mc_load_startup:0xFF
[D][05:19:31][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:31][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:31][COMM]index:52,traffic_mode:0xFF
[D][05:19:31][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:31][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:31][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:31][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:31][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:31][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:31][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:31][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:31][COMM]index:63,experiment5:0xFF
[D][05:19:31][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:31][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:31][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:31][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:31][COMM]index:68,camera_park_ps_cfg:0xFFFF


2025-07-31 23:24:32:842 ==>> 
[D][05:19:31][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:31][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:31][COMM]index:72,experiment6:0xFF
[D][05:19:31][COMM]index:73,experiment7:0xFF
[D][05:19:31][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:31][COMM]index:75,zero_value_from_server:-1
[D][05:19:31][COMM]index:76,multirider_threshold:255
[D][05:19:31][COMM]index:77,experiment8:255
[D][05:19:31][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:31][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:31][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:31][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:31][COMM]index:83,loc_report_interval:255
[D][05:19:31][COMM]index:84,multirider_threshold_p2:255
[D][05:19:31][COMM]index:85,multirider_strategy:255
[D][05:19:31][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:31][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:31][COMM]index:90,weight_param:0xFF
[D][05:19:31][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:31][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:31][COMM]index:95,current_limit:0xFF
[D][05:19:31][COMM]index:97,

2025-07-31 23:24:32:947 ==>> panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:31][COMM]index:100,location_mode:0xFF

[W][05:19:31][PROT]remove success[1629955171],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:31][PROT]add success [1629955171],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:31][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,152432.000,2301.2581775,N,11421.9432322,E,1,21,0.69,84.561,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.35,0.69,1.16,4*09

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,,,,1.35,0.69,1.16,4*04

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,41,6,64,14,37,1*7F

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,36,1*4C

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,40,43,48,160,40,1*4C

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,44,13,172,32,1*7E

$GBGSV,7,7,25,33,5,322,34,1*70

$GBGSV,3,1,09,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*78

$GBGSV,3,2,09,34,46,74,40,23,32,315,38,41,3

2025-07-31 23:24:33:007 ==>> 2,240,38,24,17,81,34,5*73

$GBGSV,3,3,09,32,17,298,35,5*49

$GBRMC,152432.000,A,2301.2581775,N,11421.9432322,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152432.000,1.789,0.177,0.187,0.268,1.392,1.435,2.626*78

                                         

2025-07-31 23:24:33:352 ==>> $GBGGA,152433.000,2301.2581618,N,11421.9432216,E,1,22,0.64,84.525,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,41,6,64,14,37,1*7F

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,35,1*4F

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,40,43,48,160,40,1*4C

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,33,15,190,34,1*72

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,09,40,77,191,41,39,66,52,41,25,51,23,40,43,48,160,39,5*79

$GBGSV,3,2,09,34,46,74,40,23,32,315,39,41,32,240,38,24,17,81,34,5*72

$GBGSV,3,3,09,32,17,298,35,5*49

$GBRMC,152433.000,A,2301.2581618,N,11421.9432216,E,0.000,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,152433.000,2.487,0.191,0.195,0.279,1.845,1.876,2.925*7A



2025-07-31 23:24:33:991 ==>> [D][05:19:33][M2M ]get csq[-1]


2025-07-31 23:24:34:384 ==>> $GBGGA,152434.000,2301.2581394,N,11421.9432093,E,1,22,0.64,84.492,M,-1.770,M,,*57

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,42,7,70,219,39,39,66,52,41,6,64,14,37,1*71

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,37,9,55,347,35,1*40

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,40,43,48,160,40,1*4C

$GBGSV,7,4,25,34,46,74,40,2,46,236,35,1,45,126,38,60,41,238,40,1*41

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,38,12,25,51,35,1*75

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,33,33,15,190,34,1*75

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,10,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*70

$GBGSV,3,2,10,34,46,74,41,23,32,315,38,41,32,240,38,24,17,81,34,5*7A

$GBGSV,3,3,10,32,17,298,35,33,15,190,33,5*7D

$GBRMC,152434.000,A,2301.2581394,N,11421.9432093,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152434.000,2.957,0.202,0.207,0.292,2.116,2.140,3.103*77

>>>>>RESEND ALLSTATE<<<<<
[W][05:19:34][PROT]remove success[1629955174],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:3

2025-07-31 23:24:34:444 ==>> 4][PROT]add success [1629955174],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:34][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:34][COMM][LOC]wifi scan is already running, error
[D][05:19:34][COMM]Main Task receive event:14 finished processing
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 23:24:34:807 ==>> [D][05:19:34][COMM]read battery soc:255


2025-07-31 23:24:35:340 ==>> $GBGGA,152435.000,2301.2581247,N,11421.9431970,E,1,22,0.64,84.456,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,41,6,64,14,37,1*7F

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,36,1*4C

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,41,43,48,160,40,1*4D

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,32,24,17,81,34,32,17,298,33,33,15,190,34,1*74

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,10,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*70

$GBGSV,3,2,10,34,46,74,40,23,32,315,39,41,32,240,39,24,17,81,35,5*7A

$GBGSV,3,3,10,32,17,298,34,33,15,190,33,5*7C

$GBRMC,152435.000,A,2301.2581247,N,11421.9431970,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,152435.000,3.023,0.198,0.202,0.285,2.148,2.169,3.089*7D



2025-07-31 23:24:35:628 ==>>                                                                                                                                                                                  ack sub_id[f], socket[0], result[-93]
[D][05:19:35][SAL ]socket send fail. id[4]
[D][05:19:35][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:35][CAT1]gsm read msg sub id: 13
[D][05:19:35][M2M ]m2m select fd[4]
[D][05:19:35][M2M ]socket[4] Link is disconnected
[D][05:19:35][M2M ]tcpclient close[4]
[D][05:19:35][SAL ]socket[4] has closed
[D][05:19:35][PROT]protocol read data ok
[E][05:19:35][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:19:35][CAT1]tx ret[8] >>> AT+CSQ

[E][05:19:35][PROT]M2M Send Fail [1629955175]
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:35][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:35][CAT1]exec over: func id: 13, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 21
[D][05:19:35][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]cell info report total[0]
[D][05:19:35][CAT1]exec over: func id: 21, ret: 6
[D][05:19:35][CAT1]gsm read msg sub id: 13
[D][05:19:35][CAT1]tx ret[8] >>> AT+CSQ

[D][05

2025-07-31 23:24:35:673 ==>> :19:35][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:35][CAT1]exec over: func id: 13, ret: 21
[D][05:19:35][CAT1]gsm read msg sub id: 10
[D][05:19:35][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:35][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:35][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 23:24:36:006 ==>> [D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 10, ret: 6
[D][05:19:35][CAT1]sub id: 10, ret: 6

[D][05:19:35][SAL ]Cellular task submsg id[68]
[D][05:19:35][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:35][M2M ]m2m gsm shut done, ret[0]
[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:35][SAL ]open socket ind id[4], rst[0]
[D][05:19:35][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:35][SAL ]Cellular task submsg id[8]
[D][05:19:35][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:35][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:35][CAT1]gsm read msg sub id: 8
[D][05:19:35][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:35][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:35][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:35][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:24:36:403 ==>> [D][05:19:36][CAT1]pdpdeact urc len[22]
$GBGGA,152436.000,2301.2581084,N,11421.9431854,E,1,22,0.64,84.402,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,41,6,64,14,37,1*7F

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,36,1*4C

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,41,43,48,160,41,1*4C

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,33,15,190,34,1*72

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,10,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*70

$GBGSV,3,2,10,34,46,74,40,23,32,315,38,41,32,240,38,24,17,81,35,5*7A

$GBGSV,3,3,10,32,17,298,34,33,15,190,33,5*7C

$GBRMC,152436.000,A,2301.2581084,N,11421.9431854,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152436.000,3.043,0.180,0.183,0.260,2.155,2.172,3.059*7B



2025-07-31 23:24:36:826 ==>> [D][05:19:36][COMM]read battery soc:255


2025-07-31 23:24:37:484 ==>> $GBGGA,152437.000,2301.2580789,N,11421.9431715,E,1,22,0.64,84.379,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,43,7,70,219,40,39,66,52,42,6,64,14,37,1*7D

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,36,1*4C

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,41,43,48,160,41,1*4C

$GBGSV,7,4,25,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,33,15,190,34,1*72

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,10,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,40,5*7E

$GBGSV,3,2,10,34,46,74,41,23,32,315,38,41,32,240,38,24,17,81,35,5*7B

$GBGSV,3,3,10,32,17,298,34,33,15,190,33,5*7C

$GBRMC,152437.000,A,2301.2580789,N,11421.9431715,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,152437.000,3.071,0.193,0.197,0.277,2.166,2.182,3.040*7D

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:37][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:37][CAT

2025-07-31 23:24:37:544 ==>> 1]tx ret[8] >>> AT+CSQ

[D][05:19:37][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:37][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:37][CAT1]<<< 
OK

[D][05:19:37][CAT1]exec over: func id: 8, ret: 6


2025-07-31 23:24:37:784 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:24:37:844 ==>>                                                                                                                                                    [14]
[D][05:19:37][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:37][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:37][M2M ]g_m2m_is_idle become true
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:37][PROT]index:1 1629955177
[D][05:19:37][PROT]is_send:0
[D][05:19:37][PROT]sequence_num:11
[D][05:19:37][PROT]retry_timeout:0
[D][05:19:37][PROT]retry_times:1
[D][05:19:37][PROT]send_path:0x2
[D][05:19:37][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:37][PROT]===========================================================
[W][05:19:37][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955177]
[D][05:19:37][PROT]===========================================================
[D][05:19:37][PROT]sending traceid [999999999990000C]
[D][05:19:37][PROT]Send_TO_M2M [1629955177]
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:37][SAL ]sock send credit cnt[6]
[D][05:19:37][SAL ]sock send ind credit cnt[6]
[D][05:19:37][M2M ]m2m send data len[166]
[D][05:19:37][SAL ]Cellular task submsg id[10]
[D

2025-07-31 23:24:37:949 ==>> ][05:19:37][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd8] format[0]
[D][05:19:37][CAT1]gsm read msg sub id: 15
[D][05:19:37][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:37][CAT1]Send Data To Server[166][169] ... ->:
0053B987113311331133113311331B88B0438B97001E301CF8B9BA36E8D8599B737257DDFE6F61F395D4C381CBB75A4F9038E2F6880774F35B088FB2C14C1033E1CE0D366B1871D9238859F5F0DC41C1DCB128
[D][05:19:37][CAT1]<<< 
SEND OK

[D][05:19:37][CAT1]exec over: func id: 15, ret: 11
[D][05:19:37][CAT1]sub id: 15, ret: 11

[D][05:19:37][SAL ]Cellular task submsg id[68]
[D][05:19:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:37][M2M ]g_m2m_is_idle become true
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:37][PROT]M2M Send ok [1629955177]


2025-07-31 23:24:38:814 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:24:38:823 ==>> 检测【关闭GPS】
2025-07-31 23:24:38:847 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:24:38:915 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:37][COMM]Main Task receive event:14
[D][05:19:37][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955177, allstateRepSeconds = 0
[D][05:19:37][COMM]index:0,power_mode:0xFF
[D][05:19:37][COMM]index:1,sound_mode:0xFF
[D][05:19:37][COMM]index:2,gsensor_mode:0xFF
[D][05:19:37][COMM]index:3,report_freq_mode:0xFF
[D][05:19:37][COMM]index:4,report_period:0xFF
[D][05:19:37][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:37][COMM]index:6,normal_reset_period:0xFF
[D][05:19:37][COMM]index:7,spock_over_speed:0xFF
[D][05:19:37][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:37][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:37][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:37][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:37][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:37][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:37][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:37][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:37][COMM]index:16,imu_config_params:0xFF
[D][05:19:37][COMM]index:17,long_connect_params:0xFF
[D][05:19:37][COMM]index:18,detain_mark:0xFF
[D][05:19:37][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:37][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:37][COMM]index:2

2025-07-31 23:24:39:020 ==>> 1,mc_mode:0xFF
[D][05:19:37][COMM]index:22,S_mode:0xFF
[D][05:19:37][COMM]index:23,overweight:0xFF
[D][05:19:37][COMM]index:24,standstill_mode:0xFF
[D][05:19:37][COMM]index:25,night_mode:0xFF
[D][05:19:37][COMM]index:26,experiment1:0xFF
[D][05:19:37][COMM]index:27,experiment2:0xFF
[D][05:19:37][COMM]index:28,experiment3:0xFF
[D][05:19:37][COMM]index:29,experiment4:0xFF
[D][05:19:37][COMM]index:30,night_mode_start:0xFF
[D][05:19:37][COMM]index:31,night_mode_end:0xFF
[D][05:19:37][COMM]index:33,park_report_minutes:0xFF
[D][05:19:37][COMM]index:34,park_report_mode:0xFF
[D][05:19:37][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:37][COMM]index:38,charge_battery_para: FF
[D][05:19:37][COMM]index:39,multirider_mode:0xFF
[D][05:19:37][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:37][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:37][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:37][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:37][COMM]index:44,riding_duration_config:0xFF
[D][05:19:37][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:37][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:37][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:37][

2025-07-31 23:24:39:125 ==>> COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:37][COMM]index:49,mc_load_startup:0xFF
[D][05:19:37][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:37][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:37][COMM]index:52,traffic_mode:0xFF
[D][05:19:37][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:37][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:37][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:37][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:37][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:37][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:37][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:37][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:37][COMM]index:63,experiment5:0xFF
[D][05:19:37][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:37][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:37][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:37][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:37][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:37][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:37][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:37][COMM]index:72,experiment6:0xFF
[D][05:

2025-07-31 23:24:39:230 ==>> 19:37][COMM]index:73,experiment7:0xFF
[D][05:19:37][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:37][COMM]index:75,zero_value_from_server:-1
[D][05:19:37][COMM]index:76,multirider_threshold:255
[D][05:19:37][COMM]index:77,experiment8:255
[D][05:19:37][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:37][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:37][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:37][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:37][COMM]index:83,loc_report_interval:255
[D][05:19:37][COMM]index:84,multirider_threshold_p2:255
[D][05:19:37][COMM]index:85,multirider_strategy:255
[D][05:19:37][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:37][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:37][COMM]index:90,weight_param:0xFF
[D][05:19:37][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:37][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:37][COMM]index:95,current_limit:0xFF
[D][05:19:37][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:37][COMM]index:100,location_mode:0xFF

[D][05:19:37][HSDK][0] flush to flash addr:[0xE42500] --- write len -

2025-07-31 23:24:39:335 ==>> -- [256]
[W][05:19:37][PROT]remove success[1629955177],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:37][PROT]add success [1629955177],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:37][PROT]index:0 1629955177
[D][05:19:37][PROT]is_send:0
[D][05:19:37][PROT]sequence_num:12
[D][05:19:37][PROT]retry_timeout:0
[D][05:19:37][PROT]retry_times:1
[D][05:19:37][PROT]send_path:0x2
[D][05:19:37][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:37][PROT]===========================================================
[W][05:19:37][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955177]
[D][05:19:37][PROT]===========================================================
[D][05:19:37][PROT]sending traceid [999999999990000D]
[D][05:19:37][PROT]Send_TO_M2M [1629955177]
[D][05:19:37][CAT1]gsm read msg sub id: 13
[D][05:19:37][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:38][SAL ]sock send credit cnt[6]
[D][05:19:38][SAL ]sock send ind credit cnt[6]
[D][05:19:38][M2M ]m2m send data len[294]
[D][05:19:38][SAL 

2025-07-31 23:24:39:441 ==>> ]Cellular task submsg id[10]
[D][05:19:38][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df0] format[0]
[D][05:19:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:38][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:19:38][CAT1]exec over: func id: 13, ret: 21
[D][05:19:38][M2M ]get csq[24]
[D][05:19:38][CAT1]gsm read msg sub id: 15
[D][05:19:38][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:38][CAT1]Send Data To Server[294][294] ... ->:
0093B984113311331133113311331B88BF2E221C2C30306297947359C9B6A85572068893E3F249FC1B578E429076D0D494845F8346EDF3BDCF8E1DFAECE52664B7B0DFFFBD4BA7C752FB61BC49908F34194C3DB866F4D89A7DDE18D9C508AEBCD8B89525BC388099C93D74C5A203BD8849B7EAB73DF355BC67C0FF97034DF604B25714A2EFFB2797E86EA6C98506952DAD2561
$GBGGA,152438.000,2301.2580737,N,11421.9431526,E,1,22,0.64,84.379,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,03,10,09,11,25,59,43,1.19,0.64,1.01,4*0C

$GBGSA,A,3,34,02,01,60,23,41,12,24,32,33,,,1.19,0.64,1.01,4*01

$GBGSV,7,1,25,40,77,191,42,7,70,219,40,39,66,52,42,6,64,14,37,1*7C

$GBGSV,7,2,25,16,64,18,39,3,61,190,40,10,57,222,38,9,55,347,36,1*4C

$GBGSV,7,3,25,11,52,113,40,25,51,23,41,59,49,131,41,43,48,160,41,1*4C

$GBGSV,7,4,25

2025-07-31 23:24:39:531 ==>> ,34,46,74,41,2,46,236,35,1,45,126,38,60,41,238,40,1*40

$GBGSV,7,5,25,4,32,111,33,23,32,315,39,41,32,240,39,12,25,51,35,1*74

$GBGSV,7,6,25,5,21,255,33,24,17,81,34,32,17,298,34,33,15,190,34,1*72

$GBGSV,7,7,25,44,13,172,32,1*46

$GBGSV,3,1,10,40,77,191,41,39,66,52,41,25,51,23,41,43,48,160,39,5*70

$GBGSV,3,2,10,34,46,74,41,23,32,315,38,41,32,240,39,24,17,81,35,5*7A

$GBGSV,3,3,10,32,17,298,34,33,15,190,33,5*7C

$GBRMC,152438.000,A,2301.2580737,N,11421.9431526,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,152438.000,3.014,0.186,0.189,0.267,2.131,2.145,2.982*74

[D][05:19:38][CAT1]<<< 
SEND OK

[D][05:19:38][CAT1]exec over: func id: 15, ret: 11
[D][05:19:38][CAT1]sub id: 15, ret: 11

[D][05:19:38][SAL ]Cellular task submsg id[68]
[D][05:19:38][SAL ]handle subc

2025-07-31 23:24:39:849 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          [W][05:19:39][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:39][GNSS]stop locating
[D][05:19:39][GNSS]stop event:8
[D][05:19:39][GNSS]GPS stop. ret=0
[D][05:19:39][GNSS]all con

2025-07-31 23:24:39:908 ==>> tinue location stop
[W][05:19:39][GNSS]stop locating
[D][05:19:39][GNSS]all sing location stop
[D][05:19:39][CAT1]gsm read msg sub id: 24
[D][05:19:39][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:39][CAT1]<<< 
OK

[D][05:19:39][CAT1]exec over: func id: 24, ret: 6
[D][05:19:39][CAT1]sub id: 24, ret: 6



2025-07-31 23:24:40:109 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:24:40:120 ==>> 检测【清空消息队列2】
2025-07-31 23:24:40:134 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:24:40:324 ==>> [D][05:19:40][GNSS]recv submsg id[1]
[D][05:19:40][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:40][GNSS]location stop evt done evt
[W][05:19:40][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:40][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:24:40:388 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:24:40:396 ==>> 检测【轮动检测】
2025-07-31 23:24:40:410 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:24:40:524 ==>> 3A A3 01 00 A3 


2025-07-31 23:24:40:628 ==>> OFF_OUT1
OVER 150


2025-07-31 23:24:40:703 ==>> [D][05:19:40][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:24:40:839 ==>> [D][05:19:40][COMM]read battery soc:255


2025-07-31 23:24:40:900 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:24:41:021 ==>> 3A A3 01 01 A3 


2025-07-31 23:24:41:127 ==>> ON_OUT1
OVER 150


2025-07-31 23:24:41:175 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:24:41:184 ==>> 检测【关闭小电池】
2025-07-31 23:24:41:198 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:24:41:232 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:24:41:448 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:24:41:457 ==>> 检测【进入休眠模式】
2025-07-31 23:24:41:482 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:24:41:583 ==>> [W][05:19:41][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:24:41:687 ==>> [D][05:19:41][COMM]Main Task receive event:28
[D][05:19:41][COMM]main task tmp_sleep_event

2025-07-31 23:24:41:717 ==>>  = 8
[D][05:19:41][COMM]prepare to sleep
[D][05:19:41][CAT1]gsm read msg sub id: 12
[D][05:19:41][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:24:42:552 ==>> [D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 12, ret: 6
[D][05:19:42][M2M ]tcpclient close[4]
[D][05:19:42][SAL ]Cellular task submsg id[12]
[D][05:19:42][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db8], socket[0]
[D][05:19:42][CAT1]gsm read msg sub id: 9
[D][05:19:42][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:42][CAT1]<<< 
OK

[D][05:19:42][CAT1]exec over: func id: 9, ret: 6
[D][05:19:42][CAT1]sub id: 9, ret: 6

[D][05:19:42][SAL ]Cellular task submsg id[68]
[D][05:19:42][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:42][SAL ]socket close ind. id[4]
[D][05:19:42][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:42][COMM]1x1 frm_can_tp_send ok
[D][05:19:42][CAT1]pdpdeact urc len[22]


2025-07-31 23:24:42:852 ==>> [E][05:19:42][COMM]1x1 rx timeout
[D][05:19:42][COMM]1x1 frm_can_tp_send ok
[D][05:19:42][COMM]read battery soc:255


2025-07-31 23:24:43:355 ==>> [E][05:19:43][COMM]1x1 rx timeout
[E][05:19:43][COMM]1x1 tp timeout
[D][05:19:43][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[E][05:19:43][COMM]1x1 error -3.
[W][05:19:43][COMM]CAN STOP!
[D][05:19:43][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:43][COMM]------------ready to Power off Acckey 1------------
[D][05:19:43][COMM]------------ready to Power off Acckey 2------------
[D][05:19:43][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:43][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1308
[D][05:19:43][COMM]bat sleep fail, reason:-1
[D][05:19:43][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:43][COMM]accel parse set 0
[D][05:19:43][COMM]imu rest ok. 114151
[D][05:19:43][COMM]imu sleep 0
[W][05:19:43][COMM]now sleep


2025-07-31 23:24:43:551 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:24:43:562 ==>> 检测【检测33V休眠电流】
2025-07-31 23:24:43:576 ==>> 开始33V电流采样
2025-07-31 23:24:43:591 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:43:662 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:24:44:673 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:24:44:736 ==>> Current33V:????:11.80

2025-07-31 23:24:45:184 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:45:198 ==>> 【检测33V休眠电流】通过,【11.8uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:24:45:216 ==>> 该项需要延时执行
2025-07-31 23:24:47:210 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:24:47:225 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:24:47:261 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:24:47:331 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1670mV
Get AD_V4 1mV
Get AD_V5 2754mV
Get AD_V6 1927mV
Get AD_V7 1086mV
OVER 150


2025-07-31 23:24:48:234 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:24:48:246 ==>> 检测【打开小电池2】
2025-07-31 23:24:48:263 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:24:48:321 ==>> 6A A6 01 A6 6A 


2025-07-31 23:24:48:426 ==>> Battery ON
OVER 150


2025-07-31 23:24:48:510 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:24:48:519 ==>> 该项需要延时执行
2025-07-31 23:24:49:014 ==>> 此处延时了:【500】毫秒
2025-07-31 23:24:49:029 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:24:49:047 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:24:49:124 ==>> 5A A5 02 5A A5 


2025-07-31 23:24:49:228 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:24:49:292 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:24:49:303 ==>> 该项需要延时执行
2025-07-31 23:24:49:793 ==>> 此处延时了:【500】毫秒
2025-07-31 23:24:49:810 ==>> 检测【进入休眠模式2】
2025-07-31 23:24:49:831 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:24:49:929 ==>> [D][05:19:49][COMM]------------ready to Power on Acckey 1------------
[D][05:19:49][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:49][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:49][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:49][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:49][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:49][COMM]----- get Acckey 1 and value:1------------
[W][05:19:49][COMM]CAN START!
[D][05:19:49][GNSS]handler GSMGet Base timeout
[D][05:19:49][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:49][COMM]1x1 frm_can_tp_send ok
[D][05:19:49][CAT1]gsm read msg sub id: 12
[D][05:19:49][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:49][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 120

2025-07-31 23:24:49:992 ==>> 643
[D][05:19:49][COMM][Audio]exec status ready.
[D][05:19:49][CAT1]<<< 
OK

[D][05:19:49][CAT1]exec over: func id: 12, ret: 6
[D][05:19:49][COMM]imu wakeup ok. 120658
[D][05:19:49][COMM]imu wakeup 1
[W][05:19:49][COMM]wake up system, wakeupEvt=0x80
[D][05:19:49][COMM]frm_can_weigth_power_set 1
[D][05:19:49][COMM]Clear Sleep Block Evt
[D][05:19:49][COMM]Main Task receive event:28 finished processing


2025-07-31 23:24:50:229 ==>> [W][05:19:49][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:49][COMM]Main Task receive event:28
[D][05:19:49][COMM]prepare to sleep
[D][05:19:49][CAT1]gsm read msg sub id: 12
[D][05:19:49][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:49][CAT1]<<< 
OK

[D][05:19:49][CAT1]exec over: func id: 12, ret: 6
[W][05:19:49][COMM]CAN STOP!
[D][05:19:49][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:49][COMM]------------ready to Power off Acckey 1------------
[D][05:19:49][COMM]------------ready to Power off Acckey 2------------
[D][05:19:49][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:49][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 136
[D][05:19:49][COMM]bat sleep fail, reason:-1
[D][05:19:49][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:49][COMM]accel parse set 0
[D][05:19:49][COMM]imu rest ok. 121010
[D][05:19:50][COMM]imu sleep 0
[W][05:19:50][COMM]now sleep


2025-07-31 23:24:50:323 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:24:50:333 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:24:50:347 ==>> 开始小电池电流采样
2025-07-31 23:24:50:375 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:50:424 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:24:51:438 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:24:51:483 ==>> CurrentBattery:ƽ��:68.55

2025-07-31 23:24:51:947 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:51:957 ==>> 【检测小电池休眠电流】通过,【68.55uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:24:51:966 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:24:51:981 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:24:52:023 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:24:52:262 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:24:52:275 ==>> 该项需要延时执行
2025-07-31 23:24:52:292 ==>> [D][05:19:51][COMM]------------ready to Power on Acckey 1------------
[D][05:19:51][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:51][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:51][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 4
[D][05:19:51][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:51][COMM]----- get Acckey 1 and value:1------------
[W][05:19:51][COMM]CAN START!
[E][05:19:51][COMM]1x1 rx timeout
[E][05:19:51][COMM]1x1 tp timeout
[E][05:19:51][COMM]1x1 error -3.
[D][05:19:51][CAT1]gsm read msg sub id: 12
[D][05:19:51][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:52][COMM][Audio]exec status ready.
[D][05:19:52][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:52][CAT1]<<< 
OK

[D][05:19:52][CAT1]exec over: func id: 12, ret: 6
[D][05:19:52][COMM]imu wakeup ok. 123027
[D][05:19:52][COMM]imu wakeup 1
[W][05:19:52][COMM]wake up system, wakeupEvt=0x80
[D][05:19:52][COMM]frm_can_weigth_power_set 1
[D][05:19:52][COMM]Clear Sleep Block Evt
[D][05:19:52][COMM]1x1 tx_id:3,8, tx_l

2025-07-31 23:24:52:316 ==>> en:2
[D][05:19:52][COMM]1x1 frm_can_tp_send ok
[D][05:19:52][COMM]read battery soc:0


2025-07-31 23:24:52:534 ==>> [E][05:19:52][COMM]1x1 rx timeout
[D][05:19:52][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:24:52:639 ==>> [D][05:19:52][COMM]msg 02A0 loss. last_tick:122995. cur_tick:123506. period:50
[D][05:19:52][COMM]msg 02A4 loss. last_tick:122995. cur_tick:123507. period:50
[D][05:19:52][COMM]msg 02A5 loss. last_tick:122995. cur_tick:123

2025-07-31 23:24:52:699 ==>> 508. period:50
[D][05:19:52][COMM]msg 02A6 loss. last_tick:122995. cur_tick:123508. period:50
[D][05:19:52][COMM]msg 02A7 loss. last_tick:122995. cur_tick:123508. period:50
[D][05:19:52][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 123509
[D][05:19:52][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 123509


2025-07-31 23:24:52:774 ==>> 此处延时了:【500】毫秒
2025-07-31 23:24:52:788 ==>> 检测【检测唤醒】
2025-07-31 23:24:52:809 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:24:53:110 ==>> [W][05:19:52][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:52][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:52][FCTY]==========Modules-nRF5340 ==========
[D][05:19:52][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:52][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:52][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:52][FCTY]DeviceID    = 460130071539550
[D][05:19:52][FCTY]HardwareID  = 867222087589406
[D][05:19:52][FCTY]MoBikeID    = 9999999999
[D][05:19:52][FCTY]LockID      = FFFFFFFFFF
[D][05:19:52][FCTY]BLEFWVersion= 105
[D][05:19:52][FCTY]BLEMacAddr   = E2ABD7C5BC11
[D][05:19:52][FCTY]Bat         = 3864 mv
[D][05:19:52][FCTY]Current     = 0 ma
[D][05:19:52][FCTY]VBUS        = 2600 mv
[D][05:19:52][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:52][FCTY]Ext battery vol = 33, adc = 1305
[D][05:19:52][FCTY]Acckey1 vol = 5566 mv, Acckey2 vol = 50 mv
[D][05:19:52][FCTY]Bike Type flag is invalied
[D][05:19:52][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:52][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:52][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:52][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:52][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:52][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:52][FCTY]Bat1  

2025-07-31 23:24:53:155 ==>>        = 3785 mv
[D][05:19:52][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:52][FCTY]==========Modules-nRF5340 ==========
[E][05:19:52][COMM]1x1 rx timeout
[E][05:19:52][COMM]1x1 tp timeout
[E][05:19:52][COMM]1x1 error -3.
[D][05:19:52][COMM]Main Task receive event:28 finished processing


2025-07-31 23:24:53:262 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 23:24:53:349 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:24:53:367 ==>> 检测【关机】
2025-07-31 23:24:53:393 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:24:53:417 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         52][COMM]bat msg 024E loss. last_tick:122995. cur_tick:124011. period:100. j,i:15 68
[D][05:19:52][COMM]bat msg 024F loss. last_tick:122995. cur_tick:124011. period:100. j,i:16 69
[D][05:19:53][COMM]CAN message fault change: 0x0000E000002200

2025-07-31 23:24:53:432 ==>> 00->0x0000E00C71E22213 124012
[D][05:19:53][COMM]CAN message bat fault change: 0x00000000->0x0001802E 124013
[D][05:19:53][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 124013


2025-07-31 23:24:53:486 ==>> [W][05:19:53][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<


2025-07-31 23:24:53:591 ==>> [D][05:19:53][COMM]arm_hub_enable: hub power: 0
[D][05:19:53][HSDK]hexlog index save 0 5888 247 @ 0 : 0
[D][05:19:53][HSDK]write save hexlog index [0]
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:53][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:24:53:696 ==>> [D][05:19:53][COMM]msg 0222 loss. last_tick:122995. cur_tick:124580. period:150
[D][05:19:53][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 124582


2025-07-31 23:24:53:801 ==>> [D][05:19:53][

2025-07-31 23:24:53:846 ==>> COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:53][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:53][COMM]----- get Acckey 1 and value:1------------
[D][05:19:53][COMM]----- get Acckey 2 and value:0------------
[D][05:19:53][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:24:54:380 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:24:54:714 ==>> [D][05:19:53][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:53][COMM]----- get Acckey 1 and value:1------------
[D][05:19:53][COMM]----- get Acckey 2 and value:1------------
[D][05:19:53][COMM]more than the number of battery plugs
[D][05:19:53][COMM]VBUS is 1
[D][05:19:53][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:53][COMM]file:B50 exist
[D][05:19:53][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:53][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:53][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:53][COMM]Bat auth off fail, error:-1
[D][05:19:53][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:53][COMM]----- get Acckey 1 and value:1------------
[D][05:19:53][COMM]----- get Acckey 2 and value:1------------
[D][05:19:53][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:53][COMM]----- get Acckey 1 and value:1------------
[D][05:19:53][COMM]----- get Acckey 2 and value:1------------
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:53][COMM]file:B50 exist
[D][05:19:53][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:53][COMM]read file, len:10800, nu

2025-07-31 23:24:54:818 ==>> m:3
[D][05:19:53][COMM]--->crc16:0xb8a
[D][05:19:53][COMM]read file success
[D][05:19:53][COMM]accel parse set 1
[D][05:19:53][COMM][Audio]mon:9,05:19:53
[D][05:19:53][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:53][COMM]Main Task receive event:65
[D][05:19:53][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:53][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:53][COMM]Main Task receive event:65 finished processing
[

2025-07-31 23:24:54:923 ==>> D][05:19:53][COMM]Main Task receive event:66
[D][05:19:53][COMM]Try to Auto Lock Bat
[D][05:19:53][COMM]Main Task receive event:66 finished processing
[D][05:19:53][COMM]Main Task receive event:60
[D][05:19:53][COMM]smart_helmet_vol=255,255
[D][05:19:53][COMM]BAT CAN get state1 Fail 204
[D][05:19:53][COMM]BAT CAN get soc Fail, 204
[D][05:19:53][COMM]BAT CAN get state2 fail 204
[D][05:19:53][COMM]get soh error
[D][05:19:53][COMM]Receive Bat Lock cmd 0
[D][05:19:53][COMM]VBUS is 1
[E][05:19:53][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:53][COMM]report elecbike
[W][05:19:53][PROT]remove success[1629955193],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:53][PROT]add success [1629955193],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:53][COMM]Main Task receive event:60 finished processing
[D][05:19:53][COMM]Main Task receive event:61
[D][05:19:53][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:53][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:53][PROT]index:0
[D][05:19:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:53][PROT]is_send:1
[D][05:19:53][PROT]sequence_num:14
[D][05:19:53][PROT]retry_timeout:0

2025-07-31 23:24:55:028 ==>> 
[D][05:19:53][PROT]retry_times:3
[D][05:19:53][PROT]send_path:0x3
[D][05:19:53][PROT]msg_type:0x5d03
[D][05:19:53][PROT]===========================================================
[W][05:19:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955193]
[D][05:19:53][PROT]===========================================================
[D][05:19:53][PROT]Sending traceid[999999999990000F]
[D][05:19:53][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:53][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:53][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:53][COMM][D301]:type:3, trace id:280
[D][05:19:53][COMM]id[], hw[000
[D][05:19:53][COMM]get mcMaincircuitVolt error
[D][05:19:53][COMM]get mcSubcircuitVolt error
[D][05:19:53][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:53][COMM]BAT CAN get state1 Fail 204
[D][05:19:53][COMM]BAT CAN get soc Fail, 204
[D][05:19:53][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:53]

2025-07-31 23:24:55:133 ==>> [COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:53][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:53][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:53][SAL ]open socket ind id[4], rst[0]
[D][05:19:53][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:53][SAL ]Cellular task submsg id[8]
[D][05:19:53][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:53][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:53][COMM]BAT CAN get state2 fail 204
[D][05:19:53][COMM]get bat work mode err
[W][05:19:53][PROT]remove success[1629955193],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:53][PROT]add success [1629955193],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:53][COMM]Main Task receive event:61 finished processing
[D][05:19:53][CAT1]gsm read msg sub id: 8
[D][05:19:53][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:53][M2M ]m2m_task: control_queue type:[M2M_GSM_

2025-07-31 23:24:55:238 ==>> POWER_ON]
[D][05:19:53][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:53][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:53][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:53][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:53][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:53][CAT1]<<< 
+CME ERROR: 100

[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:53][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:19:54][COMM]Power Off
[D][05:19:54][COMM]read battery soc:255
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:54][COMM]f:[ec800m_audio_play_pr

2025-07-31 23:24:55:313 ==>> ocess].l:[991]. send ret: 0
[D][05:19:54][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:54][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:54][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:54][COMM]arm_hub_enable: hub power: 0
[D][05:19:54][HSDK]hexlog index save 0 5888 247 @ 0 : 0
[D][05:19:54][HSDK]write save hexlog index [0]
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:54][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:24:55:343 ==>>                               

2025-07-31 23:24:56:233 ==>> [D][05:19:56][COMM]exit wheel stolen mode.
[D][05:19:56][COMM]Main Task receive event:68
[D][05:19:56][COMM]handlerWheelStolen evt type = 2.
[E][05:19:56][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:56][GNSS]stop locating
[D][05:19:56][GNSS]all continue location stop
[D][05:19:56][COMM]Main Task receive event:68 finished processing
[D][05:19:56][COMM]read battery soc:255


2025-07-31 23:24:56:973 ==>> [D][05:19:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:24:57:323 ==>> [D][05:19:57][COMM]msg 0601 loss. last_tick:122995. cur_tick:128032. period:500
[D][05:19:57][COMM]msg 02AC loss. last_tick:122995. cur_tick:128032. period:500
[D][05:19:57][COMM]bat msg 0243 loss. last_tick:122995. cur_tick:128033. period:500. j,i:4 57
[D][05:19:57][COMM]bat msg 0245 loss. last_tick:122995. cur_tick:128033. period:500. j,i:6 59
[D][05:19:57][COMM]bat msg 0246 loss. last_tick:122995. cur_tick:128033. period:500. j,i:7 60
[D][05:19:57][COMM]bat msg 0247 loss. last_tick:122995. cur_tick:128034. period:500. j,i:8 61
[D][05:19:57][COMM]bat msg 0248 loss. last_tick:122995. cur_tick:128034. period:500. j,i:9 62
[D][05:19:57][COMM]bat msg 0249 loss. last_tick:122995. cur_tick:128035. period:500. j,i:10 63
[D][05:19:57][COMM]bat msg 0252 loss. last_tick:122995. cur_tick:128035. period:500. j,i:19 72
[D][05:19:57][COMM]bat msg 0257 loss. last_tick:122995. cur_tick:128035. period:500. j,i:20 73
[D][05:19:57][COMM]bat msg 0258 loss. last_tick:122995. cur_tick:128036. period:500. j,i:21 74
[D][05:19:57][COMM]bat msg 025B loss. last_tick:122995. cur_tick:128036. period:500. j,i:23 76
[D][05:19:57][COMM]bat msg 025C loss. last_tick:122995. cur_tick:128036. 

2025-07-31 23:24:57:368 ==>> period:500. j,i:24 77
[D][05:19:57][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 128037
[D][05:19:57][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 128037


2025-07-31 23:24:57:819 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:24:57:829 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:24:57:859 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:24:57:919 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:24:58:103 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:24:58:116 ==>> 检测【检测小电池关机电流】
2025-07-31 23:24:58:137 ==>> 开始小电池电流采样
2025-07-31 23:24:58:166 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:58:180 ==>> [D][05:19:57][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:19:57][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:19:57][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:19:57][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:57][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:19:57][COMM]accel parse set 0
[D][05:19:57][COMM]128866 imu init OK
[D][05:19:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 0,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 1,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 2,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 3,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 4,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 5,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 6,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 7,volt = 17
[D][05:19:57][FCTY]get_ext_48v_vol retry i = 8,volt = 17


2025-07-31 23:24:58:205 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:24:59:209 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:24:59:254 ==>> CurrentBattery:ƽ��:67.49

2025-07-31 23:24:59:721 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:24:59:731 ==>> 【检测小电池关机电流】通过,【67.49uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:25:00:081 ==>> MES过站成功
2025-07-31 23:25:00:094 ==>> #################### 【测试结束】 ####################
2025-07-31 23:25:00:112 ==>> 关闭5V供电
2025-07-31 23:25:00:128 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:25:00:224 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:25:01:120 ==>> 关闭5V供电成功
2025-07-31 23:25:01:135 ==>> 关闭33V供电
2025-07-31 23:25:01:159 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:25:01:227 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:25:02:131 ==>> 关闭33V供电成功
2025-07-31 23:25:02:148 ==>> 关闭3.7V供电
2025-07-31 23:25:02:172 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:25:02:223 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:25:02:992 ==>>  

2025-07-31 23:25:03:142 ==>> 关闭3.7V供电成功
