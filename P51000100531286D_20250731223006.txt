2025-07-31 22:30:06:184 ==>> MES查站成功:
查站序号:P51000100531286D验证通过
2025-07-31 22:30:06:188 ==>> 扫码结果:P51000100531286D
2025-07-31 22:30:06:190 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:30:06:192 ==>> 测试参数版本:2024.10.11
2025-07-31 22:30:06:193 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:30:06:194 ==>> 检测【打开透传】
2025-07-31 22:30:06:196 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:30:06:296 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:30:06:553 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:30:06:557 ==>> 检测【检测接地电压】
2025-07-31 22:30:06:558 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:30:06:703 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:30:06:850 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:30:06:852 ==>> 检测【打开小电池】
2025-07-31 22:30:06:854 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:30:06:898 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:30:07:139 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:30:07:141 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:30:07:145 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:30:07:203 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:30:07:463 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:30:07:465 ==>> 检测【等待设备启动】
2025-07-31 22:30:07:467 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:30:07:676 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 22:30:07:871 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 22:30:08:493 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:30:08:553 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 22:30:08:947 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 22:30:09:417 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 22:30:09:550 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 22:30:09:552 ==>> 检测【产品通信】
2025-07-31 22:30:09:555 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 22:30:09:692 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 22:30:09:831 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 22:30:09:833 ==>> 检测【初始化完成检测】
2025-07-31 22:30:09:836 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 22:30:10:038 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 22:30:10:082 ==>>                                                              

2025-07-31 22:30:10:100 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 22:30:10:103 ==>> 检测【关闭大灯控制1】
2025-07-31 22:30:10:105 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:30:10:265 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:30:10:374 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:30:10:377 ==>> 检测【打开仪表指令模式1】
2025-07-31 22:30:10:378 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:30:10:662 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:30:10:907 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:30:10:910 ==>> 检测【关闭仪表供电】
2025-07-31 22:30:10:911 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:30:11:101 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 22:30:11:180 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:30:11:206 ==>> 检测【关闭AccKey2供电1】
2025-07-31 22:30:11:209 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:30:11:358 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:30:11:456 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:30:11:458 ==>> 检测【关闭AccKey1供电1】
2025-07-31 22:30:11:459 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 22:30:11:508 ==>> [D][05:17:52][COMM]3640 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:11:659 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 22:30:11:737 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 22:30:11:741 ==>> 检测【关闭转刹把供电1】
2025-07-31 22:30:11:743 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:11:886 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:30:12:011 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:30:12:013 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 22:30:12:015 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:30:12:097 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:30:12:187 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:53][COMM]read battery soc:255


2025-07-31 22:30:12:288 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:30:12:296 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 22:30:12:298 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 22:30:12:398 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 22:30:12:503 ==>> [D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][COMM]imu_task imu

2025-07-31 22:30:12:533 ==>>  work error:[-1]. goto init


2025-07-31 22:30:12:570 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 22:30:12:573 ==>> 该项需要延时执行
2025-07-31 22:30:13:048 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5006. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010

2025-07-31 22:30:13:078 ==>> 


2025-07-31 22:30:13:533 ==>> [D][05:17:54][COMM]5663 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:13:792 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:30:14:292 ==>>                                                          ... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main 

2025-07-31 22:30:14:397 ==>> Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[

2025-07-31 22:30:14:502 ==>> D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove 

2025-07-31 22:30:14:592 ==>> success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 22:30:14:698 ==>> [D][05:17:55]

2025-07-31 22:30:14:728 ==>> [CAT1]power_urc_cb ret[5]


2025-07-31 22:30:15:548 ==>> [D][05:17:56][COMM]7685 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:16:196 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 22:30:16:561 ==>> [D][05:17:57][COMM]8698 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:16:578 ==>> 此处延时了:【4000】毫秒
2025-07-31 22:30:16:580 ==>> 检测【33V输入电压ADC】
2025-07-31 22:30:16:583 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:16:912 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:17:57][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:57][COMM]adc read left brake adc:14  volt:18 mv
[D][05:17:57][COMM]adc read right brake adc:5  volt:6 mv
[D][05:17:57][COMM]adc read throttle adc:3  volt:3 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 22:30:17:159 ==>> 【33V输入电压ADC】通过,【32501mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 22:30:17:162 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 22:30:17:164 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:30:17:311 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2780mV
Get AD_V6 1992mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:30:17:443 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:17:446 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:30:17:472 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:17:474 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:30:17:477 ==>> 原始值:【2780】, 乘以分压基数【2】还原值:【5560】
2025-07-31 22:30:17:501 ==>> 【TP68_VCC5V5(ADV5)】通过,【5560mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:30:17:503 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:30:17:530 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:30:17:533 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:30:17:564 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:30:17:567 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:30:17:585 ==>> [D][05:17:58][COMM]9710 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:17:690 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2780mV
Get AD_V6 1988mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:30:17:873 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:17:878 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:30:17:933 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10014. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10015. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10016
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10016


2025-07-31 22:30:17:938 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:17:940 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:30:17:942 ==>> 原始值:【2780】, 乘以分压基数【2】还原值:【5560】
2025-07-31 22:30:17:979 ==>> 【TP68_VCC5V5(ADV5)】通过,【5560mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:30:17:982 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:30:18:008 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:30:18:039 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:30:18:045 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:30:18:047 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:30:18:116 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2780mV
Get AD_V6 1991mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:30:18:206 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 22:30:18:322 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:18:326 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 22:30:18:343 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:18:346 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 22:30:18:350 ==>> 原始值:【2780】, 乘以分压基数【2】还原值:【5560】
2025-07-31 22:30:18:372 ==>> 【TP68_VCC5V5(ADV5)】通过,【5560mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:30:18:374 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 22:30:18:401 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 22:30:18:424 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 22:30:18:428 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 22:30:18:471 ==>> 【TP1_VCC12V(ADV7)】通过,【1091mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 22:30:18:473 ==>> 检测【打开WIFI(1)】
2025-07-31 22:30:18:475 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:30:18:800 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10722 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG

2025-07-31 22:30:18:845 ==>> =200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 22:30:19:004 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:30:19:007 ==>> 检测【清空消息队列(1)】
2025-07-31 22:30:19:008 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:30:19:259 ==>>                                                                                                                                                               > AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088008885

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541617

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[

2025-07-31 22:30:19:289 ==>> D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:30:19:540 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:30:19:543 ==>> 检测【打开GPS(1)】
2025-07-31 22:30:19:544 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 22:30:19:577 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 22:30:19:682 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 22:30:19:820 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 22:30:19:823 ==>> 检测【打开GSM联网】
2025-07-31 22:30:19:825 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 22:30:19:985 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 22:30:20:093 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 22:30:20:096 ==>> 检测【打开仪表供电1】
2025-07-31 22:30:20:098 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 22:30:20:319 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:30:20:368 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 22:30:20:371 ==>> 检测【打开仪表指令模式2】
2025-07-31 22:30:20:373 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 22:30:20:594 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 22:30:20:642 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 22:30:20:645 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 22:30:20:646 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 22:30:20:789 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33270]


2025-07-31 22:30:20:912 ==>> 【读取主控ADC采集的仪表电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:30:20:915 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 22:30:20:917 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:30:21:093 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:30:21:186 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 22:30:21:189 ==>> 检测【AD_V20电压】
2025-07-31 22:30:21:192 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 22:30:21:288 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 22:30:21:423 ==>> 本次取值间隔时间:133ms
2025-07-31 22:30:21:438 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 22:30:21:635 ==>> [D][05:18:02][COMM]13736 imu init OK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 22:30:21:802 ==>> 本次取值间隔时间:370ms
2025-07-31 22:30:21:821 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:21:824 ==>> 检测【拉低OUTPUT2】
2025-07-31 22:30:21:828 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 22:30:21:909 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 22:30:22:100 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 22:30:22:103 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 22:30:22:108 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:30:22:167 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 22:30:22:437 ==>>                                                                                                              ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F373F373F3F3F3207

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][COMM]read batte

2025-07-31 22:30:22:512 ==>> ry soc:255
[D][05:18:03][CAT1]<<< 
+CSQ: 29,99

OK

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.119.37.233"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 22:30:22:685 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 22:30:22:689 ==>> 检测【拉高OUTPUT2】
2025-07-31 22:30:22:691 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 22:30:22:862 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 22:30:22:955 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 22:30:22:959 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 22:30:22:962 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 22:30:23:216 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:30:23:491 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 22:30:23:495 ==>> 检测【预留IO LED功能输出】
2025-07-31 22:30:23:520 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 22:30:23:549 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 22:30:23:698 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 22:30:23:762 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 22:30:23:765 ==>> 检测【AD_V21电压】
2025-07-31 22:30:23:768 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 22:30:23:908 ==>> 1A A1 20 00 00 
Get AD_V21 1643mV
OVER 150


2025-07-31 22:30:24:183 ==>> 本次取值间隔时间:416ms
2025-07-31 22:30:24:202 ==>> 【AD_V21电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:24:204 ==>> 检测【关闭仪表供电2】
2025-07-31 22:30:24:208 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:30:24:470 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:05][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

[D][05:18:05][CAT1]<<< 
OK

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,40,,,41,59,,,41,60,,,41,41,,,41,1*76

$GBGSV,3,2,09,39,,,40,33,,,39,34,,,39,24,,,38,1*7A

$GBGSV,3,3,09,25,,,20,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1566.376,1566.376,50.276,2097152,2097152,2097152*49

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 22:30:24:605 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 22:30:24:728 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:30:24:731 ==>> 检测【关闭仪表指令模式】
2025-07-31 22:30:24:736 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 22:30:24:895 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 22:30:25:008 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 22:30:25:012 ==>> 检测【打开AccKey2供电】
2025-07-31 22:30:25:016 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 22:30:25:171 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 22:30:25:280 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 22:30:25:284 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 22:30:25:290 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:25:381 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,1,,,43,40,,,42,59,,,41,60,,,41,1*4A

$GBGSV,5,2,17,41,,,41,39,,,41,34,,,40,25,,,40,1*78

$GBGSV,5,3,17,33,,,39,24,,,39,7,,,39,11,,,38,1*46

$GBGSV,5,4,17,16,,,37,12,,,36,44,,,36,43,,,44,1*76

$GBGSV,5,5,17,3,,,39,1*49

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1638.937,1638.937,52.381,2097152,2097152,2097152*42



2025-07-31 22:30:25:607 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:06][COMM]adc read out 24v adc:1322  volt:33437 mv
[D][05:18:06][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:06][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:06][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:06][COMM]adc read battery ts volt:14 mv
[D][05:18:06][COMM]adc read in 24v adc:1292  volt:32678 mv
[D][05:18:06][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:30:25:834 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33437mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:30:25:838 ==>> 检测【关闭AccKey2供电2】
2025-07-31 22:30:25:843 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:30:25:971 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:30:26:154 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:30:26:158 ==>> 该项需要延时执行
2025-07-31 22:30:26:378 ==>> [D][05:18:07][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,40,,,42,59,,,41,60,,,41,41,,,41,1*78

$GBGSV,5,2,20,39,,,41,34,,,41,25,,,41,3,,,41,1*4A

$GBGSV,5,3,20,1,,,40,33,,,39,24,,,39,7,,,39,1*7C

$GBGSV,5,4,20,43,,,38,11,,,38,16,,,38,12,,,36,1*78

$GBGSV,5,5,20,44,,,36,2,,,36,4,,,35,5,,,33,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.556,1608.556,51.432,2097152,2097152,2097152*4E



2025-07-31 22:30:27:430 ==>> $GBGGA,143031.224,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,59,,,41,60,,,41,41,,,41,1*7F

$GBGSV,7,2,25,39,,,41,34,,,41,25,,,41,3,,,41,1*4D

$GBGSV,7,3,25,1,,,39,33,,,39,24,,,39,7,,,39,1*75

$GBGSV,7,4,25,11,,,38,16,,,38,43,,,37,10,,,37,1*73

$GBGSV,7,5,25,12,,,36,44,,,36,2,,,36,23,,,36,1*43

$GBGSV,7,6,25,9,,,36,4,,,34,32,,,32,5,,,32,1*4B

$GBGSV,7,7,25,6,,,37,1*43

$GBRMC,143031.224,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143031.224,0.000,787.093,787.093,719.813,2097152,2097152,2097152*64



2025-07-31 22:30:27:733 ==>> $GBGGA,143031.524,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,3,,,41,39,,,41,59,,,41,1*45

$GBGSV,7,2,25,34,,,41,25,,,41,41,,,41,60,,,40,1*76

$GBGSV,7,3,25,7,,,39,1,,,39,33,,,39,24,,,38,1*74

$GBGSV,7,4,25,16,,,38,11,,,38,10,,,37,6,,,37,1*42

$GBGSV,7,5,25,43,,,37,2,,,36,9,,,36,12,,,36,1*7D

$GBGSV,7,6,25,23,,,36,44,,,35,4,,,34,5,,,32,1*75

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,143031.524,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143031.524,0.000,783.746,783.746,716.752,2097152,2097152,2097152*66



2025-07-31 22:30:28:268 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 22:30:28:708 ==>> $GBGGA,143032.504,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,40,,,41,60,,,41,59,,,41,34,,,41,1*7D

$GBGSV,7,2,26,41,,,41,3,,,40,39,,,40,25,,,40,1*4D

$GBGSV,7,3,26,7,,,39,1,,,39,33,,,39,24,,,38,1*77

$GBGSV,7,4,26,16,,,38,11,,,38,10,,,37,43,,,37,1*70

$GBGSV,7,5,26,2,,,36,6,,,36,44,,,36,12,,,36,1*77

$GBGSV,7,6,26,23,,,36,9,,,35,4,,,34,5,,,32,1*4F

$GBGSV,7,7,26,32,,,32,38,,,29,1*72

$GBRMC,143032.504,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143032.504,0.000,773.514,773.514,707.396,2097152,2097152,2097152*6B



2025-07-31 22:30:29:160 ==>> 此处延时了:【3000】毫秒
2025-07-31 22:30:29:165 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 22:30:29:168 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:29:525 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:10][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:10][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:10][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:10][COMM]adc read battery ts volt:12 mv
[D][05:18:10][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:10][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 22:30:29:698 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 22:30:29:702 ==>> 检测【打开AccKey1供电】
2025-07-31 22:30:29:707 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 22:30:29:723 ==>> $GBGGA,143033.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,3,,,41,59,,,41,34,,,41,1*49

$GBGSV,7,2,27,41,,,41,60,,,40,39,,,40,25,,,40,1*79

$GBGSV,7,3,27,7,,,39,1,,,39,33,,,39,24,,,38,1*76

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,43,,,37,1*71

$GBGSV,7,5,27,2,,,36,6,,,36,44,,,36,12,,,36,1*76

$GBGSV,7,6,27,23,,,36,9,,,35,4,,,34,5,,,32,1*4E

$GBGSV,7,7,27,32,,,31,38,,,29,14,,,39,1*7F

$GBRMC,143033.504,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143033.504,0.000,772.721,772.721,706.672,2097152,2097152,2097152*64



2025-07-31 22:30:29:885 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 22:30:29:989 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 22:30:29:992 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 22:30:29:995 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:30:30:110 ==>> 1A A1 00 40 00 
Get AD_V14 2675mV
OVER 150


2025-07-31 22:30:30:246 ==>> 原始值:【2675】, 乘以分压基数【2】还原值:【5350】
2025-07-31 22:30:30:276 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 22:30:30:279 ==>> 【读取AccKey1电压(ADV14)前】通过,【5350mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:30:30:282 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 22:30:30:285 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:30:607 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:11][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:11][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:11][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:11][COMM]adc read battery ts volt:13 mv
[D][05:18:11][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:11][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:30:30:712 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         2097152,2097152*66



2025-07-31 22:30:30:806 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5563mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 22:30:30:811 ==>> 检测【关闭AccKey1供电2】
2025-07-31 22:30:30:834 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 22:30:30:984 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 22:30:31:082 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 22:30:31:088 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 22:30:31:109 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 22:30:31:200 ==>> 1A A1 00 40 00 
Get AD_V14 2673mV
OVER 150


2025-07-31 22:30:31:336 ==>> 原始值:【2673】, 乘以分压基数【2】还原值:【5346】
2025-07-31 22:30:31:370 ==>> 【读取AccKey1电压(ADV14)后】通过,【5346mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 22:30:31:374 ==>> 检测【打开WIFI(2)】
2025-07-31 22:30:31:379 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:30:31:730 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6
$GBGGA,143035.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,60,,,41,3,,,41,59,,,41,1*48

$GBGSV,7,2,27,39,,,41,34,,,41,25,,,41,41,,,41,1*79

$GBGSV,7,3,27,7,,,39,33,,,39,24,,,38,1,,,38,1*77

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,43,,,37,1*71

$GBGSV,7,5,27,23,,,37,2,,,36,6,,,36,9,,,36,1*4C

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,32,1*76

$GBGSV,7,7,27,14,,,32,32,,,31,38,,,30,1*7C

$GBRMC,143035.504,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143035.504,0.000,772.460,772.460,706.434,2097152,2097152,2097152*62



2025-07-31 22:30:31:908 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:30:31:912 ==>> 检测【转刹把供电】
2025-07-31 22:30:31:915 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:30:32:082 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:30:32:190 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 22:30:32:198 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 22:30:32:220 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:30:32:277 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 22:30:32:292 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:30:32:367 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 22:30:32:397 ==>> 1A A1 00 80 00 
Get AD_V15 2416mV
OVER 150


2025-07-31 22:30:32:457 ==>> 原始值:【2416】, 乘以分压基数【2】还原值:【4832】
2025-07-31 22:30:32:486 ==>> 【读取AD_V15电压(前)】通过,【4832mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:30:32:490 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 22:30:32:492 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 22:30:32:592 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:30:32:742 ==>> $GBGGA,143036.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,59,,,41,39,,,41,1*47

$GBGSV,7,2,27,34,,,41,25,,,41,41,,,41,60,,,40,1*74

$GBGSV,7,3,27,7,,,39,24,,,39,33,,,39,1,,,38,1*76

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,43,,,37,1*71

$GBGSV,7,5,27,23,,,37,2,,,36,6,,,36,9,,,36,1*4C

$GBGSV,7,6,27,12,,,36,44,,,35,4,,,34,5,,,33,1*74

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143036.504,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143036.504,0.000,774.751,774.751,708.528,2097152,2097152,2097152*63

[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2445mV
OVER 150


2025-07-31 22:30:32:757 ==>> 原始值:【2445】, 乘以分压基数【2】还原值:【4890】
2025-07-31 22:30:32:781 ==>> 【读取AD_V16电压(前)】通过,【4890mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 22:30:32:784 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 22:30:32:804 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:33:140 ==>> +WIFISCAN:4,0,CC057790A620,-64
+WIFISCAN:4,1,74C330CCAB10,-70
+WIFISCAN:4,2,44A1917CA62B,-74
+WIFISCAN:4,3,CC057790A5C0,-77

[D][05:18:14][CAT1]wifi scan report total[4]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:18:14][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:14][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:14][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:14][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:14][COMM]adc read battery ts volt:12 mv
[D][05:18:14][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3104  volt:5456 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:30:33:370 ==>> 【转刹把供电电压(主控ADC)】通过,【5456mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 22:30:33:373 ==>> 检测【转刹把供电电压】
2025-07-31 22:30:33:380 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:33:782 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:14][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:14][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read right brake adc:17  volt:22 mv
[D][05:18:14][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:14][COMM]adc read battery ts volt:14 mv
[D][05:18:14][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3103  volt:5454 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
$GBGGA,143037.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,59,,,41,39,,,41,1*47

$GBGSV,7,2,27,34,,,41,25,,,41,41,,,41,7,,,40,1*45

$GBGSV,7,3,27,60,,,40,24,,,39,1,,,39,33,,,39,1*48

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,43,,,37,1*71

$GBGSV,7,5,27,23,,,37,2,,,36,6,,,36,44,,,36,1*75

$GBGSV,7,6,27,9,,,36,12,,,36,4,,,34,5,,,33,1*4E

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143037.504,V,,,,,,,310725,0.1,E,N,V*

2025-07-31 22:30:33:843 ==>> 4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143037.504,0.000,777.050,777.050,710.630,2097152,2097152,2097152*61

[D][05:18:14][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][GNSS]recv submsg id[3]
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:30:33:939 ==>> 【转刹把供电电压】通过,【5454mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 22:30:33:943 ==>> 检测【关闭转刹把供电2】
2025-07-31 22:30:33:948 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:34:070 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:30:34:222 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 22:30:34:228 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 22:30:34:234 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:34:284 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 22:30:34:329 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 22:30:34:359 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 22:30:34:404 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 22:30:34:463 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:30:34:467 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 22:30:34:472 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:34:569 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:30:34:678 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:34:707 ==>> $GBGGA,143038.504,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,3,,,41,59,,,41,39,,,41,1*47

$GBGSV,7,2,27,34,,,41,25,,,41,41,,,41,60,,,40,1*74

$GBGSV,7,3,27,7,,,39,24,,,39,33,,,39,1,,,38,1*76

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,2,,,37,6,,,37,23,,,37,9,,,36,1*4C

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,33,1*77

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143038.504,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143038.504,0.000,777.813,777.813,711.328,2097152,2097152,2097152*63



2025-07-31 22:30:34:782 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:30:34:888 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 22:30:34:903 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:16][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:30:34:994 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 22:30:35:099 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 22:30:35:141 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:30:35:145 ==>> 检测【拉高OUTPUT3】
2025-07-31 22:30:35:150 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 22:30:35:204 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 22:30:35:444 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 22:30:35:450 ==>> 检测【拉高OUTPUT4】
2025-07-31 22:30:35:455 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 22:30:35:509 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 22:30:35:719 ==>> $GBGGA,143039.504,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,3,,,41,59,,,41,1*4B

$GBGSV,7,2,27,39,,,41,34,,,41,25,,,41,41,,,41,1*79

$GBGSV,7,3,27,7,,,40,24,,,39,1,,,39,33,,,39,1*79

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,6,,,37,23,,,37,2,,,36,9,,,36,1*4D

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,33,1*77

$GBGSV,7,7,27,38,,,32,14,,,32,32,,,32,1*7D

$GBRMC,143039.504,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143039.504,0.000,780.112,780.112,713.430,2097152,2097152,2097152*6E



2025-07-31 22:30:35:723 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 22:30:35:727 ==>> 检测【拉高OUTPUT5】
2025-07-31 22:30:35:749 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 22:30:35:809 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 22:30:36:083 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 22:30:36:087 ==>> 检测【左刹电压测试1】
2025-07-31 22:30:36:093 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:36:422 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:17][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:17][COMM]adc read left brake adc:1725  volt:2274 mv
[D][05:18:17][COMM]adc read right brake adc:1718  volt:2264 mv
[D][05:18:17][COMM]adc read throttle adc:1728  volt:2278 mv
[D][05:18:17][COMM]adc read battery ts volt:11 mv
[D][05:18:17][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:17][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:17][COMM]read battery soc:255
[D][05:18:17][COMM]arm_hub adc read led yb adc:1435  volt:33270 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 22:30:36:621 ==>> 【左刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 22:30:36:625 ==>> 检测【右刹电压测试1】
2025-07-31 22:30:36:649 ==>> 【右刹电压测试1】通过,【2264】符合目标值【2250】至【2500】要求!
2025-07-31 22:30:36:656 ==>> 检测【转把电压测试1】
2025-07-31 22:30:36:668 ==>> 【转把电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 22:30:36:674 ==>> 检测【拉低OUTPUT3】
2025-07-31 22:30:36:681 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 22:30:36:722 ==>> $GBGGA,143040.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,42,60,,,41,3,,,41,1*41

$GBGSV,7,2,27,59,,,41,39,,,41,34,,,41,25,,,41,1*70

$GBGSV,7,3,27,7,,,40,24,,,39,1,,,39,33,,,39,1*79

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,6,,,37,23,,,37,2,,,36,9,,,36,1*4D

$GBGSV,7,6,27,44,,,36,12,,,36,5,,,34,4,,,34,1*70

$GBGSV,7,7,27,38,,,32,14,,,32,32,,,32,1*7D

$GBRMC,143040.504,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143040.504,0.000,781.645,781.645,714.831,2097152,2097152,2097152*6A

3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 22:30:36:961 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 22:30:36:965 ==>> 检测【拉低OUTPUT4】
2025-07-31 22:30:36:968 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 22:30:37:102 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 22:30:37:261 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 22:30:37:265 ==>> 检测【拉低OUTPUT5】
2025-07-31 22:30:37:269 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 22:30:37:405 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 22:30:37:554 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 22:30:37:558 ==>> 检测【左刹电压测试2】
2025-07-31 22:30:37:563 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 22:30:37:826 ==>> $GBGGA,143041.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,3,,,41,59,,,41,1*4B

$GBGSV,7,2,27,39,,,41,34,,,41,25,,,41,41,,,41,1*79

$GBGSV,7,3,27,7,,,40,24,,,39,1,,,39,33,,,39,1*79

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,6,,,37,23,,,37,2,,,36,9,,,36,1*4D

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,33,1*77

$GBGSV,7,7,27,32,,,33,14,,,32,38,,,31,1*7F

$GBRMC,143041.504,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143041.504,0.000,780.113,780.113,713.431,2097152,2097152,2097152*60

[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:18][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:18][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:18][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:18][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:18][COMM]adc read battery ts volt:14 mv
[D][05:18:18][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:18][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:18]

2025-07-31 22:30:37:871 ==>> [COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 22:30:38:085 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:30:38:088 ==>> 检测【右刹电压测试2】
2025-07-31 22:30:38:103 ==>> 【右刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 22:30:38:107 ==>> 检测【转把电压测试2】
2025-07-31 22:30:38:134 ==>> 【转把电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 22:30:38:139 ==>> 检测【晶振检测】
2025-07-31 22:30:38:161 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 22:30:38:311 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]
[D][05:18:19][COMM]read battery soc:255


2025-07-31 22:30:38:405 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 22:30:38:409 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 22:30:38:412 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:30:38:506 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1652mV
Get AD_V5 2780mV
Get AD_V6 1989mV
Get AD_V7 1091mV
OVER 150


2025-07-31 22:30:38:676 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 22:30:38:680 ==>> 检测【检测BootVer】
2025-07-31 22:30:38:684 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:30:38:717 ==>> $GBGGA,143042.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,60,,,41,40,,,41,3,,,41,59,,,41,1*48

$GBGSV,7,2,27,39,,,41,34,,,41,25,,,41,41,,,41,1*79

$GBGSV,7,3,27,7,,,40,24,,,39,1,,,39,33,,,39,1*79

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,23,,,37,9,,,36,6,,,36,44,,,36,1*7E

$GBGSV,7,6,27,12,,,36,2,,,35,4,,,34,5,,,33,1*46

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143042.504,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143042.504,0.000,777.051,777.051,710.631,2097152,2097152,2097152*62



2025-07-31 22:30:39:062 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071541617
[D][05:18:20][FCTY]HardwareID  = 867222088008885
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = FAD5CB9330FD
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1291
[D][05:18:20][FCTY]Acckey1 vol = 5547 mv, Acckey2 vol = 151 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20]

2025-07-31 22:30:39:107 ==>> [FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3818 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:30:39:218 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 22:30:39:221 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 22:30:39:227 ==>> 检测【检测固件版本】
2025-07-31 22:30:39:261 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 22:30:39:265 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 22:30:39:279 ==>> 检测【检测蓝牙版本】
2025-07-31 22:30:39:282 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 22:30:39:286 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 22:30:39:289 ==>> 检测【检测MoBikeId】
2025-07-31 22:30:39:322 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 22:30:39:325 ==>> 提取到MoBikeId:9999999999
2025-07-31 22:30:39:340 ==>> 检测【检测蓝牙地址】
2025-07-31 22:30:39:344 ==>> 取到目标值:FAD5CB9330FD
2025-07-31 22:30:39:347 ==>> 【检测蓝牙地址】通过,【FAD5CB9330FD】符合目标值【】要求!
2025-07-31 22:30:39:356 ==>> 提取到蓝牙地址:FAD5CB9330FD
2025-07-31 22:30:39:359 ==>> 检测【BOARD_ID】
2025-07-31 22:30:39:383 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 22:30:39:387 ==>> 检测【检测充电电压】
2025-07-31 22:30:39:412 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 22:30:39:417 ==>> 检测【检测VBUS电压1】
2025-07-31 22:30:39:435 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 22:30:39:439 ==>> 检测【检测充电电流】
2025-07-31 22:30:39:459 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 22:30:39:463 ==>> 检测【检测IMEI】
2025-07-31 22:30:39:468 ==>> 取到目标值:867222088008885
2025-07-31 22:30:39:504 ==>> 【检测IMEI】通过,【867222088008885】符合目标值【】要求!
2025-07-31 22:30:39:508 ==>> 提取到IMEI:867222088008885
2025-07-31 22:30:39:514 ==>> 检测【检测IMSI】
2025-07-31 22:30:39:541 ==>> 取到目标值:460130071541617
2025-07-31 22:30:39:545 ==>> 【检测IMSI】通过,【460130071541617】符合目标值【】要求!
2025-07-31 22:30:39:567 ==>> 提取到IMSI:460130071541617
2025-07-31 22:30:39:570 ==>> 检测【校验网络运营商(移动)】
2025-07-31 22:30:39:573 ==>> 取到目标值:460130071541617
2025-07-31 22:30:39:577 ==>> 【校验网络运营商(移动)】通过,【460130071541617】符合目标值【】要求!
2025-07-31 22:30:39:583 ==>> 检测【打开CAN通信】
2025-07-31 22:30:39:587 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 22:30:39:718 ==>> $GBGGA,143043.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,3,,,41,59,,,41,1*4B

$GBGSV,7,2,27,39,,,41,34,,,41,25,,,41,41,,,41,1*79

$GBGSV,7,3,27,7,,,39,24,,,39,1,,,39,33,,,39,1*77

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,6,,,37,1*40

$GBGSV,7,5,27,43,,,37,23,,,37,2,,,36,9,,,36,1*7C

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,33,1*77

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143043.504,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143043.504,0.000,777.816,777.816,711.331,2097152,2097152,2097152*67

[C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 22:30:39:842 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:30:39:847 ==>> 检测【检测CAN通信】
2025-07-31 22:30:39:855 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 22:30:39:928 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:30:39:988 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32115
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:30:40:048 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:30:40:108 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:30:40:122 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 22:30:40:127 ==>> 检测【关闭CAN通信】
2025-07-31 22:30:40:134 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 22:30:40:168 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 22:30:40:199 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 22:30:40:304 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 22:30:40:397 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 22:30:40:404 ==>> 检测【打印IMU STATE】
2025-07-31 22:30:40:426 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:30:40:734 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:1
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,143044.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,41,,,42,60,,,41,3,,,41,1*41

$GBGSV,7,2,27,59,,,41,39,,,41,34,,,41,25,,,41,1*70

$GBGSV,7,3,27,7,,,40,24,,,39,1,,,39,16,,,39,1*7E

$GBGSV,7,4,27,33,,,39,11,,,38,43,,,38,10,,,37,1*78

$GBGSV,7,5,27,6,,,37,23,,,37,2,,,36,9,,,36,1*4D

$GBGSV,7,6,27,44,,,36,12,,,36,4,,,34,5,,,33,1*77

$GBGSV,7,7,27,14,,,32,32,,,32,38,,,31,1*7E

$GBRMC,143044.504,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143044.504,0.000,780.885,780.885,714.137,2097152,2097152,2097152*61



2025-07-31 22:30:40:942 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:30:40:949 ==>> 检测【六轴自检】
2025-07-31 22:30:40:967 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 22:30:41:087 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 22:30:41:718 ==>> $GBGGA,143045.504,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,42,60,,,41,59,,,41,39,,,41,1*72

$GBGSV,7,2,27,34,,,41,25,,,41,41,,,41,3,,,40,1*41

$GBGSV,7,3,27,7,,,39,24,,,39,1,,,39,33,,,39,1*77

$GBGSV,7,4,27,16,,,38,11,,,38,43,,,38,10,,,37,1*7E

$GBGSV,7,5,27,6,,,37,23,,,37,2,,,36,9,,,36,1*4D

$GBGSV,7,6,27,44,,,36,12,,,36,5,,,34,4,,,34,1*70

$GBGSV,7,7,27,38,,,32,14,,,32,32,,,32,1*7D

$GBRMC,143045.504,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143045.504,0.000,779.341,779.341,712.724,2097152,2097152,2097152*62



2025-07-31 22:30:42:326 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 22:30:42:709 ==>> $GBGGA,143046.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,40,,,41,59,,,41,34,,,41,25,,,41,1*7D

$GBGSV,7,2,27,41,,,41,60,,,40,3,,,40,39,,,40,1*4D

$GBGSV,7,3,27,7,,,39,24,,,39,1,,,39,33,,,39,1*77

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,6,,,37,1*40

$GBGSV,7,5,27,43,,,37,23,,,37,2,,,36,9,,,36,1*7C

$GBGSV,7,6,27,44,,,36,12,,,36,5,,,34,4,,,33,1*77

$GBGSV,7,7,27,38,,,32,14,,,32,32,,,32,1*7D

$GBRMC,143046.504,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143046.504,0.000,775.506,775.506,709.217,2097152,2097152,2097152*6E



2025-07-31 22:30:42:800 ==>> [D][05:18:23][CAT1]<<< 
OK

[D][05:18:23][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:30:42:967 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35070 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-17,-15,4046]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 22:30:43:063 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 22:30:43:071 ==>> 检测【打印IMU STATE2】
2025-07-31 22:30:43:105 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 22:30:43:302 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:1
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 22:30:43:356 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 22:30:43:360 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 22:30:43:366 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:30:43:500 ==>> 5A A5 02 5A A5 


2025-07-31 22:30:43:606 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:30:43:652 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:30:43:657 ==>> 检测【检测VBUS电压2】
2025-07-31 22:30:43:662 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:30:43:968 ==>> $GBGGA,143047.504,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,60,,,41,40,,,41,59,,,41,39,,,41,1*71

$GBGSV,7,2,27,34,,,41,25,,,41,41,,,41,7,,,40,1*45

$GBGSV,7,3,27,3,,,40,24,,,39,33,,,39,1,,,38,1*7C

$GBGSV,7,4,27,16,,,38,11,,,38,10,,,37,6,,,37,1*40

$GBGSV,7,5,27,43,,,37,23,,,37,2,,,36,9,,,36,1*7C

$GBGSV,7,6,27,44,,,36,12,,,36,5,,,34,4,,,33,1*77

$GBGSV,7,7,27,38,,,32,14,,,32,32,,,32,1*7D

$GBRMC,143047.504,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,143047.504,0.000,777.043,777.043,710.623,2097152,2097152,2097152*64

[D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[W][05:18:24][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:24][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:24][FCTY]======

2025-07-31 22:30:44:074 ==>> ====Modules-nRF5340 ==========
[D][05:18:24][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:24][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:24][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:24][FCTY]DeviceID    = 460130071541617
[D][05:18:24][FCTY]HardwareID  = 867222088008885
[D][05:18:24][FCTY]MoBikeID    = 9999999999
[D][05:18:24][FCTY]LockID      = FFFFFFFFFF
[D][05:18:24][FCTY]BLEFWVersion= 105
[D][05:18:24][FCTY]BLEMacAddr   = FAD5CB9330FD
[D][05:18:24][FCTY]Bat         = 3944 mv
[D][05:18:24][FCTY]Current     = 0 ma
[D][05:18:24][FCTY]VBUS        = 11800 mv
[D][05:18:24][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:24][FCTY]Ext battery vol = 9, adc = 391
[D][05:18:24][FCTY]Acckey1 vol = 5549 mv, Acckey2 vol = 25 mv
[D][05:18:24][FCTY]Bike Type flag is invalied
[D][05:18:24][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:24][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:24][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:24][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:24][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:24][FCTY]Bat1         = 3818 mv
[D][05:18:24][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:1

2025-07-31 22:30:44:119 ==>> 8:24][FCTY]==========Modules-nRF5340 ==========
                                                                       

2025-07-31 22:30:44:183 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:30:44:571 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071541617
[D][05:18:25][FCTY]HardwareID  = 867222088008885
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = FAD5CB9330FD
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 4, adc = 177
[D][05:18:25][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 25 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3818 mv
[D][05:18:25][FCTY]========

2025-07-31 22:30:44:601 ==>> ============ E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:30:44:710 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:30:45:858 ==>> $GBGGA,143044.511,2301.2569807,N,11421.9430536,E,1,13,0.80,77.414,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,16,25,34,41,11,33,12,24,23,1.71,0.80,1.52,4*0E

$GBGSA,A,3,44,,,,,,,,,,,,1.71,0.80,1.52,4*01

$GBGSV,7,1,27,40,64,170,41,6,62,74,37,7,61,195,40,3,60,190,41,1*74

$GBGSV,7,2,27,39,59,33,41,16,57,8,38,59,52,129,41,25,50,350,41,1*46

$GBGSV,7,3,27,1,48,125,39,10,48,222,37,9,45,332,36,2,45,237,36,1*42

$GBGSV,7,4,27,34,43,109,41,41,42,269,41,60,41,238,41,11,36,140,38,1*72

$GBGSV,7,5,27,33,34,201,39,4,32,111,33,12,31,75,36,24,26,60,39,1*43

$GBGSV,7,6,27,23,23,294,37,5,21,256,34,44,17,53,36,14,11,322,32,1*7B

$GBGSV,7,7,27,38,5,192,32,43,,,38,32,,,32,1*7A

$GBRMC,143044.511,A,2301.2569807,N,11421.9430536,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

[D][05:18:25][GNSS]HD8040 GPS
[D][05:18:25][GNSS]GPS diff_sec 124017139, report 0x42 frame
$GBGST,143044.511,0.683,0.204,0.211,0.311,2.269,2.037,8.004*79

[D][05:18:25][COMM]Main Task receive event:131
[D][05:18:25][COMM]main task tmp_sleep_event = 80
[D][05:18:25][COMM]index:0,power_mode:0xFF
[D][05:18:25][COMM]index:1,sound_mode:0xFF
[D][0

2025-07-31 22:30:45:963 ==>> 5:18:25][COMM]index:2,gsensor_mode:0xFF
[D][05:18:25][COMM]index:3,report_freq_mode:0xFF
[D][05:18:25][COMM]index:4,report_period:0xFF
[D][05:18:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:25][COMM]index:6,normal_reset_period:0xFF
[D][05:18:25][COMM]index:7,spock_over_speed:0xFF
[D][05:18:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:25][COMM]index:16,imu_config_params:0xFF
[D][05:18:25][COMM]index:17,long_connect_params:0xFF
[D][05:18:25][COMM]index:18,detain_mark:0xFF
[D][05:18:25][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:25][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:25][COMM]index:21,mc_mode:0xFF
[D][05:18:25][COMM]index:22,S_mode:0xFF
[D][05:18:25][COMM]index:23,overweight:0xFF
[D][05:18:25][COMM]index:24,standstill_mode:0xFF
[D][05:18:25][COMM]index:25,night_mode:0x

2025-07-31 22:30:46:068 ==>> FF
[D][05:18:25][COMM]index:26,experiment1:0xFF
[D][05:18:25][COMM]index:27,experiment2:0xFF
[D][05:18:25][COMM]index:28,experiment3:0xFF
[D][05:18:25][COMM]index:29,experiment4:0xFF
[D][05:18:25][COMM]index:30,night_mode_start:0xFF
[D][05:18:25][COMM]index:31,night_mode_end:0xFF
[D][05:18:25][COMM]index:33,park_report_minutes:0xFF
[D][05:18:25][COMM]index:34,park_report_mode:0xFF
[D][05:18:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:25][COMM]index:38,charge_battery_para: FF
[D][05:18:25][COMM]index:39,multirider_mode:0xFF
[D][05:18:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:25][COMM]index:44,riding_duration_config:0xFF
[D][05:18:25][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:25][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:25][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:25][COMM]index:49,mc_load_startup:0xFF
[D][05:18:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:25][COMM]index:51,traffic_audio_play:0xFF
[D

2025-07-31 22:30:46:173 ==>> ][05:18:25][COMM]index:52,traffic_mode:0xFF
[D][05:18:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:25][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:25][COMM]index:63,experiment5:0xFF
[D][05:18:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:25][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:25][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:25][COMM]index:72,experiment6:0xFF
[D][05:18:25][COMM]index:73,experiment7:0xFF
[D][05:18:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:25][COMM]index:75,zero_value_from_server:-1
[D][05:18:25][COMM]index:76,multirider_t

2025-07-31 22:30:46:278 ==>> hreshold:255
[D][05:18:25][COMM]index:77,experiment8:255
[D][05:18:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:25][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:25][COMM]index:83,loc_report_interval:255
[D][05:18:25][COMM]index:84,multirider_threshold_p2:255
[D][05:18:25][COMM]index:85,multirider_strategy:255
[D][05:18:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:25][COMM]index:90,weight_param:0xFF
[D][05:18:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:25][COMM]index:95,current_limit:0xFF
[D][05:18:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:25][COMM]index:100,location_mode:0xFF

[W][05:18:25][PROT]remove success[1629955105],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:25][PROT]add success [1629955105],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:25][COMM]Main Task receive event:131 finished proces

2025-07-31 22:30:46:383 ==>> sing
[D][05:18:25][PROT]index:0 1629955105
[D][05:18:25][PROT]is_send:0
[D][05:18:25][PROT]sequence_num:4
[D][05:18:25][PROT]retry_timeout:0
[D][05:18:25][PROT]retry_times:1
[D][05:18:25][PROT]send_path:0x2
[D][05:18:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:25][PROT]===========================================================
[W][05:18:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955105]
[D][05:18:25][PROT]===========================================================
[D][05:18:25][PROT]sending traceid [9999999999900005]
[D][05:18:25][PROT]Send_TO_M2M [1629955105]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:25][SAL ]sock send credit cnt[6]
[D][05:18:25][SAL ]sock send ind credit cnt[6]
[D][05:18:25][M2M ]m2m send data len[294]
[D][05:18:25][SAL ]Cellular task submsg id[10]
[D][05:18:25][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:25][CAT1]gsm read msg sub id: 15
[D][05:18:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:25][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B56A83C05C6D74F665A1D20A5B595531C

2025-07-31 22:30:46:488 ==>> 574077EE78B3841E4914F0B42DB600C865B4749A7985029CD68A8132D7E17865156BC92276CE07185E7FDCBCDF515660119A9608036B8784FE4A2B4BB0D1889A0D3FF7197D900F4917D5D3E33E31074630E51ECDCA7B2A3124E53714FC317813373790F6673689B3FA4EEDBAC3296AEC8E896
[D][05:18:25][CAT1]<<< 
SEND OK

[D][05:18:25][CAT1]exec over: func id: 15, ret: 11
[D][05:18:25][CAT1]sub id: 15, ret: 11

[D][05:18:25][SAL ]Cellular task submsg id[68]
[D][05:18:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:25][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:25][M2M ]g_m2m_is_idle become true
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:25][PROT]M2M Send ok [1629955105]
[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BL

2025-07-31 22:30:46:716 ==>> [D][05:18:27][GNSS]recv submsg id[1]
[D][05:18:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:27][GNSS]location stop evt done evt


2025-07-31 22:30:47:290 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:30:47:659 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071541617
[D][05:18:28][FCTY]HardwareID  = 867222088008885
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FAD5CB9330FD
[D][05:18:28][FCTY]Bat         = 3804 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 101
[D][05:18:28][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT

2025-07-31 22:30:47:704 ==>> 1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3818 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 22:30:47:828 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 22:30:47:833 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 22:30:47:840 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:30:47:901 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:30:47:961 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 10


2025-07-31 22:30:48:051 ==>> [D][05:18:29][COMM]read battery soc:255


2025-07-31 22:30:48:104 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:30:48:109 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 22:30:48:129 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:30:48:202 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:30:48:407 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 22:30:48:413 ==>> 检测【打开WIFI(3)】
2025-07-31 22:30:48:419 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:30:48:615 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:29][CAT1]gsm read msg sub id: 12
[D][05:18:29][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:30:48:753 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:30:48:769 ==>> 检测【扩展芯片hw】
2025-07-31 22:30:48:776 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 22:30:48:893 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:30][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 22:30:49:100 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 22:30:49:108 ==>> 检测【扩展芯片boot】
2025-07-31 22:30:49:136 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 22:30:49:153 ==>> 检测【扩展芯片sw】
2025-07-31 22:30:49:178 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 22:30:49:182 ==>> 检测【检测音频FLASH】
2025-07-31 22:30:49:187 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 22:30:49:365 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 22:30:49:545 ==>> [D][05:18:30][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:0------------
[D][05:18:30][COMM]------------ready to Power on Acckey 2------------


2025-07-31 22:30:50:452 ==>>              [COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,F42A7D1297A3,-67
+WIFISCAN:4,2,CC057790A5C0,-73
+WIFISCAN:4,3,CC057790A5C1,-78

[D][05:18:30][CAT1]wifi scan report total[4]
[D][05:18:30][COMM]more than the number of battery plugs
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:30][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:30][COMM]Bat auth off fail, error:-1
[D][05:18:30][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:30][COMM]----- get Acckey 1 and value:1------------
[D][05:18:30][COMM]----- get Acckey 2 and value:1------------
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:30][COMM]file:B50 exist
[D][05:18:30][COMM][Audio].l

2025-07-31 22:30:50:557 ==>> :[255]. success, file_name:B50, size:10800
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:30][COMM]read file, len:10800, num:3
[D][05:18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:

2025-07-31 22:30:50:662 ==>> 30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:30][PROT]index:2
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:6
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]====================================================

2025-07-31 22:30:50:767 ==>> =======
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900007]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get

2025-07-31 22:30:50:872 ==>>  bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][GNSS]recv submsg id[3]
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:

2025-07-31 22:30:50:977 ==>> 2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:31][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:31][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:31][PROT]CLEAN,SEND:0
[D][05:18:31][PROT]index:2 1629955111
[D][05:18:31][PROT]is_send:0
[D][05:18:31][PROT]sequence_num:6
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x2
[D][05:18:31][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]sending traceid [999999999

2025-07-31 22:30:51:067 ==>> 9900007]
[D][05:18:31][PROT]Send_TO_M2M [1629955111]
[D][05:18:31][PROT]CLEAN:0
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:31][SAL ]sock send credit cnt[6]
[D][05:18:31][SAL ]sock send ind credit cnt[6]
[D][05:18:31][M2M ]m2m send data len[198]
[D][05:18:31][SAL ]Cellular task submsg id[10]
[D][05:18:31][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:31][CAT1]gsm read msg sub id: 15
[D][05:18:31][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:31][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88BE0A8432605A29777EAD3CC25EEFE38333A0BAC487A716D4AF2F96A06B58E0262CE637BFE6949B301D4199167287A8D335A9D6723D139C57F885F1E052823402D32E791685013F6BFBF7A85AE5E1B65E131279
[D][05:18

2025-07-31 22:30:52:057 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 22:30:52:591 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:53:467 ==>> [D][05:18:34][COMM]crc 108B
[D][05:18:34][COMM]flash test ok


2025-07-31 22:30:53:740 ==>> [D][05:18:34][COMM]45735 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 22:30:54:070 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 22:30:54:255 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 22:30:54:262 ==>> 检测【打开喇叭声音】
2025-07-31 22:30:54:269 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 22:30:55:006 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:35][COMM]file:A20 exist
[D][05:18:35][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:35][COMM]read file, len:15228, num:4
[D][05:18:35][COMM]--->crc16:0x419c
[D][05:18:35][COMM]read file success
[W][05:18:35][COMM][Audio].l:[936].close hexlog save
[D][05:18:35][COMM]accel parse set 1
[D][05:18:35][COMM][Audio]mon:9,05:18:35
[D][05:18:35][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[

2025-07-31 22:30:55:055 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 22:30:55:082 ==>> 检测【打开大灯控制】
2025-07-31 22:30:55:088 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 22:30:55:112 ==>> 704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:35][COMM]46746 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:35][COMM]f:[ec800m

2025-07-31 22:30:55:216 ==>> _audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 22:30:55:473 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        9999900007]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE5E7AFB6A6AA0F8DC397CB16AF20EE915F70BF0AD1AA4F6617AC8B303B0E9665D05ABAA0872FF214EC42869D2A124BAC47E289EB7A443475CF7B77C9AAFC1B2E

2025-07-31 22:30:55:548 ==>> EFEAD066DB0A1AFDA4C0E59F484DC26A7D6CC
[W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 22:30:55:582 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 22:30:55:588 ==>> 检测【关闭仪表供电3】
2025-07-31 22:30:55:608 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 22:30:55:790 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 22:30:55:852 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 22:30:55:881 ==>> 检测【关闭AccKey2供电3】
2025-07-31 22:30:55:901 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 22:30:55:969 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 22:30:56:060 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 22:30:56:128 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 22:30:56:136 ==>> 检测【读大灯电压】
2025-07-31 22:30:56:141 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:30:56:289 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33154]


2025-07-31 22:30:56:397 ==>> 【读大灯电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 22:30:56:403 ==>> 检测【关闭大灯控制2】
2025-07-31 22:30:56:426 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 22:30:56:580 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 22:30:56:674 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 22:30:56:680 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 22:30:56:689 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 22:30:56:887 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[69]


2025-07-31 22:30:56:950 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 22:30:56:956 ==>> 检测【打开WIFI(4)】
2025-07-31 22:30:56:966 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 22:30:57:115 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 6


2025-07-31 22:30:57:285 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 22:30:57:290 ==>> 检测【EC800M模组版本】
2025-07-31 22:30:57:295 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 22:30:57:514 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:57:619 ==>> [D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 22:30:57:821 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 22:30:57:829 ==>> 检测【配置蓝牙地址】
2025-07-31 22:30:57:854 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 22:30:58:000 ==>> +WIFISCAN:4,0,CC057790A621,-61
+WIFISCAN:4,1,F42A7D1297A3,-70
+WIFISCAN:4,2,CC057790A5C0,-78
+WIFISCAN:4,3,CC057790A5C1,-78

[D][05:18:39][CAT1]wifi scan report total[4]
[W][05:18:39][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 22:30:58:030 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FAD5CB9330FD>】
2025-07-31 22:30:58:090 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 22:30:58:195 ==>> recv ble 1
recv ble 2
ble set mac ok :fa,d5,cb,93,30,fd
enable filters ret : 0

2025-07-31 22:30:58:304 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 22:30:58:309 ==>> 检测【BLETEST】
2025-07-31 22:30:58:331 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 22:30:58:407 ==>> 4A A4 01 A4 4A 


2025-07-31 22:30:58:512 ==>> [D][05:18:39][COMM]50658 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:30:58:617 ==>> recv ble 1
recv ble 2
<BSJ*MAC:FAD5CB9330FD*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FAD5CB9330FD99999

2025-07-31 22:30:58:707 ==>> OVER 150


2025-07-31 22:30:58:767 ==>> [D][05:18:39][GNSS]recv submsg id[3]


2025-07-31 22:30:59:339 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 22:30:59:344 ==>> 该项需要延时执行
2025-07-31 22:30:59:523 ==>> [D][05:18:40][COMM]51669 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 22:31:00:132 ==>> [D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:41][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:41][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:41][COMM]accel parse set 0
[D][05:18:41][COMM][Audio].l:[1012].open hexlog save
[D][05:18:41][COMM]read battery soc:255


2025-07-31 22:31:00:685 ==>> [D][05:18:41][PROT]CLEAN,SEND:2
[D][05:18:41][PROT]index:2 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:6
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:1
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900007]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BE178BCA8BD6FC844E5B5694BD6CA6813D97FA27479

2025-07-31 22:31:00:760 ==>> 64DD4E5D8AB1D7691CDF879F725D5E41417A9966CA0121D7F95FD232D4DAB41624A44E11321C0065B97664299C8453ECC59DE228F1B0238425978DBA262
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][COMM]52680 imu init OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 22:31:02:084 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 22:31:04:090 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 22:31:05:927 ==>> [D][05:18:46][PROT]CLEAN,SEND:2
[D][05:18:46][PROT]CLEAN:2
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:3
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B36D2A81AFB7B545D75A7A3C53A50E2554

2025-07-31 22:31:06:002 ==>> 310987D7237BECE066F5DA9ECDFF2C1BFAB0C4667F71E47E4FE9DDB002A7F2D9F463788124745FDF5278E1DD14F1310E764A2BE97EFF0D973B47733558BAFD80416D
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 22:31:06:107 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 22:31:08:103 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 22:31:09:352 ==>> 此处延时了:【10000】毫秒
2025-07-31 22:31:09:361 ==>> 检测【检测WiFi结果】
2025-07-31 22:31:09:371 ==>> WiFi信号:【CC057790A620】,信号值:-64
2025-07-31 22:31:09:377 ==>> WiFi信号:【74C330CCAB10】,信号值:-70
2025-07-31 22:31:09:398 ==>> WiFi信号:【44A1917CA62B】,信号值:-74
2025-07-31 22:31:09:403 ==>> WiFi信号:【CC057790A5C0】,信号值:-77
2025-07-31 22:31:09:428 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 22:31:09:438 ==>> WiFi信号:【F42A7D1297A3】,信号值:-67
2025-07-31 22:31:09:460 ==>> WiFi信号:【CC057790A5C1】,信号值:-78
2025-07-31 22:31:09:469 ==>> WiFi信号:【CC057790A621】,信号值:-61
2025-07-31 22:31:09:491 ==>> WiFi数量【8】, 最大信号值:-34
2025-07-31 22:31:09:499 ==>> 检测【检测GPS结果】
2025-07-31 22:31:09:523 ==>> 符合定位需求的卫星数量:【22】
2025-07-31 22:31:09:528 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【6】,信号值:【37】
北斗星号:【7】,信号值:【40】
北斗星号:【3】,信号值:【41】
北斗星号:【39】,信号值:【41】
北斗星号:【16】,信号值:【38】
北斗星号:【59】,信号值:【41】
北斗星号:【25】,信号值:【41】
北斗星号:【1】,信号值:【39】
北斗星号:【10】,信号值:【37】
北斗星号:【9】,信号值:【36】
北斗星号:【2】,信号值:【36】
北斗星号:【34】,信号值:【41】
北斗星号:【41】,信号值:【41】
北斗星号:【60】,信号值:【41】
北斗星号:【11】,信号值:【38】
北斗星号:【33】,信号值:【39】
北斗星号:【12】,信号值:【36】
北斗星号:【24】,信号值:【39】
北斗星号:【23】,信号值:【37】
北斗星号:【44】,信号值:【36】
北斗星号:【43】,信号值:【38】

2025-07-31 22:31:09:538 ==>> 检测【CSQ强度】
2025-07-31 22:31:09:566 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 22:31:09:626 ==>> [D][05:18:50][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:50][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:50][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:50][CAT1]exec over: func id: 12, ret: 21


2025-07-31 22:31:09:910 ==>> 【CSQ强度】通过,【26】符合目标值【18】至【31】要求!
2025-07-31 22:31:09:916 ==>> 检测【关闭GSM联网】
2025-07-31 22:31:09:936 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 22:31:10:117 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:51][COMM]GSM test
[D][05:18:51][COMM]GSM test disable
[D][05:18:51][COMM]read battery soc:255


2025-07-31 22:31:10:182 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 22:31:10:188 ==>> 检测【4G联网测试】
2025-07-31 22:31:10:194 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 22:31:11:151 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:51][COMM]Main Task receive event:14
[D][05:18:51][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955131, allstateRepSeconds = 0
[D][05:18:51][COMM]index:0,power_mode:0xFF
[D][05:18:51][COMM]index:1,sound_mode:0xFF
[D][05:18:51][COMM]index:2,gsensor_mode:0xFF
[D][05:18:51][COMM]index:3,report_freq_mode:0xFF
[D][05:18:51][COMM]index:4,report_period:0xFF
[D][05:18:51][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:51][COMM]index:6,normal_reset_period:0xFF
[D][05:18:51][COMM]index:7,spock_over_speed:0xFF
[D][05:18:51][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:51][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:51][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:51][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:51][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:51][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:51][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:51][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:51][COMM]index:16,imu_config_params:0xFF
[D][05:18:51][COMM]index:17,long_connect_params:0xFF
[D][05:18:51][COMM]index:18,detain_mark:0xFF
[D][05:18:51][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:51][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:

2025-07-31 22:31:11:257 ==>> 18:51][COMM]index:21,mc_mode:0xFF
[D][05:18:51][COMM]index:22,S_mode:0xFF
[D][05:18:51][COMM]index:23,overweight:0xFF
[D][05:18:51][COMM]index:24,standstill_mode:0xFF
[D][05:18:51][COMM]index:25,night_mode:0xFF
[D][05:18:51][COMM]index:26,experiment1:0xFF
[D][05:18:51][COMM]index:27,experiment2:0xFF
[D][05:18:51][COMM]index:28,experiment3:0xFF
[D][05:18:51][COMM]index:29,experiment4:0xFF
[D][05:18:51][COMM]index:30,night_mode_start:0xFF
[D][05:18:51][COMM]index:31,night_mode_end:0xFF
[D][05:18:51][COMM]index:33,park_report_minutes:0xFF
[D][05:18:51][COMM]index:34,park_report_mode:0xFF
[D][05:18:51][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:51][COMM]index:38,charge_battery_para: FF
[D][05:18:51][COMM]index:39,multirider_mode:0xFF
[D][05:18:51][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:51][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:51][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:51][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:51][COMM]index:44,riding_duration_config:0xFF
[D][05:18:51][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:51][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:51][COMM]index:47,bat_info_rep_cfg:0xFF
[D]

2025-07-31 22:31:11:363 ==>> [05:18:51][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:51][COMM]index:49,mc_load_startup:0xFF
[D][05:18:51][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:51][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:51][COMM]index:52,traffic_mode:0xFF
[D][05:18:51][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:51][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:51][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:51][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:51][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:51][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:51][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:51][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:51][COMM]index:63,experiment5:0xFF
[D][05:18:51][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:51][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:51][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:51][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:51][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:51][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:51][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:51][COMM]index:72,experiment6:0xFF
[D][0

2025-07-31 22:31:11:468 ==>> 5:18:51][COMM]index:73,experiment7:0xFF
[D][05:18:51][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:51][COMM]index:75,zero_value_from_server:-1
[D][05:18:51][COMM]index:76,multirider_threshold:255
[D][05:18:51][COMM]index:77,experiment8:255
[D][05:18:51][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:51][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:51][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:51][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:51][COMM]index:83,loc_report_interval:255
[D][05:18:51][COMM]index:84,multirider_threshold_p2:255
[D][05:18:51][COMM]index:85,multirider_strategy:255
[D][05:18:51][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:51][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:51][COMM]index:90,weight_param:0xFF
[D][05:18:51][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:51][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:51][COMM]index:95,current_limit:0xFF
[D][05:18:51][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:51][COMM]index:100,location_mode:0xFF

[W][05:18:51][PROT]remove success[1629955131],send_path[2],type[0000],prio

2025-07-31 22:31:11:573 ==>> rity[0],index[0],used[0]
[W][05:18:51][PROT]add success [1629955131],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:51][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:51][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:51][CAT1]gsm read msg sub id: 13
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:8
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:1
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900009]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[294]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1

2025-07-31 22:31:11:678 ==>> ], len[294], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:51][CAT1]exec over: func id: 13, ret: 21
[D][05:18:51][M2M ]get csq[26]
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:51][CAT1]Send Data To Server[294][297] ... ->:
0093B98E113311331133113311331B88B1DBC2F64EA02DC0C36994B133550A0D1DFB2051595C72C1F5F30A7E5DE8A70D551F17D9609D3226F8A89D633239861DBBC3981315D31698750E99091BB4AFC2C16EA8FBFA912825D1B5B9DB8EE386A16104F0E551AF5CD21627D38C0213C47BD70BFBDCF41C7285852194C421323D9DDE92D493A4F68325EA429620DED356AD8A23E6
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]
>>>>>RESEND ALLSTATE<<<<<
[W][05:18:51][PROT]remove success[1629955131],send_path[2],type[000

2025-07-31 22:31:11:753 ==>> 0],priority[0],index[1],used[0]
[W][05:18:51][PROT]add success [1629955131],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:18:51][COMM]------>period, report file manifest
[D][05:18:51][COMM]Main Task receive event:14 finished processing
[D][05:18:51][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:51][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:51][CAT1]gsm read msg sub id: 21
[D][05:18:51][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]cell info report total[0]
[D][05:18:51][CAT1]exec over: func id: 21, ret: 6


2025-07-31 22:31:12:117 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 22:31:12:225 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 22:31:12:232 ==>> 检测【关闭GPS】
2025-07-31 22:31:12:241 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 22:31:12:392 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all continue location stop
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all sing location stop


2025-07-31 22:31:12:502 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 22:31:12:508 ==>> 检测【清空消息队列2】
2025-07-31 22:31:12:532 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 22:31:12:698 ==>> [D][05:18:53][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:53][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:53][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 22:31:12:777 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 22:31:12:783 ==>> 检测【轮动检测】
2025-07-31 22:31:12:789 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 22:31:12:897 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 22:31:12:988 ==>> [D][05:18:54][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 22:31:13:279 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 22:31:13:403 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 22:31:13:566 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 22:31:13:573 ==>> 检测【关闭小电池】
2025-07-31 22:31:13:578 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:31:13:708 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:31:13:868 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 22:31:13:875 ==>> 检测【进入休眠模式】
2025-07-31 22:31:13:881 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:31:14:162 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:18:55][COMM]Main Task receive event:28
[D][05:18:55][COMM]main task tmp_sleep_event = 8
[D][05:18:55][COMM]prepare to sleep
[D][05:18:55][CAT1]gsm read msg sub id: 12
[D][05:18:55][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:18:55][COMM]read battery soc:255


2025-07-31 22:31:14:780 ==>> [D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 12, ret: 6
[D][05:18:55][M2M ]tcpclient close[4]
[D][05:18:55][SAL ]Cellular task submsg id[12]
[D][05:18:55][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:18:55][CAT1]gsm read msg sub id: 9
[D][05:18:55][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:18:55][CAT1]<<< 
OK

[D][05:18:55][CAT1]exec over: func id: 9, ret: 6
[D][05:18:55][CAT1]sub id: 9, ret: 6

[D][05:18:55][SAL ]Cellular task submsg id[68]
[D][05:18:55][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:18:55][SAL ]socket close ind. id[4]
[D][05:18:55][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:18:55][COMM]1x1 frm_can_tp_send ok
[D][05:18:55][CAT1]pdpdeact urc len[22]


2025-07-31 22:31:15:070 ==>> [E][05:18:56][COMM]1x1 rx timeout
[D][05:18:56][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:15:589 ==>> [E][05:18:56][COMM]1x1 rx timeout
[E][05:18:56][COMM]1x1 tp timeout
[E][05:18:56][COMM]1x1 error -3.
[W][05:18:56][COMM]CAN STOP!
[D][05:18:56][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:18:56][COMM]------------ready to Power off Acckey 1------------
[D][05:18:56][COMM]------------ready to Power off Acckey 2------------
[D][05:18:56][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:18:56][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1290
[D][05:18:56][COMM]bat sleep fail, reason:-1
[D][05:18:56][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:18:56][COMM]accel parse set 0
[D][05:18:56][COMM]imu rest ok. 67628
[D][05:18:56][COMM]imu sleep 0
[W][05:18:56][COMM]now sleep


2025-07-31 22:31:15:729 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:31:15:735 ==>> 检测【检测33V休眠电流】
2025-07-31 22:31:15:746 ==>> 开始33V电流采样
2025-07-31 22:31:15:755 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:31:15:834 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 22:31:16:846 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 22:31:16:923 ==>> Current33V:????:17.20

2025-07-31 22:31:17:355 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 22:31:17:361 ==>> 【检测33V休眠电流】通过,【17.2uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:31:17:368 ==>> 该项需要延时执行
2025-07-31 22:31:19:371 ==>> 此处延时了:【2000】毫秒
2025-07-31 22:31:19:380 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 22:31:19:394 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 22:31:19:511 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2769mV
Get AD_V6 1945mV
Get AD_V7 1092mV
OVER 150


2025-07-31 22:31:20:394 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 22:31:20:404 ==>> 检测【打开小电池2】
2025-07-31 22:31:20:425 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:31:20:497 ==>> 6A A6 01 A6 6A 


2025-07-31 22:31:20:602 ==>> Battery ON
OVER 150


2025-07-31 22:31:20:671 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:31:20:677 ==>> 该项需要延时执行
2025-07-31 22:31:21:176 ==>> 此处延时了:【500】毫秒
2025-07-31 22:31:21:183 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 22:31:21:194 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:31:21:299 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:31:21:455 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 22:31:21:467 ==>> 该项需要延时执行
2025-07-31 22:31:21:962 ==>> 此处延时了:【500】毫秒
2025-07-31 22:31:21:975 ==>> 检测【进入休眠模式2】
2025-07-31 22:31:21:987 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 22:31:22:001 ==>> [D][05:19:02][COMM]------------ready to Power on Acckey 1------------
[D][05:19:02][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:02][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:02][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:02][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:02][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:02][COMM]----- get Acckey 1 and value:1------------
[W][05:19:02][COMM]CAN START!
[D][05:19:02][CAT1]gsm read msg sub id: 12
[D][05:19:02][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:02][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 73937
[D][05:19:02][COMM][Audio]exec status ready.
[D][05:19:02][CAT1]<<< 
OK

[D][05:19:02][CAT1]exec over: func id: 12, ret: 6
[D][05:19:02][COMM]imu wakeup ok. 73952
[D][05:19:02][COMM]imu wa

2025-07-31 22:31:22:037 ==>> keup 1
[W][05:19:02][COMM]wake up system, wakeupEvt=0x80
[D][05:19:02][COMM]frm_can_weigth_power_set 1
[D][05:19:02][COMM]Clear Sleep Block Evt
[D][05:19:02][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:02][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:22:127 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 22:31:22:231 ==>> [E][05:19:03][COMM]1x1 rx timeout
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:22:337 ==>>                                                              4431. period:50
[D][05:19:03][COMM]msg 02A4 loss. last_tick:73922. cur_tick:74431. period:50
[D][05:19:03][COMM]msg 02A5 loss. last_tick:73922. cur_tick:74432. period:50
[D][05:19:03][COMM]msg 02A6 loss. last_tick:73922. cur_tick:74432. period:50
[D][05:19:03][COMM]msg 02A7 loss. last_tick:73922. cur_tick:74432. period:50
[D][05:19:03][COMM]CAN message fault change: 0x0000000000000000->0

2025-07-31 22:31:22:367 ==>> x0000E00000220000 74433
[D][05:19:03][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 74433


2025-07-31 22:31:22:702 ==>> [E][05:19:03][COMM]1x1 rx timeout
[E][05:19:03][COMM]1x1 tp timeout
[E][05:19:03][COMM]1x1 error -3.
[D][05:19:03][COMM]Main Task receive event:28 finished processing
[D][05:19:03][COMM]Main Task receive event:28
[D][05:19:03][COMM]prepare to sleep
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 12, ret: 6
[D][05:19:03][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:03][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:23:005 ==>> [D][05:19:03][COMM]msg 0220 loss. last_tick:73922. cur_tick:74927. period:100
[D][05:19:03][COMM]msg 0221 loss. last_tick:73922. cur_tick:74928. period:100
[D][05:19:03][COMM]msg 0224 loss. last_tick:73922. cur_tick:74928. period:100
[D][05:19:03][COMM]msg 0260 loss. last_tick:73922. cur_tick:74928. period:100
[D][05:19:03][COMM]msg 0280 loss. last_tick:73922. cur_tick:74929. period:100
[D][05:19:03][COMM]msg 02C0 loss. last_tick:73922. cur_tick:74929. period:100
[D][05:19:03][COMM]msg 02C1 loss. last_tick:73922. cur_tick:74930. period:100
[D][05:19:03][COMM]msg 02C2 loss. last_tick:73922. cur_tick:74930. period:100
[D][05:19:03][COMM]msg 02E0 loss. last_tick:73922. cur_tick:74930. period:100
[D][05:19:03][COMM]msg 02E1 loss. last_tick:73922. cur_tick:74931. period:100
[D][05:19:03][COMM]msg 02E2 loss. last_tick:73922. cur_tick:74931. period:100
[D][05:19:03][COMM]msg 0300 loss. last_tick:73922. cur_tick:74931. period:100
[D][05:19:03][COMM]msg 0301 loss. last_tick:73922. cur_tick:74932. period:100
[D][05:19:03][COMM]bat msg 0240 loss. last_tick:73922. cur_tick:74932. period:100. j,i:1 54
[D][05:19:03][COMM]bat msg 0241 loss. last_tick:73922. cur_tick:74932. period:100. j,i:2 55
[D][05:19:03][COMM]bat msg 0242 lo

2025-07-31 22:31:23:110 ==>> ss. last_tick:73922. cur_tick:74933. period:100. j,i:3 56
[D][05:19:03][COMM]bat msg 0244 loss. last_tick:73922. cur_tick:74933. period:100. j,i:5 58
[D][05:19:03][COMM]bat msg 024E loss. last_tick:73922. cur_tick:74934. period:100. j,i:15 68
[D][05:19:03][COMM]bat msg 024F loss. last_tick:73922. cur_tick:74934. period:100. j,i:16 69
[D][05:19:03][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 74935
[D][05:19:03][COMM]CAN message bat fault change: 0x00000000->0x0001802E 74935
[D][05:19:03][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 74936
                                                                              

2025-07-31 22:31:23:368 ==>> [D][05:19:04][COMM]msg 0222 loss. last_tick:73922. cur_tick:75431. period:150
[D][05:19:04][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 75431
[D][05:19:04][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:04][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------


2025-07-31 22:31:23:594 ==>> [E][05:19:04][COMM]1x1 rx timeout
[E][05:19:04][COMM]1x1 tp timeout
[E][05:19:04][COMM]1x1 error -3.
[D][05:19:04][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:04][COMM]CAN STOP!
[D][05:19:04][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:04][COMM]------------ready to Power off Acckey 1------------
[D][05:19:04][COMM]------------ready to Power off Acckey 2------------
[D][05:19:04][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:04][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 99
[D][05:19:04][COMM]bat sleep fail, reason:-1
[D][05:19:04][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM]imu rest ok. 75621
[D][05:19:04][COMM]imu sleep 0
[W][05:19:04][COMM]now sleep


2025-07-31 22:31:23:785 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 22:31:23:792 ==>> 检测【检测小电池休眠电流】
2025-07-31 22:31:23:802 ==>> 开始小电池电流采样
2025-07-31 22:31:23:827 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:31:23:900 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:31:24:910 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:31:24:955 ==>> CurrentBattery:ƽ��:1733.17

2025-07-31 22:31:25:417 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:31:25:524 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 22:31:26:525 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 22:31:26:570 ==>> CurrentBattery:ƽ��:68.94

2025-07-31 22:31:27:035 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 22:31:27:042 ==>> 【检测小电池休眠电流】通过,【68.94uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 22:31:27:049 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 22:31:27:066 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 22:31:27:111 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 22:31:27:322 ==>> [D][05:19:08][COMM]------------ready to Power on Acckey 1------------
[D][05:19:08][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 16
[D][05:19:08][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:08][COMM]----- get Acckey 1 and value:1------------
[W][05:19:08][COMM]CAN START!
[D][05:19:08][COMM]read battery soc:0
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:08][COMM]CAN message bat fault change: 0x0001802E->0x00000000 79334
[D][05:19:08][COMM][Audio]exec status ready.
[D][05:19:08][CAT1]<<< 
OK

[D][05:19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:19:08][COMM]imu wakeup ok. 79348
[D][05:19:08][COMM]imu wakeup 1
[W][05:19:08][COMM]wake up system, wakeupEvt=0x80
[D][05:19:08][COMM]frm_can_weigth_power_set 1
[D][05:19:08][COMM]Clear Sleep Block Evt
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:27:354 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 22:31:27:360 ==>> 该项需要延时执行
2025-07-31 22:31:27:613 ==>> [E][05:19:08][COMM]1x1 rx timeout
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 22:31:27:718 ==>> [D][05:19:08][COMM]msg 02A0 loss. last_tick:79316. cur_tick:79828. period:50
[D][05:19:08][COMM]msg 02A4 loss. last_tick:79316. cur_tick:79828. period:50
[D][05:19:08][COMM]msg 02A5 loss. last_tick:79316. cur_tick:79829. period:50
[D]

2025-07-31 22:31:27:763 ==>> [05:19:08][COMM]msg 02A6 loss. last_tick:79316. cur_tick:79829. period:50
[D][05:19:08][COMM]msg 02A7 loss. last_tick:79316. cur_tick:79829. period:50
[D][05:19:08][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 79830
[D][05:19:08][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 79830


2025-07-31 22:31:27:869 ==>> 此处延时了:【500】毫秒
2025-07-31 22:31:27:879 ==>> 检测【检测唤醒】
2025-07-31 22:31:27:904 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 22:31:28:586 ==>> [E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[D][05:19:09][COMM]Main Task receive event:28 finished processing
[D][05:19:09][COMM]Main Task receive event:65
[D][05:19:09][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:09][COMM]Main Task receive event:65 finished processing
[D][05:19:09][COMM]Main Task receive event:60
[D][05:19:09][COMM]smart_helmet_vol=255,255
[D][05:19:09][COMM]report elecbike
[W][05:19:09][PROT]remove success[1629955149],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:09][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:09][PROT]index:0
[D][05:19:09][PROT]is_send:1
[D][05:19:09][PROT]sequence_num:10
[D][05:19:09][PROT]retry_timeout:0
[D][05:19:09][PROT]retry_times:3
[D][05:19:09][PROT]send_path:0x3
[D][05:19:09][PROT]msg_type:0x5d03
[D][05:19:09][PROT]===========================================================
[W][05:19:09][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955149]
[D][05:19:09][PROT]===========================================================
[D][05:19:09][PROT]Sending traceid[999999999990000B]
[D][05:19:09][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect


2025-07-31 22:31:28:692 ==>> 
[D][05:19:09][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:09][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:09][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:09][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:09][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:09][SAL ]open socket ind id[4], rst[0]
[D][05:19:09][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:09][SAL ]Cellular task submsg id[8]
[D][05:19:09][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:09][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:09][CAT1]gsm read msg sub id: 8
[D][05:19:09][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:19:09][PROT]add success [1629955149],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:09][COMM]Main Task receive event:60 finished processing
[D][05:19:09][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:09][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:09][CAT1]tx ret[12] >>> AT+CGATT=1

[W][05:19:09][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:09][FCTY]==========System 

2025-07-31 22:31:28:797 ==>> Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:09][FCTY]==========Modules-nRF5340 ==========
[D][05:19:09][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:09][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:09][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:09][FCTY]DeviceID    = 460130071541617
[D][05:19:09][FCTY]HardwareID  = 867222088008885
[D][05:19:09][FCTY]MoBikeID    = 9999999999
[D][05:19:09][FCTY]LockID      = FFFFFFFFFF
[D][05:19:09][FCTY]BLEFWVersion= 105
[D][05:19:09][FCTY]BLEMacAddr   = FAD5CB9330FD
[D][05:19:09][FCTY]Bat         = 3944 mv
[D][05:19:09][FCTY]Current     = 0 ma
[D][05:19:09][FCTY]VBUS        = 11800 mv
[D][05:19:09][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:09][CAT1]<<< 
+CME ERROR: 100

[D][05:19:09][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:09][FCTY]Ext battery vol = 32, adc = 1290
[D][05:19:09][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 25 mv
[D][05:19:09][FCTY]Bike Type flag is invalied
[D][05:19:09][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:09][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:09][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:09][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:09][FCTY]CAT1_GNSS_PLATFORM 

2025-07-31 22:31:28:902 ==>> = C4
[D][05:19:09][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:09][FCTY]Bat1         = 3818 mv
[D][05:19:09][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:09][FCTY]==========Modules-nRF5340 ==========
[D][05:19:09][COMM]msg 0220 loss. last_tick:79316. cur_tick:80323. period:100
[D][05:19:09][COMM]msg 0221 loss. last_tick:79316. cur_tick:80324. period:100
[D][05:19:09][COMM]msg 0224 loss. last_tick:79316. cur_tick:80324. period:100
[D][05:19:09][COMM]msg 0260 loss. last_tick:79316. cur_tick:80324. period:100
[D][05:19:09][COMM]msg 0280 loss. last_tick:79316. cur_tick:80325. period:100
[D][05:19:09][COMM]msg 02C0 loss. last_tick:79316. cur_tick:80325. period:100
[D][05:19:09][COMM]msg 02C1 loss. last_tick:79316. cur_tick:80325. period:100
[D][05:19:09][COMM]msg 02C2 loss. last_tick:79316. cur_tick:80326. period:100
[D][05:19:09][COMM]msg 02E0 loss. last_tick:79316. cur_tick:80327. period:100
[D][05:19:09][COMM]msg 02E1 loss. last_tick:79316. cur_tick:80327. period:100
[D][05:19:09][COMM]msg 02E2 loss. last_tick:79316. cur_tick:80327. period:100
[D][05:19:09][COMM]msg 0300 loss. last_tick:79316. cur_tick:80328. period:100
[D][05:19:09][COMM]msg 0301 

2025-07-31 22:31:28:942 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 22:31:28:956 ==>> 检测【关机】
2025-07-31 22:31:28:965 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:31:29:006 ==>> loss. last_tick:79316. cur_tick:80328. period:100
[D][05:19:09][COMM]bat msg 0240 loss. last_tick:79316. cur_tick:80328. period:100. j,i:1 54
[D][05:19:09][COMM]bat msg 0241 loss. last_tick:79316. cur_tick:80329. period:100. j,i:2 55
[D][05:19:09][COMM]bat msg 0242 loss. last_tick:79316. cur_tick:80329. period:100. j,i:3 56
[D][05:19:09][COMM]bat msg 0244 loss. last_tick:79316. cur_tick:80329. period:100. j,i:5 58
[D][05:19:09][COMM]bat msg 024E loss. last_tick:79316. cur_tick:80330. period:100. j,i:15 68
[D][05:19:09][COMM]bat msg 024F loss. last_tick:79316. cur_tick:80330. period:100. j,i:16 69
[D][05:19:09][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 80330
[D][05:19:09][COMM]CAN message bat fault change: 0x00000000->0x0001802E 80331
[D][05:19:09][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 80331


2025-07-31 22:31:29:607 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 22:31:29:712 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 22:31:29:817 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          ate1 Fail 204
[D][05:19:10][COMM]BAT CAN get soc Fail, 204
[D][05:19:10][COMM]BAT CAN get state2 fail 204
[D][05:19:10][COMM]get soh error
[E][05:19:10][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:10][COMM]report elecbike
[W][05:19:10][PROT]remove success[1629955150],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:10][COMM]Main Task receive event:60 finished processing
[D][05:19:10][COMM]Receive Bat Lock cmd 0
[D][05:19:10][COMM]VBUS is

2025-07-31 22:31:29:922 ==>>  1
[D][05:19:10][COMM]Main Task receive event:61
[D][05:19:10][COMM][D301]:type:3, trace id:280
[D][05:19:10][COMM]id[], hw[000
[D][05:19:10][COMM]get mcMaincircuitVolt error
[D][05:19:10][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:10][COMM]get mcSubcircuitVolt error
[D][05:19:10][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:10][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:10][PROT]index:1
[D][05:19:10][PROT]is_send:1
[D][05:19:10][PROT]sequence_num:11
[D][05:19:10][PROT]retry_timeout:0
[D][05:19:10][PROT]retry_times:3
[D][05:19:10][PROT]send_path:0x3
[D][05:19:10][PROT]msg_type:0x5d03
[D][05:19:10][PROT]===========================================================
[W][05:19:10][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955150]
[D][05:19:10][PROT]===========================================================
[D][05:19:10][PROT]Sending traceid[999999999990000C]
[D][05:19:10][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:10][BLE ]

2025-07-31 22:31:29:982 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 22:31:30:027 ==>> BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:10][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:10][COMM]BAT CAN get state1 Fail 204
[D][05:19:10][COMM]BAT CAN get soc Fail, 204
[D][05:19:10][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:10][COMM]BAT CAN get state2 fail 204
[D][05:19:10][COMM]get bat work mode err
[W][05:19:10][PROT]remove success[1629955150],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:10][PROT]add success [1629955150],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:10][COMM]Main Task receive event:61 finished processing
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:10][COMM]f:[ec800m_audio_play_process].

2025-07-31 22:31:30:133 ==>> l:[968].pkg_num:6
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:10][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:10][COMM]arm_hub_enable: hub power: 0
[D][05:19:10][HSDK]hexlog index save 0 3072 229 @ 0 : 0
[D][05:19:10][HSDK]write save hexlog index [0]
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:10][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:10][COMM]read battery soc:255
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send r

2025-07-31 22:31:30:207 ==>> et: 0
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:10][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:10][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:10][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 22:31:30:312 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:11][COMM]arm_hub_enable: hub power: 0
[D][05:19:11][HSDK]hexlog index save 0 3072 229 @ 0 : 0
[D][05:19:11][HSDK]write save hexlog index [0]
[D][05:19:11][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:11][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 22:31:30:759 ==>> [W][05:19:11][COMM]Power Off


2025-07-31 22:31:30:789 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 22:31:30:812 ==>> #################### 【测试结束】 ####################
2025-07-31 22:31:30:835 ==>> 关闭5V供电
2025-07-31 22:31:30:842 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:31:30:865 ==>> [D][05:19:12][COMM]exit wheel stolen mode.


2025-07-31 22:31:30:909 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:31:30:984 ==>> [D][05:19:12][COMM]Main Task receive event:68
[D][05:19:12][COMM]handlerWheelStolen evt type = 2.
[E][05:19:12][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:12][GNSS]stop locating
[D][05:19:12][GNSS]all continue location stop
[D][05:19:12][COMM]Main Task receive event:68 finished processing


2025-07-31 22:31:31:353 ==>> [D][05:19:12][COMM]read battery soc:255
[D][05:19:12][GNSS]handler GSMGet Base timeout


2025-07-31 22:31:31:832 ==>> 关闭5V供电成功
2025-07-31 22:31:31:853 ==>> 关闭33V供电
2025-07-31 22:31:31:880 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:31:31:910 ==>> [D][05:19:13][COMM]imu_task imu work error:[-1]. goto init
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:31:32:000 ==>> [D][05:19:13][FCTY]get_ext_48v_v 

2025-07-31 22:31:32:837 ==>> 关闭33V供电成功
2025-07-31 22:31:32:857 ==>> 关闭3.7V供电
2025-07-31 22:31:32:865 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:31:32:898 ==>> 6A A6 02 A6 6A 


2025-07-31 22:31:33:003 ==>> Battery OFF
OVER 150


2025-07-31 22:31:33:530 ==>>  

2025-07-31 22:31:33:840 ==>> 关闭3.7V供电成功
