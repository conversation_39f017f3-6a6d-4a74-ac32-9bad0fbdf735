2025-07-31 20:41:02:195 ==>> MES查站成功:
查站序号:P5100010053132B9验证通过
2025-07-31 20:41:02:199 ==>> 扫码结果:P5100010053132B9
2025-07-31 20:41:02:201 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:41:02:203 ==>> 测试参数版本:2024.10.11
2025-07-31 20:41:02:204 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:41:02:206 ==>> 检测【打开透传】
2025-07-31 20:41:02:208 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:41:02:255 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:41:02:618 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:41:02:621 ==>> 检测【检测接地电压】
2025-07-31 20:41:02:623 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:41:02:760 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:41:02:916 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:41:02:918 ==>> 检测【打开小电池】
2025-07-31 20:41:02:921 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:41:03:051 ==>> 6A A6 01 A6 6A 


2025-07-31 20:41:03:156 ==>> Battery ON
OVER 150


2025-07-31 20:41:03:208 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:41:03:210 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:41:03:211 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:41:03:355 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:41:03:497 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:41:03:499 ==>> 检测【等待设备启动】
2025-07-31 20:41:03:501 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:04:549 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:05:589 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:06:627 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:07:658 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:08:688 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:09:723 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:10:768 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:11:799 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:12:825 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:41:13:856 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 20:41:13:860 ==>> #################### 【测试结束】 ####################
2025-07-31 20:41:13:895 ==>> 关闭5V供电
2025-07-31 20:41:13:899 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:41:13:962 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:41:14:903 ==>> 关闭5V供电成功
2025-07-31 20:41:14:906 ==>> 关闭33V供电
2025-07-31 20:41:14:908 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:41:14:949 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:41:15:907 ==>> 关闭33V供电成功
2025-07-31 20:41:15:911 ==>> 关闭3.7V供电
2025-07-31 20:41:15:913 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:41:15:953 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:41:16:165 ==>>  

