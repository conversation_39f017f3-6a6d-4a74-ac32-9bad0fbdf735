2025-07-31 18:18:19:551 ==>> MES查站成功:
查站序号:P510001005312BAF验证通过
2025-07-31 18:18:19:555 ==>> 扫码结果:P510001005312BAF
2025-07-31 18:18:19:557 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:18:19:559 ==>> 测试参数版本:2024.10.11
2025-07-31 18:18:19:560 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:18:19:562 ==>> 检测【打开透传】
2025-07-31 18:18:19:564 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:18:19:664 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:18:19:919 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:18:19:954 ==>> 检测【检测接地电压】
2025-07-31 18:18:19:957 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:18:20:077 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 18:18:20:238 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:18:20:240 ==>> 检测【打开小电池】
2025-07-31 18:18:20:243 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:18:20:368 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:18:20:522 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:18:20:524 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:18:20:527 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:18:20:673 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:18:20:802 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:18:20:805 ==>> 检测【等待设备启动】
2025-07-31 18:18:20:808 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:18:21:089 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:18:21:273 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:18:21:847 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:18:22:001 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:18:22:396 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:18:22:860 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:18:22:909 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:18:22:912 ==>> 检测【产品通信】
2025-07-31 18:18:22:915 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:18:23:058 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:18:23:181 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:18:23:184 ==>> 检测【初始化完成检测】
2025-07-31 18:18:23:187 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:18:23:393 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:18:23:483 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:18:23:486 ==>> 检测【关闭大灯控制1】
2025-07-31 18:18:23:488 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:18:23:498 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:18:23:649 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:18:23:783 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:18:23:786 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:18:23:788 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:18:23:969 ==>> [D][05:17:51][COMM]2638 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:18:24:074 ==>>                                                                                                                                                                                                                                               071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:18:24:082 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:18:24:085 ==>> 检测【关闭仪表供电】
2025-07-31 18:18:24:086 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:18:24:269 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:51][COMM]set POWER 0
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:18:24:365 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:18:24:367 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:18:24:369 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:18:24:529 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:18:24:651 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:18:24:653 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:18:24:656 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:18:24:851 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:18:24:933 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:18:24:939 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:18:24:942 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:24:945 ==>> [D][05:17:52][COMM]3649 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:18:25:155 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:18:25:212 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:18:25:215 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:18:25:217 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:18:25:260 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:18:25:335 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 14


2025-07-31 18:18:25:410 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 18:18:25:495 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:18:25:497 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:18:25:499 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:18:25:575 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:18:25:791 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:18:25:794 ==>> 该项需要延时执行
2025-07-31 18:18:25:957 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:18:26:476 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5010. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5010. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5011. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5011. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5011. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5012. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5012. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5013. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5013. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5013. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5014. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5014. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5015. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5015
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5015


2025-07-31 18:18:27:438 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------
[D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1-------

2025-07-31 18:18:27:543 ==>> -----
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:54][COMM]smart_helmet_vol=255,255
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get soc error
[E][05:17:54][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:54][COMM]report elecbike
[W][05:17:54][PROT]remove success[1629955074],send_path[3],type[0000],priority[0],index[2],u

2025-07-31 18:18:27:648 ==>> sed[0]
[W][05:17:54][PROT]add success [1629955074],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:54][COMM]Receive Bat Lock cmd 0
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]Main Task receive event:60 finished processing
[D][05:17:54][COMM]Main Task receive event:61
[D][05:17:54][COMM][D301]:type:3, trace id:280
[D][05:17:54][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][PROT]index:2
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][PROT]is_send:1
[D][05:17:54][PROT]sequence_num:2
[D][05:17:54][PROT]retry_timeout:0
[D][05:17:54][PROT]retry_times:3
[D][05:17:54][PROT]send_path:0x3
[D][05:17:54][PROT]msg_type:0x5d03
[D][05:17:54][PROT]===========================================================
[W][05:17:54][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955074]
[D][05:17:54][PROT]===========================================================
[D][05:17:54][PROT]Sending traceid[9999999999900003]
[D][05:17:54][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:54][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:

2025-07-31 18:18:27:753 ==>> 54][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:54][COMM]id[], hw[000
[D][05:17:54][COMM]get mcMaincircuitVolt error
[D][05:17:54][COMM]get mcSubcircuitVolt error
[D][05:17:54][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:54][COMM]BAT CAN get state1 Fail 204
[D][05:17:54][COMM]BAT CAN get soc Fail, 204
[D][05:17:54][COMM]get bat work state err
[W][05:17:54][PROT]remove success[1629955074],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:54][PROT]add success [1629955074],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:54][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:54][COMM]Main Task receive event:61 finished processing
                                         

2025-07-31 18:18:27:981 ==>> [D][05:17:55][COMM]6682 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:18:28:992 ==>> [D][05:17:56][COMM]7694 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:18:29:425 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:18:29:795 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:18:29:799 ==>> 检测【33V输入电压ADC】
2025-07-31 18:18:29:802 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:30:087 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:17:57][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:57][COMM]adc read left brake adc:5  volt:6 mv
[D][05:17:57][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read throttle adc:12  volt:15 mv
[D][05:17:57][COMM]adc read battery ts volt:6 mv
[D][05:17:57][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:17:57][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:57][COMM]8705 imu init OK
[D][05:17:57][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:18:30:352 ==>> 【33V输入电压ADC】通过,【32982mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:18:30:354 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:18:30:357 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:18:30:473 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1660mV
Get AD_V4 2mV
Get AD_V5 2775mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:18:30:661 ==>> 【TP7_VCC3V3(ADV2)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:30:663 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:18:30:681 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:30:703 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:18:30:705 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 18:18:30:707 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:18:30:709 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:18:30:724 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:18:30:727 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:18:30:749 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:18:30:751 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:18:30:873 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2776mV
Get AD_V6 1987mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:18:31:008 ==>> [D][05:17:58][COMM]9716 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:18:31:043 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:31:045 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:18:31:066 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:31:068 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:18:31:071 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 18:18:31:092 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:18:31:095 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:18:31:114 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:18:31:116 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:18:31:137 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:18:31:139 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:18:31:283 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2776mV
Get AD_V6 1988mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:18:31:343 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10003. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10004
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10004


2025-07-31 18:18:31:411 ==>> 【TP7_VCC3V3(ADV2)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:31:414 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:18:31:430 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:31:432 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:18:31:435 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 18:18:31:449 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:18:31:451 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:18:31:453 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:18:31:467 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:18:31:469 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:18:31:490 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:18:31:492 ==>> 检测【打开WIFI(1)】
2025-07-31 18:18:31:510 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:18:31:631 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:18:31:770 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:18:31:773 ==>> 检测【清空消息队列(1)】
2025-07-31 18:18:31:775 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:18:32:252 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][COMM]10727 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 

2025-07-31 18:18:32:312 ==>> 2021
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:18:32:320 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:18:32:323 ==>> 检测【打开GPS(1)】
2025-07-31 18:18:32:325 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:18:32:727 ==>>                                                                                                                                                                                 ,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222088035086

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
460130071536901

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1

2025-07-31 18:18:32:757 ==>> ]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:18:32:851 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:18:32:854 ==>> 检测【打开GSM联网】
2025-07-31 18:18:32:856 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:18:33:063 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:18:33:130 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:18:33:133 ==>> 检测【打开仪表供电1】
2025-07-31 18:18:33:135 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:18:33:694 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][COMM]read battery soc:255
[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][0

2025-07-31 18:18:33:799 ==>> 5:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052e00], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 24,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:18:33:905 ==>>                                                                                                                                                              05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 18:18:33:937 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:18:33:940 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:18:33:943 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:18:34:272 ==>> [D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:18:34:503 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:18:34:506 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:18:34:509 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:18:34:657 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33085]


2025-07-31 18:18:34:804 ==>> 【读取主控ADC采集的仪表电压】通过,【33085mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:18:34:807 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:18:34:812 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:18:35:040 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:02][COMM]13739 imu init OK


2025-07-31 18:18:35:123 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:18:35:126 ==>> 检测【AD_V20电压】
2025-07-31 18:18:35:128 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:18:35:238 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:18:35:331 ==>> 本次取值间隔时间:78ms
2025-07-31 18:18:35:471 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255


2025-07-31 18:18:35:607 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:18:35:682 ==>> 本次取值间隔时间:342ms
2025-07-31 18:18:35:743 ==>> 本次取值间隔时间:59ms
2025-07-31 18:18:35:895 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,08,60,,,43,24,,,40,59,,,40,42,,,40,1*74

$GBGSV,2,2,08,26,,,39,39,,,39,38,,,35,13,,,37,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1634.602,1634.602,52.251,2097152,2097152,2097152*4E

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 18:18:36:030 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:18:36:197 ==>> 本次取值间隔时间:449ms
2025-07-31 18:18:36:202 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:18:36:309 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:18:36:370 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:18:36:430 ==>> 本次取值间隔时间:120ms
2025-07-31 18:18:36:497 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:18:36:598 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:18:36:675 ==>> 1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 18:18:36:720 ==>> 本次取值间隔时间:111ms
2025-07-31 18:18:36:764 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:36:767 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:18:36:769 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:18:36:780 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,24,,,41,60,,,40,42,,,40,13,,,40,1*73

$GBGSV,4,2,14,59,,,39,26,,,39,39,,,39,8,,,39,1*4F

$GBGSV,4,3,14,16,,,38,38,,,38,1,,,36,2,,,34,1*79

$GBGSV,4,4,14,5,,,30,3,,,42,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1572.225,1572.225,50.295,2097152,2097152,2097152*44



2025-07-31 18:18:36:870 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:18:37:070 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:18:37:073 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:18:37:077 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:18:37:285 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:18:37:373 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:18:37:376 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:18:37:378 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:18:37:466 ==>> [D][05:18:05][COMM]read battery soc:255
3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 18:18:37:704 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:18:37:708 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:18:37:711 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:18:37:755 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,24,,,42,60,,,40,42,,,40,13,,,40,1*72

$GBGSV,4,2,16,26,,,40,3,,,39,59,,,39,39,,,39,1*48

$GBGSV,4,3,16,8,,,39,38,,,39,16,,,38,1,,,37,1*7C

$GBGSV,4,4,16,2,,,35,4,,,32,5,,,31,40,,,31,1*41

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1557.300,1557.300,49.840,2097152,2097152,2097152*4E



2025-07-31 18:18:37:860 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:18:38:026 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:18:38:029 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:18:38:031 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:18:38:262 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:18:38:344 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:18:38:349 ==>> 检测【AD_V21电压】
2025-07-31 18:18:38:368 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:18:38:475 ==>> 1A A1 20 00 00 
Get AD_V21 1058mV
OVER 150


2025-07-31 18:18:38:595 ==>> 本次取值间隔时间:248ms
2025-07-31 18:18:38:654 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:18:38:795 ==>> $GBGGA,101842.586,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,42,,,41,60,,,40,13,,,40,1*7D

$GBGSV,5,2,19,26,,,40,3,,,40,38,,,40,59,,,39,1*47

$GBGSV,5,3,19,39,,,39,8,,,39,16,,,38,1,,,37,1*73

$GBGSV,5,4,19,2,,,34,4,,,32,5,,,32,33,,,32,1*4A

$GBGSV,5,5,19,40,,,31,14,,,38,9,,,36,1*4A

$GBRMC,101842.586,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101842.586,0.000,1551.062,1551.062,49.652,2097152,2097152,2097152*58

1A A1 20 00 00 
Get AD_V21 1644mV
OVER 150


2025-07-31 18:18:38:885 ==>> 本次取值间隔时间:226ms
2025-07-31 18:18:38:928 ==>> 【AD_V21电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:38:933 ==>> 检测【关闭仪表供电2】
2025-07-31 18:18:38:949 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:18:39:165 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:18:39:229 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:18:39:231 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:18:39:235 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:18:39:487 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!
[D][05:18:07][COMM]read battery soc:255


2025-07-31 18:18:39:762 ==>> $GBGGA,101843.566,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,40,60,,,40,13,,,40,1*76

$GBGSV,5,2,20,26,,,40,3,,,40,38,,,40,59,,,39,1*4D

$GBGSV,5,3,20,39,,,39,8,,,39,16,,,38,1,,,36,1*78

$GBGSV,5,4,20,9,,,35,6,,,35,2,,,34,33,,,34,1*48

$GBGSV,5,5,20,14,,,33,4,,,31,5,,,31,40,,,30,1*77

$GBRMC,101843.566,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101843.566,0.000,1525.701,1525.701,48.843,2097152,2097152,2097152*58



2025-07-31 18:18:39:791 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:18:39:794 ==>> 检测【打开AccKey2供电】
2025-07-31 18:18:39:798 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:18:39:867 ==>> [D][05:18:07][COMM]S->M yaw:INVALID


2025-07-31 18:18:39:957 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:18:40:067 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:18:40:080 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:18:40:083 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:40:381 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3170  volt:5572 mv
[D][05:18:07][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:18:07][COMM]adc read left brake adc:13  volt:17 mv
[D][05:18:07][COMM]adc read right brake adc:14  volt:18 mv
[D][05:18:07][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:07][COMM]adc read battery ts volt:15 mv
[D][05:18:07][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:07][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:18:40:604 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33285mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:18:40:609 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:18:40:616 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:18:40:775 ==>> $GBGGA,101844.546,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,42,,,40,13,,,40,26,,,40,1*74

$GBGSV,5,2,20,3,,,40,38,,,40,60,,,39,59,,,39,1*41

$GBGSV,5,3,20,8,,,39,39,,,38,16,,,38,1,,,36,1*79

$GBGSV,5,4,20,6,,,36,9,,,35,33,,,35,2,,,34,1*4A

$GBGSV,5,5,20,14,,,34,4,,,31,5,,,31,40,,,30,1*70

$GBRMC,101844.546,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101844.546,0.000,1527.766,1527.766,48.901,2097152,2097152,2097152*5A

[W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:18:40:893 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:18:40:896 ==>> 该项需要延时执行
2025-07-31 18:18:40:911 ==>> [D][05:18:08][COMM]M->S yaw:INVALID


2025-07-31 18:18:41:472 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:18:41:729 ==>> $GBGGA,101845.526,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,13,,,39,26,,,39,60,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,37,1,,,36,1*42

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,2,,,34,1*4B

$GBGSV,6,5,22,14,,,34,4,,,31,5,,,31,40,,,30,1*71

$GBGSV,6,6,22,7,,,29,22,,,38,1*41

$GBRMC,101845.526,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101845.526,0.000,1508.330,1508.330,48.290,2097152,2097152,2097152*5E



2025-07-31 18:18:42:713 ==>> $GBGGA,101846.506,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,41,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,21,13,,,39,26,,,39,60,,,39,8,,,39,1*49

$GBGSV,6,3,21,59,,,39,39,,,38,16,,,37,6,,,36,1*47

$GBGSV,6,4,21,9,,,36,33,,,36,1,,,35,2,,,34,1*4C

$GBGSV,6,5,21,14,,,34,5,,,31,4,,,30,40,,,30,1*73

$GBGSV,6,6,21,7,,,29,1*49

$GBRMC,101846.506,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101846.506,0.000,1504.384,1504.384,48.166,2097152,2097152,2097152*55



2025-07-31 18:18:43:495 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:18:43:707 ==>> $GBGGA,101847.506,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,13,,,40,26,,,40,60,,,40,8,,,39,1*44

$GBGSV,6,3,22,39,,,39,59,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101847.506,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101847.506,0.000,1522.686,1522.686,48.747,2097152,2097152,2097152*51



2025-07-31 18:18:43:904 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:18:43:909 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:18:43:914 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:44:176 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:18:11][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:11][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:11][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:11][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:11][COMM]adc read battery ts volt:19 mv
[D][05:18:11][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:11][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 18:18:44:439 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【151mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:18:44:444 ==>> 检测【打开AccKey1供电】
2025-07-31 18:18:44:448 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:18:44:738 ==>> $GBGGA,101848.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,13,,,40,60,,,40,26,,,39,8,,,39,1*4A

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101848.506,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101848.506,0.000,1518.915,1518.915,48.624,2097152,2097152,2097152*5A

[W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:18:44:971 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:18:44:975 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:18:44:978 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:18:45:073 ==>> 1A A1 00 40 00 
Get AD_V14 2594mV
OVER 150


2025-07-31 18:18:45:223 ==>> 原始值:【2594】, 乘以分压基数【2】还原值:【5188】
2025-07-31 18:18:45:242 ==>> 【读取AccKey1电压(ADV14)前】通过,【5188mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:18:45:246 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:18:45:249 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:45:589 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:18:13][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:13][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:13][COMM]adc read right brake adc:13  volt:17 mv
[D][05:18:13][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:13][COMM]adc read battery ts volt:14 mv
[D][05:18:13][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:13][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:13][COMM]read battery soc:255


2025-07-31 18:18:45:694 ==>>                        ,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,26,,,40,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,32,4,,,31,1*40

$GBGSV,6,6,22,40

2025-07-31 18:18:45:739 ==>> ,,,30,7,,,30,1*45

$GBRMC,101849.506,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101849.506,0.000,1520.796,1520.796,48.681,2097152,2097152,2097152*54



2025-07-31 18:18:45:773 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5575mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:18:45:777 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:18:45:781 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:18:45:950 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:18:46:044 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:18:46:047 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:18:46:052 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:18:46:163 ==>> 1A A1 00 40 00 
Get AD_V14 2594mV
OVER 150


2025-07-31 18:18:46:299 ==>> 原始值:【2594】, 乘以分压基数【2】还原值:【5188】
2025-07-31 18:18:46:318 ==>> 【读取AccKey1电压(ADV14)后】通过,【5188mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:18:46:321 ==>> 检测【打开WIFI(2)】
2025-07-31 18:18:46:323 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:18:46:494 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:18:46:594 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:18:46:599 ==>> 检测【转刹把供电】
2025-07-31 18:18:46:604 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:18:46:719 ==>> $GBGGA,101850.506,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,26,,,39,13,,,39,8,,,39,1*44

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,32,4,,,31,1*40

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101850.506,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101850.506,0.000,1518.909,1518.909,48.619,2097152,2097152,2097152*5D



2025-07-31 18:18:46:794 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:18:46:872 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:18:46:878 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:18:46:886 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:18:46:977 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:18:47:067 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2409mV
OVER 150


2025-07-31 18:18:47:142 ==>> 原始值:【2409】, 乘以分压基数【2】还原值:【4818】
2025-07-31 18:18:47:157 ==>> +WIFISCAN:4,0,CC057790A7C1,-74
+WIFISCAN:4,1,CC057790A740,-75
+WIFISCAN:4,2,CC057790A5C1,-79
+WIFISCAN:4,3,646E97BD0450,-85

[D][05:18:14][CAT1]wifi scan report total[4]


2025-07-31 18:18:47:162 ==>> 【读取AD_V15电压(前)】通过,【4818mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:18:47:166 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:18:47:173 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:18:47:277 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:18:47:367 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2438mV
OVER 150


2025-07-31 18:18:47:442 ==>> 原始值:【2438】, 乘以分压基数【2】还原值:【4876】
2025-07-31 18:18:47:466 ==>> 【读取AD_V16电压(前)】通过,【4876mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:18:47:469 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:18:47:474 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:47:503 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 18:18:47:835 ==>> $GBGGA,101851.506,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,42,,,40,3,,,40,38,,,40,1*48

$GBGSV,6,2,22,13,,,40,60,,,39,26,,,39,8,,,39,1*44

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101851.506,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101851.506,0.000,1515.141,1515.141,48.498,2097152,2097152,2097152*57

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3176  volt:5582 mv
[D][05:18:15][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:15][COMM]adc read battery ts volt:18 mv
[D][05:18:15][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3105  volt:5458 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:15][COMM]arm_hub

2025-07-31 18:18:47:865 ==>>  adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:18:48:028 ==>> 【转刹把供电电压(主控ADC)】通过,【5458mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:18:48:034 ==>> 检测【转刹把供电电压】
2025-07-31 18:18:48:048 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:48:105 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 18:18:48:379 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3179  volt:5588 mv
[D][05:18:15][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:15][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:15][COMM]adc read battery ts volt:20 mv
[D][05:18:15][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3107  volt:5461 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:18:48:586 ==>> 【转刹把供电电压】通过,【5461mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:18:48:591 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:18:48:607 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:48:709 ==>> $GBGGA,101852.506,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,13,,,40,60,,,40,26,,,39,8,,,39,1*4A

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101852.506,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101852.506,0.000,1518.915,1518.915,48.624,2097152,2097152,2097152*51



2025-07-31 18:18:48:799 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:18:49:515 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 18:18:49:710 ==>> $GBGGA,101853.506,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,39,26,,,39,8,,,39,1*44

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,6,,,36,9,,,36,33,,,36,1,,,36,1*4A

$GBGSV,6,5,22,2,,,34,14,,,34,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101853.506,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101853.506,0.000,1517.028,1517.028,48.562,2097152,2097152,2097152*51



2025-07-31 18:18:50:738 ==>> $GBGGA,101854.506,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,42,42,,,40,3,,,40,38,,,40,1*4A

$GBGSV,6,2,23,60,,,40,13,,,40,26,,,40,8,,,39,1*45

$GBGSV,6,3,23,39,,,38,59,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,23,6,,,36,9,,,36,33,,,36,1,,,36,1*4B

$GBGSV,6,5,23,14,,,35,2,,,34,5,,,32,4,,,31,1*40

$GBGSV,6,6,23,7,,,31,40,,,30,10,,,37,1*40

$GBRMC,101854.506,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101854.506,0.000,1526.445,1526.445,48.857,2097152,2097152,2097152*5D



2025-07-31 18:18:51:037 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:18:51:042 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:18:51:046 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:51:148 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:18:51:253 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:51:268 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 18:18:51:358 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:18:51:463 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 18:18:51:481 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:18:51:485 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:18:51:490 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:51:524 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 18:18:51:583 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:18:51:688 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:18:51:733 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150
$GBGGA,101855.506,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101855.506,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101855.506,0.000,1530.215,1530.215,48.979,2097152,2097152,2097152*51

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:18:51:793 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:18:51:838 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:18:51:868 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:18:51:927 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:18:51:936 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:18:51:940 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:18:52:063 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:18:52:203 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:18:52:208 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:18:52:213 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:18:52:275 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:18:52:476 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:18:52:479 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:18:52:483 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:18:52:561 ==>> 3A A3 05 01 A3 


2025-07-31 18:18:52:666 ==>> ON_OUT5
OVER 150
$GBGGA,101856.506,,,,,0

2025-07-31 18:18:52:726 ==>> ,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,35,5,,,32,4,,,32,1*43

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101856.506,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101856.506,0.000,1532.098,1532.098,49.037,2097152,2097152,2097152*50



2025-07-31 18:18:52:760 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:18:52:763 ==>> 检测【左刹电压测试1】
2025-07-31 18:18:52:766 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:53:076 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3169  volt:5570 mv
[D][05:18:20][COMM]adc read out 24v adc:7  volt:177 mv
[D][05:18:20][COMM]adc read left brake adc:1734  volt:2286 mv
[D][05:18:20][COMM]adc read right brake adc:1736  volt:2288 mv
[D][05:18:20][COMM]adc read throttle adc:1725  volt:2274 mv
[D][05:18:20][COMM]adc read battery ts volt:11 mv
[D][05:18:20][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:20][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1427  volt:33085 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:18:53:311 ==>> 【左刹电压测试1】通过,【2286】符合目标值【2250】至【2500】要求!
2025-07-31 18:18:53:314 ==>> 检测【右刹电压测试1】
2025-07-31 18:18:53:364 ==>> 【右刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 18:18:53:367 ==>> 检测【转把电压测试1】
2025-07-31 18:18:53:411 ==>> 【转把电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 18:18:53:417 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:18:53:441 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:18:53:472 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:18:53:546 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 18:18:53:651 ==>> $GBGGA,101857.506,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,2

2025-07-31 18:18:53:708 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:18:53:711 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:18:53:717 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:18:53:729 ==>> 4,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101857.506,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101857.506,0.000,1530.215,1530.215,48.979,2097152,2097152,2097152*53



2025-07-31 18:18:53:772 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:18:54:006 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:18:54:014 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:18:54:032 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:18:54:075 ==>> 3A A3 05 00 A3 


2025-07-31 18:18:54:165 ==>> OFF_OUT5
OVER 150


2025-07-31 18:18:54:313 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:18:54:316 ==>> 检测【左刹电压测试2】
2025-07-31 18:18:54:321 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:18:54:590 ==>> [D][05:18:22][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3167  volt:5566 mv
[D][05:18:22][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:22][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:22][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:22][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:22][COMM]adc read battery ts volt:13 mv
[D][05:18:22][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:22][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1428  volt:33108 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:18:54:695 ==>>                                                                                       ,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,35,5,,,31,4,,,31,1*43

$GBGSV,6,6,22,7,

2025-07-31 18:18:54:740 ==>> ,,30,40,,,30,1*45

$GBRMC,101858.506,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101858.506,0.000,1528.336,1528.336,48.924,2097152,2097152,2097152*54



2025-07-31 18:18:54:856 ==>> 【左刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 18:18:54:860 ==>> 检测【右刹电压测试2】
2025-07-31 18:18:54:880 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 18:18:54:884 ==>> 检测【转把电压测试2】
2025-07-31 18:18:54:938 ==>> 【转把电压测试2】通过,【18】符合目标值【0】至【50】要求!
2025-07-31 18:18:54:946 ==>> 检测【晶振检测】
2025-07-31 18:18:54:955 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:18:55:160 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:22][COMM][lf state:1][hf state:1]


2025-07-31 18:18:55:210 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:18:55:215 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:18:55:220 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:18:55:265 ==>> 1A A1 00 00 FC 
Get AD_V2 1656mV
Get AD_V3 1660mV
Get AD_V4 1653mV
Get AD_V5 2775mV
Get AD_V6 1991mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:18:55:484 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:18:55:491 ==>> 检测【检测BootVer】
2025-07-31 18:18:55:500 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:18:55:555 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:18:55:888 ==>> $GBGGA,101859.506,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,40,13,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,35,5,,,32,4,,,32,1*43

$GBGSV,6,6,22,7,,,31,40,,,30,1*44

$GBRMC,101859.506,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101859.506,0.000,1533.978,1533.978,49.093,2097152,2097152,2097152*51

[W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071536901
[D][05:18:23][FCTY]HardwareID  = 867222088035086
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:23][FCTY]Bat         = 3944 

2025-07-31 18:18:55:978 ==>> mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1300
[D][05:18:23][FCTY]Acckey1 vol = 5574 mv, Acckey2 vol = 202 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3825 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:18:56:027 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:18:56:031 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:18:56:037 ==>> 检测【检测固件版本】
2025-07-31 18:18:56:045 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:18:56:049 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:18:56:055 ==>> 检测【检测蓝牙版本】
2025-07-31 18:18:56:074 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:18:56:078 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:18:56:081 ==>> 检测【检测MoBikeId】
2025-07-31 18:18:56:093 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:18:56:097 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:18:56:104 ==>> 检测【检测蓝牙地址】
2025-07-31 18:18:56:111 ==>> 取到目标值:FD95DC83243D
2025-07-31 18:18:56:130 ==>> 【检测蓝牙地址】通过,【FD95DC83243D】符合目标值【】要求!
2025-07-31 18:18:56:134 ==>> 提取到蓝牙地址:FD95DC83243D
2025-07-31 18:18:56:138 ==>> 检测【BOARD_ID】
2025-07-31 18:18:56:161 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:18:56:165 ==>> 检测【检测充电电压】
2025-07-31 18:18:56:179 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:18:56:186 ==>> 检测【检测VBUS电压1】
2025-07-31 18:18:56:202 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:18:56:206 ==>> 检测【检测充电电流】
2025-07-31 18:18:56:221 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:18:56:224 ==>> 检测【检测IMEI】
2025-07-31 18:18:56:230 ==>> 取到目标值:867222088035086
2025-07-31 18:18:56:251 ==>> 【检测IMEI】通过,【867222088035086】符合目标值【】要求!
2025-07-31 18:18:56:255 ==>> 提取到IMEI:867222088035086
2025-07-31 18:18:56:269 ==>> 检测【检测IMSI】
2025-07-31 18:18:56:284 ==>> 取到目标值:460130071536901
2025-07-31 18:18:56:288 ==>> 【检测IMSI】通过,【460130071536901】符合目标值【】要求!
2025-07-31 18:18:56:291 ==>> 提取到IMSI:460130071536901
2025-07-31 18:18:56:295 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:18:56:309 ==>> 取到目标值:460130071536901
2025-07-31 18:18:56:313 ==>> 【校验网络运营商(移动)】通过,【460130071536901】符合目标值【】要求!
2025-07-31 18:18:56:317 ==>> 检测【打开CAN通信】
2025-07-31 18:18:56:323 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:18:56:368 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:18:56:582 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:18:56:589 ==>> 检测【检测CAN通信】
2025-07-31 18:18:56:611 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:18:56:713 ==>> $GBGGA,101900.506,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,26,,,40,60,,,39,13,,,39,8,,,39,1*44

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101900.506,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101900.506,0.000,1524.557,1524.557,48.794,2097152,2097152,2097152*5D

can send success


2025-07-31 18:18:56:758 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:18:56:803 ==>> [D][05:18:24][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 35511
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:18:56:851 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:18:56:855 ==>> 检测【关闭CAN通信】
2025-07-31 18:18:56:861 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:18:56:882 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:18:56:938 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:18:56:968 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:18:57:122 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:18:57:127 ==>> 检测【打印IMU STATE】
2025-07-31 18:18:57:133 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:18:57:359 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:1
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:18:57:411 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:18:57:416 ==>> 检测【六轴自检】
2025-07-31 18:18:57:427 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:18:57:584 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:25][CAT1]gsm read msg sub id: 12
[D][05:18:25][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0


[D][05:18:25][COMM]read battery soc:255


2025-07-31 18:18:58:041 ==>> $GBGGA,101901.506,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,3,,,40,38,,,40,1*4B

$GBGSV,6,2,22,26,,,40,60,,,40,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,34,2,,,34,5,,,32,4,,,32,1*43

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101901.506,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101901.506,0.000,760.870,760.870,695.835,2097152,2097152,2097152*6E



2025-07-31 18:18:58:741 ==>> $GBGGA,101902.506,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,3,,,40,60,,,40,42,,,40,1*46

$GBGSV,6,2,22,13,,,39,8,,,39,38,,,39,26,,,39,1*47

$GBGSV,6,3,22,59,,,38,39,,,38,21,,,38,16,,,37,1*7E

$GBGSV,6,4,22,9,,,36,1,,,36,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101902.506,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101902.506,0.000,758.042,758.042,693.248,2097152,2097152,2097152*6B



2025-07-31 18:18:59:275 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:18:59:380 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38101 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-25,-13,4080]
[D][05:18:

2025-07-31 18:18:59:410 ==>> 27][COMM]Main Task receive event:142 finished processing


2025-07-31 18:18:59:505 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:18:59:511 ==>> 检测【打印IMU STATE2】
2025-07-31 18:18:59:532 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:18:59:575 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 18:18:59:680 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offse

2025-07-31 18:18:59:755 ==>> t:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,101903.506,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,38,,,40,3,,,40,60,,,40,1*4B

$GBGSV,6,2,22,26,,,40,42,,,40,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,38,21,,,38,1*71

$GBGSV,6,4,22,9,,,36,1,,,36,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101903.506,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101903.506,0.000,760.867,760.867,695.833,2097152,2097152,2097152*6A



2025-07-31 18:18:59:789 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:18:59:793 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:18:59:797 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:18:59:860 ==>> 5A A5 02 5A A5 


2025-07-31 18:18:59:965 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:19:00:059 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:19:00:067 ==>> 检测【检测VBUS电压2】
2025-07-31 18:19:00:074 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:00:090 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:1

2025-07-31 18:19:00:130 ==>> 8:27][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 18:19:00:450 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071536901
[D][05:18:27][FCTY]HardwareID  = 867222088035086
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:27][FCTY]Bat         = 3964 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11700 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 351
[D][05:18:27][FCTY]Acckey1 vol = 5575 mv, Acckey2 vol = 75 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]

2025-07-31 18:19:00:495 ==>> CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3825 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 18:19:00:612 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:00:949 ==>> $GBGGA,101904.506,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,40,38,,,40,3,,,40,1*4F

$GBGSV,6,2,22,60,,,40,26,,,40,42,,,40,8,,,39,1*40

$GBGSV,6,3,22,59,,,39,39,,,38,21,,,38,16,,,37,1*7F

$GBGSV,6,4,22,9,,,36,1,,,36,6,,,36,33,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101904.506,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101904.506,0.000,761.810,761.810,696.695,2097152,2097152,2097152*6C

[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071536901
[D][05:18:28][FCTY]HardwareID  = 867222088035086
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:28][FCTY]Bat         = 

2025-07-31 18:19:01:038 ==>> 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 9200 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 4, adc = 187
[D][05:18:28][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 101 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3825 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:19:01:146 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:01:528 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071536901
[D][05:18:29][FCTY]HardwareID  = 867222088035086
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:29][FCTY]Bat         = 3864 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 9200 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 160
[D][05:18:29][FCTY]Acckey1 vol = 5565 mv, Acckey2 vol = 101 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C

2025-07-31 18:19:01:573 ==>> 4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3825 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:19:01:678 ==>> $GBGGA,101905.506,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,13,,,40,38,,,40,3,,,40,1*4F

$GBGSV,6,2,22,60,,,40,26,,,40,

2025-07-31 18:19:01:689 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:01:738 ==>> 42,,,40,8,,,39,1*40

$GBGSV,6,3,22,59,,,39,39,,,38,16,,,38,21,,,38,1*70

$GBGSV,6,4,22,33,,,37,9,,,36,1,,,36,6,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,30,40,,,30,1*45

$GBRMC,101905.506,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101905.506,0.000,763.691,763.691,698.415,2097152,2097152,2097152*69



2025-07-31 18:19:02:418 ==>>                                                                                                                ault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 40513
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071536901
[D][05:18:29][FCTY]HardwareID  = 867222088035086
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:29][FCTY]Bat         = 3864 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 9200 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 3, adc = 136
[D][05:18:29][FCTY]Acckey1 vol = 5570 mv, Acckey2 vol = 75 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CA

2025-07-31 18:19:02:466 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:02:523 ==>> T1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3825 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][GNSS]all continue location stop
[D][05:18:29][COMM]report elecbike
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],i

2025-07-31 18:19:02:628 ==>> ndex[0],used[0]
[D][05:18:29][HSDK][0] flush to flash addr:[0xE42C00] --- write len --- [256]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]re

2025-07-31 18:19:02:733 ==>> try_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6

[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[1

2025-07-31 18:19:02:838 ==>> 7] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B538581F4942D386585CF8DF8B349BA15493519D3848F2C70E44C85752F0C49D7B0A26252DCDD335649D9F13520696A372ADAC047DDE3C870DA94ED84B759A35BC50D44E02207C3263E25BCF8F00BBFC484440
[D][05:18:29][GNSS]recv submsg id[1]
[D][05:18:29][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:29][GNSS]location stop evt done evt
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]


2025-07-31 18:19:03:110 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071536901
[D][05:18:30][FCTY]HardwareID  = 867222088035086
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 107
[D][05:18:30][FCTY]Acckey1 vol = 5582 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]C

2025-07-31 18:19:03:155 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3825 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:19:03:248 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:19:03:630 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071536901
[D][05:18:31][FCTY]HardwareID  = 867222088035086
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:18:31][FCTY]Bat         = 3884 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 4900 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 58,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 104
[D][05:18:31][FCTY]Acckey1 vol = 5563 mv, Acckey2 vol = 50 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18

2025-07-31 18:19:03:674 ==>> :31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3825 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:19:03:783 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:19:03:788 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:19:03:793 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:19:03:869 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:19:03:974 ==>> [D][05:18:31][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30
[D][05:18:31][COMM]read battery soc:255


2025-07-31 18:19:04:004 ==>> 


2025-07-31 18:19:04:057 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:19:04:062 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:19:04:083 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:19:04:170 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:19:04:341 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:19:04:349 ==>> 检测【打开WIFI(3)】
2025-07-31 18:19:04:371 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:19:04:594 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:32][CAT1]gsm read msg sub id: 12
[D][05:18:32][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:32][CAT1]<<< 
OK

[D][05:18:32][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:19:04:869 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:19:04:876 ==>> 检测【扩展芯片hw】
2025-07-31 18:19:04:899 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:19:05:060 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:19:05:138 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:19:05:144 ==>> 检测【扩展芯片boot】
2025-07-31 18:19:05:165 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:19:05:171 ==>> 检测【扩展芯片sw】
2025-07-31 18:19:05:184 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:19:05:191 ==>> 检测【检测音频FLASH】
2025-07-31 18:19:05:201 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:19:05:288 ==>> +WIFISCAN:4,0,CC057790A741,-74
+WIFISCAN:4,1,CC057790A740,-74
+WIFISCAN:4,2,CC057790A7C1,-75
+WIFISCAN:4,3,646E97BD0450,-86

[D][05:18:32][CAT1]wifi scan report total[4]


2025-07-31 18:19:05:348 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:19:05:542 ==>> [D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:19:06:272 ==>> [D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]more than the number of battery plugs
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:33][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:33][COMM]Bat auth off fail, error:-1
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:1------------
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:33][COMM]file:B50 exist
[D][05:18:33][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:33][COMM]f:[ec800m_audio_play_proce

2025-07-31 18:19:06:377 ==>> ss].l:[920].cmd file 'B50'
[D][05:18:33][COMM]read file, len:10800, num:3
[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:66
[D][05:18:33][COMM]Try to Auto Lock Bat
[D][05:18:33][COMM]Main Task receive event:66 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get soc error
[E][05:18:33][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:33][HSDK][0] flush to flash addr:[0xE42D00] --- write len --- [256]
[D][05:18:33][COMM]Receive Bat Lock cmd 0
[D][05:18:33][COMM]VBUS is 1
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]index:1
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33]

2025-07-31 18:19:06:481 ==>> [PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900006]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:33][COMM]Main Task receive event:60 finished processing
[D][05:18:33][COMM]Main Task receive event:61
[D][05:18:33][COMM][D301]:type:3, trace id:280
[D][05:18:33][COMM]id[], hw[000
[D][05:18:33][COMM]get mcMaincircuitVolt error
[D][05:18:33][COMM]get mcSubcircuitVolt error
[D][05:18:33][COMM]33v/48v_in[32], mc_main_vol[

2025-07-31 18:19:06:586 ==>> 0], mc_sub_vol[0]
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[D][05:18:33][COMM]get bat work state err
[W][05:18:33][PROT]remove success[1629955113],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:33][COMM]Main Task receive event:61 finished processing
[D][05:18:33][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:33][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:33][COMM]--->crc16:0xb8a
[D][05:18:33][COMM]read file success
[W][05:18:33][COMM][Audio].l:[936].close hexlog save
[D][05:18:33][COMM]accel parse set 1
[D][05:18:33][COMM][Audio]mon:9,05:18:33
[D][05:18:33][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:33][COMM]f:[

2025-07-31 18:19:06:692 ==>> ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_pro

2025-07-31 18:19:06:767 ==>> cess].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:33][COMM]read battery soc:255
[D][05:18:33][GNSS]recv submsg id[3]


2025-07-31 18:19:07:597 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B39A4AE13CBE626367A2153D457F9BA9C0AE04B15F75B46FE092D9CDE9BF4B099EC58CFFC2A1FFF5F81222C0282CC4F8

2025-07-31 18:19:07:657 ==>> AB41977687BF6CCB05FD5C92147A2C8026BCD880CE0BEEC033F2BCD81828921B01977E
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]


2025-07-31 18:19:08:016 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 18:19:08:597 ==>> [D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:19:09:492 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:37][COMM]crc 108B
[D][05:18:37][COMM]flash test ok


2025-07-31 18:19:09:747 ==>> [D][05:18:37][COMM]48320 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:37][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:37][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:37][COMM]accel parse set 0
[D][05:18:37][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:19:10:007 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 18:19:10:222 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:19:10:231 ==>> 检测【打开喇叭声音】
2025-07-31 18:19:10:253 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:19:10:974 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:38][COMM]file:A20 exist
[D][05:18:38][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:38][COMM]read file, len:15228, num:4
[D][05:18:38][COMM]--->crc16:0x419c
[D][05:18:38][COMM]read file success
[W][05:18:38][COMM][Audio].l:[936].close hexlog save
[D][05:18:38][COMM]accel parse set 1
[D][05:18:38][COMM][Audio]mon:9,05:18:38
[D][05:18:38][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[704].aud

2025-07-31 18:19:11:007 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:19:11:014 ==>> 检测【打开大灯控制】
2025-07-31 18:19:11:024 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:19:11:079 ==>> io cmd send:AT+AUDIOSEND=1

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:38][COMM]49331 imu init OK
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_p

2025-07-31 18:19:11:184 ==>> lay_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:19:11:258 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:19:11:541 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:19:11:547 ==>> 检测【关闭仪表供电3】
2025-07-31 18:19:11:555 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:19:11:758 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 18:19:11:843 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:19:11:849 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:19:11:856 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:19:12:035 ==>> [D][05:18:39][COMM]read battery soc:255
[W][05:18:39][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:19:12:155 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:19:12:161 ==>> 检测【读大灯电压】
2025-07-31 18:19:12:167 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:19:12:355 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[32969]


2025-07-31 18:19:12:469 ==>> 【读大灯电压】通过,【32969mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:19:12:475 ==>> 检测【关闭大灯控制2】
2025-07-31 18:19:12:484 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:19:12:837 ==>> [D][05:18:40][PROT]CLEAN,SEND:1
[D][05:18:40][PROT]index:1 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:5
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:2
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900006]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:40][CAT1]gsm read msg sub id: 15
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:40][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B303E0883470E762FA8C93A87D3125BFF8A900E5F2C90B8683F78312FC8689F025C102F46B85CE3194506EDD8

2025-07-31 18:19:12:912 ==>> 4F5EB60C0D99C251C0EF7A997F8D7FC5F24FFDE6C60C9B0410248EE2C6CB0BBD28BE2E624F537
[W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]


2025-07-31 18:19:13:025 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:19:13:031 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:19:13:036 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:19:13:258 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:19:13:362 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:19:13:370 ==>> 检测【打开WIFI(4)】
2025-07-31 18:19:13:395 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:19:13:438 ==>> [D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:19:13:588 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:41][CAT1]<<< 
OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:19:13:687 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:19:13:696 ==>> 检测【EC800M模组版本】
2025-07-31 18:19:13:707 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:19:13:858 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:41][CAT1]gsm read msg sub id: 12
[D][05:18:41][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 18:19:14:086 ==>> [D][05:18:41][COMM]read battery soc:255
[D][05:18:41][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:41][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:19:14:225 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:19:14:236 ==>> 检测【配置蓝牙地址】
2025-07-31 18:19:14:259 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:19:14:281 ==>> +WIFISCAN:4,0,CC057790A7C1,-71
+WIFISCAN:4,1,44A1917CA62F,-72
+WIFISCAN:4,2,CC057790A7C0,-73
+WIFISCAN:4,3,CC057790A741,-74

[D][05:18:41][CAT1]wifi scan report total[4]


2025-07-31 18:19:14:356 ==>> [W][05:18:42][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:19:14:431 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:FD95DC83243D>】
2025-07-31 18:19:14:440 ==>> [D][05:18:42][COMM]53150 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:19:14:672 ==>> recv ble 1
recv ble 2
ble set mac ok :fd,95,dc,83,24,3d
enable filters ret : 0

2025-07-31 18:19:14:714 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:19:14:720 ==>> 检测【BLETEST】
2025-07-31 18:19:14:730 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:19:14:762 ==>> 4A A4 01 A4 4A 


2025-07-31 18:19:14:867 ==>> recv ble 1
recv ble 2
<BSJ*MAC:FD95DC83243D*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9FD95DC83243D99999OVER 150


2025-07-31 18:19:15:236 ==>> [D][05:18:42][GNSS]recv submsg id[3]


2025-07-31 18:19:15:450 ==>> [D][05:18:43][COMM]54161 imu init OK
[D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:19:15:741 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:19:15:749 ==>> 该项需要延时执行
2025-07-31 18:19:16:111 ==>> [D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:19:16:456 ==>> [D][05:18:44][COMM]55172 imu init OK


2025-07-31 18:19:18:027 ==>> [D][05:18:45][PROT]CLEAN,SEND:1
[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:5
[D][05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:1
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][PROT]sending traceid [9999999999900006]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[198]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:45][CAT1]Send Data To Server[198][201] ... ->:
0063B98

2025-07-31 18:19:18:102 ==>> 1113311331133113311331B88B32A87EDE80FE14EE154F317527B7401952EA2C8FF6EE81BC357510A77166851413D26FA9DC77A9302AD4EC74C39330621316AAA04E31B18108565EECF23AEBD912A7F694115B28283DAAA03FA8948080B926D
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]


2025-07-31 18:19:18:132 ==>>                                          

2025-07-31 18:19:20:032 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 18:19:22:057 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 18:19:23:267 ==>> [D][05:18:50][PROT]CLEAN,SEND:1
[D][05:18:50][PROT]CLEAN:1
[D][05:18:50][PROT]index:0 1629955130
[D][05:18:50][PROT]is_send:0
[D][05:18:50][PROT]sequence_num:4
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][PROT]sending traceid [9999999999900005]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[198]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052df8] format[0]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:50][CAT1]Send Data To Serv

2025-07-31 18:19:23:342 ==>> er[198][201] ... ->:
0063B982113311331133113311331B88B52D682C484658D4214208C8B5856A7C15C2C64DA1F57C5ACE5C94D77B7BF53F7528C126718F946EAD19790F3335C767065C2D4529AABF33B152C03F0D03E06B7B284EC26F0411D89A56945CDB09E21AE146DD
[D][05:18:50][CAT1]<<< 
SEND OK

[D][05:18:50][CAT1]exec over: func id: 15, ret: 11
[D][05:18:50][CAT1]sub id: 15, ret: 11

[D][05:18:50][SAL ]Cellular task submsg id[68]
[D][05:18:50][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:50][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:50][M2M ]g_m2m_is_idle become true
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:50][PROT]M2M Send ok [1629955130]


2025-07-31 18:19:24:057 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 18:19:25:559 ==>> [D][05:18:53][COMM]IMU: [19,2,-1028] ret=113 AWAKE!


2025-07-31 18:19:25:756 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:19:25:762 ==>> 检测【检测WiFi结果】
2025-07-31 18:19:25:767 ==>> WiFi信号:【CC057790A7C1】,信号值:-74
2025-07-31 18:19:25:787 ==>> WiFi信号:【CC057790A740】,信号值:-75
2025-07-31 18:19:25:796 ==>> WiFi信号:【CC057790A5C1】,信号值:-79
2025-07-31 18:19:25:805 ==>> WiFi信号:【646E97BD0450】,信号值:-85
2025-07-31 18:19:25:813 ==>> WiFi信号:【CC057790A741】,信号值:-74
2025-07-31 18:19:25:823 ==>> WiFi信号:【44A1917CA62F】,信号值:-72
2025-07-31 18:19:25:840 ==>> WiFi信号:【CC057790A7C0】,信号值:-73
2025-07-31 18:19:25:846 ==>> WiFi数量【7】, 最大信号值:-72
2025-07-31 18:19:25:871 ==>> 检测【检测GPS结果】
2025-07-31 18:19:25:881 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:19:25:972 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all continue location stop
[W][05:18:53][GNSS]stop locating
[D][05:18:53][GNSS]all sing location stop


2025-07-31 18:19:26:047 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 18:19:26:773 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:19:26:782 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:26:793 ==>> 定位已等待【1】秒.
2025-07-31 18:19:27:207 ==>> [D][05:18:54][HSDK]need to erase for write: is[0x0] ie[0x1E00]
[D][05:18:54][HSDK][0] flush to flash addr:[0xE42E00] --- write len --- [256]
[W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:54][COMM]Open GPS Module...
[D][05:18:54][COMM]LOC_MODEL_CONT
[D][05:18:54][GNSS]start event:8
[D][05:18:54][GNSS]GPS start. ret=0
[W][05:18:54][GNSS]start cont locating
[D][05:18:54][CAT1]gsm read msg sub id: 23
[D][05:18:54][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:54][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:54][CAT1]<<< 
OK

[D][05:18:54][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:19:27:282 ==>>                                                                                 1 AWAKE!


2025-07-31 18:19:27:433 ==>> [D][05:18:55][COMM]IMU: [15,7,-998] ret=40 AWAKE!


2025-07-31 18:19:27:784 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:27:815 ==>> 定位已等待【2】秒.
2025-07-31 18:19:27:904 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:19:28:055 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 18:19:28:434 ==>> [D][05:18:55][PROT]CLEAN,SEND:0
[D][05:18:55][PROT]index:0 1629955135
[D][05:18:55][PROT]is_send:0
[D][05:18:55][PROT]sequence_num:4
[D][05:18:55][PROT]retry_timeout:0
[D][05:18:55][PROT]retry_times:1
[D][05:18:55][PROT]send_path:0x2
[D][05:18:55][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:55][PROT]===========================================================
[W][05:18:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955135]
[D][05:18:55][PROT]===========================================================
[D][05:18:55][PROT]sending traceid [9999999999900005]
[D][05:18:55][PROT]Send_TO_M2M [1629955135]
[D][05:18:55][COMM]M->S yaw:INVALID
[D][05:18:55][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:55][SAL ]sock send credit cnt[6]
[D][05:18:55][SAL ]sock send ind credit cnt[6]
[D][05:18:55][M2M ]m2m send data len[198]
[D][05:18:55][SAL ]Cellular task submsg id[10]
[D][05:18:55][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053048] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 18:19:28:539 ==>> [D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:19:28:799 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:28:810 ==>> 定位已等待【3】秒.
2025-07-31 18:19:28:874 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,06,24,,,42,42,,,40,38,,,39,39,,,37,1*7E

$GBGSV,2,2,06,26,,,36,13,,,37,1*77

[D][05:18:56][CAT1]<<< 
OK

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.541,1608.541,51.416,2097152,2097152,2097152*48

[D][05:18:56][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:56][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]exec over: func id: 23, ret: 6
[D][05:18:56][CAT1]sub id: 23, ret: 6

[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B57B4A2396D8967555447AE398966B0E76AE5FA57225AB1EDEE5B271522515B897ED324D108E04A68AC45784ADBDFFC5E5FF44F87160D09B56AB1FA9634C64665335C85C291A6846B7CF5A5E34886E2C941A5B
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:

2025-07-31 18:19:28:919 ==>> 18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 18:19:29:314 ==>> [D][05:18:57][GNSS]recv submsg id[1]
[D][05:18:57][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:19:29:697 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,24,,,41,42,,,40,38,,,39,13,,,39,1*7C

$GBGSV,3,2,11,60,,,39,16,,,38,3,,,38,8,,,38,1*7C

$GBGSV,3,3,11,39,,,37,26,,,37,59,,,33,1*74

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1579.137,1579.137,50.471,2097152,2097152,2097152*48



2025-07-31 18:19:29:802 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:29:812 ==>> 定位已等待【4】秒.
2025-07-31 18:19:30:078 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 18:19:30:721 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,24,,,41,42,,,40,38,,,40,60,,,40,1*7B

$GBGSV,4,2,15,13,,,39,3,,,39,8,,,38,39,,,38,1*77

$GBGSV,4,3,15,26,,,38,16,,,37,1,,,36,59,,,35,1*47

$GBGSV,4,4,15,2,,,35,4,,,32,5,,,30,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1542.256,1542.256,49.344,2097152,2097152,2097152*41



2025-07-31 18:19:30:811 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:30:821 ==>> 定位已等待【5】秒.
2025-07-31 18:19:31:724 ==>> $GBGGA,101935.530,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,41,42,,,40,38,,,40,60,,,40,1*78

$GBGSV,5,2,17,13,,,39,3,,,39,26,,,39,21,,,39,1*41

$GBGSV,5,3,17,8,,,38,39,,,38,16,,,37,59,,,37,1*4F

$GBGSV,5,4,17,1,,,36,33,,,36,2,,,35,4,,,31,1*42

$GBGSV,5,5,17,5,,,30,1*46

$GBRMC,101935.530,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101935.530,0.000,1548.594,1548.594,49.544,2097152,2097152,2097152*50



2025-07-31 18:19:31:814 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:31:826 ==>> 定位已等待【6】秒.
2025-07-31 18:19:32:069 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 18:19:32:710 ==>> $GBGGA,101936.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,41,42,,,40,38,,,40,60,,,40,1*7C

$GBGSV,5,2,20,3,,,40,13,,,39,26,,,39,8,,,39,1*70

$GBGSV,5,3,20,21,,,38,39,,,38,16,,,38,59,,,38,1*70

$GBGSV,5,4,20,33,,,36,1,,,35,2,,,34,9,,,34,1*4C

$GBGSV,5,5,20,14,,,34,4,,,31,5,,,31,40,,,29,1*78

$GBRMC,101936.510,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101936.510,0.000,1521.548,1521.548,48.704,2097152,2097152,2097152*56



2025-07-31 18:19:32:815 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:32:824 ==>> 定位已等待【7】秒.
2025-07-31 18:19:33:732 ==>> $GBGGA,101937.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,60,,,39,13,,,39,26,,,39,8,,,38,1*48

$GBGSV,6,3,21,21,,,38,39,,,38,59,,,38,16,,,37,1*7D

$GBGSV,6,4,21,33,,,36,1,,,36,9,,,35,6,,,35,1*49

$GBGSV,6,5,21,2,,,34,14,,,34,4,,,31,5,,,30,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101937.510,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101937.510,0.000,1518.186,1518.186,48.590,2097152,2097152,2097152*58



2025-07-31 18:19:33:822 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:33:832 ==>> 定位已等待【8】秒.
2025-07-31 18:19:34:176 ==>> [D][05:19:01][PROT]CLEAN,SEND:0
[D][05:19:01][PROT]CLEAN:0
[D][05:19:01][PROT]index:2 1629955141
[D][05:19:01][PROT]is_send:0
[D][05:19:01][PROT]sequence_num:6
[D][05:19:01][PROT]retry_timeout:0
[D][05:19:01][PROT]retry_times:3
[D][05:19:01][PROT]send_path:0x2
[D][05:19:01][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:01][PROT]===========================================================
[W][05:19:01][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955141]
[D][05:19:01][PROT]===========================================================
[D][05:19:01][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:01][PROT]sending traceid [9999999999900007]
[D][05:19:01][PROT]Send_TO_M2M [1629955141]
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:01][SAL ]sock send credit cnt[6]
[D][05:19:01][SAL ]sock send ind credit cnt[6]
[D][05:19:01][M2M ]m2m send data len[134]
[D][05:19:01][SAL ]Cellular task submsg id[10]
[D][05:19:01][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:01][CAT1]gsm read msg sub id: 15
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:01][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:01][CAT1]Send Data To Server[134

2025-07-31 18:19:34:251 ==>> ][137] ... ->:
0043B683113311331133113311331B88BE428650D7CE3AE6F2853FB13AE9EAB7C271137780F990F31EBE76BD30E0F872A36E41BE334C07E611DD4431DE6A8FE1E4EF25
[D][05:19:01][CAT1]<<< 
SEND OK

[D][05:19:01][CAT1]exec over: func id: 15, ret: 11
[D][05:19:01][CAT1]sub id: 15, ret: 11

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:01][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:01][M2M ]g_m2m_is_idle become true
[D][05:19:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:01][PROT]M2M Send ok [1629955141]
[D][05:19:01][COMM]read battery soc:255


2025-07-31 18:19:34:707 ==>> $GBGGA,101938.510,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,60,,,40,13,,,39,26,,,39,8,,,39,1*47

$GBGSV,6,3,21,21,,,38,39,,,38,59,,,38,16,,,37,1*7D

$GBGSV,6,4,21,33,,,36,1,,,36,6,,,36,9,,,35,1*4A

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,30,1*40

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101938.510,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101938.510,0.000,1526.083,1526.083,48.843,2097152,2097152,2097152*54



2025-07-31 18:19:34:827 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:34:837 ==>> 定位已等待【9】秒.
2025-07-31 18:19:35:709 ==>> $GBGGA,101939.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,41,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,21,60,,,40,13,,,39,26,,,39,8,,,39,1*47

$GBGSV,6,3,21,21,,,38,39,,,38,59,,,38,16,,,38,1*72

$GBGSV,6,4,21,33,,,36,1,,,36,6,,,36,9,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,30,1*40

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101939.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101939.510,0.000,1528.053,1528.053,48.902,2097152,2097152,2097152*51



2025-07-31 18:19:35:829 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:35:838 ==>> 定位已等待【10】秒.
2025-07-31 18:19:36:076 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 18:19:36:712 ==>> $GBGGA,101940.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,41,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,21,60,,,40,13,,,39,26,,,39,8,,,39,1*47

$GBGSV,6,3,21,39,,,38,59,,,38,16,,,38,21,,,37,1*7D

$GBGSV,6,4,21,33,,,36,6,,,36,9,,,36,1,,,35,1*4A

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,31,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101940.510,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101940.510,0.000,1526.075,1526.075,48.834,2097152,2097152,2097152*5B



2025-07-31 18:19:36:832 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:36:842 ==>> 定位已等待【11】秒.
2025-07-31 18:19:37:739 ==>> $GBGGA,101941.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,60,,,40,13,,,39,26,,,39,8,,,39,1*47

$GBGSV,6,3,21,39,,,38,59,,,38,16,,,38,21,,,37,1*7D

$GBGSV,6,4,21,33,,,36,6,,,36,9,,,36,1,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,31,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101941.510,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101941.510,0.000,1530.025,1530.025,48.963,2097152,2097152,2097152*59



2025-07-31 18:19:37:844 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:37:853 ==>> 定位已等待【12】秒.
2025-07-31 18:19:38:089 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 18:19:38:711 ==>> $GBGGA,101942.510,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,60,,,40,13,,,39,26,,,39,8,,,39,1*47

$GBGSV,6,3,21,39,,,38,59,,,38,16,,,38,21,,,38,1*72

$GBGSV,6,4,21,33,,,36,6,,,36,9,,,36,1,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,31,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101942.510,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101942.510,0.000,1532.000,1532.000,49.026,2097152,2097152,2097152*53



2025-07-31 18:19:38:846 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:38:858 ==>> 定位已等待【13】秒.
2025-07-31 18:19:39:392 ==>> [D][05:19:06][PROT]CLEAN,SEND:2
[D][05:19:06][PROT]index:2 1629955146
[D][05:19:06][PROT]is_send:0
[D][05:19:06][PROT]sequence_num:6
[D][05:19:06][PROT]retry_timeout:0
[D][05:19:06][PROT]retry_times:2
[D][05:19:06][PROT]send_path:0x2
[D][05:19:06][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:06][PROT]===========================================================
[W][05:19:06][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955146]
[D][05:19:06][PROT]===========================================================
[D][05:19:06][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:06][PROT]sending traceid [9999999999900007]
[D][05:19:06][PROT]Send_TO_M2M [1629955146]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:06][SAL ]sock send credit cnt[6]
[D][05:19:06][SAL ]sock send ind credit cnt[6]
[D][05:19:06][M2M ]m2m send data len[134]
[D][05:19:06][SAL ]Cellular task submsg id[10]
[D][05:19:06][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:06][CAT1]gsm read msg sub id: 15
[D][05:19:06][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:06][CAT1]Send

2025-07-31 18:19:39:467 ==>>  Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BE0E32DFDF71FB6D56392504CDFB2C691F21774F115E92D42BF63C2A5B49AA5847BFF6F47A0D93E3363D7A8C5879C94A86512E
[D][05:19:06][CAT1]<<< 
SEND OK

[D][05:19:06][CAT1]exec over: func id: 15, ret: 11
[D][05:19:06][CAT1]sub id: 15, ret: 11

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:06][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:06][M2M ]g_m2m_is_idle become true
[D][05:19:06][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:06][PROT]M2M Send ok [1629955146]


2025-07-31 18:19:39:712 ==>> $GBGGA,101943.510,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,26,,,40,60,,,39,13,,,39,8,,,39,1*47

$GBGSV,6,3,21,39,,,38,59,,,38,21,,,38,16,,,37,1*7D

$GBGSV,6,4,21,33,,,36,6,,,36,9,,,36,1,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,31,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101943.510,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101943.510,0.000,1530.025,1530.025,48.963,2097152,2097152,2097152*5B



2025-07-31 18:19:39:847 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:39:857 ==>> 定位已等待【14】秒.
2025-07-31 18:19:40:094 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 18:19:40:720 ==>> $GBGGA,101944.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,21,26,,,39,60,,,39,13,,,39,8,,,39,1*49

$GBGSV,6,3,21,39,,,38,59,,,38,21,,,37,16,,,37,1*72

$GBGSV,6,4,21,33,,,36,6,,,36,9,,,36,1,,,36,1*49

$GBGSV,6,5,21,14,,,35,2,,,34,4,,,31,5,,,31,1*41

$GBGSV,6,6,21,40,,,30,1*72

$GBRMC,101944.510,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101944.510,0.000,1526.075,1526.075,48.834,2097152,2097152,2097152*5F



2025-07-31 18:19:40:855 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:40:885 ==>> 定位已等待【15】秒.
2025-07-31 18:19:41:741 ==>> $GBGGA,101945.510,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,42,42,,,40,38,,,40,3,,,40,1*4A

$GBGSV,6,2,23,26,,,39,60,,,39,13,,,39,8,,,39,1*4B

$GBGSV,6,3,23,39,,,38,59,,,38,21,,,38,16,,,37,1*7F

$GBGSV,6,4,23,33,,,36,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,23,14,,,35,2,,,34,4,,,31,5,,,31,1*43

$GBGSV,6,6,23,40,,,30,7,,,37,22,,,36,1*46

$GBRMC,101945.510,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101945.510,0.000,1528.049,1528.049,48.898,2097152,2097152,2097152*58



2025-07-31 18:19:41:861 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:41:870 ==>> 定位已等待【16】秒.
2025-07-31 18:19:42:093 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 18:19:42:735 ==>> $GBGGA,101946.510,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,22,26,,,39,60,,,39,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,39,,,38,59,,,38,21,,,37,16,,,37,1*71

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,36,,,37,1*70

$GBRMC,101946.510,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101946.510,0.000,1528.045,1528.045,48.893,2097152,2097152,2097152*50



2025-07-31 18:19:42:870 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:42:881 ==>> 定位已等待【17】秒.
2025-07-31 18:19:43:728 ==>> $GBGGA,101947.510,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,3,,,41,42,,,40,38,,,40,1*4A

$GBGSV,6,2,22,60,,,40,26,,,39,13,,,39,8,,,39,1*44

$GBGSV,6,3,22,39,,,38,59,,,38,16,,,38,21,,,37,1*7E

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,31,4,,,31,1*42

$GBGSV,6,6,22,40,,,30,7,,,27,1*43

$GBRMC,101947.510,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101947.510,0.000,1513.275,1513.275,48.457,2097152,2097152,2097152*55



2025-07-31 18:19:43:880 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:43:890 ==>> 定位已等待【18】秒.
2025-07-31 18:19:44:108 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 18:19:44:615 ==>> [D][05:19:12][PROT]CLEAN,SEND:2
[D][05:19:12][PROT]index:2 1629955152
[D][05:19:12][PROT]is_send:0
[D][05:19:12][PROT]sequence_num:6
[D][05:19:12][PROT]retry_timeout:0
[D][05:19:12][PROT]retry_times:1
[D][05:19:12][PROT]send_path:0x2
[D][05:19:12][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:12][PROT]===========================================================
[W][05:19:12][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955152]
[D][05:19:12][PROT]===========================================================
[D][05:19:12][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A9C89C8906980220
[D][05:19:12][PROT]sending traceid [9999999999900007]
[D][05:19:12][PROT]Send_TO_M2M [1629955152]
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:12][SAL ]sock send credit cnt[6]
[D][05:19:12][SAL ]sock send ind credit cnt[6]
[D][05:19:12][M2M ]m2m send data len[134]
[D][05:19:12][SAL ]Cellular task submsg id[10]
[D][05:19:12][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052df8] format[0]
[D][05:19:12][CAT1]gsm read msg sub id: 15
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:12][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:12][CAT1]Send Data To Server[134][137] ... ->:
004

2025-07-31 18:19:44:720 ==>> 3B686113311331133113311331B88BE4B4322CFB561FACC2A9922EFB55C71EDFB2029B6E9532E74A6744AE2F852FE3C1A2EF4468B9574A28C18D025335439CB0925
[D][05:19:12][CAT1]<<< 
SEND OK

[D][05:19:12][CAT1]exec over: func id: 15, ret: 11
[D][05:19:12][CAT1]sub id: 15, ret: 11

[D][05:19:12][SAL ]Cellular task submsg id[68]
[D][05:19:12][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:12][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:12][M2M ]g_m2m_is_idle become true
[D][05:19:12][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:12][PROT]M2M Send ok [1629955152]
                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:19:44:750 ==>>                                                                                                                                   

2025-07-31 18:19:44:886 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:44:896 ==>> 定位已等待【19】秒.
2025-07-31 18:19:45:740 ==>> $GBGGA,101949.510,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,3,,,40,42,,,40,38,,,40,1*48

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,38,1*4B

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,37,21,,,37,1*71

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,35,1*49

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,31,4,,,31,1*42

$GBGSV,6,6,22,40,,,29,7,,,29,1*45

$GBRMC,101949.510,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101949.510,0.000,1503.838,1503.838,48.141,2097152,2097152,2097152*59



2025-07-31 18:19:45:892 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:45:902 ==>> 定位已等待【20】秒.
2025-07-31 18:19:46:125 ==>> [D][05:19:13][COMM]read battery soc:255


2025-07-31 18:19:46:735 ==>> $GBGGA,101950.510,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,3,,,40,42,,,40,38,,,40,1*4B

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,37,21,,,37,1*71

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,31,4,,,31,1*42

$GBGSV,6,6,22,40,,,30,7,,,29,1*4D

$GBRMC,101950.510,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101950.510,0.000,1511.375,1511.375,48.382,2097152,2097152,2097152*5C



2025-07-31 18:19:46:900 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:46:910 ==>> 定位已等待【21】秒.
2025-07-31 18:19:47:558 ==>> [D][05:19:15][COMM]S->M yaw:INVALID


2025-07-31 18:19:47:738 ==>> $GBGGA,101951.510,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,42,,,40,38,,,40,3,,,39,1*46

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,38,1*4B

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,37,21,,,37,1*71

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,35,1*49

$GBGSV,6,5,22,14,,,35,2,,,33,5,,,31,4,,,31,1*45

$GBGSV,6,6,22,40,,,30,7,,,29,1*4D

$GBRMC,101951.510,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101951.510,0.000,1501.949,1501.949,48.077,2097152,2097152,2097152*54

[D][05:19:15][COMM]IMU: [11,-15,-1005] ret=32 AWAKE!


2025-07-31 18:19:47:905 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:47:918 ==>> 定位已等待【22】秒.
2025-07-31 18:19:48:132 ==>> [D][05:19:15][COMM]read battery soc:255


2025-07-31 18:19:48:741 ==>> [D][05:19:16][COMM]M->S yaw:INVALID
$GBGGA,101952.510,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,38,1*4B

$GBGSV,6,3,22,59,,,38,39,,,38,16,,,37,21,,,37,1*71

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,35,1*49

$GBGSV,6,5,22,14,,,35,2,,,33,5,,,31,4,,,31,1*45

$GBGSV,6,6,22,40,,,30,7,,,29,1*4D

$GBRMC,101952.510,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101952.510,0.000,1503.836,1503.836,48.139,2097152,2097152,2097152*5C



2025-07-31 18:19:48:907 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:48:917 ==>> 定位已等待【23】秒.
2025-07-31 18:19:49:720 ==>> [D][05:19:17][PROT]CLEAN,SEND:2
[D][05:19:17][PROT]CLEAN:2
$GBGGA,101953.510,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,38,1*4B

$GBGSV,6,3,22,59,,,38,39,,,38,21,,,38,16,,,37,1*7E

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101953.510,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101953.510,0.000,1515.135,1515.135,48.493,2097152,2097152,2097152*58



2025-07-31 18:19:49:915 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:49:928 ==>> 定位已等待【24】秒.
2025-07-31 18:19:50:147 ==>> [D][05:19:17][COMM]read battery soc:255


2025-07-31 18:19:50:732 ==>> $GBGGA,101954.510,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,59,,,39,1*7E

$GBGSV,6,3,22,8,,,38,39,,,38,21,,,38,16,,,37,1*4A

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101954.510,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101954.510,0.000,1517.021,1517.021,48.555,2097152,2097152,2097152*54



2025-07-31 18:19:50:930 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:50:941 ==>> 定位已等待【25】秒.
2025-07-31 18:19:51:738 ==>> $GBGGA,101955.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,22,60,,,40,26,,,39,13,,,39,59,,,39,1*70

$GBGSV,6,3,22,8,,,38,39,,,38,21,,,38,16,,,37,1*4A

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101955.510,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101955.510,0.000,1518.907,1518.907,48.617,2097152,2097152,2097152*50



2025-07-31 18:19:51:934 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:51:949 ==>> 定位已等待【26】秒.
2025-07-31 18:19:52:144 ==>> [D][05:19:19][COMM]read battery soc:255


2025-07-31 18:19:52:709 ==>> $GBGGA,101956.510,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,41,42,,,40,38,,,40,3,,,40,1*48

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,59,,,39,1*7E

$GBGSV,6,3,22,8,,,38,39,,,38,21,,,38,16,,,38,1*45

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101956.510,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101956.510,0.000,1518.903,1518.903,48.612,2097152,2097152,2097152*56



2025-07-31 18:19:52:938 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:52:948 ==>> 定位已等待【27】秒.
2025-07-31 18:19:53:732 ==>> $GBGGA,101957.510,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,40,38,,,40,3,,,40,1*4B

$GBGSV,6,2,22,60,,,39,26,,,39,13,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,38,39,,,38,21,,,38,16,,,37,1*7E

$GBGSV,6,4,22,33,,,36,6,,,36,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101957.510,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101957.510,0.000,1517.021,1517.021,48.555,2097152,2097152,2097152*57



2025-07-31 18:19:53:943 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:53:956 ==>> 定位已等待【28】秒.
2025-07-31 18:19:54:173 ==>> [D][05:19:21][COMM]read battery soc:255


2025-07-31 18:19:54:708 ==>> $GBGGA,101958.510,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,40,3,,,40,1*4A

$GBGSV,6,2,22,26,,,40,13,,,40,60,,,39,8,,,39,1*4A

$GBGSV,6,3,22,59,,,39,39,,,38,21,,,38,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,31,1*41

$GBGSV,6,6,22,40,,,30,7,,,30,1*45

$GBRMC,101958.510,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101958.510,0.000,1528.335,1528.335,48.923,2097152,2097152,2097152*55



2025-07-31 18:19:54:948 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:54:960 ==>> 定位已等待【29】秒.
2025-07-31 18:19:55:712 ==>> $GBGGA,101959.510,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,40,3,,,40,1*4A

$GBGSV,6,2,22,26,,,40,13,,,40,60,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,21,,,38,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,36,9,,,36,1,,,36,1*4B

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,31,40,,,30,1*44

$GBRMC,101959.510,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101959.510,0.000,1533.982,1533.982,49.097,2097152,2097152,2097152*53



2025-07-31 18:19:55:957 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:55:967 ==>> 定位已等待【30】秒.
2025-07-31 18:19:56:190 ==>> [D][05:19:23][COMM]read battery soc:255


2025-07-31 18:19:56:714 ==>> $GBGGA,102000.510,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,40,3,,,40,1*4A

$GBGSV,6,2,22,26,,,40,13,,,40,60,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,21,,,38,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,37,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,31,40,,,30,1*44

$GBRMC,102000.510,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102000.510,0.000,1535.866,1535.866,49.157,2097152,2097152,2097152*58



2025-07-31 18:19:56:959 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:56:970 ==>> 定位已等待【31】秒.
2025-07-31 18:19:57:733 ==>> $GBGGA,102001.510,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,40,3,,,40,1*4A

$GBGSV,6,2,22,26,,,40,13,,,40,60,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,38,21,,,38,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,37,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,35,2,,,34,5,,,32,4,,,32,1*42

$GBGSV,6,6,22,7,,,31,40,,,30,1*44

$GBRMC,102001.510,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102001.510,0.000,766.510,766.510,700.992,2097152,2097152,2097152*62



2025-07-31 18:19:57:964 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:57:975 ==>> 定位已等待【32】秒.
2025-07-31 18:19:58:199 ==>> [D][05:19:25][COMM]read battery soc:255


2025-07-31 18:19:58:738 ==>> $GBGGA,102002.510,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,42,42,,,41,13,,,40,60,,,40,1*77

$GBGSV,6,2,23,38,,,40,3,,,40,26,,,40,8,,,39,1*79

$GBGSV,6,3,23,59,,,39,39,,,39,16,,,38,21,,,38,1*70

$GBGSV,6,4,23,33,,,37,9,,,36,1,,,36,6,,,36,1*4A

$GBGSV,6,5,23,14,,,35,2,,,34,5,,,32,4,,,32,1*43

$GBGSV,6,6,23,7,,,31,40,,,30,45,,,36,1*41

$GBRMC,102002.510,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102002.510,0.000,766.511,766.511,700.994,2097152,2097152,2097152*67



2025-07-31 18:19:58:979 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:19:58:990 ==>> 定位已等待【33】秒.
2025-07-31 18:19:59:778 ==>> $GBGGA,101959.516,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,42,70,11,41,24,65,239,42,3,64,190,40,16,63,314,38,1*7C

$GBGSV,6,2,22,39,61,341,39,38,61,172,40,13,60,213,40,8,58,201,39,1*43

$GBGSV,6,3,22,6,53,9,36,59,52,130,39,2,49,241,34,1,47,123,36,1*44

$GBGSV,6,4,22,9,41,286,36,60,41,238,40,4,32,111,32,5,24,258,32,1*47

$GBGSV,6,5,22,7,17,181,30,40,13,168,30,21,2,304,38,26,,,40,1*48

$GBGSV,6,6,22,33,,,37,14,,,35,1*71

$GBRMC,101959.516,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.001,K,N*21

$GBGST,101959.516,0.000,0.213,0.185,0.383,4.760,11,83*78



2025-07-31 18:19:59:989 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:20:00:000 ==>> 定位已等待【34】秒.
2025-07-31 18:20:00:207 ==>> [D][05:19:27][COMM]read battery soc:255


2025-07-31 18:20:00:997 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:20:01:017 ==>> 定位已等待【35】秒.
2025-07-31 18:20:01:836 ==>> $GBGGA,102000.516,2301.2576481,N,11421.9410521,E,1,08,2.51,71.516,M,-1.770,M,,*5F

$GBGSA,A,3,13,08,42,24,16,39,38,60,,,,,6.64,2.51,6.15,4*02

$GBGSV,6,1,23,13,79,244,39,8,76,190,39,42,70,11,41,24,65,239,42,1*70

$GBGSV,6,2,23,3,64,190,40,16,63,314,37,39,61,341,38,38,61,172,40,1*42

$GBGSV,6,3,23,26,58,29,40,6,53,9,36,59,52,130,39,19,50,61,40,1*45

$GBGSV,6,4,23,2,49,241,34,1,47,123,36,60,44,243,40,9,41,286,36,1*4F

$GBGSV,6,5,23,4,32,111,32,5,24,258,32,7,17,181,30,40,13,168,30,1*4C

$GBGSV,6,6,23,21,2,304,38,33,,,37,14,,,35,1*7D

$GBGSV,1,1,04,42,70,11,41,24,65,239,42,39,61,341,40,38,61,172,40,5*4A

$GBRMC,102000.516,A,2301.2576481,N,11421.9410521,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

[D][05:19:28][GNSS]HD8040 GPS
[D][05:19:28][GNSS]GPS diff_sec 124002032, report 0x42 frame
$GBGST,102000.516,0.847,0.370,0.297,0.546,2.073,4.758,22*56

[D][05:19:28][COMM]Main Task receive event:131
[D][05:19:28][COMM]index:0,power_mode:0xFF
[D][05:19:28][COMM]index:1,sound_mode:0xFF
[D][05:19:28][COMM]index:2,gsensor_mode:0xFF
[D][05:19:28][COMM]index:3,report_freq_mode:0xFF
[D][05:19:28][COMM]index:4,report_period:0xFF
[D][05:19:28][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:28][COMM]index:6,normal_

2025-07-31 18:20:01:941 ==>> reset_period:0xFF
[D][05:19:28][COMM]index:7,spock_over_speed:0xFF
[D][05:19:28][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:28][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:28][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:28][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:28][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:28][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:28][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:28][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:28][COMM]index:16,imu_config_params:0xFF
[D][05:19:28][COMM]index:17,long_connect_params:0xFF
[D][05:19:28][COMM]index:18,detain_mark:0xFF
[D][05:19:28][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:28][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:28][COMM]index:21,mc_mode:0xFF
[D][05:19:28][COMM]index:22,S_mode:0xFF
[D][05:19:28][COMM]index:23,overweight:0xFF
[D][05:19:28][COMM]index:24,standstill_mode:0xFF
[D][05:19:28][COMM]index:25,night_mode:0xFF
[D][05:19:28][COMM]index:26,experiment1:0xFF
[D][05:19:28][COMM]index:27,experiment2:0xFF
[D][05:19:28][COMM]index:28,experiment3:0xFF
[D][05:19:28][COMM]index:29,experiment4:0xFF
[D][05:19:28][COMM]index:30,night

2025-07-31 18:20:02:001 ==>> 符合定位需求的卫星数量:【18】
2025-07-31 18:20:02:011 ==>> 
北斗星号:【13】,信号值:【39】
北斗星号:【8】,信号值:【39】
北斗星号:【42】,信号值:【41】
北斗星号:【24】,信号值:【42】
北斗星号:【3】,信号值:【40】
北斗星号:【16】,信号值:【37】
北斗星号:【39】,信号值:【40】
北斗星号:【38】,信号值:【40】
北斗星号:【26】,信号值:【40】
北斗星号:【6】,信号值:【36】
北斗星号:【59】,信号值:【39】
北斗星号:【19】,信号值:【40】
北斗星号:【1】,信号值:【36】
北斗星号:【60】,信号值:【40】
北斗星号:【9】,信号值:【36】
北斗星号:【21】,信号值:【38】
北斗星号:【33】,信号值:【37】
北斗星号:【14】,信号值:【35】

2025-07-31 18:20:02:032 ==>> 检测【CSQ强度】
2025-07-31 18:20:02:047 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:20:02:068 ==>> _mode_start:0xFF
[D][05:19:28][COMM]index:31,night_mode_end:0xFF
[D][05:19:28][COMM]index:33,park_report_minutes:0xFF
[D][05:19:28][COMM]index:34,park_report_mode:0xFF
[D][05:19:28][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:28][COMM]index:38,charge_battery_para: FF
[D][05:19:28][COMM]index:39,multirider_mode:0xFF
[D][05:19:28][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:28][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:28][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:28][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:28][COMM]index:44,riding_duration_config:0xFF
[D][05:19:28][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:28][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:28][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:28][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:28][COMM]index:49,mc_load_startup:0xFF
[D][05:19:28][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:28][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:28][COMM]index:52,traffic_mode:0xFF
[D][05:19:28][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:28][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:28][COMM]index:55,wheel_alarm_play_switch:255
[D][

2025-07-31 18:20:02:152 ==>> 05:19:28][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:28][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:28][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:28][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:28][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:28][COMM]index:63,experiment5:0xFF
[D][05:19:28][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:28][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:28][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:28][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:28][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:28][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:28][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:28][COMM]index:72,experiment6:0xFF
[D][05:19:28][COMM]index:73,experiment7:0xFF
[D][05:19:28][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:28][COMM]index:75,zero_value_from_server:-1
[D][05:19:28][COMM]index:76,multirider_threshold:255
[D][05:19:28][COMM]index:77,experiment8:255
[D][05:19:28][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:28][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:28][COMM]index:80,temp_park_remind

2025-07-31 18:20:02:257 ==>> er_timeout_duration:255
[D][05:19:28][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:28][COMM]index:83,loc_report_interval:255
[D][05:19:28][COMM]index:84,multirider_threshold_p2:255
[D][05:19:28][COMM]index:85,multirider_strategy:255
[D][05:19:28][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:28][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:28][COMM]index:90,weight_param:0xFF
[D][05:19:28][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:28][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:28][COMM]index:95,current_limit:0xFF
[D][05:19:28][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:28][COMM]index:100,location_mode:0xFF

[W][05:19:28][PROT]remove success[1629955168],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:28][PROT]add success [1629955168],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:28][COMM]Main Task receive event:131 finished processing
[D][05:19:28][PROT]index:0 1629955168
[D][05:19:28][PROT]is_send:0
[D][05:19:28][PROT]sequence_num:7
[D][05:19:28][PROT]retry_timeout:0
[D][05:19:28][PROT]retry_times:1
[D][05:19:28][PROT]send_path:0x2
[D][05:19:28][PR

2025-07-31 18:20:02:362 ==>> OT]min_index:0, type:0x4205, priority:0
[D][05:19:28][PROT]===========================================================
[W][05:19:28][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955168]
[D][05:19:28][PROT]===========================================================
[D][05:19:28][PROT]sending traceid [9999999999900008]
[D][05:19:28][PROT]Send_TO_M2M [1629955168]
[D][05:19:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:28][SAL ]sock send credit cnt[6]
[D][05:19:28][SAL ]sock send ind credit cnt[6]
[D][05:19:28][M2M ]m2m send data len[294]
[D][05:19:28][SAL ]Cellular task submsg id[10]
[D][05:19:28][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[0]
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:28][CAT1]gsm read msg sub id: 15
[D][05:19:28][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:28][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88B2EE937D6F9D87DE881A6C58F2313EF01F606A89B09648DF388E4210B5A46B661941884560705C2D531E8CCFBED2782384374AEE5E82668A125BF7F195C8E09F9CA3CE407105190A15FFAE1FDE47E

2025-07-31 18:20:02:453 ==>> CF9530B159EBBB7FB7F4BB5B5CCADEDE6D306267F150E61320846DFAAD3A87C1CCEF7FB50A4C24DD9EC7A3616DE686223DF77B204
[D][05:19:28][CAT1]<<< 
SEND OK

[D][05:19:28][CAT1]exec over: func id: 15, ret: 11
[D][05:19:28][CAT1]sub id: 15, ret: 11

[D][05:19:28][SAL ]Cellular task submsg id[68]
[D][05:19:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:28][M2M ]g_m2m_is_idle become true
[D][05:19:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:28][PROT]M2M Send ok [1629955168]
$GBGGA,102001.016,2301.2577579,N,11421.9413401,E,1,09,1.91,72.199,M,-1.770,M,,*52

$GBGSA,A,3,13,08,42,24,16,39,38,26,60,,,,5.21,1.91,4.85,4*00

$GBGSV,6,1,22,13,79,244,39,8,76,190,39,42,70,11,40,24,65,239,42,1*70

$GBGSV,6,2,22,3,64,190,41,16,63,314,37,39,61,341,38,38,61,172,40,1*42

$GBGSV,6,3,22,26,58,29,40

2025-07-31 18:20:02:558 ==>>                                                                                                                                                         

2025-07-31 18:20:02:664 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     [W][05:19:30][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:30][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:20:02:878 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 18:20:02:888 ==>> 检测【关闭GSM联网】
2025-07-31 18:20:02:897 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:20:03:056 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:30][COMM]GSM test
[D][05:19:30][COMM]GSM test disable


2025-07-31 18:20:03:155 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:20:03:171 ==>> 检测【4G联网测试】
2025-07-31 18:20:03:196 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:20:04:108 ==>> $GBGGA,102003.000,2301.2576752,N,11421.9412541,E,1,16,1.11,72.276,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,42,24,03,16,06,39,38,26,09,59,2.08,1.11,1.76,4*0B

$GBGSA,A,3,02,01,60,40,,,,,,,,,2.08,1.11,1.76,4*02

$GBGSV,6,1,22,13,79,244,39,8,76,190,39,42,70,11,41,24,65,239,42,1*71

$GBGSV,6,2,22,3,64,190,40,16,63,314,37,6,63,309,36,39,61,341,38,1*73

$GBGSV,6,3,22,38,61,172,40,26,58,29,40,9,57,281,36,59,52,126,38,1*79

$GBGSV,6,4,22,2,49,241,34,1,47,123,36,14,46,334,35,60,44,243,40,1*7E

$GBGSV,6,5,22,21,43,116,38,4,32,111,31,5,24,258,32,33,21,324,37,1*75

$GBGSV,6,6,22,7,17,181,30,40,10,165,30,1*48

$GBGSV,2,1,05,42,70,11,43,24,65,239,43,39,61,341,41,38,61,172,40,5*4A

$GBGSV,2,2,05,26,58,29,40,5*71

$GBRMC,102003.000,A,2301.2576752,N,11421.9412541,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,102003.000,1.290,0.221,0.227,0.308,1.336,2.074,11*5A

[D][05:19:31][HSDK][0] flush to flash addr:[0xE42F00] --- write len --- [256]
[W][05:19:31][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:31][COMM]Main Task receive event:14
[D][05:19:31][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 16

2025-07-31 18:20:04:213 ==>> 29955171, allstateRepSeconds = 0
[D][05:19:31][COMM]index:0,power_mode:0xFF
[D][05:19:31][COMM]index:1,sound_mode:0xFF
[D][05:19:31][COMM]index:2,gsensor_mode:0xFF
[D][05:19:31][COMM]index:3,report_freq_mode:0xFF
[D][05:19:31][COMM]index:4,report_period:0xFF
[D][05:19:31][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:31][COMM]index:6,normal_reset_period:0xFF
[D][05:19:31][COMM]index:7,spock_over_speed:0xFF
[D][05:19:31][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:31][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:31][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:31][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:31][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:31][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:31][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:31][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:31][COMM]index:16,imu_config_params:0xFF
[D][05:19:31][COMM]index:17,long_connect_params:0xFF
[D][05:19:31][COMM]index:18,detain_mark:0xFF
[D][05:19:31][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:31][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:31][COMM]index:21,mc_mode:0xFF
[D][05:19:31][COMM]index:22,S_mode:0xFF
[D][05:19:31][COMM]i

2025-07-31 18:20:04:318 ==>> ndex:23,overweight:0xFF
[D][05:19:31][COMM]index:24,standstill_mode:0xFF
[D][05:19:31][COMM]index:25,night_mode:0xFF
[D][05:19:31][COMM]index:26,experiment1:0xFF
[D][05:19:31][COMM]index:27,experiment2:0xFF
[D][05:19:31][COMM]index:28,experiment3:0xFF
[D][05:19:31][COMM]index:29,experiment4:0xFF
[D][05:19:31][COMM]index:30,night_mode_start:0xFF
[D][05:19:31][COMM]index:31,night_mode_end:0xFF
[D][05:19:31][COMM]index:33,park_report_minutes:0xFF
[D][05:19:31][COMM]index:34,park_report_mode:0xFF
[D][05:19:31][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:31][COMM]index:38,charge_battery_para: FF
[D][05:19:31][COMM]index:39,multirider_mode:0xFF
[D][05:19:31][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:31][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:31][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:31][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:31][COMM]index:44,riding_duration_config:0xFF
[D][05:19:31][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:31][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:31][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:31][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:31][COMM]index:49,mc_load_sta

2025-07-31 18:20:04:423 ==>> rtup:0xFF
[D][05:19:31][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:31][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:31][COMM]index:52,traffic_mode:0xFF
[D][05:19:31][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:31][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:31][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:31][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:31][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:31][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:31][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:31][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:31][COMM]index:63,experiment5:0xFF
[D][05:19:31][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:31][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:31][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:31][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:31][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:31][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:31][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:31][COMM]index:72,experiment6:0xFF
[D][05:19:31][COMM]index:73,experiment7:0xFF
[D][05:19:31][COMM]index:74,load_mess

2025-07-31 18:20:04:529 ==>> urement_cfg:0xff
[D][05:19:31][COMM]index:75,zero_value_from_server:-1
[D][05:19:31][COMM]index:76,multirider_threshold:255
[D][05:19:31][COMM]index:77,experiment8:255
[D][05:19:31][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:31][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:31][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:31][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:31][COMM]index:83,loc_report_interval:255
[D][05:19:31][COMM]index:84,multirider_threshold_p2:255
[D][05:19:31][COMM]index:85,multirider_strategy:255
[D][05:19:31][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:31][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:31][COMM]index:90,weight_param:0xFF
[D][05:19:31][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:31][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:31][COMM]index:95,current_limit:0xFF
[D][05:19:31][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:31][COMM]index:100,location_mode:0xFF

[W][05:19:31][PROT]remove success[1629955171],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:31][PROT]add success [1629955171],se

2025-07-31 18:20:04:633 ==>> nd_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:31][PROT]index:0 1629955171
[D][05:19:31][PROT]is_send:0
[D][05:19:31][PROT]sequence_num:8
[D][05:19:31][PROT]retry_timeout:0
[D][05:19:31][PROT]retry_times:1
[D][05:19:31][PROT]send_path:0x2
[D][05:19:31][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:31][PROT]===========================================================
[W][05:19:31][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955171]
[D][05:19:31][PROT]===========================================================
[D][05:19:31][PROT]sending traceid [9999999999900009]
[D][05:19:31][PROT]Send_TO_M2M [1629955171]
[D][05:19:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:31][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:31][SAL ]sock send credit cnt[6]
[D][05:19:31][SAL ]sock send ind credit cnt[6]
[D][05:19:31][M2M ]m2m send data len[294]
[D][05:19:31][CAT1]gsm read msg sub id: 13
[D][05:19:31][SAL ]Cellular task submsg id[10]
[D][05:19:31][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052df8] format[0]
[D][05:19:31][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:31][M2M ]m2m switch t

2025-07-31 18:20:04:738 ==>> o: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:31][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:31][CAT1]exec over: func id: 13, ret: 21
[D][05:19:31][M2M ]get csq[21]
[D][05:19:31][CAT1]gsm read msg sub id: 15
[D][05:19:31][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:31][CAT1]Send Data To Server[294][297] ... ->:
0093B987113311331133113311331B88B1C7B64E150C9D294D9A7B76E97CD96C23C427285172639316AEC50CD33F4B0F6F77D523F290C7B36F67C8AD6EC43F60F3F86D7596531D450330E974902B88146BBBF403193D4D27C9C9B0646B457EBCCE4B1B95649A1841A8F8D6AF52157C667AA35F25DE6FB9DAE23CA48B63276B4AA26B1997C889132715B01440820BAF0E76B593
[D][05:19:31][CAT1]<<< 
SEND OK

[D][05:19:31][CAT1]exec over: func id: 15, ret: 11
[D][05:19:31][CAT1]sub id: 15, ret: 11

[D][05:19:31][SAL ]Cellular task submsg id[68]
[D][05:19:31][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:31][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:31][M2M ]g_m2m_is_idle become true
[D][05:19:31][M2M ]m2m s

2025-07-31 18:20:04:844 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 18:20:05:237 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:20:05:245 ==>> 检测【关闭GPS】
2025-07-31 18:20:05:262 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:20:05:375 ==>> $GBGGA,102005.000,2301.2576519,N,11421.9412711,E,1,19,0.87,72.867,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,24,03,16,06,39,38,26,09,59,1.47,0.87,1.19,4*04

$GBGSA,A,3,02,01,14,60,21,33,40,,,,,,1.47,0.87,1.19,4*0B

$GBGSV,6,1,22,13,79,244,39,8,76,190,39,42,70,11,41,24,65,239,42,1*71

$GBGSV,6,2,22,3,64,190,40,16,63,314,38,6,63,309,37,39,61,341,38,1*7D

$GBGSV,6,3,22,38,61,172,40,26,58,29,40,9,57,281,36,59,52,126,39,1*78

$GBGSV,6,4,22,2,49,241,34,1,47,123,36,14,46,334,35,60,44,243,39,1*70

$GBGSV,6,5,22,21,43,116,38,4,32,111,32,5,24,258,32,33,21,324,37,1*76

$GBGSV,6,6,22,7,17,181,30,40,10,165,30,1*48

$GBGSV,2,1,08,42,70,11,43,24,65,239,43,39,61,341,41,38,61,172,41,5*46

$GBGSV,2,2,08,26,58,29,41,21,43,116,40,33,21,324,34,40,10,165,30,5*4E

$GBRMC,102005.000,A,2301.2576519,N,11421.9412711,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,102005.000,1.740,0.230,0.225,0.301,1.484,1.745,5.453*7E



2025-07-31 18:20:05:630 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:33][GNSS]stop locating
[D][05:19:33][GNSS]stop event:8
[D][05:19:33][GNSS]GPS stop. ret=0
[D][05:19:33][GNSS]all continue location stop
[W][05:19:33][GNSS]stop locating
[D][05:19:33][GNSS]all sing location stop
[D][05:19:33][CAT1]gsm read msg sub id: 24
[D][05:19:33][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 24, ret: 6
[D][05:19:33][CAT1]sub id: 24, ret: 6



2025-07-31 18:20:05:776 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:20:05:785 ==>> 检测【清空消息队列2】
2025-07-31 18:20:05:814 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:20:05:954 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:33][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:20:06:050 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:20:06:058 ==>> 检测【轮动检测】
2025-07-31 18:20:06:067 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:20:06:164 ==>> 3A A3 01 00 A3 


2025-07-31 18:20:06:239 ==>> [D][05:19:33][COMM]read battery soc:255


2025-07-31 18:20:06:269 ==>> OFF_OUT1
OVER 150


2025-07-31 18:20:06:329 ==>> [D][05:19:34][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:20:06:479 ==>> [D][05:19:34][GNSS]recv submsg id[1]
[D][05:19:34][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:34][GNSS]location stop evt done evt


2025-07-31 18:20:06:554 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:20:06:676 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:20:06:837 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:20:06:845 ==>> 检测【关闭小电池】
2025-07-31 18:20:06:853 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:20:06:966 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:20:07:120 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:20:07:128 ==>> 检测【进入休眠模式】
2025-07-31 18:20:07:136 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:20:07:341 ==>> [D][05:19:34][HSDK][0] flush to flash addr:[0xE43000] --- write len --- [256]
[W][05:19:34][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:35][COMM]Main Task receive event:28
[D][05:19:35][COMM]main task tmp_sleep_event = 8
[D][05:19:35][COMM]prepare to sleep
[D][05:19:35][CAT1]gsm read msg sub id: 12
[D][05:19:35][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:20:08:090 ==>> [D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 12, ret: 6
[D][05:19:35][M2M ]tcpclient close[4]
[D][05:19:35][SAL ]Cellular task submsg id[12]
[D][05:19:35][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c98], socket[0]
[D][05:19:35][CAT1]gsm read msg sub id: 9
[D][05:19:35][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:35][CAT1]<<< 
OK

[D][05:19:35][CAT1]exec over: func id: 9, ret: 6
[D][05:19:35][CAT1]sub id: 9, ret: 6

[D][05:19:35][SAL ]Cellular task submsg id[68]
[D][05:19:35][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:35][SAL ]socket close ind. id[4]
[D][05:19:35][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:35][COMM]1x1 frm_can_tp_send ok
[D][05:19:35][CAT1]pdpdeact urc len[22]


2025-07-31 18:20:08:255 ==>> [D][05:19:35][COMM]read battery soc:255


2025-07-31 18:20:08:390 ==>> [E][05:19:36][COMM]1x1 rx timeout
[D][05:19:36][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:20:08:905 ==>> [E][05:19:36][COMM]1x1 rx timeout
[E][05:19:36][COMM]1x1 tp timeout
[E][05:19:36][COMM]1x1 error -3.
[W][05:19:36][COMM]CAN STOP!
[D][05:19:36][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:36][COMM]------------ready to Power off Acckey 1------------
[D][05:19:36][COMM]------------ready to Power off Acckey 2------------
[D][05:19:36][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:36][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1301
[D][05:19:36][COMM]bat sleep fail, reason:-1
[D][05:19:36][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:36][COMM]accel parse set 0
[D][05:19:36][COMM]imu rest ok. 107525
[D][05:19:36][COMM]imu sleep 0
[W][05:19:36][COMM]now sleep


2025-07-31 18:20:08:953 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:20:08:965 ==>> 检测【检测33V休眠电流】
2025-07-31 18:20:08:973 ==>> 开始33V电流采样
2025-07-31 18:20:08:986 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:09:055 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:20:10:070 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:20:10:115 ==>> Current33V:????:13.93

2025-07-31 18:20:10:571 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:10:579 ==>> 【检测33V休眠电流】通过,【13.93uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:20:10:589 ==>> 该项需要延时执行
2025-07-31 18:20:12:584 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:20:12:598 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:20:12:621 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:20:12:677 ==>> 1A A1 00 00 FC 
Get AD_V2 1655mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2770mV
Get AD_V6 1986mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:20:13:611 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:20:13:624 ==>> 检测【打开小电池2】
2025-07-31 18:20:13:646 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:20:13:673 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:20:13:893 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:20:13:901 ==>> 该项需要延时执行
2025-07-31 18:20:14:399 ==>> 此处延时了:【500】毫秒
2025-07-31 18:20:14:420 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:20:14:433 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:20:14:476 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:20:14:679 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:20:14:687 ==>> 该项需要延时执行
2025-07-31 18:20:14:829 ==>> [D][05:19:42][COMM]------------ready to Power on Acckey 1------------
[D][05:19:42][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:42][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 4,volt = 5
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 5,volt = 5
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 6,volt = 5
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 7,volt = 5
[D][05:19:42][FCTY]get_ext_48v_vol retry i = 8,volt = 5
[D][05:19:42][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5
[D][05:19:42][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:42][COMM]----- get Acckey 1 and value:1------------
[W][05:19:42][COMM]CAN START!
[D][05:19:42][CAT1]gsm read msg sub id: 12
[D][05:19:42][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:42][COMM]CAN message bat fault change: 0x01FFFFFF->0x00000000 113379
[D][05:19:42][COMM][Audio]exec status ready.
[D][05:19:42][CAT1]<<< 


2025-07-31 18:20:14:890 ==>> OK

[D][05:19:42][CAT1]exec over: func id: 12, ret: 6
[D][05:19:42][COMM]imu wakeup ok. 113394
[D][05:19:42][COMM]imu wakeup 1
[W][05:19:42][COMM]wake up system, wakeupEvt=0x80
[D][05:19:42][COMM]frm_can_weigth_power_set 1
[D][05:19:42][COMM]Clear Sleep Block Evt
[D][05:19:42][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:20:15:072 ==>> [E][05:19:42][COMM]1x1 rx timeout
[D][05:19:42][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:20:15:177 ==>> [D][05:19:42][COMM]msg 02A0 loss. last_tick:113365. cur_tick:113873. period:50
[D][05:19:42][COMM]msg 02A4 loss. last_tick

2025-07-31 18:20:15:194 ==>> 此处延时了:【500】毫秒
2025-07-31 18:20:15:210 ==>> 检测【进入休眠模式2】
2025-07-31 18:20:15:242 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:20:15:268 ==>> :113365. cur_tick:113874. period:50
[D][05:19:42][COMM]msg 02A5 loss. last_tick:113365. cur_tick:113874. period:50
[D][05:19:42][COMM]msg 02A6 loss. last_tick:113365. cur_tick:113874. period:50
[D][05:19:42][COMM]msg 02A7 loss. last_tick:113365. cur_tick:113875. period:50
[D][05:19:42][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 113875
[D][05:19:42][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 113875


2025-07-31 18:20:15:387 ==>> [W][05:19:43][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:43][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 18:20:15:568 ==>> [E][05:19:43][COMM]1x1 rx timeout
[E][05:19:43][COMM]1x1 tp timeout
[E][05:19:43][COMM]1x1 error -3.
[D][05:19:43][COMM]Main Task receive event:28 finished processing
[D][05:19:43][COMM]Main Task receive event:28
[D][05:19:43][COMM]prepare to sleep
[D][05:19:43][CAT1]gsm read msg sub id: 12
[D][05:19:43][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:43][CAT1]<<< 
OK

[D][05:19:43][CAT1]exec over: func id: 12, ret: 6
[D][05:19:43][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:43][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:20:15:872 ==>> [D][05:19:43][COMM]msg 0220 loss. last_tick:113365. cur_tick:114369. period:100
[D][05:19:43][COMM]msg 0221 loss. last_tick:113365. cur_tick:114370. period:100
[D][05:19:43][COMM]msg 0224 loss. last_tick:113365. cur_tick:114370. period:100
[D][05:19:43][COMM]msg 0260 loss. last_tick:113365. cur_tick:114370. period:100
[D][05:19:43][COMM]msg 0280 loss. last_tick:113365. cur_tick:114371. period:100
[D][05:19:43][COMM]msg 02C0 loss. last_tick:113365. cur_tick:114371. period:100
[D][05:19:43][COMM]msg 02C1 loss. last_tick:113365. cur_tick:114371. period:100
[D][05:19:43][COMM]msg 02C2 loss. last_tick:113365. cur_tick:114372. period:100
[D][05:19:43][COMM]msg 02E0 loss. last_tick:113365. cur_tick:114372. period:100
[D][05:19:43][COMM]msg 02E1 loss. last_tick:113365. cur_tick:114373. period:100
[D][05:19:43][COMM]msg 02E2 loss. last_tick:113365. cur_tick:114373. period:100
[D][05:19:43][COMM]msg 0300 loss. last_tick:113365. cur_tick:114373. period:100
[D][05:19:43][COMM]msg 0301 loss. last_tick:113365. cur_tick:114373. period:100
[D][05:19:43][COMM]bat msg 0240 loss. last_tick:113365. cur_tick:114374. period:100. j,i:1 54
[D][05:19:43][COMM]bat msg 0241 loss. last_tick:113365. cur_tick:114374. period:10

2025-07-31 18:20:15:977 ==>> 0. j,i:2 55
[D][05:19:43][COMM]bat msg 0242 loss. last_tick:113365. cur_tick:114375. period:100. j,i:3 56
[D][05:19:43][COMM]bat msg 0244 loss. last_tick:113365. cur_tick:114375. period:100. j,i:5 58
[D][05:19:43][COMM]bat msg 024E loss. last_tick:113365. cur_tick:114375. period:100. j,i:15 68
[D][05:19:43][COMM]bat msg 024F loss. last_tick:113365. cur_tick:114376. period:100. j,i:16 69
[D][05:19:43][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 114376
[D][05:19:43][COMM]CAN message bat fault change: 0x00000000->0x0001802E 114377
[D][05:19:43][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 114377
                                                                              

2025-07-31 18:20:16:159 ==>> [D][05:19:43][COMM]msg 0222 loss. last_tick:113365. cur_tick:114871. period:150
[D][05:19:43][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 114872


2025-07-31 18:20:16:250 ==>>                                                                                                                poweroff type 16.... 
[D][05:19:43][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:20:16:446 ==>> [E][05:19:44][COMM]1x1 rx timeout
[E][05:19:44][COMM]1x1 tp timeout
[E][05:19:44][COMM]1x1 error -3.
[W][05:19:44][COMM]CAN STOP!
[D][05:19:44][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:44][COMM]------------ready to Power off Acckey 1------------
[D][05:19:44][COMM]------------ready to Power off Acckey 2------------
[D][05:19:44][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:44][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 99
[D][05:19:44][COMM]bat sleep fail, reason:-1
[D][05:19:44][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:44][COMM]accel parse set 0
[D][05:19:44][COMM]imu rest ok. 115066
[D][05:19:44][COMM]imu sleep 0
[W][05:19:44][COMM]now sleep


2025-07-31 18:20:16:522 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:20:16:530 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:20:16:557 ==>> 开始小电池电流采样
2025-07-31 18:20:16:579 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:16:630 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:20:17:636 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:20:17:713 ==>> CurrentBattery:ƽ��:66.52

2025-07-31 18:20:18:146 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:18:162 ==>> 【检测小电池休眠电流】通过,【66.52uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:20:18:185 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:20:18:193 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:20:18:269 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:20:18:422 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:20:18:432 ==>> 该项需要延时执行
2025-07-31 18:20:18:510 ==>> [D][05:19:46][COMM]------------ready to Power on Acckey 1------------
[D][05:19:46][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:46][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:46][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:46][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:46][COMM]----- get Acckey 1 and value:1------------
[W][05:19:46][COMM]CAN START!
[D][05:19:46][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[D][05:19:46][COMM]read battery soc:0
[D][05:19:46][CAT1]gsm read msg sub id: 12
[D][05:19:46][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:46][COMM]CAN message bat fault change: 0x0001802E->0x00000000 117082
[D][05:19:46][COMM][Audio]exec status ready.
[D][05:19:46][CAT1]<<< 
OK

[D][05:19:46][CAT1]exec over: func id: 12, ret: 6
[D][05:19:46][COMM]imu wakeup ok. 117096
[D][05:19:46][COMM]imu wakeup 1
[W][05:19:46][COMM]wake up system, wakeupEvt=0x80
[D][05:19:46][COMM]frm_can_weigth_power_set 1
[D][05:19:46][COMM]Clear Sleep Block Evt
[D][05:19:46][COMM]1

2025-07-31 18:20:18:540 ==>> x1 tx_id:3,8, tx_len:2
[D][05:19:46][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:20:18:925 ==>> 此处延时了:【500】毫秒
2025-07-31 18:20:18:938 ==>> 检测【检测唤醒】
2025-07-31 18:20:18:961 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:20:18:990 ==>> [D][05:19:46][HSDK][0] flush to flash addr:[0xE43100] --- write len --- [256]
[E][05:19:46][COMM]1x1 rx timeout
[D][05:19:46][COMM]1x1 frm_can_tp_send ok
[D][05:19:46][COMM]msg 02A0 loss. last_tick:117064. cur_tick:117576. period:50
[D][05:19:46][COMM]msg 02A4 loss. last_tick:117064. cur_tick:117577. period:50
[D][05:19:46][COMM]msg 02A5 loss. last_tick:117064. cur_tick:117577. period:50
[D][05:19:46][COMM]msg 02A6 loss. last_tick:117064. cur_tick:117578. period:50
[D][05:19:46][COMM]msg 02A7 loss. last_tick:117064. cur_tick:117578. period:50
[D][05:19:46][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 117578
[D][05:19:46][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 117579


2025-07-31 18:20:19:710 ==>> [W][05:19:46][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:46][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:46][FCTY]==========Modules-nRF5340 ==========
[D][05:19:46][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:46][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:46][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:46][FCTY]DeviceID    = 460130071536901
[D][05:19:46][FCTY]HardwareID  = 867222088035086
[D][05:19:46][FCTY]MoBikeID    = 9999999999
[D][05:19:46][FCTY]LockID      = FFFFFFFFFF
[D][05:19:46][FCTY]BLEFWVersion= 105
[D][05:19:46][FCTY]BLEMacAddr   = FD95DC83243D
[D][05:19:46][FCTY]Bat         = 3884 mv
[D][05:19:46][FCTY]Current     = 0 ma
[D][05:19:46][FCTY]VBUS        = 2600 mv
[D][05:19:46][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:46][FCTY]Ext battery vol = 32, adc = 1295
[D][05:19:46][FCTY]Acckey1 vol = 5566 mv, Acckey2 vol = 101 mv
[D][05:19:46][FCTY]Bike Type flag is invalied
[D][05:19:46][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:46][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:46][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:46][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:46][FCTY]CAT1_GNSS_PLATFORM = C4
[D][

2025-07-31 18:20:19:816 ==>> 05:19:46][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:46][FCTY]Bat1         = 3825 mv
[D][05:19:46][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:46][FCTY]==========Modules-nRF5340 ==========
[E][05:19:46][COMM]1x1 rx timeout
[E][05:19:46][COMM]1x1 tp timeout
[E][05:19:46][COMM]1x1 error -3.
[D][05:19:46][COMM]Main Task receive event:28 finished processing
[D][05:19:46][COMM]Main Task receive event:65
[D][05:19:46][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:46][COMM]Main Task receive event:65 finished processing
[D][05:19:46][COMM]Main Task receive event:60
[D][05:19:46][COMM]smart_helmet_vol=255,255
[D][05:19:46][COMM]report elecbike
[W][05:19:46][PROT]remove success[1629955186],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:46][PROT]add success [1629955186],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:46][COMM]Main Task receive event:60 finished processing
[D][05:19:46][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:46][PROT]index:0
[D][05:19:46][PROT]is_send:1
[D][05:19:46][PROT]sequence_num:10
[D][05:19:46][PROT]retry_timeout:0
[D][05:19:46][PROT]retry_times:3
[D][05:19:46]

2025-07-31 18:20:19:921 ==>> [PROT]send_path:0x3
[D][05:19:46][PROT]msg_type:0x5d03
[D][05:19:46][PROT]===========================================================
[W][05:19:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955186]
[D][05:19:46][PROT]===========================================================
[D][05:19:46][PROT]Sending traceid[999999999990000B]
[D][05:19:46][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:46][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:46][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:46][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:46][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:46][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:46][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:46][SAL ]open socket ind id[4], rst[0]
[D][05:19:46][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:46][SAL ]Cellular task submsg id[8]
[D][05:19:46][SAL ]cellular OPEN socket size[144], msg->data[0x20052db8], socket[0]
[D][05:19:46][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:46][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:1

2025-07-31 18:20:19:984 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:20:20:012 ==>> 检测【关机】
2025-07-31 18:20:20:027 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:20:20:038 ==>> 9:46][CAT1]gsm read msg sub id: 8
[D][05:19:46][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:46][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:46][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:46][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:46][CAT1]<<< 
+CME ERROR: 100

[D][05:19:47][COMM]msg 0220 loss. last_tick:117064. cur_tick:118081. period:100
[D][05:19:47][COMM]msg 0221 loss. last_tick:117064. cur_tick:118081. period:100
[D][05:19:47][COMM]msg 0224 loss. last_tick:117064. cur_tick:118082. period:100
[D][05:19:47][COMM]msg 0260 loss. last_tick:117064. cur_tick:118082. period:100
[D][05:19:47][COMM]msg 0280 loss. last_tick:117064. cur_tick:118082. period:100
[D][05:19:47][COMM]msg 02C0 loss. last_tick:117064. cur_tick:118083. period:100
[D][05:19:47][COMM]msg 02C1 loss. last_tick:117064. cur_tick:118083. period:100
[D][05:19:47][COMM]msg 02C2 loss. last_tick:117064. cur_tick:118083. period:100
[D][05:19:47][COMM]msg 02E0 loss. last_tick:117064. cur_tick:118084. period:100
[D][05:19:47][COMM]msg 02E1 loss. last_tick:117064. cur_tick:118084. period:100
[D][05:19:47][COMM]msg 02E2 loss. last_tick:117064. cur_tick:118084. period:100
[D][05:19:47][COMM]msg 0300 loss. last

2025-07-31 18:20:20:131 ==>> _tick:117064. cur_tick:118085. period:100
[D][05:19:47][COMM]msg 0301 loss. last_tick:117064. cur_tick:118085. period:100
[D][05:19:47][COMM]bat msg 0240 loss. last_tick:117064. cur_tick:118085. period:100. j,i:1 54
[D][05:19:47][COMM]bat msg 0241 loss. last_tick:117064. cur_tick:118086. period:100. j,i:2 55
[D][05:19:47][COMM]bat msg 0242 loss. last_tick:117064. cur_tick:118086. period:100. j,i:3 56
[D][05:19:47][COMM]bat msg 0244 loss. last_tick:117064. cur_tick:118087. period:100. j,i:5 58
[D][05:19:47][COMM]bat msg 024E loss. last_tick:117064. cur_tick:118087. period:100. j,i:15 68
[D][05:19:47][COMM]bat msg 024F loss. last_tick:117064. cur_tick:118088. period:100. j,i:16 69
[D][05:19:47][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 118088
[D][05:19:47][COMM]CAN message bat fault change: 0x00000000->0x0001802E 118088
[D][05:19:47][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 118089


2025-07-31 18:20:20:729 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:20:20:834 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 18:20:20:939 ==>>                                                                                                                                                                                                                                                                                                                                     processing
[D][05:19:47][COMM]Main Task receive event:66
[D][05:19:47][COMM]Try to Auto Lock Bat
[D][05:19:47][COMM]Main Task receive event:66 finished processing
[D][05:19:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:47][COMM]Main Task receive event:60
[D][05:19:47][COMM]smart_helmet_vol=255,255
[D][05:19:47][COMM]BAT CAN get state1 Fail 204
[D][05:19:47][COMM]BAT CAN get soc Fail, 204
[D][05:19:47][COMM]BAT CAN get state2 fail 204
[D][05:19:47][COMM]get soh error
[D][05:19:47][COMM]Receive Bat Lock cmd 0
[D][05:19:47][COMM]VBUS is 1
[E][05:19:47][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:47][COMM]report elecbike
[W][05:19:47][PROT]remove success[1629955187],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:47][PROT]add success [1629955187],send_path[3],type[5D03],priority[4],index[

2025-07-31 18:20:21:014 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:20:21:044 ==>> 1],used[1]
[D][05:19:47][COMM]Main Task receive event:60 finished processing
[D][05:19:47][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:47][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:47][COMM]Main Task receive event:61
[D][05:19:47][COMM][D301]:type:3, trace id:280
[D][05:19:47][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:47][PROT]index:1
[D][05:19:47][PROT]is_send:1
[D][05:19:47][PROT]sequence_num:11
[D][05:19:47][PROT]retry_timeout:0
[D][05:19:47][PROT]retry_times:3
[D][05:19:47][PROT]send_path:0x3
[D][05:19:47][PROT]msg_type:0x5d03
[D][05:19:47][PROT]===========================================================
[W][05:19:47][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955187]
[D][05:19:47][PROT]===========================================================
[D][05:19:47][PROT]Sending traceid[999999999990000C]
[D][05:19:47][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:47][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:47][PROT]ble is not inited or not connec

2025-07-31 18:20:21:149 ==>> ted or cccd not enabled
[D][05:19:47][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:47][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:47][COMM]id[], hw[000
[D][05:19:47][COMM]get mcMaincircuitVolt error
[D][05:19:47][COMM]get mcSubcircuitVolt error
[D][05:19:47][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:47][COMM]BAT CAN get state1 Fail 204
[D][05:19:47][COMM]BAT CAN get soc Fail, 204
[D][05:19:47][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:47][COMM]BAT CAN get state2 fail 204
[D][05:19:47][COMM]get bat work mode err
[W][05:19:47][PROT]remove success[1629955187],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:19:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[W][05:19:47][PROT]add success [1629955187],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:47][COMM]Main Task receive event:61 finished processing
[D][05:19:47][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:47][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:47][COMM]f:[ec800m_audio_send_h

2025-07-31 18:20:21:254 ==>> exdata_start].l:[756].recv >
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[W][05:19:47][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:47][COMM]arm_hub_enable: hub power: 0
[D][05:19:47][HSDK]hexlog index save 0 8704 187 @ 0 : 0
[D][05:19:47][HSDK]write save hexlog index [0]
[D][05:19:47][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:47][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:48][COMM]read battery soc:255
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, i

2025-07-31 18:20:21:329 ==>> ndex:5, len:2048
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:48][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:48][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:48][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 18:20:21:434 ==>> [W][05:19:49][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:49][COMM]arm_hub_enable: hub power: 0
[D][05:19:49][HSDK]hexlog index save 0 8704 187 @ 0 : 0
[D][05:19:49][HSDK]write save hexlog index [0]
[D][05:19:49][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:49][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:20:21:884 ==>> [W][05:19:49][COMM]Power Off


2025-07-31 18:20:22:054 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:20:22:069 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:20:22:099 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:20:22:175 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:20:22:250 ==>> [D][05:19:49][FCTY]get_ext_48v_vol retry i = 0,volt = 13
[D][05:19:49][FCTY]get_ext_4

2025-07-31 18:20:22:329 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:20:22:344 ==>> 检测【检测小电池关机电流】
2025-07-31 18:20:22:361 ==>> 开始小电池电流采样
2025-07-31 18:20:22:376 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:22:430 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:20:23:439 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:20:23:485 ==>> CurrentBattery:ƽ��:68.14

2025-07-31 18:20:23:951 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:20:23:960 ==>> 【检测小电池关机电流】通过,【68.14uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:20:24:375 ==>> MES过站成功
2025-07-31 18:20:24:387 ==>> #################### 【测试结束】 ####################
2025-07-31 18:20:24:421 ==>> 关闭5V供电
2025-07-31 18:20:24:437 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:20:24:475 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:20:25:435 ==>> 关闭5V供电成功
2025-07-31 18:20:25:449 ==>> 关闭33V供电
2025-07-31 18:20:25:471 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:20:25:574 ==>> 5A A5 02 5A A5 


2025-07-31 18:20:25:664 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:20:26:437 ==>> 关闭33V供电成功
2025-07-31 18:20:26:451 ==>> 关闭3.7V供电
2025-07-31 18:20:26:474 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:20:26:577 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:20:27:445 ==>> 关闭3.7V供电成功
