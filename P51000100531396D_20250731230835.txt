2025-07-31 23:08:35:405 ==>> MES查站成功:
查站序号:P51000100531396D验证通过
2025-07-31 23:08:35:416 ==>> 扫码结果:P51000100531396D
2025-07-31 23:08:35:418 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:08:35:419 ==>> 测试参数版本:2024.10.11
2025-07-31 23:08:35:421 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:08:35:422 ==>> 检测【打开透传】
2025-07-31 23:08:35:425 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:08:35:518 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:08:36:120 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:08:36:153 ==>> 检测【检测接地电压】
2025-07-31 23:08:36:155 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:08:36:227 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 23:08:36:425 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:08:36:428 ==>> 检测【打开小电池】
2025-07-31 23:08:36:431 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:08:36:516 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:08:36:702 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:08:36:705 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:08:36:707 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:08:36:821 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:08:36:978 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:08:36:981 ==>> 检测【等待设备启动】
2025-07-31 23:08:36:983 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:08:37:311 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:08:37:507 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 23:08:38:015 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:08:38:018 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:08:38:212 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:08:38:898 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 23:08:39:048 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:08:39:275 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:08:39:762 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:08:39:839 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:08:39:842 ==>> 检测【产品通信】
2025-07-31 23:08:39:845 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:08:40:490 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:08:40:686 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:08:40:866 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:08:41:900 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:08:41:975 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]frm CAN read mc pwr mode invalid,val:254
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid

2025-07-31 23:08:42:080 ==>>  255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]netcore_ver 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[

2025-07-31 23:08:42:176 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:08:42:178 ==>> 检测【初始化完成检测】
2025-07-31 23:08:42:182 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:08:42:185 ==>> D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA_BOOT_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:17:49][FCTY]Bat         = 3764 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 0
[D][05:17:49][FCTY]Acckey1 vol = 5581 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]

2025-07-31 23:08:42:290 ==>> CAT1_GNSS_VERSION =
[D][05:17:49][FCTY]Bat1         = 3685 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi scan.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][COMM][CHG]ext_48v_vol:0, disable charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[

2025-07-31 23:08:42:395 ==>> 5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][CAT1]gsm read msg sub id: 1
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:49][COMM]msg 0220 loss. last_tick:0. cur_tick:1010. period:100
[D][05:17:49][COMM]msg 0221 loss. last_tick:0. cur_tick:1011. period:100
[D][05:17:49][COMM]msg 0224 loss. last_tick:0. cur_tick:1011. period:100
[D][05:17:49][COMM]msg 0260 loss. last_tick:0. cur_tick:1011. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1012. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1012. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1012. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1013. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1013. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1013. period:100
[D][05:17:50][COMM]msg 02E2 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1014. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick

2025-07-31 23:08:42:486 ==>> :0. cur_tick:1015. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:1015. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1016. period:100. j,i:3 56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1016. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1016. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1017. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1017
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1017
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1018
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:08:42:801 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:08:42:906 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:08:43:011 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 23:08:43:116 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM

2025-07-31 23:08:43:206 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:08:43:221 ==>> ]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1----------

2025-07-31 23:08:43:251 ==>> --
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 23:08:43:356 ==>>                                     

2025-07-31 23:08:43:461 ==>>                                                                                 ]imu_task imu work error:[-1]. goto init
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 23:08:43:489 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:08:43:493 ==>> 检测【关闭大灯控制1】
2025-07-31 23:08:43:495 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:08:43:674 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:08:43:770 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:08:43:773 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:08:43:776 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:08:44:019 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:08:44:061 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:08:44:065 ==>> 检测【关闭仪表供电】
2025-07-31 23:08:44:068 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:08:44:214 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:08:44:319 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:44:337 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:08:44:339 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:08:44:341 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:08:44:485 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:08:44:662 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:08:44:665 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:08:44:668 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:08:44:808 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:08:45:045 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:08:45:049 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:08:45:051 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:08:45:181 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:08:45:333 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:08:45:337 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:08:45:347 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:08:45:350 ==>> [D][05:17:53][COMM]4649 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:45:421 ==>> 5A A5 01 5A A5 


2025-07-31 23:08:45:526 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:08:45:586 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29


2025-07-31 23:08:45:609 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:08:45:612 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:08:45:633 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:08:45:721 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:08:45:826 ==>>                                         
[D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5014. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5015. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5015. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5016. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5016. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5016. p

2025-07-31 23:08:45:889 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:08:45:892 ==>> 该项需要延时执行
2025-07-31 23:08:45:916 ==>> eriod:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5017. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5017. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5018. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5018. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5019. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5019. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5019
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5020


2025-07-31 23:08:46:345 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:47:207 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:08:47:721 ==>>                                                                                                                                                                             e:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main T

2025-07-31 23:08:47:826 ==>> ask receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMa

2025-07-31 23:08:47:931 ==>> incircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble i

2025-07-31 23:08:48:006 ==>> s not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][CAT1]power_urc_cb ret[5]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 23:08:48:357 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:49:380 ==>> [D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:49:637 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 23:08:49:894 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:08:49:897 ==>> 检测【33V输入电压ADC】
2025-07-31 23:08:49:900 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:08:50:229 ==>> [D][05:17:58][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3172  volt:5575 mv
[D][05:17:58][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:17:58][COMM]adc read left brake adc:2  volt:2 mv
[D][05:17:58][COMM]adc read right brake adc:5  volt:6 mv
[D][05:17:58][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:58][COMM]adc read battery ts volt:15 mv
[D][05:17:58][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:17:58][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:08:50:409 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:08:50:427 ==>> 【33V输入电压ADC】通过,【33032mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:08:50:432 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:08:50:435 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:08:50:514 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1668mV
Get AD_V4 1mV
Get AD_V5 2775mV
Get AD_V6 1985mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:08:50:736 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:50:739 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:08:50:757 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10007. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10008
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10008


2025-07-31 23:08:50:772 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:50:774 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:08:50:777 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 23:08:50:814 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:08:50:820 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:08:50:856 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1985mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:08:50:858 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:08:50:901 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:08:50:904 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:08:51:027 ==>> 1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1668mV
Get AD_V4 0mV
Get AD_V5 2771mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:08:51:132 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 23:08:51:312 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:51:315 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:08:51:369 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:51:372 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:08:51:375 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 23:08:51:415 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:08:51:418 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:08:51:489 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:08:51:495 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:08:51:538 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:08:51:541 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:08:51:604 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2

2025-07-31 23:08:51:649 ==>> M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1667mV
Get AD_V4 0mV
Get AD_V5 2774mV
Get AD_V6 1989mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:08:51:843 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:51:846 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:08:51:922 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:51:926 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:08:51:928 ==>> 原始值:【2774】, 乘以分压基数【2】还原值:【5548】
2025-07-31 23:08:51:959 ==>> 【TP68_VCC5V5(ADV5)】通过,【5548mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:08:51:963 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:08:52:004 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:08:52:008 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:08:52:046 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:08:52:049 ==>> 检测【打开WIFI(1)】
2025-07-31 23:08:52:052 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:08:52:056 ==>>                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087569564

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539482

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 23:08:52:159 ==>> [

2025-07-31 23:08:52:189 ==>> W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:08:52:378 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:08:52:381 ==>> 检测【清空消息队列(1)】
2025-07-31 23:08:52:386 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:08:52:404 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:08:52:599 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:08:52:662 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:08:52:665 ==>> 检测【打开GPS(1)】
2025-07-31 23:08:52:669 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:08:52:704 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1



2025-07-31 23:08:52:809 ==>>         8:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 23:08:52:914 ==>> [D

2025-07-31 23:08:52:932 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:08:52:936 ==>> 检测【打开GSM联网】
2025-07-31 23:08:52:961 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:08:52:963 ==>> ][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 23:08:53:109 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 23:08:53:208 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:08:53:212 ==>> 检测【打开仪表供电1】
2025-07-31 23:08:53:215 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:08:53:415 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:08:53:478 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:08:53:481 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:08:53:484 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:08:53:718 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:08:53:751 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:08:53:754 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:08:53:757 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:08:53:898 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33224]


2025-07-31 23:08:54:022 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:08:54:025 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:08:54:028 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:08:54:228 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:08:54:291 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:08:54:294 ==>> 检测【AD_V20电压】
2025-07-31 23:08:54:296 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:08:54:393 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:08:54:397 ==>> [D][05:18:02][COMM]13728 imu init OK


2025-07-31 23:08:54:468 ==>> 本次取值间隔时间:67ms
2025-07-31 23:08:54:498 ==>> 00 00 00 00 00 
head err!


2025-07-31 23:08:54:588 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]S->M yaw:INVALID
[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:08:54:618 ==>> 本次取值间隔时间:149ms
2025-07-31 23:08:54:801 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:08:54:936 ==>> 本次取值间隔时间:313ms
2025-07-31 23:08:55:313 ==>> 本次取值间隔时间:372ms
2025-07-31 23:08:55:317 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:08:55:403 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:08:55:418 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:08:55:508 ==>> 1A A1 10 00 00 
Get AD_V20 1657mV
OVER 150


2025-07-31 23:08:55:523 ==>> 本次取值间隔时间:95ms
2025-07-31 23:08:55:613 ==>> 【AD_V20电压】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:55:616 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:08:55:618 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:08:55:721 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 23:08:55:826 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 23:08:55:916 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:08:55:919 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:08:55:923 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:08:55:931 ==>>                                                                                                                                                                                                                                                               t[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.34.25.242"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<

2025-07-31 23:08:55:991 ==>> < 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][COMM]read battery soc:255
[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:08:56:096 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:08:56:189 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:08:56:193 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:08:56:204 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:08:56:321 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:08:56:426 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,20971

2025-07-31 23:08:56:456 ==>> 52*7A



2025-07-31 23:08:56:469 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:08:56:488 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:08:56:493 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:08:56:715 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:08:56:748 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:08:56:754 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:08:56:761 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:08:56:910 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:08:57:018 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:08:57:021 ==>> 检测【AD_V21电压】
2025-07-31 23:08:57:023 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:08:57:120 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

1A A1 20 00 00 
Get AD_V21 1043mV
OVER 150


2025-07-31 23:08:57:135 ==>> 本次取值间隔时间:108ms
2025-07-31 23:08:57:165 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:08:57:225 ==>> 1A A1 20 00 00 
Get AD_V21 1656mV
OVER 150
[D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,40,,,42,34,,,41,39,,,41,25,,,39,1

2025-07-31 23:08:57:300 ==>> *7F

$GBGSV,3,2,09,23,,,38,60,,,38,41,,,37,11,,,39,1*72

$GBGSV,3,3,09,59,,,36,1*76

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1634.580,1634.580,52.229,2097152,2097152,2097152*41

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 23:08:57:451 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:08:57:602 ==>> 本次取值间隔时间:426ms
2025-07-31 23:08:57:668 ==>> 【AD_V21电压】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:08:57:671 ==>> 检测【关闭仪表供电2】
2025-07-31 23:08:57:673 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:08:57:818 ==>> [D][05:18:06][COMM]read battery soc:255
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:08:57:942 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:08:57:945 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:08:57:947 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:08:58:118 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:08:58:222 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:08:58:226 ==>> 检测【打开AccKey2供电】
2025-07-31 23:08:58:229 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:08:58:233 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,40,,,42,39,,,41,34,,,40,11,,,39,1*72

$GBGSV,4,2,14,25,,,39,60,,,39,59,,,39,23,,,38,1*78

$GBGSV,4,3,14,41,,,38,16,,,38,7,,,38,1,,,36,1*7E

$GBGSV,4,4,14,5,,,32,36,,,52,1*45

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1

2025-07-31 23:08:58:256 ==>> 591.331,1591.331,50.877,2097152,2097152,2097152*42



2025-07-31 23:08:58:404 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:08:58:509 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:08:58:513 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:08:58:517 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:08:58:832 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:07][COMM]adc read out 24v adc:1321  volt:33412 mv
[D][05:18:07][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:07][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:07][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:07][COMM]adc read battery ts volt:6 mv
[D][05:18:07][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:08:59:049 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33412mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:08:59:053 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:08:59:055 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:08:59:181 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:08:59:325 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:08:59:329 ==>> 该项需要延时执行
2025-07-31 23:08:59:347 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,40,,,42,39,,,41,3,,,41,34,,,40,1*4C

$GBGSV,5,2,17,25,,,40,11,,,39,60,,,39,59,,,39,1*74

$GBGSV,5,3,17,41,,,39,7,,,39,23,,,38,16,,,38,1*42

$GBGSV,5,4,17,1,,,35,43,,,35,2,,,35,5,,,32,1*47

$GBGSV,5,5,17,4,,,31,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1568.107,1568.107,50.171,2097152,2097152,2097152*4D



2025-07-31 23:08:59:732 ==>> [D][05:18:08][COMM]read battery soc:255


2025-07-31 23:09:00:297 ==>> $GBGGA,150904.132,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,39,,,40,3,,,40,34,,,40,1*4A

$GBGSV,6,2,21,25,,,40,59,,,40,11,,,39,60,,,39,1*7C

$GBGSV,6,3,21,41,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,6,4,21,43,,,37,10,,,36,24,,,36,1,,,35,1*44

$GBGSV,6,5,21,2,,,34,32,,,32,5,,,32,4,,,31,1*41

$GBGSV,6,6,21,6,,,36,1*46

$GBRMC,150904.132,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150904.132,0.000,1548.476,1548.476,49.545,2097152,2097152,2097152*51



2025-07-31 23:09:00:679 ==>> $GBGGA,150904.532,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,42,39,,,40,3,,,40,34,,,40,1*4A

$GBGSV,6,2,21,25,,,40,59,,,40,11,,,39,60,,,39,1*7C

$GBGSV,6,3,21,41,,,39,7,,,39,23,,,38,16,,,38,1*44

$GBGSV,6,4,21,43,,,37,6,,,36,10,,,36,24,,,36,1*40

$GBGSV,6,5,21,1,,,35,2,,,34,32,,,32,5,,,32,1*40

$GBGSV,6,6,21,4,,,31,1*43

$GBRMC,150904.532,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150904.532,0.000,1545.808,1545.808,49.457,2097152,2097152,2097152*57



2025-07-31 23:09:01:685 ==>> $GBGGA,150905.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,42,39,,,40,34,,,40,25,,,40,1*7C

$GBGSV,6,2,23,59,,,40,60,,,40,3,,,39,11,,,39,1*4A

$GBGSV,6,3,23,41,,,39,7,,,39,23,,,38,16,,,38,1*46

$GBGSV,6,4,23,43,,,38,6,,,36,10,,,36,24,,,36,1*4D

$GBGSV,6,5,23,1,,,36,2,,,34,9,,,34,32,,,32,1*4B

$GBGSV,6,6,23,5,,,32,4,,,31,12,,,31,1*74

$GBRMC,150905.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150905.512,0.000,1532.171,1532.171,49.031,2097152,2097152,2097152*50



2025-07-31 23:09:01:746 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 23:09:02:339 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:09:02:344 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:09:02:348 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:02:719 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:10][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:10][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:10][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:10][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:10][COMM]adc read battery ts volt:9 mv
[D][05:18:10][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:10][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,150906.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,40,25,,,40,1*7A

$GBGSV,7,2,25,59,,,40,60,,,40,3,,,39,11,,,39,1*4D

$GBGSV,7,3,25,41,,,39,7,,,39,23,,,38,16,,,38,1*41

$GBGSV,7,4,25,43,,,38,6,,,36,10,,,36,24,,,36,1*4A

$GBGSV,7,5,25,1,,,36,2,,,34,9,,,34,32,,,32,1*4C

$GBGSV,7,6,25,5,,,32,4,,,32,12,,,32,4

2025-07-31 23:09:02:764 ==>> 4,,,31,1*71

$GBGSV,7,7,25,33,,,31,1*73

$GBRMC,150906.512,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150906.512,0.000,1517.401,1517.401,48.569,2097152,2097152,2097152*5A



2025-07-31 23:09:02:873 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:09:02:882 ==>> 检测【打开AccKey1供电】
2025-07-31 23:09:02:902 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:09:03:102 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:09:03:159 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:09:03:163 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:09:03:167 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:09:03:208 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 23:09:03:419 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 23:09:03:437 ==>> 【读取AccKey1电压(ADV14)前】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:09:03:442 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:09:03:466 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:03:815 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:11][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:11][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:11][COMM]adc read battery ts volt:9 mv
[D][05:18:11][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:11][COMM]adc read throttle brake in adc:1  volt:1 mv
$GBGGA,150907.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,34,,,40,25,,,40,1*7A

$GBGSV,7,2,25,59,,,40,60,,,39,3,,,39,11,,,39,1*43

$GBGSV,7,3,25,41,,,39,7,,,39,23,,,38,16,,,38,1*41

$GBGSV,7,4,25,43,,,38,10,,,37,6,,,36,24,,,36,1*4B

$GBGSV,7,5,25,1,,,36,2,,,34,9,,,34,12,,,33,1*4F

$GBGSV,7,6,25,33,,,33,32,,,32,5,,,32,4,,,32,1*71

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150907.512,V,,,,,,,,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150907.512,0.000,1522.365,1522.365,48.718,2097152,2097152,2097152*5F

[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11]

2025-07-31 23:09:03:861 ==>> [COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1431  volt:33178 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:2  volt:46 mv
[D][05:18:12][COMM]read battery soc:255


2025-07-31 23:09:04:025 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5574mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:09:04:031 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:09:04:058 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:09:04:206 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:09:04:320 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:09:04:327 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:09:04:357 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:09:04:417 ==>> 1A A1 00 40 00 
Get AD_V14 2669mV
OVER 150


2025-07-31 23:09:04:585 ==>> 原始值:【2669】, 乘以分压基数【2】还原值:【5338】
2025-07-31 23:09:04:630 ==>> 【读取AccKey1电压(ADV14)后】通过,【5338mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:09:04:634 ==>> 检测【打开WIFI(2)】
2025-07-31 23:09:04:650 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:09:04:692 ==>> $GBGGA,150908.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,41,59,,,40,34,,,40,1*72

$GBGSV,7,2,25,7,,,39,60,,,39,3,,,39,25,,,39,1*71

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,38,10,,,37,1,,,37,24,,,36,1*4B

$GBGSV,7,5,25,6,,,36,2,,,35,9,,,34,33,,,34,1*4D

$GBGSV,7,6,25,12,,,33,5,,,32,4,,,32,32,,,32,1*72

$GBGSV,7,7,25,44,,,31,1*73

$GBRMC,150908.512,V,,,,,,,,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150908.512,0.000,1524.015,1524.015,48.762,2097152,2097152,2097152*5D



2025-07-31 23:09:04:797 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFI

2025-07-31 23:09:04:842 ==>> SCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:09:04:954 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:09:04:968 ==>> 检测【转刹把供电】
2025-07-31 23:09:04:994 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:09:05:098 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:09:05:261 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:09:05:266 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:09:05:270 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:09:05:372 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:09:05:402 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 23:09:05:507 ==>> 1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 23:09:05:537 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 23:09:05:568 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:09:05:571 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:09:05:584 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:09:05:672 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:09:05:777 ==>> $GBGGA,150909.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,59,,,40,34,,,40,1*73

$GBGSV,7,2,25,7,,,39,60,,,39,3,,,39,25,,,39,1*71

$GBGSV,7,3,25,11,,,39,16,,,38,43,,,38,23,,,38,1*75

$GBGSV,7,4,25,41,,,38,1,,,37,10,,,36,24,,,36,1*4E

$GBGSV,7,5,25,6,,,36,33,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,12,,,33,5,,,32,44,,,32,4,,,32,1*73

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150909.512,V,,,,,,,,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150909.512,0.000,1520.691,1520.691,48.649,2097152,2097152,2097152*54

+WIFISCAN:4,0,F42A7D1297A3,-61
+WIFISCAN:4,1,44A1917CA62B,-71
+WIFISCAN:4,2,CC057790A7C1,-74
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:14][CAT1]wifi scan report total[4]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2437mV
OVER 150
                                         

2025-07-31 23:09:05:837 ==>> 原始值:【2437】, 乘以分压基数【2】还原值:【4874】
2025-07-31 23:09:05:888 ==>> 【读取AD_V16电压(前)】通过,【4874mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:09:05:891 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:09:05:894 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:06:228 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3171  volt:5574 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:7 mv
[D][05:18:14][COMM]adc read in 24v adc:1307  volt:33057 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3116  volt:5477 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2388  volt:3847 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:09:06:434 ==>> 【转刹把供电电压(主控ADC)】通过,【5477mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:09:06:438 ==>> 检测【转刹把供电电压】
2025-07-31 23:09:06:444 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:06:455 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 23:09:06:785 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3169  volt:5570 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:14][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:14][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:14][COMM]adc read battery ts volt:6 mv
[D][05:18:14][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3110  volt:5466 mv
$GBGGA,150910.512,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,59,,,40,34,,,40,1*73

$GBGSV,7,2,25,7,,,39,60,,,39,3,,,39,25,,,39,1*71

$GBGSV,7,3,25,16,,,38,11,,,38,43,,,38,23,,,38,1*74

$GBGSV,7,4,25,41,,,38,10,,,37,24,,,36,6,,,36,1*49

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,33,5,,,32,44,,,32,4,,,32,1*73

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150910.512,V,,,,,,,,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150910.512,0.000,1519.032,1519.032,48.595,2097152,2097152,2097152*5E

[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2390  volt:3851 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1433  vol

2025-07-31 23:09:06:815 ==>> t:33224 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:09:07:075 ==>> 【转刹把供电电压】通过,【5466mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:09:07:081 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:09:07:105 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:09:07:286 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:09:07:681 ==>> $GBGGA,150911.512,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,39,,,40,59,,,40,1*71

$GBGSV,7,2,25,34,,,40,7,,,39,3,,,39,25,,,39,1*7E

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,38,10,,,37,24,,,36,6,,,36,1*4D

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,32,,,33,5,,,32,44,,,32,1*40

$GBGSV,7,7,25,4,,,32,1*44

$GBRMC,150911.512,V,,,,,,,,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150911.512,0.000,1528.984,1528.984,48.915,2097152,2097152,2097152*5B



2025-07-31 23:09:07:740 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:09:07:745 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:09:07:751 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:09:07:774 ==>> [D][05:18:16][COMM]read battery soc:255


2025-07-31 23:09:07:846 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:09:07:922 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:09:08:106 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:09:08:113 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:09:08:135 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:09:08:208 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:09:08:315 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:09:08:320 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:09:08:420 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:09:08:480 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:09:08:525 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:09:08:570 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:09:08:573 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:09:08:576 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:09:08:630 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150
$GBGGA,150912.512,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,3,,,40,39,,,40,1*4E

$GBGSV,7,2,25,59,,,40,34,,,40,7,,,39,25,,,39,1*4F

$GBGSV,7,3,

2025-07-31 23:09:08:690 ==>> 25,11,,,39,43,,,39,41,,,39,16,,,38,1*71

$GBGSV,7,4,25,23,,,38,10,,,37,24,,,36,6,,,36,1*4D

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,4,,,33,32,,,33,5,,,32,1*75

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150912.512,V,,,,,,,,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150912.512,0.000,1533.958,1533.958,49.073,2097152,2097152,2097152*50



2025-07-31 23:09:08:844 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:09:08:848 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:09:08:851 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:09:08:916 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:09:09:126 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:09:09:130 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:09:09:144 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:09:09:218 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 23:09:09:407 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:09:09:413 ==>> 检测【左刹电压测试1】
2025-07-31 23:09:09:417 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:09:838 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3178  volt:5586 mv
[D][05:18:17][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:17][COMM]adc read left brake adc:1731  volt:2282 mv
[D][05:18:17][COMM]adc read right brake adc:1737  volt:2289 mv
[D][05:18:17][COMM]adc read throttle adc:1730  volt:2280 mv
[D][05:18:17][COMM]adc read battery ts volt:8 mv
[D][05:18:17][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:18:17][COMM]adc read throttle brake in adc:0  volt:0 mv
$GBGGA,150913.512,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,39,,,41,60,,,40,3,,,40,1*4F

$GBGSV,7,2,25,34,,,40,25,,,40,7,,,39,59,,,39,1*4F

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,38,10,,,37,24,,,36,6,,,36,1*4D

$GBGSV,7,5,25,1,,,36,9,,,35,33,,,35,2,,,34,1*4B

$GBGSV,7,6,25,12,,,34,4,,,33,32,,,33,5,,,32,1*75

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,150913.512,V,,,,,,,,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150913.512,0.000,1535.616,1535.616,49.126,2097152,2097152,2097152*50

[D][05:18:17][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:17][COMM]arm_hub

2025-07-31 23:09:09:884 ==>>  adc read vbat adc:2388  volt:3847 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:18][COMM]read battery soc:255


2025-07-31 23:09:09:933 ==>> 【左刹电压测试1】通过,【2282】符合目标值【2250】至【2500】要求!
2025-07-31 23:09:09:938 ==>> 检测【右刹电压测试1】
2025-07-31 23:09:09:964 ==>> 【右刹电压测试1】通过,【2289】符合目标值【2250】至【2500】要求!
2025-07-31 23:09:09:968 ==>> 检测【转把电压测试1】
2025-07-31 23:09:09:993 ==>> 【转把电压测试1】通过,【2280】符合目标值【2250】至【2500】要求!
2025-07-31 23:09:09:999 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:09:10:019 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:09:10:124 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 23:09:10:271 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:09:10:275 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:09:10:282 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:09:10:322 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:09:10:555 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:09:10:560 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:09:10:564 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:09:10:612 ==>> 3A A3 05 00 A3 


2025-07-31 23:09:10:687 ==>> $GBGGA,150914.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,39,,,40,59,,,40,1*71

$GBGSV,7,2,25,34,,,40,25,,,40,7,,,39,3,,,39,1*70

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,38,10,,,37,1,,,37,24,,,36,1*4B

$GBGSV,7,5,25,6,,,36,33,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,44,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150914.512,V,,,,,,,,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150914.512,0.000,1533.957,1533.957,49.072,2097152,2097152,2097152*57



2025-07-31 23:09:10:718 ==>> OFF_OUT5
OVER 150


2025-07-31 23:09:10:828 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:09:10:832 ==>> 检测【左刹电压测试2】
2025-07-31 23:09:10:838 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:09:11:132 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3170  volt:5572 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:19][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:19][COMM]adc read battery ts volt:14 mv
[D][05:18:19][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:19][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2389  volt:3849 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:09:11:362 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 23:09:11:366 ==>> 检测【右刹电压测试2】
2025-07-31 23:09:11:393 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 23:09:11:408 ==>> 检测【转把电压测试2】
2025-07-31 23:09:11:422 ==>> 【转把电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 23:09:11:426 ==>> 检测【晶振检测】
2025-07-31 23:09:11:432 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:09:11:699 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]
$GBGGA,150915.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,7,2,25,34,,,40,7,,,39,3,,,39,25,,,39,1*7E

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,38,10,,,37,24,,,36,6,,,36,1*4D

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,4,,,33,5,,,32,44,,,32,1*75

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150915.512,V,,,,,,,,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150915.512,0.000,1527.322,1527.322,48.859,2097152,2097152,2097152*56



2025-07-31 23:09:11:774 ==>> [D][05:18:20][COMM]read battery soc:255


2025-07-31 23:09:12:032 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:09:12:038 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:09:12:051 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:09:12:136 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1667mV
Get AD_V4 1650mV
Get AD_V5 2772mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 23:09:12:330 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:09:12:349 ==>> 检测【检测BootVer】
2025-07-31 23:09:12:355 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:09:12:746 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071539482
[D][05:18:20][FCTY]HardwareID  = 867222087569564
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 33, adc = 1311
[D][05:18:20][FCTY]Acckey1 vol = 5577 mv, Acckey2 vol = 101 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20][FCTY]Bat1         = 3685 mv
[D][05:1

2025-07-31 23:09:12:836 ==>> 8:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
$GBGGA,150916.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,3,,,40,39,,,40,1*4E

$GBGSV,7,2,25,59,,,40,34,,,40,7,,,39,25,,,39,1*4F

$GBGSV,7,3,25,11,,,39,16,,,38,43,,,38,23,,,38,1*75

$GBGSV,7,4,25,41,,,38,10,,,37,24,,,36,6,,,36,1*49

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,44,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150916.512,V,,,,,,,,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150916.512,0.000,1530.639,1530.639,48.965,2097152,2097152,2097152*5B



2025-07-31 23:09:12:895 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:09:12:900 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:09:12:912 ==>> 检测【检测固件版本】
2025-07-31 23:09:12:918 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:09:12:930 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:09:12:943 ==>> 检测【检测蓝牙版本】
2025-07-31 23:09:12:950 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:09:12:971 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:09:12:975 ==>> 检测【检测MoBikeId】
2025-07-31 23:09:12:979 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:09:12:987 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:09:12:991 ==>> 检测【检测蓝牙地址】
2025-07-31 23:09:12:995 ==>> 取到目标值:C34D48BE193D
2025-07-31 23:09:13:003 ==>> 【检测蓝牙地址】通过,【C34D48BE193D】符合目标值【】要求!
2025-07-31 23:09:13:007 ==>> 提取到蓝牙地址:C34D48BE193D
2025-07-31 23:09:13:011 ==>> 检测【BOARD_ID】
2025-07-31 23:09:13:029 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:09:13:033 ==>> 检测【检测充电电压】
2025-07-31 23:09:13:075 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:09:13:080 ==>> 检测【检测VBUS电压1】
2025-07-31 23:09:13:105 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:09:13:109 ==>> 检测【检测充电电流】
2025-07-31 23:09:13:136 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:09:13:140 ==>> 检测【检测IMEI】
2025-07-31 23:09:13:147 ==>> 取到目标值:867222087569564
2025-07-31 23:09:13:169 ==>> 【检测IMEI】通过,【867222087569564】符合目标值【】要求!
2025-07-31 23:09:13:173 ==>> 提取到IMEI:867222087569564
2025-07-31 23:09:13:177 ==>> 检测【检测IMSI】
2025-07-31 23:09:13:184 ==>> 取到目标值:460130071539482
2025-07-31 23:09:13:203 ==>> 【检测IMSI】通过,【460130071539482】符合目标值【】要求!
2025-07-31 23:09:13:207 ==>> 提取到IMSI:460130071539482
2025-07-31 23:09:13:211 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:09:13:216 ==>> 取到目标值:460130071539482
2025-07-31 23:09:13:247 ==>> 【校验网络运营商(移动)】通过,【460130071539482】符合目标值【】要求!
2025-07-31 23:09:13:251 ==>> 检测【打开CAN通信】
2025-07-31 23:09:13:255 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:09:13:323 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:09:13:525 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:09:13:530 ==>> 检测【检测CAN通信】
2025-07-31 23:09:13:537 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:09:13:646 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:09:13:752 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,150917.512,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,39,,,40,59,,,40,1*72

$GBGSV,7,2,25,34,,,40,7,,,39,3,,,39,25,,,39,1*7E

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,23,,,37,10,,,36,24,,,36,6,,,36,1*43

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,44,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150917.512,V,,,,,,,,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150917.512,0.000,1525.661,1525.661,48.803,2097152,2097152,2097152*5B

[D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33015
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:09:13:783 ==>>                                          

2025-07-31 23:09:13:812 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:09:13:819 ==>> 检测【关闭CAN通信】
2025-07-31 23:09:13:825 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:09:13:843 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:09:13:918 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:09:14:092 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:09:14:097 ==>> 检测【打印IMU STATE】
2025-07-31 23:09:14:100 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:09:14:319 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:22][COMM]YAW data: 32763[32763]
[D][05:18:22][COMM]pitch:-66 roll:0
[D][05:18:22][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:09:14:367 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:09:14:372 ==>> 检测【六轴自检】
2025-07-31 23:09:14:395 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:09:14:516 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:09:14:991 ==>> $GBGGA,150914.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,7,,,39,1*46

$GBGSV,7,2,25,60,,,39,3,,,39,59,,,39,25,,,39,1*4A

$GBGSV,7,3,25,41,,,39,16,,,38,11,,,38,43,,,38,1*71

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,6,,,36,1*42

$GBGSV,7,5,25,1,,,36,33,,,35,2,,,34,9,,,34,1*4A

$GBGSV,7,6,25,12,,,34,5,,,33,4,,,33,44,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150914.512,V,,,,,,,,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150914.512,0.000,1522.339,1522.339,48.691,2097152,2097152,2097152*5D



2025-07-31 23:09:15:682 ==>> $GBGGA,150915.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,42,60,,,40,39,,,40,34,,,40,1*7A

$GBGSV,7,2,25,7,,,39,3,,,39,59,,,39,25,,,39,1*7B

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,1,,,37,23,,,37,10,,,36,24,,,36,1*45

$GBGSV,7,5,25,6,,,36,33,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,12,,,34,5,,,32,44,,,32,4,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150915.512,V,,,,,,,,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150915.512,0.000,1524.010,1524.010,48.757,2097152,2097152,2097152*57



2025-07-31 23:09:15:787 ==>> [D][05:18:24][COMM]read battery soc:255


2025-07-31 23:09:16:223 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:09:16:432 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35723 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-6,-2,4067]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 23:09:16:689 ==>> $GBGGA,150916.512,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,60,,,40,59,,,40,39,,,40,1*72

$GBGSV,7,2,25,34,,,40,7,,,39,3,,,39,25,,,39,1*7E

$GBGSV,7,3,25,11,,,39,43,,,39,41,,,39,16,,,38,1*71

$GBGSV,7,4,25,23,,,38,10,,,37,1,,,37,24,,,36,1*4B

$GBGSV,7,5,25,6,,,36,33,,,35,2,,,34,9,,,34,1*4D

$GBGSV,7,6,25,12,,,34,5,,,32,44,,,32,4,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150916.512,V,,,,,,,,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150916.512,0.000,1528.984,1528.984,48.916,2097152,2097152,2097152*5F



2025-07-31 23:09:16:694 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:09:16:698 ==>> 检测【打印IMU STATE2】
2025-07-31 23:09:16:702 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:09:16:914 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:09:16:977 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:09:16:982 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:09:16:990 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:09:17:127 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:09:17:232 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry

2025-07-31 23:09:17:251 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:09:17:260 ==>> 检测【检测VBUS电压2】
2025-07-31 23:09:17:269 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:09:17:296 ==>>  i = 3,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 12


2025-07-31 23:09:17:607 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539482
[D][05:18:25][FCTY]HardwareID  = 867222087569564
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 8, adc = 342
[D][05:18:25][FCTY]Acckey1 vol = 5575 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3685 mv
[D][05:18:25][FCT

2025-07-31 23:09:17:712 ==>> Y]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:09:17:775 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:09:18:182 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539482
[D][05:18:26][FCTY]HardwareID  = 867222087569564
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 179
[D][05:18:26][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][

2025-07-31 23:09:18:227 ==>> 05:18:26][FCTY]Bat1         = 3685 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:09:18:305 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:09:18:784 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539482
[D][05:18:26][FCTY]HardwareID  = 867222087569564
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 3, adc = 151
[D][05:18:26][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1 

2025-07-31 23:09:18:832 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:09:18:889 ==>>         = 3685 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
$GBGGA,150918.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,40,,,41,39,,,40,34,,,40,7,,,39,1*46

$GBGSV,7,2,25,60,,,39,3,,,39,59,,,39,25,,,39,1*4A

$GBGSV,7,3,25,11,,,39,41,,,39,16,,,38,43,,,38,1*70

$GBGSV,7,4,25,10,,,37,23,,,37,24,,,36,1,,,36,1*45

$GBGSV,7,5,25,6,,,35,33,,,35,2,,,34,9,,,34,1*4E

$GBGSV,7,6,25,12,,,34,5,,,32,44,,,32,4,,,32,1*74

$GBGSV,7,7,25,32,,,32,1*71

$GBRMC,150918.512,V,,,,,,,,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,150918.512,0.000,1519.030,1519.030,48.594,2097152,2097152,2097152*57

[D][05:18:26][COMM]msg 0601 loss. last_tick:33004. cur_tick:38008. period:500
[D][05:18:26][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38009


2025-07-31 23:09:19:494 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539482
[D][05:18:27][FCTY]HardwareID  = 867222087569564
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C34D48BE193D
[D][05:18:27][FCTY]Bat         = 3684 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 134
[D][05:18:27][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[

2025-07-31 23:09:19:599 ==>> D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3685 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]all continue location stop
[W][05:18:27][GNSS]sing locating running
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:27][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[D][05:18:2

2025-07-31 23:09:19:619 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:09:19:625 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:09:19:636 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:09:19:704 ==>> 7][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [

2025-07-31 23:09:19:809 ==>> 1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]
[D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:27][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B595FA8027752B276DB46A7EE2CE9A2650F9F53C6227FF5BDD590B3C1A1CE11724D3AE9EC3AB8F99BF3044D00571C587A6853E1A08767F9ECA8C12B2E92F2B260413C39D4C48E6BCFF71ADC16C3ED27754C9D0
[D][05:18:27][CAT1]<<< 
SEND OK

[D][05:18:27][CAT1]exec over: func id: 15, ret: 11
[D][05:18:27][CAT1]s

2025-07-31 23:09:19:869 ==>> ub id: 15, ret: 11

[D][05:18:27][SAL ]Cellular task submsg id[68]
[D][05:18:27][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:27][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:27][M2M ]g_m2m_is_idle become true
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:27][PROT]M2M Send ok [1629955107]
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:09:19:907 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:09:19:912 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:09:19:920 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:09:19:944 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29


2025-07-31 23:09:20:019 ==>> [D][05:18:28][COMM]read battery soc:255
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:09:20:188 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:09:20:193 ==>> 检测【打开WIFI(3)】
2025-07-31 23:09:20:218 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:09:20:409 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 23:09:20:461 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:09:20:467 ==>> 检测【扩展芯片hw】
2025-07-31 23:09:20:472 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:09:21:484 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:09:21:529 ==>> [D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:09:22:108 ==>> [D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM

2025-07-31 23:09:22:213 ==>> ]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],

2025-07-31 23:09:22:318 ==>> send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:29][PROT]index:1
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:5
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900006]
[D][05:18:29][BLE ]BLE_WRN [ble_s

2025-07-31 23:09:22:423 ==>> ervice_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]read battery soc:255


2025-07-31 23:09:22:499 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:09:22:514 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:09:22:728 ==>> [D][05:18:30][COMM]f:[drv_audio_ack_receive].wait ack timeout!![41999]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[W][05:18:30][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 23:09:22:796 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:09:22:801 ==>> 检测【扩展芯片boot】
2025-07-31 23:09:22:818 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:09:22:823 ==>> 检测【扩展芯片sw】
2025-07-31 23:09:22:842 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:09:22:847 ==>> 检测【检测音频FLASH】
2025-07-31 23:09:22:853 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:09:22:999 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:09:23:269 ==>> [D][05:18:31][COMM]42575 imu init OK
[D][05:18:31][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:23:374 ==>> [

2025-07-31 23:09:23:404 ==>> D][05:18:31][CAT1]SEND RAW data timeout
[D][05:18:31][CAT1]exec over: func id: 12, ret: -52


2025-07-31 23:09:23:740 ==>> [D][05:18:32][COMM]f:[drv_audio_ack_receive].wait ack timeout!![43026]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 23:09:24:016 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 23:09:24:286 ==>> [D][05:18:32][COMM]43588 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:24:526 ==>>                                  [D][05:18:32][PROT]index:1 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900006]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:32][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 23:09:24:751 ==>> [D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44056]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:09:25:285 ==>> [D][05:18:33][COMM]44599 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:26:044 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:09:26:331 ==>> [D][05:18:34][COMM]45611 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:26:908 ==>> [D][05:18:35][COMM]crc 108B
[D][05:18:35][COMM]flash test ok


2025-07-31 23:09:27:299 ==>> [D][05:18:35][COMM]46623 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:27:900 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:09:27:905 ==>> 检测【打开喇叭声音】
2025-07-31 23:09:27:915 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:09:28:124 ==>> [D][05:18:36][COMM]read battery soc:255
[W][05:18:36][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:36][COMM]file:A20 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 23:09:28:176 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:09:28:182 ==>> 检测【打开大灯控制】
2025-07-31 23:09:28:189 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:09:28:319 ==>> [D][05:18:36][COMM]47634 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:28:394 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:09:28:461 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:09:28:467 ==>> 检测【关闭仪表供电3】
2025-07-31 23:09:28:488 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:09:28:606 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 23:09:28:790 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:09:28:819 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:09:28:835 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:09:28:997 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:09:29:315 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:09:29:322 ==>> 检测【读大灯电压】
2025-07-31 23:09:29:330 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:09:29:336 ==>> [D][05:18:37][COMM]48645 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:29:509 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[33062]


2025-07-31 23:09:29:584 ==>> [E][05:18:37][GNSS]GPS module no nmea data!
[D][05:18:37][GNSS]GPS reload stop. ret=0
[D][05:18:37][GNSS]GPS reload start. ret=0


2025-07-31 23:09:29:613 ==>> 【读大灯电压】通过,【33062mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:09:29:619 ==>> 检测【关闭大灯控制2】
2025-07-31 23:09:29:645 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:09:29:780 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:09:29:912 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:09:29:918 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:09:29:947 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:09:30:103 ==>> [D][05:18:38][COMM]read battery soc:255
[W][05:18:38][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:38][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:09:30:209 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:09:30:224 ==>> 检测【打开WIFI(4)】
2025-07-31 23:09:30:234 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:09:30:404 ==>> [D][05:18:38][COMM]49656 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 23:09:30:600 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:09:30:606 ==>> 检测【EC800M模组版本】
2025-07-31 23:09:30:611 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:31:358 ==>> [D][05:18:39][COMM]50669 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:31:635 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:32:058 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 23:09:32:364 ==>> [D][05:18:40][COMM]51680 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:09:32:439 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:32:669 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:33:367 ==>> [D][05:18:41][COMM]imu error,enter wait


2025-07-31 23:09:33:722 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:33:816 ==>> [D][05:18:42][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:34:182 ==>> [D][05:18:42][COMM]f:[drv_audio_ack_receive].wait ack timeout!![53310]
[D][05:18:42][COMM]accel parse set 0
[D][05:18:42][COMM][Audio].l:[1032].open hexlog save
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:42][COMM]read file, len:15228, num:4
[D][05:18:42][COMM]--->crc16:0x419c
[D][05:18:42][COMM]read file success
[W][05:18:42][COMM][Audio].l:[936].close hexlog save
[D][05:18:42][COMM]accel parse set 1
[D][05:18:42][COMM][Audio]mon:9,05:18:42
[D][05:18:42][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:42][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:42][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:42][COMM]read battery soc:255


2025-07-31 23:09:34:580 ==>> [D][05:18:42][CAT1]exec over: func id: 15, ret: -93
[D][05:18:42][CAT1]sub id: 15, ret: -93

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:42][SAL ]socket send fail. id[4]
[D][05:18:42][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:42][CAT1]gsm read msg sub id: 24
[D][05:18:42][M2M ]m2m select fd[4]
[D][05:18:42][M2M ]socket[4] Link is disconnected
[D][05:18:42][M2M ]tcpclient close[4]
[D][05:18:42][SAL ]socket[4] has closed
[D][05:18:42][PROT]protocol read data ok
[E][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:42][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:42][PROT]M2M Send Fail [1629955122]
[D][05:18:42][PROT]CLEAN,SEND:1
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:34:764 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:35:117 ==>> [D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54408]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:09:35:799 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:36:146 ==>> [D][05:18:44][COMM]read battery soc:255
[D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55436]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 23:09:36:422 ==>> [D][05:18:44][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 23:09:36:527 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:36:834 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:37:155 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56464]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 23:09:37:870 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:38:073 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 23:09:38:456 ==>> [D][05:18:46][CAT1]exec over: func id: 24, ret: -181
[D][05:18:46][CAT1]sub id: 24, ret: -181

[D][05:18:46][CAT1]gsm read msg sub id: 23
[D][05:18:46][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 23:09:38:654 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][GNSS]recv submsg id[1]
[D][05:18:46][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:46][GNSS]stop gps fail


2025-07-31 23:09:38:821 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:38:911 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:38:956 ==>> [D][05:18:47][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 23:09:39:499 ==>> [D][05:18:47][CAT1]exec over: func id: 23, ret: -151
[D][05:18:47][CAT1]sub id: 23, ret: -151

[D][05:18:47][CAT1]gsm read msg sub id: 12
[D][05:18:47][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 23:09:39:681 ==>> [D][05:18:47][GNSS]recv submsg id[1]
[D][05:18:47][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:47][GNSS]start gps fail
[E][05:18:47][GNSS]GPS module no nmea data!
[D][05:18:47][GNSS]GPS reload stop. ret=0
[D][05:18:47][GNSS]GPS reload start. ret=0


2025-07-31 23:09:39:941 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:40:082 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 23:09:40:638 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:40:990 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:41:323 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:42:031 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:42:078 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 23:09:42:539 ==>> [D][05:18:50][CAT1]SEND RAW data timeout
[D][05:18:50][CAT1]exec over: func id: 12, ret: -52
[W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:09:43:067 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:43:820 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:44:095 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 23:09:44:110 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:44:535 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:45:144 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:45:538 ==>> [D][05:18:53][CAT1]SEND RAW data timeout
[D][05:18:53][CAT1]exec over: func id: 12, ret: -52
[W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:53][CAT1]gsm read msg sub id: 10
[D][05:18:53][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:09:46:094 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 23:09:46:184 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:46:320 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:47:220 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:47:558 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:48:103 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 23:09:48:254 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:48:825 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:49:300 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:49:518 ==>> [D][05:18:57][COMM]f:[drv_audio_ack_receive].wait ack timeout!![68807]
[D][05:18:57][COMM]accel parse set 0
[D][05:18:57][COMM][Audio].l:[1032].open hexlog save
[D][05:18:57][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 23:09:49:608 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:49:713 ==>> [E][05:18:58][GNSS]GPS module no nmea data!
[D][05:18:58][GNSS]GPS reload stop. ret=0
[D][05:18:58][GNSS]GPS reload start. ret=0


2025-07-31 23:09:50:098 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 23:09:50:344 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:51:323 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:51:383 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:51:663 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:52:102 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 23:09:52:423 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:53:457 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:53:547 ==>> [D][05:19:01][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:09:53:697 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:53:802 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:54:117 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:09:54:473 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:55:513 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:55:769 ==>> [D][05:19:04][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:56:120 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 23:09:56:316 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:56:546 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:57:571 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:57:804 ==>> [W][05:19:06][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 23:09:58:126 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 23:09:58:609 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:58:819 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:09:59:640 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:09:59:810 ==>> [E][05:19:08][GNSS]GPS module no nmea data!
[D][05:19:08][GNSS]GPS reload stop. ret=0
[D][05:19:08][GNSS]GPS reload start. ret=0


2025-07-31 23:09:59:855 ==>>                                                                   

2025-07-31 23:10:00:146 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 23:10:00:680 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:10:01:317 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 23:10:01:546 ==>> [D][05:19:09][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 23:10:01:728 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 23:10:01:738 ==>> #################### 【测试结束】 ####################
2025-07-31 23:10:01:873 ==>> 关闭5V供电
2025-07-31 23:10:01:883 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:10:02:082 ==>> [W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:10][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:10:02:142 ==>>                                          

2025-07-31 23:10:02:884 ==>> 关闭5V供电成功
2025-07-31 23:10:02:893 ==>> 关闭33V供电
2025-07-31 23:10:02:903 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:10:03:024 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:10:03:204 ==>> [D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 12
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 23:10:03:896 ==>> 关闭33V供电成功
2025-07-31 23:10:03:906 ==>> 关闭3.7V供电
2025-07-31 23:10:03:931 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:10:04:021 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:10:04:344 ==>>  

2025-07-31 23:10:04:605 ==>>  

