2025-07-31 18:16:06:764 ==>> MES查站成功:
查站序号:P5100010053130FE验证通过
2025-07-31 18:16:06:767 ==>> 扫码结果:P5100010053130FE
2025-07-31 18:16:06:769 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:16:06:770 ==>> 测试参数版本:2024.10.11
2025-07-31 18:16:06:772 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:16:06:773 ==>> 检测【打开透传】
2025-07-31 18:16:06:775 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:16:06:865 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:16:07:369 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:16:07:372 ==>> 检测【检测接地电压】
2025-07-31 18:16:07:374 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:16:07:471 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:16:07:671 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:16:07:674 ==>> 检测【打开小电池】
2025-07-31 18:16:07:677 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:16:07:761 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:16:07:971 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:16:07:973 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:16:07:976 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:16:08:062 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 18:16:08:278 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:16:08:281 ==>> 检测【等待设备启动】
2025-07-31 18:16:08:284 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:16:08:519 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:16:08:716 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:16:09:306 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:16:09:446 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:16:09:826 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:16:10:297 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:16:10:361 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:16:10:363 ==>> 检测【产品通信】
2025-07-31 18:16:10:365 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:16:10:555 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:16:10:641 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:16:10:643 ==>> 检测【初始化完成检测】
2025-07-31 18:16:10:646 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:16:10:893 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE42500] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 18:16:10:953 ==>>                                                              

2025-07-31 18:16:11:172 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:16:11:175 ==>> 检测【关闭大灯控制1】
2025-07-31 18:16:11:176 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:16:11:382 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]2647 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:11:443 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:16:11:446 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:16:11:447 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:16:11:487 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[

2025-07-31 18:16:11:517 ==>> W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 18:16:11:622 ==>> [W

2025-07-31 18:16:11:667 ==>> ][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:16:11:716 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:16:11:719 ==>> 检测【关闭仪表供电】
2025-07-31 18:16:11:720 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:16:11:865 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:16:11:989 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:16:11:991 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:16:11:994 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:16:12:124 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:16:12:269 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:16:12:294 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:16:12:298 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:16:12:367 ==>> [D][05:17:52][COMM]3658 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:12:441 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:16:12:550 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:16:12:553 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:16:12:554 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:16:12:746 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:16:12:825 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:16:12:827 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:16:12:829 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:16:12:971 ==>> 5A A5 01 5A A5 


2025-07-31 18:16:13:061 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:16:13:095 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:16:13:099 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:16:13:101 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:16:13:166 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255
5A A5 03 5A A5 


2025-07-31 18:16:13:271 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 18:16:13:406 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:16:13:409 ==>> 该项需要延时执行
2025-07-31 18:16:13:421 ==>> [D][05:17:53][COMM]4669 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 18:16:13:908 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5011. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5012. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5012. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5013. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5014. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5014. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5015. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5015. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5016
[D][05:17:54][C

2025-07-31 18:16:13:938 ==>> OMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5016


2025-07-31 18:16:14:394 ==>> [D][05:17:54][COMM]5680 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:14:755 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:16:15:256 ==>>                                                                                             Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE42600] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COM

2025-07-31 18:16:15:361 ==>> M]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/

2025-07-31 18:16:15:466 ==>> 48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble

2025-07-31 18:16:15:556 ==>>  is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                 

2025-07-31 18:16:16:429 ==>> [D][05:17:56][COMM]7701 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:56][CAT1]power_urc_cb ret[76]


2025-07-31 18:16:17:172 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 18:16:17:414 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:16:17:417 ==>> 检测【33V输入电压ADC】
2025-07-31 18:16:17:420 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:17:429 ==>> [D][05:17:57][COMM]8712 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:17:671 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3124  volt:5491 mv
[D][05:17:57][COMM]adc read out 24v adc:1305  volt:33007 mv
[D][05:17:57][COMM]adc read left brake adc:10  volt:13 mv
[D][05:17:57][COMM]adc read right brake adc:11  volt:14 mv
[D][05:17:57][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:57][COMM]adc read battery ts volt:14 mv
[D][05:17:57][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:57][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:16:17:695 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:16:17:699 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:16:17:702 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:16:17:776 ==>> 1A A1 00 00 FC 
Get AD_V2 1633mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2766mV
Get AD_V6 1991mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:16:17:976 ==>> 【TP7_VCC3V3(ADV2)】通过,【1633mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:17:979 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:16:17:995 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:17:997 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:16:17:998 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 18:16:18:013 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:16:18:015 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:16:18:032 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:16:18:034 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:16:18:055 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:16:18:058 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:16:18:173 ==>> 1A A1 00 00 FC 
Get AD_V2 1634mV
Get AD_V3 1655mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1987mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:16:18:358 ==>> 【TP7_VCC3V3(ADV2)】通过,【1634mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:18:361 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:16:18:384 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:18:386 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:16:18:389 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:16:18:412 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:16:18:415 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:16:18:436 ==>> [D][05:17:58][COMM]9723 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:18:460 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:16:18:463 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:16:18:492 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:16:18:495 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:16:18:570 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1655mV
Get AD_V4 0mV
Get AD_V5 2768mV
Get AD_V6 1987mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:16:18:780 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10006
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 18:16:18:806 ==>> 【TP7_VCC3V3(ADV2)】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:18:809 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:16:18:860 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:18:863 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:16:18:865 ==>> 原始值:【2768】, 乘以分压基数【2】还原值:【5536】
2025-07-31 18:16:18:911 ==>> 【TP68_VCC5V5(ADV5)】通过,【5536mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:16:18:933 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:16:18:960 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1987mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:16:18:963 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:16:19:017 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:16:19:019 ==>> 检测【打开WIFI(1)】
2025-07-31 18:16:19:022 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:16:19:179 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM]read battery soc:255


2025-07-31 18:16:19:306 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:16:19:309 ==>> 检测【清空消息队列(1)】
2025-07-31 18:16:19:311 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:16:19:687 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][HSDK][0] flush to flash addr:[0xE42700] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][COMM]10734 imu init OK
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17

2025-07-31 18:16:19:747 ==>> :59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 18:16:19:849 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:16:19:853 ==>> 检测【打开GPS(1)】
2025-07-31 18:16:19:855 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:16:20:145 ==>>                                                                                   [D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087476927

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071536203

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31

2025-07-31 18:16:20:175 ==>> ] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 18:16:20:386 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:16:20:390 ==>> 检测【打开GSM联网】
2025-07-31 18:16:20:394 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:16:20:862 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 5, ret: 6
[D][05:18:00][CAT1]sub id: 5, ret: 6

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:00][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:00][M2M ]M2M_GSM_INIT OK
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:00][SAL ]open socket ind id[4], rst[0]
[D][05:18:00][M2M ]m2m tcp client connect success host[bikea

2025-07-31 18:16:20:918 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:16:20:925 ==>> 检测【打开仪表供电1】
2025-07-31 18:16:20:930 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:16:20:967 ==>> pi.mobike.com] port[9999]
[D][05:18:00][SAL ]Cellular task submsg id[8]
[D][05:18:00][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:00][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:00][CAT1]gsm read msg sub id: 8
[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:00][COMM]Main Task receive event:4
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:00][COMM]init key as 
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:00][COMM]Main Task receive event:4 finished processing
[D][05:18:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"10.184.189.230"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:16:21:072 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack

2025-07-31 18:16:21:132 ==>>  sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 18:16:21:192 ==>>                                          

2025-07-31 18:16:21:195 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:16:21:200 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:16:21:203 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:16:21:357 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:16:21:541 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:16:21:544 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:16:21:547 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:16:21:691 ==>> [D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:16:21:766 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33433]


2025-07-31 18:16:21:911 ==>> 【读取主控ADC采集的仪表电压】通过,【33433mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:16:21:917 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:16:21:936 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:16:22:056 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:16:22:271 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:16:22:274 ==>> 检测【AD_V20电压】
2025-07-31 18:16:22:277 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:16:22:380 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:16:22:488 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13745 imu init OK
1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150


2025-07-31 18:16:22:548 ==>> 本次取值间隔时间:161ms
2025-07-31 18:16:22:570 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:22:573 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:16:22:575 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:16:22:668 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:16:22:856 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:16:22:859 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:16:22:864 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:16:23:293 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE42800] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,10,24,,,42,42,,,42,38,,,41,16,,,40,1*78

$GBGSV,3,2,10,26,,,40,39,,,40,33,,,37,60,,,39,1*70

$GBGSV,3,3,10,59,,,38,14,,,36,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1670.104,1670.104,53.353,2097152,2097152,2097152*4C

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 18:16:23:386 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:16:23:390 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:16:23:392 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:16:23:474 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
3A A3 02 01 A3 


2025-07-31 18:16:23:564 ==>> ON_OUT2
OVER 150


2025-07-31 18:16:23:658 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:16:23:662 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:16:23:667 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:16:23:864 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:16:23:928 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:16:23:931 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:16:23:934 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:16:24:247 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:04][COMM]oneline display set 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,42,42,,,42,38,,,41,26,,,41,1*7B

$GBGSV,5,2,17,60,,,41,3,,,41,16,,,40,39,,,40,1*4F

$GBGSV,5,3,17,59,,,40,21,,,40,13,,,40,1,,,40,1*4A

$GBGSV,5,4,17,33,,,38,14,,,37,8,,,37,4,,,35,1*75

$GBGSV,5,5,17,5,,,35,1*43

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1633.903,1633.903,52.226,2097152,2097152,2097152*4E



2025-07-31 18:16:24:488 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:16:24:492 ==>> 检测【AD_V21电压】
2025-07-31 18:16:24:496 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:16:24:568 ==>> 1A A1 20 00 00 
Get AD_V21 1061mV
OVER 150


2025-07-31 18:16:24:853 ==>> 本次取值间隔时间:351ms
2025-07-31 18:16:24:872 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:16:24:961 ==>> 1A A1 20 00 00 
Get AD_V21 1640mV
OVER 150


2025-07-31 18:16:25:052 ==>> 本次取值间隔时间:176ms
2025-07-31 18:16:25:070 ==>> 【AD_V21电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:25:075 ==>> 检测【关闭仪表供电2】
2025-07-31 18:16:25:080 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:16:25:308 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,42,,,42,38,,,41,26,,,41,1*75

$GBGSV,5,2,19,60,,,41,3,,,41,13,,,41,39,,,40,1*45

$GBGSV,5,3,19,59,,,40,21,,,40,16,,,39,8,,,39,1*48

$GBGSV,5,4,19,1,,,38,33,,,38,14,,,37,2,,,36,1*78

$GBGSV,5,5,19,4,,,34,5,,,34,40,,,32,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1605.958,1605.958,51.368,2097152,2097152,2097152*46

[D][05:18:05][COMM]read battery soc:255
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:05][COMM]set POWER 0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:16:25:341 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:16:25:344 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:16:25:346 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:16:25:549 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:05][COMM][oneline_display]: command mode, OFF!


2025-07-31 18:16:25:614 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:16:25:617 ==>> 检测【打开AccKey2供电】
2025-07-31 18:16:25:622 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:16:25:732 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:16:25:896 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:16:25:901 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:16:25:905 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:26:296 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:06][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:06][COMM]adc read out 24v adc:1307  volt:33057 mv
[D][05:18:06][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:06][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:06][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:06][COMM]adc read battery ts volt:13 mv
[D][05:18:06][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:06][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:06][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:06][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:06][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:06][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:06][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,101630.022,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,60,,,42,42,,,41,38,,,41,1*7F

$GBGSV,6,2,21,26,,,41,3,,,41,13,,,41,21,,,41,1*47

$GBGSV,6,3,21,39,,,40,59,,,40,8,,,40,16,,,39,1*47

$GBGSV,6,4,21,1,,,38,33,,,38,14,,,37,2,,,36,1*70

$GBGSV,6,5,21,5,,,34,4,,,33,40,,,32,6,,,39,1*49

$GBGSV,6,6,21,9,,,36,1*49

$GBRMC,101630.022,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20


2025-07-31 18:16:26:325 ==>> 
$GBGST,101630.022,0.000,1608.147,1608.147,51.445,2097152,2097152,2097152*55



2025-07-31 18:16:26:434 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33057mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:16:26:438 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:16:26:440 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:16:26:735 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,101630.522,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,60,,,41,42,,,41,38,,,41,1*7F

$GBGSV,6,2,22,26,,,41,3,,,41,13,,,41,21,,,40,1*45

$GBGSV,6,3,22,39,,,40,59,,,40,8,,,40,16,,,40,1*4A

$GBGSV,6,4,22,6,,,38,1,,,38,33,,,38,14,,,37,1*79

$GBGSV,6,5,22,2,,,36,5,,,33,4,,,33,40,,,32,1*46

$GBGSV,6,6,22,45,,,48,7,,,36,1*49

$GBRMC,101630.522,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101630.522,0.000,1602.363,1602.363,51.258,2097152,2097152,2097152*5A



2025-07-31 18:16:26:974 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:16:26:978 ==>> 该项需要延时执行
2025-07-31 18:16:27:237 ==>> [D][05:18:07][COMM]read battery soc:255
[D][05:18:07][COMM]S->M yaw:INVALID


2025-07-31 18:16:27:749 ==>> $GBGGA,101631.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,42,60,,,41,42,,,41,38,,,41,1*79

$GBGSV,6,2,24,26,,,41,3,,,41,13,,,41,21,,,40,1*43

$GBGSV,6,3,24,39,,,40,59,,,40,8,,,40,16,,,39,1*42

$GBGSV,6,4,24,6,,,38,1,,,38,33,,,38,14,,,37,1*7F

$GBGSV,6,5,24,9,,,37,2,,,36,5,,,33,4,,,33,1*78

$GBGSV,6,6,24,40,,,32,7,,,29,32,,,37,45,,,36,1*48

$GBRMC,101631.502,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101631.502,0.000,788.142,788.142,720.774,2097152,2097152,2097152*63



2025-07-31 18:16:28:269 ==>> [D][05:18:08][COMM]M->S yaw:INVALID


2025-07-31 18:16:28:735 ==>> $GBGGA,101632.502,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,60,,,41,1*75

$GBGSV,6,2,23,38,,,41,3,,,41,26,,,41,21,,,41,1*4C

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,33,,,38,9,,,37,1*44

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,33,4,,,33,1*43

$GBGSV,6,6,23,40,,,32,7,,,30,32,,,,1*47

$GBRMC,101632.502,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101632.502,0.000,791.904,791.904,724.215,2097152,2097152,2097152*66



2025-07-31 18:16:29:209 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:16:29:729 ==>> $GBGGA,101633.502,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,60,,,41,13,,,41,1*74

$GBGSV,6,2,22,38,,,41,3,,,41,21,,,41,26,,,41,1*4D

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,6,,,38,33,,,38,9,,,37,1*45

$GBGSV,6,5,22,14,,,37,2,,,36,5,,,33,4,,,33,1*42

$GBGSV,6,6,22,40,,,32,7,,,31,1*46

$GBRMC,101633.502,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101633.502,0.000,792.839,792.839,725.070,2097152,2097152,2097152*67



2025-07-31 18:16:29:984 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:16:29:989 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:16:30:016 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:30:292 ==>> [D][05:18:10][HSDK][0] flush to flash addr:[0xE42900] --- write len --- [256]
[W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:10][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:10][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:10][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:10][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:10][COMM]adc read battery ts volt:12 mv
[D][05:18:10][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:10][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1443  volt:33456 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:16:30:549 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【101mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:16:30:553 ==>> 检测【打开AccKey1供电】
2025-07-31 18:16:30:555 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:16:30:795 ==>> $GBGGA,101634.502,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,42,60,,,41,13,,,41,1*74

$GBGSV,6,2,22,38,,,41,3,,,41,26,,,41,21,,,41,1*4D

$GBGSV,6,3,22,8,,,40,39,,,40,59,,,40,16,,,39,1*44

$GBGSV,6,4,22,1,,,38,6,,,38,14,,,38,33,,,38,1*76

$GBGSV,6,5,22,9,,,37,2,,,36,5,,,33,7,,,32,1*7C

$GBGSV,6,6,22,40,,,32,4,,,32,1*46

$GBRMC,101634.502,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101634.502,0.000,793.779,793.779,725.928,2097152,2097152,2097152*64

[W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 18:16:30:868 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:16:30:872 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:16:30:875 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:16:30:962 ==>> 1A A1 00 40 00 
Get AD_V14 2662mV
OVER 150


2025-07-31 18:16:31:127 ==>> 原始值:【2662】, 乘以分压基数【2】还原值:【5324】
2025-07-31 18:16:31:165 ==>> 【读取AccKey1电压(ADV14)前】通过,【5324mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:16:31:169 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:16:31:171 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:31:201 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 18:16:31:475 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:11][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:11][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:11][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:11][COMM]adc read battery ts volt:13 mv
[D][05:18:11][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:11][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 18:16:31:716 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5493mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:16:31:720 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:16:31:723 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:16:31:732 ==>> $GBGGA,101635.502,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,40,,,33,1*48

$GBGSV,6,6,23,4,,,33,45,,,32,7,,,32,1*75

$GBRMC,101635.502,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101635.502,0.000,790.752,790.752,723.160,2097152,2097152,2097152*67



2025-07-31 18:16:31:838 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:16:32:021 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:16:32:024 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:16:32:027 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:16:32:171 ==>> 1A A1 00 40 00 
Get AD_V14 2667mV
OVER 150


2025-07-31 18:16:32:275 ==>> 原始值:【2667】, 乘以分压基数【2】还原值:【5334】
2025-07-31 18:16:32:295 ==>> 【读取AccKey1电压(ADV14)后】通过,【5334mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:16:32:298 ==>> 检测【打开WIFI(2)】
2025-07-31 18:16:32:301 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:16:32:486 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:12][CAT1]gsm read msg sub id: 12
[D][05:18:12][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:12][CAT1]<<< 
OK

[D][05:18:12][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:16:32:613 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:16:32:616 ==>> 检测【转刹把供电】
2025-07-31 18:16:32:619 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:16:32:771 ==>> $GBGGA,101636.502,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,40,,,33,1*48

$GBGSV,6,6,23,4,,,33,45,,,32,7,,,32,1*75

$GBRMC,101636.502,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101636.502,0.000,790.752,790.752,723.160,2097152,2097152,2097152*64

[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:16:32:921 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:16:32:925 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:16:32:928 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:16:33:030 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:16:33:275 ==>> +WIFISCAN:4,0,CC057790A740,-69
+WIFISCAN:4,1,CC057790A741,-74
+WIFISCAN:4,2,CC057790A7C0,-80
+WIFISCAN:4,3,CC057790A5C1,-81

[D][05:18:13][CAT1]wifi scan report total[4]
[D][05:18:13][COMM]read battery soc:255
[W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:16:33:506 ==>> [D][05:18:13][GNSS]recv submsg id[3]


2025-07-31 18:16:33:733 ==>> $GBGGA,101637.502,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,33,,,39,1,,,38,6,,,38,14,,,38,1*76

$GBGSV,6,5,23,2,,,37,9,,,37,5,,,34,40,,,33,1*49

$GBGSV,6,6,23,4,,,33,45,,,32,7,,,32,1*75

$GBRMC,101637.502,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101637.502,0.000,792.550,792.550,724.804,2097152,2097152,2097152*69



2025-07-31 18:16:33:962 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:16:34:073 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:16:34:133 ==>> [W][05:18:14][COMM]>>>>>Input command = ?<<<<


2025-07-31 18:16:34:163 ==>> 1A A1 00 80 00 
Get AD_V15 2404mV
OVER 150


2025-07-31 18:16:34:224 ==>> 原始值:【2404】, 乘以分压基数【2】还原值:【4808】
2025-07-31 18:16:34:243 ==>> 【读取AD_V15电压(前)】通过,【4808mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:16:34:247 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:16:34:250 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:16:34:347 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:16:34:470 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2431mV
OVER 150


2025-07-31 18:16:34:501 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 18:16:34:529 ==>> 【读取AD_V16电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:16:34:533 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:16:34:536 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:34:893 ==>> $GBGGA,101638.502,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,42,,,42,38,,,41,1*7B

$GBGSV,6,2,23,60,,,41,3,,,41,59,,,41,26,,,41,1*4E

$GBGSV,6,3,23,21,,,41,8,,,40,39,,,40,16,,,39,1*4B

$GBGSV,6,4,23,33,,,39,1,,,38,6,,,38,14,,,38,1*76

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,40,,,33,1*48

$GBGSV,6,6,23,4,,,33,45,,,32,7,,,32,1*75

$GBRMC,101638.502,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101638.502,0.000,793.454,793.454,725.632,2097152,2097152,2097152*6C

[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:15][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3082  volt:5417 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:15][CO

2025-07-31 18:16:34:923 ==>> MM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:16:35:071 ==>> 【转刹把供电电压(主控ADC)】通过,【5417mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:16:35:078 ==>> 检测【转刹把供电电压】
2025-07-31 18:16:35:091 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:35:394 ==>> [D][05:18:15][COMM]read battery soc:255
[D][05:18:15][HSDK][0] flush to flash addr:[0xE42A00] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:15][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:15][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:15][COMM]adc read battery ts volt:10 mv
[D][05:18:15][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3076  volt:5407 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2413  volt:3888 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:16:35:612 ==>> 【转刹把供电电压】通过,【5407mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:16:35:617 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:16:35:622 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:16:35:785 ==>> $GBGGA,101639.502,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,42,,,42,8,,,41,1*48

$GBGSV,6,2,23,38,,,41,60,,,41,3,,,41,26,,,41,1*49

$GBGSV,6,3,23,21,,,41,59,,,40,39,,,40,16,,,39,1*7F

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,4,,,33,1*78

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101639.502,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101639.502,0.000,791.658,791.658,723.990,2097152,2097152,2097152*6C

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:16:35:898 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:16:35:904 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:16:35:909 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:16:35:998 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:16:36:060 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 3mV
OVER 150


2025-07-31 18:16:36:161 ==>> 【读取AD_V15电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:16:36:173 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:16:36:183 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:16:36:270 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:16:36:361 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:16:36:405 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:16:36:409 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:16:36:414 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:16:36:467 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:16:36:680 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:16:36:686 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:16:36:694 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:16:36:737 ==>> $GBGGA,101640.502,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,8,,,41,1*4B

$GBGSV,6,2,23,38,,,41,60,,,41,3,,,41,26,,,41,1*49

$GBGSV,6,3,23,21,,,41,59,,,40,39,,,40,16,,,39,1*7F

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,4,,,34,1*7F

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101640.502,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101640.502,0.000,791.653,791.653,723.985,2097152,2097152,2097152*66



2025-07-31 18:16:36:767 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:16:36:953 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:16:36:957 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:16:36:960 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:16:37:072 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:16:37:222 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 18:16:37:227 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:16:37:232 ==>> 检测【左刹电压测试1】
2025-07-31 18:16:37:254 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:37:575 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3126  volt:5494 mv
[D][05:18:17][COMM]adc read out 24v adc:4  volt:101 mv
[D][05:18:17][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:17][COMM]adc read right brake adc:1725  volt:2274 mv
[D][05:18:17][COMM]adc read throttle adc:1713  volt:2258 mv
[D][05:18:17][COMM]adc read battery ts volt:13 mv
[D][05:18:17][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:17][COMM]adc read throttle brake in adc:6  volt:10 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:16:37:680 ==>> $GBGGA,101641.502,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39

2025-07-31 18:16:37:725 ==>> ,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,4,,,33,1*78

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101641.502,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101641.502,0.000,789.856,789.856,722.341,2097152,2097152,2097152*64



2025-07-31 18:16:37:758 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 18:16:37:762 ==>> 检测【右刹电压测试1】
2025-07-31 18:16:37:780 ==>> 【右刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 18:16:37:784 ==>> 检测【转把电压测试1】
2025-07-31 18:16:37:799 ==>> 【转把电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 18:16:37:805 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:16:37:831 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:16:37:876 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:16:38:074 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:16:38:079 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:16:38:084 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:16:38:161 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:16:38:349 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:16:38:353 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:16:38:361 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:16:38:463 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 18:16:38:632 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:16:38:636 ==>> 检测【左刹电压测试2】
2025-07-31 18:16:38:640 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:16:38:734 ==>> $GBGGA,101642.502,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,33,,,38,9,,,37,1*44

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,33,1*44

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101642.502,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101642.502,0.000,788.957,788.957,721.519,2097152,2097152,2097152*6F



2025-07-31 18:16:38:974 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3121  volt:5486 mv
[D][05:18:19][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:19][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:19][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:19][COMM]adc read battery ts volt:14 mv
[D][05:18:19][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:19][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:7  volt:5 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 18:16:39:158 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 18:16:39:162 ==>> 检测【右刹电压测试2】
2025-07-31 18:16:39:178 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 18:16:39:183 ==>> 检测【转把电压测试2】
2025-07-31 18:16:39:201 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 18:16:39:205 ==>> 检测【晶振检测】
2025-07-31 18:16:39:209 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:16:39:230 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 18:16:39:335 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:19][COMM][lf state:1][hf state:1]


2025-07-31 18:16:39:474 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:16:39:478 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:16:39:483 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:16:39:577 ==>> 1A A1 00 00 FC 
Get AD_V2 1633mV
Get AD_V3 1656mV
Get AD_V4 1646mV
Get AD_V5 2767mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 18:16:39:683 ==>> $GBGGA,101643.502,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23

2025-07-31 18:16:39:728 ==>> ,1,,,38,6,,,38,33,,,38,9,,,37,1*44

$GBGSV,6,5,23,14,,,37,2,,,36,5,,,34,4,,,34,1*43

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101643.502,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101643.502,0.000,789.853,789.853,722.339,2097152,2097152,2097152*69



2025-07-31 18:16:39:821 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:16:39:825 ==>> 检测【检测BootVer】
2025-07-31 18:16:39:829 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:16:40:231 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:20][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========
[D][05:18:20][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:20][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:20][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:20][FCTY]DeviceID    = 460130071536203
[D][05:18:20][FCTY]HardwareID  = 867222087476927
[D][05:18:20][FCTY]MoBikeID    = 9999999999
[D][05:18:20][FCTY]LockID      = FFFFFFFFFF
[D][05:18:20][FCTY]BLEFWVersion= 105
[D][05:18:20][FCTY]BLEMacAddr   = D8E9DCEC02B4
[D][05:18:20][FCTY]Bat         = 3944 mv
[D][05:18:20][FCTY]Current     = 0 ma
[D][05:18:20][FCTY]VBUS        = 11800 mv
[D][05:18:20][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:20][FCTY]Ext battery vol = 32, adc = 1289
[D][05:18:20][FCTY]Acckey1 vol = 5491 mv, Acckey2 vol = 0 mv
[D][05:18:20][FCTY]Bike Type flag is invalied
[D][05:18:20][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:20][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:20][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:20][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:20][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:20

2025-07-31 18:16:40:261 ==>> ][FCTY]Bat1         = 3828 mv
[D][05:18:20][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:20][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:16:40:360 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:16:40:364 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:16:40:370 ==>> 检测【检测固件版本】
2025-07-31 18:16:40:379 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:16:40:383 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:16:40:386 ==>> 检测【检测蓝牙版本】
2025-07-31 18:16:40:398 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:16:40:404 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:16:40:414 ==>> 检测【检测MoBikeId】
2025-07-31 18:16:40:418 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:16:40:421 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:16:40:425 ==>> 检测【检测蓝牙地址】
2025-07-31 18:16:40:458 ==>> 取到目标值:D8E9DCEC02B4
2025-07-31 18:16:40:461 ==>> 【检测蓝牙地址】通过,【D8E9DCEC02B4】符合目标值【】要求!
2025-07-31 18:16:40:465 ==>> 提取到蓝牙地址:D8E9DCEC02B4
2025-07-31 18:16:40:477 ==>> 检测【BOARD_ID】
2025-07-31 18:16:40:481 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:16:40:484 ==>> 检测【检测充电电压】
2025-07-31 18:16:40:496 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:16:40:507 ==>> 检测【检测VBUS电压1】
2025-07-31 18:16:40:517 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:16:40:523 ==>> 检测【检测充电电流】
2025-07-31 18:16:40:550 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:16:40:553 ==>> 检测【检测IMEI】
2025-07-31 18:16:40:559 ==>> 取到目标值:867222087476927
2025-07-31 18:16:40:570 ==>> 【检测IMEI】通过,【867222087476927】符合目标值【】要求!
2025-07-31 18:16:40:574 ==>> 提取到IMEI:867222087476927
2025-07-31 18:16:40:577 ==>> 检测【检测IMSI】
2025-07-31 18:16:40:581 ==>> 取到目标值:460130071536203
2025-07-31 18:16:40:616 ==>> 【检测IMSI】通过,【460130071536203】符合目标值【】要求!
2025-07-31 18:16:40:620 ==>> 提取到IMSI:460130071536203
2025-07-31 18:16:40:623 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:16:40:630 ==>> 取到目标值:460130071536203
2025-07-31 18:16:40:658 ==>> 【校验网络运营商(移动)】通过,【460130071536203】符合目标值【】要求!
2025-07-31 18:16:40:662 ==>> 检测【打开CAN通信】
2025-07-31 18:16:40:669 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:16:40:733 ==>> $GBGGA,101644.502,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,6,,,38,14,,,38,33,,,38,1*77

$GBGSV,6,5,23,9,,,37,2,,,36,5,,,34,4,,,34,1*7F

$GBGSV,6,6,23,45,,,32,7,,,32,40,,,32,1*44

$GBRMC,101644.502,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101644.502,0.000,790.752,790.752,723.161,2097152,2097152,2097152*60



2025-07-31 18:16:40:763 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:16:40:947 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:16:40:951 ==>> 检测【检测CAN通信】
2025-07-31 18:16:40:955 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:16:41:087 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:16:41:162 ==>> [D][05:18:21][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 32436
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:16:41:227 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:16:41:231 ==>> 检测【关闭CAN通信】
2025-07-31 18:16:41:234 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:16:41:252 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:21][COMM]read battery soc:255


2025-07-31 18:16:41:282 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:16:41:372 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:16:41:510 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:16:41:515 ==>> 检测【打印IMU STATE】
2025-07-31 18:16:41:519 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:16:41:764 ==>> $GBGGA,101645.502,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,42,,,42,13,,,41,38,,,41,1*78

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,8,,,40,1*7B

$GBGSV,6,3,23,59,,,40,39,,,40,21,,,40,16,,,39,1*7E

$GBGSV,6,4,23,1,,,38,14,,,38,33,,,38,9,,,37,1*77

$GBGSV,6,5,23,6,,,37,2,,,36,5,,,34,4,,,34,1*70

$GBGSV,6,6,23,45,,,33,7,,,32,40,,,32,1*45

$GBRMC,101645.502,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101645.502,0.000,789.848,789.848,722.334,2097152,2097152,2097152*62

[W][05:18:21][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:21][COMM]YAW data: 32763[32763]
[D][05:18:21][COMM]pitch:-66 roll:1
[D][05:18:21][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:16:42:042 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:16:42:046 ==>> 检测【六轴自检】
2025-07-31 18:16:42:049 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:16:42:252 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:22][CAT1]gsm read msg sub id: 12
[D][05:18:22][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:16:42:729 ==>> $GBGGA,101646.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,24,,,43,13,,,42,42,,,42,38,,,41,1*7B

$GBGSV,6,2,23,60,,,41,3,,,41,26,,,41,21,,,41,1*41

$GBGSV,6,3,23,8,,,40,59,,,40,39,,,40,16,,,39,1*45

$GBGSV,6,4,23,1,,,38,14,,,38,33,,,38,9,,,37,1*77

$GBGSV,6,5,23,6,,,37,2,,,36,5,,,34,4,,,34,1*70

$GBGSV,6,6,23,45,,,33,40,,,32,7,,,31,1*46

$GBRMC,101646.502,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101646.502,0.000,790.756,790.756,723.164,2097152,2097152,2097152*67



2025-07-31 18:16:43:249 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:16:43:725 ==>> $GBGGA,101647.502,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,24,,,43,42,,,42,13,,,41,38,,,41,1*7F

$GBGSV,6,2,24,60,,,41,3,,,41,26,,,41,21,,,41,1*46

$GBGSV,6,3,24,8,,,40,59,,,40,39,,,40,16,,,39,1*42

$GBGSV,6,4,24,1,,,38,14,,,38,33,,,38,9,,,37,1*70

$GBGSV,6,5,24,6,,,37,2,,,36,5,,,34,4,,,34,1*77

$GBGSV,6,6,24,45,,,33,7,,,32,40,,,32,25,,,,1*45

$GBRMC,101647.502,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101647.502,0.000,790.749,790.749,723.158,2097152,2097152,2097152*69



2025-07-31 18:16:43:954 ==>> [D][05:18:24][CAT1]<<< 
OK

[D][05:18:24][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:16:44:105 ==>> [D][05:18:24][COMM]Main Task receive event:142
[D][05:18:24][COMM]###### 35375 imu self test OK ######
[D][05:18:24][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-15,-15,4046]
[D][05:18:24][COMM]Main Task receive event:142 finished processing


2025-07-31 18:16:44:150 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:16:44:155 ==>> 检测【打印IMU STATE2】
2025-07-31 18:16:44:161 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:16:44:366 ==>> [D][05:18:24][HSDK][0] flush to flash addr:[0xE42B00] --- write len --- [256]
[W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:1
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:16:44:440 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:16:44:447 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:16:44:461 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:16:44:562 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:16:44:737 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:16:44:745 ==>> 检测【检测VBUS电压2】
2025-07-31 18:16:44:762 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:16:45:692 ==>> [D][05:18:24][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:24][FCTY]get_ext_48v_vol retry i = 8,volt = 11
$GBGGA,101644.508,2301.2560511,N,11421.9413105,E,1,13,1.26,69.056,M,-1.770,M,,*5A

$GBGSA,A,3,13,08,42,24,16,06,38,39,26,14,21,33,1.74,1.26,1.19,4*0E

$GBGSA,A,3,40,,,,,,,,,,,,1.74,1.26,1.19,4*02

$GBGSV,6,1,24,13,79,247,41,8,77,189,40,42,69,8,42,24,64,236,43,1*40

$GBGSV,6,2,24,16,63,313,39,6,63,307,37,3,62,191,41,38,61,171,41,1*7B

$GBGSV,6,3,24,39,61,341,40,26,60,29,41,59,52,130,40,1,48,126,38,1*78

$GBGSV,6,4,24,2,46,239,36,14,45,334,38,21,44,114,41,9,42,285,37,1*72

$GBGSV,6,5,24,60,41,238,41,4,32,112,34,45,28,200,33,5,22,258,34,1*78

$GBGSV,6,6,24,33,20,323,38,7,16,181,32,40,9,165,32,25,9,226,,1*44

$GBRMC,101644.508,A,2301.2560511,N,11421.9413105,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,

2025-07-31 18:16:45:797 ==>> N,0.001,K,A*2F

[D][05:18:25][GNSS]HD8040 GPS
[D][05:18:25][GNSS]GPS diff_sec 124001899, report 0x42 frame
$GBGST,101644.508,0.766,0.299,0.267,0.344,2.938,3.730,9.132*75

[D][05:18:25][COMM]Main Task receive event:131
[D][05:18:25][COMM]index:0,power_mode:0xFF
[D][05:18:25][COMM]index:1,sound_mode:0xFF
[D][05:18:25][COMM]index:2,gsensor_mode:0xFF
[D][05:18:25][COMM]index:3,report_freq_mode:0xFF
[D][05:18:25][COMM]index:4,report_period:0xFF
[D][05:18:25][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:25][COMM]index:6,normal_reset_period:0xFF
[D][05:18:25][COMM]index:7,spock_over_speed:0xFF
[D][05:18:25][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:25][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:25][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:25][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:25][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:25][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:25][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:25][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:25][COMM]index:16,imu_config_params:0xFF
[D][05:18:25][COMM]index:17,long_connect_params:0xFF
[D][05:18:25][COMM]index:18,detain_mark:0xFF
[D][05:18:25]

2025-07-31 18:16:45:902 ==>> [COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:25][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:25][COMM]index:21,mc_mode:0xFF
[D][05:18:25][COMM]index:22,S_mode:0xFF
[D][05:18:25][COMM]index:23,overweight:0xFF
[D][05:18:25][COMM]index:24,standstill_mode:0xFF
[D][05:18:25][COMM]index:25,night_mode:0xFF
[D][05:18:25][COMM]index:26,experiment1:0xFF
[D][05:18:25][COMM]index:27,experiment2:0xFF
[D][05:18:25][COMM]index:28,experiment3:0xFF
[D][05:18:25][COMM]index:29,experiment4:0xFF
[D][05:18:25][COMM]index:30,night_mode_start:0xFF
[D][05:18:25][COMM]index:31,night_mode_end:0xFF
[D][05:18:25][COMM]index:33,park_report_minutes:0xFF
[D][05:18:25][COMM]index:34,park_report_mode:0xFF
[D][05:18:25][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:25][COMM]index:38,charge_battery_para: FF
[D][05:18:25][COMM]index:39,multirider_mode:0xFF
[D][05:18:25][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:25][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:25][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:25][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:25][COMM]index:44,riding_duration_config:0xFF
[D][05:18:25][COMM]index:45,camera_park_angle_cf

2025-07-31 18:16:46:007 ==>> g:0xFF
[D][05:18:25][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:25][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:25][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:25][COMM]index:49,mc_load_startup:0xFF
[D][05:18:25][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:25][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:25][COMM]index:52,traffic_mode:0xFF
[D][05:18:25][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:25][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:25][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:25][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:25][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:25][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:25][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:25][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:25][COMM]index:63,experiment5:0xFF
[D][05:18:25][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:25][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:25][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:25][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:25][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:25][COMM]index:70,camera_park_

2025-07-31 18:16:46:112 ==>> light_cfg:0xFF
[D][05:18:25][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:25][COMM]index:72,experiment6:0xFF
[D][05:18:25][COMM]index:73,experiment7:0xFF
[D][05:18:25][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:25][COMM]index:75,zero_value_from_server:-1
[D][05:18:25][COMM]index:76,multirider_threshold:255
[D][05:18:25][COMM]index:77,experiment8:255
[D][05:18:25][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:25][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:25][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:25][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:25][COMM]index:83,loc_report_interval:255
[D][05:18:25][COMM]index:84,multirider_threshold_p2:255
[D][05:18:25][COMM]index:85,multirider_strategy:255
[D][05:18:25][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:25][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:25][COMM]index:90,weight_param:0xFF
[D][05:18:25][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:25][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:25][COMM]index:95,current_limit:0xFF
[D][05:18:25][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:

2025-07-31 18:16:46:217 ==>> 0xFF 
[D][05:18:25][COMM]index:100,location_mode:0xFF

[W][05:18:25][PROT]remove success[1629955105],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:25][PROT]add success [1629955105],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:25][COMM]Main Task receive event:131 finished processing
[D][05:18:25][PROT]index:0 1629955105
[D][05:18:25][PROT]is_send:0
[D][05:18:25][PROT]sequence_num:4
[D][05:18:25][PROT]retry_timeout:0
[D][05:18:25][PROT]retry_times:1
[D][05:18:25][PROT]send_path:0x2
[D][05:18:25][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:25][PROT]===========================================================
[W][05:18:25][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955105]
[D][05:18:25][PROT]===========================================================
[D][05:18:25][PROT]sending traceid [9999999999900005]
[D][05:18:25][PROT]Send_TO_M2M [1629955105]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:25][SAL ]sock send credit cnt[6]
[D][05:18:25][SAL ]sock send ind credit cnt[6]
[D][05:18:25][M2M ]m2m send data len[294]
[D][05:18:25][SAL ]Cellular task submsg id[10]
[D][05:18:25][SAL ]cellular SEND socket id[0] type[1

2025-07-31 18:16:46:322 ==>> ], len[294], data[0x20052de0] format[0]
[D][05:18:25][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:25][CAT1]gsm read msg sub id: 15
[D][05:18:25][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:25][CAT1]Send Data To Server[294][297] ... ->:
0093B98A113311331133113311331B88B5A8D8F9B9134EF23AFC444D7F697EFB1439EB9C56FA148F45D6CB8511C3E71DC14E104103B1C7CDA6AD7EB7D2293E3C2DC3BC3B5DCD7A379F4D34770151E725C0A6BC05720102B0E7154F99DF9E809DA243275A833A0CB2E55B566FFD4E7CDBD22464F5870859447ED4A1D112FD9C7F8F7E409AC3A74A2B460534EA743AFCA89C3F30
[D][05:18:25][CAT1]<<< 
SEND OK

[D][05:18:25][CAT1]exec over: func id: 15, ret: 11
[D][05:18:25][CAT1]sub id: 15, ret: 11

[D][05:18:25][SAL ]Cellular task submsg id[68]
[D][05:18:25][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:25][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:25][M2M ]g_m2m_is_idle become true
[D] 

2025-07-31 18:16:46:427 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 18:16:46:776 ==>> [D][05:18:26][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:26][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:26][COMM]Main Task receive event:65
[D][05:18:26][COMM]main task tmp_sleep_event = 80
[D][05:18:26][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:26][COMM]Main Task receive event:65 finished processing
[D][05:18:26][COMM]Main Task receive event:60
[D][05:18:26][COMM]smart_helmet_vol=255,255
[D][05:18:26][COMM]BAT CAN get state1 Fail 204
[D][05:18:26][COMM]BAT CAN get soc Fail, 204
[W][05:18:26][GNSS]stop locating
[D][05:18:26][GNSS]stop event:8
[D][05:18:26][GNSS]GPS stop. ret=0
[D][05:18:26][GNSS]all continue location stop
[D][05:18:26][COMM]report elecbike
[W][05:18:26][PROT]remove success[1629955106],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:26][PROT]add success [1629955106],send_path[3],type[5D03],priority[3],index[1],used[1]
[D][05:18:26][COMM]Main Task receive event:60 finished processing
[D][05:18:26][CAT1]gsm read msg sub id: 24
[D][05:18:26][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:26][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:26][PROT]index:1
[D][05:18:26][PROT]is_send:1
[D][05:18:26][PROT]sequence_num:5
[D][05:18:26][PROT]retry_timeout:0
[D][05:18:26][PROT]retr

2025-07-31 18:16:46:881 ==>> y_times:3
[D][05:18:26][PROT]send_path:0x3
[D][05:18:26][PROT]msg_type:0x5d03
[D][05:18:26][PROT]===========================================================
[W][05:18:26][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955106]
[D][05:18:26][PROT]===========================================================
[D][05:18:26][PROT]Sending traceid[9999999999900006]
[D][05:18:26][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:26][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:26][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 24, ret: 6
[D][05:18:26][CAT1]sub id: 24, ret: 6



2025-07-31 18:16:47:318 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:16:47:683 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071536203
[D][05:18:27][FCTY]HardwareID  = 867222087476927
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D8E9DCEC02B4
[D][05:18:27][FCTY]Bat         = 3824 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 2, adc = 103
[D][05:18:27][FCTY]Acckey1 vol = 5493 mv, Acckey2 vol = 75 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18

2025-07-31 18:16:47:742 ==>> :27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3828 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][GNSS]recv submsg id[1]
[D][05:18:27][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:27][GNSS]location stop evt done evt


2025-07-31 18:16:47:851 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:16:47:857 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:16:47:880 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:16:47:968 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:16:48:074 ==>> [D][05:18:28][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30
[D][05:18:28][COMM]read battery soc:255


2025-07-31 18:16:48:152 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:16:48:157 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:16:48:162 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:16:48:270 ==>> 5A A5 04 5A A5 


2025-07-31 18:16:48:360 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:16:48:440 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:16:48:448 ==>> 检测【打开WIFI(3)】
2025-07-31 18:16:48:469 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:16:48:691 ==>> [D][05:18:28][HSDK][0] flush to flash addr:[0xE42C00] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:16:48:779 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:16:48:786 ==>> 检测【扩展芯片hw】
2025-07-31 18:16:48:814 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:16:48:964 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:29][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:16:49:108 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:16:49:113 ==>> 检测【扩展芯片boot】
2025-07-31 18:16:49:172 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:16:49:177 ==>> 检测【扩展芯片sw】
2025-07-31 18:16:49:223 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:16:49:232 ==>> 检测【检测音频FLASH】
2025-07-31 18:16:49:253 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:16:49:447 ==>> +WIFISCAN:4,0,44A1917CA62B,-75
+WIFISCAN:4,1,CC057790A741,-77
+WIFISCAN:4,2,CC057790A740,-77
+WIFISCAN:4,3,646E97BD0450,-86

[D][05:18:29][CAT1]wifi scan report total[4]
[D][05:18:29][COMM]IMU: [1,16,-976] ret=22 AWAKE!
[W][05:18:29][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 18:16:49:630 ==>> [D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------
[D][05:18:29][GNSS]recv submsg id[3]


2025-07-31 18:16:50:517 ==>>                                                                       18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:29][COMM]read file, len:10800, num:3
[D][05

2025-07-31 18:16:50:621 ==>> :18:30][COMM]--->crc16:0xb8a
[D][05:18:30][COMM]read file success
[W][05:18:30][COMM][Audio].l:[936].close hexlog save
[D][05:18:30][COMM]accel parse set 1
[D][05:18:30][COMM][Audio]mon:9,05:18:30
[D][05:18:30][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:66
[D][05:18:30][COMM]Try to Auto Lock Bat
[D][05:18:30][COMM]Main Task receive event:66 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get soc error
[E][05:18:30][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove su

2025-07-31 18:16:50:727 ==>> ccess[1629955110],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:30][COMM]Main Task receive event:61
[D][05:18:30][COMM][D301]:type:3, trace id:280
[D][05:18:30][COMM]Receive Bat Lock cmd 0
[D][05:18:30][COMM]VBUS is 1
[D][05:18:30][COMM]id[], hw[000
[D][05:18:30][COMM]get mcMaincircuitVolt error
[D][05:18:30][COMM]get mcSubcircuitVolt error
[D][05:18:30][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]get bat work state err
[W][05:18:30][PROT]remove success[1629955110],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:18:30][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][PROT]in

2025-07-31 18:16:50:832 ==>> dex:2
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:6
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900007]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:30][COMM]Main Task receive event:61 finished processing
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:30][COMM

2025-07-31 18:16:50:937 ==>> ]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l

2025-07-31 18:16:51:043 ==>> :[991]. send ret: 0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:30][PROT]CLEAN,SEND:0
[D][05:18:30][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:30][PROT]index:2 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:6
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900007]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][PROT]CLEAN:0
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][COMM]f:[ec800m_audio_response_process].audio_a

2025-07-31 18:16:51:133 ==>> ck.data_size:6
0D0A4F4B0D0A
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:30][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88BE7493E9D133C1B77AE74083BEEDF9CA3AFF07479EB66FE08C403C6F53071D8000500E395E2F461DA3AD64180BD725F065A810F7D71E23DE32CF6E71E8E6C80091A5BB5B94D9D4A1CE5F0FB20AC0FC8A667B43
[D][05:18:30][COMM]read battery soc:255
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][

2025-07-31 18:16:52:091 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 18:16:52:735 ==>> [D][05:18:33][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:53:548 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 18:16:53:835 ==>> [D][05:18:34][COMM]45020 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:34][COMM]accel parse set 0
[D][05:18:34][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:16:54:110 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 18:16:54:280 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:16:54:290 ==>> 检测【打开喇叭声音】
2025-07-31 18:16:54:314 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:16:54:965 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].

2025-07-31 18:16:55:070 ==>> l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D]

2025-07-31 18:16:55:076 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:16:55:090 ==>> 检测【打开大灯控制】
2025-07-31 18:16:55:099 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:16:55:176 ==>> [05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:35][COMM]46031 imu init OK
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:35][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:35][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:16:55:507 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<
[D][05:18:35][PROT]CLEAN,SEND:2
[D][05:18:35][PROT]index:2 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:6
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:2
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900007]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:35][CAT1]Send Data To Server[198

2025-07-31 18:16:55:582 ==>> ][201] ... ->:
0063B98C113311331133113311331B88BE1F520F1D91C2AC4EE0F18933D9D04351ECC8286D627A137740E380EFE40E689634F404A8E6561FE3EEFF065C947C71DD98447602C03A2160BA66E0A6C6F433F95F667972F0B94071C7B5743B91716DD02110
[D][05:18:35][CAT1]<<< 
SEND OK

[D][05:18:35][CAT1]exec over: func id: 15, ret: 11
[D][05:18:35][CAT1]sub id: 15, ret: 11

[D][05:18:35][SAL ]Cellular task submsg id[68]
[D][05:18:35][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:35][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:35][M2M ]g_m2m_is_idle become true
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:35][PROT]M2M Send ok [1629955115]


2025-07-31 18:16:55:616 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:16:55:621 ==>> 检测【关闭仪表供电3】
2025-07-31 18:16:55:658 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:16:55:749 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:36][COMM]set POWER 0


2025-07-31 18:16:55:914 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:16:55:922 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:16:55:929 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:16:56:025 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:16:56:115 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 18:16:56:198 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:16:56:205 ==>> 检测【读大灯电压】
2025-07-31 18:16:56:211 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:16:56:360 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[33270]


2025-07-31 18:16:56:517 ==>> 【读大灯电压】通过,【33270mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:16:56:524 ==>> 检测【关闭大灯控制2】
2025-07-31 18:16:56:533 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:16:56:636 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:16:56:791 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:16:56:798 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:16:56:822 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:16:56:958 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[69]


2025-07-31 18:16:57:072 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:16:57:078 ==>> 检测【打开WIFI(4)】
2025-07-31 18:16:57:100 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:16:57:280 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:37][CAT1]<<< 
OK

[D][05:18:37][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:16:57:403 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:16:57:409 ==>> 检测【EC800M模组版本】
2025-07-31 18:16:57:420 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:16:57:570 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:37][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:37][CAT1]gsm read msg sub id: 12
[D][05:18:37][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 18:16:57:800 ==>> [D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:16:57:961 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:16:57:971 ==>> 检测【配置蓝牙地址】
2025-07-31 18:16:57:987 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:16:57:997 ==>> +WIFISCAN:4,0,CC057790A7C0,-77
+WIFISCAN:4,1,CC057790A741,-78
+WIFISCAN:4,2,CC057790A5C1,-82
+WIFISCAN:4,3,646E97BD0450,-87

[D][05:18:38][CAT1]wifi scan report total[4]


2025-07-31 18:16:58:147 ==>> [D][05:18:38][COMM]read battery soc:255
[W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:16:58:162 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D8E9DCEC02B4>】
2025-07-31 18:16:58:377 ==>> recv ble 1
recv ble 2
ble set mac ok :d8,e9,dc,ec,2,b4
enable filters ret : 0

2025-07-31 18:16:58:469 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:16:58:474 ==>> 检测【BLETEST】
2025-07-31 18:16:58:500 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:16:58:512 ==>> [D][05:18:38][COMM]49803 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:16:58:572 ==>> 4A A4 01 A4 4A 


2025-07-31 18:16:58:647 ==>> [D][05:18:38][GNSS]recv submsg id[3]


2025-07-31 18:16:58:752 ==>> recv ble 1
recv ble 2
<BSJ*MAC:D8E9DCEC02B4*RSSI:-23*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F626

2025-07-31 18:16:58:782 ==>> 96B6513FFB30402E9D8E9DCEC02B499999

2025-07-31 18:16:58:873 ==>> OVER 150


2025-07-31 18:16:59:510 ==>> 【BLETEST】通过,【-23dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:16:59:519 ==>> 该项需要延时执行
2025-07-31 18:16:59:543 ==>> [D][05:18:39][COMM]50814 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:17:00:177 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save
[D][05:18:40][COMM]read battery soc:255


2025-07-31 18:17:00:746 ==>> [D][05:18:40][PROT]CLEAN,SEND:2
[D][05:18:40][PROT]index:2 1629955120
[D][05:18:40][PROT]is_send:0
[D][05:18:40][PROT]sequence_num:6
[D][05:18:40][PROT]retry_timeout:0
[D][05:18:40][PROT]retry_times:1
[D][05:18:40][PROT]send_path:0x2
[D][05:18:40][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:40][PROT]===========================================================
[W][05:18:40][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955120]
[D][05:18:40][PROT]===========================================================
[D][05:18:40][PROT]sending traceid [9999999999900007]
[D][05:18:40][PROT]Send_TO_M2M [1629955120]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:40][SAL ]sock send credit cnt[6]
[D][05:18:40][SAL ]sock send ind credit cnt[6]
[D][05:18:40][M2M ]m2m send data len[198]
[D][05:18:40][SAL ]Cellular task submsg id[10]
[D][05:18:40][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:40][CAT1]gsm read msg sub id: 15
[D][05:18:40][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:40][COMM]51825 imu init OK
[D][05:18:40][CAT1]Send Data To Ser

2025-07-31 18:17:00:820 ==>> ver[198][201] ... ->:
0063B981113311331133113311331B88BE509F9E6D573CF63A5BD9F847B79FABA52023A5AD0192E9255A46DE368BFF642B39D2F21AA156EC8577F2ED680C780CAFA33B470E0F3650F92BA30CE2071F5535972FED22DD19A602B3FC54D31EA8655160EF
[D][05:18:40][CAT1]<<< 
SEND OK

[D][05:18:40][CAT1]exec over: func id: 15, ret: 11
[D][05:18:40][CAT1]sub id: 15, ret: 11

[D][05:18:40][SAL ]Cellular task submsg id[68]
[D][05:18:40][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:40][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:40][M2M ]g_m2m_is_idle become true
[D][05:18:40][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:40][PROT]M2M Send ok [1629955120]


2025-07-31 18:17:02:005 ==>> [D][05:18:42][COMM]S->M yaw:INVALID


2025-07-31 18:17:02:110 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 18:17:03:053 ==>> [D][05:18:43][COMM]M->S yaw:INVALID


2025-07-31 18:17:04:125 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 18:17:05:963 ==>> [D][05:18:46][PROT]CLEAN,SEND:2
[D][05:18:46][PROT]CLEAN:2
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:3
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:3
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send

2025-07-31 18:17:06:038 ==>>  Data To Server[198][201] ... ->:
0063B982113311331133113311331B88B36E54C09ED03AD59CE504BECDB7407D306546FE53E22FB1D6DF1AAD0EB0707CD1CB2706B9766D12924BE0DB29A3A8CC477BFC15ECF5F468320838C979AA90CE0EEA6E591AF32DD096F078CFE2100E66820349
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 18:17:06:143 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 18:17:08:144 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 18:17:09:521 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:17:09:527 ==>> 检测【检测WiFi结果】
2025-07-31 18:17:09:542 ==>> WiFi信号:【CC057790A740】,信号值:-69
2025-07-31 18:17:09:553 ==>> WiFi信号:【CC057790A741】,信号值:-74
2025-07-31 18:17:09:571 ==>> WiFi信号:【CC057790A7C0】,信号值:-80
2025-07-31 18:17:09:577 ==>> WiFi信号:【CC057790A5C1】,信号值:-81
2025-07-31 18:17:09:612 ==>> WiFi信号:【44A1917CA62B】,信号值:-75
2025-07-31 18:17:09:621 ==>> WiFi信号:【646E97BD0450】,信号值:-86
2025-07-31 18:17:09:645 ==>> WiFi数量【6】, 最大信号值:-69
2025-07-31 18:17:09:653 ==>> 检测【检测GPS结果】
2025-07-31 18:17:09:676 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:17:09:761 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all continue location stop
[W][05:18:50][GNSS]stop locating
[D][05:18:50][GNSS]all sing location stop


2025-07-31 18:17:10:145 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 18:17:10:530 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:17:10:539 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:17:10:564 ==>> 定位已等待【1】秒.
2025-07-31 18:17:10:976 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:51][COMM]Open GPS Module...
[D][05:18:51][COMM]LOC_MODEL_CONT
[D][05:18:51][GNSS]start event:8
[D][05:18:51][GNSS]GPS start. ret=0
[W][05:18:51][GNSS]start cont locating
[D][05:18:51][CAT1]gsm read msg sub id: 23
[D][05:18:51][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:51][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:51][CAT1]<<< 
OK

[D][05:18:51][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:17:11:081 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 18:17:11:156 ==>>                                                                                                                                                                                                                                                                      ][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 18:17:11:539 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:17:11:549 ==>> 定位已等待【2】秒.
2025-07-31 18:17:11:738 ==>> $GBGGA,101715.070,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,13,79,247,,8,77,189,,42,69,9,,24,64,236,,1*41

$GBGSV,3,2,12,16,63,313,,6,63,307,,39,61,341,,38,61,171,,1*40

$GBGSV,3,3,12,26,60,29,,14,45,334,,21,44,115,,33,21,323,,1*4B

$GBRMC,101715.070,V,,,,,,,310725,0.1,E,N,V*49

$GBGST,101715.070,0.000,0.005,0.005,0.005,202,340,190*65



2025-07-31 18:17:12:155 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 18:17:12:352 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:17:12:550 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:17:12:559 ==>> 定位已等待【3】秒.
2025-07-31 18:17:12:688 ==>> [D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:52][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

$GBGGA,101712.296,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,30,13,79,247,40,8,77,189,40,36,70,133,,42,69,9,,1*4A

$GBGSV,8,2,30,24,64,236,43,16,63,313,39,6,63,307,44,3,62,191,41,1*7E

$GBGSV,8,3,30,38,61,171,42,39,61,341,40,26,60,29,41,59,52,130,41,1*4C

$GBGSV,8,4,30,19,50,59,,1,48,126,37,2,46,239,,14,45,334,38,1*47

$GBGSV,8,5,30,21,44,115,41,22,42,335,,9,41,285,33,60,41,238,38,1*4A

$GBGSV,8,6,30,46,35,48,,4,32,112,,45,28,200,,5,22,258,,1*47

$GBGSV,8,7,30,33,21,323,38,7,16,181,,35,15,222,,20,13,101,,1*48

$GBGSV,8,8,30,44,12,273,,10,10,191,,1*79

$GBRMC,101712.296,V,,,,,,,310725,1.9,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:52][CAT1]tx ret[14] >>> AT+GPSMODE=1

$GBGST,101712.296,1.280,0.186,0.182,0.266,3.667,4.202,11*5F

[D][05:18:52][CAT1]<<< 
OK

[D][05:18:52][CAT1]exec over: func id: 23, ret: 6
[D][05:18:52][CAT1]sub id: 23, ret: 6

[D][05:18:52][CAT1]gsm read msg sub id: 15
[D

2025-07-31 18:17:12:793 ==>> ][05:18:52][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:52][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B356CD828CD90E7AFFA1330923DC1CD125BDBCBC6567C8A9FADB78DBB2E14610157146C63AFDA6B6A3E2CC3987DE4CC877734DB30EED45D3C11BC53F2BE202455E492F43EED107FCEB112F22A95306196C695A
[D][05:18:52][CAT1]<<< 
SEND OK

[D][05:18:52][CAT1]exec over: func id: 15, ret: 11
[D][05:18:52][CAT1]sub id: 15, ret: 11

[D][05:18:52][SAL ]Cellular task submsg id[68]
[D][05:18:52][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:52][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:52][M2M ]g_m2m_is_idle become true
[D][05:18:52][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:52][PROT]M2M Send ok [1629955132]
                                                                                            

2025-07-31 18:17:13:388 ==>> $GBGGA,101713.096,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,79,247,40,8,77,189,40,42,69,9,,24,64,236,42,1*40

$GBGSV,5,2,20,16,63,313,39,6,63,307,42,3,62,191,41,38,61,171,41,1*7E

$GBGSV,5,3,20,39,61,341,39,26,60,29,41,59,52,130,39,1,48,126,38,1*7F

$GBGSV,5,4,20,2,46,239,36,14,45,334,38,21,44,115,41,9,41,285,35,1*75

$GBGSV,5,5,20,60,41,238,40,4,32,112,34,5,22,258,37,33,21,323,38,1*7E

$GBGSV,2,1,06,42,69,9,41,24,64,236,38,38,61,171,40,39,61,341,39,5*74

$GBGSV,2,2,06,26,60,29,40,21,44,115,39,5*45

$GBRMC,101713.096,V,,,,,,,310725,1.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,101713.096,1.741,0.509,0.484,0.674,2.198,2.572,6.951*76



2025-07-31 18:17:13:555 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:17:13:564 ==>> 定位已等待【4】秒.
2025-07-31 18:17:14:172 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 18:17:14:559 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:17:14:569 ==>> 定位已等待【5】秒.
2025-07-31 18:17:14:878 ==>> $GBGGA,101714.076,2301.2570610,N,11421.9412352,E,1,12,1.42,74.046,M,-1.770,M,,*5E

$GBGSA,A,3,13,08,42,24,16,06,38,39,26,14,21,33,2.43,1.42,1.98,4*02

$GBGSV,5,1,20,13,79,247,40,8,77,189,40,42,69,9,,24,64,236,42,1*40

$GBGSV,5,2,20,16,63,313,39,6,63,307,35,3,62,191,41,38,61,171,40,1*7F

$GBGSV,5,3,20,39,61,341,39,26,60,29,41,59,52,130,40,1,48,126,38,1*71

$GBGSV,5,4,20,2,46,239,36,14,45,334,38,21,44,115,40,9,41,285,36,1*77

$GBGSV,5,5,20,60,41,238,40,4,32,112,34,5,22,258,34,33,21,323,38,1*7D

$GBGSV,2,1,07,42,69,9,43,24,64,236,41,38,61,171,40,39,61,341,40,5*77

$GBGSV,2,2,07,26,60,29,41,21,44,115,40,33,21,323,32,5*7B

$GBRMC,101714.076,A,2301.2570610,N,11421.9412352,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[D][05:18:54][GNSS]GPS diff_sec 124001900, report 0x42 frame
$GBGST,101714.076,1.754,0.448,0.432,0.591,1.826,2.054,5.245*79

[D][05:18:54][COMM]Main Task receive event:131
[D][05:18:54][COMM]index:0,power_mode:0xFF
[D][05:18:54][COMM]index:1,sound_mode:0xFF
[D][05:18:54][COMM]index:2,gsensor_mode:0xFF
[D][05:18:54][COMM]index:3,report_freq_mode:0xFF
[D][05:18:54][COMM]index:4,report_period:0xFF
[D][05:18:54][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:54][COMM]index:6,normal_reset_period:0xFF
[D][05:18:54][COMM]index:7,spock_over_speed:

2025-07-31 18:17:14:982 ==>> 0xFF
[D][05:18:54][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:54][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:54][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:54][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:54][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:54][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:54][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:54][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:54][COMM]index:16,imu_config_params:0xFF
[D][05:18:54][COMM]index:17,long_connect_params:0xFF
[D][05:18:54][COMM]index:18,detain_mark:0xFF
[D][05:18:54][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:54][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:54][COMM]index:21,mc_mode:0xFF
[D][05:18:54][COMM]index:22,S_mode:0xFF
[D][05:18:54][COMM]index:23,overweight:0xFF
[D][05:18:54][COMM]index:24,standstill_mode:0xFF
[D][05:18:54][COMM]index:25,night_mode:0xFF
[D][05:18:54][COMM]index:26,experiment1:0xFF
[D][05:18:54][COMM]index:27,experiment2:0xFF
[D][05:18:54][COMM]index:28,experiment3:0xFF
[D][05:18:54][COMM]index:29,experiment4:0xFF
[D][05:18:54][COMM]index:30,night_mode_start:0xFF
[D][05:18:54][COMM]index:31,night_mode_end:

2025-07-31 18:17:15:087 ==>> 0xFF
[D][05:18:54][COMM]index:33,park_report_minutes:0xFF
[D][05:18:54][COMM]index:34,park_report_mode:0xFF
[D][05:18:54][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:54][COMM]index:38,charge_battery_para: FF
[D][05:18:54][COMM]index:39,multirider_mode:0xFF
[D][05:18:54][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:54][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:54][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:54][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:54][COMM]index:44,riding_duration_config:0xFF
[D][05:18:54][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:54][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:54][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:54][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:54][COMM]index:49,mc_load_startup:0xFF
[D][05:18:54][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:54][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:54][COMM]index:52,traffic_mode:0xFF
[D][05:18:54][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:54][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:54][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:54][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18

2025-07-31 18:17:15:192 ==>> :54][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:54][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:54][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:54][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:54][COMM]index:63,experiment5:0xFF
[D][05:18:54][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:54][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:54][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:54][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:54][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:54][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:54][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:54][COMM]index:72,experiment6:0xFF
[D][05:18:54][COMM]index:73,experiment7:0xFF
[D][05:18:54][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:54][COMM]index:75,zero_value_from_server:-1
[D][05:18:54][COMM]index:76,multirider_threshold:255
[D][05:18:54][COMM]index:77,experiment8:255
[D][05:18:54][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:54][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:54][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:54][COMM]inde

2025-07-31 18:17:15:297 ==>> x:82,loc_report_low_speed_thr:255
[D][05:18:54][COMM]index:83,loc_report_interval:255
[D][05:18:54][COMM]index:84,multirider_threshold_p2:255
[D][05:18:54][COMM]index:85,multirider_strategy:255
[D][05:18:54][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:54][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:54][COMM]index:90,weight_param:0xFF
[D][05:18:54][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:54][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:54][COMM]index:95,current_limit:0xFF
[D][05:18:54][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:54][COMM]index:100,location_mode:0xFF

[W][05:18:54][PROT]remove success[1629955134],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:54][PROT]add success [1629955134],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:54][COMM]Main Task receive event:131 finished processing
[D][05:18:54][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:54][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 18:17:15:402 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:17:15:570 ==>> 符合定位需求的卫星数量:【17】
2025-07-31 18:17:15:576 ==>> 
北斗星号:【13】,信号值:【40】
北斗星号:【8】,信号值:【40】
北斗星号:【24】,信号值:【41】
北斗星号:【16】,信号值:【39】
北斗星号:【6】,信号值:【35】
北斗星号:【3】,信号值:【41】
北斗星号:【38】,信号值:【40】
北斗星号:【39】,信号值:【40】
北斗星号:【26】,信号值:【41】
北斗星号:【59】,信号值:【40】
北斗星号:【1】,信号值:【38】
北斗星号:【2】,信号值:【36】
北斗星号:【14】,信号值:【38】
北斗星号:【21】,信号值:【40】
北斗星号:【9】,信号值:【36】
北斗星号:【60】,信号值:【40】
北斗星号:【42】,信号值:【43】

2025-07-31 18:17:15:586 ==>> 检测【CSQ强度】
2025-07-31 18:17:15:606 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:17:15:784 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:56][CAT1]gsm read msg sub id: 12
[D][05:18:56][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:56][CAT1]<<< 
+CSQ: 19,99

OK

[D][05:18:56][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:17:15:870 ==>> 【CSQ强度】通过,【19】符合目标值【18】至【31】要求!
2025-07-31 18:17:15:876 ==>> 检测【关闭GSM联网】
2025-07-31 18:17:15:886 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:17:16:059 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:56][COMM]GSM test
[D][05:18:56][COMM]GSM test disable


2025-07-31 18:17:16:174 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:17:16:180 ==>> 检测【4G联网测试】
2025-07-31 18:17:16:190 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:17:17:156 ==>> [D][05:18:56][COMM]read battery soc:255
$GBGGA,101716.036,2301.2573684,N,11421.9412931,E,1,12,1.42,74.092,M,-1.770,M,,*50

$GBGSA,A,3,13,08,42,24,16,06,38,39,26,14,21,33,2.43,1.42,1.98,4*02

$GBGSV,5,1,20,13,79,247,42,8,77,189,40,42,69,9,41,24,64,236,43,1*46

$GBGSV,5,2,20,16,63,313,39,6,63,307,37,3,62,191,41,38,61,171,41,1*7C

$GBGSV,5,3,20,39,61,341,40,26,60,29,41,59,52,130,40,1,48,126,38,1*7F

$GBGSV,5,4,20,2,46,239,36,14,45,334,38,21,44,115,40,9,41,285,37,1*76

$GBGSV,5,5,20,60,41,238,41,4,32,112,34,5,22,258,34,33,21,323,38,1*7C

$GBGSV,2,1,07,42,69,9,43,24,64,236,43,38,61,171,41,39,61,341,41,5*75

$GBGSV,2,2,07,26,60,29,41,21,44,115,41,33,21,323,32,5*7A

$GBRMC,101716.036,A,2301.2573684,N,11421.9412931,E,0.000,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,101716.036,1.560,0.225,0.219,0.309,1.433,1.600,4.244*73

[W][05:18:56][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:56][COMM]Main Task receive event:14
[D][05:18:56][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955136, allstateRepSeconds = 0
[D][05:18:56][COMM]index:0,power_mode:0xFF
[D][05:18:56][COMM]index:1,sound_mode:0xFF
[D][05:18:56][COMM]index:2,gsensor_mode:0xFF
[D][05:18:56][COMM]index:3,report_freq_mode:0xFF
[D][05:18:56][COMM]index:

2025-07-31 18:17:17:261 ==>> 4,report_period:0xFF
[D][05:18:56][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:56][COMM]index:6,normal_reset_period:0xFF
[D][05:18:56][COMM]index:7,spock_over_speed:0xFF
[D][05:18:56][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:56][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:56][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:56][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:56][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:56][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:56][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:56][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:56][COMM]index:16,imu_config_params:0xFF
[D][05:18:56][COMM]index:17,long_connect_params:0xFF
[D][05:18:56][COMM]index:18,detain_mark:0xFF
[D][05:18:56][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:56][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:56][COMM]index:21,mc_mode:0xFF
[D][05:18:56][COMM]index:22,S_mode:0xFF
[D][05:18:56][COMM]index:23,overweight:0xFF
[D][05:18:56][COMM]index:24,standstill_mode:0xFF
[D][05:18:56][COMM]index:25,night_mode:0xFF
[D][05:18:56][COMM]index:26,experiment1:0xFF
[D][05:18:56][COMM]index:27,experiment2:0xFF
[D][05:18:56][COMM]

2025-07-31 18:17:17:366 ==>> index:28,experiment3:0xFF
[D][05:18:56][COMM]index:29,experiment4:0xFF
[D][05:18:56][COMM]index:30,night_mode_start:0xFF
[D][05:18:56][COMM]index:31,night_mode_end:0xFF
[D][05:18:56][COMM]index:33,park_report_minutes:0xFF
[D][05:18:56][COMM]index:34,park_report_mode:0xFF
[D][05:18:56][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:56][COMM]index:38,charge_battery_para: FF
[D][05:18:56][COMM]index:39,multirider_mode:0xFF
[D][05:18:56][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:56][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:56][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:56][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:56][COMM]index:44,riding_duration_config:0xFF
[D][05:18:56][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:56][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:56][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:56][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:56][COMM]index:49,mc_load_startup:0xFF
[D][05:18:56][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:56][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:56][COMM]index:52,traffic_mode:0xFF
[D][05:18:56][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:56][CO

2025-07-31 18:17:17:471 ==>> MM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:56][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:56][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:56][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:56][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:56][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:56][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:56][COMM]index:63,experiment5:0xFF
[D][05:18:56][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:56][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:56][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:56][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:56][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:56][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:56][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:56][COMM]index:72,experiment6:0xFF
[D][05:18:56][COMM]index:73,experiment7:0xFF
[D][05:18:56][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:56][COMM]index:75,zero_value_from_server:-1
[D][05:18:56][COMM]index:76,multirider_threshold:255
[D][05:18:56][COMM]index:77,experiment8:255
[D][05:18:56][COMM]index:78,temp_park_audio_play_duration:

2025-07-31 18:17:17:576 ==>> 255
[D][05:18:56][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:56][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:56][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:56][COMM]index:83,loc_report_interval:255
[D][05:18:56][COMM]index:84,multirider_threshold_p2:255
[D][05:18:56][COMM]index:85,multirider_strategy:255
[D][05:18:56][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:56][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:56][COMM]index:90,weight_param:0xFF
[D][05:18:56][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:56][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:56][COMM]index:95,current_limit:0xFF
[D][05:18:56][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:56][COMM]index:100,location_mode:0xFF

[W][05:18:56][PROT]remove success[1629955136],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:56][PROT]add success [1629955136],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:56][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send

2025-07-31 18:17:17:681 ==>> :0
[D][05:18:56][PROT]sequence_num:9
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [999999999990000A]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][CAT1]gsm read msg sub id: 13
[D][05:18:56][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[294]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]<<< 
+CSQ: 19,99

OK

[D][05:18:56][CAT1]exec over: func id: 13, ret: 21
[D][05:18:56][M2M ]get csq[19]
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56][CAT1]tx ret[17] 

2025-07-31 18:17:17:771 ==>> >>> AT+QISEND=0,294

[D][05:18:56][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88BC989A7FB69ABCA6435CF2E2CC90DB6ABEE5C422C6104D431FF1E50BA4A0233367115E68572742CE5FB788FF9F977685D00314438601B1DDA44B986C38B34F1DB5F647EEF007663138AE760F11F42E495A40A35F572408BE860785199D7F07A0B5BFC7D309908846CD30255C97E9D1731296544CD2B8FB9DA75F8C9538697924B0583A
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]
>>>>>RESEND ALLSTATE<<<<<
[D][05:18

2025-07-31 18:17:17:876 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 18:17:18:223 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:17:18:230 ==>> 检测【关闭GPS】
2025-07-31 18:17:18:238 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:17:18:321 ==>> $GBGGA,101718.000,2301.2574582,N,11421.9414444,E,1,12,1.42,73.686,M,-1.770,M,,*54

$GBGSA,A,3,13,08,42,24,16,06,38,39,26,14,21,33,2.43,1.42,1.98,4*02

$GBGSV,5,1,20,13,79,247,42,8,77,189,41,42,69,9,42,24,64,236,43,1*44

$GBGSV,5,2,20,16,63,314,39,6,63,308,37,3,62,191,41,38,61,171,41,1*74

$GBGSV,5,3,20,39,61,341,40,26,60,29,41,59,52,130,41,1,48,126,39,1*7F

$GBGSV,5,4,20,2,46,239,36,14,45,334,38,21,44,115,41,9,41,285,37,1*77

$GBGSV,5,5,20,60,41,238,41,4,32,112,34,5,22,258,34,33,21,323,38,1*7C

$GBGSV,2,1,07,42,69,9,43,24,64,236,43,38,61,171,40,39,61,341,41,5*74

$GBGSV,2,2,07,26,60,29,41,21,44,115,41,33,21,323,32,5*7A

$GBRMC,101718.000,A,2301.2574582,N,11421.9414444,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,101718.000,1.300,0.203,0.198,0.281,1.132,1.270,3.644*73

[D][05:18:58][COMM]read battery soc:255


2025-07-31 18:17:18:580 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]stop event:8
[D][05:18:58][GNSS]GPS stop. ret=0
[D][05:18:58][GNSS]all continue location stop
[W][05:18:58][GNSS]stop locating
[D][05:18:58][GNSS]all sing location stop
[D][05:18:58][CAT1]gsm read msg sub id: 24
[D][05:18:58][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:58][CAT1]<<< 
OK

[D][05:18:58][CAT1]exec over: func id: 24, ret: 6
[D][05:18:58][CAT1]sub id: 24, ret: 6



2025-07-31 18:17:18:765 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:17:18:772 ==>> 检测【清空消息队列2】
2025-07-31 18:17:18:782 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:17:18:797 ==>> [D][05:18:59][GNSS]recv submsg id[1]
[D][05:18:59][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:59][GNSS]location stop evt done evt


2025-07-31 18:17:18:956 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:59][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:17:19:035 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:17:19:042 ==>> 检测【轮动检测】
2025-07-31 18:17:19:052 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:17:19:169 ==>> 3A A3 01 00 A3 


2025-07-31 18:17:19:259 ==>> OFF_OUT1
OVER 150


2025-07-31 18:17:19:349 ==>> [D][05:18:59][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:17:19:548 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:17:19:673 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:17:19:836 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:17:19:843 ==>> 检测【关闭小电池】
2025-07-31 18:17:19:854 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:17:19:960 ==>> 6A A6 02 A6 6A 


2025-07-31 18:17:20:065 ==>> Battery OFF
OVER 150


2025-07-31 18:17:20:117 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:17:20:127 ==>> 检测【进入休眠模式】
2025-07-31 18:17:20:148 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:17:20:247 ==>> [D][05:19:00][COMM]read battery soc:255
[W][05:19:00][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:17:20:352 ==>> [D][05:19:00][COMM]Main Task receive event:28
[D][05:19:00][COMM]main task tmp_sleep_event = 8
[D][05:19:00][COMM]prepare to sleep
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:17:21:194 ==>> [D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 12, ret: 6
[D][05:19:01][M2M ]tcpclient close[4]
[D][05:19:01][SAL ]Cellular task submsg id[12]
[D][05:19:01][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:01][CAT1]gsm read msg sub id: 9
[D][05:19:01][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 9, ret: 6
[D][05:19:01][CAT1]sub id: 9, ret: 6

[D][05:19:01][SAL ]Cellular task submsg id[68]
[D][05:19:01][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:01][SAL ]socket close ind. id[4]
[D][05:19:01][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:01][COMM]1x1 frm_can_tp_send ok
[D][05:19:01][CAT1]pdpdeact urc len[22]


2025-07-31 18:17:21:466 ==>> [E][05:19:01][COMM]1x1 rx timeout
[D][05:19:01][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:17:21:982 ==>> [E][05:19:02][COMM]1x1 rx timeout
[E][05:19:02][COMM]1x1 tp timeout
[E][05:19:02][COMM]1x1 error -3.
[W][05:19:02][COMM]CAN STOP!
[D][05:19:02][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:02][COMM]------------ready to Power off Acckey 1------------
[D][05:19:02][COMM]------------ready to Power off Acckey 2------------
[D][05:19:02][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:02][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1292
[D][05:19:02][COMM]bat sleep fail, reason:-1
[D][05:19:02][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:02][COMM]accel parse set 0
[D][05:19:02][COMM]imu rest ok. 73171
[D][05:19:02][COMM]imu sleep 0
[W][05:19:02][COMM]now sleep


2025-07-31 18:17:22:248 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:17:22:259 ==>> 检测【检测33V休眠电流】
2025-07-31 18:17:22:279 ==>> 开始33V电流采样
2025-07-31 18:17:22:296 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:22:362 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:17:23:372 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:17:23:402 ==>> Current33V:????:17.31

2025-07-31 18:17:23:874 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:23:881 ==>> 【检测33V休眠电流】通过,【17.31uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:17:23:888 ==>> 该项需要延时执行
2025-07-31 18:17:25:890 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:17:25:901 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:17:25:926 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:17:25:983 ==>> 1A A1 00 00 FC 
Get AD_V2 1635mV
Get AD_V3 1659mV
Get AD_V4 0mV
Get AD_V5 2755mV
Get AD_V6 1960mV
Get AD_V7 1088mV
OVER 150


2025-07-31 18:17:26:955 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:17:26:961 ==>> 检测【打开小电池2】
2025-07-31 18:17:26:999 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:17:27:074 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:17:27:255 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:17:27:262 ==>> 该项需要延时执行
2025-07-31 18:17:27:763 ==>> 此处延时了:【500】毫秒
2025-07-31 18:17:27:774 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:17:27:797 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:17:27:868 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:17:28:061 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:17:28:068 ==>> 该项需要延时执行
2025-07-31 18:17:28:548 ==>> [D][05:19:08][COMM]------------ready to Power on Acckey 1------------
[D][05:19:08][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:08][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:08][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:08][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:08][COMM]----- get Acckey 1 and value:1------------
[W][05:19:08][COMM]CAN START!
[D][05:19:08][CAT1]gsm read msg sub id: 12
[D][05:19:08][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:08][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 79663
[D][05:19:08][COMM][Audio]exec status ready.
[D][05:19:08][CAT1]<<< 
OK

[D][05:

2025-07-31 18:17:28:563 ==>> 此处延时了:【500】毫秒
2025-07-31 18:17:28:586 ==>> 检测【进入休眠模式2】
2025-07-31 18:17:28:612 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:17:28:618 ==>> 19:08][CAT1]exec over: func id: 12, ret: 6
[D][05:19:08][COMM]imu wakeup ok. 79678
[D][05:19:08][COMM]imu wakeup 1
[W][05:19:08][COMM]wake up system, wakeupEvt=0x80
[D][05:19:08][COMM]frm_can_weigth_power_set 1
[D][05:19:08][COMM]Clear Sleep Block Evt
[D][05:19:08][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:08][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:17:28:728 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:17:28:970 ==>> [D][05:19:09][HSDK][0] flush to flash addr:[0xE42F00] --- write len --- [256]
[E][05:19:09][COMM]1x1 rx timeout
[D][05:19:09][COMM]1x1 frm_can_tp_send ok
[D][05:19:09][COMM]msg 02A0 loss. last_tick:79649. cur_tick:80156. period:50
[D][05:19:09][COMM]msg 02A4 loss. last_tick:79649. cur_tick:80157. period:50
[D][05:19:09][COMM]msg 02A5 loss. last_tick:79649. cur_tick:80157. period:50
[D][05:19:09][COMM]msg 02A6 loss. last_tick:79649. cur_tick:80158. period:50
[D][05:19:09][COMM]msg 02A7 loss. last_tick:79649. cur_tick:80158. period:50
[D][05:19:09][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 80159
[D][05:19:09][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 80159


2025-07-31 18:17:29:291 ==>> [E][05:19:09][COMM]1x1 rx timeout
[E][05:19:09][COMM]1x1 tp timeout
[E][05:19:09][COMM]1x1 error -3.
[D][05:19:09][COMM]Main Task receive event:28 finished processing
[D][05:19:09][COMM]Main Task receive event:28
[D][05:19:09][COMM]prepare to sleep
[D][05:19:09][CAT1]gsm read msg sub id: 12
[D][05:19:09][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:09][CAT1]<<< 
OK

[D][05:19:09][CAT1]exec over: func id: 12, ret: 6
[D][05:19:09][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:09][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:17:29:594 ==>> [D][05:19:09][COMM]msg 0220 loss. last_tick:79649. cur_tick:80653. period:100
[D][05:19:09][COMM]msg 0221 loss. last_tick:79649. cur_tick:80653. period:100
[D][05:19:09][COMM]msg 0224 loss. last_tick:79649. cur_tick:80653. period:100
[D][05:19:09][COMM]msg 0260 loss. last_tick:79649. cur_tick:80654. period:100
[D][05:19:09][COMM]msg 0280 loss. last_tick:79649. cur_tick:80654. period:100
[D][05:19:09][COMM]msg 02C0 loss. last_tick:79649. cur_tick:80654. period:100
[D][05:19:09][COMM]msg 02C1 loss. last_tick:79649. cur_tick:80655. period:100
[D][05:19:09][COMM]msg 02C2 loss. last_tick:79649. cur_tick:80655. period:100
[D][05:19:09][COMM]msg 02E0 loss. last_tick:79649. cur_tick:80655. period:100
[D][05:19:09][COMM]msg 02E1 loss. last_tick:79649. cur_tick:80656. period:100
[D][05:19:09][COMM]msg 02E2 loss. last_tick:79649. cur_tick:80656. period:100
[D][05:19:09][COMM]msg 0300 loss. last_tick:79649. cur_tick:80656. period:100
[D][05:19:09][COMM]msg 0301 loss. last_tick:79649. cur_tick:80657. period:100
[D][05:19:09][COMM]bat msg 0240 loss. last_tick:79649. cur_tick:80657. period:100. j,i:1 54
[D][05:19:09][COMM]bat msg 0241 loss. last_tick:79649. cur_tick:80658. period:100. j,i:2 55
[D][05:19:09][COMM]bat msg 0242 loss. last_tick:79649. cur_tick:80658. period

2025-07-31 18:17:29:685 ==>> :100. j,i:3 56
[D][05:19:09][COMM]bat msg 0244 loss. last_tick:79649. cur_tick:80659. period:100. j,i:5 58
[D][05:19:09][COMM]bat msg 024E loss. last_tick:79649. cur_tick:80659. period:100. j,i:15 68
[D][05:19:09][COMM]bat msg 024F loss. last_tick:79649. cur_tick:80659. period:100. j,i:16 69
[D][05:19:09][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 80660
[D][05:19:09][COMM]CAN message bat fault change: 0x00000000->0x0001802E 80660
[D][05:19:09][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 80661
                                                                              

2025-07-31 18:17:29:880 ==>> [D][05:19:10][COMM]msg 0222 loss. last_tick:79649. cur_tick:81155. period:150
[D][05:19:10][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 81156


2025-07-31 18:17:29:969 ==>>                                                                                                               _poweroff type 16.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:17:30:074 ==>> [E][05:19:10][COMM]1x1 rx timeout
[E][05:19:10][COMM]1x1 tp timeout
[E][05:19:10][COMM]1x1 error -3.
[W][05:19:10][COMM]CAN STOP!


2025-07-31 18:17:30:149 ==>> [D][05:19:10][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:10][COMM]------------ready to Power off Acckey 1------------
[D][05:19:10][COMM]------------ready to Power off Acckey 2------------
[D][05:19:10][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:10][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 93
[D][05:19:10][COMM]bat sleep fail, reason:-1
[D][05:19:10][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:10][COMM]accel parse set 0
[D][05:19:10][COMM]imu rest ok. 81346
[D][05:19:10][COMM]imu sleep 0
[W][05:19:10][COMM]now sleep


2025-07-31 18:17:30:409 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:17:30:416 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:17:30:422 ==>> 开始小电池电流采样
2025-07-31 18:17:30:448 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:30:517 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:17:31:518 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:17:31:564 ==>> CurrentBattery:ƽ��:68.45

2025-07-31 18:17:32:023 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:32:029 ==>> 【检测小电池休眠电流】通过,【68.45uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:17:32:036 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:17:32:058 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:17:32:159 ==>> 5A A5 01 5A A5 


2025-07-31 18:17:32:264 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 18:17:32:342 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:17:32:349 ==>> 该项需要延时执行
2025-07-31 18:17:32:489 ==>> [D][05:19:12][COMM]------------ready to Power on Acckey 1------------
[D][05:19:12][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 16
[D][05:19:12][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:12][COMM]----- get Acckey 1 and value:1------------
[W][05:19:12][COMM]CAN START!
[D][05:19:12][COMM]read battery soc:0
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:12][COMM]CAN message bat fault change: 0x0001802E->0x00000000 83646
[D][05:19:12][COMM][Audio]exec status ready.
[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]imu wakeup ok. 83660
[D][05:19:12][COMM]imu wakeup 1
[W][05:19:12][COMM]wake up system, wakeupEvt=0x80
[D][05:19:12][COMM]frm_can_weigth_power_set 1
[D][05:19:12][COMM]Clear Sleep Block Evt
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:17:32:794 ==>> [D][05:19:13][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0
[E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:17:32:854 ==>> 此处延时了:【500】毫秒
2025-07-31 18:17:32:864 ==>> 检测【检测唤醒】
2025-07-31 18:17:32:890 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:17:32:900 ==>>                                                                      riod:50
[D][05:19:13][COMM]msg 02A4 loss. last_tick:83629. cur_tick:84141. period:50
[D][05:19:13][COMM]msg 02A5 loss. last_tick:83629. cur_tick:84141. period:50
[D][05:19:13][COMM]msg 02A6 loss. last_tick:83629. cur_tick:84141. period:50
[D][05:19:13][COMM]msg 02A7 loss. last_tick:83629. cur_tick:84142. period:50
[D][05:19:13][COMM]CAN message faul

2025-07-31 18:17:32:929 ==>> t change: 0x0000000000000000->0x0000E00000220000 84142
[D][05:19:13][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 84143


2025-07-31 18:17:33:636 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:13][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[D][05:19:13][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:13][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:13][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:13][FCTY]DeviceID    = 460130071536203
[D][05:19:13][FCTY]HardwareID  = 867222087476927
[D][05:19:13][FCTY]MoBikeID    = 9999999999
[D][05:19:13][FCTY]LockID      = FFFFFFFFFF
[D][05:19:13][FCTY]BLEFWVersion= 105
[D][05:19:13][FCTY]BLEMacAddr   = D8E9DCEC02B4
[D][05:19:13][FCTY]Bat         = 3364 mv
[D][05:19:13][FCTY]Current     = 0 ma
[D][05:19:13][FCTY]VBUS        = 2600 mv
[D][05:19:13][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:13][FCTY]Ext battery vol = 32, adc = 1287
[D][05:19:13][FCTY]Acckey1 vol = 5496 mv, Acckey2 vol = 0 mv
[D][05:19:13][FCTY]Bike Type flag is invalied
[D][05:19:13][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:13][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:13][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:13][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:13][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:13][FCTY]Bat1         = 3828 mv
[D]

2025-07-31 18:17:33:680 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:17:33:691 ==>> 检测【关机】
2025-07-31 18:17:33:711 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:17:33:740 ==>> [05:19:13][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:13][FCTY]==========Modules-nRF5340 ==========
[E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[D][05:19:13][COMM]Main Task receive event:28 finished processing
[D][05:19:13][COMM]Main Task receive event:65
[D][05:19:13][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:13][COMM]Main Task receive event:65 finished processing
[D][05:19:13][COMM]Main Task receive event:60
[D][05:19:13][COMM]smart_helmet_vol=255,255
[D][05:19:13][COMM]report elecbike
[W][05:19:13][PROT]remove success[1629955153],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:19:13][HSDK][0] flush to flash addr:[0xE43000] --- write len --- [256]
[W][05:19:13][PROT]add success [1629955153],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:13][COMM]Main Task receive event:60 finished processing
[D][05:19:13][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:13][PROT]index:0
[D][05:19:13][PROT]is_send:1
[D][05:19:13][PROT]sequence_num:11
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:3
[D][05:19:13][PROT]send_

2025-07-31 18:17:33:845 ==>> path:0x3
[D][05:19:13][PROT]msg_type:0x5d03
[D][05:19:13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][PROT]Sending traceid[999999999990000C]
[D][05:19:13][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:13][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:13][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:13][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:13][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:13][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:13][SAL ]open socket ind id[4], rst[0]
[D][05:19:13][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:13][SAL ]Cellular task submsg id[8]
[D][05:19:13][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:13

2025-07-31 18:17:33:950 ==>> ][CAT1]gsm read msg sub id: 8
[D][05:19:13][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:13][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:13][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:13][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:13][CAT1]<<< 
+CME ERROR: 100

[D][05:19:13][COMM]msg 0220 loss. last_tick:83629. cur_tick:84637. period:100
[D][05:19:13][COMM]msg 0221 loss. last_tick:83629. cur_tick:84637. period:100
[D][05:19:13][COMM]msg 0224 loss. last_tick:83629. cur_tick:84637. period:100
[D][05:19:13][COMM]msg 0260 loss. last_tick:83629. cur_tick:84638. period:100
[D][05:19:13][COMM]msg 0280 loss. last_tick:83629. cur_tick:84638. period:100
[D][05:19:13][COMM]msg 02C0 loss. last_tick:83629. cur_tick:84638. period:100
[D][05:19:13][COMM]msg 02C1 loss. last_tick:83629. cur_tick:84639. period:100
[D][05:19:13][COMM]msg 02C2 loss. last_tick:83629. cur_tick:84639. period:100
[D][05:19:13][COMM]msg 02E0 loss. last_tick:83629. cur_tick:84639. period:100
[D][05:19:13][COMM]msg 02E1 loss. last_tick:83629. cur_tick:84640. period:100
[D][05:19:13][COMM]msg 02E2 loss. last_tick:83629. cur_tick:84640. period:100
[D][05:19:13][COMM]msg 0300 loss. last_tick:83629. cur_tick:8464

2025-07-31 18:17:34:055 ==>> 1. period:100
[D][05:19:13][COMM]msg 0301 loss. last_tick:83629. cur_tick:84641. period:100
[D][05:19:13][COMM]bat msg 0240 loss. last_tick:83629. cur_tick:84641. period:100. j,i:1 54
[D][05:19:13][COMM]bat msg 0241 loss. last_tick:83629. cur_tick:84642. period:100. j,i:2 55
[D][05:19:13][COMM]bat msg 0242 loss. last_tick:83629. cur_tick:84642. period:100. j,i:3 56
[D][05:19:13][COMM]bat msg 0244 loss. last_tick:83629. cur_tick:84642. period:100. j,i:5 58
[D][05:19:13][COMM]bat msg 024E loss. last_tick:83629. cur_tick:84643. period:100. j,i:15 68
[D][05:19:13][COMM]bat msg 024F loss. last_tick:83629. cur_tick:84643. period:100. j,i:16 69
[D][05:19:13][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 84644
[D][05:19:13][COMM]CAN message bat fault change: 0x00000000->0x0001802E 84644
[D][05:19:13][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 84644


2025-07-31 18:17:34:705 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:17:34:735 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 18:17:34:841 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         19:14][COMM]accel parse set 1
[D][05:19:14][COMM][Audio]mon:9,05:19:14
[D][05:19:14][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[704].

2025-07-31 18:17:34:946 ==>> audio cmd send:AT+AUDIOSEND=1

[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:14][COMM]Main Task receive event:65
[D][05:19:14][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:14][COMM]Main Task receive event:65 finished processing
[D][05:19:14][COMM]Main Task receive event:66
[D][05:19:14][COMM]Try to Auto Lock Bat
[D][05:19:14][COMM]Main Task receive event:66 finished processing
[D][05:19:14][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:14][COMM]Main Task receive event:60
[D][05:19:14][COMM]smart_helmet_vol=255,255
[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]BAT CAN get state2 fail 204
[D][05:19:14][COMM]get soh error
[E][05:19:14][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:14][COMM]report elecbike
[W][05:19:14][PROT]remove success[1629955154],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:14][PROT]add success [1

2025-07-31 18:17:35:051 ==>> 629955154],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:14][COMM]Main Task receive event:60 finished processing
[D][05:19:14][COMM]Main Task receive event:61
[D][05:19:14][COMM][D301]:type:3, trace id:280
[D][05:19:14][COMM]id[], hw[000
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:14][COMM]get mcMaincircuitVolt error
[D][05:19:14][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:14][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:14][PROT]index:1
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:14][PROT]is_send:1
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:14][PROT]sequence_num:12
[D][05:19:14][PROT]retry_timeout:0
[D][05:19:14][PROT]retry_times:3
[D][05:19:14][PROT]send_path:0x3
[D][05:19:14][PROT]msg_type:0x5d03
[D][05:19:14][PROT]===========================================================
[W][05:19:14][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955154]
[D][05:19:14][PROT]===========================================================
[D][05:19:14][PROT]Sending traceid[999999999990000D]
[D][05:19:14][BLE ]BLE_WRN [ble_service_get_current_send_en

2025-07-31 18:17:35:156 ==>> abled:28] ble is not connect

[D][05:19:14][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:14][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:14][COMM]Receive Bat Lock cmd 0
[D][05:19:14][COMM]VBUS is 1
[D][05:19:14][COMM]get mcSubcircuitVolt error
[D][05:19:14][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:14][COMM]BAT CAN get state1 Fail 204
[D][05:19:14][COMM]BAT CAN get soc Fail, 204
[D][05:19:14][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:14][COMM]BAT CAN get state2 fail 204
[D][05:19:14][COMM]get bat work mode err
[W][05:19:14][PROT]remove success[1629955154],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:14][PROT]add success [1629955154],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:14][COMM]Main Task receive event:61 finished processing
[D][05:19:14][M2M ]m2m_task: control_queue typ

2025-07-31 18:17:35:261 ==>> e:[M2M_GSM_POWER_ON]
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:14][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:14][COMM]arm_hub_enable: hub power: 0
[D][05:19:14][HSDK]hexlog index save 0 8448 64 @ 0 : 0
[D][05:19:14][HSDK]write save hexlog index [0]
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:14][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:14][COMM]read battery soc:255
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800

2025-07-31 18:17:35:366 ==>> m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:14][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:14][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:14][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 18:17:35:456 ==>>                                                                                                                                                                                                                                                                            2] ready to read para flash
[D][05:19:15][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:17:35:726 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:17:35:996 ==>> [W][05:19:16][COMM]Power Off
[W][05:19:16][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:16][COMM]arm_hub_enable: hub power: 0
[D][05:19:16][HSDK]hexlog index save 0 8448 64 @ 0 : 0
[D][05:19:16][HSDK]write save hexlog index [0]
[D][05:19:16][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:16][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 18:17:36:258 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:17:36:265 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:17:36:278 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:17:36:371 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:17:36:431 ==>> [D][05:19:16][COMM]read battery soc:255
[W][05:19:16][COMM]Power Off


2025-07-31 18:17:36:550 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:17:36:557 ==>> 检测【检测小电池关机电流】
2025-07-31 18:17:36:574 ==>> 开始小电池电流采样
2025-07-31 18:17:36:580 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:36:657 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:17:37:660 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:17:37:722 ==>> CurrentBattery:ƽ��:66.59

2025-07-31 18:17:38:167 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:17:38:174 ==>> 【检测小电池关机电流】通过,【66.59uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:17:38:558 ==>> MES过站成功
2025-07-31 18:17:38:572 ==>> #################### 【测试结束】 ####################
2025-07-31 18:17:38:629 ==>> 关闭5V供电
2025-07-31 18:17:38:642 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:17:38:773 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:17:39:641 ==>> 关闭5V供电成功
2025-07-31 18:17:39:653 ==>> 关闭33V供电
2025-07-31 18:17:39:682 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:17:39:761 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:17:40:650 ==>> 关闭33V供电成功
2025-07-31 18:17:40:663 ==>> 关闭3.7V供电
2025-07-31 18:17:40:690 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:17:40:773 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:17:41:599 ==>>  

2025-07-31 18:17:41:659 ==>> 关闭3.7V供电成功
