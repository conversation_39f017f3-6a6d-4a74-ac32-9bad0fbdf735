2025-07-31 23:17:23:651 ==>> MES查站成功:
查站序号:P510001005313968验证通过
2025-07-31 23:17:23:655 ==>> 扫码结果:P510001005313968
2025-07-31 23:17:23:657 ==>> 当前测试项目:SE51_PCBA
2025-07-31 23:17:23:658 ==>> 测试参数版本:2024.10.11
2025-07-31 23:17:23:660 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 23:17:23:661 ==>> 检测【打开透传】
2025-07-31 23:17:23:663 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 23:17:23:725 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 23:17:24:011 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 23:17:24:016 ==>> 检测【检测接地电压】
2025-07-31 23:17:24:018 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 23:17:24:127 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 23:17:24:373 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 23:17:24:375 ==>> 检测【打开小电池】
2025-07-31 23:17:24:378 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:17:24:414 ==>> 6A A6 01 A6 6A 


2025-07-31 23:17:24:519 ==>> Battery ON
OVER 150


2025-07-31 23:17:24:680 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:17:24:684 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 23:17:24:689 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 23:17:24:824 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 23:17:25:014 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 23:17:25:018 ==>> 检测【等待设备启动】
2025-07-31 23:17:25:020 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:17:25:278 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:17:25:461 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 23:17:25:973 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:17:26:049 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:17:26:154 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:17:26:797 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 23:17:26:842 ==>>                                                    

2025-07-31 23:17:27:086 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 23:17:27:238 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:17:27:711 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 23:17:27:880 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 23:17:27:885 ==>> 检测【产品通信】
2025-07-31 23:17:27:887 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:17:28:438 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 23:17:28:635 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 23:17:28:913 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:17:29:343 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 23:17:29:728 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 23:17:29:954 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 23:17:30:705 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[D][05:17:50][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[D][05:17:50][COMM][MC]get MC real state err,rt:-3
[D][05:17:50][COMM]frm_can_weigth_power_set 1
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[D][05:17:50][COMM]index:0,power_mode:0xFF
[D][05:17:50][COMM]index:1,sound_mode:0xFF
[D][05:17:50][COMM]index:2,gsensor_mode:0xFF
[D][05:17:50][COMM]index:3,report_freq_mode:0xFF
[D][05:17:50][COMM]index:4,report_period:0xFF
[D][05:17:50][COMM]index:5,normal_reset_mode:0xFF
[D][05:17:50][COMM]index:6,normal_reset_period:0xFF
[D][05:17:50][COMM]index:7,spock_over_speed:0xFF
[D][05:17:50][COMM]index:8,spock_limit_speed:0xFF
[D][05:17:50][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:17:50][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:17:50][COMM]msg 0222 loss. last_tick:0. cur_tick:1515. period:150
[D][05:17:50][COMM]CAN message faul

2025-07-31 23:17:30:753 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 23:17:30:757 ==>> 检测【初始化完成检测】
2025-07-31 23:17:30:760 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 23:17:30:810 ==>> t change: 0x0000E00C71E22213->0x0000E00C71E22217 1516
[D][05:17:50][COMM]index:11,ble_scan_mode:0xFF
[D][05:17:50][COMM]index:12,ble_adv_mode:0xFF
[D][05:17:50][COMM]index:13,spock_audio_volumn:0xFF
[D][05:17:50][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:17:50][COMM]index:15,bat_auth_mode:0xFF
[D][05:17:50][COMM]index:16,imu_config_params:0xFF
[D][05:17:50][COMM]index:17,long_connect_params:0xFF
[D][05:17:50][COMM]index:18,detain_mark:0xFF
[D][05:17:50][COMM]index:19,lock_pos_report_count:0xFF
[D][05:17:50][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:17:50][COMM]index:21,mc_mode:0xFF
[D][05:17:50][COMM]index:22,S_mode:0xFF
[D][05:17:50][COMM]index:23,overweight:0xFF
[D][05:17:50][COMM]index:24,standstill_mode:0xFF
[D][05:17:50][COMM]index:25,night_mode:0xFF
[D][05:17:50][COMM]index:26,experiment1:0xFF
[D][05:17:50][COMM]index:27,experiment2:0xFF
[D][05:17:50][COMM]index:28,experiment3:0xFF
[D][05:17:50][COMM]index:29,experiment4:0xFF
[D][05:17:50][COMM]index:30,night_mode_start:0xFF
[D][05:17:50][COMM]index:31,night_mode_end:0xFF
[D][05:17:50][COMM]index:33,park_report_minutes:0xFF
[D][05:17:50][COMM]index:34,park_report_mode:0xFF
[D][05:17:50][COMM]in

2025-07-31 23:17:30:915 ==>> dex:35,mc_undervoltage_protection:0xFF
[D][05:17:50][COMM]index:38,charge_battery_para: FF
[D][05:17:50][COMM]index:39,multirider_mode:0xFF
[D][05:17:50][COMM]index:40,mc_launch_mode:0xFF
[D][05:17:50][COMM]index:41,head_light_enable_mode:0xFF
[D][05:17:50][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:17:50][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:17:50][COMM]index:44,riding_duration_config:0xFF
[D][05:17:50][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:17:50][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:17:50][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:17:50][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:17:50][COMM]index:49,mc_load_startup:0xFF
[D][05:17:50][COMM]index:50,mc_tcs_mode:0xFF
[D][05:17:50][COMM]index:51,traffic_audio_play:0xFF
[D][05:17:50][COMM]index:52,traffic_mode:0xFF
[D][05:17:50][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:17:50][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:17:50][COMM]index:55,wheel_alarm_play_switch:255
[D][05:17:50][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:17:50][COMM]index:58,traffic_light_threshold:0xFF
[D][05:17:50][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:17:50][COMM

2025-07-31 23:17:31:020 ==>> ]index:60,traffic_road_threshold:0xFF
[D][05:17:50][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:17:50][COMM]index:63,experiment5:0xFF
[D][05:17:50][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:17:50][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:17:50][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:17:50][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:17:50][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:17:50][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:17:50][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_t

2025-07-31 23:17:31:125 ==>> hreshold_p2:255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1626 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
                                                             

2025-07-31 23:17:31:305 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]2638 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:31:410 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index

2025-07-31 23:17:31:440 ==>> [1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 23:17:31:554 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 23:17:31:557 ==>> 检测【关闭大灯控制1】
2025-07-31 23:17:31:559 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:17:31:682 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:17:31:835 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:17:31:838 ==>> 检测【打开仪表指令模式1】
2025-07-31 23:17:31:840 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:17:32:013 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 23:17:32:123 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:17:32:125 ==>> 检测【关闭仪表供电】
2025-07-31 23:17:32:128 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:17:32:319 ==>> [D][05:17:52][COMM]3649 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 23:17:32:593 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:17:32:596 ==>> 检测【关闭AccKey2供电1】
2025-07-31 23:17:32:599 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:17:32:780 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:17:32:907 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:17:32:909 ==>> 检测【关闭AccKey1供电1】
2025-07-31 23:17:32:911 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 23:17:33:081 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 23:17:33:291 ==>> [D][05:17:53][COMM]4661 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:33:499 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 23:17:33:507 ==>> 检测【关闭转刹把供电1】
2025-07-31 23:17:33:509 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:17:33:824 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5011. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5012. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5012. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5013. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5013. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5014. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5014. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5015. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5015. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5016
[D][05:17:54][COMM]CAN mes

2025-07-31 23:17:33:869 ==>> sage bat fault change: 0x0001802E->0x01B987FE 5017
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:54][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:17:34:092 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:17:34:095 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 23:17:34:097 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:17:34:219 ==>> 5A A5 01 5A A5 


2025-07-31 23:17:34:324 ==>> [D][05:17:54][COMM]5673 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:17:34:373 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:17:34:377 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 23:17:34:381 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 23:17:34:429 ==>> [D][05:17:54][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 28
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 23:17:34:489 ==>> [D][05:17:54][COMM]read battery soc:255


2025-07-31 23:17:34:643 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 23:17:34:647 ==>> 该项需要延时执行
2025-07-31 23:17:35:303 ==>> [D][05:17:55][COMM]6685 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:35:546 ==>> [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 23:17:35:985 ==>> [D][05:17:56][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:56][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:56][COMM]----- get Acckey 1 and value:1------------
[D][05:17:56][COMM]----- get Acckey 2 and value:0------------
[D][05:17:56][COMM]------------ready to Power on Acckey 2------------


2025-07-31 23:17:36:482 ==>> [D][05:17:56][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:56][COMM]----- get Acckey 1 and value:1------------
[D][05:17:56][COMM]----- get Acckey 2 and value:1------------
[D][05:17:56][COMM]more than the number of battery plugs
[D][05:17:56][COMM]VBUS is 1
[D][05:17:56][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:56][COMM]file:B50 exist
[D][05:17:56][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:56][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:56][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:56][COMM]Bat auth off fail, error:-1
[D][05:17:56][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:56][COMM]----- get Acckey 1 and value:1------------
[D][05:17:56][COMM]----- get Acckey 2 and value:1------------
[D][05:17:56][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:56][COMM]----- get Acckey 1 and value:1------------
[D][05:17:56][COMM]----- get Acckey 2 and value:1------------
[D][05:17:56][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:56][COMM][Audio].l:[904].echo is not ready
[D][05:17:56][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fa

2025-07-31 23:17:36:587 ==>> il
[D][05:17:56][COMM]Main Task receive event:65
[D][05:17:56][COMM]main task tmp_sleep_event = 80
[D][05:17:56][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:56][COMM]Main Task receive event:65 finished processing
[D][05:17:56][COMM]Main Task receive event:66
[D][05:17:56][COMM]Try to Auto Lock Bat
[D][05:17:56][COMM]Main Task receive event:66 finished processing
[D][05:17:56][COMM]Main Task receive event:60
[D][05:17:56][COMM]smart_helmet_vol=255,255
[D][05:17:56][COMM]BAT CAN get state1 Fail 204
[D][05:17:56][COMM]BAT CAN get soc Fail, 204
[D][05:17:56][COMM]get soc error
[E][05:17:56][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:56][COMM]report elecbike
[W][05:17:56][PROT]remove success[1629955076],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:56][PROT]add success [1629955076],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:56][COMM]Main Task receive event:60 finished processing
[D][05:17:56][COMM]Main Task receive event:61
[D][05:17:56][COMM][D301]:type:3, trace id:280
[D][05:17:56][COMM]id[], hw[000
[D][05:17:56][COMM]get mcMaincircuitVolt error
[D][05:17:56][COMM]get mcSubcircuitVolt error


2025-07-31 23:17:36:692 ==>> 
[D][05:17:56][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:56][COMM]BAT CAN get state1 Fail 204
[D][05:17:56][COMM]BAT CAN get soc Fail, 204
[D][05:17:56][COMM]get bat work state err
[D][05:17:56][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:56][PROT]index:2
[D][05:17:56][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:56][PROT]is_send:1
[D][05:17:56][PROT]sequence_num:2
[D][05:17:56][PROT]retry_timeout:0
[D][05:17:56][PROT]retry_times:3
[D][05:17:56][PROT]send_path:0x3
[D][05:17:56][PROT]msg_type:0x5d03
[D][05:17:56][PROT]===========================================================
[W][05:17:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955076]
[D][05:17:56][PROT]===========================================================
[D][05:17:56][PROT]Sending traceid[9999999999900003]
[D][05:17:56][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:56][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:56][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:56][COMM]Receive Bat Lock cmd 0
[D][05:17:56][COMM]VBUS is 1
[W

2025-07-31 23:17:36:752 ==>> ][05:17:56][PROT]remove success[1629955076],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:56][PROT]add success [1629955076],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:56][COMM]Main Task receive event:61 finished processing
[D][05:17:56][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:56][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:56][COMM]7697 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:36:782 ==>>                                          

2025-07-31 23:17:37:322 ==>> [D][05:17:57][COMM]8708 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:38:343 ==>> [D][05:17:58][COMM]9720 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:17:38:478 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 23:17:38:645 ==>> 此处延时了:【4000】毫秒
2025-07-31 23:17:38:650 ==>> 检测【33V输入电压ADC】
2025-07-31 23:17:38:654 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:38:690 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10022


2025-07-31 23:17:38:930 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:59][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:17:59][COMM]adc read out 24v adc:1300  volt:32880 mv
[D][05:17:59][COMM]adc read left brake adc:2  volt:2 mv
[D][05:17:59][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:59][COMM]adc read throttle adc:2  volt:2 mv
[D][05:17:59][COMM]adc read battery ts volt:7 mv
[D][05:17:59][COMM]adc read in 24v adc:1277  volt:32299 mv
[D][05:17:59][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:59][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:59][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:17:59][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:59][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:59][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 23:17:39:180 ==>> 【33V输入电压ADC】通过,【32299mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 23:17:39:182 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 23:17:39:185 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:17:39:328 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1664mV
Get AD_V4 1mV
Get AD_V5 2790mV
Get AD_V6 1996mV
Get AD_V7 1097mV
OVER 150


2025-07-31 23:17:39:466 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:39:469 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:17:39:489 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:39:492 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:17:39:494 ==>> 原始值:【2790】, 乘以分压基数【2】还原值:【5580】
2025-07-31 23:17:39:510 ==>> 【TP68_VCC5V5(ADV5)】通过,【5580mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:17:39:513 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:17:39:529 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1996mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:17:39:534 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:17:39:555 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:17:39:562 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:17:39:570 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10730 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D

2025-07-31 23:17:39:615 ==>> ][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2791mV
Get AD_V6 1992mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:17:39:720 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 23:17:39:839 ==>> 【TP7_VCC3V3(ADV2)】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:39:844 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:17:39:858 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:39:861 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:17:39:863 ==>> 原始值:【2791】, 乘以分压基数【2】还原值:【5582】
2025-07-31 23:17:39:876 ==>> 【TP68_VCC5V5(ADV5)】通过,【5582mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:17:39:880 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:17:39:897 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:17:39:899 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:17:39:921 ==>> 【TP1_VCC12V(ADV7)】通过,【1098mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:17:39:924 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:17:40:036 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1663mV
Get AD_V4 1mV
Get AD_V5 2789mV
Get AD_V6 1991mV
Get AD_V7 1097mV
OVER 150


2025-07-31 23:17:40:195 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:40:197 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 23:17:40:214 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:40:216 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 23:17:40:218 ==>> 原始值:【2789】, 乘以分压基数【2】还原值:【5578】
2025-07-31 23:17:40:232 ==>> 【TP68_VCC5V5(ADV5)】通过,【5578mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:17:40:235 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 23:17:40:250 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 23:17:40:253 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 23:17:40:274 ==>> 【TP1_VCC12V(ADV7)】通过,【1097mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 23:17:40:277 ==>> 检测【打开WIFI(1)】
2025-07-31 23:17:40:279 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:17:40:343 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 23:17:40:508 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][COMM]read battery soc:255


2025-07-31 23:17:40:623 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:17:40:626 ==>> 检测【清空消息队列(1)】
2025-07-31 23:17:40:628 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:17:40:794 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:01][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:17:40:901 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:17:40:904 ==>> 检测【打开GPS(1)】
2025-07-31 23:17:40:909 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 23:17:41:115 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating


2025-07-31 23:17:41:178 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 23:17:41:181 ==>> 检测【打开GSM联网】
2025-07-31 23:17:41:185 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 23:17:41:453 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000



2025-07-31 23:17:41:742 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 23:17:41:745 ==>> 检测【打开仪表供电1】
2025-07-31 23:17:41:747 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 23:17:42:042 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087569440

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:02][CAT1]<<< 
460130071539492

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=

2025-07-31 23:17:42:072 ==>> 1,1,"cmiot","","",0



2025-07-31 23:17:42:273 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 23:17:42:276 ==>> 检测【打开仪表指令模式2】
2025-07-31 23:17:42:281 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 23:17:42:520 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]read battery soc:255


2025-07-31 23:17:42:553 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 23:17:42:556 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 23:17:42:558 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 23:17:42:715 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:03][COMM]arm_hub read adc[3],val[33224]


2025-07-31 23:17:42:823 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:17:42:826 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 23:17:42:830 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:43:021 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:17:43:100 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 23:17:43:103 ==>> 检测【AD_V20电压】
2025-07-31 23:17:43:106 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:43:201 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:43:367 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]14742 imu init OK


2025-07-31 23:17:43:595 ==>> 本次取值间隔时间:380ms
2025-07-31 23:17:43:614 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:43:625 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 23:17:43:715 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:43:820 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:17:43:895 ==>> 本次取值间隔时间:167ms
2025-07-31 23:17:43:937 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:44:047 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:44:212 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:04][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:

2025-07-31 23:17:44:317 ==>> 04][CAT1]tx ret[8] >>> AT+CSQ

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
+CSQ: 23,99

OK

[D][05:18:04][CAT1]tx ret[11] >>> AT+QIACT?

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 23:17:44:347 ==>>                                                 

2025-07-31 23:17:44:452 ==>> 本次取值间隔时间:391ms
2025-07-31 23:17:44:471 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:44:572 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:44:707 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150
                                                                                                                                                                                                                                                                                                                                                                                                       [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][COMM]read battery soc:255
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular

2025-07-31 23:17:44:752 ==>>  task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 23:17:44:872 ==>> 本次取值间隔时间:299ms
2025-07-31 23:17:44:890 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:44:994 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:45:054 ==>> 本次取值间隔时间:57ms
2025-07-31 23:17:45:219 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:17:45:294 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 23:17:45:536 ==>> 本次取值间隔时间:471ms
2025-07-31 23:17:45:927 ==>> 本次取值间隔时间:385ms
2025-07-31 23:17:45:943 ==>> [D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 23:17:45:987 ==>> 本次取值间隔时间:56ms
2025-07-31 23:17:45:991 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:46:093 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:46:168 ==>> [D][05:18:06][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,40,,,41,25,,,40,39,,,39,34,,,38,1*73

$GBGSV,3,2,09,41,,,38,23,,,37,11,,,40,59,,,38,1*76

$GBGSV,3,3,09,33,,,37,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1609.883,1609.883,51.421,2097152,2097152,2097152*4C

[D][05:18:06][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:06][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:06][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:06][CAT1]<<< 
OK

[D][05:18:06][CAT1]exec over: func id: 23, ret: 6
[D][05:18:06][CAT1]sub id: 23, ret: 6



2025-07-31 23:17:46:243 ==>> [W][05:18:06][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:17:46:288 ==>> 本次取值间隔时间:190ms
2025-07-31 23:17:46:348 ==>> [D][05:18:06][GNSS]recv submsg id[1]
[D][05:18:06][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 23:17:46:423 ==>> 本次取值间隔时间:133ms
2025-07-31 23:17:46:559 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 23:17:46:800 ==>> 本次取值间隔时间:370ms
2025-07-31 23:17:47:091 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,40,,,41,25,,,39,39,,,39,60,,,39,1*77

$GBGSV,3,2,12,34,,,38,41,,,38,59,,,38,23,,,37,1*74

$GBGSV,3,3,12,11,,,36,33,,,34,7,,,34,1,,,38,1*7D

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1556.529,1556.529,49.754,2097152,2097152,2097152*44



2025-07-31 23:17:47:151 ==>> 本次取值间隔时间:349ms
2025-07-31 23:17:47:158 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:47:256 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:47:287 ==>> [W][05:18:07][COMM]>>>>>Input command = ?<<<<<


2025-07-31 23:17:47:317 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:17:47:529 ==>> 本次取值间隔时间:260ms
2025-07-31 23:17:47:547 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:47:653 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:47:730 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 23:17:48:090 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,40,,,41,3,,,40,25,,,39,39,,,39,1*4F

$GBGSV,4,2,16,34,,,39,60,,,38,41,,,38,59,,,38,1*7E

$GBGSV,4,3,16,43,,,38,23,,,37,11,,,37,7,,,36,1*49

$GBGSV,4,4,16,1,,,34,33,,,34,5,,,32,32,,,31,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1531.360,1531.360,48.984,2097152,2097152,2097152*46



2025-07-31 23:17:48:105 ==>> 本次取值间隔时间:441ms
2025-07-31 23:17:48:123 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 23:17:48:225 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 23:17:48:330 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:08][COMM]oneline display ALL on 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1654mV
OVER 150


2025-07-31 23:17:48:405 ==>> 本次取值间隔时间:172ms
2025-07-31 23:17:48:423 ==>> 【AD_V20电压】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:48:426 ==>> 检测【拉低OUTPUT2】
2025-07-31 23:17:48:430 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 23:17:48:526 ==>> 3A A3 02 00 A3 


2025-07-31 23:17:48:556 ==>> [D][05:18:08][COMM]read battery soc:255


2025-07-31 23:17:48:616 ==>> OFF_OUT2
OVER 150


2025-07-31 23:17:48:730 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 23:17:48:734 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 23:17:48:737 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:17:48:931 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:17:49:009 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 23:17:49:014 ==>> 检测【拉高OUTPUT2】
2025-07-31 23:17:49:016 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 23:17:49:126 ==>> $GBGGA,151752.982,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,41,3,,,39,25,,,39,39,,,39,1*4F

$GBGSV,5,2,19,34,,,39,60,,,38,41,,,38,59,,,38,1*70

$GBGSV,5,3,19,43,,,38,23,,,37,11,,,37,7,,,37,1*46

$GBGSV,5,4,19,10,,,37,16,,,37,1,,,34,33,,,34,1*48

$GBGSV,5,5,19,32,,,32,5,,,31,9,,,37,1*74

$GBRMC,151752.982,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151752.982,0.000,1531.637,1531.637,48.982,2097152,2097152,2097152*58

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 23:17:49:282 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 23:17:49:287 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 23:17:49:291 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 23:17:49:522 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:17:49:558 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 23:17:49:561 ==>> 检测【预留IO LED功能输出】
2025-07-31 23:17:49:569 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 23:17:49:762 ==>> $GBGGA,151753.582,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,40,,,41,3,,,39,25,,,39,39,,,39,1*4F

$GBGSV,5,2,19,34,,,39,60,,,38,41,,,38,59,,,38,1*70

$GBGSV,5,3,19,43,,,38,23,,,37,11,,,37,7,,,37,1*46

$GBGSV,5,4,19,16,,,37,10,,,36,1,,,34,33,,,34,1*49

$GBGSV,5,5,19,32,,,32,5,,,30,12,,,17,1*4D

$GBRMC,151753.582,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151753.582,0.000,1483.888,1483.888,47.585,2097152,2097152,2097152*51

[W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:10][COMM]oneline display set 1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 23:17:49:839 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 23:17:49:842 ==>> 检测【AD_V21电压】
2025-07-31 23:17:49:845 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 23:17:49:912 ==>> 本次取值间隔时间:68ms
2025-07-31 23:17:49:928 ==>> 1A A1 20 00 00 
Get AD_V21 1647mV
OVER 150


2025-07-31 23:17:50:047 ==>> 本次取值间隔时间:130ms
2025-07-31 23:17:50:076 ==>> 【AD_V21电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:17:50:081 ==>> 检测【关闭仪表供电2】
2025-07-31 23:17:50:085 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:17:50:322 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:10][COMM]set POWER 0
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 23:17:50:356 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:17:50:359 ==>> 检测【关闭仪表指令模式】
2025-07-31 23:17:50:362 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 23:17:50:517 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 23:17:50:634 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 23:17:50:638 ==>> 检测【打开AccKey2供电】
2025-07-31 23:17:50:641 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 23:17:50:728 ==>> [D][05:18:10][COMM]read battery soc:255
$GBGGA,151754.562,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,40,,,41,3,,,39,25,,,39,39,,,39,1*47

$GBGSV,6,2,21,34,,,39,60,,,38,41,,,38,59,,,38,1*78

$GBGSV,6,3,21,43,,,38,7,,,38,23,,,37,11,,,37,1*41

$GBGSV,6,4,21,16,,,37,10,,,36,1,,,35,6,,,34,1*76

$GBGSV,6,5,21,2,,,33,33,,,33,32,,,33,12,,,31,1*44

$GBGSV,6,6,21,5,,,30,1*43

$GBRMC,151754.562,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151754.562,0.000,1506.321,1506.321,48.192,2097152,2097152,2097152*55



2025-07-31 23:17:50:803 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 23:17:50:912 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 23:17:50:917 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 23:17:50:922 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:51:231 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:11][COMM]adc read out 24v adc:1295  volt:32754 mv
[D][05:18:11][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:11][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:11][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:11][COMM]adc read battery ts volt:6 mv
[D][05:18:11][COMM]adc read in 24v adc:1278  volt:32324 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:17:51:444 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32754mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:17:51:451 ==>> 检测【关闭AccKey2供电2】
2025-07-31 23:17:51:454 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:17:51:711 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
$GBGGA,151755.542,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,3,,,39,25,,,39,39,,,39,1*44

$GBGSV,6,2,22,34,,,39,60,,,38,41,,,38,59,,,38,1*7B

$GBGSV,6,3,22,43,,,38,7,,,38,23,,,37,11,,,37,1*42

$GBGSV,6,4,22,16,,,37,10,,,36,1,,,35,6,,,34,1*75

$GBGSV,6,5,22,2,,,33,33,,,33,32,,,33,12,,,31,1*47

$GBGSV,6,6,22,5,,,30,4,,,28,1*7E

$GBRMC,151755.542,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151755.542,0.000,1490.639,1490.639,47.712,2097152,2097152,2097152*57



2025-07-31 23:17:51:978 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:17:51:981 ==>> 该项需要延时执行
2025-07-31 23:17:52:685 ==>> [D][05:18:12][COMM]read battery soc:255
$GBGGA,151756.522,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,3,,,39,25,,,39,39,,,39,1*44

$GBGSV,6,2,22,34,,,39,60,,,39,59,,,39,41,,,38,1*7B

$GBGSV,6,3,22,43,,,38,7,,,38,23,,,37,11,,,37,1*42

$GBGSV,6,4,22,16,,,37,10,,,36,1,,,35,6,,,35,1*74

$GBGSV,6,5,22,33,,,34,2,,,33,32,,,33,12,,,32,1*43

$GBGSV,6,6,22,5,,,29,4,,,29,1*77

$GBRMC,151756.522,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151756.522,0.000,1500.058,1500.058,48.009,2097152,2097152,2097152*50



2025-07-31 23:17:53:662 ==>> $GBGGA,151757.502,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,40,,,41,3,,,39,25,,,39,39,,,39,1*44

$GBGSV,6,2,22,34,,,39,59,,,39,60,,,38,41,,,38,1*7A

$GBGSV,6,3,22,43,,,38,7,,,38,23,,,37,11,,,37,1*42

$GBGSV,6,4,22,16,,,37,10,,,36,6,,,35,1,,,34,1*75

$GBGSV,6,5,22,33,,,34,32,,,33,2,,,32,12,,,32,1*42

$GBGSV,6,6,22,5,,,29,4,,,29,1*77

$GBRMC,151757.502,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151757.502,0.000,1494.407,1494.407,47.831,2097152,2097152,2097152*5F



2025-07-31 23:17:54:665 ==>> $GBGGA,151758.502,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,3,,,39,25,,,39,1*4B

$GBGSV,6,2,23,34,,,39,60,,,39,59,,,38,41,,,38,1*7B

$GBGSV,6,3,23,43,,,38,7,,,38,23,,,38,11,,,38,1*43

$GBGSV,6,4,23,16,,,37,10,,,36,6,,,35,1,,,35,1*75

$GBGSV,6,5,23,33,,,34,24,,,34,32,,,33,2,,,33,1*41

$GBGSV,6,6,23,12,,,32,5,,,30,4,,,29,1*7C

$GBRMC,151758.502,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151758.502,0.000,1501.528,1501.528,48.052,2097152,2097152,2097152*52

[D][05:18:14][COMM]read battery soc:255


2025-07-31 23:17:54:986 ==>> 此处延时了:【3000】毫秒
2025-07-31 23:17:54:991 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 23:17:54:994 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:55:334 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:15][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:15][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:15][COMM]adc read throttle adc:3  volt:3 mv
[D][05:18:15][COMM]adc read battery ts volt:14 mv
[D][05:18:15][COMM]adc read in 24v adc:1276  volt:32273 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:17:55:519 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【50mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 23:17:55:523 ==>> 检测【打开AccKey1供电】
2025-07-31 23:17:55:526 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 23:17:55:668 ==>> $GBGGA,151759.502,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,39,3,,,39,25,,,39,1*45

$GBGSV,6,2,23,34,,,39,60,,,39,59,,,38,41,,,38,1*7B

$GBGSV,6,3,23,43,,,38,7,,,38,11,,,38,23,,,37,1*4C

$GBGSV,6,4,23,16,,,37,10,,,36,6,,,35,1,,,35,1*75

$GBGSV,6,5,23,33,,,34,24,,,34,32,,,33,2,,,32,1*40

$GBGSV,6,6,23,12,,,32,5,,,30,4,,,30,1*74

$GBRMC,151759.502,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151759.502,0.000,1497.918,1497.918,47.932,2097152,2097152,2097152*53



2025-07-31 23:17:55:743 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 23:17:55:792 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 23:17:55:796 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 23:17:55:798 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:17:55:926 ==>> 1A A1 00 40 00 
Get AD_V14 2683mV
OVER 150


2025-07-31 23:17:56:047 ==>> 原始值:【2683】, 乘以分压基数【2】还原值:【5366】
2025-07-31 23:17:56:077 ==>> 【读取AccKey1电压(ADV14)前】通过,【5366mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:17:56:083 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 23:17:56:087 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:56:432 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:16][COMM]adc read throttle adc:2  volt:2 mv
[D][05:18:16][COMM]adc read battery ts volt:6 mv
[D][05:18:16][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:17:56:618 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 23:17:56:624 ==>> 检测【关闭AccKey1供电2】
2025-07-31 23:17:56:634 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 23:17:56:675 ==>> $GBGGA,151800.502,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,39,3,,,39,25,,,39,1*45

$GBGSV,6,2,23,34,,,39,60,,,39,59,,,39,41,,,38,1*7A

$GBGSV,6,3,23,43,,,38,7,,,38,11,,,37,23,,,37,1*43

$GBGSV,6,4,23,16,,,37,10,,,36,6,,,35,1,,,35,1*75

$GBGSV,6,5,23,33,,,34,24,,,34,32,,,33,2,,,32,1*40

$GBGSV,6,6,23,12,,,32,5,,,30,4,,,30,1*74

$GBRMC,151800.502,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151800.502,0.000,1497.919,1497.919,47.933,2097152,2097152,2097152*51

[D][05:18:16][COMM]read battery soc:255


2025-07-31 23:17:56:750 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:17][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 23:17:56:888 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 23:17:56:894 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 23:17:56:897 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 23:17:57:021 ==>> 1A A1 00 40 00 
Get AD_V14 2686mV
OVER 150


2025-07-31 23:17:57:141 ==>> 原始值:【2686】, 乘以分压基数【2】还原值:【5372】
2025-07-31 23:17:57:159 ==>> 【读取AccKey1电压(ADV14)后】通过,【5372mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 23:17:57:163 ==>> 检测【打开WIFI(2)】
2025-07-31 23:17:57:168 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:17:57:338 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:17:57:432 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:17:57:436 ==>> 检测【转刹把供电】
2025-07-31 23:17:57:439 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:17:57:700 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
$GBGGA,151801.502,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,39,3,,,39,25,,,39,1*45

$GBGSV,6,2,23,34,,,39,60,,,39,59,,,39,41,,,38,1*7A

$GBGSV,6,3,23,43,,,38,7,,,38,11,,,38,23,,,38,1*43

$GBGSV,6,4,23,16,,,37,10,,,36,6,,,35,1,,,35,1*75

$GBGSV,6,5,23,24,,,35,33,,,34,32,,,33,2,,,32,1*41

$GBGSV,6,6,23,12,,,32,5,,,31,4,,,30,1*75

$GBRMC,151801.502,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151801.502,0.000,751.160,751.160,686.955,2097152,2097152,2097152*6B



2025-07-31 23:17:57:962 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 23:17:57:967 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 23:17:57:974 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:17:58:064 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:17:58:124 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2428mV
OVER 150


2025-07-31 23:17:58:214 ==>> +WIFISCAN:4,0,F88C21BCF57D,-30
+WIFISCAN:4,1,F42A7D1297A3,-65
+WIFISCAN:4,2,44A1917CA62B,-76
+WIFISCAN:4,3,CC057790A5C1,-79

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 23:17:58:229 ==>> 原始值:【2428】, 乘以分压基数【2】还原值:【4856】
2025-07-31 23:17:58:247 ==>> 【读取AD_V15电压(前)】通过,【4856mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:17:58:250 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 23:17:58:255 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 23:17:58:349 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:17:58:426 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:18][GNSS]recv submsg id[3]
1A A1 01 00 00 
Get AD_V16 2455mV
OVER 150


2025-07-31 23:17:58:501 ==>> 原始值:【2455】, 乘以分压基数【2】还原值:【4910】
2025-07-31 23:17:58:526 ==>> 【读取AD_V16电压(前)】通过,【4910mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 23:17:58:531 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 23:17:58:562 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:58:846 ==>> $GBGGA,151802.502,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,40,,,41,60,,,39,3,,,39,39,,,39,1*43

$GBGSV,6,2,24,59,,,39,34,,,39,25,,,39,7,,,38,1*4E

$GBGSV,6,3,24,11,,,38,43,,,38,41,,,38,16,,,37,1*7F

$GBGSV,6,4,24,23,,,37,10,,,36,6,,,35,24,,,35,1*43

$GBGSV,6,5,24,1,,,35,33,,,34,32,,,33,2,,,32,1*71

$GBGSV,6,6,24,12,,,32,5,,,30,4,,,30,44,,,36,1*76

$GBRMC,151802.502,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151802.502,0.000,749.364,749.364,685.313,2097152,2097152,2097152*63

[D][05:18:18][COMM]read battery soc:255
[W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:19][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:19][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:19][COMM]adc read battery ts volt:7 mv
[D][05:18:19][COMM]adc read in 24v adc:1278  volt:32324 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3103  volt:5454 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D

2025-07-31 23:17:58:891 ==>> ][05:18:19][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:17:59:062 ==>> 【转刹把供电电压(主控ADC)】通过,【5454mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 23:17:59:067 ==>> 检测【转刹把供电电压】
2025-07-31 23:17:59:072 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:17:59:326 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:19][COMM]adc read battery ts volt:7 mv
[D][05:18:19][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3103  volt:5454 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 23:17:59:603 ==>> 【转刹把供电电压】通过,【5454mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 23:17:59:607 ==>> 检测【关闭转刹把供电2】
2025-07-31 23:17:59:610 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:17:59:690 ==>> $GBGGA,151803.502,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,34,,,39,25,,,39,7,,,38,59,,,38,1*48

$GBGSV,6,3,23,43,,,38,23,,,38,41,,,38,16,,,37,1*79

$GBGSV,6,4,23,11,,,37,10,,,36,24,,,35,6,,,35,1*45

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151803.502,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151803.502,0.000,751.150,751.150,686.945,2097152,2097152,2097152*68



2025-07-31 23:17:59:795 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:17:59:872 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 23:17:59:877 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 23:17:59:881 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:17:59:975 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:00:005 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:18:00:080 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:00:185 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:00:290 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:00:395 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:00:500 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:00:609 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:00:718 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:00:763 ==>> $GBGGA,151804.502,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,34,,,39,25,,,39,7,,,38,59,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,32,4,,,31,5,,,30,1*44

$GBRMC,151804.502,V,,,,,,,310725,0.1,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151804.502,0.000,751.156,751.156,686.950,2097152,2097152,2097152*6B

[W][05:18:20][COMM]>>>>>Input command = ?<<<<
[W][05:18:20][COMM]>>>>>Input command = ?<<<<
[D][05:18:20][COMM]read battery soc:255
[W][05:18:21][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:18:00:822 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:00:837 ==>> [W][05:18:21][COMM]>>>>>Input command = ?<<<<


2025-07-31 23:18:00:927 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:00:934 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 23:18:01:032 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 23:18:01:122 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 23:18:01:155 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:18:01:162 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 23:18:01:185 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 23:18:01:258 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 23:18:01:287 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 23:18:01:317 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 23:18:01:381 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:18:01:387 ==>> 检测【拉高OUTPUT3】
2025-07-31 23:18:01:393 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 23:18:01:527 ==>> 3A A3 03 01 A3 


2025-07-31 23:18:01:632 ==>> ON_OUT3
OVER 150
$GBGGA,151805.502,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,3,,,39,39,,,39,34,,,39,1*45

$GBGSV,6,2,23,25,,,39,7,,,38,60,,,38,59,,,38,1*48



2025-07-31 23:18:01:692 ==>> 
$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,32,5,,,30,4,,,30,1*45

$GBRMC,151805.502,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151805.502,0.000,748.459,748.459,684.484,2097152,2097152,2097152*6C



2025-07-31 23:18:01:907 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 23:18:01:911 ==>> 检测【拉高OUTPUT4】
2025-07-31 23:18:01:914 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 23:18:02:025 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 23:18:02:178 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 23:18:02:183 ==>> 检测【拉高OUTPUT5】
2025-07-31 23:18:02:187 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 23:18:02:316 ==>> 3A A3 05 01 A3 


2025-07-31 23:18:02:421 ==>> ON_OUT5
OVER 150


2025-07-31 23:18:02:474 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 23:18:02:478 ==>> 检测【左刹电压测试1】
2025-07-31 23:18:02:496 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:18:02:812 ==>> $GBGGA,151806.502,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,7,,,39,60,,,39,3,,,39,1*79

$GBGSV,6,2,23,39,,,39,34,,,39,25,,,39,59,,,38,1*74

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,31,5,,,30,4,,,30,1*46

$GBRMC,151806.502,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151806.502,0.000,749.365,749.365,685.313,2097152,2097152,2097152*67

[W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3161  volt:5556 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1713  volt:2258 mv
[D][05:18:22][COMM]adc read right brake adc:1716  volt:2262 mv
[D][05:18:22][COMM]adc read throttle adc:1716  volt:2262 mv
[D][05:18:22][COMM]adc read battery ts volt:7 mv
[D][05:18:22][COMM]adc read in 24v adc:1275  volt:32248 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]read battery soc:255
[D][05:18:23][COMM]arm_hub adc read vbat adc:2399  volt:3865 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[

2025-07-31 23:18:02:842 ==>> D][05:18:23][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 23:18:03:012 ==>> 【左刹电压测试1】通过,【2258】符合目标值【2250】至【2500】要求!
2025-07-31 23:18:03:017 ==>> 检测【右刹电压测试1】
2025-07-31 23:18:03:034 ==>> 【右刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 23:18:03:038 ==>> 检测【转把电压测试1】
2025-07-31 23:18:03:053 ==>> 【转把电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 23:18:03:057 ==>> 检测【拉低OUTPUT3】
2025-07-31 23:18:03:063 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 23:18:03:115 ==>> 3A A3 03 00 A3 


2025-07-31 23:18:03:220 ==>> OFF_OUT3
OVER 150


2025-07-31 23:18:03:329 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 23:18:03:333 ==>> 检测【拉低OUTPUT4】
2025-07-31 23:18:03:359 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 23:18:03:418 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 23:18:03:608 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 23:18:03:616 ==>> 检测【拉低OUTPUT5】
2025-07-31 23:18:03:624 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 23:18:03:690 ==>> $GBGGA,151807.502,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,7,,,39,60,,,39,1*4E

$GBGSV,6,2,23,3,,,39,34,,,39,25,,,39,59,,,38,1*4D

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,31,5,,,31,4,,,31,1*46

$GBRMC,151807.502,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151807.502,0.000,752.958,752.958,688.598,2097152,2097152,2097152*6E



2025-07-31 23:18:03:720 ==>> 3A A3 05 00 A3 


2025-07-31 23:18:03:825 ==>> OFF_OUT5
OVER 150


2025-07-31 23:18:03:890 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 23:18:03:894 ==>> 检测【左刹电压测试2】
2025-07-31 23:18:03:901 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 23:18:04:230 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3165  volt:5563 mv
[D][05:18:24][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:24][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:24][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:24][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:24][COMM]adc read battery ts volt:9 mv
[D][05:18:24][COMM]adc read in 24v adc:1285  volt:32501 mv
[D][05:18:24][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2400  volt:3867 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 23:18:04:445 ==>> 【左刹电压测试2】通过,【1】符合目标值【0】至【50】要求!
2025-07-31 23:18:04:454 ==>> 检测【右刹电压测试2】
2025-07-31 23:18:04:475 ==>> 【右刹电压测试2】通过,【2】符合目标值【0】至【50】要求!
2025-07-31 23:18:04:479 ==>> 检测【转把电压测试2】
2025-07-31 23:18:04:498 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 23:18:04:507 ==>> 检测【晶振检测】
2025-07-31 23:18:04:514 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 23:18:04:768 ==>> $GBGGA,151808.502,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,3,,,39,39,,,39,34,,,39,1*45

$GBGSV,6,2,23,25,,,39,7,,,38,60,,,38,59,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,32,1*47

$GBGSV,6,6,23,2,,,31,4,,,31,5,,,30,1*47

$GBRMC,151808.502,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151808.502,0.000,747.561,747.561,683.664,2097152,2097152,2097152*6A

[D][05:18:25][COMM]read battery soc:255
[D][05:18:25][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:25][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:25][COMM][lf state:1][hf state:1]


2025-07-31 23:18:05:045 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 23:18:05:050 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 23:18:05:056 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:18:05:132 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1662mV
Get AD_V4 1652mV
Get AD_V5 2789mV
Get AD_V6 1993mV
Get AD_V7 1097mV
OVER 150


2025-07-31 23:18:05:317 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 23:18:05:322 ==>> 检测【检测BootVer】
2025-07-31 23:18:05:326 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:05:742 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539492
[D][05:18:25][FCTY]HardwareID  = 867222087569440
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:25][FCTY]Bat         = 3944 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11900 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1281
[D][05:18:25][FCTY]Acckey1 vol = 5568 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18

2025-07-31 23:18:05:832 ==>> :25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3704 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,151809.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,34,,,39,25,,,39,7,,,38,59,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151809.502,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151809.502,0.000,751.150,751.150,686.945,2097152,2097152,2097152*62



2025-07-31 23:18:05:859 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 23:18:05:865 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 23:18:05:883 ==>> 检测【检测固件版本】
2025-07-31 23:18:05:888 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 23:18:05:898 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 23:18:05:905 ==>> 检测【检测蓝牙版本】
2025-07-31 23:18:05:924 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 23:18:05:928 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 23:18:05:933 ==>> 检测【检测MoBikeId】
2025-07-31 23:18:05:943 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 23:18:05:947 ==>> 提取到MoBikeId:9999999999
2025-07-31 23:18:05:963 ==>> 检测【检测蓝牙地址】
2025-07-31 23:18:05:968 ==>> 取到目标值:CD21DF6D712B
2025-07-31 23:18:05:984 ==>> 【检测蓝牙地址】通过,【CD21DF6D712B】符合目标值【】要求!
2025-07-31 23:18:05:989 ==>> 提取到蓝牙地址:CD21DF6D712B
2025-07-31 23:18:06:015 ==>> 检测【BOARD_ID】
2025-07-31 23:18:06:019 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 23:18:06:023 ==>> 检测【检测充电电压】
2025-07-31 23:18:06:040 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 23:18:06:045 ==>> 检测【检测VBUS电压1】
2025-07-31 23:18:06:060 ==>> 【检测VBUS电压1】通过,【11900mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 23:18:06:065 ==>> 检测【检测充电电流】
2025-07-31 23:18:06:088 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 23:18:06:094 ==>> 检测【检测IMEI】
2025-07-31 23:18:06:101 ==>> 取到目标值:867222087569440
2025-07-31 23:18:06:122 ==>> 【检测IMEI】通过,【867222087569440】符合目标值【】要求!
2025-07-31 23:18:06:127 ==>> 提取到IMEI:867222087569440
2025-07-31 23:18:06:131 ==>> 检测【检测IMSI】
2025-07-31 23:18:06:157 ==>> 取到目标值:460130071539492
2025-07-31 23:18:06:170 ==>> 【检测IMSI】通过,【460130071539492】符合目标值【】要求!
2025-07-31 23:18:06:175 ==>> 提取到IMSI:460130071539492
2025-07-31 23:18:06:200 ==>> 检测【校验网络运营商(移动)】
2025-07-31 23:18:06:204 ==>> 取到目标值:460130071539492
2025-07-31 23:18:06:217 ==>> 【校验网络运营商(移动)】通过,【460130071539492】符合目标值【】要求!
2025-07-31 23:18:06:221 ==>> 检测【打开CAN通信】
2025-07-31 23:18:06:225 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 23:18:06:321 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 23:18:06:475 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:18:06:480 ==>> 检测【检测CAN通信】
2025-07-31 23:18:06:487 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 23:18:06:702 ==>> can send success
$GBGGA,151810.502,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,59,,,39,34,,,39,25,,,39,7,,,38,1*49

$GBGSV,6,3,23,11,,,38,43,,,38,41,,,38,16,,,37,1*78

$GBGSV,6,4,23,23,,,37,10,,,36,24,,,35,6,,,35,1*44

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,32,,,33,1*46

$GBGSV,6,6,23,2,,,32,5,,,31,4,,,31,1*45

$GBRMC,151810.502,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151810.502,0.000,752.051,752.051,687.769,2097152,2097152,2097152*6B

[D][05:18:27][COMM]read battery soc:255
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
                                                                                           标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 

2025-07-31 23:18:06:717 ==>> 58 


2025-07-31 23:18:06:752 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 23:18:06:757 ==>> 检测【关闭CAN通信】
2025-07-31 23:18:06:778 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 23:18:06:786 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 23:18:06:823 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 23:18:07:027 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 23:18:07:032 ==>> 检测【打印IMU STATE】
2025-07-31 23:18:07:039 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:18:07:219 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:27][COMM]YAW data: 32763[32763]
[D][05:18:27][COMM]pitch:-66 roll:0
[D][05:18:27][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 23:18:07:297 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:18:07:302 ==>> 检测【六轴自检】
2025-07-31 23:18:07:312 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 23:18:07:510 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 23:18:07:971 ==>> $GBGGA,151811.502,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,59,,,39,34,,,39,25,,,39,7,,,38,1*49

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,2,,,33,12,,,33,1*75

$GBGSV,6,6,23,32,,,33,5,,,31,4,,,31,1*77

$GBRMC,151811.502,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151811.502,0.000,753.848,753.848,689.412,2097152,2097152,2097152*6B



2025-07-31 23:18:08:698 ==>> $GBGGA,151812.502,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,39,,,40,60,,,39,3,,,39,1*4A

$GBGSV,6,2,23,59,,,39,34,,,39,25,,,39,7,,,38,1*49

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,2,,,33,12,,,33,1*75

$GBGSV,6,6,23,32,,,33,5,,,31,4,,,31,1*77

$GBRMC,151812.502,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151812.502,0.000,754.750,754.750,690.237,2097152,2097152,2097152*61

[D][05:18:29][COMM]read battery soc:255


2025-07-31 23:18:09:213 ==>> [D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:18:09:318 ==>> [D][05:18

2025-07-31 23:18:09:363 ==>> :29][COMM]Main Task receive event:142
[D][05:18:29][COMM]###### 40726 imu self test OK ######
[D][05:18:29][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-10,-12,4070]
[D][05:18:29][COMM]Main Task receive event:142 finished processing


2025-07-31 23:18:09:382 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 23:18:09:398 ==>> 检测【打印IMU STATE2】
2025-07-31 23:18:09:404 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 23:18:09:713 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:29][COMM]YAW data: 32763[32763]
[D][05:18:29][COMM]pitch:-66 roll:0
[D][05:18:29][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,151813.502,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,60,,,39,3,,,39,39,,,39,1*44

$GBGSV,6,2,23,34,,,39,25,,,39,7,,,38,59,,,38,1*48

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,2,,,32,1*74

$GBGSV,6,6,23,32,,,32,5,,,31,4,,,31,1*76

$GBRMC,151813.502,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151813.502,0.000,751.154,751.154,686.949,2097152,2097152,2097152*65



2025-07-31 23:18:09:914 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 23:18:09:931 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 23:18:09:935 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:18:10:019 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:18:10:189 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:18:10:199 ==>> 检测【检测VBUS电压2】
2025-07-31 23:18:10:218 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:10:234 ==>> [D][05:18:30][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:30][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:18:10:591 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539492
[D][05:18:30][FCTY]HardwareID  = 867222087569440
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 11800 mv
[D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:30][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 7, adc = 304
[D][05:18:30][FCTY]Acckey1 vol = 5545 mv, Acckey2 vol = 0 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b

2025-07-31 23:18:10:681 ==>> 5b1
[D][05:18:30][FCTY]Bat1         = 3704 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:18:10:717 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:11:077 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539492
[D][05:18:31][FCTY]HardwareID  = 867222087569440
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:31][FCTY]Bat         = 3944 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 11800 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 4, adc = 167
[D][05:18:31][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][

2025-07-31 23:18:11:122 ==>> 05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3704 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:18:11:245 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:11:589 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071539492
[D][05:18:31][FCTY]HardwareID  = 867222087569440
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:31][FCTY]Bat         = 3944 mv
[D][05:18:31][FCTY]Current     = 50 ma
[D][05:18:31][FCTY]VBUS        = 11800 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 3, adc = 143
[D][05:18:31][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3704 mv
[D][0

2025-07-31 23:18:11:694 ==>> 5:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 23:18:11:776 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:12:470 ==>> [D][05:18:32][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[W][05:18:32][GNSS]stop locating
[D][05:18:32][GNSS]stop event:8
[D][05:18:32][GNSS]all continue location stop
[W][05:18:32][GNSS]sing locating running
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]index:0
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:

2025-07-31 23:18:12:575 ==>> 32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900005]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][PROT]index:0 1629955112
[D][05:18:32][PROT]is_send:0
[D][05:18:32][PROT]sequence_num:4
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x2
[D][05:18:32][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]sending traceid [9999999999900005]
[D][05:18:32][PROT]Send_TO_M2M [1629955112]
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:32][SAL ]sock send credit cnt[6]
[D][05:18:32][SAL ]sock send ind credit cnt[6]
[D][05:18:32][M2M ]m2m send data len[198]
[D][05:18:32][SAL ]Cellular task submsg id[10]
[D][05:18:3

2025-07-31 23:18:12:680 ==>> 2][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:32][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:32][CAT1]gsm read msg sub id: 15
[D][05:18:32][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:32][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B52437FE14F0C3C20F7AEF887C548A3238639470A4A114D221C6801541E712A610C9FB7E1643F795D17DFB507B71AB386C1F5C610BA31705BA44B8691DC738E88C559FF98A1FA2CE4A29490E9FCCEB830751DA
[W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130071539492
[D][05:18:32][FCTY]HardwareID  = 867222087569440
[D][05:18:32][FCTY]MoBikeID 

2025-07-31 23:18:12:785 ==>>    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:32][FCTY]Bat         = 3684 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 11800 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 3, adc = 128
[D][05:18:32][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3704 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][CAT1]<<< 
SEND OK

[D][05:18:32][CAT1]exec over: func id: 15, ret: 11
[D][05:18:32][CAT1]sub id: 15, ret: 11

[D][05:18:32][SAL ]Cellular task submsg id[68]
[D][05:18:32][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][0

2025-07-31 23:18:12:833 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:12:891 ==>> 5:18:32][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:32][M2M ]g_m2m_is_idle become true
[D][05:18:32][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:32][PROT]M2M Send ok [1629955112]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:18:13:178 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130071539492
[D][05:18:33][FCTY]HardwareID  = 867222087569440
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:33][FCTY]Bat         = 3684 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 11800 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 2, adc = 96
[D][05:18:33][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 0 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33

2025-07-31 23:18:13:223 ==>> ][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3704 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 23:18:13:363 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:13:743 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130071539492
[D][05:18:33][FCTY]HardwareID  = 867222087569440
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:18:33][FCTY]Bat         = 3624 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 5000 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 2, adc = 94
[D][05:18:33][FCTY]Acckey1 vol = 5563 mv, Acckey2 vol = 75 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_

2025-07-31 23:18:13:833 ==>> VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3704 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
$GBGGA,151817.502,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,40,,,41,34,,,40,60,,,39,3,,,39,1*47

$GBGSV,6,2,23,39,,,39,59,,,39,25,,,39,7,,,38,1*44

$GBGSV,6,3,23,11,,,38,43,,,38,23,,,38,41,,,38,1*71

$GBGSV,6,4,23,16,,,37,10,,,36,24,,,35,6,,,35,1*42

$GBGSV,6,5,23,1,,,35,33,,,34,12,,,33,2,,,32,1*74

$GBGSV,6,6,23,32,,,32,4,,,31,5,,,30,1*77

$GBRMC,151817.502,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,151817.502,0.000,752.062,752.062,687.779,2097152,2097152,2097152*6D



2025-07-31 23:18:13:896 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 23:18:13:906 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 23:18:13:927 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:18:14:032 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 23:18:14:092 ==>> [D][05:18:34][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2


2025-07-31 23:18:14:167 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 23:18:14:177 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:18:14:183 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 23:18:14:197 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:18:14:318 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:18:14:466 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 23:18:14:475 ==>> 检测【打开WIFI(3)】
2025-07-31 23:18:14:502 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:18:15:368 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:34][CAT1]gsm read msg sub id: 12
[D][05:18:34][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:35][CAT1]<<< 
OK

[D][05:18:35][CAT1]exec over: func id: 12, ret: 6
$GBGGA,151814.509,2301.2571877,N,11421.9432316,E,1,16,0.73,79.046,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,10,25,11,34,43,41,23,1.31,0.73,1.08,4*0C

$GBGSA,A,3,12,24,33,32,,,,,,,,,1.31,0.73,1.08,4*02

$GBGSV,6,1,23,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*72

$GBGSV,6,2,23,16,63,17,37,3,60,190,39,10,56,220,36,59,52,129,38,1*79

$GBGSV,6,3,23,25,51,19,39,11,51,117,38,1,48,125,35,34,47,78,40,1*49

$GBGSV,6,4,23,43,46,162,38,2,45,237,32,60,41,239,39,41,33,243,38,1*40

$GBGSV,6,5,23,4,32,111,31,23,31,313,38,12,26,54,33,5,21,256,31,1*48

$GBGSV,6,6,23,24,18,78,35,33,17,191,34,32,16,300,32,1*7D

$GBRMC,151814.509,A,2301.2571877,N,11421.9432316,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[D][05:18:35][GNSS]HD8040 GPS
[D][05:18:35][GNSS]GPS diff_sec 124019979, report 0x42 frame
$GBGST,151814.509,1.145,0.294,0.301,0.411,2.408,2.499,6.849*72

[D][05:18:35][COMM]Main Task receive event:131
[D][05:18:35][COMM]main task tmp_sleep_event = 80
[D][05:18:35][

2025-07-31 23:18:15:473 ==>> COMM]index:0,power_mode:0xFF
[D][05:18:35][COMM]index:1,sound_mode:0xFF
[D][05:18:35][COMM]index:2,gsensor_mode:0xFF
[D][05:18:35][COMM]index:3,report_freq_mode:0xFF
[D][05:18:35][COMM]index:4,report_period:0xFF
[D][05:18:35][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:35][COMM]index:6,normal_reset_period:0xFF
[D][05:18:35][COMM]index:7,spock_over_speed:0xFF
[D][05:18:35][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:35][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:35][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:35][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:35][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:35][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:35][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:35][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:35][COMM]index:16,imu_config_params:0xFF
[D][05:18:35][COMM]index:17,long_connect_params:0xFF
[D][05:18:35][COMM]index:18,detain_mark:0xFF
[D][05:18:35][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:35][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:35][COMM]index:21,mc_mode:0xFF
[D][05:18:35][COMM]index:22,S_mode:0xFF
[D][05:18:35][COMM]index:23,overweight:0xFF
[D][05

2025-07-31 23:18:15:507 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:18:15:513 ==>> 检测【扩展芯片hw】
2025-07-31 23:18:15:518 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:18:15:578 ==>> :18:35][COMM]index:24,standstill_mode:0xFF
[D][05:18:35][COMM]index:25,night_mode:0xFF
[D][05:18:35][COMM]index:26,experiment1:0xFF
[D][05:18:35][COMM]index:27,experiment2:0xFF
[D][05:18:35][COMM]index:28,experiment3:0xFF
[D][05:18:35][COMM]index:29,experiment4:0xFF
[D][05:18:35][COMM]index:30,night_mode_start:0xFF
[D][05:18:35][COMM]index:31,night_mode_end:0xFF
[D][05:18:35][COMM]index:33,park_report_minutes:0xFF
[D][05:18:35][COMM]index:34,park_report_mode:0xFF
[D][05:18:35][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:35][COMM]index:38,charge_battery_para: FF
[D][05:18:35][COMM]index:39,multirider_mode:0xFF
[D][05:18:35][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:35][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:35][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:35][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:35][COMM]index:44,riding_duration_config:0xFF
[D][05:18:35][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:35][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:35][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:35][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:35][COMM]index:49,mc_load_startup:0xFF
[D][05:18:35][COMM]index

2025-07-31 23:18:15:683 ==>> :50,mc_tcs_mode:0xFF
[D][05:18:35][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:35][COMM]index:52,traffic_mode:0xFF
[D][05:18:35][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:35][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:35][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:35][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:35][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:35][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:35][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:35][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:35][COMM]index:63,experiment5:0xFF
[D][05:18:35][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:35][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:35][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:35][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:35][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:35][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:35][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:35][COMM]index:72,experiment6:0xFF
[D][05:18:35][COMM]index:73,experiment7:0xFF
[D][05:18:35][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:35][COM

2025-07-31 23:18:15:788 ==>> M]index:75,zero_value_from_server:-1
[D][05:18:35][COMM]index:76,multirider_threshold:255
[D][05:18:35][COMM]index:77,experiment8:255
[D][05:18:35][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:35][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:35][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:35][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:35][COMM]index:83,loc_report_interval:255
[D][05:18:35][COMM]index:84,multirider_threshold_p2:255
[D][05:18:35][COMM]index:85,multirider_strategy:255
[D][05:18:35][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:35][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:35][COMM]index:90,weight_param:0xFF
[D][05:18:35][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:35][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:35][COMM]index:95,current_limit:0xFF
[D][05:18:35][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:35][COMM]index:100,location_mode:0xFF

[W][05:18:35][PROT]remove success[1629955115],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:35][PROT]add success [1629955115],send_path[2],type[4205],priority[0],

2025-07-31 23:18:15:893 ==>> index[1],used[1]
[D][05:18:35][COMM]Main Task receive event:131 finished processing
[D][05:18:35][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:35][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,151815.009,2301.2573344,N,11421.9432756,E,1,16,0.73,80.533,M,-1.770,M,,*53

$GBGSA,A,3,40,07,39,06,16,10,25,11,34,43,41,23,1.31,0.73,1.08,4*0C

$GBGSA,A,3,12,24,33,32,,,,,,,,,1.31,0.73,1.08,4*02

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,60,190,39,10,56,220,36,59,52,129,38,1*7E

$GBGSV,6,3,24,9,51,333,32,25,51,19,39,11,51,117,38,1,48,125,35,1*4E

$GBGSV,6,4,24,34,47,78,40,43,46,162,38,2,45,237,32,60,41,239,39,1*73

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,33,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,32,1*7F

$GBGSV,2,1,06,40,75,187,40,39,66,49,40,25,51,19,37,34,47,78,40,5*48

$GBGSV,2,2,06,43,46,162,38,41,33,243,38,5*74

$GBRMC,151815.009,A,2301.2573344,N,11421.9432756,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,151815.009,2.372,0.601,0.620,0.824,2.313,2.389,5.050*7F



2025-07-31 23:18:16:522 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   

2025-07-31 23:18:16:537 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 23:18:16:627 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:18:16:733 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      , 204
[D][05:18:36][COMM]get soc error
[E][05:18:36][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:36][COMM]report elecbike
[W][05:18:36][PROT]remove success[1629955116],send_path[3],type[0000],priority[0],in

2025-07-31 23:18:16:837 ==>> dex[2],used[0]
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:36][PROT]index:2
[D][05:18:36][PROT]is_send:1
[D][05:18:36][PROT]sequence_num:6
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x3
[D][05:18:36][PROT]msg_type:0x5d03
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]Sending traceid[9999999999900007]
[D][05:18:36][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:36][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:36][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:36][COMM]f:[ec800m_audio_p

2025-07-31 23:18:16:942 ==>> lay_process].l:[975].hexsend, index:1, len:2048
[D][05:18:36][COMM]Receive Bat Lock cmd 0
[D][05:18:36][COMM]VBUS is 1
[W][05:18:36][PROT]add success [1629955116],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:18:36][COMM]Main Task receive event:60 finished processing
[D][05:18:36][COMM]Main Task receive event:61
[D][05:18:36][COMM][D301]:type:3, trace id:280
[D][05:18:36][COMM]id[], hw[000
[D][05:18:36][COMM]get mcMaincircuitVolt error
[D][05:18:36][COMM]get mcSubcircuitVolt error
[D][05:18:36][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get bat work state err
[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:18:36][COMM]Main Task receive event:61 finished processing
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_pro

2025-07-31 23:18:17:048 ==>> cess].l:[975].hexsend, index:2, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[W][05:18:36][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:36][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:36][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:36][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
$GBGGA,151816.000,2301.2575121,N,11421.943

2025-07-31 23:18:17:153 ==>> 3356,E,1,16,0.73,80.932,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,16,10,25,11,34,43,41,23,1.31,0.73,1.08,4*0C

$GBGSA,A,3,12,24,33,32,,,,,,,,,1.31,0.73,1.08,4*02

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,60,190,39,10,56,220,36,59,52,129,38,1*7E

$GBGSV,6,3,24,9,51,333,32,25,51,19,39,11,51,117,38,1,48,125,35,1*4E

$GBGSV,6,4,24,34,47,78,39,43,46,162,38,2,45,237,32,60,41,239,39,1*7D

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,33,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,32,1*7F

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,39,34,47,78,40,5*40

$GBGSV,3,2,10,43,46,162,38,41,33,243,38,23,31,313,38,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,34,32,16,300,34,5*79

$GBRMC,151816.000,A,2301.2575121,N,11421.9433356,E,0.002,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,151816.000,2.471,0.541,

2025-07-31 23:18:17:372 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 23:18:17:378 ==>> 检测【扩展芯片boot】
2025-07-31 23:18:17:409 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 23:18:17:418 ==>> 检测【扩展芯片sw】
2025-07-31 23:18:17:438 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 23:18:17:444 ==>> 检测【检测音频FLASH】
2025-07-31 23:18:17:462 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 23:18:17:506 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    1,23,31,313,37,12,26,54,33,1*7F

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,32,1*7F

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,40,34,47,78,40,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,34,32,16,300,36,5*7B

$GBRMC,151817.000,A,2301.2576180,N,11421.9433575,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151817.000,2.344,0.212,0.216,0.301,1.948,1.971,3.663*73

[D][05:18:37][PROT]CLEAN,SEND:0
[D][05:18:37][PROT]index:2 1629955117
[D][05:18:37][PROT]is_send:0
[D][05:18:37][PROT]sequence_num:6
[D][05:18:37][PROT]retry_timeout:0
[D][05:18:37][PROT]retry_times:3
[D][05:18:37][PROT]send_pat

2025-07-31 23:18:17:611 ==>> h:0x2
[D][05:18:37][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:37][PROT]===========================================================
[W][05:18:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955117]
[D][05:18:37][PROT]===========================================================
[D][05:18:37][PROT]sending traceid [9999999999900007]
[D][05:18:37][PROT]Send_TO_M2M [1629955117]
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:37][SAL ]sock send credit cnt[6]
[D][05:18:37][SAL ]sock send ind credit cnt[6]
[D][05:18:37][M2M ]m2m send data len[198]
[D][05:18:37][SAL ]Cellular task submsg id[10]
[D][05:18:37][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:37][CAT1]gsm read msg sub id: 15
[D][05:18:37][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:37][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88BE0FED77F2D36A2FA48C31074E9F24FB4EFBD3A3C6AD913F1BB9DD39B27E84B83D9D7A309D5C45ABCA2A840570FB787E3E07AD8B96E862F19D30020F4B13F90C423E7695257DF49A22407AAA8337FCC5E6D57C
[W][05:18:37][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:37]

2025-07-31 23:18:17:686 ==>> [COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:37][CAT1]<<< 
SEND OK

[D][05:18:37][CAT1]exec over: func id: 15, ret: 11
[D][05:18:37][CAT1]sub id: 15, ret: 11

[D][05:18:37][SAL ]Cellular task submsg id[68]
[D][05:18:37][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:37][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:37][M2M ]g_m2m_is_idle become true
[D][05:18:37][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:37][PROT]M2M Send ok [1629955117]


2025-07-31 23:18:17:761 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 23:18:18:346 ==>> $GBGGA,151818.000,2301.2576423,N,11421.9434082,E,1,18,0.69,81.373,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,16,10,25,11,59,34,43,60,1.28,0.69,1.08,4*01

$GBGSA,A,3,41,23,12,24,33,32,,,,,,,1.28,0.69,1.08,4*05

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,60,190,39,10,56,220,36,9,51,333,32,1*4B

$GBGSV,6,3,24,25,51,19,39,11,51,117,38,59,49,131,38,1,48,125,35,1*78

$GBGSV,6,4,24,34,47,78,39,43,46,162,38,2,45,237,32,60,41,239,39,1*7D

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,32,1*7F

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151818.000,A,2301.2576423,N,11421.9434082,E,0.002,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,151818.000,2.331,0.173,0.177,0.251,1.879,1.895,3.373*76

[D][05:18:38][COMM]read battery soc:255


2025-07-31 23:18:18:762 ==>> [D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:18:19:329 ==>> $GBGGA,151819.000,2301.2576817,N,11421.9434275,E,1,18,0.69,81.450,M,-1.770,M,,*5C

$GBGSA,A,3,40,07,39,06,16,10,25,11,59,34,43,60,1.28,0.69,1.08,4*01

$GBGSA,A,3,41,23,12,24,33,32,,,,,,,1.28,0.69,1.08,4*05

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,60,190,39,10,56,220,36,9,51,333,32,1*4B

$GBGSV,6,3,24,25,51,19,39,11,51,117,38,59,49,131,38,1,48,125,35,1*78

$GBGSV,6,4,24,34,47,78,39,43,46,162,38,2,45,237,32,60,41,239,38,1*7C

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,32,1*78

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151819.000,A,2301.2576817,N,11421.9434275,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,151819.000,2.341,0.182,0.187,0.265,1.846,1.858,3.183*76



2025-07-31 23:18:19:831 ==>> [D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:40][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:40][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:40][COMM]accel parse set 0
[D][05:18:40][COMM][Audio].l:[1012].open hexlog save
[D][05:18:40][COMM]51164 imu init OK


2025-07-31 23:18:20:351 ==>> $GBGGA,151820.000,2301.2576888,N,11421.9434571,E,1,18,0.69,81.632,M,-1.770,M,,*55

$GBGSA,A,3,40,07,39,06,16,10,25,11,59,34,43,60,1.28,0.69,1.08,4*01

$GBGSA,A,3,41,23,12,24,33,32,,,,,,,1.28,0.69,1.08,4*05

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,60,190,39,10,56,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,25,51,19,39,11,51,117,38,59,49,131,38,1,48,125,35,1*78

$GBGSV,6,4,24,34,47,78,39,43,46,162,38,2,45,237,32,60,41,239,38,1*7C

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,37,12,26,54,33,1*7E

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,32,1*78

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,37,5*7B

$GBRMC,151820.000,A,2301.2576888,N,11421.9434571,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151820.000,2.545,0.213,0.219,0.308,1.943,1.953,3.141*7B

[D][05:18:40][COMM]read battery soc:255


2025-07-31 23:18:21:348 ==>> $GBGGA,151821.000,2301.2577082,N,11421.9434736,E,1,20,0.67,81.731,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,25,11,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,25,51,19,39,11,51,117,38,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,36,2,45,237,32,60,41,239,39,1*7C

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,34,33,17,191,33,32,16,300,32,1*79

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,37,5*7B

$GBRMC,151821.000,A,2301.2577082,N,11421.9434736,E,0.000,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

[D][05:18:41][GNSS][RTK]first enter, init gPos info.
$GBGST,151821.000,2.537,0.210,0.215,0.304,1.919,1.927,3.036*71



2025-07-31 23:18:21:746 ==>> [D][05:18:42][COMM]crc 108B
[D][05:18:42][COMM]flash test ok


2025-07-31 23:18:22:346 ==>> $GBGGA,151822.000,2301.2577408,N,11421.9434719,E,1,20,0.67,81.836,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,25,11,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,25,51,19,39,11,51,117,38,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,36,2,45,237,32,60,41,239,39,1*7C

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,34,33,17,191,33,32,16,300,32,1*79

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151822.000,A,2301.2577408,N,11421.9434719,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151822.000,2.694,0.208,0.214,0.302,1.998,2.006,3.040*77

[D][05:18:42][COMM]read battery soc:255


2025-07-31 23:18:22:521 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 23:18:22:527 ==>> 检测【打开喇叭声音】
2025-07-31 23:18:22:538 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 23:18:22:648 ==>>                                  [D][05:18:42][PROT]index:2 1629955122
[D][05:18:42][PROT]is_send:0
[D][05:18:42][PROT]sequence_num:6
[D][05:18:42][PROT]retry_timeout:0
[D][05:18:42][PROT]retry_times:2
[D][05:18:42][PROT]send_path:0x2
[D][05:18:42][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:42][PROT]===========================================================
[W][05:18:42][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955122]
[D][05:18:42][PROT]===========================================================
[D][05:18:42][PROT]sending traceid [9999999999900007]
[D][05:18:42][PROT]Send_TO_M2M [1629955122]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:42][SAL ]sock send credit cnt[6]
[D][05:18:42][SAL ]sock send ind credit cnt[6]
[D][05:18:42][M2M ]m2m send data len[198]
[D][05:18:42][SAL ]Cellular task submsg id[10]
[D][05:18:42][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:42][CAT1]gsm read msg sub id: 15
[D][05:18:42][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:42][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88BE476D99E2710C3708C16781DFE8A23B6D01C0EE7681C700054FDD9506C3A0E3854E03C

2025-07-31 23:18:22:723 ==>> 009E6083B05AB0D9D5B274C18869E74F487819332823BD5B99B2CEAACE16188A14455A9AE6093AE5CFC395612665436
[D][05:18:42][CAT1]<<< 
SEND OK

[D][05:18:42][CAT1]exec over: func id: 15, ret: 11
[D][05:18:42][CAT1]sub id: 15, ret: 11

[D][05:18:42][SAL ]Cellular task submsg id[68]
[D][05:18:42][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:42][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:42][M2M ]g_m2m_is_idle become true
[D][05:18:42][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:42][PROT]M2M Send ok [1629955122]


2025-07-31 23:18:23:370 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:43][COMM]file:A20 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:43][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]file:A20 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:43][COMM]read file, len:15228, num:4
[D][05:18:43][COMM]--->crc16:0x419c
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel parse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][0

2025-07-31 23:18:23:475 ==>> 5:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play

2025-07-31 23:18:23:580 ==>> _process].l:[975].hexsend, index:5, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
$GBGGA,151823.000,2301.2577343,N,11421.9434640,E,1,20,0.67,81.897,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,34,33,17,191,34,32,16,300,32,1*7E

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,15182

2025-07-31 23:18:23:599 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 23:18:23:615 ==>> 检测【打开大灯控制】
2025-07-31 23:18:23:640 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 23:18:23:655 ==>> 3.000,A,2301.2577343,N,11421.9434640,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151823.000,2.544,0.197,0.202,0.287,1.897,1.905,2.902*7F

[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:43][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:43][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 23:18:23:745 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 23:18:23:869 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 23:18:23:875 ==>> 检测【关闭仪表供电3】
2025-07-31 23:18:23:895 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 23:18:24:004 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:44][COMM]set POWER 0


2025-07-31 23:18:24:145 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 23:18:24:151 ==>> 检测【关闭AccKey2供电3】
2025-07-31 23:18:24:157 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 23:18:24:358 ==>> $GBGGA,151824.000,2301.2577325,N,11421.9434654,E,1,20,0.67,81.901,M,-1.770,M,,*52

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,32,1*78

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,34,32,16,300,37,5*7A

$GBRMC,151824.000,A,2301.2577325,N,11421.9434654,E,0.003,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,151824.000,2.510,0.221,0.227,0.320,1.867,1.874,2.828*7D

[D][05:18:44][COMM]read battery soc:255
[W][05:18:44][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 23:18:24:453 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 23:18:24:464 ==>> 检测【读大灯电压】
2025-07-31 23:18:24:486 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:18:24:613 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[33015]


2025-07-31 23:18:24:737 ==>> 【读大灯电压】通过,【33015mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 23:18:24:743 ==>> 检测【关闭大灯控制2】
2025-07-31 23:18:24:770 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 23:18:24:904 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 23:18:25:016 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 23:18:25:023 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 23:18:25:046 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 23:18:25:345 ==>> $GBGGA,151825.000,2301.2577280,N,11421.9434554,E,1,20,0.67,81.905,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,32,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,32,1*78

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,39,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151825.000,A,2301.2577280,N,11421.9434554,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,151825.000,2.389,0.204,0.208,0.295,1.784,1.791,2.716*7B

[W][05:18:45][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:45][COMM]arm_hub read adc[5],val[92]


2025-07-31 23:18:25:555 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 23:18:25:566 ==>> 检测【打开WIFI(4)】
2025-07-31 23:18:25:590 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 23:18:25:741 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:46][CAT1]<<< 
OK

[D][05:18:46][CAT1]exec over: func id: 12, ret: 6


2025-07-31 23:18:25:787 ==>>                                                             

2025-07-31 23:18:25:882 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 23:18:25:893 ==>> 检测【EC800M模组版本】
2025-07-31 23:18:25:916 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 23:18:26:109 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 23:18:26:350 ==>>                                                                                                 40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,34,2,45,237,32,60,41,239,38,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,32,1*70

$GBGSV,6,6,24,5,21,256,30,24,18,78,34,33,17,191,33,32,16,300,31,1*7B

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,39,41,33,243,39,23,31,313,38,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151826.000,A,2301.2577347,N,11421.9434357,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151826.000,2.436,0.222,0.228,0.318,1.806,1.812,2.701*7E

[D][05:18:46][COMM]read battery soc:255
[D][05:18:46][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:46][CAT1]exec over: func id: 12, ret: 132


2025-07-31 23:18:26:415 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 23:18:26:424 ==>> 检测【配置蓝牙地址】
2025-07-31 23:18:26:447 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 23:18:26:477 ==>>             0,CC057790A5C1,-78
+WIFISCAN:4,1,CC057790A820,-84
+WIFISCAN:4,2,CC057790A4A0,-85
+WIFISCAN:4,3,CC057790A4A1,-85

[D][05:18:46][CAT1]wifi scan report total[4]


2025-07-31 23:18:26:501 ==>>                                       

2025-07-31 23:18:26:591 ==>> [W][05:18:46][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 23:18:26:621 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:CD21DF6D712B>】
2025-07-31 23:18:26:789 ==>> [D][05:18:47][COMM]58170 imu init OK
[D][05:18:47][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:18:26:834 ==>> recv ble 1
recv ble 2
ble set mac ok :cd,21,df,6d,71,2b
enable filters ret : 0

2025-07-31 23:18:26:898 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 23:18:26:904 ==>> 检测【BLETEST】
2025-07-31 23:18:26:929 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 23:18:27:017 ==>> 4A A4 01 A4 4A 


2025-07-31 23:18:27:122 ==>> recv ble 1
recv ble 2
<BSJ*MAC:CD21DF6D712B*RSSI:-21*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9CD21DF6D712B99999OVER 150


2025-07-31 23:18:27:227 ==>> $GBGGA,151827.000,2301.2577406,N,11421.9434256,E,1,20,0.67,81.942,M,-1.770,M,,*56

$GBGSA,A,3,40,07,39,06,16,03,10

2025-07-31 23:18:27:333 ==>> ,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,40,7,69,215,38,39,66,49,39,6,63,13,35,1*74

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,34,2,45,237,32,60,41,239,38,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,32,1*71

$GBGSV,6,6,24,5,21,256,30,24,18,78,34,33,17,191,33,32,16,300,31,1*7B

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151827.000,A,2301.2577406,N,11421.9434256,E,0.003,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,151827.000,2.497,0.208,0.213,0.298,1.839,1.844,2.700*73



2025-07-31 23:18:27:892 ==>> [D][05:18:48][PROT]CLEAN,SEND:2
[D][05:18:48][PROT]index:2 1629955128
[D][05:18:48][PROT]is_send:0
[D][05:18:48][PROT]sequence_num:6
[D][05:18:48][PROT]retry_timeout:0
[D][05:18:48][PROT]retry_times:1
[D][05:18:48][PROT]send_path:0x2
[D][05:18:48][PROT]min_index:2, type:0x5D03, priority:4
[D][05:18:48][PROT]===========================================================
[W][05:18:48][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955128]
[D][05:18:48][PROT]===========================================================
[D][05:18:48][PROT]sending traceid [9999999999900007]
[D][05:18:48][PROT]Send_TO_M2M [1629955128]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:48][SAL ]sock send credit cnt[6]
[D][05:18:48][SAL ]sock send ind credit cnt[6]
[D][05:18:48][M2M ]m2m send data len[198]
[D][05:18:48][SAL ]Cellular task submsg id[10]
[D][05:18:48][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:48][CAT1]gsm read msg sub id: 15
[D][05:18:48][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:48][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88BE9CCA82A42F9D12ECB75ED5DEE5541D6761E3D9DD27ECC95ADF705CFD46B481FC51C3447F03E4542B2FFDF2C6C0BAF4716DE380DF60F9E4ECED572

2025-07-31 23:18:27:967 ==>> E63ABD118BFE9A7970E37547EDCFF6FC2E1B947BBE70EA8
[D][05:18:48][CAT1]<<< 
SEND OK

[D][05:18:48][CAT1]exec over: func id: 15, ret: 11
[D][05:18:48][CAT1]sub id: 15, ret: 11

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:48][M2M ]g_m2m_is_idle become true
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:48][PROT]M2M Send ok [1629955128]
[D][05:18:48][COMM]59180 imu init OK
[D][05:18:48][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 23:18:28:002 ==>> 【BLETEST】通过,【-21dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 23:18:28:009 ==>> 该项需要延时执行
2025-07-31 23:18:28:348 ==>> $GBGGA,151828.000,2301.2577508,N,11421.9434231,E,1,20,0.67,82.013,M,-1.770,M,,*59

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,34,2,45,237,32,60,41,239,38,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,32,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,34,33,17,191,33,32,16,300,31,1*7A

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151828.000,A,2301.2577508,N,11421.9434231,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,151828.000,2.567,0.186,0.189,0.267,1.877,1.882,2.709*7E

[D][05:18:48][COMM]read battery soc:255


2025-07-31 23:18:28:453 ==>>                                                                                                                                                                                                                                                                                                                                                                                             05:18:48][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:48][COMM]accel parse set 0
[D][05:18:48][COMM][Audio].l:[1012].open hexlog save


2025-07-31 23:18:28:802 ==>> [D][05:18:49][COMM]60192 imu init OK


2025-07-31 23:18:29:342 ==>> $GBGGA,151829.000,2301.2577613,N,11421.9434329,E,1,20,0.67,81.988,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,38,59,49,131,38,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,33,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,31,1*7B

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151829.000,A,2301.2577613,N,11421.9434329,E,0.003,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,151829.000,2.468,0.224,0.230,0.323,1.812,1.816,2.628*76



2025-07-31 23:18:30:322 ==>> $GBGGA,151830.000,2301.2577676,N,11421.9434337,E,1,20,0.67,81.954,M,-1.770,M,,*54

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,32,1*78

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151830.000,A,2301.2577676,N,11421.9434337,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,151830.000,2.629,0.149,0.151,0.220,1.907,1.911,2.695*72

[D][05:18:50][COMM]read battery soc:255


2025-07-31 23:18:31:323 ==>> $GBGGA,151831.000,2301.2577780,N,11421.9434258,E,1,20,0.67,81.941,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,31,1*7C

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151831.000,A,2301.2577780,N,11421.9434258,E,0.000,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,151831.000,2.532,0.183,0.187,0.267,1.845,1.849,2.622*73



2025-07-31 23:18:32:329 ==>> $GBGGA,151832.000,2301.2577747,N,11421.9434251,E,1,20,0.67,81.951,M,-1.770,M,,*51

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,33,1*71

$GBGSV,6,6,24,5,21,256,30,24,18,78,35,33,17,191,34,32,16,300,31,1*7D

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,39,41,33,243,39,23,31,313,38,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151832.000,A,2301.2577747,N,11421.9434251,E,0.000,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,151832.000,2.447,0.189,0.193,0.273,1.789,1.793,2.556*7E

[D][05:18:52][COMM]read battery soc:255


2025-07-31 23:18:33:110 ==>> [D][05:18:53][PROT]CLEAN,SEND:2
[D][05:18:53][PROT]CLEAN:2
[D][05:18:53][PROT]index:0 1629955133
[D][05:18:53][PROT]is_send:0
[D][05:18:53][PROT]sequence_num:4
[D][05:18:53][PROT]retry_timeout:0
[D][05:18:53][PROT]retry_times:2
[D][05:18:53][PROT]send_path:0x2
[D][05:18:53][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:53][PROT]===========================================================
[D][05:18:53][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:53][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955133]
[D][05:18:53][PROT]===========================================================
[D][05:18:53][PROT]sending traceid [9999999999900005]
[D][05:18:53][PROT]Send_TO_M2M [1629955133]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:53][SAL ]sock send credit cnt[6]
[D][05:18:53][SAL ]sock send ind credit cnt[6]
[D][05:18:53][M2M ]m2m send data len[198]
[D][05:18:53][SAL ]Cellular task submsg id[10]
[D][05:18:53][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:53][CAT1]gsm read msg sub id: 15
[D][05:18:53][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:53][CAT1]Send Data To Server[198][201] ... ->:
0063B9821133113

2025-07-31 23:18:33:185 ==>> 31133113311331B88B5E532DE46C5F0084E01F1412B5010E61C34352306183576D1B6F5FAD69717E3EBD31C8ECB58701D36E30DB2848B005C6593F33DA42DB0EC5B83B97580BFAFEBFAD8D67339C2AF419F7CFC2FB225C433A3EF26
[D][05:18:53][CAT1]<<< 
SEND OK

[D][05:18:53][CAT1]exec over: func id: 15, ret: 11
[D][05:18:53][CAT1]sub id: 15, ret: 11

[D][05:18:53][SAL ]Cellular task submsg id[68]
[D][05:18:53][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:53][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:53][M2M ]g_m2m_is_idle become true
[D][05:18:53][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:53][PROT]M2M Send ok [1629955133]


2025-07-31 23:18:33:290 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 23:18:33:350 ==>>                                                                                                                                                                                                         ,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151833.000,A,2301.2577829,N,11421.9434194,E,0.004,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.004,N,0.007,K,A*2C

$GBGST,151833.000,2.312,0.220,0.226,0.316,1.700,1.704,2.460*7C



2025-07-31 23:18:34:331 ==>> $GBGGA,151834.000,2301.2577828,N,11421.9434124,E,1,20,0.67,81.973,M,-1.770,M,,*50

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,56,220,36,9,51,333,33,1*4B

$GBGSV,6,3,24,11,51,117,38,25,51,19,38,59,49,131,39,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,32,1*70

$GBGSV,6,6,24,5,21,256,30,24,18,78,35,33,17,191,34,32,16,300,31,1*7D

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,34,32,16,300,36,5*7B

$GBRMC,151834.000,A,2301.2577828,N,11421.9434124,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,151834.000,2.366,0.235,0.241,0.338,1.733,1.736,2.476*77

[D][05:18:54][COMM]read battery soc:255


2025-07-31 23:18:35:332 ==>> $GBGGA,151835.000,2301.2577849,N,11421.9434059,E,1,20,0.67,81.975,M,-1.770,M,,*5B

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,32,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,34,33,17,191,33,32,16,300,31,1*7A

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,39,41,33,243,39,23,31,313,38,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151835.000,A,2301.2577849,N,11421.9434059,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,151835.000,2.321,0.213,0.218,0.305,1.702,1.705,2.436*75



2025-07-31 23:18:36:325 ==>> $GBGGA,151836.000,2301.2577873,N,11421.9434067,E,1,20,0.67,81.998,M,-1.770,M,,*5F

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,33,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,31,1*7B

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151836.000,A,2301.2577873,N,11421.9434067,E,0.003,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,151836.000,2.564,0.203,0.208,0.293,1.853,1.856,2.565*7A

[D][05:18:56][COMM]read battery soc:255


2025-07-31 23:18:37:351 ==>> $GBGGA,151837.000,2301.2577852,N,11421.9434129,E,1,20,0.67,81.990,M,-1.770,M,,*5E

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,33,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,33,32,16,300,31,1*7B

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,41,5*4E

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,39,24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151837.000,A,2301.2577852,N,11421.9434129,E,0.003,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,151837.000,2.571,0.214,0.219,0.307,1.856,1.858,2.558*70



2025-07-31 23:18:38:007 ==>> 此处延时了:【10000】毫秒
2025-07-31 23:18:38:014 ==>> 检测【检测WiFi结果】
2025-07-31 23:18:38:023 ==>> WiFi信号:【F88C21BCF57D】,信号值:-30
2025-07-31 23:18:38:037 ==>> WiFi信号:【F42A7D1297A3】,信号值:-65
2025-07-31 23:18:38:044 ==>> WiFi信号:【44A1917CA62B】,信号值:-76
2025-07-31 23:18:38:056 ==>> WiFi信号:【CC057790A5C1】,信号值:-79
2025-07-31 23:18:38:067 ==>> WiFi信号:【CC057790A820】,信号值:-84
2025-07-31 23:18:38:090 ==>> WiFi信号:【CC057790A4A0】,信号值:-85
2025-07-31 23:18:38:109 ==>> WiFi信号:【CC057790A4A1】,信号值:-85
2025-07-31 23:18:38:125 ==>> WiFi数量【7】, 最大信号值:-30
2025-07-31 23:18:38:140 ==>> 检测【检测GPS结果】
2025-07-31 23:18:38:155 ==>> 符合定位需求的卫星数量:【19】
2025-07-31 23:18:38:170 ==>> 
北斗星号:【40】,信号值:【41】
北斗星号:【7】,信号值:【38】
北斗星号:【39】,信号值:【41】
北斗星号:【6】,信号值:【35】
北斗星号:【16】,信号值:【37】
北斗星号:【3】,信号值:【39】
北斗星号:【10】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【11】,信号值:【38】
北斗星号:【59】,信号值:【38】
北斗星号:【1】,信号值:【35】
北斗星号:【34】,信号值:【41】
北斗星号:【43】,信号值:【38】
北斗星号:【60】,信号值:【39】
北斗星号:【41】,信号值:【39】
北斗星号:【23】,信号值:【38】
北斗星号:【24】,信号值:【36】
北斗星号:【33】,信号值:【35】
北斗星号:【32】,信号值:【36】

2025-07-31 23:18:38:188 ==>> 检测【CSQ强度】
2025-07-31 23:18:38:202 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 23:18:38:440 ==>> [D][05:18:58][PROT]CLEAN,SEND:0
[D][05:18:58][PROT]index:0 1629955138
[D][05:18:58][PROT]is_send:0
[D][05:18:58][PROT]sequence_num:4
[D][05:18:58][PROT]retry_timeout:0
[D][05:18:58][PROT]retry_times:1
[D][05:18:58][PROT]send_path:0x2
[D][05:18:58][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:58][PROT]===========================================================
[W][05:18:58][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955138]
[D][05:18:58][PROT]===========================================================
[D][05:18:58][PROT]sending traceid [9999999999900005]
[D][05:18:58][PROT]Send_TO_M2M [1629955138]
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:58][SAL ]sock send credit cnt[6]
[D][05:18:58][SAL ]sock send ind credit cnt[6]
[D][05:18:58][M2M ]m2m send data len[198]
[D][05:18:58][SAL ]Cellular task submsg id[10]
[D][05:18:58][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:58][CAT1]gsm read msg sub id: 15
[D][05:18:58][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:58][CAT1]Send Data To Server[198][201] ... ->:
0063B98E11331

2025-07-31 23:18:38:545 ==>> 1331133113311331B88B551DC2C86642FB818343D99936622901BE50CCA4D35D81B319BBA31E4514581B45ACA49467C755BBF47430637D3742305CDCD318D88059ED413152A49D63A7A71F6E95D399AE9FC150AA6256C7E6DBE9EC6F8
$GBGGA,151838.000,2301.2577817,N,11421.9434059,E,1,20,0.67,82.008,M,-1.770,M,,*5D

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,215,38,39,66,49,39,6,63,13,35,1*75

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

[D][05:18:58][CAT1]<<< 
SEND OK

[D][05:18:58][CAT1]exec over: func id: 15, ret: 11
[D][05:18:58][CAT1]sub id: 15, ret: 11

[D][05:18:58][SAL ]Cellular task submsg id[68]
[D][05:18:58][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,30,23,31,313,38,12,26,54,32,1*71

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,31,1*7C

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,39,

2025-07-31 23:18:38:635 ==>> 24,18,78,36,5*4E

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151838.000,A,2301.2577817,N,11421.9434059,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,151838.000,2.712,0.213,0.218,0.307,1.940,1.942,2.628*76

[D][05:18:58][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:58][M2M ]g_m2m_is_idle become true
[D][05:18:58][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:58][PROT]M2M Send ok [1629955138]
[W][05:18:58][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:18:58][CAT1]gsm read msg sub id: 12
[D][05:18:58][CAT1]SEND RAW data >>> AT+CSQ

[D][05:18:58][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:58][CAT1]exec over: func id: 12, ret: 21
[D][05:18:58][COMM]read battery soc:255


2025-07-31 23:18:38:822 ==>> 【CSQ强度】通过,【22】符合目标值【18】至【31】要求!
2025-07-31 23:18:38:829 ==>> 检测【关闭GSM联网】
2025-07-31 23:18:38:851 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 23:18:39:017 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:18:59][COMM]GSM test
[D][05:18:59][COMM]GSM test disable


2025-07-31 23:18:39:095 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 23:18:39:103 ==>> 检测【4G联网测试】
2025-07-31 23:18:39:127 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 23:18:40:106 ==>> $GBGGA,151839.000,2301.2577809,N,11421.9434009,E,1,20,0.67,81.979,M,-1.770,M,,*5A

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,216,38,39,66,49,39,6,63,13,35,1*76

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,38,34,47,78,39,1*74

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,39,1*7F

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,32,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,31,1*7C

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,38,41,33,243,39,23,31,313,38,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,35,32,16,300,36,5*7A

$GBRMC,151839.000,A,2301.2577809,N,11421.9434009,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,151839.000,2.578,0.159,0.162,0.233,1.857,1.860,2.543*74

[W][05:18:59][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:18:59][COMM]Main Task receive event:14
[D][05:18:59][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955139, allstateRepSeconds = 0
[D][05

2025-07-31 23:18:40:212 ==>> :18:59][COMM]index:0,power_mode:0xFF
[D][05:18:59][COMM]index:1,sound_mode:0xFF
[D][05:18:59][COMM]index:2,gsensor_mode:0xFF
[D][05:18:59][COMM]index:3,report_freq_mode:0xFF
[D][05:18:59][COMM]index:4,report_period:0xFF
[D][05:18:59][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:59][COMM]index:6,normal_reset_period:0xFF
[D][05:18:59][COMM]index:7,spock_over_speed:0xFF
[D][05:18:59][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:59][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:59][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:59][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:59][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:59][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:59][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:59][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:59][COMM]index:16,imu_config_params:0xFF
[D][05:18:59][COMM]index:17,long_connect_params:0xFF
[D][05:18:59][COMM]index:18,detain_mark:0xFF
[D][05:18:59][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:59][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:59][COMM]index:21,mc_mode:0xFF
[D][05:18:59][COMM]index:22,S_mode:0xFF
[D][05:18:59][COMM]index:23,overweight:0xFF
[D][

2025-07-31 23:18:40:316 ==>> 05:18:59][COMM]index:24,standstill_mode:0xFF
[D][05:18:59][COMM]index:25,night_mode:0xFF
[D][05:18:59][COMM]index:26,experiment1:0xFF
[D][05:18:59][COMM]index:27,experiment2:0xFF
[D][05:18:59][COMM]index:28,experiment3:0xFF
[D][05:18:59][COMM]index:29,experiment4:0xFF
[D][05:18:59][COMM]index:30,night_mode_start:0xFF
[D][05:18:59][COMM]index:31,night_mode_end:0xFF
[D][05:18:59][COMM]index:33,park_report_minutes:0xFF
[D][05:18:59][COMM]index:34,park_report_mode:0xFF
[D][05:18:59][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:59][COMM]index:38,charge_battery_para: FF
[D][05:18:59][COMM]index:39,multirider_mode:0xFF
[D][05:18:59][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:59][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:59][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:59][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:59][COMM]index:44,riding_duration_config:0xFF
[D][05:18:59][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:59][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:59][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:59][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:59][COMM]index:49,mc_load_startup:0xFF
[D][05:18:59][COMM]

2025-07-31 23:18:40:421 ==>> index:50,mc_tcs_mode:0xFF
[D][05:18:59][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:59][COMM]index:52,traffic_mode:0xFF
[D][05:18:59][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:59][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:59][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:59][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:59][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:59][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:59][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:59][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:59][COMM]index:63,experiment5:0xFF
[D][05:18:59][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:59][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:59][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:59][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:59][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:59][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:59][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:59][COMM]index:72,experiment6:0xFF
[D][05:18:59][COMM]index:73,experiment7:0xFF
[D][05:18:59][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:59

2025-07-31 23:18:40:526 ==>> ][COMM]index:75,zero_value_from_server:-1
[D][05:18:59][COMM]index:76,multirider_threshold:255
[D][05:18:59][COMM]index:77,experiment8:255
[D][05:18:59][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:59][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:59][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:59][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:59][COMM]index:83,loc_report_interval:255
[D][05:18:59][COMM]index:84,multirider_threshold_p2:255
[D][05:18:59][COMM]index:85,multirider_strategy:255
[D][05:18:59][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:59][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:59][COMM]index:90,weight_param:0xFF
[D][05:18:59][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:59][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:59][COMM]index:95,current_limit:0xFF
[D][05:18:59][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:59][COMM]index:100,location_mode:0xFF

[W][05:18:59][PROT]remove success[1629955139],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:59][PROT]add success [1629955139],send_path[2],type[4205],priori

2025-07-31 23:18:40:631 ==>> ty[0],index[0],used[1]
[D][05:18:59][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:59][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:59][CAT1]gsm read msg sub id: 13
[D][05:18:59][PROT]index:0 1629955139
[D][05:18:59][PROT]is_send:0
[D][05:18:59][PROT]sequence_num:8
[D][05:18:59][PROT]retry_timeout:0
[D][05:18:59][PROT]retry_times:1
[D][05:18:59][PROT]send_path:0x2
[D][05:18:59][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:59][PROT]===========================================================
[W][05:18:59][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955139]
[D][05:18:59][PROT]===========================================================
[D][05:18:59][PROT]sending traceid [9999999999900009]
[D][05:18:59][PROT]Send_TO_M2M [1629955139]
[D][05:18:59][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:59][SAL ]sock send credit cnt[6]
[D][05:18:59][SAL ]sock send ind credit cnt[6]
[D][05:18:59][M2M ]m2m send data len[294]
[D][05:18:59][SAL ]Cellular task submsg id[10]
[D][05:18:59][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:18:59][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][

2025-07-31 23:18:40:736 ==>> 05:18:59][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:59][CAT1]exec over: func id: 13, ret: 21
[D][05:18:59][M2M ]get csq[22]
[D][05:18:59][CAT1]gsm read msg sub id: 15
[D][05:18:59][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:18:59][CAT1]Send Data To Server[294][297] ... ->:
0093B983113311331133113311331B88B14B63D3B0131E9FE94FB3B3AFD7795AB66C37C5AE51BF475460ECD359815D7DA47A8543C0B7B2F49F38C37CCFE657178B5D723D1FB564F290841CBE4FEFAF01B16BC1A080C320390452887C30767F3E942E65B8661BBE23CE06E32072A2FC369ECC8BEF905670989E0984914D8513B281BF7245880699CA46499F3B1937BCA365AA76
[D][05:18:59][CAT1]<<< 
SEND OK

[D][05:18:59][CAT1]exec over: func id: 15, ret: 11
[D][05:18:59][CAT1]sub id: 15, ret: 11

[D][05:18:59][SAL ]Cellular task submsg id[68]
[D][05:18:59][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:59][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:59][M2M ]g_m2m_is_idl

2025-07-31 23:18:40:841 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 23:18:41:138 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 23:18:41:145 ==>> 检测【关闭GPS】
2025-07-31 23:18:41:152 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 23:18:41:519 ==>> $GBGGA,151841.000,2301.2577746,N,11421.9433948,E,1,20,0.67,81.984,M,-1.770,M,,*58

$GBGSA,A,3,40,07,39,06,16,03,10,11,25,59,34,43,1.25,0.67,1.06,4*09

$GBGSA,A,3,01,60,41,23,12,24,33,32,,,,,1.25,0.67,1.06,4*0F

$GBGSV,6,1,24,40,75,187,41,7,69,216,39,39,66,49,39,6,63,13,35,1*77

$GBGSV,6,2,24,16,63,17,37,3,61,190,39,10,57,220,36,9,51,333,33,1*4A

$GBGSV,6,3,24,11,51,117,38,25,51,19,39,59,49,131,39,34,47,78,39,1*75

$GBGSV,6,4,24,43,46,162,38,1,45,126,35,2,45,237,32,60,41,239,38,1*7E

$GBGSV,6,5,24,41,33,243,38,4,32,111,31,23,31,313,38,12,26,54,32,1*70

$GBGSV,6,6,24,5,21,256,31,24,18,78,35,33,17,191,34,32,16,300,31,1*7C

$GBGSV,3,1,10,40,75,187,41,39,66,49,41,25,51,19,41,34,47,78,40,5*4F

$GBGSV,3,2,10,43,46,162,39,41,33,243,39,23,31,313,39,24,18,78,36,5*4F

$GBGSV,3,3,10,33,17,191,34,32,16,300,36,5*7B

$GBRMC,151841.000,A,2301.2577746,N,11421.9433948,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,151841.000,2.435,0.176,0.179,0.254,1.766,1.768,2.443*7E

[W][05:19:01][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:01][GNSS]stop locating
[D][05:19:01][GNSS]all continue location stop
[W]

2025-07-31 23:18:41:609 ==>> [05:19:01][GNSS]sing locating running
[W][05:19:01][GNSS]stop locating
[D][05:19:01][GNSS]stop event:1
[D][05:19:01][GNSS]GPS stop. ret=0
[D][05:19:01][GNSS]all sing location stop
[D][05:19:01][CAT1]gsm read msg sub id: 24
[D][05:19:01][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:01][CAT1]<<< 
OK

[D][05:19:01][CAT1]exec over: func id: 24, ret: 6
[D][05:19:01][CAT1]sub id: 24, ret: 6

                                                                                                                                           

2025-07-31 23:18:41:673 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 23:18:41:681 ==>> 检测【清空消息队列2】
2025-07-31 23:18:41:700 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 23:18:41:804 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:02][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 23:18:41:944 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 23:18:41:952 ==>> 检测【轮动检测】
2025-07-31 23:18:41:977 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 23:18:42:015 ==>> 3A A3 01 00 A3 


2025-07-31 23:18:42:120 ==>> OFF_OUT1
OVER 150


2025-07-31 23:18:42:210 ==>> [D][05:19:02][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 23:18:42:285 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 23:18:42:451 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 23:18:42:528 ==>> 3A A3 01 01 A3 


2025-07-31 23:18:42:618 ==>> ON_OUT1
OVER 150


2025-07-31 23:18:42:726 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 23:18:42:734 ==>> 检测【关闭小电池】
2025-07-31 23:18:42:758 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:18:42:828 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 23:18:42:998 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 23:18:43:027 ==>> 检测【进入休眠模式】
2025-07-31 23:18:43:042 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:18:43:183 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:18:43:288 ==>> [D][05:19:03][COMM]Main Task receive event:28
[D][05:19:03][COMM]main task tmp_sleep_event = 8
[D][05:19:03][COMM]prepare to sleep
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 23:18:44:141 ==>> [D][05:19:04][CAT1]<<< 
OK

[D][05:19:04][CAT1]exec over: func id: 12, ret: 6
[D][05:19:04][M2M ]tcpclient close[4]
[D][05:19:04][SAL ]Cellular task submsg id[12]
[D][05:19:04][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:04][CAT1]gsm read msg sub id: 9
[D][05:19:04][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:04][CAT1]<<< 
OK

[D][05:19:04][CAT1]exec over: func id: 9, ret: 6
[D][05:19:04][CAT1]sub id: 9, ret: 6

[D][05:19:04][SAL ]Cellular task submsg id[68]
[D][05:19:04][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:04][SAL ]socket close ind. id[4]
[D][05:19:04][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:04][COMM]1x1 frm_can_tp_send ok
[D][05:19:04][CAT1]pdpdeact urc len[22]


2025-07-31 23:18:44:291 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 23:18:44:425 ==>> [E][05:19:04][COMM]1x1 rx timeout
[D][05:19:04][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:18:44:945 ==>> [E][05:19:05][COMM]1x1 rx timeout
[E][05:19:05][COMM]1x1 tp timeout
[E][05:19:05][COMM]1x1 error -3.
[W][05:19:05][COMM]CAN STOP!
[D][05:19:05][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:05][COMM]------------ready to Power off Acckey 1------------
[D][05:19:05][COMM]------------ready to Power off Acckey 2------------
[D][05:19:05][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:05][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1280
[D][05:19:05][COMM]bat sleep fail, reason:-1
[D][05:19:05][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:05][COMM]accel parse set 0
[D][05:19:05][COMM]imu rest ok. 76219
[D][05:19:05][COMM]imu sleep 0
[W][05:19:05][COMM]now sleep


2025-07-31 23:18:45:097 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:18:45:106 ==>> 检测【检测33V休眠电流】
2025-07-31 23:18:45:121 ==>> 开始33V电流采样
2025-07-31 23:18:45:148 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:18:45:203 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 23:18:46:216 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 23:18:46:275 ==>> Current33V:????:13.70

2025-07-31 23:18:46:724 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 23:18:46:734 ==>> 【检测33V休眠电流】通过,【13.7uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:18:46:744 ==>> 该项需要延时执行
2025-07-31 23:18:48:736 ==>> 此处延时了:【2000】毫秒
2025-07-31 23:18:48:751 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 23:18:48:772 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 23:18:48:827 ==>> 1A A1 00 00 FC 
Get AD_V2 1653mV
Get AD_V3 1664mV
Get AD_V4 0mV
Get AD_V5 2771mV
Get AD_V6 2022mV
Get AD_V7 1098mV
OVER 150


2025-07-31 23:18:49:763 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 23:18:49:791 ==>> 检测【打开小电池2】
2025-07-31 23:18:49:798 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 23:18:49:820 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 23:18:50:040 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 23:18:50:048 ==>> 该项需要延时执行
2025-07-31 23:18:50:549 ==>> 此处延时了:【500】毫秒
2025-07-31 23:18:50:570 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 23:18:50:586 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:18:50:624 ==>> 5A A5 02 5A A5 


2025-07-31 23:18:50:729 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:18:50:842 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:18:50:850 ==>> 该项需要延时执行
2025-07-31 23:18:51:348 ==>> 此处延时了:【500】毫秒
2025-07-31 23:18:51:361 ==>> 检测【进入休眠模式2】
2025-07-31 23:18:51:373 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 23:18:51:408 ==>> [D][05:19:11][COMM]------------ready to Power on Acckey 1------------
[D][05:19:11][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:11][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 0,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 1,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 2,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 3,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:11][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:11][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:11][COMM]----- get Acckey 1 and value:1------------
[W][05:19:11][COMM]CAN START!
[D][05:19:11][CAT1]gsm read msg sub id: 12
[D][05:19:11][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:11][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 82610
[D][05:19:11][COMM][Audio]exec status ready.
[D][05:19:11][CAT1]<<< 
OK

[D][05:19:11][CAT1]exec over: func id: 12, ret: 6
[D][05:19:11][COMM]im

2025-07-31 23:18:51:453 ==>> u wakeup ok. 82624
[D][05:19:11][COMM]imu wakeup 1
[W][05:19:11][COMM]wake up system, wakeupEvt=0x80
[D][05:19:11][COMM]frm_can_weigth_power_set 1
[D][05:19:11][COMM]Clear Sleep Block Evt
[D][05:19:11][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:11][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:18:51:543 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 23:18:51:648 ==>> [E][05:19:12][COMM]1x1 rx timeout
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:18:51:753 ==>> [D][05:19:12][COMM]msg 02A0 loss. last_tick:82595. cur_tick:83103. period:50
[D][05:19:12][COMM]msg 02A4 loss. last_tick:82595. cur_tick:83104. period:50
[D][05:19:12][COMM]msg 02A5 loss. 

2025-07-31 23:18:51:813 ==>> last_tick:82595. cur_tick:83104. period:50
[D][05:19:12][COMM]msg 02A6 loss. last_tick:82595. cur_tick:83104. period:50
[D][05:19:12][COMM]msg 02A7 loss. last_tick:82595. cur_tick:83105. period:50
[D][05:19:12][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 83105
[D][05:19:12][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 83105


2025-07-31 23:18:52:149 ==>> [E][05:19:12][COMM]1x1 rx timeout
[E][05:19:12][COMM]1x1 tp timeout
[D][05:19:12][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[E][05:19:12][COMM]1x1 error -3.
[D][05:19:12][COMM]Main Task receive event:28 finished processing
[D][05:19:12][COMM]Main Task receive event:28
[D][05:19:12][COMM]prepare to sleep
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:18:52:451 ==>>                                            :82594. cur_tick:83599. period:100
[D][05:19:12][COMM]msg 0221 loss. last_tick:82594. cur_tick:83600. period:100
[D][05:19:12][COMM]msg 0224 loss. last_tick:82594. cur_tick:83600. period:100
[D][05:19:12][COMM]msg 0260 loss. last_tick:82595. cur_tick:83601. period:100
[D][05:19:12][COMM]msg 0280 loss. last_tick:82595. cur_tick:83601. period:100
[D][05:19:12][COMM]msg 02C0 loss. last_tick:82595. cur_tick:83601. period:100
[D][05:19:12][COMM]msg 02C1 loss. last_tick:82595. cur_tick:83602. period:100
[D][05:19:12][COMM]msg 02C2 loss. last_tick:82595. cur_tick:83602. period:100
[D][05:19:12][COMM]msg 02E0 loss. last_tick:82595. cur_tick:83603. period:100
[D][05:19:12][COMM]msg 02E1 loss. last_tick:82595. cur_tick:83603. period:100
[D][05:19:12][COMM]msg 02E2 loss. last_tick:82595. cur_tick:83603. period:100
[D][05:19:12][COMM]msg 0300 loss. last_tick:82595. cur_tick:83604. period:100
[D][05:19:12][COMM]msg 0301 loss. last_tick:82595. cur_tick:83604. period:100
[D][05:19:12][COMM]bat msg 0240 loss. last_tick:82595. cur_tick:83605. period:100. j,i:1 54
[D][05:19:12][COMM]bat msg 0241 loss. last_tick:82595. cur_tick:83605. period:100. j,i:2 55
[D][05:19:12][COMM]bat msg 0242 loss. last_tick:82595. cur_tick:83605. period:100. j,i:3 56
[D][05

2025-07-31 23:18:52:542 ==>> :19:12][COMM]bat msg 0244 loss. last_tick:82595. cur_tick:83606. period:100. j,i:5 58
[D][05:19:12][COMM]bat msg 024E loss. last_tick:82595. cur_tick:83606. period:100. j,i:15 68
[D][05:19:12][COMM]bat msg 024F loss. last_tick:82595. cur_tick:83606. period:100. j,i:16 69
[D][05:19:12][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 83607
[D][05:19:12][COMM]CAN message bat fault change: 0x00000000->0x0001802E 83607
[D][05:19:12][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 83608
                                                                              

2025-07-31 23:18:52:739 ==>> [D][05:19:13][COMM]msg 0222 loss. last_tick:82594. cur_tick:84102. period:150
[D][05:19:13][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 84103


2025-07-31 23:18:52:814 ==>>                                                                                                                   eroff type 16.... 
[D][05:19:13][COMM]------------ready to Power off Acckey 2------------


2025-07-31 23:18:52:920 ==>> [E][05:19:13][COMM]1x1 rx t

2025-07-31 23:18:52:995 ==>> imeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[W][05:19:13][COMM]CAN STOP!
[D][05:19:13][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:13][COMM]------------ready to Power off Acckey 1------------
[D][05:19:13][COMM]------------ready to Power off Acckey 2------------
[D][05:19:13][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:13][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 96
[D][05:19:13][COMM]bat sleep fail, reason:-1
[D][05:19:13][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:13][COMM]accel parse set 0
[D][05:19:13][COMM]imu rest ok. 84291
[D][05:19:13][COMM]imu sleep 0
[W][05:19:13][COMM]now sleep


2025-07-31 23:18:53:205 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 23:18:53:218 ==>> 检测【检测小电池休眠电流】
2025-07-31 23:18:53:241 ==>> 开始小电池电流采样
2025-07-31 23:18:53:255 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:18:53:315 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:18:54:325 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:18:54:371 ==>> CurrentBattery:ƽ��:66.80

2025-07-31 23:18:54:828 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:18:54:859 ==>> 【检测小电池休眠电流】通过,【66.8uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 23:18:54:867 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 23:18:54:895 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 23:18:54:926 ==>> 5A A5 01 5A A5 


2025-07-31 23:18:55:025 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 23:18:55:134 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 23:18:55:151 ==>> 该项需要延时执行
2025-07-31 23:18:55:265 ==>> [D][05:19:15][COMM]------------ready to Power on Acckey 1------------
[D][05:19:15][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:15][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:15][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:15][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:15][COMM]----- get Acckey 1 and value:1------------
[W][05:19:15][COMM]CAN START!
[D][05:19:15][CAT1]gsm read msg sub id: 12
[D][05:19:15][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:15][COMM]CAN message bat fault change: 0x0001802E->0x00000000 86494
[D][05:19:15][COMM][Audio]exec status ready.
[D][05:19:15][CAT1]<<< 
OK

[D][05:19:15][CAT1]exec over: func id: 12, ret: 6
[D][05:19:15][COMM]imu wakeup ok. 86508
[D][05:19:15][COMM]imu wakeup 1
[W][05:19:15][COMM]wake up system, wakeupEvt=0x80
[D][05:19:15][COMM]frm_can_weigth_power_set 1
[D][05:19:15][COMM]Clear Sleep Block Evt
[D][05:19:15][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:15][COMM]1x1 frm_can_tp_send ok
[D][05:19:15][COMM]read battery soc:0


2025-07-31 23:18:55:522 ==>> [E][05:19:15][COMM]1x1 rx timeout
[D][05:19:15][COMM]1x1 frm_can_tp_send ok


2025-07-31 23:18:55:626 ==>> [D][05:19:15][COMM]msg 02A0 loss. last_tick:86475. cur_tick:86987. period:50
[D][05:19:15][COMM]msg 02A4 

2025-07-31 23:18:55:641 ==>> 此处延时了:【500】毫秒
2025-07-31 23:18:55:659 ==>> 检测【检测唤醒】
2025-07-31 23:18:55:672 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 23:18:55:716 ==>> loss. last_tick:86475. cur_tick:86988. period:50
[D][05:19:15][COMM]msg 02A5 loss. last_tick:86475. cur_tick:86988. period:50
[D][05:19:15][COMM]msg 02A6 loss. last_tick:86475. cur_tick:86988. period:50
[D][05:19:15][COMM]msg 02A7 loss. last_tick:86475. cur_tick:86989. period:50
[D][05:19:15][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 86989
[D][05:19:15][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 86990
                                                                                                                                                                                     

2025-07-31 23:18:56:379 ==>> [W][05:19:16][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:16][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:16][FCTY]==========Modules-nRF5340 ==========
[D][05:19:16][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:16][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:16][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:16][FCTY]DeviceID    = 460130071539492
[D][05:19:16][FCTY]HardwareID  = 867222087569440
[D][05:19:16][FCTY]MoBikeID    = 9999999999
[D][05:19:16][FCTY]LockID      = FFFFFFFFFF
[D][05:19:16][FCTY]BLEFWVersion= 105
[D][05:19:16][FCTY]BLEMacAddr   = CD21DF6D712B
[D][05:19:16][FCTY]Bat         = 3724 mv
[D][05:19:16][FCTY]Current     = 0 ma
[D][05:19:16][FCTY]VBUS        = 2600 mv
[D][05:19:16][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:16][FCTY]Ext battery vol = 32, adc = 1280
[D][05:19:16][FCTY]Acckey1 vol = 5563 mv, Acckey2 vol = 101 mv
[D][05:19:16][FCTY]Bike Type flag is invalied
[D][05:19:16][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:16][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:16][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:16][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:

2025-07-31 23:18:56:443 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 23:18:56:453 ==>> 检测【关机】
2025-07-31 23:18:56:469 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:18:56:491 ==>> 16][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:16][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:16][FCTY]Bat1         = 3704 mv
[D][05:19:16][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:16][FCTY]==========Modules-nRF5340 ==========
[E][05:19:16][COMM]1x1 rx timeout
[E][05:19:16][COMM]1x1 tp timeout
[E][05:19:16][COMM]1x1 error -3.
[D][05:19:16][COMM]Main Task receive event:28 finished processing
[D][05:19:16][COMM]Main Task receive event:65
[D][05:19:16][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:16][COMM]Main Task receive event:65 finished processing
[D][05:19:16][COMM]Main Task receive event:60
[D][05:19:16][COMM]smart_helmet_vol=255,255
[D][05:19:16][COMM]report elecbike
[W][05:19:16][PROT]remove success[1629955156],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:16][COMM]Main Task receive event:60 finished processing
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][PROT]index:0
[

2025-07-31 23:18:56:590 ==>> D][05:19:16][PROT]is_send:1
[D][05:19:16][PROT]sequence_num:10
[D][05:19:16][PROT]retry_timeout:0
[D][05:19:16][PROT]retry_times:3
[D][05:19:16][PROT]send_path:0x3
[D][05:19:16][PROT]msg_type:0x5d03
[D][05:19:16][PROT]===========================================================
[W][05:19:16][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955156]
[D][05:19:16][PROT]===========================================================
[D][05:19:16][PROT]Sending traceid[999999999990000B]
[D][05:19:16][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:16][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[D][05:19:16][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:16][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:16][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:16][SAL ]open socket ind id[4], rst[0]
[D][05:19:16][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:16][SAL ]Cellular task submsg id[8]
[D][05:19:16][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:16][SAL ]doma

2025-07-31 23:18:56:696 ==>> in[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:16][CAT1]gsm read msg sub id: 8
[D][05:19:16][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:16][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:16][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:16][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:16][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:16][CAT1]<<< 
+CME ERROR: 100

[D][05:19:16][COMM]msg 0220 loss. last_tick:86475. cur_tick:87484. period:100
[D][05:19:16][COMM]msg 0221 loss. last_tick:86475. cur_tick:87484. period:100
[D][05:19:16][COMM]msg 0224 loss. last_tick:86475. cur_tick:87484. period:100
[D][05:19:16][COMM]msg 0260 loss. last_tick:86475. cur_tick:87485. period:100
[D][05:19:16][COMM]msg 0280 loss. last_tick:86475. cur_tick:87485. period:100
[D][05:19:16][COMM]msg 02C0 loss. last_tick:86475. cur_tick:87486. period:100
[D][05:19:16][COMM]msg 02C1 loss. last_tick:86475. cur_tick:87486. period:100
[D][05:19:16][COMM]msg 02C2 loss. last_tick:86475. cur_tick:87486. period:100
[D][05:19:16][COMM]msg 02E0 loss. last_tick:86475. cur_tick:87487. period:100
[D][05:19:16][COMM]msg 02E1 loss. last_tick:86475. cur_tick:87487. period:100
[D][05:19:16][COMM]msg 02E2 loss. la

2025-07-31 23:18:56:801 ==>> st_tick:86475. cur_tick:87487. period:100
[D][05:19:16][COMM]msg 0300 loss. last_tick:86475. cur_tick:87488. period:100
[D][05:19:16][COMM]msg 0301 loss. last_tick:86475. cur_tick:87488. period:100
[D][05:19:16][COMM]bat msg 0240 loss. last_tick:86475. cur_tick:87489. period:100. j,i:1 54
[D][05:19:16][COMM]bat msg 0241 loss. last_tick:86475. cur_tick:87489. period:100. j,i:2 55
[D][05:19:16][COMM]bat msg 0242 loss. last_tick:86475. cur_tick:87489. period:100. j,i:3 56
[D][05:19:16][COMM]bat msg 0244 loss. last_tick:86475. cur_tick:87490. period:100. j,i:5 58
[D][05:19:16][COMM]bat msg 024E loss. last_tick:86475. cur_tick:87490. period:100. j,i:15 68
[D][05:19:16][COMM]bat msg 024F loss. last_tick:86475. cur_tick:87490. period:100. j,i:16 69
[D][05:19:16][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 87491
[D][05:19:16][COMM]CAN message bat fault change: 0x00000000->0x0001802E 87491
[D][05:19:16][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 87492


2025-07-31 23:18:57:461 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:18:57:492 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 23:18:57:597 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        0m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:17][COMM]read file, len:10800, num:3
[D][05:19:17][COMM]Try to Auto Lock Bat
[D][05:19:17][COMM]Main Task receive event:66 finished processing
[D][05:19:17][COMM]Main Task receive event:60
[D][05:19:17][COMM]smart_helmet_vol=255,255
[D][05:19:17][COMM]BAT CAN get state1 Fail 204
[D][05:19:17][COMM]BAT CAN get soc Fail, 204
[D][05:19:17][COMM]BAT CAN get state2 fail 204
[D][05:19:17][COMM]get soh error
[E][05:19:17][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:17][COMM]report elecbike
[W][05:19:17

2025-07-31 23:18:57:703 ==>> ][PROT]remove success[1629955157],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:17][COMM]Main Task receive event:60 finished processing
[D][05:19:17][COMM]Main Task receive event:61
[D][05:19:17][COMM][D301]:type:3, trace id:280
[D][05:19:17][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:17][PROT]index:1
[D][05:19:17][PROT]is_send:1
[D][05:19:17][PROT]sequence_num:11
[D][05:19:17][PROT]retry_timeout:0
[D][05:19:17][PROT]retry_times:3
[D][05:19:17][PROT]send_path:0x3
[D][05:19:17][PROT]msg_type:0x5d03
[D][05:19:17][PROT]===========================================================
[W][05:19:17][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955157]
[D][05:19:17][PROT]===========================================================
[D][05:19:17][PROT]Sending traceid[999999999990000C]
[D][05:19:17][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:17][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:17][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:17][M2M ]m2m_task: control_queue type

2025-07-31 23:18:57:809 ==>> :[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][COMM]Receive Bat Lock cmd 0
[D][05:19:17][COMM]VBUS is 1
[D][05:19:17][COMM]id[], hw[000
[D][05:19:17][COMM]get mcMaincircuitVolt error
[D][05:19:17][COMM]get mcSubcircuitVolt error
[D][05:19:17][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:17][COMM]BAT CAN get state1 Fail 204
[D][05:19:17][COMM]BAT CAN get soc Fail, 204
[D][05:19:17][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:17][COMM]BAT CAN get state2 fail 204
[D][05:19:17][COMM]get bat work mode err
[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:17][COMM]Main Task receive event:61 finished processing
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][COMM]--->crc16:0xb8a
[D][05:19:17][COMM]read file success
[W][05:19:17][COMM][Audio].l:[936].close hexlog save
[D][05:19:1

2025-07-31 23:18:57:916 ==>> 7][COMM]accel parse set 1
[D][05:19:17][COMM][Audio]mon:9,05:19:17
[D][05:19:17][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:17][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:17][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:17][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:17][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsen

2025-07-31 23:18:58:020 ==>> d, index:1, len:2048
[W][05:19:17][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:17][COMM]arm_hub_enable: hub power: 0
[D][05:19:17][HSDK]hexlog index save 0 3584 211 @ 0 : 0
[D][05:19:17][HSDK]write save hexlog index [0]
[D][05:19:17][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:17][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:17][COMM]read battery soc:255
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len

2025-07-31 23:18:58:110 ==>> :560
[D][05:19:17][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:17][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:17][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 23:18:58:200 ==>>                                                                                                                                                                                                                                                                                                                                                                    y to write para flash


2025-07-31 23:18:58:494 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 23:18:58:756 ==>> [W][05:19:19][COMM]Power Off
[W][05:19:19][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:19][COMM]arm_hub_enable: hub power: 0
[D][05:19:19][HSDK]hexlog index save 0 3584 211 @ 0 : 0
[D][05:19:19][HSDK]write save hexlog index [0]
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 23:18:59:036 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 23:18:59:047 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 23:18:59:085 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:18:59:124 ==>> 5A A5 02 5A A5 


2025-07-31 23:18:59:229 ==>> [W][05:19:19][COMM]Power Off
[D][05:19:19][COMM]read battery soc:255
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:18:59:309 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 23:18:59:335 ==>> 检测【检测小电池关机电流】
2025-07-31 23:18:59:350 ==>> 开始小电池电流采样
2025-07-31 23:18:59:376 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:18:59:409 ==>> [D][05:19:19][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:19][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 23:18:59:424 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 23:19:00:428 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 23:19:00:491 ==>> CurrentBattery:ƽ��:66.87

2025-07-31 23:19:00:939 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 23:19:00:948 ==>> 【检测小电池关机电流】通过,【66.87uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 23:19:01:321 ==>> MES过站成功
2025-07-31 23:19:01:331 ==>> #################### 【测试结束】 ####################
2025-07-31 23:19:01:354 ==>> 关闭5V供电
2025-07-31 23:19:01:375 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 23:19:01:425 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 23:19:02:356 ==>> 关闭5V供电成功
2025-07-31 23:19:02:374 ==>> 关闭33V供电
2025-07-31 23:19:02:404 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 23:19:02:437 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 23:19:03:358 ==>> 关闭33V供电成功
2025-07-31 23:19:03:372 ==>> 关闭3.7V供电
2025-07-31 23:19:03:396 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 23:19:03:425 ==>> 6A A6 02 A6 6A 


2025-07-31 23:19:03:525 ==>> Battery OFF
OVER 150


2025-07-31 23:19:04:333 ==>>  

