2025-07-31 21:06:44:302 ==>> MES查站成功:
查站序号:P51000100531366D验证通过
2025-07-31 21:06:44:316 ==>> 扫码结果:P51000100531366D
2025-07-31 21:06:44:318 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:06:44:319 ==>> 测试参数版本:2024.10.11
2025-07-31 21:06:44:321 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:06:44:323 ==>> 检测【打开透传】
2025-07-31 21:06:44:325 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:06:44:373 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:06:44:611 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:06:44:617 ==>> 检测【检测接地电压】
2025-07-31 21:06:44:619 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:06:44:662 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:06:44:908 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:06:44:910 ==>> 检测【打开小电池】
2025-07-31 21:06:44:913 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:06:44:970 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:06:45:201 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:06:45:203 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:06:45:205 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:06:45:261 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:06:45:502 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:06:45:505 ==>> 检测【等待设备启动】
2025-07-31 21:06:45:507 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:06:45:706 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:06:45:887 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 21:06:46:397 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:06:46:531 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:06:46:591 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:06:47:279 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:06:47:565 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:06:47:671 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:06:48:137 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:06:48:367 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:06:48:368 ==>> 检测【产品通信】
2025-07-31 21:06:48:370 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:06:48:883 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:06:49:078 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:06:49:400 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:06:49:758 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = <<<<<
[W][05:17:49][GNSS]start sing locating


2025-07-31 21:06:50:152 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:06:50:446 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:06:50:746 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[D][05:17:50][COMM]1616 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:50:988 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:06:50:990 ==>> 检测【初始化完成检测】
2025-07-31 21:06:50:995 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:06:51:172 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:06:51:276 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:06:51:278 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:06:51:279 ==>> 检测【关闭大灯控制1】
2025-07-31 21:06:51:281 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:06:51:427 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:06:51:560 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:06:51:562 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:06:51:564 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:06:51:871 ==>> [D][05:17:51][COMM]2627 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:06:52:099 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:06:52:102 ==>> 检测【关闭仪表供电】
2025-07-31 21:06:52:105 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:06:52:267 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:06:52:389 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:06:52:391 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:06:52:394 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:06:52:555 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:06:52:691 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:06:52:694 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:06:52:696 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:06:52:705 ==>> [D][05:17:52][COMM]3638 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:52:855 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:06:52:975 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:06:52:978 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:06:52:979 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:06:53:161 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:06:53:277 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:06:53:280 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:06:53:282 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:06:53:375 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:06:53:480 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 21:06:53:571 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:06:53:573 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:06:53:576 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:06:53:662 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:06:53:707 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:53:858 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:06:53:863 ==>> 该项需要延时执行
2025-07-31 21:06:54:244 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5008. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5009. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5010. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008

2025-07-31 21:06:54:275 ==>> F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 21:06:54:733 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:55:055 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:06:55:574 ==>>                                                                 D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_ev

2025-07-31 21:06:55:680 ==>> ent = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequenc

2025-07-31 21:06:55:785 ==>> e_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]


2025-07-31 21:06:55:830 ==>> 
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255


2025-07-31 21:06:55:875 ==>>                                                                                                                                          

2025-07-31 21:06:56:755 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:57:512 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:06:57:772 ==>> [D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:57:863 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:06:57:867 ==>> 检测【33V输入电压ADC】
2025-07-31 21:06:57:869 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:06:58:182 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3119  volt:5482 mv
[D][05:17:57][COMM]adc read out 24v adc:1308  volt:33083 mv
[D][05:17:57][COMM]adc read left brake adc:3  volt:3 mv
[D][05:17:57][COMM]adc read right brake adc:9  volt:11 mv
[D][05:17:57][COMM]adc read throttle adc:5  volt:6 mv
[D][05:17:57][COMM]adc read battery ts volt:9 mv
[D][05:17:57][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2420  volt:3899 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:06:58:411 ==>> 【33V输入电压ADC】通过,【32703mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:06:58:428 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:06:58:431 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:06:58:472 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1650mV
Get AD_V4 0mV
Get AD_V5 2763mV
Get AD_V6 1992mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:06:58:711 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:58:713 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:06:58:765 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:58:768 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:06:58:789 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 21:06:58:791 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:06:58:809 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:06:58:811 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:06:58:848 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:06:58:850 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:06:58:895 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:06:58:911 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:06:58:971 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1650mV
Get AD_V4 1mV
Get AD_V5 2760mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:06:59:136 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10021
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 21:06:59:186 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:59:188 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:06:59:217 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:59:242 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:06:59:245 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 21:06:59:248 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:06:59:250 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:06:59:280 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:06:59:282 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:06:59:319 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:06:59:321 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:06:59:471 ==>> 1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1647mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 1995mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:06:59:546 ==>> [D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 21:06:59:631 ==>> 【TP7_VCC3V3(ADV2)】通过,【1667mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:59:634 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:06:59:668 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:06:59:670 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:06:59:673 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 21:06:59:704 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:06:59:707 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:06:59:738 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1995mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:06:59:741 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:06:59:771 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:06:59:774 ==>> 检测【打开WIFI(1)】
2025-07-31 21:06:59:776 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:07:00:035 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Mai

2025-07-31 21:07:00:080 ==>> n Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:07:00:315 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:07:00:318 ==>> 检测【清空消息队列(1)】
2025-07-31 21:07:00:320 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:07:00:442 ==>>                                                                                                                                                                                                                                                                                     :00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087912723

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130020290490

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:07:00:532 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:07:00:600 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:07:00:603 ==>> 检测【打开GPS(1)】
2025-07-31 21:07:00:605 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:07:00:803 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:07:00:898 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:07:00:901 ==>> 检测【打开GSM联网】
2025-07-31 21:07:00:904 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:07:01:058 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 21:07:01:189 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:07:01:192 ==>> 检测【打开仪表供电1】
2025-07-31 21:07:01:196 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:07:01:434 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:07:01:476 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:07:01:478 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:07:01:481 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:07:01:509 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 21:07:01:659 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:07:01:766 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:07:01:769 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:07:01:772 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:07:01:949 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33572]


2025-07-31 21:07:02:063 ==>> 【读取主控ADC采集的仪表电压】通过,【33572mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:07:02:065 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:07:02:068 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:02:264 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:07:02:355 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:07:02:358 ==>> 检测【AD_V20电压】
2025-07-31 21:07:02:360 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:02:459 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:02:579 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 21:07:02:654 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:07:02:744 ==>> 本次取值间隔时间:283ms
2025-07-31 21:07:02:774 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:02:790 ==>> [D][05:18:02][COMM]13730 imu init OK


2025-07-31 21:07:02:880 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:02:986 ==>> [D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:02][COMM]S->M yaw:INVALID
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:07:03:151 ==>> 本次取值间隔时间:257ms
2025-07-31 21:07:03:185 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:03:286 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:03:361 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:07:03:406 ==>> 本次取值间隔时间:112ms
2025-07-31 21:07:03:433 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:03:466 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2

2025-07-31 21:07:03:541 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:03:545 ==>> M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ



2025-07-31 21:07:03:646 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 21:07:03:721 ==>>                                                                                                                                                                                                                                                                                                                          [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:07:03:766 ==>> 本次取值间隔时间:211ms
2025-07-31 21:07:03:794 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:03:901 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:04:052 ==>>                                                                                                                                   it done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:07:04:157 ==>> 本次取值间隔时间:244ms
2025-07-31 21:07:04:195 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:07:04:297 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:07:04:373 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1639mV
OVER 150


2025-07-31 21:07:04:718 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:07:04:748 ==>> 本次取值间隔时间:448ms
2025-07-31 21:07:04:779 ==>> 【AD_V20电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:07:04:783 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:07:04:785 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:07:04:869 ==>> 3A A3 02 00 A3 


2025-07-31 21:07:04:959 ==>> OFF_OUT2
OVER 150


2025-07-31 21:07:05:089 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:07:05:092 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:07:05:096 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:07:05:399 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:05][COMM]oneline display read state:255
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:07:05:625 ==>>                                                                                                          ,1,07,33,,,42,39,,,40,25,,,39,42,,,37,1*75

$GBGSV,2,2,07,40,,,35,24,,,19,59,,,42,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1465.103,1465.103,47.107,2097152,2097152,2097152*4A

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]opened : 0, 0
[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:05][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:05][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:05][M2M ]g_m2m_is_idle become true
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[D][05:18:05][COMM]read battery soc:255


2025-07-31 21:07:05:775 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:07:06:125 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:07:06:389 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:07:06:494 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,42,59,,,40,25,,,40,24,,,40,1*79

$GBGSV,4,2,14,39,,,39,41,,,39,60,,,39,14,,,38,1*78

$GBGSV,4,3,14,42,,,37,40,,,36,2,,,40,3,,,39,1*78

$GBGSV,4,4,14,1,,,38,38,,,36,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.806,1616.806,51.655,2097152

2025-07-31 21:07:06:524 ==>> ,2097152,2097152*4D



2025-07-31 21:07:06:681 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:07:06:684 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:07:06:688 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:07:06:770 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:07:06:981 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:07:06:985 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:07:06:990 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:07:07:180 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:07:07:270 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:07:07:274 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:07:07:277 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:07:07:562 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:07][COMM]oneline display set 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,59,,,40,25,,,40,24,,,40,1*74

$GBGSV,5,2,18,3,,,40,39,,,39,41,,,39,60,,,39,1*4C

$GBGSV,5,3,18,14,,,38,42,,,37,40,,,37,2,,,35,1*41

$GBGSV,5,4,18,38,,,35,4,,,32,34,,,32,44,,,31,1*42

$GBGSV,5,5,18,5,,,31,1,,,38,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1529.110,1529.110,48.947,2097152,2097152,2097152*49

                                         

2025-07-31 21:07:07:834 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:07:07:837 ==>> 检测【AD_V21电压】
2025-07-31 21:07:07:839 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:07:07:964 ==>> 1A A1 20 00 00 
Get AD_V21 1635mV
OVER 150


2025-07-31 21:07:08:083 ==>> 本次取值间隔时间:243ms
2025-07-31 21:07:08:121 ==>> 【AD_V21电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:07:08:126 ==>> 检测【关闭仪表供电2】
2025-07-31 21:07:08:131 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:07:08:357 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:08][COMM]set POWER 0
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:07:08:424 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:07:08:449 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:07:08:452 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:07:08:568 ==>> $GBGGA,130712.364,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,42,59,,,40,25,,,40,24,,,40,1*7D

$GBGSV,6,2,21,3,,,40,60,,,40,39,,,39,41,,,39,1*4B

$GBGSV,6,3,21,14,,,38,40,,,38,1,,,37,42,,,37,1*46

$GBGSV,6,4,21,16,,,37,2,,,35,38,,,35,23,,,34,1*4B

$GBGSV,6,5,21,4,,,33,34,,,33,44,,,32,5,,,31,1*73

$GBGSV,6,6,21,9,,,36,1*49

$GBRMC,130712.364,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130712.364,0.000,1533.968,1533.968,49.083,2097152,2097152,2097152*50



2025-07-31 21:07:08:763 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!
$GBGGA,130712.564,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,59,,,40,25,,,40,24,,,40,1*7E

$GBGSV,6,2,22,3,,,40,60,,,40,39,,,39,41,,,39,1*48

$GBGSV,6,3,22,14,,,38,40,,,38,1,,,37,42,,,37,1*45

$GBGSV,6,4,22,16,,,37,2,,,35,38,,,35,23,,,34,1*48

$GBGSV,6,5,22,4,,,33,34,,,33,44,,,32,5,,,32,1*73

$GBGSV,6,6,22,6,,,41,13,,,37,1*43

$GBRMC,130712.564,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130712.564,0.000,1536.036,1536.036,49.145,2097152,2097152,2097152*5D



2025-07-31 21:07:08:975 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:07:08:979 ==>> 检测【打开AccKey2供电】
2025-07-31 21:07:08:983 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:07:09:132 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:07:09:275 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:07:09:279 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:07:09:283 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:09:579 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:09][COMM]adc read vcc5v mc adc:3122  volt:5487 mv
[D][05:18:09][COMM]adc read out 24v adc:1316  volt:33285 mv
[D][05:18:09][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:09][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:09][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:09][COMM]adc read battery ts volt:7 mv
[D][05:18:09][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:09][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:09][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:09][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:09][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:09][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:09][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                         

2025-07-31 21:07:09:684 ==>> $GBGGA,130713.544,,,,,0,00

2025-07-31 21:07:09:759 ==>> ,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,41,59,,,40,25,,,40,1*7A

$GBGSV,7,2,26,3,,,40,60,,,40,39,,,39,41,,,39,1*4D

$GBGSV,7,3,26,14,,,38,40,,,38,1,,,37,42,,,37,1*40

$GBGSV,7,4,26,16,,,37,6,,,36,13,,,35,2,,,35,1*71

$GBGSV,7,5,26,7,,,35,9,,,35,10,,,35,38,,,34,1*75

$GBGSV,7,6,26,23,,,34,34,,,34,12,,,34,4,,,33,1*45

$GBGSV,7,7,26,44,,,33,5,,,31,1*45

$GBRMC,130713.544,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130713.544,0.000,1518.011,1518.011,48.562,2097152,2097152,2097152*5E



2025-07-31 21:07:09:829 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33285mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:07:09:833 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:07:09:837 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:07:10:035 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:07:10:121 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:07:10:124 ==>> 该项需要延时执行
2025-07-31 21:07:10:729 ==>> $GBGGA,130714.524,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,40,59,,,40,25,,,40,1*7B

$GBGSV,7,2,26,3,,,40,60,,,40,39,,,39,41,,,39,1*4D

$GBGSV,7,3,26,14,,,38,40,,,38,42,,,37,16,,,37,1*76

$GBGSV,7,4,26,1,,,36,7,,,36,6,,,35,13,,,35,1*43

$GBGSV,7,5,26,2,,,35,9,,,35,10,,,34,38,,,34,1*71

$GBGSV,7,6,26,23,,,34,34,,,34,12,,,33,4,,,33,1*42

$GBGSV,7,7,26,44,,,33,5,,,31,1*45

$GBRMC,130714.524,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130714.524,0.000,1511.634,1511.634,48.359,2097152,2097152,2097152*51



2025-07-31 21:07:11:721 ==>> [D][05:18:11][COMM]read battery soc:255
$GBGGA,130715.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,40,41,,,39,39,,,38,1*4D

$GBGSV,7,3,27,14,,,38,40,,,38,42,,,37,16,,,37,1*77

$GBGSV,7,4,27,1,,,37,7,,,36,6,,,35,13,,,35,1*43

$GBGSV,7,5,27,2,,,35,9,,,35,10,,,34,38,,,34,1*70

$GBGSV,7,6,27,23,,,34,34,,,34,12,,,33,4,,,33,1*43

$GBGSV,7,7,27,44,,,33,5,,,31,11,,,30,1*47

$GBRMC,130715.504,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130715.504,0.000,1501.722,1501.722,48.052,2097152,2097152,2097152*5A



2025-07-31 21:07:12:712 ==>> $GBGGA,130716.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,39,41,,,39,39,,,39,1*42

$GBGSV,7,3,27,14,,,38,40,,,38,42,,,37,16,,,37,1*77

$GBGSV,7,4,27,1,,,36,7,,,36,6,,,35,13,,,35,1*42

$GBGSV,7,5,27,2,,,35,9,,,35,38,,,34,23,,,34,1*70

$GBGSV,7,6,27,34,,,34,10,,,33,12,,,33,44,,,33,1*70

$GBGSV,7,7,27,4,,,32,5,,,31,11,,,30,1*72

$GBRMC,130716.504,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130716.504,0.000,1497.118,1497.118,47.908,2097152,2097152,2097152*50



2025-07-31 21:07:13:129 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:07:13:133 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:07:13:138 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:13:484 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:13][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:13][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:13][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:13][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:13][COMM]adc read battery ts volt:8 mv
[D][05:18:13][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:13][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 21:07:13:684 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【25mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:07:13:689 ==>> 检测【打开AccKey1供电】
2025-07-31 21:07:13:693 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:07:13:726 ==>> [D][05:18:13][COMM]read battery soc:255
$GBGGA,130717.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,40,41,,,39,39,,,39,1*4C

$GBGSV,7,3,27,14,,,38,40,,,38,16,,,37,42,,,36,1*76

$GBGSV,7,4,27,1,,,36,7,,,36,9,,,36,6,,,35,1*7A

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,34,23,,,34,1*4B

$GBGSV,7,6,27,34,,,34,10,,,34,12,,,33,44,,,33,1*77

$GBGSV,7,7,27,4,,,33,5,,,32,11,,,30,1*70

$GBRMC,130717.504,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130717.504,0.000,1503.255,1503.255,48.098,2097152,2097152,2097152*5E



2025-07-31 21:07:13:831 ==>> [D][05:18:13][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:13][COMM]>>>>>Input command = A

2025-07-31 21:07:13:861 ==>> T+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:07:13:970 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:07:13:973 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:07:13:976 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:07:14:073 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 21:07:14:225 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 21:07:14:261 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:07:14:264 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:07:14:267 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:14:582 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3117  volt:5479 mv
[D][05:18:14][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:14][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:14][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:14][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:14][COMM]adc read battery ts volt:12 mv
[D][05:18:14][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2419  volt:3897 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:07:14:688 ==>>                                                                                                                                                                                                     ,14,,,38,40,,,38,16,,,37,42,,,37,1*77

$GBGSV,7,4,27,1,,,37,7,,,36,9,,,36,6,,,35,1*7B

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,34,,,35,1*4D

$GBGSV,7,6,27,23,,,34,10,,,34,12,,,33,44,,,33,1*71

$GBGSV,7,7,27,4,,,33,5,,,32,11,,,30,1*70

$GBRMC,130718.504,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130718.504,0.000,

2025-07-31 21:07:14:719 ==>> 1509.394,1509.394,48.292,2097152,2097152,2097152*59



2025-07-31 21:07:14:816 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5479mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:07:14:820 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:07:14:825 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:07:14:948 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:07:15:104 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:07:15:107 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:07:15:110 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:07:15:163 ==>> 1A A1 00 40 00 
Get AD_V14 2660mV
OVER 150


2025-07-31 21:07:15:361 ==>> 原始值:【2660】, 乘以分压基数【2】还原值:【5320】
2025-07-31 21:07:15:399 ==>> 【读取AccKey1电压(ADV14)后】通过,【5320mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:07:15:404 ==>> 检测【打开WIFI(2)】
2025-07-31 21:07:15:408 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:07:15:747 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6
[D][05:18:15][COMM]read battery soc:255
$GBGGA,130719.504,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,40,41,,,39,39,,,39,1*4C

$GBGSV,7,3,27,14,,,38,40,,,38,16,,,37,42,,,37,1*77

$GBGSV,7,4,27,1,,,37,7,,,36,9,,,36,6,,,35,1*7B

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,34,34,,,34,1*4D

$GBGSV,7,6,27,23,,,34,10,,,34,12,,,33,44,,,33,1*71

$GBGSV,7,7,27,4,,,33,5,,,32,11,,,30,1*70

$GBRMC,130719.504,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130719.504,0.000,1506.326,1506.326,48.197,2097152,2097152,2097152*5E



2025-07-31 21:07:15:951 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:07:15:955 ==>> 检测【转刹把供电】
2025-07-31 21:07:15:960 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:16:132 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:07:16:240 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:07:16:245 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:07:16:251 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:16:346 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:07:16:469 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2399mV
OVER 150


2025-07-31 21:07:16:499 ==>> 原始值:【2399】, 乘以分压基数【2】还原值:【4798】
2025-07-31 21:07:16:533 ==>> 【读取AD_V15电压(前)】通过,【4798mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:07:16:537 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:07:16:541 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:16:638 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:07:16:727 ==>> +WIFISCAN:4,0,F42A7D1297A3,-68
+WIFISCAN:4,1,CC057790A7C0,-75
+WIFISCAN:4,2,CC057790A7C1,-77
+WIFISCAN:4,3,44A1917CAD81,-78

[D][05:18:16][CAT1]wifi scan report total[4]
$GBGGA,130720.504,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,39,39,,,39,41,,,38,1*43

$GBGSV,7,3,27,14,,,38,40,,,38,16,,,37,42,,,37,1*77

$GBGSV,7,4,27,1,,,36,7,,,36,9,,,35,6,,,35,1*79

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,34,34,,,34,1*4D

$GBGSV,7,6,27,23,,,34,10,,,34,12,,,34,44,,,33,1*76

$GBGSV,7,7,27,4,,,33,5,,,32,11,,,30,1*70

$GBRMC,130720.504,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130720.504,0.000,1501.715,1501.715,48.045,2097152,2097152,2097152*5A



2025-07-31 21:07:16:832 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
[D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 21:07:17:592 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:17:700 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:07:17:715 ==>> [D][05:18:17][COMM]read battery soc:255
$GBGGA,130721.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,40,59,,,40,25,,,40,1*7A

$GBGSV,7,2,27,3,,,40,60,,,40,39,,,39,41,,,39,1*4C

$GBGSV,7,3,27,14,,,38,40,,,38,16,,,37,42,,,37,1*77

$GBGSV,7,4,27,1,,,36,7,,,36,9,,,36,6,,,35,1*7A

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,34,,,34,1*4C

$GBGSV,7,6,27,23,,,34,10,,,34,12,,,34,44,,,33,1*76

$GBGSV,7,7,27,4,,,33,5,,,32,11,,,30,1*70

$GBRMC,130721.504,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130721.504,0.000,1507.858,1507.858,48.243,2097152,2097152,2097152*5F



2025-07-31 21:07:17:760 ==>> 00 00 00 00 00 
head err!


2025-07-31 21:07:17:790 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:07:18:630 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:18:722 ==>> $GBGGA,130722.504,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,59,,,40,25,,,40,1*74

$GBGSV,7,2,28,3,,,40,60,,,40,39,,,39,41,,,39,1*43

$GBGSV,7,3,28,14,,,38,40,,,38,16,,,37,42,,,37,1*78

$GBGSV,7,4,28,1,,,37,7,,,36,9,,,36,6,,,35,1*74

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,35,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,12,,,34,44,,,33,1*79

$GBGSV,7,7,28,4,,,33,5,,,33,8,,,32,11,,,30,1*47

$GBRMC,130722.504,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130722.504,0.000,1505.833,1505.833,48.181,2097152,2097152,2097152*51



2025-07-31 21:07:18:736 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:07:18:949 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:07:19:661 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:07:19:722 ==>> [D][05:18:19][COMM]read battery soc:255
$GBGGA,130723.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,59,,,40,25,,,40,1*74

$GBGSV,7,2,28,3,,,40,60,,,40,39,,,39,41,,,39,1*43

$GBGSV,7,3,28,14,,,38,40,,,38,16,,,37,42,,,37,1*78

$GBGSV,7,4,28,1,,,37,7,,,37,9,,,36,6,,,35,1*75

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,35,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,12,,,33,44,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,33,8,,,32,11,,,30,1*47

$GBRMC,130723.504,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130723.504,0.000,1505.834,1505.834,48.183,2097152,2097152,2097152*52



2025-07-31 21:07:19:766 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:07:19:781 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:07:19:871 ==>> 1A A1 01 00 00 
Get AD_V16 2430mV
OVER 150


2025-07-31 21:07:19:931 ==>> 原始值:【2430】, 乘以分压基数【2】还原值:【4860】
2025-07-31 21:07:19:970 ==>> 【读取AD_V16电压(前)】通过,【4860mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:07:19:973 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:07:19:976 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:20:283 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3118  volt:5480 mv
[D][05:18:20][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:20][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:20][COMM]adc read right brake adc:5  volt:6 mv
[D][05:18:20][COMM]adc read throttle adc:1  volt:1 mv
[D][05:18:20][COMM]adc read battery ts volt:10 mv
[D][05:18:20][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3067  volt:5391 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2419  volt:3897 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:07:20:516 ==>> 【转刹把供电电压(主控ADC)】通过,【5391mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:07:20:520 ==>> 检测【转刹把供电电压】
2025-07-31 21:07:20:523 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:20:834 ==>> $GBGGA,130724.504,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,25,,,41,59,,,40,1*75

$GBGSV,7,2,28,3,,,40,60,,,40,39,,,39,41,,,39,1*43

$GBGSV,7,3,28,14,,,38,40,,,38,16,,,37,42,,,37,1*78

$GBGSV,7,4,28,1,,,37,7,,,36,9,,,36,6,,,35,1*74

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,35,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,34,12,,,33,1*79

$GBGSV,7,7,28,4,,,33,5,,,33,8,,,32,11,,,30,1*47

$GBRMC,130724.504,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130724.504,0.000,1507.316,1507.316,48.231,2097152,2097152,2097152*5F

[W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3125  volt:5493 mv
[D][05:18:20][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:20][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:20][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:20][COMM]adc read throttle adc:10  volt:13 mv
[D][05:18:20][COMM]adc read battery ts volt:11 mv
[D][05:18:20][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3074  volt:5403 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:20][COMM]arm_hub adc read l

2025-07-31 21:07:20:864 ==>> ed yb adc:1448  volt:33572 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:07:21:089 ==>> 【转刹把供电电压】通过,【5403mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:07:21:092 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:07:21:095 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:07:21:251 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:07:21:485 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:07:21:491 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:07:21:496 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:07:21:589 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:07:21:695 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:07:21:725 ==>> [D][05:18:21][COMM]read battery soc:255
$GBGGA,130725.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,41,25,,,40,59,,,40,1*74

$GBGSV,7,2,28,3,,,40,60,,,40,39,,,39,41,,,39,1*43

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,1,,,37,1*40

$GBGSV,7,4,28,42,,,36,7,,,36,9,,,35,6,,,35,1*41

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,35,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,130725.504,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130725.504,0.000,1498.433,1498.433,47.948,2097152,2097152,2097152*54

[W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:07:21:800 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:07:21:830 ==>> [W][05:18:21][COMM]>>>>>Input command = ?<<<<


2025-07-31 21:07:21:875 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:07:21:998 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:07:22:002 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:07:22:005 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:07:22:106 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:07:22:166 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:07:22:249 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:07:22:253 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:07:22:256 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:07:22:364 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:07:22:537 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:07:22:541 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:07:22:547 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:07:22:716 ==>> $GBGGA,130726.504,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,59,,,40,1*75

$GBGSV,7,2,28,3,,,40,60,,,39,39,,,39,41,,,39,1*4D

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,1,,,37,1*40

$GBGSV,7,4,28,42,,,36,7,,,36,9,,,35,6,,,35,1*41

$GBGSV,7,5,28,13,,,35,2,,,34,38,,,34,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,130726.504,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130726.504,0.000,1492.508,1492.508,47.757,2097152,2097152,2097152*57

3A A3 04 01 A3 


2025-07-31 21:07:22:762 ==>> ON_OUT4
OVER 150


2025-07-31 21:07:22:823 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:07:22:826 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:07:22:831 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:07:22:975 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:07:23:113 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:07:23:119 ==>> 检测【左刹电压测试1】
2025-07-31 21:07:23:124 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:23:372 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:23][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:23][COMM]adc read left brake adc:1719  volt:2266 mv
[D][05:18:23][COMM]adc read right brake adc:1724  volt:2272 mv
[D][05:18:23][COMM]adc read throttle adc:1714  volt:2259 mv
[D][05:18:23][COMM]adc read battery ts volt:8 mv
[D][05:18:23][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:23][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:23][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:23][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:23][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:23][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:07:23:675 ==>> 【左刹电压测试1】通过,【2266】符合目标值【2250】至【2500】要求!
2025-07-31 21:07:23:680 ==>> 检测【右刹电压测试1】
2025-07-31 21:07:23:714 ==>> 【右刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 21:07:23:717 ==>> 检测【转把电压测试1】
2025-07-31 21:07:23:724 ==>> $GBGGA,130727.504,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,7,2,28,59,,,39,60,,,39,39,,,38,41,,,38,1*7C

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,1,,,36,1*41

$GBGSV,7,4,28,42,,,36,7,,,36,9,,,35,6,,,35,1*41

$GBGSV,7,5,28,13,,,34,2,,,34,38,,,34,34,,,34,1*42

$GBGSV,7,6,28,23,,,33,10,,,33,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,130727.504,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130727.504,0.000,1482.142,1482.142,47.424,2097152,2097152,2097152*51

[D][05:18:23][COMM]read battery soc:255


2025-07-31 21:07:23:758 ==>> 【转把电压测试1】通过,【2259】符合目标值【2250】至【2500】要求!
2025-07-31 21:07:23:764 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:07:23:769 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:07:23:874 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 21:07:24:044 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:07:24:048 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:07:24:052 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:07:24:166 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:07:24:349 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:07:24:356 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:07:24:360 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:07:24:472 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:07:24:655 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:07:24:660 ==>> 检测【左刹电压测试2】
2025-07-31 21:07:24:666 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:07:24:715 ==>> $GBGGA,130728.504,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,7,2,28,59,,,40,60,,,40,41,,,39,39,,,38,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,36,1,,,36,1*40

$GBGSV,7,4,28,42,,,36,7,,,36,9,,,35,6,,,35,1*41

$GBGSV,7,5,28,13,,,35,2,,,34,38,,,34,34,,,34,1*43

$GBGSV,7,6,28,23,,,33,10,,,33,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,130728.504,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130728.504,0.000,1486.589,1486.589,47.571,2097152,2097152,2097152*5F



2025-07-31 21:07:24:910 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:24][COMM]adc read vcc5v mc adc:3120  volt:5484 mv
[D][05:18:24][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:24][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:24][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:24][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:24][COMM]adc read battery ts volt:14 mv
[D][05:18:24][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:24][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1446  volt:33525 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 21:07:25:228 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 21:07:25:232 ==>> 检测【右刹电压测试2】
2025-07-31 21:07:25:273 ==>> 【右刹电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 21:07:25:278 ==>> 检测【转把电压测试2】
2025-07-31 21:07:25:312 ==>> 【转把电压测试2】通过,【5】符合目标值【0】至【50】要求!
2025-07-31 21:07:25:317 ==>> 检测【晶振检测】
2025-07-31 21:07:25:320 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:07:25:452 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:25][COMM][lf state:1][hf state:1]


2025-07-31 21:07:25:604 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:07:25:608 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:07:25:612 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:07:25:740 ==>> $GBGGA,130729.504,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,7,2,28,59,,,40,60,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,36,1,,,36,1*40

$GBGSV,7,4,28,42,,,36,7,,,36,9,,,35,6,,,35,1*41

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,34,34,,,34,1*42

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,30,1*46

$GBRMC,130729.504,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130729.504,0.000,1489.544,1489.544,47.659,2097152,2097152,2097152*57

[D][05:18:25][COMM]read battery soc:255
1A A1 00 00 FC 
Get AD_V2 1667mV
Get AD_V3 1647mV
Get AD_V4 1651mV
Get AD_V5 2764mV
Get AD_V6 1994mV
Get AD_V7 1092mV
OVER 150


2025-07-31 21:07:25:901 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:07:25:907 ==>> 检测【检测BootVer】
2025-07-31 21:07:25:910 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:26:233 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130020290490
[D][05:18:25][FCTY]HardwareID  = 867222087912723
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1293
[D][05:18:25][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3739 mv


2025-07-31 21:07:26:263 ==>> 
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:07:26:451 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:07:26:454 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:07:26:460 ==>> 检测【检测固件版本】
2025-07-31 21:07:26:484 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:07:26:488 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:07:26:494 ==>> 检测【检测蓝牙版本】
2025-07-31 21:07:26:517 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:07:26:523 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:07:26:528 ==>> 检测【检测MoBikeId】
2025-07-31 21:07:26:551 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:07:26:558 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:07:26:565 ==>> 检测【检测蓝牙地址】
2025-07-31 21:07:26:573 ==>> 取到目标值:D687757A78E1
2025-07-31 21:07:26:609 ==>> 【检测蓝牙地址】通过,【D687757A78E1】符合目标值【】要求!
2025-07-31 21:07:26:614 ==>> 提取到蓝牙地址:D687757A78E1
2025-07-31 21:07:26:618 ==>> 检测【BOARD_ID】
2025-07-31 21:07:26:642 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:07:26:648 ==>> 检测【检测充电电压】
2025-07-31 21:07:26:675 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:07:26:681 ==>> 检测【检测VBUS电压1】
2025-07-31 21:07:26:709 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:07:26:714 ==>> 检测【检测充电电流】
2025-07-31 21:07:26:718 ==>> $GBGGA,130730.504,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,7,2,28,59,,,40,60,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,42,,,37,1*77

$GBGSV,7,4,28,1,,,36,7,,,36,9,,,35,6,,,35,1*76

$GBGSV,7,5,28,13,,,35,2,,,35,38,,,34,34,,,34,1*42

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,32,5,,,32,8,,,32,11,,,31,1*46

$GBRMC,130730.504,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130730.504,0.000,1492.505,1492.505,47.753,2097152,2097152,2097152*54



2025-07-31 21:07:26:754 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:07:26:758 ==>> 检测【检测IMEI】
2025-07-31 21:07:26:761 ==>> 取到目标值:867222087912723
2025-07-31 21:07:26:786 ==>> 【检测IMEI】通过,【867222087912723】符合目标值【】要求!
2025-07-31 21:07:26:790 ==>> 提取到IMEI:867222087912723
2025-07-31 21:07:26:794 ==>> 检测【检测IMSI】
2025-07-31 21:07:26:800 ==>> 取到目标值:460130020290490
2025-07-31 21:07:26:823 ==>> 【检测IMSI】通过,【460130020290490】符合目标值【】要求!
2025-07-31 21:07:26:827 ==>> 提取到IMSI:460130020290490
2025-07-31 21:07:26:834 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:07:26:840 ==>> 取到目标值:460130020290490
2025-07-31 21:07:26:855 ==>> 【校验网络运营商(移动)】通过,【460130020290490】符合目标值【】要求!
2025-07-31 21:07:26:859 ==>> 检测【打开CAN通信】
2025-07-31 21:07:26:866 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:07:26:969 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:07:27:138 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:07:27:142 ==>> 检测【检测CAN通信】
2025-07-31 21:07:27:148 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:07:27:261 ==>> can send success


2025-07-31 21:07:27:307 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:367 ==>> [D][05:18:27][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 38288
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:427 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:487 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:547 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:607 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:07:27:668 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:07:27:672 ==>> 检测【关闭CAN通信】
2025-07-31 21:07:27:675 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:07:27:712 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
$GBGGA,130731.504,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,24,,,40,25,,,40,3,,,40,1*4A

$GBGSV,7,2,28,59,,,40,60,,,40,39,,,39,41,,,38,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,42,,,37,1*77

$GBGSV,7,4,28,1,,,36,7,,,36,9,,,35,6,,,35,1*76

$GBGSV,7,5,28,13,,,35,2,,,34,38,,,34,34,,,34,1*43

$GBGSV,7,6,28,23,,,34,10,,,34,44,,,33,12,,,33,1*7E

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,31,1*47

$GBRMC,130731.504,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130731.504,0.000,745.600,745.600,681.870,2097152

2025-07-31 21:07:27:772 ==>> ,2097152,2097152*67

[D][05:18:27][COMM]read battery soc:255
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:07:27:961 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:07:27:965 ==>> 检测【打印IMU STATE】
2025-07-31 21:07:27:970 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:07:28:159 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:07:28:255 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:07:28:260 ==>> 检测【六轴自检】
2025-07-31 21:07:28:264 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:07:28:466 ==>> [D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:07:28:930 ==>> $GBGGA,130732.504,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,60,,,40,3,,,40,24,,,40,1*4B

$GBGSV,7,2,28,59,,,40,25,,,40,39,,,39,40,,,38,1*7D

$GBGSV,7,3,28,41,,,38,16,,,37,14,,,37,7,,,36,1*46

$GBGSV,7,4,28,1,,,36,42,,,36,2,,,35,13,,,35,1*78

$GBGSV,7,5,28,9,,,35,6,,,35,10,,,34,38,,,34,1*7B

$GBGSV,7,6,28,34,,,34,23,,,34,44,,,33,12,,,33,1*78

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,31,1*47

$GBRMC,130732.504,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130732.504,0.000,745.599,745.599,681.869,2097152,2097152,2097152*6C



2025-07-31 21:07:29:737 ==>> $GBGGA,130733.504,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,60,,,40,3,,,40,24,,,40,1*45

$GBGSV,8,2,29,59,,,40,39,,,39,25,,,39,40,,,38,1*7D

$GBGSV,8,3,29,14,,,38,41,,,38,1,,,37,16,,,37,1*40

$GBGSV,8,4,29,7,,,36,42,,,36,2,,,35,9,,,35,1*4B

$GBGSV,8,5,29,6,,,35,13,,,34,38,,,34,34,,,34,1*49

$GBGSV,8,6,29,23,,,34,10,,,33,44,,,33,12,,,33,1*77

$GBGSV,8,7,29,4,,,33,5,,,32,8,,,32,11,,,31,1*49

$GBGSV,8,8,29,45,,,39,1*76

$GBRMC,130733.504,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130733.504,0.000,744.861,744.861,681.195,2097152,2097152,2097152*67

[D][05:18:29][COMM]read battery soc:255


2025-07-31 21:07:30:167 ==>> [D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:07:30:380 ==>> [D][05:18:30][COMM]Main Task receive event:142
[D][05:18:30][COMM]###### 41305 imu self test OK ######
[D][05:18:30][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-10,-14,4087]
[D][05:18:30][COMM]Main Task receive event:142 finished processing


2025-07-31 21:07:30:636 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:07:30:643 ==>> 检测【打印IMU STATE2】
2025-07-31 21:07:30:650 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:07:30:730 ==>> $GBGGA,130734.504,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,59,,,40,1*41

$GBGSV,7,2,28,25,,,40,60,,,39,39,,,39,40,,,38,1*79

$GBGSV,7,3,28,14,,,38,41,,,38,16,,,37,7,,,36,1*49

$GBGSV,7,4,28,1,,,36,42,,,36,2,,,35,9,,,35,1*43

$GBGSV,7,5,28,6,,,35,13,,,34,38,,,34,34,,,34,1*47

$GBGSV,7,6,28,23,,,34,10,,,33,44,,,33,12,,,33,1*79

$GBGSV,7,7,28,4,,,33,5,,,32,8,,,32,11,,,31,1*47

$GBRMC,130734.504,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130734.504,0.000,744.122,744.122,680.519,2097152,2097152,2097152*61



2025-07-31 21:07:30:835 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:30][COMM]YAW data: 32763[32763]
[D][05:18:30][COMM]pitch:-66 roll:0
[D][05:18:30][COMM]vali

2025-07-31 21:07:30:865 ==>> d_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:07:30:930 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:07:30:935 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:07:30:938 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:07:31:063 ==>> 5A A5 02 5A A5 


2025-07-31 21:07:31:168 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:07:31:214 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:07:31:221 ==>> 检测【检测VBUS电压2】
2025-07-31 21:07:31:227 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:31:533 ==>> [D][05:18:31][FCTY]get_ext_48v_vol retry i = 0,volt = 19
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 1,volt = 19
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 2,volt = 19
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:31][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130020290490
[D][05:18:31][FCTY]HardwareID  = 867222087912723
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:31][FCTY]Bat         = 3924 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 11700 mv
[D][05:18:31][FCTY]TEMP

2025-07-31 21:07:31:608 ==>> = 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 10, adc = 396
[D][05:18:31][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3739 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:07:31:713 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          35.504,0.000,742.647,742.647,679.170,2097152,2097152,2097152*6D



2025-07-31 21:07:31:760 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:32:126 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130020290490
[D][05:18:31][FCTY]HardwareID  = 867222087912723
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:31][FCTY]Bat         = 3924 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 7700 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 4, adc = 180
[D][05:18:31][FCTY]Acckey1 vol = 5480 mv, Acckey2 vol = 0 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCT

2025-07-31 21:07:32:171 ==>> Y]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3739 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:07:32:311 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:32:353 ==>> [D][05:18:32][COMM]msg 0601 loss. last_tick:38272. cur_tick:43283. period:500
[D][05:18:32][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 43284


2025-07-31 21:07:32:627 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130020290490
[D][05:18:32][FCTY]HardwareID  = 867222087912723
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:32][FCTY]Bat         = 3924 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 7700 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 3, adc = 154
[D][05:18:32][FCTY]Acckey1 vol = 5484 mv, Acckey2 vol = 126 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:32][FCTY]CAT1_G

2025-07-31 21:07:32:732 ==>> NSS_VERSION = V3465b5b1
[D][05:18:32][FCTY]Bat1         = 3739 mv
[D][05:18:32][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:07:32:855 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:33:566 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:32][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:32][FCTY]==========Modules-nRF5340 ==========
[D][05:18:32][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:32][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:32][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:32][FCTY]DeviceID    = 460130020290490
[D][05:18:32][FCTY]HardwareID  = 867222087912723
[D][05:18:32][FCTY]MoBikeID    = 9999999999
[D][05:18:32][FCTY]LockID      = FFFFFFFFFF
[D][05:18:32][FCTY]BLEFWVersion= 105
[D][05:18:32][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:32][FCTY]Bat         = 3684 mv
[D][05:18:32][FCTY]Current     = 0 ma
[D][05:18:32][FCTY]VBUS        = 7700 mv
[D][05:18:32][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:32][FCTY]Ext battery vol = 3, adc = 128
[D][05:18:32][FCTY]Acckey1 vol = 5479 mv, Acckey2 vol = 0 mv
[D][05:18:32][FCTY]Bike Type flag is invalied
[D][05:18:32][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:32][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:32][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:32][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33

2025-07-31 21:07:33:671 ==>> ][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3739 mv
[D][05:18:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:33][COMM]Main Task receive event:65
[D][05:18:33][COMM]main task tmp_sleep_event = 80
[D][05:18:33][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:33][COMM]Main Task receive event:65 finished processing
[D][05:18:33][COMM]Main Task receive event:60
[D][05:18:33][COMM]smart_helmet_vol=255,255
[D][05:18:33][COMM]BAT CAN get state1 Fail 204
[D][05:18:33][COMM]BAT CAN get soc Fail, 204
[W][05:18:33][GNSS]stop locating
[D][05:18:33][GNSS]stop event:8
[D][05:18:33][GNSS]all continue location stop
[W][05:18:33][GNSS]sing locating running
[D][05:18:33][COMM]report elecbike
[W][05:18:33][PROT]remove success[1629955113],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:33][PROT]add success [1629955113],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:33][COMM]Main Task receive event:60 

2025-07-31 21:07:33:678 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:07:33:776 ==>> finished processing
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]index:0
[D][05:18:33][PROT]is_send:1
[D][05:18:33][PROT]sequence_num:4
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x3
[D][05:18:33][PROT]msg_type:0x5d03
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]Sending traceid[9999999999900005]
[D][05:18:33][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:33][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:33][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:33][PROT]index:0 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:4
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:33][PROT]===========================================================
[D][05:18:33][HSDK][0

2025-07-31 21:07:33:881 ==>> ] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900005]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5A7BCBCDE50021EFD9E67601539734A628F8EF6766733D0E67CBCDBE784A3FD2A407198142E3D13BDA9D06FEA99B2F7C7DB47A8D8ECE619F8F7AA59D8BCEABB39F5336B50891EE649E266F4414169CFAACD8D
[D][05:18:33][CAT1]<<< 
SEND OK

[D][05:18:33][CAT1]exec over: func id: 15, ret: 11
[D][05:18:33][CAT1]sub id: 15, ret: 11

[D][05:18:33][SAL ]Cellular tas

2025-07-31 21:07:33:926 ==>> k submsg id[68]
[D][05:18:33][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:33][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:33][M2M ]g_m2m_is_idle become true
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:33][PROT]M2M Send ok [1629955113]


2025-07-31 21:07:34:214 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========
[D][05:18:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:33][FCTY]DeviceID    = 460130020290490
[D][05:18:33][FCTY]HardwareID  = 867222087912723
[D][05:18:33][FCTY]MoBikeID    = 9999999999
[D][05:18:33][FCTY]LockID      = FFFFFFFFFF
[D][05:18:33][FCTY]BLEFWVersion= 105
[D][05:18:33][FCTY]BLEMacAddr   = D687757A78E1
[D][05:18:33][FCTY]Bat         = 3684 mv
[D][05:18:33][FCTY]Current     = 0 ma
[D][05:18:33][FCTY]VBUS        = 5000 mv
[D][05:18:33][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:33][FCTY]Ext battery vol = 2, adc = 106
[D][05:18:33][FCTY]Acckey1 vol = 5482 mv, Acckey2 vol = 0 mv
[D][05:18:33][FCTY]Bike Type flag is invalied
[D][05:18:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:33][FCTY]Bat1         = 3739 mv
[D][05:18:33][F

2025-07-31 21:07:34:244 ==>> CTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:33][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:07:34:529 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:07:34:537 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:07:34:553 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:07:34:673 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:07:34:778 ==>> [D][05:18:34][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 26
[D][05:18:34][COMM]read battery soc:255


2025-07-31 21:07:34:863 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:07:34:868 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:07:34:876 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:07:34:961 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:07:35:176 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:07:35:183 ==>> 检测【打开WIFI(3)】
2025-07-31 21:07:35:187 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:07:35:362 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:35][CAT1]gsm read msg sub id: 12
[D][05:18:35][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:07:35:472 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:07:35:477 ==>> 检测【扩展芯片hw】
2025-07-31 21:07:35:482 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:07:36:337 ==>> [D][05:18:36][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:36][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:0------------
[D][05:18:36][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:07:36:521 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:07:36:874 ==>>                                                                [D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]more than the number of battery plugs
[D][05:18:36][COMM]VBUS is 1
[D][05:18:36][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:36][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:36][COMM]Bat auth off fail, error:-1
[D][05:18:36][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:36][COMM]----- get Acckey 1 and value:1------------
[D][05:18:36][COMM]----- get Acckey 2 and value:1------------
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:36][COMM]file:B50 exist
[D][05:18:36][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:36][COMM]read file, len:10800

2025-07-31 21:07:36:979 ==>> , num:3
[D][05:18:36][COMM]Main Task receive event:65
[D][05:18:36][COMM]main task tmp_sleep_event = 80
[D][05:18:36][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:36][COMM]Main Task receive event:65 finished processing
[D][05:18:36][COMM]Main Task receive event:66
[D][05:18:36][COMM]Try to Auto Lock Bat
[D][05:18:36][COMM]Main Task receive event:66 finished processing
[D][05:18:36][COMM]Main Task receive event:60
[D][05:18:36][COMM]smart_helmet_vol=255,255
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get soc error
[E][05:18:36][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:36][COMM]report elecbike
[W][05:18:36][PROT]remove success[1629955116],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:36][COMM]Main Task receive event:60 finished processing
[D][05:18:36][COMM]Main Task receive event:61
[D][05:18:36][COMM][D301]:type:3, trace id:280
[D][05:18:36][COMM]id[], hw[000
[D][05:18:36][COMM]get mcMaincircuitVolt error
[D][05:18:36][M2M ]m2m_task: control_queue t

2025-07-31 21:07:37:084 ==>> ype:[M2M_GSM_POWER_ON]
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:36][PROT]index:1
[D][05:18:36][COMM]Receive Bat Lock cmd 0
[D][05:18:36][PROT]is_send:1
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x3
[D][05:18:36][PROT]msg_type:0x5d03
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]Sending traceid[9999999999900006]
[D][05:18:36][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:36][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:36][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:36][COMM]VBUS is 1
[D][05:18:36][COMM]get mcSubcircuitVolt error
[D][05:18:36][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:36][COMM]BAT CAN get state1 Fail 204
[D][05:18:36][COMM]BAT CAN get soc Fail, 204
[D][05:18:36][COMM]get bat work state err


2025-07-31 21:07:37:189 ==>> 
[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:36][COMM]Main Task receive event:61 finished processing
[D][05:18:36][COMM]--->crc16:0xb8a
[D][05:18:36][COMM]read file success
[W][05:18:36][COMM][Audio].l:[936].close hexlog save
[D][05:18:36][COMM]accel parse set 1
[D][05:18:36][COMM][Audio]mon:9,05:18:36
[D][05:18:36][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:36][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:36][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:36][COMM]read battery soc:255


2025-07-31 21:07:37:294 ==>> [D][05:18:37][COMM]48215 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:37:399 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:37][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:37][C

2025-07-31 21:07:37:429 ==>> OMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:37][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:07:37:504 ==>>                                                                                                                                                                                                                                                                

2025-07-31 21:07:37:581 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:07:37:586 ==>> 检测【扩展芯片boot】
2025-07-31 21:07:37:615 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:07:37:620 ==>> 检测【扩展芯片sw】
2025-07-31 21:07:37:649 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:07:37:654 ==>> 检测【检测音频FLASH】
2025-07-31 21:07:37:663 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:07:37:826 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:07:38:365 ==>> [D][05:18:38][COMM]49227 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][CAT1]SEND RAW data timeout
[D][05:18:38][CAT1]exec over: func id: 12, ret: -52


2025-07-31 21:07:38:606 ==>>                                  [D][05:18:38][PROT]index:1 1629955118
[D][05:18:38][PROT]is_send:0
[D][05:18:38][PROT]sequence_num:5
[D][05:18:38][PROT]retry_timeout:0
[D][05:18:38][PROT]retry_times:3
[D][05:18:38][PROT]send_path:0x2
[D][05:18:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:38][PROT]===========================================================
[W][05:18:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955118]
[D][05:18:38][PROT]===========================================================
[D][05:18:38][PROT]sending traceid [9999999999900006]
[D][05:18:38][PROT]Send_TO_M2M [1629955118]
[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:38][SAL ]sock send credit cnt[6]
[D][05:18:38][SAL ]sock send ind credit cnt[6]
[D][05:18:38][M2M ]m2m send data len[198]
[D][05:18:38][SAL ]Cellular task submsg id[10]
[D][05:18:38][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:38][CAT1]gsm read msg sub id: 15
[D][05:18:38][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:38][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:38][COMM]f:[drv_audio_ack_receive].wait ack timeout!![49441]
[D][05:18:38][COMM]f:[

2025-07-31 21:07:38:636 ==>> ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:38][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:07:38:787 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 21:07:39:312 ==>> [D][05:18:39][COMM]50238 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:39:541 ==>> [D][05:18:39][COMM]f:[drv_audio_ack_receive].wait ack timeout!![50466]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:07:40:314 ==>> [D][05:18:40][COMM]51249 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:40:808 ==>> [D][05:18:40][COMM]read battery soc:255


2025-07-31 21:07:41:334 ==>> [D][05:18:41][COMM]52260 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:41:735 ==>> [D][05:18:41][COMM]crc 108B
[D][05:18:41][COMM]flash test ok


2025-07-31 21:07:42:336 ==>> [D][05:18:42][COMM]53272 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:42:772 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:07:42:778 ==>> 检测【打开喇叭声音】
2025-07-31 21:07:42:787 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:07:42:812 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 21:07:42:993 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:42][COMM]file:A20 exist
[D][05:18:42][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:42][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[E][05:18:42][GNSS]GPS module no nmea data!
[D][05:18:42][GNSS]GPS reload stop. ret=0
[D][05:18:42][GNSS]GPS reload start. ret=0


2025-07-31 21:07:43:079 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:07:43:088 ==>> 检测【打开大灯控制】
2025-07-31 21:07:43:114 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:07:43:221 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:07:43:326 ==>> [D][05:18:43][COMM]54283 imu init OK
[D][05:18:4

2025-07-31 21:07:43:356 ==>> 3][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:43:408 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:07:43:413 ==>> 检测【关闭仪表供电3】
2025-07-31 21:07:43:418 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:07:43:553 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:43][COMM]set POWER 0


2025-07-31 21:07:43:769 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:07:43:778 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:07:43:803 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:07:43:935 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:07:44:115 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:07:44:120 ==>> 检测【读大灯电压】
2025-07-31 21:07:44:127 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:07:44:258 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[33502]


2025-07-31 21:07:44:348 ==>> [D][05:18:44][COMM]55294 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:44:451 ==>> 【读大灯电压】通过,【33502mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:07:44:459 ==>> 检测【关闭大灯控制2】
2025-07-31 21:07:44:475 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:07:44:625 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:07:44:823 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 21:07:44:837 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:07:44:843 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:07:44:848 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:07:45:051 ==>> [W][05:18:44][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:44][COMM]arm_hub read adc[5],val[69]


2025-07-31 21:07:45:169 ==>> 【关大灯控制后读大灯电压】通过,【69mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:07:45:174 ==>> 检测【打开WIFI(4)】
2025-07-31 21:07:45:179 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:07:45:373 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:45][COMM]56305 imu init OK
[D][05:18:45][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:45:514 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:07:45:519 ==>> 检测【EC800M模组版本】
2025-07-31 21:07:45:526 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:46:396 ==>> [D][05:18:46][COMM]57316 imu init OK
[D][05:18:46][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:07:46:563 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:46:831 ==>> [D][05:18:46][COMM]read battery soc:255


2025-07-31 21:07:47:390 ==>> [D][05:18:47][COMM]imu error,enter wait
[W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:07:47:605 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:48:599 ==>> [D][05:18:48][CAT1]exec over: func id: 15, ret: -93
[D][05:18:48][CAT1]sub id: 15, ret: -93

[D][05:18:48][SAL ]Cellular task submsg id[68]
[D][05:18:48][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:48][SAL ]socket send fail. id[4]
[D][05:18:48][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:48][CAT1]gsm read msg sub id: 24
[D][05:18:48][M2M ]m2m select fd[4]
[D][05:18:48][M2M ]socket[4] Link is disconnected
[D][05:18:48][M2M ]tcpclient close[4]
[D][05:18:48][SAL ]socket[4] has closed
[D][05:18:48][PROT]protocol read data ok
[E][05:18:48][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[D][05:18:48][CAT1]tx ret[13] >>> AT+GPSPWR=0

[E][05:18:48][PROT]M2M Send Fail [1629955128]
[D][05:18:48][PROT]CLEAN,SEND:1
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:48][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 21:07:48:658 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:48:963 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59709]
[D][05:18:48][COMM]accel parse set 0
[D][05:18:48][COMM][Audio].l:[1032].open hexlog save
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:48][COMM]file:A20 exist
[D][05:18:48][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:48][COMM]read file, len:15228, num:4
[D][05:18:48][COMM]read battery soc:255
[D][05:18:48][COMM]--->crc16:0x419c
[D][05:18:48][COMM]read file success
[D][05:18:48][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:48][COMM][Audio].l:[936].close hexlog save
[D][05:18:48][COMM]accel parse set 1
[D][05:18:48][COMM][Audio]mon:9,05:18:48
[D][05:18:48][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:48][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:07:49:426 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:07:49:700 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:49:901 ==>> [D][05:18:49][COMM]f:[drv_audio_ack_receive].wait ack timeout!![60818]
[D][05:18:49][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:49][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:07:50:489 ==>> [D][05:18:50][CAT1]tx ret[13] >>> AT+GPSPWR=0



2025-07-31 21:07:50:749 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:50:935 ==>> [D][05:18:50][COMM]read battery soc:255
[D][05:18:50][COMM]f:[drv_audio_ack_receive].wait ack timeout!![61847]
[D][05:18:50][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:50][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:07:51:492 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:07:51:800 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:51:969 ==>> [D][05:18:51][COMM]f:[drv_audio_ack_receive].wait ack timeout!![62875]
[D][05:18:51][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:51][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:07:52:213 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:07:52:520 ==>> [D][05:18:52][CAT1]exec over: func id: 24, ret: -181
[D][05:18:52][CAT1]sub id: 24, ret: -181

[D][05:18:52][CAT1]gsm read msg sub id: 23
[D][05:18:52][CAT1]tx ret[12] >>> AT+GPSCFG?



2025-07-31 21:07:52:843 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:52:851 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 21:07:53:070 ==>> [D][05:18:52][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:52][GNSS]recv submsg id[1]
[D][05:18:52][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[-181]
[D][05:18:52][GNSS]stop gps fail
[E][05:18:52][GNSS]GPS module no nmea data!
[D][05:18:52][GNSS]GPS reload stop. ret=0
[D][05:18:52][GNSS]GPS reload start. ret=0


2025-07-31 21:07:53:563 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:53][CAT1]exec over: func id: 23, ret: -151
[D][05:18:53][CAT1]sub id: 23, ret: -151

[D][05:18:53][CAT1]gsm read msg sub id: 12
[D][05:18:53][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:07:53:886 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:54:057 ==>> [D][05:18:53][GNSS]recv submsg id[1]
[D][05:18:53][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[-151]
[D][05:18:53][GNSS]start gps fail


2025-07-31 21:07:54:711 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:07:54:846 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 21:07:54:921 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:55:592 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:07:55:963 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:56:584 ==>> [D][05:18:56][CAT1]SEND RAW data timeout
[D][05:18:56][CAT1]exec over: func id: 12, ret: -52
[W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:56][CAT1]gsm read msg sub id: 12
[D][05:18:56][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:07:56:874 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 21:07:57:010 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:57:212 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:07:58:041 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:58:617 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:07:58:861 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 21:07:59:077 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:07:59:608 ==>> [D][05:18:59][CAT1]SEND RAW data timeout
[D][05:18:59][CAT1]exec over: func id: 12, ret: -52
[W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:59][CAT1]gsm read msg sub id: 10
[D][05:18:59][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:07:59:713 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:00:100 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:00:872 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 21:08:01:132 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:01:628 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:02:175 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:02:205 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:02:866 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 21:08:03:109 ==>> [E][05:19:03][GNSS]GPS module no nmea data!
[D][05:19:03][GNSS]GPS reload stop. ret=0
[D][05:19:03][GNSS]GPS reload start. ret=0


2025-07-31 21:08:03:199 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:03:677 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:04:244 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:04:304 ==>> [D][05:19:04][COMM]f:[drv_audio_ack_receive].wait ack timeout!![75218]
[D][05:19:04][COMM]accel parse set 0
[D][05:19:04][COMM][Audio].l:[1032].open hexlog save
[D][05:19:04][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:08:04:712 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:04:893 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 21:08:05:275 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:05:714 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:06:308 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:06:886 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 21:08:07:205 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:07:340 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:07:581 ==>> [D][05:19:07][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:08:07:777 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:08:371 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:08:881 ==>> [D][05:19:08][COMM]read battery soc:255


2025-07-31 21:08:09:419 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:09:704 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:09:809 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:10:448 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:10:914 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 21:08:11:497 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:11:866 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:12:212 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:12:531 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:12:911 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 21:08:13:199 ==>> [E][05:19:13][GNSS]GPS module no nmea data!
[D][05:19:13][GNSS]GPS reload stop. ret=0
[D][05:19:13][GNSS]GPS reload start. ret=0


2025-07-31 21:08:13:561 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:13:902 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:14:576 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:14:711 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:08:14:908 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 21:08:15:608 ==>> [D][05:19:15][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:08:15:617 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:08:16:151 ==>> [W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:15][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:15][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[W][05:19:15][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:08:16:640 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:08:16:650 ==>> #################### 【测试结束】 ####################
2025-07-31 21:08:16:865 ==>> 关闭5V供电
2025-07-31 21:08:16:874 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:08:16:900 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 21:08:16:961 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:08:17:870 ==>> 关闭5V供电成功
2025-07-31 21:08:17:878 ==>> 关闭33V供电
2025-07-31 21:08:17:889 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:08:17:961 ==>> 5A A5 02 5A A5 


2025-07-31 21:08:18:066 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:08:18:261 ==>> [D][05:19:18][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:18][FCTY]get_ext_48v_vol retry i = 8,volt = 12
[D][05:19:18][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 6


2025-07-31 21:08:18:884 ==>> 关闭33V供电成功
2025-07-31 21:08:18:893 ==>> 关闭3.7V供电
2025-07-31 21:08:18:901 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:08:18:961 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:08:19:464 ==>>  

