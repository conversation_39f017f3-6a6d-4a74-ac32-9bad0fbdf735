2025-07-31 18:20:44:173 ==>> MES查站成功:
查站序号:P510001005312B6D验证通过
2025-07-31 18:20:44:178 ==>> 扫码结果:P510001005312B6D
2025-07-31 18:20:44:179 ==>> 当前测试项目:SE51_PCBA
2025-07-31 18:20:44:181 ==>> 测试参数版本:2024.10.11
2025-07-31 18:20:44:182 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 18:20:44:185 ==>> 检测【打开透传】
2025-07-31 18:20:44:187 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 18:20:44:270 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 18:20:45:413 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 18:20:45:424 ==>> 检测【检测接地电压】
2025-07-31 18:20:45:427 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 18:20:45:577 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 18:20:45:700 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 18:20:45:702 ==>> 检测【打开小电池】
2025-07-31 18:20:45:705 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:20:45:774 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:20:45:982 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:20:45:984 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 18:20:45:987 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 18:20:46:066 ==>> 1A A1 00 00 01 
Get AD_V0 1289mV
OVER 150


2025-07-31 18:20:46:258 ==>> 【检测小电池分压(AD_VBAT)】通过,【1289mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 18:20:46:261 ==>> 检测【等待设备启动】
2025-07-31 18:20:46:263 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:20:46:513 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 18:20:46:694 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 18:20:47:299 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 18:20:47:438 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 18:20:47:806 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 18:20:48:281 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 18:20:48:392 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 18:20:48:395 ==>> 检测【产品通信】
2025-07-31 18:20:48:397 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 18:20:48:539 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 18:20:48:681 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 18:20:48:683 ==>> 检测【初始化完成检测】
2025-07-31 18:20:48:686 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 18:20:48:952 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE43C00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 18:20:49:293 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 18:20:49:295 ==>> 检测【关闭大灯控制1】
2025-07-31 18:20:49:297 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:20:49:336 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:49:516 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:20:49:588 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:20:49:590 ==>> 检测【打开仪表指令模式1】
2025-07-31 18:20:49:593 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:20:49:761 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:20:49:903 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:20:49:905 ==>> 检测【关闭仪表供电】
2025-07-31 18:20:49:908 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:20:50:067 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 18:20:50:188 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:20:50:191 ==>> 检测【关闭AccKey2供电1】
2025-07-31 18:20:50:193 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:20:50:342 ==>> [D][05:17:52][COMM]3640 imu init OK
[W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:50:480 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:20:50:482 ==>> 检测【关闭AccKey1供电1】
2025-07-31 18:20:50:484 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 18:20:50:649 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 18:20:50:769 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 18:20:50:772 ==>> 检测【关闭转刹把供电1】
2025-07-31 18:20:50:774 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:20:50:969 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE43D00] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:20:51:058 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:20:51:061 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 18:20:51:063 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:20:51:166 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:20:51:256 ==>> D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 30


2025-07-31 18:20:51:335 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:20:51:338 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 18:20:51:340 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 18:20:51:361 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:51:466 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 18:20:51:648 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 18:20:51:651 ==>> 该项需要延时执行
2025-07-31 18:20:51:881 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5002. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5003. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5003. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5004. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5004. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5004. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5005. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5005. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5005. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5006. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5006. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5007
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5007


2025-07-31 18:20:52:154 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]


2025-07-31 18:20:52:351 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:52:861 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 18:20:53:352 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[

2025-07-31 18:20:53:458 ==>> D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get m

2025-07-31 18:20:53:562 ==>> cMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]get bat work state err


2025-07-31 18:20:53:637 ==>> [W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
                                                                                                                                          

2025-07-31 18:20:54:380 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:55:186 ==>> [D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 18:20:55:399 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:55:661 ==>> 此处延时了:【4000】毫秒
2025-07-31 18:20:55:664 ==>> 检测【33V输入电压ADC】
2025-07-31 18:20:55:667 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:20:55:985 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:17:58][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:58][COMM]adc read left brake adc:12  volt:15 mv
[D][05:17:58][COMM]adc read right brake adc:6  volt:7 mv
[D][05:17:58][COMM]adc read throttle adc:14  volt:18 mv
[D][05:17:58][COMM]adc read battery ts volt:14 mv
[D][05:17:58][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:17:58][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 18:20:56:205 ==>> 【33V输入电压ADC】通过,【32982mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 18:20:56:208 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 18:20:56:212 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:20:56:276 ==>> 1A A1 00 00 FC 
Get AD_V2 1676mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2762mV
Get AD_V6 1992mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:20:56:411 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:20:56:484 ==>> 【TP7_VCC3V3(ADV2)】通过,【1676mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:56:488 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:20:56:503 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:56:506 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:20:56:508 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 18:20:56:526 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:20:56:529 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:20:56:551 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:20:56:553 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:20:56:579 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:20:56:582 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:20:56:675 ==>> 1A A1 00 00 FC 
Get AD_V2 1677mV
Get AD_V3 1658mV
Get AD_V4 0mV
Get AD_V5 2763mV
Get AD_V6 1990mV
Get AD_V7 1089mV
OVER 150


2025-07-31 18:20:56:765 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10011. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10012. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10013
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10013


2025-07-31 18:20:56:863 ==>> 【TP7_VCC3V3(ADV2)】通过,【1677mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:56:865 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:20:56:882 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1658mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:56:885 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:20:56:887 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 18:20:56:903 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:20:56:907 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:20:56:932 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:20:56:935 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:20:56:955 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:20:56:958 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:20:57:088 ==>> 1A A1 00 00 FC 
Get AD_V2 1677mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2762mV
Get AD_V6 1992mV
Get AD_V7 1090mV
OVER 150


2025-07-31 18:20:57:232 ==>> 【TP7_VCC3V3(ADV2)】通过,【1677mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:57:234 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 18:20:57:250 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:20:57:252 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 18:20:57:255 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 18:20:57:270 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:20:57:273 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 18:20:57:289 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 18:20:57:291 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 18:20:57:312 ==>> 【TP1_VCC12V(ADV7)】通过,【1090mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 18:20:57:314 ==>> 检测【打开WIFI(1)】
2025-07-31 18:20:57:319 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:20:57:336 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 18:20:57:498 ==>> [D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][HSDK]need to erase for write: is[0x0] ie[0x2E00]
[D][05:17:59][HSDK][0] flush to flash addr:[0xE43E00] --- write len --- [256]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 18:20:57:592 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:20:57:595 ==>> 检测【清空消息队列(1)】
2025-07-31 18:20:57:596 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:20:57:756 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:20:57:864 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:20:57:867 ==>> 检测【打开GPS(1)】
2025-07-31 18:20:57:869 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:20:58:061 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 18:20:58:139 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 18:20:58:142 ==>> 检测【打开GSM联网】
2025-07-31 18:20:58:143 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 18:20:58:430 ==>>                                        [D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+

2025-07-31 18:20:58:475 ==>> IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 18:20:58:676 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 18:20:58:689 ==>> 检测【打开仪表供电1】
2025-07-31 18:20:58:692 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 18:20:59:027 ==>>                                                                                                                            [D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087638906

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071536768

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01

2025-07-31 18:20:59:088 ==>> ][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 18:20:59:223 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 18:20:59:241 ==>> 检测【打开仪表指令模式2】
2025-07-31 18:20:59:244 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 18:20:59:346 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 18:20:59:451 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 18:20:59:521 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 18:20:59:524 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 18:20:59:527 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 18:20:59:831 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33224]
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][

2025-07-31 18:20:59:906 ==>> CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CSQ: 28,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"10.40.172.93"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 18:21:00:076 ==>> 【读取主控ADC采集的仪表电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:21:00:079 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 18:21:00:081 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:00:273 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:21:00:379 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 18:21:00:382 ==>> 检测【AD_V20电压】
2025-07-31 18:21:00:385 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:00:488 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:21:00:579 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:21:00:685 ==>> [D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][COMM]13728 imu init OK
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][HSDK][0] flush to flash addr:[0xE43F00] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display

2025-07-31 18:21:00:729 ==>>  state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:21:00:849 ==>> 本次取值间隔时间:351ms
2025-07-31 18:21:00:873 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:00:975 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:21:01:066 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 18:21:01:280 ==>> 本次取值间隔时间:305ms
2025-07-31 18:21:01:310 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:01:389 ==>> [D][05:18:03][COMM]read battery soc:255
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:21:01:418 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:21:01:493 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:21:01:523 ==>> 本次取值间隔时间:95ms
2025-07-31 18:21:01:559 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:01:660 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:21:01:768 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 18:21:01:952 ==>> 本次取值间隔时间:287ms
2025-07-31 18:21:01:982 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 18:21:02:044 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:21:02:089 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 18:21:02:149 ==>> 1A A1 10 00 00 
Get AD_V

2025-07-31 18:21:02:239 ==>> 本次取值间隔时间:149ms
2025-07-31 18:21:02:254 ==>> 20 1646mV
OVER 150
[D][05:18:04][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,24,,,43,60,,,42,59,,,42,26,,,40,1*7A

$GBGSV,4,2,13,38,,,40,21,,,39,39,,,39,33,,,38,1*7F

$GBGSV,4,3,13,16,,,37,13,,,27,3,,,82,9,,,43,1*70

$GBGSV,4,4,13,6,,,39,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1604.503,1604.503,51.392,2097152,2097152,2097152*43

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub i

2025-07-31 18:21:02:284 ==>> d: 23, ret: 6



2025-07-31 18:21:02:435 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 18:21:02:555 ==>> 本次取值间隔时间:313ms
2025-07-31 18:21:02:600 ==>> 【AD_V20电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:21:02:604 ==>> 检测【拉低OUTPUT2】
2025-07-31 18:21:02:606 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 18:21:02:679 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 18:21:02:899 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 18:21:02:902 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 18:21:02:906 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:21:03:063 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:21:03:168 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,24,,,43,3,,,41,60,,,41,38,,,41,1*4E

$GBGSV,5,2,17,59,,,40,26,,,40,21,,,40,39,,,39,1*78

$GBGSV,5,3,17,33,,,38,16,,,38,9,,,37,1,,,37,1*79

$GBGSV,5,4,17,13,,,36,2,,,35,

2025-07-31 18:21:03:173 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 18:21:03:177 ==>> 检测【拉高OUTPUT2】
2025-07-31 18:21:03:181 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 18:21:03:213 ==>> 6,,,34,5,,,32,1*47

$GBGSV,5,5,17,36,,,37,1*71

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1585.777,1585.777,50.725,2097152,2097152,2097152*4A



2025-07-31 18:21:03:273 ==>> 3A A3 02 01 A3 


2025-07-31 18:21:03:378 ==>> [D][05:18:05][COMM]read battery soc:255
ON_OUT2
OVER 150


2025-07-31 18:21:03:457 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 18:21:03:461 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 18:21:03:470 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 18:21:03:668 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:21:03:732 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 18:21:03:735 ==>> 检测【预留IO LED功能输出】
2025-07-31 18:21:03:738 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 18:21:03:973 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE44000] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 18:21:04:006 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 18:21:04:009 ==>> 检测【AD_V21电压】
2025-07-31 18:21:04:013 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:21:04:078 ==>> 1A A1 20 00 00 
Get AD_V21 1060mV
OVER 150


2025-07-31 18:21:04:183 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,42,3,,,41,60,,,41,42,,,41,1*4D

$GBGSV,5,2,18,38,,,40,59,,,40,26,,,40,21,,,39,1*76

$GBGSV,5,3,18,39,,,39,33,,,38,16,,,38,13,,,38,1*77

$GBGSV,5,4,18,9,,,37,1,,,37,2,,,35,6,,,35,1*72

$GBGSV,5,5,18,4,,,33,5,,,32,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1580.014,1580.014,50.537,20

2025-07-31 18:21:04:213 ==>> 97152,2097152,2097152*4B



2025-07-31 18:21:04:459 ==>> 本次取值间隔时间:437ms
2025-07-31 18:21:04:503 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 18:21:04:567 ==>> 1A A1 20 00 00 
Get AD_V21 1640mV
OVER 150


2025-07-31 18:21:04:783 ==>> 本次取值间隔时间:277ms
2025-07-31 18:21:04:828 ==>> 【AD_V21电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:21:04:831 ==>> 检测【关闭仪表供电2】
2025-07-31 18:21:04:835 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:21:05:062 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 18:21:05:133 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:21:05:136 ==>> 检测【关闭仪表指令模式】
2025-07-31 18:21:05:139 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 18:21:05:167 ==>> $G

2025-07-31 18:21:05:227 ==>> BGGA,102109.013,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,43,8,,,42,3,,,41,60,,,41,1*70

$GBGSV,5,2,19,42,,,41,38,,,41,59,,,40,26,,,40,1*7C

$GBGSV,5,3,19,13,,,40,21,,,39,39,,,39,33,,,38,1*7C

$GBGSV,5,4,19,16,,,38,9,,,37,1,,,37,6,,,36,1*48

$GBGSV,5,5,19,2,,,35,4,,,32,5,,,32,1*4B

$GBRMC,102109.013,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102109.013,0.000,1597.239,1597.239,51.098,2097152,2097152,2097152*5D



2025-07-31 18:21:05:378 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!
[D][05:18:07][COMM]read battery soc:255


2025-07-31 18:21:05:433 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 18:21:05:437 ==>> 检测【打开AccKey2供电】
2025-07-31 18:21:05:455 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 18:21:05:731 ==>> $GBGGA,102109.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,8,,,41,3,,,41,42,,,41,1*79

$GBGSV,5,2,20,38,,,41,60,,,40,59,,,40,26,,,40,1*77

$GBGSV,5,3,20,13,,,40,21,,,39,39,,,39,16,,,38,1*71

$GBGSV,5,4,20,33,,,37,9,,,37,1,,,37,6,,,36,1*4A

$GBGSV,5,5,20,2,,,35,4,,,32,5,,,32,14,,,39,1*4E

$GBRMC,102109.513,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102109.513,0.000,1590.690,1590.690,50.886,2097152,2097152,2097152*5E

[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 18:21:06:031 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 18:21:06:037 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 18:21:06:055 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:06:377 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:08][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:18:08][COMM]adc read left brake adc:9  volt:11 mv
[D][05:18:08][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:08][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:08][COMM]adc read battery ts volt:13 mv
[D][05:18:08][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:08][COMM]adc read throttle brake in adc:2  volt:3 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3360  volt:2707 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:21:06:618 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:21:06:623 ==>> 检测【关闭AccKey2供电2】
2025-07-31 18:21:06:627 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:21:06:714 ==>> $GBGGA,102110.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,43,3,,,41,42,,,41,38,,,41,1*4A

$GBGSV,5,2,20,8,,,40,60,,,40,59,,,40,26,,,40,1*45

$GBGSV,5,3,20,13,,,40,39,,,40,21,,,39,16,,,38,1*7F

$GBGSV,5,4,20,33,,,38,9,,,37,1,,,37,6,,,36,1*45

$GBGSV,5,5,20,14,,,35,2,,,35,4,,,32,5,,,32,1*42

$GBRMC,102110.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102110.513,0.000,1585.780,1585.780,50.729,2097152,2097152,2097152*5C



2025-07-31 18:21:06:789 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:21:06:986 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:21:06:989 ==>> 该项需要延时执行
2025-07-31 18:21:07:379 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 18:21:07:716 ==>> $GBGGA,102111.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,3,,,41,42,,,41,38,,,41,1*4B

$GBGSV,5,2,20,8,,,40,60,,,40,26,,,40,13,,,40,1*4B

$GBGSV,5,3,20,59,,,39,39,,,39,21,,,39,16,,,38,1*71

$GBGSV,5,4,20,33,,,37,9,,,37,1,,,37,6,,,37,1*4B

$GBGSV,5,5,20,14,,,36,2,,,35,4,,,32,5,,,32,1*41

$GBRMC,102111.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102111.513,0.000,1581.625,1581.625,50.587,2097152,2097152,2097152*5B



2025-07-31 18:21:08:737 ==>> $GBGGA,102112.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,3,,,41,42,,,41,38,,,41,1*49

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,8,,,39,1*47

$GBGSV,6,3,21,59,,,39,39,,,39,21,,,39,16,,,38,1*73

$GBGSV,6,4,21,33,,,37,9,,,37,1,,,37,6,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102112.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102112.513,0.000,1565.552,1565.552,50.089,2097152,2097152,2097152*53



2025-07-31 18:21:09:416 ==>> [D][05:18:11][COMM]read battery soc:255
[D][05:18:11][COMM]IMU: [1,0,-909] ret=24 AWAKE!


2025-07-31 18:21:09:583 ==>> [D][05:18:11][COMM]S->M yaw:INVALID
[D][05:18:11][COMM]IMU: [4,4,-1003] ret=28 AWAKE!


2025-07-31 18:21:09:688 ==>> $GBGGA,102113.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,3,,,41,42,,,41,38,,,40,1*48

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,8,,,39,1*47

$GBGSV,6,3,21,59,,,39,39,,,39,21,,,39,16,,,38,1*73

$GBGSV,6,4,21

2025-07-31 18:21:09:733 ==>> ,33,,,37,9,,,37,6,,,37,1,,,36,1*48

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102113.513,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102113.513,0.000,1561.603,1561.603,49.962,2097152,2097152,2097152*56



2025-07-31 18:21:09:991 ==>> 此处延时了:【3000】毫秒
2025-07-31 18:21:09:996 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 18:21:10:000 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:10:283 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:12][COMM]adc read out 24v adc:10  volt:252 mv
[D][05:18:12][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:12][COMM]adc read right brake adc:6  volt:7 mv
[D][05:18:12][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:12][COMM]adc read battery ts volt:17 mv
[D][05:18:12][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:12][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2416  volt:3892 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:21:10:559 ==>> [D][05:18:12][COMM]M->S yaw:INVALID


2025-07-31 18:21:10:664 ==>> $GBGGA,102114.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,3,,,41,42,,,41,38,,,40,1*48

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,8,,,39,1*47

$GBGSV,6,3,21,59,,,39

2025-07-31 18:21:10:709 ==>> ,39,,,39,21,,,39,16,,,38,1*73

$GBGSV,6,4,21,33,,,37,6,,,37,9,,,36,1,,,36,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102114.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102114.513,0.000,1559.629,1559.629,49.900,2097152,2097152,2097152*55



2025-07-31 18:21:10:712 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【252mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 18:21:10:741 ==>> 检测【打开AccKey1供电】
2025-07-31 18:21:10:744 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 18:21:10:814 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]fr

2025-07-31 18:21:10:844 ==>> m_peripheral_device_poweron type 5.... 


2025-07-31 18:21:11:001 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 18:21:11:005 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 18:21:11:007 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:21:11:074 ==>> 1A A1 00 40 00 
Get AD_V14 2656mV
OVER 150


2025-07-31 18:21:11:255 ==>> 原始值:【2656】, 乘以分压基数【2】还原值:【5312】
2025-07-31 18:21:11:284 ==>> 【读取AccKey1电压(ADV14)前】通过,【5312mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:21:11:288 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 18:21:11:292 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:11:379 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 18:21:11:589 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:13][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:18:13][COMM]adc read out 24v adc:10  volt:252 mv
[D][05:18:13][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:13][COMM]adc read right brake adc:10  volt:13 mv
[D][05:18:13][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:13][COMM]adc read battery ts volt:13 mv
[D][05:18:13][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:13][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:13][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:13][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:18:13][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:13][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:13][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:21:11:694 ==>>      A,102115.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,3,,,40,38,,,40,1*49

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,40,1*7D

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,36,1*48

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32

2025-07-31 18:21:11:739 ==>> ,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102115.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102115.513,0.000,1561.602,1561.602,49.961,2097152,2097152,2097152*53



2025-07-31 18:21:11:833 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 18:21:11:837 ==>> 检测【关闭AccKey1供电2】
2025-07-31 18:21:11:841 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 18:21:12:056 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 18:21:12:113 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 18:21:12:119 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 18:21:12:123 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 18:21:12:161 ==>> 1A A1 00 40 00 
Get AD_V14 2657mV
OVER 150


2025-07-31 18:21:12:375 ==>> 原始值:【2657】, 乘以分压基数【2】还原值:【5314】
2025-07-31 18:21:12:400 ==>> 【读取AccKey1电压(ADV14)后】通过,【5314mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 18:21:12:406 ==>> 检测【打开WIFI(2)】
2025-07-31 18:21:12:424 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:21:12:591 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE44100] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:14][CAT1]gsm read msg sub id: 12
[D][05:18:14][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:14][CAT1]<<< 
OK

[D][05:18:14][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:21:12:670 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:21:12:676 ==>> 检测【转刹把供电】
2025-07-31 18:21:12:681 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:21:12:696 ==>>                                                                                                                                                                                               6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102116.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102116.513,0.000,1563.575,1563.575,50.024,2097152,2097152,2097152*50



2025-07-31 18:21:12:801 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:21:12:941 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 18:21:12:948 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 18:21:12:969 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:21:13:044 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:21:13:274 ==>> +WIFISCAN:4,0,CC057790A741,-70
+WIFISCAN:4,1,CC057790A5C1,-78
+WIFISCAN:4,2,CC057790A7C0,-79
+WIFISCAN:4,3,CC057790A7C1,-80

[D][05:18:15][CAT1]wifi scan report total[4]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 18:21:13:379 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 18:21:13:469 ==>> [D][05:18:15][GNSS]recv submsg id[3]


2025-07-31 18:21:13:710 ==>> $GBGGA,102117.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,3,,,40,38,,,40,1*49

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,39,1*73

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,4,,,32,5,,,32,1*43

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102117.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102117.513,0.000,1561.599,1561.599,49.959,2097152,2097152,2097152*5A



2025-07-31 18:21:14:001 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:21:14:110 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:21:14:170 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<
1A A1 00 80 00 
Get AD_V15 2398mV
OVER 150


2025-07-31 18:21:14:275 ==>> 原始值:【2398】, 乘以分压基数【2】还原值:【4796】
2025-07-31 18:21:14:308 ==>> 【读取AD_V15电压(前)】通过,【4796mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:21:14:312 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 18:21:14:317 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 18:21:14:416 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:14:476 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2431mV
OVER 150


2025-07-31 18:21:14:581 ==>> 原始值:【2431】, 乘以分压基数【2】还原值:【4862】
2025-07-31 18:21:14:634 ==>> 【读取AD_V16电压(前)】通过,【4862mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 18:21:14:638 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 18:21:14:640 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:14:733 ==>> $GBGGA,102118.513,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,3,,,40,38,,,40,1*49

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,39,1*73

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102118.513,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102118.513,0.000,1563.569,1563.569,50.018,2097152,2097152,2097152*51



2025-07-31 18:21:14:988 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:17][COMM]adc read out 24v adc:9  volt:227 mv
[D][05:18:17][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:17][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:17][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:17][COMM]adc read battery ts volt:17 mv
[D][05:18:17][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3085  volt:5422 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2416  volt:3892 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 18:21:15:204 ==>> 【转刹把供电电压(主控ADC)】通过,【5422mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 18:21:15:210 ==>> 检测【转刹把供电电压】
2025-07-31 18:21:15:221 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:15:489 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:17][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:17][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:17][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:17][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:17][COMM]adc read battery ts volt:15 mv
[D][05:18:17][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3089  volt:5429 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2416  volt:3892 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1432  volt:33201 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:17][COMM]read battery soc:255
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:21:15:732 ==>> $GBGGA,102119.513,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,38,,,41,3,,,40,1*48

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,40,1*7D

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102119.513,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102119.513,0.000,1567.521,1567.521,50.147,2097152,2097152,2097152*5B



2025-07-31 18:21:15:771 ==>> 【转刹把供电电压】通过,【5429mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 18:21:15:774 ==>> 检测【关闭转刹把供电2】
2025-07-31 18:21:15:777 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:15:942 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:21:16:111 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 18:21:16:115 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 18:21:16:118 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:16:212 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 18:21:16:242 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:21:16:272 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 18:21:16:417 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:21:16:423 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 18:21:16:439 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:16:528 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:16:558 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE44200] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 18:21:16:633 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:16:738 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:16:744 ==>> $GBGGA,102120.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,38,,,41,3,,,40,1*48

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,39,1*73

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102120.513,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102120.513,0.000,1565.546,1565.546,50.083,2097152,2097152,2097152*58



2025-07-31 18:21:16:843 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:16:948 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:17:053 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:17:068 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<
[W][05:18:19][COMM]>>>>>Input command = ?<<<<<
00 00 00 00 00 
head err!


2025-07-31 18:21:17:158 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:17:264 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 18:21:17:279 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:21:17:369 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 18:21:17:474 ==>> [D][05:18:19][COMM]read battery soc:255
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 18:21:17:521 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:21:17:524 ==>> 检测【拉高OUTPUT3】
2025-07-31 18:21:17:527 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 18:21:17:579 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 18:21:17:684 ==>> $GBGGA,102121.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,3,,,41,38,,,40,1*48

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,39,1*73

$GBGSV,6,3,21,8,,,39,39

2025-07-31 18:21:17:729 ==>> ,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$GBGSV,6,5,21,14,,,36,2,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102121.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102121.513,0.000,1565.546,1565.546,50.083,2097152,2097152,2097152*59



2025-07-31 18:21:17:793 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 18:21:17:797 ==>> 检测【拉高OUTPUT4】
2025-07-31 18:21:17:802 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 18:21:17:879 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 18:21:18:075 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 18:21:18:080 ==>> 检测【拉高OUTPUT5】
2025-07-31 18:21:18:084 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 18:21:18:172 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 18:21:18:363 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 18:21:18:367 ==>> 检测【左刹电压测试1】
2025-07-31 18:21:18:373 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:18:767 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1738  volt:2291 mv
[D][05:18:20][COMM]adc read right brake adc:1735  volt:2287 mv
[D][05:18:20][COMM]adc read throttle adc:1735  volt:2287 mv
[D][05:18:20][COMM]adc read battery ts volt:17 mv
[D][05:18:20][COMM]adc read in 24v adc:1298  volt:32830 mv
[D][05:18:20][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1433  volt:33224 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,102122.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,24,,,42,42,,,41,3,,,41,38,,,41,1*49

$GBGSV,6,2,21,60,,,40,26,,,40,13,,,40,59,,,39,1*73

$GBGSV,6,3,21,8,,,39,39,,,39,21,,,39,16,,,38,1*47

$GBGSV,6,4,21,33,,,37,6,,,37,1,,,37,9,,,37,1*49

$G

2025-07-31 18:21:18:812 ==>> BGSV,6,5,21,14,,,36,2,,,35,5,,,33,4,,,32,1*42

$GBGSV,6,6,21,40,,,31,1*73

$GBRMC,102122.513,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102122.513,0.000,1567.522,1567.522,50.148,2097152,2097152,2097152*5C



2025-07-31 18:21:18:890 ==>> 【左刹电压测试1】通过,【2291】符合目标值【2250】至【2500】要求!
2025-07-31 18:21:18:895 ==>> 检测【右刹电压测试1】
2025-07-31 18:21:18:914 ==>> 【右刹电压测试1】通过,【2287】符合目标值【2250】至【2500】要求!
2025-07-31 18:21:18:918 ==>> 检测【转把电压测试1】
2025-07-31 18:21:18:943 ==>> 【转把电压测试1】通过,【2287】符合目标值【2250】至【2500】要求!
2025-07-31 18:21:18:947 ==>> 检测【拉低OUTPUT3】
2025-07-31 18:21:18:951 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 18:21:19:067 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 18:21:19:222 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 18:21:19:227 ==>> 检测【拉低OUTPUT4】
2025-07-31 18:21:19:232 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 18:21:19:265 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 18:21:19:416 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 18:21:19:494 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 18:21:19:498 ==>> 检测【拉低OUTPUT5】
2025-07-31 18:21:19:501 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 18:21:19:566 ==>> 3A A3 05 00 A3 


2025-07-31 18:21:19:671 ==>> OFF_OUT5
OVER 150
$GBGGA,102123.513,,,,,0,00,,,M,,M,,*6E

$GBGS

2025-07-31 18:21:19:731 ==>> A,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,3,,,41,38,,,41,1*4B

$GBGSV,6,2,22,13,,,41,60,,,40,26,,,40,59,,,40,1*7F

$GBGSV,6,3,22,8,,,40,39,,,39,21,,,39,16,,,39,1*4B

$GBGSV,6,4,22,33,,,37,6,,,37,1,,,37,9,,,37,1*4A

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,33,4,,,32,1*41

$GBGSV,6,6,22,40,,,31,45,,,31,1*73

$GBRMC,102123.513,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102123.513,0.000,1564.134,1564.134,50.061,2097152,2097152,2097152*57



2025-07-31 18:21:19:889 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 18:21:19:893 ==>> 检测【左刹电压测试2】
2025-07-31 18:21:19:896 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 18:21:20:188 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:18:22][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:22][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:22][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:22][COMM]adc read throttle adc:13  volt:17 mv
[D][05:18:22][COMM]adc read battery ts volt:19 mv
[D][05:18:22][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:22][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2415  volt:3891 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1434  volt:33247 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 18:21:20:450 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 18:21:20:454 ==>> 检测【右刹电压测试2】
2025-07-31 18:21:20:500 ==>> 【右刹电压测试2】通过,【21】符合目标值【0】至【50】要求!
2025-07-31 18:21:20:504 ==>> 检测【转把电压测试2】
2025-07-31 18:21:20:548 ==>> 【转把电压测试2】通过,【17】符合目标值【0】至【50】要求!
2025-07-31 18:21:20:554 ==>> 检测【晶振检测】
2025-07-31 18:21:20:576 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 18:21:20:801 ==>> $GBGGA,102124.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,3,,,41,38,,,41,1*4B

$GBGSV,6,2,22,13,,,41,60,,,40,26,,,40,59,,,39,1*71

$GBGSV,6,3,22,8,,,39,39,,,39,21,,,39,16,,,39,1*45

$GBGSV,6,4,22,33,,,37,6,,,37,1,,,37,9,,,37,1*4A

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,33,4,,,32,1*41

$GBGSV,6,6,22,40,,,31,45,,,30,1*72

$GBRMC,102124.513,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102124.513,0.000,1558.483,1558.483,49.883,2097152,2097152,2097152*5C

[W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 18:21:21:101 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 18:21:21:106 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 18:21:21:109 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:21:21:186 ==>> 1A A1 00 00 FC 
Get AD_V2 1677mV
Get AD_V3 1656mV
Get AD_V4 1643mV
Get AD_V5 2760mV
Get AD_V6 1989mV
Get AD_V7 1090mV
OVER 150


2025-07-31 18:21:21:414 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 18:21:21:417 ==>> 检测【检测BootVer】
2025-07-31 18:21:21:421 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:21:445 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 18:21:21:790 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071536768
[D][05:18:23][FCTY]HardwareID  = 867222087638906
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11700 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 32, adc = 1298
[D][05:18:23][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 126 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = 

2025-07-31 18:21:21:881 ==>> C4
[D][05:18:23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3833 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
$GBGGA,102125.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,3,,,41,38,,,41,1*4B

$GBGSV,6,2,22,13,,,41,60,,,40,26,,,40,8,,,40,1*4B

$GBGSV,6,3,22,59,,,39,39,,,39,21,,,39,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,37,9,,,37,1,,,36,1*4B

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,33,4,,,32,1*41

$GBGSV,6,6,22,40,,,30,45,,,30,1*73

$GBRMC,102125.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102125.513,0.000,1554.720,1554.720,49.769,2097152,2097152,2097152*56



2025-07-31 18:21:22:047 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 18:21:22:051 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 18:21:22:060 ==>> 检测【检测固件版本】
2025-07-31 18:21:22:078 ==>> [D][05:18:24][COMM]S->M yaw:INVALID


2025-07-31 18:21:22:082 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 18:21:22:086 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 18:21:22:093 ==>> 检测【检测蓝牙版本】
2025-07-31 18:21:22:119 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 18:21:22:124 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 18:21:22:128 ==>> 检测【检测MoBikeId】
2025-07-31 18:21:22:152 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 18:21:22:157 ==>> 提取到MoBikeId:9999999999
2025-07-31 18:21:22:164 ==>> 检测【检测蓝牙地址】
2025-07-31 18:21:22:174 ==>> 取到目标值:D018E736E0BE
2025-07-31 18:21:22:190 ==>> 【检测蓝牙地址】通过,【D018E736E0BE】符合目标值【】要求!
2025-07-31 18:21:22:194 ==>> 提取到蓝牙地址:D018E736E0BE
2025-07-31 18:21:22:198 ==>> 检测【BOARD_ID】
2025-07-31 18:21:22:224 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 18:21:22:227 ==>> 检测【检测充电电压】
2025-07-31 18:21:22:263 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 18:21:22:268 ==>> 检测【检测VBUS电压1】
2025-07-31 18:21:22:289 ==>> 【检测VBUS电压1】通过,【11700mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 18:21:22:296 ==>> 检测【检测充电电流】
2025-07-31 18:21:22:312 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 18:21:22:316 ==>> 检测【检测IMEI】
2025-07-31 18:21:22:320 ==>> 取到目标值:867222087638906
2025-07-31 18:21:22:333 ==>> 【检测IMEI】通过,【867222087638906】符合目标值【】要求!
2025-07-31 18:21:22:341 ==>> 提取到IMEI:867222087638906
2025-07-31 18:21:22:348 ==>> 检测【检测IMSI】
2025-07-31 18:21:22:370 ==>> 取到目标值:460130071536768
2025-07-31 18:21:22:375 ==>> [D][05:18:24][COMM]IMU: [3,1,-990] ret=26 AWAKE!


2025-07-31 18:21:22:379 ==>> 【检测IMSI】通过,【460130071536768】符合目标值【】要求!
2025-07-31 18:21:22:393 ==>> 提取到IMSI:460130071536768
2025-07-31 18:21:22:398 ==>> 检测【校验网络运营商(移动)】
2025-07-31 18:21:22:403 ==>> 取到目标值:460130071536768
2025-07-31 18:21:22:426 ==>> 【校验网络运营商(移动)】通过,【460130071536768】符合目标值【】要求!
2025-07-31 18:21:22:430 ==>> 检测【打开CAN通信】
2025-07-31 18:21:22:433 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 18:21:22:469 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 18:21:22:684 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:21:22:690 ==>> 检测【检测CAN通信】
2025-07-31 18:21:22:700 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 18:21:22:712 ==>> $GBGGA,102126.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,41,3,,,40,1*4B

$GBGSV,6,2,22,13,,,40,60,,,40,26,,,40,8,,,39,1*44

$GBGSV,6,3,22,59,,,39,39,,,39,21,,,39,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,37,9,,,36,1,,,36,1*4A

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,32,4,,,32,1*40

$GBGSV,6,6,22,40,,,30,45,,,29,1*7B

$GBRMC,102126.513,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102126.513,0.000,1541.530,1541.530,49.348,2097152,2097152,2097152*52



2025-07-31 18:21:22:802 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:21:22:862 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36154


2025-07-31 18:21:22:922 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:21:22:958 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 18:21:22:964 ==>> 检测【关闭CAN通信】
2025-07-31 18:21:22:986 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 18:21:22:997 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 18:21:23:072 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 18:21:23:245 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 18:21:23:250 ==>> 检测【打印IMU STATE】
2025-07-31 18:21:23:270 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:21:23:327 ==>> [D][05:18:25][COMM]M->S yaw:INVALID


2025-07-31 18:21:23:432 ==>> [D][05:18:25][COMM]read battery soc:255
[W][05:18:25][COMM]

2025-07-31 18:21:23:477 ==>> >>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:21:23:525 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:21:23:530 ==>> 检测【六轴自检】
2025-07-31 18:21:23:534 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 18:21:23:810 ==>> $GBGGA,102127.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,41,13,,,41,1*7B

$GBGSV,6,2,22,60,,,41,3,,,40,26,,,40,8,,,39,1*74

$GBGSV,6,3,22,59,,,39,39,,,39,21,,,39,16,,,38,1*70

$GBGSV,6,4,22,33,,,37,6,,,37,9,,,37,1,,,36,1*4B

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,32,4,,,31,1*43

$GBGSV,6,6,22,40,,,30,45,,,29,1*7B

$GBRMC,102127.513,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102127.513,0.000,1545.306,1545.306,49.475,2097152,2097152,2097152*5A

[W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 18:21:24:741 ==>> $GBGGA,102128.513,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,41,13,,,40,1*7A

$GBGSV,6,2,22,60,,,40,3,,,40,26,,,40,8,,,39,1*75

$GBGSV,6,3,22,59,,,39,39,,,39,21,,,39,16,,,38,1*70

$GBGSV,6,4,22,6,,,37,9,,,37,1,,,37,33,,,36,1*4B

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,32,4,,,31,1*43

$GBGSV,6,6,22,40,,,31,45,,,29,1*7A

$GBRMC,102128.513,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102128.513,0.000,1543.413,1543.413,49.407,2097152,2097152,2097152*50



2025-07-31 18:21:25:477 ==>> [D][05:18:27][COMM]read battery soc:255
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:21:25:762 ==>> $GBGGA,102129.513,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,42,42,,,41,38,,,41,13,,,41,1*7B

$GBGSV,6,2,22,3,,,41,60,,,40,26,,,40,59,,,40,1*4E

$GBGSV,6,3,22,8,,,39,39,,,39,21,,,39,16,,,38,1*44

$GBGSV,6,4,22,6,,,37,9,,,37,1,,,37,33,,,37,1*4A

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,32,4,,,32,1*40

$GBGSV,6,6,22,40,,,31,45,,,29,1*7A

$GBRMC,102129.513,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102129.513,0.000,1552.836,1552.836,49.709,2097152,2097152,2097152*5C

[D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38971 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-14,-10,4051]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 18:21:25:875 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 18:21:25:879 ==>> 检测【打印IMU STATE2】
2025-07-31 18:21:25:887 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 18:21:26:079 ==>> [D][05:18:28][HSDK][0] flush to flash addr:[0xE44300] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 18:21:26:183 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 18:21:26:190 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 18:21:26:203 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:21:26:277 ==>> 5A A5 02 5A A5 


2025-07-31 18:21:26:367 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:21:26:460 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:21:26:467 ==>> 检测【检测VBUS电压2】
2025-07-31 18:21:26:492 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:26:500 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05

2025-07-31 18:21:26:532 ==>> :18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 18:21:26:892 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071536768
[D][05:18:28][FCTY]HardwareID  = 867222087638906
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11700 mv
$GBGGA,102130.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,24,,,43,42,,,41,38,,,41,3,,,41,1*4B

$GBGSV,6,2,22,13,,,40,60,,,40,26,,,40,59,,,40,1*7E

$GBGSV,6,3,22,8,,,39,39,,,39,21,,,39,16,,,38,1*44

$GBGSV,6,4,22,6,,,37,9,,,37,1,,,37,33,,,37,1*4A

$GBGSV,6,5,22,14,,,36,2,,,35,5,,,32,4,,,32,1*40

$GBGSV,6,6,22,40,,,31,45,,,30,1*72

$GBRMC,102130.513,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,10213

2025-07-31 18:21:26:982 ==>> 0.513,0.000,1554.716,1554.716,49.765,2097152,2097152,2097152*5E

[D][05:18:28][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 8, adc = 350
[D][05:18:28][FCTY]Acckey1 vol = 5517 mv, Acckey2 vol = 202 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3833 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 18:21:27:267 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:27:636 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071536768
[D][05:18:29][FCTY]HardwareID  = 867222087638906
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 150 ma
[D][05:18:29][FCTY]VBUS        = 7300 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 175
[D][05:18:29][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 227 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29

2025-07-31 18:21:27:666 ==>> ][FCTY]Bat1         = 3833 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:21:27:741 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 18:21:27:846 ==>> [D][05:18:30][COMM]msg 0601 loss. last_tick:36143. cur_tick:41154. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41155


2025-07-31 18:21:27:877 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:28:240 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071536768
[D][05:18:30][FCTY]HardwareID  = 867222087638906
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 7300 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 147
[D][05:18:30][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 202 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3833 mv
[D][05:18:30][FC

2025-07-31 18:21:28:270 ==>> TY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:21:28:426 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:28:917 ==>>                                                                                                      al_device_poweroff type 16.... 
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[W][05:18:30][GNSS]stop locating
[D][05:18:30][GNSS]stop event:8
[D][05:18:30][GNSS]GPS stop. ret=0
[D][05:18:30][GNSS]all continue location stop
[D][05:18:30][COMM]report elecbike
[D][05:18:30][CAT1]gsm read msg sub id: 24
[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:

2025-07-31 18:21:29:022 ==>> 30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05

2025-07-31 18:21:29:127 ==>> :18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:30][CAT1]<<< 
OK

[D][05:18:30][CAT1]exec over: func id: 24, ret: 6
[D][05:18:30][CAT1]sub id: 24, ret: 6

[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][GNSS]recv submsg id[1]
[D][05:18:30][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:30][GNSS]location stop evt done evt
[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5920EDCE87D86517DF2E7780DCBEB003C5E83E19E9122F4FD264F268C0EB9D2016AA27DCEE69CFB67390B9946F49ABD5C97E7EA6A1E51980820D5BA70BC838ABD3946B6D4A055256F1CC0BD4E3FC1AA742CE1
[D][

2025-07-31 18:21:29:232 ==>> 05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071536768
[D][05:18:30][FCTY]HardwareID  = 867222087638906
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:30][FCTY]Bat         = 3844 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATI

2025-07-31 18:21:29:307 ==>> D= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 3, adc = 121
[D][05:18:30][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 177 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3833 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:21:29:475 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:21:29:832 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:31][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========
[D][05:18:31][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:31][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:31][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:31][FCTY]DeviceID    = 460130071536768
[D][05:18:31][FCTY]HardwareID  = 867222087638906
[D][05:18:31][FCTY]MoBikeID    = 9999999999
[D][05:18:31][FCTY]LockID      = FFFFFFFFFF
[D][05:18:31][FCTY]BLEFWVersion= 105
[D][05:18:31][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:18:31][FCTY]Bat         = 3884 mv
[D][05:18:31][FCTY]Current     = 0 ma
[D][05:18:31][FCTY]VBUS        = 5000 mv
[D][05:18:31][FCTY]TEMP= 0,BATID= 87,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:31][FCTY]Ext battery vol = 2, adc = 109
[D][05:18:31][FCTY]Acckey1 vol = 5523 mv, Acckey2 vol = 177 mv
[D][05:18:31][FCTY]Bike Type flag is invalied
[D][05:18:31][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:31][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:31][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:31][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:31][FCTY]C

2025-07-31 18:21:29:877 ==>> AT1_GNSS_VERSION = V3465b5b1
[D][05:18:31][FCTY]Bat1         = 3833 mv
[D][05:18:31][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:31][FCTY]==========Modules-nRF5340 ==========


2025-07-31 18:21:30:001 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 18:21:30:007 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 18:21:30:015 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:21:30:073 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:21:30:162 ==>> [D][05:18:32][COMM]read battery soc:255
[D][05:18:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 25


2025-07-31 18:21:30:287 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:21:30:292 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 18:21:30:297 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:21:30:375 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:21:30:560 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 18:21:30:565 ==>> 检测【打开WIFI(3)】
2025-07-31 18:21:30:570 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:21:30:797 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:33][CAT1]gsm read msg sub id: 12
[D][05:18:33][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:33][CAT1]<<< 
OK

[D][05:18:33][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:21:30:842 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:21:30:847 ==>> 检测【扩展芯片hw】
2025-07-31 18:21:30:852 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 18:21:31:069 ==>> [D][05:18:33][HSDK][0] flush to flash addr:[0xE44400] --- write len --- [256]
[W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 18:21:31:146 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 18:21:31:151 ==>> 检测【扩展芯片boot】
2025-07-31 18:21:31:326 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 18:21:31:331 ==>> 检测【扩展芯片sw】
2025-07-31 18:21:31:479 ==>> +WIFISCAN:4,0,CC057790A7C0,-77
+WIFISCAN:4,1,CC057790A7C1,-77
+WIFISCAN:4,2,CC057790A5C0,-79
+WIFISCAN:4,3,CC057790A5C1,-79

[D][05:18:33][CAT1]wifi scan report total[4]


2025-07-31 18:21:31:540 ==>>                                       

2025-07-31 18:21:31:586 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 18:21:31:594 ==>> 检测【检测音频FLASH】
2025-07-31 18:21:31:601 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 18:21:32:440 ==>> [D][05:18:33][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:33][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:33][COMM]----- get Acckey 1 and value:1------------
[D][05:18:33][COMM]----- get Acckey 2 and value:0------------
[D][05:18:33][COMM]------------ready to Power on Acckey 2------------
[W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]more than the number of battery plugs
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:34][COMM]Bat auth off fail, error:-1
[D][05:18:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:34][COMM]----- get Acckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:34][COMM]----- get A

2025-07-31 18:21:32:546 ==>> cckey 1 and value:1------------
[D][05:18:34][COMM]----- get Acckey 2 and value:1------------
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:B50 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:34][COMM]read file, len:10800, num:3
[D][05:18:34][COMM]Main Task receive event:65
[D][05:18:34][COMM]main task tmp_sleep_event = 80
[D][05:18:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:34][COMM]Main Task receive event:65 finished processing
[D][05:18:34][COMM]Main Task receive event:66
[D][05:18:34][COMM]Try to Auto Lock Bat
[D][05:18:34][COMM]Main Task receive event:66 finished processing
[D][05:18:34][COMM]Main Task receive event:60
[D][05:18:34][COMM]smart_helmet_vol=255,255
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get soc error
[E][05:18:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:34][COMM]report elecbike
[W][05:18:34][PROT]remove success[1629955114],send_path[3],type[0000],priority[0],index[1],use

2025-07-31 18:21:32:650 ==>> d[0]
[W][05:18:34][PROT]add success [1629955114],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:34][COMM]Main Task receive event:60 finished processing
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]index:1
[D][05:18:34][PROT]is_send:1
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x3
[D][05:18:34][PROT]msg_type:0x5d03
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]Sending traceid[9999999999900006]
[D][05:18:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]Receive Bat Lock cmd 0
[D][05:18:34][COMM]VBUS is 1
[D][05:18:34][COMM]Main Task re

2025-07-31 18:21:32:755 ==>> ceive event:61
[D][05:18:34][COMM][D301]:type:3, trace id:280
[D][05:18:34][COMM]id[], hw[000
[D][05:18:34][COMM]get mcMaincircuitVolt error
[D][05:18:34][COMM]get mcSubcircuitVolt error
[D][05:18:34][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:34][COMM]BAT CAN get state1 Fail 204
[D][05:18:34][COMM]BAT CAN get soc Fail, 204
[D][05:18:34][COMM]get bat work state err
[W][05:18:34][PROT]remove success[1629955114],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:34][PROT]add success [1629955114],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:34][COMM]Main Task receive event:61 finished processing
[D][05:18:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:34][COMM]--->crc16:0xb8a
[D][05:18:34][COMM]read file success
[D][05:18:34][HSDK][0] flush to flash addr:[0xE44500] --- write len --- [256]
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[

2025-07-31 18:21:32:860 ==>> D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_

2025-07-31 18:21:32:965 ==>> audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]read battery soc:255
[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms


2025-07-31 18:21:33:979 ==>> [D][05:18:36][PROT]CLEAN,SEND:0
[D][05:18:36][PROT]index:1 1629955116
[D][05:18:36][PROT]is_send:0
[D][05:18:36][PROT]sequence_num:5
[D][05:18:36][PROT]retry_timeout:0
[D][05:18:36][PROT]retry_times:3
[D][05:18:36][PROT]send_path:0x2
[D][05:18:36][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:36][PROT]===========================================================
[W][05:18:36][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955116]
[D][05:18:36][PROT]===========================================================
[D][05:18:36][PROT]sending traceid [9999999999900006]
[D][05:18:36][PROT]Send_TO_M2M [1629955116]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:36][SAL ]sock send credit cnt[6]
[D][05:18:36][SAL ]sock send ind credit cnt[6]
[D][05:18:36][M2M ]m2m send data len[198]
[D][05:18:36][SAL ]Cellular task submsg id[10]
[D][05:18:36][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:36][CAT1]gsm read msg sub id: 15
[D][05:18:36][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To Server[198][201] ... ->:
0063B98D113311331133113311331B88B30D3809C0F87268A690C74BAAC293BD576F6D31047DEE8D9718C777820F14DFBC6613C8E6F126E83A4C62DDE

2025-07-31 18:21:34:054 ==>> E8FD08A67D175B35C7E3ABDEF875AFC8770F131900AFF6C9BD2869D1B36C6B960274D013EEEAC
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 18:21:34:159 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 18:21:34:770 ==>> [D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:21:34:875 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 18:21:35:956 ==>> [D][05:18:38][COMM]49091 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok
[D][05:18:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:38][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:38][COMM]accel parse set 0
[D][05:18:38][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:21:36:168 ==>> [D][05:18:38][COMM]read battery soc:255


2025-07-31 18:21:36:678 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 18:21:36:687 ==>> 检测【打开喇叭声音】
2025-07-31 18:21:36:714 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 18:21:37:373 ==>> [D][05:18:39][COMM]50102 imu init OK
[W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:39][COMM]read file, len:15228, num:4
[D][05:18:39][COMM]--->crc16:0x419c
[D][05:18:39][COMM]read file success
[W][05:18:39][COMM][Audio].l:[936].close hexlog save
[D][05:18:39][COMM]accel parse set 1
[D][05:18:39][COMM][Audio]mon:9,05:18:39
[D][05:18:39][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].

2025-07-31 18:21:37:467 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 18:21:37:475 ==>> 检测【打开大灯控制】
2025-07-31 18:21:37:498 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 18:21:37:505 ==>> l:[691].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[

2025-07-31 18:21:37:584 ==>> D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:39][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 18:21:37:658 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 18:21:37:973 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 18:21:37:983 ==>> 检测【关闭仪表供电3】
2025-07-31 18:21:37:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 18:21:38:179 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:40][COMM]set POWER 0
[D][05:18:40][COMM]read battery soc:255


2025-07-31 18:21:38:283 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 18:21:38:292 ==>> 检测【关闭AccKey2供电3】
2025-07-31 18:21:38:315 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 18:21:38:435 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 18:21:38:938 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 18:21:38:944 ==>> 检测【读大灯电压】
2025-07-31 18:21:38:957 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:21:39:240 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3432E6689E70D676FA8BD20838472F293E7CF5C02A42652FA1F80F62E5E15E9C939E00E3C9420E3CC9BF6C63AA9212306F20C

2025-07-31 18:21:39:315 ==>> 9DBC94C031DE75E14ABEC3DF1962CEAE1ED16F677660AE890B15DF3FEC57F4CD
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]
[W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[33340]


2025-07-31 18:21:39:471 ==>> 【读大灯电压】通过,【33340mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 18:21:39:477 ==>> 检测【关闭大灯控制2】
2025-07-31 18:21:39:483 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 18:21:39:635 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 18:21:39:751 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 18:21:39:761 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 18:21:39:788 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 18:21:39:859 ==>> [D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:21:39:964 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:42][COMM]arm_hub read adc[5],val[92]


2025-07-31 18:21:40:038 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 18:21:40:048 ==>> 检测【打开WIFI(4)】
2025-07-31 18:21:40:073 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 18:21:40:177 ==>> [D][05:18:42][COMM]read battery soc:255


2025-07-31 18:21:40:327 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:42][CAT1]<<< 
OK

[D][05:18:42][CAT1]exec over: func id: 12, ret: 6


2025-07-31 18:21:40:369 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 18:21:40:378 ==>> 检测【EC800M模组版本】
2025-07-31 18:21:40:385 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 18:21:40:569 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:42][CAT1]gsm read msg sub id: 12
[D][05:18:42][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 18:21:40:812 ==>> [D][05:18:43][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:43][CAT1]exec over: func id: 12, ret: 132


2025-07-31 18:21:40:872 ==>>                                       [D][05:18:43][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:21:40:911 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 18:21:40:921 ==>> 检测【配置蓝牙地址】
2025-07-31 18:21:40:930 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 18:21:41:068 ==>> +WIFISCAN:4,0,CC057790A740,-72
+WIFISCAN:4,1,CC057790A5C1,-75
+WIFISCAN:4,2,CC057790A7C0,-76
+WIFISCAN:4,3,CC057790A7C1,-76

[D][05:18:43][CAT1]wifi scan report total[4]
[W][05:18:43][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 18:21:41:113 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:D018E736E0BE>】
2025-07-31 18:21:41:280 ==>> recv ble 1
recv ble 2
ble set mac ok :d0,18,e7,36,e0,be
enable filters ret : 0

2025-07-31 18:21:41:391 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 18:21:41:402 ==>> 检测【BLETEST】
2025-07-31 18:21:41:410 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 18:21:41:478 ==>> 4A A4 01 A4 4A 


2025-07-31 18:21:41:583 ==>> [D][05:18:43][GNSS]recv submsg id[3]
recv ble 1
recv ble 2
<BSJ*MAC:D018E736E0BE*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9D018E736E0BE99999OVER 150


2025-07-31 18:21:41:883 ==>> [D][05:18:44][COMM]55185 imu init OK
[D][05:18:44][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 18:21:42:199 ==>> [D][05:18:44][COMM]read battery soc:255


2025-07-31 18:21:42:442 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 18:21:42:456 ==>> 该项需要延时执行
2025-07-31 18:21:42:499 ==>> [D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:44][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:44][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1012].open hexlog save


2025-07-31 18:21:42:876 ==>> [D][05:18:45][COMM]56196 imu init OK


2025-07-31 18:21:44:430 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][COMM]read battery soc:255
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]Send Dat

2025-07-31 18:21:44:505 ==>> a To Server[198][201] ... ->:
0063B981113311331133113311331B88B321C5612ED6AA4EA3639F46518735A79F449139222427291CAFA866C5494AA37A0DBFB9A485E2EB3948E9EE167333B83826B4723792E12F4A46B97EAA24D400D1D9787E7A6FA35504D811BBEAA328A849E9B6
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]


2025-07-31 18:21:46:194 ==>> [D][05:18:48][COMM]read battery soc:255


2025-07-31 18:21:48:220 ==>> [D][05:18:50][COMM]read battery soc:255


2025-07-31 18:21:49:650 ==>> [D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]Send Data To Server[198][201]

2025-07-31 18:21:49:725 ==>>  ... ->:
0063B982113311331133113311331B88B53350FB5FA8933B52DB9533B9CC79357C7D5D9699F304894B50BFBF4CF78370AB7F7C493040F9A705AABF6B20F60B8019185AF05C720150646DFCCBFC7AD4A38C3137CAD4F1E77F4E83C3B60AA02B3AC8A7FF
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:51][PROT]M2M Send ok [1629955131]


2025-07-31 18:21:50:208 ==>> [D][05:18:52][COMM]read battery soc:255


2025-07-31 18:21:52:232 ==>> [D][05:18:54][COMM]read battery soc:255


2025-07-31 18:21:52:446 ==>> 此处延时了:【10000】毫秒
2025-07-31 18:21:52:454 ==>> 检测【检测WiFi结果】
2025-07-31 18:21:52:461 ==>> WiFi信号:【CC057790A741】,信号值:-70
2025-07-31 18:21:52:483 ==>> WiFi信号:【CC057790A5C1】,信号值:-78
2025-07-31 18:21:52:489 ==>> WiFi信号:【CC057790A7C0】,信号值:-79
2025-07-31 18:21:52:511 ==>> WiFi信号:【CC057790A7C1】,信号值:-80
2025-07-31 18:21:52:520 ==>> WiFi信号:【CC057790A5C0】,信号值:-79
2025-07-31 18:21:52:543 ==>> WiFi信号:【CC057790A740】,信号值:-72
2025-07-31 18:21:52:552 ==>> WiFi数量【6】, 最大信号值:-70
2025-07-31 18:21:52:573 ==>> 检测【检测GPS结果】
2025-07-31 18:21:52:605 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:21:52:662 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all continue location stop
[W][05:18:54][GNSS]stop locating
[D][05:18:54][GNSS]all sing location stop


2025-07-31 18:21:53:458 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 18:21:53:467 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:53:477 ==>> 定位已等待【1】秒.
2025-07-31 18:21:53:870 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:55][COMM]Open GPS Module...
[D][05:18:55][COMM]LOC_MODEL_CONT
[D][05:18:55][GNSS]start event:8
[D][05:18:55][GNSS]GPS start. ret=0
[D][05:18:55][CAT1]gsm read msg sub id: 23
[W][05:18:55][GNSS]start cont locating
[D][05:18:55][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:55][CAT1]<<< 
+GPSCFG:0,0,115200,1,0,65472,0,1,1

OK

[D][05:18:55][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:56][CAT1]<<< 
OK

[D][05:18:56][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 18:21:54:234 ==>> [D][05:18:56][COMM]read battery soc:255


2025-07-31 18:21:54:461 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:54:471 ==>> 定位已等待【2】秒.
2025-07-31 18:21:54:584 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 18:21:54:810 ==>> [D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20053030] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK


2025-07-31 18:21:55:254 ==>> [D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 18:21:55:464 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:55:474 ==>> 定位已等待【3】秒.
2025-07-31 18:21:55:555 ==>> [D][05:18:57][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,60,,,41,59,,,40,38,,,40,24,,,37,1*72

$GBGSV,3,2,11,26,,,33,3,,,50,8,,,41,4,,,38,1*47

$GBGSV,3,3,11,5,,,38,13,,,38,6,,,36,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1583.707,1583.707,50.662,2097152,2097152,2097152*48

[D][05:18:57][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:57][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:57][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:57][CAT1]<<< 
OK

[D][05:18:57][CAT1]exec over: func id: 23, ret: 6
[D][05:18:57][CAT1]sub id: 23, ret: 6

[D][05:18:57][CAT1]gsm read msg sub id: 15
[D][05:18:57][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:57][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B56C94A0D0B442C8F80F5155133D7229AE5ECB96B69CB62C4B2D9696504056BB22F58851EF969B3367A69FFBC243D2327155E78784F5FA1052EC3F274F246851421B5201B69AA12C14B695279C089B0548C481
[D][05:18:57][CAT1]<<< 
SEND OK

[D][05:18:57][CAT1]exec over: func id: 15, ret: 11
[D][05:18:57][CAT1]sub id: 

2025-07-31 18:21:55:600 ==>> 15, ret: 11

[D][05:18:57][SAL ]Cellular task submsg id[68]
[D][05:18:57][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:57][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:57][M2M ]g_m2m_is_idle become true
[D][05:18:57][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:57][PROT]M2M Send ok [1629955137]


2025-07-31 18:21:55:660 ==>>                                                                                             

2025-07-31 18:21:56:231 ==>> [D][05:18:58][COMM]read battery soc:255


2025-07-31 18:21:56:337 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,60,,,41,38,,,41

2025-07-31 18:21:56:382 ==>> ,24,,,40,8,,,39,1*4C

$GBGSV,4,2,13,59,,,39,21,,,38,16,,,38,13,,,37,1*76

$GBGSV,4,3,13,6,,,36,42,,,36,26,,,35,4,,,32,1*74

$GBGSV,4,4,13,1,,,36,1*40

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1561.577,1561.577,49.937,2097152,2097152,2097152*4F



2025-07-31 18:21:56:472 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:56:481 ==>> 定位已等待【4】秒.
2025-07-31 18:21:57:415 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,38,,,41,24,,,41,60,,,40,3,,,40,1*43

$GBGSV,5,2,18,8,,,39,59,,,39,13,,,39,21,,,38,1*4C

$GBGSV,5,3,18,16,,,38,42,,,38,39,,,38,1,,,37,1*4C

$GBGSV,5,4,18,26,,,37,6,,,36,2,,,35,4,,,32,1*4C

$GBGSV,5,5,18,5,,,32,40,,,32,1*4E

$GBRMC,,V,,,,,,,310725,0.0,E,N,V*52

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1547.774,1547.774,49.511,2097152,2097152,2097152*47



2025-07-31 18:21:57:475 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:57:484 ==>> 定位已等待【5】秒.
2025-07-31 18:21:58:252 ==>> [D][05:19:00][COMM]read battery soc:255


2025-07-31 18:21:58:432 ==>> $GBGGA,102202.214,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,38,,,41,3,,,41,60,,,40,1*40

$GBGSV,5,2,19,13,,,40,42,,,40,8,,,39,59,,,39,1*49

$GBGSV,5,3,19,39,,,39,26,,,39,21,,,38,16,,,38,1*72

$GBGSV,5,4,19,1,,,37,6,,,36,2,,,35,4,,,32,1*78

$GBGSV,5,5,19,5,,,32,40,,,31,9,,,48,1*79

$GBRMC,102202.214,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102202.214,0.000,780.503,780.503,713.788,2097152,2097152,2097152*67



2025-07-31 18:21:58:477 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:58:487 ==>> 定位已等待【6】秒.
2025-07-31 18:21:58:739 ==>> $GBGGA,102202.514,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,24,,,42,3,,,41,38,,,41,13,,,40,1*45

$GBGSV,5,2,18,60,,,40,42,,,40,8,,,39,39,,,39,1*4A

$GBGSV,5,3,18,59,,,39,26,,,39,16,,,38,21,,,38,1*75

$GBGSV,5,4,18,1,,,37,6,,,36,2,,,35,5,,,32,1*78

$GBGSV,5,5,18,4,,,32,40,,,31,1*4C

$GBRMC,102202.514,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102202.514,0.000,780.503,780.503,713.788,2097152,2097152,2097152*60



2025-07-31 18:21:59:479 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:21:59:488 ==>> 定位已等待【7】秒.
2025-07-31 18:21:59:729 ==>> $GBGGA,102203.514,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,3,,,41,38,,,41,42,,,41,1*41

$GBGSV,5,2,19,13,,,40,60,,,40,8,,,39,39,,,39,1*4F

$GBGSV,5,3,19,59,,,39,21,,,39,26,,,39,16,,,38,1*75

$GBGSV,5,4,19,9,,,37,1,,,37,6,,,36,2,,,35,1*70

$GBGSV,5,5,19,5,,,32,4,,,32,40,,,31,1*79

$GBRMC,102203.514,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102203.514,0.000,781.893,781.893,715.059,2097152,2097152,2097152*6C



2025-07-31 18:22:00:257 ==>> [D][05:19:02][COMM]read battery soc:255


2025-07-31 18:22:00:486 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:00:495 ==>> 定位已等待【8】秒.
2025-07-31 18:22:00:890 ==>> [D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D

2025-07-31 18:22:00:995 ==>> ][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE849A16EAD31AEF7D427B28B76A73CF6B21D6997A43B330E2A00B76022FE724D9EC23E8D06C722A71BC7E975444201D8261DD
$GBGGA,102204.514,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,24,,,42,3,,,41,38,,,41,42,,,41,1*41

$GBGSV,5,2,19,13,,,40,60,,,40,26,,,40,8,,,39,1*4F

$GBGSV,5,3,19,39,,,39,59,,,39,16,,,38,21,,,38,1*7A

$GBGSV,5,4,19,9,,,37,1,,,37,6,,,37,2,,,35,1*71

$GBGSV,5,5,19,5,,,32,4,,,32,40,,,31,1*79

$GBRMC,102204.514,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102204.514,0.000,782.981,782.981,716.054,2097152,2097152,2097152*65

[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:03][M2M ]g_m2m_is_idle become true
[D][05:19:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:03][PROT]M2M Send ok [1629955143]


2025-07-31 18:22:01:489 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:01:499 ==>> 定位已等待【9】秒.
2025-07-31 18:22:01:729 ==>> $GBGGA,102205.514,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,24,,,42,38,,,41,42,,,41,13,,,40,1*7B

$GBGSV,5,2,20,60,,,40,3,,,40,26,,,40,8,,,39,1*74

$GBGSV,5,3,20,39,,,39,59,,,39,16,,,38,21,,,38,1*70

$GBGSV,5,4,20,9,,,37,1,,,37,6,,,37,2,,,35,1*7B

$GBGSV,5,5,20,5,,,32,4,,,32,7,,,31,40,,,31,1*46

$GBRMC,102205.514,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,102205.514,0.000,774.880,774.880,708.647,2097152,2097152,2097152*6F



2025-07-31 18:22:02:255 ==>> [D][05:19:04][COMM]read battery soc:255


2025-07-31 18:22:02:495 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:02:506 ==>> 定位已等待【10】秒.
2025-07-31 18:22:02:774 ==>> $GBGGA,102202.520,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,243,40,24,66,240,42,6,63,309,37,16,63,315,38,1*49

$GBGSV,5,2,20,3,62,191,40,38,60,172,40,8,57,202,39,39,53,333,39,1*71

$GBGSV,5,3,20,59,52,130,39,1,48,126,36,2,46,239,35,9,41,287,37,1*47

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,32,7,17,181,31,1*4D

$GBGSV,5,5,20,40,14,168,31,21,2,305,38,42,,,41,26,,,40,1*47

$GBRMC,102202.520,V,,,,,,,310725,1.0,E,N,V*49

$GBVTG,246.18,T,,M,0.305,N,0.564,K,N*28

$GBGST,102202.520,0.065,0.292,0.243,0.428,5.986,13,47*73



2025-07-31 18:22:03:497 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:03:507 ==>> 定位已等待【11】秒.
2025-07-31 18:22:03:768 ==>> $GBGGA,102203.500,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,243,40,24,66,240,42,6,63,309,37,16,63,315,38,1*49

$GBGSV,5,2,20,3,62,191,40,38,60,172,41,8,57,202,39,39,53,333,39,1*70

$GBGSV,5,3,20,59,52,130,39,1,48,126,36,2,46,239,35,9,41,287,37,1*47

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,32,7,17,181,31,1*4D

$GBGSV,5,5,20,40,14,168,31,21,2,305,39,42,,,41,26,,,40,1*46

$GBGSV,1,1,02,24,66,240,42,38,60,172,39,5*75

$GBRMC,102203.500,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,246.18,T,,M,0.000,N,0.001,K,N*28

$GBGST,102203.500,0.470,0.468,0.370,0.688,4.574,8.862,39*5B



2025-07-31 18:22:04:271 ==>> [D][05:19:06][COMM]read battery soc:255


2025-07-31 18:22:04:511 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:04:533 ==>> 定位已等待【12】秒.
2025-07-31 18:22:04:767 ==>> $GBGGA,102204.500,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,242,41,24,66,240,42,6,63,309,37,16,63,315,38,1*49

$GBGSV,5,2,20,3,62,191,41,38,60,172,41,8,57,202,40,39,53,333,39,1*7F

$GBGSV,5,3,20,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*46

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,32,7,17,181,31,1*4D

$GBGSV,5,5,20,40,14,168,31,21,2,305,38,42,,,41,26,,,40,1*47

$GBGSV,1,1,02,24,66,240,42,38,60,172,40,5*7B

$GBRMC,102204.500,V,,,,,,,310725,1.0,E,N,V*4D

$GBVTG,246.18,T,,M,0.000,N,0.000,K,N*29

$GBGST,102204.500,0.385,0.297,0.249,0.432,3.745,7.165,35*55



2025-07-31 18:22:05:526 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:05:537 ==>> 定位已等待【13】秒.
2025-07-31 18:22:05:766 ==>> $GBGGA,102205.500,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,242,40,24,66,240,43,6,63,309,37,16,63,315,38,1*49

$GBGSV,5,2,20,3,62,191,40,38,60,172,41,8,57,202,40,39,53,333,39,1*7E

$GBGSV,5,3,20,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*46

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,33,7,17,181,31,1*4C

$GBGSV,5,5,20,40,14,168,31,21,2,305,39,42,,,41,26,,,40,1*46

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102205.500,V,,,,,,,310725,1.0,E,N,V*4C

$GBVTG,246.18,T,,M,0.000,N,0.001,K,N*28

$GBGST,102205.500,0.782,0.333,0.274,0.485,3.375,6.193,31*51



2025-07-31 18:22:06:069 ==>> [D][05:19:08][PROT]CLEAN,SEND:2
[D][05:19:08][PROT]index:2 1629955148
[D][05:19:08][PROT]is_send:0
[D][05:19:08][PROT]sequence_num:6
[D][05:19:08][PROT]retry_timeout:0
[D][05:19:08][PROT]retry_times:2
[D][05:19:08][PROT]send_path:0x2
[D][05:19:08][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:08][PROT]===========================================================
[W][05:19:08][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955148]
[D][05:19:08][PROT]===========================================================
[D][05:19:08][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:08][PROT]sending traceid [9999999999900007]
[D][05:19:08][PROT]Send_TO_M2M [1629955148]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:08][SAL ]sock send credit cnt[6]
[D][05:19:08][SAL ]sock send ind credit cnt[6]
[D][05:19:08][M2M ]m2m send data len[134]
[D][05:19:08][SAL ]Cellular task submsg id[10]
[D][05:19:08][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:08][CAT1]gsm read msg sub id: 15
[D][05:1

2025-07-31 18:22:06:144 ==>> 9:08][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:08][CAT1]Send Data To Server[134][137] ... ->:
0043B685113311331133113311331B88BE448D80EC25AEB75A795FBE29AE77374CC6A020B5852C22D5991BBF2CAB1C6382588429318018F0D90EF0E1D1531DF5C48AAB
[D][05:19:08][CAT1]<<< 
SEND OK

[D][05:19:08][CAT1]exec over: func id: 15, ret: 11
[D][05:19:08][CAT1]sub id: 15, ret: 11

[D][05:19:08][SAL ]Cellular task submsg id[68]
[D][05:19:08][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:08][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:08][M2M ]g_m2m_is_idle become true
[D][05:19:08][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:08][PROT]M2M Send ok [1629955148]


2025-07-31 18:22:06:249 ==>> [D][05:19:08][COMM

2025-07-31 18:22:06:279 ==>> ]read battery soc:255


2025-07-31 18:22:06:534 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:06:544 ==>> 定位已等待【14】秒.
2025-07-31 18:22:06:759 ==>> $GBGGA,102206.500,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*48

$GBGSV,5,2,20,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*70

$GBGSV,5,3,20,59,52,130,40,1,48,126,36,2,46,239,35,9,41,287,37,1*49

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,32,7,17,181,31,1*4D

$GBGSV,5,5,20,40,14,168,31,21,2,305,38,42,,,41,26,,,40,1*47

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102206.500,V,,,,,,,310725,1.0,E,N,V*4F

$GBVTG,246.18,T,,M,0.000,N,0.001,K,N*28

$GBGST,102206.500,0.377,0.311,0.259,0.452,2.828,5.395,29*55



2025-07-31 18:22:07:543 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:07:553 ==>> 定位已等待【15】秒.
2025-07-31 18:22:07:759 ==>> $GBGGA,102207.500,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*48

$GBGSV,5,2,20,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*70

$GBGSV,5,3,20,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*46

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,33,7,17,181,31,1*4C

$GBGSV,5,5,20,40,14,168,31,21,2,305,38,42,,,41,26,,,40,1*47

$GBGSV,1,1,02,24,66,240,43,38,60,172,41,5*7B

$GBRMC,102207.500,V,,,,,,,310725,1.0,E,N,V*4E

$GBVTG,246.18,T,,M,0.000,N,0.000,K,N*29

$GBGST,102207.500,0.433,0.245,0.210,0.355,2.574,4.928,28*56



2025-07-31 18:22:08:268 ==>> [D][05:19:10][COMM]read battery soc:255


2025-07-31 18:22:08:553 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:08:564 ==>> 定位已等待【16】秒.
2025-07-31 18:22:08:764 ==>> $GBGGA,102208.500,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*48

$GBGSV,5,2,20,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*70

$GBGSV,5,3,20,59,52,130,39,1,48,126,36,2,46,239,36,9,41,287,37,1*44

$GBGSV,5,4,20,60,41,238,40,4,32,112,32,5,22,258,33,7,17,181,31,1*4C

$GBGSV,5,5,20,40,14,168,31,21,2,305,39,42,,,41,26,,,40,1*46

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102208.500,V,,,,,,,310725,1.0,E,N,V*41

$GBVTG,246.18,T,,M,0.000,N,0.001,K,N*28

$GBGST,102208.500,0.723,0.373,0.302,0.547,2.491,4.568,25*57



2025-07-31 18:22:09:560 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:09:574 ==>> 定位已等待【17】秒.
2025-07-31 18:22:09:774 ==>> $GBGGA,102209.500,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*4A

$GBGSV,6,2,21,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*72

$GBGSV,6,3,21,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*44

$GBGSV,6,4,21,60,41,238,40,4,32,112,32,45,30,200,38,5,22,258,33,1*7E

$GBGSV,6,5,21,7,17,181,31,40,14,168,31,21,2,305,39,42,,,41,1*4F

$GBGSV,6,6,21,26,,,40,1*75

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102209.500,V,,,,,,,310725,1.0,E,N,V*40

$GBVTG,214.24,T,,M,0.239,N,0.443,K,N*2A

$GBGST,102209.500,0.568,0.620,0.480,0.915,2.228,4.174,24*52



2025-07-31 18:22:10:299 ==>> [D][05:19:12][COMM]read battery soc:255


2025-07-31 18:22:10:571 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:10:581 ==>> 定位已等待【18】秒.
2025-07-31 18:22:10:770 ==>> $GBGGA,102210.500,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*4A

$GBGSV,6,2,21,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*72

$GBGSV,6,3,21,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*44

$GBGSV,6,4,21,60,41,238,40,4,32,112,32,45,30,200,32,5,22,258,33,1*74

$GBGSV,6,5,21,7,17,181,31,40,14,168,31,21,2,305,39,42,,,41,1*4F

$GBGSV,6,6,21,26,,,40,1*75

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102210.500,V,,,,,,,310725,1.0,E,N,V*48

$GBVTG,214.24,T,,M,0.001,N,0.001,K,N*21

$GBGST,102210.500,0.420,0.495,0.391,0.724,1.986,3.818,22*5E



2025-07-31 18:22:11:290 ==>> [D][05:19:13][PROT]CLEAN,SEND:2
[D][05:19:13][PROT]index:2 1629955153
[D][05:19:13][PROT]is_send:0
[D][05:19:13][PROT]sequence_num:6
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:1
[D][05:19:13][PROT]send_path:0x2
[D][05:19:13][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908AAC89C8906980220
[D][05:19:13][PROT]sending traceid [9999999999900007]
[D][05:19:13][PROT]Send_TO_M2M [1629955153]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:13][SAL ]sock send credit cnt[6]
[D][05:19:13][SAL ]sock send ind credit cnt[6]
[D][05:19:13][M2M ]m2m send data len[134]
[D][05:19:13][SAL ]Cellular task submsg id[10]
[D][05:19:13][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:13][CAT1]gsm read msg sub id: 15
[D][05:19:13][CAT1]

2025-07-31 18:22:11:365 ==>> tx ret[17] >>> AT+QISEND=0,134

[D][05:19:13][CAT1]Send Data To Server[134][137] ... ->:
0043B686113311331133113311331B88BE5E5BF1BAE5FB8ED92AA8BC3A33C9945B1927415F6C88497F94257BD76C5EDEF8F7DDF7DFFF86A85B79CD8BB190F936B94757
[D][05:19:13][CAT1]<<< 
SEND OK

[D][05:19:13][CAT1]exec over: func id: 15, ret: 11
[D][05:19:13][CAT1]sub id: 15, ret: 11

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:13][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:13][M2M ]g_m2m_is_idle become true
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:13][PROT]M2M Send ok [1629955153]


2025-07-31 18:22:11:577 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:11:588 ==>> 定位已等待【19】秒.
2025-07-31 18:22:11:763 ==>> $GBGGA,102211.500,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*4A

$GBGSV,6,2,21,3,62,191,40,38,60,172,40,8,57,202,39,39,53,333,39,1*73

$GBGSV,6,3,21,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*44

$GBGSV,6,4,21,60,41,238,40,4,32,112,32,45,30,200,32,5,22,258,33,1*74

$GBGSV,6,5,21,7,17,181,31,40,14,168,31,21,2,305,39,42,,,41,1*4F

$GBGSV,6,6,21,26,,,40,1*75

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102211.500,V,,,,,,,310725,1.0,E,N,V*49

$GBVTG,214.24,T,,M,0.000,N,0.001,K,N*20

$GBGST,102211.500,0.759,0.381,0.309,0.556,1.977,3.631,21*5E



2025-07-31 18:22:12:291 ==>> [D][05:19:14][COMM]read battery soc:255


2025-07-31 18:22:12:578 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:12:588 ==>> 定位已等待【20】秒.
2025-07-31 18:22:12:763 ==>> $GBGGA,102212.500,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*4A

$GBGSV,6,2,21,3,62,191,40,38,60,172,40,8,57,202,39,39,53,333,39,1*73

$GBGSV,6,3,21,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*44

$GBGSV,6,4,21,60,41,238,41,4,32,112,32,45,30,200,31,5,22,258,33,1*76

$GBGSV,6,5,21,7,17,181,31,40,14,168,31,21,2,305,39,42,,,41,1*4F

$GBGSV,6,6,21,26,,,40,1*75

$GBGSV,1,1,02,24,66,240,43,38,60,172,41,5*7B

$GBRMC,102212.500,V,,,,,,,310725,1.0,E,N,V*4A

$GBVTG,214.24,T,,M,0.001,N,0.001,K,N*21

$GBGST,102212.500,0.574,0.375,0.307,0.544,1.715,3.320,20*58



2025-07-31 18:22:13:582 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:13:595 ==>> 定位已等待【21】秒.
2025-07-31 18:22:13:762 ==>> $GBGGA,102213.500,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,13,78,242,40,24,66,240,42,6,63,309,37,16,63,315,38,1*4A

$GBGSV,6,2,21,3,62,191,41,38,60,172,40,8,57,202,39,39,53,333,39,1*72

$GBGSV,6,3,21,59,52,130,39,1,48,126,37,2,46,239,35,9,41,287,37,1*44

$GBGSV,6,4,21,60,41,238,40,4,32,112,32,45,30,200,32,5,22,258,33,1*74

$GBGSV,6,5,21,7,17,181,31,40,14,168,31,21,2,305,39,42,,,41,1*4F

$GBGSV,6,6,21,26,,,40,1*75

$GBGSV,1,1,02,24,66,240,43,38,60,172,40,5*7A

$GBRMC,102213.500,V,,,,,,,310725,1.0,E,N,V*4B

$GBVTG,214.24,T,,M,0.001,N,0.001,K,N*21

$GBGST,102213.500,0.267,0.416,0.334,0.606,1.384,3.001,19*5D



2025-07-31 18:22:14:301 ==>> [D][05:19:16][COMM]read battery soc:255


2025-07-31 18:22:14:589 ==>> 符合定位需求的卫星数量:【0】
2025-07-31 18:22:14:601 ==>> 定位已等待【22】秒.
2025-07-31 18:22:15:350 ==>> $GBGGA,102214.500,2301.2587017,N,11421.9423305,E,1,07,3.51,78.299,M,-1.770,M,,*5D

$GBGSA,A,3,13,08,24,06,16,39,38,,,,,,10.27,3.51,9.65,4*3D

$GBGSV,6,1,21,13,78,242,40,8,76,191,39,42,71,12,40,24,66,241,42,1*75

$GBGSV,6,2,21,6,63,309,37,16,63,315,38,3,62,191,41,39,60,342,39,1*7A

$GBGSV,6,3,21,38,60,172,40,26,58,29,40,59,52,130,39,1,48,126,37,1*74

$GBGSV,6,4,21,2,46,239,35,21,43,117,38,9,41,287,37,60,41,238,40,1*7A

$GBGSV,6,5,21,4,32,112,32,45,30,200,32,5,22,258,33,7,17,181,31,1*40

$GBGSV,6,6,21,40,14,168,31,1*49

$GBGSV,1,1,02,24,66,241,43,38,60,172,40,5*7B

$GBRMC,102214.500,A,2301.2587017,N,11421.9423305,E,0.001,214.24,310725,,,A,S*33

$GBVTG,214.24,T,,M,0.001,N,0.001,K,A*2E

[D][05:19:16][GNSS]HD8040 GPS
[D][05:19:16][GNSS]GPS diff_sec 124002178, report 0x42 frame
$GBGST,102214.500,0.759,0.368,0.294,0.563,1.537,2.887,17*50

[D][05:19:17][COMM]Main Task receive event:131
[D][05:19:17][COMM]index:0,power_mode:0xFF
[D][05:19:17][COMM]index:1,sound_mode:0xFF
[D][05:19:17][COMM]index:2,gsensor_mode:0xFF
[D][05:19:17][COMM]index:3,report_freq_mode:0xFF
[D][05:19:17][COMM]index:4,report_period:0xFF
[D][05:19:17][COMM]in

2025-07-31 18:22:15:455 ==>> dex:5,normal_reset_mode:0xFF
[D][05:19:17][COMM]index:6,normal_reset_period:0xFF
[D][05:19:17][COMM]index:7,spock_over_speed:0xFF
[D][05:19:17][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:17][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:17][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:17][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:17][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:17][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:17][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:17][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:17][COMM]index:16,imu_config_params:0xFF
[D][05:19:17][COMM]index:17,long_connect_params:0xFF
[D][05:19:17][COMM]index:18,detain_mark:0xFF
[D][05:19:17][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:17][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:17][COMM]index:21,mc_mode:0xFF
[D][05:19:17][COMM]index:22,S_mode:0xFF
[D][05:19:17][COMM]index:23,overweight:0xFF
[D][05:19:17][COMM]index:24,standstill_mode:0xFF
[D][05:19:17][COMM]index:25,night_mode:0xFF
[D][05:19:17][COMM]index:26,experiment1:0xFF
[D][05:19:17][COMM]index:27,experiment2:0xFF
[D][05:19:17][COMM]index:28,experiment3:0xFF
[D][05:19:17][CO

2025-07-31 18:22:15:560 ==>> MM]index:29,experiment4:0xFF
[D][05:19:17][COMM]index:30,night_mode_start:0xFF
[D][05:19:17][COMM]index:31,night_mode_end:0xFF
[D][05:19:17][COMM]index:33,park_report_minutes:0xFF
[D][05:19:17][COMM]index:34,park_report_mode:0xFF
[D][05:19:17][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:17][COMM]index:38,charge_battery_para: FF
[D][05:19:17][COMM]index:39,multirider_mode:0xFF
[D][05:19:17][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:17][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:17][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:17][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:17][COMM]index:44,riding_duration_config:0xFF
[D][05:19:17][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:17][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:17][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:17][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:17][COMM]index:49,mc_load_startup:0xFF
[D][05:19:17][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:17][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:17][COMM]index:52,traffic_mode:0xFF
[D][05:19:17][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:17][COMM]index:54,traffic_security_model_cycl

2025-07-31 18:22:15:590 ==>> 符合定位需求的卫星数量:【16】
2025-07-31 18:22:15:597 ==>> 
北斗星号:【13】,信号值:【40】
北斗星号:【8】,信号值:【39】
北斗星号:【42】,信号值:【40】
北斗星号:【24】,信号值:【43】
北斗星号:【6】,信号值:【37】
北斗星号:【16】,信号值:【38】
北斗星号:【3】,信号值:【41】
北斗星号:【39】,信号值:【39】
北斗星号:【38】,信号值:【40】
北斗星号:【26】,信号值:【40】
北斗星号:【59】,信号值:【39】
北斗星号:【1】,信号值:【37】
北斗星号:【2】,信号值:【35】
北斗星号:【21】,信号值:【38】
北斗星号:【9】,信号值:【37】
北斗星号:【60】,信号值:【40】

2025-07-31 18:22:15:607 ==>> 检测【CSQ强度】
2025-07-31 18:22:15:627 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 18:22:15:665 ==>> e:0xFF
[D][05:19:17][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:17][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:17][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:17][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:17][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:17][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:17][COMM]index:63,experiment5:0xFF
[D][05:19:17][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:17][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:17][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:17][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:17][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:17][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:17][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:17][COMM]index:72,experiment6:0xFF
[D][05:19:17][COMM]index:73,experiment7:0xFF
[D][05:19:17][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:17][COMM]index:75,zero_value_from_server:-1
[D][05:19:17][COMM]index:76,multirider_threshold:255
[D][05:19:17][COMM]index:77,experiment8:255
[D][05:19:17][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:17][COMM]index:79,temp_park_

2025-07-31 18:22:15:769 ==>> tail_light_twinkle_duration:255
[D][05:19:17][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:17][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:17][COMM]index:83,loc_report_interval:255
[D][05:19:17][COMM]index:84,multirider_threshold_p2:255
[D][05:19:17][COMM]index:85,multirider_strategy:255
[D][05:19:17][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:17][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:17][COMM]index:90,weight_param:0xFF
[D][05:19:17][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:17][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:17][COMM]index:95,current_limit:0xFF
[D][05:19:17][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:17][COMM]index:100,location_mode:0xFF

[D][05:19:17][HSDK][0] flush to flash addr:[0xE44600] --- write len --- [256]
[W][05:19:17][PROT]remove success[1629955157],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:19:17][PROT]add success [1629955157],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:17][COMM]Main 

2025-07-31 18:22:15:874 ==>> Task receive event:131 finished processing
$GBGGA,102215.000,2301.2580988,N,11421.9411933,E,1,10,1.83,71.769,M,-1.770,M,,*57

$GBGSA,A,3,13,08,42,24,06,16,39,38,26,21,,,5.92,1.83,5.63,4*01

$GBGSV,6,1,21,13,78,242,40,8,76,191,39,42,71,12,41,24,66,241,42,1*74

$GBGSV,6,2,21,6,63,309,37,16,63,315,38,3,62,191,41,39,60,342,39,1*7A

$GBGSV,6,3,21,38,60,172,40,26,58,29,40,59,52,130,39,1,48,126,37,1*74

$GBGSV,6,4,21,2,46,239,35,21,43,117,39,9,41,287,37,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,32,45,30,200,32,5,22,258,33,7,17,181,31,1*40

$GBGSV,6,6,21,40,14,168,31,1*49

$GBGSV,2,1,06,42,71,12,41,24,66,241,43,39,60,342,39,38,60,172,40,5*49

$GBGSV,2,2,06,26,58,29,39,21,43,117,37,5*4B

$GBRMC,102215.000,A,2301.2580988,N,11421.9411933,E,0.002,214.24,310725,,,A,S*32

$GBVTG,214.24,T,,M,0.002,N,0.003,K,A*2F

$GBGST,102215.000,1.143,0.488,0.411,0.813,1.296,2.086,12*50



2025-07-31 18:22:15:980 ==>> [W][05:19:18][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:18][CAT1]gsm read msg sub id: 12
[D][05:19:18][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:18][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:18][CAT1]exec over: func id: 12, ret: 21


2025-07-31 18:22:16:225 ==>> 【CSQ强度】通过,【20】符合目标值【18】至【31】要求!
2025-07-31 18:22:16:232 ==>> 检测【关闭GSM联网】
2025-07-31 18:22:16:244 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 18:22:16:556 ==>> $GBGGA,102216.000,2301.2580148,N,11421.9410195,E,1,10,1.83,70.694,M,-1.770,M,,*57

$GBGSA,A,3,13,08,42,24,06,16,39,38,26,21,,,5.92,1.83,5.63,4*01

$GBGSV,6,1,21,13,78,242,40,8,76,191,39,42,71,12,41,24,66,241,42,1*74

$GBGSV,6,2,21,6,63,309,37,16,63,315,38,3,62,191,40,39,60,342,39,1*7B

$GBGSV,6,3,21,38,60,172,41,26,58,29,40,59,52,130,39,1,48,126,37,1*75

$GBGSV,6,4,21,2,46,239,35,21,43,117,39,9,41,287,37,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,32,45,30,200,32,5,22,258,32,7,17,181,31,1*41

$GBGSV,6,6,21,40,14,168,31,1*49

$GBGSV,2,1,06,42,71,12,42,24,66,241,43,39,60,342,40,38,60,172,41,5*45

$GBGSV,2,2,06,26,58,29,40,21,43,117,39,5*4B

$GBRMC,102216.000,A,2301.2580148,N,11421.9410195,E,0.001,214.24,310725,,,A,S*33

$GBVTG,214.24,T,,M,0.001,N,0.001,K,A*2E

$GBGST,102216.000,0.823,1.230,1.001,2.100,0.877,1.552,9.622*7D

[D][05:19:18][PROT]CLEAN,SEND:2
[D][05:19:18][PROT]CLEAN:2
[D][05:19:18][COMM]read battery soc:255
[D][05:19:18][PROT]index:0 1629955158
[D][05:19:18][PROT]is_send:0
[D][05:19:18][PROT]sequence_num:7
[D][05:19:18][PROT]retry_timeout:0
[D][05:19:18][PROT]retry_times:1
[D][05:19:18][PROT]send_path:0x2
[D][05:19:18][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:18][PROT]===========

2025-07-31 18:22:16:661 ==>> ================================================
[W][05:19:18][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]sending traceid [9999999999900008]
[D][05:19:18][PROT]Send_TO_M2M [1629955158]
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:18][SAL ]sock send credit cnt[6]
[D][05:19:18][SAL ]sock send ind credit cnt[6]
[D][05:19:18][M2M ]m2m send data len[294]
[D][05:19:18][SAL ]Cellular task submsg id[10]
[D][05:19:18][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:18][CAT1]gsm read msg sub id: 15
[D][05:19:18][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:18][CAT1]Send Data To Server[294][297] ... ->:
0093B989113311331133113311331B88B297978C7B4CF1F1440619A26FCD5E6CB0A331742C4B1294796F686753A127AD921CB1F3DF40B94A972F6B31A10403D52FC3F435B15A75A4E373EFAE761B2EF623F3DA83ED6C7059A422418B79C8A684640E89DF4139850C0D46FA95135DC1AF256797730B8E0C140B77E73B818C9E9ABF545794DABA063BBFF16AFA0F780328F7BB44
[D][05:19:18][CAT1]<<< 
SEND OK

[D][05:19:18][CAT1]exec over: func

2025-07-31 18:22:16:761 ==>>  id: 15, ret: 11
[D][05:19:18][CAT1]sub id: 15, ret: 11

[D][05:19:18][SAL ]Cellular task submsg id[68]
[D][05:19:18][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:18][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:18][M2M ]g_m2m_is_idle become true
[D][05:19:18][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:18][PROT]M2M Send ok [1629955158]
[W][05:19:18][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:18][COMM]GSM test
[D][05:19:18][COMM]GSM test disable


2025-07-31 18:22:16:770 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 18:22:16:777 ==>> 检测【4G联网测试】
2025-07-31 18:22:16:798 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 18:22:17:838 ==>> [W][05:19:19][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:19][COMM]Main Task receive event:14
[D][05:19:19][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955159, allstateRepSeconds = 0
[D][05:19:19][COMM]index:0,power_mode:0xFF
[D][05:19:19][COMM]index:1,sound_mode:0xFF
[D][05:19:19][COMM]index:2,gsensor_mode:0xFF
[D][05:19:19][COMM]index:3,report_freq_mode:0xFF
[D][05:19:19][COMM]index:4,report_period:0xFF
[D][05:19:19][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:19][COMM]index:6,normal_reset_period:0xFF
[D][05:19:19][COMM]index:7,spock_over_speed:0xFF
[D][05:19:19][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:19][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:19][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:19][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:19][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:19][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:19][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:19][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:19][COMM]index:16,imu_config_params:0xFF
[D][05:19:19][COMM]index:17,long_connect_params:0xFF
[D][05:19:19][COMM]index:18,detain_mark:0xFF
[D][05:19:19][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:19][COMM]index:

2025-07-31 18:22:17:943 ==>> 20,lock_pos_report_interval:0xFF
[D][05:19:19][COMM]index:21,mc_mode:0xFF
[D][05:19:19][COMM]index:22,S_mode:0xFF
[D][05:19:19][COMM]index:23,overweight:0xFF
[D][05:19:19][COMM]index:24,standstill_mode:0xFF
[D][05:19:19][COMM]index:25,night_mode:0xFF
[D][05:19:19][COMM]index:26,experiment1:0xFF
[D][05:19:19][COMM]index:27,experiment2:0xFF
[D][05:19:19][COMM]index:28,experiment3:0xFF
[D][05:19:19][COMM]index:29,experiment4:0xFF
[D][05:19:19][COMM]index:30,night_mode_start:0xFF
[D][05:19:19][COMM]index:31,night_mode_end:0xFF
[D][05:19:19][COMM]index:33,park_report_minutes:0xFF
[D][05:19:19][COMM]index:34,park_report_mode:0xFF
[D][05:19:19][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:19][COMM]index:38,charge_battery_para: FF
[D][05:19:19][COMM]index:39,multirider_mode:0xFF
[D][05:19:19][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:19][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:19][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:19][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:19][COMM]index:44,riding_duration_config:0xFF
[D][05:19:19][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:19][COMM]index:46,camera_park_type_cfg:0xFF
[D][

2025-07-31 18:22:18:048 ==>> 05:19:19][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:19][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:19][COMM]index:49,mc_load_startup:0xFF
[D][05:19:19][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:19][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:19][COMM]index:52,traffic_mode:0xFF
[D][05:19:19][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:19][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:19][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:19][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:19][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:19][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:19][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:19][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:19][COMM]index:63,experiment5:0xFF
[D][05:19:19][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:19][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:19][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:19][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:19][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:19][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:19][COMM]index:71,camera_park_self_check_cfg:0

2025-07-31 18:22:18:153 ==>> xFF
[D][05:19:19][COMM]index:72,experiment6:0xFF
[D][05:19:19][COMM]index:73,experiment7:0xFF
[D][05:19:19][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:19][COMM]index:75,zero_value_from_server:-1
[D][05:19:19][COMM]index:76,multirider_threshold:255
[D][05:19:19][COMM]index:77,experiment8:255
[D][05:19:19][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:19][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:19][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:19][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:19][COMM]index:83,loc_report_interval:255
[D][05:19:19][COMM]index:84,multirider_threshold_p2:255
[D][05:19:19][COMM]index:85,multirider_strategy:255
[D][05:19:19][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:19][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:19][COMM]index:90,weight_param:0xFF
[D][05:19:19][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:19][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:19][COMM]index:95,current_limit:0xFF
[D][05:19:19][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:19][COMM]index:100,location_mode:0xFF

[W][05:1

2025-07-31 18:22:18:258 ==>> 9:19][PROT]remove success[1629955159],send_path[2],type[0000],priority[0],index[0],used[0]
[D][05:19:19][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:19][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:19][PROT]index:0 1629955159
[D][05:19:19][PROT]is_send:0
[D][05:19:19][PROT]sequence_num:8
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:1
[D][05:19:19][PROT]send_path:0x2
[D][05:19:19][PROT]min_index:0, type:0x4205, priority:0
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][PROT]sending traceid [9999999999900009]
[D][05:19:19][PROT]Send_TO_M2M [1629955159]
[W][05:19:19][PROT]add success [1629955159],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:19][CAT1]gsm read msg sub id: 13
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:19][SAL ]sock send credit cnt[6]
[D][05:19:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[294]
[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][SAL ]Cellular task subm

2025-07-31 18:22:18:363 ==>> sg id[10]
[D][05:19:19][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052de0] format[0]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:19:19][CAT1]exec over: func id: 13, ret: 21
[D][05:19:19][M2M ]get csq[20]
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:19][CAT1]Send Data To Server[294][297] ... ->:
0093B987113311331133113311331B88B1F47D50B60BC1F37C23442DB7BA474CB75DE32920FF5B79DA539FD2F642ABC0966C957BA28DD7BB148BDBB6E50AB1C57BD58EF45E3F9D20D2617ACC46BFFD87E8C8C58FDE8AF2A0FDC7D372C1FF9DBEBBCE48D671502D03141D8890360B7DAF887D48AF2D23656F58B4AC89A8A1B538052C1D3BA6A1F49BE07328F6D43D589888A2C6
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]
$GBGGA,102217.000,2301

2025-07-31 18:22:18:468 ==>> .2580040,N,11421.9410323,E,1,10,1.83,71.927,M,-1.770,M,,*56

$GBGSA,A,3,13,08,42,24,16,06,39,38,26,21,,,5.91,1.83,5.62,4*03

$GBGSV,6,1,21,13,78,242,40,8,76,191,39,42,71,12,40,24,66,241,42,1*75

$GBGSV,6,2,21,16,63,315,38,6,63,309,37,3,62,191,41,39,60,342,39,1*7A

$GBGSV,6,3,21,38,60,172,40,26,58,29,40,59,52,130,39,1,48,126,37,1*74

$GBGSV,6,4,21,2,46,239,35,21,43,117,39,9,41,287,37,60,41,238,40,1*7B

$GBGSV,6,5,21,4,32,112,32,45,30,200,32,5,22,258,33,7,17,181,31,1*40

$GBGSV,6,6,21,40,14,168,31,1*49

$GBGSV,2,1,06,42,71,12,42,24,66,241,43,39,60,342,40,38,60,172,41,5*45

$GBGSV,2,2,06,26,58,29,41,21,43,117,40,5*44

$GBRMC,102217.000,A,2301.2580040,N,11421.9410323,E,0.001,214.24,310725,,,A,S*34

$GBVTG,214.24,T,,M,0.001,N,0.002,K,A*2D

$GBGST,102217.000,1.106,0.257,0.229,0.406,1.032,1.559,8.430*78

>>>>>RESEND ALLSTATE<<<<<
[D][05:19:19][HSDK][0] flus 

2025-07-31 18:22:18:573 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 18:22:18:803 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 18:22:18:812 ==>> 检测【关闭GPS】
2025-07-31 18:22:18:827 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 18:22:19:143 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:21][GNSS]stop locating
[D][05:19:21][GNSS]stop event:8
[D][05:19:21][GNSS]GPS stop. ret=0
[D][05:19:21][GNSS]all continue location stop
[W][05:19:21][GNSS]stop locating
[D][05:19:21][GNSS]all sing location stop
[D][05:19:21][CAT1]gsm read msg sub id: 24
[D][05:19:21][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:21][CAT1]<<< 
OK

[D][05:19:21][CAT1]exec over: func id: 24, ret: 6
[D][05:19:21][CAT1]sub id: 24, ret: 6



2025-07-31 18:22:19:348 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 18:22:19:356 ==>> 检测【清空消息队列2】
2025-07-31 18:22:19:369 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 18:22:19:564 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:21][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 18:22:19:671 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 18:22:19:679 ==>> 检测【轮动检测】
2025-07-31 18:22:19:691 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 18:22:19:774 ==>> [D][05:19:22][GNSS]recv submsg id[1]
[D][05:19:22][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:22][GNSS]location stop evt done evt
3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 18:22:19:849 ==>> [D][05:19:22][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 18:22:20:181 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 18:22:20:272 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 18:22:20:331 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 18:22:20:479 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 18:22:20:492 ==>> 检测【关闭小电池】
2025-07-31 18:22:20:508 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:22:20:575 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:22:20:780 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 18:22:20:795 ==>> 检测【进入休眠模式】
2025-07-31 18:22:20:823 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:22:20:952 ==>> [W][05:19:23][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:22:21:044 ==>> [D][05:19:23][COMM]Main Task receive event:28
[D][05:19:23][COMM]main task tmp_sleep_event = 8
[D][05:19:23][COMM]prepare to sleep
[D][05:19:23][CAT1]gsm read msg sub id: 12
[D][05:19:23][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 18:22:21:881 ==>> [D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]exec over: func id: 12, ret: 6
[D][05:19:24][M2M ]tcpclient close[4]
[D][05:19:24][SAL ]Cellular task submsg id[12]
[D][05:19:24][SAL ]cellular CLOSE socket size[4], msg->data[0x20052c48], socket[0]
[D][05:19:24][CAT1]gsm read msg sub id: 9
[D][05:19:24][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:24][CAT1]<<< 
OK

[D][05:19:24][CAT1]exec over: func id: 9, ret: 6
[D][05:19:24][CAT1]sub id: 9, ret: 6

[D][05:19:24][SAL ]Cellular task submsg id[68]
[D][05:19:24][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:24][SAL ]socket close ind. id[4]
[D][05:19:24][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:24][COMM]1x1 frm_can_tp_send ok
[D][05:19:24][CAT1]pdpdeact urc len[22]


2025-07-31 18:22:22:157 ==>> [E][05:19:24][COMM]1x1 rx timeout
[D][05:19:24][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:22:22:353 ==>> [D][05:19:24][COMM]read battery soc:255


2025-07-31 18:22:22:671 ==>> [E][05:19:24][COMM]1x1 rx timeout
[E][05:19:24][COMM]1x1 tp timeout
[E][05:19:24][COMM]1x1 error -3.
[W][05:19:24][COMM]CAN STOP!
[D][05:19:24][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:24][COMM]------------ready to Power off Acckey 1------------
[D][05:19:24][COMM]------------ready to Power off Acckey 2------------
[D][05:19:24][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:24][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1298
[D][05:19:24][COMM]bat sleep fail, reason:-1
[D][05:19:24][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:24][COMM]accel parse set 0
[D][05:19:24][COMM]imu rest ok. 95873
[D][05:19:24][COMM]imu sleep 0
[W][05:19:24][COMM]now sleep


2025-07-31 18:22:22:894 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:22:22:906 ==>> 检测【检测33V休眠电流】
2025-07-31 18:22:22:926 ==>> 开始33V电流采样
2025-07-31 18:22:22:934 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:23:006 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:22:24:015 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:22:24:108 ==>> Current33V:????:468.42

2025-07-31 18:22:24:519 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:24:625 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 18:22:25:626 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 18:22:25:672 ==>> Current33V:????:19.81

2025-07-31 18:22:26:133 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:26:141 ==>> 【检测33V休眠电流】通过,【19.81uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:22:26:149 ==>> 该项需要延时执行
2025-07-31 18:22:28:152 ==>> 此处延时了:【2000】毫秒
2025-07-31 18:22:28:165 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 18:22:28:187 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 18:22:28:277 ==>> 1A A1 00 00 FC 
Get AD_V2 1677mV
Get AD_V3 1658mV
Get AD_V4 1mV
Get AD_V5 2750mV
Get AD_V6 2019mV
Get AD_V7 1091mV
OVER 150


2025-07-31 18:22:29:208 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 18:22:29:220 ==>> 检测【打开小电池2】
2025-07-31 18:22:29:239 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 18:22:29:280 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 18:22:29:499 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 18:22:29:507 ==>> 该项需要延时执行
2025-07-31 18:22:30:004 ==>> 此处延时了:【500】毫秒
2025-07-31 18:22:30:018 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 18:22:30:040 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:22:30:080 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:22:30:297 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:22:30:307 ==>> 该项需要延时执行
2025-07-31 18:22:30:770 ==>> [D][05:19:32][COMM]------------ready to Power on Acckey 1------------
[D][05:19:32][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:32][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 4,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 5,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 6,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 7,volt = 8
[D][05:19:32][FCTY]get_ext_48v_vol retry i = 8,volt = 8
[D][05:19:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:19:32][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:32][COMM]----- get Acckey 1 and value:1------------
[W][05:19:32][COMM]CAN START!
[D][05:19:32][CAT1]gsm read msg sub id: 12
[D][05:19:32][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:32][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 103894
[D][05:19:32][COMM][Audio]exec status ready.
[

2025-07-31 18:22:30:800 ==>> 此处延时了:【500】毫秒
2025-07-31 18:22:30:812 ==>> 检测【进入休眠模式2】
2025-07-31 18:22:30:839 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 18:22:30:865 ==>> D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]exec over: func id: 12, ret: 6
[D][05:19:32][COMM]imu wakeup ok. 103909
[D][05:19:32][COMM]imu wakeup 1
[W][05:19:32][COMM]wake up system, wakeupEvt=0x80
[D][05:19:32][COMM]frm_can_weigth_power_set 1
[D][05:19:32][COMM]Clear Sleep Block Evt
[D][05:19:32][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:32][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:22:30:935 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 18:22:31:025 ==>> [D][05:19:33][HSDK][0] flush to flash addr:[0xE44800] --- write len --- [256]
[E][05:19:33][COMM]1x1 rx timeout
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:22:31:130 ==>>                                                                                                                                879. cur_tick:104388. period:50
[D][05:19:33][COMM]msg 02A5 loss. last_tick:103879. cur_tick:104389. period:50
[D][05:19:33][COMM]msg 02A6 loss. last_tick:103879. cur_tick:104389. period:50
[D][05:19:33][COMM]msg 02A7 loss. last_tick:103879. cur_tick:104389. period:50
[D][05:19:33][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 104390


2025-07-31 18:22:31:160 ==>> 
[D][05:19:33][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 104390


2025-07-31 18:22:31:490 ==>> [E][05:19:33][COMM]1x1 rx timeout
[E][05:19:33][COMM]1x1 tp timeout
[E][05:19:33][COMM]1x1 error -3.
[D][05:19:33][COMM]Main Task receive event:28 finished processing
[D][05:19:33][COMM]Main Task receive event:28
[D][05:19:33][COMM]prepare to sleep
[D][05:19:33][CAT1]gsm read msg sub id: 12
[D][05:19:33][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:33][CAT1]<<< 
OK

[D][05:19:33][CAT1]exec over: func id: 12, ret: 6
[D][05:19:33][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:22:31:820 ==>> [D][05:19:33][COMM]msg 0220 loss. last_tick:103879. cur_tick:104884. period:100
[D][05:19:33][COMM]msg 0221 loss. last_tick:103879. cur_tick:104884. period:100
[D][05:19:33][COMM]msg 0224 loss. last_tick:103879. cur_tick:104885. period:100
[D][05:19:33][COMM]msg 0260 loss. last_tick:103879. cur_tick:104885. period:100
[D][05:19:33][COMM]msg 0280 loss. last_tick:103879. cur_tick:104885. period:100
[D][05:19:33][COMM]msg 02C0 loss. last_tick:103879. cur_tick:104886. period:100
[D][05:19:33][COMM]msg 02C1 loss. last_tick:103879. cur_tick:104886. period:100
[D][05:19:33][COMM]msg 02C2 loss. last_tick:103879. cur_tick:104886. period:100
[D][05:19:33][COMM]msg 02E0 loss. last_tick:103879. cur_tick:104887. period:100
[D][05:19:33][COMM]msg 02E1 loss. last_tick:103879. cur_tick:104887. period:100
[D][05:19:33][COMM]msg 02E2 loss. last_tick:103879. cur_tick:104887. period:100
[D][05:19:33][COMM]msg 0300 loss. last_tick:103879. cur_tick:104888. period:100
[D][05:19:33][COMM]msg 0301 loss. last_tick:103879. cur_tick:104888. period:100
[D][05:19:33][COMM]bat msg 0240 loss. last_tick:103879. cur_tick:104889. period:100. j,i:1 54
[D][05:19:33][COMM]bat msg 0241 loss. last_tick:103879. cur_tick:104889. period:100. j,i:2 55
[D][05:19:33][COMM]bat msg 0242 loss. last_tick:10

2025-07-31 18:22:31:911 ==>> 3879. cur_tick:104889. period:100. j,i:3 56
[D][05:19:33][COMM]bat msg 0244 loss. last_tick:103879. cur_tick:104890. period:100. j,i:5 58
[D][05:19:33][COMM]bat msg 024E loss. last_tick:103879. cur_tick:104890. period:100. j,i:15 68
[D][05:19:33][COMM]bat msg 024F loss. last_tick:103879. cur_tick:104891. period:100. j,i:16 69
[D][05:19:33][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 104891
[D][05:19:33][COMM]CAN message bat fault change: 0x00000000->0x0001802E 104892
[D][05:19:33][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 104892
                                                                              

2025-07-31 18:22:32:092 ==>> [D][05:19:34][COMM]msg 0222 loss. last_tick:103879. cur_tick:105387. period:150
[D][05:19:34][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 105388


2025-07-31 18:22:32:167 ==>>   ][05:19:34][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 1
[D][05:19:34][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:34][COMM]------------ready to Power off Acckey 2------------


2025-07-31 18:22:32:377 ==>> [E][05:19:34][COMM]1x1 rx timeout
[E][05:19:34][COMM]1x1 tp timeout
[E][05:19:34][COMM]1x1 error -3.
[W][05:19:34][COMM]CAN STOP!
[D][05:19:34][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:34][COMM]------------ready to Power off Acckey 1------------
[D][05:19:34][COMM]------------ready to Power off Acckey 2------------
[D][05:19:34][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:34][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 102
[D][05:19:34][COMM]bat sleep fail, reason:-1
[D][05:19:34][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:34][COMM]accel parse set 0
[D][05:19:34][COMM]imu rest ok. 105577
[D][05:19:34][COMM]imu sleep 0
[W][05:19:34][COMM]now sleep


2025-07-31 18:22:32:611 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 18:22:32:619 ==>> 检测【检测小电池休眠电流】
2025-07-31 18:22:32:639 ==>> 开始小电池电流采样
2025-07-31 18:22:32:651 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:32:713 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:22:33:714 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:22:33:776 ==>> CurrentBattery:ƽ��:68.75

2025-07-31 18:22:34:229 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:34:237 ==>> 【检测小电池休眠电流】通过,【68.75uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 18:22:34:264 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 18:22:34:278 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 18:22:34:380 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 18:22:34:536 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 18:22:34:547 ==>> 该项需要延时执行
2025-07-31 18:22:34:619 ==>> [D][05:19:36][COMM]------------ready to Power on Acckey 1------------
[D][05:19:36][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:36][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:36][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:36][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:36][COMM]----- get Acckey 1 and value:1------------
[W][05:19:36][COMM]CAN START!
[D][05:19:36][CAT1]gsm read msg sub id: 12
[D][05:19:36][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:36][COMM]CAN message bat fault change: 0x0001802E->0x00000000 107769
[D][05:19:36][COMM][Audio]exec status ready.
[D][05:19:36][CAT1]<<< 
OK

[D][05:19:36][CAT1]exec over: func id: 12, ret: 6
[D][05:19:36][COMM]imu wakeup ok. 107783
[D][05:19:36][COMM]imu wakeup 1
[W][05:19:36][COMM]wake up system, wakeupEvt=0x80
[D][05:19:36][COMM]frm_can_weigth_power_set 1
[D][05:19:36][COMM]Clear Sleep Block Evt
[D][05:19:36][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:36][COMM]1x1 frm_can_tp_send ok
[D][05:19:36][COMM]read battery soc:0


2025-07-31 18:22:34:769 ==>> [D][05:19:37][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 18:22:34:875 ==>> [E][05:19:37][COMM]1x1 rx timeout
[D][05:19:37][COMM]1x1 frm_can_tp_send ok


2025-07-31 18:22:34:980 ==>> [D][05:19:37][COMM]msg 02A0 loss. last_tick:107752. cur_tick:108263. period:50
[D][05:19:37][COMM]msg 02A4 loss. last_tick:107752. cur_

2025-07-31 18:22:35:040 ==>> 此处延时了:【500】毫秒
2025-07-31 18:22:35:052 ==>> 检测【检测唤醒】
2025-07-31 18:22:35:080 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 18:22:35:105 ==>> tick:108263. period:50
[D][05:19:37][COMM]msg 02A5 loss. last_tick:107752. cur_tick:108264. period:50
[D][05:19:37][COMM]msg 02A6 loss. last_tick:107752. cur_tick:108264. period:50
[D][05:19:37][COMM]msg 02A7 loss. last_tick:107752. cur_tick:108265. period:50
[D][05:19:37][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 108265
[D][05:19:37][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 108266


2025-07-31 18:22:35:835 ==>> [W][05:19:37][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:37][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:37][FCTY]==========Modules-nRF5340 ==========
[D][05:19:37][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:37][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:37][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:37][FCTY]DeviceID    = 460130071536768
[D][05:19:37][FCTY]HardwareID  = 867222087638906
[D][05:19:37][FCTY]MoBikeID    = 9999999999
[D][05:19:37][FCTY]LockID      = FFFFFFFFFF
[D][05:19:37][FCTY]BLEFWVersion= 105
[D][05:19:37][FCTY]BLEMacAddr   = D018E736E0BE
[D][05:19:37][FCTY]Bat         = 3864 mv
[D][05:19:37][FCTY]Current     = 0 ma
[D][05:19:37][FCTY]VBUS        = 2600 mv
[D][05:19:37][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:37][FCTY]Ext battery vol = 32, adc = 1299
[D][05:19:37][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 202 mv
[D][05:19:37][FCTY]Bike Type flag is invalied
[D][05:19:37][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:37][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:37][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:37][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:37][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:19:37][FCTY]CAT1_GNSS_VER

2025-07-31 18:22:35:940 ==>> SION = V3465b5b1
[D][05:19:37][FCTY]Bat1         = 3833 mv
[D][05:19:37][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:37][FCTY]==========Modules-nRF5340 ==========
[E][05:19:37][COMM]1x1 rx timeout
[E][05:19:37][COMM]1x1 tp timeout
[E][05:19:37][COMM]1x1 error -3.
[D][05:19:37][COMM]Main Task receive event:28 finished processing
[D][05:19:37][COMM]Main Task receive event:65
[D][05:19:37][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:37][COMM]Main Task receive event:65 finished processing
[D][05:19:37][COMM]Main Task receive event:60
[D][05:19:37][COMM]smart_helmet_vol=255,255
[D][05:19:37][COMM]report elecbike
[W][05:19:37][PROT]remove success[1629955177],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:37][PROT]add success [1629955177],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:37][COMM]Main Task receive event:60 finished processing
[D][05:19:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:37][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:37][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:37][PROT]index:0
[D][05:19:37][PROT]is_send:1
[D][05:19:37][PROT]sequence_num:10

2025-07-31 18:22:36:045 ==>> 
[D][05:19:37][PROT]retry_timeout:0
[D][05:19:37][PROT]retry_times:3
[D][05:19:37][PROT]send_path:0x3
[D][05:19:37][PROT]msg_type:0x5d03
[D][05:19:37][PROT]===========================================================
[D][05:19:37][HSDK][0] flush to flash addr:[0xE44900] --- write len --- [256]
[W][05:19:37][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955177]
[D][05:19:37][PROT]===========================================================
[D][05:19:37][PROT]Sending traceid[999999999990000B]
[D][05:19:37][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:37][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:37][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:37][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:37][SAL ]open socket ind id[4], rst[0]
[D][05:19:37][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:37][SAL ]Cellular task submsg id[8]
[D][05:19:37][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:37][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:

2025-07-31 18:22:36:091 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 18:22:36:100 ==>> 检测【关机】
2025-07-31 18:22:36:129 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:22:36:150 ==>> 37][CAT1]gsm read msg sub id: 8
[D][05:19:37][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:37][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:37][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:37][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:37][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:37][CAT1]<<< 
+CME ERROR: 100

[D][05:19:37][COMM]msg 0220 loss. last_tick:107752. cur_tick:108761. period:100
[D][05:19:37][COMM]msg 0221 loss. last_tick:107752. cur_tick:108761. period:100
[D][05:19:37][COMM]msg 0224 loss. last_tick:107752. cur_tick:108762. period:100
[D][05:19:37][COMM]msg 0260 loss. last_tick:107752. cur_tick:108762. period:100
[D][05:19:37][COMM]msg 0280 loss. last_tick:107752. cur_tick:108762. period:100
[D][05:19:37][COMM]msg 02C0 loss. last_tick:107752. cur_tick:108763. period:100
[D][05:19:37][COMM]msg 02C1 loss. last_tick:107752. cur_tick:108763. period:100
[D][05:19:37][COMM]msg 02C2 loss. last_tick:107752. cur_tick:108764. period:100
[D][05:19:37][COMM]msg 02E0 loss. last_tick:107752. cur_tick:108764. period:100
[D][05:19:37][COMM]msg 02E1 loss. last_tick:107752. cur_tick:108764. period:100
[D][05:19:37][COMM]msg 02E2 loss. last_tick:107752. cur_ti

2025-07-31 18:22:36:255 ==>> ck:108765. period:100
[D][05:19:37][COMM]msg 0300 loss. last_tick:107752. cur_tick:108765. period:100
[D][05:19:37][COMM]msg 0301 loss. last_tick:107752. cur_tick:108765. period:100
[D][05:19:37][COMM]bat msg 0240 loss. last_tick:107752. cur_tick:108766. period:100. j,i:1 54
[D][05:19:37][COMM]bat msg 0241 loss. last_tick:107752. cur_tick:108766. period:100. j,i:2 55
[D][05:19:37][COMM]bat msg 0242 loss. last_tick:107752. cur_tick:108766. period:100. j,i:3 56
[D][05:19:37][COMM]bat msg 0244 loss. last_tick:107752. cur_tick:108767. period:100. j,i:5 58
[D][05:19:37][COMM]bat msg 024E loss. last_tick:107752. cur_tick:108767. period:100. j,i:15 68
[D][05:19:37][COMM]bat msg 024F loss. last_tick:107752. cur_tick:108768. period:100. j,i:16 69
[D][05:19:37][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 108768
[D][05:19:37][COMM]CAN message bat fault change: 0x00000000->0x0001802E 108769
[D][05:19:37][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 108769


2025-07-31 18:22:36:855 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 18:22:36:960 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 18:22:37:065 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    19:38][COMM]Try to Auto Lock Bat
[D][05:19:38][COMM]Main Task receive event:66 finished processing
[D][05:19:38][COMM]Main Task receive event:60
[D][05:19:38][COMM]smart_helmet_vol=255,255
[D][05:19:38][COMM]BAT CAN get state1 Fail 204
[D][05:19:38][COMM]BAT CAN get soc Fail, 204
[D][05:19:38][COMM]BAT CAN get state2 fail 204
[D][05:19:38][COMM]get soh error
[E][05:19:38][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:38][COMM]report elecbike
[W][05:19:38][PROT]remove success[1629955178],send_path[3],typ

2025-07-31 18:22:37:110 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 18:22:37:170 ==>> e[0000],priority[0],index[1],used[0]
[W][05:19:38][PROT]add success [1629955178],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:38][COMM]Main Task receive event:60 finished processing
[D][05:19:38][COMM]Main Task receive event:61
[D][05:19:38][COMM][D301]:type:3, trace id:280
[D][05:19:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:38][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:38][PROT]index:1
[D][05:19:38][PROT]is_send:1
[D][05:19:38][PROT]sequence_num:11
[D][05:19:38][PROT]retry_timeout:0
[D][05:19:38][PROT]retry_times:3
[D][05:19:38][PROT]send_path:0x3
[D][05:19:38][PROT]msg_type:0x5d03
[D][05:19:38][PROT]===========================================================
[W][05:19:38][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955178]
[D][05:19:38][PROT]===========================================================
[D][05:19:38][PROT]Sending traceid[999999999990000C]
[D][05:19:38][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:38][BLE ]BLE_WRN [frm_ble_get_current_fr

2025-07-31 18:22:37:275 ==>> amer:357] ble is not connect

[W][05:19:38][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:38][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:38][COMM]Receive Bat Lock cmd 0
[D][05:19:38][COMM]VBUS is 1
[D][05:19:38][COMM]id[], hw[000
[D][05:19:38][COMM]get mcMaincircuitVolt error
[D][05:19:38][COMM]get mcSubcircuitVolt error
[D][05:19:38][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:38][COMM]BAT CAN get state1 Fail 204
[D][05:19:38][COMM]BAT CAN get soc Fail, 204
[D][05:19:38][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:38][COMM]BAT CAN get state2 fail 204
[D][05:19:38][COMM]get bat work mode err
[W][05:19:38][PROT]remove success[1629955178],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:38][PROT]add success [1629955178],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:38][COMM]Main Task receive eve

2025-07-31 18:22:37:380 ==>> nt:61 finished processing
[D][05:19:38][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:38][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[W][05:19:38][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:38][COMM]arm_hub_enable: hub power: 0
[D][05:19:38][HSDK]hexlog index save 0 14848 58 @ 0 : 0
[D][05:19:38][HSDK]write save hexlog index [0]
[D][05:19:38][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:38][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:38][COMM]read battery soc:255
[D][05:19:38][COMM]f:[ec8

2025-07-31 18:22:37:455 ==>> 00m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:38][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:38][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:38][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 18:22:37:620 ==>> [W][05:19:39][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:39][COMM]arm_hub_enable: hub power: 0
[D][05:19:39][HSDK]hexlog index save 0 14848 58 @ 0 : 0
[D][05:19:39][HSDK]write save hexlog index [0]
[D][05:19:39][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:39][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:39][GNSS]handler GSMGet Base timeout


2025-07-31 18:22:37:695 ==>> [D][05:19:40][COMM]exit wheel stolen mode.


2025-07-31 18:22:37:800 ==>> [D][05:19:40][COMM]Main Task receive event:68
[D][05:19:40][COMM]handlerWheelStolen evt type = 2.
[E][05:19:40][COMM][MC]exit stolen,get 

2025-07-31 18:22:37:831 ==>> work mode err,rt:-3
[W][05:19:40][GNSS]stop locating
[D][05:19:40][GNSS]all continue location stop
[D][05:19:40][COMM]Main Task receive event:68 finished processing


2025-07-31 18:22:38:012 ==>> [W][05:19:40][COMM]Power Off


2025-07-31 18:22:38:155 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 18:22:38:163 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 18:22:38:199 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:22:38:271 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:22:38:376 ==>> [D][05:19:40][FCTY]get

2025-07-31 18:22:38:437 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 18:22:38:445 ==>> 检测【检测小电池关机电流】
2025-07-31 18:22:38:487 ==>> 开始小电池电流采样
2025-07-31 18:22:38:503 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:38:541 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 18:22:39:555 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 18:22:39:631 ==>> CurrentBattery:ƽ��:69.73

2025-07-31 18:22:40:062 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 18:22:40:070 ==>> 【检测小电池关机电流】通过,【69.73uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 18:22:40:394 ==>> MES过站成功
2025-07-31 18:22:40:403 ==>> #################### 【测试结束】 ####################
2025-07-31 18:22:40:446 ==>> 关闭5V供电
2025-07-31 18:22:40:460 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 18:22:40:567 ==>> 5A A5 04 5A A5 


2025-07-31 18:22:40:672 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 18:22:41:461 ==>> 关闭5V供电成功
2025-07-31 18:22:41:475 ==>> 关闭33V供电
2025-07-31 18:22:41:502 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 18:22:41:569 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 18:22:42:471 ==>> 关闭33V供电成功
2025-07-31 18:22:42:485 ==>> 关闭3.7V供电
2025-07-31 18:22:42:515 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 18:22:42:580 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 18:22:43:403 ==>>  

2025-07-31 18:22:43:478 ==>> 关闭3.7V供电成功
