2025-07-31 21:16:17:117 ==>> MES查站成功:
查站序号:P51000100531328D验证通过
2025-07-31 21:16:17:121 ==>> 扫码结果:P51000100531328D
2025-07-31 21:16:17:122 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:16:17:124 ==>> 测试参数版本:2024.10.11
2025-07-31 21:16:17:126 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:16:17:128 ==>> 检测【打开透传】
2025-07-31 21:16:17:130 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:16:17:172 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:16:17:729 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:16:17:732 ==>> 检测【检测接地电压】
2025-07-31 21:16:17:734 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:16:17:871 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:16:18:057 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:16:18:060 ==>> 检测【打开小电池】
2025-07-31 21:16:18:062 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:16:18:165 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:16:18:377 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:16:18:379 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:16:18:381 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:16:18:471 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:16:18:698 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:16:18:700 ==>> 检测【等待设备启动】
2025-07-31 21:16:18:703 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:16:19:025 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:16:19:222 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:16:19:732 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:16:19:855 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:16:19:915 ==>>                                 GPS Will Not Open


2025-07-31 21:16:20:304 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:16:20:767 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:16:20:782 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:16:21:062 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:16:21:065 ==>> 检测【产品通信】
2025-07-31 21:16:21:068 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:16:21:229 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 21:16:21:353 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:16:21:356 ==>> 检测【初始化完成检测】
2025-07-31 21:16:21:359 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:16:21:439 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:16:21:605 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:16:21:834 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:21:894 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:16:21:896 ==>> 检测【关闭大灯控制1】
2025-07-31 21:16:21:898 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:16:22:014 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:16:22:074 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:16:22:186 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:16:22:189 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:16:22:190 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:16:22:364 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:16:22:476 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:16:22:478 ==>> 检测【关闭仪表供电】
2025-07-31 21:16:22:480 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:16:22:671 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:16:22:774 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:16:22:777 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:16:22:781 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:16:22:851 ==>> [D][05:17:52][COMM]3640 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:22:956 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:16:23:072 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:16:23:075 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:16:23:077 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:16:23:231 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:16:23:371 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:16:23:374 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:16:23:375 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:16:23:538 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:16:23:663 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:16:23:665 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:16:23:667 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:16:23:767 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:16:23:872 ==>> [D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 19
[D][05:17:53][COMM]4651 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:23:949 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:16:23:965 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:16:23:967 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:16:24:068 ==>> 5A A5 03 5A A5 


2025-07-31 21:16:24:173 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 21:16:24:261 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:16:24:264 ==>> 该项需要延时执行
2025-07-31 21:16:24:398 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5007. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5008. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5010. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5011


2025-07-31 21:16:24:870 ==>> [D][05:17:54][COMM]5662 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:25:431 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:16:25:979 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:5

2025-07-31 21:16:26:084 ==>> 5][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][PROT]min_index:2, type:

2025-07-31 21:16:26:189 ==>> 0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][P

2025-07-31 21:16:26:264 ==>> ROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6673 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
                                        

2025-07-31 21:16:26:900 ==>> [D][05:17:56][COMM]7684 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:27:927 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8695 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:28:265 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:16:28:269 ==>> 检测【33V输入电压ADC】
2025-07-31 21:16:28:271 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:28:586 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3164  volt:5561 mv
[D][05:17:58][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:58][COMM]adc read left brake adc:11  volt:14 mv
[D][05:17:58][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:58][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:58][COMM]adc read battery ts volt:12 mv
[D][05:17:58][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:10  volt:231 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:1  volt:23 mv


2025-07-31 21:16:28:826 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:16:28:829 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:16:28:833 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:16:28:917 ==>> [D][05:17:58][COMM]9706 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:16:28:977 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1654mV
Get AD_V4 1mV
Get AD_V5 2776mV
Get AD_V6 1991mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:16:29:118 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:29:128 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:16:29:154 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:29:158 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:16:29:159 ==>> 原始值:【2776】, 乘以分压基数【2】还原值:【5552】
2025-07-31 21:16:29:191 ==>> 【TP68_VCC5V5(ADV5)】通过,【5552mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:16:29:194 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:16:29:224 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:16:29:229 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:16:29:261 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:16:29:263 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:16:29:282 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10020


2025-07-31 21:16:29:386 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1655mV
Get AD_V4 1mV
Get AD_V5 2778mV
Get AD_V6 1990mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:16:29:564 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:29:567 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:16:29:601 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:29:605 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:16:29:608 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 21:16:29:636 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:16:29:638 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:16:29:673 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1990mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:16:29:677 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:16:29:711 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:16:29:713 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:16:29:785 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2778mV
Get AD_V6 1988mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:16:30:007 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:30:028 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:16:30:039 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:30:042 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:16:30:044 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 21:16:30:072 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:16:30:076 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:16:30:105 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:16:30:107 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:16:30:141 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:16:30:143 ==>> 检测【打开WIFI(1)】
2025-07-31 21:16:30:146 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:16:30:290 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10717 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gs

2025-07-31 21:16:30:395 ==>> m read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][CAT1]Tail EXCEPTION i[0] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[1] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[2] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[3] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[4] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[5] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[6] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[7] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[8] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[9] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[10] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[11] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[12] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[13] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[14] [17]

2025-07-31 21:16:30:440 ==>>  
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[15] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]Tail EXCEPTION i[16] [17] 
+MT ERROR:700

[D][05:17:59][CAT1]<<< 
+MT ERROR:700



2025-07-31 21:16:30:516 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:16:30:689 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:16:30:694 ==>> 检测【清空消息队列(1)】
2025-07-31 21:16:30:700 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:16:30:863 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:16:30:924 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:16:30:983 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:16:30:987 ==>> 检测【打开GPS(1)】
2025-07-31 21:16:30:990 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:16:31:166 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:16:31:272 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:16:31:277 ==>> 检测【打开GSM联网】
2025-07-31 21:16:31:289 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:16:31:457 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:16:31:559 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:16:31:562 ==>> 检测【打开仪表供电1】
2025-07-31 21:16:31:565 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:16:31:760 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:16:31:846 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:16:31:849 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:16:31:855 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:16:31:867 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 21:16:32:076 ==>> [D][05:18:01][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:16:32:144 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:16:32:148 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:16:32:150 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:16:32:612 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 31, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 32
[D][05:18:02][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 32, ret: 6
[D][05:18:02][CAT1]gsm read msg sub id: 5
[D][05:18:02][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:02][CAT1]<<< 
867222087724045

OK

[D][05:18:02][CAT1]tx ret[9] >>> AT+CIMI

[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33409]
[D][05:18:02][CAT1]<<< 
460130071539162

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:02][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:02][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:16:32:685 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:16:32:688 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:16:32:693 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:16:32:868 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:16:32:971 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:16:32:974 ==>> 检测【AD_V20电压】
2025-07-31 21:16:32:976 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:16:33:080 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:16:33:171 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:16:33:369 ==>> 本次取值间隔时间:284ms
2025-07-31 21:16:33:399 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:16:33:403 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:16:33:504 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:16:33:580 ==>> 本次取值间隔时间:62ms
2025-07-31 21:16:33:584 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1643mV
OVER 150


2025-07-31 21:16:33:761 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:16:33:866 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 21:16:33:926 ==>>                 MM]14729 imu init OK


2025-07-31 21:16:33:971 ==>> 本次取值间隔时间:379ms
2025-07-31 21:16:33:998 ==>> 【AD_V20电压】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:34:001 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:16:34:005 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:16:34:076 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:16:34:288 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:16:34:291 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:16:34:296 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:16:34:460 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:16:34:577 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:16:34:580 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:16:34:583 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:16:34:672 ==>> 3A A3 02 01 A3 


2025-07-31 21:16:34:778 ==>> ON_OUT2
OVER 150


2025-07-31 21:16:34:852 ==>> [D][05:18:04][COMM]S->M yaw:INVALID


2025-07-31 21:16:34:866 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:16:34:868 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:16:34:884 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:16:35:200 ==>> [D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 5, ret: 6
[D][05:18:04][CAT1]sub id: 5, ret: 6

[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:04][M2M ]m2m gsm comm init done, ret[0]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][M2M ]M2M_GSM_INIT OK
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:04][SAL ]open socket ind id[4], rst[0]
[D][05:18:04][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:04][SAL ]Cellular task submsg id[8]
[D][05:18:04][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:04][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:04][CAT1]gsm read msg sub id: 8
[D][05:18:04][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:16:35:305 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           

2025-07-31 21:16:35:349 ==>>                                                                                                                                                                                                                         [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:16:35:433 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:16:35:436 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:16:35:440 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:16:35:704 ==>> [D][05:18:05][CAT1]opened : 0, 0
[D][05:18:05][SAL ]Cellular task submsg id[68]
[D][05:18:05][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:05][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:05][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:05][M2M ]g_m2m_is_idle become true
[D][05:18:05][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:16:35:978 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:16:35:982 ==>> 检测【AD_V21电压】
2025-07-31 21:16:35:985 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:16:36:148 ==>> 1A A1 20 00 00 
Get AD_V21 1639mV
OVER 150
[D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:05][GNSS]location recv gms init done evt
[D][05:18:05][GNSS]GPS start. ret=0
[D][05:18:05][CAT1]gsm read msg sub id: 23
[D][05:18:05][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:05][COMM]read battery soc:255
[D][05:18:05][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:05][COMM]M->S yaw:INVALID
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:16:36:332 ==>> 本次取值间隔时间:346ms
2025-07-31 21:16:36:399 ==>> 【AD_V21电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:36:410 ==>> 检测【关闭仪表供电2】
2025-07-31 21:16:36:414 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:16:36:566 ==>> [D][05:18:06][COMM]S->M yaw:INVALID
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:16:36:763 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:16:36:766 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:16:36:769 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:16:36:859 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,01,59,,,40,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:16:36:964 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:16:37:070 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:16:37:073 ==>> 检测【打开AccKey2供电】
2025-07-31 21:16:37:076 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:16:37:255 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:16:37:364 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:16:37:368 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:16:37:372 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:37:749 ==>> [D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:07][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:18:07][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:07][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:07][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:07][COMM]adc read battery ts volt:9 mv
[D][05:18:07][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:07][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:07][COMM]M->S yaw:INVALID
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,43,24,,,40,39,,,39,40,,,37,1*77

$GBGSV,2,2,07,25,,,33,13,,,39,14,,,38,1*70

$GBRMC,

2025-07-31 21:16:37:824 ==>> ,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1592.018,1592.018,50.947,2097152,2097152,2097152*40

[D][05:18:07][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:07][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:07][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:07][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:07][CAT1]<<< 
OK

[D][05:18:07][CAT1]exec over: func id: 23, ret: 6
[D][05:18:07][CAT1]sub id: 23, ret: 6



2025-07-31 21:16:37:918 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33133mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:16:37:921 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:16:37:926 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:16:37:929 ==>> [D][05:18:07][GNSS]recv submsg id[1]

2025-07-31 21:16:37:958 ==>> 
[D][05:18:07][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:16:38:048 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:16:38:207 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:16:38:211 ==>> 该项需要延时执行
2025-07-31 21:16:38:655 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,42,24,,,40,60,,,40,59,,,40,1*78

$GBGSV,4,2,14,39,,,39,14,,,37,40,,,37,25,,,36,1*76

$GBGSV,4,3,14,2,,,36,44,,,34,4,,,34,13,,,33,1*75

$GBGSV,4,4,14,5,,,32,38,,,36,1*49

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1530.775,1530.775,48.977,2097152,2097152,2097152*4A



2025-07-31 21:16:39:455 ==>> [D][05:18:09][COMM]S->M yaw:INVALID


2025-07-31 21:16:39:666 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,24,,,40,60,,,40,59,,,40,1*7A

$GBGSV,4,2,16,39,,,39,40,,,38,25,,,38,14,,,37,1*75

$GBGSV,4,3,16,34,,,37,1,,,36,2,,,35,44,,,35,1*73

$GBGSV,4,4,16,38,,,34,4,,,33,13,,,33,5,,,32,1*7F

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1526.185,1526.185,48.825,2097152,2097152,2097152*4C



2025-07-31 21:16:39:955 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:16:40:496 ==>> [D][05:18:10][COMM]M->S yaw:INVALID


2025-07-31 21:16:40:692 ==>> $GBGGA,131644.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,3,,,41,24,,,40,60,,,40,1*41

$GBGSV,6,2,22,59,,,40,39,,,39,25,,,39,40,,,38,1*78

$GBGSV,6,3,22,14,,,38,34,,,37,1,,,37,16,,,37,1*48

$GBGSV,6,4,22,2,,,35,44,,,35,38,,,35,9,,,35,1*74

$GBGSV,6,5,22,13,,,34,23,,,33,4,,,33,10,,,33,1*44

$GBGSV,6,6,22,5,,,32,6,,,32,1*75

$GBRMC,131644.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131644.512,0.000,1517.004,1517.004,48.538,2097152,2097152,2097152*50



2025-07-31 21:16:41:212 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:16:41:217 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:16:41:221 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:41:227 ==>> [D][05:18:11][COMM]S->M yaw:INVALID


2025-07-31 21:16:41:485 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3158  volt:5551 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:11][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:11][COMM]adc read throttle adc:5  volt:6 mv
[D][05:18:11][COMM]adc read battery ts volt:13 mv
[D][05:18:11][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:11][COMM]adc read throttle brake in adc:3  volt:5 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:16:41:711 ==>> $GBGGA,131645.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,41,59,,,41,24,,,40,1*4C

$GBGSV,7,2,25,60,,,40,25,,,40,39,,,39,41,,,39,1*7A

$GBGSV,7,3,25,40,,,38,14,,,38,1,,,37,16,,,37,1*42

$GBGSV,7,4,25,34,,,36,9,,,36,2,,,35,44,,,35,1*7E

$GBGSV,7,5,25,38,,,35,13,,,34,23,,,34,10,,,34,1*7B

$GBGSV,7,6,25,6,,,34,7,,,33,4,,,33,5,,,32,1*76

$GBGSV,7,7,25,8,,,31,1*4B

$GBRMC,131645.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131645.512,0.000,1515.726,1515.726,48.500,2097152,2097152,2097152*5A



2025-07-31 21:16:41:760 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:16:41:764 ==>> 检测【打开AccKey1供电】
2025-07-31 21:16:41:769 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:16:41:955 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 
[D][05:18:11][COMM]read battery soc:255


2025-07-31 21:16:42:047 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:16:42:051 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:16:42:055 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:16:42:169 ==>> 1A A1 00 40 00 
Get AD_V14 2668mV
OVER 150


2025-07-31 21:16:42:304 ==>> 原始值:【2668】, 乘以分压基数【2】还原值:【5336】
2025-07-31 21:16:42:341 ==>> 【读取AccKey1电压(ADV14)前】通过,【5336mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:16:42:344 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:16:42:347 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:42:752 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3162  volt:5558 mv
[D][05:18:12][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:12][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:12][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:12][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:12][COMM]adc read battery ts volt:15 mv
[D][05:18:12][COMM]adc read in 24v adc:1300  volt:32880 mv
[D][05:18:12][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:12][COMM]M->S yaw:INVALID
[D][05:18:12][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,131646.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,40,59,,,40,24,,,40,1*40

$GBGSV,7,2,28,60,,,40,25,,,40,39,,,39,41,,,39,1*77

$GBGSV,7,3,28,40,,,38,14,,,38,1,,,37,16,,,37,1*4F

$GBGSV,7,4,28,34,,,36,9,,,36,2,,,35,44,,,35,1*73

$GBGSV,7,5,28,38,,,35,7,,,35,42,,,35,13,,,34,1*47

$GB

2025-07-31 21:16:42:797 ==>> GSV,7,6,28,23,,,34,10,,,34,6,,,34,4,,,33,1*78

$GBGSV,7,7,28,8,,,32,12,,,32,11,,,31,5,,,31,1*72

$GBRMC,131646.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131646.512,0.000,1499.916,1499.916,47.998,2097152,2097152,2097152*5B



2025-07-31 21:16:42:907 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5558mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:16:42:911 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:16:42:914 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:16:43:056 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:16:43:197 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:16:43:200 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:16:43:205 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:16:43:268 ==>> 1A A1 00 40 00 
Get AD_V14 2666mV
OVER 150


2025-07-31 21:16:43:451 ==>> 原始值:【2666】, 乘以分压基数【2】还原值:【5332】
2025-07-31 21:16:43:488 ==>> 【读取AccKey1电压(ADV14)后】通过,【5332mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:16:43:491 ==>> 检测【打开WIFI(2)】
2025-07-31 21:16:43:494 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:16:43:760 ==>> $GBGGA,131647.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,59,,,40,24,,,40,1*40

$GBGSV,7,2,28,60,,,40,25,,,40,39,,,39,41,,,39,1*77

$GBGSV,7,3,28,40,,,38,14,,,38,1,,,37,16,,,37,1*4F

$GBGSV,7,4,28,34,,,36,9,,,36,7,,,36,2,,,35,1*47

$GBGSV,7,5,28,44,,,35,38,,,35,42,,,35,13,,,35,1*71

$GBGSV,7,6,28,6,,,35,23,,,34,10,,,34,4,,,32,1*78

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,32,11,,,31,1*71

$GBRMC,131647.512,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131647.512,0.000,1504.353,1504.353,48.135,2097152,2097152,2097152*5A

[W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:16:43:958 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 21:16:44:041 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:16:44:045 ==>> 检测【转刹把供电】
2025-07-31 21:16:44:048 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:16:44:248 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:16:44:327 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:16:44:331 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:16:44:335 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:16:44:430 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:16:44:770 ==>> +WIFISCAN:4,0,F88C21BCF57D,-31
+WIFISCAN:4,1,CC057790A740,-70
+WIFISCAN:4,2,CC057790A741,-71
+WIFISCAN:4,3,CC057790A5C1,-80

[D][05:18:14][CAT1]wifi scan report total[4]
$GBGGA,131648.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,59,,,40,24,,,40,1*40

$GBGSV,7,2,28,60,,,40,25,,,40,39,,,39,41,,,39,1*77

$GBGSV,7,3,28,40,,,38,14,,,38,1,,,37,16,,,37,1*4F

$GBGSV,7,4,28,34,,,36,9,,,36,7,,,36,2,,,35,1*47

$GBGSV,7,5,28,44,,,35,38,,,35,42,,,35,13,,,35,1*71

$GBGSV,7,6,28,6,,,35,23,,,34,10,,,34,4,,,32,1*78

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,31,11,,,31,1*72

$GBRMC,131648.512,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131648.512,0.000,1502.875,1502.875,48.091,2097152,2097152,2097152*5A

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:16:44:966 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 21:16:45:362 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:16:45:467 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:16:45:587 ==>> [W][05:18:15][COMM]>>>>>Input command = ?<<<<
[D][05:18:15][COMM]S->M yaw:INVALID
1A A1 00 80 00 
Get AD_V15 2415mV
OVER 150


2025-07-31 21:16:45:632 ==>> 原始值:【2415】, 乘以分压基数【2】还原值:【4830】
2025-07-31 21:16:45:666 ==>> 【读取AD_V15电压(前)】通过,【4830mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:16:45:672 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:16:45:679 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:16:45:692 ==>>                       0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,60,,,40,1*4A

$GBGSV,7,2,28,25,,,40,59,,,39,39,,,39,41,,,39,1*73

$GBGSV,7,3,28,40,,,38,14,,,38,1,,,37,16,,,37,1*4F

$GBGSV,7,4,28,34,,,36,9,,,36,7,,,36,2,,,35,1*47

$GBGSV,7,5,28,44,,,35,38,,,35,42,,,35,13,,,35,1*71

$GBGSV,7,6,28,6,,,35,23,,,34,10,,,34,4,,,32,1*78

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,32,11,,,30,1*70

$GBRMC,131649.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,13

2025-07-31 21:16:45:722 ==>> 1649.512,0.000,1501.394,1501.394,48.042,2097152,2097152,2097152*55



2025-07-31 21:16:45:767 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:16:45:827 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:16:45:872 ==>> 1A A1 01 00 00 
Get AD_V16 2448mV
OVER 150


2025-07-31 21:16:45:932 ==>> 原始值:【2448】, 乘以分压基数【2】还原值:【4896】
2025-07-31 21:16:45:974 ==>> 【读取AD_V16电压(前)】通过,【4896mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:16:45:978 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:16:45:980 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:45:983 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 21:16:46:279 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:15][COMM]adc read out 24v adc:5  volt:126 mv
[D][05:18:15][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:15][COMM]adc read battery ts volt:15 mv
[D][05:18:15][COMM]adc read in 24v adc:1297  volt:32804 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3102  volt:5452 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2410  volt:3883 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:16:46:524 ==>> 【转刹把供电电压(主控ADC)】通过,【5452mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:16:46:528 ==>> 检测【转刹把供电电压】
2025-07-31 21:16:46:533 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:46:722 ==>> $GBGGA,131650.512,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,60,,,40,1*4B

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,39,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,1,,,36,1*41

$GBGSV,7,4,28,34,,,36,7,,,36,9,,,35,2,,,35,1*44

$GBGSV,7,5,28,44,,,35,38,,,35,42,,,35,13,,,35,1*71

$GBGSV,7,6,28,6,,,35,23,,,34,10,,,34,4,,,32,1*78

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,31,11,,,30,1*73

$GBRMC,131650.512,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131650.512,0.000,1495.473,1495.473,47.855,2097152,2097152,2097152*5C



2025-07-31 21:16:46:947 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3163  volt:5559 mv
[D][05:18:16][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:16][COMM]adc read left brake adc:14  volt:18 mv
[D][05:18:16][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:16][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:16][COMM]adc read battery ts volt:11 mv
[D][05:18:16][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3102  volt:5452 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2412  volt:3886 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 21:16:47:087 ==>> 【转刹把供电电压】通过,【5452mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:16:47:090 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:16:47:093 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:16:47:234 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:16:47:394 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:16:47:401 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:16:47:406 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:16:47:504 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:16:47:564 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 21:16:47:669 ==>> $GBGGA,131651.512,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,60,,,40,1*4B

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,39,1*7D

$GBGSV,7,3,28,40,,,38,14,,,38,16,,,37,1,,,37,1*4F

$GBGSV,7,4,28,7,,,37,34,,,36,9,,,35,44,

2025-07-31 21:16:47:714 ==>> ,,35,1*77

$GBGSV,7,5,28,42,,,35,13,,,35,6,,,35,2,,,34,1*7F

$GBGSV,7,6,28,38,,,34,23,,,34,10,,,34,4,,,32,1*44

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,32,11,,,30,1*70

$GBRMC,131651.512,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131651.512,0.000,1498.434,1498.434,47.950,2097152,2097152,2097152*59



2025-07-31 21:16:47:845 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:16:47:849 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:16:47:853 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:16:47:959 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:16:47:974 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:16:48:064 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:16:48:079 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 21:16:48:169 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:16:48:277 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:16:48:393 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:16:48:397 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:16:48:402 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:16:48:474 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 21:16:48:715 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:16:48:720 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:16:48:724 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:16:48:764 ==>> $GBGGA,131652.512,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,60,,,40,1*4A

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,39,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,7,,,37,1*46

$GBGSV,7,4,28,1,,,36,34,,,36,9,,,35,44,,,35,1*70

$GBGSV,7,5,28,42,,,35,13,,,35,6,,,35,2,,,35,1*7E

$GBGSV,7,6,28,38,,,35,23,,,34,10,,,34,4,,,32,1*45

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,32,11,,,30,1*70

$GBRMC,131652.512,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131652.512,0.000,1499.914,1499.914,47.997,2097152,2097152,2097152*51

[D][05:18:18][COMM]M->S yaw:INVALID
3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:16:49:034 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:16:49:039 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:16:49:046 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:16:49:170 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 21:16:49:394 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:16:49:399 ==>> 检测【左刹电压测试1】
2025-07-31 21:16:49:406 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:49:754 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:19][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:19][COMM]adc read left brake adc:1724  volt:2272 mv
[D][05:18:19][COMM]adc read right brake adc:1723  volt:2271 mv
[D][05:18:19][COMM]adc read throttle adc:1733  volt:2284 mv
[D][05:18:19][COMM]adc read battery ts volt:9 mv
[D][05:18:19][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:19][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:5  volt:115 mv
$GBGGA,131653.512,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,60,,,40,1*4B

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,38,1*7C

$GBGSV,7,3,28,40,,,38,14,,,38,16,,,37,7,,,37,1*49

$GBGSV,7,4,28,1,,,37,34,,,36,9,,,35,44,,,35,1*71

$GBGSV,7,5,28,42,,,35,13,,,35,6,,,35,2,,,35,1*7E

$GBGSV,7,6,28,38,,,35,23,,,34,10,,,34,4,,,32,

2025-07-31 21:16:49:799 ==>> 1*45

$GBGSV,7,7,28,8,,,32,12,,,32,5,,,32,11,,,30,1*70

$GBRMC,131653.512,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131653.512,0.000,1499.911,1499.911,47.994,2097152,2097152,2097152*53



2025-07-31 21:16:49:942 ==>> 【左刹电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 21:16:49:946 ==>> 检测【右刹电压测试1】
2025-07-31 21:16:49:966 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 21:16:50:120 ==>> 【右刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 21:16:50:124 ==>> 检测【转把电压测试1】
2025-07-31 21:16:50:157 ==>> 【转把电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 21:16:50:160 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:16:50:163 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:16:50:273 ==>> 3A A3 03 00 A3 


2025-07-31 21:16:50:363 ==>> OFF_OUT3
OVER 150


2025-07-31 21:16:50:441 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:16:50:447 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:16:50:452 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:16:50:577 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:16:50:682 ==>> $GBGGA,131654.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,60,,,40,1*4B

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,38,1*7C

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,7,,,37,1*46

$GBGSV,7,4,28,1,,,37,34,,,36,9,,,35,44,,,35,1*71

$GBGSV,7,5,28,42,,,35,13,,,35,6,,,35,2,,,35,1*7E

$GBGSV,7,6,28,38,,,35,23,,,34,10,,,34,4,,,32,1*45

$GBGSV,7,7,28,8,,,32,12,,,32,5

2025-07-31 21:16:50:727 ==>> ,,,32,11,,,30,1*70

$GBRMC,131654.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131654.512,0.000,1498.430,1498.430,47.946,2097152,2097152,2097152*5B



2025-07-31 21:16:50:733 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:16:50:738 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:16:50:742 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:16:50:877 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 21:16:51:031 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:16:51:035 ==>> 检测【左刹电压测试2】
2025-07-31 21:16:51:043 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:16:51:392 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3159  volt:5552 mv
[D][05:18:21][COMM]adc read out 24v adc:6  volt:151 mv
[D][05:18:21][COMM]adc read left brake adc:6  volt:7 mv
[D][05:18:21][COMM]adc read right brake adc:12  volt:15 mv
[D][05:18:21][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:21][COMM]adc read battery ts volt:13 mv
[D][05:18:21][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:21][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2411  volt:3884 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                     

2025-07-31 21:16:51:583 ==>> 【左刹电压测试2】通过,【7】符合目标值【0】至【50】要求!
2025-07-31 21:16:51:587 ==>> 检测【右刹电压测试2】
2025-07-31 21:16:51:623 ==>> 【右刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 21:16:51:626 ==>> 检测【转把电压测试2】
2025-07-31 21:16:51:659 ==>> 【转把电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 21:16:51:663 ==>> 检测【晶振检测】
2025-07-31 21:16:51:671 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:16:51:714 ==>> $GBGGA,131655.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,40,24,,,40,60,,,40,1*4B

$GBGSV,7,2,28,25,,,40,59,,,40,39,,,39,41,,,38,1*7C

$GBGSV,7,3,28,40,,,38,14,,,37,16,,,37,7,,,37,1*46

$GBGSV,7,4,28,1,,,37,34,,,36,9,,,35,44,,,35,1*71

$GBGSV,7,5,28,42,,,35,6,,,35,38,,,35,13,,,34,1*46

$GBGSV,7,6,28,2,,,34,23,,,34,10,,,34,4,,,33,1*7C

$GBGSV,7,7,28,5,,,33,8,,,32,12,,,32,11,,,30,1*71

$GBRMC,131655.512,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131655.512,0.000,1498.428,1498.428,47.943,2097152,2097152,2097152*5F



2025-07-31 21:16:51:819 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 21:16:51:959 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:16:51:963 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:16:51:969 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:16:51:987 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:16:52:076 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1654mV
Get AD_V4 1651mV
Get AD_V5 2776mV
Get AD_V6 1989mV
Get AD_V7 1089mV
OVER 150


2025-07-31 21:16:52:248 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1651mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:16:52:253 ==>> 检测【检测BootVer】
2025-07-31 21:16:52:258 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:16:52:651 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:22][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:22][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:22][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:22][FCTY]DeviceID    = 460130071539162
[D][05:18:22][FCTY]HardwareID  = 867222087724045
[D][05:18:22][FCTY]MoBikeID    = 9999999999
[D][05:18:22][FCTY]LockID      = FFFFFFFFFF
[D][05:18:22][FCTY]BLEFWVersion= 105
[D][05:18:22][FCTY]BLEMacAddr   = C5444B92F49F
[D][05:18:22][FCTY]Bat         = 3944 mv
[D][05:18:22][FCTY]Current     = 0 ma
[D][05:18:22][FCTY]VBUS        = 11800 mv
[D][05:18:22][FCTY]TEMP= 0,BATID= 293,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:22][FCTY]Ext battery vol = 32, adc = 1304
[D][05:18:22][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 50 mv
[D][05:18:22][FCTY]Bike Type flag is invalied
[D][05:18:22][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:22][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:22][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:22][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:22][FCTY

2025-07-31 21:16:52:756 ==>> ]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:22][FCTY]Bat1         = 3722 mv
[D][05:18:22][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:22][FCTY]==========Modules-nRF5340 ==========
[D][05:18:22][COMM]M->S yaw:INVALID
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 21:16:52:803 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:16:52:807 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:16:52:810 ==>> 检测【检测固件版本】
2025-07-31 21:16:52:838 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:16:52:842 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:16:52:845 ==>> 检测【检测蓝牙版本】
2025-07-31 21:16:52:870 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:16:52:874 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:16:52:878 ==>> 检测【检测MoBikeId】
2025-07-31 21:16:52:903 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:16:52:907 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:16:52:914 ==>> 检测【检测蓝牙地址】
2025-07-31 21:16:52:920 ==>> 取到目标值:C5444B92F49F
2025-07-31 21:16:52:935 ==>> 【检测蓝牙地址】通过,【C5444B92F49F】符合目标值【】要求!
2025-07-31 21:16:52:944 ==>> 提取到蓝牙地址:C5444B92F49F
2025-07-31 21:16:52:949 ==>> 检测【BOARD_ID】
2025-07-31 21:16:52:969 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:16:52:973 ==>> 检测【检测充电电压】
2025-07-31 21:16:53:004 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:16:53:007 ==>> 检测【检测VBUS电压1】
2025-07-31 21:16:53:036 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:16:53:040 ==>> 检测【检测充电电流】
2025-07-31 21:16:53:071 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:16:53:075 ==>> 检测【检测IMEI】
2025-07-31 21:16:53:085 ==>> 取到目标值:867222087724045
2025-07-31 21:16:53:107 ==>> 【检测IMEI】通过,【867222087724045】符合目标值【】要求!
2025-07-31 21:16:53:110 ==>> 提取到IMEI:867222087724045
2025-07-31 21:16:53:114 ==>> 检测【检测IMSI】
2025-07-31 21:16:53:120 ==>> 取到目标值:460130071539162
2025-07-31 21:16:53:191 ==>> 【检测IMSI】通过,【460130071539162】符合目标值【】要求!
2025-07-31 21:16:53:196 ==>> 提取到IMSI:460130071539162
2025-07-31 21:16:53:201 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:16:53:207 ==>> 取到目标值:460130071539162
2025-07-31 21:16:53:259 ==>> 【校验网络运营商(移动)】通过,【460130071539162】符合目标值【】要求!
2025-07-31 21:16:53:263 ==>> 检测【打开CAN通信】
2025-07-31 21:16:53:267 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:16:53:381 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:16:53:602 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:16:53:606 ==>> 检测【检测CAN通信】
2025-07-31 21:16:53:611 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:16:53:747 ==>> $GBGGA,131657.512,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,24,,,40,25,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,28,40,,,38,14,,,37,7,,,37,16,,,36,1*47

$GBGSV,7,4,28,1,,,36,34,,,36,9,,,35,44,,,35,1*70

$GBGSV,7,5,28,42,,,35,6,,,35,2,,,35,38,,,34,1*76

$GBGSV,7,6,28,13,,,34,23,,,34,10,,,34,4,,,33,1*4C

$GBGSV,7,7,28,5,,,32,8,,,32,12,,,32,11,,,30,1*70

$GBRMC,131657.512,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131657.512,0.000,1493.988,1493.988,47.804,2097152,2097152,2097152*5F

can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:16:53:807 ==>> [D][05:18:23][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 34596
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:16:53:897 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:23][COMM]M->S yaw:INVALID


2025-07-31 21:16:53:914 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:16:53:919 ==>> 检测【关闭CAN通信】
2025-07-31 21:16:53:925 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:16:53:931 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:16:53:987 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS
[D][05:18:23][COMM]read battery soc:255


2025-07-31 21:16:54:092 ==>> [D][05:18:23][COMM]S->M yaw:INVALID


2025-07-31 21:16:54:205 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:16:54:209 ==>> 检测【打印IMU STATE】
2025-07-31 21:16:54:213 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:16:54:366 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:24][COMM]YAW data: 32763[32763]
[D][05:18:24][COMM]pitch:-66 roll:0
[D][05:18:24][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:16:54:487 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:16:54:491 ==>> 检测【六轴自检】
2025-07-31 21:16:54:495 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:16:54:748 ==>> $GBGGA,131658.512,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,25,,,41,24,,,40,1*4A

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,39,41,,,38,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,7,,,37,1,,,37,1*70

$GBGSV,7,4,28,16,,,36,34,,,36,9,,,35,44,,,35,1*46

$GBGSV,7,5,28,42,,,35,6,,,35,2,,,35,38,,,34,1*76

$GBGSV,7,6,28,13,,,34,23,,,34,10,,,34,4,,,32,1*4D

$GBGSV,7,7,28,5,,,32,8,,,32,12,,,32,11,,,30,1*70

$GBRMC,131658.512,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131658.512,0.000,1493.991,1493.991,47.807,2097152,2097152,2097152*53

[W][05:18:24][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:24][CAT1]gsm read msg sub id: 12
[D][05:18:24][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:16:55:723 ==>> $GBGGA,131659.512,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,25,,,40,24,,,40,1*4B

$GBGSV,7,2,28,60,,,39,59,,,39,39,,,39,41,,,38,1*7D

$GBGSV,7,3,28,40,,,38,14,,,37,7,,,37,1,,,37,1*70

$GBGSV,7,4,28,16,,,37,34,,,36,6,,,36,9,,,35,1*72

$GBGSV,7,5,28,44,,,35,42,,,35,2,,,35,38,,,34,1*40

$GBGSV,7,6,28,13,,,34,23,,,34,10,,,34,4,,,32,1*4D

$GBGSV,7,7,28,5,,,32,8,,,32,12,,,32,11,,,30,1*70

$GBRMC,131659.512,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131659.512,0.000,1495.469,1495.469,47.851,2097152,2097152,2097152*51



2025-07-31 21:16:55:995 ==>> [D][05:18:25][COMM]read battery soc:255


2025-07-31 21:16:56:355 ==>> [D][05:18:26][CAT1]<<< 
OK

[D][05:18:26][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:16:56:535 ==>> [D][05:18:26][COMM]Main Task receive event:142
[D][05:18:26][COMM]###### 37307 imu self test OK ######
[D][05:18:26][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-6,-6,4054]
[D][05:18:26][COMM]Main Task receive event:142 finished processing


2025-07-31 21:16:56:715 ==>> $GBGGA,131700.512,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,25,,,40,24,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,28,40,,,38,14,,,37,7,,,37,1,,,37,1*70

$GBGSV,7,4,28,16,,,37,34,,,36,9,,,36,44,,,36,1*47

$GBGSV,7,5,28,6,,,35,42,,,35,2,,,35,38,,,35,1*77

$GBGSV,7,6,28,13,,,35,23,,,35,10,,,34,8,,,33,1*40

$GBGSV,7,7,28,4,,,32,5,,,32,12,,,32,11,,,30,1*7C

$GBRMC,131700.512,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131700.512,0.000,1504.349,1504.349,48.131,2097152,2097152,2097152*5C



2025-07-31 21:16:56:822 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:16:56:827 ==>> 检测【打印IMU STATE2】
2025-07-31 21:16:56:830 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:16:56:974 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:16:57:114 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:16:57:118 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:16:57:122 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:16:57:171 ==>> 5A A5 02 5A A5 


2025-07-31 21:16:57:276 ==>> CLOSE_POWER_OUT1
OVER 150
[D][05:18:27][COMM]M->S yaw:INVALID


2025-07-31 21:16:57:381 ==>> [D][05:18:27][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:

2025-07-31 21:16:57:409 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:16:57:413 ==>> 检测【检测VBUS电压2】
2025-07-31 21:16:57:420 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:16:57:427 ==>> 18:27][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:27][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 21:16:57:792 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539162
[D][05:18:27][FCTY]HardwareID  = 867222087724045
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C5444B92F49F
[D][05:18:27][FCTY]Bat         = 3924 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 8, adc = 347
[D][05:18:27][FCTY]Acckey1 vol = 5544 mv, Acckey2 vol = 50 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNS

2025-07-31 21:16:57:897 ==>> S_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3722 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
$GBGGA,131701.512,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,42,3,,,41,25,,,40,24,,,40,1*4B

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,39,41,,,38,1*7D

$GBGSV,7,3,28,40,,,38,14,,,38,7,,,37,1,,,37,1*7F

[D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
$GBGSV,7,4,28,16,,,37,34,,,36,9,,,36,44,,,35,1*44

$GBGSV,7,5,28,6,,,35,42,,,35,2,,,35,38,,,35,1*77

$GBGSV,7,6,28,13,,,35,23,,,35,10,,,34,4,,,33,1*4C

$GBGSV,7,7,28,8,,,32,5,,,32,12,,,32,11,,,30,1*70

$GBRMC,131701.512,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131701.512,0.000,751.513,751.513,687.277,2097152,2097152,2097152*69



2025-07-31 21:16:57:960 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:16:58:332 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539162
[D][05:18:27][FCTY]HardwareID  = 867222087724045
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = C5444B92F49F
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 50 ma
[D][05:18:27][FCTY]VBUS        = 11800 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 178
[D][05:18:27][FCTY]Acckey1 vol = 5561 mv, Acckey2 vol = 50 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION 

2025-07-31 21:16:58:377 ==>> = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3722 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:16:58:437 ==>>                                      

2025-07-31 21:16:58:535 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:16:58:954 ==>> $GBGGA,131702.512,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,8,1,29,33,,,42,3,,,41,25,,,41,60,,,40,1*44

$GBGSV,8,2,29,24,,,40,59,,,40,39,,,39,40,,,38,1*72

$GBGSV,8,3,29,41,,,38,7,,,37,1,,,37,16,,,37,1*7D

$GBGSV,8,4,29,14,,,37,9,,,36,34,,,36,42,,,36,1*4D

$GBGSV,8,5,29,2,,,35,13,,,35,38,,,35,44,,,35,1*4B

$GBGSV,8,6,29,6,,,35,23,,,35,10,,,34,5,,,33,1*77

$GBGSV,8,7,29,4,,,33,8,,,32,12,,,32,11,,,30,1*7E

$GBGSV,8,8,29,28,,,,1*77

$GBRMC,131702.512,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,131702.512,0.000,752.990,752.990,688.627,2097152,2097152,2097152*64

[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539162
[D][05:18:28][FCTY]HardwareID  = 867222087724045
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = C5444B92F4

2025-07-31 21:16:59:059 ==>> 9F
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 50 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 3, adc = 149
[D][05:18:28][FCTY]Acckey1 vol = 5558 mv, Acckey2 vol = 202 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3722 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][COMM]msg 0601 loss. last_tick:34586. cur_tick:39594. period:500
[D][05:18:28][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 39594


2025-07-31 21:16:59:337 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:16:59:758 ==>> [D][05:18:28][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:28][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]Main Task receive event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[W][05:18:29][GNSS]stop locating
[D][05:18:29][GNSS]stop event:8
[D][05:18:29][GNSS]GPS stop. ret=0
[D][05:18:29][CAT1]gsm read msg sub id: 24
[D][05:18:29][GNSS]all continue location stop
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05

2025-07-31 21:16:59:863 ==>> :18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:29][PROT]===========================================================
[D][05:18:29][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18

2025-07-31 21:16:59:968 ==>> :29][PROT]sending traceid [9999999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:29][CAT1]<<< 
OK

[D][05:18:29][CAT1]exec over: func id: 24, ret: 6
[D][05:18:29][CAT1]sub id: 24, ret: 6

[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B508C6A21BF0E8BCBF5C320FD0E4C66AF2522E795BC5D5CAEC3F98A29B65BA4CBAA5E7E5DD3ED01DF2E81A0AA76DC6153F612DA0975AB22E19CB51B9E7E1A2D23F3CAE5F0B7BAFAC6F9EFD2A6A02894419DF4C
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[

2025-07-31 21:17:00:073 ==>> D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]
[D][05:18:29][COMM]M->S yaw:INVALID
[W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539162
[D][05:18:29][FCTY]HardwareID  = 867222087724045
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = C5444B92F49F
[D][05:18:29][FCTY]Bat         = 3684 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 4900 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18

2025-07-31 21:17:00:178 ==>> :29][FCTY]Ext battery vol = 2, adc = 117
[D][05:18:29][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 50 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1         = 3722 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                           

2025-07-31 21:17:00:394 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:17:00:727 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539162
[D][05:18:30][FCTY]HardwareID  = 867222087724045
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = C5444B92F49F
[D][05:18:30][FCTY]Bat         = 3764 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 4900 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 2, adc = 99
[D][05:18:30][FCTY]Acckey1 vol = 5542 mv, Acckey2 vol = 75 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:30][FCTY]CAT1_

2025-07-31 21:17:00:772 ==>> GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3722 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:17:00:938 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:17:00:943 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:17:00:951 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:17:01:074 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:17:01:134 ==>> [D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 21:17:01:233 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:17:01:238 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:17:01:247 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:17:01:258 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 21:17:01:374 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:17:01:530 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:17:01:539 ==>> 检测【打开WIFI(3)】
2025-07-31 21:17:01:546 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:17:01:758 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:17:01:821 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:17:01:826 ==>> 检测【扩展芯片hw】
2025-07-31 21:17:01:835 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:17:02:339 ==>> [D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:02:740 ==>> [D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:17:02:861 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:17:03:259 ==>>                                                                             [COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]more than the number of battery plugs
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:32][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:32][COMM]Bat auth off fail, error:-1
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[

2025-07-31 21:17:03:364 ==>> D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:32][COMM]Main Task receive event:65
[D][05:18:32][COMM]main task tmp_sleep_event = 80
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove suc

2025-07-31 21:17:03:470 ==>> cess[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PRO

2025-07-31 21:17:03:559 ==>> T]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32][PROT]===========================================================
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 21:17:03:604 ==>>                                                                                                                                            

2025-07-31 21:17:03:895 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:17:03:940 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
[D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44673]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:17:04:045 ==>> [W][05:18:33][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:33][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:17:04:075 ==>> 


2025-07-31 21:17:04:180 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:17:04:185 ==>> 检测【扩展芯片boot】
2025-07-31 21:17:04:210 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:17:04:215 ==>> 检测【扩展芯片sw】
2025-07-31 21:17:04:240 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:17:04:245 ==>> 检测【检测音频FLASH】
2025-07-31 21:17:04:252 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:17:04:381 ==>> [D][05:18:34][COMM]45155 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:04:456 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:17:04:730 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:3
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052e08] format[0]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
                                                         

2025-07-31 21:17:04:760 ==>>                                                                                                                                        

2025-07-31 21:17:04:925 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45700]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:17:05:234 ==>> [D][05:18:35][COMM]read battery soc:255


2025-07-31 21:17:05:384 ==>> [D][05:18:35][COMM]46166 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:05:952 ==>> [D][05:18:35][COMM]f:[drv_audio_ack_receive].wait ack timeout!![46723]
[D][05:18:35][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:35][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:17:06:401 ==>> [D][05:18:36][COMM]47178 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:07:253 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:17:07:405 ==>> [D][05:18:37][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:18:37][COMM]48189 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:08:420 ==>> [D][05:18:38][COMM]crc 108B
[D][05:18:38][COMM]flash test ok
[D][05:18:38][COMM]49201 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:09:254 ==>> [D][05:18:39][COMM]read battery soc:255


2025-07-31 21:17:09:314 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:17:09:324 ==>> 检测【打开喇叭声音】
2025-07-31 21:17:09:336 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:17:09:479 ==>> [D][05:18:39][COMM]50212 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:39][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:39][COMM]file:A20 exist
[D][05:18:39][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:39][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:17:09:602 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:17:09:607 ==>> 检测【打开大灯控制】
2025-07-31 21:17:09:611 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:17:09:753 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:17:09:896 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:17:09:904 ==>> 检测【关闭仪表供电3】
2025-07-31 21:17:09:917 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:17:10:054 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:39][COMM]set POWER 0


2025-07-31 21:17:10:198 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:17:10:204 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:17:10:214 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:17:10:329 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:17:10:434 ==>> [D][05:18:40][COMM]51223 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:10:490 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:17:10:496 ==>> 检测【读大灯电压】
2025-07-31 21:17:10:500 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:17:10:660 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:40][COMM]arm_hub read adc[5],val[33224]


2025-07-31 21:17:10:819 ==>> 【读大灯电压】通过,【33224mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:17:10:825 ==>> 检测【关闭大灯控制2】
2025-07-31 21:17:10:836 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:17:10:935 ==>> [W][05:18:40][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:17:11:152 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:17:11:161 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:17:11:169 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:17:11:274 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:17:11:364 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:41][COMM]arm_hub read adc[5],val[115]


2025-07-31 21:17:11:446 ==>> 【关大灯控制后读大灯电压】通过,【115mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:17:11:457 ==>> 检测【打开WIFI(4)】
2025-07-31 21:17:11:465 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:17:11:477 ==>> [D][05:18:41][COMM]52235 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:11:634 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:17:11:798 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:17:11:804 ==>> 检测【EC800M模组版本】
2025-07-31 21:17:11:812 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:12:455 ==>> [D][05:18:42][COMM]53245 imu init OK
[D][05:18:42][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:17:12:841 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:13:277 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 21:17:13:457 ==>> [D][05:18:43][COMM]imu error,enter wait


2025-07-31 21:17:13:700 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:13:866 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:14:887 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:14:932 ==>> [D][05:18:44][CAT1]exec over: func id: 15, ret: -93
[D][05:18:44][CAT1]sub id: 15, ret: -93

[D][05:18:44][SAL ]Cellular task submsg id[68]
[D][05:18:44][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:44][SAL ]socket send fail. id[4]
[D][05:18:44][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:44][CAT1]gsm read msg sub id: 12
[D][05:18:44][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:44][M2M ]m2m select fd[4]
[D][05:18:44][M2M ]socket[4] Link is disconnected
[D][05:18:44][M2M ]tcpclient close[4]
[D][05:18:44][SAL ]socket[4] has closed
[D][05:18:44][PROT]protocol read data ok
[E][05:18:44][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:44][PROT]M2M Send Fail [1629955124]
[D][05:18:44][PROT]CLEAN,SEND:1
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:44][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:18:44][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:15:374 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55989]
[D][05:18:44][COMM]accel parse set 0
[D][05:18:44][COMM][Audio].l:[1032].open hexlog save
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:44][COMM]file:A20 exist
[D][05:18:45][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:45][COMM]read file, len:15228, num:4
[D][05:18:45][COMM]--->crc16:0x419c
[D][05:18:45][COMM]read file success
[W][05:18:45][COMM][Audio].l:[936].close hexlog save
[D][05:18:45][COMM]accel parse set 1
[D][05:18:45][COMM][Audio]mon:9,05:18:45
[D][05:18:45][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:45][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:45][COMM]read battery soc:255


2025-07-31 21:17:15:736 ==>> [W][05:18:45][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:15:917 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:16:334 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57088]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:17:16:950 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:17:352 ==>> [D][05:18:47][COMM]read battery soc:255
[D][05:18:47][COMM]f:[drv_audio_ack_receive].wait ack timeout!![58116]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:17:17:842 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:47][CAT1]SEND RAW data timeout
[D][05:18:47][CAT1]exec over: func id: 12, ret: -52
[W][05:18:47][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:47][CAT1]gsm read msg sub id: 12
[D][05:18:47][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:17:17:992 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:18:362 ==>> [D][05:18:48][COMM]f:[drv_audio_ack_receive].wait ack timeout!![59145]
[D][05:18:48][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:48][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:17:19:038 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:19:288 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 21:17:19:860 ==>> [D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:20:072 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:20:851 ==>> [D][05:18:50][CAT1]SEND RAW data timeout
[D][05:18:50][CAT1]exec over: func id: 12, ret: -52
[W][05:18:50][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:50][CAT1]gsm read msg sub id: 10
[D][05:18:50][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:17:21:108 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:21:293 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:17:22:149 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:22:350 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:22:875 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:23:196 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:23:290 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:17:24:247 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:24:856 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:24:916 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:25:272 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:25:318 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:17:26:284 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:26:974 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:27:315 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:27:361 ==>> [D][05:18:57][COMM]read battery soc:255
[D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:28:370 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:28:852 ==>> [D][05:18:58][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:17:29:017 ==>> [W][05:18:58][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:29:309 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 21:17:29:414 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:29:850 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:30:455 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:30:733 ==>> [D][05:19:00][COMM]f:[drv_audio_ack_receive].wait ack timeout!![71489]
[D][05:19:00][COMM]accel parse set 0
[D][05:19:00][COMM][Audio].l:[1032].open hexlog save
[D][05:19:00][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:17:31:070 ==>> [W][05:19:00][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:31:314 ==>> [D][05:19:01][COMM]read battery soc:255


2025-07-31 21:17:31:496 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:32:357 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:32:524 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:33:119 ==>> [W][05:19:02][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:33:334 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 21:17:33:548 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:34:576 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:34:857 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:35:167 ==>> [W][05:19:04][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:35:334 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 21:17:35:610 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:36:668 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:36:854 ==>> [D][05:19:06][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:17:37:210 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:37:360 ==>> [D][05:19:07][COMM]read battery soc:255
[D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:37:715 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:38:745 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:39:274 ==>> [D][05:19:09][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:17:39:349 ==>> [D][05:19:09][COMM]read battery soc:255


2025-07-31 21:17:39:798 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:39:860 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:40:851 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:41:350 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:11][COMM]read battery soc:255


2025-07-31 21:17:41:908 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:17:42:347 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:17:42:949 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:17:42:959 ==>> #################### 【测试结束】 ####################
2025-07-31 21:17:43:181 ==>> 关闭5V供电
2025-07-31 21:17:43:190 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:17:43:263 ==>> 5A A5 04 5A A5 


2025-07-31 21:17:43:368 ==>> [D][05:19:13][COMM]read battery soc:255
[W][05:19:13][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:17:44:190 ==>> 关闭5V供电成功
2025-07-31 21:17:44:199 ==>> 关闭33V供电
2025-07-31 21:17:44:211 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:17:44:267 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:17:44:463 ==>> [D][05:19:14][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:14][FCTY]get_ext_48v_vol retry i = 8,volt = 12
[D][05:19:14][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 21:17:45:129 ==>> [D][05:19:14][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:19:14][CAT1]exec over: func id: 10, ret: -43
[D][05:19:14][CAT1]sub id: 10, ret: -43

[D][05:19:14][SAL ]Cellular task submsg id[68]
[D][05:19:14][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
[D][05:19:14][M2M ]m2m gsm shut done, ret[1]
[D][05:19:14][CAT1]gsm read msg sub id: 12
[D][05:19:14][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
[D][05:19:14][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE
[D][05:19:14][M2M ]g_m2m_is_idle become true
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:14][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
[D][05:19:14][PROT]index:1 1629955154
[D][05:19:14][PROT]is_send:0
[D][05:19:14][PROT]sequence_num:5
[D][05:19:14][PROT]retry_timeout:0
[D][05:19:14][PROT]retry_times:2
[D][05:19:14][PROT]send_path:0x2
[D][05:19:14][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:14][PROT]===========================================================
[W][05:19:14][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955154]
[D][05:19:14][PROT]===========================

2025-07-31 21:17:45:204 ==>> 关闭33V供电成功
2025-07-31 21:17:45:214 ==>> 关闭3.7V供电
2025-07-31 21:17:45:226 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:17:45:240 ==>> ================================
[D][05:19:14][PROT]sending traceid [9999999999900006]
[D][05:19:14][PROT]Send_TO_M2M [1629955154]
[D][05:19:14][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:14][SAL ]open socket ind id[4], rst[0]
[D][05:19:14][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:14][SAL ]Cellular task submsg id[8]
[D][05:19:14][SAL ]cellular OPEN socket size[144], msg->data[0x20053040], socket[0]
[D][05:19:14][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:14][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:14][COMM]Main Task receive event:93
[D][05:19:14][COMM]main task tmp_sleep_event = 80
[W][05:19:14][PROT]remove success[1629955154],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:19:14][PROT]add success [1629955154],send_path[2],type[8301],priority[0],index[3],used[1]
[D][05:19:14][COMM]Main Task receive event:93 finished processing


2025-07-31 21:17:45:280 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:17:45:850 ==>>  

2025-07-31 21:17:46:219 ==>> 关闭3.7V供电成功
