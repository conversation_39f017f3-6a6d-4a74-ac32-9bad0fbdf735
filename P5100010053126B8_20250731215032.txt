2025-07-31 21:50:32:658 ==>> MES查站成功:
查站序号:P5100010053126B8验证通过
2025-07-31 21:50:32:663 ==>> 扫码结果:P5100010053126B8
2025-07-31 21:50:32:666 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:50:32:669 ==>> 测试参数版本:2024.10.11
2025-07-31 21:50:32:671 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:50:32:673 ==>> 检测【打开透传】
2025-07-31 21:50:32:677 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:50:32:788 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:50:33:041 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:50:33:044 ==>> 检测【检测接地电压】
2025-07-31 21:50:33:045 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:50:33:189 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:50:33:335 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:50:33:338 ==>> 检测【打开小电池】
2025-07-31 21:50:33:340 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:50:33:388 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:50:33:611 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:50:33:613 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:50:33:616 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:50:33:680 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:50:33:898 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:50:33:900 ==>> 检测【等待设备启动】
2025-07-31 21:50:33:902 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:50:34:155 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:50:34:353 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:50:34:936 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:50:35:059 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 21:50:35:446 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:50:35:917 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:50:35:986 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:50:35:988 ==>> 检测【产品通信】
2025-07-31 21:50:35:989 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:50:36:145 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:50:36:274 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:50:36:276 ==>> 检测【初始化完成检测】
2025-07-31 21:50:36:278 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:50:36:513 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 21:50:36:552 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:50:36:555 ==>> 检测【关闭大灯控制1】
2025-07-31 21:50:36:557 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:50:36:588 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:50:36:753 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:50:36:845 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:50:36:847 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:50:36:849 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:50:37:166 ==>> [D][05:17:51][COMM]2646 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:50:37:387 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:50:37:390 ==>> 检测【关闭仪表供电】
2025-07-31 21:50:37:392 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:50:37:578 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:50:37:659 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:50:37:684 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:50:37:686 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:50:37:863 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:50:37:972 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:50:37:974 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:50:37:976 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:50:38:014 ==>> [D][05:17:52][COMM]3657 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:38:164 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:50:38:270 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:50:38:273 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:50:38:274 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:50:38:470 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:50:38:578 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:50:38:581 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:50:38:583 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:50:38:684 ==>> 5A A5 01 5A A5 


2025-07-31 21:50:38:789 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:50:38:875 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:50:38:877 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:50:38:880 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:50:38:894 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255


2025-07-31 21:50:38:984 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 21:50:39:029 ==>> [D][05:17:53][COMM]4669 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:39:157 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:50:39:160 ==>> 该项需要延时执行
2025-07-31 21:50:39:552 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5015. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5016. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5016. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5016. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5017. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5017. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5018. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5018. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5019. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5019. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5019. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5020. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5020. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5020
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5021


2025-07-31 21:50:40:037 ==>> [D][05:17:54][COMM]5680 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:40:450 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:50:40:983 ==>>                           ipheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio

2025-07-31 21:50:41:088 ==>> ].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]

2025-07-31 21:50:41:193 ==>> min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN

2025-07-31 21:50:41:298 ==>>  get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
                                                                                                                                         

2025-07-31 21:50:42:067 ==>> [D][05:17:56][COMM]7704 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:42:907 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 21:50:43:073 ==>> [D][05:17:57][COMM]8715 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:43:163 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:50:43:167 ==>> 检测【33V输入电压ADC】
2025-07-31 21:50:43:170 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:50:43:503 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3144  volt:5526 mv
[D][05:17:57][COMM]adc read out 24v adc:1315  volt:33260 mv
[D][05:17:57][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:57][COMM]adc read right brake adc:1  volt:1 mv
[D][05:17:57][COMM]adc read throttle adc:1  volt:1 mv
[D][05:17:57][COMM]adc read battery ts volt:6 mv
[D][05:17:57][COMM]adc read in 24v adc:1303  volt:32956 mv
[D][05:17:57][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2409  volt:3881 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:11  volt:255 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:50:43:733 ==>> 【33V输入电压ADC】通过,【32956mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:50:43:736 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:50:43:739 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:50:43:789 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2767mV
Get AD_V6 1992mV
Get AD_V7 1083mV
OVER 150


2025-07-31 21:50:44:022 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:024 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:50:44:064 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:067 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:50:44:070 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 21:50:44:095 ==>> [D][05:17:58][COMM]9726 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:50:44:099 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:50:44:102 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:50:44:137 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:50:44:140 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:50:44:190 ==>> 【TP1_VCC12V(ADV7)】通过,【1083mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:50:44:193 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:50:44:293 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1988mV
Get AD_V7 1083mV
OVER 150


2025-07-31 21:50:44:398 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10006. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10007
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10007


2025-07-31 21:50:44:476 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:479 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:50:44:503 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:506 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:50:44:509 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 21:50:44:530 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:50:44:533 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:50:44:572 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:50:44:575 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:50:44:604 ==>> 【TP1_VCC12V(ADV7)】通过,【1083mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:50:44:615 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:50:44:689 ==>> 1A A1 00 00 FC 
Get AD_V2 1652mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2766mV
Get AD_V6 1992mV
Get AD_V7 1083mV
OVER 150


2025-07-31 21:50:44:898 ==>> 【TP7_VCC3V3(ADV2)】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:901 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:50:44:915 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
[D][05:17:59][COMM]read battery soc:255


2025-07-31 21:50:44:922 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:44:925 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:50:44:927 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 21:50:44:946 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:50:44:949 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:50:44:976 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:50:44:979 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:50:45:021 ==>> 【TP1_VCC12V(ADV7)】通过,【1083mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:50:45:024 ==>> 检测【打开WIFI(1)】
2025-07-31 21:50:45:026 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:50:45:293 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10738 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][0

2025-07-31 21:50:45:323 ==>> 5:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 21:50:45:561 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:50:45:564 ==>> 检测【清空消息队列(1)】
2025-07-31 21:50:45:569 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:50:45:767 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087843381

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541240

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 21:50:45:872 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:50:46:086 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 21:50:46:093 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:50:46:102 ==>> 检测【打开GPS(1)】
2025-07-31 21:50:46:105 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:50:46:283 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 21:50:46:429 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:50:46:431 ==>> 检测【打开GSM联网】
2025-07-31 21:50:46:434 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:50:46:573 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 21:50:46:723 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 21:50:46:743 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:50:46:746 ==>> 检测【打开仪表供电1】
2025-07-31 21:50:46:750 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:50:46:983 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:50:47:048 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:50:47:050 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:50:47:052 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:50:47:289 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:50:47:356 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:50:47:359 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:50:47:363 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:50:47:579 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33456]


2025-07-31 21:50:47:657 ==>> 【读取主控ADC采集的仪表电压】通过,【33456mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:50:47:661 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:50:47:665 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:47:880 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:50:47:954 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:50:47:957 ==>> 检测【AD_V20电压】
2025-07-31 21:50:47:987 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:48:060 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:48:090 ==>> [D][05:18:02][COMM]13750 imu init OK


2025-07-31 21:50:48:150 ==>> 本次取值间隔时间:82ms
2025-07-31 21:50:48:255 ==>> 本次取值间隔时间:90ms
2025-07-31 21:50:48:315 ==>> [D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:50:48:511 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 21:50:48:739 ==>> 本次取值间隔时间:475ms
2025-07-31 21:50:49:103 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][COMM]read battery soc:255
[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:50:49:163 ==>> 本次取值间隔时间:409ms
2025-07-31 21:50:49:167 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:49:268 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:49:391 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:49:496 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 

2025-07-31 21:50:49:600 ==>>                                                                                                                                    NSS]GPS start. ret=0
[D][05:18:03][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.129.129.191"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = ?<<<<<
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL 

2025-07-31 21:50:49:645 ==>> ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:50:49:660 ==>> 本次取值间隔时间:381ms
2025-07-31 21:50:49:685 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:49:795 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:49:871 ==>> 本次取值间隔时间:74ms
2025-07-31 21:50:49:886 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:50:157 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:50:50:276 ==>> 本次取值间隔时间:391ms
2025-07-31 21:50:50:298 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:50:400 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:50:492 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:50:778 ==>> 本次取值间隔时间:363ms
2025-07-31 21:50:50:810 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:50:823 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:50:50:913 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:51:065 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,05,33,,,42,40,,,39,41,,,39,39,,,38,1*76

$GBGSV,2,2,05,34,,,35,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

$GBGST,,0.000,1600.255,1600.255,51.157,2097152,2097152,2097152*48

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][COMM]read battery soc:255
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:51:171 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 21:50:51:291 ==>> 本次取值间隔时间:365ms
2025-07-31 21:50:51:311 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:51:416 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:51:491 ==>> 本次取值间隔时间:74ms
2025-07-31 21:50:51:495 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:51:524 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:51:627 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:51:686 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:51:807 ==>> 本次取值间隔时间:179ms
2025-07-31 21:50:51:828 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:51:930 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:52:020 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,11,33,,,40,59,,,40,60,,,40,41,,,39,1*75

$GBGSV,3,2,11,3,,,39,40,,,38,39,,,38,44,,,37,1*44

$GBGSV,3,3,11,34,,,35,9,,,34,10,,,33,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1556.546,1556.546,49.770,2097152,2097152,2097152*42

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:52:249 ==>> 本次取值间隔时间:311ms
2025-07-31 21:50:52:271 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:52:373 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:52:496 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 21:50:52:694 ==>> 本次取值间隔时间:315ms
2025-07-31 21:50:52:718 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:50:52:830 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:50:52:890 ==>> 1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 21:50:52:995 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:07][COMM]oneline display ALL on 1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,15,33,,,40,59,,,39,60,,,39,41,,,39,1*76

$GBGSV,4,2,15,3,,,39,40,,,38,39,,,38,44,,,36,1*46

$GBGSV,4,3,15,34,,,35,1,,,35,9,,,34,10,,,33,1*7C

$GBGSV,4,4,15,4,,,33,2,,,32,5,,,28,1*4A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1486.996,1486.996,47.596,2097152,2097152,2097152*46

[D][05:18:07][COMM]read battery soc:255


2025-07-31 21:50:53:041 ==>> 本次取值间隔时间:202ms
2025-07-31 21:50:53:065 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:50:53:068 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:50:53:070 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:50:53:194 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 21:50:53:360 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:50:53:364 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:50:53:369 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:50:53:609 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:50:54:003 ==>> $GBGGA,135057.822,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,40,59,,,39,60,,,39,41,,,39,1*7A

$GBGSV,5,2,18,3,,,39,40,,,38,39,,,38,25,,,38,1*43

$GBGSV,5,3,18,24,,,37,44,,,36,34,,,35,1,,,35,1*48

$GBGSV,5,4,18,16,,,35,9,,,34,10,,,34,2,,,32,1*74

$GBGSV,5,5,18,4,,,31,5,,,29,1*77

$GBRMC,135057.822,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135057.822,0.000,1492.510,1492.510,47.759,2097152,2097152,2097152*54



2025-07-31 21:50:54:391 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:50:54:700 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,135058.522,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,40,59,,,39,60,,,39,3,,,39,1*4D

$GBGSV,5,2,19,25,,,39,41,,,38,40,,,38,39,,,38,1*74

$GBGSV,5,3,19,24,,,37,44,,,35,34,,,35,1,,,35,1*4A

$GBGSV,5,4,19,16,,,35,9,,,34,10,,,34,2,,,32,1*75

$GBGSV,5,5,19,4,,,31,5,,,29,7,,,36,1*44

$GBRMC,135058.522,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135058.522,0.000,1490.207,1490.207,47.686,2097152,2097152,2097152*55



2025-07-31 21:50:54:942 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 21:50:55:436 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:50:55:714 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:10][COMM]oneline display read state:1
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,135059.502,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,40,59,,,39,60,,,39,3,,,39,1*47

$GBGSV,6,2,23,25,,,39,41,,,39,40,,,38,39,,,38,1*7F

$GBGSV,6,3,23,24,,,38,7,,,36,16,,,36,44,,,35,1*49

$GBGSV,6,4,23,34,,,35,1,,,35,9,,,34,10,,,34,1*7B

$GBGSV,6,5,23,12,,,33,2,,,32,11,,,32,38,,,30,1*4D

$GBGSV,6,6,23,4,,,30,5,,,29,6,,,36,1*4D

$GBRMC,135059.502,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135059.502,0.000,1469.911,1469.911,47.050,2097152,2097152,2097152*5B



2025-07-31 21:50:56:487 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 21:50:56:491 ==>> #################### 【测试结束】 ####################
2025-07-31 21:50:56:516 ==>> 关闭5V供电
2025-07-31 21:50:56:521 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:50:56:581 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:50:56:686 ==>> $GBGGA,135100.502,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,40,59,,,39,60,,,39,3,,,39,1*47

$GBGSV,6,2,23,25,,,39,41,,,39,40,,,38,39,,,38,1*7F

$GBGSV,6,3,23,24,,,38,7,,,36,16,,,36,44,,,35,1*49

$GBGSV,6,4,23,34,,,35,1,,,35,9,,,34,6,,,33,1*4B

$GBGSV,6,5,23,10,,,33,12,,,33,11,,,33,2,,,32,1*45

$GBGSV,6,6,23,38,,,30,4,,,30,5,,,30,1*7E

$GBRMC,135100.502,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135100.502,0.000,1467.283,1467.283,46.960,2097152,2097152,2097152*5D



2025-07-31 21:50:56:972 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:50:57:531 ==>> 关闭5V供电成功
2025-07-31 21:50:57:536 ==>> 关闭33V供电
2025-07-31 21:50:57:561 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:50:57:591 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:50:57:801 ==>> $GBGGA,135101.502,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,40,59,,,39,60,,,39,3,,,39,1*40

$GBGSV,7,2,25,25,,,39,41,,,39,39,,,39,40,,,38,1*79

$GBGSV,7,3,25,24,,,38,7,,,36,16,,,36,14,,,36,1*48

$GBGSV,7,4,25,44,,,35,34,,,35,1,,,35,9,,,34,1*7C

$GBGSV,7,5,25,10,,,34,6,,,33,12,,,33,11,,,33,1*40

$GBGSV,7,6,25,2,,,32,13,,,32,38,,,30,4,,,30,1*7F

$GBGSV,7,7,25,5,,,30,1*47

$GBRMC,135101.502,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135101.502,0.000,731.630,731.630,669.096,2097152,2097152,2097152*67

[D][05:18:12][FCTY]get_ext_48v_vol retry i = 0,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 1,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 2,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 3,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 4,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 5,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 6,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 7,volt = 17
[D][05:18:12][FCTY]get_ext_48v_vol retry i = 8,volt = 17


2025-07-31 21:50:57:967 ==>> [D][05:18:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 21:50:58:540 ==>> 关闭33V供电成功
2025-07-31 21:50:58:544 ==>> 关闭3.7V供电
2025-07-31 21:50:58:549 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:50:58:724 ==>> $GBGGA,135102.502,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,39,60,,,39,59,,,39,1*43

$GBGSV,7,2,27,39,,,39,25,,,39,41,,,39,40,,,38,1*7B

$GBGSV,7,3,27,24,,,38,7,,,36,16,,,36,34,,,36,1*48

$GBGSV,7,4,27,1,,,35,44,,,35,14,,,35,10,,,34,1*44

$GBGSV,7,5,27,6,,,34,9,,,34,12,,,34,11,,,34,1*7D

$GBGSV,7,6,27,2,,,33,13,,,32,38,,,31,5,,,30,1*7C

$GBGSV,7,7,27,4,,,30,8,,,38,23,,,,1*76

$GBRMC,135102.502,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,135102.502,0.000,736.590,736.590,673.631,2097152,2097152,2097152*64

6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:50:59:345 ==>>  

