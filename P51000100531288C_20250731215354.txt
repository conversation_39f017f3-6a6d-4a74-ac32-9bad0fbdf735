2025-07-31 21:53:54:809 ==>> MES查站成功:
查站序号:P51000100531288C验证通过
2025-07-31 21:53:54:816 ==>> 扫码结果:P51000100531288C
2025-07-31 21:53:54:818 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:53:54:819 ==>> 测试参数版本:2024.10.11
2025-07-31 21:53:54:821 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:53:54:824 ==>> 检测【打开透传】
2025-07-31 21:53:54:825 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:53:54:881 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:53:55:191 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:53:55:207 ==>> 检测【检测接地电压】
2025-07-31 21:53:55:209 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:53:55:290 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:53:55:507 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:53:55:509 ==>> 检测【打开小电池】
2025-07-31 21:53:55:512 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:53:55:590 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:53:55:794 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:53:55:796 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:53:55:798 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:53:55:881 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:53:56:072 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:53:56:076 ==>> 检测【等待设备启动】
2025-07-31 21:53:56:079 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:53:57:108 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:53:58:142 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:53:59:186 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:00:206 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:01:225 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:02:257 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:03:298 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:04:323 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:05:358 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:54:06:397 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 21:54:06:399 ==>> #################### 【测试结束】 ####################
2025-07-31 21:54:06:426 ==>> 关闭5V供电
2025-07-31 21:54:06:429 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:54:06:488 ==>> 5A A5 04 5A A5 


2025-07-31 21:54:06:593 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:54:07:438 ==>> 关闭5V供电成功
2025-07-31 21:54:07:441 ==>> 关闭33V供电
2025-07-31 21:54:07:444 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:54:07:483 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:54:08:443 ==>> 关闭33V供电成功
2025-07-31 21:54:08:446 ==>> 关闭3.7V供电
2025-07-31 21:54:08:448 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:54:08:582 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 21:54:08:921 ==>>  

