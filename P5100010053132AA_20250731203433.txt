2025-07-31 20:34:33:788 ==>> MES查站成功:
查站序号:P5100010053132AA验证通过
2025-07-31 20:34:33:792 ==>> 扫码结果:P5100010053132AA
2025-07-31 20:34:33:794 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:34:33:796 ==>> 测试参数版本:2024.10.11
2025-07-31 20:34:33:798 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:34:33:799 ==>> 检测【打开透传】
2025-07-31 20:34:33:801 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:34:33:849 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:34:34:133 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:34:34:140 ==>> 检测【检测接地电压】
2025-07-31 20:34:34:142 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:34:34:258 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:34:34:411 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:34:34:414 ==>> 检测【打开小电池】
2025-07-31 20:34:34:418 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:34:34:558 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:34:34:708 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:34:34:710 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:34:34:712 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:34:34:846 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:34:34:991 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:34:34:994 ==>> 检测【等待设备启动】
2025-07-31 20:34:34:996 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:34:35:341 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:34:35:536 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:34:36:016 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:34:36:184 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:34:36:259 ==>> [W][05:17:49][GNSS]start sing locating


2025-07-31 20:34:36:658 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:34:37:051 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:34:37:126 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:34:37:331 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:34:37:333 ==>> 检测【产品通信】
2025-07-31 20:34:37:334 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:34:37:536 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:34:37:633 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:34:37:635 ==>> 检测【初始化完成检测】
2025-07-31 20:34:37:638 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:34:37:749 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:34:37:854 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1-----

2025-07-31 20:34:37:884 ==>> -------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:34:38:159 ==>> [D][05:17:51][COMM]2626 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:38:193 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:34:38:195 ==>> 检测【关闭大灯控制1】
2025-07-31 20:34:38:198 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:34:38:369 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:34:38:491 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:34:38:506 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:34:38:510 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:34:38:654 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:34:38:788 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:34:38:791 ==>> 检测【关闭仪表供电】
2025-07-31 20:34:38:793 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:34:38:944 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:34:39:171 ==>> [D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:39:447 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:34:39:450 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:34:39:451 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:34:39:617 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:34:39:749 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:34:39:751 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:34:39:754 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:34:39:908 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:34:40:035 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:34:40:038 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:34:40:040 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:34:40:226 ==>> [D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][CAT1]power_urc_cb ret[5]
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:34:40:316 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:34:40:320 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:34:40:322 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:34:40:451 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:34:40:589 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:34:40:593 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:34:40:595 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:34:40:706 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5008. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN me

2025-07-31 20:34:40:766 ==>> ssage fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5010
[D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:54][COMM]read battery soc:255
5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:34:40:859 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:34:40:862 ==>> 该项需要延时执行
2025-07-31 20:34:41:194 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:42:138 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:34:42:638 ==>>                                                                                       - get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]6674 imu init OK
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_p

2025-07-31 20:34:42:743 ==>> lay_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:

2025-07-31 20:34:42:848 ==>> 61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get

2025-07-31 20:34:42:923 ==>>  state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:56][COMM]read battery soc:255


2025-07-31 20:34:43:225 ==>> [D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]7690 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:44:231 ==>> [D][05:17:57][COMM]8702 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:44:552 ==>> [D][05:17:58][COMM]read battery soc:255


2025-07-31 20:34:44:869 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:34:44:872 ==>> 检测【33V输入电压ADC】
2025-07-31 20:34:44:875 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:34:45:164 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:17:58][COMM]adc read out 24v adc:1306  volt:33032 mv
[D][05:17:58][COMM]adc read left brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read right brake adc:0  volt:0 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:0 mv
[D][05:17:58][COMM]adc read in 24v adc:1286  volt:32526 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:34:45:254 ==>> [D][05:17:58][COMM]9712 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:34:45:411 ==>> 【33V输入电压ADC】通过,【32526mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:34:45:414 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:34:45:420 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:34:45:601 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2765mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150
[D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10019
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10019


2025-07-31 20:34:45:694 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:45:697 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:34:45:716 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:45:719 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:34:45:723 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:34:45:735 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:34:45:739 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:34:45:754 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:34:45:757 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:34:45:777 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:34:45:779 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:34:45:856 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1656mV
Get AD_V4 1mV
Get AD_V5 2767mV
Get AD_V6 1991mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:34:46:062 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:46:064 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:34:46:080 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:46:082 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:34:46:084 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 20:34:46:099 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:34:46:101 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:34:46:128 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:34:46:149 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:34:46:189 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:34:46:192 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:34:46:270 ==>> 1A A1 00 00 FC 
Get AD_V2 1663mV
Get AD_V3 1656mV
Get AD_V4 0mV
Get AD_V5 2766mV
Get AD_V6 1992mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:34:46:375 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10723 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETV

2025-07-31 20:34:46:474 ==>> 【TP7_VCC3V3(ADV2)】通过,【1663mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:46:476 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:34:46:480 ==>> ERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK


2025-07-31 20:34:46:493 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1656mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:46:496 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:34:46:498 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:34:46:512 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:34:46:528 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:34:46:532 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:34:46:535 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:34:46:557 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:34:46:560 ==>> 检测【打开WIFI(1)】
2025-07-31 20:34:46:562 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:34:46:768 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][COMM]read battery soc:255
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087737880

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539140

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:34:46:832 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:34:46:835 ==>> 检测【清空消息队列(1)】
2025-07-31 20:34:46:838 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:34:46:873 ==>>                                                                                                                                                                    [D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,

2025-07-31 20:34:46:903 ==>> 3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:34:47:009 ==>> [D][05:18:00][HSDK][0] fl

2025-07-31 20:34:47:038 ==>> ush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:34:47:101 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:34:47:104 ==>> 检测【打开GPS(1)】
2025-07-31 20:34:47:106 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:34:47:263 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:34:47:371 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:34:47:374 ==>> 检测【打开GSM联网】
2025-07-31 20:34:47:377 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:34:47:752 ==>>                                                                                  [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 5, ret: 6
[D][05:18:00][CAT1]sub id: 5, ret: 6

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:00][M2M ]m2m gsm comm init done, ret[0]
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cel

2025-07-31 20:34:47:857 ==>> lular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 29,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:34:47:905 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:34:47:911 ==>> 检测【打开仪表供电1】
2025-07-31 20:34:47:916 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:34:47:947 ==>>                                                                                                                                                                                                                                                                                                                                                                                  


2025-07-31 20:34:48:143 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:34:48:176 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:34:48:179 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:34:48:182 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:34:48:494 ==>> [D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:34:48:570 ==>> [D][05:18:02][COMM]read battery soc:255


2025-07-31 20:34:48:707 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:34:48:710 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:34:48:712 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:34:48:937 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33595]


2025-07-31 20:34:49:039 ==>> 【读取主控ADC采集的仪表电压】通过,【33595mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:34:49:044 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:34:49:060 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:49:291 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13735 imu init OK


2025-07-31 20:34:49:573 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:34:49:577 ==>> 检测【AD_V20电压】
2025-07-31 20:34:49:579 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:49:679 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:49:754 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:34:49:859 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:34:50:055 ==>> [D][05:18:03][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,41,25,,,40,24,,,39,39,,,39,1*78

$GBGSV,2,2,07,42,,,38,59,,,41,60,,,40,1*77

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1633.359,1633.359,52.154,2097152,2097152,2097152*48

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 20:34:50:160 ==>> 本次取值间隔时间:468ms
2025-07-31 20:34:50:179 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:50:254 ==>> [D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:34:50:284 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:50:359 ==>> [D][05:18:03][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:34:50:389 ==>> 本次取值间隔时间:92ms
2025-07-31 20:34:50:418 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:50:525 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:50:650 ==>> [D][05:18:04][COMM]read battery soc:255
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
00 00 00 00 00 
head err!


2025-07-31 20:34:50:987 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,42,3,,,41,59,,,40,25,,,40,1*4D

$GBGSV,4,2,14,24,,,40,14,,,40,60,,,39,39,,,39,1*7A

$GBGSV,4,3,14,42,,,38,2,,,36,13,,,35,1,,,35,1*7D

$GBGSV,4,4,14,5,,,31,41,,,36,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1581.795,1581.795,50.603,2097152,2097152,2097152*4F



2025-07-31 20:34:51:032 ==>> 本次取值间隔时间:499ms
2025-07-31 20:34:51:292 ==>> 本次取值间隔时间:249ms
2025-07-31 20:34:51:477 ==>> 本次取值间隔时间:173ms
2025-07-31 20:34:51:946 ==>> 本次取值间隔时间:465ms
2025-07-31 20:34:51:951 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:52:006 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,25,,,41,3,,,40,59,,,40,1*4F

$GBGSV,4,2,16,24,,,40,14,,,40,60,,,40,39,,,39,1*76

$GBGSV,4,3,16,42,,,38,40,,,37,41,,,36,13,,,36,1*7C

$GBGSV,4,4,16,2,,,35,1,,,35,44,,,34,5,,,31,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1565.052,1565.052,50.067,2097152,2097152,2097152*4B



2025-07-31 20:34:52:051 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:52:111 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELIN

2025-07-31 20:34:52:156 ==>> E_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:34:52:276 ==>> 本次取值间隔时间:216ms
2025-07-31 20:34:52:295 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:52:401 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:52:446 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:34:52:551 ==>> [D][05:18:06][COMM]read battery soc:255


2025-07-31 20:34:52:555 ==>> 本次取值间隔时间:148ms
2025-07-31 20:34:52:570 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:52:672 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:52:748 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:34:53:007 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,42,25,,,41,3,,,40,24,,,40,1*4B

$GBGSV,5,2,19,14,,,40,60,,,40,59,,,39,39,,,39,1*7C

$GBGSV,5,3,19,42,,,38,40,,,37,41,,,36,13,,,36,1*72

$GBGSV,5,4,19,1,,,36,16,,,35,2,,,34,44,,,34,1*78

$GBGSV,5,5,19,5,,,31,23,,,30,34,,,29,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1520.907,1520.907,48.696,2097152,2097152,2097152*4A



2025-07-31 20:34:53:083 ==>> 本次取值间隔时间:408ms
2025-07-31 20:34:53:107 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:53:221 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:53:357 ==>> 本次取值间隔时间:132ms
2025-07-31 20:34:53:448 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:34:53:523 ==>> 本次取值间隔时间:154ms
2025-07-31 20:34:54:006 ==>> 本次取值间隔时间:479ms
2025-07-31 20:34:54:036 ==>> $GBGGA,123457.833,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,25,,,41,24,,,41,14,,,41,1*77

$GBGSV,6,2,23,3,,,40,60,,,40,59,,,39,39,,,39,1*40

$GBGSV,6,3,23,42,,,39,40,,,37,41,,,36,13,,,36,1*79

$GBGSV,6,4,23,1,,,36,16,,,36,7,,,35,6,,,35,1*42

$GBGSV,6,5,23,2,,,34,44,,,34,9,,,33,8,,,32,1*46

$GBGSV,6,6,23,5,,,31,23,,,30,34,,,29,1*4E

$GBRMC,123457.833,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123457.833,0.000,1506.962,1506.962,48.251,2097152,2097152,2097152*55



2025-07-31 20:34:54:454 ==>> 本次取值间隔时间:437ms
2025-07-31 20:34:54:458 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:34:54:563 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:34:54:657 ==>> 1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150


2025-07-31 20:34:54:748 ==>> [D][05:18:08][COMM]read battery soc:255
[W][05:18:08][COMM]>>>>>Input command = ?<<<<<
$GBGGA,123458.533,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,25,,,41,24,,,41,14,,,41,1*73

$GBGSV,7,2,26,3,,,40,60,,,40,59,,,40,39,,,39,1*4A

$GBGSV,7,3,26,42,,,38,40,,,37,41,,,36,13,,,36,1*7C

$GBGSV,7,4,26,1,,,36,16,,,36,38,,,36,7,,,35,1*78

$GBGSV,7,5,26,6,,,35,2,,,34,44,,,34,9,,,34,1*4C

$GBGSV,7,6,26,8,,,33,10,,,33,5,,,31,4,,,30,1*4A

$GBGSV,7,7,26,23,,,30,34,,,30,1*74

$GBRMC,123458.533,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123458.533,0.000,1495.727,1495.727,47.889,2097152,2097152,2097152*57



2025-07-31 20:34:55:005 ==>> 本次取值间隔时间:433ms
2025-07-31 20:34:55:057 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:55:060 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:34:55:062 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:34:55:158 ==>> 3A A3 02 00 A3 


2025-07-31 20:34:55:248 ==>> OFF_OUT2
OVER 150


2025-07-31 20:34:55:350 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:34:55:354 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:34:55:356 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:34:55:599 ==>> [D][05:18:08][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:09][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:34:55:633 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:34:55:636 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:34:55:638 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:34:55:704 ==>>                                                                                                                                                                                                                                             
$GBGSV,7,4,26,1,,,36,16,,,36,38,,,36,7,,,35,1*78

$GBGSV,7,5,26,6,,,35,2,,,34,44,,,34,9,,,34,1*4C

$GBGSV,7,6,26,8,,,33,10,,,33,5,,,31,4,,,30,1*4A

$GBGSV,7,7,26,23,,,30,34,,,30,1*74

$GBRMC,12

2025-07-31 20:34:55:749 ==>> 3459.513,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123459.513,0.000,1497.323,1497.323,47.941,2097152,2097152,2097152*51

3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 20:34:55:907 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:34:55:910 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:34:55:913 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:34:56:150 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:34:56:192 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:34:56:195 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:34:56:198 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:34:56:346 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:09][COMM]oneline display set 1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:34:56:470 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:34:56:474 ==>> 检测【AD_V21电压】
2025-07-31 20:34:56:477 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:34:56:556 ==>> 1A A1 20 00 00 
Get AD_V21 1043mV
OVER 150


2025-07-31 20:34:56:586 ==>> [D][05:18:10][COMM]read battery soc:255


2025-07-31 20:34:56:691 ==>>                                  ,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,24,,,41,14,,,41,1*72

$GBGSV,7,2,27,3,,,40,60,,,40,59,,,40,39,,,39,1*4B

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,41,,,36,1*79

$GBGSV,7,4,27,13,,,36,1,,,36,38,,,36,7,,,35

2025-07-31 20:34:56:736 ==>> ,1*7C

$GBGSV,7,5,27,6,,,35,9,,,35,2,,,34,44,,,34,1*4C

$GBGSV,7,6,27,8,,,34,10,,,33,26,,,33,5,,,31,1*7F

$GBGSV,7,7,27,4,,,31,23,,,30,34,,,30,1*43

$GBRMC,123500.513,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123500.513,0.000,1497.139,1497.139,47.928,2097152,2097152,2097152*53



2025-07-31 20:34:56:856 ==>> 本次取值间隔时间:384ms
2025-07-31 20:34:56:885 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:34:56:946 ==>> 1A A1 20 00 00 
Get AD_V21 1640mV
OVER 150


2025-07-31 20:34:56:976 ==>> 本次取值间隔时间:82ms
2025-07-31 20:34:56:995 ==>> 【AD_V21电压】通过,【1640mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:34:57:000 ==>> 检测【关闭仪表供电2】
2025-07-31 20:34:57:004 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:34:57:141 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:10][COMM]set POWER 0
[D][05:18:10][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:34:57:265 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:34:57:270 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:34:57:274 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:34:57:444 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:10][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:34:57:537 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:34:57:541 ==>> 检测【打开AccKey2供电】
2025-07-31 20:34:57:544 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:34:57:763 ==>> $GBGGA,123501.513,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,14,,,41,24,,,40,1*73

$GBGSV,7,2,27,3,,,40,59,,,40,39,,,40,60,,,39,1*4B

$GBGSV,7,3,27,42,,,39,40,,,37,16,,,37,41,,,36,1*78

$GBGSV,7,4,27,13,,,36,1,,,36,38,,,36,7,,,35,1*7C

$GBGSV,7,5,27,6,,,35,9,,,35,2,,,34,44,,,34,1*4C

$GBGSV,7,6,27,8,,,34,10,,,33,26,,,33,5,,,32,1*7C

$GBGSV,7,7,27,4,,,31,34,,,31,23,,,30,1*42

$GBRMC,123501.513,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123501.513,0.000,748.713,748.713,684.718,2097152,2097152,2097152*66

[W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:34:57:811 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:34:57:815 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:34:57:819 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:34:58:156 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:11][COMM]adc read out 24v adc:1305  volt:33007 mv
[D][05:18:11][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:11][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:11][COMM]adc read battery ts volt:3 mv
[D][05:18:11][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:34:58:340 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33007mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:34:58:344 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:34:58:349 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:34:58:507 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:34:58:597 ==>> [D][05:18:12][COMM]read battery soc:255


2025-07-31 20:34:58:628 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:34:58:631 ==>> 该项需要延时执行
2025-07-31 20:34:58:702 ==>>                                        

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,60,,,40,3,,,40,59,,,40,39,,,40,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,1,,,37,16,,,37,1*4C

$GBGSV,7,4,27,13,,,36,38,,,36,6,,,36,41,,,36,1*4A

$GBGSV,7,5,27,7,,,35,9,,,35,2,,,34,8,,,34,1*75

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,5,,,32,1*44

$GBGSV,7,7,27

2025-07-31 20:34:58:747 ==>> ,34,,,31,4,,,31,23,,,30,1*42

$GBRMC,123502.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123502.513,0.000,751.014,751.014,686.823,2097152,2097152,2097152*60



2025-07-31 20:34:59:737 ==>> $GBGGA,123503.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,60,,,40,3,,,40,59,,,40,39,,,39,1*4B

$GBGSV,7,3,27,42,,,38,40,,,37,1,,,37,16,,,37,1*4C

$GBGSV,7,4,27,13,,,36,38,,,36,41,,,36,7,,,35,1*48

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,34,1*48

$GBGSV,7,6,27,8,,,34,44,,,34,10,,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123503.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123503.513,0.000,751.008,751.008,686.817,2097152,2097152,2097152*66



2025-07-31 20:35:00:593 ==>> [D][05:18:14][COMM]read battery soc:255


2025-07-31 20:35:00:698 ==>>         23504.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,60,,,40,3,,,40,59,,,40,39,,,39,1*4B

$GBGSV,7,3,27,42,,,39,40,,,37,16,,,37,13,,,36,1*7F

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,34,1*48

$GBGSV,7,6,27,8,,,34,44,,,34,10,

2025-07-31 20:35:00:743 ==>> ,,33,5,,,32,1*7F

$GBGSV,7,7,27,4,,,31,34,,,31,23,,,30,1*42

$GBRMC,123504.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123504.513,0.000,750.246,750.246,686.120,2097152,2097152,2097152*6C



2025-07-31 20:35:01:642 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:35:01:647 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:35:01:651 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:01:748 ==>> $GBGGA,123505.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,60,,,40,3,,,40,59,,,40,39,,,39,1*4B

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,26,,,34,1*48

$GBGSV,7,6,27,8,,,34,44,,,34,10,,,33,5,,,32,1*7F

$GBGSV,7,7,27,34,,,31,4,,,31,23,,,29,1*4A

$GBRMC,123505.513,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123505.513,0.000,748.716,748.716,684.721,2097152,2097152,2097152*68



2025-07-31 20:35:01:959 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3129  volt:5500 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:3 mv
[D][05:18:15][COMM]adc read in 24v adc:1290  volt:32627 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:35:02:176 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:35:02:179 ==>> 检测【打开AccKey1供电】
2025-07-31 20:35:02:183 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:35:02:345 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:15][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:35:02:454 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:35:02:457 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:35:02:462 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:35:02:559 ==>> 1A A1 00 40 00 
Get AD_V14 2652mV
OVER 150


2025-07-31 20:35:02:711 ==>> 原始值:【2652】, 乘以分压基数【2】还原值:【5304】
2025-07-31 20:35:02:730 ==>> 【读取AccKey1电压(ADV14)前】通过,【5304mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:35:02:735 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:35:02:741 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:02:771 ==>> [D][05:18:16][COMM]read battery soc:255
$GBGGA,123506.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,3,,,40,1*45

$GBGSV,7,2,27,59,,,40,14,,,40,60,,,39,39,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,5,,,31,1*47

$GBGSV,7,7,27,34,,,31,4,,,31,23,,,30,1*42

$GBRMC,123506.513,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123506.513,0.000,746.413,746.413,682.615,2097152,2097152,2097152*6B



2025-07-31 20:35:03:058 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:16][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:16][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:16][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:16][COMM]adc read battery ts volt:0 mv
[D][05:18:16][COMM]adc read in 24v adc:1281  volt:32400 mv
[D][05:18:16][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:16][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:35:03:258 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:35:03:262 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:35:03:267 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:35:03:413 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:35:03:537 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:35:03:540 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:35:03:545 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:35:03:656 ==>> 1A A1 00 40 00 
Get AD_V14 2654mV
OVER 150


2025-07-31 20:35:03:746 ==>> $GBGGA,123507.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,60,,,40,3,,,40,59,,,39,39,,,39,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,7,,,36,38,,,36,1,,,36,41,,,36,1*78

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,5,,,32,1*44

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123507.513,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123507.513,0.000,749.475,749.475,685.415,2097152,2097152,2097152*6F



2025-07-31 20:35:03:791 ==>> 原始值:【2654】, 乘以分压基数【2】还原值:【5308】
2025-07-31 20:35:03:810 ==>> 【读取AccKey1电压(ADV14)后】通过,【5308mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:35:03:814 ==>> 检测【打开WIFI(2)】
2025-07-31 20:35:03:818 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:35:04:068 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:17][CAT1]gsm read msg sub id: 12
[D][05:18:17][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:17][CAT1]<<< 
OK

[D][05:18:17][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:35:04:348 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:35:04:352 ==>> 检测【转刹把供电】
2025-07-31 20:35:04:355 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:35:04:517 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:35:04:626 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:35:04:630 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:35:04:632 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:35:04:730 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:35:04:745 ==>> [D][05:18:18][COMM]read battery soc:255
$GBGGA,123508.513,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,14,,,40,60,,,39,59,,,39,39,,,39,1*7D

$GBGSV,7,3,27,42,,,38,40,,,37,13,,,36,38,,,36,1*73

$GBGSV,7,4,27,1,,,36,16,,,36,41,,,36,7,,,35,1*77

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,4,,,32,1*45

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,123508.513,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123508.513,0.000,744.874,744.874,681.207,2097152,2097152,2097152*61



2025-07-31 20:35:04:821 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:35:04:850 ==>> 1A A1 00 80 00 
Get AD_V15 2407mV
OVER 150


2025-07-31 20:35:04:895 ==>> 原始值:【2407】, 乘以分压基数【2】还原值:【4814】
2025-07-31 20:35:04:914 ==>> 【读取AD_V15电压(前)】通过,【4814mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:35:04:923 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:35:04:943 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:35:04:948 ==>> +WIFISCAN:4,0,F42A7D1297A3,-63
+WIFISCAN:4,1,44A1917CA62B,-75
+WIFISCAN:4,2,CC057790A640,-76
+WIFISCAN:4,3,CC057790A641,-77

[D][05:18:18][CAT1]wifi scan report total[4]


2025-07-31 20:35:05:015 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:35:05:230 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:35:05:335 ==>> [D][05:18:18][GNSS]recv submsg id[3]


2025-07-31 20:35:05:734 ==>> $GBGGA,123509.513,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,24,,,40,14,,,40,59,,,39,39,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,13,,,36,38,,,36,1*73

$GBGSV,7,4,27,1,,,36,16,,,36,7,,,35,9,,,35,1*48

$GBGSV,7,5,27,6,,,35,41,,,35,2,,,34,8,,,34,1*48

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,4,,,32,1*45

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,123509.513,V,,,,,,,310725,0.1,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123509.513,0.000,744.876,744.876,681.209,2097152,2097152,2097152*6E



2025-07-31 20:35:05:931 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:35:06:036 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:35:06:111 ==>> [W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:35:06:156 ==>> 1A A1 01 00 00 
Get AD_V16 2436mV
OVER 150


2025-07-31 20:35:06:201 ==>> 原始值:【2436】, 乘以分压基数【2】还原值:【4872】
2025-07-31 20:35:06:267 ==>> 【读取AD_V16电压(前)】通过,【4872mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:35:06:271 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:35:06:277 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:06:569 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3136  volt:5512 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:19][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:19][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:19][COMM]adc read battery ts volt:5 mv
[D][05:18:19][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:19][COMM]adc read throttle brake in adc:3077  volt:5408 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:35:06:674 ==>> [D][05:18:20][COMM]read battery soc:255
$GBGGA,123510.513,,,,,0,00,,,M,,M,,*69



2025-07-31 20:35:06:749 ==>> $GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,25,,,41,3,,,40,1*45

$GBGSV,7,2,27,60,,,40,14,,,40,59,,,39,39,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,7,,,36,1*4B

$GBGSV,7,4,27,13,,,36,38,,,36,1,,,36,41,,,36,1*4D

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,10,,,33,26,,,33,4,,,32,1*45

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,123510.513,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123510.513,0.000,747.943,747.943,684.014,2097152,2097152,2097152*6D



2025-07-31 20:35:06:811 ==>> 【转刹把供电电压(主控ADC)】通过,【5408mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:35:06:819 ==>> 检测【转刹把供电电压】
2025-07-31 20:35:06:828 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:07:158 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3133  volt:5507 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:20][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:20][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:20][COMM]adc read battery ts volt:6 mv
[D][05:18:20][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:20][COMM]adc read throttle brake in adc:3072  volt:5400 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:35:07:362 ==>> 【转刹把供电电压】通过,【5400mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:35:07:369 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:35:07:374 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:35:07:535 ==>> [D][05:18:20][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:20][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:35:07:650 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:35:07:654 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:35:07:675 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:35:07:745 ==>> $GBGGA,123511.513,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,24,,,41,14,,,41,25,,,41,1*72

$GBGSV,7,2,27,3,,,40,60,,,40,59,,,39,39,,,39,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,10,,,33,26,,,33,5,,,32,1*44

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123511.513,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123511.513,0.000,748.709,748.709,684.715,2097152,2097152,2097152*6A



2025-07-31 20:35:07:760 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:35:07:850 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:35:07:884 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:35:07:890 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:35:07:897 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:35:07:986 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:35:08:016 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:35:08:046 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 20:35:08:110 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:35:08:118 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:35:08:125 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:35:08:256 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:35:08:380 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:35:08:384 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:35:08:390 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:35:08:455 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:35:08:654 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:35:08:659 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:35:08:667 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:35:08:756 ==>> [D][05:18:22][COMM]read battery soc:255
$GBGGA,123512.513,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,41,25,,,41,60,,,40,1*73

$GBGSV,7,2,27,3,,,40,24,,,40,59,,,39,39,,,39,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,7,,,36,1*4B

$GBGSV,7,4,27,13,,,36,38,,,36,1,,,36,41,,,36,1*4D

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,4,,,32,1*45

$GBGSV,7,7,27,5,,,31,34,,,31,23,,,30,1*43

$GBRMC,123512.513,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123512.513,0.000,747.943,747.943,684.014,2097152,2097152,2097152*6F

3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:35:08:926 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:35:08:930 ==>> 检测【左刹电压测试1】
2025-07-31 20:35:08:935 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:09:257 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3138  volt:5516 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:1716  volt:2262 mv
[D][05:18:22][COMM]adc read right brake adc:1723  volt:2271 mv
[D][05:18:22][COMM]adc read throttle adc:1716  volt:2262 mv
[D][05:18:22][COMM]adc read battery ts volt:0 mv
[D][05:18:22][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1450  volt:33618 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:35:09:458 ==>> 【左刹电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 20:35:09:463 ==>> 检测【右刹电压测试1】
2025-07-31 20:35:09:481 ==>> 【右刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 20:35:09:484 ==>> 检测【转把电压测试1】
2025-07-31 20:35:09:500 ==>> 【转把电压测试1】通过,【2262】符合目标值【2250】至【2500】要求!
2025-07-31 20:35:09:504 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:35:09:507 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:35:09:549 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:35:09:760 ==>> $GBGGA,123513.513,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,41,25,,,41,3,,,40,1*46

$GBGSV,7,2,27,60,,,40,59,,,40,24,,,40,39,,,39,1*7E

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,7,,,36,1*4B

$GBGSV,7,4,27,13,,,36,38,,,36,1,,,36,41,,,36,1*4D

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,44,,,34,10,,,33,26,,,33,5,,,32,1*44

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123513.513,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123513.513,0.000,749.475,749.475,685.414,2097152,2097152,2097152*6B



2025-07-31 20:35:09:792 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:35:09:823 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:35:09:827 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:35:09:850 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:35:10:070 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:35:10:074 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:35:10:078 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:35:10:154 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:35:10:359 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:35:10:365 ==>> 检测【左刹电压测试2】
2025-07-31 20:35:10:373 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:35:10:772 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:23][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:23][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:23][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:23][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:23][COMM]adc read battery ts volt:1 mv
[D][05:18:23][COMM]adc read in 24v adc:1283  volt:32450 mv
[D][05:18:23][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:24][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:24][COMM]arm_hub adc read vbat adc:2406  volt:3876 mv
[D][05:18:24][COMM]arm_hub adc read led yb adc:1449  volt:33595 mv
[D][05:18:24][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:24][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:24][COMM]read battery soc:255
$GBGGA,123514.513,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,41,25,,,41,60,,,40,1*73

$GBGSV,7,2,27,3,,,40,24,,,40,59,,,39,39,,,39,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,

2025-07-31 20:35:10:817 ==>> ,34,1*74

$GBGSV,7,6,27,44,,,34,26,,,33,10,,,33,5,,,32,1*44

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123514.513,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123514.513,0.000,747.940,747.941,684.012,2097152,2097152,2097152*6E



2025-07-31 20:35:10:899 ==>> 【左刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:35:10:905 ==>> 检测【右刹电压测试2】
2025-07-31 20:35:10:920 ==>> 【右刹电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:35:10:923 ==>> 检测【转把电压测试2】
2025-07-31 20:35:10:940 ==>> 【转把电压测试2】通过,【0】符合目标值【0】至【50】要求!
2025-07-31 20:35:10:943 ==>> 检测【晶振检测】
2025-07-31 20:35:10:949 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:35:11:134 ==>> [W][05:18:24][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:24][COMM][lf state:1][hf state:1]


2025-07-31 20:35:11:216 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:35:11:220 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:35:11:226 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:35:11:362 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1656mV
Get AD_V4 1653mV
Get AD_V5 2766mV
Get AD_V6 1988mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:35:11:487 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1653mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:35:11:492 ==>> 检测【检测BootVer】
2025-07-31 20:35:11:497 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:35:11:868 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:25][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
[D][05:18:25][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:25][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:25][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:25][FCTY]DeviceID    = 460130071539140
[D][05:18:25][FCTY]HardwareID  = 867222087737880
[D][05:18:25][FCTY]MoBikeID    = 9999999999
[D][05:18:25][FCTY]LockID      = FFFFFFFFFF
[D][05:18:25][FCTY]BLEFWVersion= 105
[D][05:18:25][FCTY]BLEMacAddr   = C61DA8746BAC
[D][05:18:25][FCTY]Bat         = 3924 mv
[D][05:18:25][FCTY]Current     = 0 ma
[D][05:18:25][FCTY]VBUS        = 11800 mv
[D][05:18:25][FCTY]TEMP= 0,BATID= 264,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:25][FCTY]Ext battery vol = 32, adc = 1286
[D][05:18:25][FCTY]Acckey1 vol = 5507 mv, Acckey2 vol = 0 mv
[D][05:18:25][FCTY]Bike Type flag is invalied
[D][05:18:25][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:25][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:25][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:1

2025-07-31 20:35:11:973 ==>> 8:25][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:25][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:25][FCTY]Bat1         = 3815 mv
[D][05:18:25][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:25][FCTY]==========Modules-nRF5340 ==========
$GBGGA,123515.513,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,14,,,41,25,,,41,60,,,40,1*73

$GBGSV,7,2,27,3,,,40,24,,,40,59,,,39,39,,,39,1*45

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,26,,,33,10,,,33,44,,,33,5,,,32,1*43

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,30,1*41

$GBRMC,123515.513,V,,,,,,,310725,0.1,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123515.513,0.000,747.176,747.176,683.312,2097152,2097152,2097152*6A



2025-07-31 20:35:12:028 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:35:12:033 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:35:12:037 ==>> 检测【检测固件版本】
2025-07-31 20:35:12:047 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:35:12:052 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:35:12:059 ==>> 检测【检测蓝牙版本】
2025-07-31 20:35:12:107 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:35:12:112 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:35:12:115 ==>> 检测【检测MoBikeId】
2025-07-31 20:35:12:205 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:35:12:215 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:35:12:232 ==>> 检测【检测蓝牙地址】
2025-07-31 20:35:12:238 ==>> 取到目标值:C61DA8746BAC
2025-07-31 20:35:12:244 ==>> 【检测蓝牙地址】通过,【C61DA8746BAC】符合目标值【】要求!
2025-07-31 20:35:12:249 ==>> 提取到蓝牙地址:C61DA8746BAC
2025-07-31 20:35:12:258 ==>> 检测【BOARD_ID】
2025-07-31 20:35:12:291 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:35:12:295 ==>> 检测【检测充电电压】
2025-07-31 20:35:12:311 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:35:12:320 ==>> 检测【检测VBUS电压1】
2025-07-31 20:35:12:337 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:35:12:343 ==>> 检测【检测充电电流】
2025-07-31 20:35:12:369 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:35:12:376 ==>> 检测【检测IMEI】
2025-07-31 20:35:12:383 ==>> 取到目标值:867222087737880
2025-07-31 20:35:12:410 ==>> 【检测IMEI】通过,【867222087737880】符合目标值【】要求!
2025-07-31 20:35:12:419 ==>> 提取到IMEI:867222087737880
2025-07-31 20:35:12:426 ==>> 检测【检测IMSI】
2025-07-31 20:35:12:445 ==>> 取到目标值:460130071539140
2025-07-31 20:35:12:448 ==>> 【检测IMSI】通过,【460130071539140】符合目标值【】要求!
2025-07-31 20:35:12:452 ==>> 提取到IMSI:460130071539140
2025-07-31 20:35:12:456 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:35:12:462 ==>> 取到目标值:460130071539140
2025-07-31 20:35:12:467 ==>> 【校验网络运营商(移动)】通过,【460130071539140】符合目标值【】要求!
2025-07-31 20:35:12:484 ==>> 检测【打开CAN通信】
2025-07-31 20:35:12:489 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:35:12:558 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:35:12:738 ==>> [D][05:18:26][COMM]read battery soc:255
$GBGGA,123516.513,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,24,,,40,14,,,40,59,,,39,39,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,26,,,33,10,,,33,44,,,33,5,,,32,1*43

$GBGSV,7,7,27,4,,,31,34,,,31,23,,,29,1*4A

$GBRMC,123516.513,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123516.513,0.000,744.881,744.881,681.214,2097152,2097152,2097152*6C



2025-07-31 20:35:12:751 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:35:12:755 ==>> 检测【检测CAN通信】
2025-07-31 20:35:12:758 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:35:12:843 ==>> can send success


2025-07-31 20:35:12:888 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:35:12:948 ==>> [D][05:18:26][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 37407
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:35:13:009 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:35:13:028 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:35:13:032 ==>> 检测【关闭CAN通信】
2025-07-31 20:35:13:039 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:35:13:068 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:35:13:158 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:35:13:312 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:35:13:320 ==>> 检测【打印IMU STATE】
2025-07-31 20:35:13:343 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:35:13:548 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:26][COMM]YAW data: 32763[32763]
[D][05:18:26][COMM]pitch:-66 roll:0
[D][05:18:26][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:35:13:582 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:35:13:588 ==>> 检测【六轴自检】
2025-07-31 20:35:13:594 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:35:13:792 ==>> $GBGGA,123517.513,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,25,,,41,60,,,40,3,,,40,1*44

$GBGSV,7,2,27,24,,,40,14,,,40,59,,,39,39,,,39,1*73

$GBGSV,7,3,27,42,,,38,40,,,37,16,,,37,13,,,36,1*7E

$GBGSV,7,4,27,38,,,36,1,,,36,41,,,36,7,,,35,1*7B

$GBGSV,7,5,27,9,,,35,6,,,35,2,,,34,8,,,34,1*74

$GBGSV,7,6,27,26,,,33,10,,,33,44,,,33,5,,,32,1*43

$GBGSV,7,7,27,4,,,32,34,,,31,23,,,29,1*49

$GBRMC,123517.513,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,123517.513,0.000,745.645,745.645,681.912,2097152,2097152,2097152*60

[W][05:18:27][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:27][CAT1]gsm read msg sub id: 12
[D][05:18:27][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:35:14:637 ==>> [D][05:18:28][COMM]read battery soc:255


2025-07-31 20:35:15:840 ==>> $GBGGA,123514.519,2301.2577990,N,11421.9418203,E,1,20,0.63,72.868,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.16,0.63,0.97,4*08

$GBGSA,A,3,40,10,38,41,44,26,34,23,,,,,1.16,0.63,0.97,4*0C

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,40,1*4C

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,37,9,48,321,35,2,46,238,34,42,43,164,38,1*4F

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,33,34,13,153,31,23,2,251,29,1*4E

$GBRMC,123514.519,A,2301.2577990,N,11421.9418203,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[D][05:18:28][GNSS]HD8040 GPS
[W][05:18:28][GNSS]single mode encounter continous mode, immediately report.
[D][05:18:28][GNSS]GPS diff_sec 124010206, report 0x42 frame
$GBGST,123514.519,1.797,0.365,0.334,0.483,2.162,3.257,7.129*72

[D][05:18:28][COMM]Main Task receive event:131
[D][05:18:28][COMM]index:0,power_mode:0xFF
[D][05:18:28][COMM]index:1,sound_mode:0xFF
[D][05:18:28][COMM]index:2,gsensor_mode:0xFF
[D][05:18:28][COMM]index:3,report_freq_mod

2025-07-31 20:35:15:945 ==>> e:0xFF
[D][05:18:28][COMM]index:4,report_period:0xFF
[D][05:18:28][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:28][COMM]index:6,normal_reset_period:0xFF
[D][05:18:28][COMM]index:7,spock_over_speed:0xFF
[D][05:18:28][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:28][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:28][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:28][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:28][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:28][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:28][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:28][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:28][COMM]index:16,imu_config_params:0xFF
[D][05:18:28][COMM]index:17,long_connect_params:0xFF
[D][05:18:28][COMM]index:18,detain_mark:0xFF
[D][05:18:28][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:28][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:28][COMM]index:21,mc_mode:0xFF
[D][05:18:28][COMM]index:22,S_mode:0xFF
[D][05:18:28][COMM]index:23,overweight:0xFF
[D][05:18:28][COMM]index:24,standstill_mode:0xFF
[D][05:18:28][COMM]index:25,night_mode:0xFF
[D][05:18:28][COMM]index:26,experiment1:0xFF
[D][05:18:28][COMM]index:27,experim

2025-07-31 20:35:16:050 ==>> ent2:0xFF
[D][05:18:28][COMM]index:28,experiment3:0xFF
[D][05:18:28][COMM]index:29,experiment4:0xFF
[D][05:18:28][COMM]index:30,night_mode_start:0xFF
[D][05:18:28][COMM]index:31,night_mode_end:0xFF
[D][05:18:28][COMM]index:33,park_report_minutes:0xFF
[D][05:18:28][COMM]index:34,park_report_mode:0xFF
[D][05:18:28][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:28][COMM]index:38,charge_battery_para: FF
[D][05:18:28][COMM]index:39,multirider_mode:0xFF
[D][05:18:28][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:28][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:28][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:28][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:28][COMM]index:44,riding_duration_config:0xFF
[D][05:18:28][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:28][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:28][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:28][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:28][COMM]index:49,mc_load_startup:0xFF
[D][05:18:28][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:28][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:28][COMM]index:52,traffic_mode:0xFF
[D][05:18:28][COMM]index:53,traffic_info_collect

2025-07-31 20:35:16:155 ==>> _freq:0xFF
[D][05:18:28][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:28][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:28][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:28][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:28][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:28][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:28][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:28][COMM]index:63,experiment5:0xFF
[D][05:18:28][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:28][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:28][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:28][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:28][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:28][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:28][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:28][COMM]index:72,experiment6:0xFF
[D][05:18:28][COMM]index:73,experiment7:0xFF
[D][05:18:28][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:28][COMM]index:75,zero_value_from_server:-1
[D][05:18:28][COMM]index:76,multirider_threshold:255
[D][05:18:28][COMM]index:77,experiment8:255
[D][05:18:28][COMM]ind

2025-07-31 20:35:16:260 ==>> ex:78,temp_park_audio_play_duration:255
[D][05:18:28][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:28][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:28][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:28][COMM]index:83,loc_report_interval:255
[D][05:18:28][COMM]index:84,multirider_threshold_p2:255
[D][05:18:28][COMM]index:85,multirider_strategy:255
[D][05:18:28][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:28][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:28][COMM]index:90,weight_param:0xFF
[D][05:18:28][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:28][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:28][COMM]index:95,current_limit:0xFF
[D][05:18:28][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:28][COMM]index:100,location_mode:0xFF

[D][05:18:28][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:18:28][COMM]Main Task receive event:131 finished processing
[D][05:18:

2025-07-31 20:35:16:366 ==>> 28][PROT]index:0 1629955108
[D][05:18:28][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:28][PROT]is_send:0
[D][05:18:28][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:28][PROT]sequence_num:4
[D][05:18:28][PROT]retry_timeout:0
[D][05:18:28][PROT]retry_times:1
[D][05:18:28][PROT]send_path:0x2
[D][05:18:28][PROT]min_index:0, type:0x4205, priority:0
[D][05:18:28][PROT]===========================================================
[W][05:18:28][PROT]SEND DATA TYPE:4205, SENDPATH:0x2 [1629955108]
[D][05:18:28][PROT]===========================================================
[D][05:18:28][PROT]sending traceid [9999999999900005]
[D][05:18:28][PROT]Send_TO_M2M [1629955108]
[D][05:18:28][COMM]Main Task receive event:20
[D][05:18:28][GNSS]stop event:1
[D][05:18:28][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:28][COMM]----- get Acckey 1 and value:1------------
[D][05:18:28][COMM]----- get Acckey 2 and value:0------------
[D][05:18:28][COMM]------------ready to Power on Acckey 2------------
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:28][SAL ]sock send credit cnt[6]
[D][05:18:28][SAL ]sock send ind credit cnt[6]
[D][05:18:28][M2M ]m2m send dat

2025-07-31 20:35:16:456 ==>> a len[294]
[D][05:18:28][SAL ]Cellular task submsg id[10]
[D][05:18:28][SAL ]cellular SEND socket id[0] type[1], len[294], data[0x20052e08] format[0]
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:28][COMM]First location,do verification
[D][05:18:28][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:28][COMM][frmJournal_read][220] read fail
[D][05:18:28][COMM]get hw mark: ffffffff
[D][05:18:28][COMM]bat type 255
[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:18:28][PROT]add success [1629955108],send_path[2],type[0306],priority[3],index[1],used[1]
[D][05:18:28][COMM]photo upload succeed!!! send ack
[D][05:18:28][COMM]photo upload taskid:[00000000 00000000]
[W][05:18:28][PROT]remove success[1629955108],send_path[2],type[0000],priority[0],i

2025-07-31 20:35:16:561 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:35:16:592 ==>>                                                                                              

2025-07-31 20:35:16:652 ==>>           30][COMM]read battery soc:255


2025-07-31 20:35:17:395 ==>> $GBGGA,123517.000,2301.2581010,N,11421.9413991,E,1,19,0.74,74.199,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.29,4*00

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.29,4*05

$GBGSV,7,1,27,33,69,282,42,14,68,193,41,3,61,191,40,59,52,129,40,1*4D

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,41,1*73

$GBGSV,7,3,27,1,48,126,37,9,48,321,35,2,46,238,34,42,43,164,38,1*4F

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,36,1*40

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,34,1*4A

$GBGSV,7,7,27,26,13,50,34,34,13,153,30,23,2,251,30,1*40

$GBGSV,3,1,12,33,69,282,43,39,52,7,39,24,51,13,42,42,43,164,40,5*43

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,35,41,22,320,35,5*71

$GBGSV,3,3,12,44,15,101,33,26,13,50,33,34,13,153,30,23,,,31,5*74

$GBRMC,123517.000,A,2301.2581010,N,11421.9413991,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:30][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123517.000,2.737,0.204,0.187,0.275,2.077,2.300,4.057*7A



2025-07-31 20:35:17:950 ==>> [D][05:18:31][COMM]msg 0601 loss. last_tick:37391. cur_tick:42399. period:500
[D][05:18:31][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 42399


2025-07-31 20:35:18:395 ==>> $GBGGA,123518.000,2301.2581708,N,11421.9412973,E,1,19,0.74,74.565,M,-1.770,M,,*55

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.29,4*00

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.29,4*05

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,40,1*4C

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,34,1*4A

$GBGSV,7,7,27,26,13,50,34,34,13,153,31,23,2,251,30,1*41

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,35,41,22,320,35,5*71

$GBGSV,3,3,12,44,15,101,33,26,13,50,33,34,13,153,30,23,,,30,5*75

$GBRMC,123518.000,A,2301.2581708,N,11421.9412973,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:31][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123518.000,2.787,0.219,0.199,0.296,2.070,2.244,3.779*7A



2025-07-31 20:35:18:650 ==>> [D][05:18:32][COMM]read battery soc:255


2025-07-31 20:35:19:412 ==>> $GBGGA,123519.000,2301.2582308,N,11421.9412301,E,1,19,0.74,74.763,M,-1.770,M,,*58

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.29,4*00

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.29,4*05

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,40,1*4C

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,34,1*4A

$GBGSV,7,7,27,26,13,50,33,34,13,153,30,23,2,251,30,1*47

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,40,40,35,160,37,38,28,192,35,41,22,320,35,5*70

$GBGSV,3,3,12,44,15,101,33,26,13,50,33,34,13,153,30,23,,,30,5*75

$GBRMC,123519.000,A,2301.2582308,N,11421.9412301,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

[W][05:18:32][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123519.000,2.825,0.276,0.244,0.370,2.069,2.212,3.590*71



2025-07-31 20:35:20:400 ==>> $GBGGA,123520.000,2301.2582680,N,11421.9412021,E,1,19,0.74,74.749,M,-1.770,M,,*5E

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.29,4*00

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.29,4*05

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,39,1*42

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,33,34,13,153,30,23,2,251,30,1*47

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,40,40,35,160,36,38,28,192,34,41,22,320,35,5*70

$GBGSV,3,3,12,44,15,101,33,26,13,50,34,34,13,153,30,23,,,30,5*72

$GBRMC,123520.000,A,2301.2582680,N,11421.9412021,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

[W][05:18:33][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123520.000,2.950,0.211,0.193,0.285,2.125,2.245,3.498*79



2025-07-31 20:35:20:889 ==>> [D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][COMM]read battery soc:255
[D][05:18:34][PROT]index:1 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:5
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:10
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:1, type:0x0306, priority:3
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:0306, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900006]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][PROT]CLEAN:0
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34][CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D

2025-07-31 20:35:20:980 ==>> ][05:18:34][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B36206AE69A2D03426EE91911BEA7565613CD1F81977BE69337BA0D355AEC0907094E1E262EE7F8DF6329B1F17E44946B668716A475B28442DA9FE64EB1E612187EB02D1447C13E4B9076762F49A0A29742697
[D][05:18:34][CAT1]<<< 
SEND OK

[D][05:18:34][CAT1]exec over: func id: 15, ret: 11
[D][05:18:34][CAT1]sub id: 15, ret: 11

[D][05:18:34][SAL ]Cellular task submsg id[68]
[D][05:18:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:34][M2M ]g_m2m_is_idle become true
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:34][PROT]M2M Send ok [1629955114]


2025-07-31 20:35:21:411 ==>> $GBGGA,123521.000,2301.2582910,N,11421.9412004,E,1,19,0.74,74.787,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.30,4*08

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.30,4*0D

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,39,1*42

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,31,10,30,187,33,1*77

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,33,34,13,153,30,23,2,251,29,1*4F

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,34,41,22,320,35,5*70

$GBGSV,3,3,12,44,15,101,33,26,13,50,34,34,13,153,30,23,,,30,5*72

$GBRMC,123521.000,A,2301.2582910,N,11421.9412004,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:34][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123521.000,3.177,0.190,0.178,0.256,2.241,2.342,3.488*73



2025-07-31 20:35:22:415 ==>> $GBGGA,123522.000,2301.2582924,N,11421.9412177,E,1,19,0.74,74.859,M,-1.770,M,,*51

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.30,4*08

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.30,4*0D

$GBGSV,7,1,27,33,69,282,42,14,68,193,41,3,61,191,40,59,52,129,39,1*43

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,33,34,13,153,31,23,2,251,29,1*4E

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,36,38,28,192,35,41,22,320,35,5*70

$GBGSV,3,3,12,44,15,101,33,26,13,50,34,34,13,153,30,23,,,30,5*72

$GBRMC,123522.000,A,2301.2582924,N,11421.9412177,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:35][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123522.000,3.146,0.247,0.222,0.331,2.216,2.306,3.389*73



2025-07-31 20:35:22:677 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 20:35:23:401 ==>> $GBGGA,123523.000,2301.2583030,N,11421.9412148,E,1,19,0.74,74.875,M,-1.770,M,,*5F

$GBGSA,A,3,33,14,06,39,16,24,09,42,25,13,07,08,1.49,0.74,1.30,4*08

$GBGSA,A,3,40,10,38,41,44,26,34,,,,,,1.49,0.74,1.30,4*0D

$GBGSV,7,1,27,33,69,282,42,14,68,193,40,3,61,191,40,59,52,129,39,1*42

$GBGSV,7,2,27,6,52,344,35,39,52,7,39,16,52,347,37,24,51,13,40,1*72

$GBGSV,7,3,27,1,48,126,36,9,48,321,35,2,46,238,34,42,43,164,38,1*4E

$GBGSV,7,4,27,25,42,281,41,13,41,219,36,60,41,238,40,7,38,176,35,1*43

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,32,34,13,153,31,23,2,251,29,1*4F

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,35,41,22,320,35,5*71

$GBGSV,3,3,12,44,15,101,33,26,13,50,34,34,13,153,30,23,,,30,5*72

$GBRMC,123523.000,A,2301.2583030,N,11421.9412148,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[W][05:18:36][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123523.000,3.006,0.195,0.181,0.263,2.131,2.214,3.251*77



2025-07-31 20:35:23:406 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 20:35:23:411 ==>> #################### 【测试结束】 ####################
2025-07-31 20:35:23:430 ==>> 关闭5V供电
2025-07-31 20:35:23:437 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:35:23:552 ==>> 5A A5 04 5A A5 


2025-07-31 20:35:23:657 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:35:24:413 ==>> $GBGGA,123524.000,2301.2583084,N,11421.9412145,E,1,23,0.64,74.819,M,-1.770,M,,*58

$GBGSA,A,3,33,14,03,06,39,16,24,59,09,01,42,60,1.29,0.64,1.12,4*0D

$GBGSA,A,3,25,13,07,08,40,10,38,41,44,26,34,,1.29,0.64,1.12,4*00

$GBGSV,7,1,27,33,69,282,42,14,68,193,41,3,62,190,40,6,52,344,35,1*7E

$GBGSV,7,2,27,39,52,7,39,16,52,347,36,24,51,13,40,59,50,128,39,1*4F

$GBGSV,7,3,27,9,48,321,35,1,46,125,36,2,46,238,34,42,43,164,39,1*42

$GBGSV,7,4,27,60,43,241,40,25,42,281,41,13,41,219,36,7,38,176,35,1*4F

$GBGSV,7,5,27,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*74

$GBGSV,7,6,27,38,28,192,36,41,22,320,36,5,22,257,32,44,15,101,33,1*4D

$GBGSV,7,7,27,26,13,50,32,34,13,153,31,23,2,251,29,1*4F

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,35,41,22,320,35,5*71

$GBGSV,3,3,12,44,15,101,33,26,13,50,34,34,13,153,30,23,,,30,5*72

$GBRMC,123524.000,A,2301.2583084,N,11421.9412145,E,0.003,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

[W][05:18:37][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123524.000,2.838,0.225,0.211,0.305,2.028,2.105,3.108*72



2025-07-31 20:35:24:443 ==>> 关闭5V供电成功
2025-07-31 20:35:24:451 ==>> 关闭33V供电
2025-07-31 20:35:24:458 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:35:24:548 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:35:24:653 ==>> [D][05:18:38][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:3

2025-07-31 20:35:24:713 ==>> 8][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:38][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:35:24:958 ==>> [D][05:18:38][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:35:25:402 ==>> $GBGGA,123525.000,2301.2583031,N,11421.9412094,E,1,23,0.64,74.886,M,-1.770,M,,*5C

$GBGSA,A,3,33,14,03,06,39,16,24,59,09,01,42,60,1.29,0.64,1.12,4*0D

$GBGSA,A,3,25,13,07,08,40,10,38,41,44,26,34,,1.29,0.64,1.12,4*00

$GBGSV,7,1,26,33,69,282,42,14,68,193,40,3,62,190,40,6,52,344,35,1*7E

$GBGSV,7,2,26,39,52,7,39,16,52,347,36,24,51,13,40,59,50,128,39,1*4E

$GBGSV,7,3,26,9,48,321,35,1,46,125,36,2,46,238,34,42,43,164,38,1*42

$GBGSV,7,4,26,60,43,241,40,25,42,281,41,13,41,219,36,7,38,176,35,1*4E

$GBGSV,7,5,26,8,37,207,34,40,35,160,37,4,32,112,32,10,30,187,33,1*75

$GBGSV,7,6,26,38,28,192,36,41,22,320,36,44,15,101,33,26,13,50,32,1*4A

$GBGSV,7,7,26,34,13,153,31,23,2,251,28,1*4D

$GBGSV,3,1,12,33,69,282,43,39,52,7,40,24,51,13,42,42,43,164,40,5*4D

$GBGSV,3,2,12,25,42,281,41,40,35,160,37,38,28,192,35,41,22,320,35,5*71

$GBGSV,3,3,12,44,15,101,33,26,13,50,33,34,13,153,30,23,,,31,5*74

$GBRMC,123525.000,A,2301.2583031,N,11421.9412094,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[W][05:18:38][GNSS]single mode encounter continous mode, immediately report.
$GBGST,123525.000,2.957,0.217,0.204,0.295,2.093,2.162,3.117*79



2025-07-31 20:35:25:447 ==>> 关闭33V供电成功
2025-07-31 20:35:25:455 ==>> 关闭3.7V供电
2025-07-31 20:35:25:464 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:35:25:553 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:35:26:139 ==>>  

