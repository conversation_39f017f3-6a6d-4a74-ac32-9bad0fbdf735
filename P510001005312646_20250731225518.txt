2025-07-31 22:55:18:177 ==>> MES查站成功:
查站序号:P510001005312646验证通过
2025-07-31 22:55:18:184 ==>> 扫码结果:P510001005312646
2025-07-31 22:55:18:196 ==>> 当前测试项目:SE51_PCBA
2025-07-31 22:55:18:197 ==>> 测试参数版本:2024.10.11
2025-07-31 22:55:18:198 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 22:55:18:199 ==>> 检测【打开透传】
2025-07-31 22:55:18:201 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 22:55:18:316 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 22:55:18:528 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 22:55:18:534 ==>> 检测【检测接地电压】
2025-07-31 22:55:18:536 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 22:55:18:607 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 22:55:18:811 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 22:55:18:814 ==>> 检测【打开小电池】
2025-07-31 22:55:18:816 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 22:55:18:909 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 22:55:19:098 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 22:55:19:100 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 22:55:19:103 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 22:55:19:215 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 22:55:19:370 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 22:55:19:372 ==>> 检测【等待设备启动】
2025-07-31 22:55:19:375 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:20:399 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:21:432 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:22:458 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:23:489 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:24:515 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:25:556 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:26:605 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:27:639 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:28:670 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 22:55:29:705 ==>> 未匹配到【等待设备启动】数据,请核对检查!
2025-07-31 22:55:29:708 ==>> #################### 【测试结束】 ####################
2025-07-31 22:55:29:729 ==>> 关闭5V供电
2025-07-31 22:55:29:732 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 22:55:29:815 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 22:55:30:736 ==>> 关闭5V供电成功
2025-07-31 22:55:30:739 ==>> 关闭33V供电
2025-07-31 22:55:30:742 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 22:55:30:813 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 22:55:31:750 ==>> 关闭33V供电成功
2025-07-31 22:55:31:753 ==>> 关闭3.7V供电
2025-07-31 22:55:31:755 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 22:55:31:813 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 22:55:32:072 ==>>  

