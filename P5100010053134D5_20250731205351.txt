2025-07-31 20:53:51:647 ==>> MES查站成功:
查站序号:P5100010053134D5验证通过
2025-07-31 20:53:51:651 ==>> 扫码结果:P5100010053134D5
2025-07-31 20:53:51:654 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:53:51:655 ==>> 测试参数版本:2024.10.11
2025-07-31 20:53:51:657 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:53:51:658 ==>> 检测【打开透传】
2025-07-31 20:53:51:660 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:53:51:756 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:53:52:035 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:53:52:038 ==>> 检测【检测接地电压】
2025-07-31 20:53:52:039 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:53:52:157 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:53:52:316 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:53:52:318 ==>> 检测【打开小电池】
2025-07-31 20:53:52:321 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:53:52:463 ==>> 6A A6 01 A6 6A 


2025-07-31 20:53:52:553 ==>> Battery ON
OVER 150


2025-07-31 20:53:52:590 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:53:52:592 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:53:52:594 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:53:52:658 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:53:52:861 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:53:52:865 ==>> 检测【等待设备启动】
2025-07-31 20:53:52:869 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:53:53:292 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:53:53:491 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:53:53:887 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:53:54:173 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:53:54:554 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:53:54:921 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:53:55:031 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:53:55:201 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:53:55:203 ==>> 检测【产品通信】
2025-07-31 20:53:55:204 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:53:55:320 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:53:55:474 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:53:55:476 ==>> 检测【初始化完成检测】
2025-07-31 20:53:55:478 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:53:55:731 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:53:56:012 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:53:56:024 ==>> 检测【关闭大灯控制1】
2025-07-31 20:53:56:026 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:53:56:132 ==>> [D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:53:56:236 ==>>                                                                                                                    TY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:53:56:288 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:53:56:291 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:53:56:293 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:53:56:462 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:53:56:576 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:53:56:579 ==>> 检测【关闭仪表供电】
2025-07-31 20:53:56:580 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:53:56:750 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:53:56:895 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:53:56:897 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:53:56:899 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:53:57:025 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:53:57:131 ==>> [D][05:17:52][COMM]3649 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:53:57:177 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:53:57:179 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:53:57:181 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:53:57:327 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:53:57:456 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:53:57:458 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:53:57:459 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:53:57:617 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:53:57:729 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:53:57:731 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:53:57:732 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:53:57:861 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:53:57:920 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0


2025-07-31 20:53:58:002 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:53:58:005 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:53:58:006 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:53:58:026 ==>> [D][05:17:53][COMM]read battery soc:255


2025-07-31 20:53:58:057 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:53:58:146 ==>> [D][05:17:53][COMM]4660 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:53:58:288 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:53:58:290 ==>> 该项需要延时执行
2025-07-31 20:53:58:665 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5005. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5006. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5006. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5007. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5007. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5007. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5008. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5008. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5009. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5009. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5009. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5010. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5010
[D][05:17:53][COMM]CAN message bat fault change: 0x0001

2025-07-31 20:53:58:695 ==>> 802E->0x01B987FE 5010


2025-07-31 20:53:59:164 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:53:59:529 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:54:00:030 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 an

2025-07-31 20:54:00:134 ==>> d value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT

2025-07-31 20:54:00:240 ==>> ]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]rem

2025-07-31 20:54:00:330 ==>> ove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
                                                                                                                                                                                  

2025-07-31 20:54:01:184 ==>> [D][05:17:56][COMM]7693 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:54:02:047 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:54:02:182 ==>> [D][05:17:57][COMM]8704 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:54:02:302 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:54:02:307 ==>> 检测【33V输入电压ADC】
2025-07-31 20:54:02:310 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:02:572 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:17:57][COMM]adc read out 24v adc:1342  volt:33943 mv
[D][05:17:57][COMM]adc read left brake adc:22  volt:29 mv
[D][05:17:57][COMM]adc read right brake adc:32  volt:42 mv
[D][05:17:57][COMM]adc read throttle adc:19  volt:25 mv
[D][05:17:57][COMM]adc read battery ts volt:20 mv
[D][05:17:57][COMM]adc read in 24v adc:1312  volt:33184 mv
[D][05:17:57][COMM]adc read throttle brake in adc:14  volt:24 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:13  volt:301 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:54:02:850 ==>> 【33V输入电压ADC】通过,【33184mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:54:02:876 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:54:02:877 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:54:02:966 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2760mV
Get AD_V6 1992mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:54:03:121 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:123 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:54:03:142 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:144 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:54:03:147 ==>> 原始值:【2760】, 乘以分压基数【2】还原值:【5520】
2025-07-31 20:54:03:163 ==>> 【TP68_VCC5V5(ADV5)】通过,【5520mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:54:03:165 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:54:03:181 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:54:03:184 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:54:03:206 ==>> 【TP1_VCC12V(ADV7)】通过,【1093mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:54:03:209 ==>> [D][05:17:58][COMM]9715 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:54:03:211 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:54:03:266 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2762mV
Get AD_V6 1989mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:54:03:490 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:492 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:54:03:514 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:517 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:54:03:520 ==>> 原始值:【2762】, 乘以分压基数【2】还原值:【5524】
2025-07-31 20:54:03:537 ==>> 【TP68_VCC5V5(ADV5)】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:54:03:539 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:54:03:556 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10019. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10020
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10021


2025-07-31 20:54:03:559 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1989mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:54:03:561 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:54:03:585 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:54:03:587 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:54:03:660 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1661mV
Get AD_V4 0mV
Get AD_V5 2763mV
Get AD_V6 1988mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:54:03:825 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:54:03:869 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:871 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:54:03:891 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:03:895 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:54:03:897 ==>> 原始值:【2763】, 乘以分压基数【2】还原值:【5526】
2025-07-31 20:54:03:913 ==>> 【TP68_VCC5V5(ADV5)】通过,【5526mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:54:03:932 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:54:03:935 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1988mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:54:03:938 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:54:03:961 ==>> 【TP1_VCC12V(ADV7)】通过,【1094mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:54:03:964 ==>> 检测【打开WIFI(1)】
2025-07-31 20:54:03:966 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:54:04:035 ==>> [D][05:17:59][COMM]read battery soc:255


2025-07-31 20:54:04:443 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10726 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] 

2025-07-31 20:54:04:488 ==>> >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:54:04:492 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:54:04:495 ==>> 检测【清空消息队列(1)】
2025-07-31 20:54:04:518 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:54:04:901 ==>>                                                                                                                                                                                        
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087756849

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539186

OK

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot",

2025-07-31 20:54:04:932 ==>> "","",0



2025-07-31 20:54:05:028 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:54:05:031 ==>> 检测【打开GPS(1)】
2025-07-31 20:54:05:038 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:54:05:266 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:54:05:303 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:54:05:306 ==>> 检测【打开GSM联网】
2025-07-31 20:54:05:307 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:54:05:446 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 20:54:05:586 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:54:05:589 ==>> 检测【打开仪表供电1】
2025-07-31 20:54:05:591 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:54:05:846 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:54:06:058 ==>> [D][05:18:01][COMM]read battery soc:255


2025-07-31 20:54:06:125 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:54:06:128 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:54:06:130 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:54:06:358 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:54:06:409 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:54:06:412 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:54:06:415 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:54:06:553 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33409]


2025-07-31 20:54:06:700 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:54:06:703 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:54:06:705 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:06:858 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:54:06:992 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:54:06:995 ==>> 检测【AD_V20电压】
2025-07-31 20:54:06:999 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:07:038 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:54:07:098 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:54:07:143 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:54:07:218 ==>>          :02][COMM]13740 imu init OK


2025-07-31 20:54:07:569 ==>> 本次取值间隔时间:465ms
2025-07-31 20:54:07:594 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:07:692 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]


2025-07-31 20:54:07:707 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:54:07:767 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:54:07:797 ==>> 本次取值间隔时间:86ms
2025-07-31 20:54:07:825 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:07:932 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:54:07:977 ==>>                                                                                           8:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F383832313F333E07

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to write para2 flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELIN

2025-07-31 20:54:08:052 ==>> E_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][CAT1]<<< 
+CSQ: 26,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.226.44.133"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:54:08:277 ==>> 本次取值间隔时间:331ms
2025-07-31 20:54:08:443 ==>>                                          [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]opened : 0, 0
[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:03][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][M2M ]g_m2m_is_idle become true
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] 

2025-07-31 20:54:08:473 ==>> >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:54:08:533 ==>> 本次取值间隔时间:247ms
2025-07-31 20:54:08:668 ==>> 本次取值间隔时间:130ms
2025-07-31 20:54:09:046 ==>> 本次取值间隔时间:367ms
2025-07-31 20:54:09:050 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:09:061 ==>> [D][05:18:04][COMM]IMU: [6,0,-955] ret=24 AWAKE!


2025-07-31 20:54:09:136 ==>>             0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:54:09:151 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:54:09:241 ==>> [W][05:18:04][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:54:09:481 ==>> 本次取值间隔时间:325ms
2025-07-31 20:54:09:506 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:54:09:620 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:54:09:650 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:54:09:755 ==>> 1A A1 10 00 00 
Get AD_V20 1652mV
OVER 150


2025-07-31 20:54:09:800 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:54:09:965 ==>> 本次取值间隔时间:333ms
2025-07-31 20:54:09:990 ==>> 【AD_V20电压】通过,【1652mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:09:993 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:54:10:012 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:54:10:014 ==>> [D][05:18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,09,33,,,44,24,,,41,60,,,41,25,,,40,1*7E

$GBGSV,3,2,09,39,,,39,40,,,38,42,,,34,41,,,34,1*72

$GBGSV,3,3,09,14,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1611.706,1611.706,51.572,2097152,2097152,2097152*4B

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6



2025-07-31 20:54:10:055 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:54:10:100 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:54:10:205 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:54:10:278 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:54:10:281 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:54:10:283 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:54:10:463 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:0
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:54:10:568 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:54:10:573 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:54:10:576 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:54:10:659 ==>> 3A A3 02 01 A3 


2025-07-31 20:54:10:764 ==>> ON_OUT2
OVER 150


2025-07-31 20:54:10:847 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:54:10:850 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:54:10:854 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:54:10:977 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,44,24,,,41,60,,,41,25,,,41,1*76

$GBGSV,4,2,16,59,,,41,3,,,41,39,,,40,40,,,39,1*48

$GBGSV,4,3,16,1,,,39,14,,,38,13,,,37,4,,,37,1*75

$GBGSV,4,4,16,42,,,36,41,,,36,5,,,36,9,,,33,1*7B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1606.497,1606.497,51.379,2097152,2097152,2097152*46

[D][05:18:06][COMM]IMU: [11,20,-924] ret=28 AWAKE!


2025-07-31 20:54:11:081 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM]oneline display read state:1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:06][COMM]IMU: [10,8,-985] ret=30 AWAKE!


2025-07-31 20:54:11:139 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:54:11:144 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:54:11:148 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:54:11:356 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:06][COMM]oneline display set 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:54:11:429 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:54:11:432 ==>> 检测【AD_V21电压】
2025-07-31 20:54:11:436 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:54:11:566 ==>> 1A A1 20 00 00 
Get AD_V21 1062mV
OVER 150


2025-07-31 20:54:11:701 ==>> [D][05:18:07][COMM]IMU: [-8,14,-1080] ret=37 AWAKE!


2025-07-31 20:54:11:866 ==>> 本次取值间隔时间:431ms
2025-07-31 20:54:11:892 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:54:11:971 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,43,24,,,41,60,,,41,25,,,41,1*7F

$GBGSV,5,2,19,59,,,41,3,,,41,39,,,40,40,,,39,1*46

$GBGSV,5,3,19,14,,,39,1,,,38,13,,,37,42,,,37,1*49

$GBGSV,5,4,19,41,,,37,2,,,37,4,,,36,5,,,35,1*4A

$GBGSV,5,5,19,38,,,35,9,,,35,44,,,34,1*4B

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1586.308,1586.308,50.728,2097152,2097152,2097152*47

1A A1 20 00 00 
Get AD_V21 1646mV
OVER 150


2025-07-31 20:54:12:076 ==>> [D][05:18:

2025-07-31 20:54:12:106 ==>> 07][COMM]read battery soc:255


2025-07-31 20:54:12:273 ==>> 本次取值间隔时间:378ms
2025-07-31 20:54:12:300 ==>> 【AD_V21电压】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:12:304 ==>> 检测【关闭仪表供电2】
2025-07-31 20:54:12:307 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:54:12:487 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:07][COMM]set POWER 0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:54:12:590 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:54:12:592 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:54:12:595 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:54:12:743 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:08][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:54:12:877 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:54:12:880 ==>> 检测【打开AccKey2供电】
2025-07-31 20:54:12:884 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:54:13:002 ==>> $GBGGA,125416.791,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,43,24,,,41,60,,,41,25,,,41,1*75

$GBGSV,6,2,23,59,,,41,3,,,41,39,,,40,14,,,40,1*43

$GBGSV,6,3,23,40,,,39,1,,,38,42,,,38,41,,,38,1*45

$GBGSV,6,4,23,16,,,38,13,,,37,2,,,37,9,,,36,1*75

$GBGSV,6,5,23,4,,,35,5,,,35,38,,,35,44,,,35,1*7E

$GBGSV,6,6,23,34,,,35,23,,,34,12,,,15,1*77

$GBRMC,125416.791,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125416.791,0.000,1537.686,1537.686,49.311,2097152,2097152,2097152*55

[D][05:18:08][COMM]IMU: [11,2,-944] ret=22 AWAKE!


2025-07-31 20:54:13:107 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<
[D][05:18:08][COMM]IMU: [14,11,-992] ret=23 AWAKE!


2025-07-31 20:54:13:163 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:54:13:167 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:54:13:169 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:13:474 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:08][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:08][COMM]adc read out 24v adc:1329  volt:33614 mv
[D][05:18:08][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:08][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:08][COMM]adc read throttle adc:14  volt:18 mv
[D][05:18:08][COMM]adc read battery ts volt:12 mv
[D][05:18:08][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:08][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:18:08][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:08][COMM]arm_hub adc read vbat adc:2402  volt:3870 mv
[D][05:18:08][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:08][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:08][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:54:13:696 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33614mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:54:13:700 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:54:13:703 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:54:13:806 ==>> $GBGGA,125417.591,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,25,,,42,59,,,41,1*7B

$GBGSV,7,2,26,3,,,41,14,,,41,60,,,40,39,,,40,1*4D

$GBGSV,7,3,26,40,,,39,41,,,39,1,,,38,42,,,38,1*40

$GBGSV,7,4,26,16,,,38,13,,,37,2,,,37,7,,,37,1*7E

$GBGSV,7,5,26,9,,,36,44,,,36,38,,,35,34,,,35,1*45

$GBGSV,7,6,26,10,,,35,4,,,34,5,,,34,23,,,33,1*74

$GBGSV,7,7,26,6,,,33,12,,,32,1*46

$GBRMC,125417.591,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125417.591,0.000,1556.289,1556.289,49.795,2097152,2097152,2097152*5E



2025-07-31 20:54:13:881 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:54:13:986 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:54:13:990 ==>> 该项需要延时执行
2025-07-31 20:54:14:124 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:54:14:786 ==>> $GBGGA,125418.571,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,42,25,,,41,59,,,41,1*79

$GBGSV,7,2,27,3,,,41,14,,,41,60,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,1,,,39,42,,,39,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,2,,,36,44,,,36,34,,,36,38,,,35,1*4C

$GBGSV,7,6,27,10,,,35,4,,,35,6,,,35,5,,,34,1*45

$GBGSV,7,7,27,23,,,33,12,,,32,26,,,31,1*76

$GBRMC,125418.571,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125418.571,0.000,1555.466,1555.466,49.771,2097152,2097152,2097152*55



2025-07-31 20:54:15:776 ==>> $GBGGA,125419.551,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,24,,,42,25,,,41,59,,,41,1*79

$GBGSV,7,2,27,3,,,41,14,,,41,60,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,2,,,36,44,,,36,34,,,36,38,,,35,1*4C

$GBGSV,7,6,27,4,,,35,6,,,35,10,,,34,5,,,34,1*44

$GBGSV,7,7,27,23,,,33,12,,,32,26,,,32,1*75

$GBRMC,125419.551,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125419.551,0.000,1553.928,1553.928,49.720,2097152,2097152,2097152*52



2025-07-31 20:54:16:125 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:54:16:748 ==>> $GBGGA,125420.531,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,24,,,42,25,,,42,59,,,41,1*7D

$GBGSV,7,2,27,3,,,41,14,,,41,60,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,39,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,2,,,36,44,,,36,34,,,36,6,,,36,1*72

$GBGSV,7,6,27,38,,,35,4,,,35,10,,,35,5,,,34,1*78

$GBGSV,7,7,27,23,,,33,26,,,33,12,,,32,1*74

$GBRMC,125420.531,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125420.531,0.000,1563.141,1563.141,50.014,2097152,2097152,2097152*56



2025-07-31 20:54:16:988 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:54:16:993 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:54:16:997 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:17:020 ==>> [D][05:18:12][COMM]IMU: [18,7,-1116] ret=61 AWAKE!


2025-07-31 20:54:17:275 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:12][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:12][COMM]adc read left brake adc:24  volt:31 mv
[D][05:18:12][COMM]adc read right brake adc:24  volt:31 mv
[D][05:18:12][COMM]adc read throttle adc:24  volt:31 mv
[D][05:18:12][COMM]adc read battery ts volt:28 mv
[D][05:18:12][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:12][COMM]adc read throttle brake in adc:19  volt:33 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:12][COMM]IMU: [6,1,-1005] ret=45 AWAKE!
[D][05:18:12][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:54:17:528 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【303mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:54:17:531 ==>> 检测【打开AccKey1供电】
2025-07-31 20:54:17:536 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:54:17:848 ==>> $GBGGA,125421.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,24,,,42,25,,,42,59,,,41,1*7D

$GBGSV,7,2,27,3,,,41,14,,,41,60,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,39,1*41

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,2,,,36,44,,,36,34,,,36,6,,,36,1*72

$GBGSV,7,6,27,38,,,35,4,,,35,10,,,35,5,,,35,1*79

$GBGSV,7,7,27,23,,,33,26,,,33,12,,,32,1*74

$GBRMC,125421.511,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125421.511,0.000,1564.674,1564.674,50.061,2097152,2097152,2097152*57

[W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweron type 5.... 
[D][05:18:13][COMM]S->M yaw:INVALID
[D][05:18:13][COMM]IMU: [-7,13,-997] ret=42 AWAKE!


2025-07-31 20:54:18:074 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:54:18:078 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:54:18:081 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:54:18:155 ==>> [D][05:18:13][COMM]read battery soc:255
1A A1 00 40 00 
Get AD_V14 2657mV
OVER 150


2025-07-31 20:54:18:336 ==>> 原始值:【2657】, 乘以分压基数【2】还原值:【5314】
2025-07-31 20:54:18:364 ==>> 【读取AccKey1电压(ADV14)前】通过,【5314mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:54:18:367 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:54:18:370 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:18:757 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:14][COMM]adc read out 24v adc:11  volt:278 mv
[D][05:18:14][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:14][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:14][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:14][COMM]adc read battery ts volt:22 mv
[D][05:18:14][COMM]adc read in 24v adc:1302  volt:32931 mv
[D][05:18:14][COMM]adc read throttle brake in adc:10  volt:17 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
$GBGGA,125422.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,24,,,42,25,,,41,59,,,41,1*7E

$GBGSV,7,2,27,3,,,41,60,,,41,14,,,40,39,,,40,1*4C

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,6,,,37,2,,,36,44

2025-07-31 20:54:18:817 ==>> ,,,36,38,,,36,1*7F

$GBGSV,7,6,27,34,,,35,4,,,35,10,,,35,5,,,34,1*74

$GBGSV,7,7,27,23,,,34,26,,,34,12,,,32,1*74

$GBRMC,125422.511,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125422.511,0.000,1563.130,1563.130,50.004,2097152,2097152,2097152*57

                                     

2025-07-31 20:54:18:902 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:54:18:905 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:54:18:909 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:54:19:046 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:54:19:195 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:54:19:201 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:54:19:206 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:54:19:256 ==>> 1A A1 00 40 00 
Get AD_V14 2654mV
OVER 150


2025-07-31 20:54:19:454 ==>> 原始值:【2654】, 乘以分压基数【2】还原值:【5308】
2025-07-31 20:54:19:549 ==>> 【读取AccKey1电压(ADV14)后】通过,【5308mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:54:19:553 ==>> 检测【打开WIFI(2)】
2025-07-31 20:54:19:555 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:54:19:836 ==>> $GBGGA,125423.511,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,42,24,,,41,59,,,41,1*79

$GBGSV,7,2,27,3,,,41,60,,,41,14,,,40,39,,,40,1*4C

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,6,,,36,2,,,36,44,,,36,38,,,36,1*7E

$GBGSV,7,6,27,34,,,36,10,,,35,4,,,34,5,,,34,1*76

$GBGSV,7,7,27,23,,,34,26,,,34,12,,,32,1*74

$GBRMC,125423.511,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125423.511,0.000,1560.057,1560.057,49.903,2097152,2097152,2097152*50

[W][05:18:15][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:15][CAT1]gsm read msg sub id: 12
[D][05:18:15][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:15][CAT1]<<< 
OK

[D][05:18:15][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:54:20:138 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:54:20:145 ==>> 检测【转刹把供电】
2025-07-31 20:54:20:150 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:54:20:154 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:54:20:354 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:54:20:429 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:54:20:432 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:54:20:435 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:54:20:534 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:54:20:747 ==>> 00 00 00 00 00 
head err!
+WIFISCAN:4,0,CC057790A621,-61
+WIFISCAN:4,1,F42A7D1297A3,-69
+WIFISCAN:4,2,CC057790A5C0,-83
+WIFISCAN:4,3,CC057790A5C1,-84

[D][05:18:16][CAT1]wifi scan report total[4]
$GBGGA,125424.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,43,25,,,42,24,,,42,59,,,41,1*7A

$GBGSV,7,2,27,3,,,41,60,,,41,14,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,6,,,36,2,,,36,44,,,36,34,,,36,1*72

$GBGSV,7,6,27,38,,,35,10,,,35,4,,,34,5,,,34,1*79

$GBGSV,7,7,27,23,,,34,26,,,33,12,,,32,1*73

$GBRMC,125424.511,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125424.511,0.000,1560.065,1560.065,49.912,2097152,2097152,2097152*57

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:54:21:258 ==>> [D][05:18:16][GNSS]recv submsg id[3]


2025-07-31 20:54:21:485 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:54:21:595 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:54:21:655 ==>> 1A A1 00 80 00 
Get AD_V15 2400mV
OVER 150


2025-07-31 20:54:21:730 ==>> $GBGGA,125425.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,24,,,42,59,,,41,1*7D

$GBGSV,7,2,27,3,,,41,60,,,41,14,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,6,,,36,2,,,36,44,,,36,34,,,36,1*72

$GBGSV,7,6,27,38,,,35,10,,,34,4,,,34,5,,,34,1*78

$GBGSV,7,7,27,23,,,34,26,,,33,12,,,32,1*73

$GBRMC,125425.511,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125425.511,0.000,1560.071,1560.071,49.917,2097152,2097152,2097152*53

[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:54:21:760 ==>> 原始值:【2400】, 乘以分压基数【2】还原值:【4800】
2025-07-31 20:54:21:782 ==>> 【读取AD_V15电压(前)】通过,【4800mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:54:21:785 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:54:21:787 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:54:21:896 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:54:21:926 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:54:21:956 ==>> 1A A1 01 00 00 
Get AD_V16 2427mV
OVER 150


2025-07-31 20:54:22:061 ==>> 原始值:【2427】, 乘以分压基数【2】还原值:【4854】
2025-07-31 20:54:22:088 ==>> 【读取AD_V16电压(前)】通过,【4854mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:54:22:092 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:54:22:096 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:22:137 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 20:54:22:378 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3168  volt:5568 mv
[D][05:18:17][COMM]adc read out 24v adc:14  volt:354 mv
[D][05:18:17][COMM]adc read left brake adc:22  volt:29 mv
[D][05:18:17][COMM]adc read right brake adc:21  volt:27 mv
[D][05:18:17][COMM]adc read throttle adc:17  volt:22 mv
[D][05:18:17][COMM]adc read battery ts volt:29 mv
[D][05:18:17][COMM]adc read in 24v adc:1315  volt:33260 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3120  volt:5484 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:17][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:54:22:628 ==>> 【转刹把供电电压(主控ADC)】通过,【5484mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:54:22:632 ==>> 检测【转刹把供电电压】
2025-07-31 20:54:22:637 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:22:725 ==>> $GBGGA,125426.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,44,25,,,42,24,,,42,59,,,41,1*7D

$GBGSV,7,2,27,3,,,41,60,,,41,14,,,41,39,,,40,1*4D

$GBGSV,7,3,27,40,,,39,41,,,39,42,,,39,1,,,38,1*40

$GBGSV,7,4,27,16,,,38,13,,,37,7,,,37,9,,,37,1*74

$GBGSV,7,5,27,6,,,36,2,,,36,44,,,36,34,,,36,1*72

$GBGSV,7,6,27,38,,,36,10,,,35,4,,,34,5,,,34,1*7A

$GBGSV,7,7,27,23,,,34,26,,,33,12,,,32,1*73

$GBRMC,125426.511,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125426.511,0.000,1563.139,1563.139,50.012,2097152,2097152,2097152*54



2025-07-31 20:54:22:965 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3157  volt:5549 mv
[D][05:18:18][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:18][COMM]adc read left brake adc:16  volt:21 mv
[D][05:18:18][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:18][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:18][COMM]adc read battery ts volt:20 mv
[D][05:18:18][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3108  volt:5463 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:54:23:171 ==>> 【转刹把供电电压】通过,【5463mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:54:23:175 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:54:23:177 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:23:345 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:54:23:455 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:54:23:459 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:54:23:462 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:23:570 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:54:23:675 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:23:735 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150
$GBGGA,125427.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,25,,,41,59,,,41,1*76

$GBGSV,7,2,28,3,,,41,60,,,41,14,,,40,39,,,39,1*4D

$GBGSV,7,3,28,40,,,39,41,,,39,42,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,13,,,37,7,,,37,9,,,37,1*7B

$GBGSV,7,5,28,6,,,36,2,,,36,44,,,36,34,,,35,1*7E

$GBGSV,7,6,28,38,,,35,10,,,35,4,,,34,5,,,34,1*76

$GBGSV,7,7,28,23,,,34,26,,,33,12,,,32,8,,,39,1*4E

$GBRMC,125427.511,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125427.511,0.000,1553.920,1553.920,49.711,2097152,2097152,2097152*59

[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:54:23:780 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:54:23:841 ==>> [D][05:18:19][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:54:23:914 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:54:23:917 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:54:23:920 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:24:021 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:54:24:126 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:24:156 ==>> [D][05:18:19][COMM]read battery soc:255


2025-07-31 20:54:24:231 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:54:24:337 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:24:445 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:54:24:451 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
[W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:54:24:550 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:54:24:555 ==>> 1A A1 01 00 00 
Get AD_V16 2mV
OVER 150


2025-07-31 20:54:24:655 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:54:24:777 ==>> $GBGGA,125428.511,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,41,25,,,41,59,,,41,1*75

$GBGSV,7,2,28,60,,,41,3,,,40,14,,,40,39,,,39,1*4C

$GBGSV,7,3,28,40,,,39,41,,,39,42,,,38,1,,,38,1*4E

$GBGSV,7,4,28,16,,,38,13,,,37,7,,,37,9,,,36,1*7A

$GBGSV,7,5,28,6,,,36,2,,,36,44,,,36,34,,,36,1*7D

$GBGSV,7,6,28,38,,,35,10,,,35,4,,,34,5,,,34,1*76

$GBGSV,7,7,28,23,,,34,8,,,33,26,,,33,12,,,32,1*44

$GBRMC,125428.511,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125428.511,0.000,1542.840,1542.840,49.355,2097152,2097152,2097152*52

[W][05:18:20][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:54:24:793 ==>> 【读取AD_V16电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:54:24:797 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:54:24:804 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:54:24:852 ==>> 3A A3 03 01 A3 


2025-07-31 20:54:24:957 ==>> ON_OUT3
OVER 150


2025-07-31 20:54:25:078 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:54:25:082 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:54:25:089 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:54:25:155 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:54:25:360 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:54:25:363 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:54:25:367 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:54:25:456 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:54:25:646 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:54:25:650 ==>> 检测【左刹电压测试1】
2025-07-31 20:54:25:656 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:25:727 ==>> $GBGGA,125429.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,41,25,,,41,59,,,41,1*75

$GBGSV,7,2,28,60,,,41,3,,,41,14,,,40,39,,,39,1*4D

$GBGSV,7,3,28,40,,,39,41,,,39,42,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,13,,,37,7,,,37,9,,,36,1*7A

$GBGSV,7,5,28,6,,,36,2,,,36,44,,,36,34,,,35,1*7E

$GBGSV,7,6,28,38,,,35,10,,,35,4,,,34,5,,,34,1*76

$GBGSV,7,7,28,23,,,34,8,,,34,26,,,33,12,,,32,1*43

$GBRMC,125429.511,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125429.511,0.000,1545.803,1545.803,49.451,2097152,2097152,2097152*50



2025-07-31 20:54:25:967 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:21][COMM]adc read out 24v adc:16  volt:404 mv
[D][05:18:21][COMM]adc read left brake adc:1740  volt:2293 mv
[D][05:18:21][COMM]adc read right brake adc:1736  volt:2288 mv
[D][05:18:21][COMM]adc read throttle adc:1733  volt:2284 mv
[D][05:18:21][COMM]adc read battery ts volt:21 mv
[D][05:18:21][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:21][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:54:26:164 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:54:26:205 ==>> 【左刹电压测试1】通过,【2293】符合目标值【2250】至【2500】要求!
2025-07-31 20:54:26:213 ==>> 检测【右刹电压测试1】
2025-07-31 20:54:26:235 ==>> 【右刹电压测试1】通过,【2288】符合目标值【2250】至【2500】要求!
2025-07-31 20:54:26:242 ==>> 检测【转把电压测试1】
2025-07-31 20:54:26:267 ==>> 【转把电压测试1】通过,【2284】符合目标值【2250】至【2500】要求!
2025-07-31 20:54:26:274 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:54:26:279 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:54:26:359 ==>> 3A A3 03 00 A3 


2025-07-31 20:54:26:464 ==>> OFF_OUT3
OVER 150


2025-07-31 20:54:26:545 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:54:26:551 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:54:26:557 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:54:26:737 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150
$GBGGA,125430.511,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,25,,,42,59,,,41,1*75

$GBGSV,7,2,28,60,,,41,3,,,41,14,,,41,39,,,40,1*42

$GBGSV,7,3,28,40,,,39,41,,,39,42,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,13,,,37,7,,,37,9,,,37,1*7B

$GBGSV,7,5,28,6,,,37,2,,,36,44,,,36,38,,,36,1*70

$GBGSV,7,6,28,34,,,35,10,,,35,4,,,34,5,,,34,1*7A

$GBGSV,7,7,28,23,,,34,8,,,34,26,,,33,12,,,32,1*43

$GBRMC,125430.511,V,,,,,,,,0.0,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125430.511,0.000,1556.173,1556.173,49.788,2097152,2097152,2097152*5F



2025-07-31 20:54:26:819 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:54:26:823 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:54:26:826 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:54:26:965 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:54:27:101 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:54:27:107 ==>> 检测【左刹电压测试2】
2025-07-31 20:54:27:113 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:54:27:377 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3156  volt:5547 mv
[D][05:18:22][COMM]adc read out 24v adc:12  volt:303 mv
[D][05:18:22][COMM]adc read left brake adc:11  volt:14 mv
[D][05:18:22][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:22][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:22][COMM]adc read battery ts volt:19 mv
[D][05:18:22][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:18:22][COMM]adc read throttle brake in adc:11  volt:19 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2403  volt:3872 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1442  volt:33433 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:54:27:645 ==>> 【左刹电压测试2】通过,【14】符合目标值【0】至【50】要求!
2025-07-31 20:54:27:649 ==>> 检测【右刹电压测试2】
2025-07-31 20:54:27:669 ==>> 【右刹电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 20:54:27:677 ==>> 检测【转把电压测试2】
2025-07-31 20:54:27:694 ==>> 【转把电压测试2】通过,【21】符合目标值【0】至【50】要求!
2025-07-31 20:54:27:700 ==>> 检测【晶振检测】
2025-07-31 20:54:27:704 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:54:27:744 ==>> $GBGGA,125431.511,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,44,24,,,42,25,,,42,59,,,41,1*72

$GBGSV,7,2,28,60,,,41,3,,,41,14,,,41,39,,,40,1*42

$GBGSV,7,3,28,40,,,39,41,,,39,42,,,39,1,,,38,1*4F

$GBGSV,7,4,28,16,,,38,13,,,37,7,,,37,9,,,37,1*7B

$GBGSV,7,5,28,6,,,37,2,,,36,44,,,36,38,,,35,1*73

$GBGSV,7,6,28,34,,,35,10,,,35,4,,,34,5,,,34,1*7A

$GBGSV,7,7,28,23,,,34,8,,,34,26,,,33,12,,,32,1*43

$GBRMC,125431.511,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125431.511,0.000,776.639,776.639,710.254,2097152,2097152,2097152*61



2025-07-31 20:54:27:849 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:23][COMM][lf state:1][hf state:1]


2025-07-31 20:54:27:980 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:54:27:987 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:54:27:992 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:54:28:064 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1661mV
Get AD_V4 1650mV
Get AD_V5 2762mV
Get AD_V6 1990mV
Get AD_V7 1094mV
OVER 150


2025-07-31 20:54:28:169 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 20:54:28:270 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:54:28:274 ==>> 检测【检测BootVer】
2025-07-31 20:54:28:280 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:54:28:620 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:23][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
[D][05:18:23][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:23][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:23][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:23][FCTY]DeviceID    = 460130071539186
[D][05:18:23][FCTY]HardwareID  = 867222087756849
[D][05:18:23][FCTY]MoBikeID    = 9999999999
[D][05:18:23][FCTY]LockID      = FFFFFFFFFF
[D][05:18:23][FCTY]BLEFWVersion= 105
[D][05:18:23][FCTY]BLEMacAddr   = F4B99F7434DC
[D][05:18:23][FCTY]Bat         = 3944 mv
[D][05:18:23][FCTY]Current     = 0 ma
[D][05:18:23][FCTY]VBUS        = 11800 mv
[D][05:18:23][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:23][FCTY]Ext battery vol = 33, adc = 1311
[D][05:18:23][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 404 mv
[D][05:18:23][FCTY]Bike Type flag is invalied
[D][05:18:23][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:23][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:23][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:23][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18

2025-07-31 20:54:28:725 ==>> :23][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:23][FCTY]Bat1         = 3791 mv
[D][05:18:23][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:23][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:54:28:813 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:54:28:818 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:54:28:824 ==>> 检测【检测固件版本】
2025-07-31 20:54:28:839 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:54:28:844 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:54:28:848 ==>> 检测【检测蓝牙版本】
2025-07-31 20:54:28:865 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:54:28:870 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:54:28:878 ==>> 检测【检测MoBikeId】
2025-07-31 20:54:28:890 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:54:28:895 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:54:28:899 ==>> 检测【检测蓝牙地址】
2025-07-31 20:54:28:902 ==>> 取到目标值:F4B99F7434DC
2025-07-31 20:54:28:915 ==>> 【检测蓝牙地址】通过,【F4B99F7434DC】符合目标值【】要求!
2025-07-31 20:54:28:921 ==>> 提取到蓝牙地址:F4B99F7434DC
2025-07-31 20:54:28:925 ==>> 检测【BOARD_ID】
2025-07-31 20:54:28:946 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:54:28:949 ==>> 检测【检测充电电压】
2025-07-31 20:54:29:012 ==>> 【检测充电电压】通过,【3944mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:54:29:020 ==>> 检测【检测VBUS电压1】
2025-07-31 20:54:29:059 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:54:29:065 ==>> 检测【检测充电电流】
2025-07-31 20:54:29:094 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:54:29:098 ==>> 检测【检测IMEI】
2025-07-31 20:54:29:105 ==>> 取到目标值:867222087756849
2025-07-31 20:54:29:122 ==>> 【检测IMEI】通过,【867222087756849】符合目标值【】要求!
2025-07-31 20:54:29:129 ==>> 提取到IMEI:867222087756849
2025-07-31 20:54:29:149 ==>> 检测【检测IMSI】
2025-07-31 20:54:29:158 ==>> 取到目标值:460130071539186
2025-07-31 20:54:29:161 ==>> 【检测IMSI】通过,【460130071539186】符合目标值【】要求!
2025-07-31 20:54:29:166 ==>> 提取到IMSI:460130071539186
2025-07-31 20:54:29:169 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:54:29:179 ==>> 取到目标值:460130071539186
2025-07-31 20:54:29:187 ==>> 【校验网络运营商(移动)】通过,【460130071539186】符合目标值【】要求!
2025-07-31 20:54:29:193 ==>> 检测【打开CAN通信】
2025-07-31 20:54:29:200 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:54:29:267 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:54:29:478 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:54:29:484 ==>> 检测【检测CAN通信】
2025-07-31 20:54:29:490 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:54:29:554 ==>> can send success


2025-07-31 20:54:29:584 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:54:29:689 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[D][05:18:25][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 36159
$GBGGA,125433.511,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,3,,,41,24,,,41,25,,,41,1*4A

$GBGSV,7,2,28,60,,,40,59,,,40,39,,,40,14,,,40,1*7C

$GBGSV,7,3,28,40,,,39,41,,,39,1,,,38,16,,,38,1*4F

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,6,,,37,1*75

$GBGSV,7,5,2

2025-07-31 20:54:29:749 ==>> 8,2,,,36,44,,,36,9,,,36,10,,,35,1*77

$GBGSV,7,6,28,38,,,35,34,,,35,5,,,34,8,,,34,1*7C

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,32,1*4F

$GBRMC,125433.511,V,,,,,,,310725,0.1,E,N,V*4A

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125433.511,0.000,770.714,770.714,704.835,2097152,2097152,2097152*6B

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:54:29:779 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:54:29:796 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:54:29:801 ==>> 检测【关闭CAN通信】
2025-07-31 20:54:29:808 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:54:29:869 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:54:30:077 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:54:30:081 ==>> 检测【打印IMU STATE】
2025-07-31 20:54:30:088 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:54:30:267 ==>> [D][05:18:25][COMM]read battery soc:255
[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:54:30:367 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:54:30:374 ==>> 检测【六轴自检】
2025-07-31 20:54:30:380 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:54:30:555 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:26][CAT1]gsm read msg sub id: 12
[D][05:18:26][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:54:31:038 ==>> $GBGGA,125434.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,60,,,41,3,,,41,1*48

$GBGSV,7,2,28,59,,,41,14,,,41,25,,,41,39,,,40,1*7C

$GBGSV,7,3,28,40,,,39,41,,,39,1,,,38,16,,,38,1*4F

$GBGSV,7,4,28,42,,,38,2,,,37,7,,,37,13,,,37,1*71

$GBGSV,7,5,28,6,,,37,44,,,36,9,,,36,10,,,35,1*72

$GBGSV,7,6,28,38,,,35,34,,,35,4,,,35,5,,,34,1*71

$GBGSV,7,7,28,8,,,34,23,,,34,26,,,33,12,,,32,1*43

$GBRMC,125434.511,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125434.511,0.000,775.153,775.153,708.895,2097152,2097152,2097152*6A



2025-07-31 20:54:31:726 ==>> $GBGGA,125435.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,60,,,41,3,,,41,1*48

$GBGSV,7,2,28,59,,,41,25,,,41,39,,,40,14,,,40,1*7D

$GBGSV,7,3,28,40,,,39,41,,,39,1,,,38,16,,,38,1*4F

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,2,,,36,1*70

$GBGSV,7,5,28,44,,,36,9,,,36,6,,,36,38,,,35,1*79

$GBGSV,7,6,28,34,,,35,10,,,34,5,,,34,8,,,34,1*77

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,32,1*4F

$GBRMC,125435.511,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125435.511,0.000,771.462,771.462,705.519,2097152,2097152,2097152*6F



2025-07-31 20:54:32:176 ==>> [D][05:18:27][COMM]read battery soc:255


2025-07-31 20:54:32:266 ==>> [D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:54:32:416 ==>> [D][05:18:27][COMM]Main Task receive event:142
[D][05:18:27][COMM]###### 38913 imu self test OK ######
[D][05:18:27][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-19,-11,4082]
[D][05:18:27][COMM]Main Task receive event:142 finished processing


2025-07-31 20:54:32:456 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:54:32:460 ==>> 检测【打印IMU STATE2】
2025-07-31 20:54:32:464 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:54:32:762 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:28][COMM]YAW data: 32763[32763]
[D][05:18:28][COMM]pitch:-66 roll:0
[D][05:18:28][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB
$GBGGA,125436.511,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,60,,,41,3,,,41,1*48

$GBGSV,7,2,28,59,,,41,25,,,41,39,,,40,14,,,40,1*7D

$GBGSV,7,3,28,40,,,39,41,,,39,1,,,38,16,,,38,1*4F

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,2,,,36,1*70

$GBGSV,7,5,28,44,,,36,9,,,36,6,,,36,10,,,35,1*73

$GBGSV,7,6,28,38,,,35,34,,,35,5,,,34,8,,,34,1*7C

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,32,1*4F

$GBRMC,125436.511,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125436.511,0.000,772.199,772.199,706.193,2097152,2097152,2097152*69



2025-07-31 20:54:32:995 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:54:33:000 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:54:33:004 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:54:33:062 ==>> 5A A5 02 5A A5 


2025-07-31 20:54:33:167 ==>> CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:54:33:272 ==>> [D][05:18:28][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:28][FCTY]g

2025-07-31 20:54:33:280 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:54:33:285 ==>> 检测【检测VBUS电压2】
2025-07-31 20:54:33:294 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:54:33:317 ==>> et_ext_48v_vol retry i = 3,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:28][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:54:33:635 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = 460130071539186
[D][05:18:28][FCTY]HardwareID  = 867222087756849
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = F4B99F7434DC
[D][05:18:28][FCTY]Bat         = 3944 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 11800 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 9, adc = 361
[D][05:18:28][FCTY]Acckey1 vol = 5572 mv, Acckey2 vol = 252 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_RTK = 1.2.

2025-07-31 20:54:33:740 ==>> 4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3791 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:54:33:825 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:54:34:226 ==>> [W][05:18:29][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:29][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========
[D][05:18:29][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:29][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:29][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:29][FCTY]DeviceID    = 460130071539186
[D][05:18:29][FCTY]HardwareID  = 867222087756849
[D][05:18:29][FCTY]MoBikeID    = 9999999999
[D][05:18:29][FCTY]LockID      = FFFFFFFFFF
[D][05:18:29][FCTY]BLEFWVersion= 105
[D][05:18:29][FCTY]BLEMacAddr   = F4B99F7434DC
[D][05:18:29][FCTY]Bat         = 3944 mv
[D][05:18:29][FCTY]Current     = 0 ma
[D][05:18:29][FCTY]VBUS        = 11800 mv
[D][05:18:29][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:29][FCTY]Ext battery vol = 4, adc = 184
[D][05:18:29][FCTY]Acckey1 vol = 5554 mv, Acckey2 vol = 126 mv
[D][05:18:29][FCTY]Bike Type flag is invalied
[D][05:18:29][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:29][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:29][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:29][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:29][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:29][FCTY]Bat1        

2025-07-31 20:54:34:256 ==>>  = 3791 mv
[D][05:18:29][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:29][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:54:34:373 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:54:34:802 ==>> [D][05:18:30][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:30][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:30][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:30][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:30][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:30][FCTY]DeviceID    = 460130071539186
[D][05:18:30][FCTY]HardwareID  = 867222087756849
[D][05:18:30][FCTY]MoBikeID    = 9999999999
[D][05:18:30][FCTY]LockID      = FFFFFFFFFF
[D][05:18:30][FCTY]BLEFWVersion= 105
[D][05:18:30][FCTY]BLEMacAddr   = F4B99F7434DC
[D][05:18:30][FCTY]Bat         = 3944 mv
[D][05:18:30][FCTY]Current     = 0 ma
[D][05:18:30][FCTY]VBUS        = 5000 mv
[D][05:18:30][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:30][FCTY]Ext battery vol = 4, adc = 162
[D][05:18:30][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 75 mv
[D][05:18:30][FCTY]Bike Type flag is invalied
[D][05:18:30][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:30][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:30][FCTY]CAT1_KERNEL_RTK = 1.2.4


2025-07-31 20:54:34:907 ==>> [D][05:18:30][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:30][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:30][FCTY]Bat1         = 3791 mv
[D][05:18:30][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:30][FCTY]==========Modules-nRF5340 ==========
[D][05:18:30][COMM]msg 0601 loss. last_tick:36145. cur_tick:41154. period:500
[D][05:18:30][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 41154
$GBGGA,125438.511,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,24,,,42,60,,,41,3,,,41,1*48

$GBGSV,7,2,28,59,,,41,14,,,41,25,,,41,39,,,40,1*7C

$GBGSV,7,3,28,40,,,39,1,,,39,42,,,39,41,,,39,1*4E

$GBGSV,7,4,28,16,,,38,7,,,37,13,,,37,6,,,37,1*74

$GBGSV,7,5,28,2,,,36,44,,,36,9,,,36,34,,,36,1*72

$GBGSV,7,6,28,10,,,35,38,,,35,5,,,34,8,,,34,1*7A

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,31,1*4C

$GBRMC,125438.511,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125438.511,0.000,775.159,775.159,708.901,2097152,2097152,2097152*6A



2025-07-31 20:54:34:920 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:54:34:924 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:54:34:931 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:54:35:059 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:54:35:211 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:54:35:216 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:54:35:221 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:54:35:256 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:54:35:497 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:54:35:506 ==>> 检测【打开WIFI(3)】
2025-07-31 20:54:35:513 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:54:35:586 ==>> [D][05:18:30][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:30][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:18:30][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 29
[D][05:18:30][COMM]read battery soc:255
[D][05:18:30][COMM]Main Task receive event:65
[D][05:18:30][COMM]main task tmp_sleep_event = 80
[D][05:18:30][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:30][COMM]Main Task receive event:65 finished processing
[D][05:18:30][COMM]Main Task receive event:60
[D][05:18:30][COMM]smart_helmet_vol=255,255
[D][05:18:30][COMM]BAT CAN get state1 Fail 204
[D][05:18:30][COMM]BAT CAN get soc Fail, 204
[D][05:18:30][COMM]report elecbike
[W][05:18:30][PROT]remove success[1629955110],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:30][PROT]add success [1629955110],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:30][COMM]Main Task receive event:60 finished processing
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]index:0
[D][05:18:30][PROT]is_send:1
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x3
[D][05:18:30][PROT]msg_type:0x5d03
[D]

2025-07-31 20:54:35:691 ==>> [05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]Sending traceid[9999999999900005]
[D][05:18:30][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:30][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:30][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:30][PROT]index:0 1629955110
[D][05:18:30][PROT]is_send:0
[D][05:18:30][PROT]sequence_num:4
[D][05:18:30][PROT]retry_timeout:0
[D][05:18:30][PROT]retry_times:3
[D][05:18:30][PROT]send_path:0x2
[D][05:18:30][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:30][PROT]===========================================================
[W][05:18:30][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955110]
[D][05:18:30][PROT]===========================================================
[D][05:18:30][PROT]sending traceid [9999999999900005]
[D][05:18:30][PROT]Send_TO_M2M [1629955110]
[D][05:18:30][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:30][M2M ]

2025-07-31 20:54:35:796 ==>> m2m_task: gpc:[0],gpo:[1]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:30][SAL ]sock send credit cnt[6]
[D][05:18:30][SAL ]sock send ind credit cnt[6]
[D][05:18:30][M2M ]m2m send data len[198]
[D][05:18:30][SAL ]Cellular task submsg id[10]
[D][05:18:30][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:30][CAT1]gsm read msg sub id: 15
[D][05:18:30][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:30][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B526C9699878888AB89D5E8D8310E79DB9D7EFAEC4C4AAA08244BA9C97127B1154A93D6E2C1C0BC6F873866BE83D24C7EC7AD42BA3FA67DCE1609B51558F57C16A562158AB1A1CCA58B1EF438A38887C738FB3
[D][05:18:30][CAT1]<<< 
SEND OK

[D][05:18:30][CAT1]exec over: func id: 15, ret: 11
[D][05:18:30][CAT1]sub id: 15, ret: 11

[D][05:18:30][SAL ]Cellular task submsg id[68]
[D][05:18:30][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:30][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:30][M2M ]g_m2m_is_idle become true
[D][05:18:30][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:30][PROT]M2M Send ok [1629955110]


2025-07-31 20:54:35:902 ==>>                                                                          

2025-07-31 20:54:35:992 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             [W][05:18:31][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:31][CAT1]gsm read msg sub id: 12
[D][05:18:31][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:31][CAT1]<<< 
OK

[D][05:18:31][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:54:36:191 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:54:36:200 ==>> 检测【扩展芯片hw】
2025-07-31 20:54:36:222 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:54:36:359 ==>> [W][05:18:31][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:31][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 20:54:36:553 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:54:36:558 ==>> 检测【扩展芯片boot】
2025-07-31 20:54:36:671 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:54:36:677 ==>> 检测【扩展芯片sw】
2025-07-31 20:54:36:736 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:54:36:749 ==>> 检测【检测音频FLASH】
2025-07-31 20:54:36:757 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:54:36:800 ==>> $GBGGA,125440.511,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,24,,,41,1*4B

$GBGSV,7,2,28,25,,,41,59,,,40,39,,,40,14,,,40,1*7C

$GBGSV,7,3,28,40,,,39,41,,,39,1,,,38,16,,,38,1*4F

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,9,,,37,1*7A

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,38,,,35,1*72

$GBGSV,7,6,28,34,,,35,10,,,34,5,,,34,8,,,34,1*77

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,31,1*4C

$GBRMC,125440.511,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125440.511,0.000,769.983,769.983,704.167,2097152,2097152,2097152*61

[D][05:18:32][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:32][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:0------------
[D][05:18:32][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:54:37:471 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:32][COMM]----- get Acckey 1 and value:1------------
[D][05:18:32][COMM]----- get Acckey 2 and value:1------------
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:32][COMM]file:B50 exist
[D][05:18:32][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:18:32][COMM]read file, len:10800, num:3
[D][05:18:32][

2025-07-31 20:54:37:575 ==>> COMM]Main Task receive event:65
[D][05:18:32][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:32][COMM]Main Task receive event:65 finished processing
[D][05:18:32][COMM]Main Task receive event:66
[D][05:18:32][COMM]Try to Auto Lock Bat
[D][05:18:32][COMM]Main Task receive event:66 finished processing
[D][05:18:32][COMM]Main Task receive event:60
[D][05:18:32][COMM]Receive Bat Lock cmd 0
[D][05:18:32][COMM]VBUS is 1
[D][05:18:32][COMM]smart_helmet_vol=255,255
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get soc error
[E][05:18:32][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:32][COMM]report elecbike
[W][05:18:32][PROT]remove success[1629955112],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:32][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[D][05:18:32][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:32][PROT]index:1
[D][05:18:32][PROT]is_send:1
[D][05:18:32][PROT]sequence_num:5
[D][05:18:32][PROT]retry_timeout:0
[D][05:18:32][PROT]retry_times:3
[D][05:18:32][PROT]send_path:0x3
[D][05:18:32][PROT]msg_type:0x5d03
[D][05:18:32

2025-07-31 20:54:37:680 ==>> ][PROT]===========================================================
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:32][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955112]
[D][05:18:32][PROT]===========================================================
[D][05:18:32][PROT]Sending traceid[9999999999900006]
[D][05:18:32][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:32][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:32][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:32][PROT]add success [1629955112],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:32][COMM]--->crc16:0xb8a
[D][05:18:32][COMM]read file success
[W][05:18:32][COMM][Audio].l:[936].close hexlog save
[D][05:18:32][COMM]accel parse set 1
[D][05:18:32][COMM][Audio]mon:9,05:18:32
[D][05:18:32][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18

2025-07-31 20:54:37:785 ==>> :32][COMM]Main Task receive event:60 finished processing
[D][05:18:32][COMM]Main Task receive event:61
[D][05:18:32][COMM][D301]:type:3, trace id:280
[D][05:18:32][COMM]id[], hw[000
[D][05:18:32][COMM]get mcMaincircuitVolt error
[D][05:18:32][COMM]get mcSubcircuitVolt error
[D][05:18:32][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:32][COMM]BAT CAN get state1 Fail 204
[D][05:18:32][COMM]BAT CAN get soc Fail, 204
[D][05:18:32][COMM]get bat work state err
[W][05:18:32][PROT]remove success[1629955112],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:32][PROT]add success [1629955112],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:32][COMM]Main Task receive event:61 finished processing
[D][05:18:32][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:32][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:54:37:890 ==>> 
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
+WIFISCAN:4,0,CC057790A621,-54
+WIFISCAN:4,1,F42A7D1297A3,-72
+WIFISCAN:4,2,CC057790A5C1,-82
+WIFISCAN:4,3,CC057790A5C0,-83

[D][05:18:32][CAT1]wifi scan report total[4]
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32

2025-07-31 20:54:37:995 ==>> ][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:18:32][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:32][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:32][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[D][05:18:32][COMM]read battery soc:255
[D][05:18:32][GNSS]recv submsg id[3]
                                                                                                                                                                                                                                  

2025-07-31 20:54:38:055 ==>>                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:54:38:744 ==>> $GBGGA,125442.511,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,14,,,40,40,,,39,1*77

$GBGSV,7,3,28,39,,,39,41,,,39,1,,,38,16,,,38,1*41

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,9,,,37,1*7A

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,38,,,35,1*72

$GBGSV,7,6,28,34,,,35,10,,,34,5,,,34,8,,,34,1*77

$GBGSV,7,7,28,4,,,34,23,,,34,26,,,33,12,,,31,1*4C

$GBRMC,125442.511,V,,,,,,,310725,0.1,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125442.511,0.000,769.983,769.983,704.167,2097152,2097152,2097152*63



2025-07-31 20:54:39:215 ==>> [D][05:18:34][COMM]read battery soc:255


2025-07-31 20:54:39:768 ==>> $GBGGA,125443.511,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,28,33,,,43,60,,,41,3,,,41,59,,,41,1*41

$GBGSV,7,2,28,24,,,41,25,,,41,14,,,40,40,,,39,1*77

$GBGSV,7,3,28,39,,,39,41,,,39,1,,,38,16,,,38,1*41

$GBGSV,7,4,28,42,,,38,7,,,37,13,,,37,9,,,37,1*7A

$GBGSV,7,5,28,2,,,36,44,,,36,6,,,36,38,,,35,1*72

$GBGSV,7,6,28,34,,,35,10,,,34,8,,,34,4,,,34,1*76

$GBGSV,7,7,28,23,,,34,26,,,33,5,,,33,12,,,31,1*4A

$GBRMC,125443.511,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,125443.511,0.000,769.247,769.247,703.494,2097152,2097152,2097152*6C

                                                            

2025-07-31 20:54:40:675 ==>> [D][05:18:35][PROT]CLEAN,SEND:0
[D][05:18:35][PROT]index:1 1629955115
[D][05:18:35][PROT]is_send:0
[D][05:18:35][PROT]sequence_num:5
[D][05:18:35][PROT]retry_timeout:0
[D][05:18:35][PROT]retry_times:3
[D][05:18:35][PROT]send_path:0x2
[D][05:18:35][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:35][PROT]===========================================================
[W][05:18:35][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955115]
[D][05:18:35][PROT]===========================================================
[D][05:18:35][PROT]sending traceid [9999999999900006]
[D][05:18:35][PROT]Send_TO_M2M [1629955115]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:35][SAL ]sock send credit cnt[6]
[D][05:18:35][SAL ]sock send ind credit cnt[6]
[D][05:18:35][M2M ]m2m send data len[198]
[D][05:18:35][SAL ]Cellular task submsg id[10]
[D][05:18:35][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:35][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:35][CAT1]gsm read msg sub id: 15
[D][05:18:35][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:36][CAT1]Send Data To S

2025-07-31 20:54:40:750 ==>> erver[198][201] ... ->:
0063B98D113311331133113311331B88B381129AEB556D0F20ED533989D9FBE7B95E2AC3C9CFAF78479B196E7B31D27BB070C66C7044C2ABFF70AFEDBD70F4CB563290E13020000D69E828681828C822AB0FEDAD191249E9C493CEBC0B437BF01339A6
[D][05:18:36][CAT1]<<< 
SEND OK

[D][05:18:36][CAT1]exec over: func id: 15, ret: 11
[D][05:18:36][CAT1]sub id: 15, ret: 11

[D][05:18:36][SAL ]Cellular task submsg id[68]
[D][05:18:36][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:36][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:36][M2M ]g_m2m_is_idle become true
[D][05:18:36][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:36][PROT]M2M Send ok [1629955116]


2025-07-31 20:54:40:855 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:54:40:930 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:54:40:960 ==>>                                                                

2025-07-31 20:54:41:215 ==>> [D][05:18:36][COMM]read battery soc:255


2025-07-31 20:54:42:400 ==>> $GBGGA,125441.518,2301.2581323,N,11421.9411861,E,1,09,1.13,73.386,M,-1.770,M,,*53

$GBGSA,A,3,33,39,09,24,25,40,13,42,41,,,,3.00,1.13,2.78,4*02

$GBGSV,7,1,28,33,68,256,44,3,61,190,41,14,59,189,40,16,55,28,38,1*77

$GBGSV,7,2,28,6,54,41,37,59,52,129,41,39,52,11,40,1,48,126,39,1*70

$GBGSV,7,3,28,9,47,325,37,24,46,21,42,2,46,238,36,7,45,191,37,1*74

$GBGSV,7,4,28,25,44,293,42,60,41,238,41,40,39,160,39,13,36,218,37,1*72

$GBGSV,7,5,28,42,34,166,39,10,34,199,35,4,32,112,35,26,32,242,33,1*45

$GBGSV,7,6,28,41,29,316,39,5,22,257,34,38,19,199,35,8,18,203,34,1*7C

$GBGSV,7,7,28,44,,,36,34,,,35,23,,,34,12,,,31,1*7F

$GBGSV,2,1,05,33,68,256,42,24,46,21,41,25,44,293,40,42,34,166,36,5*41

$GBGSV,2,2,05,41,29,316,36,5*48

$GBRMC,125441.518,A,2301.2581323,N,11421.9411861,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

[D][05:18:37][GNSS]HD8040 GPS
[D][05:18:37][GNSS]GPS diff_sec 124011364, report 0x42 frame
$GBGST,125441.518,0.995,0.253,0.223,0.387,1.471,2.101,6.661*76

[D][05:18:37][COMM]48301 imu init OK
[D][05:18:37][COMM]Main Task receive event:131
[D][05:18:37][COMM]index:0,power_mode:0xFF
[D][05:18:37][COMM]index:1,sound_mode:0xFF


2025-07-31 20:54:42:505 ==>> 
[D][05:18:37][COMM]index:2,gsensor_mode:0xFF
[D][05:18:37][COMM]index:3,report_freq_mode:0xFF
[D][05:18:37][COMM]index:4,report_period:0xFF
[D][05:18:37][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:37][COMM]index:6,normal_reset_period:0xFF
[D][05:18:37][COMM]index:7,spock_over_speed:0xFF
[D][05:18:37][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:37][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:37][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:37][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:37][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:37][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:37][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:37][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:37][COMM]index:16,imu_config_params:0xFF
[D][05:18:37][COMM]index:17,long_connect_params:0xFF
[D][05:18:37][COMM]index:18,detain_mark:0xFF
[D][05:18:37][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:37][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:37][COMM]index:21,mc_mode:0xFF
[D][05:18:37][COMM]index:22,S_mode:0xFF
[D][05:18:37][COMM]index:23,overweight:0xFF
[D][05:18:37][COMM]index:24,standstill_mode:0xFF
[D][05:18:37][COMM]index:25,night_m

2025-07-31 20:54:42:610 ==>> ode:0xFF
[D][05:18:37][COMM]index:26,experiment1:0xFF
[D][05:18:37][COMM]index:27,experiment2:0xFF
[D][05:18:37][COMM]index:28,experiment3:0xFF
[D][05:18:37][COMM]index:29,experiment4:0xFF
[D][05:18:37][COMM]index:30,night_mode_start:0xFF
[D][05:18:37][COMM]index:31,night_mode_end:0xFF
[D][05:18:37][COMM]index:33,park_report_minutes:0xFF
[D][05:18:37][COMM]index:34,park_report_mode:0xFF
[D][05:18:37][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:37][COMM]index:38,charge_battery_para: FF
[D][05:18:37][COMM]index:39,multirider_mode:0xFF
[D][05:18:37][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:37][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:37][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:37][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:37][COMM]index:44,riding_duration_config:0xFF
[D][05:18:37][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:37][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:37][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:37][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:37][COMM]index:49,mc_load_startup:0xFF
[D][05:18:37][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:37][COMM]index:51,traffic_audio_play:0xF

2025-07-31 20:54:42:715 ==>> F
[D][05:18:37][COMM]index:52,traffic_mode:0xFF
[D][05:18:37][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:37][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:37][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:37][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:18:37][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:37][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:37][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:37][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:37][COMM]index:63,experiment5:0xFF
[D][05:18:37][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:37][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:37][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:37][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:37][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:37][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:37][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:37][COMM]index:72,experiment6:0xFF
[D][05:18:37][COMM]index:73,experiment7:0xFF
[D][05:18:37][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:37][COMM]index:75,zero_value_from_server:-1
[D][05:18:37][COMM]index:76,multir

2025-07-31 20:54:42:820 ==>> ider_threshold:255
[D][05:18:37][COMM]index:77,experiment8:255
[D][05:18:37][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:37][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:37][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:18:37][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:37][COMM]index:83,loc_report_interval:255
[D][05:18:37][COMM]index:84,multirider_threshold_p2:255
[D][05:18:37][COMM]index:85,multirider_strategy:255
[D][05:18:37][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:37][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:37][COMM]index:90,weight_param:0xFF
[D][05:18:37][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:37][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:37][COMM]index:95,current_limit:0xFF
[D][05:18:37][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:37][COMM]index:100,location_mode:0xFF

[W][05:18:37][PROT]remove success[1629955117],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:18:37][PROT]add success [1629955117],send_path[2],type[4205],priority[0],index[3],used[1]
[D][05:18:37][COMM]Main Task receive event:131 finished

2025-07-31 20:54:42:925 ==>>  processing
[D][05:18:37][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:37][M2M ]m2m_task: gpc:[0],gpo:[1]
$GBGGA,125442.018,2301.2581821,N,11421.9412425,E,1,14,0.81,73.847,M,-1.770,M,,*53

$GBGSA,A,3,33,14,39,59,09,24,25,60,40,13,42,04,2.13,0.81,1.97,4*07

$GBGSA,A,3,41,38,,,,,,,,,,,2.13,0.81,1.97,4*00

$GBGSV,7,1,28,33,68,256,43,3,61,190,41,14,59,189,41,16,55,28,38,1*71

$GBGSV,7,2,28,6,54,41,37,39,52,11,40,59,50,128,41,1,48,126,39,1*73

$GBGSV,7,3,28,9,47,325,37,24,46,21,42,2,46,238,36,7,45,191,37,1*74

$GBGSV,7,4,28,25,44,293,42,60,43,241,41,40,39,160,39,13,36,218,37,1*7E

$GBGSV,7,5,28,42,34,166,39,10,34,199,35,26,32,242,33,4,31,113,34,1*46

$GBGSV,7,6,28,41,29,316,39,38,24,192,35,5,22,257,34,8,18,203,34,1*79

$GBGSV,7,7,28,44,18,93,36,34,,,36,23,,,34,12,,,31,1*7F

$GBGSV,2,1,07,33,68,256,42,39,52,11,40,24,46,21,41,25,44,293,40,5*7F

$GBGSV,2,2,07,40,39,160,39,42,34,166,37,41,29,316,37,5*4C

$GBRMC,125442.018,A,2301.2581821,N,11421.9412425,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125442.018,1.411,0.471,0.437,0.752,1.373,1.696,4.930*7F



2025-07-31 20:54:43:360 ==>> $GBGGA,125443.000,2301.2582779,N,11421.9412567,E,1,15,0.74,74.940,M,-1.770,M,,*57

$GBGSA,A,3,33,14,39,59,09,24,25,60,40,13,42,04,1.84,0.74,1.69,4*01

$GBGSA,A,3,41,38,44,,,,,,,,,,1.84,0.74,1.69,4*06

$GBGSV,7,1,28,33,68,256,43,3,61,190,41,14,59,189,41,16,55,28,38,1*71

$GBGSV,7,2,28,6,54,41,37,39,52,11,40,59,50,128,41,1,48,126,38,1*72

$GBGSV,7,3,28,9,47,325,37,24,46,21,42,2,46,238,36,7,45,191,37,1*74

$GBGSV,7,4,28,25,44,293,41,60,43,241,41,40,39,160,39,13,36,218,37,1*7D

$GBGSV,7,5,28,42,34,166,39,10,34,199,35,26,32,242,33,4,31,113,34,1*46

$GBGSV,7,6,28,41,29,316,39,38,24,192,35,5,22,257,34,8,18,203,34,1*79

$GBGSV,7,7,28,44,18,93,36,34,18,147,35,23,,,34,12,,,31,1*47

$GBGSV,2,1,07,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7F

$GBGSV,2,2,07,40,39,160,39,42,34,166,38,41,29,316,37,5*43

$GBRMC,125443.000,A,2301.2582779,N,11421.9412567,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125443.000,1.217,0.199,0.194,0.308,1.094,1.353,4.017*79

[D][05:18:38][COMM]read battery soc:255


2025-07-31 20:54:44:383 ==>> $GBGGA,125444.000,2301.2582649,N,11421.9412890,E,1,19,0.70,75.544,M,-1.770,M,,*56

$GBGSA,A,3,33,14,39,16,59,09,24,25,60,07,40,13,1.69,0.70,1.54,4*0A

$GBGSA,A,3,42,10,04,41,38,44,34,,,,,,1.69,0.70,1.54,4*0B

$GBGSV,7,1,28,33,68,256,43,3,61,190,41,14,59,189,40,6,54,41,36,1*41

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,1,48,126,38,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,42,2,46,238,36,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,26,32,242,33,4,31,113,35,1*41

$GBGSV,7,6,28,41,29,316,39,38,24,192,35,5,22,257,34,8,18,203,34,1*79

$GBGSV,7,7,28,44,18,93,36,34,18,147,35,12,17,119,31,23,6,258,34,1*71

$GBGSV,3,1,10,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,10,40,39,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*72

$GBGSV,3,3,10,44,18,93,33,34,18,147,30,5*4F

$GBRMC,125444.000,A,2301.2582649,N,11421.9412890,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125444.000,1.469,0.267,0.251,0.399,1.217,1.412,3.583*75



2025-07-31 20:54:45:425 ==>> $GBGGA,125445.000,2301.2582397,N,11421.9413610,E,1,28,0.51,75.792,M,-1.770,M,,*5E

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,36,24,46,21,42,1,46,125,38,25,44,293,41,1*46

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,34,1*7A

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,36,12,17,119,31,26,9,56,34,23,6,258,34,1*4D

$GBGSV,3,1,11,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,11,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*75

$GBGSV,3,3,11,44,18,93,33,34,18,147,30,23,6,258,32,5*47

$GBRMC,125445.000,A,2301.2582397,N,11421.9413610,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125445.000,2.879,0.178,0.178,0.255,2.104,2.225,3.845*7A

[D][05:18:40][COMM]read battery soc:255


2025-07-31 20:54:45:901 ==>> [D][05:18:41][PROT]CLEAN,SEND:1
[D][05:18:41][PROT]index:1 1629955121
[D][05:18:41][PROT]is_send:0
[D][05:18:41][PROT]sequence_num:5
[D][05:18:41][PROT]retry_timeout:0
[D][05:18:41][PROT]retry_times:2
[D][05:18:41][PROT]send_path:0x2
[D][05:18:41][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:41][PROT]===========================================================
[W][05:18:41][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955121]
[D][05:18:41][PROT]===========================================================
[D][05:18:41][PROT]sending traceid [9999999999900006]
[D][05:18:41][PROT]Send_TO_M2M [1629955121]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:41][SAL ]sock send credit cnt[6]
[D][05:18:41][SAL ]sock send ind credit cnt[6]
[D][05:18:41][M2M ]m2m send data len[198]
[D][05:18:41][SAL ]Cellular task submsg id[10]
[D][05:18:41][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:41][CAT1]gsm read msg sub id: 15
[D][05:18:41][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:41][CAT1]Send Data To Server[198][201] ... ->:
0063B98C113311331133113311331B88B3A8E6AE0F2C82

2025-07-31 20:54:45:976 ==>> FBE65ABBF837AD022398A9EBEE0EE5A755F2AFFC949FCF996F3F8A43087231E8D012DD8F7A975FB509954684ACE79CB3C215CDF3AA122D0D4C03E858B396F9DF16B8522EA19D7D64EE4A3223
[D][05:18:41][CAT1]<<< 
SEND OK

[D][05:18:41][CAT1]exec over: func id: 15, ret: 11
[D][05:18:41][CAT1]sub id: 15, ret: 11

[D][05:18:41][SAL ]Cellular task submsg id[68]
[D][05:18:41][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:41][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:41][M2M ]g_m2m_is_idle become true
[D][05:18:41][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:41][PROT]M2M Send ok [1629955121]


2025-07-31 20:54:46:420 ==>> $GBGGA,125446.000,2301.2582292,N,11421.9413710,E,1,28,0.51,76.007,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,41,6,52,347,37,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,36,24,46,21,42,1,46,125,38,25,44,293,42,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,17,119,31,26,9,56,34,23,6,258,34,1*4E

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,9,56,30,23,6,258,31,5*7B

$GBRMC,125446.000,A,2301.2582292,N,11421.9413710,E,0.000,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,125446.000,2.827,0.191,0.191,0.269,2.055,2.158,3.610*7F



2025-07-31 20:54:46:822 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:54:47:025 ==>> [W][05:18:42][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:54:47:400 ==>> $GBGGA,125447.000,2301.2582278,N,11421.9413578,E,1,28,0.51,76.169,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,40,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,17,119,31,26,9,56,34,23,6,258,34,1*4E

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,39,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*77

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,9,56,30,23,6,258,31,5*7C

$GBRMC,125447.000,A,2301.2582278,N,11421.9413578,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125447.000,2.818,0.202,0.202,0.288,2.036,2.126,3.456*71

[D][05:18:42][COMM]re

2025-07-31 20:54:47:430 ==>> ad battery soc:255


2025-07-31 20:54:48:403 ==>> $GBGGA,125448.000,2301.2582248,N,11421.9413600,E,1,28,0.51,76.096,M,-1.770,M,,*51

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,37,1*70

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,17,119,31,26,9,56,34,23,6,258,34,1*4E

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,9,56,30,23,6,258,30,5*7D

$GBRMC,125448.000,A,2301.2582248,N,11421.9413600,E,0.001,0.00,310725,,,A,S*33

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125448.000,2.651,0.218,0.218,0.310,1.927,2.009,3.254*7F



2025-07-31 20:54:49:422 ==>> $GBGGA,125449.000,2301.2582330,N,11421.9413703,E,1,28,0.51,76.006,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,37,1*70

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,35,12,17,119,32,26,9,56,34,23,6,258,34,1*4D

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,9,56,30,23,6,258,30,5*7D

$GBRMC,125449.000,A,2301.2582330,N,11421.9413703,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125449.000,2.568,0.218,0.219,0.312,1.869,1.943,3.112*7A

[D][05:18:44][COMM]read battery soc:255


2025-07-31 20:54:50:404 ==>> $GBGGA,125450.000,2301.2582308,N,11421.9413777,E,1,28,0.51,75.992,M,-1.770,M,,*52

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,37,1*70

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,40,7,42,177,37,40,39,160,39,13,36,218,37,1*42

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,36,12,18,119,32,26,9,56,34,23,6,258,34,1*41

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,9,56,30,23,6,258,31,5*7B

$GBRMC,125450.000,A,2301.2582308,N,11421.9413777,E,0.001,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125450.000,2.561,0.194,0.194,0.279,1.858,1.926,3.026*71



2025-07-31 20:54:51:192 ==>> [D][05:18:46][PROT]CLEAN,SEND:1
[D][05:18:46][PROT]index:1 1629955126
[D][05:18:46][PROT]is_send:0
[D][05:18:46][PROT]sequence_num:5
[D][05:18:46][PROT]retry_timeout:0
[D][05:18:46][PROT]retry_times:1
[D][05:18:46][PROT]send_path:0x2
[D][05:18:46][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:46][PROT]===========================================================
[W][05:18:46][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955126]
[D][05:18:46][PROT]===========================================================
[D][05:18:46][PROT]sending traceid [9999999999900006]
[D][05:18:46][PROT]Send_TO_M2M [1629955126]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:46][SAL ]sock send credit cnt[6]
[D][05:18:46][SAL ]sock send ind credit cnt[6]
[D][05:18:46][M2M ]m2m send data len[198]
[D][05:18:46][SAL ]Cellular task submsg id[10]
[D][05:18:46][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:46][CAT1]gsm read msg sub id: 15
[D][05:18:46][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:46][CAT1]Send Data To Server[198][201] ... ->:
0063B981113311331133113311331B88B34E89F8FCF193ABE7E8877697DBD3E93DBBE8143C7E4F4F52E78B24416C4FAAE010815B1DA10BD5FCFDF8D0DD51785B0E0FC3AF12EF89DDADB72C14AD

2025-07-31 20:54:51:266 ==>> 2BD366039DE78CF9845DF10322259422849789943260
[D][05:18:46][CAT1]<<< 
SEND OK

[D][05:18:46][CAT1]exec over: func id: 15, ret: 11
[D][05:18:46][CAT1]sub id: 15, ret: 11

[D][05:18:46][SAL ]Cellular task submsg id[68]
[D][05:18:46][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:46][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:46][M2M ]g_m2m_is_idle become true
[D][05:18:46][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:46][PROT]M2M Send ok [1629955126]
[D][05:18:46][COMM]crc 108B
[D][05:18:46][COMM]flash test ok


2025-07-31 20:54:51:371 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     

2025-07-31 20:54:51:446 ==>>                                                                                                                                                                                                                                                                                                                                                                

$GBRMC,125451.000,A,2301.2582270,N,11421.9413836,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125451.000,2.678,0.208,0.208,0.294,1.924,1.985,3.021*7C

[D][05:18:46][COMM]read battery soc:255


2025-07-31 20:54:51:891 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:54:51:899 ==>> 检测【打开喇叭声音】
2025-07-31 20:54:51:908 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:54:52:569 ==>> [W][05:18:47][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:47][COMM]file:A20 exist
[D][05:18:47][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:47][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:47][COMM]file:A20 exist
[D][05:18:47][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:47][COMM]read file, len:15228, num:4
[D][05:18:47][COMM]--->crc16:0x419c
[D][05:18:47][COMM]read file success
[W][05:18:47][COMM][Audio].l:[936].close hexlog save
[D][05:18:47][COMM]accel parse set 1
[D][05:18:47][COMM][Audio]mon:9,05:18:47
[D][05:18:47][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:47][

2025-07-31 20:54:52:673 ==>> COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
$GBGGA,125452.000,2301.2582210,N,11421.9413715,E,1,27,0.53,76.080,M,-1.770,M,,*58

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.08,0.53,0.94,4*00

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,38,44,34,1.08,0.53,0.94,4*0F

$GBGSA,A,3,12,26,23,,,,,,,,,,1.08,0.53,0.94,4

2025-07-31 20:54:52:684 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:54:52:691 ==>> 检测【打开大灯控制】
2025-07-31 20:54:52:699 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:54:52:778 ==>> *0C

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*79

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,,38,24,192,36,44,18,93,36,1*75

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,9,56,33,23,6,258,34,1*45

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,39,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,9,56,30,23,6,258,30,5*7D

$GBRMC,125452.000,A,2301.2582210,N,11421.9413715,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125452.000,2.730,0.224,0.221,0.317,1.951,2.007,2.994*79

[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, 

2025-07-31 20:54:52:868 ==>> index:5, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:47][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:47][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:47][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms


2025-07-31 20:54:52:959 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:54:53:229 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:54:53:239 ==>> 检测【关闭仪表供电3】
2025-07-31 20:54:53:261 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:54:53:398 ==>> $GBGGA,125453.000,2301.2582131,N,11421.9413760,E,1,27,0.53,76.151,M,-1.770,M,,*56

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.08,0.53,0.94,4*00

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,38,44,34,1.08,0.53,0.94,4*0F

$GBGSA,A,3,12,26,23,,,,,,,,,,1.08,0.53,0.94,4*0C

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,36,24,46,21,42,1,46,125,38,25,44,293,41,1*46

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,39,160,39,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,,38,24,192,36,44,18,93,36,1*75

$GBGSV,7,7,28,34,18,147,36,12,18,119,32,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,39,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*77

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,8,56,30,23,6,258,30,5*7B

$GBRMC,125453.000,A,2301.2582131,N,11421.9413760,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125453.000,2.698,0.207,0.204,0.291,1.929,1.980,2.932*

2025-07-31 20:54:53:428 ==>> 74

[D][05:18:48][COMM]read battery soc:255


2025-07-31 20:54:53:503 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:49][COMM]set POWER 0


2025-07-31 20:54:53:776 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:54:53:783 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:54:53:792 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:54:53:920 ==>> [W][05:18:49][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:54:54:075 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:54:54:082 ==>> 检测【读大灯电压】
2025-07-31 20:54:54:089 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:54:54:424 ==>> $GBGGA,125454.000,2301.2582178,N,11421.9413775,E,1,26,0.54,76.171,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.09,0.54,0.95,4*00

$GBGSA,A,3,07,40,13,42,10,08,04,41,38,44,34,12,1.09,0.54,0.95,4*0D

$GBGSA,A,3,26,23,,,,,,,,,,,1.09,0.54,0.95,4*08

$GBGSV,7,1,27,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,37,1*7F

$GBGSV,7,2,27,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*77

$GBGSV,7,3,27,9,47,325,37,24,46,21,41,25,44,293,41,60,43,241,41,1*76

$GBGSV,7,4,27,7,42,177,37,40,40,160,39,13,36,218,37,42,34,166,39,1*4B

$GBGSV,7,5,27,10,33,189,34,8,32,207,34,4,31,113,34,41,29,316,39,1*7E

$GBGSV,7,6,27,5,24,258,,38,24,192,36,44,18,93,36,34,18,147,36,1*73

$GBGSV,7,7,27,12,18,119,32,26,8,56,33,23,6,258,34,1*71

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,40,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7E

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,8,56,30,23,6,258,30,5*7B

$GBRMC,125454.000,A,2301.2582178,N,11421.9413775,E,0.002,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125454.000,2.470,0.196,0.193,0.275,1.786,1.836,2.773*77

[W][05:18:49][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:49][COMM]arm

2025-07-31 20:54:54:454 ==>> _hub read adc[5],val[33154]


2025-07-31 20:54:54:620 ==>> 【读大灯电压】通过,【33154mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:54:54:627 ==>> 检测【关闭大灯控制2】
2025-07-31 20:54:54:636 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:54:54:824 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:54:54:914 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:54:54:924 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:54:54:931 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:54:55:159 ==>> [D][05:18:50][COMM]imu_task imu work error:[-1]. goto init
[W][05:18:50][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:50][COMM]arm_hub read adc[5],val[92]


2025-07-31 20:54:55:205 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:54:55:215 ==>> 检测【打开WIFI(4)】
2025-07-31 20:54:55:238 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:54:55:447 ==>> $GBGGA,125455.000,2301.2582227,N,11421.9413956,E,1,27,0.52,76.183,M,-1.770,M,,*51

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.06,0.52,0.92,4*0E

$GBGSA,A,3,07,40,13,42,10,08,04,41,05,38,44,34,1.06,0.52,0.92,4*05

$GBGSA,A,3,12,26,23,,,,,,,,,,1.06,0.52,0.92,4*05

$GBGSV,7,1,27,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,37,1*7F

$GBGSV,7,2,27,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*77

$GBGSV,7,3,27,9,47,325,37,24,46,21,42,25,44,293,41,60,43,241,41,1*75

$GBGSV,7,4,27,7,42,177,37,40,40,160,39,13,36,218,37,42,34,166,39,1*4B

$GBGSV,7,5,27,10,33,189,35,8,32,207,34,4,31,113,34,41,29,316,39,1*7F

$GBGSV,7,6,27,5,24,258,34,38,24,192,36,44,18,93,36,34,18,147,35,1*77

$GBGSV,7,7,27,12,18,119,32,26,8,56,33,23,6,258,34,1*71

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7E

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,8,56,30,23,6,258,30,5*7B

$GBRMC,125455.000,A,2301.2582227,N,11421.9413956,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125455.000,2.421,0.176,0.175,0.246,1.753,1.800,2.710*7C

[D][05:18:50][COMM]read battery soc:255
[W][05:18:50][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05

2025-07-31 20:54:55:492 ==>> :18:50][CAT1]gsm read msg sub id: 12
[D][05:18:50][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:50][CAT1]<<< 
OK

[D][05:18:50][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:54:55:563 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:54:55:570 ==>> 检测【EC800M模组版本】
2025-07-31 20:54:55:579 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:54:55:751 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:51][CAT1]gsm read msg sub id: 12
[D][05:18:51][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 20:54:55:856 ==>> [D][05:18:51][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:51][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:54:56:110 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:54:56:117 ==>> 检测【配置蓝牙地址】
2025-07-31 20:54:56:123 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:54:56:311 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:F4B99F7434DC>】
2025-07-31 20:54:56:508 ==>> [D][05:18:51][COMM]62635 imu init OK
[D][05:18:51][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:51][PROT]CLEAN,SEND:1
[D][05:18:51][PROT]CLEAN:1
[D][05:18:51][PROT]index:0 1629955131
[D][05:18:51][PROT]is_send:0
[D][05:18:51][PROT]sequence_num:4
[D][05:18:51][PROT]retry_timeout:0
[D][05:18:51][PROT]retry_times:2
[D][05:18:51][PROT]send_path:0x2
[D][05:18:51][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:51][PROT]===========================================================
[W][05:18:51][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955131]
[D][05:18:51][PROT]===========================================================
[D][05:18:51][PROT]sending traceid [9999999999900005]
[D][05:18:51][PROT]Send_TO_M2M [1629955131]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:51][SAL ]sock send credit cnt[6]
[D][05:18:51][SAL ]sock send ind credit cnt[6]
[D][05:18:51][M2M ]m2m send data len[198]
[D][05:18:51][SAL ]Cellular task submsg id[10]
[D][05:18:51][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:51][CAT1]gsm read msg sub id: 15
[D][05:18:51][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:51][CAT1]Send Data To Server[198][201] ... 

2025-07-31 20:54:56:613 ==>> ->:
0063B982113311331133113311331B88B5619640A734526662B77DE0DF519D4A7EFFD108039986E5CD043B556784FD525AB864FE07323FD93CCA7C3D507EFAE9F910FF63F517E3A3EC30E8ED7ACBB8AD7C91B206DA0DF2C703C21049271E6F171D549C
[D][05:18:51][CAT1]<<< 
SEND OK

[D][05:18:51][CAT1]exec over: func id: 15, ret: 11
[D][05:18:51][CAT1]sub id: 15, ret: 11

[D][05:18:51][SAL ]Cellular task submsg id[68]
[D][05:18:51][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:51][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:51][M2M ]g_m2m_is_idle become true
[D][05:18:51][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[W][05:18:51][COMM]>>>>>Input command = nRFReset<<<<<
[D][05:18:51][PROT]M2M Send ok [1629955131]
$GBGGA,125456.000,2301.2582200,N,11421.9414084,E,1,28,0.51,76.175,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,36,24,46,21,42,1,46,125,38,25,44,293,41,1*46

$GBGSV,7,4,28

2025-07-31 20:54:56:718 ==>> ,60,43,241,40,7,42,177,37,40,40,160,39,13,36,218,37,1*4C

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,8,56,33,23,6,258,34,1*44

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*79

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,8,56,30,23,6,258,30,5*7B

$GBRMC,125456.000,A,2301.2582200,N,11421.9414084,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125456.000,2.378,0.190,0.190,0.270,1.723,1.768,2.653*72

+WIFISCAN:4,0,F88C21BCF57D,-34
+WIFISCAN:4,1,CC057790A620,-61
+WIFISCAN:4,2,CC057790A621,-61
+WIFISCAN:4,3,F42A7D1297A3,-70

[D][05:18:51][CAT1]wifi scan report total[4]
[D][05:18:51][GNSS]recv submsg id[3]
recv ble 1
recv ble 2
ble set mac ok :f4,b9,9f,74,34,dc
enable filters ret : 0

2025-07-31 20:54:56:906 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:54:56:916 ==>> 检测【BLETEST】
2025-07-31 20:54:56:937 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:54:56:959 ==>> 4A A4 01 A4 4A 


2025-07-31 20:54:57:064 ==>> recv ble 1
recv ble 2
<BSJ*MAC:F4B99F7434DC*RSSI:-24*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9F4B99F7434DC99999

2025-07-31 20:54:57:124 ==>> [D][05:18:52][COMM]63647 imu init OK
[D][05:18:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:54:57:154 ==>> OVER 150


2025-07-31 20:54:57:426 ==>> $GBGGA,125457.000,2301.2582189,N,11421.9414183,E,1,28,0.51,76.186,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,347,36,1*71

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,40,2,48,239,36,1*79

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,40,7,42,177,37,40,40,160,39,13,36,218,37,1*4C

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,8,56,34,23,6,258,34,1*43

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,40,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*79

$GBGSV,3,3,12,44,18,93,33,34,18,147,31,26,8,56,30,23,6,258,30,5*7B

$GBRMC,125457.000,A,2301.2582189,N,11421.9414183,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125457.000,2.657,0.175,0.176,0.251,1.895,1.935,2.778*77

[D][05:18:52][COMM]read battery soc:255


2025-07-31 20:54:57:697 ==>> [D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:53][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:53][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:53][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:53][COMM]accel parse set 0
[D][05:18:53][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:54:57:959 ==>> 【BLETEST】通过,【-24dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:54:57:970 ==>> 该项需要延时执行
2025-07-31 20:54:58:123 ==>> [D][05:18:53][COMM]64658 imu init OK


2025-07-31 20:54:58:426 ==>> $GBGGA,125458.000,2301.2582162,N,11421.9414247,E,1,28,0.51,76.224,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,40,7,42,177,37,40,40,160,39,13,36,218,37,1*4C

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,34,1*7A

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,34,23,6,258,34,1*40

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7E

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,30,5*7C

$GBRMC,125458.000,A,2301.2582162,N,11421.9414247,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125458.000,2.798,0.194,0.194,0.275,1.978,2.016,2.829*7D



2025-07-31 20:54:58:762 ==>> [D][05:18:54][COMM]S->M yaw:INVALID


2025-07-31 20:54:59:430 ==>> $GBGGA,125459.000,2301.2582173,N,11421.9414232,E,1,28,0.51,76.266,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,39,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,39,10,33,189,35,8,32,207,34,4,31,113,35,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*73

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,8,56,34,23,6,258,34,1*43

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,33,5*7F

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,29,23,6,258,30,5*74

$GBRMC,125459.000,A,2301.2582173,N,11421.9414232,E,0.003,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,125459.000,2.847,0.201,0.201,0.285,2.005,2.041,2.835*71

[D][05:18:54][COMM]read battery soc:255


2025-07-31 20:55:00:397 ==>> $GBGGA,125500.000,2301.2582180,N,11421.9414263,E,1,28,0.51,76.256,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,39,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,33,189,35,8,32,207,34,4,31,113,34,1*7B

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,34,23,6,258,34,1*40

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,33,5*7F

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125500.000,A,2301.2582180,N,11421.9414263,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125500.000,2.779,0.192,0.192,0.272,1.96

2025-07-31 20:55:00:427 ==>> 4,1.999,2.781*74



2025-07-31 20:55:00:517 ==>> [D][05:18:56][COMM]M->S yaw:INVALID


2025-07-31 20:55:01:601 ==>> $GBGGA,125501.000,2301.2582226,N,11421.9414278,E,1,28,0.51,76.197,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,35,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,37,1*70

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,8,56,34,23,6,258,34,1*43

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,38,38,24,192,33,5*70

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,29,23,6,258,31,5*75

$GBRMC,125501.000,A,2301.2582226,N,11421.9414278,E,0.002,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125501.000,2.712,0.195,0.195,0.277,1.923,1.956,2.727*71

[D][05:18:56][COMM]read

2025-07-31 20:55:01:706 ==>>  battery soc:255
[D][05:18:56][PROT]CLEAN,SEND:0
[D][05:18:56][PROT]index:0 1629955136
[D][05:18:56][PROT]is_send:0
[D][05:18:56][PROT]sequence_num:4
[D][05:18:56][PROT]retry_timeout:0
[D][05:18:56][PROT]retry_times:1
[D][05:18:56][PROT]send_path:0x2
[D][05:18:56][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:56][PROT]===========================================================
[D][05:18:56][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[W][05:18:56][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955136]
[D][05:18:56][PROT]===========================================================
[D][05:18:56][PROT]sending traceid [9999999999900005]
[D][05:18:56][PROT]Send_TO_M2M [1629955136]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:56][SAL ]sock send credit cnt[6]
[D][05:18:56][SAL ]sock send ind credit cnt[6]
[D][05:18:56][M2M ]m2m send data len[198]
[D][05:18:56][SAL ]Cellular task submsg id[10]
[D][05:18:56][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:56][CAT1]gsm read msg sub id: 15
[D][05:18:56]

2025-07-31 20:55:01:796 ==>> [CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:56][CAT1]Send Data To Server[198][201] ... ->:
0063B98E113311331133113311331B88B59A53B6555124E586EB7514FD164AF4A5088296B0E11C72FDA6BCA624CBB3E9C52CBD1C29AFE324AD9019A0BFC483C8D57CEC8AA3C6E6E5A12336C6BC0C8795D26AF1F358D17746AA7AFC4D80BC5048BDF544
[D][05:18:56][CAT1]<<< 
SEND OK

[D][05:18:56][CAT1]exec over: func id: 15, ret: 11
[D][05:18:56][CAT1]sub id: 15, ret: 11

[D][05:18:56][SAL ]Cellular task submsg id[68]
[D][05:18:56][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:56][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:56][M2M ]g_m2m_is_idle become true
[D][05:18:56][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:56][PROT]M2M Send ok [1629955136]


2025-07-31 20:55:02:428 ==>> $GBGGA,125502.000,2301.2582263,N,11421.9414276,E,1,28,0.51,76.192,M,-1.770,M,,*50

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,32,26,8,56,33,23,6,258,34,1*44

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,38,38,24,192,34,5*77

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125502.000,A,2301.2582263,N,11421.9414276,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125502.000,2.668,0.201,0.201,0.286,1.895,1.927,2.687*71



2025-07-31 20:55:03:446 ==>> $GBGGA,125503.000,2301.2582259,N,11421.9414184,E,1,28,0.51,76.179,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,40,14,59,189,40,6,52,348,36,1*7F

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,42,1,46,125,38,25,44,293,41,1*47

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,40,13,36,218,37,1*43

$GBGSV,7,5,28,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,35,38,24,192,36,44,18,93,37,1*72

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,38,38,24,192,34,5*77

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125503.000,A,2301.2582259,N,11421.9414184,E,0.000,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.000,N,0.001,K,A*2E

$GBGST,125503.000,2.728,0.218,0.218,0.310,1.931,1.961,2.705*7D

[D][05:18:58][COMM]read battery soc:255


2025-07-31 20:55:04:410 ==>> $GBGGA,125504.000,2301.2582284,N,11421.9414216,E,1,28,0.51,76.153,M,-1.770,M,,*54

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,39,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*78

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125504.000,A,2301.2582284,N,11421.9414216,E,0.002,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,125504.000,2.537,0.203,0.203,0.288,1.813,1.843,2.586*7F



2025-07-31 20:55:05:497 ==>> [D][05:19:00][COMM]read battery soc:255
$GBGGA,125505.000,2301.2582306,N,11421.9414241,E,1,28,0.51,76.124,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,39,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,34,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,35,44,18,93,36,1*71

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7A

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,38,38,24,192,34,5*76

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,29,23,6,258,31,5*75

$GBRMC,125505.000,A,2301.2582306,N,11421.9414241,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGS

2025-07-31 20:55:05:527 ==>> T,125505.000,2.627,0.176,0.176,0.249,1.868,1.897,2.624*7F



2025-07-31 20:55:06:429 ==>> $GBGGA,125506.000,2301.2582309,N,11421.9414328,E,1,28,0.51,76.120,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,40,14,59,189,40,6,52,348,37,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*7C

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*79

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125506.000,A,2301.2582309,N,11421.9414328,E,0.002,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125506.000,2.523,0.193,0.194,0.276,1.803,1.831,2.555*74



2025-07-31 20:55:06:840 ==>> [D][05:19:02][PROT]CLEAN,SEND:0
[D][05:19:02][PROT]CLEAN:0
[D][05:19:02][PROT]index:2 1629955142
[D][05:19:02][PROT]is_send:0
[D][05:19:02][PROT]sequence_num:6
[D][05:19:02][PROT]retry_timeout:0
[D][05:19:02][PROT]retry_times:3
[D][05:19:02][PROT]send_path:0x2
[D][05:19:02][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:02][PROT]===========================================================
[W][05:19:02][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955142]
[D][05:19:02][PROT]===========================================================
[D][05:19:02][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:19:02][PROT]sending traceid [9999999999900007]
[D][05:19:02][PROT]Send_TO_M2M [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[134]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:19:02][M2M ]m

2025-07-31 20:55:06:915 ==>> 2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]Send Data To Server[134][137] ... ->:
0043B683113311331133113311331B88BE8EFA8E65D742A035CCA7BAA4E986A3DA24CB0AFC2360C0AB444C3159D3C5FF350187A62E78160517AD58C39BE16A21E046AD
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]


2025-07-31 20:55:07:417 ==>> $GBGGA,125507.000,2301.2582351,N,11421.9414366,E,1,28,0.51,76.113,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,40,14,59,189,40,6,52,348,36,1*7F

$GBGSV,7,2,28,39,52,11,39,16,52,351,38,59,50,128,40,2,48,239,36,1*77

$GBGSV,7,3,28,9,47,325,36,24,46,21,41,1,46,125,38,25,44,293,41,1*45

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*7C

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,33,5*7F

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,30,23,6,258,31,5*7D

$GBRMC,125507.000,A,2301.2582351,N,11421.9414366,E,0.001,0.00,310725,,,A,S*32

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,125507.000,2.630,0.194,0.194,0.276,1.868,

2025-07-31 20:55:07:447 ==>> 1.895,2.605*76

[D][05:19:02][COMM]read battery soc:255


2025-07-31 20:55:07:966 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:55:07:974 ==>> 检测【检测WiFi结果】
2025-07-31 20:55:07:997 ==>> WiFi信号:【CC057790A621】,信号值:-61
2025-07-31 20:55:08:004 ==>> WiFi信号:【F42A7D1297A3】,信号值:-69
2025-07-31 20:55:08:012 ==>> WiFi信号:【CC057790A5C0】,信号值:-83
2025-07-31 20:55:08:027 ==>> WiFi信号:【CC057790A5C1】,信号值:-84
2025-07-31 20:55:08:035 ==>> WiFi信号:【F88C21BCF57D】,信号值:-34
2025-07-31 20:55:08:045 ==>> WiFi信号:【CC057790A620】,信号值:-61
2025-07-31 20:55:08:062 ==>> WiFi数量【6】, 最大信号值:-34
2025-07-31 20:55:08:075 ==>> 检测【检测GPS结果】
2025-07-31 20:55:08:108 ==>> 符合定位需求的卫星数量:【23】
2025-07-31 20:55:08:123 ==>> 
北斗星号:【33】,信号值:【42】
北斗星号:【3】,信号值:【41】
北斗星号:【14】,信号值:【40】
北斗星号:【16】,信号值:【38】
北斗星号:【6】,信号值:【37】
北斗星号:【59】,信号值:【41】
北斗星号:【39】,信号值:【40】
北斗星号:【1】,信号值:【39】
北斗星号:【9】,信号值:【37】
北斗星号:【24】,信号值:【41】
北斗星号:【2】,信号值:【36】
北斗星号:【7】,信号值:【37】
北斗星号:【25】,信号值:【40】
北斗星号:【60】,信号值:【41】
北斗星号:【40】,信号值:【39】
北斗星号:【13】,信号值:【37】
北斗星号:【42】,信号值:【36】
北斗星号:【10】,信号值:【35】
北斗星号:【4】,信号值:【35】
北斗星号:【41】,信号值:【36】
北斗星号:【38】,信号值:【35】
北斗星号:【44】,信号值:【36】
北斗星号:【34】,信号值:【35】

2025-07-31 20:55:08:140 ==>> 检测【CSQ强度】
2025-07-31 20:55:08:148 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:55:08:171 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+CSQ<<<<<
[D][05:19:03][CAT1]gsm read msg sub id: 12
[D][05:19:03][CAT1]SEND RAW data >>> AT+CSQ



2025-07-31 20:55:08:402 ==>> $GBGGA,125508.000,2301.2582328,N,11421.9414396,E,1,28,0.51,76.094,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,40,14,59,189,40,6,52,348,36,1*7F

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,40,2,48,239,36,1*79

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,41,1*44

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,34,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,36,38,24,192,36,44,18,93,36,1*70

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,34,5*79

$GBGSV,3,3,12,44,18,93,34,34,18,147,32,26,8,56,29,23,6,258,31,5*76

$GBRMC,125508.000,A,2301.2582328,N,11421.9414396,E,0.001,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125508.000,2.886,0.192,0.192,0.274,2.021,2.046,2.738*74



2025-07-31 20:55:09:425 ==>> $GBGGA,125509.000,2301.2582247,N,11421.9414327,E,1,28,0.51,76.086,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.04,0.51,0.91,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.04,0.51,0.91,4*06

$GBGSA,A,3,34,12,26,23,,,,,,,,,1.04,0.51,0.91,4*00

$GBGSV,7,1,28,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*7E

$GBGSV,7,2,28,39,52,11,40,16,52,351,38,59,50,128,40,2,48,239,37,1*78

$GBGSV,7,3,28,9,47,325,37,24,46,21,41,1,46,125,38,25,44,293,42,1*47

$GBGSV,7,4,28,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*4D

$GBGSV,7,5,28,42,34,166,38,10,34,189,34,8,32,207,34,4,31,113,34,1*7D

$GBGSV,7,6,28,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*72

$GBGSV,7,7,28,34,18,147,35,12,18,119,31,26,8,56,33,23,6,258,34,1*47

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,38,38,24,192,34,5*77

$GBGSV,3,3,12,44,18,93,34,34,18,147,32,26,8,56,29,23,6,258,31,5*76

$GBRMC,125509.000,A,2301.2582247,N,11421.9414327,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125509.000,2.755,0.211,0.212,0.300,1.943,1.968,2.658*7A

[D][05:19:04][COMM]read battery soc:255


2025-07-31 20:55:10:070 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:55:10:425 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+CSQ<<<<<
$GBGGA,125510.000,2301.2582201,N,11421.9414419,E,1,27,0.52,76.130,M,-1.770,M,,*5C

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.06,0.52,0.92,4*0E

$GBGSA,A,3,07,40,13,42,10,08,04,41,05,38,44,34,1.06,0.52,0.92,4*05

$GBGSA,A,3,12,26,23,,,,,,,,,,1.06,0.52,0.92,4*05

$GBGSV,7,1,27,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*71

$GBGSV,7,2,27,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*77

$GBGSV,7,3,27,9,47,325,37,24,46,21,41,25,44,293,41,60,43,241,41,1*76

$GBGSV,7,4,27,7,42,177,37,40,40,160,39,13,36,218,37,42,34,166,38,1*4A

$GBGSV,7,5,27,10,34,189,35,8,32,207,34,4,31,113,34,41,29,316,39,1*78

$GBGSV,7,6,27,5,24,258,34,38,24,192,36,44,18,93,36,34,18,147,35,1*77

$GBGSV,7,7,27,12,18,119,32,26,8,56,34,23,6,258,34,1*76

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*78

$GBGSV,3,3,12,44,18,93,34,34,18,147,31,26,8,56,29,23,6,258,31,5*75

$GBRMC,125510.000,A,2301.2582201,N,11421.9414419,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125510.000,2.698,0.210,0.209,0.297,1.908,1.933,2.618*73



2025-07-31 20:55:11:420 ==>> [D][05:19:06][CAT1]SEND RAW data timeout
[D][05:19:06][CAT1]exec over: func id: 12, ret: -52
[D][05:19:06][CAT1]gsm read msg sub id: 12
[D][05:19:06][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:06][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 21
$GBGGA,125511.000,2301.2582199,N,11421.9414448,E,1,27,0.52,76.144,M,-1.770,M,,*58

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,25,60,1.06,0.52,0.92,4*0E

$GBGSA,A,3,07,40,13,42,10,08,04,41,05,38,44,34,1.06,0.52,0.92,4*05

$GBGSA,A,3,12,26,23,,,,,,,,,,1.06,0.52,0.92,4*05

$GBGSV,7,1,27,33,68,256,44,3,62,190,41,14,59,189,40,6,52,348,36,1*76

$GBGSV,7,2,27,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*77

$GBGSV,7,3,27,9,47,325,37,24,46,21,42,25,44,293,41,60,43,241,41,1*75

$GBGSV,7,4,27,7,42,177,37,40,40,160,39,13,36,218,37,42,34,166,39,1*4B

$GBGSV,7,5,27,10,34,189,35,8,32,207,34,4,31,113,34,41,29,316,39,1*78

$GBGSV,7,6,27,5,24,258,34,38,24,192,35,44,18,93,36,34,18,147,35,1*74

$GBGSV,7,7,27,12,18,119,32,26,8,56,34,23,6,258,34,1*76

$GBGSV,3,1,12,33,68,256,43,39,52,11,41,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,12,40,40,160,38,42,34,166,39,41,29,316,37,38,24,192,34,5*78

$G

2025-07-31 20:55:11:465 ==>> BGSV,3,3,12,44,18,93,34,34,18,147,32,26,8,56,29,23,6,258,31,5*76

$GBRMC,125511.000,A,2301.2582199,N,11421.9414448,E,0.002,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,125511.000,2.501,0.204,0.203,0.288,1.787,1.811,2.498*72

[D][05:19:06][COMM]read battery soc:255


2025-07-31 20:55:11:665 ==>> 【CSQ强度】通过,【21】符合目标值【18】至【31】要求!
2025-07-31 20:55:11:673 ==>> 检测【关闭GSM联网】
2025-07-31 20:55:11:686 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:55:12:027 ==>> [D][05:19:07][PROT]CLEAN,SEND:2
[W][05:19:07][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:07][COMM]GSM test
[D][05:19:07][COMM]GSM test disable
[D][05:19:07][PROT]index:2 1629955147
[D][05:19:07][PROT]is_send:0
[D][05:19:07][PROT]sequence_num:6
[D][05:19:07][PROT]retry_timeout:0
[D][05:19:07][PROT]retry_times:2
[D][05:19:07][PROT]send_path:0x2
[D][05:19:07][PROT]min_index:2, type:0xD302, priority:0
[D][05:19:07][PROT]===========================================================
[W][05:19:07][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955147]
[D][05:19:07][PROT]===========================================================
[D][05:19:07][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A8C89C8906980220
[D][05:19:07][PROT]sending traceid [9999999999900007]
[D][05:19:07][PROT]Send_TO_M2M [1629955147]
[D][05:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:07][SAL ]sock send credit cnt[6]
[D][05:19:07][SAL ]sock send ind credit cnt[6]
[D][05:19:07][M2M ]m2m send data len[134]
[D][05:19:07][SAL ]Cellular task submsg id[10]
[D][05:19:07][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:19:07][CAT1]gsm read msg sub id: 15
[D][05:19:07][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][0

2025-07-31 20:55:12:057 ==>> 5:19:07][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:07][CAT1]<<< 
ERROR



2025-07-31 20:55:12:239 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:55:12:253 ==>> 检测【4G联网测试】
2025-07-31 20:55:12:281 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:55:12:408 ==>> $GBGGA,125512.000,2301.2582184,N,11421.9414442,E,1,26,0.58,76.103,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,24,01,25,1.34,0.58,1.21,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.34,0.58,1.21,4*06

$GBGSA,A,3,34,12,,,,,,,,,,,1.34,0.58,1.21,4*05

$GBGSV,7,1,26,33,68,256,44,3,62,190,41,14,59,189,40,6,52,348,36,1*77

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,24,46,21,42,1,46,125,38,25,44,293,41,1*49

$GBGSV,7,4,26,60,43,241,41,7,42,177,36,40,40,160,39,13,36,218,37,1*42

$GBGSV,7,5,26,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*7C

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,38,38,24,192,33,5*73

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125512.000,A,2301.2582184,N,11421.9414442,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125512.000,1.722,0.187,0.185,0.281,1.253,1.281,2.004*73



2025-07-31 20:55:13:016 ==>> [W][05:19:08][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:08][COMM]Main Task receive event:14
[D][05:19:08][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955148, allstateRepSeconds = 0
[D][05:19:08][COMM]index:0,power_mode:0xFF
[D][05:19:08][COMM]index:1,sound_mode:0xFF
[D][05:19:08][COMM]index:2,gsensor_mode:0xFF
[D][05:19:08][COMM]index:3,report_freq_mode:0xFF
[D][05:19:08][COMM]index:4,report_period:0xFF
[D][05:19:08][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:08][COMM]index:6,normal_reset_period:0xFF
[D][05:19:08][COMM]index:7,spock_over_speed:0xFF
[D][05:19:08][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:08][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:08][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:08][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:08][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:08][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:08][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:08][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:08][COMM]index:16,imu_config_params:0xFF
[D][05:19:08][COMM]index:17,long_connect_params:0xFF
[D][05:19:08][

2025-07-31 20:55:13:121 ==>> COMM]index:18,detain_mark:0xFF
[D][05:19:08][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:08][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:08][COMM]index:21,mc_mode:0xFF
[D][05:19:08][COMM]index:22,S_mode:0xFF
[D][05:19:08][COMM]index:23,overweight:0xFF
[D][05:19:08][COMM]index:24,standstill_mode:0xFF
[D][05:19:08][COMM]index:25,night_mode:0xFF
[D][05:19:08][COMM]index:26,experiment1:0xFF
[D][05:19:08][COMM]index:27,experiment2:0xFF
[D][05:19:08][COMM]index:28,experiment3:0xFF
[D][05:19:08][COMM]index:29,experiment4:0xFF
[D][05:19:08][COMM]index:30,night_mode_start:0xFF
[D][05:19:08][COMM]index:31,night_mode_end:0xFF
[D][05:19:08][COMM]index:33,park_report_minutes:0xFF
[D][05:19:08][COMM]index:34,park_report_mode:0xFF
[D][05:19:08][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:08][COMM]index:38,charge_battery_para: FF
[D][05:19:08][COMM]index:39,multirider_mode:0xFF
[D][05:19:08][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:08][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:08][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:08][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:08][COMM]index:44,riding_duration_config:0xFF
[D

2025-07-31 20:55:13:226 ==>> ][05:19:08][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:08][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:08][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:08][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:08][COMM]index:49,mc_load_startup:0xFF
[D][05:19:08][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:08][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:08][COMM]index:52,traffic_mode:0xFF
[D][05:19:08][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:08][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:08][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:08][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:08][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:08][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:08][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:08][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:08][COMM]index:63,experiment5:0xFF
[D][05:19:08][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:08][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:08][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:08][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:08][COMM]index:68,camera_park_ps_cfg:0xFFFF


2025-07-31 20:55:13:331 ==>> 
[D][05:19:08][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:08][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:08][COMM]index:72,experiment6:0xFF
[D][05:19:08][COMM]index:73,experiment7:0xFF
[D][05:19:08][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:08][COMM]index:75,zero_value_from_server:-1
[D][05:19:08][COMM]index:76,multirider_threshold:255
[D][05:19:08][COMM]index:77,experiment8:255
[D][05:19:08][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:08][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:08][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:08][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:08][COMM]index:83,loc_report_interval:255
[D][05:19:08][COMM]index:84,multirider_threshold_p2:255
[D][05:19:08][COMM]index:85,multirider_strategy:255
[D][05:19:08][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:08][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:08][COMM]index:90,weight_param:0xFF
[D][05:19:08][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:08][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:08][COMM]index:95,current_limit:0xFF
[D][05:19:08][COMM]index:97

2025-07-31 20:55:13:436 ==>> ,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:08][COMM]index:100,location_mode:0xFF

[W][05:19:08][PROT]remove success[1629955148],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:08][PROT]add success [1629955148],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:08][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:08][M2M ]m2m_task: gpc:[0],gpo:[1]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:55:13:496 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:55:14:401 ==>> $GBGGA,125514.000,2301.2582227,N,11421.9414433,E,1,26,0.58,76.051,M,-1.770,M,,*59

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.34,0.58,1.21,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.34,0.58,1.21,4*06

$GBGSA,A,3,34,12,,,,,,,,,,,1.34,0.58,1.21,4*05

$GBGSV,7,1,26,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*70

$GBGSV,7,2,26,39,52,11,39,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,41,25,44,293,41,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*7C

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,32,5*4A

$GBRMC,125514.000,A,2301.2582227,N,11421.9414433,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125514.000,1.932,0.194,0.192,0.296,1.406,1.432,2.130*76



2025-07-31 20:55:14:584 ==>> [D][05:19:10][M2M ]get csq[-1]


2025-07-31 20:55:14:931 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:10][PROT]remove success[1629955150],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:19:10][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:19:10][PROT]add success [1629955150],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:10][COMM]------>period, report file manifest
[D][05:19:10][COMM]Main Task receive event:14 finished processing
[D][05:19:10][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:10][M2M ]m2m_task: gpc:[0],gpo:[1]


2025-07-31 20:55:15:431 ==>> $GBGGA,125515.000,2301.2582193,N,11421.9414460,E,1,26,0.58,76.089,M,-1.770,M,,*57

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.34,0.58,1.21,4*0B

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.34,0.58,1.21,4*06

$GBGSA,A,3,34,12,,,,,,,,,,,1.34,0.58,1.21,4*05

$GBGSV,7,1,26,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*70

$GBGSV,7,2,26,39,52,11,39,16,52,351,38,59,50,128,41,2,48,239,36,1*78

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,41,25,44,293,41,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*7C

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,32,5*4A

$GBRMC,125515.000,A,2301.2582193,N,11421.9414460,E,0.003,0.00,310725,,,A,S*3E

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,125515.000,2.048,0.183,0.181,0.279,1.487,1.512,2.200*7F

[D][05:19:10][COMM]read battery soc:255


2025-07-31 20:55:16:399 ==>> $GBGGA,125516.000,2301.2582131,N,11421.9414460,E,1,26,0.58,76.097,M,-1.770,M,,*53

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.21,4*0A

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.21,4*07

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.21,4*04

$GBGSV,7,1,26,33,68,256,43,3,62,190,41,14,59,189,40,6,52,348,36,1*70

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*77

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,41,25,44,293,41,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,35,1*73

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*7C

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,256,43,39,52,11,40,24,46,21,41,25,44,293,41,5*78

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,38,38,24,192,34,5*74

$GBGSV,3,3,10,44,18,93,34,34,18,147,32,5*4A

$GBRMC,125516.000,A,2301.2582131,N,11421.9414460,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125516.000,2.095,0.194,0.192,0.299,1.519,1.543,2.225*73



2025-07-31 20:55:17:376 ==>> $GBGGA,125517.000,2301.2582084,N,11421.9414422,E,1,26,0.58,76.069,M,-1.770,M,,*5A

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.21,4*0A

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.21,4*07

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.21,4*04

$GBGSV,7,1,26,33,68,255,44,3,62,190,41,14,59,189,40,6,52,348,37,1*75

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,37,1*77

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,41,25,44,293,41,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,35,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*7D

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,255,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125517.000,A,2301.2582084,N,11421.9414422,E,0.002,0.00,310725,,,A,S*3C

$GBVTG,0.00,T,,M,0.002,N,0.005,K,A*28

$GBGST,125517.000,2.100,0.187,0.185,0.284,1.523,1.546,2.224*7A



2025-07-31 20:55:17:421 ==>>                                          

2025-07-31 20:55:18:276 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:55:18:398 ==>> $GBGGA,125518.000,2301.2582069,N,11421.9414436,E,1,26,0.58,76.090,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.21,4*0A

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.21,4*07

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.21,4*04

$GBGSV,7,1,26,33,68,255,44,3,62,190,41,14,59,189,40,6,52,348,37,1*75

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,42,25,44,293,41,1*49

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*73

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,36,1*7C

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,255,43,39,52,11,40,24,46,21,41,25,44,293,40,5*7A

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125518.000,A,2301.2582069,N,11421.9414436,E,0.002,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,125518.000,2.004,0.181,0.179,0.273,1.456,1.480,2.158*7D



2025-07-31 20:55:18:489 ==>> [W][05:19:14][COMM]>>>>>Input command = AT+ALLSTATE<<<<<


2025-07-31 20:55:19:079 ==>> [D][05:19:14][COMM]Main Task receive event:14
[D][05:19:14][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955154, allstateRepSeconds = 0
[D][05:19:14][COMM]index:0,power_mode:0xFF
[D][05:19:14][COMM]index:1,sound_mode:0xFF
[D][05:19:14][COMM]index:2,gsensor_mode:0xFF
[D][05:19:14][COMM]index:3,report_freq_mode:0xFF
[D][05:19:14][COMM]index:4,report_period:0xFF
[D][05:19:14][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:14][COMM]index:6,normal_reset_period:0xFF
[D][05:19:14][COMM]index:7,spock_over_speed:0xFF
[D][05:19:14][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:14][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:14][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:14][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:14][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:14][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:14][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:14][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:14][COMM]index:16,imu_config_params:0xFF
[D][05:19:14][COMM]index:17,long_connect_params:0xFF
[D][05:19:14][COMM]index:18,detain_mark:0xFF
[D][05:19:14][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19

2025-07-31 20:55:19:184 ==>> :14][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:14][COMM]index:21,mc_mode:0xFF
[D][05:19:14][COMM]index:22,S_mode:0xFF
[D][05:19:14][COMM]index:23,overweight:0xFF
[D][05:19:14][COMM]index:24,standstill_mode:0xFF
[D][05:19:14][COMM]index:25,night_mode:0xFF
[D][05:19:14][COMM]index:26,experiment1:0xFF
[D][05:19:14][COMM]index:27,experiment2:0xFF
[D][05:19:14][COMM]index:28,experiment3:0xFF
[D][05:19:14][COMM]index:29,experiment4:0xFF
[D][05:19:14][COMM]index:30,night_mode_start:0xFF
[D][05:19:14][COMM]index:31,night_mode_end:0xFF
[D][05:19:14][COMM]index:33,park_report_minutes:0xFF
[D][05:19:14][COMM]index:34,park_report_mode:0xFF
[D][05:19:14][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:14][COMM]index:38,charge_battery_para: FF
[D][05:19:14][COMM]index:39,multirider_mode:0xFF
[D][05:19:14][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:14][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:14][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:14][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:14][COMM]index:44,riding_duration_config:0xFF
[D][05:19:14][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:14][COMM]index:46,camera_park_

2025-07-31 20:55:19:289 ==>> type_cfg:0xFF
[D][05:19:14][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:14][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:19:14][COMM]index:49,mc_load_startup:0xFF
[D][05:19:14][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:14][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:14][COMM]index:52,traffic_mode:0xFF
[D][05:19:14][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:14][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:14][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:14][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:14][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:14][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:14][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:14][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:14][COMM]index:63,experiment5:0xFF
[D][05:19:14][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:14][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:14][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:14][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:14][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:14][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:14][COMM]index:71,camera_pa

2025-07-31 20:55:19:394 ==>> rk_self_check_cfg:0xFF
[D][05:19:14][COMM]index:72,experiment6:0xFF
[D][05:19:14][COMM]index:73,experiment7:0xFF
[D][05:19:14][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:14][COMM]index:75,zero_value_from_server:-1
[D][05:19:14][COMM]index:76,multirider_threshold:255
[D][05:19:14][COMM]index:77,experiment8:255
[D][05:19:14][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:14][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:14][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:14][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:14][COMM]index:83,loc_report_interval:255
[D][05:19:14][COMM]index:84,multirider_threshold_p2:255
[D][05:19:14][COMM]index:85,multirider_strategy:255
[D][05:19:14][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:14][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:14][COMM]index:90,weight_param:0xFF
[D][05:19:14][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:14][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:14][COMM]index:95,current_limit:0xFF
[D][05:19:14][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:14][COMM]index:100,location_mode:0x

2025-07-31 20:55:19:499 ==>> FF

[W][05:19:14][PROT]remove success[1629955154],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:19:14][PROT]add success [1629955154],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:14][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:14][M2M ]m2m_task: gpc:[0],gpo:[1]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            

2025-07-31 20:55:19:559 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                              

2025-07-31 20:55:20:380 ==>> $GBGGA,125520.000,2301.2582022,N,11421.9414412,E,1,26,0.58,76.080,M,-1.770,M,,*56

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.22,4*09

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.22,4*04

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.22,4*07

$GBGSV,7,1,26,33,68,255,44,3,62,190,41,14,59,189,40,6,52,348,37,1*75

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,42,25,44,293,42,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*73

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*7D

$GBGSV,7,7,26,34,18,147,35,12,18,119,32,1*7A

$GBGSV,3,1,10,33,68,255,43,39,52,11,40,24,46,21,41,25,44,293,40,5*7A

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125520.000,A,2301.2582022,N,11421.9414412,E,0.001,0.00,310725,,,A,S*34

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125520.000,1.799,0.193,0.191,0.292,1.308,1.332,2.014*77



2025-07-31 20:55:20:635 ==>> [D][05:19:16][M2M ]get csq[-1]


2025-07-31 20:55:20:984 ==>> >>>>>RESEND ALLSTATE<<<<<
[W][05:19:16][PROT]remove success[1629955156],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:16][PROT]add success [1629955156],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:16][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:16][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:16][COMM]------>period, report file manifest, waiting for Verify or count 1 less
[D][05:19:16][COMM][LOC]wifi scan is already running, error
[D][05:19:16][COMM]Main Task receive event:14 finished processing


2025-07-31 20:55:21:407 ==>> $GBGGA,125521.000,2301.2581998,N,11421.9414417,E,1,26,0.58,76.079,M,-1.770,M,,*5F

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.22,4*09

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.22,4*04

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.22,4*07

$GBGSV,7,1,26,33,68,255,44,3,62,190,41,14,59,189,40,6,52,348,37,1*75

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,42,25,44,293,42,1*4A

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,40,13,36,218,37,1*4D

$GBGSV,7,5,26,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,34,1*73

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*7D

$GBGSV,7,7,26,34,18,147,36,12,18,119,32,1*79

$GBGSV,3,1,10,33,68,255,43,39,52,11,40,24,46,21,41,25,44,293,40,5*7A

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125521.000,A,2301.2581998,N,11421.9414417,E,0.001,0.00,310725,,,A,S*3B

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125521.000,1.782,0.169,0.167,0.253,1.295,1.319,1.998*7F



2025-07-31 20:55:21:437 ==>>                                          

2025-07-31 20:55:22:109 ==>> [D][05:19:17][CAT1]exec over: func id: 15, ret: -93
[D][05:19:17][CAT1]sub id: 15, ret: -93

[D][05:19:17][SAL ]Cellular task submsg id[68]
[D][05:19:17][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:17][SAL ]socket send fail. id[4]
[D][05:19:17][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:17][CAT1]gsm read msg sub id: 13
[D][05:19:17][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:17][M2M ]m2m select fd[4]
[D][05:19:17][M2M ]socket[4] Link is disconnected
[D][05:19:17][M2M ]tcpclient close[4]
[D][05:19:17][SAL ]socket[4] has closed
[D][05:19:17][PROT]protocol read data ok
[E][05:19:17][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:17][PROT]M2M Send Fail [1629955157]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:17][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:17][CAT1]exec over: func id: 13, ret: 21
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:17][CAT1]gsm read msg sub id: 21
[D][05:19:17][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]cell info report total[0]
[D][05:19:17][CAT1]exec over: func id: 21, ret: 6
[D][05:19:17][CAT1]gsm read msg sub id: 13
[D][05:19:17][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:17][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:19:17][CA

2025-07-31 20:55:22:154 ==>> T1]exec over: func id: 13, ret: 21
[D][05:19:17][CAT1]gsm read msg sub id: 10
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:55:22:489 ==>> $GBGGA,125522.000,2301.2581975,N,11421.9414415,E,1,26,0.58,76.075,M,-1.770,M,,*51

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.22,4*09

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.22,4*04

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.22,4*07

$GBGSV,7,1,26,33,68,255,44,3,62,190,41,14,59,189,41,6,52,348,37,1*74

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,42,25,44,293,41,1*49

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,39,10,34,189,35,8,32,207,34,4,31,113,35,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*7D

$GBGSV,7,7,26,34,18,147,36,12,18,119,32,1*79

$GBGSV,3,1,10,33,68,255,43,39,52,11,40,24,46,21,41,25,44,293,41,5*7B

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,34,34,18,147,31,5*49

$GBRMC,125522.000,A,2301.2581975,N,11421.9414415,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,125522.000,1.922,0.143,0.142,0.214,1.398,1.420,2.089*7F

[D][05:19:17][CAT1]<<< 
OK

[D][05:19:17][CAT1]exec over: func id: 10, ret: 6
[D][05:19:17][CAT1]sub id: 10, ret: 6

[D][05:19:17][SAL ]Cellular task submsg id[68]
[

2025-07-31 20:55:22:579 ==>> D][05:19:17][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:17][M2M ]m2m gsm shut done, ret[0]
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:55:22:669 ==>> [D][05:19:18][CAT1]pdpdeact urc len[22]


2025-07-31 20:55:23:403 ==>> $GBGGA,125523.000,2301.2581944,N,11421.9414445,E,1,26,0.58,76.088,M,-1.770,M,,*55

$GBGSA,A,3,33,03,14,06,39,16,59,02,09,01,24,25,1.35,0.58,1.22,4*09

$GBGSA,A,3,60,07,40,13,42,10,08,04,41,05,38,44,1.35,0.58,1.22,4*04

$GBGSA,A,3,34,12,,,,,,,,,,,1.35,0.58,1.22,4*07

$GBGSV,7,1,26,33,68,255,43,3,62,190,41,14,59,189,40,6,52,348,37,1*72

$GBGSV,7,2,26,39,52,11,40,16,52,351,38,59,50,128,41,2,48,239,36,1*76

$GBGSV,7,3,26,9,47,325,37,1,46,125,38,24,46,21,42,25,44,293,41,1*49

$GBGSV,7,4,26,60,43,241,41,7,42,177,37,40,40,160,39,13,36,218,37,1*43

$GBGSV,7,5,26,42,34,166,38,10,34,189,35,8,32,207,34,4,31,113,34,1*72

$GBGSV,7,6,26,41,29,316,39,5,24,258,34,38,24,192,36,44,18,93,37,1*7D

$GBGSV,7,7,26,34,18,147,36,12,18,119,32,1*79

$GBGSV,3,1,10,33,68,255,43,39,52,11,41,24,46,21,41,25,44,293,40,5*7B

$GBGSV,3,2,10,40,40,160,38,42,34,166,38,41,29,316,37,38,24,192,33,5*7C

$GBGSV,3,3,10,44,18,93,33,34,18,147,31,5*4E

$GBRMC,125523.000,A,2301.2581944,N,11421.9414445,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,125523.000,1.972,0.210,0.208,0.320,1.433,1.454,2.118*79



2025-07-31 20:55:23:449 ==>>                                          

2025-07-31 20:55:23:783 ==>> [D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:19][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:19][CAT1]tx ret[8] >>> AT+CSQ

[D][05:19:19][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:19:19][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:19:19][CAT1]<<< 
OK

[D][05:19:19][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:55:24:196 ==>> [D][05:19:19][CAT1]opened : 0, 0
[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:19:19][SAL ]socket connect ind. id[4], rst[3]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]index:1 1629955159
[D][05:19:19][PROT]is_send:0
[D][05:19:19][PROT]sequence_num:11
[D][05:19:19][PROT]retry_timeout:0
[D][05:19:19][PROT]retry_times:1
[D][05:19:19][PROT]send_path:0x2
[D][05:19:19][PROT]min_index:1, type:0x5004, priority:2
[D][05:19:19][PROT]===========================================================
[W][05:19:19][PROT]SEND DATA TYPE:5004, SENDPATH:0x2 [1629955159]
[D][05:19:19][PROT]===========================================================
[D][05:19:19][PROT]sending traceid [999999999990000C]
[D][05:19:19][PROT]Send_TO_M2M [1629955159]
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:19][SAL ]sock send credit cnt[6]
[D][05:19:19][SAL ]sock send ind credit cnt[6]
[D][05:19:19][M2M ]m2m send data len[166]
[D]

2025-07-31 20:55:24:301 ==>> [05:19:19][SAL ]Cellular task submsg id[10]
[D][05:19:19][SAL ]cellular SEND socket id[0] type[1], len[166], data[0x20052dd0] format[0]
[D][05:19:19][CAT1]gsm read msg sub id: 15
[D][05:19:19][CAT1]tx ret[17] >>> AT+QISEND=0,166

[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:19][CAT1]Send Data To Server[166][169] ... ->:
0053B986113311331133113311331B88B0976149637EA18598BDE2BE925818DBF637BB9161B1186D89886AE5171951681CDDD3F6D7AA338F9E46FB1FC478829DC5CE59B429B2065BBB9043C8A841B7D6A2825C
[D][05:19:19][CAT1]<<< 
SEND OK

[D][05:19:19][CAT1]exec over: func id: 15, ret: 11
[D][05:19:19][CAT1]sub id: 15, ret: 11

[D][05:19:19][SAL ]Cellular task submsg id[68]
[D][05:19:19][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:19:19][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:19][M2M ]g_m2m_is_idle become true
[D][05:19:19][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:19][PROT]M2M Send ok [1629955159]
                                                                  

2025-07-31 20:55:24:348 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:55:24:358 ==>> 检测【关闭GPS】
2025-07-31 20:55:24:368 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:55:24:406 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               

2025-07-31 20:55:24:695 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]stop event:8
[D][05:19:20][GNSS]GPS stop. ret=0
[D][05:19:20][GNSS]all continue location stop
[D][05:19:20][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]all sing location stop
[D][05:19:20][CAT1]gsm read msg sub id: 24
[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:20][CAT1]<<< 
OK

[D][05:19:20][CAT1]exec over: func id: 24, ret: 6
[D][05:19:20][CAT1]sub id: 24, ret: 6



2025-07-31 20:55:24:891 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:55:24:900 ==>> 检测【清空消息队列2】
2025-07-31 20:55:24:908 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:55:25:046 ==>> [W][05:19:20][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:20][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:55:25:181 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:55:25:190 ==>> 检测【轮动检测】
2025-07-31 20:55:25:205 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:55:25:259 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:55:25:349 ==>> [D][05:19:20][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:55:25:424 ==>> [D][05:19:20][COMM]read battery soc:255


2025-07-31 20:55:25:637 ==>> [D][05:19:21][GNSS]recv submsg id[1]
[D][05:19:21][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:21][GNSS]location stop evt done evt


2025-07-31 20:55:25:682 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:55:25:742 ==>> 3A A3 01 01 A3 


2025-07-31 20:55:25:847 ==>> ON_OUT1
OVER 150


2025-07-31 20:55:25:962 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:55:25:971 ==>> 检测【关闭小电池】
2025-07-31 20:55:25:984 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:55:26:059 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:55:26:252 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:55:26:260 ==>> 检测【进入休眠模式】
2025-07-31 20:55:26:269 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:55:26:426 ==>> [W][05:19:21][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:55:26:531 ==>> [D][05:19:22][COMM]Main Task receive event:28
[D][05:19:22][COMM]main task tmp_sleep_event = 8
[D][05:19:22][COMM]prepare to sleep
[D][05:19:22][CAT1]gsm read msg sub id: 12
[D][05:19:22][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:55:27:326 ==>> [D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 12, ret: 6
[D][05:19:22][M2M ]tcpclient close[4]
[D][05:19:22][SAL ]Cellular task submsg id[12]
[D][05:19:22][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:22][CAT1]gsm read msg sub id: 9
[D][05:19:22][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:22][CAT1]<<< 
OK

[D][05:19:22][CAT1]exec over: func id: 9, ret: 6
[D][05:19:22][CAT1]sub id: 9, ret: 6

[D][05:19:22][SAL ]Cellular task submsg id[68]
[D][05:19:22][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:22][SAL ]socket close ind. id[4]
[D][05:19:22][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:22][COMM]1x1 frm_can_tp_send ok
[D][05:19:22][CAT1]pdpdeact urc len[22]


2025-07-31 20:55:27:416 ==>> [D][05:19:22][COMM]read battery soc:255


2025-07-31 20:55:27:598 ==>> [E][05:19:23][COMM]1x1 rx timeout
[D][05:19:23][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:55:28:115 ==>> [E][05:19:23][COMM]1x1 rx timeout
[E][05:19:23][COMM]1x1 tp timeout
[E][05:19:23][COMM]1x1 error -3.
[W][05:19:23][COMM]CAN STOP!
[D][05:19:23][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:23][COMM]------------ready to Power off Acckey 1------------
[D][05:19:23][COMM]------------ready to Power off Acckey 2------------
[D][05:19:23][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:23][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1304
[D][05:19:23][COMM]bat sleep fail, reason:-1
[D][05:19:23][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:23][COMM]accel parse set 0
[D][05:19:23][COMM]imu rest ok. 94539
[D][05:19:23][COMM]imu sleep 0
[W][05:19:23][COMM]now sleep


2025-07-31 20:55:28:357 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:55:28:365 ==>> 检测【检测33V休眠电流】
2025-07-31 20:55:28:379 ==>> 开始33V电流采样
2025-07-31 20:55:28:398 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:28:467 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:55:29:475 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:55:29:521 ==>> Current33V:????:16.50

2025-07-31 20:55:29:987 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:29:996 ==>> 【检测33V休眠电流】通过,【16.5uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:55:30:005 ==>> 该项需要延时执行
2025-07-31 20:55:32:012 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:55:32:026 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:55:32:050 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:55:32:074 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1662mV
Get AD_V4 0mV
Get AD_V5 2749mV
Get AD_V6 2021mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:55:33:050 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:55:33:059 ==>> 检测【打开小电池2】
2025-07-31 20:55:33:072 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:55:33:163 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:55:33:350 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:55:33:360 ==>> 该项需要延时执行
2025-07-31 20:55:33:851 ==>> 此处延时了:【500】毫秒
2025-07-31 20:55:33:867 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:55:33:889 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:55:33:961 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:55:34:139 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:55:34:151 ==>> 该项需要延时执行
2025-07-31 20:55:34:645 ==>> 此处延时了:【500】毫秒
2025-07-31 20:55:34:662 ==>> 检测【进入休眠模式2】
2025-07-31 20:55:34:683 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:55:34:699 ==>> [D][05:19:29][COMM]------------ready to Power on Acckey 1------------
[D][05:19:29][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:29][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 0,volt = 8
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 1,volt = 8
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 2,volt = 8
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 3,volt = 8
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 4,volt = 7
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 5,volt = 7
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 6,volt = 7
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 7,volt = 7
[D][05:19:29][FCTY]get_ext_48v_vol retry i = 8,volt = 7
[D][05:19:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:29][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:29][COMM]----- get Acckey 1 and value:1------------
[W][05:19:29][COMM]CAN START!
[D][05:19:29][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:29][COMM]1x1 frm_can_tp_send ok
[D][05:19:29][CAT1]gsm read msg sub id: 12
[D][05:19:29][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:29][COMM]CAN message bat fault change: 0x01B987FE->0x00000

2025-07-31 20:55:34:726 ==>> 000 100988
[D][05:19:29][COMM][Audio]exec status ready.
[D][05:19:29][CAT1]<<< 
OK

[D][05:19:29][CAT1]exec over: func id: 12, ret: 6
[D][05:19:29][COMM]imu wakeup ok. 101002
[D][05:19:30][COMM]imu wakeup 1
[W][05:19:30][COMM]wake up system, wakeupEvt=0x80
[D][05:19:30][COMM]frm_can_weigth_power_set 1
[D][05:19:30][COMM]Clear Sleep Block Evt
[D][05:19:30][COMM]Main Task receive event:28 finished processing


2025-07-31 20:55:35:068 ==>> [W][05:19:30][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[E][05:19:30][COMM]1x1 rx timeout
[D][05:19:30][COMM]1x1 frm_can_tp_send ok
[D][05:19:30][COMM]Main Task receive event:28
[D][05:19:30][COMM]prepare to sleep
[D][05:19:30][CAT1]gsm read msg sub id: 12
[D][05:19:30][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:30][CAT1]<<< 
OK

[D][05:19:30][CAT1]exec over: func id: 12, ret: 6
[W][05:19:30][COMM]CAN STOP!
[D][05:19:30][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:30][COMM]------------ready to Power off Acckey 1------------
[D][05:19:30][COMM]------------ready to Power off Acckey 2------------
[D][05:19:30][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:30][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 135
[D][05:19:30][COMM]bat sleep fail, reason:-1
[D][05:19:30][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:30][COMM]accel parse set 0
[D][05:19:30][COMM]imu rest ok. 101464
[D][05:19:30][COMM]imu sleep 0
[W][05:19:30][COMM]now sleep


2025-07-31 20:55:35:184 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:55:35:193 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:55:35:203 ==>> 开始小电池电流采样
2025-07-31 20:55:35:234 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:35:293 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:55:36:300 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:55:36:360 ==>> CurrentBattery:ƽ��:68.33

2025-07-31 20:55:36:805 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:36:814 ==>> 【检测小电池休眠电流】通过,【68.33uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:55:36:824 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:55:36:838 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:55:36:958 ==>> 5A A5 01 5A A5 


2025-07-31 20:55:37:063 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 20:55:37:109 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:55:37:117 ==>> 该项需要延时执行
2025-07-31 20:55:37:304 ==>> [D][05:19:32][COMM]------------ready to Power on Acckey 1------------
[D][05:19:32][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:32][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:32][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 2
[D][05:19:32][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:32][COMM]----- get Acckey 1 and value:1------------
[W][05:19:32][COMM]CAN START!
[E][05:19:32][COMM]1x1 rx timeout
[E][05:19:32][COMM]1x1 tp timeout
[E][05:19:32][COMM]1x1 error -3.
[D][05:19:32][GNSS]handler GSMGet Base timeout
[D][05:19:32][COMM]read battery soc:0
[D][05:19:32][CAT1]gsm read msg sub id: 12
[D][05:19:32][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:32][COMM][Audio]exec status ready.
[D][05:19:32][CAT1]<<< 
OK

[D][05:19:32][CAT1]exec over: func id: 12, ret: 6
[D][05:19:32][COMM]imu wakeup ok. 103684
[D][05:19:32][COMM]imu wakeup 1
[D][05:19:32][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:19:32][COMM]wake up system, wakeupEvt=0x80
[D][05:19:32][COMM]frm_can_weigth_power_set 1
[D][05:19:32][COMM]Clear Sleep Block Evt
[D][05:19:32][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:32][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:55:37:559 ==>> [E][05:19:33][COMM]1x1 rx timeout
[D][05:19:33][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:55:37:619 ==>> 此处延时了:【500】毫秒
2025-07-31 20:55:37:635 ==>> 检测【检测唤醒】
2025-07-31 20:55:37:655 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:55:37:672 ==>> [D][05:19:33][COMM]msg 02A0 loss. last_tick:103653. cur_tick:104165. period:50
[D][05:19:3

2025-07-31 20:55:37:724 ==>> 3][COMM]msg 02A4 loss. last_tick:103653. cur_tick:104165. period:50
[D][05:19:33][COMM]msg 02A5 loss. last_tick:103653. cur_tick:104165. period:50
[D][05:19:33][COMM]msg 02A6 loss. last_tick:103653. cur_tick:104166. period:50
[D][05:19:33][COMM]msg 02A7 loss. last_tick:103653. cur_tick:104166. period:50
[D][05:19:33][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 104166
[D][05:19:33][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 104167


2025-07-31 20:55:38:060 ==>> [W][05:19:33][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:33][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[D][05:19:33][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:33][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:33][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:33][FCTY]DeviceID    = 460130071539186
[D][05:19:33][FCTY]HardwareID  = 867222087756849
[D][05:19:33][FCTY]MoBikeID    = 9999999999
[D][05:19:33][FCTY]LockID      = FFFFFFFFFF
[D][05:19:33][FCTY]BLEFWVersion= 105
[D][05:19:33][FCTY]BLEMacAddr   = F4B99F7434DC
[D][05:19:33][FCTY]Bat         = 3904 mv
[D][05:19:33][FCTY]Current     = 0 ma
[D][05:19:33][FCTY]VBUS        = 2600 mv
[D][05:19:33][FCTY]TEMP= 0,BATID= 352,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:33][FCTY]Ext battery vol = 32, adc = 1295
[D][05:19:33][FCTY]Acckey1 vol = 5556 mv, Acckey2 vol = 202 mv
[D][05:19:33][FCTY]Bike Type flag is invalied
[D][05:19:33][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:33][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:33][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:33][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05

2025-07-31 20:55:38:120 ==>> :19:33][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:33][FCTY]Bat1         = 3791 mv
[D][05:19:33][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:33][FCTY]==========Modules-nRF5340 ==========
[E][05:19:33][COMM]1x1 rx timeout
[E][05:19:33][COMM]1x1 tp timeout
[E][05:19:33][COMM]1x1 error -3.
[D][05:19:33][COMM]Main Task receive event:28 finished processing


2025-07-31 20:55:38:168 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:55:38:177 ==>> 检测【关机】
2025-07-31 20:55:38:191 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:55:38:407 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      4663. period:100
[D][05:19:33][COMM]msg 02C2 loss. last_tick:103653. cur_tick:104664. period:100
[D][05:19:33][COMM]msg 02E0 loss. last_tick:103653. cur_tick:104664. period:100
[D][05:19:33][COMM]msg 02E1 loss. last_tick:103653. cur_tick:104665. period:100
[D][05:19:33][COMM]msg 02E2 loss. last_tick:103653. cur_tick:104665. period:100
[D][05:19:33][COMM]msg 0300 loss. last_tick:103653. cur_tick:104665. period:100
[D][05:19:33][COMM]msg 0301 loss. last_tick:103653. cur_tick:104666. period:100
[D][05:19:33][COMM]bat msg 0240 loss. last_tick:103653. cur_tick:104666. period:100. j,i:1 54
[D][05:19:33][COMM]bat msg 0241 loss. last_tick:103653. cur_tick:104666. period:100. j,i:2 55
[D][05:19:33][COMM]bat msg 0242 loss. last_

2025-07-31 20:55:38:512 ==>> tick:103653. cur_tick:104667. period:100. j,i:3 56
[D][05:19:33][COMM]bat msg 0244 loss. last_tick:103653. cur_tick:104667. period:100. j,i:5 58
[D][05:19:33][COMM]bat msg 024E loss. last_tick:103653. cur_tick:104667. period:100. j,i:15 68
[D][05:19:33][COMM]bat msg 024F loss. last_tick:103653. cur_tick:104668. period:100. j,i:16 69
[D][05:19:33][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 104668
[D][05:19:33][COMM]CAN message bat fault change: 0x00000000->0x0001802E 104668
[D][05:19:33][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 104669
[W][05:19:33][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
                                                                                                                                                                                                                                                                                                                          

2025-07-31 20:55:38:617 ==>> [D][05:19:34][COMM]

2025-07-31 20:55:38:647 ==>> msg 0222 loss. last_tick:103653. cur_tick:105160. period:150
[D][05:19:34][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 105161


2025-07-31 20:55:38:891 ==>> [D][05:19:34][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:0------------
[D][05:19:34][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:55:39:190 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:55:39:720 ==>> [D][05:19:34][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]more than the number of battery plugs
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]verify_batlock_state ret -516, soc 0
[D][05:19:34][COMM]Main Task receive event:65
[D][05:19:34][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:34][COMM]Main Task receive event:65 finished processing
[D][05:19:34][COMM]Main Task receive event:66
[D][05:19:34][COMM]Try to Auto Lock Bat
[D][05:19:34][COMM]Main Task receive event:66 finished processing
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:19:34][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:19:34][COMM]Bat auth off fail, error:-1
[D][05:19:34][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]frm_peripheral_device_p

2025-07-31 20:55:39:825 ==>> oweron type 16.... 
[D][05:19:34][COMM]----- get Acckey 1 and value:1------------
[D][05:19:34][COMM]----- get Acckey 2 and value:1------------
[D][05:19:34][COMM]Receive Bat Lock cmd 0
[D][05:19:34][COMM]VBUS is 1
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:19:34][COMM]file:B50 exist
[D][05:19:34][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D][05:19:34][COMM]read file, len:10800, num:3
[D][05:19:34][COMM]Main Task receive event:60
[D][05:19:34][COMM]smart_helmet_vol=255,255
[D][05:19:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get soh error
[E][05:19:34][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:34][COMM]report elecbike
[W][05:19:34][PROT]remove success[1629955174],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:19:34][COMM]Main Task receive event:60 finished processing
[D][05:19:34][COMM]Main Task receive event:61
[

2025-07-31 20:55:39:930 ==>> D][05:19:34][COMM][D301]:type:3, trace id:280
[D][05:19:34][COMM]id[], hw[000
[D][05:19:34][COMM]get mcMaincircuitVolt error
[D][05:19:34][COMM]get mcSubcircuitVolt error
[D][05:19:34][COMM]33v/48v_in[33], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:34][COMM]BAT CAN get state1 Fail 204
[D][05:19:34][COMM]BAT CAN get soc Fail, 204
[D][05:19:34][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][PROT]min_index:0, type:0x5D03, priority:4
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][PROT]index:0
[D][05:19:34][PROT]is_send:1
[D][05:19:34][PROT]sequence_num:12
[D][05:19:34][PROT]retry_timeout:0
[D][05:19:34][PROT]retry_times:3
[D][05:19:34][PROT]send_path:0x3
[D][05:19:34][PROT]msg_type:0x5d03
[D][05:19:34][PROT]===========================================================
[W][05:19:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955174]
[D][05:19:34][PROT]===========================================================
[D][05:19:34][PROT]Sending traceid[999999999990000D]
[D

2025-07-31 20:55:40:035 ==>> ][05:19:34][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:34][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:34][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:34][COMM]BAT CAN get state2 fail 204
[D][05:19:34][COMM]get bat work mode err
[W][05:19:34][PROT]remove success[1629955174],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:34][PROT]add success [1629955174],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:19:34][COMM]Main Task receive event:61 finished processing
[D][05:19:34][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:34][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:34][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:34][SAL ]open socket ind id[4], rst[0]
[D][05:19:34][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:34][SAL ]Cellular task submsg id[8]
[D][05:19:34][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:34][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:34][CAT1]gsm read msg sub id: 8
[D][

2025-07-31 20:55:40:141 ==>> 05:19:34][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:34][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:34][COMM]--->crc16:0xb8a
[D][05:19:34][COMM]read file success
[D][05:19:34][COMM]accel parse set 1
[D][05:19:34][COMM][Audio]mon:9,05:19:34
[D][05:19:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:34][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:34][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[W][05:19:34][COMM]Power Off
[D][05:19:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:A

2025-07-31 20:55:40:239 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:55:40:249 ==>> T+AUDIODATAHEX=0,10800,0

[D][05:19:34][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:34][CAT1]<<< 
+CME ERROR: 100

[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:34][COMM]read battery soc:255
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec8

2025-07-31 20:55:40:261 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:55:40:276 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:55:40:350 ==>> 00m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
[W][05:19:34][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:34][COMM]arm_hub_enable: hub power: 0
[D][05:19:34][HSDK]hexlog index save 0 3584 91 @ 0 : 0
[D][05:19:34][HSDK]write save hexlog index [0]
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:34][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150
                              

2025-07-31 20:55:40:530 ==>> [D][05:19:35][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 3,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:19:35][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:55:40:547 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:55:40:577 ==>> 检测【检测小电池关机电流】
2025-07-31 20:55:40:586 ==>> 开始小电池电流采样
2025-07-31 20:55:40:601 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:40:650 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:55:41:658 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:55:41:718 ==>> CurrentBattery:ƽ��:68.89

2025-07-31 20:55:42:172 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:55:42:182 ==>> 【检测小电池关机电流】通过,【68.89uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:55:42:695 ==>> MES过站成功
2025-07-31 20:55:42:705 ==>> #################### 【测试结束】 ####################
2025-07-31 20:55:42:778 ==>> 关闭5V供电
2025-07-31 20:55:42:794 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:55:42:862 ==>> 5A A5 04 5A A5 


2025-07-31 20:55:42:967 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:55:43:781 ==>> 关闭5V供电成功
2025-07-31 20:55:43:795 ==>> 关闭33V供电
2025-07-31 20:55:43:828 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:55:43:858 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:55:44:795 ==>> 关闭33V供电成功
2025-07-31 20:55:44:810 ==>> 关闭3.7V供电
2025-07-31 20:55:44:843 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:55:44:858 ==>> 6A A6 02 A6 6A 


2025-07-31 20:55:44:962 ==>> Battery OFF
OVER 150


2025-07-31 20:55:45:758 ==>>  

2025-07-31 20:55:45:803 ==>> 关闭3.7V供电成功
