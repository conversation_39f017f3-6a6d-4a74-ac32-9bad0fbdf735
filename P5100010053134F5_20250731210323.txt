2025-07-31 21:03:23:283 ==>> MES查站成功:
查站序号:P5100010053134F5验证通过
2025-07-31 21:03:23:297 ==>> 扫码结果:P5100010053134F5
2025-07-31 21:03:23:298 ==>> 当前测试项目:SE51_PCBA
2025-07-31 21:03:23:299 ==>> 测试参数版本:2024.10.11
2025-07-31 21:03:23:301 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 21:03:23:303 ==>> 检测【打开透传】
2025-07-31 21:03:23:304 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 21:03:23:364 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 21:03:23:613 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 21:03:23:632 ==>> 检测【检测接地电压】
2025-07-31 21:03:23:633 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 21:03:23:765 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 21:03:23:922 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 21:03:23:924 ==>> 检测【打开小电池】
2025-07-31 21:03:23:927 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 21:03:24:073 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 21:03:24:212 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 21:03:24:214 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 21:03:24:218 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 21:03:24:271 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 21:03:24:505 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 21:03:24:508 ==>> 检测【等待设备启动】
2025-07-31 21:03:24:511 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:03:24:938 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 21:03:25:135 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 21:03:25:539 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:03:25:774 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 21:03:25:819 ==>>                                                    

2025-07-31 21:03:26:219 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 21:03:26:575 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 21:03:26:698 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 21:03:26:865 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 21:03:26:867 ==>> 检测【产品通信】
2025-07-31 21:03:26:870 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 21:03:27:036 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 21:03:27:156 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 21:03:27:158 ==>> 检测【初始化完成检测】
2025-07-31 21:03:27:161 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 21:03:27:414 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 21:03:27:702 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 21:03:27:704 ==>> 检测【关闭大灯控制1】
2025-07-31 21:03:27:706 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:03:27:769 ==>> [D][05:17:51][COMM]2628 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:27:874 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][F

2025-07-31 21:03:27:919 ==>> CTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 21:03:27:993 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:03:27:996 ==>> 检测【打开仪表指令模式1】
2025-07-31 21:03:27:998 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:03:28:164 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:03:28:291 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:03:28:294 ==>> 检测【关闭仪表供电】
2025-07-31 21:03:28:296 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:03:28:452 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 21:03:28:574 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:03:28:576 ==>> 检测【关闭AccKey2供电1】
2025-07-31 21:03:28:578 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:03:28:802 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3639 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:28:865 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:03:28:868 ==>> 检测【关闭AccKey1供电1】
2025-07-31 21:03:28:870 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 21:03:29:032 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 21:03:29:149 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 21:03:29:152 ==>> 检测【关闭转刹把供电1】
2025-07-31 21:03:29:155 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:29:322 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:03:29:470 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:03:29:475 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 21:03:29:478 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:03:29:565 ==>> 5A A5 01 5A A5 


2025-07-31 21:03:29:670 ==>> OPEN_POWER_OUT1
OVER 150


2025-07-31 21:03:29:764 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:03:29:768 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 21:03:29:769 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 21:03:29:775 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4650 imu init OK
[D][05:17:53][COMM]imu_task imu work er

2025-07-31 21:03:29:805 ==>> ror:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 21:03:29:865 ==>> 5A A5 03 5A A5 


2025-07-31 21:03:29:970 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 21:03:30:051 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 21:03:30:053 ==>> 该项需要延时执行
2025-07-31 21:03:30:319 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5006. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5007. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5007. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5008. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5008. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5008. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5009. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5009. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5009. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5010. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5010. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:5011. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5011. period:500. j,i:24 77
[D][05:17:53][COMM]CAN messag

2025-07-31 21:03:30:349 ==>> e fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5011
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5012


2025-07-31 21:03:30:809 ==>> [D][05:17:54][COMM]5661 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:31:346 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:03:31:884 ==>>                                                         .... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo

2025-07-31 21:03:31:988 ==>>  is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][0

2025-07-31 21:03:32:094 ==>> 5:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2

2025-07-31 21:03:32:168 ==>> M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6672 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:32:832 ==>> [D][05:17:56][COMM]7683 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:33:832 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8694 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:34:063 ==>> 此处延时了:【4000】毫秒
2025-07-31 21:03:34:067 ==>> 检测【33V输入电压ADC】
2025-07-31 21:03:34:080 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:34:381 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:17:58][COMM]adc read out 24v adc:1320  volt:33386 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:13  volt:17 mv
[D][05:17:58][COMM]adc read throttle adc:15  volt:19 mv
[D][05:17:58][COMM]adc read battery ts volt:20 mv
[D][05:17:58][COMM]adc read in 24v adc:1299  volt:32855 mv
[D][05:17:58][COMM]adc read throttle brake in adc:13  volt:22 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:10  volt:231 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 21:03:34:612 ==>> 【33V输入电压ADC】通过,【32855mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 21:03:34:616 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 21:03:34:618 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:03:34:682 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1655mV
Get AD_V4 0mV
Get AD_V5 2778mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 21:03:34:847 ==>> [D][05:17:58][COMM]9705 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:34:907 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:34:910 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:03:34:939 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:34:942 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:03:34:945 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 21:03:34:972 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:03:34:974 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:03:35:008 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:03:35:010 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:03:35:047 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:03:35:049 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:03:35:213 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10017
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018
1A A1 00 00 FC 
Get AD_V2 1660mV
Get AD_V3 1655mV
Get AD_V4 0mV
Get AD_V5 2778mV
Get AD_V6 1992mV
Get AD_V7 1087mV
OVER 150


2025-07-31 21:03:35:345 ==>> 【TP7_VCC3V3(ADV2)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:35:347 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:03:35:381 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:35:384 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:03:35:386 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 21:03:35:418 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:03:35:420 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:03:35:454 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:03:35:456 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:03:35:494 ==>> 【TP1_VCC12V(ADV7)】通过,【1087mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:03:35:497 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:03:35:575 ==>> 1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1654mV
Get AD_V4 1mV
Get AD_V5 2778mV
Get AD_V6 1991mV
Get AD_V7 1088mV
OVER 150


2025-07-31 21:03:35:795 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:35:797 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 21:03:35:827 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:35:830 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 21:03:35:832 ==>> 原始值:【2778】, 乘以分压基数【2】还原值:【5556】
2025-07-31 21:03:35:858 ==>> 【TP68_VCC5V5(ADV5)】通过,【5556mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:03:35:860 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 21:03:35:864 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10716 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:03:35:889 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 21:03:35:891 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 21:03:35:981 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 21:03:36:001 ==>> 检测【打开WIFI(1)】
2025-07-31 21:03:36:005 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:03:36:135 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:03:36:283 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:03:36:287 ==>> 检测【清空消息队列(1)】
2025-07-31 21:03:36:288 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 21:03:36:547 ==>> [D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing
[D][05:18:00][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:00][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]


2025-07-31 21:03:36:577 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 21:03:36:838 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 21:03:36:841 ==>> 检测【打开GPS(1)】
2025-07-31 21:03:36:844 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 21:03:37:310 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                         IMI

[D][05:18:00][CAT1]<<< 
***************

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:00][COMM]imu error,enter wait
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:

2025-07-31 21:03:37:415 ==>> 00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 5, ret: 6
[D][05:18:00][CAT1]sub id: 5, ret: 6

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:00][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:00][M2M ]M2M_GSM_INIT OK
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:00][SAL ]open socket ind id[4], rst[0]
[D][05:18:00][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:00][SAL ]Cellular task submsg id[8]
[D][05:18:00][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:00][SAL ]domain[bikeapi.mobike.com]

2025-07-31 21:03:37:520 ==>>  port[9999] type[1]
[D][05:18:00][COMM]Main Task receive event:4
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:00][COMM]init key as 
[D][05:18:00][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:00][COMM]Main Task receive event:4 finished processing
[D][05:18:00][CAT1]gsm read msg sub id: 8
[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][CAT1]<<< 
+CSQ: 21,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 21:03:37:625 ==>>                                                                           id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 21:03:37:646 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 21:03:37:649 ==>> 检测【打开GSM联网】
2025-07-31 21:03:37:653 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 21:03:38:082 ==>> [D][05:18:01][COMM]read battery soc:255
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 21:03:38:187 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 21:03:38:191 ==>> 检测【打开仪表供电1】
2025-07-31 21:03:38:194 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 21:03:38:357 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:03:38:478 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 21:03:38:482 ==>> 检测【打开仪表指令模式2】
2025-07-31 21:03:38:485 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 21:03:38:658 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 21:03:38:763 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 21:03:38:766 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 21:03:38:769 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 21:03:38:770 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 21:03:38:838 ==>> [D][05:18:02][COMM]13728 imu init OK


2025-07-31 21:03:38:943 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33688]


2025-07-31 21:03:39:052 ==>> 【读取主控ADC采集的仪表电压】通过,【33688mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:03:39:056 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 21:03:39:061 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:03:39:259 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:03:39:340 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 21:03:39:343 ==>> 检测【AD_V20电压】
2025-07-31 21:03:39:345 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 21:03:39:425 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 21:03:39:455 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 21:03:39:650 ==>> 1A A1 10 00 00 
Get AD_V20 1644mV
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,1,1,04,24,,,41,33,,,41,39,,,38,25,,,32,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1575.453,1575.453,50.435,2097152,2097152,2097152*48

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6



2025-07-31 21:03:39:710 ==>> 本次取值间隔时间:243ms
2025-07-31 21:03:39:742 ==>> 【AD_V20电压】通过,【1644mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:39:745 ==>> 检测【拉低OUTPUT2】
2025-07-31 21:03:39:749 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 21:03:39:785 ==>> [D][05:18:03][COMM]read battery soc:255


2025-07-31 21:03:39:860 ==>>                    recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]
3A A3 02 00 A3 


2025-07-31 21:03:39:965 ==>> OFF_OUT2
OVER 150


2025-07-31 21:03:40:034 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 21:03:40:037 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 21:03:40:040 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:03:40:285 ==>> [D][05:18:04][HSDK][0] flush to flash addr:[0xE41F00] --- write len --- [256]
[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:0
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:03:40:575 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,33,,,42,24,,,40,59,,,40,39,,,39,1*7B

$GBGSV,3,2,12,14,,,37,25,,,37,9,,,35,44,,,34,1*4E

$GBGSV,3,3,12,60,,,40,41,,,38,40,,,38,1,,,36,1*42

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1575.397,1575.397,50.379,2097152,2097152,2097152*47



2025-07-31 21:03:40:583 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 21:03:40:585 ==>> 检测【拉高OUTPUT2】
2025-07-31 21:03:40:589 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 21:03:40:665 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150


2025-07-31 21:03:40:892 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 21:03:40:895 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 21:03:40:912 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 21:03:41:064 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:03:41:371 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 21:03:41:374 ==>> 检测【预留IO LED功能输出】
2025-07-31 21:03:41:378 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 21:03:41:624 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,16,33,,,42,3,,,42,60,,,41,24,,,40,1*46

$GBGSV,4,2,16,59,,,40,39,,,39,25,,,39,40,,,38,1*7D

$GBGSV,4,3,16,14,,,37,1,,,37,2,,,36,41,,,35,1*76

$GBGSV,4,4,16,9,,,35,44,,,34,38,,,34,4,,,30,1*72

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1552.112,1552.112,49.669,2097152,2097152,2097152*4B

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 21:03:41:789 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 21:03:41:930 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 21:03:41:933 ==>> 检测【AD_V21电压】
2025-07-31 21:03:41:935 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 21:03:42:060 ==>> 1A A1 20 00 00 
Get AD_V21 1639mV
OVER 150


2025-07-31 21:03:42:243 ==>> 本次取值间隔时间:311ms
2025-07-31 21:03:42:301 ==>> 【AD_V21电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:42:305 ==>> 检测【关闭仪表供电2】
2025-07-31 21:03:42:308 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:03:42:460 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 21:03:42:565 ==>> $GBGGA,130346.425,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,33,,,42,3,,,41,60,,,40,24,,,40,1*4B

$GBGSV,5,2,18,59,,,40,25,,,40,39,,,39,40,,,38,1*7C

$GBGSV,5,3,18,16,,,38,14,,,37,41,,,37,1,,,36,1*41

$GBGSV,5,4,18,9,,,36,2,,,35,44,,,34,38,,,34,1*7D

$G

2025-07-31 21:03:42:610 ==>> BGSV,5,5,18,5,,,32,4,,,31,1*7D

$GBRMC,130346.425,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130346.425,0.000,1543.179,1543.179,49.375,2097152,2097152,2097152*5D



2025-07-31 21:03:42:670 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:03:42:676 ==>> 检测【关闭仪表指令模式】
2025-07-31 21:03:42:680 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 21:03:42:850 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:06][COMM][oneline_display]: command mode, OFF!


2025-07-31 21:03:42:974 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 21:03:42:979 ==>> 检测【打开AccKey2供电】
2025-07-31 21:03:42:983 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 21:03:43:150 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 21:03:43:291 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 21:03:43:294 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 21:03:43:298 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:43:631 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3151  volt:5538 mv
[D][05:18:07][COMM]adc read out 24v adc:1303  volt:32956 mv
[D][05:18:07][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:07][COMM]adc read right brake adc:16  volt:21 mv
[D][05:18:07][COMM]adc read throttle adc:12  volt:15 mv
[D][05:18:07][COMM]adc read battery ts volt:21 mv
[D][05:18:07][COMM]adc read in 24v adc:1301  volt:32906 mv
[D][05:18:07][COMM]adc read throttle brake in adc:12  volt:21 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1452  volt:33664 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,130347.405,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,22,33,,,42,3,,,40,60,,,40,24,,,40,1*40

$GBGSV,6,2,22,59,,,40,25,,,40,39,,,39,40,,,38,1*76

$GBGSV,6,3,22,41,,,38,16,,,37,14,,,37,1,,,36,1*4B

$GBGSV,6,4,22,9,,,36,7,,,36,2,,,35,6,,,35,1*7E

$GBGSV,6,5,22,44,,,34,38,,,34,13,,,34,5,,,32,1*4F

$GBGSV,6,6,22,4,,,31,42,,,38,1*4D

$GBRMC,130347.405,V,,,,,,,,0.0,E,N,V*4D

$GB

2025-07-31 21:03:43:661 ==>> VTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130347.405,0.000,1528.036,1528.036,48.884,2097152,2097152,2097152*5A



2025-07-31 21:03:43:811 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 21:03:43:843 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【32956mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:03:43:847 ==>> 检测【关闭AccKey2供电2】
2025-07-31 21:03:43:850 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:03:44:025 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:03:44:212 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:03:44:216 ==>> 该项需要延时执行
2025-07-31 21:03:44:588 ==>> $GBGGA,130348.385,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,60,,,40,24,,,40,1*46

$GBGSV,6,2,24,59,,,40,25,,,40,39,,,39,40,,,38,1*70

$GBGSV,6,3,24,41,,,38,16,,,37,14,,,37,1,,,36,1*4D

$GBGSV,6,4,24,7,,,36,42,,,35,9,,,35,2,,,35,1*4B

$GBGSV,6,5,24,6,,,35,13,,,35,44,,,34,38,,,34,1*4C

$GBGSV,6,6,24,34,,,34,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,130348.385,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130348.385,0.000,1514.953,1514.953,48.462,2097152,2097152,2097152*5E



2025-07-31 21:03:44:768 ==>> $GBGGA,130348.585,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,3,,,40,60,,,40,24,,,40,1*46

$GBGSV,6,2,24,59,,,40,25,,,40,39,,,39,40,,,38,1*70

$GBGSV,6,3,24,41,,,38,16,,,37,14,,,37,1,,,36,1*4D

$GBGSV,6,4,24,7,,,36,42,,,35,9,,,35,2,,,35,1*4B

$GBGSV,6,5,24,6,,,35,13,,,35,44,,,34,38,,,34,1*4C

$GBGSV,6,6,24,34,,,34,10,,,33,5,,,32,4,,,32,1*70

$GBRMC,130348.585,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130348.585,0.000,1514.953,1514.953,48.462,2097152,2097152,2097152*58



2025-07-31 21:03:45:775 ==>> $GBGGA,130349.565,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,39,59,,,39,39,,,39,40,,,38,1*71

$GBGSV,7,3,25,41,,,38,16,,,37,14,,,37,1,,,36,1*4D

$GBGSV,7,4,25,7,,,36,42,,,36,9,,,35,2,,,35,1*48

$GBGSV,7,5,25,6,,,35,13,,,35,44,,,34,38,,,34,1*4C

$GBGSV,7,6,25,34,,,33,10,,,33,5,,,32,4,,,32,1*77

$GBGSV,7,7,25,12,,,31,1*70

$GBRMC,130349.565,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130349.565,0.000,1502.451,1502.451,48.068,2097152,2097152,2097152*59



2025-07-31 21:03:45:805 ==>>                                          

2025-07-31 21:03:46:759 ==>> $GBGGA,130350.545,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,39,59,,,39,39,,,39,40,,,38,1*71

$GBGSV,7,3,25,41,,,38,16,,,36,14,,,36,1,,,36,1*4D

$GBGSV,7,4,25,7,,,36,42,,,36,9,,,35,6,,,35,1*4C

$GBGSV,7,5,25,13,,,35,2,,,34,44,,,34,38,,,34,1*49

$GBGSV,7,6,25,34,,,33,10,,,33,4,,,33,5,,,32,1*76

$GBGSV,7,7,25,12,,,31,1*70

$GBRMC,130350.545,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130350.545,0.000,1499.133,1499.133,47.960,2097152,2097152,2097152*5D



2025-07-31 21:03:47:223 ==>> 此处延时了:【3000】毫秒
2025-07-31 21:03:47:228 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 21:03:47:242 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:47:588 ==>> [D][05:18:11][HSDK][0] flush to flash addr:[0xE42000] --- write len --- [256]
[W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3146  volt:5530 mv
[D][05:18:11][COMM]adc read out 24v adc:8  volt:202 mv
[D][05:18:11][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:11][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:11][COMM]adc read throttle adc:16  volt:21 mv
[D][05:18:11][COMM]adc read battery ts volt:17 mv
[D][05:18:11][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:11][COMM]adc read throttle brake in adc:4  volt:7 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1453  volt:33688 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:2  volt:46 mv


2025-07-31 21:03:47:694 ==>>                                                                                                 2,3,,,40,24,,,40,25,,,40,1*47

$GBGSV,7,2,25,60,,,39,59,,,39,39,,,39,41,,,38,1*70

$GBGSV,7,3,25,40,,,37,14,,,37,16,,,36,1,,,36,1*42

$GBGSV,7,4,25,7,,,36,42,,,36,9,,,35,6,,,35,1*4C

$GBGSV,7,5,25,13,,,35,2,,,34,44,,,34,38,,,34,1*49

$GBGSV,7,6,25,34,,,34,10,,,33,4,,,32,5,,,32,1*70

$GBG

2025-07-31 21:03:47:739 ==>> SV,7,7,25,12,,,31,1*70

$GBRMC,130351.525,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130351.525,0.000,1499.133,1499.133,47.960,2097152,2097152,2097152*5A



2025-07-31 21:03:47:798 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【202mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 21:03:47:816 ==>> 检测【打开AccKey1供电】
2025-07-31 21:03:47:818 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 21:03:47:821 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 21:03:47:905 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[

2025-07-31 21:03:47:935 ==>> D][05:18:11][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 21:03:48:150 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 21:03:48:155 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 21:03:48:158 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:03:48:267 ==>> 1A A1 00 40 00 
Get AD_V14 2610mV
OVER 150


2025-07-31 21:03:48:403 ==>> 原始值:【2610】, 乘以分压基数【2】还原值:【5220】
2025-07-31 21:03:48:456 ==>> 【读取AccKey1电压(ADV14)前】通过,【5220mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:03:48:459 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 21:03:48:461 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:48:825 ==>> $GBGGA,130352.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,7,2,26,60,,,39,59,,,39,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,14,,,37,16,,,36,1,,,36,1*4E

$GBGSV,7,4,26,7,,,36,42,,,36,9,,,35,6,,,35,1*4F

$GBGSV,7,5,26,13,,,35,2,,,34,44,,,34,38,,,34,1*4A

$GBGSV,7,6,26,34,,,34,10,,,33,4,,,32,5,,,32,1*73

$GBGSV,7,7,26,12,,,31,8,,,37,1*4F

$GBRMC,130352.505,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130352.505,0.000,1500.792,1500.792,48.014,2097152,2097152,2097152*5E

[W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:12][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:12][COMM]adc read throttle adc:11  volt:14 mv
[D][05:18:12][COMM]adc read battery ts volt:13 mv
[D][05:18:12][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:

2025-07-31 21:03:48:870 ==>> 2407  volt:3878 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1454  volt:33711 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:03:49:041 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5524mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 21:03:49:045 ==>> 检测【关闭AccKey1供电2】
2025-07-31 21:03:49:049 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 21:03:49:254 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 21:03:49:334 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 21:03:49:338 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 21:03:49:341 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 21:03:49:464 ==>> 1A A1 00 40 00 
Get AD_V14 2605mV
OVER 150


2025-07-31 21:03:49:599 ==>> 原始值:【2605】, 乘以分压基数【2】还原值:【5210】
2025-07-31 21:03:49:632 ==>> 【读取AccKey1电压(ADV14)后】通过,【5210mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 21:03:49:636 ==>> 检测【打开WIFI(2)】
2025-07-31 21:03:49:639 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:03:49:720 ==>> $GBGGA,130353.505,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,40,24,,,40,25,,,40,1*44

$GBGSV,7,2,26,60,,,40,59,,,40,39,,,39,41,,,38,1*73

$GBGSV,7,3,26,40,,,38,14,,,37,16,,,37,1,,,36,1*4F

$GBGSV,7,4,26,7,,,36,42,,,36,9,,,35,6,,,35,1*4F

$GBGSV,7,5,26,13,,,35,2,,,35,38,,,35,44,,,34,1*4A

$GBGSV,7,6,26,34,,,34,10,,,34,4,,,32,5,,,32,1*74

$GBGSV,7,7,26,8,,,31,12,,,31,1*49

$GBRMC,130353.505,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130353.505,0.000,1502.074,1502.074,48.062,2097152,2097152,2097152*5E



2025-07-31 21:03:49:825 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][COMM]read battery soc:255
[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:03:49:914 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:03:49:920 ==>> 检测【转刹把供电】
2025-07-31 21:03:49:926 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:03:50:052 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 21:03:50:207 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 21:03:50:211 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 21:03:50:213 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:03:50:313 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:03:50:373 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2414mV
OVER 150


2025-07-31 21:03:50:478 ==>> 原始值:【2414】, 乘以分压基数【2】还原值:【4828】
2025-07-31 21:03:50:514 ==>> 【读取AD_V15电压(前)】通过,【4828mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:03:50:517 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 21:03:50:520 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 21:03:50:628 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:03:50:764 ==>> $GBGGA,130354.505,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,37,1,,,36,1*4E

$GBGSV,7,4,27,7,,,36,42,,,36,9,,,35,6,,,35,1*4E

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,10,,,33,4,,,33,5,,,33,1*72

$GBGSV,7,7,27,8,,,32,12,,,31,23,,,28,1*40

$GBRMC,130354.505,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130354.505,0.000,1490.982,1490.982,47.717,2097152,2097152,2097152*53

[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
+WIFISCAN:4,0,F88C21BCF57D,-35
+WIFISCAN:4,1,F42A7D1297A3,-69
+WIFISCAN:4,2,CC057790A7C1,-73
+WIFISCAN:4,3,44A1917CAD81,-84

[D][05:18:14][CAT1]wifi scan report total[4]
1A A1 01 00 00 
Get AD_V16 2444mV
OVER 150


2025-07-31 21:03:50:779 ==>> 原始值:【2444】, 乘以分压基数【2】还原值:【4888】
2025-07-31 21:03:50:819 ==>> 【读取AD_V16电压(前)】通过,【4888mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 21:03:50:822 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 21:03:50:827 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:50:899 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 21:03:51:172 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3152  volt:5540 mv
[D][05:18:14][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:14][COMM]adc read left brake adc:10  volt:13 mv
[D][05:18:14][COMM]adc read right brake adc:11  volt:14 mv
[D][05:18:14][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:14][COMM]adc read battery ts volt:14 mv
[D][05:18:14][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:14][COMM]adc read throttle brake in adc:3096  volt:5442 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1454  volt:33711 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:03:51:374 ==>> 【转刹把供电电压(主控ADC)】通过,【5442mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 21:03:51:379 ==>> 检测【转刹把供电电压】
2025-07-31 21:03:51:384 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:51:749 ==>> [D][05:18:15][HSDK][0] flush to flash addr:[0xE42100] --- write len --- [256]
[W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3155  volt:5545 mv
[D][05:18:15][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:15][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:15][COMM]adc read right brake adc:9  volt:11 mv
[D][05:18:15][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:15][COMM]adc read battery ts volt:13 mv
[D][05:18:15][COMM]adc read in 24v adc:1304  volt:32982 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3091  volt:5433 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1454  volt:33711 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
$GBGGA,130355.505,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,59,,,40,60,,,40,39,,,39,41,,,38,1*72

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,1,,,36,1*4F

$GBGSV,7,4,27,7,,,36,42,,,36,9,,,35,6,,,35,1*4E

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,44,,

2025-07-31 21:03:51:795 ==>> ,34,1*4B

$GBGSV,7,6,27,34,,,33,10,,,33,4,,,33,5,,,33,1*75

$GBGSV,7,7,27,8,,,32,12,,,32,23,,,31,1*4B

$GBRMC,130355.505,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130355.505,0.000,1495.577,1495.577,47.853,2097152,2097152,2097152*5D



2025-07-31 21:03:51:824 ==>>                                          

2025-07-31 21:03:51:920 ==>> 【转刹把供电电压】通过,【5433mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 21:03:51:924 ==>> 检测【关闭转刹把供电2】
2025-07-31 21:03:51:928 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:52:142 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:03:52:202 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 21:03:52:207 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 21:03:52:209 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:52:307 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 21:03:52:322 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:03:52:367 ==>> 1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 21:03:52:446 ==>> 【读取AD_V15电压(后)】通过,【2mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:03:52:449 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 21:03:52:454 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:52:547 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:03:52:652 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:52:727 ==>> $GBGGA,130356.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,41,3,,,40,24,,,40,25,,,40,1*46

$GBGSV,7,2,27,59,,,39,60,,,39,39,,,38,41,,,38,1*73

$GBGSV,7,3,27,40,,,37,14,,,37,16,,,36,7,,,36,1*46

$GBGSV,7,4,27,42,,,36,1,,,35,9,,,35,6,,,35,1*4B

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,33,10,,,33,4,,,33,5,,,32,1*74

$GBGSV,7,7,27,8,,,32,23,,,32,12,,,31,1*4B

$GBRMC,130356.505,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130356.505,0.000,1484.821,1484.821,47.501,2097152,2097152,2097152*54

[W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:03:52:757 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:03:52:787 ==>> [W][05:18:16][COMM]>>>>>Input command = ?<<<<<


2025-07-31 21:03:52:862 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 21:03:52:867 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:03:52:967 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 21:03:53:028 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 21:03:53:073 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 21:03:53:203 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 21:03:53:207 ==>> 检测【拉高OUTPUT3】
2025-07-31 21:03:53:212 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 21:03:53:269 ==>> 3A A3 03 01 A3 


2025-07-31 21:03:53:359 ==>> ON_OUT3
OVER 150


2025-07-31 21:03:53:501 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 21:03:53:508 ==>> 检测【拉高OUTPUT4】
2025-07-31 21:03:53:512 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 21:03:53:570 ==>> 3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 21:03:53:675 ==>> $GBGGA,130357.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,59,,,39,60,,,39,39,,,38,41,,,38,1*73

$GBGSV,7,3,27,40,,,37,14,,,37,16,,,37,7,,,36,1*47

$GBGSV,7,4,27,42,,,36,1,,,36,9,,,35,6,,,35,1*48

$GBGSV,7,5,27,13,,,35,2,,,35,38,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,33,10,,,33,4,,,33,23,,,33,1*41

$GBGSV,7,7,27,5,,,32

2025-07-31 21:03:53:720 ==>> ,8,,,32,12,,,31,1*7F

$GBRMC,130357.505,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130357.505,0.000,1490.964,1490.964,47.699,2097152,2097152,2097152*57



2025-07-31 21:03:53:796 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 21:03:53:806 ==>> 检测【拉高OUTPUT5】
2025-07-31 21:03:53:814 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 21:03:53:826 ==>> [D][05:18:17][COMM]read battery soc:255


2025-07-31 21:03:53:870 ==>> 3A A3 05 01 A3 


2025-07-31 21:03:53:960 ==>> ON_OUT5
OVER 150


2025-07-31 21:03:54:085 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 21:03:54:089 ==>> 检测【左刹电压测试1】
2025-07-31 21:03:54:097 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:54:382 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3141  volt:5521 mv
[D][05:18:18][COMM]adc read out 24v adc:3  volt:75 mv
[D][05:18:18][COMM]adc read left brake adc:1722  volt:2270 mv
[D][05:18:18][COMM]adc read right brake adc:1728  volt:2278 mv
[D][05:18:18][COMM]adc read throttle adc:1724  volt:2272 mv
[D][05:18:18][COMM]adc read battery ts volt:11 mv
[D][05:18:18][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:18][COMM]adc read throttle brake in adc:5  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:8  volt:6 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2407  volt:3878 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1453  volt:33688 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:03:54:630 ==>> 【左刹电压测试1】通过,【2270】符合目标值【2250】至【2500】要求!
2025-07-31 21:03:54:634 ==>> 检测【右刹电压测试1】
2025-07-31 21:03:54:672 ==>> 【右刹电压测试1】通过,【2278】符合目标值【2250】至【2500】要求!
2025-07-31 21:03:54:676 ==>> 检测【转把电压测试1】
2025-07-31 21:03:54:709 ==>> 【转把电压测试1】通过,【2272】符合目标值【2250】至【2500】要求!
2025-07-31 21:03:54:712 ==>> 检测【拉低OUTPUT3】
2025-07-31 21:03:54:715 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 21:03:54:720 ==>> $GBGGA,130358.505,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,27,40,,,37,14,,,37,16,,,36,7,,,36,1*46

$GBGSV,7,4,27,42,,,36,1,,,36,9,,,35,6,,,35,1*48

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,34,44,,,34,1*4A

$GBGSV,7,6,27,34,,,33,10,,,33,4,,,33,23,,,33,1*41

$GBGSV,7,7,27,5,,,32,8,,,32,12,,,31,1*7F

$GBRMC,130358.505,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130358.505,0.000,1490.968,1490.968,47.703,2097152,2097152,2097152*5A



2025-07-31 21:03:54:758 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 21:03:55:006 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 21:03:55:010 ==>> 检测【拉低OUTPUT4】
2025-07-31 21:03:55:014 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 21:03:55:068 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 21:03:55:298 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 21:03:55:304 ==>> 检测【拉低OUTPUT5】
2025-07-31 21:03:55:310 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 21:03:55:359 ==>> 3A A3 05 00 A3 


2025-07-31 21:03:55:464 ==>> OFF_OUT5
OVER 150


2025-07-31 21:03:55:588 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 21:03:55:592 ==>> 检测【左刹电压测试2】
2025-07-31 21:03:55:597 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:55:722 ==>> $GBGGA,130359.505,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,36,7,,,36,1*49

$GBGSV,7,4,27,42,,,36,1,,,36,9,,,35,6,,,35,1*48

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,35,44,,,34,1*4B

$GBGSV,7,6,27,34,,,34,10,,,33,4,,,33,23,,,33,1*46

$GBGSV,7,7,27,5,,,32,8,,,32,12,,,31,1*7F

$GBRMC,130359.505,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130359.505,0.000,1495.573,1495.573,47.849,2097152,2097152,2097152*5A



2025-07-31 21:03:55:827 ==>>                                                                                            

2025-07-31 21:03:55:917 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                     rake in adc:3  volt:5 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1454  volt:33711 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:3  volt:69 mv
[D][05:18:19][COMM]read battery soc:255


2025-07-31 21:03:56:101 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 21:03:56:379 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:18:20][COMM]adc read out 24v adc:15  volt:379 mv
[D][05:18:20][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:20][COMM]adc read right brake adc:15  volt:19 mv
[D][05:18:20][COMM]adc read throttle adc:9  volt:11 mv
[D][05:18:20][COMM]adc read battery ts volt:17 mv
[D][05:18:20][COMM]adc read in 24v adc:1305  volt:33007 mv
[D][05:18:20][COMM]adc read throttle brake in adc:8  volt:14 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:9  volt:7 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2408  volt:3880 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1454  volt:33711 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 21:03:56:653 ==>> 【左刹电压测试2】通过,【15】符合目标值【0】至【50】要求!
2025-07-31 21:03:56:660 ==>> 检测【右刹电压测试2】
2025-07-31 21:03:56:687 ==>> 【右刹电压测试2】通过,【19】符合目标值【0】至【50】要求!
2025-07-31 21:03:56:694 ==>> 检测【转把电压测试2】
2025-07-31 21:03:56:709 ==>> $GBGGA,130400.505,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,40,24,,,40,25,,,40,1*45

$GBGSV,7,2,27,60,,,40,59,,,39,39,,,39,41,,,38,1*7C

$GBGSV,7,3,27,40,,,38,14,,,37,16,,,37,7,,,36,1*48

$GBGSV,7,4,27,42,,,36,1,,,36,9,,,35,6,,,35,1*48

$GBGSV,7,5,27,13,,,35,38,,,35,2,,,34,44,,,34,1*4A

$GBGSV,7,6,27,34,,,34,10,,,33,4,,,33,23,,,33,1*46

$GBGSV,7,7,27,8,,,33,5,,,32,12,,,32,1*7D

$GBRMC,130400.505,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130400.505,0.000,1498.640,1498.640,47.943,2097152,2097152,2097152*5A



2025-07-31 21:03:56:720 ==>> 【转把电压测试2】通过,【11】符合目标值【0】至【50】要求!
2025-07-31 21:03:56:723 ==>> 检测【晶振检测】
2025-07-31 21:03:56:728 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 21:03:56:952 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:20][COMM][lf state:1][hf state:1]


2025-07-31 21:03:57:018 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 21:03:57:022 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 21:03:57:026 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 21:03:57:179 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1655mV
Get AD_V4 1655mV
Get AD_V5 2778mV
Get AD_V6 1990mV
Get AD_V7 1087mV
OVER 150


2025-07-31 21:03:57:304 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 21:03:57:308 ==>> 检测【检测BootVer】
2025-07-31 21:03:57:313 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:03:57:639 ==>> [D][05:18:21][HSDK][0] flush to flash addr:[0xE42200] --- write len --- [256]
[W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = ***************
[D][05:18:21][FCTY]HardwareID  = 867222087692408
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:21][FCTY]Bat         = 3924 mv
[D][05:18:21][FCTY]Current     = 0 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 234,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1302
[D][05:18:21][FCTY]Acckey1 vol = 5540 mv, Acckey2 vol = 202 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D

2025-07-31 21:03:57:744 ==>> ][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3717 mv
[D][05:18:21][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             

2025-07-31 21:03:57:849 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 21:03:57:857 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 21:03:57:861 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 21:03:57:865 ==>> 检测【检测固件版本】
2025-07-31 21:03:57:889 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 21:03:57:895 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 21:03:57:901 ==>> 检测【检测蓝牙版本】
2025-07-31 21:03:57:922 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 21:03:57:929 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 21:03:57:944 ==>> 检测【检测MoBikeId】
2025-07-31 21:03:57:954 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 21:03:57:958 ==>> 提取到MoBikeId:9999999999
2025-07-31 21:03:57:963 ==>> 检测【检测蓝牙地址】
2025-07-31 21:03:57:970 ==>> 取到目标值:D03656F70A7F
2025-07-31 21:03:57:987 ==>> 【检测蓝牙地址】通过,【D03656F70A7F】符合目标值【】要求!
2025-07-31 21:03:57:992 ==>> 提取到蓝牙地址:D03656F70A7F
2025-07-31 21:03:57:998 ==>> 检测【BOARD_ID】
2025-07-31 21:03:58:018 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 21:03:58:024 ==>> 检测【检测充电电压】
2025-07-31 21:03:58:052 ==>> 【检测充电电压】通过,【3924mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 21:03:58:056 ==>> 检测【检测VBUS电压1】
2025-07-31 21:03:58:085 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 21:03:58:089 ==>> 检测【检测充电电流】
2025-07-31 21:03:58:117 ==>> 【检测充电电流】通过,【0ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 21:03:58:124 ==>> 检测【检测IMEI】
2025-07-31 21:03:58:128 ==>> 取到目标值:867222087692408
2025-07-31 21:03:58:148 ==>> 【检测IMEI】通过,【867222087692408】符合目标值【】要求!
2025-07-31 21:03:58:152 ==>> 提取到IMEI:867222087692408
2025-07-31 21:03:58:158 ==>> 检测【检测IMSI】
2025-07-31 21:03:58:164 ==>> 取到目标值:***************
2025-07-31 21:03:58:184 ==>> 【检测IMSI】通过,【***************】符合目标值【】要求!
2025-07-31 21:03:58:189 ==>> 提取到IMSI:***************
2025-07-31 21:03:58:194 ==>> 检测【校验网络运营商(移动)】
2025-07-31 21:03:58:201 ==>> 取到目标值:***************
2025-07-31 21:03:58:223 ==>> 【校验网络运营商(移动)】通过,【***************】符合目标值【】要求!
2025-07-31 21:03:58:228 ==>> 检测【打开CAN通信】
2025-07-31 21:03:58:247 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 21:03:58:369 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 21:03:58:507 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:03:58:514 ==>> 检测【检测CAN通信】
2025-07-31 21:03:58:519 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 21:03:58:581 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:03:58:641 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:03:58:747 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33503
$GBGGA,130402.505,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,41,,,38,16,,,37,14,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,42,,,36,2,,,35,13,,,35,1*77

$GBGSV,7,5,27,38,,,35,9,,,35,6,,,35,44,,,34,1*74

$GBGSV,7,6,27,34,,,34,10,,,33,8,,,33,4,,,33,1*7F

$GBGSV,7,7,27,23,,,33,5,,,32,12,,,31,1*47

$GBRMC,130402.505,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130402.505,0.000,748.689,748.689,684.694,2097152,2097152,2097152*64

标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:03:58:820 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 21:03:58:824 ==>> 检测【关闭CAN通信】
2025-07-31 21:03:58:829 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 21:03:58:834 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:03:58:881 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 21:03:58:971 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 
[C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 21:03:59:131 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 21:03:59:135 ==>> 检测【打印IMU STATE】
2025-07-31 21:03:59:142 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:03:59:365 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:03:59:463 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:03:59:467 ==>> 检测【六轴自检】
2025-07-31 21:03:59:472 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 21:03:59:742 ==>> $GBGGA,130403.505,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,59,,,40,1*4E

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,41,,,38,16,,,37,14,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,9,,,36,42,,,36,2,,,35,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,6,,,35,44,,,34,1*4F

$GBGSV,7,6,27,34,,,34,10,,,33,8,,,33,4,,,33,1*7F

$GBGSV,7,7,27,23,,,33,5,,,32,12,,,32,1*44

$GBRMC,130403.505,V,,,,,,,310725,0.1,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130403.505,0.000,750.218,750.218,686.092,2097152,2097152,2097152*67

[W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 21:03:59:847 ==>> [D][05:18:23][COMM]read battery soc:255


2025-07-31 21:04:00:734 ==>> $GBGGA,130404.505,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,3,,,41,60,,,40,59,,,40,1*4F

$GBGSV,7,2,27,24,,,40,25,,,40,39,,,39,40,,,38,1*78

$GBGSV,7,3,27,41,,,38,16,,,37,14,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,9,,,36,42,,,36,2,,,35,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,6,,,35,44,,,34,1*4F

$GBGSV,7,6,27,34,,,34,10,,,33,8,,,33,4,,,33,1*7F

$GBGSV,7,7,27,23,,,33,5,,,32,12,,,32,1*44

$GBRMC,130404.505,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130404.505,0.000,750.987,750.987,686.795,2097152,2097152,2097152*60



2025-07-31 21:04:01:363 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 21:04:01:468 ==>> [D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36349 imu self tes

2025-07-31 21:04:01:498 ==>> t OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-20,-10,4066]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 21:04:01:583 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 21:04:01:587 ==>> 检测【打印IMU STATE2】
2025-07-31 21:04:01:593 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 21:04:01:815 ==>> $GBGGA,130405.505,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,27,33,,,42,60,,,40,3,,,40,24,,,40,1*44

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,40,,,38,1*7C

$GBGSV,7,3,27,41,,,38,16,,,37,14,,,37,7,,,36,1*49

$GBGSV,7,4,27,1,,,36,9,,,36,42,,,36,2,,,35,1*4F

$GBGSV,7,5,27,13,,,35,38,,,35,6,,,35,44,,,34,1*4F

$GBGSV,7,6,27,34,,,34,10,,,33,8,,,33,4,,,33,1*7F

$GBGSV,7,7,27,23,,,33,5,,,32,12,,,32,1*44

$GBRMC,130405.505,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130405.505,0.000,749.449,749.449,685.389,2097152,2097152,2097152*6B

[W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 21:04:01:860 ==>>                                          

2025-07-31 21:04:01:879 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 21:04:01:884 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 21:04:01:890 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:04:01:965 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:04:02:146 ==>> [D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:25][FCTY]get_ext_48v_vol retry i = 5,volt = 11


2025-07-31 21:04:02:165 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 21:04:02:170 ==>> 检测【检测VBUS电压2】
2025-07-31 21:04:02:176 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:04:02:544 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087692408
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 0 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 311
[D][05:18:26][FCTY]Acckey1 vol = 5528 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_

2025-07-31 21:04:02:590 ==>> RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3717 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:04:02:695 ==>>                                                                                                                               

$GBGSV,7,2,27,25,,,40,59,,,39,39,,,39,40,,,38,1*7C

$GBGSV,7,3,27,41,,,38,16,,,37,42,,,37,14,,,37,1*79

$GBGSV,7,4,27,7,,,36,1,,,36,9,,,36,2,,,35,1*7E

$GBGSV,7,5,27,13,,,35,38,,,35,6,,,35,44,,,34,1*4F

$GBGSV,7,6,27,34,,,34,10,,,33,8,,,33,4,,,33,1*7F

$GBGSV,7,7

2025-07-31 21:04:02:711 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:04:02:739 ==>> ,27,23,,,33,5,,,32,12,,,32,1*44

$GBRMC,130406.505,V,,,,,,,310725,0.1,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,130406.505,0.000,750.985,750.985,686.794,2097152,2097152,2097152*63



2025-07-31 21:04:03:027 ==>> [W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = ***************
[D][05:18:26][FCTY]HardwareID  = 867222087692408
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:26][FCTY]Bat         = 3944 mv
[D][05:18:26][FCTY]Current     = 50 ma
[D][05:18:26][FCTY]VBUS        = 11700 mv
[D][05:18:26][FCTY]TEMP= 0,BATID= 117,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 4, adc = 180
[D][05:18:26][FCTY]Acckey1 vol = 5551 mv, Acckey2 vol = 177 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b

2025-07-31 21:04:03:072 ==>> 5b1
[D][05:18:26][FCTY]Bat1         = 3717 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:04:03:250 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:04:03:620 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = ***************
[D][05:18:27][FCTY]HardwareID  = 867222087692408
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:27][FCTY]Bat         = 3944 mv
[D][05:18:27][FCTY]Current     = 50 ma
[D][05:18:27][FCTY]VBUS        = 11700 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 146,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 159
[D][05:18:27][FCTY]Acckey1 vol = 5538 mv, Acckey2 vol = 278 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][0

2025-07-31 21:04:03:725 ==>> 5:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3717 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          

2025-07-31 21:04:03:755 ==>>              

2025-07-31 21:04:03:821 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:04:03:920 ==>> [D][05:18:27][COMM]vbus remove, EXT_BAT_STATE_POWERON  ,vbuswake : 0
[D][05:18:27][COMM]frm_peripheral_device_poweroff type 16.... 


2025-07-31 21:04:04:525 ==>> [D][05:18:27][COMM]Main Task receive event:65
[D][05:18:27][COMM]main task tmp_sleep_event = 80
[D][05:18:27][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:18:27][COMM]Main Task receive event:65 finished processing
[D][05:18:27][COMM]Main Task receive event:60
[D][05:18:27][COMM]smart_helmet_vol=255,255
[D][05:18:27][COMM]BAT CAN get state1 Fail 204
[D][05:18:27][COMM]BAT CAN get soc Fail, 204
[W][05:18:27][GNSS]stop locating
[D][05:18:27][GNSS]stop event:8
[D][05:18:27][GNSS]GPS stop. ret=0
[D][05:18:27][GNSS]all continue location stop
[D][05:18:27][COMM]report elecbike
[W][05:18:27][PROT]remove success[1629955107],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:27][PROT]add success [1629955107],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][CAT1]gsm read msg sub id: 24
[D][05:18:27][PROT]index:0
[D][05:18:27][PROT]is_send:1
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x3
[D][05:18:27][PROT]msg_type:0x5d03
[D][05:18:27][PROT]==============================================

2025-07-31 21:04:04:630 ==>> =============
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]Sending traceid[9999999999900005]
[D][05:18:27][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:27][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:27][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:27][PROT]index:0 1629955107
[D][05:18:27][PROT]is_send:0
[D][05:18:27][PROT]sequence_num:4
[D][05:18:27][PROT]retry_timeout:0
[D][05:18:27][PROT]retry_times:3
[D][05:18:27][PROT]send_path:0x2
[D][05:18:27][PROT]min_index:0, type:0x5D03, priority:3
[D][05:18:27][PROT]===========================================================
[W][05:18:27][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955107]
[D][05:18:27][PROT]===========================================================
[D][05:18:27][PROT]sending traceid [9999999999900005]
[D][05:18:27][PROT]Send_TO_M2M [1629955107]
[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSPWR=0

[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:27][SAL ]sock send credit cnt[6]


2025-07-31 21:04:04:734 ==>> [D][05:18:27][SAL ]sock send ind credit cnt[6]
[D][05:18:27][M2M ]m2m send data len[198]
[D][05:18:27][SAL ]Cellular task submsg id[10]
[D][05:18:27][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:27][COMM]Main Task receive event:60 finished processing
[D][05:18:27][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:27][HSDK][0] flush to flash addr:[0xE42300] --- write len --- [256]
[W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = ***************
[D][05:18:27][FCTY]HardwareID  = 867222087692408
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:27][FCTY]Bat         = 3664 mv
[D][05:18:27][FCTY]Current     = 0 ma
[D][05:18:27][FCTY]VBUS        = 4900 mv
[D][05:18:27][CAT1]<<< 
OK


2025-07-31 21:04:04:839 ==>> 

[D][05:18:27][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:18:27][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 3, adc = 126
[D][05:18:27][FCTY]Acckey1 vol = 5528 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3717 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:18:27][CAT1]<<< 
OK

[D][05:18:27][CAT1]exec over: func id: 24, ret: 6
[D][05:18:27][CAT1]sub id: 24, ret: 6

[D][05:18:27][CAT1]gsm read msg sub id: 15
[D][05:18:27][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:28][CAT1]Send Data To Server[198][201] ... ->:
0063B98F113311331133113311331B88B5B3864AE87509932B6DBD0E38A3BF32EA2330A520F02B963F0C5F026D5EEA063764B0CD1FFD8E24271CE1

2025-07-31 21:04:04:905 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 21:04:04:915 ==>> 06AEC4B465E5A8D85A6F5F238F08F4CCFFF70D74B8BAD074FFB3CD8EE1E8BFC11E78474A334CF56C
[D][05:18:28][CAT1]<<< 
SEND OK

[D][05:18:28][CAT1]exec over: func id: 15, ret: 11
[D][05:18:28][CAT1]sub id: 15, ret: 11

[D][05:18:28][SAL ]Cellular task submsg id[68]
[D][05:18:28][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:28][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:28][M2M ]g_m2m_is_idle become true
[D][05:18:28][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:28][PROT]M2M Send ok [1629955108]


2025-07-31 21:04:05:230 ==>> [D][05:18:28][GNSS]recv submsg id[1]
[D][05:18:28][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:18:28][GNSS]location stop evt done evt
[W][05:18:28][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:28][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========
[D][05:18:28][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:28][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:28][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:28][FCTY]DeviceID    = ***************
[D][05:18:28][FCTY]HardwareID  = 867222087692408
[D][05:18:28][FCTY]MoBikeID    = 9999999999
[D][05:18:28][FCTY]LockID      = FFFFFFFFFF
[D][05:18:28][FCTY]BLEFWVersion= 105
[D][05:18:28][FCTY]BLEMacAddr   = D03656F70A7F
[D][05:18:28][FCTY]Bat         = 3664 mv
[D][05:18:28][FCTY]Current     = 0 ma
[D][05:18:28][FCTY]VBUS        = 4900 mv
[D][05:18:28][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:28][FCTY]Ext battery vol = 2, adc = 104
[D][05:18:28][FCTY]Acckey1 vol = 5521 mv, Acckey2 vol = 75 mv
[D][05:18:28][FCTY]Bike Type flag is invalied
[D][05:18:28][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:28][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:28][FCTY]CAT1_KERNEL_R

2025-07-31 21:04:05:275 ==>> TK = 1.2.4
[D][05:18:28][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:28][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:28][FCTY]Bat1         = 3717 mv
[D][05:18:28][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:28][FCTY]==========Modules-nRF5340 ==========


2025-07-31 21:04:05:485 ==>> 【检测VBUS电压2】通过,【4900mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 21:04:05:489 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 21:04:05:496 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 21:04:05:561 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 21:04:05:667 ==>> [D][05:18:29][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 32
[D][05:18

2025-07-31 21:04:05:697 ==>> :29][COMM]read battery soc:255


2025-07-31 21:04:05:782 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 21:04:05:787 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 21:04:05:791 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:04:05:863 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:04:06:091 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 21:04:06:098 ==>> 检测【打开WIFI(3)】
2025-07-31 21:04:06:104 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:04:06:259 ==>> [W][05:18:30][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:30][CAT1]gsm read msg sub id: 12
[D][05:18:30][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10



2025-07-31 21:04:06:384 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:04:06:389 ==>> 检测【扩展芯片hw】
2025-07-31 21:04:06:393 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:04:07:234 ==>> [D][05:18:31][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:31][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:0------------
[D][05:18:31][COMM]------------ready to Power on Acckey 2------------


2025-07-31 21:04:07:414 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 21:04:07:766 ==>>                                                                                           t Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]more than the number of battery plugs
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:31][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:31][COMM]Bat auth off fail, error:-1
[D][05:18:31][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:31][COMM]----- get Acckey 1 and value:1------------
[D][05:18:31][COMM]----- get Acckey 2 and value:1------------
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:31][COMM]file:B50 exist
[D][05:18:31][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:31][COMM]f:[ec800m_audio_play_pro

2025-07-31 21:04:07:872 ==>> cess].l:[920].cmd file 'B50'
[D][05:18:31][COMM]read file, len:10800, num:3
[D][05:18:31][COMM]Main Task receive event:65
[D][05:18:31][COMM]main task tmp_sleep_event = 80
[D][05:18:31][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:31][COMM]Main Task receive event:65 finished processing
[D][05:18:31][COMM]Main Task receive event:66
[D][05:18:31][COMM]Try to Auto Lock Bat
[D][05:18:31][COMM]Main Task receive event:66 finished processing
[D][05:18:31][COMM]Receive Bat Lock cmd 0
[D][05:18:31][COMM]VBUS is 1
[D][05:18:31][COMM]Main Task receive event:60
[D][05:18:31][COMM]smart_helmet_vol=255,255
[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get soc error
[E][05:18:31][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:31][COMM]report elecbike
[W][05:18:31][PROT]remove success[1629955111],send_path[3],type[0000],priority[0],index[1],used[0]
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:31][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:31][PROT]index:1
[D][05:18:31][PROT]is_send:1
[D][05:18:31][PR

2025-07-31 21:04:07:977 ==>> OT]sequence_num:5
[D][05:18:31][PROT]retry_timeout:0
[D][05:18:31][PROT]retry_times:3
[D][05:18:31][PROT]send_path:0x3
[D][05:18:31][PROT]msg_type:0x5d03
[D][05:18:31][PROT]===========================================================
[W][05:18:31][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955111]
[D][05:18:31][PROT]===========================================================
[D][05:18:31][PROT]Sending traceid[9999999999900006]
[D][05:18:31][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:31][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:31][PROT]ble is not inited or not connected or cccd not enabled
[W][05:18:31][PROT]add success [1629955111],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:18:31][COMM]Main Task receive event:60 finished processing
[D][05:18:31][COMM]Main Task receive event:61
[D][05:18:31][COMM][D301]:type:3, trace id:280
[D][05:18:31][COMM]id[], hw[000
[D][05:18:31][COMM]get mcMaincircuitVolt error
[D][05:18:31][COMM]get mcSubcircuitVolt error
[D][05:18:31][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:31][COMM]--->crc16:0xb8a
[D][05:18:31][COMM]read file success


2025-07-31 21:04:08:083 ==>> 
[W][05:18:31][COMM][Audio].l:[936].close hexlog save
[D][05:18:31][COMM]accel parse set 1
[D][05:18:31][COMM][Audio]mon:9,05:18:31
[D][05:18:31][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:31][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:31][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:31][COMM]BAT CAN get state1 Fail 204
[D][05:18:31][COMM]BAT CAN get soc Fail, 204
[D][05:18:31][COMM]get bat work state err
[W][05:18:31][PROT]remove success[1629955111],send_path[2],type[0000],priority[0],index[2],used[0]
[D][05:18:31][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:31][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:31][PROT]add success [1629955111],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:18:31][COMM]Main Task receive event:61 finished processing
[D][05:18:31][COMM]read battery soc:255


2025-07-31 21:04:08:188 ==>> [D][05:18:32][COMM]43050 imu init OK
[D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:08:294 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version

2025-07-31 21:04:08:338 ==>> :[0x1], build_time:[GD  May 24 2024]
[W][05:18:32][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:32][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]


2025-07-31 21:04:08:398 ==>>                                                                                                                                                                                                                                                                

2025-07-31 21:04:08:505 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 21:04:08:512 ==>> 检测【扩展芯片boot】
2025-07-31 21:04:08:576 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 21:04:08:581 ==>> 检测【扩展芯片sw】
2025-07-31 21:04:08:621 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 21:04:08:629 ==>> 检测【检测音频FLASH】
2025-07-31 21:04:08:645 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 21:04:08:852 ==>> [W][05:18:32][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 21:04:09:265 ==>> [D][05:18:33][COMM]44061 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][CAT1]SEND RAW data timeout
[D][05:18:33][CAT1]exec over: func id: 12, ret: -52


2025-07-31 21:04:09:554 ==>> [D][05:18:33][PROT]CLEAN,SEND:0
[D][05:18:33][PROT]index:1 1629955113
[D][05:18:33][PROT]is_send:0
[D][05:18:33][PROT]sequence_num:5
[D][05:18:33][PROT]retry_timeout:0
[D][05:18:33][PROT]retry_times:3
[D][05:18:33][PROT]send_path:0x2
[D][05:18:33][PROT]min_index:1, type:0x5D03, priority:4
[D][05:18:33][PROT]===========================================================
[W][05:18:33][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955113]
[D][05:18:33][PROT]===========================================================
[D][05:18:33][PROT]sending traceid [9999999999900006]
[D][05:18:33][PROT]Send_TO_M2M [1629955113]
[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:33][SAL ]sock send credit cnt[6]
[D][05:18:33][SAL ]sock send ind credit cnt[6]
[D][05:18:33][M2M ]m2m send data len[198]
[D][05:18:33][SAL ]Cellular task submsg id[10]
[D][05:18:33][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:33][CAT1]gsm read msg sub id: 15
[D][05:18:33][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:33][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:33][COMM]f:[drv_audio_ack_receive].wait ack timeout!![44274]
[D][05:18:33][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:33][COMM]f:[ec800m_audio_start].l:[682].audio 

2025-07-31 21:04:09:584 ==>> cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954



2025-07-31 21:04:09:689 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 21:04:10:209 ==>> [D][05:18:34][COMM]45072 imu init OK
[D][05:18:34][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:10:465 ==>> [D][05:18:34][COMM]f:[drv_audio_ack_receive].wait ack timeout!![45300]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:34][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:04:11:237 ==>> [D][05:18:35][COMM]46085 imu init OK
[D][05:18:35][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:11:713 ==>> D][05:18:35][COMM]read battery soc:255


2025-07-31 21:04:12:235 ==>> [D][05:18:36][COMM]47096 imu init OK
[D][05:18:36][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:12:741 ==>> [D][05:18:36][COMM]crc 108B
[D][05:18:36][COMM]flash test ok


2025-07-31 21:04:13:247 ==>> [D][05:18:37][COMM]48107 imu init OK
[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:13:700 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 21:04:13:711 ==>> 检测【打开喇叭声音】
2025-07-31 21:04:13:734 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 21:04:13:739 ==>> [D][05:18:37][COMM]read battery soc:255


2025-07-31 21:04:13:866 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:37][COMM]file:A20 exist
[D][05:18:37][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:37][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]


2025-07-31 21:04:13:995 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 21:04:14:000 ==>> 检测【打开大灯控制】
2025-07-31 21:04:14:005 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 21:04:14:121 ==>> [W][05:18:37][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 21:04:14:226 ==>> [D][05:18:38][COMM]49118 imu init OK
[D][05:18:38][COMM]imu_task im

2025-07-31 21:04:14:256 ==>> u work error:[-1]. goto init


2025-07-31 21:04:14:305 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 21:04:14:310 ==>> 检测【关闭仪表供电3】
2025-07-31 21:04:14:314 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 21:04:14:451 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:38][COMM]set POWER 0


2025-07-31 21:04:14:610 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 21:04:14:615 ==>> 检测【关闭AccKey2供电3】
2025-07-31 21:04:14:623 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 21:04:14:741 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 21:04:14:925 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 21:04:14:930 ==>> 检测【读大灯电压】
2025-07-31 21:04:14:934 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:04:15:155 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[33201]


2025-07-31 21:04:15:220 ==>> 【读大灯电压】通过,【33201mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 21:04:15:226 ==>> 检测【关闭大灯控制2】
2025-07-31 21:04:15:234 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 21:04:15:260 ==>> [D][05:18:39][COMM]50130 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:15:350 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 21:04:15:528 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 21:04:15:536 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 21:04:15:545 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 21:04:15:757 ==>> [D][05:18:39][COMM]read battery soc:255
[W][05:18:39][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:39][COMM]arm_hub read adc[5],val[46]


2025-07-31 21:04:15:823 ==>> 【关大灯控制后读大灯电压】通过,【46mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 21:04:15:830 ==>> 检测【打开WIFI(4)】
2025-07-31 21:04:15:842 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 21:04:16:027 ==>> [W][05:18:39][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 21:04:16:166 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 21:04:16:178 ==>> 检测【EC800M模组版本】
2025-07-31 21:04:16:199 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:16:267 ==>> [D][05:18:40][COMM]51141 imu init OK
[D][05:18:40][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:17:193 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:17:286 ==>> [D][05:18:41][COMM]52152 imu init OK
[D][05:18:41][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 21:04:17:728 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 21:04:18:095 ==>> [W][05:18:41][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:18:230 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:18:291 ==>> [D][05:18:42][COMM]imu error,enter wait


2025-07-31 21:04:19:275 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:19:532 ==>> [D][05:18:43][CAT1]exec over: func id: 15, ret: -93
[D][05:18:43][CAT1]sub id: 15, ret: -93

[D][05:18:43][SAL ]Cellular task submsg id[68]
[D][05:18:43][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:18:43][SAL ]socket send fail. id[4]
[D][05:18:43][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:18:43][CAT1]gsm read msg sub id: 12
[D][05:18:43][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:43][M2M ]m2m select fd[4]
[D][05:18:43][M2M ]socket[4] Link is disconnected
[D][05:18:43][M2M ]tcpclient close[4]
[D][05:18:43][SAL ]socket[4] has closed
[D][05:18:43][PROT]protocol read data ok
[E][05:18:43][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:18:43][PROT]M2M Send Fail [1629955123]
[D][05:18:43][PROT]CLEAN,SEND:1
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:18:43][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK


2025-07-31 21:04:19:864 ==>> [D][05:18:43][COMM]f:[drv_audio_ack_receive].wait ack timeout!![54562]
[D][05:18:43][COMM]accel parse set 0
[D][05:18:43][COMM][Audio].l:[1032].open hexlog save
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:43][COMM]file:A20 exist
[D][05:18:43][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:43][COMM]read file, len:15228, num:4
[D][05:18:43][COMM]read battery soc:255
[D][05:18:43][COMM]--->crc16:0x419c
[D][05:18:43][COMM]read file success
[W][05:18:43][COMM][Audio].l:[936].close hexlog save
[D][05:18:43][COMM]accel parse set 1
[D][05:18:43][COMM][Audio]mon:9,05:18:43
[D][05:18:43][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:43][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:43][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:04:20:121 ==>> [W][05:18:43][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:20:305 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:20:822 ==>> [D][05:18:44][COMM]f:[drv_audio_ack_receive].wait ack timeout!![55671]
[D][05:18:44][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:44][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:04:21:336 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:21:728 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 21:04:21:833 ==>> [D][05:18:45][COMM]f:[drv_audio_ack_receive].wait ack timeout!![56700]
[D][05:18:45][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:45][C

2025-07-31 21:04:21:863 ==>> OMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796



2025-07-31 21:04:22:184 ==>> [W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:22:368 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:22:445 ==>> [D][05:18:46][CAT1]SEND RAW data timeout
[D][05:18:46][CAT1]exec over: func id: 12, ret: -52
[W][05:18:46][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:46][CAT1]gsm read msg sub id: 12
[D][05:18:46][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL



2025-07-31 21:04:22:885 ==>> [D][05:18:46][COMM]f:[drv_audio_ack_receive].wait ack timeout!![57729]
[D][05:18:46][COMM]f:[ec800m_audio_play_process].l:[956].start ret: -1
[D][05:18:46][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0



2025-07-31 21:04:23:285 ==>> [D][05:18:47][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:23:406 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:23:719 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 21:04:24:440 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:24:470 ==>> [W][05:18:48][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:25:463 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:25:473 ==>> [D][05:18:49][CAT1]SEND RAW data timeout
[D][05:18:49][CAT1]exec over: func id: 12, ret: -52
[W][05:18:49][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:49][CAT1]gsm read msg sub id: 10
[D][05:18:49][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:04:25:793 ==>> [D][05:18:49][COMM]read battery soc:255
[D][05:18:49][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:26:494 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:27:473 ==>> [W][05:18:51][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:27:533 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:27:743 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 21:04:28:287 ==>> [D][05:18:52][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:28:559 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:29:524 ==>> [W][05:18:53][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:29:599 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:29:749 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 21:04:30:627 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:30:793 ==>> [D][05:18:54][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:31:568 ==>> [W][05:18:55][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:31:673 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:31:766 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 21:04:32:710 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:33:286 ==>> [D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:33:451 ==>> [D][05:18:57][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:04:33:632 ==>> [W][05:18:57][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:33:752 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:33:767 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 21:04:34:801 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:35:223 ==>> [D][05:18:59][COMM]f:[drv_audio_ack_receive].wait ack timeout!![70073]
[D][05:18:59][COMM]accel parse set 0
[D][05:18:59][COMM][Audio].l:[1032].open hexlog save
[D][05:18:59][COMM]f:[ec800m_audio_play_process].l:[1037].play audio file play fail


2025-07-31 21:04:35:668 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:35:773 ==>> [D][05:18:59][COMM]read battery soc:255
[D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:35:848 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:36:876 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:37:792 ==>> [D][05:19:01][HSDK][0] flush to flash addr:[0xE42400] --- write len --- [256]
[W][05:19:01][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:01][COMM]read battery soc:255


2025-07-31 21:04:37:928 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:38:283 ==>> [D][05:19:02][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:38:963 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:39:793 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:19:03][COMM]read battery soc:255


2025-07-31 21:04:39:995 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:40:786 ==>> [D][05:19:04][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:41:033 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:41:465 ==>> [D][05:19:05][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 21:04:41:829 ==>> [D][05:19:05][COMM]read battery soc:255
[W][05:19:05][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:42:075 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:43:117 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:43:283 ==>> [D][05:19:07][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:43:805 ==>> [D][05:19:07][COMM]read battery soc:255


2025-07-31 21:04:43:865 ==>> [W][05:19:07][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:44:150 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:45:189 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:45:789 ==>> [D][05:19:09][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d
[D][05:19:09][COMM]read battery soc:25

2025-07-31 21:04:45:811 ==>> 5


2025-07-31 21:04:45:916 ==>> [W][05:19:09][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:46:233 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 21:04:47:256 ==>> 未匹配到【EC800M模组版本】数据,请核对检查!
2025-07-31 21:04:47:265 ==>> #################### 【测试结束】 ####################
2025-07-31 21:04:47:434 ==>> 关闭5V供电
2025-07-31 21:04:47:443 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 21:04:47:560 ==>> 5A A5 04 5A A5 


2025-07-31 21:04:47:666 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 21:04:47:801 ==>> [D][05:19:11][COMM]read battery soc:255


2025-07-31 21:04:47:968 ==>> [W][05:19:11][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<


2025-07-31 21:04:48:283 ==>> [D][05:19:12][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 21:04:48:436 ==>> 关闭5V供电成功
2025-07-31 21:04:48:445 ==>> 关闭33V供电
2025-07-31 21:04:48:454 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 21:04:48:559 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 21:04:48:754 ==>> [D][05:19:12][FCTY]get_ext_48v_vol retry i = 0,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 1,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 2,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 3,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 4,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 5,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 6,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 7,volt = 14
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 8,volt = 14
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 5


2025-07-31 21:04:49:437 ==>> 关闭33V供电成功
2025-07-31 21:04:49:447 ==>> 关闭3.7V供电
2025-07-31 21:04:49:456 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 21:04:49:557 ==>> 6A A6 02 A6 6A 


2025-07-31 21:04:49:662 ==>> [D][05:19:13][CAT1]exec over: func id: 10, ret: -43
[D][05:19:13][CAT1]sub id: 10, ret: -43

[D][05:19:13][SAL ]Cellular task submsg id[68]
[D][05:19:13][SAL ]handle subcmd ack sub_id[a], socket[0], result[-43]
[D][05:19:13][M2M ]m2m gsm shut done, ret[1]
[D][05:19:13][CAT1]gsm read msg sub id: 12
[D][05:19:13][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_RESET
[D][05:19:13][M2M ]M2M_GSM_SOCKET_RESET REACH THE LIMIT,ENTER IDLE

2025-07-31 21:04:49:767 ==>> 
[D][05:19:13][M2M ]g_m2m_is_idle become true
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:13][M2M ]M2M_GSM_SOCKET_IDLE, g_tcp_reconnect_times = 1
[D][05:19:13][PROT]index:1 1629955153
[D][05:19:13][PROT]is_send:0
[D][05:19:13][PROT]sequence_num:5
[D][05:19:13][PROT]retry_timeout:0
[D][05:19:13][PROT]retry_times:2
[D][05:19:13][PROT]send_path:0x2
[D][05:19:13][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:13][PROT]===========================================================
[W][05:19:13][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955153]
[D][05:19:13][PROT]===========================================================
[D][05:19:13][PROT]sending traceid [9999999999900006]
[D][05:19:13][PROT]Send_TO_M2M [1629955153]
[D][05:19:13][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:13][SAL ]open socket ind id[4], rst[0]
[D][05:19:13][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:13][SAL ]Cellular task submsg id[8]
[D][05:19:13][SAL ]cellular OPEN socket size[144], msg->data[0x20053040], socket[0]
[D][05:19:13][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]

2025-07-31 21:04:49:827 ==>> 
[D][05:19:13][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:13][COMM]Main Task receive event:93
[D][05:19:13][COMM]main task tmp_sleep_event = 80
[W][05:19:13][PROT]remove success[1629955153],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:19:13][PROT]add success [1629955153],send_path[2],type[8301],priority[0],index[3],used[1]
[D][05:19:13][COMM]Main Task receive event:93 finished processing
Battery OFF
OVER 150


2025-07-31 21:04:50:301 ==>>  

2025-07-31 21:04:50:452 ==>> 关闭3.7V供电成功
