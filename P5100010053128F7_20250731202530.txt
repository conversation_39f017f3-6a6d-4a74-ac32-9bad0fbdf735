2025-07-31 20:25:30:054 ==>> MES查站成功:
查站序号:P5100010053128F7验证通过
2025-07-31 20:25:30:069 ==>> 扫码结果:P5100010053128F7
2025-07-31 20:25:30:086 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:25:30:088 ==>> 测试参数版本:2024.10.11
2025-07-31 20:25:30:089 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:25:30:090 ==>> 检测【打开透传】
2025-07-31 20:25:30:092 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:25:30:146 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:25:30:419 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:25:30:424 ==>> 检测【检测接地电压】
2025-07-31 20:25:30:427 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:25:30:555 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:25:30:695 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:25:30:697 ==>> 检测【打开小电池】
2025-07-31 20:25:30:700 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:25:30:752 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:25:30:973 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:25:30:975 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:25:30:978 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:25:31:056 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:25:31:254 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:25:31:257 ==>> 检测【等待设备启动】
2025-07-31 20:25:31:260 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:25:31:516 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:25:31:711 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:25:32:296 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:25:32:478 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[W][05:17:49][PROT]Low Battery, Will Not Power On GSM
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][GNSS]start sing locating


2025-07-31 20:25:32:870 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:25:33:325 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:25:33:340 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:25:33:629 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:25:33:631 ==>> 检测【产品通信】
2025-07-31 20:25:33:632 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:25:33:833 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:51][COMM]Password OK


2025-07-31 20:25:33:938 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:25:33:940 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:25:33:942 ==>> 检测【初始化完成检测】
2025-07-31 20:25:33:943 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:25:34:181 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE43600] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:25:34:238 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:25:34:241 ==>> 检测【关闭大灯控制1】
2025-07-31 20:25:34:243 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:25:34:552 ==>> [D][05:17:51][COMM]2658 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:25:34:785 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:25:34:789 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:25:34:791 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:25:34:941 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:25:35:077 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:25:35:079 ==>> 检测【关闭仪表供电】
2025-07-31 20:25:35:080 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:25:35:246 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:25:35:351 ==>> [D][05:17:52][COMM]3669 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:25:35:372 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:25:35:375 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:25:35:378 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:25:35:502 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:25:35:692 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:25:35:695 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:25:35:697 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:25:35:805 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:25:35:983 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:25:35:986 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:25:35:988 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:25:36:137 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE43700] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:25:36:267 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:25:36:270 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:25:36:271 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:25:36:347 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:25:36:452 ==>> [D][05:17:53][COMM]4681 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][COMM

2025-07-31 20:25:36:482 ==>> ]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:25:36:546 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:25:36:548 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:25:36:549 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:25:36:648 ==>> 5A A5 03 5A A5 


2025-07-31 20:25:36:753 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:25:36:816 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:25:36:820 ==>> 该项需要延时执行
2025-07-31 20:25:36:858 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5008. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5009. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5009. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5010. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5010. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5010. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5011. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5011. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5011. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5012. period:500. j,i:20 73
[D][05:17:54][COMM]bat

2025-07-31 20:25:36:918 ==>>  msg 0258 loss. last_tick:0. cur_tick:5012. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5012. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5013. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5013
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5013


2025-07-31 20:25:37:206 ==>> [D][05:17:54][CAT1]power_urc_cb ret[5]


2025-07-31 20:25:37:416 ==>> [D][05:17:54][COMM]5692 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:25:38:039 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:25:38:555 ==>>                                                                                                                                                  ----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task 

2025-07-31 20:25:38:660 ==>> tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:5

2025-07-31 20:25:38:765 ==>> 5][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],

2025-07-31 20:25:38:825 ==>> index[3],used[0]
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][COMM]6703 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255


2025-07-31 20:25:39:435 ==>> [D][05:17:56][COMM]7715 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:25:40:236 ==>> [D][05:17:57][CAT1]power_urc_cb ret[76]


2025-07-31 20:25:40:491 ==>> [D][05:17:57][COMM]8725 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:57][COMM]read battery soc:255


2025-07-31 20:25:40:821 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:25:40:824 ==>> 检测【33V输入电压ADC】
2025-07-31 20:25:40:827 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:25:41:154 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3137  volt:5514 mv
[D][05:17:58][COMM]adc read out 24v adc:1317  volt:33310 mv
[D][05:17:58][COMM]adc read left brake adc:16  volt:21 mv
[D][05:17:58][COMM]adc read right brake adc:10  volt:13 mv
[D][05:17:58][COMM]adc read throttle adc:15  volt:19 mv
[D][05:17:58][COMM]adc read battery ts volt:19 mv
[D][05:17:58][COMM]adc read in 24v adc:1306  volt:33032 mv
[D][05:17:58][COMM]adc read throttle brake in adc:9  volt:15 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2401  volt:3868 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3359  volt:2706 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:25:41:396 ==>> 【33V输入电压ADC】通过,【33032mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:25:41:420 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:25:41:423 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:25:41:465 ==>> [D][05:17:58][COMM]9736 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2759mV
Get AD_V6 1991mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:25:41:698 ==>> 【TP7_VCC3V3(ADV2)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:41:701 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:25:41:717 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:41:719 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:25:41:724 ==>> 原始值:【2759】, 乘以分压基数【2】还原值:【5518】
2025-07-31 20:25:41:743 ==>> 【TP68_VCC5V5(ADV5)】通过,【5518mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:25:41:748 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:25:41:780 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10020. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10021. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10022. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10022
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10023


2025-07-31 20:25:41:783 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:25:41:785 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:25:41:816 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:25:41:819 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:25:41:963 ==>> 1A A1 00 00 FC 
Get AD_V2 1664mV
Get AD_V3 1660mV
Get AD_V4 1mV
Get AD_V5 2757mV
Get AD_V6 1991mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:25:42:096 ==>> 【TP7_VCC3V3(ADV2)】通过,【1664mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:42:099 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:25:42:116 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:42:119 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:25:42:121 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 20:25:42:144 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:25:42:147 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:25:42:165 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:25:42:168 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:25:42:206 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:25:42:208 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:25:42:358 ==>> 1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2757mV
Get AD_V6 1992mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:25:42:464 ==>> [D][05:17:59

2025-07-31 20:25:42:488 ==>> 【TP7_VCC3V3(ADV2)】通过,【1665mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:42:490 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:25:42:494 ==>> ][COMM]10749 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][COMM]read battery soc:255


2025-07-31 20:25:42:514 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1660mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:42:517 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:25:42:520 ==>> 原始值:【2757】, 乘以分压基数【2】还原值:【5514】
2025-07-31 20:25:42:542 ==>> 【TP68_VCC5V5(ADV5)】通过,【5514mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:25:42:546 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:25:42:574 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:25:42:576 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:25:42:615 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:25:42:619 ==>> 检测【打开WIFI(1)】
2025-07-31 20:25:42:644 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:25:42:841 ==>> [D][05:18:00][HSDK][0] flush to flash addr:[0xE43800] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:25:42:913 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:25:42:916 ==>> 检测【清空消息队列(1)】
2025-07-31 20:25:42:918 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:25:43:470 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]tx ret[4] >>> AT

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[6] >>> ATE0

[D][05:18:00][CAT1]<<< 

OK

[D][05:18:00][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:00][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:18:00][CAT1]<<< 
+CFUN: 1

OK

[D][05:18:00][CAT1]exec over: func id: 1, ret: 18
[D][05:18:00][CAT1]sub id: 1, ret: 18

[D][05:18:00][SAL ]Cellular task submsg id[68]
[D][05:18:00][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:18:00][SAL ]gsm power on ind rst[18]
[D][05:18:00][M2M ]m2m gsm power on, ret[0]
[D][05:18:00][COMM][Audio]exec status ready.
[D][05:18:00][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:18:00][M2M ]first set address
[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:18:00][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:18:00][COMM]set time err 2021
[D][05:18:00][CAT1]gsm read msg sub id: 31
[D][05:18:00][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1

2025-07-31 20:25:43:514 ==>> ,0,1,0,1,0,0,16,2000

[D][05:18:00][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:18:00][COMM]Main Task receive event:1
[D][05:18:00][COMM]Main Task receive event:1 finished processing


2025-07-31 20:25:43:739 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:25:43:742 ==>> 检测【打开GPS(1)】
2025-07-31 20:25:43:745 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:25:44:005 ==>>                                          [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087966562

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541421

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:01][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:01][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[W][05:18:01][COMM]>>>>>Input c

2025-07-31 20:25:44:050 ==>> ommand = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:25:44:125 ==>>                                                                                                                                                                           

2025-07-31 20:25:44:288 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:25:44:292 ==>> 检测【打开GSM联网】
2025-07-31 20:25:44:295 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:25:44:440 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:25:44:487 ==>>             ][COMM]read battery soc:255


2025-07-31 20:25:44:595 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:25:44:598 ==>> 检测【打开仪表供电1】
2025-07-31 20:25:44:600 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:25:45:006 ==>> [D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:02][COMM]set POWER 1
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 5, ret: 6
[D][05:18:02][CAT1]sub id: 5, ret: 6

[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:02][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:02][M2M ]M2M_GSM_INIT OK
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:02][SAL ]open socket ind id[4], rst[0]
[D][05:18:02][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:02][SAL ]Cellular task submsg id[8]
[D][05:18:02][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:02][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:02][CAT1]gsm read msg sub id: 8
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?



2025-07-31 20:25:45:095 ==>> 
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:02][COMM]Main Task receive event:4
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:02][COMM]init key as 
[D][05:18:02][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:02][COMM]Main Task receive event:4 finished processing
[D][05:18:02][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:02][CAT1]<<< 
+QIACT: 1,1,1,"************"

OK

[D][05:18:02][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:25:45:151 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:25:45:154 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:25:45:160 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:25:45:200 ==>> [D][05:18:02][CAT1]opened : 0, 0
[D][05:18:02][SAL ]Cellular task submsg id[68]
[D][05:18:02][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:02][SAL ]socket connect ind. id[4], rst[

2025-07-31 20:25:45:230 ==>> 3]
[D][05:18:02][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:02][M2M ]g_m2m_is_idle become true
[D][05:18:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:25:45:334 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:25:45:427 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:25:45:430 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:25:45:432 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:25:45:699 ==>> [D][05:18:02][GNSS]recv submsg id[1]
[D][05:18:02][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:02][GNSS]location recv gms init done evt
[D][05:18:02][GNSS]GPS start. ret=0
[D][05:18:02][CAT1]gsm read msg sub id: 23
[D][05:18:02][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:02][COMM]13760 imu init OK
[D][05:18:02][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:02][COMM]arm_hub read adc[3],val[33247]


2025-07-31 20:25:45:963 ==>> 【读取主控ADC采集的仪表电压】通过,【33247mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:25:45:966 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:25:45:968 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:46:142 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:25:46:237 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:25:46:243 ==>> 检测【AD_V20电压】
2025-07-31 20:25:46:247 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:46:352 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:46:474 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[D][05:18:03][HSDK][0] flush to flash addr:[0xE43900] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:25:46:504 ==>>                                          

2025-07-31 20:25:46:686 ==>> 本次取值间隔时间:328ms
2025-07-31 20:25:46:705 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:46:807 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:47:005 ==>> 本次取值间隔时间:195ms
2025-07-31 20:25:47:066 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:25:47:170 ==>>                                                                                    

2025-07-31 20:25:47:216 ==>> 本次取值间隔时间:202ms
2025-07-31 20:25:47:246 ==>>                                                       ,,,39,1*75

$GBGSV,2,2,06,38,,,37,26,,,36,1*7E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1637.575,1637.575,52.357,2097152,2097152,2097152*49

[D][05:18:04][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:04][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:04][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]exec over: func id: 23, ret: 6
[D][05:18:04][CAT1]sub id: 23, ret: 6



2025-07-31 20:25:47:457 ==>> [D][05:18:04][GNSS]recv submsg id[1]
[D][05:18:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:25:47:533 ==>> 本次取值间隔时间:306ms
2025-07-31 20:25:47:671 ==>> 本次取值间隔时间:130ms
2025-07-31 20:25:47:676 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:47:780 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:47:855 ==>> [W][05:18:05][COMM]>>>>>Input command = ?<<<<<
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:25:47:931 ==>> 本次取值间隔时间:148ms
2025-07-31 20:25:47:958 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:48:070 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:48:220 ==>> 1A A1 10 00 00 
Get AD_V20 3mV
OVER 150
[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,33,,,43,24,,,41,25,,,41,14,,,41,1*70

$GBGSV,4,2,14,59,,,41,60,,,41,39,,,40,38,,,38,1*71

$GBGSV,4,3,14,16,,,38,2,,,38,1,,,37,26,,,36,1*75

$GBGSV,4,4,14,5,,,32,4,,,30,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1590.253,1590.253,50.902,2097152,2097152,2097152*41



2025-07-31 20:25:48:480 ==>> 本次取值间隔时间:398ms
2025-07-31 20:25:48:498 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:48:510 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:25:48:601 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:48:616 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, 

2025-07-31 20:25:48:646 ==>> enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:25:48:969 ==>> 本次取值间隔时间:366ms
2025-07-31 20:25:49:014 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:25:49:124 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:25:49:292 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,17,33,,,43,24,,,42,14,,,42,3,,,42,1*45

$GBGSV,5,2,17,25,,,41,59,,,41,60,,,41,39,,,40,1*71

$GBGSV,5,3,17,38,,,38,16,,,38,1,,,38,2,,,36,1*77

$GBGSV,5,4,17,40,,,36,26,,,35,5,,,33,34,,,33,1*40

$GBGSV,5,5,17,4,,,30,1*47

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1582.771,1582.771,50.671,2097152,2097152,2097152*4A

[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:06][COMM]oneline display ALL on 1
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1650mV
OVER 150


2025-07-31 20:25:49:413 ==>> 本次取值间隔时间:278ms
2025-07-31 20:25:49:496 ==>> 【AD_V20电压】通过,【1650mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:25:49:508 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:25:49:511 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:25:49:553 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:25:49:798 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:25:49:801 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:25:49:806 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:25:50:060 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:25:50:165 ==>> $GBGGA,122554.030,,,,,0,00

2025-07-31 20:25:50:224 ==>> ,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,33,,,43,24,,,42,14,,,42,3,,,41,1*48

$GBGSV,5,2,19,25,,,41,59,,,41,60,,,41,39,,,40,1*7F

$GBGSV,5,3,19,38,,,38,16,,,38,1,,,38,40,,,37,1*4E

$GBGSV,5,4,19,2,,,36,26,,,35,5,,,33,34,,,33,1*78

$GBGSV,5,5,19,4,,,30,13,,,20,9,,,40,1*74

$GBRMC,122554.030,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122554.030,0.000,1540.996,1540.996,49.423,2097152,2097152,2097152*5F



2025-07-31 20:25:50:509 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:25:50:737 ==>> $GBGGA,122554.530,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,43,24,,,42,14,,,42,3,,,41,1*40

$GBGSV,6,2,21,25,,,41,59,,,41,60,,,41,39,,,40,1*77

$GBGSV,6,3,21,38,,,38,16,,,38,1,,,38,40,,,38,1*49

$GBGSV,6,4,21,13,,,38,7,,,37,8,,,37,2,,,36,1*46

$GBGSV,6,5,21,9,,,35,26,,,35,5,,,33,34,,,33,1*79

$GBGSV,6,6,21,4,,,31,1*43

$GBRMC,122554.530,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122554.530,0.000,1575.432,1575.432,50.413,2097152,2097152,2097152*51



2025-07-31 20:25:50:827 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:25:51:041 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:08][COMM]oneline display read state:1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:25:51:738 ==>> $GBGGA,122555.510,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,42,14,,,42,3,,,42,1*46

$GBGSV,7,2,25,25,,,42,59,,,41,60,,,41,42,,,41,1*7C

$GBGSV,7,3,25,39,,,40,38,,,38,16,,,38,1,,,38,1*4D

$GBGSV,7,4,25,40,,,38,13,,,38,7,,,37,6,,,37,1*75

$GBGSV,7,5,25,8,,,36,2,,,36,9,,,36,26,,,36,1*44

$GBGSV,7,6,25,41,,,36,44,,,34,5,,,33,34,,,33,1*45

$GBGSV,7,7,25,4,,,31,1*47

$GBRMC,122555.510,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122555.510,0.000,1573.772,1573.772,50.359,2097152,2097152,2097152*5B



2025-07-31 20:25:51:858 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:25:52:058 ==>> [D][05:18:09][HSDK][0] flush to flash addr:[0xE43A00] --- write len --- [256]
[W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:09][COMM]oneline display read state:1
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:25:52:532 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:25:52:714 ==>> $GBGGA,122556.510,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,14,,,42,3,,,41,1*46

$GBGSV,7,2,26,25,,,41,59,,,41,60,,,41,42,,,41,1*7C

$GBGSV,7,3,26,39,,,40,38,,,38,16,,,38,1,,,38,1*4E

$GBGSV,7,4,26,40,,,38,13,,,38,6,,,37,9,,,37,1*78

$GBGSV,7,5,26,7,,,36,8,,,36,2,,,36,41,,,36,1*48

$GBGSV,7,6,26,26,,,35,44,,,34,5,,,33,34,,,33,1*44

$GBGSV,7,7,26,10,,,33,4,,,31,1*45

$GBRMC,122556.510,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122556.510,0.000,1561.081,1561.081,49.955,2097152,2097152,2097152*56



2025-07-31 20:25:52:897 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:25:52:915 ==>> #################### 【测试结束】 ####################
2025-07-31 20:25:52:935 ==>> 关闭5V供电
2025-07-31 20:25:52:939 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:25:53:052 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:25:53:730 ==>> $GBGGA,122557.510,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,14,,,42,3,,,41,1*46

$GBGSV,7,2,26,25,,,41,59,,,41,60,,,41,42,,,41,1*7C

$GBGSV,7,3,26,39,,,40,16,,,38,1,,,38,40,,,38,1*41

$GBGSV,7,4,26,13,,,38,38,,,37,9,,,37,6,,,36,1*79

$GBGSV,7,5,26,7,,,36,8,,,36,2,,,36,41,,,36,1*48

$GBGSV,7,6,26,26,,,35,44,,,34,5,,,33,34,,,33,1*44

$GBGSV,7,7,26,10,,,33,4,,,32,1*46

$GBRMC,122557.510,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122557.510,0.000,1559.483,1559.483,49.901,2097152,2097152,2097152*56



2025-07-31 20:25:53:942 ==>> 关闭5V供电成功
2025-07-31 20:25:53:946 ==>> 关闭33V供电
2025-07-31 20:25:53:950 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:25:54:047 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:25:54:152 ==>> [D][05:18:11][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 1,volt 

2025-07-31 20:25:54:212 ==>> = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:11][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:25:54:452 ==>> [D][05:18:11][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 8


2025-07-31 20:25:54:737 ==>> $GBGGA,122558.510,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,14,,,42,3,,,41,1*46

$GBGSV,7,2,26,25,,,41,59,,,41,60,,,41,42,,,41,1*7C

$GBGSV,7,3,26,39,,,40,16,,,38,1,,,38,40,,,38,1*41

$GBGSV,7,4,26,13,,,38,38,,,38,9,,,37,6,,,36,1*76

$GBGSV,7,5,26,7,,,36,8,,,36,2,,,36,41,,,36,1*48

$GBGSV,7,6,26,26,,,35,44,,,34,5,,,33,34,,,33,1*44

$GBGSV,7,7,26,10,,,33,4,,,32,1*46

$GBRMC,122558.510,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,122558.510,0.000,1561.078,1561.078,49.952,2097152,2097152,2097152*5F



2025-07-31 20:25:54:951 ==>> 关闭33V供电成功
2025-07-31 20:25:54:956 ==>> 关闭3.7V供电
2025-07-31 20:25:54:961 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:25:55:043 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:25:55:656 ==>>  

