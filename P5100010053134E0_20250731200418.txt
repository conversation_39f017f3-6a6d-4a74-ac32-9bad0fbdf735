2025-07-31 20:04:18:586 ==>> MES查站成功:
查站序号:P5100010053134E0验证通过
2025-07-31 20:04:18:597 ==>> 扫码结果:P5100010053134E0
2025-07-31 20:04:18:599 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:04:18:601 ==>> 测试参数版本:2024.10.11
2025-07-31 20:04:18:603 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:04:18:604 ==>> 检测【打开透传】
2025-07-31 20:04:18:606 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:04:18:738 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:04:18:882 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:04:18:888 ==>> 检测【检测接地电压】
2025-07-31 20:04:18:889 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:04:18:936 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:04:19:167 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:04:19:171 ==>> 检测【打开小电池】
2025-07-31 20:04:19:174 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:04:19:241 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:04:19:443 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:04:19:445 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:04:19:449 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:04:19:533 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:04:19:718 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:04:19:722 ==>> 检测【等待设备启动】
2025-07-31 20:04:19:724 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:04:19:977 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:04:20:159 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:04:20:748 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:04:20:838 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:04:21:219 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:04:21:701 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:04:21:795 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:04:21:797 ==>> 检测【产品通信】
2025-07-31 20:04:21:799 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:04:22:017 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:04:22:066 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:04:22:069 ==>> 检测【初始化完成检测】
2025-07-31 20:04:22:072 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:04:22:272 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!


2025-07-31 20:04:22:336 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:04:22:339 ==>> 检测【关闭大灯控制1】
2025-07-31 20:04:22:341 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:04:22:362 ==>> [D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:04:22:498 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:04:22:606 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:04:22:609 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:04:22:611 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:04:22:944 ==>> [D][05:17:51][COMM]2625 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:04:23:146 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:04:23:148 ==>> 检测【关闭仪表供电】
2025-07-31 20:04:23:149 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:04:23:340 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:04:23:418 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:04:23:420 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:04:23:422 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:04:23:601 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:04:23:700 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:04:23:702 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:04:23:703 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:04:23:797 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:23:902 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:04:23:981 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:04:23:983 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:04:23:995 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:24:128 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:04:24:252 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:04:24:255 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:04:24:256 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:04:24:339 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:04:24:489 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 31
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:04:24:522 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:04:24:527 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:04:24:530 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:04:24:639 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:04:24:793 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:04:24:797 ==>> 该项需要延时执行
2025-07-31 20:04:24:805 ==>> [D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:25:333 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5013. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5014. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5014. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5015. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5015. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5015. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5016. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5016. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5016. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5017. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5017. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5018. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault cha

2025-07-31 20:04:25:363 ==>> nge: 0x0000E00C71E22217->0x0008F00C71E22217 5018
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5019


2025-07-31 20:04:25:818 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:26:030 ==>> [D][05:17:54][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:0------------
[D][05:17:54][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:04:26:505 ==>>                                                         .... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]more than the number of battery plugs
[D][05:17:54][COMM]VBUS is 1
[D][05:17:54][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:54][COMM]file:B50 exist
[D][05:17:54][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:54][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:54][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:54][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:54][COMM]Bat auth off fail, error:-1
[D][05:17:54][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[D][05:17:54][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:54][COMM]----- get Acckey 1 and value:1------------
[D][05:17:54][COMM]----- get Acckey 2 and value:1------------
[E][05:17:54][COMM][Audio].l:[904].echo is not ready
[D][05:17:54][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][

2025-07-31 20:04:26:611 ==>> 05:17:54][COMM]Main Task receive event:65
[D][05:17:54][COMM]main task tmp_sleep_event = 80
[D][05:17:54][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:54][COMM]Main Task receive event:65 finished processing
[D][05:17:54][COMM]Main Task receive event:66
[D][05:17:54][COMM]Try to Auto Lock Bat
[D][05:17:54][COMM]Main Task receive event:66 finished processing
[D][05:17:54][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][PROT]index:2
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]=====================

2025-07-31 20:04:26:717 ==>> ======================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]remove succ

2025-07-31 20:04:26:778 ==>> ess[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]


2025-07-31 20:04:26:868 ==>>                                                                                                                                           [D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:04:27:838 ==>> [D][05:17:56][COMM]7681 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:28:473 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:04:28:810 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:04:28:814 ==>> 检测【33V输入电压ADC】
2025-07-31 20:04:28:818 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:28:841 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:29:142 ==>> [W][05:17:57][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:57][COMM]adc read vcc5v mc adc:3160  volt:5554 mv
[D][05:17:57][COMM]adc read out 24v adc:1311  volt:33159 mv
[D][05:17:57][COMM]adc read left brake adc:4  volt:5 mv
[D][05:17:57][COMM]adc read right brake adc:8  volt:10 mv
[D][05:17:57][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:57][COMM]adc read battery ts volt:9 mv
[D][05:17:57][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:17:57][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:17:57][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:57][COMM]arm_hub adc read vbat adc:2425  volt:3907 mv
[D][05:17:57][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:57][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:17:57][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:04:29:346 ==>> 【33V输入电压ADC】通过,【32754mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:04:29:348 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:04:29:358 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:04:29:447 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1654mV
Get AD_V4 0mV
Get AD_V5 2771mV
Get AD_V6 2025mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:04:29:616 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:29:618 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:04:29:637 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1654mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:29:639 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:04:29:642 ==>> 原始值:【2771】, 乘以分压基数【2】还原值:【5542】
2025-07-31 20:04:29:655 ==>> 【TP68_VCC5V5(ADV5)】通过,【5542mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:04:29:657 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:04:29:676 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2025mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:04:29:679 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:04:29:699 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:04:29:701 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:04:29:862 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1657mV
Get AD_V4 1mV
Get AD_V5 2775mV
Get AD_V6 1993mV
Get AD_V7 1092mV
OVER 150
[D][05:17:58][COMM]9703 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:04:29:975 ==>> 【TP7_VCC3V3(ADV2)】通过,【1668mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:29:983 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:04:29:993 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1657mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:29:996 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:04:29:999 ==>> 原始值:【2775】, 乘以分压基数【2】还原值:【5550】
2025-07-31 20:04:30:012 ==>> 【TP68_VCC5V5(ADV5)】通过,【5550mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:04:30:015 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:04:30:031 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:04:30:033 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:04:30:052 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:04:30:054 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:04:30:213 ==>> 1A A1 00 00 FC 
Get AD_V2 1666mV
Get AD_V3 1655mV
Get AD_V4 0mV
Get AD_V5 2772mV
Get AD_V6 2024mV
Get AD_V7 1092mV
OVER 150
[D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10001. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10002. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10003
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10003


2025-07-31 20:04:30:330 ==>> 【TP7_VCC3V3(ADV2)】通过,【1666mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:30:335 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:04:30:353 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1655mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:30:358 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:04:30:362 ==>> 原始值:【2772】, 乘以分压基数【2】还原值:【5544】
2025-07-31 20:04:30:372 ==>> 【TP68_VCC5V5(ADV5)】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:04:30:375 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:04:30:390 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:04:30:394 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:04:30:414 ==>> 【TP1_VCC12V(ADV7)】通过,【1092mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:04:30:416 ==>> 检测【打开WIFI(1)】
2025-07-31 20:04:30:419 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:04:30:515 ==>> [D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]power_urc_cb ret[76]


2025-07-31 20:04:30:620 ==>> [W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<


2025-07-31 20:04:30:683 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:04:30:686 ==>> 检测【清空消息队列(1)】
2025-07-31 20:04:30:688 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:04:31:080 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[D][05:17:59][CAT1]<<< 

OK

[W][05:17:59][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:17:59][COMM]Protocol queue cleaned by AT_CMD!
[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10714 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu 

2025-07-31 20:04:31:140 ==>> default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:04:31:219 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:04:31:222 ==>> 检测【打开GPS(1)】
2025-07-31 20:04:31:230 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:04:31:535 ==>>                                                                                                                                                                  T+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087738524

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539169

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating
[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:

2025-07-31 20:04:31:565 ==>> 18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:04:31:750 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:04:31:754 ==>> 检测【打开GSM联网】
2025-07-31 20:04:31:758 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:04:31:933 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable


2025-07-31 20:04:32:040 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:04:32:043 ==>> 检测【打开仪表供电1】
2025-07-31 20:04:32:045 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:04:32:238 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:04:32:316 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:04:32:320 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:04:32:323 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:04:32:434 ==>> [D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:04:32:539 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:01][COMM][oneline_display]: command mode, ON!
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:04:32:587 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:04:32:591 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:04:32:594 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:04:32:720 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:01][COMM]arm_hub read adc[3],val[33386]


2025-07-31 20:04:32:859 ==>> 【读取主控ADC采集的仪表电压】通过,【33386mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:04:32:862 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:04:32:864 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:04:33:026 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:01][COMM]oneline display ALL on 1
[D][05:18:01][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:04:33:135 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:04:33:138 ==>> 检测【AD_V20电压】
2025-07-31 20:04:33:139 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:04:33:239 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:04:33:333 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 2mV
OVER 150


2025-07-31 20:04:33:563 ==>> 本次取值间隔时间:313ms
2025-07-31 20:04:33:584 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:04:33:688 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:04:33:749 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 2mV
OVER 150


2025-07-31 20:04:33:809 ==>> 本次取值间隔时间:119ms
2025-07-31 20:04:33:828 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:04:33:854 ==>> [D][05:18:02][COMM]13726 imu init OK


2025-07-31 20:04:33:929 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:04:34:019 ==>> 本次取值间隔时间:76ms
2025-07-31 20:04:34:049 ==>> [D][05:18:02][CAT1]<<< 
OK

[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 2mV
OVER 150


2025-07-31 20:04:34:230 ==>> [D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[12] >>> AT+QIACT=1



2025-07-31 20:04:34:366 ==>> 本次取值间隔时间:343ms
2025-07-31 20:04:34:387 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:04:34:489 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:04:34:549 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:03][COMM]read battery soc:255
1A A1 10 00 00 
Get AD_V20 1647mV
OVER 150


2025-07-31 20:04:34:639 ==>> 本次取值间隔时间:136ms
2025-07-31 20:04:34:658 ==>> 【AD_V20电压】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:34:660 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:04:34:663 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:04:34:746 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:04:34:936 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:04:34:939 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:04:34:943 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:04:35:281 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][COMM]Main Task receive event:4
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:03][GNSS]rtk_id: 303E383D3535373F3838343F32353307

[D][05:18:03][FCTY]F:[appRTKpara2FlashDataUpdate].L:[4474] ready to writ

2025-07-31 20:04:35:386 ==>> e para2 flash
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][COMM]init key as 
[D][05:18:03][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:03][COMM]Main Task receive event:4 finished processing
[D][05:18:03][CAT1]<<< 
+CSQ: 22,99

OK

[D][05:18:03][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0
[D][05:18:03][CAT1]<<< 
+QIACT: 1,1,1,"10.63.231.254"

OK

[D][05:18:03][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 8, ret: 6
[D][05:18:03][CAT1]gsm read msg sub id: 23
[D][05:18:03][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:03][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret

2025-07-31 20:04:35:476 ==>> [20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[13] >>> AT+GPSPWR=1

[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:0
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:04:35:724 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:04:35:728 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:04:35:754 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:04:35:874 ==>> 3A A3 02 01 A3 
ON_OUT2
OVER 150
$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:04:36:001 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:04:36:003 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:04:36:007 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:04:36:260 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:04:36:539 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:04:36:567 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:04:36:569 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:04:36:572 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F

[D][05:18:05][COMM]read battery soc:255


2025-07-31 20:04:36:807 ==>>       :18:05][CAT1]<<< 
OK

$GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,3,1,12,60,,,45,59,,,42,42,,,41,39,,,39,1*79

$GBGSV,3,2,12,38,,,38,41,,,38,24,,,36,33,,,36,1*7C

$GBGSV,3,3,12,40,,,32,25,,,42,14,,,38,13,,,36,1*78

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1598.480,1598.480,51.166,2097152,2097152,2097152*4A

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

[W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:05][COMM]oneline display set 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:04:36:897 ==>> [D][05:18:05][GNSS]recv submsg id[1]
[D][05:18:05][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:04:37:067 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:04:37:071 ==>> 检测【AD_V21电压】
2025-07-31 20:04:37:075 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:04:37:142 ==>> 1A A1 20 00 00 
Get AD_V21 1076mV
OVER 150


2025-07-31 20:04:37:450 ==>> 本次取值间隔时间:377ms
2025-07-31 20:04:37:475 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:04:37:542 ==>> 1A A1 20 00 00 
Get AD_V21 1641mV
OVER 150


2025-07-31 20:04:37:647 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,60,,,42,59,,,42,25,,,41,42,,,41,1*70

$GBGSV,5,2,18,14,,,41,3,,,41,1,,,40,39,,,3

2025-07-31 20:04:37:692 ==>> 9,1*7B

$GBGSV,5,3,18,13,,,39,24,,,39,38,,,38,41,,,38,1*73

$GBGSV,5,4,18,33,,,38,2,,,37,40,,,36,9,,,36,1*7E

$GBGSV,5,5,18,5,,,34,6,,,32,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1598.435,1598.435,51.121,2097152,2097152,2097152*49



2025-07-31 20:04:37:937 ==>> 本次取值间隔时间:445ms
2025-07-31 20:04:37:955 ==>> 【AD_V21电压】通过,【1641mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:37:959 ==>> 检测【关闭仪表供电2】
2025-07-31 20:04:37:963 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:04:38:137 ==>> [W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:06][COMM]set POWER 0
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:04:38:231 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:04:38:234 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:04:38:236 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:04:38:429 ==>> [D][05:18:07][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:07][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:04:38:507 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:04:38:511 ==>> 检测【打开AccKey2供电】
2025-07-31 20:04:38:513 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:04:38:534 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:04:38:745 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,19,60,,,41,59,,,41,25,,,41,42,,,41,1*71

$GBGSV,5,2,19,14,,,41,3,,,41,33,,,41,24,,,40,1*48

$GBGSV,5,3,19,1,,,39,39,,,39,13,,,39,38,,,38,1*4B

$GBGSV,5,4,19,41,,,38,40,,,37,2,,,36,9,,,36,1*7A

$GBGSV,5,5,19,5,,,34,6,,,34,4,,,32,1*48

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1590.681,1590.681,50.877,2097152,2097152,2097152*42

[W][05:18:07][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:04:38:784 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:04:38:788 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:04:38:793 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:39:050 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:07][COMM]adc read vcc5v mc adc:3153  volt:5542 mv
[D][05:18:07][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:18:07][COMM]adc read left brake adc:4  volt:5 mv
[D][05:18:07][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:07][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:07][COMM]adc read battery ts volt:12 mv
[D][05:18:07][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:18:07][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:07][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:07][COMM]arm_hub adc read vbat adc:2424  volt:3905 mv
[D][05:18:07][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:07][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:07][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:04:39:313 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33184mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:04:39:317 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:04:39:320 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:04:39:528 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:04:39:597 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:04:39:601 ==>> 该项需要延时执行
2025-07-31 20:04:39:725 ==>> $GBGGA,120443.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,42,60,,,41,59,,,41,25,,,41,1*78

$GBGSV,6,2,23,14,,,41,3,,,41,33,,,41,24,,,41,1*43

$GBGSV,6,3,23,1,,,39,39,,,39,13,,,39,16,,,39,1*4C

$GBGSV,6,4,23,38,,,38,41,,,38,40,,,38,2,,,36,1*43

$GBGSV,6,5,23,9,,,36,6,,,35,5,,,34,4,,,32,1*7F

$GBGSV,6,6,23,10,,,17,8,,,39,7,,,36,1*70

$GBRMC,120443.508,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120443.508,0.000,1557.768,1557.768,49.950,2097152,2097152,2097152*5D



2025-07-31 20:04:40:561 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:04:40:666 ==>> $GBGGA,120444.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,42,33,,,42,24,,,42,59,,,41,1*7F

$GBGSV,6,2,23,25,,,41,1

2025-07-31 20:04:40:726 ==>> 4,,,41,3,,,41,60,,,40,1*45

$GBGSV,6,3,23,1,,,39,39,,,39,13,,,39,16,,,39,1*4C

$GBGSV,6,4,23,38,,,39,41,,,38,40,,,38,8,,,37,1*49

$GBGSV,6,5,23,2,,,36,9,,,36,6,,,36,5,,,34,1*7E

$GBGSV,6,6,23,7,,,33,4,,,33,10,,,31,1*77

$GBRMC,120444.508,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120444.508,0.000,1580.832,1580.832,50.579,2097152,2097152,2097152*55



2025-07-31 20:04:41:722 ==>> $GBGGA,120445.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,42,33,,,42,24,,,42,59,,,41,1*7F

$GBGSV,6,2,23,25,,,41,14,,,41,3,,,41,60,,,40,1*45

$GBGSV,6,3,23,39,,,39,13,,,39,38,,,39,1,,,38,1*41

$GBGSV,6,4,23,16,,,38,41,,,38,40,,,38,8,,,37,1*44

$GBGSV,6,5,23,2,,,36,9,,,36,6,,,36,5,,,34,1*7E

$GBGSV,6,6,23,7,,,34,4,,,33,10,,,31,1*70

$GBRMC,120445.508,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120445.508,0.000,1579.026,1579.026,50.518,2097152,2097152,2097152*53



2025-07-31 20:04:42:559 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:04:42:604 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:04:42:608 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:04:42:612 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:42:664 ==>> $GBGGA,120446.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,42,,,42,33,,,42,24,,,42,59,,,41,

2025-07-31 20:04:42:724 ==>> 1*7F

$GBGSV,6,2,23,25,,,41,14,,,41,3,,,41,60,,,41,1*44

$GBGSV,6,3,23,39,,,39,13,,,39,1,,,39,38,,,38,1*41

$GBGSV,6,4,23,16,,,38,41,,,38,40,,,38,8,,,36,1*45

$GBGSV,6,5,23,2,,,36,9,,,36,6,,,36,5,,,35,1*7F

$GBGSV,6,6,23,7,,,34,4,,,33,10,,,32,1*73

$GBRMC,120446.508,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120446.508,0.000,1582.626,1582.626,50.628,2097152,2097152,2097152*50



2025-07-31 20:04:42:950 ==>> [W][05:18:11][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:11][COMM]adc read vcc5v mc adc:3143  volt:5524 mv
[D][05:18:11][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:11][COMM]adc read left brake adc:7  volt:9 mv
[D][05:18:11][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:11][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:11][COMM]adc read battery ts volt:14 mv
[D][05:18:11][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:11][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:11][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:11][COMM]arm_hub adc read vbat adc:2475  volt:3988 mv
[D][05:18:11][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:11][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:11][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:04:43:137 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:04:43:142 ==>> 检测【打开AccKey1供电】
2025-07-31 20:04:43:146 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:04:43:321 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:12][COMM]frm_peripheral_device_poweron type 5.... 


2025-07-31 20:04:43:415 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:04:43:420 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:04:43:444 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:04:43:533 ==>> 1A A1 00 40 00 
Get AD_V14 2591mV
OVER 150


2025-07-31 20:04:43:668 ==>> 原始值:【2591】, 乘以分压基数【2】还原值:【5182】
2025-07-31 20:04:43:687 ==>> 【读取AccKey1电压(ADV14)前】通过,【5182mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:04:43:691 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:04:43:697 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:43:728 ==>> $GBGGA,120447.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,41,59,,,41,1*7B

$GBGSV,7,2,25,25,,,41,14,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,,39,13,,,39,1,,,39,38,,,39,1*47

$GBGSV,7,4,25,16,,,38,41,,,38,40,,,38,8,,,36,1*42

$GBGSV,7,5,25,2,,,36,9,,,36,6,,,36,7,,,35,1*7A

$GBGSV,7,6,25,26,,,35,5,,,34,4,,,33,10,,,32,1*74

$GBGSV,7,7,25,21,,,40,1*76

$GBRMC,120447.508,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120447.508,0.000,1577.142,1577.142,50.451,2097152,2097152,2097152*5D



2025-07-31 20:04:43:954 ==>> [W][05:18:12][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:12][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:12][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:12][COMM]adc read left brake adc:12  volt:15 mv
[D][05:18:12][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:12][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:12][COMM]adc read battery ts volt:11 mv
[D][05:18:12][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:12][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:12][COMM]arm_hub adc read bat_id adc:16  volt:12 mv
[D][05:18:12][COMM]arm_hub adc read vbat adc:2486  volt:4005 mv
[D][05:18:12][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:12][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:12][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:04:44:219 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5544mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:04:44:225 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:04:44:228 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:04:44:419 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:13][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:04:44:497 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:04:44:501 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:04:44:505 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:04:44:555 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:04:44:645 ==>> 1A A1 00 40 00 
Get AD_V14 2593mV
OVER 150


2025-07-31 20:04:44:720 ==>> $GBGGA,120448.508,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,41,1*78

$GBGSV,7,2,25,25,,,41,14,,,41,3,,,41,60,,,40,1*42

$GBGSV,7,3,25,39,,,39,13,,,39,1,,,39,38,,,39,1*47

$GBGSV,7,4,25,16,,,38,41,,,38,40,,,38,8,,,36,1*42

$GBGSV,7,5,25,2,,,36,9,,,36,6,,,36,26,,,36,1*4A

$GBGSV,7,6,25,7,,,35,5,,,34,21,,,33,4,,,33,1*44

$GBGSV,7,7,25,10,,,32,1*71

$GBRMC,120448.508,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120448.508,0.000,1570.444,1570.444,50.242,2097152,2097152,2097152*56



2025-07-31 20:04:44:750 ==>> 原始值:【2593】, 乘以分压基数【2】还原值:【5186】
2025-07-31 20:04:44:780 ==>> 【读取AccKey1电压(ADV14)后】通过,【5186mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:04:44:785 ==>> 检测【打开WIFI(2)】
2025-07-31 20:04:44:790 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:04:44:949 ==>> [W][05:18:13][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:13][CAT1]gsm read msg sub id: 12
[D][05:18:13][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:13][CAT1]<<< 
OK

[D][05:18:13][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:04:45:058 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:04:45:063 ==>> 检测【转刹把供电】
2025-07-31 20:04:45:068 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:04:45:223 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:04:45:332 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:04:45:335 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:04:45:338 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:04:45:435 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:45:542 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 00 80 00 
Get AD_V15 2411mV
OVER 150


2025-07-31 20:04:45:587 ==>> 原始值:【2411】, 乘以分压基数【2】还原值:【4822】
2025-07-31 20:04:45:606 ==>> 【读取AD_V15电压(前)】通过,【4822mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:04:45:611 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:04:45:633 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:04:45:707 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:04:45:767 ==>> +WIFISCAN:4,0,CC057790A740,-74
+WIFISCAN:4,1,CC057790A7C1,-75
+WIFISCAN:4,2,CC057790A5C1,-78
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:14][CAT1]wifi scan report total[4]
$GBGGA,120449.508,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,41,1*78

$GBGSV,7,2,25,25,,,41,14,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,,39,13,,,39,1,,,39,38,,,38,1*46

$GBGSV,7,4,25,16,,,38,41,,,38,40,,,38,8,,,36,1*42

$GBGSV,7,5,25,2,,,36,9,,,36,6,,,36,26,,,36,1*4A

$GBGSV,7,6,25,7,,,35,5,,,34,4,,,34,21,,,33,1*43

$GBGSV,7,7,25,10,,,32,1*71

$GBRMC,120449.508,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120449.508,0.000,1572.101,1572.101,50.293,2097152,2097152,2097152*5B



2025-07-31 20:04:45:842 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:04:45:932 ==>> [D][05:18:14][GNSS]recv submsg id[3]


2025-07-31 20:04:46:565 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:04:46:655 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:04:46:670 ==>> $GBGGA,120450.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,24,,,42,42,,,42,59,,,41,1*78

$GBGSV,7,2,25,25,,,41,14,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,

2025-07-31 20:04:46:730 ==>> ,39,13,,,39,1,,,39,38,,,39,1*47

$GBGSV,7,4,25,16,,,38,41,,,38,40,,,38,6,,,37,1*4D

$GBGSV,7,5,25,8,,,36,2,,,36,9,,,36,26,,,36,1*44

$GBGSV,7,6,25,7,,,35,5,,,34,4,,,33,21,,,33,1*44

$GBGSV,7,7,25,10,,,32,1*71

$GBRMC,120450.508,V,,,,,,,,0.0,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120450.508,0.000,1573.761,1573.761,50.349,2097152,2097152,2097152*55



2025-07-31 20:04:46:760 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:04:46:805 ==>> [W][05:18:15][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:04:46:835 ==>> 1A A1 01 00 00 
Get AD_V16 2441mV
OVER 150


2025-07-31 20:04:46:925 ==>> 原始值:【2441】, 乘以分压基数【2】还原值:【4882】
2025-07-31 20:04:46:948 ==>> 【读取AD_V16电压(前)】通过,【4882mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:04:46:952 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:04:46:956 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:47:248 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3148  volt:5533 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:8  volt:10 mv
[D][05:18:15][COMM]adc read right brake adc:4  volt:5 mv
[D][05:18:15][COMM]adc read throttle adc:6  volt:7 mv
[D][05:18:15][COMM]adc read battery ts volt:11 mv
[D][05:18:15][COMM]adc read in 24v adc:1293  volt:32703 mv
[D][05:18:15][COMM]adc read throttle brake in adc:3104  volt:5456 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2421  volt:3901 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:04:47:480 ==>> 【转刹把供电电压(主控ADC)】通过,【5456mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:04:47:484 ==>> 检测【转刹把供电电压】
2025-07-31 20:04:47:487 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:47:802 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:16][COMM]adc read vcc5v mc adc:3150  volt:5537 mv
[D][05:18:16][COMM]adc read out 24v adc:2  volt:50 mv
[D][05:18:16][COMM]adc read left brake adc:5  volt:6 mv
[D][05:18:16][COMM]adc read right brake adc:7  volt:9 mv
[D][05:18:16][COMM]adc read throttle adc:8  volt:10 mv
[D][05:18:16][COMM]adc read battery ts volt:10 mv
[D][05:18:16][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:16][COMM]adc read throttle brake in adc:3103  volt:5454 mv
[D][05:18:16][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:16][COMM]arm_hub adc read vbat adc:2481  volt:3997 mv
$GBGGA,120451.508,,,,,0,00,,,M,,M,,*64

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,42,42,,,42,59,,,41,1*79

$GBGSV,7,2,25,25,,,41,14,,,41,3,,,41,60,,,41,1*43

$GBGSV,7,3,25,39,,,39,13,,,39,1,,,39,38,,,39,1*47

$GBGSV,7,4,25,16,,,38,41,,,38,40,,,38,6,,,36,1*4C

$GBGSV,7,5,25,8,,,36,2,,,36,9,,,36,26,,,36,1*44

$GBGSV,7,6,25,7,,,35,5,,,35,4,,,33,21,,,33,1*45

$GBGSV,7,7,25,10,,,33,1*70

$GBRMC,120451.508,V,,,,,,,,0.0,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120451.508,0.000,1577.076,1577.076,50.452,2097152,2097152,2097152*59

[D][05:18:16][COMM]a

2025-07-31 20:04:47:847 ==>> rm_hub adc read led yb adc:1439  volt:33363 mv
[D][05:18:16][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:18:16][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:04:47:998 ==>> [D][05:18:16][COMM]IMU: [-15,-6,-1046] ret=29 AWAKE!
[D][05:18:16][COMM]S->M yaw:INVALID


2025-07-31 20:04:48:020 ==>> 【转刹把供电电压】通过,【5454mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:04:48:024 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:04:48:029 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:48:223 ==>> [D][05:18:17][COMM]IMU: [-3,7,-991] ret=26 AWAKE!
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:04:48:301 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:04:48:305 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:04:48:308 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:48:407 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:48:516 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:48:623 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:48:728 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:48:774 ==>> [D][05:18:17][COMM]read battery soc:255
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,120452.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,42,,,42,59,,,41,1*7A

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,40,1*41

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,6,,,36,1*4F

$GBGSV,7,5,26,8,,,36,2,,,36,9,,,36,26,,,36,1*47

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,33,1*40

$GBGSV,7,7,26,10,,,33,34,,,30,1*77

$GBRMC,120452.508,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120452.508,0.000,1562.676,1562.676,50.007,2097152,2097152,2097152*5E

[W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:04:48:833 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:48:863 ==>> [W][05:18:17][COMM]>>>>>Input command = ?<<<<


2025-07-31 20:04:48:938 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:48:943 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:04:49:043 ==>> [D][05:18:17][COMM]M->S yaw:INVALID


2025-07-31 20:04:49:048 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:04:49:133 ==>> [D][05:18:17][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
1A A1 00 80 00 
Get AD_V15 2mV
OVER 150


2025-07-31 20:04:49:166 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:04:49:172 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:04:49:177 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:04:49:268 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:04:49:298 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:04:49:343 ==>> 1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:04:49:399 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:04:49:403 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:04:49:409 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:04:49:539 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:04:49:677 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:04:49:683 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:04:49:691 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:04:49:735 ==>> $GBGGA,120453.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,42,59,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,8,,,37,1*40

$GBGSV,7,5,26,6,,,36,2,,,36,9,,,36,26,,,36,1*49

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,34,1*47

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120453.508,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120453.508,0.000,1567.451,1567.451,50.150,2097152,2097152,2097152*5C

3A A3 04 01 A3 
ON_OUT4
OVER 150


2025-07-31 20:04:49:954 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:04:49:957 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:04:49:960 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:04:50:036 ==>> 3A A3 05 01 A3 
ON_OUT5
OVER 150


2025-07-31 20:04:50:236 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:04:50:239 ==>> 检测【左刹电压测试1】
2025-07-31 20:04:50:245 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:50:556 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:19][COMM]adc read vcc5v mc adc:3145  volt:5528 mv
[D][05:18:19][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:19][COMM]adc read left brake adc:1723  volt:2271 mv
[D][05:18:19][COMM]adc read right brake adc:1725  volt:2274 mv
[D][05:18:19][COMM]adc read throttle adc:1729  volt:2279 mv
[D][05:18:19][COMM]adc read battery ts volt:13 mv
[D][05:18:19][COMM]adc read in 24v adc:1294  volt:32729 mv
[D][05:18:19][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:19][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:19][COMM]arm_hub adc read vbat adc:2471  volt:3981 mv
[D][05:18:19][COMM]arm_hub adc read led yb adc:1440  volt:33386 mv
[D][05:18:19][COMM]arm_hub adc read board id adc:3356  volt:2703 mv
[D][05:18:19][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
                                        

2025-07-31 20:04:50:586 ==>>  

2025-07-31 20:04:50:691 ==>>                  ,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,42,,,42,59,,,41,1*7A

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,8,,,36,1*41

$GBGSV,7,5,26,6,,,36,2,,,36,9,,,36,26,,,36,1*49

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,33,1*40

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120454.

2025-07-31 20:04:50:721 ==>> 508,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120454.508,0.000,1565.862,1565.862,50.106,2097152,2097152,2097152*58



2025-07-31 20:04:50:770 ==>> 【左刹电压测试1】通过,【2271】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:50:773 ==>> 检测【右刹电压测试1】
2025-07-31 20:04:50:793 ==>> 【右刹电压测试1】通过,【2274】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:50:796 ==>> 检测【转把电压测试1】
2025-07-31 20:04:50:812 ==>> 【转把电压测试1】通过,【2279】符合目标值【2250】至【2500】要求!
2025-07-31 20:04:50:816 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:04:50:821 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:04:50:932 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:04:51:085 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:04:51:089 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:04:51:093 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:04:51:143 ==>> 3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:04:51:360 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:04:51:365 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:04:51:373 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:04:51:434 ==>> 3A A3 05 00 A3 


2025-07-31 20:04:51:539 ==>> OFF_OUT5
OVER 150


2025-07-31 20:04:51:633 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:04:51:640 ==>> 检测【左刹电压测试2】
2025-07-31 20:04:51:661 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:04:51:735 ==>> $GBGGA,120455.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,42,59,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,40,1*41

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,8,,,37,1*40

$GBGSV,7,5,26,6,,,36,2,,,36,9,,,36,26,,,36,1*49

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,33,1*40

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120455.508,V,,,,,,,,0.0,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120455.508,0.000,1564.263,1564.263,50.050,2097152,2097152,2097152*5B



2025-07-31 20:04:51:944 ==>> [W][05:18:20][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:20][COMM]adc read vcc5v mc adc:3154  volt:5544 mv
[D][05:18:20][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:20][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:20][COMM]adc read right brake adc:8  volt:10 mv
[D][05:18:20][COMM]adc read throttle adc:7  volt:9 mv
[D][05:18:20][COMM]adc read battery ts volt:14 mv
[D][05:18:20][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:20][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:20][COMM]arm_hub adc read bat_id adc:17  volt:13 mv
[D][05:18:20][COMM]arm_hub adc read vbat adc:2487  volt:4007 mv
[D][05:18:20][COMM]arm_hub adc read led yb adc:1441  volt:33409 mv
[D][05:18:20][COMM]arm_hub adc read board id adc:3357  volt:2704 mv
[D][05:18:20][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:04:52:158 ==>> 【左刹电压测试2】通过,【3】符合目标值【0】至【50】要求!
2025-07-31 20:04:52:163 ==>> 检测【右刹电压测试2】
2025-07-31 20:04:52:176 ==>> 【右刹电压测试2】通过,【10】符合目标值【0】至【50】要求!
2025-07-31 20:04:52:182 ==>> 检测【转把电压测试2】
2025-07-31 20:04:52:198 ==>> 【转把电压测试2】通过,【9】符合目标值【0】至【50】要求!
2025-07-31 20:04:52:206 ==>> 检测【晶振检测】
2025-07-31 20:04:52:212 ==>> 向【COM34】发送指令:【AT+SYSCHECK】
2025-07-31 20:04:52:429 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+SYSCHECK<<<<<
[D][05:18:21][COMM][lf state:1][hf state:1]


2025-07-31 20:04:52:478 ==>> 【晶振检测】通过,【[lf state:1][hf state:1]】符合目标值【[lf state:1][hf state:1]】要求!
2025-07-31 20:04:52:486 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)2】
2025-07-31 20:04:52:492 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:04:52:534 ==>> 1A A1 00 00 FC 
Get AD_V2 1665mV
Get AD_V3 1656mV
Get AD_V4 1643mV
Get AD_V5 2772mV
Get AD_V6 2025mV
Get AD_V7 1092mV
OVER 150


2025-07-31 20:04:52:729 ==>> [D][05:18:21][COMM]read battery soc:255
$GBGGA,120456.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,42,59,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,6,,,37,1*4E

$GBGSV,7,5,26,8,,,36,2,,,36,9,,,36,26,,,36,1*47

$GBGSV,7,6,26,7,,,35,5,,,35,21,,,34,4,,,34,1*46

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120456.508,V,,,,,,,,0.0,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120456.508,0.000,1569.043,1569.043,50.199,2097152,2097152,2097152*5C



2025-07-31 20:04:52:749 ==>> 【TP33_VCC3V3_GNSS(ADV4)2】通过,【1643mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:04:52:755 ==>> 检测【检测BootVer】
2025-07-31 20:04:52:763 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:04:53:106 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:21][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========
[D][05:18:21][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:21][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:21][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:21][FCTY]DeviceID    = 460130071539169
[D][05:18:21][FCTY]HardwareID  = 867222087738524
[D][05:18:21][FCTY]MoBikeID    = 9999999999
[D][05:18:21][FCTY]LockID      = FFFFFFFFFF
[D][05:18:21][FCTY]BLEFWVersion= 105
[D][05:18:21][FCTY]BLEMacAddr   = E4AE7AC8032E
[D][05:18:21][FCTY]Bat         = 4064 mv
[D][05:18:21][FCTY]Current     = 150 ma
[D][05:18:21][FCTY]VBUS        = 11800 mv
[D][05:18:21][FCTY]TEMP= 0,BATID= 500,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:21][FCTY]Ext battery vol = 32, adc = 1291
[D][05:18:21][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 0 mv
[D][05:18:21][FCTY]Bike Type flag is invalied
[D][05:18:21][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:21][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:21][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:21][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:21][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:21][FCTY]Bat1         = 3820 mv
[D][05:18:21

2025-07-31 20:04:53:136 ==>> ][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:21][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:04:53:280 ==>> 【检测BootVer】通过,【SA_BOOT_V109】符合目标值【SA_BOOT_V109】要求!
2025-07-31 20:04:53:285 ==>> 提取到BootVersion:SA_BOOT_V109
2025-07-31 20:04:53:290 ==>> 检测【检测固件版本】
2025-07-31 20:04:53:299 ==>> 【检测固件版本】通过,【MSL_SA_A_E4_X50_917】符合目标值【MSL_SA_A_E4_X50_917】要求!
2025-07-31 20:04:53:304 ==>> 提取到FwVersion:MSL_SA_A_E4_X50_917
2025-07-31 20:04:53:310 ==>> 检测【检测蓝牙版本】
2025-07-31 20:04:53:318 ==>> 【检测蓝牙版本】通过,【BLE_BE_105_102_100】符合目标值【BLE_BE_105_102_100】要求!
2025-07-31 20:04:53:322 ==>> 提取到BleVersion:BLE_BE_105_102_100
2025-07-31 20:04:53:327 ==>> 检测【检测MoBikeId】
2025-07-31 20:04:53:337 ==>> 【检测MoBikeId】通过,【9999999999】符合目标值【9999999999】要求!
2025-07-31 20:04:53:341 ==>> 提取到MoBikeId:9999999999
2025-07-31 20:04:53:344 ==>> 检测【检测蓝牙地址】
2025-07-31 20:04:53:347 ==>> 取到目标值:E4AE7AC8032E
2025-07-31 20:04:53:355 ==>> 【检测蓝牙地址】通过,【E4AE7AC8032E】符合目标值【】要求!
2025-07-31 20:04:53:359 ==>> 提取到蓝牙地址:E4AE7AC8032E
2025-07-31 20:04:53:365 ==>> 检测【BOARD_ID】
2025-07-31 20:04:53:377 ==>> 【BOARD_ID】通过,【0xD1】符合目标值【202】至【218】要求!
2025-07-31 20:04:53:380 ==>> 检测【检测充电电压】
2025-07-31 20:04:53:397 ==>> 【检测充电电压】通过,【4064mv】符合目标值【3700mv】至【4200mv】要求!
2025-07-31 20:04:53:401 ==>> 检测【检测VBUS电压1】
2025-07-31 20:04:53:415 ==>> 【检测VBUS电压1】通过,【11800mv】符合目标值【10500mv】至【12500mv】要求!
2025-07-31 20:04:53:419 ==>> 检测【检测充电电流】
2025-07-31 20:04:53:434 ==>> 【检测充电电流】通过,【150ma】符合目标值【0ma】至【1200ma】要求!
2025-07-31 20:04:53:438 ==>> 检测【检测IMEI】
2025-07-31 20:04:53:443 ==>> 取到目标值:867222087738524
2025-07-31 20:04:53:452 ==>> 【检测IMEI】通过,【867222087738524】符合目标值【】要求!
2025-07-31 20:04:53:455 ==>> 提取到IMEI:867222087738524
2025-07-31 20:04:53:462 ==>> 检测【检测IMSI】
2025-07-31 20:04:53:469 ==>> 取到目标值:460130071539169
2025-07-31 20:04:53:492 ==>> 【检测IMSI】通过,【460130071539169】符合目标值【】要求!
2025-07-31 20:04:53:496 ==>> 提取到IMSI:460130071539169
2025-07-31 20:04:53:499 ==>> 检测【校验网络运营商(移动)】
2025-07-31 20:04:53:502 ==>> 取到目标值:460130071539169
2025-07-31 20:04:53:511 ==>> 【校验网络运营商(移动)】通过,【460130071539169】符合目标值【】要求!
2025-07-31 20:04:53:515 ==>> 检测【打开CAN通信】
2025-07-31 20:04:53:520 ==>> 向【COM34】发送指令:【[C128]RECVCAN:1】
2025-07-31 20:04:53:639 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:1RECVCAN SET SUCCESS


2025-07-31 20:04:53:729 ==>> $GBGGA,120457.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,42,59,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,6,,,36,1*4F

$GBGSV,7,5,26,8,,,36,2,,,36,9,,,36,26,,,36,1*47

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,34,1*47

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120457.508,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120457.508,0.000,1565.857,1565.857,50.100,2097152,2097152,2097152*5D



2025-07-31 20:04:53:797 ==>> 【打开CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:04:53:805 ==>> 检测【检测CAN通信】
2025-07-31 20:04:53:836 ==>> 向【COM34】发送指令:【8A 00 00 00 00 06 01 08 51 52 53 54 55 56 57 58 8A】
2025-07-31 20:04:53:956 ==>> can send success
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:04:54:017 ==>> [D][05:18:22][COMM]CAN message fault change: 0x0008F80C71E2223F->0x0008E80C71E2223F 33857
标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:04:54:076 ==>> 标准帧 ID:00000600 DATA:51 52 53 54 55 56 57 58 


2025-07-31 20:04:54:080 ==>> 【检测CAN通信】通过,【ID:00000600 DATA:51 52 53 54 55 56 57 58】符合目标值【ID:00000600 DATA:51 52 53 54 55 56 57 58】要求!
2025-07-31 20:04:54:084 ==>> 检测【关闭CAN通信】
2025-07-31 20:04:54:111 ==>> 向【COM34】发送指令:【[C128]RECVCAN:0】
2025-07-31 20:04:54:136 ==>> [C128]connect ready
CAN_flg_t.recv_can_flg:0RECVCAN SET SUCCESS


2025-07-31 20:04:54:350 ==>> 【关闭CAN通信】通过,【connect ready】符合目标值【connect ready】要求!
2025-07-31 20:04:54:355 ==>> 检测【打印IMU STATE】
2025-07-31 20:04:54:361 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:04:54:528 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:23][COMM]YAW data: 32763[32763]
[D][05:18:23][COMM]pitch:-66 roll:0
[D][05:18:23][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:04:54:621 ==>> 【打印IMU STATE】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:04:54:626 ==>> 检测【六轴自检】
2025-07-31 20:04:54:629 ==>> 向【COM34】发送指令:【AT+IMU_SELFTEST】
2025-07-31 20:04:54:725 ==>> [D][05:18:23][COMM]read battery soc:255
$GBGGA,120458.508,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,42,59,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,41,,,38,40,,,38,9,,,37,1*41

$GBGSV,7,5,26,6,,,36,8,,,36,2,,,36,26,,,36,1*48

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,34,1*47

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120458.508,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120458.508,0.000,1567.451,1567.451,50.150,2097152,2097152,2097152*57



2025-07-31 20:04:54:830 ==>> [W][05:18:23][COMM]>>>>>Input command = AT+IMU_SELFTEST<<<<<
[D][05:18:23][CAT1]gsm read msg sub id: 12
[D][05:18:23][CAT1]SEND RAW data >>> AT+IMUCTRL=1,0




2025-07-31 20:04:55:720 ==>> $GBGGA,120459.508,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,41,59,,,41,1*78

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,38,1*45

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,9,,,36,1*4F

$GBGSV,7,5,26,6,,,36,8,,,36,2,,,36,26,,,35,1*4B

$GBGSV,7,6,26,7,,,35,5,,,34,21,,,34,4,,,34,1*47

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120459.508,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120459.508,0.000,1559.478,1559.478,49.896,2097152,2097152,2097152*5D



2025-07-31 20:04:56:544 ==>> [D][05:18:25][CAT1]<<< 
OK

[D][05:18:25][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:04:56:814 ==>> [D][05:18:25][COMM]read battery soc:255
$GBGGA,120500.508,,,,,0,00,,,M,,M,,*61

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,42,,,41,59,,,41,1*78

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,38,1*45

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,38,2,,,37,1*4A

$GBGSV,7,5,26,9,,,36,6,,,36,8,,,36,26,,,36,1*43

$GBGSV,7,6,26,7,,,35,5,,,35,21,,,34,4,,,34,1*46

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120500.508,V,,,,,,,,0.0,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120500.508,0.000,1565.851,1565.851,50.095,2097152,2097152,2097152*53

[D][05:18:25][COMM]Main Task receive event:142
[D][05:18:25][COMM]###### 36573 imu self test OK ######
[D][05:18:25][COMM]imu selftest. GYRO:[0,0,0] ACCEL:[-8,-3,4049]
[D][05:18:25][COMM]Main Task receive event:142 finished processing


2025-07-31 20:04:56:968 ==>> 【六轴自检】通过,【imu self test OK】符合目标值【imu self test OK】要求!
2025-07-31 20:04:56:975 ==>> 检测【打印IMU STATE2】
2025-07-31 20:04:56:981 ==>> 向【COM34】发送指令:【AT+YAW】
2025-07-31 20:04:57:132 ==>> [W][05:18:25][COMM]>>>>>Input command = AT+YAW<<<<<
[D][05:18:25][COMM]YAW data: 32763[32763]
[D][05:18:25][COMM]pitch:-66 roll:0
[D][05:18:25][COMM]valid_flag:66BB result:0 offset:-1 yaw:0 time:0 invalid:7FFB


2025-07-31 20:04:57:241 ==>> 【打印IMU STATE2】通过,【>>>>>Input command = AT+YAW<<<<<】符合目标值【>>>>>Input command = AT+YAW<<<<<】要求!
2025-07-31 20:04:57:248 ==>> 检测【关闭33V供电(C128电源OUT1)1】
2025-07-31 20:04:57:254 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:04:57:345 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:04:57:450 ==>> [D][05:18:26][FCTY]get_ext_48v_vol retry i = 0,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 1,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 2,volt = 12
[D][05:18:26][FCTY]get_e

2025-07-31 20:04:57:495 ==>> xt_48v_vol retry i = 3,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 4,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 5,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 6,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 7,volt = 12
[D][05:18:26][FCTY]get_ext_48v_vol retry i = 8,volt = 12


2025-07-31 20:04:57:514 ==>> 【关闭33V供电(C128电源OUT1)1】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:04:57:520 ==>> 检测【检测VBUS电压2】
2025-07-31 20:04:57:527 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:04:57:922 ==>> $GBGGA,120501.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,24,,,42,59,,,42,42,,,41,1*7B

$GBGSV,7,2,26,25,,,41,14,,,41,3,,,41,60,,,41,1*40

$GBGSV,7,3,26,39,,,39,13,,,39,1,,,39,38,,,39,1*44

$GBGSV,7,4,26,16,,,38,40,,,38,41,,,37,2,,,36,1*44

$GBGSV,7,5,26,9,,,36,6,,,36,8,,,36,26,,,36,1*43

$GBGSV,7,6,26,7,,,35,5,,,35,21,,,34,4,,,34,1*46

$GBGSV,7,7,26,10,,,33,34,,,31,1*76

$GBRMC,120501.508,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120501.508,0.000,781.467,781.467,714.669,2097152,2097152,2097152*60

[W][05:18:26][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:26][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========
[D][05:18:26][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:26][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:26][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:26][FCTY]DeviceID    = 460130071539169
[D][05:18:26][FCTY]HardwareID  = 867222087738524
[D][05:18:26][FCTY]MoBikeID    = 9999999999
[D][05:18:26][FCTY]LockID      = FFFFFFFFFF
[D][05:18:26][FCTY]BLEFWVersion= 105
[D][05:18:26][FCTY]BLEMacAddr   = E4AE7AC8032E
[D][05:18:26][FCTY]Bat         = 4064 mv
[

2025-07-31 20:04:58:012 ==>> D][05:18:26][FCTY]Current     = 150 ma
[D][05:18:26][FCTY]VBUS        = 11800 mv
[D][05:18:26][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:18:26][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:26][FCTY]Ext battery vol = 7, adc = 301
[D][05:18:26][FCTY]Acckey1 vol = 5531 mv, Acckey2 vol = 0 mv
[D][05:18:26][FCTY]Bike Type flag is invalied
[D][05:18:26][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:26][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:26][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:18:26][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:26][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:26][FCTY]Bat1         = 3820 mv
[D][05:18:26][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:26][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:04:58:046 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:04:58:393 ==>> [W][05:18:27][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:18:27][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========
[D][05:18:27][FCTY]BootVersion = SA_BOOT_V109
[D][05:18:27][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:18:27][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:18:27][FCTY]DeviceID    = 460130071539169
[D][05:18:27][FCTY]HardwareID  = 867222087738524
[D][05:18:27][FCTY]MoBikeID    = 9999999999
[D][05:18:27][FCTY]LockID      = FFFFFFFFFF
[D][05:18:27][FCTY]BLEFWVersion= 105
[D][05:18:27][FCTY]BLEMacAddr   = E4AE7AC8032E
[D][05:18:27][FCTY]Bat         = 4064 mv
[D][05:18:27][FCTY]Current     = 150 ma
[D][05:18:27][FCTY]VBUS        = 5000 mv
[D][05:18:27][FCTY]TEMP= 0,BATID= 176,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:18:27][FCTY]Ext battery vol = 4, adc = 174
[D][05:18:27][FCTY]Acckey1 vol = 5530 mv, Acckey2 vol = 0 mv
[D][05:18:27][FCTY]Bike Type flag is invalied
[D][05:18:27][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:18:27][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:18:27][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][

2025-07-31 20:04:58:438 ==>> 05:18:27][FCTY]CAT1_GNSS_PLATFORM = C4
[D][05:18:27][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:18:27][FCTY]Bat1         = 3820 mv
[D][05:18:27][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:18:27][FCTY]==========Modules-nRF5340 ==========


2025-07-31 20:04:58:577 ==>> 【检测VBUS电压2】通过,【5000mV】符合目标值【4400mV】至【5000mV】要求!
2025-07-31 20:04:58:582 ==>> 检测【打开33V供电(C128电源OUT1)2】
2025-07-31 20:04:58:588 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:04:58:635 ==>> 5A A5 01 5A A5 


2025-07-31 20:04:58:740 ==>> $GBGGA,120502.508,,,,,0,00,,,M,,M,,*63

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,24,,,42,42,,,42,33,,,42,3,,,41,1*44

$GBGSV,7,2,26,60,,,41,25,,,41,59,,,41,14,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,1,,,39,39,,,39,1*44

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,2,,,36,1*44

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,26,,,36,1*43

$GBGSV,7,6,26,7,,,35,5,,,35,4,,,34,10,,,33,1*43

$GBGSV,7,7,26,21,,,33,34,,,31,1*74

$GBRMC,120502.508,V,,,,,,,310725,0.1,E,N,V*44

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120502.508,0.000,780.674,780.674,713.944,2097152,2097152,2097152*64

OPEN_POWER_OUT1
OVER 150


2025-07-31 20:04:58:830 ==>> [D][05:18:27][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 20
[D][05:18:27][COMM]read battery soc:255


2025-07-31 20:04:58:853 ==>> 【打开33V供电(C128电源OUT1)2】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:04:58:858 ==>> 检测【关闭5V供电(C128电源OUT2)】
2025-07-31 20:04:58:864 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:04:58:935 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:04:59:025 ==>> [D][05:18:27][COMM]msg 0601 loss. last_tick:33853. cur_tick:38873. period:500
[D][05:18:27][COMM]CAN message fault change: 0x0008E80C71E2223F->0x0008F80C71E2223F 38874


2025-07-31 20:04:59:134 ==>> 【关闭5V供电(C128电源OUT2)】通过,【CLOSE_POWER_OUT2】符合目标值【CLOSE_POWER_OUT2】要求!
2025-07-31 20:04:59:138 ==>> 检测【打开WIFI(3)】
2025-07-31 20:04:59:142 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:04:59:361 ==>> [W][05:18:28][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:28][CAT1]gsm read msg sub id: 12
[D][05:18:28][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:28][CAT1]<<< 
OK

[D][05:18:28][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:04:59:411 ==>> 【打开WIFI(3)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:04:59:415 ==>> 检测【扩展芯片hw】
2025-07-31 20:04:59:422 ==>> 向【COM34】发送指令:【AT+ARMHUBVER】
2025-07-31 20:04:59:774 ==>> [D][05:18:28][HSDK][0] flush to flash addr:[0xE41800] --- write len --- [256]
[W][05:18:28][COMM]>>>>>Input command = AT+ARMHUBVER<<<<<
[D][05:18:28][COMM]arm_hub: id:[0x0], hw:[0xb200], boot:[0x101], sw:[0x103], protocol_version:[0x1], build_time:[GD  May 24 2024]
$GBGGA,120503.508,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,60,,,41,25,,,41,1*45

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,14,,,41,1*7E

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,38,2,,,36,1*4B

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,26,,,36,1*43

$GBGSV,7,6,26,7,,,35,5,,,35,10,,,33,21,,,33,1*73

$GBGSV,7,7,26,4,,,33,34,,,31,1*43

$GBRMC,120503.508,V,,,,,,,310725,0.1,E,N,V*45

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120503.508,0.000,778.284,778.285,711.759,2097152,2097152,2097152*64



2025-07-31 20:04:59:941 ==>> 【扩展芯片hw】通过,【0xb200】符合目标值【0xb200】要求!
2025-07-31 20:04:59:946 ==>> 检测【扩展芯片boot】
2025-07-31 20:04:59:960 ==>> 【扩展芯片boot】通过,【0x101】符合目标值【0x101】要求!
2025-07-31 20:04:59:967 ==>> 检测【扩展芯片sw】
2025-07-31 20:04:59:979 ==>> 【扩展芯片sw】通过,【0x103】符合目标值【0x103】要求!
2025-07-31 20:05:00:007 ==>> 检测【检测音频FLASH】
2025-07-31 20:05:00:011 ==>> 向【COM34】发送指令:【AT+FLASH_TEST=4235,2】
2025-07-31 20:05:00:137 ==>> +WIFISCAN:4,0,CC057790A740,-76
+WIFISCAN:4,1,CC057790A5C1,-77
+WIFISCAN:4,2,CC057790A6E1,-81
+WIFISCAN:4,3,F86FB0660A82,-84

[D][05:18:28][CAT1]wifi scan report total[4]
[W][05:18:28][COMM]>>>>>Input command = AT+FLASH_TEST=4235,2<<<<<


2025-07-31 20:05:00:409 ==>> [D][05:18:29][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:0------------
[D][05:18:29][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:05:01:322 ==>>                                                            . 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]more than the number of battery plugs
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]verify_batlock_state ret -516, soc 0
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:18:29][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:18:29][COMM]Bat auth off fail, error:-1
[D][05:18:29][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:18:29][COMM]----- get Acckey 1 and value:1------------
[D][05:18:29][COMM]----- get Acckey 2 and value:1------------
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:29][COMM]file:B50 exist
[D][05:18:29][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'B50'
[D

2025-07-31 20:05:01:428 ==>> ][05:18:29][COMM]read file, len:10800, num:3
[D][05:18:29][COMM]--->crc16:0xb8a
[D][05:18:29][COMM]read file success
[W][05:18:29][COMM][Audio].l:[936].close hexlog save
[D][05:18:29][COMM]accel parse set 1
[D][05:18:29][COMM][Audio]mon:9,05:18:29
[D][05:18:29][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:18:29][COMM]Main Task receive event:65
[D][05:18:29][COMM]main task tmp_sleep_event = 80
[D][05:18:29][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:18:29][COMM]Main Task receive event:65 finished processing
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:29][COMM]Main Task receive event:66
[D][05:18:29][COMM]Try to Auto Lock Bat
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:29][COMM]Main Task receive event:66 finished processing
[D][05:18:29][COMM]Main Task receiv

2025-07-31 20:05:01:534 ==>> e event:60
[D][05:18:29][COMM]smart_helmet_vol=255,255
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get soc error
[E][05:18:29][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:18:29][COMM]report elecbike
[W][05:18:29][PROT]remove success[1629955109],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:18:29][PROT]add success [1629955109],send_path[3],type[5D03],priority[4],index[0],used[1]
[D][05:18:29][COMM]Main Task receive event:60 finished processing
[D][05:18:29][COMM]Main Task receive event:61
[D][05:18:29][COMM][D301]:type:3, trace id:280
[D][05:18:29][COMM]id[], hw[000
[D][05:18:29][COMM]get mcMaincircuitVolt error
[D][05:18:29][COMM]get mcSubcircuitVolt error
[D][05:18:29][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:18:29][COMM]BAT CAN get state1 Fail 204
[D][05:18:29][COMM]BAT CAN get soc Fail, 204
[D][05:18:29][COMM]get bat work state err
[W][05:18:29][PROT]remove success[1629955109],send_path[2],type[0000],priority[0],index[1],used[0]
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:29][PROT]index:0
[D][05:18:29][PROT]is_send:1
[D][05:18:29][PROT]

2025-07-31 20:05:01:639 ==>> sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x3
[D][05:18:29][PROT]msg_type:0x5d03
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]Sending traceid[9999999999900005]
[D][05:18:29][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:18:29][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:18:29][PROT]ble is not inited or not connected or cccd not enabled
[D][05:18:29][PROT]index:0 1629955109
[D][05:18:29][PROT]is_send:0
[D][05:18:29][PROT]sequence_num:4
[D][05:18:29][PROT]retry_timeout:0
[D][05:18:29][PROT]retry_times:3
[D][05:18:29][PROT]send_path:0x2
[D][05:18:29][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:29][PROT]===========================================================
[W][05:18:29][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955109]
[D][05:18:29][PROT]===========================================================
[D][05:18:29][PROT]sending traceid [99

2025-07-31 20:05:01:745 ==>> 99999999900005]
[D][05:18:29][PROT]Send_TO_M2M [1629955109]
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:29][SAL ]sock send credit cnt[6]
[D][05:18:29][SAL ]sock send ind credit cnt[6]
[D][05:18:29][M2M ]m2m send data len[198]
[D][05:18:29][SAL ]Cellular task submsg id[10]
[D][05:18:29][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052ef8] format[0]
[D][05:18:29][COMM]Receive Bat Lock cmd 0
[D][05:18:29][COMM]VBUS is 1
[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:29][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[W][05:18:29][PROT]add success [1629955109],send_path[2],type[D302],priority[0],index[1],used[1]
[D][05:18:29][COMM]Main Task receive event:61 finished processing
[D][05:18:29][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:29][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:18:29][M2M ]m2m swi

2025-07-31 20:05:01:850 ==>> tch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:29][CAT1]gsm read msg sub id: 15
[D][05:18:29][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:29][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:29][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:29][CAT1]Send Data To Server[198][198] ... ->:
0063B98F113311331133113311331B88B56C91EB5D85DE354E9087A364F11865FC0793619858C5F4AFD3495A3196D2B7BE238027A7FE4887D0D44B435D7F096A9D12D66ECDC53BD44B703127CA6ADAEF814BEBE4B23A695751B2AC0723234610287B6C
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:29][CAT1]<<< 
SEND OK

[D][05:18:29][CAT1]exec over: func id: 15, ret: 11
[D][05:18:29][CAT1]sub id: 15, ret: 11

[D][05:18:29][SAL ]Cellular task submsg id[68]
[D][05:18:29][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:29][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:29][M2M ]g_m2m_is_idle become true
[D][05:18:29][M2M ]m2m 

2025-07-31 20:05:01:941 ==>> switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:29][PROT]M2M Send ok [1629955109]
$GBGGA,120504.508,,,,,0,00,,,M,,M,,*65

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,43,24,,,42,42,,,42,3,,,41,1*45

$GBGSV,7,2,26,60,,,41,25,,,41,59,,,41,14,,,41,1*7F

$GBGSV,7,3,26,13,,,39,38,,,39,1,,,39,39,,,39,1*44

$GBGSV,7,4,26,40,,,38,16,,,38,2,,,37,8,,,37,1*78

$GBGSV,7,5,26,41,,,37,9,,,36,6,,,36,26,,,36,1*7F

$GBGSV,7,6,26,7,,,35,5,,,35,10,,,33,21,,,33,1*73

$GBGSV,7,7,26,4,,,33,34,,,31,1*43

$GBRMC,120504.508,V,,,,,,,310725,0.1,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:29][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
$GBGST,120504.508,0.000,782.270,782.270,715.404,2097152,2097152,2097152*6D

[D][05:18:29][COMM]f:[ec800m_aud

2025-07-31 20:05:02:032 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:05:02:754 ==>> $GBGGA,120506.508,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,25,,,41,59,,,41,1*4F

$GBGSV,7,2,26,24,,,41,42,,,41,14,,,41,60,,,40,1*75

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,2,,,36,1*44

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,7,,,35,1*73

$GBGSV,7,6,26,5,,,35,26,,,35,10,,,33,4,,,33,1*77

$GBGSV,7,7,26,21,,,32,34,,,31,1*75

$GBRMC,120506.508,V,,,,,,,310725,0.1,E,N,V*40

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120506.508,0.000,775.104,775.104,708.851,2097152,2097152,2097152*6F



2025-07-31 20:05:02:844 ==>> [D][05:18:31][COMM]read battery soc:255


2025-07-31 20:05:03:285 ==>> [D][05:18:32][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:05:03:501 ==>> [D][05:18:32][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:05:03:743 ==>> $GBGGA,120507.508,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,59,,,42,33,,,42,3,,,41,60,,,41,1*4D

$GBGSV,7,2,26,25,,,41,24,,,41,42,,,41,14,,,41,1*75

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,16,,,38,2,,,37,41,,,37,1*45

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,7,,,35,1*73

$GBGSV,7,6,26,5,,,35,26,,,35,4,,,34,10,,,33,1*70

$GBGSV,7,7,26,21,,,32,34,,,31,1*75

$GBRMC,120507.508,V,,,,,,,310725,0.1,E,N,V*41

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120507.508,0.000,778.288,778.288,711.762,2097152,2097152,2097152*69



2025-07-31 20:05:04:304 ==>> [D][05:18:33][COMM]crc 108B
[D][05:18:33][COMM]flash test ok


2025-07-31 20:05:04:589 ==>> [D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:33][COMM]44365 imu init OK
[D][05:18:33][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:33][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:33][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:33][COMM]accel parse set 0
[D][05:18:33][COMM][Audio].l:[1012].open hexlog save


2025-07-31 20:05:04:694 ==>> $GBGGA,120508.508,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,25,,,41,59,,,41,1*4F

$GBGSV,7,2,26,24,,,41,42,,,41,14,,,41,60,,,40,1*75

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,2

2025-07-31 20:05:04:754 ==>> 6,40,,,38,16,,,38,41,,,37,2,,,36,1*44

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,7,,,35,1*73

$GBGSV,7,6,26,5,,,35,26,,,35,10,,,33,4,,,33,1*77

$GBGSV,7,7,26,21,,,32,34,,,31,1*75

$GBRMC,120508.508,V,,,,,,,310725,0.1,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120508.508,0.000,775.104,775.104,708.851,2097152,2097152,2097152*61



2025-07-31 20:05:04:859 ==>> [D][05:18:33][COMM]read battery soc:255


2025-07-31 20:05:05:046 ==>> 【检测音频FLASH】通过,【flash test ok】符合目标值【flash test ok】要求!
2025-07-31 20:05:05:051 ==>> 检测【打开喇叭声音】
2025-07-31 20:05:05:059 ==>> 向【COM34】发送指令:【AT+BEEP】
2025-07-31 20:05:06:029 ==>> [W][05:18:34][COMM]>>>>>Input command = AT+BEEP<<<<<
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[frm_audio_send_cmd].send cmd type:0, format:1, file[A20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[D][05:18:34][COMM]file:A20 exist
[D][05:18:34][COMM][Audio].l:[255]. success, file_name:A20, size:15228
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[920].cmd file 'A20'
[D][05:18:34][COMM]read file, len:15228, num:4
[D][05:18:34][COMM]--->crc16:0x419c
[D][05:18:34][COMM]read file success
[W][05:18:34][COMM][Audio].l:[936].close hexlog save
[D][05:18:34][COMM]accel parse set 1
[D][05:18:34][COMM][Audio]mon:9,05:18:34
[D][05:18:34][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[945].file 'A20' size:15228
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,15228,2048,16796

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[691].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:18:34][COMM]f:[ec800m_aud

2025-07-31 20:05:06:092 ==>> 【打开喇叭声音】通过,【frm_audio_send_cmd】符合目标值【frm_audio_send_cmd】要求!
2025-07-31 20:05:06:100 ==>> 检测【打开大灯控制】
2025-07-31 20:05:06:109 ==>> 向【COM34】发送指令:【AT+ARMLAMP=1】
2025-07-31 20:05:06:133 ==>> io_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:34][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,15228,0

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv >
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:8
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend

2025-07-31 20:05:06:238 ==>> , index:5, len:2048
[D][05:18:34][COMM]45378 imu init OK
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:7, len:2048
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:8, len:892
[D][05:18:34][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
$GBGGA,120509.508,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,3,,,41,60,,,41,25,,,41,1*45

$GBGSV,7,2,26,59,,,41,24,,,41,42,,,41,14,,,41,1*7E

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,16,,,38,41,,,37,2,,,36,1*44

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,7,,,35,1*73

$GBGSV,7,6,26,5,,,35,26,,,35,10,,,33,4,,,33,1*77

$GBGSV,7,7,26,21,,,32,34,,,31,1*75

$GBRMC,120509.508,V,,,,,,,310725,0.1,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

[D][05:18:34][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A


2025-07-31 20:05:06:343 ==>> $GBGST,120509.508,0.000,775.902,775.902,709.580,2097152,2097152,2097152*60

[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:18:34][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:12000ms
[D][05:18:34][PROT]CLEAN,SEND:0
[D][05:18:34][PROT]index:0 1629955114
[D][05:18:34][PROT]is_send:0
[D][05:18:34][PROT]sequence_num:4
[D][05:18:34][PROT]retry_timeout:0
[D][05:18:34][PROT]retry_times:2
[D][05:18:34][PROT]send_path:0x2
[D][05:18:34][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:34][PROT]===========================================================
[W][05:18:34][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955114]
[D][05:18:34][PROT]===========================================================
[D][05:18:34][PROT]sending traceid [9999999999900005]
[D][05:18:34][PROT]Send_TO_M2M [1629955114]
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:34][SAL ]sock send credit cnt[6]
[D][05:18:34][SAL ]sock send ind credit cnt[6]
[D][05:18:34][M2M ]m2m send data len[198]
[D][05:18:34][SAL ]Cellular task submsg id[10]
[D][05:18:34][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:34]

2025-07-31 20:05:06:433 ==>> [CAT1]gsm read msg sub id: 15
[D][05:18:34][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:34][CAT1]Send Data To Server[198][201] ... ->:
0063B980113311331133113311331B88B520DEF3F291F3D33EB92056DC88A6D3F510D6962CA62E94CD62079235296BA947F56EC5417EE22F87F90B10B636D860AFB09A3233ECE78E1BFB34995ADDC98831577397A7D36372B8BCEF2A4CC01F255CA7A4
[D][05:18:34][CAT1]<<< 
SEND OK

[D][05:18:34][CAT1]exec over: func id: 15, ret: 11
[D][05:18:34][CAT1]sub id: 15, ret: 11

[D][05:18:34][SAL ]Cellular task submsg id[68]
[D][05:18:34][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:34][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:34][M2M ]g_m2m_is_idle become true
[D][05:18:34][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:34][PROT]M2M Send ok [1629955114]


2025-07-31 20:05:06:523 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ARMLAMP=1<<<<<


2025-07-31 20:05:06:617 ==>> 【打开大灯控制】通过,【>>>>>Input command = AT+ARMLAMP=1<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=1<<<<<】要求!
2025-07-31 20:05:06:624 ==>> 检测【关闭仪表供电3】
2025-07-31 20:05:06:644 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:05:06:748 ==>> $GBGGA,120510.508,,,,,0,00,,,M,,M,,*60

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,59,,,42,33,,,42,3,,,41,60,,,41,1*4D

$GBGSV,7,2,26,25,,,41,24,,,41,42,,,41,14,,,41,1*75

$GBGSV,7,3,26,13,,,39,1,,,39,39,,,39,38,,,38,1*45

$GBGSV,7,4,26,40,,,38,16,,,38,2,,,37,41,,,37,1*45

$GBGSV,7,5,26,8,,,36,9,,,36,6,,,36,7,,,35,1*73

$GBGSV,7,6,26,26,,,35,5,,,34,10,,,33,4,,,33,1*76

$GBGSV,7,7,26,21,,,32,34,,,30,1*74

$GBRMC,120510.508,V,,,,,,,310725,0.1,E,N,V*47

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,120510.508,0.000,775.910,775.910,709.588,2097152,2097152,2097152*60



2025-07-31 20:05:06:853 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:35][COMM]set POWER 0
[D][05:18:35][COMM]read battery soc:255


2025-07-31 20:05:06:887 ==>> 【关闭仪表供电3】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:05:06:894 ==>> 检测【关闭AccKey2供电3】
2025-07-31 20:05:06:919 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:05:07:018 ==>> [W][05:18:35][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:05:07:163 ==>> 【关闭AccKey2供电3】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:05:07:172 ==>> 检测【读大灯电压】
2025-07-31 20:05:07:181 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:05:07:318 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[33317]


2025-07-31 20:05:07:442 ==>> 【读大灯电压】通过,【33317mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:05:07:451 ==>> 检测【关闭大灯控制2】
2025-07-31 20:05:07:473 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:05:07:593 ==>> [W][05:18:36][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:05:07:718 ==>> 【关闭大灯控制2】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:05:07:726 ==>> 检测【关大灯控制后读大灯电压】
2025-07-31 20:05:07:748 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:05:08:425 ==>> $GBGGA,120507.514,2301.2572161,N,11421.9414891,E,1,08,1.61,71.536,M,-1.770,M,,*50

$GBGSA,A,3,14,33,24,42,39,25,38,41,,,,,2.52,1.61,1.93,4*06

$GBGSV,7,1,26,14,82,223,41,3,61,191,41,33,61,311,42,24,59,358,42,1*45

$GBGSV,7,2,26,42,57,158,41,59,52,129,41,16,52,17,38,39,52,1,39,1*45

$GBGSV,7,3,26,6,51,29,36,1,48,126,39,2,46,238,37,60,41,238,41,1*7A

$GBGSV,7,4,26,9,38,312,36,25,37,265,41,40,36,169,38,7,35,184,35,1*7F

$GBGSV,7,5,26,38,35,191,39,21,32,326,31,13,32,211,39,4,32,112,34,1*42

$GBGSV,7,6,26,8,29,205,36,10,25,186,33,5,22,257,34,26,21,43,35,1*4F

$GBGSV,7,7,26,41,12,323,37,34,,,30,1*46

$GBRMC,120507.514,A,2301.2572161,N,11421.9414891,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

[D][05:18:36][GNSS]HD8040 GPS
[D][05:18:36][GNSS]GPS diff_sec 124008391, report 0x42 frame
$GBGST,120507.514,1.003,0.229,0.195,0.285,2.787,5.771,12*5E

[D][05:18:36][COMM]Main Task receive event:131
[D][05:18:36][COMM]index:0,power_mode:0xFF
[D][05:18:36][COMM]index:1,sound_mode:0xFF
[D][05:18:36][COMM]index:2,gsensor_mode:0xFF
[D][05:18:36][COMM]index:3,report_freq_mode:0xFF
[D][05:18:36][COMM]index:4,report_period:0xFF
[D][05:18:36][COMM]index:5,normal_reset_mode:0xFF
[D][05:18:36][COMM]index:6,normal_reset_period:0xFF
[D][05:

2025-07-31 20:05:08:530 ==>> 18:36][COMM]index:7,spock_over_speed:0xFF
[D][05:18:36][COMM]index:8,spock_limit_speed:0xFF
[D][05:18:36][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:18:36][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:18:36][COMM]index:11,ble_scan_mode:0xFF
[D][05:18:36][COMM]index:12,ble_adv_mode:0xFF
[D][05:18:36][COMM]index:13,spock_audio_volumn:0xFF
[D][05:18:36][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:18:36][COMM]index:15,bat_auth_mode:0xFF
[D][05:18:36][COMM]index:16,imu_config_params:0xFF
[D][05:18:36][COMM]index:17,long_connect_params:0xFF
[D][05:18:36][COMM]index:18,detain_mark:0xFF
[D][05:18:36][COMM]index:19,lock_pos_report_count:0xFF
[D][05:18:36][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:18:36][COMM]index:21,mc_mode:0xFF
[D][05:18:36][COMM]index:22,S_mode:0xFF
[D][05:18:36][COMM]index:23,overweight:0xFF
[D][05:18:36][COMM]index:24,standstill_mode:0xFF
[D][05:18:36][COMM]index:25,night_mode:0xFF
[D][05:18:36][COMM]index:26,experiment1:0xFF
[D][05:18:36][COMM]index:27,experiment2:0xFF
[D][05:18:36][COMM]index:28,experiment3:0xFF
[D][05:18:36][COMM]index:29,experiment4:0xFF
[D][05:18:36][COMM]index:30,night_mode_start:0xFF
[D][05:18:

2025-07-31 20:05:08:635 ==>> 36][COMM]index:31,night_mode_end:0xFF
[D][05:18:36][COMM]index:33,park_report_minutes:0xFF
[D][05:18:36][COMM]index:34,park_report_mode:0xFF
[D][05:18:36][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:18:36][COMM]index:38,charge_battery_para: FF
[D][05:18:36][COMM]index:39,multirider_mode:0xFF
[D][05:18:36][COMM]index:40,mc_launch_mode:0xFF
[D][05:18:36][COMM]index:41,head_light_enable_mode:0xFF
[D][05:18:36][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:18:36][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:18:36][COMM]index:44,riding_duration_config:0xFF
[D][05:18:36][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:18:36][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:18:36][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:18:36][COMM]index:48,shlmt_sensor_en:0xFF
[D][05:18:36][COMM]index:49,mc_load_startup:0xFF
[D][05:18:36][COMM]index:50,mc_tcs_mode:0xFF
[D][05:18:36][COMM]index:51,traffic_audio_play:0xFF
[D][05:18:36][COMM]index:52,traffic_mode:0xFF
[D][05:18:36][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:18:36][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:18:36][COMM]index:55,wheel_alarm_play_switch:255
[D][05:18:36][COMM]index:57,

2025-07-31 20:05:08:740 ==>> traffic_sens_cycle:0xFF
[D][05:18:36][COMM]index:58,traffic_light_threshold:0xFF
[D][05:18:36][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:18:36][COMM]index:60,traffic_road_threshold:0xFF
[D][05:18:36][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:18:36][COMM]index:63,experiment5:0xFF
[D][05:18:36][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:18:36][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:18:36][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:18:36][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:18:36][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:18:36][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:18:36][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:18:36][COMM]index:72,experiment6:0xFF
[D][05:18:36][COMM]index:73,experiment7:0xFF
[D][05:18:36][COMM]index:74,load_messurement_cfg:0xff
[D][05:18:36][COMM]index:75,zero_value_from_server:-1
[D][05:18:36][COMM]index:76,multirider_threshold:255
[D][05:18:36][COMM]index:77,experiment8:255
[D][05:18:36][COMM]index:78,temp_park_audio_play_duration:255
[D][05:18:36][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:18:36][COMM]index:80,temp_park_reminder_timeout_dur

2025-07-31 20:05:08:755 ==>> 向【COM34】发送指令:【AT+ARM_ADC=5】
2025-07-31 20:05:08:845 ==>> ation:255
[D][05:18:36][COMM]index:82,loc_report_low_speed_thr:255
[D][05:18:36][COMM]index:83,loc_report_interval:255
[D][05:18:36][COMM]index:84,multirider_threshold_p2:255
[D][05:18:36][COMM]index:85,multirider_strategy:255
[D][05:18:36][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:18:36][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:18:36][COMM]index:90,weight_param:0xFF
[D][05:18:36][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:18:36][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:18:36][COMM]index:95,current_limit:0xFF
[D][05:18:36][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:18:36][COMM]index:100,location_mode:0xFF

[W][05:18:36][PROT]remove success[1629955116],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:18:36][PROT]add success [1629955116],send_path[2],type[4205],priority[0],index[2],used[1]
[D][05:18:36][COMM]Main Task receive event:131 finished processing
[D][05:18:36][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:18:36][M2M ]m2m_task: gpc:[0],gpo:[1]
[W][05:18:36][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:36][COMM]arm_hub read adc[5],val[92]
$GBGGA,120508.014,2301.25

2025-07-31 20:05:08:950 ==>> 77507,N,11421.9412231,E,1,11,1.22,72.797,M,-1.770,M,,*58

$GBGSA,A,3,14,33,24,42,39,13,09,25,38,40,41,,1.94,1.22,1.51,4*09

$GBGSV,7,1,26,14,82,223,41,3,61,191,41,33,61,311,42,24,59,358,42,1*45

$GBGSV,7,2,26,42,57,158,42,59,52,129,41,16,52,17,38,39,52,1,39,1*46

$GBGSV,7,3,26,6,51,29,36,13,50,221,39,9,49,314,36,1,48,126,39,1*73

$GBGSV,7,4,26,2,46,238,37,60,41,238,41,25,37,265,41,7,35,184,35,1*7E

$GBGSV,7,5,26,38,35,191,38,21,32,326,32,4,32,112,33,8,29,205,36,1*7D

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,35,26,21,43,35,1*7D

$GBGSV,7,7,26,41,12,323,37,34,,,30,1*46

$GBGSV,2,1,06,33,61,311,36,24,59,358,41,42,57,158,41,39,52,1,39,5*7C

$GBGSV,2,2,06,25,37,265,39,38,35,191,36,5*7D

$GBRMC,120508.014,A,2301.2577507,N,11421.9412231,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120508.014,1.335,0.531,0.424,0.617,1.689,2.726,6.716*7E

[D][05:18:37][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:05:09:029 ==>> 【关大灯控制后读大灯电压】通过,【92mV】符合目标值【0mV】至【2000mV】要求!
2025-07-31 20:05:09:038 ==>> 检测【打开WIFI(4)】
2025-07-31 20:05:09:060 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:05:09:065 ==>>                                          [W][05:18:37][COMM]>>>>>Input command = AT+ARM_ADC=5<<<<<
[D][05:18:37][COMM]arm_hub read adc[5],val[115]


2025-07-31 20:05:09:406 ==>> $GBGGA,120509.000,2301.2578161,N,11421.9414778,E,1,16,0.87,72.466,M,-1.770,M,,*5D

$GBGSA,A,3,14,03,33,24,42,39,59,13,09,02,01,60,1.68,0.87,1.43,4*00

$GBGSA,A,3,25,38,40,41,,,,,,,,,1.68,0.87,1.43,4*03

$GBGSV,7,1,26,14,82,223,41,3,63,190,41,33,61,311,42,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,16,52,17,38,39,52,1,39,6,51,29,36,1*4E

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,60,43,241,41,25,37,265,41,7,35,184,35,1*71

$GBGSV,7,5,26,38,35,191,38,21,32,326,32,4,32,112,33,8,29,205,36,1*7D

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,35,26,21,43,35,1*7D

$GBGSV,7,7,26,41,12,323,37,34,,,31,1*47

$GBGSV,2,1,07,33,61,311,39,24,59,358,42,42,57,158,42,39,52,1,40,5*7C

$GBGSV,2,2,07,25,37,265,39,38,35,191,37,41,12,323,31,5*4B

$GBRMC,120509.000,A,2301.2578161,N,11421.9414778,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,120509.000,1.666,0.528,0.482,0.710,1.626,2.209,5.402*73

[W][05:18:38][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:38][COMM]49105 imu init OK
[D][05:18:38][COMM]imu_task imu work error:[-1]. goto init
[D][05:18:38][CAT1]<<< 
OK

[D][05:18:38

2025-07-31 20:05:09:436 ==>> ][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:05:09:652 ==>> 【打开WIFI(4)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:05:09:658 ==>> 检测【EC800M模组版本】
2025-07-31 20:05:09:667 ==>> 向【COM34】发送指令:【AT+GETVERSION=TOTAL】
2025-07-31 20:05:09:891 ==>> [W][05:18:38][COMM]>>>>>Input command = AT+GETVERSION=TOTAL<<<<<
[D][05:18:38][CAT1]gsm read msg sub id: 12
[D][05:18:38][CAT1]SEND RAW data >>> AT+GETVERSION=TOTAL

[D][05:18:38][CAT1]<<< 
+GETVERSION: "TOTAL","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:38][CAT1]exec over: func id: 12, ret: 132


2025-07-31 20:05:09:925 ==>> 【EC800M模组版本】通过,【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】符合目标值【EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1】要求!
2025-07-31 20:05:09:932 ==>> 检测【配置蓝牙地址】
2025-07-31 20:05:09:954 ==>> 向【COM34】发送指令:【nRFReset】
2025-07-31 20:05:09:963 ==>>                                                                                                                                                              n report total[4]


2025-07-31 20:05:10:041 ==>> [D][05:18:38][GNSS]recv submsg id[3]


2025-07-31 20:05:10:116 ==>> [W][05:18:38][COMM]>>>>>Input command = nRFReset<<<<<


2025-07-31 20:05:10:131 ==>> 向【COM34】发送指令:【<SPBSJ*MAC:E4AE7AC8032E>】
2025-07-31 20:05:10:404 ==>> $GBGGA,120510.000,2301.2577973,N,11421.9415759,E,1,18,0.84,72.758,M,-1.770,M,,*50

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.65,0.84,1.42,4*09

$GBGSA,A,3,01,60,25,38,40,41,,,,,,,1.65,0.84,1.42,4*0B

$GBGSV,7,1,26,14,82,223,41,3,63,190,41,33,61,311,42,24,59,358,41,1*45

$GBGSV,7,2,26,42,57,158,41,6,53,337,36,16,53,341,38,39,52,1,39,1*42

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,60,43,241,40,25,37,265,41,7,35,184,35,1*70

$GBGSV,7,5,26,38,35,191,39,21,33,326,32,4,32,112,33,8,29,205,36,1*7D

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,34,26,21,43,35,1*7C

$GBGSV,7,7,26,41,12,323,37,34,,,31,1*47

$GBGSV,2,1,08,33,61,311,41,24,59,358,43,42,57,158,42,39,52,1,40,5*7D

$GBGSV,2,2,08,25,37,265,40,38,35,191,37,40,28,160,35,41,12,323,32,5*76

$GBRMC,120510.000,A,2301.2577973,N,11421.9415759,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120510.000,1.516,0.248,0.227,0.340,1.388,1.820,4.517*71

[D][05:18:39][COMM]50115 imu init OK
[D][05:18:39][COMM]imu_task imu work error:[-1]. goto init
recv ble 1
recv ble 2
ble set mac ok :e4,ae,7a,c8,3,2e
enable filters ret : 0

2025-07-31 20:05:10:665 ==>> 【配置蓝牙地址】通过,【ble set mac ok】符合目标值【ble set mac ok】要求!
2025-07-31 20:05:10:674 ==>> 检测【BLETEST】
2025-07-31 20:05:10:696 ==>> 向【COM34】发送指令:【4A A4 01 A4 4A】
2025-07-31 20:05:10:736 ==>> 4A A4 01 A4 4A 


2025-07-31 20:05:10:946 ==>> [D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:15
2B415544494F504C41593A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_send_hexdata_end].l:[824].Received +AUDIOPLAY:OK from slave
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[856].audio cmd send:AT+AUDIOSEND=0

[D][05:18:39][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:18:39][COMM]f:[ec800m_audio_end].l:[863].recv ok
[D][05:18:39][COMM]accel parse set 0
[D][05:18:39][COMM][Audio].l:[1012].open hexlog save
[D][05:18:39][COMM]read battery soc:255
recv ble 1
recv ble 2
<BSJ*MAC:E4AE7AC8032E*RSSI:-22*ADD:1107656B69626F6D52005A004700A0FA00A0*SCAN:02010607096D6F62696B6513FFB30402E9E4AE7AC8032E99999OVER 150


2025-07-31 20:05:11:233 ==>> [D][05:18:39][PROT]CLEAN,SEND:0
[D][05:18:39][PROT]index:0 1629955119
[D][05:18:39][PROT]is_send:0
[D][05:18:39][PROT]sequence_num:4
[D][05:18:39][PROT]retry_timeout:0
[D][05:18:39][PROT]retry_times:1
[D][05:18:39][PROT]send_path:0x2
[D][05:18:39][PROT]min_index:0, type:0x5D03, priority:4
[D][05:18:39][PROT]===========================================================
[W][05:18:39][PROT]SEND DATA TYPE:5D03, SENDPATH:0x2 [1629955119]
[D][05:18:39][PROT]===========================================================
[D][05:18:39][PROT]sending traceid [9999999999900005]
[D][05:18:39][PROT]Send_TO_M2M [1629955119]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:39][SAL ]sock send credit cnt[6]
[D][05:18:39][SAL ]sock send ind credit cnt[6]
[D][05:18:39][M2M ]m2m send data len[198]
[D][05:18:39][SAL ]Cellular task submsg id[10]
[D][05:18:39][SAL ]cellular SEND socket id[0] type[1], len[198], data[0x20052de0] format[0]
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:39][CAT1]gsm read msg sub id: 15
[D][05:18:39][CAT1]tx ret[17] >>> AT+QISEND=0,198

[D][05:18:39][CAT1]Send Data To Server[198][2

2025-07-31 20:05:11:308 ==>> 01] ... ->:
0063B98D113311331133113311331B88B532634AFA2AA73677DC355BFCB74755F6E0936DB45EFA6EA5F26825B46814BD9329622E2C980567BD7705CCD140152D8684C5856C7C5720E039608C561CF69C3B9DEC0D692DA9B72AE09FD9E64AFDD0BA274F
[D][05:18:39][CAT1]<<< 
SEND OK

[D][05:18:39][CAT1]exec over: func id: 15, ret: 11
[D][05:18:39][CAT1]sub id: 15, ret: 11

[D][05:18:39][SAL ]Cellular task submsg id[68]
[D][05:18:39][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:39][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:39][M2M ]g_m2m_is_idle become true
[D][05:18:39][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:39][PROT]M2M Send ok [1629955119]


2025-07-31 20:05:11:413 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:05:11:688 ==>> 【BLETEST】通过,【-22dB】符合目标值【-48dB】至【-10dB】要求!
2025-07-31 20:05:11:696 ==>> 该项需要延时执行
2025-07-31 20:05:12:377 ==>> $GBGGA,120512.000,2301.2577703,N,11421.9415352,E,1,21,0.80,73.361,M,-1.770,M,,*55

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.53,0.80,1.30,4*0D

$GBGSA,A,3,01,08,60,25,38,07,40,10,41,,,,1.53,0.80,1.30,4*01

$GBGSV,7,1,26,14,82,223,41,3,63,190,41,33,61,311,42,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,39,1*41

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,36,1*76

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,21,33,326,32,4,32,112,33,1*7E

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,35,26,21,43,35,1*7D

$GBGSV,7,7,26,41,12,323,37,34,,,30,1*46

$GBGSV,2,1,08,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*7F

$GBGSV,2,2,08,25,37,265,40,38,35,191,37,40,28,160,35,41,12,323,32,5*76

$GBRMC,120512.000,A,2301.2577703,N,11421.9415352,E,0.002,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120512.000,1.556,0.242,0.220,0.324,1.297,1.569,3.672*70



2025-07-31 20:05:12:879 ==>> [D][05:18:41][COMM]read battery soc:255


2025-07-31 20:05:13:378 ==>> $GBGGA,120513.000,2301.2577839,N,11421.9415615,E,1,21,0.80,73.187,M,-1.770,M,,*5E

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.53,0.80,1.30,4*0D

$GBGSA,A,3,01,08,60,25,38,07,40,10,41,,,,1.53,0.80,1.30,4*01

$GBGSV,7,1,26,14,82,223,41,3,63,190,41,33,61,311,43,24,59,358,42,1*47

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,39,1*41

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,36,1*76

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,21,33,326,32,4,32,112,34,1*79

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,35,26,21,43,35,1*7D

$GBGSV,7,7,26,41,12,323,37,34,,,30,1*46

$GBGSV,2,1,08,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*7F

$GBGSV,2,2,08,25,37,265,40,38,35,191,37,40,28,160,34,41,12,323,32,5*77

$GBRMC,120513.000,A,2301.2577839,N,11421.9415615,E,0.001,0.00,310725,,,A,S*3D

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120513.000,1.539,0.208,0.192,0.281,1.253,1.485,3.420*7C



2025-07-31 20:05:14:379 ==>> $GBGGA,120514.000,2301.2578169,N,11421.9415908,E,1,21,0.80,73.144,M,-1.770,M,,*56

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.53,0.80,1.30,4*0D

$GBGSA,A,3,01,08,60,25,38,07,40,10,41,,,,1.53,0.80,1.30,4*01

$GBGSV,7,1,26,14,82,223,42,3,63,190,41,33,61,311,43,24,59,358,42,1*44

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,21,33,326,32,4,32,112,34,1*79

$GBGSV,7,6,26,40,28,160,38,10,25,186,33,5,22,257,34,26,21,43,35,1*7C

$GBGSV,7,7,26,41,12,323,37,34,,,30,1*46

$GBGSV,2,1,08,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,41,5*7E

$GBGSV,2,2,08,25,37,265,40,38,35,191,37,40,28,160,34,41,12,323,32,5*77

$GBRMC,120514.000,A,2301.2578169,N,11421.9415908,E,0.001,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120514.000,1.526,0.222,0.205,0.297,1.220,1.421,3.220*7B



2025-07-31 20:05:14:906 ==>> [D][05:18:43][COMM]read battery soc:255


2025-07-31 20:05:15:395 ==>> $GBGGA,120515.000,2301.2578342,N,11421.9415889,E,1,23,0.67,73.035,M,-1.770,M,,*58

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.21,0.67,1.01,4*03

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,,1.21,0.67,1.01,4*08

$GBGSV,7,1,26,14,82,223,41,3,63,190,41,33,61,311,43,24,59,358,42,1*47

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,37,1*7E

$GBGSV,7,7,26,21,8,148,32,34,6,162,30,1*72

$GBGSV,2,1,08,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,41,5*7E

$GBGSV,2,2,08,25,37,265,40,38,35,191,37,40,28,160,35,41,12,323,32,5*76

$GBRMC,120515.000,A,2301.2578342,N,11421.9415889,E,0.003,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,120515.000,1.578,0.244,0.225,0.322,1.242,1.413,3.080*71



2025-07-31 20:05:16:563 ==>> [D][05:18:45][PROT]CLEAN,SEND:0
[D][05:18:45][PROT]CLEAN:0
$GBGGA,120516.000,2301.2578377,N,11421.9416166,E,1,24,0.66,72.770,M,-1.770,M,,*57

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,22,257,35,26,21,43,35,41,12,323,37,1*7F

$GBGSV,7,7,26,21,8,148,33,34,6,162,30,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,41,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,35,26,21,43,35,5*4C

$GBGSV,3,3,11,41,12,323,32,21,8,148,34,34,6,162,29,5*49

$GBRMC,120516.000,A,2301.2578377,N,11421.9416166,E,0.001,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120516.000,3.561,0.236,0.219,0.304,2.452,2.555,3.837*76

[D][05:18:45][PROT]index:1 1629955125
[D][05:18:45][PROT]is_send:0
[D][05:18:45][PROT]sequence_num:5
[D][

2025-07-31 20:05:16:669 ==>> 05:18:45][PROT]retry_timeout:0
[D][05:18:45][PROT]retry_times:3
[D][05:18:45][PROT]send_path:0x2
[D][05:18:45][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:45][PROT]===========================================================
[W][05:18:45][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955125]
[D][05:18:45][PROT]===========================================================
[D][05:18:45][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:18:45][PROT]sending traceid [9999999999900006]
[D][05:18:45][PROT]Send_TO_M2M [1629955125]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:45][SAL ]sock send credit cnt[6]
[D][05:18:45][SAL ]sock send ind credit cnt[6]
[D][05:18:45][M2M ]m2m send data len[134]
[D][05:18:45][SAL ]Cellular task submsg id[10]
[D][05:18:45][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:45][CAT1]gsm read msg sub id: 15
[D][05:18:45][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:45][CAT1]Send Data To Server[134][137] ... ->:
0043B68C113311331133113311331B88B32DE691098371569A21D5ECF198CB179DE9554906E007BF65465D8D23411F5C73BAFE

2025-07-31 20:05:16:729 ==>> AE2A4B646141D2AD3401EAAFD049F31A
[D][05:18:45][CAT1]<<< 
SEND OK

[D][05:18:45][CAT1]exec over: func id: 15, ret: 11
[D][05:18:45][CAT1]sub id: 15, ret: 11

[D][05:18:45][SAL ]Cellular task submsg id[68]
[D][05:18:45][SAL ]handle subcmd ack sub_id[f], socket[0], result[11]
[D][05:18:45][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:18:45][M2M ]g_m2m_is_idle become true
[D][05:18:45][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:18:45][PROT]M2M Send ok [1629955125]


2025-07-31 20:05:16:895 ==>> [D][05:18:45][COMM]read battery soc:255


2025-07-31 20:05:17:394 ==>> $GBGGA,120517.000,2301.2578544,N,11421.9416232,E,1,24,0.66,72.571,M,-1.770,M,,*51

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,40,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,22,257,35,26,21,43,35,41,12,323,37,1*7F

$GBGSV,7,7,26,21,8,148,33,34,6,162,30,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,35,26,21,43,35,5*4C

$GBGSV,3,3,11,41,12,323,32,21,8,148,33,34,6,162,30,5*46

$GBRMC,120517.000,A,2301.2578544,N,11421.9416232,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120517.000,3.633,0.246,0.227,0.318,2.480,2.571,3.760*70



2025-07-31 20:05:18:397 ==>> $GBGGA,120518.000,2301.2578880,N,11421.9416448,E,1,24,0.66,72.390,M,-1.770,M,,*59

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,42,24,59,358,41,1*44

$GBGSV,7,2,26,42,57,158,41,6,53,337,36,16,53,341,38,39,52,1,39,1*42

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,37,1*7E

$GBGSV,7,7,26,21,8,148,33,34,6,162,30,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,33,34,6,162,30,5*46

$GBRMC,120518.000,A,2301.2578880,N,11421.9416448,E,0.002,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120518.000,3.616,0.223,0.207,0.291,2.465,2.546,3.663*74



2025-07-31 20:05:18:925 ==>> [D][05:18:47][COMM]read battery soc:255


2025-07-31 20:05:19:394 ==>> $GBGGA,120519.000,2301.2579114,N,11421.9416607,E,1,24,0.66,72.388,M,-1.770,M,,*5D

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,37,1*7E

$GBGSV,7,7,26,21,8,148,33,34,6,162,30,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,43,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,33,34,6,162,30,5*46

$GBRMC,120519.000,A,2301.2579114,N,11421.9416607,E,0.001,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120519.000,3.536,0.234,0.218,0.302,2.418,2.493,3.558*7F



2025-07-31 20:05:20:399 ==>> $GBGGA,120520.000,2301.2579231,N,11421.9416652,E,1,24,0.66,72.336,M,-1.770,M,,*56

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,37,16,53,341,38,39,52,1,40,1*4E

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,36,41,12,323,37,1*7D

$GBGSV,7,7,26,21,8,148,33,34,6,162,30,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,43,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,36,5*4E

$GBGSV,3,3,11,41,12,323,32,21,8,148,33,34,6,162,30,5*46

$GBRMC,120520.000,A,2301.2579231,N,11421.9416652,E,0.001,0.00,310725,,,A,S*31

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120520.000,3.470,0.192,0.183,0.252,2.379,2.447,3.465*7A



2025-07-31 20:05:20:934 ==>> [D][05:18:49][COMM]read battery soc:255


2025-07-31 20:05:21:408 ==>> $GBGGA,120521.000,2301.2579367,N,11421.9416767,E,1,24,0.66,72.309,M,-1.770,M,,*5E

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,37,1*7E

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,35,26,21,43,36,5*4F

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120521.000,A,2301.2579367,N,11421.9416767,E,0.001,0.00,310725,,,A,S*35

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120521.000,3.354,0.222,0.206,0.289,2.313,2.377,3.361*71



2025-07-31 20:05:21:633 ==>>                                                                                                                                  _num:5
[D][05:18:50][PROT]retry_timeout:0
[D][05:18:50][PROT]retry_times:2
[D][05:18:50][PROT]send_path:0x2
[D][05:18:50][PROT]min_index:1, type:0xD302, priority:0
[D][05:18:50][PROT]===========================================================
[W][05:18:50][PROT]SEND DATA TYPE:D302, SENDPATH:0x2 [1629955130]
[D][05:18:50][PROT]===========================================================
[D][05:18:50][COMM]PB encode data:29
0A1B0803120022033030302A0330303030013A0908A5C89C8906980220
[D][05:18:50][PROT]sending traceid [9999999999900006]
[D][05:18:50][PROT]Send_TO_M2M [1629955130]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:18:50][SAL ]sock send credit cnt[6]
[D][05:18:50][SAL ]sock send ind credit cnt[6]
[D][05:18:50][M2M ]m2m send data len[134]
[D][05:18:50][SAL ]Cellular task submsg id[10]
[D][05:18:50][SAL ]cellular SEND socket id[0] type[1], len[134], data[0x20052de0] format[0]
[D][05:18:50][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:18:50][CAT1]gsm read msg sub id: 15
[D][

2025-07-31 20:05:21:664 ==>> 05:18:50][CAT1]tx ret[17] >>> AT+QISEND=0,134

[D][05:18:50][CAT1]<<< 
ERROR



2025-07-31 20:05:21:693 ==>> 此处延时了:【10000】毫秒
2025-07-31 20:05:21:701 ==>> 检测【检测WiFi结果】
2025-07-31 20:05:21:716 ==>> WiFi信号:【CC057790A740】,信号值:-74
2025-07-31 20:05:21:722 ==>> WiFi信号:【CC057790A7C1】,信号值:-75
2025-07-31 20:05:21:729 ==>> WiFi信号:【CC057790A5C1】,信号值:-78
2025-07-31 20:05:21:735 ==>> WiFi信号:【CC057790A6E1】,信号值:-80
2025-07-31 20:05:21:769 ==>> WiFi信号:【F86FB0660A82】,信号值:-84
2025-07-31 20:05:21:776 ==>> WiFi数量【5】, 最大信号值:-74
2025-07-31 20:05:21:787 ==>> 检测【检测GPS结果】
2025-07-31 20:05:21:797 ==>> 符合定位需求的卫星数量:【21】
2025-07-31 20:05:21:820 ==>> 
北斗星号:【14】,信号值:【41】
北斗星号:【3】,信号值:【41】
北斗星号:【33】,信号值:【42】
北斗星号:【24】,信号值:【42】
北斗星号:【42】,信号值:【41】
北斗星号:【59】,信号值:【41】
北斗星号:【16】,信号值:【38】
北斗星号:【39】,信号值:【39】
北斗星号:【6】,信号值:【36】
北斗星号:【1】,信号值:【39】
北斗星号:【2】,信号值:【37】
北斗星号:【60】,信号值:【41】
北斗星号:【9】,信号值:【36】
北斗星号:【25】,信号值:【41】
北斗星号:【40】,信号值:【38】
北斗星号:【7】,信号值:【35】
北斗星号:【38】,信号值:【39】
北斗星号:【13】,信号值:【39】
北斗星号:【8】,信号值:【36】
北斗星号:【26】,信号值:【35】
北斗星号:【41】,信号值:【37】

2025-07-31 20:05:21:830 ==>> 检测【CSQ强度】
2025-07-31 20:05:21:848 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:05:21:903 ==>> [W][05:18:50][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:05:22:395 ==>> $GBGGA,120522.000,2301.2579359,N,11421.9416766,E,1,24,0.66,72.296,M,-1.770,M,,*56

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,42,33,61,311,43,24,59,358,42,1*45

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,36,1*7F

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,35,26,21,43,36,5*4F

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120522.000,A,2301.2579359,N,11421.9416766,E,0.002,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.002,N,0.004,K,A*29

$GBGST,120522.000,3.453,0.260,0.238,0.334,2.363,2.421,3.364*78



2025-07-31 20:05:22:941 ==>> [D][05:18:51][COMM]read battery soc:255


2025-07-31 20:05:23:393 ==>> $GBGGA,120523.000,2301.2579524,N,11421.9416823,E,1,24,0.66,72.217,M,-1.770,M,,*5C

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.14,0.66,0.92,4*0F

$GBGSA,A,3,01,08,60,25,38,07,40,10,26,41,21,34,1.14,0.66,0.92,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,22,257,34,26,21,43,35,41,12,323,37,1*7E

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,43,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,30,5*47

$GBRMC,120523.000,A,2301.2579524,N,11421.9416823,E,0.001,0.00,310725,,,A,S*39

$GBVTG,0.00,T,,M,0.001,N,0.001,K,A*2F

$GBGST,120523.000,3.471,0.210,0.197,0.273,2.370,2.424,3.338*74



2025-07-31 20:05:23:791 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:05:24:017 ==>> [W][05:18:52][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:05:24:398 ==>> $GBGGA,120524.000,2301.2579534,N,11421.9416715,E,1,25,0.62,72.175,M,-1.770,M,,*52

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,42,24,59,358,42,1*47

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,24,258,35,26,21,43,35,41,12,323,36,1*77

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,30,5*47

$GBRMC,120524.000,A,2301.2579534,N,11421.9416715,E,0.002,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120524.000,3.564,0.210,0.200,0.278,2.417,2.467,3.352*7D



2025-07-31 20:05:24:958 ==>> [D][05:18:53][COMM]read battery soc:255


2025-07-31 20:05:25:393 ==>> $GBGGA,120525.000,2301.2579479,N,11421.9416698,E,1,25,0.62,72.219,M,-1.770,M,,*56

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,42,24,59,358,42,1*47

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,24,258,34,26,21,43,35,41,12,323,36,1*76

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120525.000,A,2301.2579479,N,11421.9416698,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120525.000,3.616,0.247,0.232,0.323,2.442,2.489,3.349*7C



2025-07-31 20:05:25:851 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:05:26:061 ==>> [W][05:18:54][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:05:26:406 ==>> $GBGGA,120526.000,2301.2579419,N,11421.9416827,E,1,25,0.62,72.262,M,-1.770,M,,*55

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,39,1*41

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,24,258,35,26,21,43,35,41,12,323,36,1*77

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,43,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120526.000,A,2301.2579419,N,11421.9416827,E,0.001,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120526.000,3.635,0.208,0.198,0.275,2.450,2.495,3.334*70



2025-07-31 20:05:26:954 ==>> [D][05:18:55][COMM]read battery soc:255


2025-07-31 20:05:27:419 ==>> $GBGGA,120527.000,2301.2579440,N,11421.9416912,E,1,25,0.62,72.248,M,-1.770,M,,*57

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,42,3,63,190,41,33,61,311,43,24,59,358,42,1*45

$GBGSV,7,2,26,42,57,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,24,258,35,26,21,43,36,41,12,323,36,1*74

$GBGSV,7,7,26,21,8,148,33,34,6,162,31,1*72

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,57,158,42,39,52,1,40,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,30,5*47

$GBRMC,120527.000,A,2301.2579440,N,11421.9416912,E,0.003,0.00,310725,,,A,S*3F

$GBVTG,0.00,T,,M,0.003,N,0.005,K,A*29

$GBGST,120527.000,3.557,0.240,0.226,0.314,2.408,2.450,3.274*7B



2025-07-31 20:05:27:905 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:05:28:117 ==>> [W][05:18:56][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:05:28:417 ==>> $GBGGA,120528.000,2301.2579443,N,11421.9416993,E,1,25,0.62,72.250,M,-1.770,M,,*5B

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,56,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4E

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,24,258,34,26,21,43,36,41,12,323,36,1*75

$GBGSV,7,7,26,21,8,148,32,34,6,162,32,1*70

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,56,158,42,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,30,5*47

$GBRMC,120528.000,A,2301.2579443,N,11421.9416993,E,0.001,0.00,310725,,,A,S*38

$GBVTG,0.00,T,,M,0.001,N,0.003,K,A*2D

$GBGST,120528.000,3.601,0.209,0.199,0.275,2.429,2.469,3.273*76

[D][05:18:57][COMM]FeedAll is 0x0003c6bd, wdg event is 0x000

2025-07-31 20:05:28:447 ==>> 3c63d


2025-07-31 20:05:28:986 ==>> [D][05:18:57][COMM]read battery soc:255


2025-07-31 20:05:29:415 ==>> $GBGGA,120529.000,2301.2579392,N,11421.9417022,E,1,25,0.62,72.238,M,-1.770,M,,*5D

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,43,24,59,358,42,1*46

$GBGSV,7,2,26,42,56,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4E

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,37,60,43,241,41,25,37,265,41,1*71

$GBGSV,7,5,26,38,35,191,39,7,33,174,35,4,32,112,34,40,28,160,38,1*7F

$GBGSV,7,6,26,10,25,186,33,5,24,258,35,26,21,43,36,41,12,323,36,1*74

$GBGSV,7,7,26,21,8,148,32,34,6,162,31,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,56,158,43,39,52,1,41,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,36,5*4E

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,30,5*47

$GBRMC,120529.000,A,2301.2579392,N,11421.9417022,E,0.001,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.001,N,0.002,K,A*2C

$GBGST,120529.000,3.608,0.231,0.220,0.302,2.432,2.470,3.258*7E



2025-07-31 20:05:29:970 ==>> 向【COM34】发送指令:【AT+CSQ】
2025-07-31 20:05:30:167 ==>> [W][05:18:59][COMM]>>>>>Input command = AT+CSQ<<<<<


2025-07-31 20:05:30:407 ==>> $GBGGA,120530.000,2301.2579371,N,11421.9417077,E,1,25,0.62,72.227,M,-1.770,M,,*56

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,42,24,59,358,42,1*47

$GBGSV,7,2,26,42,56,158,42,6,53,337,36,16,53,341,38,39,52,1,40,1*4E

$GBGSV,7,3,26,59,51,128,41,13,50,221,39,9,49,314,36,2,48,240,37,1*77

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,24,258,34,26,21,43,36,41,12,323,36,1*75

$GBGSV,7,7,26,21,8,148,32,34,6,162,32,1*70

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,56,158,43,39,52,1,41,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,36,5*4E

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120530.000,A,2301.2579371,N,11421.9417077,E,0.003,0.00,310725,,,A,S*37

$GBVTG,0.00,T,,M,0.003,N,0.006,K,A*2A

$GBGST,120530.000,3.647,0.227,0.215,0.299,2.451,2.486,3.258*73



2025-07-31 20:05:30:776 ==>> [D][05:18:59][COMM]FeedAll is 0x0003c6bd, wdg event is 0x0003c63d


2025-07-31 20:05:30:987 ==>> [D][05:18:59][COMM]read battery soc:255


2025-07-31 20:05:31:398 ==>> $GBGGA,120531.000,2301.2579395,N,11421.9417119,E,1,25,0.62,72.201,M,-1.770,M,,*50

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,41,3,63,190,41,33,61,311,42,24,59,358,42,1*47

$GBGSV,7,2,26,42,56,158,42,6,53,337,37,16,53,341,38,39,52,1,40,1*4F

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,24,258,34,26,21,43,36,41,12,323,36,1*75

$GBGSV,7,7,26,21,8,148,32,34,6,162,32,1*70

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,56,158,42,39,52,1,41,5*76

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,36,5*4E

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120531.000,A,2301.2579395,N,11421.9417119,E,0.000,0.00,310725,,,A,S*36

$GBVTG,0.00,T,,M,0.000,N,0.000,K,A*2F

$GBGST,120531.000,3.745,0.203,0.194,0.269,2.500,2.534,3.288*72



2025-07-31 20:05:31:796 ==>> [D][05:19:00][CAT1]exec over: func id: 15, ret: -93
[D][05:19:00][CAT1]sub id: 15, ret: -93

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[f], socket[0], result[-93]
[D][05:19:00][SAL ]socket send fail. id[4]
[D][05:19:00][SAL ]select read evt socket_id[4], p_data[0] len[0]
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][M2M ]m2m select fd[4]
[D][05:19:00][M2M ]socket[4] Link is disconnected
[D][05:19:00][M2M ]tcpclient close[4]
[D][05:19:00][SAL ]socket[4] has closed
[D][05:19:00][PROT]protocol read data ok
[D][05:19:00][HSDK][0] flush to flash addr:[0xE41900] --- write len --- [256]
[E][05:19:00][M2M ]M2M_GSM_SOCKET_SEND_ACK GSM_ERROR
[E][05:19:00][PROT]M2M Send Fail [1629955140]
[D][05:19:00][PROT]CLEAN,SEND:1
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_SHUT_ACK
[D][05:19:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 25,99

O

2025-07-31 20:05:31:886 ==>> K

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21
[D][05:19:00][CAT1]gsm read msg sub id: 12
[D][05:19:00][CAT1]SEND RAW data >>> AT+CSQ

[D][05:19:00][CAT1]<<< 
+CSQ: 25,99

OK

[D][05:19:00][CAT1]exec over: func id: 12, ret: 21
[D][05:19:00][CAT1]gsm read msg sub id: 10
[D][05:19:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:00][CAT1]<<< 
+CGATT: 1

OK

[D][05:19:00][CAT1]tx ret[12] >>> AT+CGATT=0



2025-07-31 20:05:32:133 ==>> 【CSQ强度】通过,【25】符合目标值【18】至【31】要求!
2025-07-31 20:05:32:142 ==>> 检测【关闭GSM联网】
2025-07-31 20:05:32:149 ==>> 向【COM34】发送指令:【AT+GSMTEST=0】
2025-07-31 20:05:32:175 ==>> [D][05:19:00][CAT1]<<< 
OK

[D][05:19:00][CAT1]exec over: func id: 10, ret: 6
[D][05:19:00][CAT1]sub id: 10, ret: 6

[D][05:19:00][SAL ]Cellular task submsg id[68]
[D][05:19:00][SAL ]handle subcmd ack sub_id[a], socket[0], result[6]
[D][05:19:00][M2M ]m2m gsm shut done, ret[0]
[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:00][SAL ]open socket ind id[4], rst[0]
[D][05:19:00][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:00][SAL ]Cellular task submsg id[8]
[D][05:19:00][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:00][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:00][CAT1]gsm read msg sub id: 8
[D][05:19:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:00][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:00][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:00][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:05:32:264 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+GSMTEST=0<<<<<
[D][05:19:01][COMM]GSM test
[D][05:19:01][COMM]GSM test disable


2025-07-31 20:05:32:405 ==>> 【关闭GSM联网】通过,【GSM test disable】符合目标值【GSM test disable】要求!
2025-07-31 20:05:32:413 ==>> 检测【4G联网测试】
2025-07-31 20:05:32:424 ==>> 向【COM34】发送指令:【AT+ALLSTATE】
2025-07-31 20:05:32:564 ==>> [D][05:19:01][CAT1]pdpdeact urc len[22]
$GBGGA,120532.000,2301.2579425,N,11421.9417075,E,1,25,0.62,72.240,M,-1.770,M,,*51

$GBGSA,A,3,14,03,33,24,42,06,16,39,59,13,09,02,1.08,0.62,0.89,4*0C

$GBGSA,A,3,01,08,60,25,38,07,40,10,05,26,41,21,1.08,0.62,0.89,4*02

$GBGSA,A,3,34,,,,,,,,,,,,1.08,0.62,0.89,4*03

$GBGSV,7,1,26,14,82,222,42,3,63,190,41,33,61,311,43,24,59,358,42,1*45

$GBGSV,7,2,26,42,56,158,42,6,53,337,36,16,53,341,38,39,52,1,39,1*40

$GBGSV,7,3,26,59,51,128,42,13,50,221,39,9,49,314,36,2,48,240,37,1*74

$GBGSV,7,4,26,1,46,124,39,8,46,206,36,60,43,241,41,25,37,265,41,1*70

$GBGSV,7,5,26,38,35,191,38,7,33,174,35,4,32,112,34,40,28,160,38,1*7E

$GBGSV,7,6,26,10,25,186,33,5,24,258,34,26,21,43,36,41,12,323,36,1*75

$GBGSV,7,7,26,21,8,148,32,34,6,162,31,1*73

$GBGSV,3,1,11,33,61,311,43,24,59,358,43,42,56,158,42,39,52,1,40,5*77

$GBGSV,3,2,11,25,37,265,40,38,35,191,37,40,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120532.000,A,2301.2579425,N,11421.9417075,E,0.002,0.00,310725,,,A,S*30

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120532.000,3.714,0.227,0.215,0.297,2.484,2.516,3.260*73



2025-07-31 20:05:33:229 ==>> [W][05:19:01][COMM]>>>>>Input command = AT+ALLSTATE<<<<<
[D][05:19:01][COMM]Main Task receive event:14
[D][05:19:01][COMM]handlerPeriodRep, g_elecBatMissedCount = 0, time = 1629955141, allstateRepSeconds = 0
[D][05:19:01][COMM]index:0,power_mode:0xFF
[D][05:19:01][COMM]index:1,sound_mode:0xFF
[D][05:19:01][COMM]index:2,gsensor_mode:0xFF
[D][05:19:01][COMM]index:3,report_freq_mode:0xFF
[D][05:19:01][COMM]index:4,report_period:0xFF
[D][05:19:01][COMM]index:5,normal_reset_mode:0xFF
[D][05:19:01][COMM]index:6,normal_reset_period:0xFF
[D][05:19:01][COMM]index:7,spock_over_speed:0xFF
[D][05:19:01][COMM]index:8,spock_limit_speed:0xFF
[D][05:19:01][COMM]index:9,spock_report_period_unlock:0xFF
[D][05:19:01][COMM]index:10,spock_report_period_unlock_unit:0xFF
[D][05:19:01][COMM]index:11,ble_scan_mode:0xFF
[D][05:19:01][COMM]index:12,ble_adv_mode:0xFF
[D][05:19:01][COMM]index:13,spock_audio_volumn:0xFF
[D][05:19:01][COMM]index:14,spock_low_bat_alarm_soc:0xFF
[D][05:19:01][COMM]index:15,bat_auth_mode:0xFF
[D][05:19:01][COMM]index:16,imu_config_params:0xFF
[D][05:19:01][COMM]index:17,long_connect_params:0xFF
[D][05:19:01][COMM]index:18,detain_mark:0xFF
[D][05:19:01][COMM]index:19,lock_pos_report_count:0xFF
[D][05:19:01][COMM]index:20,lock_pos_report_interval:0xFF
[D][05:19:01][COMM]index:21,mc_mod

2025-07-31 20:05:33:334 ==>> e:0xFF
[D][05:19:01][COMM]index:22,S_mode:0xFF
[D][05:19:01][COMM]index:23,overweight:0xFF
[D][05:19:01][COMM]index:24,standstill_mode:0xFF
[D][05:19:01][COMM]index:25,night_mode:0xFF
[D][05:19:01][COMM]index:26,experiment1:0xFF
[D][05:19:01][COMM]index:27,experiment2:0xFF
[D][05:19:01][COMM]index:28,experiment3:0xFF
[D][05:19:01][COMM]index:29,experiment4:0xFF
[D][05:19:01][COMM]index:30,night_mode_start:0xFF
[D][05:19:01][COMM]index:31,night_mode_end:0xFF
[D][05:19:01][COMM]index:33,park_report_minutes:0xFF
[D][05:19:01][COMM]index:34,park_report_mode:0xFF
[D][05:19:01][COMM]index:35,mc_undervoltage_protection:0xFF
[D][05:19:01][COMM]index:38,charge_battery_para: FF
[D][05:19:01][COMM]index:39,multirider_mode:0xFF
[D][05:19:01][COMM]index:40,mc_launch_mode:0xFF
[D][05:19:01][COMM]index:41,head_light_enable_mode:0xFF
[D][05:19:01][COMM]index:42,set_time_ble_mode_begin_min:0xFF
[D][05:19:01][COMM]index:43,set_time_ble_mode_end_min:0xFF
[D][05:19:01][COMM]index:44,riding_duration_config:0xFF
[D][05:19:01][COMM]index:45,camera_park_angle_cfg:0xFF
[D][05:19:01][COMM]index:46,camera_park_type_cfg:0xFF
[D][05:19:01][COMM]index:47,bat_info_rep_cfg:0xFF
[D][05:19:01][COMM

2025-07-31 20:05:33:439 ==>> ]index:48,shlmt_sensor_en:0xFF
[D][05:19:01][COMM]index:49,mc_load_startup:0xFF
[D][05:19:01][COMM]index:50,mc_tcs_mode:0xFF
[D][05:19:01][COMM]index:51,traffic_audio_play:0xFF
[D][05:19:01][COMM]index:52,traffic_mode:0xFF
[D][05:19:01][COMM]index:53,traffic_info_collect_freq:0xFF
[D][05:19:01][COMM]index:54,traffic_security_model_cycle:0xFF
[D][05:19:01][COMM]index:55,wheel_alarm_play_switch:255
[D][05:19:01][COMM]index:57,traffic_sens_cycle:0xFF
[D][05:19:01][COMM]index:58,traffic_light_threshold:0xFF
[D][05:19:01][COMM]index:59,traffic_retrograde_threshold:0xFF
[D][05:19:01][COMM]index:60,traffic_road_threshold:0xFF
[D][05:19:01][COMM]index:61,traffic_sens_threshold:0xFF
[D][05:19:01][COMM]index:63,experiment5:0xFF
[D][05:19:01][COMM]index:64,camera_park_markline_cfg:0xFF
[D][05:19:01][COMM]index:65,camera_park_fenceline_cfg:0xFF
[D][05:19:01][COMM]index:66,camera_park_distance_cfg:0xFF
[D][05:19:01][COMM]index:67,camera_park_rc_cfg:0xFFFFFFFF
[D][05:19:01][COMM]index:68,camera_park_ps_cfg:0xFFFF
[D][05:19:01][COMM]index:70,camera_park_light_cfg:0xFF
[D][05:19:01][COMM]index:71,camera_park_self_check_cfg:0xFF
[D][05:19:01][COMM]index:72,experiment6:0xFF
[D][05:19:01]

2025-07-31 20:05:33:544 ==>> [COMM]index:73,experiment7:0xFF
[D][05:19:01][COMM]index:74,load_messurement_cfg:0xff
[D][05:19:01][COMM]index:75,zero_value_from_server:-1
[D][05:19:01][COMM]index:76,multirider_threshold:255
[D][05:19:01][COMM]index:77,experiment8:255
[D][05:19:01][COMM]index:78,temp_park_audio_play_duration:255
[D][05:19:01][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:19:01][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:19:01][COMM]index:82,loc_report_low_speed_thr:255
[D][05:19:01][COMM]index:83,loc_report_interval:255
[D][05:19:01][COMM]index:84,multirider_threshold_p2:255
[D][05:19:01][COMM]index:85,multirider_strategy:255
[D][05:19:01][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:19:01][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:19:01][COMM]index:90,weight_param:0xFF
[D][05:19:01][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:19:01][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:19:01][COMM]index:95,current_limit:0xFF
[D][05:19:01][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:19:01][COMM]index:100,location_mode:0xFF

[W][05:19:01][PROT]remove success[1629955141],send_path[2],type[0000],prio

2025-07-31 20:05:33:589 ==>> rity[0],index[0],used[0]
[W][05:19:01][PROT]add success [1629955141],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:19:01][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:01][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:01][COMM]read battery soc:255


2025-07-31 20:05:33:694 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      

2025-07-31 20:05:33:783 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    

2025-07-31 20:05:34:083 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     [1629955142]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND
[D][05:19:02][SAL ]sock send credit cnt[6]
[D][05:19:02][SAL ]sock send ind credit cnt[6]
[D][05:19:02][M2M ]m2m send data len[294]
[D][05:19:02][SAL ]Cellular task submsg id[10]
[D][05:19:02][SAL ]cellular SEND socket id[0] type[1], len[294], data

2025-07-31 20:05:34:188 ==>> [0x20052dd0] format[0]
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_SEND_ACK
[D][05:19:02][CAT1]gsm read msg sub id: 15
[D][05:19:02][CAT1]tx ret[17] >>> AT+QISEND=0,294

[D][05:19:02][CAT1]Send Data To Server[294][294] ... ->:
0093B982113311331133113311331B88B2DD909E27F9E5AF394AD34716C4A3E6F766645DF54687B2EB272763A4AA6A0B0134EB40B05898822FB6776FB90DA1B4EF9B39A0E1AD488A8EA8A4FD3327A20333499B13BFBDD24C41474D6AE3CD6D7BBF1E51D36EBE21EA8FD9B00F0CFF5532FD86ECCD917EB447333EFD6687F23536F44DCEE3595269F93BED97A42F838E0B191E9F
>>>>>RESEND ALLSTATE<<<<<
[W][05:19:02][PROT]remove success[1629955142],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:19:02][PROT]add success [1629955142],send_path[2],type[5004],priority[2],index[1],used[1]
[D][05:19:02][COMM]------>period, report file manifest
[D][05:19:02][COMM]Main Task receive event:14 finished processing
[D][05:19:02][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:02][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:02][CAT1]<<< 
SEND OK

[D][05:19:02][CAT1]exec over: func id: 15, ret: 11
[D][05:19:02][CAT1]sub id: 15, ret: 11

[D][05:19:02][SAL ]Cellular task submsg id[68]
[D][05:19:02][SAL ]handle subcmd ack 

2025-07-31 20:05:34:248 ==>> sub_id[f], socket[0], result[11]
[D][05:19:02][M2M ]M2M_GSM_SOCKET_SEND_ACK OK
[D][05:19:02][CAT1]gsm read msg sub id: 21
[D][05:19:02][CAT1]tx ret[15] >>> AT+QCELLINFO?

[D][05:19:02][M2M ]g_m2m_is_idle become true
[D][05:19:02][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
[D][05:19:02][PROT]M2M Send ok [1629955142]
[D][05:19:02][CAT1]<<< 
OK

[D][05:19:02][CAT1]cell info report total[0]
[D][05:19:02][CAT1]exec over: func id: 21, ret: 6


2025-07-31 20:05:34:353 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  

2025-07-31 20:05:34:428 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                0,28,160,34,26,21,43,35,5*4D

$GBGSV,3,3,11,41,12,323,32,21,8,148,32,34,6,162,31,5*46

$GBRMC,120534.000,A,2301.2579477,N,11421.9417108,E,0.002,0.00,310725,,,A,S*3A

$GBVTG,0.00,T,,M,0.002,N,0.003,K,A*2E

$GBGST,120534.000,3.622,0.214,0.203,0.283,2.434,2.465,3.193*72



2025-07-31 20:05:34:435 ==>> 【4G联网测试】通过,【SEND OK】符合目标值【SEND OK】要求!
2025-07-31 20:05:34:448 ==>> 检测【关闭GPS】
2025-07-31 20:05:34:465 ==>> 向【COM34】发送指令:【AT+GPSLOG=0】
2025-07-31 20:05:34:806 ==>> [W][05:19:03][COMM]>>>>>Input command = AT+GPSLOG=0<<<<<
[W][05:19:03][GNSS]stop locating
[D][05:19:03][GNSS]stop event:8
[D][05:19:03][GNSS]GPS stop. ret=0
[D][05:19:03][GNSS]all continue location stop
[D][05:19:03][HSDK][0] flush to flash addr:[0xE41A00] --- write len --- [256]
[D][05:19:03][CAT1]gsm read msg sub id: 24
[D][05:19:03][CAT1]tx ret[13] >>> AT+GPSPWR=0

[W][05:19:03][GNSS]stop locating
[D][05:19:03][GNSS]all sing location stop
[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[13] >>> AT+GPSRTK=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]tx ret[12] >>> AT+GPSDR=0

[D][05:19:03][CAT1]<<< 
OK

[D][05:19:03][CAT1]exec over: func id: 24, ret: 6
[D][05:19:03][CAT1]sub id: 24, ret: 6



2025-07-31 20:05:35:019 ==>> [D][05:19:03][COMM]read battery soc:255


2025-07-31 20:05:35:035 ==>> 【关闭GPS】通过,【stop locating】符合目标值【stop locating】要求!
2025-07-31 20:05:35:069 ==>> 检测【清空消息队列2】
2025-07-31 20:05:35:094 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:05:35:243 ==>> [D][05:19:04][GNSS]recv submsg id[1]
[D][05:19:04][GNSS]LOC_SUBCMD_GSM_OPS_IND[24] rst[6]
[D][05:19:04][GNSS]location stop evt done evt
[W][05:19:04][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:19:04][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:05:35:308 ==>> 【清空消息队列2】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:05:35:321 ==>> 检测【轮动检测】
2025-07-31 20:05:35:353 ==>> 向【COM34】发送指令:【3A A3 01 00 A3】
2025-07-31 20:05:35:440 ==>> 3A A3 01 00 A3 
OFF_OUT1
OVER 150


2025-07-31 20:05:35:500 ==>> [D][05:19:04][COMM]Wheel signal detected, lock state = 2, singal = 1


2025-07-31 20:05:35:823 ==>> 向【COM34】发送指令:【3A A3 01 01 A3】
2025-07-31 20:05:35:944 ==>> 3A A3 01 01 A3 
ON_OUT1
OVER 150


2025-07-31 20:05:36:097 ==>> 【轮动检测】通过,【Wheel signal detected】符合目标值【Wheel signal detected】要求!
2025-07-31 20:05:36:105 ==>> 检测【关闭小电池】
2025-07-31 20:05:36:117 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:05:36:234 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:05:36:373 ==>> 【关闭小电池】通过,【Battery OFF】符合目标值【Battery OFF】要求!
2025-07-31 20:05:36:385 ==>> 检测【进入休眠模式】
2025-07-31 20:05:36:408 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:05:36:581 ==>> [W][05:19:05][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<
[D][05:19:05][COMM]Main Task receive event:28
[D][05:19:05][COMM]main task tmp_sleep_event = 8
[D][05:19:05][COMM]prepare to sleep
[D][05:19:05][CAT1]gsm read msg sub id: 12
[D][05:19:05][CAT1]SEND RAW data >>> AT+CFUN=4




2025-07-31 20:05:37:026 ==>> [D][05:19:05][COMM]read battery soc:255


2025-07-31 20:05:37:356 ==>> [D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 12, ret: 6
[D][05:19:06][M2M ]tcpclient close[4]
[D][05:19:06][SAL ]Cellular task submsg id[12]
[D][05:19:06][SAL ]cellular CLOSE socket size[4], msg->data[0x20052db0], socket[0]
[D][05:19:06][CAT1]gsm read msg sub id: 9
[D][05:19:06][CAT1]tx ret[14] >>> AT+QICLOSE=0

[D][05:19:06][CAT1]<<< 
OK

[D][05:19:06][CAT1]exec over: func id: 9, ret: 6
[D][05:19:06][CAT1]sub id: 9, ret: 6

[D][05:19:06][SAL ]Cellular task submsg id[68]
[D][05:19:06][SAL ]handle subcmd ack sub_id[9], socket[0], result[6]
[D][05:19:06][SAL ]socket close ind. id[4]
[D][05:19:06][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:06][COMM]1x1 frm_can_tp_send ok
[D][05:19:06][CAT1]pdpdeact urc len[22]


2025-07-31 20:05:37:646 ==>> [E][05:19:06][COMM]1x1 rx timeout
[D][05:19:06][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:05:38:154 ==>> [E][05:19:06][COMM]1x1 rx timeout
[E][05:19:06][COMM]1x1 tp timeout
[E][05:19:06][COMM]1x1 error -3.
[W][05:19:06][COMM]CAN STOP!
[D][05:19:06][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:06][COMM]------------ready to Power off Acckey 1------------
[D][05:19:06][COMM]------------ready to Power off Acckey 2------------
[D][05:19:06][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:06][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 1295
[D][05:19:06][COMM]bat sleep fail, reason:-1
[D][05:19:06][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:06][COMM]accel parse set 0
[D][05:19:06][COMM]imu rest ok. 77911
[D][05:19:06][COMM]imu sleep 0
[W][05:19:06][COMM]now sleep


2025-07-31 20:05:38:203 ==>> 【进入休眠模式】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:05:38:210 ==>> 检测【检测33V休眠电流】
2025-07-31 20:05:38:222 ==>> 开始33V电流采样
2025-07-31 20:05:38:237 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:38:304 ==>> 向【COM36】发送指令:【AT+AUTOCA=1】
2025-07-31 20:05:39:305 ==>> 向【COM36】发送指令:【AT+AVG?】
2025-07-31 20:05:39:350 ==>> Current33V:????:15.84

2025-07-31 20:05:39:812 ==>> 向【COM36】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:39:819 ==>> 【检测33V休眠电流】通过,【15.84uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:05:39:826 ==>> 该项需要延时执行
2025-07-31 20:05:41:824 ==>> 此处延时了:【2000】毫秒
2025-07-31 20:05:41:838 ==>> 检测【TP33_VCC3V3_GNSS(ADV4)3】
2025-07-31 20:05:41:859 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:05:41:945 ==>> 1A A1 00 00 FC 
Get AD_V2 1668mV
Get AD_V3 1660mV
Get AD_V4 0mV
Get AD_V5 2757mV
Get AD_V6 1966mV
Get AD_V7 1093mV
OVER 150


2025-07-31 20:05:42:849 ==>> 【TP33_VCC3V3_GNSS(ADV4)3】通过,【0mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:05:42:856 ==>> 检测【打开小电池2】
2025-07-31 20:05:42:875 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:05:42:937 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:05:43:128 ==>> 【打开小电池2】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:05:43:135 ==>> 该项需要延时执行
2025-07-31 20:05:43:640 ==>> 此处延时了:【500】毫秒
2025-07-31 20:05:43:654 ==>> 检测【关闭33V供电(C128电源OUT1)2】
2025-07-31 20:05:43:676 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:05:43:733 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:05:43:919 ==>> 【关闭33V供电(C128电源OUT1)2】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:05:43:926 ==>> 该项需要延时执行
2025-07-31 20:05:44:068 ==>> [D][05:19:12][COMM]------------ready to Power on Acckey 1------------
[D][05:19:12][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:12][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:12][FCTY]get_ext_48v_vol retry i = 8,volt = 11
[D][05:19:12][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 11
[D][05:19:12][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:12][COMM]----- get Acckey 1 and value:1------------
[W][05:19:12][COMM]CAN START!
[D][05:19:12][CAT1]gsm read msg sub id: 12
[D][05:19:12][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:12][COMM]CAN message bat fault change: 0x01B987FE->0x00000000 83739
[D][05:19:12][COMM][Audio]exec status ready.
[D][05:19:12][CAT1]<<< 
OK

[D][05:19:12][CAT1]exec over: func id: 12, ret: 6
[D][05:19:12][COMM]imu wakeup ok. 83753
[D][05:19:12][COMM]imu

2025-07-31 20:05:44:114 ==>>  wakeup 1
[W][05:19:12][COMM]wake up system, wakeupEvt=0x80
[D][05:19:12][COMM]frm_can_weigth_power_set 1
[D][05:19:12][COMM]Clear Sleep Block Evt
[D][05:19:12][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:12][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:05:44:188 ==>> [D][05:19:13][COMM][MULTIRIDER] v:0 a:0 la:32767 s:0 th:100 z:0 r:5 n:0 step_th:40, step_th_p2:40,multimes:0,f_pitch_one:0,f_pitch_mul:0,g_p2_temp:40,dynamic:0,g_scre:0,g_imu_p2:0


2025-07-31 20:05:44:431 ==>> 此处延时了:【500】毫秒
2025-07-31 20:05:44:444 ==>> 检测【进入休眠模式2】
2025-07-31 20:05:44:469 ==>> 向【COM34】发送指令:【AT+SLEEPMODE=2】
2025-07-31 20:05:44:496 ==>> [E][05:19:13][COMM]1x1 rx timeout
[D][05:19:13][COMM]1x1 frm_can_tp_send ok
[D][05:19:13][COMM]msg 02A0 loss. last_tick:83724. cur_tick:84232. period:50
[D][05:19:13][COMM]msg 02A4 loss. last_tick:83724. cur_tick:84232. period:50
[D][05:19:13][COMM]msg 02A5 loss. last_tick:83724. cur_tick:84233. period:50
[D][05:19:13][COMM]msg 02A6 loss. last_tick:83724. cur_tick:84233. period:50
[D][05:19:13][COMM]msg 02A7 loss. last_tick:83724. cur_tick:84234. period:50
[D][05:19:13][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 84234
[D][05:19:13][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 84235


2025-07-31 20:05:44:627 ==>> [W][05:19:13][COMM]>>>>>Input command = AT+SLEEPMODE=2<<<<<


2025-07-31 20:05:44:807 ==>> [E][05:19:13][COMM]1x1 rx timeout
[E][05:19:13][COMM]1x1 tp timeout
[E][05:19:13][COMM]1x1 error -3.
[D][05:19:13][COMM]Main Task receive event:28 finished processing
[D][05:19:13][COMM]Main Task receive event:28
[D][05:19:13][COMM]prepare to sleep
[D][05:19:13][CAT1]gsm read msg sub id: 12
[D][05:19:13][CAT1]SEND RAW data >>> AT+CFUN=4


[D][05:19:13][CAT1]<<< 
OK

[D][05:19:13][CAT1]exec over: func id: 12, ret: 6
[D][05:19:13][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:13][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:05:45:095 ==>> [D][05:19:13][COMM]msg 0220 loss. last_tick:83724. cur_tick:84729. period:100
[D][05:19:13][COMM]msg 0221 loss. last_tick:83724. cur_tick:84729. period:100
[D][05:19:13][COMM]msg 0224 loss. last_tick:83724. cur_tick:84729. period:100
[D][05:19:13][COMM]msg 0260 loss. last_tick:83724. cur_tick:84730. period:100
[D][05:19:13][COMM]msg 0280 loss. last_tick:83724. cur_tick:84730. period:100
[D][05:19:13][COMM]msg 02C0 loss. last_tick:83724. cur_tick:84730. period:100
[D][05:19:13][COMM]msg 02C1 loss. last_tick:83724. cur_tick:84731. period:100
[D][05:19:13][COMM]msg 02C2 loss. last_tick:83724. cur_tick:84731. period:100
[D][05:19:13][COMM]msg 02E0 loss. last_tick:83724. cur_tick:84731. period:100
[D][05:19:13][COMM]msg 02E1 loss. last_tick:83724. cur_tick:84732. period:100
[D][05:19:13][COMM]msg 02E2 loss. last_tick:83724. cur_tick:84732. period:100
[D][05:19:13][COMM]msg 0300 loss. last_tick:83724. cur_tick:84732. period:100
[D][05:19:13][COMM]msg 0301 loss. last_tick:83724. cur_tick:84733. period:100
[D][05:19:13][COMM]bat msg 0240 loss. last_tick:83724. cur_tick:84734. period:100. j,i:1 54
[D][05:19:13][COMM]bat msg 0241 loss. last_tick:83724. cur_tick

2025-07-31 20:05:45:170 ==>> :84734. period:100. j,i:2 55
[D][05:19:13][COMM]bat msg 0242 loss. last_tick:83724. cur_tick:84734. period:100. j,i:3 56
[D][05:19:13][COMM]bat msg 0244 loss. last_tick:83724. cur_tick:84735. period:100. j,i:5 58
[D][05:19:13][COMM]bat msg 024E loss. last_tick:83724. cur_tick:84735. period:100. j,i:15 68
[D][05:19:13][COMM]bat msg 024F loss. last_tick:83724. cur_tick:84735. period:100. j,i:16 69
[D][05:19:13][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 84736
[D][05:19:13][COMM]CAN message bat fault change: 0x00000000->0x0001802E 84736
[D][05:19:13][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 84737


2025-07-31 20:05:45:200 ==>>                                                                               

2025-07-31 20:05:45:395 ==>> [D][05:19:14][COMM]msg 0222 loss. last_tick:83724. cur_tick:85231. period:150
[D][05:19:14][COMM]CAN message fault change: 0x0000E00C71E22213->0x0000E00C71E22217 85232


2025-07-31 20:05:45:470 ==>>                                                        ,vbuswake : 1
[D][05:19:14][COMM]frm_peripheral_device_poweroff type 16.... 
[D][05:19:14][COMM]------------ready to Power off Acckey 2------------


2025-07-31 20:05:45:682 ==>> [E][05:19:14][COMM]1x1 rx timeout
[E][05:19:14][COMM]1x1 tp timeout
[D][05:19:14][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[E][05:19:14][COMM]1x1 error -3.
[W][05:19:14][COMM]CAN STOP!
[D][05:19:14][COMM]frm_peripheral_device_poweroff type 3.... 
[D][05:19:14][COMM]------------ready to Power off Acckey 1------------
[D][05:19:14][COMM]------------ready to Power off Acckey 2------------
[D][05:19:14][COMM]logic is lock ,logic is 2 ,mc power power off
[D][05:19:14][COMM]frm_mc_bat_sleep EXT_BAT_STATE_POWERON 95
[D][05:19:14][COMM]bat sleep fail, reason:-1
[D][05:19:14][COMM]arm_hub_silent_mode silent mode 1[1][tx_tail 0]
[D][05:19:14][COMM]accel parse set 0
[D][05:19:14][COMM]imu rest ok. 85421
[D][05:19:14][COMM]imu sleep 0
[W][05:19:14][COMM]now sleep


2025-07-31 20:05:45:764 ==>> 【进入休眠模式2】通过,【now sleep】符合目标值【now sleep】要求!
2025-07-31 20:05:45:778 ==>> 检测【检测小电池休眠电流】
2025-07-31 20:05:45:792 ==>> 开始小电池电流采样
2025-07-31 20:05:45:805 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:45:878 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:05:46:888 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:05:46:964 ==>> CurrentBattery:ƽ��:71.28

2025-07-31 20:05:47:391 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:47:403 ==>> 【检测小电池休眠电流】通过,【71.28uA】符合目标值【0.01uA】至【200uA】要求!
2025-07-31 20:05:47:426 ==>> 检测【打开33V供电(C128电源OUT1)3】
2025-07-31 20:05:47:439 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:05:47:541 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:05:47:667 ==>> 【打开33V供电(C128电源OUT1)3】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:05:47:675 ==>> 该项需要延时执行
2025-07-31 20:05:47:782 ==>> [D][05:19:16][COMM]------------ready to Power on Acckey 1------------
[D][05:19:16][COMM]############# RESUME SIGNAL [0x80] #############
[D][05:19:16][COMM]arm_hub_silent_mode silent mode 0[0][tx_tail 0]
[D][05:19:16][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7
[D][05:19:16][COMM]frm_peripheral_device_poweron type 12.... 
[D][05:19:16][COMM]----- get Acckey 1 and value:1------------
[W][05:19:16][COMM]CAN START!
[D][05:19:16][CAT1]gsm read msg sub id: 12
[D][05:19:16][CAT1]SEND RAW data >>> AT+IMUCTRL=3,1


[D][05:19:16][COMM]CAN message bat fault change: 0x0001802E->0x00000000 87479
[D][05:19:16][COMM][Audio]exec status ready.
[D][05:19:16][CAT1]<<< 
OK

[D][05:19:16][CAT1]exec over: func id: 12, ret: 6
[D][05:19:16][COMM]imu wakeup ok. 87493
[D][05:19:16][COMM]imu wakeup 1
[W][05:19:16][COMM]wake up system, wakeupEvt=0x80
[D][05:19:16][COMM]frm_can_weigth_power_set 1
[D][05:19:16][COMM]Clear Sleep Block Evt
[D][05:19:16][COMM]1x1 tx_id:3,8, tx_len:2
[D][05:19:16][COMM]1x1 frm_can_tp_send ok
[D][05:19:16][COMM]read battery soc:0


2025-07-31 20:05:48:043 ==>> [E][05:19:16][COMM]1x1 rx timeout
[D][05:19:16][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:05:48:148 ==>> [D][05:19:16][COMM]msg 02A0 loss. last_tick:87462. cur_tick:87973. period:50
[D][05:19:16][COMM]msg 02A4 loss. last_tick:87462. cur_tick:87974. period:50
[D][05:19:16][

2025-07-31 20:05:48:178 ==>> 此处延时了:【500】毫秒
2025-07-31 20:05:48:189 ==>> 检测【检测唤醒】
2025-07-31 20:05:48:217 ==>> 向【COM34】发送指令:【AT+INFO】
2025-07-31 20:05:48:230 ==>> COMM]msg 02A5 loss. last_tick:87462. cur_tick:87975. period:50
[D][05:19:16][COMM]msg 02A6 loss. last_tick:87462. cur_tick:87975. period:50
[D][05:19:16][COMM]msg 02A7 loss. last_tick:87462. cur_tick:87975. period:50
[D][05:19:16][COMM]CAN message fault change: 0x0000000000000000->0x0000E00000220000 87976
[D][05:19:16][COMM]CAN fault change: 0x0000000000000000->0x0000000000010800 87976


2025-07-31 20:05:48:896 ==>> [W][05:19:17][COMM]>>>>>Input command = AT+INFO<<<<<
[D][05:19:17][FCTY]==========System Info E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:17][FCTY]==========Modules-nRF5340 ==========
[D][05:19:17][FCTY]BootVersion = SA_BOOT_V109
[D][05:19:17][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:19:17][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:19:17][FCTY]DeviceID    = 460130071539169
[D][05:19:17][FCTY]HardwareID  = 867222087738524
[D][05:19:17][FCTY]MoBikeID    = 9999999999
[D][05:19:17][FCTY]LockID      = FFFFFFFFFF
[D][05:19:17][FCTY]BLEFWVersion= 105
[D][05:19:17][FCTY]BLEMacAddr   = E4AE7AC8032E
[D][05:19:17][FCTY]Bat         = 3844 mv
[D][05:19:17][FCTY]Current     = 0 ma
[D][05:19:17][FCTY]VBUS        = 2600 mv
[D][05:19:17][FCTY]TEMP= 0,BATID= 323,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:19:17][FCTY]Ext battery vol = 32, adc = 1291
[D][05:19:17][FCTY]Acckey1 vol = 5537 mv, Acckey2 vol = 0 mv
[D][05:19:17][FCTY]Bike Type flag is invalied
[D][05:19:17][FCTY]CAT1_KERNEL_BOOT = 0.0.0
[D][05:19:17][FCTY]CAT1_KERNEL_KERNEL = 5.0.0
[D][05:19:17][FCTY]CAT1_KERNEL_APP = 21.2.1
[D][05:19:17][FCTY]CAT1_KERNEL_RTK = 1.2.4
[D][05:19:17][FCTY]CAT1_GNSS

2025-07-31 20:05:49:001 ==>> _PLATFORM = C4
[D][05:19:17][FCTY]CAT1_GNSS_VERSION = V3465b5b1
[D][05:19:17][FCTY]Bat1         = 3820 mv
[D][05:19:17][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:19:17][FCTY]==========Modules-nRF5340 ==========
[E][05:19:17][COMM]1x1 rx timeout
[E][05:19:17][COMM]1x1 tp timeout
[E][05:19:17][COMM]1x1 error -3.
[D][05:19:17][COMM]Main Task receive event:28 finished processing
[D][05:19:17][COMM]Main Task receive event:65
[D][05:19:17][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSOUT]!!!
[D][05:19:17][COMM]Main Task receive event:65 finished processing
[D][05:19:17][COMM]Main Task receive event:60
[D][05:19:17][COMM]smart_helmet_vol=255,255
[D][05:19:17][COMM]report elecbike
[W][05:19:17][PROT]remove success[1629955157],send_path[3],type[0000],priority[0],index[0],used[0]
[W][05:19:17][PROT]add success [1629955157],send_path[3],type[5D03],priority[3],index[0],used[1]
[D][05:19:17][COMM]Main Task receive event:60 finished processing
[D][05:19:17][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:17][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:17][PROT]min_index:0, type:0x5D03, priority:3
[D][05:19:17][PROT]index:0
[D][05:19:17][P

2025-07-31 20:05:49:018 ==>> 【检测唤醒】通过,【Bike Type flag is invalied】符合目标值【Bike Type flag is invalied】要求!
2025-07-31 20:05:49:037 ==>> 检测【关机】
2025-07-31 20:05:49:052 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:05:49:106 ==>> ROT]is_send:1
[D][05:19:17][PROT]sequence_num:9
[D][05:19:17][PROT]retry_timeout:0
[D][05:19:17][PROT]retry_times:3
[D][05:19:17][PROT]send_path:0x3
[D][05:19:17][PROT]msg_type:0x5d03
[D][05:19:17][PROT]===========================================================
[W][05:19:17][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955157]
[D][05:19:17][PROT]===========================================================
[D][05:19:17][PROT]Sending traceid[999999999990000A]
[D][05:19:17][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:17][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:17][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:17][M2M ]M2M_Task:m_m2m_thread_setting_queue:7
[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:19:17][SAL ]open socket ind id[4], rst[0]
[D][05:19:17][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:19:17][SAL ]Cellular task submsg id[8]
[D][05:19:17][SAL ]cellular OPEN socket size[144], msg->data[0x20052db0], socket[0]
[D][05:19:17][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:19:17][CAT1]gsm read msg sub id: 8
[

2025-07-31 20:05:49:211 ==>> D][05:19:17][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:19:17][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:19:17][CAT1]<<< 
+CGATT: 0

OK

[D][05:19:17][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:19:17][CAT1]TEST for CME ERR: +CME ERROR: 100
, 17, 2
[D][05:19:17][CAT1]<<< 
+CME ERROR: 100

[D][05:19:17][COMM]msg 0220 loss. last_tick:87462. cur_tick:88470. period:100
[D][05:19:17][COMM]msg 0221 loss. last_tick:87462. cur_tick:88470. period:100
[D][05:19:17][COMM]msg 0224 loss. last_tick:87462. cur_tick:88470. period:100
[D][05:19:17][COMM]msg 0260 loss. last_tick:87462. cur_tick:88471. period:100
[D][05:19:17][COMM]msg 0280 loss. last_tick:87462. cur_tick:88471. period:100
[D][05:19:17][COMM]msg 02C0 loss. last_tick:87462. cur_tick:88471. period:100
[D][05:19:17][COMM]msg 02C1 loss. last_tick:87462. cur_tick:88472. period:100
[D][05:19:17][COMM]msg 02C2 loss. last_tick:87462. cur_tick:88472. period:100
[D][05:19:17][COMM]msg 02E0 loss. last_tick:87462. cur_tick:88472. period:100
[D][05:19:17][COMM]msg 02E1 loss. last_tick:87462. cur_tick:88473. period:100
[D][05:19:17][COMM]msg 02E2 loss. last_tick:87462. cur_tick:88473. period:100
[D][05:19:17][COMM]msg 0300 loss.

2025-07-31 20:05:49:316 ==>>  last_tick:87462. cur_tick:88473. period:100
[D][05:19:17][COMM]msg 0301 loss. last_tick:87462. cur_tick:88474. period:100
[D][05:19:17][COMM]bat msg 0240 loss. last_tick:87462. cur_tick:88474. period:100. j,i:1 54
[D][05:19:17][COMM]bat msg 0241 loss. last_tick:87462. cur_tick:88475. period:100. j,i:2 55
[D][05:19:17][COMM]bat msg 0242 loss. last_tick:87462. cur_tick:88475. period:100. j,i:3 56
[D][05:19:17][COMM]bat msg 0244 loss. last_tick:87462. cur_tick:88475. period:100. j,i:5 58
[D][05:19:17][COMM]bat msg 024E loss. last_tick:87462. cur_tick:88476. period:100. j,i:15 68
[D][05:19:17][COMM]bat msg 024F loss. last_tick:87462. cur_tick:88476. period:100. j,i:16 69
[D][05:19:17][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 88476
[D][05:19:17][COMM]CAN message bat fault change: 0x00000000->0x0001802E 88477
[D][05:19:17][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 88477


2025-07-31 20:05:50:019 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        

2025-07-31 20:05:50:049 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:05:50:124 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               ][COMM]read file success
[W][05:19:18][COMM][Audio].l:[936].close hexlog save
[D][05:19:18][COMM]accel parse set 1
[D][05:19:18][COMM][Audio]mon:9,05:19:18
[D][05:19:18][COMM]f:[frm_audio_get_volume].l:[1799].volume:[20]
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[945].file 'B50' size:10800
[D][05:19:18][COMM]f:[ec800m_audio_start].l:[682].audio cmd send:AT+AUDIOPLAY=0,7,0,"mp3",0,10800,2048,2954

[D][05:19:18][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:27
0D0A2B5041434B41474553495A453A323034380D0A0D0A4F4B0D0A
[D][05:19:18][COMM]f:

2025-07-31 20:05:50:230 ==>> [ec800m_audio_start].l:[691].recv ok
[D][05:19:18][COMM]f:[ec800m_audio_start].l:[704].audio cmd send:AT+AUDIOSEND=1

[D][05:19:18][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:18][COMM]Main Task receive event:65
[D][05:19:18][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:19:18][COMM]Main Task receive event:65 finished processing
[D][05:19:18][COMM]Main Task receive event:66
[D][05:19:18][COMM]Try to Auto Lock Bat
[D][05:19:18][COMM]Main Task receive event:66 finished processing
[D][05:19:18][COMM]Main Task receive event:60
[D][05:19:18][COMM]smart_helmet_vol=255,255
[D][05:19:18][COMM]f:[ec800m_audio_start].l:[713].recv ok
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[956].start ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_send_hexdata_start].l:[747].audio cmd send:AT+AUDIODATAHEX=0,10800,0

[D][05:19:18][COMM]BAT CAN get state1 Fail 204
[D][05:19:18][COMM]BAT CAN get soc Fail, 204
[D][05:19:18][COMM]BAT CAN get state2 fail 204
[D][05:19:18][COMM]get soh error
[E][05:19:18][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:19:18][COMM]report elecbike
[W][05:19:18][PROT]remove success[1629955158],s

2025-07-31 20:05:50:334 ==>> end_path[3],type[0000],priority[0],index[1],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[3],type[5D03],priority[4],index[1],used[1]
[D][05:19:18][COMM]Main Task receive event:60 finished processing
[D][05:19:18][COMM]Main Task receive event:61
[D][05:19:18][COMM][D301]:type:3, trace id:280
[D][05:19:18][COMM]id[], hw[000
[D][05:19:18][COMM]get mcMaincircuitVolt error
[D][05:19:18][COMM]get mcSubcircuitVolt error
[D][05:19:18][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:19:18][COMM]BAT CAN get state1 Fail 204
[D][05:19:18][COMM]BAT CAN get soc Fail, 204
[D][05:19:18][COMM]BatVolt[0], Current[0], DisplaySoc[0], ProtectTag[0], ErrorTag[0],                     CellTempMax[0], CellTempMin[0], DischargeMosTemp[0], AfeTemp[0], WorkMode[254]
[D][05:19:18][COMM]BAT CAN get state2 fail 204
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][PROT]min_index:1, type:0x5D03, priority:4
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][PROT]index:1
[D][05:19:18][PROT]is_send:1
[D][05:19:18][PROT]sequence_num:10
[D][05:19:18][PROT]retry_timeout:0
[D][05:19:18][PROT]retry_times:3
[D][05:19:18][PROT]send_path:0x3
[D][05

2025-07-31 20:05:50:439 ==>> :19:18][PROT]msg_type:0x5d03
[D][05:19:18][PROT]===========================================================
[W][05:19:18][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955158]
[D][05:19:18][PROT]===========================================================
[D][05:19:18][PROT]Sending traceid[999999999990000B]
[D][05:19:18][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:19:18][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:19:18][PROT]ble is not inited or not connected or cccd not enabled
[D][05:19:18][COMM]Receive Bat Lock cmd 0
[D][05:19:18][COMM]VBUS is 1
[D][05:19:18][COMM]get bat work mode err
[W][05:19:18][PROT]remove success[1629955158],send_path[2],type[0000],priority[0],index[2],used[0]
[W][05:19:18][PROT]add success [1629955158],send_path[2],type[D302],priority[0],index[2],used[1]
[D][05:19:18][COMM]Main Task receive event:61 finished processing
[D][05:19:18][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:19:18][M2M ]m2m_task: gpc:[0],gpo:[1]
[D][05:19:18][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:4
0D0A3E20
[D][05:19:18][COMM]f:[ec800m_audio_send_hexdata_start].l:[756].recv

2025-07-31 20:05:50:544 ==>>  >
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[968].pkg_num:6
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:1, len:2048
[W][05:19:18][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:18][COMM]arm_hub_enable: hub power: 0
[D][05:19:18][HSDK]hexlog index save 0 3072 241 @ 0 : 0
[D][05:19:18][HSDK]write save hexlog index [0]
[D][05:19:18][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:18][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:2, len:2048
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:3, len:2048
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:4, len:2048
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:5, len:2048
[D][05:19:18][COMM]read battery soc:255
[D][05:19:1

2025-07-31 20:05:50:619 ==>> 8][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[975].hexsend, index:6, len:560
[D][05:19:18][COMM]f:[ec800m_audio_play_process].l:[991]. send ret: 0
[D][05:19:18][COMM]f:[ec800m_audio_response_process].audio_ack.data_size:6
0D0A4F4B0D0A
[D][05:19:18][COMM]f:[ec800m_audio_send_hexdata_end].l:[807].recv ok
[D][05:19:18][COMM]f:[ec800m_audio_send_hexdata_end].l:[818].set receive timeout:9000ms
                              

2025-07-31 20:05:50:724 ==>> [W][05:19:19][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:19][COMM]arm_hub_enable: hub power: 0
[D][05:19:19][HSDK]hexlog index save 0 3072 241 @ 0 : 0
[D][05:19:19][HSDK]write save hexlog index [0]
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:19][FCTY]F:[syncParaFromRamToFlash].L:[969] rea

2025-07-31 20:05:50:754 ==>> dy to write para flash


2025-07-31 20:05:51:075 ==>> 向【COM34】发送指令:【AT+POWEROFF=6789】
2025-07-31 20:05:51:300 ==>> [D][05:19:20][COMM]exit wheel stolen mode.
[D][05:19:20][COMM]Main Task receive event:68
[D][05:19:20][COMM]handlerWheelStolen evt type = 2.
[E][05:19:20][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:19:20][GNSS]stop locating
[D][05:19:20][GNSS]all continue location stop
[D][05:19:20][COMM]Main Task receive event:68 finished processing
[W][05:19:20][COMM]Power Off
[W][05:19:20][COMM]>>>>>Input command = AT+POWEROFF=6789<<<<<
[D][05:19:20][COMM]arm_hub_enable: hub power: 0
[D][05:19:20][HSDK]hexlog index save 0 3072 241 @ 0 : 0
[D][05:19:20][HSDK]write save hexlog index [0]
[D][05:19:20][FCTY]F:[syncParaFromRamToFlash].L:[962] ready to read para flash
[D][05:19:20][FCTY]F:[syncParaFromRamToFlash].L:[969] ready to write para flash


2025-07-31 20:05:51:350 ==>> 【关机】通过,【Power Off】符合目标值【Power Off】要求!
2025-07-31 20:05:51:358 ==>> 检测【关闭33V供电(C128电源OUT1)3】
2025-07-31 20:05:51:371 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:05:51:435 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:05:51:616 ==>> [D][05:19:20][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:19:20][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:05:51:625 ==>> 【关闭33V供电(C128电源OUT1)3】通过,【CLOSE_POWER_OUT1】符合目标值【CLOSE_POWER_OUT1】要求!
2025-07-31 20:05:51:633 ==>> 检测【检测小电池关机电流】
2025-07-31 20:05:51:666 ==>> 开始小电池电流采样
2025-07-31 20:05:51:679 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:51:736 ==>> 向【COM35】发送指令:【AT+AUTOCA=1】
2025-07-31 20:05:52:745 ==>> 向【COM35】发送指令:【AT+AVG?】
2025-07-31 20:05:52:822 ==>> CurrentBattery:ƽ��:70.26

2025-07-31 20:05:53:251 ==>> 向【COM35】发送指令:【AT+AUTOCA=0】
2025-07-31 20:05:53:259 ==>> 【检测小电池关机电流】通过,【70.26uA】符合目标值【0.01uA】至【100uA】要求!
2025-07-31 20:05:53:541 ==>> MES过站成功
2025-07-31 20:05:53:555 ==>> #################### 【测试结束】 ####################
2025-07-31 20:05:53:578 ==>> 关闭5V供电
2025-07-31 20:05:53:593 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:05:53:632 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:05:54:576 ==>> 关闭5V供电成功
2025-07-31 20:05:54:589 ==>> 关闭33V供电
2025-07-31 20:05:54:619 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:05:54:638 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:05:55:587 ==>> 关闭33V供电成功
2025-07-31 20:05:55:600 ==>> 关闭3.7V供电
2025-07-31 20:05:55:627 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:05:55:653 ==>> 6A A6 02 A6 6A 


2025-07-31 20:05:55:738 ==>> Battery OFF
OVER 150


2025-07-31 20:05:56:530 ==>>  

