2025-07-31 20:15:41:140 ==>> MES查站成功:
查站序号:P5100010053132B5验证通过
2025-07-31 20:15:41:144 ==>> 扫码结果:P5100010053132B5
2025-07-31 20:15:41:145 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:15:41:146 ==>> 测试参数版本:2024.10.11
2025-07-31 20:15:41:148 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:15:41:150 ==>> 检测【打开透传】
2025-07-31 20:15:41:151 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:15:41:245 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:15:41:676 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:15:41:680 ==>> 检测【检测接地电压】
2025-07-31 20:15:41:682 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:15:41:748 ==>> 1A A1 40 00 00 
Get AD_V22 235mV
OVER 150


2025-07-31 20:15:41:966 ==>> 【检测接地电压】通过,【235mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:15:41:969 ==>> 检测【打开小电池】
2025-07-31 20:15:41:972 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:15:42:036 ==>> 6A A6 01 A6 6A 


2025-07-31 20:15:42:141 ==>> Battery ON
OVER 150


2025-07-31 20:15:42:270 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:15:42:272 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:15:42:274 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:15:42:339 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:15:42:579 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:15:42:582 ==>> 检测【等待设备启动】
2025-07-31 20:15:42:585 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:42:984 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:43:164 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 20:15:43:598 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:43:752 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:43:918 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Time

2025-07-31 20:15:44:453 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:44:633 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:44:635 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer 

2025-07-31 20:15:45:154 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:45:348 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer st 

2025-07-31 20:15:45:656 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:45:857 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:46:040 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer stat

2025-07-31 20:15:46:563 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:46:701 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:46:745 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer sta 

2025-07-31 20:15:47:261 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:47:442 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer st

2025-07-31 20:15:47:730 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:47:960 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:48:141 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer sta 

2025-07-31 20:15:48:642 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:48:763 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:48:823 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer sta

2025-07-31 20:15:49:336 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:49:531 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:15:49:789 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:50:203 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]Battery Low, GPS Will Not Open


2025-07-31 20:15:50:577 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:15:50:824 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:15:51:056 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:15:51:112 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:15:51:114 ==>> 检测【产品通信】
2025-07-31 20:15:51:116 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:15:51:296 ==>>  

2025-07-31 20:15:51:804 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:15:51:985 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:15:52:151 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:15:53:193 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:15:53:268 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255
[W][05:17:49][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:49][COMM]Password OK
[D][05:17:49][GNSS]loc task start.
[D][05:17:49][COMM]file system init success
[D][05:17:49][FCTY]==========NORMAL MODE E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]appBledevGetCfg:scan_mode:255,interval 65535,windows 65535,scan_time 255
[D][05:17:49][COMM]g_appBledevGetCfg:scan_mode:1,interval 16,windows 10,scan_time 3
[D][05:17:49][COMM]appBledevGetCfg:dev:0,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]frm CAN read mc pwr mode invalid,val:254
[D][05:17:49][COMM]appBledevGetCfg:dev:1,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:2,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:3,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][C

2025-07-31 20:15:53:373 ==>> OMM]appBledevGetCfg:dev:4,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]appBledevGetCfg:dev:5,type 255,businessid 255,rssi 0xFF,0xFF,start 255,len 255,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][COMM]ble_mix_para interval 0xffff, window 0xffff, timeout 0x3e418, type 0xff
[D][05:17:49][COMM]frm CAN read mc work mode invalid,val:254
[D][05:17:49][COMM][MC]set min voltage(300) failed,getMode err:-4
[D][05:17:49][COMM]APP_START frmMC_getMinVoltage 65534 ok
[D][05:17:49][FCTY]F:[appParkGetCfg].L:[16303] ready to read para flash
[D][05:17:49][COMM]appParkGetCfg:scan mode 0xFF,type 0xFF,rssi 0xFF,0xFF,0xFF,start 0xFF,len 0xFF,info:
FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF
[D][05:17:49][FCTY]F:[appParkApplyCfg].L:[16325] ready to read para flash
[D][05:17:49][COMM]netcore_ver 105
[D][05:17:49][COMM]netboot_ver 66
[D][05:17:49][BLE ]BLE_INF [app_ble_init:925] app_ble init start

[D][05:17:49][BLE ]BLE_WRN [frm_ble_adv_set_event:250] frm_ble is not inited

[D][05:17:49][FCTY]BoardINFO:[E4_X50, EC800M, SE510, C4#TAU804S]
[D][05:17:49][FCTY]BOARD TYPE:[E4_X50]
[D][05:17:49][FCTY]==========System Info E4_X50_917

2025-07-31 20:15:53:468 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:15:53:471 ==>> 检测【初始化完成检测】
2025-07-31 20:15:53:474 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:15:53:479 ==>> V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][FCTY]BootVersion = SA_BOOT_V109
[D][05:17:49][FCTY]APPVersion  = MSL_SA_A_E4_X50_917
[D][05:17:49][FCTY]BLEVersion = BLE_BE_105_102_100
[D][05:17:49][FCTY]DeviceID    = 
[D][05:17:49][FCTY]HardwareID  = 
[D][05:17:49][FCTY]MoBikeID    = 9999999999
[D][05:17:49][FCTY]LockID      = FFFFFFFFFF
[D][05:17:49][FCTY]BLEFWVersion= 105
[D][05:17:49][FCTY]BLEMacAddr   = FB664455B137
[D][05:17:49][FCTY]Bat         = 3764 mv
[D][05:17:49][FCTY]Current     = 0 ma
[D][05:17:49][FCTY]VBUS        = 2600 mv
[D][05:17:49][FCTY]TEMP= 0,BATID= 205,BAT_TYPE = 0, BOARD_ID = 0xD1
[D][05:17:49][FCTY]Ext battery vol = 0, adc = 0
[D][05:17:49][FCTY]Acckey1 vol = 5512 mv, Acckey2 vol = 0 mv
[D][05:17:49][FCTY]Bike Type flag is invalied
[D][05:17:49][FCTY]CAT1_KERNEL_BOOT =
[D][05:17:49][FCTY]CAT1_KERNEL_KERNEL =
[D][05:17:49][FCTY]CAT1_KERNEL_APP =
[D][05:17:49][FCTY]CAT1_KERNEL_GNSS =
[D][05:17:49][FCTY]CAT1_KERNEL_RTK =
[D][05:17:49][FCTY]CAT1_GNSS_PLATFORM =
[D][05:17:49][FCTY]CAT1_GNSS_VERSION =
[D][05:17:49][FCTY]F:[app_ble_init].L:[950] ready to read para flash


2025-07-31 20:15:53:584 ==>> 
[D][05:17:49][FCTY]F:[app_ble_init].L:[973] ready to write para flash
[D][05:17:49][BLE ]BLE_INF [app_ble_init:1008] app_ble init end

[D][05:17:49][FCTY]Bat1         = 3743 mv
[D][05:17:49][FCTY]==================== E4_X50_917V1.1_b1e44e40 ==========
[D][05:17:49][FCTY]==========Modules-nRF5340 ==========
[D][05:17:49][COMM]set batlock type : 1 (0-normal,1-with sensor check)
[D][05:17:49][COMM]Open GPS Module...
[D][05:17:49][GNSS]start event:1
[W][05:17:49][GNSS]start sing locating
[D][05:17:49][GNSS]gps single mode only, do wifi scan.
[D][05:17:49][COMM]m2m_set_address over
[D][05:17:49][COMM]reset default value of volumn. HighSpeed:25
[D][05:17:49][COMM]reset default value of volumn. HighTempAlarm:99
[D][05:17:49][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:49][COMM]1x1 tx_id:3,3, tx_len:2
[D][05:17:49][COMM]1x1 frm_can_tp_send ok
[D][05:17:49][COMM][LedDisplay]recv Cmd:2,3,3,op:0xc63
[D][05:17:49][COMM][CHG]ext_48v_vol:0, disable charge_en, save bat inplace:0, charge_en pin:1
[D][05:17:49][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:49][M2M ]m2m_task: gpc:[5],gpo:[0]
[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON
[D][05:17:49][CAT1]gsm read msg sub id: 

2025-07-31 20:15:53:689 ==>> 1
[D][05:17:49][CAT1]tx ret[4] >>> AT

[D][05:17:49][M2M ]m2m switch to: M2M_GSM_PWR_ON_ACK
[D][05:17:50][COMM]msg 0220 loss. last_tick:0. cur_tick:1020. period:100
[D][05:17:50][COMM]msg 0221 loss. last_tick:0. cur_tick:1020. period:100
[D][05:17:50][COMM]msg 0224 loss. last_tick:0. cur_tick:1021. period:100
[D][05:17:50][COMM]msg 0260 loss. last_tick:0. cur_tick:1021. period:100
[D][05:17:50][COMM]msg 0280 loss. last_tick:0. cur_tick:1021. period:100
[D][05:17:50][COMM]msg 02C0 loss. last_tick:0. cur_tick:1022. period:100
[D][05:17:50][COMM]msg 02C1 loss. last_tick:0. cur_tick:1022. period:100
[D][05:17:50][COMM]msg 02C2 loss. last_tick:0. cur_tick:1023. period:100
[D][05:17:50][COMM]msg 02E0 loss. last_tick:0. cur_tick:1023. period:100
[D][05:17:50][COMM]msg 02E1 loss. last_tick:0. cur_tick:1023. period:100
[D][05:17:50][COMM]msg 02E2 loss. last_tick:0. cur_tick:1024. period:100
[D][05:17:50][COMM]msg 0300 loss. last_tick:0. cur_tick:1024. period:100
[D][05:17:50][COMM]msg 0301 loss. last_tick:0. cur_tick:1024. period:100
[D][05:17:50][COMM]bat msg 0240 loss. last_tick:0. cur_tick:1025. period:100. j,i:1 54
[D][05:17:50][COMM]bat msg 0241 loss. last_tick:0. cur_tick:

2025-07-31 20:15:53:779 ==>> 1025. period:100. j,i:2 55
[D][05:17:50][COMM]bat msg 0242 loss. last_tick:0. cur_tick:1025. period:100. j,i:3 56
[D][05:17:50][COMM]bat msg 0244 loss. last_tick:0. cur_tick:1026. period:100. j,i:5 58
[D][05:17:50][COMM]bat msg 024E loss. last_tick:0. cur_tick:1026. period:100. j,i:15 68
[D][05:17:50][COMM]bat msg 024F loss. last_tick:0. cur_tick:1026. period:100. j,i:16 69
[D][05:17:50][COMM]CAN message fault change: 0x0000E00000220000->0x0000E00C71E22213 1027
[D][05:17:50][COMM]CAN message bat fault change: 0x00000000->0x0001802E 1027
[D][05:17:50][COMM]CAN fault change: 0x0000000000010800->0x0000000300010E01 1028
[E][05:17:50][COMM]1x1 rx timeout
[D][05:17:50][COMM]1x1 frm_can_tp_send ok


2025-07-31 20:15:54:099 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       

2025-07-31 20:15:54:204 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:15:54:309 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         

2025-07-31 20:15:54:414 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               _check_cfg:0xFF
[D][05:17:50][COMM]index:72,experiment6:0xFF
[D][05:17:50][COMM]index:73,experiment7:0xFF
[D][05:17:50][COMM]index:74,load_messurement_cfg:0xff
[D][05:17:50][COMM]index:75,zero_value_from_server:-1
[D][05:17:50][COMM]index:76,multirider_threshold:255
[D][05:17:50][COMM]index:77,experiment8:255
[D][05:17:50][COMM]index:78,temp_park_audio_play_duration:255
[D][05:17:50][COMM]index:79,temp_park_tail_light_twinkle_duration:255
[D][05:17:50][COMM]index:80,temp_park_reminder_timeout_duration:255
[D][05:17:50][COMM]index:82,loc_report_low_speed_thr:255
[D][05:17:50][COMM]index:83,loc_report_interval:255
[D][05:17:50][COMM]index:84,multirider_threshold_p2

2025-07-31 20:15:54:489 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:15:54:519 ==>> :255
[D][05:17:50][COMM]index:85,multirider_strategy:255
[D][05:17:50][COMM]index:81,camera_park_similar_thr_cfg:0xFF
[D][05:17:50][COMM]index:86,camera_park_self_check_period_cfg:0xFF
[D][05:17:50][COMM]index:90,weight_param:0xFF
[D][05:17:50][COMM]index:93,lock_anti_theft_mode:0xFF
[D][05:17:50][COMM]index:94,high_temp_alarm_count:0xFF
[D][05:17:50][COMM]index:95,current_limit:0xFF
[D][05:17:50][COMM]index:97,panel display threshold cfg B:0xFF C:0xFF D:0xFF 
[D][05:17:50][COMM]index:100,location_mode:0xFF

[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]
[D][05:17:50][COMM]Main Task receive event:122
[D][05:17:50][COMM]Main Task receive event:122 finished processing
[D][05:17:50][COMM]1615 imu init OK
[D][05:17:50][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:50][HSDK][0] flush to flash addr:[0xE41100] --- write len --- [256]
[W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK
[W][05:17:50][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:50][COMM]frm_peripheral_device_pow

2025-07-31 20:15:54:564 ==>> eron type 0.... 
[D][05:17:50][COMM]----- get Acckey 1 and value:1------------
[D][05:17:50][COMM]----- get Acckey 2 and value:1------------
[D][05:17:50][COMM]SE50 init success!


2025-07-31 20:15:54:669 ==>>                                                                                                                            

2025-07-31 20:15:54:759 ==>>                                    [W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing


2025-07-31 20:15:54:762 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:15:54:775 ==>> 检测【关闭大灯控制1】
2025-07-31 20:15:54:777 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:15:54:909 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:15:55:044 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:15:55:047 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:15:55:049 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:15:55:246 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:52][COMM][oneline_display]: command mode, ON!
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:15:55:324 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:15:55:327 ==>> 检测【关闭仪表供电】
2025-07-31 20:15:55:328 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:15:55:537 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:15:55:627 ==>> [D][05:17:52][COMM]3637 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:15:55:629 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:15:55:631 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:15:55:632 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:15:55:825 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:15:55:933 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:15:55:936 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:15:55:939 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:15:56:132 ==>> [D][05:17:53][HSDK][0] flush to flash addr:[0xE41200] --- write len --- [256]
[W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:15:56:274 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:15:56:279 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:15:56:282 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:15:56:405 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:15:56:571 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:15:56:574 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:15:56:589 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:15:56:648 ==>> [D][05:17:53][COMM]4647 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:15:56:753 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 17
[D][05:17:53][COMM]read battery soc:255


2025-07-31 20:15:56:847 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:15:56:851 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:15:56:861 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:15:56:935 ==>> 5A A5 03 5A A5 


2025-07-31 20:15:57:040 ==>> OPEN_POWER_OUT2
OVER 150


2025-07-31 20:15:57:118 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:15:57:124 ==>> 该项需要延时执行
2025-07-31 20:15:57:147 ==>> [D][05:17:53][COMM]msg 0601 loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]msg 02AC loss. last_tick:0. cur_tick:5003. period:500
[D][05:17:53][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5004. period:500. j,i:4 57
[D][05:17:53][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5004. period:500. j,i:6 59
[D][05:17:53][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5005. period:500. j,i:7 60
[D][05:17:53][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5005. period:500. j,i:8 61
[D][05:17:53][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5005. period:500. j,i:9 62
[D][05:17:53][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5006. period:500. j,i:10 63
[D][05:17:53][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5006. period:500. j,i:19 72
[D][05:17:53][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5006. period:500. j,i:20 73
[D][05:17:53][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5007. period:500. j,i:21 74
[D][05:17:53][COMM]bat msg 025B loss. last_tick:0. cur_tick:50

2025-07-31 20:15:57:190 ==>> 07. period:500. j,i:23 76
[D][05:17:53][COMM]bat msg 025C loss. last_tick:0. cur_tick:5007. period:500. j,i:24 77
[D][05:17:53][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5008
[D][05:17:53][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5008


2025-07-31 20:15:57:653 ==>> [D][05:17:54][COMM]5658 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:15:58:302 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:15:58:840 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_proce

2025-07-31 20:15:58:946 ==>> ss].l:[1021].play audio file status fail
[D][05:17:55][COMM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], 

2025-07-31 20:15:59:051 ==>> hw[000
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17

2025-07-31 20:15:59:126 ==>> :55][COMM]get bat work state err
[W][05:17:55][PROT]remove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]6670 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][CAT1]power_urc_cb ret[5]


2025-07-31 20:15:59:667 ==>> [D][05:17:56][COMM]7680 imu init OK
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:16:00:682 ==>> [D][05:17:57][COMM]8692 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:16:00:757 ==>> [D][05:17:57][COMM]read battery soc:255


2025-07-31 20:16:01:129 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:16:01:133 ==>> 检测【33V输入电压ADC】
2025-07-31 20:16:01:136 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:01:468 ==>> [D][05:17:58][HSDK][0] flush to flash addr:[0xE41300] --- write len --- [256]
[W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:17:58][COMM]adc read out 24v adc:1310  volt:33133 mv
[D][05:17:58][COMM]adc read left brake adc:4  volt:5 mv
[D][05:17:58][COMM]adc read right brake adc:1  volt:1 mv
[D][05:17:58][COMM]adc read throttle adc:0  volt:0 mv
[D][05:17:58][COMM]adc read battery ts volt:8 mv
[D][05:17:58][COMM]adc read in 24v adc:1288  volt:32577 mv
[D][05:17:58][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2508  volt:4041 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:16:01:669 ==>> 【33V输入电压ADC】通过,【32577mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:16:01:673 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:16:01:676 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:16:01:681 ==>> [D][05:17:58][COMM]9702 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:16:01:756 ==>> 1A A1 00 00 FC 
Get AD_V2 1672mV
Get AD_V3 1646mV
Get AD_V4 1mV
Get AD_V5 2764mV
Get AD_V6 1991mV
Get AD_V7 1089mV
OVER 150


2025-07-31 20:16:01:942 ==>> 【TP7_VCC3V3(ADV2)】通过,【1672mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:01:945 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:16:01:960 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:01:963 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:16:01:965 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 20:16:01:978 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:16:01:981 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:16:01:998 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1991mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:16:02:002 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:16:02:023 ==>> 【TP1_VCC12V(ADV7)】通过,【1089mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:16:02:026 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:16:02:057 ==>> [D][05:17:59][COMM]msg 0223 loss. last_tick:0. cur_tick:10016. period:1000
[D][05:17:59][COMM]msg 0225 loss. last_tick:0. cur_tick:10017. period:1000
[D][05:17:59][COMM]msg 0229 loss. last_tick:0. cur_tick:10018. period:1000
[D][05:17:59][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10018
[D][05:17:59][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10018


2025-07-31 20:16:02:147 ==>> 1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1645mV
Get AD_V4 0mV
Get AD_V5 2764mV
Get AD_V6 2021mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:16:02:306 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:02:309 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:16:02:325 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1645mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:02:328 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:16:02:332 ==>> 原始值:【2764】, 乘以分压基数【2】还原值:【5528】
2025-07-31 20:16:02:344 ==>> 【TP68_VCC5V5(ADV5)】通过,【5528mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:16:02:347 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:16:02:363 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2021mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:16:02:366 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:16:02:386 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:16:02:389 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:16:02:453 ==>> [D][05:17:59][CAT1]power_urc_cb ret[76]
1A A1 00 00 FC 
Get AD_V2 1669mV
Get AD_V3 1646mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 2024mV
Get AD_V7 1088mV
OVER 150


2025-07-31 20:16:02:674 ==>> 【TP7_VCC3V3(ADV2)】通过,【1669mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:02:678 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:16:02:693 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:02:696 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:16:02:699 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:16:02:712 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:16:02:716 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:16:02:731 ==>> 【TP3_VCC_SYS(ADV6)】通过,【2024mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:16:02:735 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:16:02:756 ==>> 【TP1_VCC12V(ADV7)】通过,【1088mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:16:02:760 ==>> 检测【打开WIFI(1)】
2025-07-31 20:16:02:766 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:16:02:908 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]10714 imu init OK
[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][COMM]read batter

2025-07-31 20:16:02:953 ==>> y soc:255
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:16:03:358 ==>> [D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 31, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 32
[W][05:18:00][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:00][CAT1]tx ret[23] >>> AT+IMUCTRL=2,0,0,0,10

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087737047

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071539120

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:16:03:543 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:16:03:546 ==>> 检测【清空消息队列(1)】
2025-07-31 20:16:03:549 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:16:03:722 ==>> [D][05:18:00][COMM]imu error,enter wait
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!


2025-07-31 20:16:03:825 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:16:03:830 ==>> 检测【打开GPS(1)】
2025-07-31 20:16:03:833 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:16:04:088 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:01][COMM]Open GPS Module...
[D][05:18:01][COMM]LOC_MODEL_CONT
[D][05:18:01][GNSS]start event:8
[W][05:18:01][GNSS]start cont locating
[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+CGATT=1



2025-07-31 20:16:04:360 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:16:04:363 ==>> 检测【打开GSM联网】
2025-07-31 20:16:04:366 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:16:04:526 ==>> [W][05:18:01][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:01][COMM]GSM test
[D][05:18:01][COMM]GSM test enable


2025-07-31 20:16:04:634 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:16:04:638 ==>> 检测【打开仪表供电1】
2025-07-31 20:16:04:640 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:16:04:847 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:16:04:912 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:16:04:916 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:16:04:918 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:16:05:134 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:16:05:183 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:16:05:186 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:16:05:189 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:16:05:329 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33549]


2025-07-31 20:16:05:453 ==>> 【读取主控ADC采集的仪表电压】通过,【33549mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:16:05:456 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:16:05:463 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:05:650 ==>> [D][05:18:02][HSDK][0] flush to flash addr:[0xE41400] --- write len --- [256]
[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:05:710 ==>>                                       

2025-07-31 20:16:05:735 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:16:05:739 ==>> 检测【AD_V20电压】
2025-07-31 20:16:05:741 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:05:846 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:05:984 ==>> [D][05:18:02][CAT1]<<< 
OK

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:02][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:02][CAT1]tx ret[12] >>> AT+QIACT=1

1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:16:06:211 ==>> 本次取值间隔时间:364ms
2025-07-31 20:16:06:256 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:06:362 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:06:438 ==>> [W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:03][COMM]oneline display ALL on 1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 3mV
OVER 150


2025-07-31 20:16:06:801 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 5, ret: 6
[D][05:18:03][CAT1]sub id: 5, ret: 6

[D][05:18:03][SAL ]Cellular task submsg id[68]
[D][05:18:03][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:03][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:03][M2M ]M2M_GSM_INIT OK
[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:03][SAL ]open socket ind id[4], rst[0]
[D][05:18:03][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:03][SAL ]Cellular task submsg id[8]
[D][05:18:03][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:03][SAL ]domain[bikeapi.mobike.com] port[9999] type[1]
[D][05:18:03][CAT1]gsm read msg sub id: 8
[D][05:18:03][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:03][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:03][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:03][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:03][GNSS]recv submsg id[1]
[D][05:1

2025-07-31 20:16:06:806 ==>> 本次取值间隔时间:429ms
2025-07-31 20:16:06:830 ==>> 8:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:03][GNSS]location recv gms init done evt
[D][05:18:03][GNSS]GPS start. ret=0


2025-07-31 20:16:06:893 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:06:997 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:07:210 ==>>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         [CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[19] >>> AT+GPSBAUD=1152

2025-07-31 20:16:07:300 ==>> 00

[W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:04][CAT1]<<< 
OK

[D][05:18:04][CAT1]tx ret[13] >>> AT+GPSPWR=1

[D][05:18:04][CAT1]opened : 0, 0
[D][05:18:04][SAL ]Cellular task submsg id[68]
[D][05:18:04][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:04][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:04][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:04][M2M ]g_m2m_is_idle become true
[D][05:18:04][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE
1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:16:07:315 ==>> 本次取值间隔时间:312ms
2025-07-31 20:16:07:387 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:07:499 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:07:544 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:07:649 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:16:07:739 ==>> 本次取值间隔时间:237ms
2025-07-31 20:16:07:757 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:07:814 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A



2025-07-31 20:16:07:859 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:07:919 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:04][COMM]oneline display ALL on 1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:07:949 ==>> 1A A1 10 00 00 
Get AD_V20 4mV
OVER 150


2025-07-31 20:16:08:131 ==>> 本次取值间隔时间:270ms
2025-07-31 20:16:08:149 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:16:08:255 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:16:08:347 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:05][COMM]oneline display ALL on 1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1638mV
OVER 150


2025-07-31 20:16:08:452 ==>> [D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:16:08:708 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,2,1,07,33,,,41,42,,,41,39,,,40,38,,,38,1*7A

$GBGSV,2,2,07,41,,,34,59,,,40,14,,,37,1*7A

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.566,1608.566,51.441,2097152,2097152,2097152*4A

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:05][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:05][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:05][CAT1]<<< 
OK

[D][05:18:05][CAT1]exec over: func id: 23, ret: 6
[D][05:18:05][CAT1]sub id: 23, ret: 6

                                                                                            

2025-07-31 20:16:08:738 ==>> 本次取值间隔时间:475ms
2025-07-31 20:16:08:832 ==>> 【AD_V20电压】通过,【1638mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:08:836 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:16:08:841 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:16:08:846 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:16:08:948 ==>> 3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:16:09:142 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:16:09:149 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:16:09:154 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:16:09:404 ==>> [D][05:18:06][HSDK][0] flush to flash addr:[0xE41500] --- write len --- [256]
[W][05:18:06][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:06][COMM][frm_arm_hub_gpio_read]: Failed -2
[D][05:18:06][COMM]oneline display read state:255
[D][05:18:06][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:09:616 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,14,3,,,43,33,,,42,59,,,42,42,,,41,1*4D

$GBGSV,4,2,14,14,,,41,60,,,41,39,,,39,38,,,38,1*76

$GBGSV,4,3,14,24,,,37,25,,,37,26,,,36,41,,,35,1*77

$GBGSV,4,4,14,8,,,31,1,,,38,1*73

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1604.135,1604.135,51.333,2097152,2097152,2097152*48



2025-07-31 20:16:10:175 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:16:10:346 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:0
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:10:455 ==>> 【预留IO RX功能检查(0)】通过,【oneline display read state:0】符合目标值【oneline display read state:0】要求!
2025-07-31 20:16:10:460 ==>> 检测【拉高OUTPUT2】
2025-07-31 20:16:10:464 ==>> 向【COM34】发送指令:【3A A3 02 01 A3】
2025-07-31 20:16:10:544 ==>> 3A A3 02 01 A3 


2025-07-31 20:16:10:649 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,18,3,,,42,33,,,42,14,,,42,59,,,41,1*42

$GBGSV,5,2,18,42,,,41,60,,,41,39,,,40,1,,,39,1*4D

$GBGSV,5,3,18,24,,,39,25,,,39,38,,,38,40,,,38,1*77

$GBGSV,5,4,18,41,,,37,26,,,36,5,,,35,8,,,34,1*72

$GBGSV,5,5,18,4,,,33,44,,,36,1*4E

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1602.232,1602.232,51.246,2097152,2097152,2097152*4B

ON_OUT2
OVER 150


2025-07-31 20:16:10:727 ==>> 【拉高OUTPUT2】通过,【ON_OUT2】符合目标值【ON_OUT2】要求!
2025-07-31 20:16:10:731 ==>> 检测【预留IO RX功能检查(1)】
2025-07-31 20:16:10:735 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:16:10:846 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:16:10:951 ==>> [W][05:18:07][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:07][COMM]oneline display read state:1
[D][05:18:07][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:10:999 ==>> 【预留IO RX功能检查(1)】通过,【oneline display read state:1】符合目标值【oneline display read state:1】要求!
2025-07-31 20:16:11:003 ==>> 检测【预留IO LED功能输出】
2025-07-31 20:16:11:008 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,1】
2025-07-31 20:16:11:242 ==>> [W][05:18:08][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,1<<<<<
[D][05:18:08][COMM]oneline display set 1
[D][05:18:08][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:16:11:317 ==>> 【预留IO LED功能输出】通过,【oneline display set 1】符合目标值【oneline display set 1】要求!
2025-07-31 20:16:11:323 ==>> 检测【AD_V21电压】
2025-07-31 20:16:11:328 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:16:11:437 ==>> 1A A1 20 00 00 
Get AD_V21 1071mV
OVER 150


2025-07-31 20:16:11:527 ==>> 本次取值间隔时间:200ms
2025-07-31 20:16:11:573 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:16:11:679 ==>> $GBGGA,121615.447,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,23,33,,,42,3,,,41,14,,,41,59,,,41,1*49

$GBGSV,6,2,23,60,,,41,42,,,40,39,,,40,24,,,40,1*7E

$GBGSV,6,3,23,25,,,40,1,,,39,2,,,38,38,,,37,1*7C

$GBGSV,6,4,23,40,,,37,41,,,37,26,,,37,16,,,37,1*77

$GBGSV,6,5,23,5,,,35,8,,,35,44,,,34,4,,,33,1*4A

$GBGSV,6,6,23,10,,,32,7,,,16,9,,,46,1*7C

$GBRMC,121615.447,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121615.447,0.000,1532.197,1532.197,49.136,2097152,2097152,2097152*5F

1A A1 20 00 00 
Get AD_V21 1071mV
OVER 150


2025-07-31 20:16:11:879 ==>> 本次取值间隔时间:299ms
2025-07-31 20:16:11:943 ==>> 向【COM34】发送指令:【1A A1 20 00 00】
2025-07-31 20:16:12:051 ==>> 1A A1 20 00 00 
Get AD_V21 1635mV
OVER 150


2025-07-31 20:16:12:172 ==>> 本次取值间隔时间:217ms
2025-07-31 20:16:12:221 ==>> 【AD_V21电压】通过,【1635mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:16:12:225 ==>> 检测【关闭仪表供电2】
2025-07-31 20:16:12:229 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:16:12:431 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:18:09][COMM]set POWER 0
[D][05:18:09][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:0


2025-07-31 20:16:12:523 ==>> 【关闭仪表供电2】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:16:12:527 ==>> 检测【关闭仪表指令模式】
2025-07-31 20:16:12:530 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,0】
2025-07-31 20:16:12:627 ==>> $GBGGA,121616.427,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,24,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,6,2,24,60,,,41,42,,,41,24,,,41,25,,,41,1*75

$GBGSV,6,3,24,39,,,40,1,,,39,38,,,38,40,,,38,1*4F

$GBGSV,6,4,24,9,,,37,2,,,37,41,,,37,26,,,37,1*78

$GBGSV,6,5,24,16,,,37,7,,,36,6,,,36,8,,,35,1*4F

$GBGSV,6,6,24,5,,,34,44,,,33,4,,,33,10,,,33,1*77

$GBRMC,121616.427,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121616.427,0.000,1571.961,1571.961,50.287,2097152,2097152,2097152*5B



2025-07-31 20:16:12:732 ==>> [W][05:18:09][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,0<<<<<
[D][05:18:09][COMM][oneline_display]: command mode, OFF!


2025-07-31 20:16:12:803 ==>> 【关闭仪表指令模式】通过,【command mode, OFF】符合目标值【command mode, OFF】要求!
2025-07-31 20:16:12:807 ==>> 检测【打开AccKey2供电】
2025-07-31 20:16:12:813 ==>> 向【COM34】发送指令:【AT+ACCKEY2=1】
2025-07-31 20:16:12:868 ==>> [D][05:18:09][COMM]read battery soc:255


2025-07-31 20:16:13:018 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=1<<<<<


2025-07-31 20:16:13:082 ==>> 【打开AccKey2供电】通过,【>>>>>Input command = AT+ACCKEY2=1<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=1<<<<<】要求!
2025-07-31 20:16:13:092 ==>> 检测【通过主控ADC采集ACC_KEY2电压】
2025-07-31 20:16:13:112 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:13:351 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:10][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:10][COMM]adc read out 24v adc:1309  volt:33108 mv
[D][05:18:10][COMM]adc read left brake adc:0  volt:0 mv
[D][05:18:10][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:10][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:10][COMM]adc read battery ts volt:9 mv
[D][05:18:10][COMM]adc read in 24v adc:1296  volt:32779 mv
[D][05:18:10][COMM]adc read throttle brake in adc:1  volt:1 mv
[D][05:18:10][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:10][COMM]arm_hub adc read vbat adc:2464  volt:3970 mv
[D][05:18:10][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:10][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:10][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:16:13:614 ==>> 【通过主控ADC采集ACC_KEY2电压】通过,【33108mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:16:13:618 ==>> 检测【关闭AccKey2供电2】
2025-07-31 20:16:13:623 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:16:13:628 ==>> $GBGGA,121617.407,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,60,,,41,42,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,3,25,39,,,40,1,,,38,38,,,38,40,,,38,1*4E

$GBGSV,7,4,25,41,,,38,16,,,38,9,,,37,26,,,37,1*4D

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,43,,,36,1*73

$GBRMC,121617.407,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121617.407,0.000,1573.685,1573.685,50.339,2097152,2097152,2097152*5C



2025-07-31 20:16:13:806 ==>> [W][05:18:10][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<


2025-07-31 20:16:13:890 ==>> 【关闭AccKey2供电2】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:16:13:894 ==>> 该项需要延时执行
2025-07-31 20:16:14:608 ==>> $GBGGA,121618.387,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,60,,,41,42,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,3,25,39,,,40,1,,,38,38,,,38,40,,,38,1*4E

$GBGSV,7,4,25,41,,,38,16,,,38,9,,,37,26,,,37,1*4D

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121618.387,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121618.387,0.000,1565.468,1565.468,50.082,2097152,2097152,2097152*5F



2025-07-31 20:16:14:788 ==>> $GBGGA,121618.587,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,60,,,41,42,,,41,24,,,41,25,,,41,1*75

$GBGSV,7,3,25,39,,,40,1,,,38,38,,,38,41,,,38,1*4F

$GBGSV,7,4,25,16,,,38,40,,,37,9,,,37,26,,,37,1*43

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121618.587,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121618.587,0.000,1563.810,1563.810,50.029,2097152,2097152,2097152*58



2025-07-31 20:16:14:863 ==>> [D][05:18:11][COMM]read battery soc:255


2025-07-31 20:16:15:785 ==>> $GBGGA,121619.567,,,,,0,00,,,M,,M,,*62

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,40,1*74

$GBGSV,7,3,25,39,,,40,1,,,38,38,,,38,41,,,38,1*4F

$GBGSV,7,4,25,16,,,38,40,,,37,9,,,37,26,,,37,1*43

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121619.567,V,,,,,,,,0.0,E,N,V*46

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121619.567,0.000,1562.150,1562.150,49.975,2097152,2097152,2097152*5F



2025-07-31 20:16:16:774 ==>> $GBGGA,121620.547,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,42,3,,,41,59,,,41,1*4D

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,40,1,,,38,38,,,38,41,,,38,1*4F

$GBGSV,7,4,25,16,,,38,40,,,37,9,,,37,26,,,37,1*43

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121620.547,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121620.547,0.000,1563.810,1563.810,50.029,2097152,2097152,2097152*5F



2025-07-31 20:16:16:864 ==>> [D][05:18:13][COMM]read battery soc:255


2025-07-31 20:16:16:894 ==>> 此处延时了:【3000】毫秒
2025-07-31 20:16:16:899 ==>> 检测【通过主控ADC采集ACC_KEY2电压2】
2025-07-31 20:16:16:904 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:17:166 ==>> [D][05:18:14][HSDK][0] flush to flash addr:[0xE41600] --- write len --- [256]
[W][05:18:14][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:14][COMM]adc read vcc5v mc adc:3130  volt:5501 mv
[D][05:18:14][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:14][COMM]adc read left brake adc:1  volt:1 mv
[D][05:18:14][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:14][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:14][COMM]adc read battery ts volt:7 mv
[D][05:18:14][COMM]adc read in 24v adc:1289  volt:32602 mv
[D][05:18:14][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:14][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:14][COMM]arm_hub adc read vbat adc:2438  volt:3928 mv
[D][05:18:14][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:14][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:14][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:16:17:451 ==>> 【通过主控ADC采集ACC_KEY2电压2】通过,【0mV】符合目标值【0mV】至【1200mV】要求!
2025-07-31 20:16:17:455 ==>> 检测【打开AccKey1供电】
2025-07-31 20:16:17:459 ==>> 向【COM34】发送指令:【AT+MCPOWER=1】
2025-07-31 20:16:17:738 ==>> [W][05:18:14][COMM]>>>>>Input command = AT+MCPOWER=1<<<<<
[D][05:18:14][COMM]frm_peripheral_device_poweron type 5.... 
$GBGGA,121621.527,,,,,0,00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,39,1,,,38,16,,,38,38,,,37,1*4C

$GBGSV,7,4,25,41,,,37,40,,,37,26,,,37,9,,,36,1*4F

$GBGSV,7,5,25,2,,,36,7,,,36,6,,,36,8,,,35,1*7B

$GBGSV,7,6,25,5,,,34,10,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121621.527,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121621.527,0.000,1555.516,1555.516,49.762,2097152,2097152,2097152*58



2025-07-31 20:16:18:006 ==>> 【打开AccKey1供电】通过,【>>>>>Input command = AT+MCPOWER=1<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=1<<<<<】要求!
2025-07-31 20:16:18:010 ==>> 检测【读取AccKey1电压(ADV14)前】
2025-07-31 20:16:18:013 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:16:18:148 ==>> 1A A1 00 40 00 
Get AD_V14 2659mV
OVER 150


2025-07-31 20:16:18:268 ==>> 原始值:【2659】, 乘以分压基数【2】还原值:【5318】
2025-07-31 20:16:18:287 ==>> 【读取AccKey1电压(ADV14)前】通过,【5318mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:16:18:290 ==>> 检测【通过主控ADC采集ACC_KEY1电压】
2025-07-31 20:16:18:294 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:18:561 ==>> [W][05:18:15][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:15][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:15][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:15][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read right brake adc:3  volt:3 mv
[D][05:18:15][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:15][COMM]adc read battery ts volt:9 mv
[D][05:18:15][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:15][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:15][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:15][COMM]arm_hub adc read vbat adc:2507  volt:4039 mv
[D][05:18:15][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:15][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:15][COMM]arm_hub adc read front lamp adc:4  volt:92 mv


2025-07-31 20:16:18:666 ==>> $GBGGA,121622.507,,,,,0,00,,,M,,M,,*6C

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,39,1,,,38,16,,,38,38,,,38,1*43

$GBGSV,7,4,25,41,,,37,40,,,37,26,,,37,9,,,37,1*4E

$GBGSV,7,5,25,2,,,36,6,,,36,7,,,35,8

2025-07-31 20:16:18:711 ==>> ,,,35,1*78

$GBGSV,7,6,25,10,,,34,5,,,33,44,,,33,4,,,33,1*77

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121622.507,V,,,,,,,,0.0,E,N,V*48

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121622.507,0.000,1555.519,1555.519,49.765,2097152,2097152,2097152*5E



2025-07-31 20:16:18:822 ==>> 【通过主控ADC采集ACC_KEY1电压】通过,【5498mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:16:18:827 ==>> 检测【关闭AccKey1供电2】
2025-07-31 20:16:18:832 ==>> 向【COM34】发送指令:【AT+MCPOWER=0】
2025-07-31 20:16:18:891 ==>> [D][05:18:15][COMM]read battery soc:255


2025-07-31 20:16:19:026 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+MCPOWER=0<<<<<
[D][05:18:16][COMM]frm_peripheral_device_poweroff type 5.... 


2025-07-31 20:16:19:104 ==>> 【关闭AccKey1供电2】通过,【>>>>>Input command = AT+MCPOWER=0<<<<<】符合目标值【>>>>>Input command = AT+MCPOWER=0<<<<<】要求!
2025-07-31 20:16:19:109 ==>> 检测【读取AccKey1电压(ADV14)后】
2025-07-31 20:16:19:114 ==>> 向【COM34】发送指令:【1A A1 00 40 00】
2025-07-31 20:16:19:251 ==>> 1A A1 00 40 00 
Get AD_V14 2661mV
OVER 150


2025-07-31 20:16:19:355 ==>> 原始值:【2661】, 乘以分压基数【2】还原值:【5322】
2025-07-31 20:16:19:373 ==>> 【读取AccKey1电压(ADV14)后】通过,【5322mV】符合目标值【5000mV】至【5500mV】要求!
2025-07-31 20:16:19:377 ==>> 检测【打开WIFI(2)】
2025-07-31 20:16:19:383 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:16:19:555 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:18:16][CAT1]gsm read msg sub id: 12
[D][05:18:16][CAT1]SEND RAW data >>> AT+WIFISCAN=4,10

[D][05:18:16][CAT1]<<< 
OK

[D][05:18:16][CAT1]exec over: func id: 12, ret: 6


2025-07-31 20:16:19:649 ==>> 【打开WIFI(2)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:16:19:653 ==>> 检测【转刹把供电】
2025-07-31 20:16:19:656 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:16:19:662 ==>> $GBGGA,121623.507,,,,,0,

2025-07-31 20:16:19:736 ==>> 00,,,M,,M,,*6D

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,39,1,,,38,16,,,38,38,,,38,1*43

$GBGSV,7,4,25,41,,,38,40,,,37,26,,,37,9,,,36,1*40

$GBGSV,7,5,25,2,,,36,6,,,36,7,,,35,8,,,35,1*78

$GBGSV,7,6,25,10,,,34,5,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121623.507,V,,,,,,,,0.0,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121623.507,0.000,1557.176,1557.176,49.816,2097152,2097152,2097152*54



2025-07-31 20:16:19:811 ==>> [W][05:18:16][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:16:19:918 ==>> 【转刹把供电】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<】要求!
2025-07-31 20:16:19:924 ==>> 检测【读取AD_V15电压(前)】
2025-07-31 20:16:19:928 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:16:20:021 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:16:20:112 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<


2025-07-31 20:16:20:143 ==>> 1A A1 00 80 00 
Get AD_V15 2399mV
OVER 150


2025-07-31 20:16:20:172 ==>> 原始值:【2399】, 乘以分压基数【2】还原值:【4798】
2025-07-31 20:16:20:190 ==>> 【读取AD_V15电压(前)】通过,【4798mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:16:20:197 ==>> 检测【读取AD_V16电压(前)】
2025-07-31 20:16:20:201 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=1】
2025-07-31 20:16:20:293 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:16:20:338 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=1<<<<<
1A A1 01 00 00 
Get AD_V16 2428mV
OVER 150


2025-07-31 20:16:20:429 ==>> +WIFISCAN:4,0,603A7CF67DD4,-73
+WIFISCAN:4,1,CC057790A7C0,-74
+WIFISCAN:4,2,CC057790A741,-76
+WIFISCAN:4,3,CC057790A6E1,-80

[D][05:18:17][CAT1]wifi scan report total[4]


2025-07-31 20:16:20:444 ==>> 原始值:【2428】, 乘以分压基数【2】还原值:【4856】
2025-07-31 20:16:20:470 ==>> 【读取AD_V16电压(前)】通过,【4856mV】符合目标值【4600mV】至【5000mV】要求!
2025-07-31 20:16:20:474 ==>> 检测【转刹把供电电压(主控ADC)】
2025-07-31 20:16:20:481 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:20:821 ==>> [W][05:18:17][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:17][COMM]adc read vcc5v mc adc:3127  volt:5496 mv
[D][05:18:17][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:17][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:17][COMM]adc read right brake adc:1  volt:1 mv
[D][05:18:17][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:17][COMM]adc read battery ts volt:11 mv
[D][05:18:17][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:17][COMM]adc read throttle brake in adc:3072  volt:5400 mv
[D][05:18:17][COMM]arm_hub adc read bat_id adc:13  volt:10 mv
$GBGGA,121624.507,,,,,0,00,,,M,,M,,*6A

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,39,1,,,38,16,,,38,38,,,38,1*43

$GBGSV,7,4,25,40,,,38,41,,,37,26,,,37,9,,,36,1*40

$GBGSV,7,5,25,2,,,36,6,,,36,8,,,36,7,,,35,1*7B

$GBGSV,7,6,25,10,,,34,5,,,34,44,,,33,4,,,33,1*70

$GBGSV,7,7,25,34,,,33,1*76

$GBRMC,121624.507,V,,,,,,,,0.0,E,N,V*4E

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121624.507,0.000,1558.832,1558.832,49.868,2097152,2097152,2097152*5A

[D][05:18:17][COMM]arm_

2025-07-31 20:16:20:882 ==>> hub adc read vbat adc:2500  volt:4028 mv
[D][05:18:17][COMM]arm_hub adc read led yb adc:1448  volt:33572 mv
[D][05:18:17][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:17][COMM]arm_hub adc read front lamp adc:4  volt:92 mv
[D][05:18:17][GNSS]recv submsg id[3]
                                         

2025-07-31 20:16:21:007 ==>> 【转刹把供电电压(主控ADC)】通过,【5400mV】符合目标值【5300mV】至【5600mV】要求!
2025-07-31 20:16:21:011 ==>> 检测【转刹把供电电压】
2025-07-31 20:16:21:014 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:21:367 ==>> [D][05:18:18][HSDK][0] flush to flash addr:[0xE41700] --- write len --- [256]
[W][05:18:18][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:18][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:18][COMM]adc read out 24v adc:1  volt:25 mv
[D][05:18:18][COMM]adc read left brake adc:2  volt:2 mv
[D][05:18:18][COMM]adc read right brake adc:2  volt:2 mv
[D][05:18:18][COMM]adc read throttle adc:4  volt:5 mv
[D][05:18:18][COMM]adc read battery ts volt:12 mv
[D][05:18:18][COMM]adc read in 24v adc:1287  volt:32552 mv
[D][05:18:18][COMM]adc read throttle brake in adc:3073  volt:5401 mv
[D][05:18:18][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:18:18][COMM]arm_hub adc read vbat adc:2456  volt:3957 mv
[D][05:18:18][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:18][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:18][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:16:21:608 ==>> 【转刹把供电电压】通过,【5401mV】符合目标值【5300mV】至【5550mV】要求!
2025-07-31 20:16:21:614 ==>> 检测【关闭转刹把供电2】
2025-07-31 20:16:21:619 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:16:21:730 ==>> $GBGGA,121625.507,,,,,0,00,,,M,,M,,*6B

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,42,14,,,41,3,,,41,59,,,41,1*4E

$GBGSV,7,2,25,60,,,41,24,,,41,25,,,41,42,,,41,1*75

$GBGSV,7,3,25,39,,,39,1,,,38,16,,,38,38,,,38,1*43

$GBGSV,7,4,25,40,,,37,41,,,37,26,,,37,9,,,36,1*4F

$GBGSV,7,5,25,2,,,36,6,,,36,8,,,35,7,,,35,1*78

$GBGSV,7,6,25,10,,,34,5,,,33,4,,,33,34,,,33,1*70

$GBGSV,7,7,25,44,,,32,1*70

$GBRMC,121625.507,V,,,,,,,,0.0,E,N,V*4F

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121625.507,0.000,1552.207,1552.207,49.663,2097152,2097152,2097152*5E



2025-07-31 20:16:21:805 ==>> [W][05:18:18][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:16:21:941 ==>> 【关闭转刹把供电2】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:16:21:948 ==>> 检测【读取AD_V15电压(后)】
2025-07-31 20:16:21:962 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:16:22:048 ==>> 向【COM34】发送指令:【1A A1 00 80 00】
2025-07-31 20:16:22:109 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:16:22:138 ==>> 1A A1 00 80 00 
Get AD_V15 1mV
OVER 150


2025-07-31 20:16:22:307 ==>> 【读取AD_V15电压(后)】通过,【1mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:16:22:311 ==>> 检测【读取AD_V16电压(后)】
2025-07-31 20:16:22:317 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:16:22:413 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:16:22:523 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:16:22:633 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:16:22:739 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:16:22:769 ==>> [W][05:18:19][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<
$GBGGA,121626.507,,,,,0,00,,,M,,M,,*68

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,59,,,41,60,,,41,1*7B

$GBGSV,7,2,26,24,,,41,25,,,41,42,,,41,3,,,40,1*42

$GBGSV,7,3,26,39,,,39,1,,,38,16,,,38,38,,,37,1*4F

$GBGSV,7,4,26,40,,,37,41,,,37,26,,,36,9,,,36,1*4D

$GBGSV,7,5,26,2,,,36,6,,,36,8,,,35,7,,,35,1*7B

$GBGSV,7,6,26,10,,,34,5,,,33,4,,,33,34,,,33,1*73

$GBGSV,7,7,26,44,,,32,13,,,38,1*7A

$GBRMC,121626.507,V,,,,,,,,0.0,E,N,V*4C

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121626.507,0.000,1548.891,1548.891,49.559,2097152,2097152,2097152*57

[W][05:18:19][COMM]>>>>>Input command = ?<<<<<


2025-07-31 20:16:22:843 ==>> 向【COM34】发送指令:【1A A1 01 00 00】
2025-07-31 20:16:22:950 ==>> [D][05:18:19][COMM]read battery soc:255
[W][05:18:19][COMM]>>>>>Input command = ?<<<<<
1A A1 01 00 00 
Get AD_V16 3mV
OVER 150


2025-07-31 20:16:22:993 ==>> 【读取AD_V16电压(后)】通过,【3mV】符合目标值【0mV】至【50mV】要求!
2025-07-31 20:16:22:998 ==>> 检测【拉高OUTPUT3】
2025-07-31 20:16:23:004 ==>> 向【COM34】发送指令:【3A A3 03 01 A3】
2025-07-31 20:16:23:040 ==>> 3A A3 03 01 A3 
ON_OUT3
OVER 150


2025-07-31 20:16:23:274 ==>> 【拉高OUTPUT3】通过,【ON_OUT3】符合目标值【ON_OUT3】要求!
2025-07-31 20:16:23:279 ==>> 检测【拉高OUTPUT4】
2025-07-31 20:16:23:286 ==>> 向【COM34】发送指令:【3A A3 04 01 A3】
2025-07-31 20:16:23:346 ==>> 3A A3 04 01 A3 


2025-07-31 20:16:23:436 ==>> ON_OUT4
OVER 150


2025-07-31 20:16:23:546 ==>> 【拉高OUTPUT4】通过,【ON_OUT4】符合目标值【ON_OUT4】要求!
2025-07-31 20:16:23:554 ==>> 检测【拉高OUTPUT5】
2025-07-31 20:16:23:568 ==>> 向【COM34】发送指令:【3A A3 05 01 A3】
2025-07-31 20:16:23:648 ==>> 3A A3 05 01 A3 


2025-07-31 20:16:23:738 ==>> $GBGGA,121627.507,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,59,,,41,60,,,41,1*7B

$GBGSV,7,2,26,24,,,41,25,,,41,42,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,39,1,,,38,16,,,38,13,,,38,1*49

$GBGSV,7,4,26,38,,,38,40,,,37,41,,,37,26,,,37,1*70

$GBGSV,7,5,26,9,,,36,2,,,36,6,,,36,8,,,35,1*76

$GBGSV,7,6,26,7,,,35,10,,,34,5,,,33,4,,,33,1*45

$GBGSV,7,7,26,34,,,33,44,,,32,1*74

$GBRMC,121627.507,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121627.507,0.000,1554.694,1554.694,49.742,2097152,2097152,2097152*5E

ON_OUT5
OVER 150


2025-07-31 20:16:23:818 ==>> 【拉高OUTPUT5】通过,【ON_OUT5】符合目标值【ON_OUT5】要求!
2025-07-31 20:16:23:823 ==>> 检测【左刹电压测试1】
2025-07-31 20:16:23:828 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:24:161 ==>> [W][05:18:21][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:21][COMM]adc read vcc5v mc adc:3132  volt:5505 mv
[D][05:18:21][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:21][COMM]adc read left brake adc:1717  volt:2263 mv
[D][05:18:21][COMM]adc read right brake adc:1717  volt:2263 mv
[D][05:18:21][COMM]adc read throttle adc:1712  volt:2257 mv
[D][05:18:21][COMM]adc read battery ts volt:6 mv
[D][05:18:21][COMM]adc read in 24v adc:1284  volt:32476 mv
[D][05:18:21][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:21][COMM]arm_hub adc read bat_id adc:10  volt:8 mv
[D][05:18:21][COMM]arm_hub adc read vbat adc:2440  volt:3931 mv
[D][05:18:21][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:21][COMM]arm_hub adc read board id adc:3354  volt:2702 mv
[D][05:18:21][COMM]arm_hub adc read front lamp adc:3  volt:69 mv


2025-07-31 20:16:24:347 ==>> 【左刹电压测试1】通过,【2263】符合目标值【2250】至【2500】要求!
2025-07-31 20:16:24:352 ==>> 检测【右刹电压测试1】
2025-07-31 20:16:24:369 ==>> 【右刹电压测试1】通过,【2263】符合目标值【2250】至【2500】要求!
2025-07-31 20:16:24:373 ==>> 检测【转把电压测试1】
2025-07-31 20:16:24:395 ==>> 【转把电压测试1】通过,【2257】符合目标值【2250】至【2500】要求!
2025-07-31 20:16:24:403 ==>> 检测【拉低OUTPUT3】
2025-07-31 20:16:24:425 ==>> 向【COM34】发送指令:【3A A3 03 00 A3】
2025-07-31 20:16:24:449 ==>> 3A A3 03 00 A3 
OFF_OUT3
OVER 150


2025-07-31 20:16:24:670 ==>> 【拉低OUTPUT3】通过,【OFF_OUT3】符合目标值【OFF_OUT3】要求!
2025-07-31 20:16:24:679 ==>> 检测【拉低OUTPUT4】
2025-07-31 20:16:24:689 ==>> 向【COM34】发送指令:【3A A3 04 00 A3】
2025-07-31 20:16:24:740 ==>> $GBGGA,121628.507,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,59,,,41,60,,,41,1*7B

$GBGSV,7,2,26,24,,,41,25,,,41,42,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,38,16,,,38,13,,,38,1*47

$GBGSV,7,4,26,38,,,38,41,,,38,40,,,37,26,,,37,1*7F

$GBGSV,7,5,26,9,,,36,2,,,36,6,,,36,7,,,36,1*7A

$GBGSV,7,6,26,8,,,35,10,,,34,5,,,34,4,,,33,1*4D

$GBGSV,7,7,26,34,,,32,44,,,32,1*75

$GBRMC,121628.507,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121628.507,0.000,1559.478,1559.478,49.896,2097152,2097152,2097152*57

3A A3 04 00 A3 
OFF_OUT4
OVER 150


2025-07-31 20:16:24:891 ==>> [D][05:18:21][COMM]read battery soc:255


2025-07-31 20:16:24:940 ==>> 【拉低OUTPUT4】通过,【OFF_OUT4】符合目标值【OFF_OUT4】要求!
2025-07-31 20:16:24:946 ==>> 检测【拉低OUTPUT5】
2025-07-31 20:16:24:953 ==>> 向【COM34】发送指令:【3A A3 05 00 A3】
2025-07-31 20:16:25:041 ==>> 3A A3 05 00 A3 
OFF_OUT5
OVER 150


2025-07-31 20:16:25:212 ==>> 【拉低OUTPUT5】通过,【OFF_OUT5】符合目标值【OFF_OUT5】要求!
2025-07-31 20:16:25:220 ==>> 检测【左刹电压测试2】
2025-07-31 20:16:25:245 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:16:25:551 ==>> [W][05:18:22][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:18:22][COMM]adc read vcc5v mc adc:3128  volt:5498 mv
[D][05:18:22][COMM]adc read out 24v adc:0  volt:0 mv
[D][05:18:22][COMM]adc read left brake adc:3  volt:3 mv
[D][05:18:22][COMM]adc read right brake adc:0  volt:0 mv
[D][05:18:22][COMM]adc read throttle adc:0  volt:0 mv
[D][05:18:22][COMM]adc read battery ts volt:9 mv
[D][05:18:22][COMM]adc read in 24v adc:1291  volt:32653 mv
[D][05:18:22][COMM]adc read throttle brake in adc:0  volt:0 mv
[D][05:18:22][COMM]arm_hub adc read bat_id adc:12  volt:9 mv
[D][05:18:22][COMM]arm_hub adc read vbat adc:2507  volt:4039 mv
[D][05:18:22][COMM]arm_hub adc read led yb adc:1447  volt:33549 mv
[D][05:18:22][COMM]arm_hub adc read board id adc:3355  volt:2703 mv
[D][05:18:22][COMM]arm_hub adc read front lamp adc:5  volt:115 mv


2025-07-31 20:16:25:731 ==>> $GBGGA,121629.507,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,42,59,,,41,1*78

$GBGSV,7,2,26,60,,,41,25,,,41,42,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,38,16,,,38,13,,,38,1*47

$GBGSV,7,4,26,38,,,38,41,,,38,40,,,37,26,,,37,1*7F

$GBGSV,7,5,26,9,,,36,2,,,36,6,,,36,7,,,36,1*7A

$GBGSV,7,6,26,8,,,36,10,,,34,5,,,34,4,,,33,1*4E

$GBGSV,7,7,26,34,,,32,44,,,32,1*75

$GBRMC,121629.507,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121629.507,0.000,1562.668,1562.668,49.998,2097152,2097152,2097152*59



2025-07-31 20:16:25:736 ==>> System.ArgumentOutOfRangeException: 索引超出范围。必须为非负值并小于集合大小。
参数名: chunkLength
   在 System.Text.StringBuilder.ToString()
   在 AppSe5x.FormMain.DoWork()
2025-07-31 20:16:25:741 ==>> #################### 【测试结束】 ####################
2025-07-31 20:16:25:760 ==>> 关闭5V供电
2025-07-31 20:16:25:767 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:16:25:836 ==>> 5A A5 04 5A A5 


2025-07-31 20:16:25:942 ==>> CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:16:26:733 ==>> $GBGGA,121630.507,,,,,0,00,,,M,,M,,*6F

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,42,24,,,41,59,,,41,1*7B

$GBGSV,7,2,26,60,,,41,25,,,41,42,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,40,1,,,38,16,,,38,13,,,38,1*47

$GBGSV,7,4,26,38,,,38,41,,,38,40,,,37,26,,,37,1*7F

$GBGSV,7,5,26,9,,,36,2,,,36,6,,,36,7,,,35,1*79

$GBGSV,7,6,26,8,,,35,10,,,34,5,,,34,4,,,33,1*4D

$GBGSV,7,7,26,34,,,32,44,,,32,1*75

$GBRMC,121630.507,V,,,,,,,,0.0,E,N,V*4B

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121630.507,0.000,1557.885,1557.885,49.846,2097152,2097152,2097152*53



2025-07-31 20:16:26:763 ==>> 关闭5V供电成功
2025-07-31 20:16:26:770 ==>> 关闭33V供电
2025-07-31 20:16:26:776 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:16:26:838 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:16:27:033 ==>> [D][05:18:23][COMM]read battery soc:255
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:23][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:16:27:230 ==>> [D][05:18:24][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:16:27:752 ==>> $GBGGA,121631.507,,,,,0,00,,,M,,M,,*6E

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,26,33,,,42,14,,,41,24,,,41,59,,,41,1*78

$GBGSV,7,2,26,60,,,41,25,,,41,42,,,41,3,,,41,1*43

$GBGSV,7,3,26,39,,,39,1,,,38,16,,,38,13,,,38,1*49

$GBGSV,7,4,26,38,,,38,41,,,38,40,,,37,26,,,37,1*7F

$GBGSV,7,5,26,9,,,36,2,,,36,6,,,36,7,,,35,1*79

$GBGSV,7,6,26,8,,,35,10,,,34,5,,,34,4,,,33,1*4D

$GBGSV,7,7,26,34,,,32,44,,,32,1*75

$GBRMC,121631.507,V,,,,,,,310725,0.1,E,N,V*49

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121631.507,0.000,775.896,775.896,709.574,2097152,2097152,2097152*6D



2025-07-31 20:16:27:767 ==>> 关闭33V供电成功
2025-07-31 20:16:27:777 ==>> 关闭3.7V供电
2025-07-31 20:16:27:787 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:16:27:842 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:16:28:740 ==>>  

