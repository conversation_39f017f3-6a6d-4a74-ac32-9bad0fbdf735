2025-07-31 20:14:14:534 ==>> MES查站成功:
查站序号:P5100010053128F2验证通过
2025-07-31 20:14:14:542 ==>> 扫码结果:P5100010053128F2
2025-07-31 20:14:14:543 ==>> 当前测试项目:SE51_PCBA
2025-07-31 20:14:14:545 ==>> 测试参数版本:2024.10.11
2025-07-31 20:14:14:546 ==>> 参数更新时间:2024-10-11 10:07:56
2025-07-31 20:14:14:548 ==>> 检测【打开透传】
2025-07-31 20:14:14:549 ==>> 向【COM34】发送指令:【[C128]RECVTTL:1】
2025-07-31 20:14:14:646 ==>> [C128]connect ready
RECVTTL SET SUCCESS


2025-07-31 20:14:14:815 ==>> 【打开透传】通过,【RECVTTL SET SUCCESS】符合目标值【RECVTTL SET SUCCESS】要求!
2025-07-31 20:14:14:822 ==>> 检测【检测接地电压】
2025-07-31 20:14:14:824 ==>> 向【COM34】发送指令:【1A A1 40 00 00】
2025-07-31 20:14:14:938 ==>> 1A A1 40 00 00 
Get AD_V22 234mV
OVER 150


2025-07-31 20:14:15:093 ==>> 【检测接地电压】通过,【234mV】符合目标值【100mV】至【300mV】要求!
2025-07-31 20:14:15:096 ==>> 检测【打开小电池】
2025-07-31 20:14:15:099 ==>> 向【COM34】发送指令:【6A A6 01 A6 6A】
2025-07-31 20:14:15:151 ==>> 6A A6 01 A6 6A 
Battery ON
OVER 150


2025-07-31 20:14:15:366 ==>> 【打开小电池】通过,【Battery ON】符合目标值【Battery ON】要求!
2025-07-31 20:14:15:370 ==>> 检测【检测小电池分压(AD_VBAT)】
2025-07-31 20:14:15:374 ==>> 向【COM34】发送指令:【1A A1 00 00 01】
2025-07-31 20:14:15:438 ==>> 1A A1 00 00 01 
Get AD_V0 1290mV
OVER 150


2025-07-31 20:14:15:639 ==>> 【检测小电池分压(AD_VBAT)】通过,【1290mV】符合目标值【1200mV】至【1450mV】要求!
2025-07-31 20:14:15:642 ==>> 检测【等待设备启动】
2025-07-31 20:14:15:644 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:14:16:038 ==>> *** Booting Zephyr OS build E2_X40_191V1  ***
flash is 24bit address mode

SPI Flash init success, ID is 0xC84018

HW SW version: 5340 109

netcore sw 105, netboot sw 102
get_boot_mode 0
is_app_complete 0


2025-07-31 20:14:16:222 ==>> *** Booting Zephyr OS build v2.7.99-ncs1-1  ***
[ADC]Timer status: enabled

[ADC]init adc success.



2025-07-31 20:14:16:684 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:14:16:879 ==>> [W][05:17:49][COMM]BKP RESET_MODE[a5a5], reason[0-0]
[E][05:17:49][COMM]RESETREAS:0x00000000
[E][05:17:49][COMM]Multirider mode not support: 255


2025-07-31 20:14:16:925 ==>>                                                    

2025-07-31 20:14:17:330 ==>> [E][05:17:50][COMM]1x1 rx timeout


2025-07-31 20:14:17:711 ==>> 开始监测关键字:【Init MC LOCK_STATE 2】
2025-07-31 20:14:17:801 ==>> [E][05:17:50][COMM]1x1 rx timeout
[E][05:17:50][COMM]1x1 tp timeout
[E][05:17:50][COMM]1x1 error -3.
[W][05:17:50][COMM]Bat auth off fail, error:-1
[E][05:17:50][COMM][MC]exit stolen,get work mode err,rt:-3
[W][05:17:50][COMM]Init MC LOCK_STATE 2
[W][05:17:50][PROT]remove success[1629955070],send_path[2],type[0000],priority[0],index[0],used[0]
[W][05:17:50][PROT]add success [1629955070],send_path[2],type[4205],priority[0],index[0],used[1]


2025-07-31 20:14:17:988 ==>> 【等待设备启动】通过,【Init MC LOCK_STATE 2】符合目标值【Init MC LOCK_STATE 2】要求!
2025-07-31 20:14:17:991 ==>> 检测【产品通信】
2025-07-31 20:14:17:993 ==>> 向【COM34】发送指令:【AT+PWD=6789】
2025-07-31 20:14:18:108 ==>> [W][05:17:50][COMM]>>>>>Input command = AT+PWD=6789<<<<<
[D][05:17:50][COMM]Password OK


2025-07-31 20:14:18:261 ==>> 【产品通信】通过,【Password OK】符合目标值【Password OK】要求!
2025-07-31 20:14:18:264 ==>> 检测【初始化完成检测】
2025-07-31 20:14:18:267 ==>> 向【COM34】发送指令:【AT+INIT_CHECK】
2025-07-31 20:14:18:470 ==>> [D][05:17:51][HSDK][0] flush to flash addr:[0xE41B00] --- write len --- [256]
[W][05:17:51][COMM]>>>>>Input command = AT+INIT_CHECK<<<<<
[D][05:17:51][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:51][COMM]----- get Acckey 1 and value:1------------
[D][05:17:51][COMM]----- get Acckey 2 and value:1------------
[D][05:17:51][COMM]SE50 init success!
[D][05:17:51][COMM][LedDisplay]LED run over,op:0xc63,cnt:15


2025-07-31 20:14:18:534 ==>> 【初始化完成检测】通过,【init success】符合目标值【init success】要求!
2025-07-31 20:14:18:536 ==>> 检测【关闭大灯控制1】
2025-07-31 20:14:18:537 ==>> 向【COM34】发送指令:【AT+ARMLAMP=0】
2025-07-31 20:14:18:729 ==>> [W][05:17:51][COMM]>>>>>Input command = AT+ARMLAMP=0<<<<<


2025-07-31 20:14:18:807 ==>> 【关闭大灯控制1】通过,【>>>>>Input command = AT+ARMLAMP=0<<<<<】符合目标值【>>>>>Input command = AT+ARMLAMP=0<<<<<】要求!
2025-07-31 20:14:18:809 ==>> 检测【打开仪表指令模式1】
2025-07-31 20:14:18:810 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:14:18:864 ==>> [D][05:17:51][COMM]2637 imu init OK
[D][05:17:51][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:19:074 ==>> [D][05:17:51][COMM]Main Task receive event:121
[D][05:17:51][COMM]main task tmp_sleep_event = 40
[D][05:17:51][FCTY]F:[handlerVariableConfigReportPeriodCheck].L:[7774] ready to read para2 flash
[W][05:17:51][PROT]remove success[1629955071],send_path[2],type[0000],priority[0],index[1],used[0]
[W][05:17:51][PROT]add success [1629955071],send_path[2],type[4B02],priority[0],index[1],used[1]
[D][05:17:51][COMM]Main Task receive event:121 finished processing
[W][05:17:51][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:17:51][COMM][oneline_display]: command mode, ON!
[D][05:17:51][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:14:19:332 ==>> 【打开仪表指令模式1】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:14:19:335 ==>> 检测【关闭仪表供电】
2025-07-31 20:14:19:337 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,0】
2025-07-31 20:14:19:542 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,0<<<<<
[D][05:17:52][COMM]set POWER 0
[D][05:17:52][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:0


2025-07-31 20:14:19:606 ==>> 【关闭仪表供电】通过,【set POWER 0】符合目标值【set POWER 0】要求!
2025-07-31 20:14:19:609 ==>> 检测【关闭AccKey2供电1】
2025-07-31 20:14:19:611 ==>> 向【COM34】发送指令:【AT+ACCKEY2=0】
2025-07-31 20:14:19:874 ==>> [W][05:17:52][COMM]>>>>>Input command = AT+ACCKEY2=0<<<<<
[D][05:17:52][COMM]3648 imu init OK
[D][05:17:52][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:20:143 ==>> 【关闭AccKey2供电1】通过,【>>>>>Input command = AT+ACCKEY2=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY2=0<<<<<】要求!
2025-07-31 20:14:20:145 ==>> 检测【关闭AccKey1供电1】
2025-07-31 20:14:20:148 ==>> 向【COM34】发送指令:【AT+ACCKEY1=0】
2025-07-31 20:14:20:320 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+ACCKEY1=0<<<<<


2025-07-31 20:14:20:414 ==>> 【关闭AccKey1供电1】通过,【>>>>>Input command = AT+ACCKEY1=0<<<<<】符合目标值【>>>>>Input command = AT+ACCKEY1=0<<<<<】要求!
2025-07-31 20:14:20:417 ==>> 检测【关闭转刹把供电1】
2025-07-31 20:14:20:419 ==>> 向【COM34】发送指令:【AT+THROTTLE_BRAKE_POWER=0】
2025-07-31 20:14:20:626 ==>> [W][05:17:53][COMM]>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<


2025-07-31 20:14:20:693 ==>> 【关闭转刹把供电1】通过,【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】符合目标值【>>>>>Input command = AT+THROTTLE_BRAKE_POWER=0<<<<<】要求!
2025-07-31 20:14:20:696 ==>> 检测【打开33V供电(C128电源OUT1)1】
2025-07-31 20:14:20:697 ==>> 向【COM34】发送指令:【5A A5 01 5A A5】
2025-07-31 20:14:20:731 ==>> 5A A5 01 5A A5 
OPEN_POWER_OUT1
OVER 150


2025-07-31 20:14:20:897 ==>> [D][05:17:53][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 0
[D][05:17:53][COMM]read battery soc:255
[D][05:17:53][COMM]4659 imu init OK
[D][05:17:53][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:53][CAT1]power_urc_cb ret[5]


2025-07-31 20:14:20:976 ==>> 【打开33V供电(C128电源OUT1)1】通过,【OPEN_POWER_OUT1】符合目标值【OPEN_POWER_OUT1】要求!
2025-07-31 20:14:20:979 ==>> 检测【打开5V供电(C128电源OUT2)】
2025-07-31 20:14:20:981 ==>> 向【COM34】发送指令:【5A A5 03 5A A5】
2025-07-31 20:14:21:047 ==>> 5A A5 03 5A A5 
OPEN_POWER_OUT2
OVER 150


2025-07-31 20:14:21:249 ==>> 【打开5V供电(C128电源OUT2)】通过,【OPEN_POWER_OUT2】符合目标值【OPEN_POWER_OUT2】要求!
2025-07-31 20:14:21:252 ==>> 该项需要延时执行
2025-07-31 20:14:21:412 ==>> [D][05:17:54][COMM]msg 0601 loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]msg 02AC loss. last_tick:0. cur_tick:5012. period:500
[D][05:17:54][COMM]bat msg 0243 loss. last_tick:0. cur_tick:5013. period:500. j,i:4 57
[D][05:17:54][COMM]bat msg 0245 loss. last_tick:0. cur_tick:5013. period:500. j,i:6 59
[D][05:17:54][COMM]bat msg 0246 loss. last_tick:0. cur_tick:5013. period:500. j,i:7 60
[D][05:17:54][COMM]bat msg 0247 loss. last_tick:0. cur_tick:5014. period:500. j,i:8 61
[D][05:17:54][COMM]bat msg 0248 loss. last_tick:0. cur_tick:5014. period:500. j,i:9 62
[D][05:17:54][COMM]bat msg 0249 loss. last_tick:0. cur_tick:5014. period:500. j,i:10 63
[D][05:17:54][COMM]bat msg 0252 loss. last_tick:0. cur_tick:5015. period:500. j,i:19 72
[D][05:17:54][COMM]bat msg 0257 loss. last_tick:0. cur_tick:5015. period:500. j,i:20 73
[D][05:17:54][COMM]bat msg 0258 loss. last_tick:0. cur_tick:5016. period:500. j,i:21 74
[D][05:17:54][COMM]bat msg 025B loss. last_tick:0. cur_tick:5016. period:500. j,i:23 76
[D][05:17:54][COMM]bat msg 025C loss. last_tick:0. cur_tick:5016. period:500. j,i:24 77
[D][05:17:54][COMM]CAN message fault change: 0x0000E00C71E22217->0x0008F00C71E22217 5017
[D][05:17:54][COMM]CAN message bat fault change: 0x0001802E->0x01B987FE 5017


2025-07-31 20:14:21:881 ==>> [D][05:17:54][COMM]5671 imu init OK
[D][05:17:54][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:22:412 ==>> [D][05:17:55][COMM]vbus in, EXT_BAT_STATE_POWERON ,vbuswake : 0
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:0------------
[D][05:17:55][COMM]------------ready to Power on Acckey 2------------


2025-07-31 20:14:22:991 ==>> [D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]more than the number of battery plugs
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]verify_batlock_state ret -516, soc 0
[D][05:17:55][COMM]file:B50 exist
[D][05:17:55][COMM][Audio].l:[255]. success, file_name:B50, size:10800
[D][05:17:55][COMM]f:[frm_audio_send_cmd].send cmd type:17, format:1, file[B50]
[D][05:17:55][COMM]Bat frm_mc_bat_auth_and_poweron
[D][05:17:55][HSDK][0] flush to flash addr:[0xE41C00] --- write len --- [256]
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[893].play audio op:[1]
[W][05:17:55][COMM]Bat auth off fail, error:-1
[D][05:17:55][COMM]frm_peripheral_device_poweron type 0.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[D][05:17:55][COMM]frm_peripheral_device_poweron type 16.... 
[D][05:17:55][COMM]----- get Acckey 1 and value:1------------
[D][05:17:55][COMM]----- get Acckey 2 and value:1------------
[E][05:17:55][COMM][Audio].l:[904].echo is not ready
[D][05:17:55][COMM]f:[ec800m_audio_play_process].l:[1021].play audio file status fail
[D][05:17:55][CO

2025-07-31 20:14:23:096 ==>> MM]Main Task receive event:65
[D][05:17:55][COMM]main task tmp_sleep_event = 80
[D][05:17:55][FCTY]F:[handlerBatLockReport] cmd [BATLOCK_EVT_VBUSIN]!!!
[D][05:17:55][COMM]Main Task receive event:65 finished processing
[D][05:17:55][COMM]Main Task receive event:66
[D][05:17:55][COMM]Try to Auto Lock Bat
[D][05:17:55][COMM]Main Task receive event:66 finished processing
[D][05:17:55][COMM]Main Task receive event:60
[D][05:17:55][COMM]smart_helmet_vol=255,255
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get soc error
[E][05:17:55][COMM]Fatal!!! missing comm with Bat, set fatal code
[D][05:17:55][COMM]report elecbike
[W][05:17:55][PROT]remove success[1629955075],send_path[3],type[0000],priority[0],index[2],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[3],type[5D03],priority[4],index[2],used[1]
[D][05:17:55][COMM]Main Task receive event:60 finished processing
[D][05:17:55][COMM]Main Task receive event:61
[D][05:17:55][COMM][D301]:type:3, trace id:280
[D][05:17:55][COMM]id[], hw[000
[D][05:17:55][PROT]min_index:2, type:0x5D03, priority:4
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POW

2025-07-31 20:14:23:201 ==>> ER_ON]
[D][05:17:55][PROT]index:2
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][PROT]is_send:1
[D][05:17:55][PROT]sequence_num:2
[D][05:17:55][PROT]retry_timeout:0
[D][05:17:55][PROT]retry_times:3
[D][05:17:55][PROT]send_path:0x3
[D][05:17:55][PROT]msg_type:0x5d03
[D][05:17:55][PROT]===========================================================
[W][05:17:55][PROT]SEND DATA TYPE:5D03, SENDPATH:0x1 [1629955075]
[D][05:17:55][PROT]===========================================================
[D][05:17:55][PROT]Sending traceid[9999999999900003]
[D][05:17:55][BLE ]BLE_WRN [ble_service_get_current_send_enabled:28] ble is not connect

[D][05:17:55][BLE ]BLE_WRN [frm_ble_get_current_framer:357] ble is not connect

[W][05:17:55][PROT]ble is not inited or not connected or cccd not enabled
[D][05:17:55][COMM]Receive Bat Lock cmd 0
[D][05:17:55][COMM]VBUS is 1
[D][05:17:55][COMM]get mcMaincircuitVolt error
[D][05:17:55][COMM]get mcSubcircuitVolt error
[D][05:17:55][COMM]33v/48v_in[32], mc_main_vol[0], mc_sub_vol[0]
[D][05:17:55][COMM]BAT CAN get state1 Fail 204
[D][05:17:55][COMM]BAT CAN get soc Fail, 204
[D][05:17:55][COMM]get bat work state err
[W][05:17:55][PROT]r

2025-07-31 20:14:23:261 ==>> emove success[1629955075],send_path[2],type[0000],priority[0],index[3],used[0]
[W][05:17:55][PROT]add success [1629955075],send_path[2],type[D302],priority[0],index[3],used[1]
[D][05:17:55][COMM]Main Task receive event:61 finished processing
[D][05:17:55][M2M ]m2m_task: control_queue type:[M2M_GSM_POWER_ON]
[D][05:17:55][M2M ]m2m_task: gpc:[0],gpo:[0]
[D][05:17:55][COMM]read battery soc:255
[D][05:17:55][COMM]6682 imu init OK
[D][05:17:55][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:23:904 ==>> [D][05:17:56][COMM]7693 imu init OK
[D][05:17:56][CAT1]power_urc_cb ret[76]
[D][05:17:56][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:24:933 ==>> [D][05:17:57][COMM]read battery soc:255
[D][05:17:57][COMM]8705 imu init OK
[D][05:17:57][COMM]imu_task imu work error:[-1]. goto init


2025-07-31 20:14:25:252 ==>> 此处延时了:【4000】毫秒
2025-07-31 20:14:25:256 ==>> 检测【33V输入电压ADC】
2025-07-31 20:14:25:260 ==>> 向【COM34】发送指令:【AT+ADC_ALL】
2025-07-31 20:14:25:561 ==>> [W][05:17:58][COMM]>>>>>Input command = AT+ADC_ALL<<<<<
[D][05:17:58][COMM]adc read vcc5v mc adc:3139  volt:5517 mv
[D][05:17:58][COMM]adc read out 24v adc:1312  volt:33184 mv
[D][05:17:58][COMM]adc read left brake adc:15  volt:19 mv
[D][05:17:58][COMM]adc read right brake adc:13  volt:17 mv
[D][05:17:58][COMM]adc read throttle adc:11  volt:14 mv
[D][05:17:58][COMM]adc read battery ts volt:16 mv
[D][05:17:58][COMM]adc read in 24v adc:1295  volt:32754 mv
[D][05:17:58][COMM]adc read throttle brake in adc:7  volt:12 mv
[D][05:17:58][COMM]arm_hub adc read bat_id adc:11  volt:8 mv
[D][05:17:58][COMM]arm_hub adc read vbat adc:2424  volt:3905 mv
[D][05:17:58][COMM]arm_hub adc read led yb adc:12  volt:278 mv
[D][05:17:58][COMM]arm_hub adc read board id adc:3358  volt:2705 mv
[D][05:17:58][COMM]arm_hub adc read front lamp adc:0  volt:0 mv


2025-07-31 20:14:25:796 ==>> 【33V输入电压ADC】通过,【32754mV】符合目标值【31000mV】至【35000mV】要求!
2025-07-31 20:14:25:799 ==>> 检测【TP7_VCC3V3(ADV2)】
2025-07-31 20:14:25:800 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:14:25:957 ==>> [D][05:17:58][COMM]9717 imu init OK
[D][05:17:58][COMM]imu_task imu work error:[-1]. goto init
1A A1 00 00 FC 
Get AD_V2 1661mV
Get AD_V3 1647mV
Get AD_V4 0mV
Get AD_V5 2766mV
Get AD_V6 1994mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:14:26:065 ==>> 【TP7_VCC3V3(ADV2)】通过,【1661mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:069 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:14:26:083 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1647mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:086 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:14:26:088 ==>> 原始值:【2766】, 乘以分压基数【2】还原值:【5532】
2025-07-31 20:14:26:101 ==>> 【TP68_VCC5V5(ADV5)】通过,【5532mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:14:26:104 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:14:26:119 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1994mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:14:26:121 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:14:26:143 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:14:26:145 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:14:26:292 ==>> [D][05:17:58][COMM]msg 0223 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0225 loss. last_tick:0. cur_tick:10004. period:1000
[D][05:17:58][COMM]msg 0229 loss. last_tick:0. cur_tick:10005. period:1000
[D][05:17:58][COMM]CAN message fault change: 0x0008F00C71E22217->0x0008F80C71E2223F 10005
[D][05:17:58][COMM]CAN fault change: 0x0000000300010E01->0x0000000300010F01 10006
1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1646mV
Get AD_V4 1mV
Get AD_V5 2767mV
Get AD_V6 1992mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:14:26:420 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:423 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:14:26:440 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:442 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:14:26:443 ==>> 原始值:【2767】, 乘以分压基数【2】还原值:【5534】
2025-07-31 20:14:26:458 ==>> 【TP68_VCC5V5(ADV5)】通过,【5534mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:14:26:461 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:14:26:477 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1992mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:14:26:479 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:14:26:500 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:14:26:502 ==>> 向【COM34】发送指令:【1A A1 00 00 FC】
2025-07-31 20:14:26:646 ==>> 1A A1 00 00 FC 
Get AD_V2 1662mV
Get AD_V3 1646mV
Get AD_V4 0mV
Get AD_V5 2765mV
Get AD_V6 1993mV
Get AD_V7 1095mV
OVER 150


2025-07-31 20:14:26:774 ==>> 【TP7_VCC3V3(ADV2)】通过,【1662mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:776 ==>> 检测【TP8_VCC3V3_STM32(ADV3)】
2025-07-31 20:14:26:795 ==>> 【TP8_VCC3V3_STM32(ADV3)】通过,【1646mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:26:814 ==>> 检测【TP68_VCC5V5(ADV5)】
2025-07-31 20:14:26:816 ==>> 原始值:【2765】, 乘以分压基数【2】还原值:【5530】
2025-07-31 20:14:26:818 ==>> 【TP68_VCC5V5(ADV5)】通过,【5530mV】符合目标值【5300mV】至【5700mV】要求!
2025-07-31 20:14:26:820 ==>> 检测【TP3_VCC_SYS(ADV6)】
2025-07-31 20:14:26:832 ==>> 【TP3_VCC_SYS(ADV6)】通过,【1993mV】符合目标值【1900mV】至【2100mV】要求!
2025-07-31 20:14:26:834 ==>> 检测【TP1_VCC12V(ADV7)】
2025-07-31 20:14:26:854 ==>> 【TP1_VCC12V(ADV7)】通过,【1095mV】符合目标值【1000mV】至【1200mV】要求!
2025-07-31 20:14:26:862 ==>> 检测【打开WIFI(1)】
2025-07-31 20:14:26:864 ==>> 向【COM34】发送指令:【AT+WIFISCAN=4,10】
2025-07-31 20:14:27:185 ==>> [D][05:17:59][CAT1]tx ret[4] >>> AT

[D][05:17:59][COMM]read battery soc:255
[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][CAT1]tx ret[6] >>> ATE0

[D][05:17:59][CAT1]<<< 

OK

[D][05:17:59][COMM]10728 imu init OK
[D][05:17:59][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:17:59][COMM]imu_task imu work error:[-1]. goto init
[D][05:17:59][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#NULL#UNKNOW"

OK

[D][05:17:59][CAT1]tx ret[10] >>> AT+CFUN?

[D][05:17:59][CAT1]<<< 
+CFUN: 1

OK

[D][05:17:59][CAT1]exec over: func id: 1, ret: 18
[D][05:17:59][CAT1]sub id: 1, ret: 18

[D][05:17:59][SAL ]Cellular task submsg id[68]
[D][05:17:59][SAL ]handle subcmd ack sub_id[1], socket[0], result[18]
[D][05:17:59][SAL ]gsm power on ind rst[18]
[D][05:17:59][M2M ]m2m gsm power on, ret[0]
[W][05:17:59][COMM]>>>>>Input command = AT+WIFISCAN=4,10<<<<<
[D][05:17:59][COMM][Audio]exec status ready.
[D][05:17:59][M2M ]M2M_Task:m_m2m_thread_setting_queue:0
[D][05:17:59][M2M ]first set address
[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT
[D][05:17:59][COMM]imu default ctrl x[0],y[0],z[10]
[D][05:17:59][COMM]set time err 2021
[D][05:17:59][CAT1]gsm read msg

2025-07-31 20:14:27:230 ==>>  sub id: 31
[D][05:17:59][CAT1]tx ret[56] >>> AT+IMUCFG=200,10,1,1,1,0,23,0,0,-1,0,1,0,1,0,0,16,2000

[D][05:17:59][M2M ]m2m switch to: M2M_GSM_INIT_ACK
[D][05:17:59][COMM]Main Task receive event:1
[D][05:17:59][COMM]Main Task receive event:1 finished processing


2025-07-31 20:14:27:384 ==>> 【打开WIFI(1)】通过,【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】符合目标值【>>>>>Input command = AT+WIFISCAN=4,10<<<<<】要求!
2025-07-31 20:14:27:387 ==>> 检测【清空消息队列(1)】
2025-07-31 20:14:27:399 ==>> 向【COM34】发送指令:【AT+PROTOCOL_TEST=1】
2025-07-31 20:14:27:611 ==>>                                                                                                                                                                                                                               :18:00][CAT1]exec over: func id: 32, ret: 6
[D][05:18:00][CAT1]gsm read msg sub id: 5
[D][05:18:00][CAT1]tx ret[8] >>> AT+GSN

[D][05:18:00][CAT1]<<< 
867222087966224

OK

[D][05:18:00][CAT1]tx ret[9] >>> AT+CIMI

[D][05:18:00][CAT1]<<< 
460130071541423

OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CMGF=0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[15] >>> AT+CSCS="GSM"

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[19] >>> AT+CNMI=2,2,0,0,0

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[10] >>> AT+CPMS?

[D][05:18:00][CAT1]<<< 
+CPMS: "ME",0,180,"ME",0,180,"ME",0,180

OK

[D][05:18:00][CAT1]tx ret[37] >>> AT+QICFG="tcp/keepalive",1,600,25,3

[D][05:18:00][HSDK][0] flush to flash addr:[0xE41D00] --- write len --- [256]
[W][05:18:00][COMM]>>>>>Input command = AT+PROTOCOL_TEST=1<<<<<
[D][05:18:00][COMM]Protocol queue cleaned by AT_CMD!
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[31] >>> AT+QICSGP=1,1,"cmiot","","",0



2025-07-31 20:14:27:659 ==>> 【清空消息队列(1)】通过,【Protocol queue cleaned by AT_CMD】符合目标值【Protocol queue cleaned by AT_CMD】要求!
2025-07-31 20:14:27:662 ==>> 检测【打开GPS(1)】
2025-07-31 20:14:27:666 ==>> 向【COM34】发送指令:【AT+GPSLOG=8】
2025-07-31 20:14:27:836 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GPSLOG=8<<<<<
[D][05:18:00][COMM]Open GPS Module...
[D][05:18:00][COMM]LOC_MODEL_CONT
[D][05:18:00][GNSS]start event:8
[W][05:18:00][GNSS]start cont locating


2025-07-31 20:14:27:930 ==>> 【打开GPS(1)】通过,【start cont locating】符合目标值【start cont locating】要求!
2025-07-31 20:14:27:932 ==>> 检测【打开GSM联网】
2025-07-31 20:14:27:935 ==>> 向【COM34】发送指令:【AT+GSMTEST=1】
2025-07-31 20:14:27:941 ==>> [D][05:18:00][COMM]imu error,enter wait


2025-07-31 20:14:28:573 ==>> [W][05:18:00][COMM]>>>>>Input command = AT+GSMTEST=1<<<<<
[D][05:18:00][COMM]GSM test
[D][05:18:00][COMM]GSM test enable
[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[14] >>> AT+QSCLKEX=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[12] >>> AT+CGATT=1

[D][05:18:00][CAT1]<<< 
OK

[D][05:18:00][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[12] >>> AT+QIACT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[28] >>> AT+QICFG="passiveclosed",1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[17] >>> AT+GPSMODULE=C4

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 5, ret: 6
[D][05:18:01][CAT1]sub id: 5, ret: 6

[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[5], socket[0], result[6]
[D][05:18:01][M2M ]m2m gsm comm init done, ret[0]
[D][05:18:01][M2M ]M2M_GSM_INIT OK
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN
[D][05:18:01][SAL ]open socket ind id[4], rst[0]
[D][05:18:01][M2M ]m2m tcp client connect success host[bikeapi.mobike.com] port[9999]
[D][05:18:01][SAL ]Cellular task submsg id[8]
[D][05:18:01][SAL ]cellular OPEN socket size[144], msg->data[0x20052de8], socket[0]
[D][05:18:01][SAL ]domain[bikeapi.mobike.com] p

2025-07-31 20:14:28:678 ==>> ort[9999] type[1]
[D][05:18:01][CAT1]gsm read msg sub id: 8
[D][05:18:01][CAT1]tx ret[11] >>> AT+CGATT?

[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_OPEN_ACK
[D][05:18:01][CAT1]<<< 
+CGATT: 1

OK

[D][05:18:01][CAT1]tx ret[8] >>> AT+CSQ

[D][05:18:01][COMM]Main Task receive event:4
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13328] ready to read para flash
[D][05:18:01][COMM]init key as 
[D][05:18:01][FCTY]F:[handlerGSMInitSucc].L:[13364] ready to write para flash
[D][05:18:01][COMM]Main Task receive event:4 finished processing
[D][05:18:01][CAT1]<<< 
+CSQ: 20,99

OK

[D][05:18:01][CAT1]tx ret[11] >>> AT+QIACT?

[D][05:18:01][CAT1]<<< 
+QIACT: 1,1,1,"*************"

OK

[D][05:18:01][CAT1]tx ret[51] >>> AT+QIOPEN=1,0,"TCP","bikeapi.mobike.com",9999,0,1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]exec over: func id: 8, ret: 6


2025-07-31 20:14:28:712 ==>> 【打开GSM联网】通过,【GSM test enable】符合目标值【GSM test enable】要求!
2025-07-31 20:14:28:716 ==>> 检测【打开仪表供电1】
2025-07-31 20:14:28:719 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=POWER,1】
2025-07-31 20:14:28:783 ==>> [D][05:18:01][CAT1]opened : 0, 0
[D][05:18:01][SAL ]Cellular task submsg id[68]
[D][05:18:01][SAL ]handle subcmd ack sub_id[8], socket[0], result[14]
[D][05:18:01][SAL ]socket connect ind. id[4], rst[3]
[D][05:18:01][M2M ]M2M_GSM_SOCKET_OPEN OK, gsm_send_status:0
[D][05:18:01][M2M ]g_m2m_is_idle become true
[D][05:18:01][M2M ]m2m switch to: M2M_GSM_SOCKET_IDLE


2025-07-31 20:14:29:178 ==>> [D][05:18:01][COMM]read battery soc:255
[W][05:18:01][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=POWER,1<<<<<
[D][05:18:01][COMM]set POWER 1
[D][05:18:01][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1
[D][05:18:01][GNSS]recv submsg id[1]
[D][05:18:01][GNSS]LOC_SUBCMD_GSM_OPS_IND[5] rst[6]
[D][05:18:01][GNSS]location recv gms init done evt
[D][05:18:01][GNSS]GPS start. ret=0
[D][05:18:01][CAT1]gsm read msg sub id: 23
[D][05:18:01][CAT1]tx ret[12] >>> AT+GPSCFG?

[D][05:18:01][CAT1]<<< 
+GPSCFG:0,0,115200,0,0,65504,0,1,1

OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSPORT=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[14] >>> AT+GPSFREQ=1

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[20] >>> AT+GPSMSGMASK=FFC0

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[19] >>> AT+GPSBAUD=115200

[D][05:18:01][CAT1]<<< 
OK

[D][05:18:01][CAT1]tx ret[13] >>> AT+GPSPWR=1



2025-07-31 20:14:29:241 ==>> 【打开仪表供电1】通过,【set POWER 1】符合目标值【set POWER 1】要求!
2025-07-31 20:14:29:245 ==>> 检测【打开仪表指令模式2】
2025-07-31 20:14:29:249 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=CMD_MODE,1】
2025-07-31 20:14:29:436 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=CMD_MODE,1<<<<<
[D][05:18:02][COMM][oneline_display]: command mode, ON!
[D][05:18:02][COMM]display state, enable_display:0x0, value:0, enable_twinkle:0x0, power:1


2025-07-31 20:14:29:515 ==>> 【打开仪表指令模式2】通过,【command mode, ON】符合目标值【command mode, ON】要求!
2025-07-31 20:14:29:518 ==>> 检测【读取主控ADC采集的仪表电压】
2025-07-31 20:14:29:521 ==>> 向【COM34】发送指令:【AT+ARM_ADC=3】
2025-07-31 20:14:29:736 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ARM_ADC=3<<<<<
[D][05:18:02][COMM]arm_hub read adc[3],val[33409]


2025-07-31 20:14:29:784 ==>> 【读取主控ADC采集的仪表电压】通过,【33409mV】符合目标值【30000mV】至【35000mV】要求!
2025-07-31 20:14:29:788 ==>> 检测【TP53测试仪表一线通通信功能】
2025-07-31 20:14:29:791 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:14:29:980 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBGST,,0.000,0.005,0.005,0.005,2097152,2097152,2097152*7A

[W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
[D][05:18:02][COMM]13741 imu init OK


2025-07-31 20:14:30:058 ==>> 【TP53测试仪表一线通通信功能】通过,【oneline display ALL on 1】符合目标值【oneline display ALL on 1】要求!
2025-07-31 20:14:30:061 ==>> 检测【AD_V20电压】
2025-07-31 20:14:30:064 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=TEST,1】
2025-07-31 20:14:30:163 ==>> 向【COM34】发送指令:【1A A1 10 00 00】
2025-07-31 20:14:30:239 ==>> [W][05:18:02][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=TEST,1<<<<<
[D][05:18:02][COMM]oneline display ALL on 1
[D][05:18:02][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1
1A A1 10 00 00 
Get AD_V20 1639mV
OVER 150


2025-07-31 20:14:30:554 ==>> [D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]tx ret[17] >>> AT+GPSNAVSAT=1F



2025-07-31 20:14:30:615 ==>> 本次取值间隔时间:446ms
2025-07-31 20:14:30:632 ==>> 【AD_V20电压】通过,【1639mV】符合目标值【1500mV】至【1700mV】要求!
2025-07-31 20:14:30:636 ==>> 检测【拉低OUTPUT2】
2025-07-31 20:14:30:640 ==>> 向【COM34】发送指令:【3A A3 02 00 A3】
2025-07-31 20:14:30:660 ==>> [D][05:18:03][CAT1]<<< 
OK



2025-07-31 20:14:30:749 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,4,1,13,33,,,43,24,,,41,25,,,41,59,,,40,1*7F

$GBGSV,4,2,13,14,,,40,38,,,37,2,,,45,60,,,42,1*4F

$GBGSV,4,3,13,13,,,40,3,,,39,4,,,38,7,,,37,1*40

$GBGSV,4,4,13,6,,,37,1*46

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1672.089,1672.089,53.427,2097152,2097152,2097152*48

[D][05:18:03][CAT1]tx ret[21] >>> AT+GETVERSION=total

[D][05:18:03][CAT1]<<< 
+GETVERSION: "total","EC800M_APP_MTE_V21.2.1#EC800MCNLER06A05M08_OCPU_MTE_V5.0.0#NULL#RTK_MTE_V1.2.4#C4_VHD8040B.3465b5b1"

OK

[D][05:18:03][CAT1]tx ret[14] >>> AT+GPSMODE=1

[D][05:18:03][CAT1]<<< 
OK

[D][05:18:03][CAT1]exec over: func id: 23, ret: 6
[D][05:18:03][CAT1]sub id: 23, ret: 6

3A A3 02 00 A3 
OFF_OUT2
OVER 150


2025-07-31 20:14:30:907 ==>> 【拉低OUTPUT2】通过,【OFF_OUT2】符合目标值【OFF_OUT2】要求!
2025-07-31 20:14:30:910 ==>> 检测【预留IO RX功能检查(0)】
2025-07-31 20:14:30:914 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:14:30:946 ==>> [D][05:18:03][COMM]read battery soc:255
[D][05:18:03][GNSS]recv submsg id[1]
[D][05:18:03][GNSS]LOC_SUBCMD_GSM_OPS_IND[23] rst[6]


2025-07-31 20:14:31:171 ==>> [D][05:18:03][HSDK]need to erase for write: is[0x0] ie[0xE00]
[D][05:18:03][HSDK][0] flush to flash addr:[0xE41E00] --- write len --- [256]
[W][05:18:03][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:03][COMM]oneline display read state:1
[D][05:18:03][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:14:31:699 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,43,60,,,42,1,,,42,24,,,41,1*43

$GBGSV,5,2,20,25,,,41,59,,,41,14,,,41,3,,,41,1*4E

$GBGSV,5,3,20,13,,,39,39,,,39,38,,,38,16,,,38,1*76

$GBGSV,5,4,20,6,,,37,9,,,36,7,,,35,5,,,35,1*79

$GBGSV,5,5,20,4,,,34,42,,,42,2,,,39,41,,,39,1*70

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1616.861,1616.861,51.710,2097152,2097152,2097152*4D



2025-07-31 20:14:31:940 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:14:32:165 ==>> [W][05:18:04][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:04][COMM]oneline display read state:1
[D][05:18:04][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:14:32:702 ==>> $GBGGA,,,,,,0,00,,,M,,M,,*74

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,5,1,20,33,,,43,42,,,42,24,,,42,25,,,42,1*76

$GBGSV,5,2,20,14,,,42,60,,,41,59,,,41,3,,,41,1*4C

$GBGSV,5,3,20,1,,,40,13,,,39,39,,,39,38,,,38,1*4F

$GBGSV,5,4,20,16,,,38,2,,,37,6,,,37,9,,,36,1*41

$GBGSV,5,5,20,7,,,36,5,,,34,4,,,34,40,,,34,1*44

$GBRMC,,V,,,,,,,,0.0,E,N,V*50

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,,0.000,1608.579,1608.579,51.454,2097152,2097152,2097152*4E



2025-07-31 20:14:32:886 ==>> [D][05:18:05][COMM]read battery soc:255


2025-07-31 20:14:32:977 ==>> 向【COM34】发送指令:【AT+ONELINE_DISPLAY=GPIO,0】
2025-07-31 20:14:33:162 ==>> [W][05:18:05][COMM]>>>>>Input command = AT+ONELINE_DISPLAY=GPIO,0<<<<<
[D][05:18:05][COMM]oneline display read state:1
[D][05:18:05][COMM]display state, enable_display:0x7f, value:136, enable_twinkle:0x0, power:1


2025-07-31 20:14:33:732 ==>> $GBGGA,121437.515,,,,,0,00,,,M,,M,,*69

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,6,1,21,33,,,43,24,,,42,25,,,42,14,,,42,1*77

$GBGSV,6,2,21,42,,,41,60,,,41,59,,,41,3,,,41,1*4E

$GBGSV,6,3,21,1,,,39,13,,,39,39,,,39,38,,,38,1*43

$GBGSV,6,4,21,16,,,38,2,,,37,6,,,37,9,,,36,1*43

$GBGSV,6,5,21,7,,,36,40,,,36,26,,,35,5,,,34,1*75

$GBGSV,6,6,21,4,,,34,1*46

$GBRMC,121437.515,V,,,,,,,,0.0,E,N,V*4D

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121437.515,0.000,1601.070,1601.070,51.208,2097152,2097152,2097152*5C



2025-07-31 20:14:34:017 ==>> 未匹配到【预留IO RX功能检查(0)】数据,请核对检查!
2025-07-31 20:14:34:023 ==>> #################### 【测试结束】 ####################
2025-07-31 20:14:34:041 ==>> 关闭5V供电
2025-07-31 20:14:34:045 ==>> 向【COM34】发送指令:【5A A5 04 5A A5】
2025-07-31 20:14:34:140 ==>> 5A A5 04 5A A5 
CLOSE_POWER_OUT2
OVER 150


2025-07-31 20:14:34:731 ==>> $GBGGA,121438.515,,,,,0,00,,,M,,M,,*66

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,42,25,,,42,14,,,42,1*72

$GBGSV,7,2,25,42,,,41,60,,,41,59,,,41,3,,,41,1*4B

$GBGSV,7,3,25,1,,,39,13,,,39,39,,,39,38,,,38,1*46

$GBGSV,7,4,25,16,,,38,2,,,37,6,,,37,40,,,37,1*7A

$GBGSV,7,5,25,9,,,36,7,,,36,8,,,36,26,,,35,1*42

$GBGSV,7,6,25,10,,,34,5,,,34,4,,,34,44,,,31,1*75

$GBGSV,7,7,25,34,,,28,1*7C

$GBRMC,121438.515,V,,,,,,,,0.0,E,N,V*42

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121438.515,0.000,1560.527,1560.527,49.957,2097152,2097152,2097152*5B



2025-07-31 20:14:34:881 ==>> [D][05:18:07][COMM]read battery soc:255


2025-07-31 20:14:35:049 ==>> 关闭5V供电成功
2025-07-31 20:14:35:053 ==>> 关闭33V供电
2025-07-31 20:14:35:057 ==>> 向【COM34】发送指令:【5A A5 02 5A A5】
2025-07-31 20:14:35:139 ==>> 5A A5 02 5A A5 
CLOSE_POWER_OUT1
OVER 150


2025-07-31 20:14:35:319 ==>> [D][05:18:08][FCTY]get_ext_48v_vol retry i = 0,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 1,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 2,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 3,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 4,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 5,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 6,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 7,volt = 11
[D][05:18:08][FCTY]get_ext_48v_vol retry i = 8,volt = 11


2025-07-31 20:14:35:544 ==>> [D][05:18:08][COMM]VBUS Insert EXTI Come sw3 EXT_BAT_STATE_POWERON, 7


2025-07-31 20:14:35:724 ==>> $GBGGA,121439.515,,,,,0,00,,,M,,M,,*67

$GBGSA,A,1,,,,,,,,,,,,,,,,4*14

$GBGSV,7,1,25,33,,,43,24,,,42,25,,,42,14,,,42,1*72

$GBGSV,7,2,25,42,,,41,60,,,41,59,,,41,3,,,41,1*4B

$GBGSV,7,3,25,39,,,40,1,,,39,13,,,39,38,,,38,1*48

$GBGSV,7,4,25,16,,,38,40,,,38,6,,,37,9,,,37,1*7E

$GBGSV,7,5,25,2,,,36,7,,,36,8,,,36,26,,,36,1*4A

$GBGSV,7,6,25,10,,,34,5,,,34,4,,,34,44,,,32,1*76

$GBGSV,7,7,25,34,,,29,1*7D

$GBRMC,121439.515,V,,,,,,,,0.0,E,N,V*43

$GBVTG,0.00,T,,M,0.000,N,0.000,K,N*20

$GBGST,121439.515,0.000,1568.809,1568.809,50.212,2097152,2097152,2097152*58



2025-07-31 20:14:36:063 ==>> 关闭33V供电成功
2025-07-31 20:14:36:066 ==>> 关闭3.7V供电
2025-07-31 20:14:36:071 ==>> 向【COM34】发送指令:【6A A6 02 A6 6A】
2025-07-31 20:14:36:138 ==>> 6A A6 02 A6 6A 
Battery OFF
OVER 150


2025-07-31 20:14:36:738 ==>>  

